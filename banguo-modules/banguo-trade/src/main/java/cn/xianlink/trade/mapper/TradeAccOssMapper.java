package cn.xianlink.trade.mapper;

import cn.xianlink.common.mybatis.core.mapper.BaseMapperPlus;
import cn.xianlink.trade.domain.TradeAccOss;
import cn.xianlink.trade.domain.vo.TradeAccOssStatusVo;
import cn.xianlink.trade.domain.vo.TradeAccOssVo;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * 数据文件Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
public interface TradeAccOssMapper extends BaseMapperPlus<TradeAccOss, TradeAccOssVo> {

    TradeAccOssStatusVo queryAcctStatus(@Param("fileType") String fileType, @Param("acctDate") LocalDate acctDate);

}
