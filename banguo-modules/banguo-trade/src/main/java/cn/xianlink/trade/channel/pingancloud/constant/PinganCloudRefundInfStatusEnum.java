package cn.xianlink.trade.channel.pingancloud.constant;

import cn.hutool.core.util.ObjectUtil;

public enum PinganCloudRefundInfStatusEnum {
    // 退款状态对应关系   转成 RefundInfStatusEnum 枚举值
    REFUND_PAYING("2", "处理中", 0),
    REFUND_SUCCESS("1", "退款成功", 1),
    REFUND_FAIL("4", "失败", 2);

    private final String status;
    private final Integer infStatus;
    private final String desc;

    PinganCloudRefundInfStatusEnum(String status, String desc, Integer infStatus) {
        this.status = status;
        this.infStatus = infStatus;
        this.desc = desc;
    }

    public String getStatus() {
        return status;
    }

    public Integer getInfStatus() {
        return infStatus;
    }

    public String getDesc() {
        return desc;
    }

    public static Integer getInfStatus(String status) {
        for (PinganCloudRefundInfStatusEnum e : values()) {
            if (ObjectUtil.equal(e.getStatus(), status)) {
                return e.getInfStatus();
            }
        }
        return -1;
    }
}
