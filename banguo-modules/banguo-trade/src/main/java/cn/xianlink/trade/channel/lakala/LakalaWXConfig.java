package cn.xianlink.trade.channel.lakala;


import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class LakalaWXConfig implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private String appId;
    private String serialNo;
    private String priKeyPath;
    private String lklCerPath;
    private String lklNotifyCerPath;
    private String sm4Key;
    private String serverUrl;
    // 业务参数
    private String merchantId;
    private String termNo;
    // 回调path
    private String callbackUrl;
    private String callbackPath;
    //
    private Map<String, List<String>> regionWhIps;
    private String ftpUrl;

}
