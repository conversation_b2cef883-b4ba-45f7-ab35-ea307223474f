package cn.xianlink.trade.channel.weixinb2b.constant;

public enum WeixinB2bPayApiEnum {
    // https://api.weixin.qq.com
//    ACCESS_TOKEN("/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s", false, "GET 获取访问token，有效期为2个小时，重复获取上次的失效"),
//    AUTHORIZER_TOKEN("/cgi-bin/component/api_authorizer_token?component_access_token=COMPONENT_ACCESS_TOKEN", false, "获取访问token"),

    PAY("requestCommonPayment", true, "支付"),
    PAY_QUERY("/retail/B2b/getorder", true, "支付查询"),
    // PAY_CLOSE("/close", "支付关闭"),
    PAY_REFUND("/retail/B2b/refund", true, "支付退款"),
    PAY_REFUND_QUERY("/retail/B2b/getrefund", true, "退款查询"),
    PAY_DOWNLOAD("/retail/B2b/downloadbill", true, "下载账单"),
    RETAIL_INFO("/wxa/business/getretailinfo", false, "门店信息查询api"),
    RETAIL_PROFIT("/retail/B2b/setmchprofitrate", false, "报名技术服务费优惠活动"),
//    ORDER_DELIVERY("/wxa/sec/order/upload_shipping_info", false, "订单发货"),
//    ORDER_QUERY("/wxa/sec/order/get_order", false, "订单发货查询"),
//    ORDER_LIST_QUERY("/wxa/sec/order/get_order_list", false, "订单发货列表查询"),
//    ORDER_SHIP_INFO("/wxa/sec/order/upload_shipping_info", false, "提醒用户做确认收货"),
//    PUSH_TEMPLATE("/cgi-bin/message/wxopen/template/send", false, "提醒用户做确认收货"),
//    // 已完成订单管理授权的商户号，产生的订单均需要通过发货信息管理服务进行发货
//    MANAGE_1("/wxa/sec/order/is_trade_managed", false, "查询是否开通发货信息管理服务"),
//    MANAGE_2("/wxa/sec/order/is_trade_management_confirmation_completeds", false, "查询是否开通发货信息管理服务"),
    ;

    private final String url;
    private final boolean isPaySig;
    private final String remark;

    WeixinB2bPayApiEnum(String url, boolean isPaySig, String remark) {
        this.url = url;
        this.isPaySig = isPaySig;
        this.remark = remark;
    }

    public String getUrl() {
        return url;
    }
    public boolean isPaySig() {
        return isPaySig;
    }
    public String getRemark() {
        return remark;
    }
}
