package cn.xianlink.trade.channel.pingancloud;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.net.NetUtil;
import cn.xianlink.common.api.enums.trade.*;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.trade.channel.pingancloud.constant.PinganCloudClientApiEnum;
import cn.xianlink.trade.channel.pingancloud.constant.PinganCloudOrgTypeEnum;
import cn.xianlink.trade.config.properties.ChannelsProperties;
import cn.xianlink.trade.domain.bo.org.TradeOrgBankRelaAuthBo;
import cn.xianlink.trade.domain.bo.org.TradeOrgBankRelaBo;
import cn.xianlink.trade.domain.vo.org.TradeOrgBankAuthVo;
import cn.xianlink.trade.domain.vo.org.TradeOrgBankRelaAuthVo;
import cn.xianlink.trade.domain.vo.org.TradeOrgBankRelaCheckVo;
import cn.xianlink.trade.domain.vo.org.TradeOrgBankRelaVo;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * 平安见证宝对接
 *
 * <AUTHOR>
 * @date 2024-05-27
*/

@CustomLog
@RequiredArgsConstructor
@Service
public class PinganCloudOrgBankRelaService {

    private final transient ChannelsProperties channelsProperties;
    private final transient PinganCloudClientService client;

    /*
        返回是否管控
     */
    public boolean queryOrgControlType(TradeOrgBankRelaVo vo) {
        JSONObject body = new JSONObject();
        body.put("CustAcctId", vo.getOutAcctCode());
        body.put("ThirdCustId", vo.getOutOrgCode());
        JSONObject resp = client.post(vo.getOutOrgCode(), "queryOrgControlType",  PinganCloudClientApiEnum.ACC_ORG_QUERY, body);
        // resp.getString("BusinessFlag");  // 个体工商户标志  1-是 0-否
        // resp.getString("ClientLvl"); // 客户属性：00-普通;SH-商户
        // resp.getString("OrangePay"); // 是否有登记行为记录信息 S 是 F 否
        // resp.getString("ControlType"); // 管控类型 :N-正常 0 禁止提现 1 禁止入金 2 禁止出金 3 禁止出入金 4 解除风险 5 解除禁止提现 6 解除禁止入金 7 解除禁止出金 D 解冻 8 疑似风险 9 解除禁止出入金 10 平台已管控
        return !"N".equals(resp.getString("ControlType"));
    }

    public TradeOrgBankRelaBo openOrgRelate(TradeOrgBankRelaVo vo) {
        /* 开户 */
        if (StringUtils.isBlank(vo.getOutOrgCode())) {
            vo.setOutOrgCode(String.format("%s%s%s", channelsProperties.getAccountNoPrefix(),
                    PinganCloudOrgTypeEnum.getPrefix(vo.getOrgType()), vo.getOrgCode()));
        }
        JSONObject body = new JSONObject();
        body.put("FunctionFlag", "1"); // 1 开户 2 销户
        body.put("MemberProperty", vo.getOutOrgProperty());
        setCommonOrgRelationJson(true, body, vo);
        JSONObject resp;
        try {
            resp = client.post(vo.getOutOrgCode(), "openOrgRelate", PinganCloudClientApiEnum.ACC_ORG_RELATION, body);
        } catch (ServiceException se) {
            if (se.getMessage().contains("[ERR115]")) {
                resp = JSONObject.parseObject(se.getDetailMessage());
                if (StringUtils.isNotBlank(resp.getString("SubAcctNo"))) {
                    // 该会员代码已在开户中，请稍后重试[ERR115]
                    log.keyword(vo.getOutOrgCode(), "openOrgRelate").warn("[ERR115] 转异常", se);
                } else {
                    throw se;
                }
            } else {
                throw se;
            }
        }
        TradeOrgBankRelaBo respBo = new TradeOrgBankRelaBo();
        respBo.setOrgCode(vo.getOrgCode());
        respBo.setOrgType(vo.getOrgType());
        respBo.setOutOrgCode(vo.getOutOrgCode());
        respBo.setOutOrgProperty(vo.getOutOrgProperty());
        respBo.setOutAcctCode(resp.getString("SubAcctNo"));
        // 不调用同名接口 TradeOrgBankRelaBo respBo = sameOrgBank(vo);
        respBo.setRelaId(vo.getRelaId());
        respBo.setOrgId(vo.getOrgId());
        respBo.setOrgName(vo.getOrgName());
        respBo.setOutOrgType(vo.getOrgType());
        respBo.setOutOrgName(vo.getOrgName());
        respBo.setOutAcctMobile(vo.getBankMobile());
        respBo.setStatus(AccountOrgBindEnum.OPEN.getCode());
        // 以下 5 个值， 在绑定银行时，是不可修改的
        respBo.setCompanyGlobalId(vo.getCompanyGlobalId());
        respBo.setCompanyName(vo.getCompanyName());
        respBo.setReprGlobalId(vo.getReprGlobalId());
        respBo.setReprName(vo.getReprName());
        respBo.setBusinessFlag(vo.getBusinessFlag());
        if (AccountBusinessFlagEnum.ENTERPRISE.getCode().equals(vo.getBusinessFlag())) {
            respBo.setBankAccName(vo.getCompanyName());
        } else {
            respBo.setBankAccName(vo.getReprName());
        }
        return respBo;
    }

    private void setCommonOrgRelationJson(boolean isOpen, JSONObject body, TradeOrgBankRelaVo vo) {
        /* 原系统逻辑
            1 开户
                个人          MemberGlobalType = 1
                企业 和 个体  MemberGlobalType = 73
            2 绑卡
                企业           MemberGlobalType = 73   -> 6240
                个人 和 个体    MemberGlobalType = 1    -> 6238     绑卡时虽传入 1， 但账户 MemberGlobalType 还是 73
                可绑多卡， 6098 查询同会员可能多条记录
            3 提现
               法人提现 MemberGlobalType = 1
            绑卡和开户手机号可以不同
         */
        body.put("TranNetMemberCode", vo.getOutOrgCode()); // 企业系统内编码
        body.put("Mobile", vo.getBankMobile()); // 银行卡手机
        if (AccountBusinessFlagEnum.ENTERPRISE.getCode().equals(vo.getBusinessFlag())
                || isOpen && AccountBusinessFlagEnum.INDIVIDUAL.getCode().equals(vo.getBusinessFlag())) {
            // 企业送企业信息
            body.put("MemberName", vo.getCompanyName());
            body.put("MemberGlobalType", "73"); // 会员是企业，73
            body.put("MemberGlobalId", vo.getCompanyGlobalId()); // 会员身份证号
            if (!isOpen && vo.getBankFlag() != null && !AccountBankFlagEnum.COMPANY.getCode().equals(vo.getBankFlag())) {
                throw new ServiceException("企业只能使用对公账户");
            }
        } else {
            // 个体送法人信息
            body.put("MemberName", vo.getReprName());
            body.put("MemberGlobalType", "1"); // 会员是个人主体
            body.put("MemberGlobalId", vo.getReprGlobalId()); // 会员身份证号
        }
        // AccountBusinessFlagEnum.PERSON.getCode() = "0"
		if ("0".equals(vo.getBusinessFlag())) {
            body.put("IndivBusinessFlag", "2");
        } else {
            body.put("IndivBusinessFlag", vo.getBusinessFlag());// 1 个体工商户  2 企业 (个人)
            body.put("CompanyName", vo.getCompanyName()); // 企业名称
            body.put("CompanyGlobalType", "73"); // 73 统一社会信用代码证号
            body.put("CompanyGlobalId", vo.getCompanyGlobalId()); // 公司证件号码
            body.put("ShopId", vo.getOutOrgCode());
            body.put("ShopName", vo.getCompanyName());
            body.put("RepFlag", "1"); // 1 是法人
            body.put("ReprName", vo.getReprName()); // 法人姓名
            body.put("ReprGlobalType", "1"); //  1 身份证
            body.put("ReprGlobalId", vo.getReprGlobalId()); // 法人身份证号
        }
    }

    public TradeOrgBankRelaBo updateOrgRelate(TradeOrgBankRelaVo tvo, TradeOrgBankRelaVo vo) {
        // AccountBusinessFlagEnum.PERSON.getCode() = "0"
		if ("0".equals(vo.getBusinessFlag())) {
            throw new ServiceException("个人账户不支持更改");
        }
        TradeOrgBankRelaBo respBo = new TradeOrgBankRelaBo();
        respBo.setOrgCode(vo.getOrgCode());
        respBo.setOrgType(vo.getOrgType());
        respBo.setOutOrgType(vo.getOutOrgType());
        respBo.setOutOrgCode(vo.getOutOrgCode());
        respBo.setOutOrgProperty(vo.getOutOrgProperty());
        respBo.setOutOrgName(tvo.getOrgName());
        if (!tvo.getCompanyName().equals(vo.getCompanyName())
                || !tvo.getReprName().equals(vo.getReprName())
                || !tvo.getReprGlobalId().equals(vo.getReprGlobalId())) {
            // 更新 公司名称 和 法人
            JSONObject body = new JSONObject();
            body.put("TranNetMemberCode", vo.getOutOrgCode()); // 企业系统内编码
            body.put("SubAcctNo", vo.getOutAcctCode());
            body.put("MemberName", tvo.getCompanyName());
            body.put("CompanyName", tvo.getCompanyName());
            body.put("ReprName", tvo.getReprName()); // 法人姓名
            body.put("ReprGlobalType", "1"); //  1 身份证
            body.put("ReprGlobalId", tvo.getReprGlobalId()); // 法人身份证号
            client.post(vo.getOutOrgCode(), "updateOrgRelate", PinganCloudClientApiEnum.ACC_ORG_UPDATE, body);
            respBo.setCompanyName(tvo.getCompanyName());
            respBo.setReprGlobalId(tvo.getReprGlobalId());
            respBo.setReprName(tvo.getReprName());
            if (AccountBusinessFlagEnum.ENTERPRISE.getCode().equals(vo.getBusinessFlag())) {
                respBo.setBankAccName(tvo.getCompanyName());
            } else {
                respBo.setBankAccName(tvo.getReprName());
            }
        }
        return respBo;
    }

    public TradeOrgBankRelaBo closeOrgRelate(TradeOrgBankRelaVo vo) {
        // 销户仅测试环境可用， 销户不可逆
        JSONObject body = new JSONObject();
        body.put("FunctionFlag", "2"); // 1 开户 2 销户
        body.put("TranNetMemberCode", vo.getOutOrgCode()); // 企业系统内编码
        body.put("SubAcctNo", vo.getOutAcctCode());
        // 企业主体 + 企业证件开户
        // AccountBusinessFlagEnum.PERSON.getCode() = "0"
		if ("0".equals(vo.getBusinessFlag())) {
            body.put("MemberName", vo.getReprName());
            body.put("MemberGlobalType", "1");
            body.put("MemberGlobalId", vo.getReprGlobalId());
            body.put("MemberProperty", vo.getOutOrgProperty());
            body.put("IndivBusinessFlag", "2");
        } else {
            body.put("MemberName", vo.getCompanyName());
            body.put("MemberGlobalType", "73"); // 会员是企业，73
            body.put("MemberGlobalId", vo.getCompanyGlobalId());
            body.put("MemberProperty", vo.getOutOrgProperty());
            body.put("IndivBusinessFlag", vo.getBusinessFlag());
        }
        client.post(vo.getOutOrgCode(), "closeOrgRelate",  PinganCloudClientApiEnum.ACC_ORG_RELATION, body);
        TradeOrgBankRelaBo respBo = new TradeOrgBankRelaBo();
        respBo.setOrgCode(vo.getOrgCode());
        respBo.setOrgType(vo.getOrgType());
        respBo.setOutOrgType(vo.getOutOrgType());
        respBo.setOutOrgCode(vo.getOutOrgCode());
        respBo.setOutOrgProperty(vo.getOutOrgProperty());
        respBo.setStatus(AccountOrgBindEnum.CLOSE.getCode());
        return respBo;
    }

    public TradeOrgBankRelaBo bindBank(TradeOrgBankRelaVo vo) {
        // 本他行标志错误选择[E90012]
        JSONObject body = toBindBankJson(vo);
        try {
            JSONObject resp = client.post(vo.getOutOrgCode(), "bindBank", PinganCloudClientApiEnum.ACC_BIND_SMS_APPLY, body);
            return toBindBankBo(resp, vo);
        } catch (ServiceException se) {
            if (se.getMessage().contains("[ERR145]")) {
                // {\"ErrorCode\":\"ERR145\",\"ErrorMessage\":\"限定时间内禁止频繁发起小额转账鉴权绑定申请\"}
                se.setMessage("限定时间内禁止频繁发起同银行卡的绑定申请");
            }
            throw se;
        }
    }

    public TradeOrgBankRelaBo bindBankAmt(TradeOrgBankRelaVo vo) {
        JSONObject body = toBindBankJson(vo);
        body.put("AgencyClientFlag", "2"); // 是否经办人  1 是，2否
        try {
            JSONObject resp = client.post(vo.getOutOrgCode(), "bindBankAmt",  PinganCloudClientApiEnum.ACC_BIND_AMT_APPLY, body);
            // "Data\":{\"TxnReturnCode\":\"000000\",\"TxnReturnMsg\":\"交易成功\",\"ReservedMsg\":\"2024071209344946639145\",\"CnsmrSeqNo\":\"2024071209414718653391\"}
            return toBindBankBo(resp, vo);
        } catch (ServiceException se) {
            if (se.getMessage().contains("[FFFFFF]")) {
                // {"ErrorCode":"FFFFFF","ErrorMessage":"转账预处理异常，须进行异常标记，不能直接标记为失败"}
                JSONObject resp = JSONObject.parseObject(se.getDetailMessage());
                String bindNo = resp.getString("ReservedMsg");
                if (StringUtils.isEmpty(bindNo)) {
                    bindNo = resp.getString("CnsmrSeqNo");
                }
                vo.setBankBindNo(bindNo);
                if (queryBindBankAmt(vo)) {
                    log.keyword(vo.getOutOrgCode(), "bindBankAmt").warn("[FFFFFF] 转异常", se);
                    return toBindBankBo(resp, vo);
                }
            } else if (se.getMessage().contains("[ERR145]")) {
                // {\"ErrorCode\":\"ERR145\",\"ErrorMessage\":\"限定时间内禁止频繁发起小额转账鉴权绑定申请\"}  这种是短时间内使用相同银行卡频繁重试
                se.setMessage("限定时间内禁止频繁发起同银行卡的绑定申请");
            }
            throw se;
        }
    }
    private JSONObject toBindBankJson(TradeOrgBankRelaVo vo){
        vo.setBankType(vo.getBankBranchName().contains("平安银行") ? "1" : "2");
        JSONObject body = new JSONObject();
        body.put("SubAcctNo", vo.getOutAcctCode());
        body.put("MemberAcctNo", vo.getBankAccount()); //
        body.put("BankType", vo.getBankType()); // 是否有平安  1：本行 2：他行
        body.put("AcctOpenBranchName", vo.getBankBranchName()); // 银行名 ???
        body.put("EiconBankBranchId", vo.getBankEicon()); // 银行名
        setCommonOrgRelationJson(false, body, vo);
        return body;
    }
    private TradeOrgBankRelaBo toBindBankBo(JSONObject resp, TradeOrgBankRelaVo vo){
        TradeOrgBankRelaBo respBo = new TradeOrgBankRelaBo();
        respBo.setOrgCode(vo.getOrgCode()); // 缓存
        respBo.setOrgType(vo.getOrgType());
        respBo.setOutOrgType(vo.getOutOrgType());
        respBo.setOutOrgCode(vo.getOutOrgCode()); // 更新主键
        respBo.setOutAcctCode(vo.getOutAcctCode());
        respBo.setOutOrgProperty(vo.getOutOrgProperty());
        respBo.setStatus(AccountOrgBindEnum.OPEN.getCode());
        respBo.setBankAccount(vo.getBankAccount());
        respBo.setBankFlag(vo.getBankFlag());
        respBo.setBankType(vo.getBankType());
        respBo.setBankMobile(vo.getBankMobile());
        respBo.setBankEicon(vo.getBankEicon());
        respBo.setBankBranchName(vo.getBankBranchName());
        if (AccountBusinessFlagEnum.ENTERPRISE.getCode().equals(vo.getBusinessFlag())) {
            respBo.setBankAccName(vo.getCompanyName());
        } else {
            respBo.setBankAccName(vo.getReprName());
        }
        String bindNo = resp.getString("ReservedMsg");
        if (StringUtils.isEmpty(bindNo)) {
            bindNo = resp.getString("CnsmrSeqNo");
        }
        respBo.setBankBindNo(bindNo);
        respBo.setOutBindNo("");
        respBo.setOutUnbindNo("");
        return respBo;
    }
    public TradeOrgBankRelaBo checkBindBank(TradeOrgBankRelaVo vo, TradeOrgBankRelaCheckVo checkVo) {
        JSONObject body = new JSONObject();
        body.put("TranNetMemberCode", vo.getOutOrgCode());
        body.put("SubAcctNo", vo.getOutAcctCode());
        body.put("MemberAcctNo", vo.getBankAccount()); //
        body.put("MessageCheckCode", checkVo.getSmsCode());
        JSONObject resp = client.post(vo.getOutOrgCode(), "checkBindBank",  PinganCloudClientApiEnum.ACC_BIND_SMS_COMMIT, body);
        return toCheckBindBankBo(resp, vo);
    }

    public TradeOrgBankRelaBo checkBindBankAmt(TradeOrgBankRelaVo vo, TradeOrgBankRelaCheckVo checkVo) {
        JSONObject body = new JSONObject();
        body.put("TranNetMemberCode", vo.getOutOrgCode());
        body.put("SubAcctNo", vo.getOutAcctCode());
        body.put("TakeCashAcctNo", vo.getBankAccount()); //
        body.put("AuthAmt", checkVo.getAuthAmt().toString());
        body.put("OrderNo", checkVo.getSmsCode());
        body.put("Ccy", "RMB");
        JSONObject resp = client.post(vo.getOutOrgCode(), "checkBindBankAmt",  PinganCloudClientApiEnum.ACC_BIND_AMT_COMMIT, body);
        return toCheckBindBankBo(resp, vo);
    }
    private TradeOrgBankRelaBo toCheckBindBankBo(JSONObject resp,TradeOrgBankRelaVo vo){
        TradeOrgBankRelaBo respBo = new TradeOrgBankRelaBo();
        respBo.setOrgCode(vo.getOrgCode()); // 缓存
        respBo.setOrgType(vo.getOrgType());
        respBo.setOutOrgCode(vo.getOutOrgCode()); // 更新主键
        respBo.setOutOrgProperty(vo.getOutOrgProperty());
        respBo.setBankAccount(vo.getBankAccount());
        respBo.setBankBindNo("");
        respBo.setOutBindNo(resp.getString("FrontSeqNo"));
        respBo.setOutUnbindNo("");
        respBo.setOutBindTime(Convert.toDate(DateUtil.now()));
        respBo.setStatus(AccountOrgBindEnum.BIND.getCode());
        return respBo;
    }

    public void bindBankRecord(TradeOrgBankRelaVo vo) {
        JSONObject body = new JSONObject();
        body.put("FunctionFlag", "1");
        body.put("TranNetMemberCode", vo.getOutOrgCode());
        body.put("SubAcctNo", vo.getOutAcctCode());
        body.put("OpClickTime", Convert.toDate(DateUtil.now())); // FunctionFlag=1时必输
        body.put("IpAddress", NetUtil.getLocalhostStr()); // FunctionFlag=1时必输
        body.put("MacAddress", NetUtil.getLocalMacAddress()); // FunctionFlag=1时必输
        body.put("SignChannel", "4"); // 1-app 2-平台H5网页 3-公众号 4-小程序
        client.post(vo.getOutOrgCode(), "bindBankRecord",  PinganCloudClientApiEnum.ACC_BIND_RECORD, body);
    }

    public boolean queryBindBankAmt(TradeOrgBankRelaVo vo) {
        // 金额验证，时间很长
        if (StringUtils.isEmpty(vo.getBankBindNo()) || vo.getBankBindNo().length() < 8) {
            return false;
        }
        LocalDate tranDate = PinganCloudUtil.dateToDate(vo.getBankBindNo().substring(0, 8));
        JSONObject body = new JSONObject();
        body.put("OldTranSeqNo", vo.getBankBindNo());
        body.put("TranDate", PinganCloudUtil.dateToStr(tranDate == null ? LocalDate.now() : tranDate));
        try {
            JSONObject resp = client.post(vo.getOutOrgCode(), "queryBindBankAmt", PinganCloudClientApiEnum.ACC_BIND_AMT_QUERY, body);
            // {"TxnReturnCode":"000000","ReturnMsg":"小额转账已发起且成功,等待客户回填金额","TxnReturnMsg":"交易成功","ReservedMsg":"查询成功","ReturnStatu":"0","CnsmrSeqNo":"2024071209470818135535"}
            //   (0：成功，1：失败，2：待确认)
            if ("1".equals(resp.getString("ReturnStatu"))) {
                throw new ServiceException(resp.getString("ReturnMsg"));
            }
            return true;
        } catch (ServiceException se) {
            if (se.getMessage().contains("[ERR020]")) {
                // {"ErrorCode":"ERR020","ErrorMessage":"无符合条件的小额转账鉴权申请记录"}
                log.keyword(vo.getOutOrgCode(), "queryBindBankAmt").warn("[ERR020] 转异常", se);
                return false;
            }
            throw se;
        }
    }

    public TradeOrgBankRelaBo unbindBank(TradeOrgBankRelaVo vo) {
        JSONObject body = new JSONObject();
        body.put("FunctionFlag", "1");
        body.put("TranNetMemberCode", vo.getOutOrgCode());
        body.put("SubAcctNo", vo.getOutAcctCode());
        body.put("MemberAcctNo", vo.getBankAccount()); //
        JSONObject resp = null;
        try {
            resp = client.post(vo.getOutOrgCode(), "unbindBank", PinganCloudClientApiEnum.ACC_BIND_REMOVE, body);
        } catch (ServiceException se) {
            if (se.getMessage().contains("[ERR020]")) {
                // {"ErrorCode":"ERR020","ErrorMessage":"无符合条件记录[ERR020]"}
                log.keyword(vo.getOutOrgCode(), "unbindBank").warn("[ERR020] 转异常", se);
            } else {
                throw se;
            }
        }
        TradeOrgBankRelaBo respBo = new TradeOrgBankRelaBo();
        respBo.setOrgCode(vo.getOrgCode());
        respBo.setOrgType(vo.getOrgType());
        respBo.setOutOrgType(vo.getOutOrgType());
        respBo.setOutOrgCode(vo.getOutOrgCode());
        respBo.setOutOrgProperty(vo.getOutOrgProperty());
        respBo.setBankAccount(vo.getBankAccount());
        respBo.setBankBindNo("");
        respBo.setOutBindNo("");
        respBo.setOutUnbindNo(resp == null ? "" : resp.getString("FrontSeqNo"));
        respBo.setOutUnbindTime(Convert.toDate(DateUtil.now()));
        respBo.setStatus(AccountOrgBindEnum.OPEN.getCode());
        return respBo;
    }

    /*
        仅验证是否绑卡， 返回值中只有绑定状态， 和银行卡绑定信息， 没有 bankFlag ！！！！！
     */
    public TradeOrgBankRelaBo queryBindBank(TradeOrgBankRelaVo vo) {
        JSONObject body = new JSONObject();
        body.put("PageNum", "1");
        body.put("QueryFlag", "2");
        body.put("SubAcctNo", vo.getOutAcctCode());
        JSONObject resp = client.post(vo.getOutOrgCode(), "queryBindBank", PinganCloudClientApiEnum.ACC_BIND_QUERY, body);
        if (resp.getString("ResultNum").equals("0")) {
            return null;
        }
        JSONArray arrays = resp.getJSONArray("TranItemArray");
        for (int i = 0; i < arrays.size(); i++) {
            JSONObject row = arrays.getJSONObject(i);
            // 可能多行 ！！！！！
            if (row.getString("TranNetMemberCode").equals(vo.getOutOrgCode())) {
                TradeOrgBankRelaBo respBo = toTradeOrgBankRela(row);
                respBo.setOrgCode(vo.getOrgCode());
                respBo.setOrgType(vo.getOrgType()); // 缓存使用
                respBo.setOutOrgCode(vo.getOutOrgCode()); // 主键使用
                respBo.setOutOrgProperty(vo.getOutOrgProperty());
                // respBo.setOutAcctCode(vo.getOutAcctCode());
                return respBo;
            }
        }
        return null;
    }

    // 验证是否绑卡
    public TableDataInfo<TradeOrgBankRelaBo> queryBindBank(int page) {
        JSONObject body = new JSONObject();
        body.put("PageNum", Integer.valueOf(page).toString());
        body.put("QueryFlag", "1");
        JSONObject resp = client.post(Integer.toString(page), "queryBindBank", PinganCloudClientApiEnum.ACC_BIND_QUERY, body);
        List<TradeOrgBankRelaBo> list = new ArrayList<>();
        JSONArray arrays = resp.getJSONArray("TranItemArray");
        for (int i = 0; i < arrays.size(); i++) {
            JSONObject row = arrays.getJSONObject(i);
            list.add(toTradeOrgBankRela(row));
        }
        Page<TradeOrgBankRelaBo> result = new Page<>(page, resp.getLongValue("TotalNum"), resp.getLongValue("ResultNum"));
        result.setRecords(list);
        return TableDataInfo.build(result);
    }

    private TradeOrgBankRelaBo toTradeOrgBankRela(JSONObject row) {
        TradeOrgBankRelaBo respBo = new TradeOrgBankRelaBo();
        respBo.setOutOrgCode(row.getString("TranNetMemberCode"));
        respBo.setOutAcctCode(row.getString("SubAcctNo"));
        String bankAccount = row.getString("MemberAcctNo");
        respBo.setBankAccount(bankAccount);
        if (StringUtils.isNotBlank(bankAccount)) {
            respBo.setStatus(AccountOrgBindEnum.BIND.getCode());
            respBo.setBankEicon(row.getString("EiconBankBranchId"));
            respBo.setBankMobile(row.getString("Mobile"));
            respBo.setBankAccName(row.getString("MemberName"));
            respBo.setBankBranchName(row.getString("AcctOpenBranchName"));
            respBo.setBankType(row.getString("BankType"));
            if (row.getString("MemberGlobalType").equals("989")) {
                // 内部子账号
                respBo.setOutOrgType(BankOrgTypeEnum.ACCT_CODE.getCode());
                respBo.setOutOrgName(row.getString("MemberName"));
                respBo.setCompanyGlobalId(row.getString("MemberGlobalId"));
            } else if (row.getString("MemberGlobalType").equals("73")) {
                // 当前的开户方式， 都是公司
                respBo.setCompanyName(row.getString("MemberName"));
                respBo.setCompanyGlobalId(row.getString("MemberGlobalId"));
            } else {
                // MemberGlobalType == "1" 是法人
                respBo.setReprName(row.getString("MemberName"));
                respBo.setReprGlobalId(row.getString("MemberGlobalId"));
            }
        } else {
            respBo.setStatus(AccountOrgBindEnum.OPEN.getCode());
        }
        return respBo;
    }

    /*
        创建同名
    */
    public TradeOrgBankRelaBo sameOrgBank(TradeOrgBankRelaVo vo) {
        vo.setCommOrgCode(vo.getOutOrgCode() + "C");
        JSONObject body = new JSONObject();
        body.put("FunctionFlag", "2"); // 2 申请 4 查询
        body.put("MerSubAcctMemberCode", vo.getOutOrgCode()); //
        body.put("CommonSubAcctMemberCode", vo.getCommOrgCode()); //
        JSONObject resp;
        try {
            resp = client.post(vo.getOutOrgCode(), "sameOrgBank", PinganCloudClientApiEnum.ACC_ORG_SAME, body);
        } catch (ServiceException se) {
            if (se.getMessage().contains("[E10016]")) {
                // {"ErrorCode":"E10018","ErrorMessage":"商户子账户会员代码已维护过同名户"}
                // {"ErrorCode":"E10016","ErrorMessage":"同名户绑定关系已存在"}
                log.keyword(vo.getOutOrgCode(), "sameOrgBank").warn("[E10016] 转异常", se);
                resp = JSONObject.parseObject(se.getDetailMessage());
            } else {
                throw se;
            }
        }
        return toTradeOrgBankRela(resp, vo);
    }
    /*
        查询同名
     */
    public TradeOrgBankRelaBo querySameOrgBank(TradeOrgBankRelaVo vo) {
        JSONObject body = new JSONObject();
        body.put("FunctionFlag", "4"); //  2 申请 4 查询
        body.put("MerSubAcctMemberCode", vo.getOutOrgCode()); //
        body.put("CommonSubAcctMemberCode", vo.getCommOrgCode());
        JSONObject resp = client.post(vo.getOutOrgCode(), "querySameOrgBank",  PinganCloudClientApiEnum.ACC_ORG_SAME, body);
        return toTradeOrgBankRela(resp, vo);
    }

    private TradeOrgBankRelaBo toTradeOrgBankRela(JSONObject resp,TradeOrgBankRelaVo vo) {
        TradeOrgBankRelaBo respBo = new TradeOrgBankRelaBo();
        respBo.setOrgCode(vo.getOrgCode());
        respBo.setOrgType(vo.getOrgType());
        respBo.setOutOrgCode(vo.getOutOrgCode());
        respBo.setOutOrgProperty(vo.getOutOrgProperty());
        respBo.setCommOrgCode(resp.getString("ReservedMsgTwo"));
        respBo.setCommAcctCode(resp.getString("CommonSubAcctNo"));
        return respBo;
    }

    public TradeOrgBankAuthVo getSameOrgDefaultParams() {
        TradeOrgBankAuthVo vo = new TradeOrgBankAuthVo();
        vo.setAuthSingleAmt(new BigDecimal(client.config.getAuthSingleAmt()));
        vo.setAuthTotalAmt(new BigDecimal(client.config.getAuthTotalAmt()));
        vo.setAuthValidDays(client.config.getAuthValidDays());
        return vo;
    }
    /*
        同名授权   可重复申请， AuthAcceptSeqNo 会变
     */
    public TradeOrgBankRelaAuthBo sameOrgAuth(TradeOrgBankRelaAuthVo vo) {
        if (vo.getAuthSingleAmt() == null) {
            vo.setAuthSingleAmt(new BigDecimal(client.config.getAuthSingleAmt()));
        }
        if (vo.getAuthTotalAmt() == null) {
            vo.setAuthTotalAmt(new BigDecimal(client.config.getAuthTotalAmt()));
        }
        if (vo.getAuthValidDays() == null) {
            vo.setAuthValidDays(client.config.getAuthValidDays());
        }
        if (StringUtils.isEmpty(vo.getCommAcctCode())) {
            throw new ServiceException("未绑定同名账户");
        }
        JSONObject body = new JSONObject();
        body.put("FunctionFlag", "1");  // 1:免密限额授权申请 2:免密限额授权回填OTP 3:免密限额授权状态查询
        body.put("MerSubAcctNo", vo.getOutAcctCode()); // 还是 TraderSubAcctNo ？？
        body.put("CommonSubAcctNo", vo.getCommAcctCode());
        body.put("MaxSinglePayAmt", String.valueOf(vo.getAuthSingleAmt()));
        body.put("MaxTotalPayAmt", String.valueOf(vo.getAuthTotalAmt()));
        body.put("AuthValidDate", String.valueOf(vo.getAuthValidDays()));
        body.put("CurReserverMobile", vo.getBankMobile());  // 送开户时的手机号 还是 绑卡的手机号 ？？？
        JSONObject resp = client.post(vo.getOutOrgCode(), "sameOrgAuth", PinganCloudClientApiEnum.ACC_ORG_SAME_LIMIT, body);
        // {"ErrorCode":"E10029","ErrorMessage":"手机号码不一致"}
        // resp.getString("OtpMobile")  // 短信验证码
        TradeOrgBankRelaAuthBo bo = toTradeOrgBankAuth(resp, vo);
        bo.setAuthAcceptNo(resp.getString("AuthAcceptSeqNo"));
        return bo;
    }
    /*
        同名授权短信验证
    */
    public TradeOrgBankRelaAuthBo checkSameOrgAuth(TradeOrgBankRelaAuthVo vo) {
        JSONObject body = new JSONObject();
        body.put("FunctionFlag", "2");  // 1:免密限额授权申请 2:免密限额授权回填OTP 3:免密限额授权状态查询
        body.put("MerSubAcctNo", vo.getOutAcctCode());
        body.put("CommonSubAcctNo", vo.getCommAcctCode());
        body.put("OldRecevSeqNo", vo.getAuthAcceptNo());
        body.put("MessageCheckCode", vo.getSmsCode());  // OtpMobile
        try {
            JSONObject resp = client.post(vo.getOutOrgCode(), "checkSameOrgAuth", PinganCloudClientApiEnum.ACC_ORG_SAME_LIMIT, body);
            // 验证后， resp 返回的 AuthApplyDate 中有值了
            return toTradeOrgBankAuth(resp, vo);
        } catch (ServiceException se) {
            if (se.getMessage().contains("[ERR020]")) {
                // {\"ErrorCode\":\"ERR020\",\"ErrorMessage\":\"该会员无最新待回填的授权申请记录或最新待回填记录已失效\"}
				se.setMessage("短信验证码已失效[ERR020]");
            }
            throw se;
        }

    }
    /*
        同名授权查询
    */
    public TradeOrgBankRelaAuthBo querySameOrgAuth(TradeOrgBankRelaAuthVo vo) {
        JSONObject body = new JSONObject();
        body.put("FunctionFlag", "3");
        body.put("TraderSubAcctNo", vo.getOutAcctCode()); // 还是 MerSubAcctNo
        body.put("CommonSubAcctNo", vo.getCommAcctCode());
        body.put("OldRecevSeqNo", vo.getAuthAcceptNo());
        JSONObject resp = client.post(vo.getOutOrgCode(), "querySameOrgAuth", PinganCloudClientApiEnum.ACC_ORG_SAME_LIMIT, body);
        return toTradeOrgBankAuth(resp, vo);
    }

    private TradeOrgBankRelaAuthBo toTradeOrgBankAuth(JSONObject resp, TradeOrgBankRelaAuthVo vo) {
        TradeOrgBankRelaAuthBo bo = new TradeOrgBankRelaAuthBo();
        bo.setOrgCode(vo.getOrgCode()); // 缓存使用
        bo.setOrgType(vo.getOrgType());
        bo.setOutOrgCode(vo.getOutOrgCode()); // 主键更新使用
        bo.setOutOrgProperty(vo.getOutOrgProperty());
        LocalDate authApplyDate = PinganCloudUtil.dateToDate(resp.getString("AuthApplyDate"));
        Integer authValidDays = Integer.parseInt(resp.getString("AuthValidDate"));
        bo.setAuthValidDate(authApplyDate == null ? null : authApplyDate.plusDays(authValidDays - 1));
        bo.setAuthValidDays(authValidDays);
        bo.setAuthTotalAmt(PinganCloudUtil.yuanToYuan(resp.getString("MaxTotalPayAmt")));
        bo.setAuthSingleAmt(PinganCloudUtil.yuanToYuan(resp.getString("MaxSinglePayAmt")));
        bo.setAuthAccumAmt(PinganCloudUtil.yuanToYuan(resp.getString("TotalPayAmt")));
        return bo;
    }
}
