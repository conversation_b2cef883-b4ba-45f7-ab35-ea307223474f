package cn.xianlink.trade.channel;

import cn.xianlink.trade.domain.bo.TradeAccCashBo;
import cn.xianlink.trade.domain.bo.TradeAccChargeBo;
import cn.xianlink.trade.domain.bo.TradeAccTransBo;
import cn.xianlink.trade.domain.bo.TradeAccTransferBo;
import cn.xianlink.trade.domain.bo.comm.TradeCommCashBo;
import cn.xianlink.trade.domain.bo.org.TradeOrgBankAmtBo;
import cn.xianlink.trade.domain.vo.*;
import cn.xianlink.trade.domain.vo.comm.TradeCommCashVo;
import cn.xianlink.trade.domain.vo.excel.ExcelDataInfo;
import cn.xianlink.trade.domain.vo.org.TradeOrgBankBindVo;
import cn.xianlink.trade.domain.vo.org.TradeOrgBankRelaVo;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Properties;

/**
 * 平安见证宝对接
 *
 * <AUTHOR>
 * @date 2024-05-27
*/

public interface AccountService {

    default void init(Properties properties) {}
    /*
     *  是否要分账操作
     */
    default String getDefaultOutCode(Integer orgType) {
        return null;
    };

    default String getMarketingAcctCode() {
        return null;
    }

    default String getServiceFeeAcctCode() {
        return null;
    }

    TradeOrgBankAmtBo queryBankAmt(String outOrgCode, String outAcctCode);

    Map<String, TradeOrgBankAmtBo> queryBankAmt();

    TradeOrgBankAmtBo queryBankHistoryAmt(String outOrgCode, String outAcctCode);

    List<TradeOrgBankAmtBo> queryBankHistoryAmt(String outOrgCode, String outAcctCode, LocalDate dateEnd);

    TradeAccTransferBo transferSource(TradeAccTransferVo vo, TradeOrgBankBindVo sourceBindVo);

    TradeAccTransferBo transferPay(TradeAccTransferVo vo, TradeOrgBankBindVo sourceBindVo,
                                  TradeOrgBankBindVo bindVo);

    TradeAccSplitVo splitQuery(String transNo, String splitNo);

    TradeAccSplitVo splitAdjust(String transNo, String splitNo);
    boolean splitSupply(String orderNo, String tradeNo, BigDecimal payAmt);
    <T> String fileUpload(ExcelDataInfo<T> data);
    <T> ExcelDataInfo<T> fileDownload(String fileType, LocalDate fileDate);

    TradeAccChargeBo recharge(TradeAccChargeVo vo);

    TradeAccChargeBo rechargeRefund(TradeAccChargeVo vo);

    TradeAccTransferBo transferOut(TradeAccTransferVo vo);

    TradeAccTransferBo transferOutRefund(TradeAccTransferVo vo);

    TradeAccTransferBo transferIn(TradeAccTransferVo vo);

    TradeAccTransferBo transferInRefund(TradeAccTransferVo vo);

    TradeAccTransBo discountPay(TradeAccTransVo vo);

    TradeAccTransBo discountRefund(TradeAccTransVo vo);

    TradeAccCashBo withdrawCash(TradeAccCashVo vo, TradeOrgBankRelaVo bvo);

    TradeAccCashBo withdrawQuery(TradeAccCashVo vo);

    TradeCommCashBo commCashExpire(TradeCommCashVo vo, TradeOrgBankRelaVo bvo);

}
