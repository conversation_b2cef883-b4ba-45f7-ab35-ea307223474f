package cn.xianlink.trade.domain;

import cn.xianlink.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 分账变更流水对象 trade_acc_avail_trans
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("trade_acc_avail_trans")
public class TradeAccAvailTrans extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id主键    //差额退
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 流水单id
     */
    private Long transId;
    /**
     * 账单日期
     */
    private LocalDate accountDate;
    /**
     * 销售日期
     */
    private LocalDate transDate;
    /**
     * 供应商id
     */
    private Long orgId;
    /**
     * 机构代码
     */
    private String orgCode;
    /**
     * 调整金额
     */
    private BigDecimal transAmt;
    /**
     * 佣金金额
     */
    private BigDecimal commissionAmt;
    /**
     * 0 未确认 1 已确认 2 超期确认
     */
    private Integer status;
    /**
     * 结算单id
     */
    private Long availId;
    /**
     * 结算单号
     */
    private String availNo;
    /**
     * 结算日期 （是对应销售日期的）
     */
    private LocalDate availDate;
    /**
     * 删除标志（0代表存在 存入这条数据的主键代表删除）
     */
    // @TableLogic(delval = "id")
    private Long delFlag;


}
