package cn.xianlink.trade.domain;

import cn.xianlink.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 账务总对象 trade_acc_account
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("trade_cust_account")
public class TradeCustAccount extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id主键
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 客户用户id
     */
    private Long customerId;
    /**
     * 昨日可提现金额
     */
    private BigDecimal ydaAvailAmt;
    /**
     * 待结算金额
     */
    private BigDecimal freezeAmt;

    /**
     * 可提现金额
     */
    private BigDecimal availAmt;
    /**
     * 处理到的trade_comm_trans.avail_date
     */
    private LocalDate commAvailDate;
    /**
     * 佣金待结算余额
     */
    private BigDecimal commFreezeAmt;

    /**
     * 佣金余额
     */
    private BigDecimal commAvailAmt;
    /**
     * 佣金提现金额
     */
    private BigDecimal commCashAmt;
    /**
     * 佣金提现待审核金额
     */
    private BigDecimal commCheckAmt;
    /**
     * 佣金过期金额
     */
    private BigDecimal commExpireAmt;
    /**
     * 发薪待结算余额
     */
    private BigDecimal salaFreezeAmt;
    /**
     * 发薪余额
     */
    private BigDecimal salaAvailAmt;
    /**
     * 发薪提现金额
     */
    private BigDecimal salaCashAmt;
    /**
     * 发薪提现待审核金额
     */
    private BigDecimal salaCheckAmt;
    /**
     * 发薪过期金额
     */
    private BigDecimal salaExpireAmt;
    /**
     * 发薪到账金额
     */
    private BigDecimal salaExpectedAmt;

    /**
     * 删除标志（0代表存在 存入这条数据的主键代表删除）
     */
    @TableLogic(delval = "id")
    private Long delFlag;


}
