package cn.xianlink.trade.mapper;

import cn.xianlink.common.mybatis.core.mapper.BaseMapperPlus;
import cn.xianlink.trade.domain.TradeAccCharge;
import cn.xianlink.trade.domain.vo.TradeAccChargeVo;
import org.apache.ibatis.annotations.Param;

/**
 * 数据文件Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
public interface TradeAccChargeMapper extends BaseMapperPlus<TradeAccCharge, TradeAccChargeVo> {

    TradeAccCharge queryChargeByRefund(@Param("chargeNo") String chargeNo);

}
