package cn.xianlink.trade.mapper;


import cn.xianlink.common.mybatis.core.mapper.BaseMapperPlus;
import cn.xianlink.trade.domain.TradeAccAccount;
import cn.xianlink.trade.domain.vo.TradeAccAccountVo;
import cn.xianlink.trade.domain.vo.TradeAccTransAcctVo;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 账务总Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
public interface TradeAccAccountMapper extends BaseMapperPlus<TradeAccAccount, TradeAccAccountVo> {

    TradeAccAccountVo queryOrgAccountSum(@Param("orgCode") String orgCode, @Param("orgType") Integer orgType, @Param("deptId") Long deptId);

    Page<TradeAccAccountVo> customSupAvailList(@Param("page") Page<TradeAccAccount> page, @Param("ew") Wrapper<TradeAccAccount> wrapper);

    List<TradeAccAccountVo> customSupAvailList(@Param("ew") Wrapper<TradeAccAccount> wrapper);

    Page<TradeAccAccountVo> customSupList(@Param("page") Page<TradeAccAccount> page, @Param("ew") Wrapper<TradeAccAccount> wrapper);

    List<TradeAccAccountVo> customSupList(@Param("ew") Wrapper<TradeAccAccount> wrapper);

    void insertTransAcctByAvail(@Param("updates") Map<String, String> updates, @Param("ids") List<Long> ids);

    void insertTransAcctByTrans(@Param("updates") Map<String, String> updates, @Param("ids") List<Long> ids);

    List<TradeAccAccountVo> queryExistAccount(@Param("vos") List<TradeAccTransAcctVo> vos);

    void insertNotExistAccount(@Param("vos") List<TradeAccTransAcctVo> vos);

    List<TradeAccAccountVo> _queryAccountAmt(@Param("ids") List<Long> ids, @Param("virtuals") List<String> virtuals);

    int _updateAccountAmt(@Param("vos") List<TradeAccAccountVo> accountVos);

    int updateAccountAmt(@Param("ids") List<Long> ids, @Param("virtuals") List<String> virtuals);
}
