package cn.xianlink.trade.domain.convert.pay;

import cn.xianlink.trade.api.domain.bo.OrderRefundBo;
import cn.xianlink.trade.domain.bo.TradePayRefundBo;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

/**
 * 数据转换器
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OrdeRefundBoConvert extends BaseMapper<OrderRefundBo, TradePayRefundBo> {

}
