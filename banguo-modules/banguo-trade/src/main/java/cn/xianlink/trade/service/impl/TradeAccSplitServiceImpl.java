package cn.xianlink.trade.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.xianlink.common.api.enums.trade.*;
import cn.xianlink.common.core.enums.YNStatusEnum;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.api.util.BaseEntityAutoFill;
import cn.xianlink.trade.constant.Constants;
import cn.xianlink.trade.domain.TradeAccSplit;
import cn.xianlink.trade.domain.TradeAccTrans;
import cn.xianlink.trade.domain.bo.TradeAccSplitAddBo;
import cn.xianlink.trade.domain.bo.TradeAccSplitUpdateBo;
import cn.xianlink.trade.domain.bo.TradeAccSupSplitQueryBo;
import cn.xianlink.trade.domain.vo.TradeAccSplitVo;
import cn.xianlink.trade.domain.vo.TradeAccTransVo;
import cn.xianlink.trade.domain.vo.TradePayRefundVo;
import cn.xianlink.trade.domain.vo.TradePayVo;
import cn.xianlink.trade.mapper.TradeAccSplitMapper;
import cn.xianlink.trade.mapper.TradeAccTransMapper;
import cn.xianlink.trade.service.ITradeAccSplitService;
import cn.xianlink.trade.service.biz.TradeBaseUtilBizService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 支付分账流水Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@RequiredArgsConstructor
@Service
public class TradeAccSplitServiceImpl implements ITradeAccSplitService {

    private final transient TradeAccSplitMapper baseMapper;
    private final transient TradeAccTransMapper tradeAccTransMapper;
    private final transient TradeBaseUtilBizService tradeBaseUtilBizService;

    @Override
    @BaseEntityAutoFill
    public TableDataInfo<TradeAccSplitVo> queryPageList(TradeAccSupSplitQueryBo bo) {
        LambdaQueryWrapper<TradeAccSplit> lqw = buildQueryWrapper(bo);
        Page<TradeAccSplitVo> result = baseMapper.selectVoPage(bo.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询划转流水列表
     */
    @Override
    @BaseEntityAutoFill
    public List<TradeAccSplitVo> queryList(TradeAccSupSplitQueryBo bo) {
        LambdaQueryWrapper<TradeAccSplit> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
        //         return MapstructUtils.convert(baseMapper.selectVoList(lqw), TradeAccSupSplitVo.class);
    }

    private LambdaQueryWrapper<TradeAccSplit> buildQueryWrapper(TradeAccSupSplitQueryBo bo) {
        boolean isTransDate = bo.getTransDateStart() != null && bo.getTransDateEnd() != null;
        boolean isAcctDate = bo.getAcctDateStart() != null && bo.getAcctDateEnd() != null;
        if (!isTransDate && !isAcctDate) {
            throw new ServiceException("销售日期或记账日期必须输入一组");
        }
        LambdaQueryWrapper<TradeAccSplit> lqw = Wrappers.lambdaQuery();
        lqw.in(TradeAccSplit::getTransType, bo.getTransTypes());
        lqw.between(isTransDate, TradeAccSplit::getTransDate, bo.getTransDateStart(), bo.getTransDateEnd());
        lqw.between(isAcctDate, TradeAccSplit::getAcctDate, bo.getAcctDateStart(), bo.getAcctDateEnd());
        lqw.eq(StringUtils.isNotBlank(bo.getChannel()), TradeAccSplit::getChannel, bo.getChannel());
        lqw.eq(StringUtils.isNotBlank(bo.getBusiType()), TradeAccSplit::getBusiType, bo.getBusiType());
        lqw.eq(StringUtils.isNotBlank(bo.getTransType()), TradeAccSplit::getTransType, bo.getTransType());
        lqw.eq(StringUtils.isNotBlank(bo.getTransNo()), TradeAccSplit::getTransNo, bo.getTransNo());
        lqw.eq(StringUtils.isNotBlank(bo.getSplitNo()), TradeAccSplit::getSplitNo, bo.getSplitNo());
        lqw.eq(StringUtils.isNotBlank(bo.getRelateNo()), TradeAccSplit::getRelateNo, bo.getRelateNo());
        lqw.eq(StringUtils.isNotBlank(bo.getSplitRelNo()), TradeAccSplit::getSplitRelNo, bo.getSplitRelNo());
        lqw.eq(StringUtils.isNotBlank(bo.getOutAcctCode()), TradeAccSplit::getOutAcctCode, bo.getOutAcctCode());
        lqw.in(CollectionUtil.isNotEmpty(bo.getOrgCodes()), TradeAccSplit::getOrgCode, bo.getOrgCodes());
        if (CollectionUtil.isNotEmpty(bo.getAcctOrgCodes())) {
            lqw.in(TradeAccSplit::getAcctOrgId, tradeBaseUtilBizService.queryIdsByCodes(bo.getAcctOrgCodes(), BaseTypeEnum.REGION_WH.getCode()));
        }
        if (CollectionUtil.isNotEmpty(bo.getTransOrgCodes())) {
            lqw.in(TradeAccSplit::getTransOrgId, tradeBaseUtilBizService.queryIdsByCodes(bo.getTransOrgCodes(), BaseTypeEnum.CITY_WH.getCode()));
        }
        lqw.eq(bo.getInfStatus() != null, TradeAccSplit::getInfStatus, bo.getInfStatus());
        lqw.eq(TradeAccSplit::getDelFlag, YNStatusEnum.DISABLE.getCode());
        lqw.orderByDesc(TradeAccSplit::getId);
        return lqw;
    }

    @Override
    public List<TradePayVo> queryPaySplitError(LocalDate acctDate) {
        return baseMapper.queryPaySplitError(acctDate);
    }

    @Override
    public List<TradePayVo> queryPaySplitDeleteError(LocalDate acctDate) {
        return baseMapper.queryPaySplitDeleteError(acctDate);
    }

    @Override
    public List<TradePayRefundVo> queryRefundSplitError(LocalDate acctDate) {
        return baseMapper.queryRefundSplitError(acctDate);
    }

    @Override
    public List<TradePayRefundVo> queryRefundSplitDeleteError(LocalDate acctDate) {
        return baseMapper.queryRefundSplitDeleteError(acctDate);
    }

    private Map<String, TradeAccSplit> toTradeAccSplit(TradeAccSplitAddBo addBo, List<TradeAccTrans> addBos) {
        /* // 暂不验证未删除的 split
        Set<String> splitNos = addBos.stream().map(TradeAccTrans::getSplitNo).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        if (splitNos.size() > 0 && baseMapper.exists(Wrappers.lambdaQuery(TradeAccSplit.class).in(TradeAccSplit::getSplitNo, splitNos))) {
            throw new ServiceException("分账记录已存在，不要再重试", R.FAIL);
        }*/
        // split 表中都记录成正数
        addBo.setTotalAmt(addBo.getTotalAmt().abs());
        addBo.setInitTime(addBo.getInitTime().compareTo(addBo.getInfTime()) > 0 ? addBo.getInfTime() : addBo.getInitTime());
        /* 佣金分帐计算
			订单分帐    供应商    splitAmt - transMinAmt
						佣金账户  transMinAmt
         */
        // 基于 splitRelNo 和 splitOrgCode 是一样的结果
        Map<String, List<TradeAccTrans>> acctTranss = addBos.stream().collect(Collectors.groupingBy(TradeAccTrans::getSplitOrgCode));
        // 基于 split_out_code 合计生成 split 表数据
        Map<String, TradeAccSplit> acctSplits = new HashMap<>();
        for (Map.Entry<String, List<TradeAccTrans>> entry: acctTranss.entrySet()) {
            TradeAccTrans trans = entry.getValue().get(0);
            TradeAccSplit split = new TradeAccSplit();
            BeanUtil.copyProperties(trans, split, Constants.BeanCopyIgnoreNullValue);
            BeanUtil.copyProperties(addBo, split, Constants.BeanCopyIgnoreNullValue);
            split.setId(null);
            split.setCreateTime(null);
            split.setTransAmt(entry.getValue().stream().map(vo -> vo.getSplitAmt().subtract(vo.getTransMinAmt())).reduce(BigDecimal.ZERO, BigDecimal::add));
            split.setCommissionAmt(entry.getValue().stream().map(TradeAccTrans::getCommissionAmt).reduce(BigDecimal.ZERO, BigDecimal::add));
            split.setFeeAmt(entry.getValue().stream().map(TradeAccTrans::getFeeAmt).reduce(BigDecimal.ZERO, BigDecimal::add));
            split.setOutOrgCode(trans.getSplitOrgCode());
            split.setOutAcctCode(trans.getSplitAcctCode());
            split.setInfStatus(RefundInfStatusEnum.PROCESSING.getCode()); // 统一设置成执行中
            if (split.getTransAmt().compareTo(BigDecimal.ZERO) < 0) {
                split.setTransAmt(split.getTransAmt().negate());
                split.setCommissionAmt(split.getCommissionAmt().negate());
                split.setFeeAmt(split.getFeeAmt().negate());
            } else if (split.getTransAmt().compareTo(BigDecimal.ZERO) == 0) {
                split.setInfStatus(RefundInfStatusEnum.SUCCESS.getCode());
                split.setInfReason("分帐金额为零，不处理");
            }
            acctSplits.put(split.getOutOrgCode(), split);
        }
        // 去掉了合计未零的数据
        return acctSplits;
    }

    // 订单插入分账
    @Override
    public List<TradeAccSplitVo> batchInsertByBo(boolean isSplit, TradeAccSplitAddBo addBo, List<TradeAccTrans> addBos) {
        List<TradeAccSplit> adds = null;
        if (isSplit) {
            Map<String, TradeAccSplit> acctSplits = toTradeAccSplit(addBo, addBos);
            adds = acctSplits.values().stream().toList();
            try {
                batchInsertSplitBos(adds);
            } catch (DuplicateKeyException dke) {
                batchInsertSplitBos(adds);  // 重试一次
            }
            addBos.forEach(trans -> {
                TradeAccSplit vo = acctSplits.get(trans.getSplitOrgCode());
                if (vo != null) { // splitAmt 可能为零， 造成没有对应分账单
                    trans.setSplitNo(vo.getSplitNo());
                }
            });
        }
        tradeAccTransMapper.insertBatch(addBos);
        return MapstructUtils.convert(adds, TradeAccSplitVo.class);
    }

    private void batchInsertSplitBos(List<TradeAccSplit> adds) {
        adds.forEach(bo -> {
            bo.setAcctDate(LocalDate.now());
            bo.setSplitNo(tradeBaseUtilBizService.getNextSplitNo(bo.getAcctDate()));
        });
        baseMapper.insertBatch(adds);
    }

    // 划转 退款 插入分账
    @Override
    public List<TradeAccSplitVo> batchInsertByBo(String orderNo, List<String> refundNos, boolean isSplit, TradeAccSplitAddBo addBo, List<TradeAccTrans> addBos) {
        List<TradeAccSplitVo> splitVos = batchInsertByBo(isSplit, addBo, addBos);
        if (orderNo != null) {
            TradeAccTransVo transVo = tradeAccTransMapper.queryPayCancelTrans(false, orderNo, refundNos).stream()
                    .filter(vo -> vo.getTransAmt().compareTo(BigDecimal.ZERO) < 0
                            || vo.getSplitAmt().compareTo(BigDecimal.ZERO) < 0
                            || vo.getCommissionAmt().compareTo(BigDecimal.ZERO) < 0
                            || vo.getTransAmt().compareTo(vo.getSplitAmt()) < 0
                            || vo.getTransAmt().compareTo(vo.getCommissionAmt()) < 0)
                    .findFirst().orElse(null);
            if (transVo != null) {
                String info = "";
                if (transVo.getCommissionAmt().compareTo(BigDecimal.ZERO) < 0) {
                    info = "佣金余额不足";
                } else if (transVo.getSplitAmt().compareTo(BigDecimal.ZERO) < 0) {
                    info = "实际退款余额不足";
                } else if (transVo.getTransAmt().compareTo(BigDecimal.ZERO) < 0) {
                    info = "退款余额不足";
                } else if (transVo.getTransAmt().compareTo(transVo.getSplitAmt()) < 0) {
                    info = "剩余实退金额不能大于剩余退款金额";
                } else if (transVo.getTransAmt().compareTo(transVo.getCommissionAmt()) < 0) {
                    info = "剩余佣金不能大于剩余退款金额";
                }
                throw new ServiceException(String.format("支付单 %s, %s(%s) %s", orderNo,
                        transVo.getSkuName() == null ? "" : transVo.getSkuName(), transVo.getSkuId(), info));
            }
        }
        return splitVos;
    }

    /**
     * 更新记账日志
     */
    @Override
    public List<TradeAccSplitVo> batchUpdateByNo(TradeAccSplitAddBo addBo, List<TradeAccTrans> updateBos) {
        Map<String, TradeAccSplit> acctSplits = toTradeAccSplit(addBo, updateBos);
        List<TradeAccSplit> updates = acctSplits.values().stream().toList();
        try {
            batchInsertSplitBos(updates);
        } catch (DuplicateKeyException dke) {
            batchInsertSplitBos(updates);  // 重试一次
        }
        List<TradeAccSplitVo> orderSplitVos = MapstructUtils.convert(updates, TradeAccSplitVo.class);
        Map<String, TradeAccSplitVo> tradeAccSplitVoMap = orderSplitVos.stream()
                .collect(Collectors.toMap(TradeAccSplitVo::getOutOrgCode, Function.identity()));
        List<TradeAccTrans> updateTrans = updateBos.stream().map(bo -> {
            TradeAccSplitVo splitVo = tradeAccSplitVoMap.get(bo.getSplitOrgCode());
            if (splitVo == null) { // splitAmt 可能为零， 造成没有对应分账单
                return null;
            }
            TradeAccTrans trans = new TradeAccTrans();
            trans.setId(bo.getId());
            trans.setSplitNo(splitVo.getSplitNo());
            return trans;
        }).filter(Objects::nonNull).toList();
        if (updateTrans.size() > 0) {
            tradeAccTransMapper.updateBatchById(updateTrans);
        }
        return orderSplitVos;
    }

    // 订单或退款，提现等一次更新
    @Override
    public boolean updateSplit(TradeAccSplitUpdateBo updateBo) {
        // accTrans.setSplitAmt(splitAmt); trans 记录有正负之分
        if (CollectionUtil.isEmpty(updateBo.getTransTypes())) {
            return false;
        }
        if (updateBo.getTransTypes().contains(AccountTransTypeEnum.OR.getCode())) {
            tradeAccTransMapper.updateRefundInfStatus(updateBo.getTransNo(), null);
        } else {
            tradeAccTransMapper.updateInfStatus(updateBo.getTransNo(), updateBo.getTransTypes(), null);
        }
        // 银行查询接口，也可能回执数据
        TradeAccSplit accSplit = new TradeAccSplit();
        accSplit.setInfStatus(updateBo.getInfStatus());
        accSplit.setOutTradeNo(updateBo.getOutTradeNo());
        if (updateBo.getOutSuccessTime() != null) {
            accSplit.setOutSuccessTime(updateBo.getOutSuccessTime());
            /* 临时使用 OutSuccessTime 作为对账日期， 最后统一使用对账文件中日期 */
            accSplit.setAcctDate(LocalDateTimeUtil.of(updateBo.getOutSuccessTime()).toLocalDate());
        }
        if (updateBo.getSplitAmt() != null) {
            accSplit.setTransAmt(updateBo.getSplitAmt());
        }
        return baseMapper.update(accSplit, Wrappers.lambdaUpdate(TradeAccSplit.class)
                .eq(TradeAccSplit::getTransNo, updateBo.getTransNo()).eq(TradeAccSplit::getDelFlag, 0)
                .in(TradeAccSplit::getTransType, updateBo.getTransTypes())) > 0;
    }

    /*
       其他 service 调用
     */
    @Override
    public void deleteSplit(boolean isTrans, TradeAccSplitUpdateBo updateBo) {
        baseMapper.update(Wrappers.lambdaUpdate(TradeAccSplit.class)
                        .setSql("del_flag=id").set(TradeAccSplit::getInfReason, updateBo.getInfReason())
                        .eq(TradeAccSplit::getTransNo, updateBo.getTransNo()).eq(TradeAccSplit::getDelFlag, 0)
                        .ne(TradeAccSplit::getInfStatus, RefundInfStatusEnum.SUCCESS.getCode())
                        .in(TradeAccSplit::getTransType, updateBo.getTransTypes()));
        if (isTrans) {
            deleteTrans(updateBo);
        }
    }

    // 订单或退款， 多次调用, 基于分账单号更新
    @Override
    public boolean updateSplit(TradeAccSplitUpdateBo updateBo, List<TradeAccSplitVo> splitVos) {
        // accTrans.setSplitAmt(splitAmt); trans 记录有正负之分
        if (CollectionUtil.isEmpty(updateBo.getTransTypes())) {
            return false;
        }
        List<String> splitNos = splitVos.stream().map(vo -> {
            if (vo.getOutSuccessTime() != null) {
                /* 临时使用 OutSuccessTime 作为对账日期， 最后统一使用对账文件中日期 */
                vo.setAcctDate(LocalDateTimeUtil.of(vo.getOutSuccessTime()).toLocalDate());
            }
            return vo.getSplitNo();
        }).toList();
        if (updateBo.getTransTypes().contains(AccountTransTypeEnum.OR.getCode())) {
            tradeAccTransMapper.updateRefundInfStatus(updateBo.getTransNo(), splitNos);
        } else {
            tradeAccTransMapper.updateInfStatus(updateBo.getTransNo(), updateBo.getTransTypes(), splitNos);
        }
        return splitVos.size() == 0 || baseMapper.updateSplitBatch(splitVos) > 0;
    }
    /*
       其他 service 调用
     */
    @Override
    public void deleteTrans(TradeAccSplitUpdateBo updateBo) {
        tradeAccTransMapper.delete(Wrappers.lambdaQuery(TradeAccTrans.class)
                .eq(TradeAccTrans::getTransNo, updateBo.getTransNo()).eq(TradeAccTrans::getDelFlag, 0)
                .in(TradeAccTrans::getTransType, updateBo.getTransTypes()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAccStatus(LocalDate acctDate, List<String> splitNos) {
        if (!CollectionUtil.isEmpty(splitNos)) {
            // splitNo 中有删除的也更新状态， 所以不加删除条件
            baseMapper.update(Wrappers.lambdaUpdate(TradeAccSplit.class).set(TradeAccSplit::getAcctDate, acctDate)
                    .set(TradeAccSplit::getAcctStatus, RefundInfStatusEnum.SUCCESS.getCode())
                    .in(TradeAccSplit::getSplitNo, splitNos));
        }
    }

}
