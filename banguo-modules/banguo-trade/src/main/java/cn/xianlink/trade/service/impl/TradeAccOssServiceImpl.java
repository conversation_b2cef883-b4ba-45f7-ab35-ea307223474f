package cn.xianlink.trade.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.trade.domain.TradeAccOss;
import cn.xianlink.trade.domain.bo.TradeAccOssBo;
import cn.xianlink.trade.domain.bo.TradeAccOssDownBo;
import cn.xianlink.trade.domain.bo.TradeAccOssQueryBo;
import cn.xianlink.trade.domain.vo.TradeAccOssStatusVo;
import cn.xianlink.trade.domain.vo.TradeAccOssVo;
import cn.xianlink.trade.mapper.TradeAccOssMapper;
import cn.xianlink.trade.service.ITradeAccOssService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;

/**
 * 数据文件Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@CustomLog
@RequiredArgsConstructor
@Service
public class TradeAccOssServiceImpl implements ITradeAccOssService {

    private final transient TradeAccOssMapper baseMapper;

    @Override
    public TradeAccOssVo queryByBo(TradeAccOssDownBo bo) {
        return baseMapper.selectVoOne(Wrappers.lambdaQuery(TradeAccOss.class)
                .eq(StringUtils.isNotBlank(bo.getMerchantId()), TradeAccOss::getMerchantId, bo.getMerchantId())
                .eq(TradeAccOss::getFileType, bo.getFileType()).eq(TradeAccOss::getTransDate, bo.getTransDate()));
    }

    @Override
    public TableDataInfo<TradeAccOssVo> queryPageList(TradeAccOssQueryBo bo) {
        LambdaQueryWrapper<TradeAccOss> lqw = buildQueryWrapper(bo);
        IPage<TradeAccOssVo> result = baseMapper.selectVoPage(bo.build(), lqw);
        return TableDataInfo.build(result);
    }

    @Override
    public List<TradeAccOssVo> queryList(TradeAccOssQueryBo bo) {
        LambdaQueryWrapper<TradeAccOss> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<TradeAccOss> buildQueryWrapper(TradeAccOssQueryBo bo) {
        LambdaQueryWrapper<TradeAccOss> lqw = Wrappers.lambdaQuery();
        lqw.ge(bo.getTransDateStart() != null, TradeAccOss::getTransDate, bo.getTransDateStart());
        lqw.le(bo.getTransDateEnd() != null, TradeAccOss::getTransDate, bo.getTransDateEnd());
        lqw.eq(StringUtils.isNotBlank(bo.getFileType()), TradeAccOss::getFileType, bo.getFileType());
        lqw.eq(bo.getAcctStatus() != null, TradeAccOss::getAcctStatus, bo.getAcctStatus());
        lqw.orderByDesc(TradeAccOss::getId);
        return lqw;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertByBo(TradeAccOssBo addBo) {
        TradeAccOss add = MapstructUtils.convert(addBo, TradeAccOss.class);
        return baseMapper.insert(add) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateAcctStatus(Long fileId, String fileType, LocalDate acctDate) {
        TradeAccOssStatusVo statusVo = baseMapper.queryAcctStatus(fileType, acctDate);
        TradeAccOss update = new TradeAccOss();
        update.setId(fileId);
        update.setAcctReason("");
        if (statusVo == null || statusVo.getCompleteCount() == null) {
            update.setAcctStatus(0);
        } else if (statusVo.getIncompleteCount() == 0 && statusVo.getDelCompleteCount() == 0) {
            update.setAcctStatus(1);
        } else {
            update.setAcctStatus(2);
            if (statusVo.getIncompleteCount() > 0) {
                update.setAcctReason(" 1 系统中有，平安端没有 " + statusVo.getIncompleteNos());
            }
            if (statusVo.getDelCompleteCount() > 0) {
                update.setAcctReason(update.getAcctReason() + " 2 系统中删除，平安端有 " + statusVo.getDelCompleteNos());
            }
            log.keyword(fileType, "accTransContrast").error(update.getAcctReason());
            update.setAcctReason(StrUtil.sub(update.getAcctReason(), 0, 450));
        }
        return baseMapper.updateById(update) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateByBo(TradeAccOssBo updateBo) {
        TradeAccOss update = MapstructUtils.convert(updateBo, TradeAccOss.class);
        return baseMapper.updateById(update) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(List<Long> fileIds) {
        return baseMapper.deleteBatchIds(fileIds) > 0;
    }
}
