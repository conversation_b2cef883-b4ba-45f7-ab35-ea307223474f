package cn.xianlink.trade.channel.pingancloud.constant;

import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.common.api.enums.trade.AccountBusinessFlagEnum;
import cn.xianlink.common.api.enums.trade.AccountOrgPropertyEnum;

public enum PinganCloudOrgTypeEnum {

    REGION_WH(0, "总仓", "R"),
    CITY_WH(1, "城市仓", "W"),
    SUPPLIER(2, "供应商", "S"),
    CUSTOMER(7, "客户", "C"),
    SYS_USER(8, "用户", "U"),

    ;

    private final Integer code;
    private final String desc;
    private final String prefix;

    PinganCloudOrgTypeEnum(Integer code, String desc, String prefix) {
        this.code = code;
        this.desc = desc;
        this.prefix = prefix;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public String getPrefix() {
        return prefix;
    }

    public static String getPrefix(Integer code) {
        for (PinganCloudOrgTypeEnum e : values()) {
            if (ObjectUtil.equal(e.getCode(), code)) {
                return e.getPrefix();
            }
        }
        return "";
    }

}
