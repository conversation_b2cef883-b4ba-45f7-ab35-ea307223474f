package cn.xianlink.trade.channel;


import cn.xianlink.trade.api.domain.bo.OrderPayBo;
import cn.xianlink.trade.api.domain.bo.OrderRefundBo;
import cn.xianlink.trade.domain.bo.TradePayBo;
import cn.xianlink.trade.domain.bo.TradePayRefundBo;
import cn.xianlink.trade.domain.vo.TradePayRefundVo;
import cn.xianlink.trade.domain.vo.TradePayVo;
import cn.xianlink.trade.domain.vo.excel.ExcelDataInfo;
import com.alibaba.fastjson.JSONObject;
import jakarta.servlet.http.HttpServletRequest;

import java.io.IOException;
import java.time.LocalDate;
import java.util.List;
import java.util.Properties;

/**
 * <AUTHOR>
 * @date 2024-05-27
 */
public interface PaymentService {

    default void init(String wxAppId, Properties properties) { };

    default List<String> getWxAppIds() { return null; };
    /*
     *  切换appId
     */
    default void switchover(String wxAppId) { };
    /*
     *  切换path, 用于回调
     */
    default String switchByPath(String path) {return null; };
    /*
     *  切换商户，用于下载文件
     */
    default void switchByMerchant(String merchantId) { };
    /*
     *  是否渠道每次都执行签名操作
     *  true  创建新单号， 重新签名    (因微信b2b没有关闭操作，每次单号未支付，只能重新生成个新的支付单号)
     *  false  先关闭，后重新创建
     */
    default boolean isAlwaysSign() {
        return false;
    };
    /*
     *  不同客户，商户号不同（用于线下）
     */
    default String getMerchantId() {
        return "";
    };
    /*
     *  退款校验
     */
    default int payValidate(OrderPayBo bo) {  return 0; };
    /*
     *  支付
     */
    TradePayBo pay(TradePayVo vo);
    /*
     *  支付查询，未找到不返回异常， 返回 null
     */
    TradePayBo payQuery(TradePayVo vo);
    /*
     *  支付关闭， 请求已关闭的不报异常， 返回状态
     */
    TradePayBo payClose(TradePayVo vo);
    /*
     *  回调
     */
    JSONObject callback(String reqBody, HttpServletRequest request) throws IOException;
    /*
     *  支付回调
     */
    TradePayBo payCallback(JSONObject resp);
    /*
     *  退款单生成前校验
     */
    default int refundValidate(OrderRefundBo bo, TradePayVo payVo) { return 0; };
    /*
     *  退款前前校验
     */
    default boolean refundValidate(TradePayRefundVo payVo) { return true; };
    /*
     *  退款
     */
    TradePayRefundBo refund(TradePayRefundVo vo);
    /*
     *  支付查询，未找到不返回异常， 返回 null
     */
    TradePayRefundBo refundQuery(TradePayRefundVo vo);

    /*
     *  文件下载
     */
    default <T> ExcelDataInfo<T> fileDownload(String fileType, LocalDate fileDate) { return null; };

    /*
     *  分账方式，  0  不手工分账（平安微信支付）   1 平安大额 （6034，6164 单条分账）  2 其他支付 （6216，6217 每20条一次分账）
     */
    default int getSplitCount() {
        return 0;
    };

    default void paySplit(String merchantNo, TradePayVo vo) {};

    default void refundSplit(String outChannelNo, TradePayRefundVo vo) {};
}
