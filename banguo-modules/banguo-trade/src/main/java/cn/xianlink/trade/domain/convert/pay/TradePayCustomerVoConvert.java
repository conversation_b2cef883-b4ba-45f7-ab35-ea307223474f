package cn.xianlink.trade.domain.convert.pay;

import cn.xianlink.trade.domain.vo.TradePayCustomerVo;
import cn.xianlink.trade.domain.vo.TradePayVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * 数据转换器
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TradePayCustomerVoConvert {

    TradePayCustomerVoConvert INSTANCE = Mappers.getMapper(TradePayCustomerVoConvert.class);

    @Mappings({
            @Mapping(source = "paySplitAmt", target = "payAmt"),
            @Mapping(source = "refundSplitAmt", target = "refundAmt"),
            @Mapping(source = "refundSplitAmt1", target = "refundAmt1"),
            @Mapping(source = "refundSplitAmt2", target = "refundAmt2"),
            @Mapping(source = "refundSplitAmt3", target = "refundAmt3"),
            @Mapping(source = "refundSplitAmt4", target = "refundAmt4"),
    })
    TradePayCustomerVo toConvert(TradePayVo bo);

}
