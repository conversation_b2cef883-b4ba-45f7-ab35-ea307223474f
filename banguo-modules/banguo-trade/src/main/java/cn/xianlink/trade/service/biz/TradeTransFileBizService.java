package cn.xianlink.trade.service.biz;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.io.LineHandler;
import cn.hutool.core.text.csv.CsvReader;
import cn.hutool.core.text.csv.CsvRow;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpUtil;
import cn.xianlink.bi.api.IRemoteBiTradeService;
import cn.xianlink.bi.api.domain.vo.RemoteOrderPayVo;
import cn.xianlink.common.api.enums.trade.*;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.resource.api.RemoteFileService;
import cn.xianlink.resource.api.domain.RemoteFile;
import cn.xianlink.trade.api.domain.bo.OrderPayQueryBo;
import cn.xianlink.trade.channel.AccountFileTypes;
import cn.xianlink.trade.channel.AccountService;
import cn.xianlink.trade.channel.ChannelsContext;
import cn.xianlink.trade.channel.PaymentService;
import cn.xianlink.trade.channel.pingancloud.PinganCloudUtil;
import cn.xianlink.trade.constant.TradeCacheNames;
import cn.xianlink.trade.domain.TradeAccDaily;
import cn.xianlink.trade.domain.bo.*;
import cn.xianlink.trade.domain.vo.*;
import cn.xianlink.trade.domain.vo.comm.TradeCommCashVo;
import cn.xianlink.trade.domain.vo.excel.*;
import cn.xianlink.trade.domain.vo.org.TradeAccDailyAmtVo;
import cn.xianlink.trade.domain.vo.org.TradeAccDailyVo;
import cn.xianlink.trade.dubbo.RemotePaymentServiceImpl;
import cn.xianlink.trade.service.*;
import cn.xianlink.trade.utils.ZipFileUtils;
import com.baomidou.lock.annotation.Lock4j;
import com.google.common.collect.Lists;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 机构子账户绑定Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@CustomLog
@RequiredArgsConstructor
@Service
public class TradeTransFileBizService {

    private final static String CZ_FILE_OR_TYPE = "在途充值撤销";
    private final static String CZ_FILE_PAY_TYPE = "订单支付模式入金";
    private final static String TX_FILE_REFUND_TYPE = "收款编码退款";
    private final transient ITradeAccOssService tradeAccOssService;
    private final transient ITradeOrgBankService tradeOrgBankService;
    private final transient ITradeAccSplitService tradeAccSplitService;
    private final transient ITradeAccTransService tradeAccTransService;
    private final transient ITradePayService tradePayService;
    private final transient ITradePayRefundService tradePayRefundService;
    private final transient ITradePayRecvService tradePayRecvService;
    private final transient ITradeAccTransferService tradeAccTransferService;
    private final transient ITradeAccAvailService tradeAccAvailService;
    private final transient ITradeAccDailyService tradeAccDailyService;
    private final transient ITradeCommCashService tradeCommCashService;
    private final transient AccountService accountService;
    private final transient ChannelsContext channelsContext;
    private final transient RemotePaymentServiceImpl remotePaymentService;

    @DubboReference(timeout = 300000)
    private final transient RemoteFileService remoteFileService;
    @DubboReference(timeout = 300000)
    private final transient IRemoteBiTradeService remoteBiTradeService;

    /*
        cos下载并对账
     */
    public <T> ExcelDataInfo<T> createExcelData(TradeAccOssDownBo downBo) {
        ExcelDataInfo<T> data = downBo.isForce() ? fileDownload(downBo) : ossDownload(downBo);
        if (data == null) {
            throw new ServiceException(String.format("%s 没有%s", downBo.getTransDate(),
                    AccountFileTypeEnum.getDescByCode(downBo.getFileType())));
        }
        return readExcelData(data);
    }

    /*
        文件下载并上传cos + 对账 (锁定 5 分钟)
            平安下载      8 - 9 点
            微信b2b      11 点后
     */
    @Lock4j(name= TradeCacheNames.LOCK_CLOUD_DOWNLOAD, keys = "#downBo.transDate + '_' + #downBo.fileType", expire = 300000, acquireTimeout = 1000)
    public <T> void cloudDownload(TradeAccOssDownBo downBo) {
        log.keyword(downBo.getFileType(), "accTransContrast").info("开始");
        ExcelDataInfo<T> data = ossDownload(downBo);
        if (data != null && !downBo.isForce()) {
            if (!downBo.isReValid()) {
                return;
            }
        } else {
            if (data != null && data.getFileId() != null) {
                tradeAccOssService.deleteByIds(List.of(data.getFileId()));
            }
            log.keyword(downBo.getFileType(), "accTransContrast").info("下载调用");
            data = fileDownload(downBo);
        }
        if (data != null) {
            try {
                accTransContrast(data);
                log.keyword(downBo.getFileType(), "accTransContrast").info("对比完成");
            } catch (InterruptedException ie) {
                log.keyword(downBo.getFileType(), "accTransContrast").error("对比完成", ie);
            }
        }
    }

    private <T> void accTransContrast(ExcelDataInfo<T> data) throws InterruptedException {
        /*
            对账异常的两种情况： trade_acc_oss.acct_status = 2 对应备注中的两种情况，以下两种情况平安测试环境都出现过！
                1 系统中有，平安端没有的情况 （可调用补账6147接口）
                    1 CZ 文件：  原因是 云收款支付成功，见证支付失败
                    2
                2 系统中删除，平安端有的情况 （只能联系平安运维）
                    1 TX 文件：  原因是 云收款退款失败，见证退款成功
                    2
         */
        List<T> lists = readExcelData(data).getList();
        switch (Objects.requireNonNull(AccountFileTypeEnum.getEnumByCode(data.getFileType()))) {
            case WJY -> {
                // 微信b2b
                List<WJYExcelColumnVo> columnVos = (List<WJYExcelColumnVo>) lists;
                if (CollectionUtil.isNotEmpty(columnVos)) {
                    List<WJYExcelColumnVo> payVos = columnVos.stream().filter(vo -> "SUCCESS".equals(vo.getStatus())).toList();
                    List<WJYExcelColumnVo> refundVos = columnVos.stream().filter(vo -> "REFUND".equals(vo.getStatus())).toList();
                    List<String> acctReason = new ArrayList<>();
                    updatePayAcctStatus(data.getTransDate(), payVos.stream().map(vo->
                            new TradePayBo().setTradeNo(vo.getTradeNo())
                                    .setOutFeeAmt(vo.getServiceFeeAmt()).setOutSuccessTime(vo.getTransTime())).toList());
                    updateRefundAcctStatus(data.getTransDate(), refundVos.stream().map(vo->
                            new TradePayRefundBo().setTradeRefundNo(vo.getTradeRefundNo())
                                    .setOutFeeAmt(vo.getServiceFeeAmt().abs()).setOutSuccessTime(vo.getTransTime())).toList());
                    // 2 更新后，从数据库查询一次
                    List<TradePayVo> tradePayVos = queryDayPayData(payVos.stream().map(WJYExcelColumnVo::getTradeNo).toList());
                    List<TradePayRefundVo> tradeRefundVos = queryDayRefundData(refundVos.stream().map(WJYExcelColumnVo::getTradeRefundNo).toList());
                    // 3 上传文件
                    if (StringUtils.isBlank(data.getUploadSeqNo())) {
                        createUploadFile(data, tradePayVos, tradeRefundVos);
                        RemoteFile remoteFile = remoteFileService.upload(data.getFileName(), data.getFileName(), ContentType.OCTET_STREAM.getValue(), data.getFile());
                        tradeAccOssService.updateByBo(new TradeAccOssBo().setId(data.getFileId()).setUploadOssId(remoteFile.getOssId()).setUploadOssUrl(remoteFile.getUrl()));
                        String uploadSeqNo = accountService.fileUpload(data);
                        tradeAccOssService.updateByBo(new TradeAccOssBo().setId(data.getFileId()).setUploadSeqNo(uploadSeqNo));
                    }
                    // 4 验证数据
                    validWeixinB2bOccurAmtError(data.getTransDate(), tradePayVos, tradeRefundVos, payVos, refundVos, acctReason);
                    for (String reason : acctReason) {
                        log.keyword(data.getFileType(), "accTransContrast").error(data.getTransDate() + " " + reason);
                    }
                    tradeAccOssService.updateByBo(new TradeAccOssBo().setId(data.getFileId()).setAcctStatus(acctReason.size() > 0 ? 2 : 1)
                            .setAcctReason(String.join(";", acctReason.stream().map(ss -> (ss.length() > 80 ? ss.substring(0, 79) : ss)).toList())));
                } else {
                    tradeAccOssService.updateByBo(new TradeAccOssBo().setId(data.getFileId()).setAcctStatus(1).setAcctReason("无数据"));
                }
            }
//            case LCZ -> {
//                // 拉卡拉
//                List<LCZExcelColumnVo> columnVos = (List<LCZExcelColumnVo>) lists;
//                if (CollectionUtil.isNotEmpty(columnVos)) {
//
//                }
//            }
            case CZ -> {
                // 见证+收单充值（OP 含分账） ， 在途充值（OP） ， 在途充值撤销（OR），  充值（）       订单支付模式入金（trade_pay）
                List<String> splitNos = ((List<CZExcelColumnVo>) lists).stream()
                        .map(vo -> CZ_FILE_OR_TYPE.equals(vo.getBusiType()) ? vo.getOutTradeNo() : vo.getSplitNo())
                        .filter(StringUtils::isNotBlank).distinct().toList();
                updateAcctStatus(data.getFileId(), data.getFileType(), data.getTransDate(), splitNos);
                updatePayAcctStatus(data.getTransDate(),  ((List<CZExcelColumnVo>) lists).stream().filter(vo -> CZ_FILE_PAY_TYPE.equals(vo.getBusiType()))
                        .map(vo-> new TradePayBo().setTradeNo(vo.getSplitNo()).setOutFeeAmt(BigDecimal.ZERO).setOutSuccessTime(vo.getTransTime())).toList());
                updatePayRecvAcctStatus(data.getTransDate(),  ((List<CZExcelColumnVo>) lists).stream().filter(vo -> CZ_FILE_PAY_TYPE.equals(vo.getBusiType()))
                        .map(vo-> new TradePayRecvBo().setRecvNo(vo.getSplitNo()).setOutSuccessTime(vo.getTransTime())).toList());
            }
            case TX -> {
                // 平安退款（OR） 和 提现(CH)
                List<String> splitNos = ((List<TXExcelColumnVo>) lists).stream()
                        .map(TXExcelColumnVo::getSplitNo).filter(StringUtils::isNotBlank).distinct().toList();
                updateAcctStatus(data.getFileId(), data.getFileType(), data.getTransDate(), splitNos);
                updateRefundAcctStatus(data.getTransDate(),  ((List<TXExcelColumnVo>) lists).stream().filter(vo -> TX_FILE_REFUND_TYPE.equals(vo.getBusiType()))
                        .map(vo-> new TradePayRefundBo().setTradeRefundNo(vo.getSplitNo()).setOutFeeAmt(BigDecimal.ZERO).setOutSuccessTime(vo.getTransTime())).toList());
            }
            case JY -> {
                // 6种类型, 营销账户充值和退款（CI ，CO），  营销转帐和退款（TI， DI， DO），  转账和转账退款（TI，TD，暂无）
                List<String> splitNos = ((List<JYExcelColumnVo>) lists).stream()
                        .map(JYExcelColumnVo::getSplitNo).filter(StringUtils::isNotBlank).distinct().toList();
                updateAcctStatus(data.getFileId(), data.getFileType(), data.getTransDate(), splitNos);
            }
            case YE -> {
                List<YEExcelColumnVo> columnVos = (List<YEExcelColumnVo>) lists;
                List<String> acctReason = new ArrayList<>();
                validBankDayOccurAmtError(data.getTransDate(), columnVos, acctReason); // 比较两日YE文件供应商差额， 与分账表对比
                validTradeProcessingError(data.getTransDate(), acctReason); // 验证未执行完成的数据
                for (String reason : acctReason) {
                    log.keyword(data.getFileType(), "accTransContrast").error(data.getTransDate() + " " + reason);
                }
                tradeAccOssService.updateByBo(new TradeAccOssBo().setId(data.getFileId()).setAcctStatus(acctReason.size() > 0 ? 2 : 1)
                        .setAcctReason(String.join(";", acctReason.stream().map(ss -> (ss.length() > 80 ? ss.substring(0, 79) : ss)).toList())));
            }
        }
    }

    private void validWeixinB2bOccurAmtError(LocalDate fileDate, List<TradePayVo> tradePayVos, List<TradePayRefundVo> tradeRefundVos,
                                             List<WJYExcelColumnVo> payVos, List<WJYExcelColumnVo> refundVos, List<String> acctReason) {
        // 1 更新后，从数据库查询一次
        // List<TradePayVo> tradePayVos = queryDayPayData(payVos.stream().map(WCZExcelColumnVo::getTradeNo).toList());
        Map<String, TradePayVo> tradePayMap = tradePayVos.stream().collect(Collectors.toMap(TradePayVo::getTradeNo, Function.identity()));
        // 对比总金额
        BigDecimal datePayTotalAmt = payVos.stream().map(WJYExcelColumnVo::getPayAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal tradePayTotalAmt = tradePayVos.stream().map(TradePayVo::getPaySplitAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
        List<String> payNos = tradePayVos.stream().filter(vo ->
                !((vo.getDelFlag() == 0 && PayInfStatusEnum.SUCCESS.getCode().equals(vo.getPayInfStatus()))
                        || (vo.getRefundSeq() == 10 && PayInfStatusEnum.CANCEL.getCode().equals(vo.getPayInfStatus())))).map(TradePayVo::getOrderNo).toList();
        List<String> wxPayNos = payVos.stream().map(WJYExcelColumnVo::getTradeNo).filter(tradeNo -> !tradePayMap.containsKey(tradeNo)).toList();
        if (datePayTotalAmt.compareTo(tradePayTotalAmt) != 0 || payNos.size() > 0 || wxPayNos.size() > 0) {
            acctReason.add(String.format("%s %s,%s; 本地=%s; 微信=%s", "1支付", datePayTotalAmt, tradePayTotalAmt, String.join(";", payNos), String.join(";", wxPayNos)));
        }
        // List<TradePayRefundVo> tradeRefundVos = queryDayRefundData(refundVos.stream().map(WCZExcelColumnVo::getTradeRefundNo).toList());
        Map<String, TradePayRefundVo> tradeRefundMap = tradeRefundVos.stream().collect(Collectors.toMap(TradePayRefundVo::getTradeRefundNo, Function.identity()));
        BigDecimal dateRefundTotalAmt = refundVos.stream().map(WJYExcelColumnVo::getRefundAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal tradeRefundTotalAmt = tradeRefundVos.stream().map(TradePayRefundVo::getRefundSplitAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
        List<String> refundNos = tradeRefundVos.stream().filter(vo ->
                !((vo.getDelFlag() == 0 || RefundBusiTypeEnum.REFUND_EXCEPTION.getCode().equals(vo.getBusiType()))
                        && RefundInfStatusEnum.SUCCESS.getCode().equals(vo.getRefundInfStatus()))).map(TradePayRefundVo::getRefundNo).toList();
        List<String> wxRefundNos = refundVos.stream().map(WJYExcelColumnVo::getTradeRefundNo).filter(tradeRefundNo -> !tradeRefundMap.containsKey(tradeRefundNo)).toList();
        if (dateRefundTotalAmt.compareTo(tradeRefundTotalAmt) != 0 || refundNos.size() > 0 || wxRefundNos.size() > 0) {
            acctReason.add(String.format("%s %s,%s; 本地=%s; 微信=%s", "2退款", dateRefundTotalAmt, tradeRefundTotalAmt, String.join(";", refundNos), String.join(";", wxRefundNos)));
        }
        List<WJYExcelColumnVo> refundIngVos = refundVos.stream().filter(vo -> "PROCESSING".equals(vo.getRefundStatus())).toList();
        if (refundIngVos.size() > 0) {
            acctReason.add(String.format("%s %s", "3退款中", String.join(";", refundIngVos.stream()
                    .map(WJYExcelColumnVo::getTradeRefundNo).distinct().toList())));
        }
        payNos = tradePayVos.stream().filter(vo -> vo.getFeeAmt().compareTo(vo.getOutFeeAmt()) < 0
                && PayInfStatusEnum.SUCCESS.getCode().equals(vo.getPayInfStatus())
                && !AccountStatusEnum.CANCEL.getCode().equals(vo.getStatus())).map(TradePayVo::getOrderNo).toList();
        if (CollectionUtil.isNotEmpty(payNos)) {
            acctReason.add(String.format("%s %s", "4手续费", String.join(";", payNos)));
        }
        List<TradePayVo> payErrors = tradeAccSplitService.queryPaySplitError(fileDate);
        if (CollectionUtil.isNotEmpty(payErrors)) {
            acctReason.add(String.format("%s %s", "5支付分账", String.join(";", payErrors.stream().map(TradePayVo::getOrderNo).toList())));
        }
        payErrors = tradeAccSplitService.queryPaySplitDeleteError(fileDate);
        if (CollectionUtil.isNotEmpty(payErrors)) {
            acctReason.add(String.format("%s %s", "6支付分账", String.join(";", payErrors.stream().map(TradePayVo::getOrderNo).toList())));
        }
        List<TradePayRefundVo> refundErrors = tradeAccSplitService.queryRefundSplitError(fileDate);
        if (CollectionUtil.isNotEmpty(refundErrors)) {
            acctReason.add(String.format("%s %s", "7退款分账", String.join(";", refundErrors.stream().map(TradePayRefundVo::getRefundNo).toList())));
        }
        refundErrors = tradeAccSplitService.queryRefundSplitDeleteError(fileDate);
        if (CollectionUtil.isNotEmpty(refundErrors)) {
            acctReason.add(String.format("%s %s", "8退款分账", String.join(";", refundErrors.stream().map(TradePayRefundVo::getRefundNo).toList())));
        }
    }

    private <T> void validBankDayOccurAmtError(LocalDate acctDate, List<YEExcelColumnVo> list, List<String> acctReason) {
        Map<String, List<YEExcelColumnVo>> listMap = list.stream().collect(Collectors.groupingBy(YEExcelColumnVo::getOutOrgCode));
        Map<String, Long> dailyMap = insertDailyBatch(acctDate, listMap.keySet().stream().toList());
        updateDailyBatch(listMap.values().stream().filter(vo -> dailyMap.containsKey(vo.get(0).getOutOrgCode()))
                .map(vo -> new TradeAccDaily().setId(dailyMap.get(vo.get(0).getOutOrgCode())).setBalanceAmt(vo.get(0).getAvailAmt())).toList());
        updateYesterdayBankAvailAmt(listMap.values().stream().filter(vo -> dailyMap.containsKey(vo.get(0).getOutOrgCode()))
                .map(vo -> new TradeAccDailyVo().setAcctDate(acctDate).setOutOrgCode(vo.get(0).getOutOrgCode()).setBalanceAmt(vo.get(0).getAvailAmt())).toList()); // 更新各各供应商，昨日余额
        // 1 前日YE余额
        ExcelDataInfo<T> preData = ossDownload(new TradeAccOssDownBo().setFileType(AccountFileTypeEnum.YE.getCode()).setTransDate(acctDate.minusDays(1)));
        if (preData != null) {
            List<YEExcelColumnVo> preList = (List<YEExcelColumnVo>) readExcelData(preData).getList();
            Map<String, List<YEExcelColumnVo>> preListMap = preList.stream().collect(Collectors.groupingBy(YEExcelColumnVo::getOutOrgCode));
            updateDailyBatch(preListMap.values().stream().filter(vo -> dailyMap.containsKey(vo.get(0).getOutOrgCode()))
                    .map(vo -> new TradeAccDaily().setId(dailyMap.get(vo.get(0).getOutOrgCode())).setYdaBalanceAmt(vo.get(0).getAvailAmt())).toList());
        }
        List<TradeAccDailyAmtVo> dailyAmtVos = tradeAccDailyService.queryDailyAmt(acctDate);
        // 2 处理营销账户和手续费对账
        String marketingAcctCode = accountService.getMarketingAcctCode();
        TradeAccDailyAmtVo marketingVo = dailyAmtVos.stream().filter(vo -> marketingAcctCode.equals(vo.getOutOrgCode())).findFirst().orElse(null);
        if (marketingVo == null) {
            marketingVo = getDailyVo(acctDate, marketingAcctCode);
            dailyAmtVos.add(marketingVo);
        }
        TradeAccDailyAmtVo serviceFeeVo = getDailyVo(acctDate, accountService.getServiceFeeAcctCode());
        dailyAmtVos.add(serviceFeeVo);
        // 2.1 营销子账户对账 = 营销子账户日充值 - 其他子账户的日发生额
        BigDecimal marketInAmt = dailyAmtVos.stream().map(TradeAccDailyAmtVo::getInAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal marketOtherAmt = dailyAmtVos.stream().map(TradeAccDailyAmtVo::getMarketAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal marketPayAmt = dailyAmtVos.stream().map(TradeAccDailyAmtVo::getMarketPayAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal marketRefundAmt = dailyAmtVos.stream().map(TradeAccDailyAmtVo::getMarketRefundAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 2.2 处理手续费账户对账
        BigDecimal feeCashFeeAmt = dailyAmtVos.stream().map(TradeAccDailyAmtVo::getCashFeeAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal feeOutAmt = dailyAmtVos.stream().map(TradeAccDailyAmtVo::getOutAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 2.3 设置到特殊账户中
        marketingVo.setPayAmt(marketPayAmt.negate()).setRefundAmt(marketRefundAmt.negate()).setInAmt(marketOtherAmt).setOutAmt(marketInAmt.negate());
        serviceFeeVo.setInAmt(feeOutAmt.negate()).setOutAmt(feeCashFeeAmt);
        updateDailyBatch(dailyAmtVos.stream().map(vo -> new TradeAccDaily()
                .setId(dailyMap.get(vo.getOutOrgCode())).setMarketAmt(vo.getMarketPayAmt().add(vo.getMarketRefundAmt()))
                .setOutAmt(vo.getOutAmt()).setInAmt(vo.getInAmt()).setPayAmt(vo.getPayAmt()).setRefundAmt(vo.getRefundAmt())
                .setRefundAmt1(vo.getRefundAmt().subtract(vo.getRefundAmt2()).subtract(vo.getRefundAmt3()))
                .setRefundAmt2(vo.getRefundAmt2()).setRefundAmt3(vo.getRefundAmt3()).setCashAmt(vo.getCashAmt())
                .setOccurAmt(vo.getInAmt().add(vo.getOutAmt()).add(vo.getPayAmt()).add(vo.getRefundAmt())
                        .add(vo.getCashAmt()).add(vo.getMarketPayAmt().add(vo.getMarketRefundAmt())))
                .setCommissionAmt(vo.getCommPayAmt().add(vo.getCommRefundAmt())) // 佣金不合计到 OccurAmt 中
        ).toList());
        List<TradeAccDailyVo> dailyVos = tradeAccDailyService.queryDailyAmtError(acctDate);
        if (CollectionUtil.isNotEmpty(dailyVos)) {
            for (TradeAccDailyVo dailyVo: dailyVos) {
                log.keyword(AccountFileTypeEnum.YE.getCode(), "accTransContrast").error(
                        String.format("供应商分账 outOrgCode=%s transDate=%s OccurAmt=%s YEDiffAmt=%s",
                                dailyVo.getOutOrgCode(), acctDate, dailyVo.getOccurAmt(), dailyVo.getBalanceAmt().subtract(dailyVo.getYdaBalanceAmt())));
            }
            List<String> errOutOrgCodes = dailyVos.stream().map(TradeAccDailyVo::getOutOrgCode).toList();
            acctReason.add(String.format("%s %s", "1日差额", String.join(";", errOutOrgCodes)));
        }
    }

    private TradeAccDailyAmtVo getDailyVo(LocalDate acctDate, String outOrgCode) {
        return new TradeAccDailyAmtVo().setAcctDate(acctDate).setOutOrgCode(outOrgCode)
                .setMarketAmt(BigDecimal.ZERO).setMarketPayAmt(BigDecimal.ZERO).setMarketRefundAmt(BigDecimal.ZERO)
                .setCommPayAmt(BigDecimal.ZERO).setCommRefundAmt(BigDecimal.ZERO)
                .setOccurAmt(BigDecimal.ZERO).setPayAmt(BigDecimal.ZERO).setRefundAmt(BigDecimal.ZERO)
                .setRefundAmt1(BigDecimal.ZERO).setRefundAmt2(BigDecimal.ZERO).setRefundAmt3(BigDecimal.ZERO).setRefundAmt4(BigDecimal.ZERO)
                .setInAmt(BigDecimal.ZERO).setOutAmt(BigDecimal.ZERO).setCashAmt(BigDecimal.ZERO).setCashFeeAmt(BigDecimal.ZERO);
    }

    private <T> void validTradeProcessingError(LocalDate fileDate, List<String> acctReason) {
        // 预先处理昨日未支付完成的状态
        tradePayService.queryPayFailed(fileDate).stream()
                .filter(vo -> PayInfStatusEnum.UNPAID.getCode().equals(vo.getPayInfStatus()))
                .forEach(vo -> remotePaymentService.payQuery(new OrderPayQueryBo().setOrderNo(vo.getOrderNo())));
        // 4 查询退款，补贴，划转单未完成的
        Set<String> availNos = tradeAccAvailService.queryAvailProcessing(new TradeAccSupAvailQueryBo()
                .setAvailDateStart(fileDate).setAvailDateEnd(fileDate))
                .stream().map(TradeAccAvailVo::getAvailNo).collect(Collectors.toSet());
        Set<String> orderNos = tradePayService.queryPayFailed(fileDate)
                .stream().map(TradePayVo::getOrderNo).collect(Collectors.toSet());
        Set<String> refundNos = tradePayRefundService.queryRefundFailed(fileDate)
                .stream().map(TradePayRefundVo::getRefundNo).collect(Collectors.toSet());
        Set<String> discountNos = tradeAccTransService.queryDiscountFailed(fileDate)
                .stream().map(TradeAccTransVo::getTransNo).collect(Collectors.toSet());
        Set<String> transferNos = tradeAccTransferService.queryTransferFailed(fileDate)
                .stream().map(TradeAccTransferVo::getTransNo).collect(Collectors.toSet());
        Set<String> commCashNos = tradeCommCashService.queryCashFailed(fileDate)
                .stream().map(TradeCommCashVo::getCashNo).collect(Collectors.toSet());
        if (CollectionUtil.isNotEmpty(availNos)) {
            acctReason.add(String.format("%s %s", "2结算", String.join(";", availNos)));
        }
        if (CollectionUtil.isNotEmpty(orderNos)) {
            acctReason.add(String.format("%s %s", "3支付", String.join(";", orderNos)));
        }
        if (CollectionUtil.isNotEmpty(refundNos)) {
            acctReason.add(String.format("%s %s", "4退款", String.join(";", refundNos)));
        }
        if (CollectionUtil.isNotEmpty(discountNos)) {
            acctReason.add(String.format("%s %s", "5营销", String.join(";", discountNos)));
        }
        if (CollectionUtil.isNotEmpty(transferNos)) {
            acctReason.add(String.format("%s %s", "6划转", String.join(";", transferNos)));
        }
        if (CollectionUtil.isNotEmpty(commCashNos)) {
            acctReason.add(String.format("%s %s", "7发薪", String.join(";", commCashNos)));
        }
        try {
            // 防止bi服务调用报错
            Set<String> unExecOrderNos = remoteBiTradeService.queryPayCancelUnExecuted(fileDate)
                    .stream().map(RemoteOrderPayVo::getOrderNo).collect(Collectors.toSet());
            Set<String> execOrderNos = remoteBiTradeService.queryPayCancelExecuted(fileDate)
                    .stream().map(RemoteOrderPayVo::getOrderNo).collect(Collectors.toSet());
            if (CollectionUtil.isNotEmpty(unExecOrderNos)) {
                acctReason.add(String.format("%s %s", "8支付取消", String.join(";", unExecOrderNos)));
            }
            if (CollectionUtil.isNotEmpty(execOrderNos)) {
                acctReason.add(String.format("%s %s", "9支付取消", String.join(";", execOrderNos)));
            }
        } catch (Exception e) {
            log.keyword("", "accTransContrast").error("", e);
        }
    }

    private void updateYesterdayBankAvailAmt(List<TradeAccDailyVo> list) {
        for (List<TradeAccDailyVo> ls : Lists.partition(list, 500)) {
            tradeOrgBankService.updateBankAmtBatch(ls);
        }
    }

    private Map<String, Long> insertDailyBatch(LocalDate fileDate, List<String> outOrgCodes) {
        List<TradeAccDailyVo> vos = new ArrayList<>();
        for (List<String> ls : Lists.partition(outOrgCodes, 500)) {
            vos.addAll(tradeAccDailyService.insertBatch(fileDate, ls));
        }
        return vos.stream().collect(Collectors.toMap(TradeAccDailyVo::getOutOrgCode, TradeAccDailyVo::getId));
    }

    private void updateDailyBatch(List<TradeAccDaily> list) {
        for (List<TradeAccDaily> ls : Lists.partition(list, 500)) {
            tradeAccDailyService.updateBatch(ls);
        }
    }

    private void updateAcctStatus(Long fileId, String fileType, LocalDate fileDate, List<String> splitNos) throws InterruptedException {
        if (CollectionUtil.isNotEmpty(splitNos)) {
            for (List<String> ls : Lists.partition(splitNos, 1000)) {
                tradeAccSplitService.updateAccStatus(fileDate, ls);
            }
            Thread.sleep(2000); // 等待上面事务执行完成。下面sql才不会报错
            tradeAccOssService.updateAcctStatus(fileId, fileType, fileDate);
        } else {
            tradeAccOssService.updateByBo(new TradeAccOssBo().setId(fileId).setAcctStatus(1).setAcctReason("无数据"));
        }
    }

    private void updatePayAcctStatus(LocalDate fileDate, List<TradePayBo> list) {
        for (List<TradePayBo> ls : Lists.partition(list, 500)) {
            tradePayService.updateAcctStatus(fileDate, ls);
        }
    }

    private void updatePayRecvAcctStatus(LocalDate fileDate, List<TradePayRecvBo> list) {
        for (List<TradePayRecvBo> ls : Lists.partition(list, 500)) {
            tradePayRecvService.updateAcctStatus(fileDate, ls);
        }
    }

    private void updateRefundAcctStatus(LocalDate fileDate, List<TradePayRefundBo> list) {
        for (List<TradePayRefundBo> ls : Lists.partition(list, 500)) {
            tradePayRefundService.updateAcctStatus(fileDate, ls);
        }
    }

    private <T> ExcelDataInfo<T> ossDownload(TradeAccOssDownBo downBo) {
        TradeAccOssVo ossVo = tradeAccOssService.queryByBo(downBo);
        if (ossVo == null) {
            return null;
        }
        byte[] file = HttpUtil.downloadBytes(ossVo.getUrl());
        if (ZipFileUtils.isZip(file)) {
            try {
                file = ZipFileUtils.unzip(file);
            } catch (IOException e) {
                throw new ServiceException(e);
            }
        }
        return new ExcelDataInfo<T>().setFileId(ossVo.getId()).setUploadSeqNo(ossVo.getUploadSeqNo())
                .setFile(file).setTransDate(ossVo.getTransDate()).setMerchantId(ossVo.getMerchantId())
                .setFileType(ossVo.getFileType()).setFileName(ossVo.getFileName());
    }

    private <T> ExcelDataInfo<T> fileDownload(TradeAccOssDownBo downBo) {
        ExcelDataInfo<T> dataInfo;
        String channel = Objects.requireNonNull(AccountFileTypeEnum.getEnumByCode(downBo.getFileType())).getChannel();
        if (PayChannelEnum.PAY_PINGAN_CLOUD_WX.getCode().equals(channel)) {
            dataInfo = accountService.fileDownload(downBo.getFileType(), downBo.getTransDate());
        } else {
            PaymentService paymentService = channelsContext.initPayment(channel);
            paymentService.switchByMerchant(downBo.getMerchantId());
            dataInfo = paymentService.fileDownload(downBo.getFileType(), downBo.getTransDate());
        }
        if (dataInfo != null) {
            String fileName = dataInfo.getFileName();
            byte[] fileByte = dataInfo.getFile();
            if (fileName.endsWith(".txt") || fileName.endsWith(".csv")) {
                fileName = fileName.substring(0, fileName.length() - 4) + ".zip";
                fileByte = ZipFileUtils.zip(dataInfo.getFileName(), fileByte);
            }
            // cos 的 key 重复时， 覆盖， 不报异常 ！！！
            RemoteFile remoteFile = remoteFileService.upload(fileName, fileName, ContentType.OCTET_STREAM.getValue(), fileByte);
            TradeAccOssBo bo = new TradeAccOssBo();
            bo.setId(remoteFile.getOssId());
            bo.setUrl(remoteFile.getUrl());
            bo.setFileName(fileName);
            bo.setMerchantId(dataInfo.getMerchantId());
            bo.setFileType(dataInfo.getFileType());
            bo.setTransDate(dataInfo.getTransDate());
            tradeAccOssService.insertByBo(bo);
            dataInfo.setFileId(bo.getId());
        }
        return dataInfo;
    }

    private <T> ExcelDataInfo<T> readExcelData(ExcelDataInfo<T> data) {
        data.setClazz((Class<T>) AccountFileTypes.EXCEL_COLUMN_CLAZZ.get(data.getFileType()))
                .setList(new ArrayList<>()).setTitle(AccountFileTypeEnum.getDescByCode(data.getFileType()));
        List<Field> fields = Arrays.stream(data.getClazz().getDeclaredFields()).filter(field -> {
            field.setAccessible(true);
            return !Modifier.isStatic(field.getModifiers());
        }).toList();
        if (PayChannelEnum.PAY_PINGAN_CLOUD_WX.getCode().equals(
                Objects.requireNonNull(AccountFileTypeEnum.getEnumByCode(data.getFileType())).getChannel())) {
            // 无第一行列头描述
            IoUtil.readLines(new ByteArrayInputStream(data.getFile()), CharsetUtil.CHARSET_GBK,
                    (LineHandler) line -> {
                        T t = ReflectUtil.newInstance(data.getClazz());
                        data.getList().add(t);
                        String[] strs = line.split("&");
                        int len = Math.min(strs.length, fields.size());
                        for (int j = 0; j < len; j++) {
                            ReflectUtil.setFieldValue(t, fields.get(j), strs[j]);
                        }
                    });
        } else {
            // 有第一行列头描述
            CsvReader csvReader = CsvUtil.getReader();
            List<CsvRow> csvData = csvReader.read(IoUtil.getUtf8Reader(new ByteArrayInputStream(data.getFile()))).getRows();
            for (int i = 0; i < csvData.size(); i++) {
                if (i != 0) { // 跳过第一行
                    T t = ReflectUtil.newInstance(data.getClazz());
                    data.getList().add(t);
                    List<String> strs = csvData.get(i).getRawList();
                    int len = Math.min(strs.size(), fields.size());
                    for (int j = 0; j < len; j++) {
                        // 第一个字符都是 `
                        String val = strs.get(j).substring(1).trim();
                        if (val.startsWith("\ufeff")) {
                            // 第一个字符不合法
                            val = val.substring(1);
                        } else if (Date.class.isAssignableFrom(fields.get(j).getType())) {
                            // 20241115 15:46:17
                            switch (val.length()) {
                                case 0 -> val = null;
                                case 17 ->
                                        val = String.format("%s-%s-%s", val.substring(0, 4), val.substring(4, 6), val.substring(6));
                            }
                        }
                        ReflectUtil.setFieldValue(t, fields.get(j), val);
                    }
                }
            }
        }
        return data;
    }

    /*
       对账文件上传    文件名：  交易日期_商户号_TRANSDETAIL.txt  （文件编码为UTF-8）
         第一行 汇总行样例：  交易日期,商户号,支付交易总笔数,支付交易合计总金额,撤销交易总笔数,撤销交易合计总金额,退款交易总笔数,退款交易合计总金额
            20200101,M000000001,3,196.34,1,67.89,1,4.99
         支付交易明细
            交易时间(YYYYMMDDHHMMSS),商户号(即外部平台在第三方支付渠道获分配的商户号),子商户号(暂不启用),交易类型(0-支付交易，1-撤销交易，2-退款交易),
                第三方支付渠道的支付交易的订单号或流水号,商户的支付交易的订单号或流水号,
                订单支付金额(即该笔支付交易的订单金额，保留2位小数，撤销交易或退款交易类型下此字段应为0.00),
                第三方支付渠道的撤销或退款交易的订单号或流水号,商户的撤销或退款交易的订单号或流水号,
                订单退款金额(即该笔撤销交易或退款交易的订单金额，保留2位小数的正数，支付交易类型下此字段应为0.00),
                第三方支付渠道收取的手续费(支付交易此字段应为非负数,撤销交易或退款交易此字段应为非正数),备注或附言信息,预留字段1,预留字段2,预留字段3
            20200101055900,M000000001,,0,PAPAY2878477,MOrder90000001,123.45,,,0.00,0.00,加油卡充值123.45元,,,
         退款交易明细
            20200101055901,M000000001,,0,PAPAY2878480,MOrder90000004,5.00,,,0.00,0.00,购物,,,
            20200102060000,M000000001,,2,PAPAY2878480,MOrder90000004,0.00,PAPAY2878481,MOrder90000005,4.99,0.00,退货,,,
    */
    private <T> void createUploadFile(ExcelDataInfo<T> data, List<TradePayVo> tradePayVos,
                                                     List<TradePayRefundVo> tradeRefundVos) {
        Map<String, TradePayVo> payMap = tradePayVos.stream().collect(Collectors.toMap(TradePayVo::getTradeNo, Function.identity()));
        List<String> refundTradeNos = tradeRefundVos.stream().map(TradePayRefundVo::getTradeNo).filter(tradeNo -> !payMap.containsKey(tradeNo)).distinct().toList();
        Map<String, TradePayVo> payRefundMap = queryDayPayData(refundTradeNos).stream().collect(Collectors.toMap(TradePayVo::getTradeNo, Function.identity()));
        payRefundMap.putAll(payMap);
        String fileDate = PinganCloudUtil.dateToStr(data.getTransDate());
        String fileName = String.format("%s_%s_TRANSDETAIL.", fileDate, data.getMerchantId());
        StringBuilder sb = new StringBuilder();
        BigDecimal payAmt = tradePayVos.stream().map(TradePayVo::getPaySplitAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal refundAmt = tradeRefundVos.stream().map(TradePayRefundVo::getRefundSplitAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
        sb.append(String.format("%s,%s,%s,%s,%s,%s,%s,%s\n", fileDate, data.getMerchantId(), tradePayVos.size(), payAmt, "0", "0", tradeRefundVos.size(), refundAmt));
        for (TradePayVo vos : tradePayVos) {
            sb.append(String.format("%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s\n", PinganCloudUtil.timeToStr(vos.getOutSuccessTime()),
                    data.getMerchantId(), "", "0", vos.getOutChannelNo(), vos.getTradeNo(), vos.getPaySplitAmt(),
                    "", "", vos.getRefundSplitAmt(), vos.getOutFeeAmt(), vos.getOrderNo(), "", "", ""));
        }
        for (TradePayRefundVo vos : tradeRefundVos) {
            TradePayVo payVo = payRefundMap.get(vos.getTradeNo());
            sb.append(String.format("%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s\n", PinganCloudUtil.timeToStr(vos.getOutSuccessTime()),
                    data.getMerchantId(), "", "2", payVo.getOutChannelNo(), vos.getTradeNo(), "0.00", vos.getOutChannelNo(), vos.getTradeRefundNo(),
                    vos.getRefundSplitAmt(), vos.getOutFeeAmt(), vos.getOrderNo(), "", "", ""));
        }
        data.setFile(ZipFileUtils.zip(fileName + "txt", sb.toString().getBytes(StandardCharsets.UTF_8)));
        data.setFileName(fileName + "zip");
    }

    private List<TradePayVo> queryDayPayData(List<String> tradeNos) {
        List<TradePayVo> payList = new ArrayList<>();
        for (List<String> ls : Lists.partition(tradeNos, 2000)) {
            payList.addAll(tradePayService.queryByTradeNos(ls));
        }
        return payList;
    }

    private List<TradePayRefundVo> queryDayRefundData(List<String> tradeNos) {
        List<TradePayRefundVo> refundList = new ArrayList<>();
        for (List<String> ls : Lists.partition(tradeNos, 2000)) {
            refundList.addAll(tradePayRefundService.queryByTradeNos(ls));
        }
        return refundList;
    }

    /**
     * 运维使用，下载账单数据
     */
    public <T> ExcelDataInfo<T> downloadAndParse(String fileType, String url) {
        byte[] file = HttpUtil.downloadBytes(url);
        if (ZipFileUtils.isZip(file)) {
            try {
                file = ZipFileUtils.unzip(file);
            } catch (IOException e) {
                throw new ServiceException(e);
            }
        }
        ExcelDataInfo data = new ExcelDataInfo<T>().setFile(file).setFileType(fileType);
        return readExcelData(data);
    }
}