package cn.xianlink.trade.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.xianlink.common.api.enums.trade.AccountOrgBindEnum;
import cn.xianlink.common.api.enums.trade.AccountOrgTypeEnum;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.trade.domain.TradeOrgBank;
import cn.xianlink.trade.domain.bo.org.TradeOrgBankAmtBo;
import cn.xianlink.trade.domain.bo.org.TradeOrgBankAmtQueryBo;
import cn.xianlink.trade.domain.bo.org.TradeOrgBankAuthQueryBo;
import cn.xianlink.trade.domain.vo.org.TradeAccDailyVo;
import cn.xianlink.trade.domain.vo.org.TradeOrgBankAmtVo;
import cn.xianlink.trade.domain.vo.org.TradeOrgBankAuthVo;
import cn.xianlink.trade.domain.vo.org.TradeOrgBankVo;
import cn.xianlink.trade.mapper.TradeOrgBankMapper;
import cn.xianlink.trade.service.ITradeOrgBankService;
import cn.xianlink.trade.utils.MapstructPageUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 机构子账户绑定Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@RequiredArgsConstructor
@Service
public class TradeOrgBankServiceImpl implements ITradeOrgBankService {

    private final transient TradeOrgBankMapper baseMapper;

    @Override
    public TradeOrgBankVo queryByCode(String outOrgCode) {
        return baseMapper.selectVoOne(Wrappers.lambdaQuery(TradeOrgBank.class).eq(TradeOrgBank::getOutOrgCode, outOrgCode));
    }

    @Override
    public List<TradeOrgBankVo> selectListByName(String name) {
        LambdaQueryWrapper<TradeOrgBank> lqw = Wrappers.lambdaQuery();
        lqw.ne(TradeOrgBank::getStatus, AccountOrgBindEnum.CLOSE.getCode());
        lqw.and(StringUtils.isNotBlank(name), q -> q.likeRight(TradeOrgBank::getOutOrgName, name)
                .or().likeRight(TradeOrgBank::getOutOrgCode, name));
        lqw.orderByAsc(TradeOrgBank::getId);
		PageQuery page = new PageQuery();
        page.setPageSize(20);
        return baseMapper.selectVoPage(page.build(), lqw).getRecords();
    }

    @Override
    public TableDataInfo<TradeOrgBankAuthVo> queryPageList(TradeOrgBankAuthQueryBo queryBo) {
        LambdaQueryWrapper<TradeOrgBank> lqw = buildQueryWrapper(queryBo);
        Page<TradeOrgBankVo> result = baseMapper.selectVoPage(queryBo.build(), lqw);
        return TableDataInfo.build(MapstructPageUtils.convert(result, TradeOrgBankAuthVo.class));
    }

    @Override
    public List<TradeOrgBankAuthVo> queryList(TradeOrgBankAuthQueryBo queryBo) {
        LambdaQueryWrapper<TradeOrgBank> lqw = buildQueryWrapper(queryBo);
        return MapstructUtils.convert(baseMapper.selectVoList(lqw), TradeOrgBankAuthVo.class);
    }

    private LambdaQueryWrapper<TradeOrgBank> buildQueryWrapper(TradeOrgBankAuthQueryBo bo) {
        LambdaQueryWrapper<TradeOrgBank> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getOutOrgType() != null, TradeOrgBank::getOutOrgType, bo.getOutOrgType());
        lqw.in(CollectionUtil.isNotEmpty(bo.getOutOrgCodes()), TradeOrgBank::getOutOrgCode, bo.getOutOrgCodes());
        lqw.eq(StringUtils.isNotBlank(bo.getOutAcctCode()), TradeOrgBank::getOutAcctCode, bo.getOutAcctCode());
        lqw.eq(bo.getStatus() != null, TradeOrgBank::getStatus, bo.getStatus());
        lqw.orderByDesc(TradeOrgBank::getId);
        return lqw;
    }

    @Override
    public TableDataInfo<TradeOrgBankAmtVo> customPageList(TradeOrgBankAmtQueryBo queryBo) {
        Page<TradeOrgBank> page = queryBo.build();
        page.setOptimizeJoinOfCountSql(false);
        Page<TradeOrgBankAmtVo> amtVoPage = AccountOrgTypeEnum.CUSTOMER.getCode().equals(queryBo.getOutOrgType()) ?
                baseMapper.customCustList(page, queryBo) : baseMapper.customList(page, queryBo);
        return TableDataInfo.build(amtVoPage);
    }

    @Override
    public List<TradeOrgBankAmtVo> customList(TradeOrgBankAmtQueryBo queryBo) {
        return AccountOrgTypeEnum.CUSTOMER.getCode().equals(queryBo.getOutOrgType()) ?
                baseMapper.customCustList(queryBo) : baseMapper.customList(queryBo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateAmtByBo(TradeOrgBankAmtBo bo) {
        TradeOrgBank update = MapstructUtils.convert(bo, TradeOrgBank.class);
        return baseMapper.update(update, Wrappers.lambdaUpdate(TradeOrgBank.class)
                .eq(TradeOrgBank::getOutOrgCode, update.getOutOrgCode())) > 0;

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBankAmtBatch(List<TradeAccDailyVo> bos) {
        if (bos.size() > 0) {
            baseMapper.updateBankAmtBatch(bos);
        }
    }

}
