package cn.xianlink.trade.channel.weixinb2b;

import cn.binarywang.wx.miniapp.util.WxMaConfigHolder;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.xianlink.common.api.enums.trade.PayInfStatusEnum;
import cn.xianlink.common.api.enums.trade.RefundInfStatusEnum;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.json.utils.JsonUtils;
import cn.xianlink.trade.constant.Constants;
import cn.xianlink.trade.domain.bo.TradePayBo;
import cn.xianlink.trade.domain.bo.TradePayRefundBo;
import cn.xianlink.trade.domain.vo.TradeAccSplitVo;
import cn.xianlink.trade.domain.vo.TradePayRefundVo;
import cn.xianlink.trade.domain.vo.TradePayVo;
import com.alibaba.fastjson.JSONObject;
import jakarta.servlet.http.HttpServletRequest;
import lombok.CustomLog;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Properties;

/**
 * 平安云收款对接 -- 微信小程序 --
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@CustomLog
@Component
@ConditionalOnProperty(prefix = "channels", name = "debug-local", havingValue = "true", matchIfMissing = false)
public class WeixinB2bTestPayServiceImpl extends WeixinB2bPayServiceImpl {

    @Override
    public void init(String wxAppId, Properties properties) {
        WeixinB2bConfig wxConfig = new WeixinB2bConfig();
        BeanUtil.copyProperties(properties, wxConfig, Constants.BeanCopyIgnoreNullValue);
        wxConfigs.put(wxAppId, wxConfig);
        // wxAppId, path, merchantId 是同渠道下， 一对一 对一 的
        this.wxAppIds.put(wxConfig.getCallbackPath(), wxAppId);
        this.wxAppIds.put(wxConfig.getMerchantId(), wxAppId);
    }

    @Override
    public void switchover(String wxAppId) {
        WxMaConfigHolder.set(wxAppId);
    }

    @Override
    public TradePayBo payQuery(TradePayVo vo) {
        if(StringUtils.isEmpty(vo.getTradeNo())){
            throw new ServiceException("接口单号不能为空");
        }
        TradePayBo respBo = new TradePayBo();
        respBo.setId(vo.getId());
        respBo.setPayInfStatus(PayInfStatusEnum.SUCCESS.getCode());
        respBo.setInfReason("");
        respBo.setOutTradeNo("order_id");
        respBo.setOutChannelNo("wxpay_transaction_id");
        respBo.setOutFeeAmt(BigDecimal.ZERO);
        respBo.setOutDiscountAmt(BigDecimal.ZERO);
        respBo.setOutCouponAmt(BigDecimal.ZERO);
        respBo.setOutSuccessTime(Convert.toDate(DateUtil.now()));
        respBo.setPayerId("payer_openid");
        return respBo;
    }

    @Override
    public JSONObject callback(String reqBody, HttpServletRequest request) {
        log.keyword("payCallback").info("支付回调 {}", reqBody);
        // JSONObject body = JSONObject.parseObject(data);
        return null;
    }

    @Override
    public TradePayBo payCallback(JSONObject resp) {
        return null;
    }

    @Override
    public TradePayRefundBo refund(TradePayRefundVo vo) {
        log.keyword(vo.getOrderNo(), vo.getRefundNo()).info(JsonUtils.toJsonString(vo));
        if(StringUtils.isEmpty(vo.getTradeNo()) || StringUtils.isEmpty(vo.getTradeRefundNo()) || StringUtils.isEmpty(vo.getRefundNo())){
            throw new ServiceException("接口单号不能为空");
        }
        if(vo.getRefundSplitAmt() == null || vo.getRefundSplitAmt().compareTo(BigDecimal.ZERO) < 0){
            throw new ServiceException("接口退款金额错误");
        }
        // 成功 或 抛异常
        TradePayRefundBo respBo = new TradePayRefundBo();
        respBo.setId(vo.getId());
        respBo.setInfReason("");
        respBo.setOutTradeNo("refund_id");
        respBo.setOutCouponAmt(BigDecimal.ZERO);
        respBo.setOutDiscountAmt(BigDecimal.ZERO);
        respBo.setOutCashAmt(vo.getRefundSplitAmt());
        respBo.setRefundInfStatus(RefundInfStatusEnum.PROCESSING.getCode());
        return respBo;
    }

    @Override
    public TradePayRefundBo refundQuery(TradePayRefundVo vo) {
        if(StringUtils.isEmpty(vo.getTradeNo()) || StringUtils.isEmpty(vo.getTradeRefundNo())){
            throw new ServiceException("接口单号不能为空");
        }
        TradePayRefundBo respBo = new TradePayRefundBo();
        respBo.setId(vo.getId());
        respBo.setInfReason("");
        respBo.setOutTradeNo("refund_id");
        respBo.setOutCouponAmt(BigDecimal.ZERO);
        respBo.setOutDiscountAmt(BigDecimal.ZERO);
        respBo.setRefundInfStatus(RefundInfStatusEnum.SUCCESS.getCode());
        respBo.setOutChannelNo("wxpay_refund_id");
        respBo.setOutCashAmt(BigDecimal.ZERO);
        respBo.setOutSuccessTime(Convert.toDate(DateUtil.now()));  // refundQuery 才有
        return respBo;
    }

    @Override
    public int getSplitCount() {
        return 20;
    }

    @Override
    public void paySplit(String merchantNo, TradePayVo vo) {
        if (StringUtils.isEmpty(vo.getOutChannelNo()) || vo.getSplits().size() == 0 || vo.getSplits().size() > 20) {
            throw new ServiceException(String.format("接口分账参数不合法，%s,%s", vo.getOutChannelNo(), vo.getSplits().size()));
        }
        Date infTime = Convert.toDate(DateUtil.now());
        for (TradeAccSplitVo splitVo : vo.getSplits()) {
            splitVo.setInfStatus(PayInfStatusEnum.SUCCESS.getCode());
            splitVo.setInfReason("");
            splitVo.setInfTime(infTime);
            splitVo.setOutSuccessTime(infTime);
            splitVo.setOutTradeNo("11111111");
            if (splitVo.getTransAmt().compareTo(BigDecimal.ZERO) == 0) {
                splitVo.setInfReason("分帐金额为零");
            }
        }
    }

    @Override
    public void refundSplit(String outChannelNo, TradePayRefundVo vo) {
        if (StringUtils.isEmpty(outChannelNo) || vo.getSplits().size() == 0 || vo.getSplits().size() > 20) {
            throw new ServiceException(String.format("接口分账参数不合法，%s,%s", outChannelNo, vo.getSplits().size()));
        }
        // throw new ServiceException(String.format("[ERR180]，%s,%s", outChannelNo, vo.getSplits().size()));
        Date infTime = Convert.toDate(DateUtil.now());
        for (TradeAccSplitVo splitVo : vo.getSplits()) {
            splitVo.setInfStatus(RefundInfStatusEnum.SUCCESS.getCode());
            splitVo.setInfReason("");
            splitVo.setInfTime(infTime);
            splitVo.setOutSuccessTime(infTime);
            splitVo.setOutTradeNo("11111111");
            if (splitVo.getTransAmt().compareTo(BigDecimal.ZERO) == 0) {
                splitVo.setInfReason("分帐金额为零");
            }
        }
    }

}
