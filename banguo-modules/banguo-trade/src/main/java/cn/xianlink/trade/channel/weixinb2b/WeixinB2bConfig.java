package cn.xianlink.trade.channel.weixinb2b;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class WeixinB2bConfig implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private String baseUrl;
    private String attach;
    // b2b 支付完成时间 和 结算完成时间差 180 秒
    private Integer refundDelaySecond;
    // 小程序后台获取
    // private String appId;
    private String secret;
    private String merchantId;
    // 支付接口签名  小程序后台获取
    private String testAppKey;
    private String prodAppKey;
    // 配置消息推送需要
    private String token; // 自定义
    private String aesKey; // 随机
    private String msgDataFormat;
    // 回调path
    private String callbackPath;
    private Integer profitRate; // 优惠费率
}
