package cn.xianlink.trade.channel.pingancloud.constant;

import cn.hutool.core.util.ObjectUtil;

public enum PinganCloudPayInfStatusEnum {
    // 支付状态对应关系   转成 PayInfStatusEnum 枚举值
    PAY_INIT("0", "已受理", 0),
    PAY_SUCCESS("1", "交易成功", 1),
    PAY_UNPAID("2", "交易中", 0),
    PAY_PAYING("3", "用户支付中", 0),
    PAY_CLOSE("4", "交易关闭", 2),
    PAY_CANCEL("9", "已撤销", 3);

    private final String status;
    private final Integer infStatus;
    private final String desc;

    PinganCloudPayInfStatusEnum(String status, String desc, Integer infStatus) {
        this.status = status;
        this.infStatus = infStatus;
        this.desc = desc;
    }

    public String getStatus() {
        return status;
    }

    public Integer getInfStatus() {
        return infStatus;
    }

    public String getDesc() {
        return desc;
    }

    public static Integer getInfStatus(String status) {
        for (PinganCloudPayInfStatusEnum e : values()) {
            if (ObjectUtil.equal(e.getStatus(), status)) {
                return e.getInfStatus();
            }
        }
        return -1;
    }

}
