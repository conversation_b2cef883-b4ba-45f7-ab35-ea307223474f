package cn.xianlink.trade.service.biz;

import cn.hutool.core.bean.BeanUtil;
import cn.xianlink.common.api.enums.trade.AccountTransTypeEnum;
import cn.xianlink.common.api.enums.trade.ChargeInfStatusEnum;
import cn.xianlink.common.api.enums.trade.DiscountInfStatusEnum;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.trade.channel.AccountService;
import cn.xianlink.trade.constant.Constants;
import cn.xianlink.trade.domain.bo.TradeAccChargeBo;
import cn.xianlink.trade.domain.bo.TradeAccChargeCreateBo;
import cn.xianlink.trade.domain.bo.TradeAccChargeRefundBo;
import cn.xianlink.trade.domain.bo.TradeAccTransBo;
import cn.xianlink.trade.domain.vo.TradeAccChargeVo;
import cn.xianlink.trade.domain.vo.TradeAccTransVo;
import cn.xianlink.trade.service.ITradeAccChargeService;
import cn.xianlink.trade.service.ITradeAccTransService;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * 机构子账户绑定Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@CustomLog
@RequiredArgsConstructor
@Service
public class TradeAccountBizService {

    private final transient ITradeAccChargeService tradeAccChargeService;
    private final transient ITradeAccTransService tradeAccTransService;
    private final transient AccountService accountService;

    public void recharge(TradeAccChargeCreateBo bo) {
        TradeAccChargeVo chargeVo = tradeAccChargeService.insertByBo(bo, accountService.getMarketingAcctCode());
        try {
            TradeAccChargeBo respBo = accountService.recharge(chargeVo);
            log.keyword(chargeVo.getChargeNo(), "recharge").info("充值接口完成");
            tradeAccChargeService.updateInfData(respBo, chargeVo);
            BeanUtil.copyProperties(respBo, chargeVo, Constants.BeanCopyIgnoreNullValue);
        } catch (ServiceException se) {
            tradeAccChargeService.updateInfFail(ChargeInfStatusEnum.INF_FAIL.getCode(), chargeVo, se);
            log.keyword(chargeVo.getChargeNo(), "recharge").warn("充值接口异常", se);
            throw se;
        }
    }

    public void rechargeRefund(TradeAccChargeRefundBo bo) {
        TradeAccChargeVo chargeVo = tradeAccChargeService.insertByBo(bo, accountService.getMarketingAcctCode());
        try {
            TradeAccChargeBo respBo = accountService.rechargeRefund(chargeVo);
            log.keyword(chargeVo.getChargeNo(), "rechargeRefund").info("充值退款接口完成");
            tradeAccChargeService.updateInfData(respBo, chargeVo);
            BeanUtil.copyProperties(respBo, chargeVo, Constants.BeanCopyIgnoreNullValue);
        } catch (ServiceException se) {
            tradeAccChargeService.updateInfFail(ChargeInfStatusEnum.INF_FAIL.getCode(), chargeVo, se);
            log.keyword(chargeVo.getChargeNo(), "rechargeRefund").warn("充值退款接口异常", se);
            throw se;
        }
    }

    public void disposeDiscount(TradeAccTransBo accTransBo) {
        if (accTransBo.getSplitAmt().compareTo(BigDecimal.ZERO) == 0) {
            TradeAccTransBo respBo = new TradeAccTransBo();
            respBo.setInfStatus(DiscountInfStatusEnum.SUCCESS.getCode());
            tradeAccTransService.updateDiscountInfData(respBo, MapstructUtils.convert(accTransBo, TradeAccTransVo.class));
        } else {
            TradeAccTransVo transVo = tradeAccTransService.updateDiscountInf(accTransBo);
            try {
                TradeAccTransBo respBo = AccountTransTypeEnum.OP.getCode().equals(transVo.getTransType()) ?
                        accountService.discountPay(transVo) : accountService.discountRefund(transVo);
                log.keyword(accTransBo.getTransNo(), "disposeDiscount").info("营销接口完成");
                tradeAccTransService.updateDiscountInfData(respBo, transVo);
                BeanUtil.copyProperties(respBo, transVo, Constants.BeanCopyIgnoreNullValue);
            } catch (ServiceException se) {
                tradeAccTransService.updateDiscountInfFail(DiscountInfStatusEnum.CHECK.getCode(), transVo, se);
                log.keyword(accTransBo.getTransNo(), "disposeDiscount").warn("营销接口异常", se);
                throw se;
            }
        }
        // 验证订单状态是 1 还是 2， 转成订单一致的状态
    }

}
