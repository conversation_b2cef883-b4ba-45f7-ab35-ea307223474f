package cn.xianlink.trade.channel.pingancloud.constant;

import cn.hutool.core.util.ObjectUtil;

public enum PinganCloudTradeStatusEnum {
    // 单笔交易查询状态  KFEJZB6110 	（（0：成功，1：失败，2：待确认, 5：待处理，6：处理中））
    TRADE_INIT("2", "待确认", 0),
    TRADE_WAIT("5", "待处理", 0),
    TRADE_ING("6", "处理中", 0),
    TRADE_SUCCESS("0", "成功", 1),
    TRADE_FAIL("1", "失败", 2);

    private final String status;
    private final Integer infStatus;
    private final String desc;

    PinganCloudTradeStatusEnum(String status, String desc, Integer infStatus) {
        this.status = status;
        this.desc = desc;
        this.infStatus = infStatus;
    }

    public String getStatus() {
        return status;
    }

    public Integer getInfStatus() {
        return infStatus;
    }

    public String getDesc() {
        return desc;
    }

    public static Integer getInfStatus(String status) {
        for (PinganCloudTradeStatusEnum e : values()) {
            if (ObjectUtil.equal(e.getStatus(), status)) {
                return e.getInfStatus();
            }
        }
        return 0;
    }
}
