package cn.xianlink.trade.domain;

import cn.xianlink.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * 提现单对象 trade_comm_cash
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("trade_comm_cash")
public class TradeCommCash extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id主键
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 业务类型 CH 提现; CE 过期;
     */
    private String cashType;
    /**
     * 提现单号
     */
    private String cashNo;

    /**
     * 提现时间
     */
    private LocalDate cashDate;

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 客户代码
     */
    private String customerCode;

    /**
     * 提现金额
     */
    private BigDecimal cashAmt;

    /**
     * 发薪金额
     */
    private BigDecimal salaryAmt;
    /**
     * 银行账户名
     */
    private String personName;
    /**
     * 银行卡号
     */
    private String bankAccount;
    /**
     * 银行名
     */
    private String bankBranch;
    /**
     * 分帐单号
     */
    private String splitNo;
    /**
     * 支付发起时间
     */
    private Date cashInfTime;
    /**
     * 支付发起时间
     */
    private Date infTime;
    /**
     * 重试次数
     */
    private Integer infRetries;
    /**
     * 接口状态 0 未支付;1 支付完成 ;2 支付取消; 10 接口未调用; 11 接口失败
     */
    private Integer infStatus;
    /**
     * 接口失败原因
     */
    private String infReason;
    /**
     * 实际到账金额
     */
    private BigDecimal outActualAmt;
    /**
     * 手续费
     */
    private BigDecimal outFeeAmt;
    /**
     * 银行流水号
     */
    private String outTradeNo;
    /**
     * 平安提现时间
     */
    private Date outCashTime;
    /**
     * 有为流水号
     */
    private String ywMerchNo;
    /**
     * 支付发起时间
     */
    private Date ywInfTime;
    /**
     * 重试次数
     */
    private Integer ywInfRetries;
    /**
     * 接口状态 0 未支付;1 支付完成 ;2 支付取消; 10 接口未调用; 11 接口失败
     */
    private Integer ywInfStatus;
    /**
     * 接口状态 0 未创单  1 创单完成
     */
    private Integer ywOrderStatus;
    /**
     * 有为系统订单号
     */
    private String ywOrderNo;
    /**
     * 有为系统结算号
     */
    private String ywSerialNo;
    /**
     * 有为到账金额
     */
    private BigDecimal ywActualAmt;
    /**
     * 有为折扣金额
     */
    private BigDecimal ywDeductAmt;

    /**
     * 分账状态 0 执行中  1 提现完成 3 审核
     * */
    private Integer cashStatus;
    /**
     * 提现申请时间
     */
    private Date cashCheckTime;
    /**
     * 提现时间
     */
    private Date cashTime;
    /**
     * 删除标志（0代表存在 存入这条数据的主键代表删除）
     */
    @TableLogic(delval = "id")
    private Long delFlag;

    /**
     * 微信 openid
     */
    private String openId;

    /**
     * 签约渠道 1 有为  2 yzh
     */
    private Integer signChannel;
}