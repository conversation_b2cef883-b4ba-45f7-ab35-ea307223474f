package cn.xianlink.trade.domain;

import cn.xianlink.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * 支付对象 trade_pay
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value="trade_pay_recv", autoResultMap = true)
public class TradePayRecv extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 收款单号
     */
    private String recvNo;
    /**
     * 收款日期
     */
    private LocalDate recvDate;
    /**
     * 业务类型 PO 客户订单;  PC 客户存款
     */
    private String recvType;
    /**
     * 收款金额
     */
    private BigDecimal recvAmt;
    /**
     * 客户用户id
     */
    private Long customerId;
    /**
     * 客户代码
     */
    private String customerCode;
    /**
     * 客户关联代码
     */
    private String customerOutCode;
    /**
     * 客户子账号
     */
    private String customerAcctCode;
    /**
     * pay.id
     */
    private Long payId;
    /**
     * 订单id主键
     */
    private Long orderId;
    /**
     * 订单唯一单号
     */
    private String orderNo;
    /**
     * 订单日期
     */
    private LocalDate orderDate;
    /**
     * 支付发起时间
     */
    private Date infTime;

    /**
     * 接口状态 0 未支付;1 支付完成 ;2 支付取消; 10 接口未调用; 11 接口失败
     */
    private Integer infStatus;
    /**
     * 接口失败原因
     */
    private String infReason;
    /**
     * 支付关闭时间
     */
    private Date closeTime;
    /**
     * 收款卡号
     */
    private String outRecvCode;
    /**
     * 收款账户名
     */
    private String outRecvAccName;
    /**
     *银行流水号ThirdSeqNo 回执的
     */
    private String outTradeNo;
    /**
     * 实际到账金额
     */
    private BigDecimal outCashAmt;
    /**
     * 对接渠道支付完成时间 回执的
     */
    private Date outSuccessTime;
    /**
     * 银行账户名
     */
    private String inAccName;
    /**
     * 银行卡号
     */
    private String inAccount;
    /**
     * 银行超级网银号
     */
    private String inBankEicon;
    /**
     * 银行支行名
     */
    private String inBankName;
    /**
     * 对账状态 0 未对账 1 完成 2 失败
     */
    private Integer acctStatus;
    /**
     * 对账日期
     */
    private LocalDate acctDate;
    /**
     * 删除标志; 4 交易关闭; 9 已撤销; 101 接口失败; 这3中状态设置成删除
     */
    // @TableLogic(delval = "id") //不加注解，手动处理删除标识
    private Long delFlag;

}
