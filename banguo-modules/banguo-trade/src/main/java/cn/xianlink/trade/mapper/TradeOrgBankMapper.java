package cn.xianlink.trade.mapper;

import cn.xianlink.common.mybatis.core.mapper.BaseMapperPlus;
import cn.xianlink.trade.domain.TradeOrgBank;
import cn.xianlink.trade.domain.bo.org.TradeOrgBankAmtQueryBo;
import cn.xianlink.trade.domain.vo.org.TradeAccDailyVo;
import cn.xianlink.trade.domain.vo.org.TradeOrgBankAmtVo;
import cn.xianlink.trade.domain.vo.org.TradeOrgBankVo;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 机构子账户绑定Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
public interface TradeOrgBankMapper extends BaseMapperPlus<TradeOrgBank, TradeOrgBankVo> {

    Page<TradeOrgBankAmtVo> customList(@Param("page") Page<TradeOrgBank> page, @Param("qbo") TradeOrgBankAmtQueryBo bo);

    List<TradeOrgBankAmtVo> customList(@Param("qbo") TradeOrgBankAmtQueryBo bo);

    Page<TradeOrgBankAmtVo> customCustList(@Param("page") Page<TradeOrgBank> page, @Param("qbo") TradeOrgBankAmtQueryBo bo);

    List<TradeOrgBankAmtVo> customCustList(@Param("qbo") TradeOrgBankAmtQueryBo bo);

    void updateBankAmtBatch(@Param("bos") List<TradeAccDailyVo> bos);

}
