package cn.xianlink.trade.mapper;

import cn.xianlink.common.mybatis.core.mapper.BaseMapperPlus;
import cn.xianlink.trade.domain.TradePayRefund;
import cn.xianlink.trade.domain.bo.TradePayRefundBo;
import cn.xianlink.trade.domain.vo.TradePayRefundVo;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * 支付退款Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
public interface TradePayRefundMapper extends BaseMapperPlus<TradePayRefund, TradePayRefundVo> {

    TradePayRefund queryStatusByTrans(@Param("transNo") String transNo, @Param("transTypes") List<String> transTypes);

    void updateAcctStatus(@Param("acctDate") LocalDate acctDate, @Param("bos") List<TradePayRefundBo> bos);
}
