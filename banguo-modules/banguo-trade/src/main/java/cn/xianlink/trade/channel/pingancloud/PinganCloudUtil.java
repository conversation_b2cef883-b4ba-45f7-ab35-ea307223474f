package cn.xianlink.trade.channel.pingancloud;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.xianlink.common.core.utils.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.Date;

public class PinganCloudUtil {
    public static BigDecimal fenToYuan(String fen){
        BigDecimal bigFen = new BigDecimal(StringUtils.isEmpty(fen) ? "0" : fen);
        BigDecimal percent = new BigDecimal("100");
        return bigFen.divide(percent, 2, RoundingMode.HALF_UP);
    }
    public static String yuanToFen(BigDecimal yuan){
        BigDecimal percent = new BigDecimal("100");
        return String.valueOf(yuan.multiply(percent).intValueExact());
    }

    public static BigDecimal yuanToYuan(String yuan){
        return new BigDecimal(StringUtils.isEmpty(yuan) ? "0" : yuan);
    }

    public static String timeToStr(Date date) {
        return DateUtil.format(date, "yyyyMMddHHmmss");
    }

    public static String dateToStr(Date date) {
        return DateUtil.format(date, "yyyyMMdd");
    }

    public static Date timeToDate(String date) {
        try {
            return DateUtil.parse(date, "yyyyMMddHHmmss").toJdkDate();
        } catch (Exception e) {
            return null;
        }
    }

    public static LocalDate dateToDate(String date) {
        try {
            String format = date.length() == 6 ? "yyMMdd" : "yyyyMMdd";
            return LocalDateTimeUtil.parseDate(date, format);
        } catch (Exception e) {
            return null;
        }
    }

    public static String dateToStr(LocalDate date) {
        return LocalDateTimeUtil.format(date, "yyyyMMdd");
    }

    public static String toString(String str) {
        return str == null ? "" : str;
    }

}
