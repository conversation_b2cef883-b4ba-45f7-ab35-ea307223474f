package cn.xianlink.trade.mapper;


import cn.xianlink.common.mybatis.core.mapper.BaseMapperPlus;
import cn.xianlink.trade.domain.TradeAccDaily;
import cn.xianlink.trade.domain.vo.org.TradeAccDailyAmtVo;
import cn.xianlink.trade.domain.vo.org.TradeAccDailyVo;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;


/**
 * 账务总Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
public interface TradeAccDailyMapper extends BaseMapperPlus<TradeAccDaily, TradeAccDailyVo> {

    List<TradeAccDailyAmtVo> queryDailyAmt(@Param("acctDate") LocalDate acctDate);

    int insertBatch( @Param("acctDate") LocalDate acctDate, @Param("outOrgCodes") List<String> outOrgCodes);

}
