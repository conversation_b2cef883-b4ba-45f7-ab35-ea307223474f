package cn.xianlink.trade.service.impl;

import cn.xianlink.ai.api.RemoteSupportMessageService;
import cn.xianlink.ai.api.domain.bo.RemoteSupportMessageBo;
import cn.xianlink.trade.service.ISupportMessageService;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * 客服消息
 */
@CustomLog
@Service
@RequiredArgsConstructor
public class SupportMessageServiceImpl implements ISupportMessageService {

    @DubboReference
    private final RemoteSupportMessageService remoteSupportMessageService;

    @Override
    public void handleMessage(String messageBody) {

        try {
            log.keyword("SupportMessageService.handleMessage").info("messageBody:{}", messageBody);
            RemoteSupportMessageBo messageInfo = new RemoteSupportMessageBo(messageBody);
            //微信客户信息回调
            remoteSupportMessageService.processMessage(messageInfo);
            //发送mq
        } catch (Exception e) {
            log.keyword("WXMSGERROR(").error("微信消息处理失败:{}", ExceptionUtils.getStackTrace(e));
        }

    }
} 