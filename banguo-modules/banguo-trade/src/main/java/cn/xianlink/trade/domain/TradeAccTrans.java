package cn.xianlink.trade.domain;

import cn.xianlink.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * 分账变更流水对象 trade_acc_trans
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("trade_acc_trans")
public class TradeAccTrans extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id主键    //差额退
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 业务类型 OP 订单;  OR 退单; TI 划转入; TO 划转出;
     */
    private String transType;
    /**
     * 业务id
     */
    private Long transId;
    /**
     * 业务单号
     */
    private String transNo;
    /**
     * 记账日期
     */
    private LocalDate transDate;
    /**
     * 0 总仓 1 城市仓 2 供应商
     */
    private Integer orgType;
    /**
     * 机构id
     */
    private Long orgId;
    /**
     * 机构代码
     */
    private String orgCode;
    /**
     * 部门id
     */
    private Long deptId;
    /**
     * 账务机构id
     */
    private Long acctOrgId;
    /**
     * 业务机构id
     */
    private Long transOrgId;
    /**
     * 批次商品id
     */
    private Long skuId;
    /**
     * 平台商品名称
     */
    private String skuName;
	/**
     * 业务类型
     */
    private String busiType;
    /**
     * 累加字段 单维度
     */
    private String busiField;
    /**
     * 分账单号
     */
    private String splitNo;
    /**
     * 分账单号
     */
    private String splitRelNo;
    /**
     * 分账收款金额
     */
    private BigDecimal splitRelAmt;
    /**
     * 营销分账单号
     */
    private String splitDisNo;
    /**
     * 业务类型 OP 订单;  TI 划转入;
     */
    private String relateType;
    /**
     * 关联单号
     */
    private String relateNo;
    /**
     * 关联单id
     */
    private Long relateId;
    /**
     * 收款金额
     */
    private BigDecimal relateAmt;
    /**
     * 分账总金额
     */
    private BigDecimal totalAmt;
    /**
     * 调整金额
     */
    private BigDecimal transAmt;
    /**
     * 实际金额
     */
    private BigDecimal splitAmt;
    /**
     * 佣金金额
     */
    private BigDecimal commissionAmt;
    /**
     * 发薪金额
     */
    private BigDecimal commSalaryAmt;
    /**
     * 佣金客户id bas_customer.id
     */
    private Long commCustomerId;
    /**
     * splitAmt commissionAmt 小
     */
    private BigDecimal transMinAmt;
    /**
     * splitAmt commissionAmt 大
     */
    private BigDecimal transMaxAmt;
    /**
     * 费用金额
     */
    private BigDecimal feeAmt;
    /**
     * 是否占用 0 正常  1 占用
     */
    private Integer isOccupy;
    /**
     * 支付发起时间
     */
    private Date infTime;
    /**
     * 重试次数
     */
    private Integer infRetries;
    /**
     * 接口状态 0 未支付;1 支付完成 ;2 支付取消;  3待处理 10 接口未调用; 11 接口失败
     */
    private Integer infStatus;
    /**
     * 接口失败原因
     */
    private String infReason;
    /**
     * 银行流水号BankOrderNo 回执的
     */
    private String outTradeNo;
    /**
     * 对接外部代码
     */
    private String splitOrgCode;
    /**
     * 分账子账户
     */
    private String splitAcctCode;
    /**
     * 对接外部代码
     */
    private String outOrgCode;
    /**
     * 分账子账户
     */
    private String outAcctCode;
    /**
     * 资金状态 0未完成 1待结算;2可提现;3已提现;
     */
    private Integer status;
    /**
     * 状态时间
     */
    private Date statusTime;
    /**
     * 结算单id
     */
    private Long availId;
    /**
     * 结算单号
     */
    private String availNo;
    /**
     * 提现单id
     */
    private Long cashId;
    /**
     * 提现单号
     */
    private String cashNo;
    /**
     * 待结算时间，，订单和退单是接口完成时间
     */
    private Date freezeTime;
    /**
     * 转成可提现时间
     */
    private Date availTime;
    /**
     * 提现时间
     */
    private Date cashTime;
    /**
     * 备注
     */
    private String remark;
    /**
     * 删除标志（0代表存在 存入这条数据的主键代表删除）
     */
    @TableLogic(delval = "id")
    private Long delFlag;


}
