package cn.xianlink.trade.domain;

import cn.xianlink.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 结算单对象 trade_acc_avail
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("trade_acc_daily")
public class TradeAccDaily extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单 id 主键
     */
    @TableId(value = "id")
    private Long id;
    /**
     * trade_org_bank.id
     */
    private Long bankId;
    /**
     * 来源机构
     */
    private Long sourceOrgId;
    /**
     * 来源机构
     */
    private String sourceOrgCode;
    /**
     * 0 总仓 1 城市仓 2 供应商 3 门店
     */
    private Integer outOrgType;
    /**
     * 分账机构名称（企业名称）
     */
    private String outOrgName;
    /**
     * 分账机构子账户
     */
    private String outOrgCode;
    /**
     * 分账机构子账户
     */
    private String outAcctCode;
    /**
     * 账单日期
     */
    private LocalDate acctDate;

    /**
     * 昨日余额
     */
    private BigDecimal ydaBalanceAmt;
    /**
     * 余额
     */
    private BigDecimal balanceAmt;
    /**
     * 发生金额
     */
    private BigDecimal occurAmt;
    /**
     * 划转入金额
     */
    private BigDecimal inAmt;

    /**
     * 划转出金额
     */
    private BigDecimal outAmt;

    /**
     * 订单金额
     */
    private BigDecimal payAmt;

    /**
     * 退款总金额
     */
    private BigDecimal refundAmt;

    /**
     * 退货退款
     */
    private BigDecimal refundAmt1;

    /**
     * 差额退款
     */
    private BigDecimal refundAmt2;

    /**
     * 报损退款
     */
    private BigDecimal refundAmt3;

    /**
     * 未使用
     */
    private BigDecimal refundAmt4;
    /**
     * 提现退款
     */
    private BigDecimal cashAmt;
    /**
     * 营销金额
     */
    private BigDecimal marketAmt;
    /**
     * 佣金
     */
    private BigDecimal commissionAmt;

    /**
     * 删除标志（0代表存在 存入这条数据的主键代表删除）
     */
    @TableLogic(delval = "id")
    private Long delFlag;


}
