package cn.xianlink.trade.channel.weixinb2b.constant;

import cn.hutool.core.util.ObjectUtil;

public enum WeixinB2bPayInfStatusEnum {
    // 支付状态对应关系   转成 PayInfStatusEnum 枚举值
    ORDER_INIT("ORDER_INIT", "订单初始化", 0),
    ORDER_PRE_PAY("ORDER_PRE_PAY", "订单预下单成功", 0),
    ORDER_PAY_SUCC("ORDER_PAY_SUCC", "订单支付成功", 1),
    ORDER_CLOSE("ORDER_CLOSE", "订单已关闭", 2),
    ORDER_REFUND_PROCESSING("ORDER_REFUND_PROCESSING", "订单正在退款中", 1),
    ORDER_REFUND("ORDER_REFUND", "订单已有退款", 1);

    private final String status;
    private final String desc;
    private final Integer infStatus;

    WeixinB2bPayInfStatusEnum(String status, String desc, Integer infStatus) {
        this.status = status;
        this.desc = desc;
        this.infStatus = infStatus;
    }

    public String getStatus() {
        return status;
    }

    public Integer getInfStatus() {
        return infStatus;
    }

    public String getDesc() {
        return desc;
    }

    public static Integer getInfStatus(String status) {
        for (WeixinB2bPayInfStatusEnum e : values()) {
            if (ObjectUtil.equal(e.getStatus(), status)) {
                return e.getInfStatus();
            }
        }
        return -1;
    }

}
