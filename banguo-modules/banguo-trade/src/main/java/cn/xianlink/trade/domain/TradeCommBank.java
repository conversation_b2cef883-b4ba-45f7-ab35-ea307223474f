package cn.xianlink.trade.domain;

import cn.xianlink.common.core.validate.AddGroup;
import cn.xianlink.common.core.validate.EditGroup;
import cn.xianlink.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * trade_comm_bank
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("trade_comm_bank")
public class TradeCommBank extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id主键
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 客户id
     */
    private Long customerId;
    /**
     * 客户代码
     */
    private String customerCode;
    /**
     * 签约用户名，也是银行卡户名
     */
    private String personName;
    /**
     *签约人身份证，也是银行卡对应身份证
     */
    private String personIdCard;

    /**
     * 身份证正面
     */
    private String idCardFront;

    /**
     * 身份证反面
     */
    private String idCardBack;

    /**
     * 银行卡绑定手机号
     */
    private String bankMobile;
    /**
     * 银行卡号
     */
    private String bankAccount;
    /**
     * 银行超级网银号
     */
    private String bankEicon;
    /**
     * 银行支行名
     */
    private String bankBranch;
    /**
     * 签约合同url
     */
    private String contractUrl;
    /**
     * 解约合同url
     */
    private String cancelUrl;
    /**
     * 签约申请编号
     */
    private String outFlowId;
    /**
     * 签约发起时间
     */
    private Date infTime;
    /**
     * 有为签约状态 1已签约 2 已解约 3 受理中 4失败
     */
    private Integer infStatus;
    /**
     * 接口失败原因
     */
    private String infReason;
    /**
     * 删除标志（0代表存在 存入这条数据的主键代表删除）
     */
    @TableLogic(delval = "id")
    private Long delFlag;

    /**
     * 微信 openid
     */
    private String openId;

    /**
     * 签约渠道 1 有为  2 yzh
     */
    private Integer signChannel;
}