package cn.xianlink.trade.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import cn.xianlink.common.api.enums.trade.*;
import cn.xianlink.common.api.vo.RemoteBaseDataVo;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.enums.YNStatusEnum;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.system.api.model.LoginUser;
import cn.xianlink.common.api.util.BaseEntityAutoFill;
import cn.xianlink.trade.api.domain.bo.OrderPayBo;
import cn.xianlink.trade.api.domain.bo.OrderPayJobQueryBo;
import cn.xianlink.trade.comm.CommServiceHelper;
import cn.xianlink.trade.constant.Constants;
import cn.xianlink.trade.domain.*;
import cn.xianlink.trade.domain.bo.*;
import cn.xianlink.trade.domain.convert.pay.TradePayCustomerVoConvert;
import cn.xianlink.trade.domain.vo.*;
import cn.xianlink.trade.domain.vo.org.TradeOrgBankRelaVo;
import cn.xianlink.trade.mapper.TradePayMapper;
import cn.xianlink.trade.mapper.TradePayRecvMapper;
import cn.xianlink.trade.mapper.TradePayRefundMapper;
import cn.xianlink.trade.service.*;
import cn.xianlink.trade.service.biz.TradeBaseDataBizService;
import cn.xianlink.trade.service.biz.TradeBaseUtilBizService;
import cn.xianlink.trade.utils.MapstructPageUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 支付Service业务层处理
 *
 * <AUTHOR>
 * 2024-05-27
 */
@RequiredArgsConstructor
@CustomLog
@Service
public class TradePayServiceImpl implements ITradePayService {

    private final transient TradePayMapper baseMapper;
    private final transient TradePayRefundMapper tradePayRefundMapper;
    private final transient TradePayRecvMapper tradePayRecvMapper;
    private final transient ITradePayRefundService tradePayRefundService;
    private final transient ITradeAccTransService tradeAccTransService;
    private final transient ITradeAccSplitService tradeAccSplitService;
    private final transient ITradeCustTransService tradeCustTransService;
    private final transient ITradeOrgRelationService tradeOrgRelationService;
    private final transient TradeBaseUtilBizService tradeBaseUtilBizService;
    private final transient TradeBaseDataBizService tradeBaseDataBizService;
    private final transient CommServiceHelper commServiceHelper;
    private final static List<Integer> CustomerAvailStatus = Arrays.asList(AccountStatusEnum.CASH.getCode(),
            AccountStatusEnum.CHECK.getCode(), AccountStatusEnum.AVAIL.getCode());


    /**
     * 查询支付
     */
    @Override
    public List<TradePayVo> queryByNos(List<String> orderNos) {
        if (CollectionUtil.isEmpty(orderNos)) {
            return new ArrayList<>();
        }
        return baseMapper.selectVoList(Wrappers.lambdaQuery(TradePay.class)
                .select(TradePay::getOrderNo, TradePay::getInfTime, TradePay::getOrderDate)
                .in(TradePay::getOrderNo, orderNos).eq(TradePay::getDelFlag, 0));
    }

    /**
     * 查询支付
     */
    @Override
    public List<TradePayVo> queryByTradeNos(List<String> tradeNos) {
        if (CollectionUtil.isEmpty(tradeNos)) {
            return new ArrayList<>();
        }
        return baseMapper.selectVoList(Wrappers.lambdaQuery(TradePay.class)
                .select(TradePay::getOrderNo, TradePay::getOrderDate, TradePay::getTradeNo, TradePay::getOutChannelNo,
                        TradePay::getPayAmt, TradePay::getPaySplitAmt, TradePay::getOutSuccessTime, TradePay::getFeeRate,
                        TradePay::getRefundAmt, TradePay::getRefundSplitAmt, TradePay::getOutFeeAmt, TradePay::getFeeAmt,
                        TradePay::getRefundSeq, TradePay::getPayInfStatus, TradePay::getInfStatus, TradePay::getStatus, TradePay::getDelFlag)
                .in(TradePay::getTradeNo, tradeNos));
    }

    /**
     * 查询支付    trade_pay表 删除标记要手动处理！！！！！！ 不能使用 baseMapper.deleteById 方法 ！！！！！
     */
    @Override
    public TradePayVo queryByNo(String orderNo) {
        return baseMapper.selectVoOne(Wrappers.lambdaQuery(TradePay.class)
                .eq(TradePay::getOrderNo, orderNo).eq(TradePay::getDelFlag, 0));
    }

    @Override
    public TradePayVo queryByTradeNo(String tradeNo) {
        // 回调时查询， 不验证删除， 为了把已经删除的进行退款
        return baseMapper.selectVoOne(Wrappers.lambdaQuery(TradePay.class).eq(TradePay::getTradeNo, tradeNo));
    }

    @Override
    @BaseEntityAutoFill
    public TableDataInfo<TradePayPageVo> queryPageList(TradePayQueryBo bo) {
        LambdaQueryWrapper<TradePay> lqw = buildRefundQueryWrapper(bo);
        Page<TradePayVo> result = baseMapper.selectVoPage(bo.build(), lqw);
        return TableDataInfo.build(MapstructPageUtils.convert(result, TradePayPageVo.class));
    }

    @Override
    @BaseEntityAutoFill
    public List<TradePayPageVo> queryList(TradePayQueryBo bo) {
        LambdaQueryWrapper<TradePay> lqw = buildRefundQueryWrapper(bo);
        return MapstructUtils.convert(baseMapper.selectVoList(lqw), TradePayPageVo.class);
    }

    private LambdaQueryWrapper<TradePay> buildRefundQueryWrapper(TradePayQueryBo bo) {
        boolean isOrderDate = bo.getOrderDateStart() != null && bo.getOrderDateEnd() != null;
        boolean isAcctDate = bo.getAcctDateStart() != null && bo.getAcctDateEnd() != null;
        if (!isOrderDate && !isAcctDate) {
            throw new ServiceException("销售日期或记账日期必须输入一组");
        }
        LambdaQueryWrapper<TradePay> lqw = Wrappers.lambdaQuery();
        lqw.between(isOrderDate, TradePay::getOrderDate, bo.getOrderDateStart(), bo.getOrderDateEnd());
        lqw.between(isAcctDate, TradePay::getAcctDate, bo.getAcctDateStart(), bo.getAcctDateEnd());
        lqw.eq(StringUtils.isNotBlank(bo.getChannel()), TradePay::getChannel, bo.getChannel());
        // lqw.eq(StringUtils.isNotBlank(bo.getBusiType()), TradePay::getBusiType, bo.getBusiType());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderNo()), TradePay::getOrderNo, bo.getOrderNo());
        lqw.eq(StringUtils.isNotBlank(bo.getTradeNo()), TradePay::getTradeNo, bo.getTradeNo());
        lqw.eq(bo.getStatus() != null, TradePay::getStatus, bo.getStatus());
        // lqw.eq(bo.getInfStatus() != null, TradePay::getInfStatus, bo.getInfStatus());
        lqw.eq(bo.getPayInfStatus() != null, TradePay::getPayInfStatus, bo.getPayInfStatus());
        if (CollectionUtil.isNotEmpty(bo.getAcctOrgCodes())) {
            lqw.in(TradePay::getAcctOrgId, tradeBaseUtilBizService.queryIdsByCodes(bo.getAcctOrgCodes(), BaseTypeEnum.REGION_WH.getCode()));
        }
        if (CollectionUtil.isNotEmpty(bo.getTransOrgCodes())) {
            lqw.in(TradePay::getOrderOrgId, tradeBaseUtilBizService.queryIdsByCodes(bo.getTransOrgCodes(), BaseTypeEnum.CITY_WH.getCode()));
        }
        if (CollectionUtil.isNotEmpty(bo.getCustomerCodes())) {
            lqw.in(TradePay::getCustomerId, tradeBaseUtilBizService.queryIdsByCodes(bo.getCustomerCodes(), BaseTypeEnum.CUSTOMER.getCode()));
        }
        lqw.and(ll -> ll.eq(TradePay::getDelFlag, 0).or(l -> l.eq(TradePay::getRefundSeq, 10)));
        lqw.orderByDesc(TradePay::getId);
        return lqw;
    }

    /**
     * 查询支付列表
     */
    @Override
    public TableDataInfo<TradePayCustomerVo> queryCustomerPage(TradePayCustomerQueryBo queryBo) {
        Page<TradePayVo> tvos = queryPageList(queryBo, true);
        return TableDataInfo.build(tvos.getRecords().stream()
                .map(TradePayCustomerVoConvert.INSTANCE::toConvert).toList(), tvos.getTotal());
    }

    /**
     * 查询支付列表
     */
    @Override
    @BaseEntityAutoFill
    public TableDataInfo<TradePayCustomerVo> queryCityPage(TradePayCustomerQueryBo queryBo) {
        Page<TradePayVo> tvos = queryPageList(queryBo, false);
        return TableDataInfo.build(tvos.getRecords().stream()
                .map(TradePayCustomerVoConvert.INSTANCE::toConvert).toList(), tvos.getTotal());
    }

    private Page<TradePayVo> queryPageList(TradePayCustomerQueryBo queryBo, boolean isCustomer) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        LambdaQueryWrapper<TradePay> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(queryBo.getOrderNo()), TradePay::getOrderNo, queryBo.getOrderNo());
        if (isCustomer) {
            lqw.eq(TradePay::getCustomerId, queryBo.getCustomerId());
        } else {
            // TODO 城市仓时，使用管理机构查询
            lqw.eq(TradePay::getOrderOrgId, loginUser == null ? 0 : loginUser.getRelationId());
        }
        lqw.eq(TradePay::getInfStatus, PayInfStatusEnum.SUCCESS.getCode());
        lqw.ge(queryBo.getOrderDateStart() != null, TradePay::getOrderDate, queryBo.getOrderDateStart());
        lqw.le(queryBo.getOrderDateEnd() != null, TradePay::getOrderDate, queryBo.getOrderDateEnd());
        lqw.ne(TradePay::getStatus, 0);
        if (queryBo.getStatus() != null) {
            if (queryBo.getStatus() == 1) {
                lqw.eq(TradePay::getStatus, AccountStatusEnum.FREEZE.getCode());
            } else {
                // 给客户显示时， 2， 3  4 状态 未已结算状态
                lqw.in(TradePay::getStatus, CustomerAvailStatus);
            }
        }
        lqw.eq(TradePay::getDelFlag, 0);
        lqw.orderByDesc(TradePay::getId);
        // lqw.eq(TradePay::getDelFlag, 0);   支付查询流水时 不用加. 业务处理时要加
        Page<TradePayVo> result = baseMapper.selectVoPage(queryBo.build(), lqw);
        result.getRecords().forEach(vo -> {
            vo.setStatus(CustomerAvailStatus.contains(vo.getStatus()) ? AccountStatusEnum.AVAIL.getCode() : vo.getStatus());
            vo.setAvailAmt(vo.getPaySplitAmt().subtract(vo.getRefundSplitAmt()));
        });
        return result;
    }

    @Override
    public List<TradePayVo> queryPayProcessing(OrderPayJobQueryBo bo) {
        LambdaQueryWrapper<TradePay> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getChannel()), TradePay::getChannel, bo.getChannel());
        lqw.between(TradePay::getInfTime, bo.getInfTimeStart(), bo.getInfTimeEnd());
        lqw.in(TradePay::getPayInfStatus, Arrays.asList(PayInfStatusEnum.UNPAID.getCode(), PayInfStatusEnum.PRE_SUCCESS.getCode()));
        lqw.le(TradePay::getPayInfTime, DateUtil.offsetSecond(new Date(), -10));
        lqw.eq(TradePay::getDelFlag, 0);
        lqw.orderByAsc(TradePay::getId);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 查询支付未审核
     */
    @Override
    public List<TradePayVo> queryPayFailed(LocalDate transDate) {
        LambdaQueryWrapper<TradePay> lqw = Wrappers.lambdaQuery();
        lqw.between(TradePay::getInfTime, transDate, transDate.plusDays(1));
        lqw.and(l -> l.ne(TradePay::getInfStatus, PayInfStatusEnum.SUCCESS.getCode())
                .or(ll -> ll.ne(TradePay::getPayInfStatus, PayInfStatusEnum.SUCCESS.getCode())));
        lqw.eq(TradePay::getDelFlag, 0);
        return baseMapper.selectVoList(lqw);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TradePayVo insertByBo(boolean isOnlyPay, TradePayBo tbo, List<TradeAccTransBo> transBos) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        TradePay add = MapstructUtils.convert(tbo, TradePay.class);
        List<TradeAccTrans> addBos = MapstructUtils.convert(transBos, TradeAccTrans.class);
        add.setInfTime(Convert.toDate(DateUtil.now()));
        add.setInfStatus(PayInfStatusEnum.INF_INIT.getCode());
        add.setInfRetries(0);
        add.setPayInfTime(add.getInfTime());
        add.setPayInfStatus(add.getInfStatus());
        add.setBusiField(PayBusiTypeEnum.getFieldByCode(add.getBusiType()));
        add.setCustomerEntry((loginUser == null || !add.getCustomerId().equals(loginUser.getRelationId())) ? 0 : 1);
        try {
            insertPay(add);
        } catch (DuplicateKeyException dke) {
            insertPay(add); // 重试一次
        }
        if (PayChannelEnum.PAY_PINGAN_CLOUD_LARGE.getCode().equals(tbo.getChannel())) {
            // 大额支付， 插入转账记录
            TradePayRecv addRecv = new TradePayRecv();
            addRecv.setRecvNo(add.getTradeNo());
            addRecv.setRecvType(PayRecvBusiTypeEnum.CUSTOM_ORDER.getCode());
            addRecv.setRecvDate(LocalDate.now());
            addRecv.setRecvAmt(add.getPaySplitAmt());
            addRecv.setCustomerId(add.getCustomerId());
            addRecv.setCustomerCode(add.getCustomerCode());
            addRecv.setCustomerAcctCode(add.getCustomerAcctCode());
            addRecv.setCustomerOutCode(add.getCustomerOutCode());
            addRecv.setPayId(add.getId());
            addRecv.setOrderId(add.getOrderId());
            addRecv.setOrderNo(add.getOrderNo());
            addRecv.setOrderDate(add.getOrderDate());
            addRecv.setInfTime(add.getInfTime());
            addRecv.setInfStatus(add.getInfStatus());
            tradePayRecvMapper.insert(addRecv);
        } else if (PayChannelEnum.PAY_PINGAN_CLOUD_BALANCE.getCode().equals(tbo.getChannel())) {
            // 余额支付， 插入冲减流水， 并验证余额是否充足
            tradeCustTransService.updateFreezeByPay(add.getCustomerId(), add.getCustomerOutCode(), add.getPaySplitAmt());
        }
        TradePayVo payVo = MapstructUtils.convert(add, TradePayVo.class);
        payVo.setPayerKey(tbo.getPayerKey()); // 暂不保存
        if (CollectionUtil.isNotEmpty(addBos)) {
            Map<Long, RemoteBaseDataVo> skuMap = tradeBaseDataBizService.querySupplierSkuList(
                    addBos.stream().map(TradeAccTrans::getSkuId).filter(skuId -> skuId != 0L).collect(Collectors.toSet()).stream().toList()
            ).stream().collect(Collectors.toMap(RemoteBaseDataVo::getId, Function.identity()));
            Map<Long, RemoteBaseDataVo> commCustMap = tradeBaseUtilBizService.queryBaseListByIds(
                    addBos.stream().map(TradeAccTrans::getCommCustomerId).filter(customerId -> customerId != 0L).collect(Collectors.toSet()).stream().toList(),
                    AccountOrgTypeEnum.CUSTOMER.getCode()).stream().collect(Collectors.toMap(RemoteBaseDataVo::getId, Function.identity()));
            BigDecimal totalAmt = BigDecimal.ZERO;
            for (TradeAccTrans trans : addBos) {
                if (AccountOrgTypeEnum.SUPPLIER.getCode().equals(trans.getOrgType())) {
                    RemoteBaseDataVo dataVo = skuMap.get(trans.getSkuId());
                    if (dataVo == null) {
                        throw new ServiceException(String.format("批次商品id %s不存在",
                                trans.getSkuId() == null ? "" : trans.getSkuId()));
                    }
                    trans.setSkuName(dataVo.getName());
                }
                if (trans.getCommCustomerId() != 0) {
                    RemoteBaseDataVo dataVo = commCustMap.get(trans.getCommCustomerId());
                    if (dataVo == null) {
                        throw new ServiceException(String.format("分销客户id %s不存在",
                                trans.getCommCustomerId() == 0L ? "" : trans.getCommCustomerId()));
                    }
                }
                if (trans.getTransAmt().compareTo(BigDecimal.ZERO) < 0) {
                    throw new ServiceException("原金额不能小于零");
                } else if (trans.getTransAmt().compareTo(BigDecimal.ZERO) == 0) {
                    trans.setInfStatus(PayInfStatusEnum.SUCCESS.getCode());
                }
                if (trans.getSplitAmt().compareTo(trans.getTransAmt()) > 0) {
                    throw new ServiceException("实付金额不能大于原金额");
                }
                if (trans.getCommissionAmt().compareTo(trans.getTransAmt()) > 0) {
                    throw new ServiceException("佣金不能大于原金额");
                }
                if (trans.getCommSalaryAmt().compareTo(trans.getCommissionAmt()) > 0) {
                    throw new ServiceException("发薪金额不能大于佣金");
                }
                totalAmt = totalAmt.add(trans.getTransAmt());  // transAmt 含 feeAmt
                setTransRow(trans, add);
            }
            if (totalAmt.compareTo(payVo.getPayAmt()) != 0) {
                throw new ServiceException("分账金额与总金额不一致");
            }
            TradeAccTrans commTransRow = getInsertCommTransRow(addBos);
            if (commTransRow != null) {
                setTransRow(commTransRow, add);
                addBos.add(commTransRow);
            }
            payVo.setSplits(tradeAccSplitService.batchInsertByBo(!isOnlyPay, new TradeAccSplitAddBo().setChannel(payVo.getChannel())
                    .setTradeNo(payVo.getTradeNo()).setTotalAmt(payVo.getPaySplitAmt())
                    .setInfTime(payVo.getInfTime()).setInitTime(payVo.getCreateTime()), addBos));
        }
        return payVo;
    }

    private void setTransRow(TradeAccTrans trans, TradePay add) {
        trans.setTransType(AccountTransTypeEnum.OP.getCode());
        trans.setTransId(add.getOrderId());
        trans.setTransNo(add.getOrderNo());
        trans.setTransDate(add.getOrderDate());
        trans.setTransOrgId(add.getOrderOrgId());
        trans.setAcctOrgId(add.getAcctOrgId());
        trans.setBusiType(add.getBusiType());
        trans.setBusiField(add.getBusiField());
        trans.setRelateType(AccountTransTypeEnum.OP.getCode());
        trans.setRelateNo(add.getOrderNo());  // 订单关联单号
        trans.setRelateId(add.getOrderId());
        trans.setRelateAmt(add.getPayAmt());
        trans.setTotalAmt(add.getPayAmt());
        trans.setSplitRelNo("");
        trans.setSplitRelAmt(BigDecimal.ZERO);
        trans.setStatusTime(Convert.toDate(DateUtil.now()));
        trans.setSplitOrgCode(trans.getOutOrgCode());
        trans.setSplitAcctCode(trans.getOutAcctCode());
    }

    private TradeAccTrans getInsertCommTransRow(List<TradeAccTrans> transList) {
        BigDecimal commissionAmt = BigDecimal.ZERO;
        BigDecimal transMinAmt = BigDecimal.ZERO;
        for (TradeAccTrans trans : transList) {
            commissionAmt = commissionAmt.add(trans.getCommissionAmt());
            transMinAmt = transMinAmt.add(trans.getTransMinAmt());
        }
        if (commissionAmt.compareTo(BigDecimal.ZERO) == 0) {
            return null;
        }
        TradeOrgBankRelaVo commOrgVo = tradeOrgRelationService.queryBankByCode(
                commServiceHelper.getCommAccOrgCode(), AccountOrgTypeEnum.REGION_WH.getCode());
        TradeAccTrans trans = new TradeAccTrans();
        trans.setTransAmt(commissionAmt);
        trans.setSplitAmt(transMinAmt);
        trans.setCommissionAmt(BigDecimal.ZERO);
        trans.setCommSalaryAmt(BigDecimal.ZERO);
        trans.setTransMinAmt(trans.getCommissionAmt());
        trans.setTransMaxAmt(trans.getSplitAmt());
        trans.setFeeAmt(BigDecimal.ZERO);
        trans.setDeptId(0L);
        trans.setSkuId(0L);
        trans.setCommCustomerId(0L);
        trans.setOrgType(commOrgVo.getOrgType());
        trans.setOrgId(commOrgVo.getOrgId());
        trans.setOrgCode(commOrgVo.getOrgCode());
        trans.setOutOrgCode(commOrgVo.getOutOrgCode());
        trans.setOutAcctCode(commOrgVo.getOutAcctCode());
        return trans;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TradePayVo updatePayCheckByBo(TradePayBo tbo, TradePayVo tvo, List<TradeAccTransBo> accTransBos) {
        if (tbo.getPayAmt().compareTo(tvo.getPayAmt()) != 0 || tbo.getPaySplitAmt().compareTo(tvo.getPaySplitAmt()) != 0) {
            throw new ServiceException("同支付单金额不一致");
        }
        LoginUser loginUser = LoginHelper.getLoginUser();
        TradePay update = MapstructUtils.convert(tbo, TradePay.class);
        update.setId(tvo.getId());
        update.setInfTime(Convert.toDate(DateUtil.now()));
        update.setPayInfTime(update.getInfTime());
        update.setBusiField(PayBusiTypeEnum.getFieldByCode(update.getBusiType()));
        update.setCustomerEntry((loginUser == null || !update.getCustomerId().equals(loginUser.getRelationId())) ? 0 : 1);
        boolean isUpdate;
        try {
            isUpdate = updatePay(update);
        } catch (DuplicateKeyException dke) {
            isUpdate = updatePay(update); // 重试一次
        }
        if (!isUpdate) {
            throw new ServiceException("支付单不是待支付状态");
        }
        List<TradeAccTrans> updateBos = MapstructUtils.convert(accTransBos, TradeAccTrans.class);
        updateBos.forEach(bo -> {
            if (bo.getSkuId() == null) {
                bo.setSkuId(0L);
            }
        });
        List<TradeAccTransVo> accTransVos = tradeAccTransService.queryTransList(tvo.getOrderNo(), Constants.PayTypes);
        Map<String, List<TradeAccTransVo>> transVoMaps = accTransVos.stream().collect(
                Collectors.groupingBy(vo -> String.format("%s_%s", vo.getTransId(), vo.getSkuId()), Collectors.toList()));
        Map<String, List<TradeAccTrans>> transBoMaps = updateBos.stream().collect(
                Collectors.groupingBy(vo -> String.format("%s_%s", tvo.getOrderId(), vo.getSkuId()), Collectors.toList()));
        if (transVoMaps.size() != transBoMaps.size()) {
            throw new ServiceException("支付单数据异常，", R.FAIL);
        }
        for (Map.Entry<String, List<TradeAccTransVo>> entry : transVoMaps.entrySet()) {
            List<TradeAccTrans> transBos = transBoMaps.get(entry.getKey());
            List<TradeAccTransVo> transVos = entry.getValue();
            if (transBos == null) {
                throw new ServiceException(String.format("批次商品id %s不存在",
                        transVos.get(0).getSkuId() == 0L ? "" : transVos.get(0).getSkuId()));
            }
            if (transVos.get(0).getTransAmt().compareTo(transBos.get(0).getTransAmt()) != 0
                    || transVos.get(0).getSplitAmt().compareTo(transBos.get(0).getSplitAmt()) != 0
                    || transVos.get(0).getCommissionAmt().compareTo(transBos.get(0).getCommissionAmt()) != 0) {
                throw new ServiceException(String.format("批次商品id %s金额不一致",
                        transVos.get(0).getSkuId() == 0L ? "" : transVos.get(0).getSkuId()));
            }
        }
        tvo.setPayerKey(tbo.getPayerKey()); // 暂不保存
        tvo.setTradeNo(update.getTradeNo());
        return tvo;
    }

    private void insertPay(TradePay add) {
        add.setTradeNo(tradeBaseUtilBizService.getNextTradeNo(add.getOrderDate()));
        baseMapper.insert(add);
    }

    private boolean updatePay(TradePay update) {
        update.setTradeNo(tradeBaseUtilBizService.getNextTradeNo(update.getOrderDate()));
        return baseMapper.update(update, Wrappers.lambdaUpdate(TradePay.class)
                .eq(TradePay::getId, update.getId()).eq(TradePay::getDelFlag, 0)
                .eq(TradePay::getPayInfStatus, PayInfStatusEnum.UNPAID.getCode())) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePayInfData(boolean isOnlyPay, TradePayBo tbo, TradePayVo tvo) {
        TradePay update = MapstructUtils.convert(tbo, TradePay.class);
        boolean isFail = PayInfStatusEnum.CANCEL.getCode().equals(update.getPayInfStatus());
        update.setDelFlag(isFail ? tbo.getId() : 0);
        if (update.getOutSuccessTime() != null) {
            update.setAcctDate(LocalDateTimeUtil.of(update.getOutSuccessTime()).toLocalDate());
        }
        if (PayInfStatusEnum.UNPAID.getCode().equals(update.getPayInfStatus())
                && PayInfStatusEnum.PRE_SUCCESS.getCode().equals(tvo.getPayInfStatus())) {
            // 如果已经是预支付状态， 则该状态不不变
            update.setPayInfStatus(PayInfStatusEnum.PRE_SUCCESS.getCode());
        }
        if (PayInfStatusEnum.SUCCESS.getCode().equals(update.getPayInfStatus())) {
            update.setInfStatus(PayChannelEnum.INNER_ZERO.getCode().equals(tvo.getChannel()) ?
                    PayInfStatusEnum.SUCCESS.getCode() : PayInfStatusEnum.CHECK.getCode());
            update.setInfTime(Convert.toDate(DateUtil.now()));
        }
        boolean isSuccess = baseMapper.update(update, Wrappers.lambdaUpdate(TradePay.class)
                .eq(TradePay::getId, update.getId()).eq(TradePay::getDelFlag, 0)
                .ne(TradePay::getPayInfStatus, PayInfStatusEnum.SUCCESS.getCode())) > 0;
        if (isSuccess) {
            if (isFail) {
                // 支付失败， 删除分帐， 大额支付回执
                tradeAccSplitService.deleteSplit(true, new TradeAccSplitUpdateBo()
                        .setTransNo(tvo.getOrderNo()).setTransTypes(Constants.PayTypes).setInfReason(update.getInfReason()));
            } else if (!isOnlyPay && PayInfStatusEnum.SUCCESS.getCode().equals(update.getPayInfStatus())) {
                //  分帐结束后，更新分帐信息 (仅平安支付时才会调用到 ！！！)
                tradeAccSplitService.updateSplit(new TradeAccSplitUpdateBo()
                        .setTransNo(tvo.getOrderNo()).setTransTypes(Constants.PayTypes)
                        .setInfStatus(PayInfStatusEnum.SUCCESS.getCode()).setOutSuccessTime(update.getOutSuccessTime())
                        .setOutTradeNo(update.getOutTradeNo()));
            }
            if (PayChannelEnum.PAY_PINGAN_CLOUD_LARGE.getCode().equals(tvo.getChannel())) {
                TradePayRecv recv = MapstructUtils.convert(tbo.getTradePayRecvBo(), TradePayRecv.class);
                recv.setAcctDate(update.getAcctDate());
                tradePayRecvMapper.update(recv, Wrappers.lambdaUpdate(TradePayRecv.class).setSql(isFail, "del_flag=id")
                        .eq(TradePayRecv::getRecvNo, recv.getRecvNo()).eq(TradePayRecv::getDelFlag, 0));
            } else if ( PayChannelEnum.PAY_PINGAN_CLOUD_BALANCE.getCode().equals(tvo.getChannel())) {
                if (isFail) {
                    tradeCustTransService.updateFreezeByPay(tvo.getCustomerId(), tvo.getCustomerOutCode(), tvo.getPaySplitAmt().negate());
                } else {
                    tradeCustTransService.insertTransByPay(update.getOutSuccessTime(), tvo);
                }
            }
        }
        return isSuccess;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePayInfFail(boolean isOnlyPay, TradePayVo tvo, ServiceException se) {
        String message = StrUtil.sub(se.getMessage(), 0, 128);
        TradePay update = new TradePay();
        update.setDelFlag(tvo.getId());
        if (!isOnlyPay) {
            update.setInfStatus(PayInfStatusEnum.INF_FAIL.getCode());
        }
        update.setPayInfStatus(PayInfStatusEnum.INF_FAIL.getCode());
        update.setInfReason(message);
        int count = baseMapper.update(update, Wrappers.lambdaQuery(TradePay.class)
                .eq(TradePay::getId, tvo.getId()).eq(TradePay::getDelFlag, 0));
        if (count > 0) {
            tradeAccSplitService.deleteTrans(new TradeAccSplitUpdateBo()
                    .setTransNo(tvo.getOrderNo()).setTransTypes(Constants.PayTypes).setInfReason(message));
            if (PayChannelEnum.PAY_PINGAN_CLOUD_LARGE.getCode().equals(tvo.getChannel())) {
                tradePayRecvMapper.update(Wrappers.lambdaUpdate(TradePayRecv.class)
                        .setSql("del_flag=id").set(TradePayRecv::getInfStatus, update.getPayInfStatus())
                        .set(TradePayRecv::getInfReason, update.getInfReason())
                        .eq(TradePayRecv::getRecvNo, tvo.getTradeNo()).eq(TradePayRecv::getDelFlag, 0));
            } else if (PayChannelEnum.PAY_PINGAN_CLOUD_BALANCE.getCode().equals(tvo.getChannel())) {
                tradeCustTransService.updateFreezeByPay(tvo.getCustomerId(), tvo.getCustomerOutCode(), tvo.getPaySplitAmt().negate());
            }
        }
    }

    /**
     * 分账
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TradePayVo updateSplitCheckByBo(TradePayVo tvo) {
        TradePay update = new TradePay();
        update.setId(tvo.getId());
        update.setInfRetries(tvo.getInfRetries() + 1);
        update.setInfTime(Convert.toDate(DateUtil.now()));
        update.setInfStatus(PayInfStatusEnum.UNPAID.getCode());
        if (baseMapper.update(update, Wrappers.lambdaUpdate(TradePay.class)
                .eq(TradePay::getId, update.getId()).eq(TradePay::getDelFlag, 0)
                .eq(TradePay::getPayInfStatus, PayInfStatusEnum.SUCCESS.getCode())
                .and(l -> l.eq(TradePay::getInfStatus, PayInfStatusEnum.CHECK.getCode())
                        .or(ll -> ll.eq(TradePay::getInfStatus, PayInfStatusEnum.UNPAID.getCode())
                                .le(TradePay::getInfTime, DateUtil.offsetSecond(new Date(), -10))))) == 0) {
            throw new ServiceException(String.format("支付单 %s 接口状态已改变", tvo.getOrderNo()));
        }
        // 更新分账单号 和 记账日期
        List<TradeAccTransVo> transVos = tradeAccTransService.queryTransList(tvo.getOrderNo(), Constants.PayTypes)
                .stream().filter(vo -> !PayInfStatusEnum.SUCCESS.getCode().equals(vo.getInfStatus())
                        && !PayInfStatusEnum.CHECK.getCode().equals(vo.getInfStatus())).toList();
        BeanUtil.copyProperties(update, tvo, Constants.BeanCopyIgnoreNullValue);
        // 分账明细是 null 的问题
        tvo.setSplits(tradeAccSplitService.batchUpdateByNo(new TradeAccSplitAddBo().setChannel(tvo.getChannel())
                        .setTradeNo(tvo.getTradeNo()).setTotalAmt(tvo.getPaySplitAmt())
                        .setInfTime(tvo.getInfTime()).setInitTime(tvo.getCreateTime()),
                MapstructUtils.convert(transVos, TradeAccTrans.class)));
        return tvo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSplitInfData(boolean isEnd, TradePayVo pvo) {
        if (isEnd) {
            TradePay update = new TradePay();
            update.setId(pvo.getId());
            update.setInfStatus(PayInfStatusEnum.SUCCESS.getCode());
            update.setInfReason("");
            baseMapper.updateById(update);
            /* 退款单在未调用支付分账前调用，给退款单回填新的分账单号 */
            List<TradePayRefundVo> refundVos = tradePayRefundMapper.selectVoList(Wrappers.lambdaQuery(TradePayRefund.class)
                    .select(TradePayRefund::getRefundNo, TradePayRefund::getInfStatus)
                    .eq(TradePayRefund::getOrderNo, pvo.getOrderNo())
                    .ne(TradePayRefund::getInfStatus, RefundInfStatusEnum.SUCCESS.getCode())
                    .eq(TradePayRefund::getDelFlag, YNStatusEnum.DISABLE.getCode()));
            for (TradePayRefundVo refundVo : refundVos) {
                tradePayRefundService.updateRefundSplitNo(pvo.getOrderNo(), refundVo.getRefundNo());
            } 
        }
        tradeAccSplitService.updateSplit(new TradeAccSplitUpdateBo()
                .setTransNo(pvo.getOrderNo()).setTransTypes(Constants.PayTypes), pvo.getSplits());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSplitInfFail(TradePayVo tvo, ServiceException se) {
        String message = StrUtil.sub(se.getMessage(), 0, 128);
        TradePay update = new TradePay();
        update.setId(tvo.getId());
        update.setInfStatus(PayInfStatusEnum.CHECK.getCode());
        update.setInfReason(message);
        int count = baseMapper.updateById(update);
        if (count > 0) {
            tradeAccSplitService.deleteSplit(false, new TradeAccSplitUpdateBo()
                    .setTransNo(tvo.getOrderNo()).setTransTypes(Constants.PayTypes).setInfReason(message));
        }
    }

    @Override
    public boolean querySame(OrderPayBo bo, TradePayVo tvo) {
        return tvo.getPayAmt().compareTo(bo.getPayAmt()) == 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAcctStatus(LocalDate acctDate, List<TradePayBo> bos) {
        if (bos.size() > 0) {
            baseMapper.updateAcctStatus(acctDate, bos);
        }
    }

}