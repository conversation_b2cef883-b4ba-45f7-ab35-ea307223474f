package cn.xianlink.trade.mapper;

import cn.xianlink.common.mybatis.core.mapper.BaseMapperPlus;
import cn.xianlink.trade.domain.TradeAccCash;
import cn.xianlink.trade.domain.vo.TradeAccCashVo;
import org.apache.ibatis.annotations.Param;

/**
 * 提现单Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
public interface TradeAccCashMapper extends BaseMapperPlus<TradeAccCash, TradeAccCashVo> {

    int updateCashAmt(@Param("cashId") Long cashId);

}
