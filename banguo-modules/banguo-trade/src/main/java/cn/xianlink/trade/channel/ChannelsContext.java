package cn.xianlink.trade.channel;

import cn.dev33.satoken.context.SaHolder;
import cn.dev33.satoken.stp.SaLoginModel;
import cn.dev33.satoken.stp.StpUtil;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.redis.utils.RedisUtils;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.system.api.model.LoginUser;
import cn.xianlink.trade.config.properties.ChannelsProperties;
import cn.xianlink.trade.constant.TradeCacheNames;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.List;
import java.util.Map;

/**
 * 支付宝付款码支付
 *  发布名： 渠道名
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@RequiredArgsConstructor
@Service
public class ChannelsContext {
    private static final String ACCOUNT_SERVICE_SUFFIX = "AccountServiceImpl";
    private static final String PAYMENT_SERVICE_SUFFIX = "PayServiceImpl";
    private final transient Map<String, AccountService> accountServices;
    private final transient Map<String, PaymentService> paymentServices;
    private final transient Map<String, AccountService> appIdAccountMap;
    private transient ChannelsProperties.WebUserProperties webUserProperties;

    public void setWebUser(ChannelsProperties.WebUserProperties webUserProperties) {
        this.webUserProperties = webUserProperties;
    }

    public AccountService initAccount(String channel, List<String> appIds) {
        AccountService accountService = accountServices.get(channel + ACCOUNT_SERVICE_SUFFIX);
        if (accountService == null) {
            // 测试时取第一个，就是测试环境的
            accountService = accountServices.values().iterator().next();
        }
        for (String appId : appIds.stream().distinct().toList()) {
            appIdAccountMap.put(appId, accountService);
        }
        return accountService;
    }

    public AccountService getAccount(String channel, String appId) {
        return appIdAccountMap.get(String.format("%s_%s", channel, appId));
    }

    public PaymentService initPayment(String channel) {
        return paymentServices.get(channel + PAYMENT_SERVICE_SUFFIX);
    }

    public PaymentService getPayment(String channel, String appId) {
        PaymentService paymentService = paymentServices.get(channel + PAYMENT_SERVICE_SUFFIX);
        if (paymentService == null) {
            throw new ServiceException("支付渠道不存在");
        }
        paymentService.switchover(appId);
        return paymentService;
    }

    public void initWebUserToken() {
        LoginUser user = webUserProperties.getLoginUser();
        String cacheKey = TradeCacheNames.ONLINE_TOKEN_KEY + user.getUserId();
        SaHolder.getStorage().set(LoginHelper.TENANT_KEY, user.getTenantId());
        String tokenValue = RedisUtils.getCacheObject(cacheKey);
        if (tokenValue == null) {
            SaLoginModel model = webUserProperties.getSaLoginModel();
            LoginHelper.login(user, model);
            RedisUtils.setCacheObject(cacheKey, StpUtil.getTokenValue(), Duration.ofMillis(model.timeout));
        } else {
            StpUtil.setTokenValue(tokenValue);
        }
    }


}
