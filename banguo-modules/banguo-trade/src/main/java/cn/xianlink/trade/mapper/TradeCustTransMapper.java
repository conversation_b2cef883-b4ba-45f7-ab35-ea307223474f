package cn.xianlink.trade.mapper;

import cn.xianlink.common.mybatis.core.mapper.BaseMapperPlus;
import cn.xianlink.trade.domain.TradeCustTrans;
import cn.xianlink.trade.domain.bo.TradeCustTransBo;
import cn.xianlink.trade.domain.vo.TradeCustTransVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 分账变更流水Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
public interface TradeCustTransMapper extends BaseMapperPlus<TradeCustTrans, TradeCustTransVo> {

    void updateCustBalAmt(@Param("bos") List<TradeCustTransBo> bos);

}
