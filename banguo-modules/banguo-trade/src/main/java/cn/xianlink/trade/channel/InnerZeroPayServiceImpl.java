package cn.xianlink.trade.channel;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.xianlink.common.api.enums.trade.PayInfStatusEnum;
import cn.xianlink.common.api.enums.trade.RefundInfStatusEnum;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.trade.api.domain.bo.OrderRefundBo;
import cn.xianlink.trade.domain.bo.TradePayBo;
import cn.xianlink.trade.domain.bo.TradePayRefundBo;
import cn.xianlink.trade.domain.vo.TradePayRefundVo;
import cn.xianlink.trade.domain.vo.TradePayVo;
import com.alibaba.fastjson.JSONObject;
import jakarta.servlet.http.HttpServletRequest;
import lombok.CustomLog;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024-05-27
 */
@CustomLog
@Component
public class InnerZeroPayServiceImpl implements PaymentService {

    @Override
    public int getSplitCount() {
        return 20;
    };

    @Override
    public TradePayBo pay(TradePayVo vo) {
        TradePayBo respBo = payQuery(vo);
        respBo.setCallbackWeixin(new JSONObject()
                .fluentPut("channel", vo.getChannel())
                .fluentPut("params", new JSONObject()).toJSONString());
        return respBo;
    }

    @Override
    public TradePayBo payQuery(TradePayVo vo) {
        TradePayBo respBo = new TradePayBo();
        respBo.setId(vo.getId());
        respBo.setPayInfStatus(PayInfStatusEnum.SUCCESS.getCode());
        respBo.setInfStatus(PayInfStatusEnum.SUCCESS.getCode());
        respBo.setInfTime(Convert.toDate(DateUtil.now()));
        respBo.setOutCashAmt(vo.getPaySplitAmt());
        respBo.setOutSuccessTime(respBo.getInfTime());
        return respBo;
    }

    @Override
    public TradePayBo payClose(TradePayVo vo) {
        return payQuery(vo);
    }

    @Override
    public JSONObject callback(String reqBody, HttpServletRequest request) {
        return null;
    }

    @Override
    public TradePayBo payCallback(JSONObject resp) {
        return null;
    }

    @Override
    public int refundValidate(OrderRefundBo bo, TradePayVo payVo) {
        if(CollectionUtil.isEmpty(bo.getSplits())){
            throw new ServiceException("分账数据不能为空");
        }
        return 0;
    }

    @Override
    public TradePayRefundBo refund(TradePayRefundVo vo) {
        TradePayRefundBo respBo = new TradePayRefundBo();
        respBo.setId(vo.getId());
        respBo.setRefundInfStatus(RefundInfStatusEnum.SUCCESS.getCode());
        respBo.setInfStatus(RefundInfStatusEnum.SUCCESS.getCode());
        respBo.setInfTime(Convert.toDate(DateUtil.now()));
        respBo.setOutCashAmt(vo.getRefundSplitAmt());
        respBo.setOutSuccessTime(respBo.getInfTime());
        return respBo;
    }

    @Override
    public TradePayRefundBo refundQuery(TradePayRefundVo vo) {
        return refund(vo);
    }

}
