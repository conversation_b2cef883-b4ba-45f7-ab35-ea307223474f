package cn.xianlink.trade.service.biz;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.xianlink.common.api.enums.trade.*;
import cn.xianlink.common.api.vo.RemoteBaseDataVo;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.json.utils.JsonUtils;
import cn.xianlink.trade.api.domain.bo.OrderRefundQueryBo;
import cn.xianlink.trade.api.domain.bo.RemoteTransferChangeBo;
import cn.xianlink.trade.api.domain.bo.RemoteTransferCreateBo;
import cn.xianlink.trade.api.domain.bo.RemoteTransferSplitBo;
import cn.xianlink.trade.channel.AccountService;
import cn.xianlink.trade.config.properties.ChannelsProperties;
import cn.xianlink.trade.constant.Constants;
import cn.xianlink.trade.domain.bo.*;
import cn.xianlink.trade.domain.vo.TradeAccAvailVo;
import cn.xianlink.trade.domain.vo.TradeAccTransVo;
import cn.xianlink.trade.domain.vo.TradeAccTransferVo;
import cn.xianlink.trade.domain.vo.org.TradeOrgBankBindVo;
import cn.xianlink.trade.mq.producer.TradeAccTransferChangeProducer;
import cn.xianlink.trade.service.ITradeAccAvailService;
import cn.xianlink.trade.service.ITradeAccTransService;
import cn.xianlink.trade.service.ITradeAccTransferService;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import net.bytebuddy.asm.Advice;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * status 状态转换类
 *  INIT
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@CustomLog
@RequiredArgsConstructor
@Service
public class TradeAccTransBizService {
    private final transient ChannelsProperties channelsProperties;
    private final transient ITradeAccTransService tradeAccTransService;
    private final transient ITradeAccAvailService tradeAccAvailService;
    private final transient ITradeAccTransferService tradeAccTransferService;
    private final transient TradeBaseDataBizService tradeBaseDataBizService;
    private final transient TradeOrgBankBizService tradeOrgBankBizService;
    private final transient TradeAccTransferChangeProducer tradeAccTransferChangeProducer;
    private final transient AccountService accountService;

    /*
         整单退款 0 -> 6
     */
    public void updateCancel(String transNo, List<String> transTypes, Date statusTime) {
        log.keyword(transNo, "updateCancel").info("转取消");
        updateStatus(AccountStatusEnum.INTI.getCode(), AccountStatusEnum.CANCEL.getCode(), transNo, transTypes, statusTime);
        log.keyword(transNo, "updateCancel").info("转取消完成");
    }
    /*
        整单订单 和 退款 更新成 0 -> 1
     */
    public List<TradeAccTransVo> updateFreeze(String transNo, List<String> transTypes, Date statusTime) {
        log.keyword(transNo, "updateFreeze").info("转待结算");
        List<TradeAccTransVo> vos = updateStatus(AccountStatusEnum.INTI.getCode(), AccountStatusEnum.FREEZE.getCode(), transNo, transTypes, statusTime);
        log.keyword(transNo, "updateFreeze").info("转待结算完成");
        return vos;
    }

    public void updateCancelFreeze(String transNo, List<String> transTypes) {
        log.keyword(transNo, "updateCancelFreeze").info("转初始");
        updateStatus(AccountStatusEnum.FREEZE.getCode(), AccountStatusEnum.INTI.getCode(), transNo, transTypes, null);
        log.keyword(transNo, "updateCancelFreeze").info("转初始完成");
    }

    /*
        整单的状态改变，流水中 preStatus 要一致
     */
    private List<TradeAccTransVo> updateStatus(Integer preStatus, Integer status,
                                               String transNo, List<String> transTypes, Date statusTime) {
        TradeAccTransChangeBo changeBo = new TradeAccTransChangeBo().setTransNo(transNo)
                .setTransTypes(transTypes).setStatusTime(statusTime).setPreStatus(preStatus).setStatus(status).setTrans(new ArrayList<>());
        List<TradeAccTransVo> vos = tradeAccTransService.queryStatusListByNo(changeBo, null);
        for (TradeAccTransVo vo : vos) {
            if (!vo.getStatus().equals(changeBo.getPreStatus()) || vo.getAvailId() != null) {
                continue;
            }
            changeBo.getTrans().add(new TradeAccTransAvaildsBo().setTransId(vo.getId()).setCommissionAmt(vo.getCommissionAmt()));
        }
        tradeAccTransService.updateStatus(changeBo);
        return vos;
    }

    /*
       取消退款 使整个订单 + 之前的退款 + 划转  都从  更新成 1 -> 6  和  0 -> 6
    */
    public void updateCancelByRelate(String relateNo) {
        log.keyword(relateNo, "updateCancelByRelate").info("转订单取消（整单）");
        TradeAccTransChangeBo changeBo = new TradeAccTransChangeBo().setTransNo(relateNo).setTransTypes(Constants.PayTypes).setStatusTime(null)
                .setPreStatus(AccountStatusEnum.FREEZE.getCode()).setStatus(AccountStatusEnum.CANCEL.getCode()).setTrans(new ArrayList<>());
        updateStatusByRelate(changeBo); // 仅处理已经转成 FREEZE 的
        log.keyword(relateNo, "updateCancelByRelate").info("转订单取消完成（整单）");
    }

    private List<TradeAccTransVo> updateStatusByRelate(TradeAccTransChangeBo changeBo) {
        List<TradeAccTransVo> vos = tradeAccTransService.queryStatusListByRelate(changeBo);
        Map<String, TradeAccTransChangeBo> tboMap = new HashMap<>();
        for (TradeAccTransVo vo : vos) {
            if (!vo.getStatus().equals(changeBo.getPreStatus()) || vo.getAvailId() != null) {
                continue;
            }
            TradeAccTransChangeBo tbo = getTradeTransChange(changeBo, vo, tboMap);
            tbo.getTrans().add(new TradeAccTransAvaildsBo().setTransId(vo.getId()).setCommissionAmt(vo.getCommissionAmt()));
        }
        tboMap.forEach((key, tbo) -> tradeAccTransService.updateStatus(tbo));
        return vos;
    }

    /*
        1 订单+退款完结调用  relateType=OP  订单号 + 供应商 + skuId 来结算完成
            增加限制：　执行前　　订单号 + 供应商 + skuId　对应数据必须全是 FREEZE 状态 ！
            仅记入结算单， 不改变 FREEZE 状态  （FREEZE 转 AVAIL 在 job 中实现 tradeAvailProcessingJob）
        2 划转 relateType=TI
     */
    public List<TradeAccTransVo> updateAvailInitByRelate(String relateNo, String relateType, LocalDate availDate, List<TradeAccTransAvaildsBo> bos) {
        if (AccountTransTypeEnum.OP.getCode().equals(relateType)) {
            // 1 (换供应商) 对所有未结且金额合计为零的， 执行全部完结
            try {
                List<TradeAccTransAvaildsBo> cancelBos = tradeAccTransService.querySupplierFreezeTrans(relateNo).stream()
                        .filter(vo -> vo.getTransAmt().compareTo(BigDecimal.ZERO) == 0).map(vo ->
                            new TradeAccTransAvaildsBo().setOrgType(vo.getOrgType()).setOrgCode(vo.getOrgCode()).setSkuId(vo.getSkuId())
                        ).toList();
                if (cancelBos.size() > 0) {
                    updateStatusByRelate(new TradeAccTransChangeBo().setTransNo(relateNo).setTransTypes(Constants.PayTypes).setStatusTime(null)
                            .setPreStatus(AccountStatusEnum.FREEZE.getCode()).setStatus(AccountStatusEnum.CANCEL.getCode()).setTrans(cancelBos));
                }
            } catch (ServiceException se) {
                log.keyword(relateNo, "updateAvailInitByRelate").warn("转取消， 合计为零的剩余数据", se);
            }
        }
        // 2 执行对应单据的可提现处理
        return _updateAvailInitByRelate(relateNo, relateType, availDate, bos);
    }

    /*
        划转单入口
     */
    private List<TradeAccTransVo> _updateAvailInitByRelate(String relateNo, String relateType, LocalDate availDate, List<TradeAccTransAvaildsBo> bos) {
        log.keyword(relateNo, "updateAvailInitByRelate").info("转结算单（skuId） json=" + JsonUtils.toJsonString(bos));
        boolean isTransfer = AccountTransTypeEnum.TI.getCode().equals(relateType);
        TradeAccTransChangeBo changeBo = new TradeAccTransChangeBo().setTrans(bos)
                .setTransNo(relateNo).setTransTypes(isTransfer ? Constants.TransferTypes : Constants.PayTypes)
                .setPreStatus(AccountStatusEnum.FREEZE.getCode()).setStatus(AccountStatusEnum.AVAIL.getCode()); // Status = AVAIL 仅为返回，不更新数据
        List<TradeAccTransVo> vos = isTransfer ? tradeAccTransService.queryStatusListByNo(changeBo, null) :
                tradeAccTransService.queryStatusListByRelate(changeBo);
        Map<String, TradeAccTransChangeBo> tboMap = new HashMap<>();
        Map<String, TradeAccAvailVo> availMap = new HashMap<>();
        for (TradeAccTransVo vo : vos) {
            if (AccountStatusEnum.INTI.getCode().equals(vo.getStatus())) {
                if (Constants.RefundTypes.contains(vo.getTransType())) {
                    continue; // 退款未完成的，先不结， 等待退款成功后自动结
                } else {
                    throw new ServiceException(String.format("skuId %s 存在未结算数据，不能生成结算单", vo.getSkuId()));
                }
            }
            if (!vo.getStatus().equals(changeBo.getPreStatus()) || vo.getAvailId() != null) {
                continue;
            }
            // isTransfer ? 0 : 1   不在使用该逻辑， 加扣款也加到同一个结算单中
            TradeAccAvailVo availVo = getTradeAccAvail(1, availDate, vo, availMap);
            if (availVo == null) {
                continue;
            }
            TradeAccTransChangeBo tbo = getTradeTransChange(changeBo, vo, tboMap);
            tbo.getTrans().add(new TradeAccTransAvaildsBo()
                    .setTransId(vo.getId()).setAvailId(availVo.getId()).setAvailNo(availVo.getAvailNo())
                    .setAcctOrgId(availVo.getAcctOrgId()).setOrgCode(availVo.getOrgCode()).setOrgType(availVo.getOrgType()));
            vo.setAvailId(availVo.getId());
            vo.setAvailNo(availVo.getAvailNo()); // 为了返回
            vo.setStatus(changeBo.getStatus());
        }
        for (Map.Entry<Long, List<TradeAccTransAvaildsBo>> entry :
                tboMap.values().stream().flatMap(bo -> bo.getTrans().stream()).collect(
                        Collectors.groupingBy(TradeAccTransAvaildsBo::getAvailId, Collectors.toList())).entrySet()) {
            tradeAccTransService.updateAvailInit(entry.getKey(), entry.getValue());
        }
        log.keyword(relateNo, "updateAvailInitByRelate").info("转结算单完成（skuId） size=" + tboMap.size());
        // 1 订单数据的结算，仅返回OP类型  2 划转全部返回
        return isTransfer ? vos : vos.stream().filter(vo -> AccountTransTypeEnum.OP.getCode().equals(vo.getTransType())).toList();
    }

    /*
        划转单不能调用该方法， 因生成 isAuto = 1  结算单 ！！！！！
     */
    public void updateAvailInit(LocalDate availDate, List<Long> transIds) {
        log.keyword("updateAvailInit").info("转结算单（transId） " + JsonUtils.toJsonString(transIds));
        TradeAccTransChangeBo changeBo = new TradeAccTransChangeBo()
                .setPreStatus(AccountStatusEnum.FREEZE.getCode()).setStatus(AccountStatusEnum.AVAIL.getCode());
        List<TradeAccTransVo> vos = tradeAccTransService.queryStatusListByIds(changeBo, transIds);
        Map<String, TradeAccTransChangeBo> tboMap = new HashMap<>();
        Map<String, TradeAccAvailVo> availMap = new HashMap<>();
        for (TradeAccTransVo vo : vos) {
            if (!vo.getStatus().equals(changeBo.getPreStatus()) || vo.getAvailId() != null) {
                continue;
            }
            TradeAccAvailVo availVo = getTradeAccAvail(1, availDate, vo, availMap);
            if (availVo == null) {
                continue;
            }
            TradeAccTransChangeBo tbo = getTradeTransChange(changeBo, vo, tboMap);
            tbo.getTrans().add(new TradeAccTransAvaildsBo()
                    .setTransId(vo.getId()).setAvailId(availVo.getId()).setAvailNo(availVo.getAvailNo()));
        }
        for(Map.Entry<Long, List<TradeAccTransAvaildsBo>> entry:
                tboMap.values().stream().flatMap(bo -> bo.getTrans().stream()).collect(
                        Collectors.groupingBy(TradeAccTransAvaildsBo::getAvailId, Collectors.toList())).entrySet()){
            tradeAccTransService.updateAvailInit(entry.getKey(), entry.getValue());
        }
        log.keyword("updateAvailInit").info("转结算单完成（transId） size=" + tboMap.size());
    }

    /*
         取消该单所有已是 可提现的数据  (测试使用)
      */
    public void updateAvailInitCancel(String transNo, String transType) {
        log.keyword(transNo, "updateAvailInitCancel").info("取消结算单");
        TradeAccTransChangeBo changeBo = new TradeAccTransChangeBo().setTransNo(transNo).setTransTypes(Arrays.asList(transType))
                .setPreStatus(AccountStatusEnum.FREEZE.getCode()).setStatus(AccountStatusEnum.FREEZE.getCode()).setTrans(new ArrayList<>());
        List<TradeAccTransVo> vos = tradeAccTransService.queryStatusListByNo(changeBo, new TradeAccTransStatusBo().setPreStatus(changeBo.getPreStatus()));
        for (TradeAccTransVo vo : vos) {
            if (!vo.getStatus().equals(changeBo.getPreStatus()) || vo.getCashId() != null || vo.getAvailId() == null) {
                continue;
            }
            changeBo.getTrans().add(new TradeAccTransAvaildsBo()
                    .setTransId(vo.getId()).setAvailId(vo.getAvailId())
                    .setAcctOrgId(vo.getAcctOrgId()).setOrgCode(vo.getOrgCode()).setOrgType(vo.getOrgType()));
        }
        for(Map.Entry<Long, List<TradeAccTransAvaildsBo>> entry: changeBo.getTrans().stream().collect(
                        Collectors.groupingBy(TradeAccTransAvaildsBo::getAvailId, Collectors.toList())).entrySet()){
            tradeAccTransService.updateAvailInitCancel(entry.getKey(), entry.getValue());
        }
        log.keyword(transNo, "updateAvailInitCancel").info("取消结算单完成");
    }

    private TradeAccTransChangeBo getTradeTransChange(TradeAccTransChangeBo changeBo,
                                                      TradeAccTransVo vo, Map<String, TradeAccTransChangeBo> tboMap) {
        String changeKey = String.format("%s_%s", vo.getTransNo(), vo.getTransType());
        TradeAccTransChangeBo tbo = tboMap.get(changeKey);
        if (tbo == null) {
            tbo = new TradeAccTransChangeBo().setTransNo(vo.getTransNo()).setTransTypes(changeBo.getTransTypes())
                    .setPreStatus(changeBo.getPreStatus()).setStatus(changeBo.getStatus()).setTrans(new ArrayList<>());
            tboMap.put(changeKey, tbo);
        }
        return tbo;
    }

    private TradeAccAvailVo getTradeAccAvail(Integer isAuto, LocalDate availDate, TradeAccTransVo vo, Map<String, TradeAccAvailVo> availMap) {
//        if (!AccountOrgTypeEnum.SUPPLIER.getCode().equals(vo.getOrgType())
//                && !AccountOrgTypeEnum.CITY_WH.getCode().equals(vo.getOrgType())) {
//            throw new ServiceException(String.format("流水单%s不能生成结算单", vo.getTransNo()));
//        }
        // 不判断划水单， 划水单直接转结算单
        if (!AccountTransTypeEnum.TI.getCode().equals(vo.getTransType()) && !AccountTransTypeEnum.TO.getCode().equals(vo.getTransType())
                && !AccountStatusEnum.FREEZE.getCode().equals(vo.getStatus())) {
            throw new ServiceException(String.format("流水单 %s 不是待结算状态", vo.getTransNo()));
        }
        if (AccountOrgTypeEnum.REGION_WH.getCode().equals(vo.getOrgType())
                || AccountOrgTypeEnum.CITY_WH.getCode().equals(vo.getOrgType())) {
            // 总仓 和 城市仓(未注册的)， 不生成结算单
            return null;
        }
        // 结算日期，只支持昨日和当日  ！！！
        LocalDate acctDate = availDate == null ? LocalDate.now() : availDate;
        TradeAccAvailVo availVo;
        if (isAuto == 1) {
            String availKey = String.format("%s_%s_%s_%s_%s", vo.getAcctOrgId(), vo.getOrgCode(), vo.getOrgType(), vo.getDeptId(), acctDate);
            availVo = availMap.get(availKey);
            if (availVo == null) {
                availVo = tradeAccAvailService.queryAvailOrById(vo.getAcctOrgId(), vo.getOrgCode(),
                        vo.getOrgId(), vo.getOrgType(), vo.getDeptId(), null, acctDate);
                availMap.put(availKey, availVo);
            }
        } else {
            TradeAccAvailBo accAvailBo = new TradeAccAvailBo();
            accAvailBo.setAcctOrgId(vo.getAcctOrgId());
            accAvailBo.setOrgId(vo.getOrgId());
            accAvailBo.setOrgCode(vo.getOrgCode());
            accAvailBo.setOrgType(vo.getOrgType());
            accAvailBo.setDeptId(vo.getDeptId());
            accAvailBo.setAvailDate(acctDate);
            availVo = tradeAccAvailService.insertByBo(accAvailBo);
        }
        return availVo;
    }

    /*
         退款， 如果对应订单数据已经结算，自动完成结算
     */
    public void refundAvailInitByRelate(OrderRefundQueryBo bo, List<TradeAccTransVo> vos) {
        // updateAvailInitByRelate 不能直接调用，退款完成的消息中调用才行   TODO 换供应商要重新验证
        List<String> refundSupplierSplitNos = (vos != null ? vos
                : tradeAccTransService.queryTransList(bo.getRefundNo(), Collections.singletonList(AccountTransTypeEnum.OR.getCode())))
                .stream().filter(vo -> AccountOrgTypeEnum.SUPPLIER.getCode().equals(vo.getOrgType())).map(TradeAccTransVo::getSplitRelNo).toList();
        // 退款单分账单对应的订单，找到已结的， 自动转结算
        List<TradeAccTransVo> orderTransVos = tradeAccTransService.queryOrderAvailTransList(bo.getOrderNo(),
                refundSupplierSplitNos).stream().filter(vo -> vo.getAvailId() != null).toList();
        if (orderTransVos.size() > 0) {
            try {
                // 超期退款， 获取账期， 指定结算日
                //RemoteSupSaleSettleDateVo supSaleSettleDateVo = tradeBaseDataBizService.queryOrderSaleDate(orderTransVos.get(0).getOrgId());
                _updateAvailInitByRelate(bo.getOrderNo(), AccountTransTypeEnum.OR.getCode(),
                        LocalDate.now().plusDays(channelsProperties.getRefundAvailDelayDays()),
                        MapstructUtils.convert(orderTransVos, TradeAccTransAvaildsBo.class));
            } catch (ServiceException se) {
                // 结算不了，异常不 throw
                log.keyword(bo.getRefundNo(), "refundAvailInitByRelate").info("", se);
            }
        }
    }

    public TradeAccTransferVo transferChange(RemoteTransferChangeBo supplierBo) {
        log.keyword(supplierBo.getOrderNo(), "transferChange").info("1 更换供应商 " + JsonUtils.toJsonString(supplierBo));
        if (supplierBo.getTransAmt() == null || supplierBo.getTransAmt().compareTo(BigDecimal.ZERO) != 0) {
            // 换供应商代码未继续维护， 要开通必须重新修改和测试
            throw new ServiceException("不支持换供应商类型");
        }
        TradeAccTransferVo vo = tradeAccTransferService.queryByTransId(supplierBo.getTransId(),
                TransferBusiTypeEnum.TRANSFER_CHANGE.getCode());
        if (vo == null) {
            TradeAccTransferBo addBo = new TradeAccTransferBo();
            addBo.setBusiType(TransferBusiTypeEnum.TRANSFER_CHANGE.getCode());
            addBo.setOrgType(AccountOrgTypeEnum.SUPPLIER.getCode());
            addBo.setOrgId(supplierBo.getOrgId());
            addBo.setDeptId(supplierBo.getDeptId());
            addBo.setSourceOrgType(AccountOrgTypeEnum.SUPPLIER.getCode());
            addBo.setSourceOrgId(supplierBo.getSourceOrgId());
            addBo.setSourceDeptId(supplierBo.getSourceDeptId());
            addBo.setTransAmt(supplierBo.getTransAmt());
            addBo.setRelateNo(supplierBo.getOrderNo());
            addBo.setTransId(supplierBo.getTransId());
            addBo.setTransDate(LocalDate.now());
            addBo.setAcctOrgId(0L); // 先设置成 0
            if (addBo.getOrgId().equals(addBo.getSourceOrgId())) {
                throw new ServiceException("更换供应商相同");
            }
            transferCreateCheck(addBo);
            vo = tradeAccTransferService.insertByBo(addBo, transferCreateTrans(false, addBo,
                    Collections.singletonList(new RemoteTransferSplitBo().setSkuId(supplierBo.getSkuId())
                            .setSourceSkuId(supplierBo.getSourceSkuId()).setTransAmt(supplierBo.getTransAmt()))));
            log.keyword(supplierBo.getOrderNo(), "transferChange").info("2 更换供应商插入");
        }
        updateFreeze(vo.getTransNo(), Constants.TransferTypes, null);
        vo.setStatus(AccountStatusEnum.FREEZE.getCode());
        log.keyword(supplierBo.getOrderNo(), "transferChange").info("3 更换供应商完成 ");
        return vo;
    }

    /*
        加扣款等划转单， 直接生成结算单
           创建过程 返回的 splitNo(入账方), availNo（优先 出账方 （供应商 或 城市仓））
     */
    public TradeAccTransferVo transferCreate(RemoteTransferCreateBo createBo) {
        // 如果已存在， 判断 两个机构 + 单据类型 + 是否生成结算单， 是否调用接口完成， 判断是否可更新
        log.keyword(createBo.getTransNo(), "transferCreate").info("1 划转单生成 " + JsonUtils.toJsonString(createBo));
        TradeAccTransferBo addBo = MapstructUtils.convert(createBo, TradeAccTransferBo.class);
        TradeAccTransferVo vo = tradeAccTransferService.queryByNo(createBo.getTransNo());
        if (vo == null) {
            if (TransferBusiTypeEnum.TRANSFER_CHANGE.getCode().equals(addBo.getBusiType())) {
                throw new ServiceException("不支持换供应商类型");
            }
            transferCreateCheck(addBo);
            boolean isSameOrg = addBo.getOrgCode().equals(addBo.getSourceOrgCode()) && addBo.getOrgType().equals(addBo.getSourceOrgType());
            if (isSameOrg && !AccountOrgTypeEnum.REGION_WH.getCode().equals(addBo.getOrgType())) {
                throw new ServiceException(String.format("划转单 %s 两机构不能相同", addBo.getTransNo()));
            }
            vo = tradeAccTransferService.insertByBo(addBo, transferCreateTrans(isSameOrg, addBo, createBo.getSplits()));
            log.keyword(createBo.getTransNo(), "transferCreate").info("2 划转单插入");
            if (vo.getOutOrgCode().equals(vo.getSourceOutOrgCode())) {
                Integer finishStatus = TransferInfStatusEnum.INF_FAIL.getCode();  // 调用接口时使用 SUCCESS
                TradeAccTransferBo respBo = new TradeAccTransferBo();
                respBo.setId(vo.getId());
                respBo.setInfSource(finishStatus);
                respBo.setInfStatus(finishStatus);
                respBo.setInfReason("");
                respBo.setOutSuccessTime(Convert.toDate(DateUtil.now()));
                tradeAccTransferService.updateTransferInfData(respBo, vo);
                BeanUtil.copyProperties(respBo, vo, Constants.BeanCopyIgnoreNullValue);
                log.keyword(createBo.getTransNo(), "transferCreate").info("3 划转单无需转账");
            }
        }
        updateFreeze(vo.getTransNo(), Constants.TransferTypes, null);
        vo.setStatus(AccountStatusEnum.FREEZE.getCode());
        log.keyword(vo.getTransNo(), "transferCreate").info("4 划转单生成完成");
        return vo;
    }

    private void transferCreateCheck(TradeAccTransferBo addBo) {
        if (addBo.getTransAmt().compareTo(BigDecimal.ZERO) < 0) {
            // 负值转正， 机构也调换过来
            addBo.setTransAmt(addBo.getTransAmt().negate());
            Long orgId = addBo.getSourceOrgId();
            Integer orgType = addBo.getSourceOrgType();
            Long deptId = addBo.getSourceDeptId();
            addBo.setSourceOrgId(addBo.getOrgId());
            addBo.setSourceOrgType(addBo.getOrgType());
            addBo.setSourceDeptId(addBo.getDeptId());
            addBo.setOrgId(orgId);
            addBo.setOrgType(orgType);
            addBo.setDeptId(deptId);
        }
        RemoteBaseDataVo orgVo = tradeBaseDataBizService.queryById(addBo.getOrgId(), addBo.getOrgType());
        if (orgVo == null) {
            throw new ServiceException(String.format("%s %s未定义", addBo.getOrgId(), AccountOrgTypeEnum.getDescByCode(addBo.getOrgType())));
        }
        TradeOrgBankBindVo bindVo = tradeOrgBankBizService.queryBindByCode(orgVo.getCode(), orgVo.getType());
        if (StringUtils.isEmpty(bindVo.getOutOrgCode()) || StringUtils.isEmpty(bindVo.getOutAcctCode())) {
            throw new ServiceException(String.format("[%s] 未开户，不能划转", orgVo.getCode()));
        }
        RemoteBaseDataVo sourceOrgVo = tradeBaseDataBizService.queryById(addBo.getSourceOrgId(), addBo.getSourceOrgType());
        if (sourceOrgVo == null) {
            throw new ServiceException(String.format("%s %s未定义", addBo.getSourceOrgId(), AccountOrgTypeEnum.getDescByCode(addBo.getSourceOrgType())));
        }
        TradeOrgBankBindVo sourceBindVo = tradeOrgBankBizService.queryBindByCode(sourceOrgVo.getCode(), sourceOrgVo.getType());
        if (StringUtils.isEmpty(sourceBindVo.getOutOrgCode()) || StringUtils.isEmpty(sourceBindVo.getOutAcctCode())) {
            throw new ServiceException(String.format("[%s] 未开户，不能划转", sourceOrgVo.getCode()));
        }
        addBo.setOrgCode(orgVo.getCode());
        addBo.setOutOrgCode(bindVo.getOutOrgCode());
        addBo.setOutAcctCode(bindVo.getOutAcctCode());
        addBo.setSourceOrgCode(sourceOrgVo.getCode());
        addBo.setSourceOutOrgCode(sourceBindVo.getOutOrgCode());
        addBo.setSourceOutAcctCode(sourceBindVo.getOutAcctCode());
        TransferBusiTypeEnum busiTypeEnum = TransferBusiTypeEnum.getEnumByCode(addBo.getBusiType());
        addBo.setBusiField(busiTypeEnum.getInField());
        addBo.setSourceBusiField(busiTypeEnum.getOutField());
    }

    private List<TradeAccTransBo> transferCreateTrans(boolean isSameOrg, TradeAccTransferBo addBo, List<RemoteTransferSplitBo> splits) {
        List<TradeAccTransBo> transBos = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(splits)) {
            BigDecimal totalAmt = BigDecimal.ZERO;
            for (RemoteTransferSplitBo bo : splits) {
                Long skuId = bo.getSkuId();
                Long sourceSkuId = bo.getSourceSkuId();
                BigDecimal transAmt = bo.getTransAmt();
                if (transAmt.compareTo(BigDecimal.ZERO) < 0) {
                    transAmt = transAmt.negate();
                    Long tmpId = skuId;
                    skuId = sourceSkuId;
                    sourceSkuId = tmpId;
                }
                totalAmt = totalAmt.add(transAmt);
                if (AccountOrgTypeEnum.SUPPLIER.getCode().equals(addBo.getSourceOrgType()) && sourceSkuId == null) {
                    throw new ServiceException(String.format("划转单 %s 转出明细批次id不存在", addBo.getTransNo()));
                }
                if (AccountOrgTypeEnum.SUPPLIER.getCode().equals(addBo.getOrgType()) && skuId == null) {
                    throw new ServiceException(String.format("划转单 %s 转入明细批次id不存在", addBo.getTransNo()));
                }
                transBos.add(new TradeAccTransBo().setOrgCode(addBo.getSourceOrgCode()).setOrgId(addBo.getSourceOrgId())
                        .setOrgType(addBo.getSourceOrgType()).setDeptId(addBo.getSourceDeptId()).setSkuId(sourceSkuId).setTransAmt(transAmt.negate())
                        .setTransType(AccountTransTypeEnum.TO.getCode()));
                if (!isSameOrg) { // 相同机构仅产生负单
                    transBos.add(new TradeAccTransBo().setOrgCode(addBo.getOrgCode()).setOrgId(addBo.getOrgId())
                            .setOrgType(addBo.getOrgType()).setDeptId(addBo.getDeptId()).setSkuId(skuId).setTransAmt(transAmt)
                            .setTransType(AccountTransTypeEnum.TI.getCode()));
                }
            }
            if (totalAmt.compareTo(addBo.getTransAmt()) != 0) {
                throw new ServiceException("分账金额与总金额不一致");
            }
        } else {
            transBos.add(new TradeAccTransBo().setOrgCode(addBo.getSourceOrgCode()).setOrgId(addBo.getSourceOrgId())
                    .setOrgType(addBo.getSourceOrgType()).setDeptId(addBo.getSourceDeptId()).setTransAmt(addBo.getTransAmt().negate())
                    .setTransType(AccountTransTypeEnum.TO.getCode()));
            if (!isSameOrg) { // 相同机构仅产生负单
                transBos.add(new TradeAccTransBo().setOrgCode(addBo.getOrgCode()).setOrgId(addBo.getOrgId())
                        .setOrgType(addBo.getOrgType()).setDeptId(addBo.getDeptId()).setTransAmt(addBo.getTransAmt())
                        .setTransType(AccountTransTypeEnum.TI.getCode()));
            }
        }
        return transBos;
    }

//
//    public TradeAccTransferVo transferCheck(String transNo) {
//        TradeAccTransferVo vo = tradeAccTransferService.queryByNo(transNo);
//        if (vo == null) {
//            throw new ServiceException(String.format("划转单 %s 不存在", transNo));
//        }
//        Integer finishStatus = TransferInfStatusEnum.INF_FAIL.getCode();  // 调用接口时使用 SUCCESS
//        if (finishStatus.equals(vo.getInfStatus())) {
//            return vo;
//        }
//        /*
//        TradeOrgBankBindVo sourceBindVo = tradeOrgBankBizService.queryBindByCode(vo.getSourceOrgCode(), vo.getSourceOrgType());
//        if (StringUtils.isEmpty(sourceBindVo.getCommAcctCode())) {
//            throw new ServiceException(String.format("[%s] 未绑定同名账户", sourceBindVo.getOrgCode()), R.FAIL);
//        }*/
//        // 1 验证同名余额是否够用
//        /*
//        TradeOrgBankAuthBo authBo = pinganCloudOrgBankRelaService.querySameOrgAuth(MapstructUtils.convert(sourceBindVo, TradeOrgBankRelaAuthVo.class));
//        if (authBo.getAuthValidDate() == null || authBo.getAuthValidDate().isBefore(LocalDate.now())) {
//            throw new ServiceException(String.format("[%s] 转账授权已过期", sourceBindVo.getOrgCode()));
//        }
//        if (vo.getTransAmt().compareTo(authBo.getAuthSingleAmt()) > 0) {
//            throw new ServiceException(String.format("[%s] 转账授权单次金额超限", sourceBindVo.getOrgCode()));
//        }*/
//        if (!finishStatus.equals(vo.getInfSource())) {
//            TradeAccTransferBo tbo = MapstructUtils.convert(vo, TradeAccTransferBo.class);
//            vo = tradeAccTransferService.updateCheckByBo(true, tbo);
//            // 2 先同名转账， 同商户子账户 -> 同 00 子账户
//            try {
//                /*
//                if (sourceBindVo.getAuthValidDate() == null) {
//                    throw new ServiceException(String.format("[%s] 未设置转账授权", sourceBindVo.getOrgCode()), R.FAIL);
//                }
//                if (sourceBindVo.getAuthValidDate().isBefore(LocalDate.now())) {
//                    throw new ServiceException(String.format("[%s] 转账授权已过期", sourceBindVo.getOrgCode()), R.FAIL);
//                }
//                TradeAccTransferBo respBo = accountService.transferSource(vo, sourceBindVo);
//                */
//                TradeAccTransferBo respBo = new TradeAccTransferBo();
//                respBo.setId(vo.getId());
//                respBo.setOutSourceNo("**********");
//                respBo.setInfSource(finishStatus);
//                respBo.setInfReason("");
//                respBo.setOutSuccessTime(Convert.toDate(DateUtil.now()));
//                tradeAccTransferService.updateTransferInfData(respBo, vo);
//                BeanUtil.copyProperties(respBo, vo, Constants.BeanCopyIgnoreNullValue);
//                log.keyword(vo.getTransNo(), "transferCheck").info("划转单同名转账完成");
//                // throw new ServiceException("11");
//            } catch (ServiceException se) {
//                tradeAccTransferService.updateTransferInfFail(true, TransferInfStatusEnum.CHECK.getCode(), vo, se);
//                throw se;
//            }
//        }
//        if (!finishStatus.equals(vo.getInfStatus())) {
//            // TradeOrgBankBindVo bindVo = tradeOrgBankBizService.queryBindByCode(vo.getOrgCode(), vo.getOrgType());
//            TradeAccTransferBo tbo = MapstructUtils.convert(vo, TradeAccTransferBo.class);
//            vo = tradeAccTransferService.updateCheckByBo(false, tbo);
//            // 3 转账，  同 00 子账户 -> 其他商户子账户
//            try {
//                // TradeAccTransferBo respBo = accountService.transferPay(vo, sourceBindVo, bindVo);
//                TradeAccTransferBo respBo = new TradeAccTransferBo();
//                respBo.setId(vo.getId());
//                respBo.setOutTradeNo("**********");
//                respBo.setInfStatus(finishStatus);
//                respBo.setInfReason("");
//                respBo.setOutSuccessTime(Convert.toDate(DateUtil.now()));
//                tradeAccTransferService.updateTransferInfData(respBo, vo);
//                BeanUtil.copyProperties(respBo, vo, Constants.BeanCopyIgnoreNullValue);
//                log.keyword(vo.getTransNo(), "transferCheck").info("划转单银行转账完成");
//            } catch (ServiceException se) {
//                tradeAccTransferService.updateTransferInfFail(false, TransferInfStatusEnum.CHECK.getCode(), vo, se);
//                throw se;
//            }
//        }
//
//        if (TransferBusiTypeEnum.TRANSFER_CHANGE.getCode().equals(vo.getBusiType())) {
//            // 更换供应商的， 已是可提现状态， 也不发消息， 仅转账完成就可以了
//            // 转出商品转出取消状态
//            log.keyword(vo.getTransNo(), "transferCheck").info("划转单生成完成");
//        } else if (finishStatus.equals(vo.getInfStatus())) {
//            /*
//                划转单   1 两个供应商，    生成 2 个结算单， 两个status = 2
//                        2 一个供应商，    生成 1 个结算单， 一个 status = 1，一个 status = 2
//                        3 无供应商        不生成结算单，   两个 status = 1
//             */
//            List<TradeAccTransVo> transVos = updateAvailInitByRelate(vo.getTransNo(), AccountTransTypeEnum.TI.getCode(), null);
//            List<TradeAccTransVo> availVos = transVos.stream().filter(v -> StringUtils.isNotBlank(v.getAvailNo())).toList();
//            if (availVos.size() > 0) {
//                // 1 updateStatus 中 TradeAccTransMapper.updateStatus 更改字段 status， availNo
//                //                TradeAccTransferMapper.updateStatusByTrans 更改表字段 status， availNo
//                // 2 结算单号反填规则， 优先供应商， 优先负值的 （通过 updateAvailByRelate 中返回的顺序确定）
//                vo.setStatus(availVos.get(0).getStatus());
//                vo.setAvailNo(availVos.get(0).getAvailNo());
//                log.keyword(vo.getTransNo(), "transferCheck").info(String.format("划转单生成 %s 条结算单", availVos.size()));
//            }
//            log.keyword(vo.getTransNo(), "transferCheck").info("划转单生成完成");
//            tradeAccTransferChangeProducer.send(vo);
//        }
//        return vo;
//    }

    public TradeAccTransferVo transferDisCheck(String transNo) {
        TradeAccTransferVo vo = tradeAccTransferService.queryByNo(transNo);
        if (vo == null) {
            throw new ServiceException(String.format("划转单 %s 不存在", transNo));
        }
        Integer finishStatus = TransferInfStatusEnum.SUCCESS.getCode();  // 调用接口时使用 SUCCESS
        if (finishStatus.equals(vo.getInfSource())) {
            return vo;
        }
        TradeAccTransferBo tbo = MapstructUtils.convert(vo, TradeAccTransferBo.class);
        vo = tradeAccTransferService.updateDisCheckByBo(tbo);
        try {
            TradeAccTransferBo respBo =  AccountOrgTypeEnum.SUPPLIER.getCode().equals(vo.getOrgType()) ?
                    accountService.transferIn(vo) : accountService.transferOut(vo);
            tradeAccTransferService.updateTransferDisInfData(respBo, vo);
            BeanUtil.copyProperties(respBo, vo, Constants.BeanCopyIgnoreNullValue);
            log.keyword(vo.getTransNo(), "transferCheck").info("划转单转账完成");
        } catch (ServiceException se) {
            tradeAccTransferService.updateTransferDisInfFail(TransferInfStatusEnum.CHECK.getCode(), vo, se);
            throw se;
        }
        if (finishStatus.equals(vo.getInfStatus())) {
            /*
                划转单   1 两个供应商，    生成 2 个结算单， 两个status = 2
                        2 一个供应商，    生成 1 个结算单， 一个 status = 1，一个 status = 2
                        3 无供应商        不生成结算单，   两个 status = 1
             */
            List<TradeAccTransVo> transVos = _updateAvailInitByRelate(vo.getTransNo(), AccountTransTypeEnum.TI.getCode(), null, null);
            List<TradeAccTransVo> availVos = transVos.stream().filter(v -> StringUtils.isNotBlank(v.getAvailNo())).toList();
            if (availVos.size() > 0) {
                // 1 updateStatus 中 TradeAccTransMapper.updateStatus 更改字段 status， availNo
                //                TradeAccTransferMapper.updateStatusByTrans 更改表字段 status， availNo
                // 2 结算单号反填规则， 优先供应商， 优先负值的 （通过 updateAvailByRelate 中返回的顺序确定）
                vo.setStatus(availVos.get(0).getStatus());
                vo.setAvailNo(availVos.get(0).getAvailNo());
                log.keyword(vo.getTransNo(), "transferCheck").info(String.format("划转单生成 %s 条结算单", availVos.size()));
            }
            log.keyword(vo.getTransNo(), "transferCheck").info("划转单生成完成");
            tradeAccTransferChangeProducer.send(vo);
        }
        return vo;
    }

    public TradeAccTransferVo transferClose(String transNo) {
        TradeAccTransferVo vo = tradeAccTransferService.queryByNo(transNo);
        if (vo == null) {
            throw new ServiceException(String.format("划转单 %s 不存在", transNo));
        }
        if (TransferInfStatusEnum.SUCCESS.getCode().equals(vo.getInfStatus())
                || !AccountStatusEnum.FREEZE.getCode().equals(vo.getStatus())
                || TransferBusiTypeEnum.TRANSFER_CHANGE.getCode().equals(vo.getBusiType())) {
            return vo;
        }
        updateCancelFreeze(transNo, Constants.TransferTypes);
        TradeAccTransferBo transferBo = new TradeAccTransferBo().setId(vo.getId())
                .setInfSource(TransferInfStatusEnum.FAIL.getCode())
                .setInfStatus(TransferInfStatusEnum.FAIL.getCode());
        tradeAccTransferService.updateTransferInfData(transferBo, vo);
        BeanUtil.copyProperties(transferBo, vo, Constants.BeanCopyIgnoreNullValue);
        return vo;
    }

}
