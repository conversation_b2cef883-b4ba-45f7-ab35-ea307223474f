package cn.xianlink.trade.channel.pingancloud;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class PinganCloudConfig implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private String appId;
    private String publicKey;
    private String fundSummaryAcctNo;
    private String txnClientNo;
    private String mrchCode;
    private String traderNo;
    private String payModeNo;
    private String callbackUrl;
    // 分账限制
    private Integer payRemarkMaxLen;
    private Integer payRemarkMaxSplit;
    private Integer refundRemarkMaxSplit;
    private String paySplitLimitAmts;
    // 默认账户
    private String regionWhOrgCode;
    private String cityWhOrgCode;
    // private String supplierOrgCode;
    // 同名转账授权参数
    private Integer authTotalAmt;
    private Integer authSingleAmt;
    private Integer authValidDays;
    // 平安内部账户（营销子账号）
    private String marketingAcctCode;
    // 平安内部账户（手续费子账号）
    private String serviceFeeAcctCode;
    // 平安佣金子账户（手工创建）
    private String commissionOrgCode;
    //
    private List<BigDecimal> _paySplitLimitAmts;

}
