package cn.xianlink.trade.domain;

import cn.xianlink.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * 分账变更流水对象 trade_acc_trans
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("trade_comm_trans")
public class TradeCommTrans extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id主键    //差额退
     */
    @TableId(value = "id")
    private Long id;
    /**
     * trade_acc_trans.id
     */
    private Long accTransId;
    /**
     * 业务类型 OP 订单;  OR 退单; CH 提现; CE 过期;
     */
    private String transType;
    /**
     * 业务id
     */
    private Long transId;
    /**
     * 业务单号
     */
    private String transNo;
    /**
     * 记账日期
     */
    private LocalDate transDate;
    /**
     * 部门id
     */
    private Long deptId;
    /**
     * 账务机构id
     */
    private Long acctOrgId;
    /**
     * 业务机构id
     */
    private Long transOrgId;
    /**
     * 客户id
     */
    private Long customerId;
    /**
     * 批次商品id
     */
    private Long skuId;
    /**
     * 平台商品名称
     */
    private String skuName;
	/**
     * 业务类型
     */
    private String busiType;
    /**
     * 关联单号
     */
    private String relateNo;
    /**
     * 关联单id
     */
    private Long relateId;
    /**
     * 佣金金额
     */
    private BigDecimal commissionAmt;
    /**
     * 发薪金额
     */
    private BigDecimal salaryAmt;
    /**
     * 已提现金额
     */
    private BigDecimal cashAmt;
    /**
     * 已过期金额
     */
    private BigDecimal expireAmt;
    /**
     * 状态 1 待结算 2 已结算 3 已提现 4 提现待审核 5 已过期 6 作废（订单作废）
     */
    private Integer status;
    /**
     * 结算日期
     */
    private LocalDate availDate;
    /**
     * 删除标志（0代表存在 存入这条数据的主键代表删除）
     */
    @TableLogic(delval = "id")
    private Long delFlag;


}
