package cn.xianlink.trade.channel.pingancloud;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.xianlink.common.api.enums.trade.PayInfStatusEnum;
import cn.xianlink.common.api.enums.trade.RefundInfStatusEnum;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.json.utils.JsonUtils;
import cn.xianlink.trade.api.domain.bo.OrderPayBo;
import cn.xianlink.trade.api.domain.bo.OrderRefundBo;
import cn.xianlink.trade.channel.PaymentService;
import cn.xianlink.trade.domain.bo.TradePayBo;
import cn.xianlink.trade.domain.bo.TradePayRefundBo;
import cn.xianlink.trade.domain.vo.TradePayRefundVo;
import cn.xianlink.trade.domain.vo.TradePayVo;
import com.alibaba.fastjson.JSONObject;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.CustomLog;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * 平安云收款对接 -- 微信小程序 --
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@CustomLog
@Component
@ConditionalOnProperty(prefix = "channels", name = "debug-local", havingValue = "true", matchIfMissing = false)
public class PinganCloudWXTestPayServiceImpl implements PaymentService {

    @Resource
    private transient PinganCloudClientService client;

    @Override
    public int payValidate(OrderPayBo bo) {
        if(StringUtils.isEmpty(bo.getPayerId())){
            throw new ServiceException("用户id不能为空");
        }
        if(StringUtils.isEmpty(bo.getAppId())){
            throw new ServiceException("wxAppId不能为空");
        }
        if(CollectionUtil.isEmpty(bo.getSplits())){
            throw new ServiceException("分账数据不能为空");
        }
        return client.config.getPayRemarkMaxSplit();
    }

    @Override
    public TradePayBo pay(TradePayVo vo) {
        log.keyword(vo.getOrderNo(), "pay").info(JsonUtils.toJsonString(vo));
        if(StringUtils.isEmpty(vo.getTradeNo()) || StringUtils.isEmpty(vo.getOrderNo())){
            throw new ServiceException("接口单号不能为空");
        }
        if(vo.getPaySplitAmt() == null || vo.getPaySplitAmt().compareTo(BigDecimal.ZERO) < 0){
            throw new ServiceException("接口金额错误");
        }
        if (client.config.getPayRemarkMaxSplit() > 0 && vo.getSplits().size() > client.config.getPayRemarkMaxSplit()) {
            throw new ServiceException("下单商品数太多，请拆分下单");
        }
        if (client.config.get_paySplitLimitAmts().contains(vo.getPaySplitAmt())) {
            // 是平安测试环境调账 KFEJZB6147 接口的触发金额 ！！
            throw new ServiceException(String.format("%s元是平安异常触发金额，请更换测试金额", vo.getPaySplitAmt().toString()));
        }
        // 新创建对象， 更新数据库
        TradePayBo respBo = new TradePayBo();
        respBo.setId(vo.getId());
        respBo.setInfStatus(PayInfStatusEnum.UNPAID.getCode());
        respBo.setPayInfStatus(respBo.getInfStatus());
        respBo.setInfReason("");
        respBo.setOutTradeNo("BankOrderNo");
        respBo.setOutChannelNo("ChannelOrderNo");
        respBo.setCallbackWeixin(new JSONObject()
                .fluentPut("channel", vo.getChannel())
                .fluentPut("params", new JSONObject()
                        .fluentPut("appId", "AppId")
                        .fluentPut("timeStamp", "TimeStamp")
                        .fluentPut("nonceStr", "RandomSting")
                        .fluentPut("package", "ExtendString")
                        .fluentPut("signType", "SignatureMode")
                        .fluentPut("paySign", "Signature")).toJSONString());
        return respBo;
    }

    @Override
    public TradePayBo payQuery(TradePayVo vo) {
        if(StringUtils.isEmpty(vo.getTradeNo())){
            throw new ServiceException("接口单号不能为空");
        }
        // 新创建对象， 更新数据库
        TradePayBo respBo = new TradePayBo();
        respBo.setId(vo.getId());
        respBo.setInfStatus(PayInfStatusEnum.SUCCESS.getCode());
        respBo.setPayInfStatus(respBo.getInfStatus());
        respBo.setInfReason("");
        respBo.setOutTradeNo("BankOrderNo");
        respBo.setOutChannelNo("ChannelOrderNo");
        respBo.setOutFeeAmt(BigDecimal.ZERO);
        respBo.setOutDiscountAmt(BigDecimal.ZERO);
        respBo.setOutSuccessTime(Convert.toDate(DateUtil.now()));
        return respBo;
    }

    @Override
    public TradePayBo payClose(TradePayVo vo) {
        if(StringUtils.isEmpty(vo.getTradeNo())){
            throw new ServiceException("接口单号不能为空");
        }
        // 新创建对象， 更新数据库
        TradePayBo respBo = new TradePayBo();
        respBo.setId(vo.getId());
        respBo.setInfStatus(PayInfStatusEnum.CANCEL.getCode());
        respBo.setPayInfStatus(respBo.getInfStatus());
        respBo.setCloseTime(Convert.toDate(DateUtil.now()));
        respBo.setInfReason("");
        return respBo;
    }

    @Override
    public JSONObject callback(String reqBody, HttpServletRequest request) {
        log.keyword("payCallback").info("支付回调 {}", reqBody);
        // JSONObject body = JSONObject.parseObject(data);
        return null;
    }

    @Override
    public TradePayBo payCallback(JSONObject resp) {
        return null;
    }

    @Override
    public int refundValidate(OrderRefundBo bo, TradePayVo payVo) {
        if(CollectionUtil.isEmpty(bo.getSplits())){
            throw new ServiceException("接口分账数据不能为空");
        }
        return client.config.getRefundRemarkMaxSplit();
    }

    @Override
    public TradePayRefundBo refund(TradePayRefundVo vo) {
        log.keyword(vo.getOrderNo(), vo.getRefundNo()).info(JsonUtils.toJsonString(vo));
        if(StringUtils.isEmpty(vo.getTradeNo()) || StringUtils.isEmpty(vo.getTradeRefundNo()) || StringUtils.isEmpty(vo.getRefundNo())){
            throw new ServiceException("接口单号不能为空");
        }
        if(vo.getRefundSplitAmt() == null || vo.getRefundSplitAmt().compareTo(BigDecimal.ZERO) < 0){
            throw new ServiceException("接口金额错误");
        }
        if (client.config.getRefundRemarkMaxSplit() > 0 && vo.getSplits().size() > client.config.getRefundRemarkMaxSplit()) {
            throw new ServiceException("退单商品数太多，请拆分下单");
        }
        // 成功 或 抛异常
        TradePayRefundBo respBo = new TradePayRefundBo();
        respBo.setId(vo.getId());
        respBo.setInfStatus(RefundInfStatusEnum.SUCCESS.getCode());
        respBo.setRefundInfStatus(respBo.getInfStatus());
        respBo.setInfReason("");
        respBo.setOutTradeNo("BankOrderNo");
        respBo.setOutChannelNo("ChannelOrderNo");
        respBo.setOutCouponAmt(BigDecimal.ZERO);
        respBo.setOutDiscountAmt(BigDecimal.ZERO);
        if (RefundInfStatusEnum.SUCCESS.getCode().equals(respBo.getInfStatus())) {
            respBo.setOutSuccessTime(Convert.toDate(DateUtil.now()));
        }
        return respBo;
        // throw new ServiceException("test refund exception");
    }

    @Override
    public TradePayRefundBo refundQuery(TradePayRefundVo vo) {
        if(StringUtils.isEmpty(vo.getTradeNo()) || StringUtils.isEmpty(vo.getTradeRefundNo())){
            throw new ServiceException("接口单号不能为空");
        }
        // 新创建对象， 更新数据库
        TradePayRefundBo respBo = new TradePayRefundBo();
        respBo.setId(vo.getId());
        respBo.setInfStatus(RefundInfStatusEnum.SUCCESS.getCode());
        respBo.setRefundInfStatus(respBo.getInfStatus());
        respBo.setInfReason("");
        respBo.setOutTradeNo("BankOrderNo");
        respBo.setOutChannelNo("ChannelOrderNo");
        respBo.setOutCouponAmt(BigDecimal.ZERO);
        respBo.setOutDiscountAmt(BigDecimal.ZERO);
        respBo.setOutCashAmt(BigDecimal.ZERO);
        respBo.setOutSuccessTime(Convert.toDate(DateUtil.now()));
        return respBo;
    }

}
