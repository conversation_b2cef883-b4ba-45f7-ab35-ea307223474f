package cn.xianlink.trade.mapper;

import cn.xianlink.common.mybatis.core.mapper.BaseMapperPlus;
import cn.xianlink.trade.domain.TradeAccAvail;
import cn.xianlink.trade.domain.vo.TradeAccAvailVo;
import org.apache.ibatis.annotations.Param;

import java.util.Set;

/**
 * 结算单Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
public interface TradeAccAvailMapper extends BaseMapperPlus<TradeAccAvail, TradeAccAvailVo> {

    TradeAccAvailVo queryCashAmt(@Param("cashId") Long cashId);

    int updateAvailAmt(@Param("fields") Set<String> fields, @Param("availId") Long availId);

}
