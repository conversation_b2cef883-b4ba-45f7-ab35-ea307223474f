package cn.xianlink.trade.channel.lakala.constant;

public enum LakalaPayApiEnum {

    PAY("/api/v3/labs/trans/preorder", "支付"),
    PAY_QUERY("/api/v3/labs/query/tradequery", "支付查询"),
    PAY_CLOSE("/api/v3/labs/relation/close", "支付关闭"),
    PAY_REFUND("/api/v3/labs/relation/refund", "支付退款"),
    PAY_REFUND_QUERY("/api/v3/labs/query/tradequery", "退款查询"),
    // 下载的接口要单独申请  https://o.lakala.com/#/home/<USER>/detail?id=375
    PAY_DOWNLOAD("/api/v3/bmmp4/checkFile/apply", "下载账单"),

    ;

    private final String url;
    private final String remark;

    LakalaPayApiEnum(String url, String remark) {
        this.url = url;
        this.remark = remark;
    }

    public String getUrl() {
        return url;
    }
    public String getRemark() {
        return remark;
    }
}
