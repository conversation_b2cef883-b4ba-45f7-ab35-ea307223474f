package cn.xianlink.trade.domain.convert;

import cn.xianlink.trade.domain.vo.org.TradeOrgBankRelaVo;
import cn.xianlink.trade.domain.vo.org.TradeOrgBankVo;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

/**
 * 数据转换器
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TradeOrgBankConvert extends BaseMapper<TradeOrgBankVo, TradeOrgBankRelaVo> {
}
