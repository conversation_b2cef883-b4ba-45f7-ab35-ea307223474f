package cn.xianlink.trade.channel.lakala;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.xianlink.common.core.utils.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.Date;

public class LakalaUtil {
    public static BigDecimal fenToYuan(String fen){
        BigDecimal bigFen = new BigDecimal(StringUtils.isEmpty(fen) ? "0" : fen);
        BigDecimal percent = new BigDecimal("100");
        return bigFen.divide(percent, 2, RoundingMode.HALF_UP);
    }
    public static String yuanToFen(BigDecimal yuan){
        BigDecimal percent = new BigDecimal("100");
        return String.valueOf(yuan.multiply(percent).intValueExact());
    }

    public static Date timeToDate(String date) {
        try {
            return DateUtil.parse(date, "yyyyMMddHHmmss").toJdkDate();
        } catch (Exception e) {
            return null;
        }
    }

    public static String dateToDate(LocalDate date) {
        return LocalDateTimeUtil.format(date, "yyyyMMdd");
    }

    public static String toString(String str) {
        return str == null ? "" : str;
    }

}
