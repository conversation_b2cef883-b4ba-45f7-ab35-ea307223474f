package cn.xianlink.trade.channel.weixinb2b;


import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl;
import cn.binarywang.wx.miniapp.config.impl.WxMaRedissonConfigImpl;
import cn.binarywang.wx.miniapp.util.WxMaConfigHolder;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.xianlink.common.api.enums.trade.AccountFileTypeEnum;
import cn.xianlink.common.api.enums.trade.PayInfStatusEnum;
import cn.xianlink.common.api.enums.trade.RefundBusiTypeEnum;
import cn.xianlink.common.api.enums.trade.RefundInfStatusEnum;
import cn.xianlink.common.core.constant.HttpStatus;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.SpringUtils;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.trade.api.domain.bo.OrderPayBo;
import cn.xianlink.trade.api.domain.bo.OrderRefundBo;
import cn.xianlink.trade.api.domain.bo.RetailInfoBo;
import cn.xianlink.trade.api.domain.vo.RetailInfoVo;
import cn.xianlink.trade.channel.PaymentService;
import cn.xianlink.trade.channel.pingancloud.PinganCloudClientService;
import cn.xianlink.trade.channel.pingancloud.constant.PinganCloudClientApiEnum;
import cn.xianlink.trade.channel.weixinb2b.constant.WeixinB2bPayApiEnum;
import cn.xianlink.trade.channel.weixinb2b.constant.WeixinB2bPayInfStatusEnum;
import cn.xianlink.trade.channel.weixinb2b.constant.WeixinB2bRefundInfStatusEnum;
import cn.xianlink.trade.constant.Constants;
import cn.xianlink.trade.domain.bo.TradePayBo;
import cn.xianlink.trade.domain.bo.TradePayRefundBo;
import cn.xianlink.trade.domain.vo.TradeAccSplitVo;
import cn.xianlink.trade.domain.vo.TradePayRefundVo;
import cn.xianlink.trade.domain.vo.TradePayVo;
import cn.xianlink.trade.domain.vo.excel.ExcelDataInfo;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.CustomLog;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.common.util.SignUtils;
import me.chanjar.weixin.common.util.crypto.WxCryptUtil;
import me.chanjar.weixin.common.util.http.apache.DefaultApacheHttpClientBuilder;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;

/*
    支付服务费：  订单金额x技术服务费费率   （四舍五入）
    退款服务费：  退款金额x技术服务费费率   （向下取整， 如 0.238 ，取整后 0.23）
                 全部退款后（可多次）， 最后退款总费用是全部退回的
 */
@CustomLog
@Component
public class WeixinB2bPayServiceImpl implements PaymentService {

    final transient Map<String, WeixinB2bConfig> wxConfigs = new HashMap<>();
    final transient Map<String, String> wxAppIds = new HashMap<>();
    private final transient Map<String, WxCryptUtil> wxCryptUtils = new HashMap<>();
    private transient WxMaService wxMaService;

    @Resource
    private transient PinganCloudClientService client;

    @Override
    public void init(String wxAppId, Properties properties) {
        WeixinB2bConfig wxConfig = new WeixinB2bConfig();
        BeanUtil.copyProperties(properties, wxConfig, Constants.BeanCopyIgnoreNullValue);
        wxConfigs.put(wxAppId, wxConfig);
        wxCryptUtils.put(wxAppId, new WxCryptUtil(wxConfig.getToken(), wxConfig.getAesKey(), wxAppId));
        RedissonClient redissonClient = SpringUtils.getBean(RedissonClient.class);
        DefaultApacheHttpClientBuilder httpClientBuilder = DefaultApacheHttpClientBuilder.get();
        httpClientBuilder.setMaxTotalConn(200); // 连接池最大连接数
        /* 解决 错误 api.weixin.qq.com:443 failed to respond
           原因是微信端 http keepalive 时长为 20s，若复用超时而微信端已抛弃连接就会报错 */
        httpClientBuilder.setIdleConnTimeout(15000); // 空闲链接的超时时间
        httpClientBuilder.setCheckWaitTime(15000); // 空闲链接的检测周期
        WxMaRedissonConfigImpl configImpl = new WxMaRedissonConfigImpl(redissonClient);
        configImpl.setAppid(wxAppId);
        configImpl.setSecret(wxConfig.getSecret());
        configImpl.setToken(wxConfig.getToken());
        configImpl.setAesKey(wxConfig.getAesKey());
        configImpl.setMsgDataFormat(wxConfig.getMsgDataFormat());
        configImpl.setApacheHttpClientBuilder(httpClientBuilder);
        if (this.wxMaService == null) {
            this.wxMaService = new WxMaServiceImpl();
            this.wxMaService.setWxMaConfig(configImpl);
        } else {
            this.wxMaService.addConfig(wxAppId, configImpl);
        }
        // wxAppId, path, merchantId 是同渠道下， 一对一 对一 的
        this.wxAppIds.put(wxConfig.getCallbackPath(), wxAppId);
        this.wxAppIds.put(wxConfig.getMerchantId(), wxAppId);
    }

    @Override
    public List<String> getWxAppIds() {
        return wxConfigs.keySet().stream().toList();
    }

    @Override
    public void switchover(String wxAppId) {
        if (!wxMaService.switchover(wxAppId)) {
            throw new ServiceException("无法找到对应小程序配置信息");
        }
    }

    @Override
    public String switchByPath(String path) {
        String wxAppId = this.wxAppIds.get(path);
        if (StringUtils.isNotBlank(wxAppId)) {
            switchover(wxAppId);
            return wxAppId;
        } else {
            throw new ServiceException("无法找到对应小程序配置信息");
        }
    }

    @Override
    public void switchByMerchant(String merchantId) {
        String wxAppId = this.wxAppIds.get(merchantId);
        if (StringUtils.isNotBlank(wxAppId)) {
            switchover(wxAppId);
        } else {
            throw new ServiceException("无法找到对应小程序配置信息");
        }
    }
    /*
     *  是否渠道每次都执行签名操作
     *  true  创建新单号， 重新签名    (因微信b2b没有关闭操作，每次单号未支付，只能重新生成个新的支付单号)
     *  false  先关闭，后重新创建
     */
    @Override
    public boolean isAlwaysSign() {
        return true;
    };
    /*
     *  不同客户，商户号不同（用于线下）
     */
    @Override
    public String getMerchantId() {
        return wxConfigs.get(WxMaConfigHolder.get()).getMerchantId();
    }

    public int payValidate(OrderPayBo bo) {
        if(StringUtils.isEmpty(bo.getPayerId())){
            throw new ServiceException("openId不能为空");
        }
        if(StringUtils.isEmpty(bo.getPayerKey())){
            throw new ServiceException("sessionKey不能为空");
        }
        if(StringUtils.isEmpty(bo.getAppId())){
            throw new ServiceException("wxAppId不能为空");
        }
        if(!wxConfigs.containsKey(bo.getAppId())) {
            throw new ServiceException("不支持的wxAppId");
        }
        if(CollectionUtil.isEmpty(bo.getSplits())){
            throw new ServiceException("分账数据不能为空");
        }
        return 0;
    }

    @Override
    public TradePayBo pay(TradePayVo vo) {
        if (vo.getPaySplitAmt().compareTo(BigDecimal.ZERO) == 0) {
            TradePayBo respBo = new TradePayBo();
            respBo.setPayInfStatus(PayInfStatusEnum.SUCCESS.getCode());
            respBo.setOutSuccessTime(Convert.toDate(DateUtil.now()));
            respBo.setCallbackWeixin(new JSONObject()
                    .fluentPut("channel", vo.getChannel())
                    .fluentPut("params", new JSONObject()).toJSONString());
            return respBo.setId(vo.getId());
        } else {
            WeixinB2bConfig wxConfig = wxConfigs.get(WxMaConfigHolder.get());
            JSONObject signData = new JSONObject();
            signData.put("mchid", wxConfig.getMerchantId());
            signData.put("out_trade_no", vo.getTradeNo());
            signData.put("description", vo.getOrderNo());
            signData.put("env", "prod".equals(wxConfig.getAttach()) ? 0 : 1);
            signData.put("attach", wxConfig.getAttach()); // 附加参数
        /*
            https://developers.weixin.qq.com/minigame/product/jiaoyilei/yunyingguifan.html#_1-5-%E8%B5%84%E9%87%91%E7%BB%93%E7%AE%97
            1.同城配送 2.快递配送 3.门店自提 4.无需配送与提货
                快递物流
                    1、如用户主动确认收货，则资金将于收货后结算；
                    2、如用户未主动确认收货，则资金将于订单发货后的第10天，系统自动确认收货后结算
                自提/同城配送/虚拟发货
                    1、如用户主动确认收货，则资金将于收货后结算；
                    2、如用户未主动确认收货，则资金将于订单发货后的第2天，系统自动确认收货后结算
         */
            signData.put("delivery_type", 3);
            signData.put("amount", new JSONObject()
                    .fluentPut("order_amount", WeixinB2bUtil.yuanToFen(vo.getPaySplitAmt()))
                    .fluentPut("currency", "CNY")
            );
            String postBody = signData.toJSONString();
            TradePayBo respBo = new TradePayBo();
            respBo.setId(vo.getId());
            respBo.setPayInfStatus(PayInfStatusEnum.UNPAID.getCode());
            respBo.setCallbackWeixin(new JSONObject()
                    .fluentPut("channel", vo.getChannel())
                    .fluentPut("params", new JSONObject()
                            .fluentPut("signData", postBody)
                            .fluentPut("mode", "retail_pay_goods")
                            .fluentPut("paySig", SignUtils.createHmacSha256Sign(String.format("%s&%s", WeixinB2bPayApiEnum.PAY.getUrl(), postBody),
                                    "prod".equals(wxConfig.getAttach()) ? wxConfig.getProdAppKey() : wxConfig.getTestAppKey()).toLowerCase())
                            .fluentPut("signature", SignUtils.createHmacSha256Sign(postBody, vo.getPayerKey()).toLowerCase())).toJSONString());
            return respBo;
        }
    }

    @Override
    public TradePayBo payClose(TradePayVo vo) {
        TradePayBo respBo = new TradePayBo();
        respBo.setId(vo.getId());
        respBo.setInfReason("");
        respBo.setPayInfStatus(PayInfStatusEnum.CANCEL.getCode());
        respBo.setInfStatus(respBo.getPayInfStatus());
        respBo.setCloseTime(Convert.toDate(DateUtil.now()));
        return respBo;
    }

    @Override
    public TradePayBo payQuery(TradePayVo vo) {
        WeixinB2bConfig wxConfig = wxConfigs.get(WxMaConfigHolder.get());
        JSONObject body = new JSONObject();
        body.put("mchid", wxConfig.getMerchantId());
        body.put("out_trade_no", vo.getTradeNo());
        try {
            JSONObject resp = post(vo.getOrderNo(), "payQuery", WeixinB2bPayApiEnum.PAY_QUERY, body);
            return payRespToBo(resp).setId(vo.getId());
        } catch (ServiceException se) {
            if (se.getMessage().contains("[9403201]")) {
                // 订单不存在[9403201] , 返回null
                // log.keyword(vo.getOrderNo(), "payQuery").warn("[9403201] 数据查询不到", se);
                return null;
            }
            throw se;
        }
    }

    private TradePayBo payRespToBo(JSONObject resp) {
        TradePayBo respBo = new TradePayBo();
        respBo.setPayInfStatus(WeixinB2bPayInfStatusEnum.getInfStatus(resp.getString("pay_status")));
        respBo.setInfReason("");
        respBo.setOutTradeNo(resp.getString("order_id")); // B2b支付订单号
        respBo.setOutChannelNo(resp.getString("wxpay_transaction_id")); // 微信支付订单号
        respBo.setOutFeeAmt(BigDecimal.ZERO);
        respBo.setOutCouponAmt(BigDecimal.ZERO);
        JSONObject amtObject = resp.getJSONObject("amount");
        BigDecimal payerAmount = WeixinB2bUtil.fenToYuan(amtObject.getString("payer_amount"));
        if (PayInfStatusEnum.SUCCESS.getCode().equals(respBo.getPayInfStatus())) {
            respBo.setOutDiscountAmt(WeixinB2bUtil.fenToYuan(amtObject.getString("order_amount")).subtract(payerAmount));
        } else {
            respBo.setOutCouponAmt(BigDecimal.ZERO);
        }
        respBo.setOutCashAmt(payerAmount);
        respBo.setOutSuccessTime(WeixinB2bUtil.timeToDate(resp.getString("pay_time")));  // query 才有
        respBo.setPayerId(resp.getString("payer_openid"));
        return respBo;
    }

    @Override
    public JSONObject callback(String reqBody, HttpServletRequest request) {
        String signature = request.getParameter("signature");
        String timestamp = request.getParameter("timestamp");
        String nonce = request.getParameter("nonce");
        String msgSignature = request.getParameter("msg_signature"); // 随机字符串
        if (StringUtils.isNotBlank(signature) && StringUtils.isNotBlank(timestamp)
                && StringUtils.isNotBlank(nonce) && StringUtils.isNotBlank(msgSignature)) {
            if (wxMaService.checkSignature(timestamp, nonce, signature)) {
                JSONObject postData = JSON.parseObject(reqBody);
                String encrypt = postData.getString("Encrypt");
                String body = wxCryptUtils.get(WxMaConfigHolder.get()).decryptContent(msgSignature, timestamp, nonce, encrypt);
                log.keyword("payCallback").info("微信b2b回调 {}", body);
                return JSON.parseObject(body);
            }
        }
        return null;
    }

    @Override
    public TradePayBo payCallback(JSONObject resp) {
        return payRespToBo(resp).setTradeNo(resp.getString("out_trade_no"));
    }

    @Override
    public int refundValidate(OrderRefundBo bo, TradePayVo payVo) {
        if(CollectionUtil.isEmpty(bo.getSplits())){
            throw new ServiceException("分账数据不能为空");
        }
        // 退款先同步订单的 OutChannelNo 字段， 后续退款完成修改成退款对应的 微信退单号
        bo.setOutChannelNo(payVo.getOutChannelNo());
        bo.setRefundInfo(new JSONObject().fluentPut("successTime", payVo.getOutSuccessTime())
                .fluentPut("channelNo", payVo.getOutChannelNo()).toJSONString());
        return 0;
    }

    @Override
    public boolean refundValidate(TradePayRefundVo vo) {
        JSONObject params = JSONObject.parseObject(vo.getRefundInfo());
        if (params != null) {
            WeixinB2bConfig wxConfig = wxConfigs.get(WxMaConfigHolder.get());
            Date successTime = params.getDate("successTime");
            if (successTime != null && wxConfig.getRefundDelaySecond() != null) {
                // b2b 支付成功 ， 结算完成才能退款， 而结算完成基本支付完成的 3 分钟以上，
                Date time = DateUtil.offsetSecond(successTime, wxConfig.getRefundDelaySecond());
                if (time.after(new Date())) {
                    log.keyword(vo.getRefundNo(), "refundValidate").info("未到退款接口调用时间，稍后执行");
                    return false;
                }
            }
        }
        return true;
    }

    @Override
    public TradePayRefundBo refund(TradePayRefundVo vo) {
        /*
            a、订单如需多次退款，请等待前一次退款完成后再发起调用
            b、退款接口只是发起退款请求，不表示退款成功，请2分钟后调用退款查询结果轮询退款状态
         */
        if (vo.getRefundSplitAmt().compareTo(BigDecimal.ZERO) == 0) {
            TradePayRefundBo respBo = new TradePayRefundBo();
            respBo.setRefundInfStatus(RefundInfStatusEnum.SUCCESS.getCode());
            respBo.setOutSuccessTime(Convert.toDate(DateUtil.now()));
            return respBo.setId(vo.getId());
        } else {
            WeixinB2bConfig wxConfig = wxConfigs.get(WxMaConfigHolder.get());
            JSONObject body = new JSONObject();
            body.put("mchid", wxConfig.getMerchantId());
            // body.put("attach", client.config.getAttach()); // 对账文件中，退单没有
            body.put("out_trade_no", vo.getTradeNo()); // 对应订单号
            body.put("out_refund_no", vo.getTradeRefundNo()); // 同一退款单号多次请求只退一笔
            body.put("refund_amount", WeixinB2bUtil.yuanToFen(vo.getRefundSplitAmt()));
            // 1：人工客服退款 2：用户自己退款 3：其他
            body.put("refund_from", RefundBusiTypeEnum.PAY_CANCEL.getCode().equals(vo.getBusiType()) ? 2 : 1);
            // 0：暂无描述 1：产品问题 2：售后问题 3：意愿问题 4：价格问题 5：其他原因
            // body.put("refund_reasom", RefundBusiTypeEnum.PAY_CANCEL.getCode().equals(vo.getBusiType()) ? 3 : 0);
            JSONObject resp = post(vo.getRefundNo(), "refund", WeixinB2bPayApiEnum.PAY_REFUND, body);
            return refundRespToBo(resp).setId(vo.getId());
        }
    }

    @Override
    public TradePayRefundBo refundQuery(TradePayRefundVo vo) {
        WeixinB2bConfig wxConfig = wxConfigs.get(WxMaConfigHolder.get());
        JSONObject body = new JSONObject();
        body.put("mchid", wxConfig.getMerchantId());
        body.put("out_refund_no", vo.getTradeRefundNo());
        try {
            JSONObject resp = post(vo.getRefundNo(), "refundQuery", WeixinB2bPayApiEnum.PAY_REFUND_QUERY, body);
            return refundRespToBo(resp).setId(vo.getId());
        } catch (ServiceException se) {
            if (se.getMessage().contains("[9403201]")) {
                // log.keyword(vo.getRefundNo(), "refundQuery").warn("[9403201] 数据查询不到", se);
                return null;
            }
            throw se;
        }
    }


    private TradePayRefundBo refundRespToBo(JSONObject resp) {
        TradePayRefundBo respBo = new TradePayRefundBo();
        respBo.setOutTradeNo(resp.getString("refund_id")); //  B2b退单号
        respBo.setOutCouponAmt(BigDecimal.ZERO);
        respBo.setOutDiscountAmt(BigDecimal.ZERO);
        JSONObject amtObject = resp.getJSONObject("amount");
        if (amtObject == null) {
            respBo.setRefundInfStatus(RefundInfStatusEnum.PROCESSING.getCode());
            respBo.setInfReason("");
        } else {
            respBo.setRefundInfStatus(WeixinB2bRefundInfStatusEnum.getInfStatus(resp.getString("refund_status")));
            respBo.setInfReason(WeixinB2bUtil.toString(resp.getString("refund_desc")));
            respBo.setOutChannelNo(resp.getString("wxpay_refund_id"));
            respBo.setOutCashAmt(WeixinB2bUtil.fenToYuan(amtObject.getString("refund_amount")));
            respBo.setOutSuccessTime(WeixinB2bUtil.timeToDate(resp.getString("refund_time")));  // refundQuery 才有
        }
        return respBo;
    }

    public TradePayRefundBo refundCallback(JSONObject resp) {
        TradePayRefundBo respBo = new TradePayRefundBo();
        respBo.setTradeRefundNo(resp.getString("out_refund_no"));
        respBo.setOutTradeNo(resp.getString("refund_id")); //  B2b退单号
        respBo.setOutCouponAmt(BigDecimal.ZERO);
        respBo.setOutDiscountAmt(BigDecimal.ZERO);
        respBo.setRefundInfStatus(WeixinB2bRefundInfStatusEnum.getInfStatus(resp.getString("refund_status")));
        respBo.setInfReason(WeixinB2bUtil.toString(resp.getString("refund_desc")));
        respBo.setOutChannelNo(resp.getString("wxpay_refund_id"));
        respBo.setOutCashAmt(WeixinB2bUtil.fenToYuan(resp.getString("refund_amount")));
        respBo.setOutSuccessTime(WeixinB2bUtil.timeToDate(resp.getString("refund_time")));
        return respBo;
    }

    public WxMaService getWxMaService() {
        return this.wxMaService;
    }

    public String callbackCheck(HttpServletRequest request) {
        String signature = request.getParameter("signature");
        String timestamp = request.getParameter("timestamp");
        String nonce = request.getParameter("nonce");
        String echostr = request.getParameter("echostr"); // 随机字符串
        if (StringUtils.isNotBlank(signature) && StringUtils.isNotBlank(timestamp) && StringUtils.isNotBlank(nonce)) {
            if (wxMaService.checkSignature(timestamp, nonce, signature)) {
                return echostr;
            }
        }
        return null;
    }

    private JSONObject post(String orderNo, String refundNo, WeixinB2bPayApiEnum constant, JSONObject body) {
        try {
            WeixinB2bConfig wxConfig = wxConfigs.get(WxMaConfigHolder.get());
            String bodyStr = body.toJSONString();
            log.keyword(orderNo, refundNo).info("request = " + bodyStr);
            String url;
            if (constant.isPaySig()) {
                String paySig = SignUtils.createHmacSha256Sign(String.format("%s&%s",
                        constant.getUrl(), bodyStr), wxConfig.getProdAppKey()).toLowerCase();
                url = String.format("%s%s?pay_sig=%s", wxConfig.getBaseUrl(), constant.getUrl(), paySig);
            } else {
                url = wxConfig.getBaseUrl() + constant.getUrl();
            }
            String respStr = wxMaService.post(url, bodyStr);
            JSONObject resp = JSON.parseObject(respStr);
            if (resp.containsKey("errcode") && resp.getInteger("errcode") != 0) {
                log.keyword(orderNo, refundNo).warn("response = " + respStr);
                throw new ServiceException(String.format("%s[%s]",
                        resp.getString("errmsg"), resp.getInteger("errcode")), HttpStatus.WARN);
            } else {
                log.keyword(orderNo, refundNo).info("response = " + respStr);
                return resp;
            }
        } catch (ServiceException se) {
            throw se;
        } catch (WxErrorException wee) {
            // getretailinfo 接口错误太多，不打印
            log.keyword(orderNo, refundNo).info(wee.getMessage(), wee);
            throw new ServiceException(String.format("%s[%s]", wee.getError().getErrorMsg(),
                    wee.getError().getErrorCode()), HttpStatus.WARN);
        } catch (Exception e) {
            log.keyword(orderNo, refundNo).error(e.getMessage(), e);
            throw new ServiceException("微信b2b支付服务异常，请联系管理员", R.FAIL);
        }
    }

    @Override
    public <T> ExcelDataInfo<T> fileDownload(String fileType, LocalDate fileDate) {
        WeixinB2bConfig wxConfig = wxConfigs.get(WxMaConfigHolder.get());
        String date = LocalDateTimeUtil.format(fileDate, "yyyyMMdd");
        JSONObject body = new JSONObject();
        body.put("mchid", wxConfig.getMerchantId());
        body.put("bill_date", date);
        try {
            JSONObject resp = post(fileType, "fileDownload", WeixinB2bPayApiEnum.PAY_DOWNLOAD, body);
            /*
                success_bill_url    仅支付的文件
                refund_bill_url     仅退款的文件
                all_bill_url        支付 + 退款的文件
                fund_bill_url       账户结余
                ended_day_total_amt     可用金额 = 冻结 + 可提现
                ended_day_avail_amt     可提现金额
             */
            HttpClient httpClient = (HttpClient) wxMaService.getRequestHttp().getRequestHttpClient();
            if (AccountFileTypeEnum.WJY.getCode().equals(fileType)) {
                String url = resp.getString("all_bill_url");
                if (StringUtils.isNotBlank(url)) {
                    log.keyword(fileType, "fileDownload").info(date + " 下载微信b2b交易文件");
                    // 微信总余额 = cashAmt + frozenAmt，   也就是 ended_day_total_amt 字段
                    // BigDecimal cashAmt = WeixinB2bUtil.fenToYuan(resp.getString("ended_day_avail_amt")); // 和每日提现金额相同
                    // BigDecimal frozenAmt =  WeixinB2bUtil.fenToYuan(resp.getString("ended_day_frozen_amt")); // 暂不知道用处
                    return new ExcelDataInfo<T>().setFile(getFileBytes(httpClient, url)).setTransDate(fileDate)
                            .setFileType(fileType).setMerchantId(wxConfig.getMerchantId())
                            .setFileName(String.format("b2b_%s_%s.csv", wxConfig.getMerchantId(), date));
                }

            } else if (AccountFileTypeEnum.WZJ.getCode().equals(fileType)) {
                String url = resp.getString("fund_bill_url");
                if (StringUtils.isNotBlank(url)) {
                    log.keyword(fileType, "fileDownload").info(date + " 下载微信b2b资金文件");
                    return new ExcelDataInfo<T>().setFile(getFileBytes(httpClient, url)).setTransDate(fileDate)
                            .setFileType(fileType).setMerchantId(wxConfig.getMerchantId())
                            .setFileName(String.format("b2b_fund_%s_%s.csv", wxConfig.getMerchantId(), date));
                }
            }
        } catch (ServiceException se) {
            throw se;
        } catch (IOException ioe) {
            log.keyword(fileType, "fileDownload").error("微信b2b", ioe);
            throw new ServiceException("微信b2b文件下载接口异常", R.FAIL);
        }
        log.keyword(fileType, "fileDownload").info(date + " 暂无可下载的文件");
        return null;
    }

    private byte[] getFileBytes(HttpClient httpClient, String url) throws IOException {
        HttpResponse response = httpClient.execute(new HttpGet(url));
        HttpEntity entity = response.getEntity();
        if (response.getStatusLine().getStatusCode() != 200) {
            throw new ServiceException("微信b2b文件下载接口异常", R.FAIL);
        }
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        entity.writeTo(baos);
        baos.close();
        return baos.toByteArray();
    }

    public RetailInfoVo getRetailInfo(RetailInfoBo bo) {
        JSONObject body = new JSONObject();
        body.put("openid", bo.getPayerId());
        try {
            JSONObject resp = post(bo.getPayerId(), "getretailinfo", WeixinB2bPayApiEnum.RETAIL_INFO, body);
            JSONArray infos = resp.getJSONArray("info");
            RetailInfoVo infoVo = new RetailInfoVo();
            for (int i = 0; i < infos.size(); i++) {
                JSONObject info = infos.getJSONObject(i);
                infoVo.setMobilePhone(info.getString("mobile_phone"));
                infoVo.setRetailType(info.getString("retail_type"));
                infoVo.setRetailName(info.getString("retail_name"));
                infoVo.setLegalPersonName(info.getString("legal_person_name"));
                infoVo.setPayerId(info.getString("openid"));
                infoVo.setStatus(info.getInteger("status"));
                infoVo.setAuthTime(new Date(info.getLongValue("auth_time") * 1000));
                infoVo.setGrantTime(new Date(info.getLongValue("grant_time") * 1000));
            }
            return infoVo;
        } catch (ServiceException se) {
            if (se.getMessage().contains("[9404102]")) {
                // log.keyword(bo.getAppId(), "getretailinfo").warn("[9404102] 数据查询不到", se);
                return null;
            }
            throw se;
        }
    }

    @Override
    public int getSplitCount() {
        return 20;
    }

    @Override
    public void paySplit(String merchantNo, TradePayVo vo) {
        BigDecimal totalAmt = BigDecimal.ZERO;
        Date infTime = Convert.toDate(DateUtil.now());
        List<TradeAccSplitVo> splitVos = new ArrayList<>();
        JSONArray arrays = new JSONArray();
        for (TradeAccSplitVo splitVo : vo.getSplits()) {
            splitVo.setInfStatus(PayInfStatusEnum.SUCCESS.getCode());
            splitVo.setInfTime(infTime);
            splitVo.setOutSuccessTime(infTime);
            splitVo.setInfReason("");
            if (splitVo.getTransAmt().compareTo(BigDecimal.ZERO) == 0) {
                splitVo.setInfReason("分帐金额为零");
            } else {
                splitVos.add(splitVo);
                arrays.add(new JSONObject()
                        .fluentPut("RechargeSubAcctNo", splitVo.getOutAcctCode())
                        .fluentPut("SubOrderFillMemberCd", splitVo.getOutOrgCode())
                        .fluentPut("SubOrderTranAmt", splitVo.getTransAmt().setScale(2, RoundingMode.HALF_UP).toString())
                        .fluentPut("SubOrderTranFee", "0")
                        .fluentPut("SubOrderNo", splitVo.getSplitNo())
                );
                totalAmt = totalAmt.add(splitVo.getTransAmt());
            }
        }
        if (arrays.size() > 0) {
            JSONObject body = new JSONObject();
            body.put("PayChannelType", "0001");
            body.put("PayChannelAssignMerNo", merchantNo); // 微信侧的商户号一般是1开头的
            body.put("TotalOrderNo", vo.getOutChannelNo());  // 支付表中 outChannelNo ， 微信渠道订单号42开头的
            body.put("TranItemArray", arrays);
            body.put("OrdersCount", arrays.size());  // 本次的
            body.put("TranTotalAmt", totalAmt.toString());  // 本次的
            JSONObject resp = client.post(vo.getOrderNo(), "paySplit", PinganCloudClientApiEnum.PAY_SPLIT, body);
            arrays = resp.getJSONArray("TranItemArray");
            for (int i = 0; i < arrays.size(); i++) {
                JSONObject split = arrays.getJSONObject(i);
                TradeAccSplitVo splitVo = splitVos.get(i);
                splitVo.setOutTradeNo(split.getString("FrontSeqNo"));
            }
        }
    }

    @Override
    public void refundSplit(String outChannelNo, TradePayRefundVo vo) {
        BigDecimal totalAmt = BigDecimal.ZERO;
        Date infTime = Convert.toDate(DateUtil.now());
        List<TradeAccSplitVo> splitVos = new ArrayList<>();
        JSONArray arrays = new JSONArray();
        for (TradeAccSplitVo splitVo : vo.getSplits()) {
            splitVo.setInfStatus(PayInfStatusEnum.SUCCESS.getCode());
            splitVo.setInfTime(infTime);
            splitVo.setOutSuccessTime(infTime);
            splitVo.setInfReason("");
            if (splitVo.getTransAmt().compareTo(BigDecimal.ZERO) == 0) {
                splitVo.setInfReason("分帐金额为零");
            } else {
                splitVos.add(splitVo);
                arrays.add(new JSONObject()
                        .fluentPut("SubOrderRefundSubAcctNo", splitVo.getOutAcctCode())
                        .fluentPut("SubOrderRefundMemberCd", splitVo.getOutOrgCode())
                        .fluentPut("SubOrderMemberRefundAmt", splitVo.getTransAmt().setScale(2, RoundingMode.HALF_UP).toString())
                        .fluentPut("SubOrderFeeRefundAmt", "0")
                        .fluentPut("SubOrderRefundOrderNo", splitVo.getSplitRelNo())
                        .fluentPut("SubOrderRefundSeqNo", splitVo.getSplitNo())
                );
                totalAmt = totalAmt.add(splitVo.getTransAmt());
            }
        }
        if (arrays.size() > 0) {
            JSONObject body = new JSONObject();
            body.put("OldPayChannelType", "0001");
            body.put("OldTotalOrderNo", outChannelNo); // 支付表中 outChannelNo ， 微信渠道订单号42开头的
            body.put("TranItemArray", arrays);
            body.put("RefundOrderNum", arrays.size());
            body.put("TotalRefundAmt", totalAmt.toString());
            JSONObject resp = client.post(vo.getRefundNo(), "refundSplit", PinganCloudClientApiEnum.PAY_REFUND_SPLIT, body);
            arrays = resp.getJSONArray("TranItemArray");
            for (int i = 0; i < arrays.size(); i++) {
                JSONObject split = arrays.getJSONObject(i);
                TradeAccSplitVo splitVo = splitVos.get(i);
                splitVo.setOutTradeNo(split.getString("FrontSeqNo"));
            }
        }
    }

}
