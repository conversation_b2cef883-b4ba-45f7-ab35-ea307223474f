package cn.xianlink.trade.channel.lakala.constant;

import cn.hutool.core.util.ObjectUtil;

public enum LakalaPayInfStatusEnum {
    // 支付状态对应关系   转成 PayInfStatusEnum 枚举值
    INIT("INIT", "初始化", 0),
    CREATE("CREATE", "下单成功", 0),
    SUCCESS("SUCCESS", "交易成功", 1),
    FAIL("FAIL", "交易失败", 2),
    DEAL("DEAL", "交易处理中", 0),
    UNKNOWN("UNKNOWN", "未知状态", 0),
    CLOSE("CLOSE", "订单关闭", 2),
    PART_REFUND("PART_REFUND", "部分退款", 1),
    REFUND("REFUND", "全部退款", 1),
    REVOKED("REVOKED", "订单撤销", 1);

    private final String status;
    private final String desc;
    private final Integer infStatus;

    LakalaPayInfStatusEnum(String status, String desc, Integer infStatus) {
        this.status = status;
        this.desc = desc;
        this.infStatus = infStatus;
    }

    public String getStatus() {
        return status;
    }

    public Integer getInfStatus() {
        return infStatus;
    }

    public String getDesc() {
        return desc;
    }

    public static Integer getInfStatus(String status) {
        for (LakalaPayInfStatusEnum e : values()) {
            if (ObjectUtil.equal(e.getStatus(), status)) {
                return e.getInfStatus();
            }
        }
        return -1;
    }

}
