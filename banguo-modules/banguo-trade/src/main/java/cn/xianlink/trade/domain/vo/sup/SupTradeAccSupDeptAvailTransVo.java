package cn.xianlink.trade.domain.vo.sup;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;


/**
 * 结算单视图对象 trade_acc_avail
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@Data

public class SupTradeAccSupDeptAvailTransVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 销售日期
     */
    @ExcelProperty(value = "销售日期")
    @DateTimeFormat("yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate transDate;
    /**
     * 结算日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate availDate;
    /**
     * 状态 0 未结， 1 已结
     */
    @ExcelProperty(value = "状态")
    private Integer status;
    /**
     * 提现金额
     */
    @ExcelProperty(value = "提现金额")
    private BigDecimal cashAmt;
    /**
     * 可提现金额 = pay_amt - refund_amt + in_amt - out_amt
     */
    @ExcelProperty(value = "总收入金额")
    private BigDecimal totalAmt;
    /**
     * 佣金
     */
    @ExcelProperty(value = "佣金")
    private BigDecimal commissionAmt;
    /**
     * 销售金额
     */
    @ExcelProperty(value = "销售金额")
    private BigDecimal payAmt;
    /**
     * 退款总金额
     */
    @ExcelProperty(value = "退款总金额")
    private BigDecimal refundAmt;
    /**
     * 退货退款 = refundLackAmt1 + refundShortAmt1
     */
    @ExcelProperty(value = "退货退款")
    private BigDecimal refundAmt1;
    /**
     * 少货退款
     */
    @ExcelProperty(value = "少货退款")
    private BigDecimal refundLackAmt1;
    /**
     * 缺货退款
     */
    @ExcelProperty(value = "缺货退款")
    private BigDecimal refundShortAmt1;
    /**
     * 差额退金额
     */
    @ExcelProperty(value = "差额退金额")
    private BigDecimal refundAmt2;
    /**
     * 报损金额
     */
    @ExcelProperty(value = "报损金额")
    private BigDecimal refundAmt3;
    /**
     * 加扣款金额
     */
    @ExcelProperty(value = "加扣款金额")
    private BigDecimal inOutAmt;
    /**
     * 加款金额
     */
    @ExcelProperty(value = "加款金额")
    private BigDecimal inAmt;
    /**
     * 扣款金额
     */
    @ExcelProperty(value = "扣款金额")
    private BigDecimal outAmt;
    /**
     * 未履约销售总金额
     */
    @ExcelProperty(value = "未履约销售总金额")
    private BigDecimal undonePayAmt;
//    /**
//     * 未履约待送货
//     */
//    @ExcelProperty(value = "未履约待送货")
//    private BigDecimal undonePayDeliverAmt;
//    /**
//     * 未履约待质检
//     */
//    @ExcelProperty(value = "未履约待质检")
//    private BigDecimal undonePayCheckAmt;
//    /**
//     * 未履约待装车
//     */
//    @ExcelProperty(value = "未履约待装车")
//    private BigDecimal undonePayLoadingAmt;
    /**
     * 未确认退款总金额
     */
    @ExcelProperty(value = "未确认退款总金额")
    private BigDecimal undoneRefundAmt;
    /**
     * 未确认少货退款
     */
    @ExcelProperty(value = "未确认少货退款")
    private BigDecimal undoneRefundLackAmt1;
    /**
     * 未确认缺货退款
     */
    @ExcelProperty(value = "未确认缺货退款")
    private BigDecimal undoneRefundShortAmt1;
    /**
     * 未确认报损金额
     */
    @ExcelProperty(value = "未确认报损金额")
    private BigDecimal undoneRefundAmt3;

    /**
     * 历史销售退还总金额
     */
    @ExcelProperty(value = "历史销售退还总金额")
    private BigDecimal overduePayAmt;

    /**
     * 历史退回退款总金额
     */
    @ExcelProperty(value = "历史退回退款总金额")
    private BigDecimal overdueRefundAmt;
    /**
     * 历史退回少货退款
     */
    @ExcelProperty(value = "历史退回少货退款")
    private BigDecimal overdueRefundLackAmt1;
    /**
     * 历史退回缺货退款
     */
    @ExcelProperty(value = "历史退回缺货退款")
    private BigDecimal overdueRefundShortAmt1;
    /**
     * 历史退回报损金额
     */
    @ExcelProperty(value = "历史退回报损金额")
    private BigDecimal overdueRefundAmt3;

    /**
     * 平台补贴金额
     */
    @ExcelProperty(value = "平台补贴")
    private BigDecimal platformSubsidyAmt;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;
}