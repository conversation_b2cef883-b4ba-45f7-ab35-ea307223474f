package cn.xianlink.trade.mapper;

import cn.xianlink.common.mybatis.core.mapper.BaseMapperPlus;
import cn.xianlink.trade.domain.TradePay;
import cn.xianlink.trade.domain.bo.TradePayBo;
import cn.xianlink.trade.domain.vo.TradePayVo;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 支付Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
public interface TradePayMapper extends BaseMapperPlus<TradePay, TradePayVo> {

    TradePay queryRefundAmt(@Param("orderNo") String orderNo);

    void updateRefundAmt(@Param("payId") Long payId, @Param("vo") TradePay pay);

    TradePay queryStatusByTrans(@Param("transNo") String transNo, @Param("transTypes") List<String> transTypes);

    int updateStatusByTrans(@Param("vo") TradePay pay);

    void updateAcctStatus(@Param("acctDate") LocalDate acctDate, @Param("bos") List<TradePayBo> bos);
}
