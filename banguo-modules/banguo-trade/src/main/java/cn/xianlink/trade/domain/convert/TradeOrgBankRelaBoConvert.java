package cn.xianlink.trade.domain.convert;

import cn.xianlink.trade.domain.TradeOrgRelation;
import cn.xianlink.trade.domain.bo.org.TradeOrgBankRelaBo;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

/**
 * 数据转换器
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TradeOrgBankRelaBoConvert extends BaseMapper<TradeOrgBankRelaBo, TradeOrgRelation> {
}
