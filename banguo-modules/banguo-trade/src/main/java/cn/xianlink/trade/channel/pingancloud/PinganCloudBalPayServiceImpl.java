package cn.xianlink.trade.channel.pingancloud;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.xianlink.common.api.enums.trade.AccountOrgTypeEnum;
import cn.xianlink.common.api.enums.trade.PayChannelEnum;
import cn.xianlink.common.api.enums.trade.PayInfStatusEnum;
import cn.xianlink.common.api.enums.trade.RefundInfStatusEnum;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.trade.api.domain.bo.OrderPayBo;
import cn.xianlink.trade.api.domain.bo.OrderRefundBo;
import cn.xianlink.trade.channel.AccountService;
import cn.xianlink.trade.channel.PaymentService;
import cn.xianlink.trade.channel.pingancloud.constant.PinganCloudClientApiEnum;
import cn.xianlink.trade.domain.bo.TradePayBo;
import cn.xianlink.trade.domain.bo.TradePayRefundBo;
import cn.xianlink.trade.domain.bo.org.TradeOrgBankAmtBo;
import cn.xianlink.trade.domain.vo.TradeAccSplitVo;
import cn.xianlink.trade.domain.vo.TradePayRefundVo;
import cn.xianlink.trade.domain.vo.TradePayVo;
import cn.xianlink.trade.domain.vo.org.TradeOrgRelationInfoVo;
import cn.xianlink.trade.service.ITradeOrgBankDetailService;
import com.alibaba.fastjson.JSONObject;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.CustomLog;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * 0 6248  开 00 户 ， 不用绑卡
 * 1 支付 6276（异步）， 转成同步查询到  OrderRecvCode
 *    1 测试环境模拟支付  6211
 *    2 编号查询 6279
 *    3 编号关闭 6277（异步）， 转成同步查询到  CancelStatus 状态
 *    4 分账 6034 单条
 * 2 退款 6278 ， 查询 6110
 *    1 分账 6164 单条
 *
 * <AUTHOR>
 * @date 2025-02-25
 */
@CustomLog
@Component
public class PinganCloudBalPayServiceImpl implements PaymentService {

    @Resource
    private transient PinganCloudClientService client;
    @Resource
    private transient AccountService accountService;
    @Resource
    private transient ITradeOrgBankDetailService tradeOrgBankDetailService;

    @Override
    public int payValidate(OrderPayBo bo) {
        if(StringUtils.isEmpty(bo.getPayerId())){
            throw new ServiceException("openId不能为空");
        }
        if(StringUtils.isEmpty(bo.getAppId())){
            throw new ServiceException("wxAppId不能为空");
        }
        if(CollectionUtil.isEmpty(bo.getSplits())){
            throw new ServiceException("分账数据不能为空");
        }
        TradeOrgRelationInfoVo infoVo = tradeOrgBankDetailService.getInfoByCode(bo.getCustomerCode(), AccountOrgTypeEnum.CUSTOMER.getCode());
        if (infoVo == null || StringUtils.isEmpty(infoVo.getOutOrgCode()) || StringUtils.isEmpty(infoVo.getOutAcctCode())) {
            throw new ServiceException("未开户，不能支付");
        }
        if (infoVo.getStatus() != 2) {
            throw new ServiceException("未绑银行卡，不能支付");
        }
        bo.setCustomerAcctCode(infoVo.getOutAcctCode());
        bo.setCustomerOutCode(infoVo.getOutOrgCode());
        return 0;
    }

    @Override
    public TradePayBo pay(TradePayVo vo) {
        TradeOrgBankAmtBo amtBo = accountService.queryBankAmt(vo.getCustomerOutCode(), vo.getCustomerAcctCode());
        if (amtBo == null || amtBo.getBankAvailAmt().compareTo(vo.getPaySplitAmt()) < 0) {
            throw new ServiceException(String.format("银行可用余额 %s 元, 不能支付",
                    amtBo == null ? "0" : amtBo.getBankAvailAmt()));
        }
        TradePayBo respBo = payQuery(vo);
        respBo.setCallbackWeixin(new JSONObject()
                .fluentPut("channel", vo.getChannel())
                .fluentPut("params", new JSONObject()).toJSONString());
        return respBo;
    }

    @Override
    public TradePayBo payQuery(TradePayVo vo) {
        TradePayBo respBo = new TradePayBo();
        respBo.setId(vo.getId());
        respBo.setPayInfStatus(PayInfStatusEnum.SUCCESS.getCode());
        respBo.setOutChannelNo("");
        respBo.setOutFeeAmt(BigDecimal.ZERO);
        respBo.setOutCouponAmt(BigDecimal.ZERO);
        respBo.setOutCashAmt(vo.getPaySplitAmt());
        respBo.setOutSuccessTime(vo.getCreateTime());
        return respBo;
    }

    @Override
    public TradePayBo payClose(TradePayVo vo) {
        return payQuery(vo);
    }

    @Override
    public JSONObject callback(String reqBody, HttpServletRequest request) {
        return null;
    }

    @Override
    public TradePayBo payCallback(JSONObject resp) {
        return null;
    }

    @Override
    public int refundValidate(OrderRefundBo bo, TradePayVo payVo) {
        if(CollectionUtil.isEmpty(bo.getSplits())){
            throw new ServiceException("分账数据不能为空");
        }
        return 0;
    }

    @Override
    public TradePayRefundBo refund(TradePayRefundVo vo) {
        return refundQuery(vo);
    }

    @Override
    public TradePayRefundBo refundQuery(TradePayRefundVo vo) {
        TradePayRefundBo respBo = new TradePayRefundBo();
        respBo.setId(vo.getId());
        respBo.setRefundInfStatus(RefundInfStatusEnum.SUCCESS.getCode());
        respBo.setOutCouponAmt(BigDecimal.ZERO);
        respBo.setOutDiscountAmt(BigDecimal.ZERO);
        respBo.setOutCashAmt(vo.getRefundSplitAmt());
        respBo.setOutSuccessTime(vo.getCreateTime());
        respBo.setInfReason("");
        return respBo;
    }

    @Override
    public int getSplitCount() {
        return 1;
    }

    @Override
    public void paySplit(String merchantNo, TradePayVo vo) {
        TradeAccSplitVo splitVo = vo.getSplits().get(0);
        Date infTime = Convert.toDate(DateUtil.now());
        splitVo.setInfStatus(PayInfStatusEnum.SUCCESS.getCode());
        splitVo.setInfTime(infTime);
        splitVo.setOutSuccessTime(infTime);
        splitVo.setInfReason("");
        if (splitVo.getTransAmt().compareTo(BigDecimal.ZERO) == 0) {
            splitVo.setInfReason("分帐金额为零");
        } else {
            JSONObject body = new JSONObject();
            body.put("FunctionFlag", "6"); // 6 是 t+1，  9 是 t+0
            body.put("OutSubAcctNo", vo.getCustomerAcctCode());
            body.put("OutMemberCode", vo.getCustomerOutCode());
            body.put("InSubAcctNo", splitVo.getOutAcctCode());
            body.put("InMemberCode", splitVo.getOutOrgCode());
            body.put("TranAmt", splitVo.getTransAmt().toString());
            body.put("TranFee", "0.0");
            body.put("TranType", "01");
            body.put("Ccy", "RMB");
            body.put("OrderNo", splitVo.getRelateNo()); //
            body.put("Remark", PayChannelEnum.PAY_PINGAN_CLOUD_LARGE.getCode().equals(vo.getChannel()) ? "大额分账支付" : "余额分账支付");
            body.put("CnsmrSeqNo", splitVo.getSplitNo());
            JSONObject resp = client.post(splitVo.getRelateNo(), "paySplit", PinganCloudClientApiEnum.ACC_TRANSFER_PAY, body);
            splitVo.setOutTradeNo(resp.getString("FrontSeqNo"));
        }
    }

    @Override
    public void refundSplit(String outChannelNo, TradePayRefundVo vo) {
        TradeAccSplitVo splitVo = vo.getSplits().get(0);
        Date infTime = Convert.toDate(DateUtil.now());
        splitVo.setInfStatus(PayInfStatusEnum.SUCCESS.getCode());
        splitVo.setInfTime(infTime);
        splitVo.setOutSuccessTime(infTime);
        splitVo.setInfReason("");
        if (splitVo.getTransAmt().compareTo(BigDecimal.ZERO) == 0) {
            splitVo.setInfReason("分帐金额为零");
        } else {
            JSONObject body = new JSONObject();
            body.put("FunctionFlag", "2"); // 2 是针对 6034-6或-9 的退款操作
            body.put("OldOutSubAcctNo", vo.getCustomerAcctCode());
            body.put("OldOutMemberCode", vo.getCustomerOutCode());
            body.put("OldInSubAcctNo", splitVo.getOutAcctCode());
            body.put("OldInMemberCode", splitVo.getOutOrgCode());
            body.put("OldTranSeqNo", splitVo.getSplitRelNo());
            body.put("OldOrderNo", splitVo.getRelateNo());
            body.put("ReturnAmt", splitVo.getTransAmt().toString());
            body.put("ReturnCommission", "0.0"); // 6034-6 时填 0.0
            body.put("Remark", PayChannelEnum.PAY_PINGAN_CLOUD_LARGE.getCode().equals(vo.getChannel()) ? "大额分账支付" : "余额分账支付");
            body.put("CnsmrSeqNo", splitVo.getSplitNo());
            JSONObject resp = client.post(vo.getRefundNo(), "refundSplit", PinganCloudClientApiEnum.ACC_TRANSFER_REFUND, body);
            splitVo.setOutTradeNo(resp.getString("FrontSeqNo"));
        }
    }

    public void payBalanceComplete(String recvCode, String recvAcctName,BigDecimal recvAmt) {
        JSONObject body = new JSONObject();
        body.put("RecvAcctNo", recvCode); // OrderRecvCode
        body.put("RecvAcctName", recvAcctName); // OrderRecvCodeAcctName
        body.put("ApplyTakeCashAmt", recvAmt.toString());
        body.put("MarketChargeCommission", "0.00");
        // 下面是转账人的信息  （从FAT7测试中，随便取了一个银行卡， 真实银行卡转账测试不过）
        body.put("HoldOne", "6230580100004266097");
        body.put("HoldTwo", "夏侯翠");
        body.put("RecvAcctOpenBranchName", "平安银行");
        body.put("RecvAcctOpenBranchInterbankId", "0798"); // 默认值 ？？
        client.post(recvCode, "balanceComplete", PinganCloudClientApiEnum.ACC_TRANSFER_TEST, body);
    }

}
