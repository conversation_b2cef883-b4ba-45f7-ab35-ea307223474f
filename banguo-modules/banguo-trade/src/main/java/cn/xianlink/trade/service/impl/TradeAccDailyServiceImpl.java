package cn.xianlink.trade.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.xianlink.trade.channel.AccountService;
import cn.xianlink.trade.domain.TradeAccDaily;
import cn.xianlink.trade.domain.vo.org.TradeAccDailyAmtVo;
import cn.xianlink.trade.domain.vo.org.TradeAccDailyVo;
import cn.xianlink.trade.mapper.*;
import cn.xianlink.trade.service.ITradeAccDailyService;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 账务总Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@CustomLog
@RequiredArgsConstructor
@Service
public class TradeAccDailyServiceImpl implements ITradeAccDailyService {

    private final transient TradeAccDailyMapper baseMapper;
    private final transient AccountService accountService;

    @Override
    public List<TradeAccDailyVo> queryList(LocalDate acctDate) {
        return baseMapper.selectVoList(Wrappers.lambdaQuery(TradeAccDaily.class).eq(TradeAccDaily::getAcctDate, acctDate));
    }

    @Override
    public List<TradeAccDailyAmtVo> queryDailyAmt(LocalDate acctDate) {
        return baseMapper.queryDailyAmt(acctDate);
    }

    @Override
    public List<TradeAccDailyVo> queryDailyAmtError(LocalDate acctDate) {
        // 手续子账户： 因转账出，在系统中没有记录，就不对比了
        return baseMapper.selectVoList(Wrappers.query(TradeAccDaily.class)
                .and(ll -> ll.ne("out_org_type", 10).or(l-> l.eq("out_org_code", accountService.getMarketingAcctCode())))
                .eq("acct_date", acctDate).apply("occur_amt != balance_amt - yda_balance_amt"));
    }

    @Override
    public List<TradeAccDailyVo> insertBatch(LocalDate acctDate, List<String> outOrgCodes) {
        if (CollectionUtil.isNotEmpty(outOrgCodes)) {
            List<TradeAccDailyVo> existVos = baseMapper.selectVoList(Wrappers.lambdaQuery(TradeAccDaily.class)
                    .select(TradeAccDaily::getId, TradeAccDaily::getOutOrgCode)
                    .eq(TradeAccDaily::getAcctDate, acctDate).in(TradeAccDaily::getOutOrgCode, outOrgCodes));
            Map<String, List<TradeAccDailyVo>> existMap = existVos.stream().collect(Collectors.groupingBy(TradeAccDailyVo::getOutOrgCode, Collectors.toList()));
            List<String> notExistCodes = outOrgCodes.stream().filter(vo -> !existMap.containsKey(vo)).toList();
            if (CollectionUtil.isNotEmpty(notExistCodes)) {
                baseMapper.insertBatch(acctDate, notExistCodes);
                existVos.addAll(baseMapper.selectVoList(Wrappers.lambdaQuery(TradeAccDaily.class)
                        .select(TradeAccDaily::getId, TradeAccDaily::getOutOrgCode)
                        .eq(TradeAccDaily::getAcctDate, acctDate).in(TradeAccDaily::getOutOrgCode, notExistCodes)));
            }
            return existVos;
        }
        return new ArrayList<>();
    }
    /**
     * 更新昨日数据
     */
    @Override
    public void updateBatch(List<TradeAccDaily> bos) {
        if (bos.size() > 0) {
            baseMapper.updateBatchById(bos);
        }
    }

}
