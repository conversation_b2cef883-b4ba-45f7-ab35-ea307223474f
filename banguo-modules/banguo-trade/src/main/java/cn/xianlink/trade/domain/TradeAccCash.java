package cn.xianlink.trade.domain;

import cn.xianlink.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * 提现单对象 trade_acc_cash
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("trade_acc_cash")
public class TradeAccCash extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 提现单号
     */
    private String cashNo;

    /**
     * 提现时间
     */
    private LocalDate cashDate;

    /**
     * 0 总仓 1 城市仓 2 供应商
     */
    private Integer orgType;

    /**
     * 分账机构id
     */
    private Long orgId;

    /**
     * 分账机构代码
     */
    private String orgCode;
	/**
     * 部门id
     */
    private Long deptId;

    /**
     * 提现金额
     */
    private BigDecimal cashAmt;
    /**
     * 佣金金额
     */
    private BigDecimal commissionAmt;
    /**
     * 实际到账金额
     */
    private BigDecimal outActualAmt;

    /**
     * 手续费
     */
    private BigDecimal outFeeAmt;

    /**
     * 结算单个数
     */
    private Integer availCount;
    /**
     * 银行卡号
     */
    private String bankAccName;
    /**
     * 银行卡号
     */
    private String bankAccount;
    /**
     * 银行名 对应bankBranchName
     */
    private String bankBranchName;

    /**
     * 银行流水号
     */
    private String outTradeNo;
    /**
     *
     */
    private String outOrgCode;
    /**
     * 子账号
     */
    private String outAcctCode;
    /**
     * 总仓机构ids
     */
    private String acctOrgIds;
    /**
     * 部门ids
     */
    private String deptIds;
    /**
     * 支付发起时间
     */
    private Date infTime;
    /**
     * 重试次数
     */
    private Integer infRetries;
    /**
     * 接口状态 0 未支付;1 支付完成 ;2 支付取消; 10 接口未调用; 11 接口失败
     */
    private Integer infStatus;
    /**
     * 接口失败原因
     */
    private String infReason;
    /**
     * 分账单号
     */
    private String splitNo;
    /**
     * 分账状态 2提现 3提现完成
     */
    private Integer status;
    /**
     * 提现申请时间
     */
    private Date cashCheckTime;
    /**
     * 提现时间
     */
    private Date cashTime;
    /**
     * 删除标志（0代表存在 存入这条数据的主键代表删除）
     */
    @TableLogic(delval = "id")
    private Long delFlag;


}
