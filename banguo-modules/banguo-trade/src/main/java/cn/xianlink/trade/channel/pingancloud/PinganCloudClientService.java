package cn.xianlink.trade.channel.pingancloud;

import cn.com.agree.cipher.jwt.JWT;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base64;
import cn.xianlink.common.core.constant.HttpStatus;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.json.utils.JsonUtils;
import cn.xianlink.trade.channel.pingancloud.constant.PinganCloudClientApiEnum;
import cn.xianlink.trade.constant.Constants;
import cn.xianlink.trade.utils.ZipFileUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.pingan.openbank.api.sdk.client.ApiClient;
import com.pingan.openbank.api.sdk.common.StringUtil;
import com.pingan.openbank.api.sdk.common.helper.FileService;
import com.pingan.openbank.api.sdk.common.http.HttpException;
import com.pingan.openbank.api.sdk.common.http.HttpResult;
import com.pingan.openbank.api.sdk.common.util.Md5Util;
import com.pingan.openbank.api.sdk.entity.*;
import com.pingan.openbank.api.sdk.enums.SignatureEnum;
import com.pingan.openbank.api.sdk.exception.ExceptionEnum;
import com.pingan.openbank.api.sdk.exception.OpenBankSdkException;
import com.pingan.openbank.api.sdk.service.SignatureService;
import io.jsonwebtoken.lang.Strings;
import io.micrometer.common.util.StringUtils;
import lombok.CustomLog;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import javax.crypto.CipherInputStream;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.*;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * 平安支付client
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@CustomLog
@Component
public class PinganCloudClientService {
    //    T6509("订单号已存在"),
    //    T6534("退款单号已存在"),
    //    T9100("交易通讯超时");  以下错误码，调用后再次查询后返回
    private final static List<String> queryCodeLists  = Arrays.asList("T6509", "T6534", "T9100");
    private final static Random random = new Random();
    private transient ApiClient apiClient = null;
    final transient PinganCloudConfig config = new PinganCloudConfig();
    // 拷贝下载文件的代码，原代码下载后文件无法删除，outs
    private final static byte[] API_DEFAULT_IV = new byte[]{1, 2, 3, 4, 5, 6, 7, 8};
    private final static String API_ALG = "DesEde/CBC/PKCS5Padding";
    private final static String API_ALGORITHM= "DesEde";
    private transient Method apiCheckAndGetDownLoadConfigMethod;
    private transient Method apiGetHttpClientMethod;

    public void init(Properties properties) {
        BeanUtil.copyProperties(properties, config, Constants.BeanCopyIgnoreNullValue);
        this.config.set_paySplitLimitAmts(StringUtils.isEmpty(this.config.getPaySplitLimitAmts()) ? new ArrayList<>() :
                Arrays.stream(this.config.getPaySplitLimitAmts().split(";")).map(vo -> new BigDecimal(vo.trim())).toList());
        this.apiClient = ApiClient.getInstance(properties);
        try {
            apiCheckAndGetDownLoadConfigMethod = FileService.class.getDeclaredMethod("checkAndGetDownLoadConfig", String.class);
            apiGetHttpClientMethod = FileService.class.getDeclaredMethod("getHttpClient", String.class);
            apiCheckAndGetDownLoadConfigMethod.setAccessible(true);
            apiGetHttpClientMethod.setAccessible(true);
        } catch (NoSuchMethodException e) {
            throw new ServiceException(e);
        }
    }

    public String getDefaultOutCode(Integer orgType){
        switch (orgType) {
            case 0 -> {
                return config.getRegionWhOrgCode();
            }
            case 1 -> {
                return config.getCityWhOrgCode();
            }
            default -> {
                return null; // config.getSupplierOrgCode();
            }
        }
    }

    public JSONObject post(String orderNo, String refundNo, PinganCloudClientApiEnum constant, JSONObject jsonObject) {
        // 通用参数
        jsonObject.put("TraderNo", config.getTraderNo());
        jsonObject.put("MrchCode", config.getMrchCode());
        jsonObject.put("TxnClientNo", config.getTxnClientNo());
        jsonObject.put("FundSummaryAcctNo", config.getFundSummaryAcctNo());
        jsonObject.put("TxnCode", constant.getTxnCode());
        if (jsonObject.getString("CnsmrSeqNo") == null) {
            // 交易流水号:系统流水号，建议规范：日期（14位）+随机编号（8位）
            jsonObject.put("CnsmrSeqNo", PinganCloudUtil.timeToStr(new Date()) + random.nextInt(99999999));
        }
        log.keyword(orderNo, refundNo, constant.getTxnCode()).info("request = " + jsonObject.toJSONString());
        SdkRequest sdkRequest = new SdkRequest();
        sdkRequest.setInterfaceName(constant.getServiceId());
        sdkRequest.setBody(jsonObject.clone());
        String code = "";
        String message = "";
        try {
            HttpResult httpResult = this.apiClient.invoke(sdkRequest);
            JSONObject resp = JSONObject.parseObject(httpResult.getData());
            if (resp.containsKey("Errors")) {
                // 业务失败，都返回 E50000
                code = resp.getString("Code");
                message = resp.getString("Message");
                JSONArray errors = resp.getJSONArray("Errors");
                if (errors.size() > 0) {
                    JSONObject error = (JSONObject) errors.get(0);
                    code = error.getString("ErrorCode");
                    message = error.getString("ErrorMessage");
                }
                log.keyword(orderNo, refundNo, constant.getTxnCode()).warn("response = " + resp.toJSONString());
                ServiceException se = new ServiceException(String.format("%s[%s]", message, code),
                        queryCodeLists.contains(code) ? R.SUCCESS:HttpStatus.WARN);
                JSONObject extendData = resp.getJSONObject("ExtendData");
                if (extendData != null) {
                    se.setDetailMessage(JsonUtils.toJsonString(extendData));
                }
                throw se;
            } else {
                log.keyword(orderNo, refundNo, constant.getTxnCode()).info("response = " + resp.toJSONString());
                return resp.getJSONObject("Data");
            }
        } catch (ServiceException se) {
            throw se;
        } catch (Exception e) {
            if (e.getCause() != null && e.getCause() instanceof HttpException he) {
                try {
                    /*
                        平安接口返回   "Code":"E20000","Message":"服务不可用",
                    */
                    JSONObject resp = JSONObject.parseObject(he.getResponse());
                    code = resp.getString("Code");
                    message = String.format("%s[%s]", resp.getString("Message"), code);
                    throw new ServiceException(String.format("%s[%s]", message, code), R.FAIL);
                } catch (JSONException je) {
                    log.keyword(orderNo, refundNo, constant.getTxnCode()).warn("response = HttpException." + he.getResponse());
                }
            }
            log.keyword(orderNo, refundNo, constant.getTxnCode()).error(message, e);
            throw new ServiceException("平安银行服务异常，请联系管理员", R.FAIL);
        }
    }
    // 由源码改写，原下载逻辑中FileOutputStream未关闭，使下载文件无法删除
    public byte[] fileDownLoad(String fileNo, String password) throws InvocationTargetException, IllegalAccessException {
        if (StringUtil.isEmpty(fileNo)) {
            log.error("文件名或者文件编号不能为空！");
            throw new OpenBankSdkException(ExceptionEnum.UPLOAD_FILE_FAIL, "下载文件失败，文件名或者文件编号不能为空");
        }
        OpenBankConfig openBankConfig = (OpenBankConfig) apiCheckAndGetDownLoadConfigMethod.invoke(null, config.getAppId());
        String appId = openBankConfig.getAppId();
        String container = openBankConfig.getContainer();
        String fileDownLoadUrl = openBankConfig.getFileDownLoadUrl();
        HttpClient clients = (HttpClient) apiGetHttpClientMethod.invoke(null, config.getAppId());
        HttpPost httpPost = new HttpPost(openBankConfig.getFileDownLoadUrl());
        httpPost.addHeader("appId", appId);
        httpPost.addHeader("container", container);
        httpPost.addHeader("fileNo", URLEncoder.encode(fileNo, StandardCharsets.UTF_8));

        DownloadSignVo body = new DownloadSignVo();
        body.setFileNo(fileNo);
        body.setAppId(appId);
        body.setContainer(container);
        // log.debug("签名：" + JSONObject.toJSONString(body));
        String sign = null; // SdkSignature.sign(appId, openBankConfig.getAppPrivateKey(), JSONObject.toJSONString(body));
        try {
            sign = JWT.signJWS(appId, openBankConfig.getAppPrivateKey(), JSONObject.toJSONString(body));
        } catch (IOException var4) {
            // log.error("JWT.signJWS fail,kid:[" + appId + "] ,privateKeyStr:[" + openBankConfig.getAppPrivateKey() + "] ,plainText:[" + plainText + "]", var4);
            throw new OpenBankSdkException(ExceptionEnum.SERCURITY_JWT_ERROR, var4);
        }
        httpPost.addHeader("sign", sign);

        try {
            // 1 下载
            HttpResponse httpResponse = clients.execute(httpPost);
            HttpEntity entity = httpResponse.getEntity();
            if (httpResponse.getStatusLine().getStatusCode() != 200) {
                throw new OpenBankSdkException(ExceptionEnum.DOWNLOAD_FILE_SERVER_FAIL, "文件下载接口异常,Error：" + EntityUtils.toString(entity, Strings.UTF_8));
            }
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            entity.writeTo(baos);
            baos.close();
            // 2 解密
            SecretKeySpec keySpec = new SecretKeySpec(Base64.decode(password.getBytes()), API_ALGORITHM);
            Cipher cipher = Cipher.getInstance(API_ALG);
            cipher.init(2, keySpec, new IvParameterSpec(API_DEFAULT_IV));
            InputStream ins = new CipherInputStream(new BufferedInputStream(new ByteArrayInputStream(baos.toByteArray())), cipher);
            return ZipFileUtils.unzip(ins);
        } catch (Exception var17) {
            throw new OpenBankSdkException(ExceptionEnum.DOWNLOAD_FILE_SERVER_FAIL, "调用文件下载接口,url：" + fileDownLoadUrl, var17);
        }


    }

    public String fileUpload(String fileName, byte[] fileBytes) throws InvocationTargetException, IllegalAccessException {
        OpenBankConfig openBankConfig = (OpenBankConfig) apiCheckAndGetDownLoadConfigMethod.invoke(null, config.getAppId());
        String fileUploadUrl = openBankConfig.getFileUploadUrl();
        String appId = openBankConfig.getAppId();
        String fileMd5 = Md5Util.getFileMd5(new ByteArrayInputStream(fileBytes));
        // String appPrivateKey = openBankConfig.getAppPrivateKey();
        HttpClient clients = (HttpClient) apiGetHttpClientMethod.invoke(null, config.getAppId());
        HttpPost filePost = new HttpPost(fileUploadUrl);
        filePost.addHeader("md5", fileMd5);
        filePost.addHeader("appId", appId);
        filePost.addHeader("filePathName", ""); // 要求文件名唯一，路径可为空
        filePost.addHeader("container", openBankConfig.getContainer());
        filePost.addHeader("x-pab-signMethod", openBankConfig.getSignMethod());
        filePost.addHeader("fileSize", fileBytes.length + "");
        filePost.addHeader("fileName", URLEncoder.encode(fileName, StandardCharsets.UTF_8));

        UploadSignVo body = new UploadSignVo();
        body.setFileName(URLEncoder.encode(fileName, StandardCharsets.UTF_8));
        body.setMd5(fileMd5);
        body.setAppId(appId);
        body.setContainer(openBankConfig.getContainer());
        body.setFileSize(fileBytes.length + "");
        SignatureService signatureService = SignatureEnum.getSignatureService(openBankConfig.getSignMethod());
        SignatureData signatureData = new SignatureData();
        signatureData.setOpenBankConfig(openBankConfig);
        signatureData.setData(JSONObject.toJSONString(body));
        String sign = signatureService.sign(signatureData);
        filePost.addHeader("sign", sign);
        String responseBodyAsString = null;
        JSONObject jsonObject = null;
        String fileNo = null;

        try {
            MultipartEntityBuilder multipartEntityBuilder = MultipartEntityBuilder.create();
            multipartEntityBuilder.setContentType(ContentType.MULTIPART_FORM_DATA);
            multipartEntityBuilder.addBinaryBody("file", fileBytes, ContentType.DEFAULT_BINARY, fileName);
            multipartEntityBuilder.setCharset(StandardCharsets.UTF_8);
            HttpEntity build = multipartEntityBuilder.build();
            filePost.setEntity(build);
            log.keyword("fileUpload", fileName).info("调用文件上传接口请求头：" + Arrays.toString(filePost.getAllHeaders()));
            HttpResponse httpResponse = clients.execute(filePost);
            responseBodyAsString = EntityUtils.toString(httpResponse.getEntity(), Strings.UTF_8);
            log.keyword("fileUpload", fileName).info("调用文件上传接口,接口响应参数为" + responseBodyAsString);
        } catch (IOException var24) {
            log.keyword("fileUpload", fileName).error("请求文件应用上传服务异常", var24);
            throw new OpenBankSdkException(ExceptionEnum.UPLOAD_FILE_FAIL, "请求文件应用上传服务异常,url：" + fileUploadUrl);
        }

        jsonObject = JSONObject.parseObject(responseBodyAsString);
        Integer code = (Integer)jsonObject.get("code");
        if (code != null && code != 200) {
            log.keyword("fileUpload", fileName).error((String)jsonObject.get("msg"));
            throw new OpenBankSdkException(String.valueOf(jsonObject.get("code")), (String)jsonObject.get("msg"));
        } else {
            fileNo = (String)((JSONObject)JSONObject.parseObject(responseBodyAsString).get("data")).get("fileNo");
            log.keyword("fileUpload", fileName).info("文件上传成功，文件唯一标识：" + fileNo);
            return fileNo;
        }
    }
}