package cn.xianlink.trade.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import cn.xianlink.common.api.enums.trade.*;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.api.util.BaseEntityAutoFill;
import cn.xianlink.trade.api.domain.bo.OrderPayRecvJobQueryBo;
import cn.xianlink.trade.domain.TradePayRecv;
import cn.xianlink.trade.domain.bo.TradePayRecvBo;
import cn.xianlink.trade.domain.bo.TradePayRecvQueryBo;
import cn.xianlink.trade.domain.vo.TradePayRecvPageVo;
import cn.xianlink.trade.domain.vo.TradePayRecvVo;
import cn.xianlink.trade.mapper.TradePayRecvMapper;
import cn.xianlink.trade.service.ITradeCustTransService;
import cn.xianlink.trade.service.ITradePayRecvService;
import cn.xianlink.trade.service.biz.TradeBaseUtilBizService;
import cn.xianlink.trade.utils.MapstructPageUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;


/**
 * 支付Service业务层处理
 *
 * <AUTHOR>
 * 2024-05-27
 */
@RequiredArgsConstructor
@CustomLog
@Service
public class TradePayRecvServiceImpl implements ITradePayRecvService {

    private final transient TradePayRecvMapper baseMapper;
    private final transient ITradeCustTransService tradeCustTransService;
    private final transient TradeBaseUtilBizService tradeBaseUtilBizService;

    @Override
    public TradePayRecvVo queryByNo(String recvNo) {
        return baseMapper.selectVoOne(Wrappers.lambdaQuery(TradePayRecv.class)
                .eq(TradePayRecv::getRecvNo, recvNo).eq(TradePayRecv::getDelFlag, 0));
    }

    @Override
    @BaseEntityAutoFill
    public TableDataInfo<TradePayRecvPageVo> queryPageList(TradePayRecvQueryBo bo, boolean isCustomer) {
        LambdaQueryWrapper<TradePayRecv> lqw = buildRefundQueryWrapper(bo, isCustomer);
        Page<TradePayRecvVo> result = baseMapper.selectVoPage(bo.build(), lqw);
        return TableDataInfo.build(MapstructPageUtils.convert(result, TradePayRecvPageVo.class));
    }

    @Override
    @BaseEntityAutoFill
    public List<TradePayRecvPageVo> queryList(TradePayRecvQueryBo bo, boolean isCustomer) {
        LambdaQueryWrapper<TradePayRecv> lqw = buildRefundQueryWrapper(bo, isCustomer);
        return MapstructUtils.convert(baseMapper.selectVoList(lqw), TradePayRecvPageVo.class);
    }

    private LambdaQueryWrapper<TradePayRecv> buildRefundQueryWrapper(TradePayRecvQueryBo bo, boolean isCustomer) {
        boolean isRecvDate = bo.getRecvDateStart() != null && bo.getRecvDateEnd() != null;
        boolean isAcctDate = bo.getAcctDateStart() != null && bo.getAcctDateEnd() != null;
        if (!isRecvDate && !isAcctDate) {
            throw new ServiceException("收款日期或记账日期必须输入一组");
        }
        LambdaQueryWrapper<TradePayRecv> lqw = Wrappers.lambdaQuery();
        if (isCustomer) {
            lqw.eq(TradePayRecv::getCustomerId, bo.getCustomerId());
            lqw.eq(TradePayRecv::getRecvType, PayRecvBusiTypeEnum.CUSTOM_RECHARGE.getCode());
        }
        lqw.between(isRecvDate, TradePayRecv::getRecvDate, bo.getRecvDateStart(), bo.getRecvDateEnd());
        lqw.between(isAcctDate, TradePayRecv::getAcctDate, bo.getAcctDateStart(), bo.getAcctDateEnd());
        lqw.eq(StringUtils.isNotBlank(bo.getRecvNo()), TradePayRecv::getRecvNo, bo.getRecvNo());
        lqw.eq(bo.getInfStatus() != null, TradePayRecv::getInfStatus, bo.getInfStatus());
        if (CollectionUtil.isNotEmpty(bo.getCustomerCodes())) {
            lqw.in(TradePayRecv::getCustomerId, tradeBaseUtilBizService.queryIdsByCodes(bo.getCustomerCodes(), BaseTypeEnum.CUSTOMER.getCode()));
        }
        lqw.eq(TradePayRecv::getDelFlag, 0);
        lqw.orderByDesc(TradePayRecv::getId);
        return lqw;
    }

    @Override
    public List<TradePayRecvVo> queryPayRecvProcessing(OrderPayRecvJobQueryBo bo) {
        LambdaQueryWrapper<TradePayRecv> lqw = Wrappers.lambdaQuery();
        lqw.between(TradePayRecv::getRecvDate, bo.getDateStart(), bo.getDateEnd());
        lqw.eq(TradePayRecv::getInfStatus, PayRecvInfStatusEnum.UNPAID.getCode());
        lqw.eq(TradePayRecv::getDelFlag, 0);
        lqw.orderByAsc(TradePayRecv::getId);
        return baseMapper.selectVoList(lqw);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TradePayRecvVo insertByBo(TradePayRecvBo bo) {
        TradePayRecv add = MapstructUtils.convert(bo, TradePayRecv.class);
        add.setRecvDate(LocalDate.now());
        add.setRecvType(PayRecvBusiTypeEnum.CUSTOM_RECHARGE.getCode());
        add.setRecvNo(tradeBaseUtilBizService.getNextTradeNo(add.getRecvDate()));
        add.setInfTime(Convert.toDate(DateUtil.now()));
        add.setInfStatus(PayRecvInfStatusEnum.UNPAID.getCode());
        baseMapper.insert(add);
        return MapstructUtils.convert(add, TradePayRecvVo.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateInfData(TradePayRecvBo tbo, TradePayRecvVo tvo) {
        // 存款模式的回调
        TradePayRecv update = MapstructUtils.convert(tbo, TradePayRecv.class);
        boolean isFail = PayRecvInfStatusEnum.CANCEL.getCode().equals(update.getInfStatus());
        update.setDelFlag(isFail ? tbo.getId() : 0);
        if (update.getOutSuccessTime() != null) {
            update.setAcctDate(LocalDateTimeUtil.of(update.getOutSuccessTime()).toLocalDate());
        }
        boolean isSuccess = baseMapper.update(update, Wrappers.lambdaUpdate(TradePayRecv.class)
                .eq(TradePayRecv::getId, update.getId()).eq(TradePayRecv::getDelFlag, 0)
                .eq(TradePayRecv::getInfStatus, PayRecvInfStatusEnum.UNPAID.getCode())) > 0;
        if (isSuccess && !isFail && PayRecvInfStatusEnum.SUCCESS.getCode().equals(update.getInfStatus())) {
            tradeCustTransService.insertTransByRecv(update.getOutSuccessTime(), tvo);
        }
        return isSuccess;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateInfFail(TradePayRecvVo tvo, ServiceException se) {
        String message = StrUtil.sub(se.getMessage(), 0, 128);
        TradePayRecv update = new TradePayRecv();
        update.setDelFlag(tvo.getId());
        update.setId(tvo.getId());
        update.setInfStatus(PayInfStatusEnum.INF_FAIL.getCode());
        update.setInfReason(message);
         baseMapper.updateById(update);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAcctStatus(LocalDate acctDate, List<TradePayRecvBo> bos) {
        if (bos.size() > 0) {
            baseMapper.updateAcctStatus(acctDate, bos);
        }
    }
}
