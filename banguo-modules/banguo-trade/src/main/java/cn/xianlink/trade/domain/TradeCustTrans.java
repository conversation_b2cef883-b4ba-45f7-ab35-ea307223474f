package cn.xianlink.trade.domain;

import cn.xianlink.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * 分账变更流水对象 trade_acc_trans
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("trade_cust_trans")
public class TradeCustTrans extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id主键    //差额退
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 业务类型 OC 存入; OP 订单;  OR 退单;
     */
    private String transType;
    /**
     * 业务id
     */
    private Long transId;
    /**
     * 业务单号
     */
    private String transNo;
    /**
     * 记账日期
     */
    private LocalDate transDate;
    /**
     * 客户用户id
     */
    private Long customerId;
    /**
     * 客户代码
     */
    private String customerCode;
    /**
     * 客户关联代码
     */
    private String customerOutCode;
    /**
     * 客户子账号
     */
    private String customerAcctCode;
    /**
     * 账务机构id
     */
    private Long acctOrgId;
    /**
     * 业务机构id
     */
    private Long transOrgId;

	/**
     * 业务类型
     */
    private String busiType;
    /**
     * 累加字段 单维度
     */
    private String busiField;

    /**
     * 业务类型 OP 订单;
     */
    private String relateType;
    /**
     * 关联单号
     */
    private String relateNo;
    /**
     * 关联单id
     */
    private Long relateId;
    /**
     * 收款金额
     */
    private BigDecimal relateAmt;
    /**
     * 调整金额
     */
    private BigDecimal transAmt;
    /**
     * 调整后余额
     */
    private BigDecimal availBalAmt;
    /**
     * 状态 0未结 1待结算 2可提现
     */
    private Integer status;
    /**
     * 待结算时间，，订单和退单是接口完成时间
     */
    private Date freezeTime;
    /**
     * 转成可提现时间
     */
    private Date availTime;
    /**
     * 记账日期
     */
    private LocalDate acctDate;
    /**
     * 删除标志（0代表存在 存入这条数据的主键代表删除）
     */
    @TableLogic(delval = "id")
    private Long delFlag;


}
