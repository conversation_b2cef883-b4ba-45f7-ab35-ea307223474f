package cn.xianlink.trade.domain;

import cn.xianlink.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * 机构子账户绑定对象 trade_org_relation
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("trade_org_bank_detail")
public class TradeOrgBankDetail extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id主键
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 0 总仓 1 城市仓 2 供应商 3 门店
     */
    private Integer outOrgType;
    /**
     * 分账机构子账户
     */
    private String outOrgCode;
    /**
     * 分账机构子账户
     */
    private String outAcctCode;
    /**
     * 银行卡类型  AccountBankFlagEnum 枚举 1 个人, 2 对公;  （企业是个体工商户才能选个人）
     */
    private String bankFlag;
    /**
     * 分银行卡类型 1 本行;2外行
     */
    private String bankType;
    /**
     * 银行卡绑定手机号
     */
    private String bankMobile;
    /**
     * 收款账户名
     */
    private String bankAccName;
    /**
     * 银行卡号 对应 MemberAcctNo
     */
    private String bankAccount;
    /**
     * 银行超级网银号 对应EiconBankBranchId
     */
    private String bankEicon;
    /**
     * 银行名 对应AcctOpenBranchName
     */
    private String bankBranchName;
    /**
     * 绑定时的单号，银行返回
     */
    private String bankBindNo;
    /**
     * 绑定单号
     */
    private String outBindNo;
    /**
     * 绑定时间
     */
    private Date outBindTime;
    /**
     * 解绑单号
     */
    private String outUnbindNo;
    /**
     * 解绑时间
     */
    private Date outUnbindTime;
    /**
     * 绑定状态 1 开户  2绑定 3 销户
     */
    private Integer status;
    /**
     * 删除标志（0代表存在 存入这条数据的主键代表删除）
     */
    @TableLogic(delval = "id")
    private Long delFlag;


}
