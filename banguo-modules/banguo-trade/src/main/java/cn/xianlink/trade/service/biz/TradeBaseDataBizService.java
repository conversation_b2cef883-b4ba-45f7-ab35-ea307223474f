package cn.xianlink.trade.service.biz;

import cn.xianlink.basic.api.RemoteBasCustomerService;
import cn.xianlink.basic.api.RemoteCityWhService;
import cn.xianlink.basic.api.RemoteRegionWhService;
import cn.xianlink.basic.api.RemoteSupplierService;
import cn.xianlink.basic.api.domain.vo.RemoteCityWhVo;
import cn.xianlink.basic.api.domain.vo.RemoteCustomerVo;
import cn.xianlink.basic.api.domain.vo.RemoteRegionWhVo;
import cn.xianlink.basic.api.domain.vo.RemoteSupplierVo;
import cn.xianlink.common.api.enums.trade.AccountOrgTypeEnum;
import cn.xianlink.common.api.enums.trade.AccountVirtualOrgEnum;
import cn.xianlink.common.api.enums.trade.BaseTypeEnum;
import cn.xianlink.common.api.vo.RemoteBaseDataVo;
import cn.xianlink.common.core.domain.vo.CodeNameVo;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.product.api.RemoteSupplierSkuService;
import cn.xianlink.product.api.domain.vo.RemoteSupplierSkuFundsInfoVo;
import cn.xianlink.system.api.RemoteDeptService;
import cn.xianlink.system.api.RemoteUserService;
import cn.xianlink.system.api.domain.bo.RemoteUserBo;
import cn.xianlink.trade.aop.BaseEntityAutoFillAspect;
import cn.xianlink.trade.domain.bo.org.TradeBaseDataSelectBo;
import lombok.CustomLog;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 业务机构转成账务机构
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@CustomLog
@Service
public class TradeBaseDataBizService {

    @DubboReference
    private transient RemoteRegionWhService regionWhService;
    @DubboReference
    private transient RemoteCityWhService cityWhService;
    @DubboReference
    private transient RemoteSupplierService supplierService;
    @DubboReference
    private transient RemoteDeptService deptService;
    @DubboReference
    private transient RemoteUserService userService;
    @DubboReference
    private transient RemoteBasCustomerService customerService;
    @DubboReference
    private transient RemoteSupplierSkuService supplierSkuService;


    public List<RemoteBaseDataVo> selectListByName(TradeBaseDataSelectBo selectBo) {
        List<RemoteBaseDataVo> list = new ArrayList<>();
        int pageSize = 20;
        List<Integer> orgTypes = selectBo.getOrgTypes() == null ? Arrays.asList(0, 1, 2) : selectBo.getOrgTypes();
        if (orgTypes.contains(AccountOrgTypeEnum.REGION_WH.getCode())) {
            List<RemoteRegionWhVo> vos = regionWhService.selectListByName(selectBo.getName());
            list.addAll(vos.stream().limit(pageSize).map(this::toRemoteBaseDataVo).toList());
        }
        if (list.size() < pageSize && orgTypes.contains(AccountOrgTypeEnum.CITY_WH.getCode())) {
            List<RemoteCityWhVo> vos = cityWhService.selectListByName(selectBo.getName(), null);
            list.addAll(vos.stream().limit(pageSize - list.size()).map(this::toRemoteBaseDataVo).toList());
        }
        if (list.size() < pageSize && orgTypes.contains(AccountOrgTypeEnum.SUPPLIER.getCode())) {
            List<RemoteSupplierVo> vos = supplierService.selectListByName(selectBo.getName());
            list.addAll(vos.stream().limit(pageSize - list.size()).map(this::toRemoteBaseDataVo).toList());
        }
        return list;
    }

    /**
     * 查询机构子账户绑定， 只有绑定后的数据才缓存
     */
    @Cacheable(cacheNames = BaseEntityAutoFillAspect.CACHE_BASE_DATA, key = "#id + '_id' + #type")
    public RemoteBaseDataVo queryById(Long id, Integer type) {
        if (id != null) {
            if (id > 0L) {
                try {
                    switch (Objects.requireNonNull(BaseTypeEnum.getEnumByCode(type))) {
                        case REGION_WH -> {
                            return toRemoteBaseDataVo(regionWhService.queryById(id));
                        }
                        case CITY_WH -> {
                            return toRemoteBaseDataVo(cityWhService.queryById(id));
                        }
                        case SUPPLIER -> {
                            return toRemoteBaseDataVo(supplierService.getSupplierById(id));
                        }
                        case DEPT -> {
                            return toRemoteBaseDataVo(id, deptService.getDeptCodeNameMap(Collections.singletonList(id)).get(id));
                        }
                        case CUSTOMER -> {
                            return toRemoteBaseDataVo(customerService.getById(id));
                        }
                        case SYS_USER -> {
                            return toRemoteBaseDataVo(userService.getUsersByUserIds(Collections.singletonList(id)).get(id));
                        }
                        case SUPPLIER_SKU -> {
                            List<RemoteSupplierSkuFundsInfoVo> vos = supplierSkuService.queryInfoListByFunds(Collections.singletonList(id));
                            if (vos.size() == 1) {
                                return toRemoteBaseDataVo(vos.get(0));
                            }
                        }
                    }
                } catch (Exception e) {
                    log.warn("", e);
                }
            } else {
                if (BaseTypeEnum.REGION_WH.getCode().equals(type)) {
                    AccountVirtualOrgEnum orgEnum = AccountVirtualOrgEnum.getEnumById(id);
                    if (orgEnum != null) {
                        RemoteBaseDataVo vo = new RemoteBaseDataVo();
                        vo.setId(orgEnum.getId());
                        vo.setType(BaseTypeEnum.REGION_WH.getCode());
                        vo.setCode(orgEnum.getCode());
                        vo.setName(orgEnum.getName());
                        return vo;
                    }
                }
            }
        }
        return null;
    }

    /**
     * 查询机构子账户绑定
     */
    @Cacheable(cacheNames = BaseEntityAutoFillAspect.CACHE_BASE_DATA, key = "#code + '_code' + #type")
    public RemoteBaseDataVo queryByCode(String code, Integer type) {
        if (StringUtils.isNotBlank(code)) {
            try {
                switch (Objects.requireNonNull(BaseTypeEnum.getEnumByCode(type))) {
                    case REGION_WH -> {
                        return toRemoteBaseDataVo(regionWhService.queryByCode(code));
                    }
                    case CITY_WH -> {
                        return toRemoteBaseDataVo(cityWhService.queryByCode(code));
                    }
                    case SUPPLIER -> {
                        List<RemoteSupplierVo> vos = supplierService.getSupplierByCode(Collections.singletonList(code));
                        if (vos.size() == 1) {
                            return toRemoteBaseDataVo(vos.get(0));
                        }
                    }
                    case DEPT -> {
                        // 暂不支持
                    }
                    case CUSTOMER -> {
                        return toRemoteBaseDataVo(customerService.getByCode(code));
                    }
                    case SYS_USER -> {
                        return toRemoteBaseDataVo(userService.getUserByUserCode(code));
                    }
                    case SUPPLIER_SKU -> {
                        // 暂不支持
                    }
                }
            } catch (Exception e) {
                log.warn("", e);
            }
        }
        return null;
    }

    public List<RemoteBaseDataVo> querySupplierSkuList(List<Long> skuIds) {
        List<RemoteSupplierSkuFundsInfoVo> vos = supplierSkuService.queryInfoListByFunds(skuIds);
        return vos.stream().map(this::toRemoteBaseDataVo).toList();
    }

    private RemoteBaseDataVo toRemoteBaseDataVo(RemoteRegionWhVo whVo) {
        if (whVo == null) {
            return null;
        }
        RemoteBaseDataVo vo = new RemoteBaseDataVo();
        vo.setId(whVo.getId());
        vo.setType(BaseTypeEnum.REGION_WH.getCode());
        vo.setCode(whVo.getRegionWhCode());
        vo.setName(whVo.getRegionWhName());
        return vo;
    }
    private RemoteBaseDataVo toRemoteBaseDataVo(RemoteCityWhVo cityWhVo) {
        if (cityWhVo == null) {
            return null;
        }
        RemoteBaseDataVo vo = new RemoteBaseDataVo();
        vo.setId(cityWhVo.getId());
        vo.setType(BaseTypeEnum.CITY_WH.getCode());
        vo.setCode(cityWhVo.getCode());
        vo.setName(cityWhVo.getName());
        return vo;
    }

    private RemoteBaseDataVo toRemoteBaseDataVo(RemoteSupplierVo supplierVo) {
        if (supplierVo == null) {
            return null;
        }
        RemoteBaseDataVo vo = new RemoteBaseDataVo();
        vo.setId(supplierVo.getId());
        vo.setType(BaseTypeEnum.SUPPLIER.getCode());
        vo.setCode(supplierVo.getCode());
        vo.setName(supplierVo.getName());
        vo.setAlias(supplierVo.getAlias());
        return vo;
    }

    private RemoteBaseDataVo toRemoteBaseDataVo(Long id, CodeNameVo deptNameVo) {
        if (deptNameVo == null) {
            return null;
        }
        RemoteBaseDataVo vo = new RemoteBaseDataVo();
        vo.setId(id);
        vo.setType(BaseTypeEnum.DEPT.getCode());
        vo.setCode(deptNameVo.getCode());
        vo.setName(deptNameVo.getName());
        return vo;
    }

    private RemoteBaseDataVo toRemoteBaseDataVo(RemoteCustomerVo customerVo) {
        if (customerVo == null) {
            return null;
        }
        RemoteBaseDataVo vo = new RemoteBaseDataVo();
        vo.setId(customerVo.getId());
        vo.setType(BaseTypeEnum.CUSTOMER.getCode());
        vo.setCode(customerVo.getCode());
        vo.setName(customerVo.getName());
        vo.setAlias(customerVo.getAlias());
        return vo;
    }

    private RemoteBaseDataVo toRemoteBaseDataVo(RemoteUserBo userBo) {
        if (userBo == null) {
            return null;
        }
        RemoteBaseDataVo vo = new RemoteBaseDataVo();
        vo.setId(userBo.getUserId());
        vo.setType(BaseTypeEnum.SYS_USER.getCode());
        vo.setCode(userBo.getUserCode());
        vo.setName(userBo.getRealName());
        vo.setAlias(userBo.getRealName());
        return vo;
    }

    private RemoteBaseDataVo toRemoteBaseDataVo(RemoteSupplierSkuFundsInfoVo skuVo) {
        if (skuVo == null) {
            return null;
        }
        RemoteBaseDataVo vo = new RemoteBaseDataVo();
        vo.setId(skuVo.getSkuId());
        vo.setType(BaseTypeEnum.SUPPLIER_SKU.getCode());
        vo.setCode(skuVo.getSkuCode());
        vo.setName(skuVo.getSkuName());
        vo.setProducer(skuVo.getProducer());
        vo.setSpuStandards(skuVo.getSpuStandards());
        return vo;
    }

}
