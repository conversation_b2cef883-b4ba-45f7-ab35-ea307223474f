package cn.xianlink.trade.mapper;

import cn.xianlink.common.mybatis.core.mapper.BaseMapperPlus;
import cn.xianlink.trade.domain.TradePayRecv;
import cn.xianlink.trade.domain.bo.TradePayRecvBo;
import cn.xianlink.trade.domain.vo.TradePayRecvVo;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * 支付Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
public interface TradePayRecvMapper extends BaseMapperPlus<TradePayRecv, TradePayRecvVo> {

    void updateAcctStatus(@Param("acctDate") LocalDate acctDate, @Param("bos") List<TradePayRecvBo> bos);
}
