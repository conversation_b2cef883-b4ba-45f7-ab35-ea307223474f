package cn.xianlink.trade.domain;

import cn.xianlink.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * 数据文件表 trade_acc_charge
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("trade_acc_charge")
public class TradeAccCharge extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * id主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 充值单号
     */
    private String chargeNo;

    /**
     * 充值日期
     */
    private LocalDate chargeDate;
    /**
     * 业务类型 CI 转入; CO 转出;
     */
    private String chargeType;
    /**
     * 0 总仓 1 城市仓 2 供应商
     */
    private Integer orgType;
    /**
     * 分账机构id
     */
    private Long orgId;
    /**
     * 分账机构代码
     */
    private String orgCode;
    /**
     * 分账机构ids
     */
    private String acctOrgIds;

    /**
     * 充值金额
     */
    private BigDecimal chargeAmt;
    /**
     * 退回金额
     */
    private BigDecimal refundAmt;
    /**
     * 退回次数
     */
    private Integer refundCount;
    /**
     *
     */
    private String outOrgCode;
    /**
     * 子账号
     */
    private String outAcctCode;

    /**
     * 关联充值单号
     */
    private String relateNo;
    /**
     * 分账单号
     */
    private String splitNo;
    /**
     * 分账关联单号
     */
    private String splitRelNo;
    /**
     * 支付发起时间
     */
    private Date infTime;
    /**
     * 接口状态 0 未支付;1 支付完成 ;2 支付取消; 10 接口未调用; 11 接口失败
     */
    private Integer infStatus;
    /**
     * 接口失败原因
     */
    private String infReason;
    /**
     * 银行流水号FrontSeqNo
     */
    private String outTradeNo;
    /**
     * 接口成功时间
     */
    private Date outSuccessTime;
    /**
     * 备注
     */
    private String remark;
    /**
     * 删除标志（0代表存在 存入这条数据的主键代表删除）
     */
    @TableLogic(delval = "id")
    private Long delFlag;
}
