package cn.xianlink.trade.domain.convert.pay;

import cn.xianlink.trade.domain.bo.TradePayBo;
import cn.xianlink.trade.domain.vo.TradePayVo;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

/**
 * 数据转换器
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TradePayConvert extends BaseMapper<TradePayBo, TradePayVo> {

}
