package cn.xianlink.trade.domain;

import cn.xianlink.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * 结算单对象 trade_acc_avail
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("trade_acc_avail")
public class TradeAccAvail extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单 id 主键
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 账务机构id
     */
    private Long acctOrgId;

    /**
     * 0 总仓 1 城市仓 2 供应商
     */
    private Integer orgType;

    /**
     * 分账机构id
     */
    private Long orgId;

    /**
     * 分账机构代码
     */
    private String orgCode;
    /**
     * 部门id
     */
    private Long deptId;
    /**
     * 记账日期
     */
    private LocalDate availDate;
    /**
     * 销售日期
     */
    private LocalDate transDate;

    /**
     * 结算单
     */
    private String availNo;

    /**
     * 结算合计金额=pay_amt+refund_amt+in_amt+out_amt
     */
    private BigDecimal availAmt;
    /**
     * 佣金金额
     */
    private BigDecimal commissionAmt;
    /**
     * 划转入金额
     */
    private BigDecimal inAmt;

    /**
     * 划转出金额
     */
    private BigDecimal outAmt;

    /**
     * 订单金额
     */
    private BigDecimal payAmt;

    /**
     * 退款总金额
     */
    private BigDecimal refundAmt;

    /**
     * 退货退款
     */
    private BigDecimal refundAmt1;

    /**
     * 差额退款
     */
    private BigDecimal refundAmt2;

    /**
     * 报损退款
     */
    private BigDecimal refundAmt3;

    /**
     * 未使用
     */
    private BigDecimal refundAmt4;
    /**
     * 关联单号（划转单号）
     */
    private String relateNo;
    /**
     * 优先级 负数划转单优先值为 1
     */
    private Integer priority;
    /**
     * 自动日单据 方式 0手动 1自动
     */
    private Integer isAuto;

    /**
     * 分账状态  2可提现 3
     */
    private Integer status;
    /**
     * 状态时间
     */
    private Date statusTime;
    /**
     * 结算时间（完成时间）
     */
    private Date finishTime;
    /**
     * 提现 id 主键
     */
    private Long cashId;
    /**
     * 提现单号
     */
    private String cashNo;
    /**
     * 提现申请时间
     */
    private Date cashCheckTime;
    /**
     * 提现时间
     */
    private Date cashTime;

    /**
     * 删除标志（0代表存在 存入这条数据的主键代表删除）
     */
    @TableLogic(delval = "id")
    private Long delFlag;


}
