package cn.xianlink.trade.mapper;


import cn.xianlink.common.mybatis.core.mapper.BaseMapperPlus;
import cn.xianlink.trade.domain.TradeCustAccount;
import cn.xianlink.trade.domain.vo.TradeCustAccountVo;
import cn.xianlink.trade.domain.vo.TradeCustTransAcctVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 账务总Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
public interface TradeCustAccountMapper extends BaseMapperPlus<TradeCustAccount, TradeCustAccountVo> {

    TradeCustAccountVo queryAccountSum(@Param("customerId") Long customerId);

    List<TradeCustAccountVo> queryExistAccount(@Param("ids") List<Long> ids);

    void insertNotExistAccount(@Param("vos") List<TradeCustTransAcctVo> vos);

    List<TradeCustAccountVo> queryAccountAmt(@Param("ids") List<Long> ids);

    int updateAccountAmt(@Param("vos") List<TradeCustAccountVo> accountVos);

    void insertTransAcctByCommAvail(@Param("updates") Map<String, String> updates, @Param("ids") List<Long> ids);

    void insertTransAcctByCommTrans(@Param("updates") Map<String, String> updates, @Param("ids") List<Long> ids);
}
