package cn.xianlink.trade.domain;

import cn.xianlink.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * 机构子账户绑定对象 trade_org_relation
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("trade_org_bank")
public class TradeOrgBank extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id主键
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 0 绑定（可同步out_org_name值） 1 手工
     */
    private Integer sourceType;
    /**
     * 来源机构
     */
    private Long sourceOrgId;
    /**
     * 来源机构
     */
    private String sourceOrgCode;
    /**
     * 0 总仓 1 城市仓 2 供应商 3 门店
     */
    private Integer outOrgType;
    /**
     * 分账机构名称（企业名称）
     */
    private String outOrgName;
    /**
     * 分账机构子账户
     */
    private String outOrgCode;
    /**
     * 分账机构子账户
     */
    private String outAcctCode;
    /**
     * 开户手机号
     */
    private String outAcctMobile;
    /**
     * 账户属性  SH 收款账户，00 付款账户
     */
    private String outOrgProperty;
    /**
     * 同名子账户
     */
    private String commOrgCode;
    /**
     * 同名子账户
     */
    private String commAcctCode;
    /**
     * 银行卡类型  AccountBankFlagEnum 枚举 1 个人, 2 对公;  （企业是个体工商户才能选个人）
     */
    private String bankFlag;
    /**
     * 分银行卡类型 1 本行;2外行
     */
    private String bankType;
    /**
     * 银行卡绑定手机号
     */
    private String bankMobile;
    /**
     * 收款账户名
     */
    private String bankAccName;
    /**
     * 银行卡号 对应 MemberAcctNo
     */
    private String bankAccount;
    /**
     * 银行超级网银号 对应EiconBankBranchId
     */
    private String bankEicon;
    /**
     * 银行名 对应AcctOpenBranchName
     */
    private String bankBranchName;
    /**
     * 绑定时的单号，银行返回
     */
    private String bankBindNo;
    /**
     * 企业类型 对应 IndivBusinessFlag, 1个体工商户；2企业
     */
    private String businessFlag;
    /**
     * 公司名称（个体必输） 对应 CompanyName
     */
    private String companyName;
    /**
     * 公司证件号（个体必输） 对应 CompanyGlobalId
     */
    private String companyGlobalId;
    /**
     * 法人 对应 ReprName
     */
    private String reprName;
    /**
     * 法人身份证 对应 ReprGlobalId
     */
    private String reprGlobalId;
    /**
     * 绑定单号
     */
    private String outBindNo;
    /**
     * 绑定时间
     */
    private Date outBindTime;
    /**
     * 解绑单号
     */
    private String outUnbindNo;
    /**
     * 解绑时间
     */
    private Date outUnbindTime;
    /**
     * 绑定状态 1 开户  2绑定 3 销户
     */
    private Integer status;
    /**
     * 6249-1 返回的 AuthAcceptSeqNo
     */
    private String authAcceptNo;
    /**
     * 6249-1 授权金额
     */
    private BigDecimal authTotalAmt;
    /**
     * 6249-1 授权金额
     */
    private BigDecimal authSingleAmt;
    /**
     * 6249-1 累加授权金额
     */
    private BigDecimal authAccumAmt;
    /**
     * 6249-1 授权有效时长
     */
    private Integer authValidDays;
    /**
     * 6249-1 授权截至日期
     */
    private LocalDate authValidDate;
    /**
     * 当日发生
     */
    private BigDecimal bankOccurAmt;
    /**
     * 当日待转
     */
    private BigDecimal bankWaitAmt;
    /**
     * 当日可提现
     */
    private BigDecimal bankCashAmt;
    /**
     * 上日可用余额（银行端金额，YE文件中获取）
     */
    private BigDecimal ydaBankAvailAmt;
    /**
     * 当前可用余额 (银行端金额，刷新获取)
     */
    private BigDecimal bankAvailAmt;
    /**
     * 银行端开户日期或最后修改日期
     */
    private LocalDate bankAmtDate;
    /**
     * 银行数据刷新时间
     */
    private Date bankAmtTime;
    /**
     * 删除标志（0代表存在 存入这条数据的主键代表删除）
     */
    @TableLogic(delval = "id")
    private Long delFlag;


}
