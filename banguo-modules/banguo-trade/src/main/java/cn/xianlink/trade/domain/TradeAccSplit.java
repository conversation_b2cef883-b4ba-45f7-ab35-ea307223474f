package cn.xianlink.trade.domain;

import cn.xianlink.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * 分账变更流水对象 trade_acc_trans
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("trade_acc_split")
public class TradeAccSplit extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id主键  使用id生成单号
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 支付渠道类型 pinganCloudWX
     */
    private String channel;
    /**
     * 分账单号
     */
    private String splitNo;
    /**
     * 业务类型 OP 订单;  OR 退单; TI 划转入; TO 划转出;
     */
    private String transType;
    /**
     * 业务id
     */
    private Long transId;
    /**
     * 业务单号
     */
    private String transNo;
    /**
     * 请求单号
     */
    private String tradeNo;
    /**
     * 记账日期
     */
    private LocalDate transDate;
    /**
     * 分账单号
     */
    private String splitRelNo;
    /**
     * 分账收款金额
     */
    private BigDecimal splitRelAmt;
    /**
     * 业务类型 OP 订单;  TI 划转入;
     */
    private String relateType;
    /**
     * 关联单号
     */
    private String relateNo;
    /**
     * 关联单分账金额
     */
    private BigDecimal relateAmt;
    /**
     * 对接外部代码
     */
    private String outOrgCode;

    /**
     * 分账子账户
     */
    private String outAcctCode;

    /**
     * 账务机构id
     */
    private Long acctOrgId;
    /**
     * 业务机构id
     */
    private Long transOrgId;
    /**
     * 0 总仓 1 城市仓 2 供应商
     */
    private Integer orgType;
    /**
     * 机构id
     */
    private Long orgId;
    /**
     * 机构代码
     */
    private String orgCode;
    /**
     * 调整总金额
     */
    private BigDecimal totalAmt;
    /**
     * 调整金额
     */
    private BigDecimal transAmt;
    /**
     * 佣金金额
     */
    private BigDecimal commissionAmt;
    /**
     * 费用金额
     */
    private BigDecimal feeAmt;
    /**
     * 业务类型
     */
    private String busiType;
    /**
     * 业务单发起时间
     */
    private Date initTime;
    /**
     * 接口状态 0 进行中 1 完成 2 失败
     */
    private Integer infStatus;
    /**
     * 接口时间
     */
    private Date infTime;
    /**
     * 接口失败原因
     */
    private String infReason;
    /**
     * 银行单号
     */
    private String outTradeNo;
    /**
     * 对接渠道支付完成时间 回执的
     */
    private Date outSuccessTime;
    /**
     * 对账状态 0 未验证 1 完成 2 失败
     */
    private Integer acctStatus;
    /**
     * 对账日期
     */
    private LocalDate acctDate;
    /**
     * 对账失败原因
     */
    private String acctReason;

    /**
     * 删除标志（0代表存在 存入这条数据的主键代表删除）
     */
    // @TableLogic(delval = "id") 手动控制删除
    private Long delFlag;


}
