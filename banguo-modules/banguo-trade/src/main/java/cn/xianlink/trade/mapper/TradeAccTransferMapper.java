package cn.xianlink.trade.mapper;

import cn.xianlink.common.mybatis.core.mapper.BaseMapperPlus;
import cn.xianlink.trade.domain.TradeAccTransfer;
import cn.xianlink.trade.domain.vo.TradeAccTransferVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 划转流水Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
public interface TradeAccTransferMapper extends BaseMapperPlus<TradeAccTransfer, TradeAccTransferVo> {

    TradeAccTransfer queryStatusByTrans(@Param("transNo") String transNo, @Param("transTypes") List<String> transTypes);

}
