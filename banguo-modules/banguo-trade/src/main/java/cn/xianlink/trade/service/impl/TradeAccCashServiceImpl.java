package cn.xianlink.trade.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.xianlink.common.api.enums.trade.*;
import cn.xianlink.common.core.enums.YNStatusEnum;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.api.util.BaseEntityAutoFill;
import cn.xianlink.trade.api.domain.bo.OrderPayJobQueryBo;
import cn.xianlink.trade.config.properties.ChannelsProperties;
import cn.xianlink.trade.constant.TradeCacheNames;
import cn.xianlink.trade.domain.TradeAccAvail;
import cn.xianlink.trade.domain.TradeAccCash;
import cn.xianlink.trade.domain.bo.TradeAccCashBo;
import cn.xianlink.trade.domain.bo.TradeAccCashQueryBo;
import cn.xianlink.trade.domain.bo.TradeAccSupCashQueryBo;
import cn.xianlink.trade.domain.bo.sup.SupTradeAccSupCashQueryBo;
import cn.xianlink.trade.domain.convert.acc.SupTradeAccSupCashConvert;
import cn.xianlink.trade.domain.vo.TradeAccAvailVo;
import cn.xianlink.trade.domain.vo.TradeAccCashVo;
import cn.xianlink.trade.domain.vo.TradeAccSupCashVo;
import cn.xianlink.trade.domain.vo.org.TradeOrgBankBindVo;
import cn.xianlink.trade.domain.vo.org.TradeOrgBankRelaVo;
import cn.xianlink.trade.domain.vo.sup.SupTradeAccSupCashVo;
import cn.xianlink.trade.mapper.TradeAccAvailMapper;
import cn.xianlink.trade.mapper.TradeAccCashMapper;
import cn.xianlink.trade.service.ITradeAccCashService;
import cn.xianlink.trade.service.ITradeOrgRelationService;
import cn.xianlink.trade.service.biz.TradeBaseUtilBizService;
import cn.xianlink.trade.utils.MapstructPageUtils;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;

/**
 * 提现单Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@RequiredArgsConstructor
@Service
public class TradeAccCashServiceImpl implements ITradeAccCashService {
    private final transient ChannelsProperties channelsProperties;
    private final transient TradeAccCashMapper baseMapper;
    private final transient TradeAccAvailMapper tradeAccAvailMapper;
    private final transient ITradeOrgRelationService tradeOrgRelationService;
    private final transient TradeBaseUtilBizService tradeBaseUtilBizService;
    private final static String AccIds = ";%s;";

    /*
        获取实时提现单金额
     */
    @Override
    public TradeAccCashVo queryById(Long cashId) {
        TradeAccCashVo cashVo = baseMapper.selectVoById(cashId);
        if (cashVo == null) {
            throw new ServiceException("提现单不存在");
        }
//        if (orgCode != null && orgType != null && (!orgCode.equals(cashVo.getOrgCode()) || !orgType.equals(cashVo.getOrgType()))) {
//            throw new ServiceException(String.format("提现单 %s %s类型错误", cashVo.getCashNo(),
//                    AccountOrgTypeEnum.getDescByCode(orgType)));
//        }
        if (cashVo.getOutActualAmt().compareTo(BigDecimal.ZERO) == 0 && cashVo.getCashAmt().compareTo(cashVo.getOutFeeAmt()) >= 0) {
            cashVo.setOutActualAmt(cashVo.getCashAmt().subtract(cashVo.getOutFeeAmt()));
        }
        return cashVo;
    }

    @Override
    public TradeAccCashVo queryByNo(String cashNo) {
        return baseMapper.selectVoOne(Wrappers.lambdaQuery(TradeAccCash.class).eq(TradeAccCash::getCashNo, cashNo).eq(TradeAccCash::getDelFlag, 0));
    }

    @Override
    public TradeAccCashVo queryCashInfo(String orgCode, Long orgId, Integer orgType) {
        TradeOrgBankBindVo bindVo = tradeOrgRelationService.queryBindByCode(orgCode, orgType);
        if (bindVo == null || !AccountOrgBindEnum.BIND.getCode().equals(bindVo.getStatus())) {
            throw new ServiceException(String.format("%s %s 未绑定银行", AccountOrgTypeEnum.getDescByCode(orgType), orgCode));
        }
        TradeAccCash accCash = new TradeAccCash();
        accCash.setBankAccName(bindVo.getBankAccName());
        accCash.setBankAccount(bindVo.getBankAccount());
        accCash.setBankBranchName(bindVo.getBankBranchName());
        accCash.setOutOrgCode(bindVo.getOutOrgCode());
        accCash.setOutAcctCode(bindVo.getOutAcctCode());
        accCash.setOutFeeAmt(channelsProperties.getWithdrawFeeAmt()); // 默认 0.5， 提现后使用银行返回再次反填
        accCash.setInfStatus(CashInfStatusEnum.INF_INIT.getCode());
        return MapstructUtils.convert(accCash, TradeAccCashVo.class);
    }

    /**
     * 查询并创建提现单 (当日仅一张，提现后也只能看到当前提现单)
     */
    // @Cacheable(cacheNames = TradeCacheNames.CACHE_CREATE_CASH, key = "#orgCode + '_' + #orgType" + '_' + #deptId")
    @Lock4j(name= TradeCacheNames.LOCK_CREATE_CASH, keys = "#orgCode + '_' + #orgType",
            expire = 30000, acquireTimeout = 1000)
    @Transactional(rollbackFor = Exception.class)
    @Override
    public TradeAccCashVo queryCashOrById(String orgCode, Long orgId, Integer orgType, Long deptId, Long cashId) {
        LambdaQueryWrapper<TradeAccCash> lqw = Wrappers.lambdaQuery();
        lqw.eq(TradeAccCash::getOrgType, orgType);
        lqw.eq(TradeAccCash::getOrgCode, orgCode);
        lqw.eq(TradeAccCash::getCashDate, LocalDate.now());
        lqw.eq(deptId != null && deptId != 0L, TradeAccCash::getDeptId, deptId);
        lqw.eq(TradeAccCash::getStatus, AccountStatusEnum.AVAIL.getCode());
        lqw.orderByAsc(TradeAccCash::getId);
        List<TradeAccCashVo> accCashVos = baseMapper.selectVoList(lqw);
        if (accCashVos.size() > 0) {
            List<TradeAccCashVo> cashVos = accCashVos.stream().filter(vo -> vo.getId().equals(cashId)).toList();
            TradeAccCashVo cashVo = cashVos.size() > 0 ? cashVos.get(0) : accCashVos.get(0);
            // 填上预估到账
            if (cashVo.getOutActualAmt().compareTo(BigDecimal.ZERO) == 0 && cashVo.getCashAmt().compareTo(cashVo.getOutFeeAmt()) >= 0) {
                cashVo.setOutActualAmt(cashVo.getCashAmt().subtract(cashVo.getOutFeeAmt()));
            }
            return cashVo;
        }
        TradeOrgBankRelaVo bankVo = tradeOrgRelationService.queryBankByCode(orgCode, orgType);
        if (bankVo == null || !AccountOrgBindEnum.BIND.getCode().equals(bankVo.getStatus())) {
            throw new ServiceException(String.format("%s %s 未绑定银行", AccountOrgTypeEnum.getDescByCode(orgType), orgCode));
        }
        TradeAccCashBo cashBo = new TradeAccCashBo();
        cashBo.setOrgId(orgId);
        cashBo.setOrgCode(orgCode);
        cashBo.setOrgType(orgType);
        cashBo.setDeptId(deptId == null ? 0L : deptId);
        cashBo.setOutFeeAmt(channelsProperties.getWithdrawFeeAmt()); // 默认 0.5， 提现后使用银行返回再次反填
        return insertByBo(cashBo, bankVo);
    }

    /**
     * 查询提现单列表
     */
    @Override
    @BaseEntityAutoFill
    public TableDataInfo<SupTradeAccSupCashVo> querySupPageList(String orgCode, SupTradeAccSupCashQueryBo queryBo) {
        LambdaQueryWrapper<TradeAccCash> lqw = Wrappers.lambdaQuery();
        lqw.ne(TradeAccCash::getAvailCount, 0);
        lqw.eq(TradeAccCash::getOrgType, AccountOrgTypeEnum.SUPPLIER.getCode());
        lqw.eq(TradeAccCash::getOrgCode, orgCode);
        lqw.eq(queryBo.getDeptId() != null && queryBo.getDeptId() != 0L, TradeAccCash::getDeptId, queryBo.getDeptId());
        lqw.in(CollectionUtil.isNotEmpty(queryBo.getDeptIds()), TradeAccCash::getDeptId, queryBo.getDeptIds());
        lqw.ge(queryBo.getCashDateStart() != null, TradeAccCash::getCashDate, queryBo.getCashDateStart());
        lqw.le(queryBo.getCashDateEnd() != null, TradeAccCash::getCashDate, queryBo.getCashDateEnd());
        lqw.like(StringUtils.isNotBlank(queryBo.getCashNo()), TradeAccCash::getCashNo, queryBo.getCashNo());
        lqw.eq(queryBo.getStatus() != null, TradeAccCash::getStatus, queryBo.getStatus());
        lqw.orderByDesc(TradeAccCash::getId);
        Page<TradeAccCashVo> result = baseMapper.selectVoPage(queryBo.build(), lqw);
        return TableDataInfo.build(result.getRecords().stream()
                .map(SupTradeAccSupCashConvert.INSTANCE::toConvert).toList(), result.getTotal());
    }
    /**
     * 查询提现单列表
     */
    @Override
    @BaseEntityAutoFill
    public TableDataInfo<TradeAccSupCashVo> querySupPageList(TradeAccSupCashQueryBo queryBo) {
        LambdaQueryWrapper<TradeAccCash> lqw = buildQueryWrapper(queryBo);
        Page<TradeAccCashVo> result = baseMapper.selectVoPage(queryBo.build(), lqw);
        return TableDataInfo.build(MapstructPageUtils.convert(result, TradeAccSupCashVo.class));
    }
    /**
     * 查询提现单列表
     */
    @Override
    @BaseEntityAutoFill
    public List<TradeAccSupCashVo> querySupList(TradeAccSupCashQueryBo queryBo) {
        LambdaQueryWrapper<TradeAccCash> lqw = buildQueryWrapper(queryBo);
        return MapstructUtils.convert(baseMapper.selectVoList(lqw), TradeAccSupCashVo.class);
    }

    private LambdaQueryWrapper<TradeAccCash> buildQueryWrapper(TradeAccSupCashQueryBo queryBo) {
        return buildQueryWrapper(MapstructUtils.convert(queryBo, TradeAccCashQueryBo.class));
    }

    /**
     * 查询提现单列表
     */
    @Override
    @BaseEntityAutoFill
    public TableDataInfo<TradeAccCashVo> queryPageList(TradeAccCashQueryBo queryBo) {
        LambdaQueryWrapper<TradeAccCash> lqw = buildQueryWrapper(queryBo);
        Page<TradeAccCashVo> result = baseMapper.selectVoPage(queryBo.build(), lqw);
        return TableDataInfo.build(result);
    }
    /**
     * 查询提现单列表
     */
    @Override
    @BaseEntityAutoFill
    public List<TradeAccCashVo> queryList(TradeAccCashQueryBo queryBo) {
        LambdaQueryWrapper<TradeAccCash> lqw = buildQueryWrapper(queryBo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 查询提现单进行中的提现单
     */
    @Override
    public List<TradeAccCashVo> queryCashProcessing(OrderPayJobQueryBo bo) {
        LambdaQueryWrapper<TradeAccCash> lqw = Wrappers.lambdaQuery(TradeAccCash.class)
                .select(TradeAccCash::getId, TradeAccCash::getCashNo, TradeAccCash::getCashDate,TradeAccCash::getInfStatus,
                        TradeAccCash::getStatus,TradeAccCash::getOrgType,TradeAccCash::getSplitNo);
        lqw.between(TradeAccCash::getInfTime, bo.getInfTimeStart(), bo.getInfTimeEnd());
        lqw.eq(TradeAccCash::getStatus, AccountStatusEnum.CHECK.getCode());
        lqw.eq(TradeAccCash::getInfStatus, CashInfStatusEnum.PROCESSING.getCode());
        // lqw.eq(TradeAccCash::getOrgType, AccountOrgTypeEnum.SUPPLIER.getCode());
        lqw.eq(TradeAccCash::getDelFlag, YNStatusEnum.DISABLE.getCode());
        lqw.orderByAsc(TradeAccCash::getId);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<TradeAccCash> buildQueryWrapper(TradeAccCashQueryBo queryBo) {
        LambdaQueryWrapper<TradeAccCash> lqw = Wrappers.lambdaQuery();
        // 管理台查询不到未选择结算单的提现单
        lqw.ne(TradeAccCash::getAvailCount, 0);
        lqw.ne(TradeAccCash::getInfStatus, CashInfStatusEnum.INF_INIT.getCode());
        lqw.eq(queryBo.getOrgType() != null, TradeAccCash::getOrgType, queryBo.getOrgType()); // 可能是城市仓 或 供应商
        lqw.between(TradeAccCash::getCashDate, queryBo.getCashDateStart(), queryBo.getCashDateEnd());
        lqw.eq(StringUtils.isNotBlank(queryBo.getOutAcctCode()), TradeAccCash::getOutAcctCode, queryBo.getOutAcctCode());
        lqw.eq(queryBo.getStatus() != null, TradeAccCash::getStatus, queryBo.getStatus());
        lqw.eq(queryBo.getInfStatus() != null, TradeAccCash::getInfStatus, queryBo.getInfStatus());
        lqw.eq(StringUtils.isNotBlank(queryBo.getCashNo()), TradeAccCash::getCashNo, queryBo.getCashNo());
        lqw.eq(StringUtils.isNotBlank(queryBo.getBankAccount()), TradeAccCash::getBankAccount, queryBo.getBankAccount());
        lqw.eq(StringUtils.isNotBlank(queryBo.getOutTradeNo()), TradeAccCash::getOutTradeNo, queryBo.getOutTradeNo());
        lqw.ge(queryBo.getCashTimeStart() != null, TradeAccCash::getCashTime, queryBo.getCashTimeStart());
        lqw.le(queryBo.getCashTimeEnd() != null, TradeAccCash::getCashTime, queryBo.getCashTimeEnd() == null ? null : queryBo.getCashTimeEnd().plusDays(1));
        lqw.in(CollectionUtil.isNotEmpty(queryBo.getDeptIds()), TradeAccCash::getDeptId, queryBo.getDeptIds());
        if (queryBo.getIsFail() != null) {
            if (queryBo.getIsFail() == 0) {
                lqw.eq(TradeAccCash::getInfReason, "");
            } else {
                lqw.ne(TradeAccCash::getInfReason, "");
            }
        }
        buildQueryWrapper(lqw, queryBo.getAcctOrgCodes(), queryBo.getOrgCodes(), queryBo.getAvailNo());
        lqw.orderByDesc(TradeAccCash::getId);
        return lqw;
    }

    private void buildQueryWrapper(LambdaQueryWrapper<TradeAccCash> lqw, List<String> acctOrgCodes, List<String> orgCodes, String availNo) {
        lqw.in(CollectionUtil.isNotEmpty(orgCodes), TradeAccCash::getOrgCode, orgCodes);
        if (CollectionUtil.isNotEmpty(acctOrgCodes)) {
            Set<Long> acctOrgIds = tradeBaseUtilBizService.queryIdsByCodes(acctOrgCodes, BaseTypeEnum.REGION_WH.getCode());
            lqw.and(CollectionUtil.isNotEmpty(acctOrgIds), l -> {
                acctOrgIds.forEach(id -> {
                    l.like(TradeAccCash::getAcctOrgIds, String.format(AccIds, id)).or();
                });
            });
        }
        if (StringUtils.isNotBlank(availNo)) {
            TradeAccAvailVo accAvailVo = tradeAccAvailMapper.selectVoOne(Wrappers.lambdaQuery(TradeAccAvail.class)
                    .eq(TradeAccAvail::getAvailNo, availNo));
            lqw.eq(TradeAccCash::getId, accAvailVo == null || accAvailVo.getCashId() == null ? -1 : accAvailVo.getCashId());
        }
    }

    // 总部提现
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TradeAccCashVo insertByBo(TradeAccCashBo bo, TradeOrgBankRelaVo bankVo) {
        TradeAccCash accCash = MapstructUtils.convert(bo, TradeAccCash.class);
        /*
            1 初始化的提现单  status=2 且 infStatus=10 才可以读入结算单
            2 总部的提现单单  status=4 且 infStatus=10  可查询更新后续提现状态
         */
        if (AccountOrgTypeEnum.SUPPLIER.getCode().equals(accCash.getOrgType())) {
            accCash.setStatus(AccountStatusEnum.AVAIL.getCode());
        } else {
            // 无明细，直接设置成待审核
            accCash.setStatus(AccountStatusEnum.CHECK.getCode());
            accCash.setCashCheckTime(Convert.toDate(DateUtil.now()));
            accCash.setAcctOrgIds(String.format(AccIds, accCash.getOrgId()));
            accCash.setDeptIds("");
        }
        accCash.setCashDate(LocalDate.now());
        accCash.setInfStatus(CashInfStatusEnum.INF_INIT.getCode());
        accCash.setInfRetries(0);
        accCash.setBankAccName(bankVo.getBankAccName());
        accCash.setBankAccount(bankVo.getBankAccount());
        accCash.setBankBranchName(bankVo.getBankBranchName());
        accCash.setOutOrgCode(bankVo.getOutOrgCode());
        accCash.setOutAcctCode(bankVo.getOutAcctCode());
        try {
            insertCash(accCash);
        } catch (DuplicateKeyException dke) {
            insertCash(accCash); // 重试一次
        }
        return MapstructUtils.convert(accCash, TradeAccCashVo.class);
    }

    private void insertCash(TradeAccCash accCash) {
        accCash.setCashNo(tradeBaseUtilBizService.getNextCashNo(accCash.getCashDate()));
        baseMapper.insert(accCash);
    }

    // 用于汇总（其他事务中调用）
    public void updateCashAmt(Long cashId) {
        TradeAccAvailVo availVo = tradeAccAvailMapper.queryCashAmt(cashId); // 查询的有可能为空
        TradeAccCash cash = new TradeAccCash();
        cash.setId(cashId);
        cash.setCashAmt(availVo.getAvailAmt());
        cash.setCommissionAmt(availVo.getCommissionAmt());
        cash.setAvailCount(Math.toIntExact(availVo.getId()));
        cash.setAcctOrgIds(StringUtils.isBlank(availVo.getAvailNo()) ? "" : availVo.getAvailNo());
        cash.setDeptIds(StringUtils.isBlank(availVo.getCashNo()) ? "" : availVo.getCashNo());
//        cash.setDeptIds(StringUtils.isBlank(availVo.getCashNo()) ? "" : String.format(";%s;", String.join(";",
//                Arrays.stream(availVo.getCashNo().replace(";;", ";").split(";")).filter(StringUtils::isNotBlank).collect(Collectors.toSet()))));
        baseMapper.updateById(cash);
    }

}
