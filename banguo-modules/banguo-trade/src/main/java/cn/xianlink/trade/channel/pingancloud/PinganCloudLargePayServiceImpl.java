package cn.xianlink.trade.channel.pingancloud;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.xianlink.common.api.enums.trade.*;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.trade.api.domain.bo.OrderRefundBo;
import cn.xianlink.trade.channel.pingancloud.constant.PinganCloudTradeStatusEnum;
import cn.xianlink.trade.channel.pingancloud.constant.PinganCloudClientApiEnum;
import cn.xianlink.trade.domain.bo.TradePayBo;
import cn.xianlink.trade.domain.bo.TradePayRecvBo;
import cn.xianlink.trade.domain.bo.TradePayRefundBo;
import cn.xianlink.trade.domain.vo.TradePayRecvVo;
import cn.xianlink.trade.domain.vo.TradePayRefundVo;
import cn.xianlink.trade.domain.vo.TradePayVo;
import com.alibaba.fastjson.JSONObject;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.CustomLog;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;


/**
 *
 * 0 6248  开 00 户 ， 不用绑卡
 * 1 支付 6276（异步）， 转成同步查询到  OrderRecvCode
 *    1 测试环境模拟支付  6211
 *    2 编号查询 6279
 *    3 编号关闭 6277（异步）， 转成同步查询到  CancelStatus 状态
 *    4 分账 6034 单条
 * 2 退款 6278 ， 查询 6110
 *    1 分账 6164 单条
 *
 * <AUTHOR>
 * @date 2025-02-25
 */
@CustomLog
@Component
public class PinganCloudLargePayServiceImpl extends PinganCloudBalPayServiceImpl {

    @Resource
    private transient PinganCloudClientService client;
    @Resource
    private transient PinganCloudPayRecvService recvService;

    private TradePayRecvVo getPayRecvVo(TradePayVo vo) {
        TradePayRecvVo recvVo = new TradePayRecvVo();
        recvVo.setOrderNo(vo.getOrderNo());
        recvVo.setCustomerCode(vo.getCustomerCode());
        recvVo.setCustomerAcctCode(vo.getCustomerAcctCode());
        recvVo.setCustomerOutCode(vo.getCustomerOutCode());
        recvVo.setRecvNo(vo.getTradeNo());
        return recvVo;
    }

    @Override
    public TradePayBo pay(TradePayVo vo) {
        if (vo.getPaySplitAmt().compareTo(BigDecimal.ZERO) == 0) {
            TradePayBo respBo = new TradePayBo();
            respBo.setPayInfStatus(PayInfStatusEnum.SUCCESS.getCode());
            respBo.setOutSuccessTime(Convert.toDate(DateUtil.now()));
            respBo.setCallbackWeixin(new JSONObject()
                    .fluentPut("channel", vo.getChannel())
                    .fluentPut("params", new JSONObject()).toJSONString());
            return respBo.setId(vo.getId());
        } else {
            TradePayRecvVo recvVo = getPayRecvVo(vo);
            recvVo.setRecvAmt(vo.getPaySplitAmt());
            TradePayRecvBo recvBo = recvService.payRecv(recvVo);
            recvBo.getTradePayBo().setId(vo.getId());
            return recvBo.getTradePayBo();
        }
    }

    @Override
    public TradePayBo payQuery(TradePayVo vo) {
        TradePayRecvVo recvVo = getPayRecvVo(vo);
        recvVo.setOutRecvCode(vo.getOutTradeNo());
        recvVo.setInfTime(vo.getPayInfTime());
        TradePayRecvBo recvBo = recvService.payRecvQuery(recvVo);
        if (recvBo != null) {
            recvBo.getTradePayBo().setId(vo.getId());
            return recvBo.getTradePayBo();
        }
        return null;
    }

    @Override
    public TradePayBo payClose(TradePayVo vo) {
        TradePayRecvVo recvVo = getPayRecvVo(vo);
        recvVo.setOutRecvCode(vo.getOutTradeNo());
        TradePayRecvBo recvBo = recvService.payRecvClose(recvVo);
        recvBo.getTradePayBo().setId(vo.getId());
        return recvBo.getTradePayBo();
    }

    @Override
    public JSONObject callback(String reqBody, HttpServletRequest request) {
        return null;
    }

    @Override
    public TradePayBo payCallback(JSONObject resp) {
        return null;
    }

    @Override
    public int refundValidate(OrderRefundBo bo, TradePayVo payVo) {
        if(CollectionUtil.isEmpty(bo.getSplits())){
            throw new ServiceException("分账数据不能为空");
        }
        // 退款时需要的订单中数据同步
        JSONObject params = JSONObject.parseObject(payVo.getCallbackWeixin()).getJSONObject("params")
                .fluentRemove("bankAccount").fluentRemove("bankAccName").fluentPut("channelNo", payVo.getOutChannelNo());
        bo.setRefundInfo(params.toJSONString());
        return 0;
    }

    @Override
    public boolean refundValidate(TradePayRefundVo vo) {
        // 第二日 9 时才能退, T+1
        Date refundInfTime = DateUtil.offsetDay(vo.getInfTime(), 1)
                .setField(DateField.HOUR_OF_DAY, 9).setField(DateField.MINUTE, 0).setField(DateField.SECOND, 0);
        if (refundInfTime.after(new Date())) {
            log.keyword(vo.getRefundNo(), "refundValidate").info("未到退款接口调用时间，稍后执行");
            return false;
        }
        return true;
    }

    @Override
    public TradePayRefundBo refund(TradePayRefundVo vo) {
        if (vo.getRefundSplitAmt().compareTo(BigDecimal.ZERO) == 0) {
            TradePayRefundBo respBo = new TradePayRefundBo();
            respBo.setRefundInfStatus(RefundInfStatusEnum.SUCCESS.getCode());
            respBo.setOutSuccessTime(Convert.toDate(DateUtil.now()));
            return respBo.setId(vo.getId());
        } else {
            JSONObject params = JSONObject.parseObject(vo.getRefundInfo());
            String bankName = params.getString("inBankName");
            JSONObject body = new JSONObject();
            body.put("MemberSubAcctNo", vo.getCustomerAcctCode());
            body.put("MemberCode", vo.getCustomerOutCode());
            body.put("OldThirdSeqNo", params.getString("channelNo"));
            body.put("BankBranchId", params.getString("inBankEicon"));
            body.put("RecvAcctNo", params.getString("inAccount"));
            body.put("RecvAcctName", params.getString("inAccName"));
            body.put("RecvAcctBankName", bankName);
            body.put("BankType", (bankName != null && bankName.contains("平安银行")) ? "1" : "2");
            body.put("ReturnAmt", vo.getRefundSplitAmt().toString());
            body.put("CnsmrSeqNo", vo.getTradeRefundNo()); // 不单独再生成一个单号了   vo.getSplitNo()
            JSONObject resp = client.post(vo.getRefundNo(), "refund", PinganCloudClientApiEnum.LARGE_PAY_REFUND, body);
            TradePayRefundBo respBo = new TradePayRefundBo();
            respBo.setId(vo.getId());
            respBo.setRefundInfStatus(RefundInfStatusEnum.PROCESSING.getCode());
            respBo.setInfReason("");
            respBo.setOutTradeNo(resp.getString("WitnessSysSeqNo"));
            return respBo;
        }
    }

    @Override
    public TradePayRefundBo refundQuery(TradePayRefundVo vo) {
        JSONObject body = new JSONObject();
        body.put("FunctionFlag", "3");
        body.put("TranNetSeqNo", vo.getTradeRefundNo());
        try {
            // 查不到
            JSONObject resp = client.post(vo.getRefundNo(), "refundQuery", PinganCloudClientApiEnum.ACC_TRADE_QUERY, body);
            TradePayRefundBo respBo = new TradePayRefundBo();
            respBo.setId(vo.getId());
            respBo.setRefundInfStatus(PinganCloudTradeStatusEnum.getInfStatus(resp.getString("TranStatus")));
            // respBo.setOutChannelNo(resp.getString("OldTranFrontSeqNo"));  等价于 上面方法中的 WitnessSysSeqNo 值
            if (RefundInfStatusEnum.SUCCESS.getCode().equals(respBo.getRefundInfStatus())) {
                respBo.setOutCouponAmt(BigDecimal.ZERO);
                respBo.setOutDiscountAmt(BigDecimal.ZERO);
                respBo.setOutCashAmt(PinganCloudUtil.yuanToYuan(resp.getString("TranAmt")));
                // 下面的时间是提交时间， 不是最终转账成功的时间 ！！！！！！
                respBo.setOutSuccessTime(PinganCloudUtil.timeToDate(resp.getString("TranDate") + resp.getString("TranTime")));
                respBo.setInfReason("");
            } else {
                respBo.setInfReason(PinganCloudUtil.toString(resp.getString("FailMsg")));
            }
            return respBo;
        } catch (ServiceException se) {
            if (se.getMessage().contains("[ERR020]")) {
                // 无符合条件记录[ERR020] , 返回null
                log.keyword(vo.getRefundNo(), "refundQuery").warn("[ERR020] 转异常", se);
                return null;
            }
            throw se;
        }
    }

}
