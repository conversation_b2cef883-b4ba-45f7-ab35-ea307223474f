package cn.xianlink.trade.domain;

import cn.xianlink.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 账务总对象 trade_acc_account
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("trade_acc_trans_acct")
public class TradeAccTransAcct extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id主键
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 销售日期
     */
    private LocalDate transDate;
    /**
     * 账务机构id
     */
    private Long acctOrgId;
    /**
     * 0 总仓 1 城市仓 2 供应商
     */
    private Integer orgType;

    /**
     * 分账机构id
     */
    private Long orgId;

    /**
     * 分账机构代码
     */
    private String orgCode;
    /**
     * 部门id
     */
    private Long deptId;
    /**
     *
     */
    private String outOrgCode;
    /**
     * 子账号
     */
    private String outAcctCode;
    /**
     * 累加字段
     */
    private String busiField;
    /**
     * 业务类型 OP 订单;  OR 退单; TI 划转入; TO 划转出;
     */
    private String transType;
    /**
     * 业务id
     */
    private Long transId;
    /**
     * 业务单号
     */
    private String transNo;
    /**
     * 待结算金额
     */
    private BigDecimal freezeAmt;

    /**
     * 可提现划金额
     */
    private BigDecimal availAmt;

    /**
     * 已提金额
     */
    private BigDecimal cashAmt;
    /**
     * 提现待审核金额
     */
    private BigDecimal checkAmt;
    /**
     * 佣金待结算余额
     */
    private BigDecimal freezeCommAmt;
    /**
     * 佣金余额
     */
    private BigDecimal availCommAmt;
    /**
     * 佣金提现金额
     */
    private BigDecimal cashCommAmt;
    /**
     * 佣金提现待审核金额
     */
    private BigDecimal checkCommAmt;
    /**
     * 删除标志（0代表存在 存入这条数据的主键代表删除）
     */
    private Long delFlag;


}
