package cn.xianlink.trade.domain.convert.org;

import cn.xianlink.trade.api.domain.vo.RemoteOrgRelationStatusVo;
import cn.xianlink.trade.domain.vo.org.TradeOrgBankRelaVo;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

/**
 * 数据转换器
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RemoteOrgBankRelaStatusConvert extends BaseMapper<TradeOrgBankRelaVo, RemoteOrgRelationStatusVo> {
}
