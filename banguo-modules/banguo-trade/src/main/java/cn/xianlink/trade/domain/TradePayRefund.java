package cn.xianlink.trade.domain;

import cn.xianlink.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * 支付退款对象 trade_pay_refund
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("trade_pay_refund")
public class TradePayRefund extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 支付渠道类型 pinganCloudWXProgramQL
     */
    private String channel;
    /**
     * 支付appId
     */
    private String appId;

    /**
     * trade_pay.id
     */
    private Long payId;

    /**
     * 订单id主键
     */
    private Long orderId;

    /**
     * 订单唯一单号
     */
    private String orderNo;

    /**
     * 订单日期
     */
    private LocalDate orderDate;
    /**
     * 账务机构id
     */
    private Long acctOrgId;
    /**
     * 业务机构id
     */
    private Long orderOrgId;
    /**
     * 支付人requestIp
     */
    private String payerIp;
    /**
     * 订单用户id
     */
    private Long customerId;
    /**
     * 客户代码
     */
    private String customerCode;
    /**
     * 客户关联代码
     */
    private String customerOutCode;
    /**
     * 客户子账号
     */
    private String customerAcctCode;
    /**
     * 支付金额
     */
    private BigDecimal payAmt;
    /**
     * 实际支付金额
     */
    private BigDecimal paySplitAmt;
    /**
     * 支付发起时间
     */
    private Date orderInfTime;
    /**
     * 订单生成单号  对应银行 TradeOrderNo
     */
    private String tradeNo;
    /**
     * 退单生成单号  对应银行 ReturnOrderNo
     */
    private String tradeRefundNo;

    /**
     * 订单内退款顺序号，从10开始
     */
    private Integer refundSeq;

    /**
     * 退款id主键
     */
    private Long refundId;
    /**
     * 退款单号
     */
    private String refundNo;
    /**
     * 退款关联单号
     */
    private String refundRelNo;
    /**
     * 退款日期
     */
    private LocalDate refundDate;
    /**
     * 退款金额
     */
    private BigDecimal refundAmt;
    /**
     * 实际退款金额
     */
    private BigDecimal refundSplitAmt;
    /**
     * 退款类型 RA取消;RB差价;RL少货;RD串品;RE缺货;RF服务费;RH未采;RC报损
     */
    private String busiType;
    /**
     * 累加支付单的退款金额字段 单维度
     */
    private String busiField;
    /**
     * 是否占用 0 正常  1 占用
     */
    private Integer isOccupy;
    /**
     * 重试次数
     */
    private Integer refundInfRetries;
    /**
     * 退款状态 0 处理中 1 退款完成 2 退款失败; 10 接口未调用; 11 接口失败
     */
    private Integer refundInfStatus;
    /**
     * 退款发起时间
     */
    private Date refundInfTime;
    /**
     * 是否自动调用接口 0 否  1 自动
     */
    private Integer infAuto;
    /**
     * 退款发起时间
     */
    private Date infTime;
    /**
     * 重试次数
     */
    private Integer infRetries;
    /**
     * 支付总状态（含分账） 0 处理中 1 退款完成 2 退款失败; 10 接口未调用; 11 接口失败
     */
    private Integer infStatus;

    /**
     * 接口调用失败原因
     */
    private String infReason;

    /**
     * 平安银行： 银行流水号BankOrderNo 回执的
     * 微信b2b：  refund_id
     */
    private String outTradeNo;

    /**
     * 平安银行：渠道流水号ChannelOrderNo
     * 微信b2b: 无
     */
    private String outChannelNo;
    /**
     * 服务费金额
     */
    private BigDecimal outFeeAmt;
    /**
     * 代金券金额
     */
    private BigDecimal outCouponAmt;

    /**
     * 折扣金额
     */
    private BigDecimal outDiscountAmt;

    /**
     * 实际到账金额
     */
    private BigDecimal outCashAmt;

    /**
     * 对接渠道退款完成时间
     */
    private Date outSuccessTime;
    /**
     * 分账状态 0未结 1结算;
     */
    private Integer status;
    /**
     * 对账状态 0 未对账 1 完成 2 失败
     */
    private Integer acctStatus;
    /**
     * 对账日期
     */
    private LocalDate acctDate;
    /**
     * 辅助退款信息
     */
    private String refundInfo;
    /**
     * 删除标志; 4 失败; 101 接口失败; 这2个状态删除
     */
    // @TableLogic(delval = "id") 不加注解，手动处理删除标识
    private Long delFlag;


}
