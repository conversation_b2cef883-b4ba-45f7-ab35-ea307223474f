package cn.xianlink.trade.mapper;


import cn.xianlink.common.mybatis.core.mapper.BaseMapperPlus;
import cn.xianlink.trade.domain.TradeCommTrans;
import cn.xianlink.trade.domain.vo.comm.TradeCommTransVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;



/**
 * 机构子账户绑定Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
public interface TradeCommTransMapper extends BaseMapperPlus<TradeCommTrans, TradeCommTransVo> {

    List<TradeCommTransVo> queryCommCashTransById(@Param("cashId") Long cashId);

    void insertCommTransByTrans(@Param("ids") List<Long> ids);

}
