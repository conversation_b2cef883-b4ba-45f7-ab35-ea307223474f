package cn.xianlink.trade.domain;

import cn.xianlink.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 账务总对象 trade_acc_account
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("trade_cust_trans_acct")
public class TradeCustTransAcct extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id主键
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 客户用户id
     */
    private Long customerId;
    /**
     * 销售日期
     */
    private LocalDate transDate;
    /**
     * 业务类型 OP 订单;  OR 退单; TI 划转入; TO 划转出;
     */
    private String transType;
    /**
     * 业务id
     */
    private Long transId;
    /**
     * 业务单号
     */
    private String transNo;
    /**
     * 待结算金额
     */
    private BigDecimal freezeAmt;

    /**
     * 可提现划金额
     */
    private BigDecimal availAmt;
    /**
     * 业务id
     */
    private Long custTransId;
    /**
     * 佣金结算日期
     */
    private LocalDate commAvailDate;
    /**
     * 来源类型 0 子账户流水  1 佣金流水
     */
    private Integer sourceType;
    /**
     * 佣金待结算余额
     */
    private BigDecimal commFreezeAmt;

    /**
     * 佣金余额
     */
    private BigDecimal commAvailAmt;
    /**
     * 佣金提现金额
     */
    private BigDecimal commCashAmt;
    /**
     * 佣金提现待审核金额
     */
    private BigDecimal commCheckAmt;
    /**
     * 佣金过期金额
     */
    private BigDecimal commExpireAmt;
    /**
     * 发薪待结算余额
     */
    private BigDecimal salaFreezeAmt;
    /**
     * 发薪余额
     */
    private BigDecimal salaAvailAmt;
    /**
     * 发薪提现金额
     */
    private BigDecimal salaCashAmt;
    /**
     * 发薪提现待审核金额
     */
    private BigDecimal salaCheckAmt;
    /**
     * 发薪过期金额
     */
    private BigDecimal salaExpireAmt;
    /**
     * 发薪到账金额
     */
    private BigDecimal salaExpectedAmt;
    /**
     * 删除标志（0代表存在 存入这条数据的主键代表删除）
     */
    private Long delFlag;


}
