package cn.xianlink.trade.domain.convert.pay;

import cn.xianlink.trade.api.domain.bo.OrderPaySplitBo;
import cn.xianlink.trade.domain.bo.TradeAccTransBo;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OrderPaySplitBoConvert  extends BaseMapper<OrderPaySplitBo, TradeAccTransBo> {
}
