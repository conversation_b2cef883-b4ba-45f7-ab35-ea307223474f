package cn.xianlink.trade.domain;

import cn.xianlink.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.time.LocalDate;

/**
 * 数据文件表 trade_acc_oss
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("trade_acc_oss")
public class TradeAccOss extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * id主键
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 记账日期
     */
    private LocalDate transDate;
    /**
     * 商户号
     */
    private String merchantId;
    /**
     * 文件类型
     */
    private String fileType;
    /**
     * 文件名称
     */
    private String fileName;
    /**
     * 其他信息
     */
    private String fileOther;
    /**
     * url
     */
    private String url;
    /**
     * 对账状态 0 未对账 1 完成 2 失败
     */
    private Integer acctStatus;
    /**
     * 对账失败原因
     */
    private String acctReason;
    /**
     * 上传平安的回执单号
     */
    private String uploadSeqNo;
    /**
     * 上传本地ossId
     */
    private Long uploadOssId;
    /**
     * 上传本地地址
     */
    private String uploadOssUrl;
    /**
     * 删除标志（0代表存在 存入这条数据的主键代表删除）
     */
    @TableLogic(delval = "id")
    private Long delFlag;

}
