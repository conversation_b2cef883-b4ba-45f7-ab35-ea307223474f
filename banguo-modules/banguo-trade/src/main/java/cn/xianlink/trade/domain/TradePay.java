package cn.xianlink.trade.domain;

import cn.xianlink.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * 支付对象 trade_pay
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value="trade_pay", autoResultMap = true)
public class TradePay extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 支付渠道类型 pinganCloudWX
     */
    private String channel;
    /**
     * 支付appId
     */
    private String appId;

    /**
     * 订单id主键
     */
    private Long orderId;

    /**
     * 订单唯一单号
     */
    private String orderNo;

    /**
     * 订单日期
     */
    private LocalDate orderDate;
    /**
     * 账务机构id
     */
    private Long acctOrgId;
    /**
     * 业务机构id
     */
    private Long orderOrgId;
    /**
     * 登录用户id
     */
    private Long orderUserId;
    /**
     * 客户用户id
     */
    private Long customerId;
    /**
     * 客户代码
     */
    private String customerCode;
    /**
     * 客户关联代码
     */
    private String customerOutCode;
    /**
     * 客户子账号
     */
    private String customerAcctCode;
    /**
     * 客户录入 0 否 1 是
     */
    private Integer customerEntry;
    /**
     * 订单生成单号  对应银行 tradeOrderNo
     */
    private String tradeNo;
    /**
     * 支付金额
     */
    private BigDecimal payAmt;
    /**
     * 实际支付金额
     */
    private BigDecimal paySplitAmt;
    /**
     * 支付人openid
     */
    private String payerId;

    /**
     * 支付人requestIp
     */
    private String payerIp;

    /**
     * 支付关闭时间
     */
    private Date closeTime;
    /**
     * 支付单类型 PO 订单;PC 客户充值
     */
    private String busiType;
    /**
     * 累加支付单的退款金额字段 单维度
     */
    private String busiField;
    /**
     * 支付状态 0 处理中 1 退款完成 2 退款失败; 10 接口未调用; 11 接口失败
     */
    private Integer payInfStatus;
    /**
     * 支付发起时间
     */
    private Date payInfTime;
    /**
     * 支付发起时间
     */
    private Date infTime;

    /**
     * 接口状态 0 未支付;1 支付完成 ;2 支付取消; 10 接口未调用; 11 接口失败
     */
    private Integer infStatus;

    /**
     * 接口失败原因
     */
    private String infReason;
    /**
     * 重试次数（仅分账时使用）
     */
    private Integer infRetries;

    /**
     * 微信回调json字符串 @TableField(typeHandler = JacksonTypeHandler.class)
     */
    private String callbackWeixin;
    /**
     * 支付手续费率  0.006 或 0.0022
     */
    private BigDecimal feeRate;
    /**
     * 费用金额
     */
    private BigDecimal feeAmt;

    /**
     * 平安银行： 银行流水号BankOrderNo 回执的
     * 微信b2b： order_id
     */
    private String outTradeNo;

    /**
     * 平安银行：渠道流水号ChannelOrderNo
     * 微信b2b: wxpay_transaction_id
     */
    private String outChannelNo;
    /**
     * 费用金额 回执的
     */
    private BigDecimal outFeeAmt;

    /**
     * 代金券金额
     */
    private BigDecimal outCouponAmt;

    /**
     * 折扣金额
     */
    private BigDecimal outDiscountAmt;

    /**
     * 实际到账金额
     */
    private BigDecimal outCashAmt;

    /**
     * 对接渠道支付完成时间 回执的
     */
    private Date outSuccessTime;

    /**
     * 退款顺序号
     */
    private Integer refundSeq;

    /**
     * 退款总金额
     */
    private BigDecimal refundAmt;
    /**
     * 实际退款金额
     */
    private BigDecimal refundSplitAmt;

    /**
     * 退货退款
     */
    private BigDecimal refundAmt1;

    /**
     * 差额退款
     */
    private BigDecimal refundAmt2;

    /**
     * 报损退款
     */
    private BigDecimal refundAmt3;

    /**
     * 未使用
     */
    private BigDecimal refundAmt4;
    /**
     * 退货退款
     */
    private BigDecimal refundSplitAmt1;

    /**
     * 差额退款
     */
    private BigDecimal refundSplitAmt2;

    /**
     * 报损退款
     */
    private BigDecimal refundSplitAmt3;

    /**
     * 未使用
     */
    private BigDecimal refundSplitAmt4;

    /**
     * 补偿金额
     */
    private BigDecimal extraAmt;
    /**
     * 分账状态 0未结 1结算;
     */
    private Integer status;
    /**
     * 对账状态 0 未对账 1 完成 2 失败
     */
    private Integer acctStatus;
    /**
     * 对账日期
     */
    private LocalDate acctDate;
    /**
     * 删除标志; 4 交易关闭; 9 已撤销; 101 接口失败; 这3中状态设置成删除
     */
    // @TableLogic(delval = "id") 不加注解，手动处理删除标识
    private Long delFlag;

}
