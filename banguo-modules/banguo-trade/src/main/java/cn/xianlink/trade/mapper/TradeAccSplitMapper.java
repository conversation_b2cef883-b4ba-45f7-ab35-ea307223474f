package cn.xianlink.trade.mapper;

import cn.xianlink.common.mybatis.core.mapper.BaseMapperPlus;
import cn.xianlink.trade.domain.TradeAccSplit;
import cn.xianlink.trade.domain.vo.TradeAccSplitVo;
import cn.xianlink.trade.domain.vo.TradePayRefundVo;
import cn.xianlink.trade.domain.vo.TradePayVo;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * 支付分账流水Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
public interface TradeAccSplitMapper extends BaseMapperPlus<TradeAccSplit, TradeAccSplitVo> {

    int updateSplitBatch(@Param("bos") List<TradeAccSplitVo> bos);

    List<TradePayVo> queryPaySplitError(@Param("acctDate") LocalDate acctDate);

    List<TradePayVo> queryPaySplitDeleteError(@Param("acctDate") LocalDate acctDate);

    List<TradePayRefundVo> queryRefundSplitError(@Param("acctDate") LocalDate acctDate);

    List<TradePayRefundVo> queryRefundSplitDeleteError(@Param("acctDate") LocalDate acctDate);
}
