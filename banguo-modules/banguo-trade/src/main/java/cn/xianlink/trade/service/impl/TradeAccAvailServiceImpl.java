package cn.xianlink.trade.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.xianlink.common.api.enums.trade.AccountOrgTypeEnum;
import cn.xianlink.common.api.enums.trade.AccountStatusEnum;
import cn.xianlink.common.api.enums.trade.BaseTypeEnum;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.api.util.BaseEntityAutoFill;
import cn.xianlink.trade.constant.TradeCacheNames;
import cn.xianlink.trade.domain.TradeAccAvail;
import cn.xianlink.trade.domain.bo.sup.SupTradeAccSupAvailQueryBo;
import cn.xianlink.trade.domain.bo.TradeAccAvailBo;
import cn.xianlink.trade.domain.bo.TradeAccSupAvailQueryBo;
import cn.xianlink.trade.domain.convert.acc.TradeAccSupAvailConvert;
import cn.xianlink.trade.domain.vo.TradeAccAvailVo;
import cn.xianlink.trade.domain.vo.TradeAccCashVo;
import cn.xianlink.trade.domain.vo.TradeAccSupAvailVo;
import cn.xianlink.trade.mapper.TradeAccAvailMapper;
import cn.xianlink.trade.service.ITradeAccAvailService;
import cn.xianlink.trade.service.ITradeAccCashService;
import cn.xianlink.trade.service.biz.TradeBaseUtilBizService;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;

/**
 * 结算单Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@RequiredArgsConstructor
@Service
public class TradeAccAvailServiceImpl implements ITradeAccAvailService {

    private final transient TradeAccAvailMapper baseMapper;
    private final transient ITradeAccCashService tradeAccCashService;
    private final transient TradeBaseUtilBizService tradeBaseUtilBizService;

    @Cacheable(cacheNames = TradeCacheNames.CACHE_CREATE_AVAIL, key = "#orgCode + '_' + #acctOrgId + '_' + #orgType + '_' + #deptId")
    @Lock4j(name= TradeCacheNames.LOCK_CREATE_AVAIL, keys = "#orgCode + '_' + #acctOrgId + '_' + #orgType + '_' + #deptId",
            expire = 30000, acquireTimeout = 1000)
    @Transactional(rollbackFor = Exception.class)
    @Override
    public TradeAccAvailVo queryAvailOrById(Long acctOrgId, String orgCode, Long orgId, Integer orgType, Long deptId, Long availId, LocalDate availDate) {
        LambdaQueryWrapper<TradeAccAvail> lqw = Wrappers.lambdaQuery();
        lqw.eq(TradeAccAvail::getAcctOrgId, acctOrgId);
        lqw.eq(TradeAccAvail::getOrgType, orgType);
        lqw.eq(TradeAccAvail::getOrgCode, orgCode);
        lqw.eq(TradeAccAvail::getAvailDate, availDate);
        lqw.eq(TradeAccAvail::getStatus, AccountStatusEnum.FREEZE.getCode());
        lqw.eq(TradeAccAvail::getIsAuto, 1);
        lqw.eq(TradeAccAvail::getDeptId, deptId);
        lqw.orderByAsc(TradeAccAvail::getId);
        List<TradeAccAvailVo> accAvailVos = baseMapper.selectVoList(lqw);
        if (accAvailVos.size() > 0) {
            List<TradeAccAvailVo> availVos = accAvailVos.stream().filter(vo -> vo.getId().equals(availId)).toList();
            if (availVos.size() > 0) {
                return availVos.get(0);
            }
            return accAvailVos.get(0);
        }
        TradeAccAvailBo accAvailBo = new TradeAccAvailBo();
        accAvailBo.setAcctOrgId(acctOrgId);
        accAvailBo.setOrgId(orgId);
        accAvailBo.setOrgCode(orgCode);
        accAvailBo.setOrgType(orgType);
		accAvailBo.setIsAuto(1);
        accAvailBo.setDeptId(deptId);
        accAvailBo.setAvailDate(availDate);
        return insertByBo(accAvailBo);
    }

    @Override
    public List<TradeAccAvailVo> queryAvailProcessing(TradeAccSupAvailQueryBo bo) {
        LambdaQueryWrapper<TradeAccAvail> lqw = Wrappers.lambdaQuery();
        lqw.between(TradeAccAvail::getAvailDate, bo.getAvailDateStart(), bo.getAvailDateEnd());
        // lqw.eq(TradeAccAvail::getAvailAmt, BigDecimal.ZERO);
        lqw.eq(TradeAccAvail::getStatus, AccountStatusEnum.FREEZE.getCode());
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 基于提现单id查询 结算单
     */
    @Override
    public List<TradeAccAvailVo> queryListByCashId(Long cashId) {
        return baseMapper.selectVoList(Wrappers.lambdaQuery(TradeAccAvail.class)
                .eq(TradeAccAvail::getCashId, cashId).eq(TradeAccAvail::getStatus, AccountStatusEnum.AVAIL.getCode()));
    }
    /**
     * 查询结算单列表
     */
    @Override
    public List<TradeAccAvailVo> queryListByIds(List<Long> availIds) {
        return baseMapper.selectVoBatchIds(availIds);
    }
    /**
     * 查询全部结算单列表
     */
    @Override
    @BaseEntityAutoFill
    public TableDataInfo<TradeAccAvailVo> querySupPageList(String orgCode, SupTradeAccSupAvailQueryBo queryBo) {
        LambdaQueryWrapper<TradeAccAvail> lqw = buildQueryWrapper(orgCode, queryBo);
        IPage<TradeAccAvailVo> result = baseMapper.selectVoPage(queryBo.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询可提现结算单列表
     */
    @Override
    @BaseEntityAutoFill
    public List<TradeAccAvailVo> querySupList(String orgCode, SupTradeAccSupAvailQueryBo queryBo) {
        LambdaQueryWrapper<TradeAccAvail> lqw = buildQueryWrapper(orgCode, queryBo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 查询未选择的全部结算单
     */
    @Override
    @BaseEntityAutoFill
    public List<TradeAccAvailVo> querySupExclusionList(String orgCode, SupTradeAccSupAvailQueryBo queryBo) {
        LambdaQueryWrapper<TradeAccAvail> lqw = buildQueryWrapper(orgCode, queryBo);
        // 排序后面添加条件也没有问题 ！！！！！！！！！！！！！
        lqw.eq(TradeAccAvail::getStatus, AccountStatusEnum.AVAIL.getCode());
        lqw.isNull(TradeAccAvail::getCashId);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<TradeAccAvail> buildQueryWrapper(String orgCode, SupTradeAccSupAvailQueryBo queryBo) {
        LambdaQueryWrapper<TradeAccAvail> lqw = Wrappers.lambdaQuery();
        lqw.eq(TradeAccAvail::getOrgType, AccountOrgTypeEnum.SUPPLIER.getCode());
        lqw.eq(TradeAccAvail::getOrgCode, orgCode);
        lqw.ne(TradeAccAvail::getAvailAmt, BigDecimal.ZERO);
        lqw.le(TradeAccAvail::getAvailDate, LocalDate.now());
        lqw.eq(queryBo.getDeptId() != null && queryBo.getDeptId() != 0L, TradeAccAvail::getDeptId, queryBo.getDeptId());
        lqw.eq(queryBo.getCashId() != null, TradeAccAvail::getCashId, queryBo.getCashId());
        // 以上是固定条件
        lqw.in(CollectionUtil.isNotEmpty(queryBo.getDeptIds()), TradeAccAvail::getDeptId, queryBo.getDeptIds());
        lqw.eq(StringUtils.isNotBlank(queryBo.getAvailNo()), TradeAccAvail::getAvailNo, queryBo.getAvailNo());
        lqw.ge(queryBo.getAvailDateStart() != null, TradeAccAvail::getAvailDate, queryBo.getAvailDateStart());
        lqw.le(queryBo.getAvailDateEnd() != null, TradeAccAvail::getAvailDate, queryBo.getAvailDateEnd());
        lqw.ge(queryBo.getStatusTimeStart() != null, TradeAccAvail::getStatusTime, queryBo.getStatusTimeStart());
        lqw.le(queryBo.getStatusTimeEnd() != null, TradeAccAvail::getStatusTime, queryBo.getStatusTimeEnd());
        lqw.eq(queryBo.getStatus() != null, TradeAccAvail::getStatus, queryBo.getStatus());
        lqw.in(CollectionUtil.isNotEmpty(queryBo.getStatuss()), TradeAccAvail::getStatus, queryBo.getStatuss());
        if (StringUtils.isNotBlank(queryBo.getAcctOrgCode())) {
            lqw.in(TradeAccAvail::getAcctOrgId, tradeBaseUtilBizService.queryIdsByCodes(Collections.singletonList(queryBo.getAcctOrgCode()), BaseTypeEnum.REGION_WH.getCode()));
        }
        lqw.orderByDesc(TradeAccAvail::getPriority).orderByDesc(TradeAccAvail::getId);
        return lqw;
    }

    @Override
    @BaseEntityAutoFill
    public TableDataInfo<TradeAccSupAvailVo> querySupPageList(TradeAccSupAvailQueryBo queryBo) {
        LambdaQueryWrapper<TradeAccAvail> lqw = buildQueryWrapper(queryBo);
        Page<TradeAccAvailVo> result = baseMapper.selectVoPage(queryBo.build(), lqw);
        return TableDataInfo.build(result.getRecords().stream().map(TradeAccSupAvailConvert.INSTANCE::toConvert).toList(), result.getTotal());
    }

    @Override
    @BaseEntityAutoFill
    public List<TradeAccSupAvailVo> querySupList(TradeAccSupAvailQueryBo queryBo) {
        LambdaQueryWrapper<TradeAccAvail> lqw = buildQueryWrapper(queryBo);
        return baseMapper.selectVoList(lqw).stream().map(TradeAccSupAvailConvert.INSTANCE::toConvert).toList();
    }

    private LambdaQueryWrapper<TradeAccAvail> buildQueryWrapper(TradeAccSupAvailQueryBo queryBo) {
        LambdaQueryWrapper<TradeAccAvail> lqw = Wrappers.lambdaQuery();
        lqw.eq(TradeAccAvail::getOrgType, AccountOrgTypeEnum.SUPPLIER.getCode());
        lqw.ne(TradeAccAvail::getAvailAmt, BigDecimal.ZERO);
        // 以上是固定条件
        lqw.le(TradeAccAvail::getAvailDate, LocalDate.now());
        lqw.between(TradeAccAvail::getAvailDate, queryBo.getAvailDateStart(), queryBo.getAvailDateEnd());
        lqw.eq(StringUtils.isNotBlank(queryBo.getAvailNo()), TradeAccAvail::getAvailNo, queryBo.getAvailNo());
        lqw.eq(queryBo.getStatus() != null, TradeAccAvail::getStatus, queryBo.getStatus());
        lqw.in(CollectionUtil.isNotEmpty(queryBo.getOrgCodes()), TradeAccAvail::getOrgCode, queryBo.getOrgCodes());
        if (CollectionUtil.isNotEmpty(queryBo.getAcctOrgCodes())) {
            lqw.in(TradeAccAvail::getAcctOrgId, tradeBaseUtilBizService.queryIdsByCodes(queryBo.getAcctOrgCodes(), BaseTypeEnum.REGION_WH.getCode()));
        }
        if (StringUtils.isNotBlank(queryBo.getCashNo())) {
            TradeAccCashVo cashVo = tradeAccCashService.queryByNo(queryBo.getCashNo());
            if (cashVo == null) {
                lqw.eq(TradeAccAvail::getId, -1);
            } else {
                lqw.eq(TradeAccAvail::getCashId, cashVo.getId());
            }
        }
        lqw.orderByDesc(TradeAccAvail::getId);
        return lqw;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TradeAccAvailVo insertByBo(TradeAccAvailBo addBo) {
        TradeAccAvail add = MapstructUtils.convert(addBo, TradeAccAvail.class);
        add.setStatus(AccountStatusEnum.FREEZE.getCode());
        add.setStatusTime(Convert.toDate(DateUtil.now()));
        try {
            insertAccAvail(add);
        } catch (DuplicateKeyException dke) {
            insertAccAvail(add); // 重试一次
        }
        return MapstructUtils.convert(add, TradeAccAvailVo.class);
    }

    private void insertAccAvail(TradeAccAvail add) {
        add.setAvailNo(tradeBaseUtilBizService.getNextAvailNo(add.getAvailDate()));
        baseMapper.insert(add);
    }

}
