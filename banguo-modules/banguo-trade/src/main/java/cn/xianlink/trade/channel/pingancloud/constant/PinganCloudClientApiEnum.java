package cn.xianlink.trade.channel.pingancloud.constant;

/*
    1 工商核验系统维护的通知
        1、子台账开立（6248接、6293接口）;
        2、子台账信息维护（6171接口、6242接口、6296接口、6378接口）;
        3、子台账绑卡及鉴权（6055接口、6238接口、6240接口、6294接口、6349接口、6357接口）;
        4、子台账收款功能开通申请及查询（6261接口）。

 */
public enum PinganCloudClientApiEnum {
    // 平安测试环境， 手机号都要填  1111111111，  可以跳过验证
    // 1 支付 退款
    PAY("CLOUDPAY5001", "/V1.0/order/pay", "支付"),
    PAY_QUERY("CLOUDPAY5002", "/V1.0/order/querySingleOrder", "支付查询"),
    PAY_CLOSE("CLOUDPAY5029", "/V1.0/order/close", "支付关闭"),
    PAY_REFUND("CLOUDPAY5004", "/V1.0/order/applyRefund", "支付退款"),
    PAY_REFUND_QUERY("CLOUDPAY5005", "/V1.0/order/querySingleRefund", "退款查询"),
    // 2 分账， 单独调用
    PAY_SPLIT("KFEJZB6216", "/V1.0/OnWayTopThirdPaySplit", "第三方支付渠道在途充值（分账）, T+1, 最多20个子订单，超过20个可多次请求"),
    PAY_REFUND_SPLIT("KFEJZB6217", "/V1.0/RevokeOnWayTopThirdPaySplit", "第三方支付渠道在途充值撤销（分账）, T+1, 最多20个子退单"),

    // 2 子账户注册和银行绑定相关
    ACC_ORG_RELATION("KFEJZB6248", "/V1.0/AutonymOpenCustAcctId", "开通子账户，绑定orgcode和银行子账户，一对一"),
    ACC_ORG_QUERY("KFEJZB6324", "/V1.0/EJZBCustInformationQuery", "(!可选)开通子账号查询，敏感信息*， 有 ControlType管控类型字段， N为正常, 验证登记行为是否生效 ！"),
    ACC_BIND_QUERY("KFEJZB6098", "/V1.0/MemberBindQuery", "会员绑定申请信息查询，可查询到所有绑定成功的子账户（仅是近期绑定的，不返回未绑定银行的子账户）"),

    // ACC_ORG_UPDATE("KFEJZB6162", "/V1.0/MbrPropertyRevise", "(可选)修改会员属性， 仅 MemberProperty 字段，从 00 改为 SH， 无权限"),
    ACC_ORG_UPDATE("KFEJZB6296", "/V1.0/MemberInformationChange", "(可选)会员信息修改， 可修改MemberName,CompanyName,ReprName,ReprGlobalType,ReprGlobalId， 可更新法人，公司名称！！！！"),
    // 2.1 绑定银行卡
    ACC_BIND_SMS_APPLY("KFEJZB6238", "/V1.0/BindUnionPayWithCheckCorp", "会员绑定提现账户银联鉴权-校验法人， 绑定个人银行卡"),
    ACC_BIND_SMS_COMMIT("KFEJZB6239", "/V1.0/CheckMsgCodeWithCorp", "银联鉴权回填短信码-校验法人，传入短信"),
    ACC_BIND_AMT_APPLY("KFEJZB6240", "/V1.0/BindSmallAmountWithCheckCorp", "会员绑定提现账户小额鉴权-校验法人，绑定企业银行卡绑定开始"),
    ACC_BIND_AMT_COMMIT("KFEJZB6241", "/V1.0/CheckAmountWithCorp", "小额鉴权验证，传入金额，短信"),
    ACC_BIND_AMT_QUERY("KFEJZB6061", "/V1.0/SmallAmountTransferQuery", "查询小额往账鉴权的转账状态，用6238或6240的流水号查询"),
    // ACC_BIND_("KFEJZB6138", "/V1.0/MntMbrBindRelateAcctBankCode", "(!可选) 维护会员绑定提现账户联行号"),
    ACC_BIND_RECORD("KFEJZB6244", "/V1.0/RegisterBehaviorRecordInfo", "登记行为记录信息，提交绑定短信时，记录ip，mac地址，时间"),
    ACC_BIND_REMOVE("KFEJZB6065", "/V1.0/UnbindRelateAcct", "会员解绑提现账户 (银行卡解绑)"),

    ACC_PHONE_APPLY("KFEJZB6083", "/V1.0/ApplyForChangeOfCellPhoneNum", "(可选) 申请修改手机号码，修改绑定记录中的手机号"),
    ACC_PHONE_COMMIT("KFEJZB6084", "/V1.0/BackfillDynamicPassword", "(可选) 回填动态码-修改手机"),

    // 2.2 子账户账务查询
    ACC_AVAIL_QUERY("KFEJZB6011", "/V1.0/SupAcctIdBalanceQuery", "(可选)查询资金汇总账户余额，所有子账户的合计总余额，也可按id查询"),
    ACC_AVAIL_BYCUST_QUERY("KFEJZB6093", "/V1.0/QueryCustAcctIdBalance", "(可选)查询会员子账号余额, 单子账户查询，仅可用余额"),
    ACC_AVAIL_HISTORY_QUERY("KFEJZB6114", "/V1.0/CustAcctIdHistoryBalanceQuery", "(!可选)查询子帐号历史余额及待转可提现状态，单子账户查询，可显示每日余额"),
    ACC_AVAIL_BYACCT_QUERY("KFEJZB6010", "/V1.0/CustAcctIdBalanceQuery", "查询银行子账户余额，单子账户查询 或 查询全部特殊子账号，返回可用余额，可提现余额,  6010查开户,6098查绑卡 "),
    ACC_AVAIL_BYCUST_QUERY2 ("KFEJZB6037", "/V1.0/QueryCustAcctId", "(!可选)查询会员子账号，单子账户查询，返回可用余额，可提现余额，冻结余额"),

    // 2.2 子账户账务变更
    ACC_CASH("KFEJZB6033", "/V1.0/MembershipWithdrawCash", "会员提现，提现余额转入银行卡"),
    ACC_TRANSFER_PAY("KFEJZB6034", "/V1.0/MemberTransaction", "(!可选)会员间交易-不验证 FuncFlag=6 or 9，普通子账号(或营销子账户) -> 商户子账户，商户间无法转"),
    ACC_TRANSFER_REFUND("KFEJZB6164", "/V1.0/MemberTransactionRefund", "(!可选)会员间交易退款-不验证 FuncFlag=2 or 6"),
    ACC_CREDIT_PAY("KFEJZB6139", "/V1.0/RegisterBillSupportWithdraw", "登记挂账，挂账子账户（或公司的银行账户（叫监管账户）） -> 普通子账号(或营销子账户)"),
    ACC_CREDIT_REFUND("KFEJZB6140", "/V1.0/RevRegisterBillSupportWithdraw", "登记挂账 撤销,  挂账子账户 <- 普通子账号"),
    ACC_FREEZE_PAY("KFEJZB6163", "/V1.0/MembershipTranchePay", "(!可选)会员资金支付-不验证，普通子账号（冻结余额） -> 商户子账户，转出子账号只能是普通子账户"),
    ACC_FREEZE("KFEJZB6007", "/V1.0/MembershipTrancheFreeze", "(可选)会员资金冻结-不验证， 普通子账号（可用余额，可提现余额）<-> 当前普通子账号（冻结余额）"),
    ACC_TRANSFER_TEST("KFEJZB6211", "/V1.0/ApntTransfer", "!指定转账划款（测试专用，不能投产）"),

    // 2.3 交易流水查询
    ACC_FEE_QUERY("KFEJZB6109", "/V1.0/BankCostDsDealResultQuery", "(!可选)查询银行费用扣收结果"),
    ACC_TRADE_QUERY("KFEJZB6110", "/V1.0/SingleTransactionStatusQuery", "查询银行单笔交易状态，基于CnsmrSeqNo查询6034, 6033交易"),
    ACC_CLEAR_QUERY("KFEJZB6108", "/V1.0/BankClearQuery", "查询银行在途清算结果"),
    ACC_CASH_QUERY("KFEJZB6048", "/V1.0/BankWithdrawCashBackQuery", "(可选)查询银行提现退票信息"),
    ACC_CLEAR_CASH_QUERY("KFEJZB6073", "/V1.0/BankWithdrawCashDetailsQuery", "查询银行时间段内清分提现明细(可查支付和退款的分账明细)， TX"),
    ACC_RECHARGE_QUERY("KFEJZB6050", "/V1.0/CommonTransferRechargeQuery", "!查询普通转账充值明细 "),
    ACC_TRADE_DETAIL_QUERY("KFEJZB6072", "/V1.0/BankTransactionDetailsQuery", "查询银行时间段内交易明细"),

    // 2.4 对账  见证+收单接口   查看明细，调账等接口中 OrderNo 是分账单号
    // 订单支付完成，但分账流水不一定记账成功，要使用分账处理接口查询（6146），并调账（6145） 或 补账（6147）
    ACC_FILE_QUERY("KFEJZB6103", "/V1.0/ReconciliationDocumentQuery", "查询平安的对账文件，使用 apiClient.fileDownload 下载"),
    ACC_FILE_UPLOAD_NOFITY("KFEJZB6379", "/V1.0/NotifyDownloadThirdPayFile", "通知平安下载对账文件，使用 apiClient.fileUpload 上传，上传完文件后通知平安，平安是10：00-22：00检索昨日对账文件，必须在22点前上传"),
    ACC_FILE_UPLOAD_QUERY("KFEJZB6380", "/V1.0/QueryThirdPayFileStatus", "平安收到下载对账文件的通知后，查询是否下载完成"),
    ACC_SPLIT_QUERY("KFEJZB6146", "/V1.0/ChargeDetailQuery", "(可选)查询充值明细，(只能是支付)基于分账子订单号查询，返回金额，是否成功等"),
    ACC_SPLIT_ADJUST("KFEJZB6145", "/V1.0/AccountRegulation", "(可选)调账，对6146无法入账的(只能是支付)（之前入账子账号错误造成无法入帐的），修改分账子订单的入账子账号"),
    ACC_SPLIT_SUPPLY("KFEJZB6147", "/V1.0/PlatformAccountSupply", "(可选)平台补账，(只能是支付)对6146查询未入账进行补账"),

    // 2.5 同名账户处理
    ACC_ORG_SAME("KFEJZB6293", "/V1.0/MntMbrBindSameRealCustNameAcct", "(可选)实名开同名账户关联关系维护，是6167的升级接口，商户子账户间不能直接转账，要先 6249 申请额度， 6250 同名转到普通子账户， 6034 普通子账户转给其他商户子账户"),
    ACC_ORG_SAME_LIMIT("KFEJZB6249", "/V1.0/MntBindCustLimit", "(可选)维护同名户免密限额，申请从同名商户子账户转到同名普通子账户的转账限额"),
    ACC_TRANSFER_SAME("KFEJZB6250", "/V1.0/BindCustTran", "(可选)同名户互转，同名商户子账户转到同名普通子账户的转账"),

    ACC_ORG_WHITE("KFEJZB6245", "/V1.0/CustAcctWhiteMaintence", "(可选)商户子账户白名单维护， 商户间转账白名单？？"),
    ACC_ORG_WHITE_QUERY("KFEJZB6246", "/V1.0/CustAcctWhiteResultQuery", "(可选)查询商户子账户白名单维护结果"),
    // 2.6 收款编码支付相关
    LARGE_PAY("KFEJZB6276", "/V1.0/OrderRecriptSubcodeOpen", "创建收款编号 和 查询"),
    LARGE_PAY_CLOSE("KFEJZB6277", "/V1.0/OrderRecriptSubcodeCancel", "收款编号关闭"),
    LARGE_PAY_REFUND("KFEJZB6278", "/V1.0/OrderTransferRefund", "收款编号退款 和 查询"),
    LARGE_PAY_QUERY("KFEJZB6279", "/V1.0/OrderRecriptQuery", "收款编号分页查询"),
    // 目前收款编码均为绑定卡支付，需要支付者在银行系统绑卡，若支付者不绑卡，或者不方便绑卡，需要补充：6347+6348+6349+6350  需要用健全的方式为付款方加付款白名单

    ;


    private final String txnCode;
    private final String serviceId;
    private final String remark;

    PinganCloudClientApiEnum(String txnCode, String serviceId, String remark) {
        this.txnCode = txnCode;
        this.serviceId = serviceId;
        this.remark = remark;
    }

    public String getTxnCode() {
        return txnCode;
    }

    public String getServiceId() {
        return serviceId;
    }

    public String getRemark() {
        return remark;
    }

}
