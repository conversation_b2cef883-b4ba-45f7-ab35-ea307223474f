package cn.xianlink.trade.service.biz;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.xianlink.common.api.enums.trade.*;
import cn.xianlink.common.api.vo.RemoteBaseDataVo;
import cn.xianlink.common.core.enums.YNStatusEnum;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.trade.channel.AccountService;
import cn.xianlink.trade.channel.pingancloud.PinganCloudOrgBankRelaService;
import cn.xianlink.trade.comm.CommServiceHelper;
import cn.xianlink.trade.comm.SignChannelEnum;
import cn.xianlink.trade.comm.youwei.YouweiCommServiceImpl;
import cn.xianlink.trade.config.properties.ChannelsProperties;
import cn.xianlink.trade.config.properties.YouweiProperties;
import cn.xianlink.trade.constant.Constants;
import cn.xianlink.trade.constant.TradeCacheNames;
import cn.xianlink.trade.domain.bo.TradeAccCashAcctCashBo;
import cn.xianlink.trade.domain.bo.TradeAccCashBo;
import cn.xianlink.trade.domain.bo.comm.TradeCommCashBo;
import cn.xianlink.trade.domain.bo.org.*;
import cn.xianlink.trade.domain.bo.platform.TradePlatformCustQueryBo;
import cn.xianlink.trade.domain.bo.sup.SupTradeAccSupCashAvaildsBo;
import cn.xianlink.trade.domain.vo.TradeAccAvailVo;
import cn.xianlink.trade.domain.vo.TradeAccCashVo;
import cn.xianlink.trade.domain.vo.TradeCustAccountVo;
import cn.xianlink.trade.domain.vo.comm.TradeCommBankVo;
import cn.xianlink.trade.domain.vo.comm.TradeCommCashVo;
import cn.xianlink.trade.domain.vo.org.*;
import cn.xianlink.trade.service.*;
import com.baomidou.lock.annotation.Lock4j;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;

/**
 * 机构子账户绑定Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@CustomLog
@RequiredArgsConstructor
@Service
public class TradeOrgBankBizService {

    private final transient ChannelsProperties channelsProperties;
    private final transient YouweiProperties youweiProperties;
    private final transient ITradeOrgBankService tradeOrgBankService;
    private final transient ITradeAccTransService tradeAccTransService;
    private final transient ITradeAccCashService tradeAccCashService;
    private final transient ITradeCommCashService tradeCommCashService;
    private final transient ITradeOrgRelationService tradeOrgRelationService;
    private final transient ITradeAccAvailService tradeAccAvailService;
    private final transient ITradeCustAccountService tradeCustAccountService;
    private final transient ITradeCommBankService tradeCommBankService;
    private final transient TradeBaseDataBizService tradeBaseDataBizService;
    private final transient YouweiCommServiceImpl youweiCommService;
    private final transient PinganCloudOrgBankRelaService pinganCloudOrgBankRelaService;
	private final transient AccountService accountService;
    private final transient CommServiceHelper commServiceHelper;
    /**
     * 仅供应商调用 ！
     */
    public TradeOrgBankBindVo queryBindByCode(String orgCode, Integer orgType) {
        TradeOrgBankBindVo vo = tradeOrgRelationService.queryBindByCode(orgCode, orgType);
        if (vo == null || vo.getStatus() == null) {
            String outOrgCode = accountService.getDefaultOutCode(orgType);
            TradeOrgBankRelaBo relaBo = new TradeOrgBankRelaBo();
            RemoteBaseDataVo baseDataVo = tradeBaseDataBizService.queryByCode(orgCode, orgType);
            if (baseDataVo == null) {
                throw new ServiceException(String.format("%s %s未定义", orgCode, AccountOrgTypeEnum.getDescByCode(orgType)));
            }
            relaBo.setOrgType(orgType);
            relaBo.setOrgCode(orgCode);
            relaBo.setOrgName(baseDataVo.getName());
            relaBo.setOrgId(baseDataVo.getId());
            if (StringUtils.isNotBlank(outOrgCode)) {
                TradeOrgBankVo bankVo = tradeOrgBankService.queryByCode(outOrgCode);
                // 此处未空， 说明未手工配置 outOrgCode 对应的 bank 数据
                relaBo.setOutOrgCode(outOrgCode);
                relaBo.setOutAcctCode(bankVo.getOutAcctCode());
//                relaBo.setSplitOrgCode(outOrgCode);
//                relaBo.setSplitAcctCode(bankVo.getOutAcctCode());
                tradeOrgRelationService.insertByBo(relaBo, vo != null);
                vo = tradeOrgRelationService.queryBindByCode(orgCode, orgType);
            } else {
                vo = new TradeOrgBankBindVo();
                BeanUtil.copyProperties(relaBo, vo, Constants.BeanCopyIgnoreNullValue);
            }
        }
        return vo;
    }

    public TradeOrgBankAuthVo sameOrgAuth(TradeOrgBankAuthBo bo) {
        TradeOrgBankVo vo = tradeOrgBankService.queryByCode(bo.getOutOrgCode());
        if (StringUtils.isEmpty(vo.getCommAcctCode())) {
            throw new ServiceException("未绑定同名账户");
        }
        if (StringUtils.isEmpty(vo.getBankMobile())) {
            throw new ServiceException("未绑定银行");
        }
        TradeOrgBankRelaAuthVo tvo = MapstructUtils.convert(vo, TradeOrgBankRelaAuthVo.class);
        tvo.setAuthSingleAmt(bo.getAuthSingleAmt());
        tvo.setAuthTotalAmt(bo.getAuthTotalAmt());
        tvo.setAuthValidDays(bo.getAuthValidDays());
        TradeOrgBankRelaAuthBo infTbo = pinganCloudOrgBankRelaService.sameOrgAuth(tvo);
        tradeOrgRelationService.updateBankByAuthBo(infTbo);
        BeanUtil.copyProperties(infTbo, vo, Constants.BeanCopyIgnoreNullValue);
        return MapstructUtils.convert(vo, TradeOrgBankAuthVo.class);
    }

    public TradeOrgBankAuthVo sameOrgAuthCheck(TradeOrgBankAuthSmsBo bo) {
        TradeOrgBankVo vo = tradeOrgBankService.queryByCode(bo.getOutOrgCode());
        if (StringUtils.isEmpty(vo.getCommAcctCode())) {
            throw new ServiceException("未绑定同名账户");
        }
        if (StringUtils.isEmpty(vo.getAuthAcceptNo())) {
            throw new ServiceException("请先发起授权操作");
        }
        TradeOrgBankRelaAuthVo tvo = MapstructUtils.convert(vo, TradeOrgBankRelaAuthVo.class);
        tvo.setSmsCode(bo.getSmsCode());
        TradeOrgBankRelaAuthBo infTbo = pinganCloudOrgBankRelaService.checkSameOrgAuth(tvo);
        tradeOrgRelationService.updateBankByAuthBo(infTbo);
        BeanUtil.copyProperties(infTbo, vo, Constants.BeanCopyIgnoreNullValue);
        return MapstructUtils.convert(vo, TradeOrgBankAuthVo.class);
    }


    public List<SupTradeAccSupCashAvaildsBo> cashInit(List<TradeAccAvailVo> vos, TradeAccCashVo accCashVo) {
        Map<Long, SupTradeAccSupCashAvaildsBo> tboMap = new HashMap<>();
        Map<String, TradeAccCashVo> cashMap = new HashMap<>();
        for (TradeAccAvailVo vo : vos) {
            if (!AccountStatusEnum.AVAIL.getCode().equals(vo.getStatus())) {
                throw new ServiceException(String.format("结算单 %s 不是可提现状态", vo.getAvailNo()));
            }
            if (vo.getCashId() != null) { // 已读跳过
                continue;
            }
            TradeAccCashVo cashVo = accCashVo;
            if (cashVo == null) {
                String cashKey = String.format("%s_%s", vo.getOrgCode(), vo.getOrgType());
                cashVo = cashMap.get(cashKey);
                if (cashVo == null) {
                    // 供应商默认总仓机构生成提现单， 城市仓使用发生机构作为总仓机构
                    cashVo = tradeAccCashService.queryCashOrById(vo.getOrgCode(), vo.getOrgId(), vo.getOrgType(), null, null);
                    cashMap.put(cashKey, cashVo);
                }
            } else {
                // 约束的机构不同
                if (!cashVo.getOrgCode().equals(vo.getOrgCode()) || !cashVo.getOrgType().equals(vo.getOrgType())
                        || (cashVo.getDeptId() != null && cashVo.getDeptId() != 0L && !cashVo.getDeptId().equals(vo.getDeptId()))) {
                    continue;
                }
            }
            if (!CashInfStatusEnum.INF_INIT.getCode().equals(cashVo.getInfStatus())) {
                if (accCashVo == null) {
                    throw new ServiceException("当日提现单不是初始状态");
                }
                // 供应商情况时， 不报异常， 管理台页面报异常
                continue;
            }
            SupTradeAccSupCashAvaildsBo tbo = tboMap.get(cashVo.getId());
            if (tbo == null) {
                tbo = new SupTradeAccSupCashAvaildsBo();
                tbo.setCashId(cashVo.getId());
                tbo.setCashNo(cashVo.getCashNo());
                tbo.setAvailIds(new ArrayList<>());
                tbo.getAvailIds().add(vo.getId());
                tboMap.put(cashVo.getId(), tbo);
            } else {
                tbo.getAvailIds().add(vo.getId());
            }
        }
        tboMap.forEach((key, tbo) -> tradeAccTransService.updateCashInit(tbo));
        return tboMap.values().stream().toList();
    }

    /*
        结算单读入提现单
     */
    public void cashInitCancelByIds(List<Long> availIds, RemoteBaseDataVo baseDataVo) {
        if (CollectionUtil.isEmpty(availIds)) {
            return;
        }
        cashInitCancel(tradeAccAvailService.queryListByIds(availIds), baseDataVo);
    }

    public void cashInitCancel(List<TradeAccAvailVo> vos, RemoteBaseDataVo baseDataVo) {
        Map<Long, SupTradeAccSupCashAvaildsBo> tboMap = new HashMap<>();
        for (TradeAccAvailVo vo : vos) {
            if (!AccountStatusEnum.AVAIL.getCode().equals(vo.getStatus())) {
                throw new ServiceException(String.format("结算单 %s 不是可提现状态", vo.getAvailNo()));
            }
            if (vo.getCashId() == null) {
                continue;
            }
            if (baseDataVo != null && (!baseDataVo.getCode().equals(vo.getOrgCode())
                    || !baseDataVo.getType().equals(vo.getOrgType()) || !vo.getPriority().equals(0))){
                // 1 约束的机构不同    2 baseDataVo 不为空，供应商情况，优先级高的不能删除
                continue;
            }
            SupTradeAccSupCashAvaildsBo tbo = tboMap.get(vo.getCashId());
            if (tbo == null) {
                tbo = new SupTradeAccSupCashAvaildsBo();
                tbo.setCashId(vo.getCashId());
                tbo.setCashNo(vo.getCashNo());
                tbo.setAvailIds(new ArrayList<>());
                tbo.getAvailIds().add(vo.getId());
                tboMap.put(vo.getCashId(), tbo);
            } else {
                tbo.getAvailIds().add(vo.getId());
            }
        }
        tboMap.forEach((key, tbo) -> tradeAccTransService.updateCashInitCancel(tbo));
    }

    public RemoteBaseDataVo getManageOrgCode() {
        return tradeBaseDataBizService.queryByCode(channelsProperties.getManageOrgCode(), BankOrgTypeEnum.REGION_WH.getCode());
    }

    // 公司账户提现
    @Lock4j(name= TradeCacheNames.LOCK_WITHDRAW_CASH, keys = "#bo.outOrgCode", expire = 30000, acquireTimeout = 1000)
    public void withdrawCash(TradeAccCashAcctCashBo bo) {
        RemoteBaseDataVo manageOrgVo = getManageOrgCode();
        if (manageOrgVo == null) {
            throw new ServiceException("默认管理机构未定义");
        }
        TradeOrgBankRelaVo bankVo = tradeOrgRelationService.queryBankByCode(manageOrgVo.getCode(), manageOrgVo.getType(), bo.getOutOrgCode());
        if (bankVo == null || !AccountOrgBindEnum.BIND.getCode().equals(bankVo.getStatus())) {
            throw new ServiceException(String.format("%s 未绑定银行", bo.getOutOrgCode()));
        }
        if (!BankOrgTypeEnum.REGION_WH.getCode().equals(bankVo.getOutOrgType())) {
            throw new ServiceException(String.format("%s 往来单位类型不支持", bo.getOutOrgCode()));
        }
        TradeAccCashBo cashBo = new TradeAccCashBo();
        cashBo.setOrgId(manageOrgVo.getId());
        cashBo.setOrgType(manageOrgVo.getType());
        cashBo.setOrgCode(manageOrgVo.getCode());
        cashBo.setDeptId(0L);
        cashBo.setOutFeeAmt(BigDecimal.ZERO); // 总部提现 0 费用
        cashBo.setCommissionAmt(BigDecimal.ZERO);
        cashBo.setCashAmt(bo.getCashAmt());
        withdrawCash(tradeAccCashService.insertByBo(cashBo, bankVo), bankVo);
    }

    /*
       供应商提现 （验证是否审核，如需要审核，状态改成审核状态）
           新生成提现单, 不幂等， 用 lock 控制
    */
    @Lock4j(name= TradeCacheNames.LOCK_WITHDRAW_CASH, keys = "#dataVo.code + '_' + #dataVo.type", expire = 30000, acquireTimeout = 1000)
    public void withdrawCash(boolean isInf, SupTradeAccSupCashAvaildsBo bo, TradeSupBaseDataVo dataVo) {
        List<TradeAccAvailVo> vos = null;
        if (CollectionUtil.isNotEmpty(bo.getAvailIds())) {
            vos = tradeAccAvailService.queryListByIds(bo.getAvailIds()).stream()
                    .filter(avo -> avo.getCashId() == null && (bo.getDeptId() == null || bo.getDeptId() == 0L || bo.getDeptId().equals(avo.getDeptId()))
                            && AccountStatusEnum.AVAIL.getCode().equals(avo.getStatus())).toList();
        }
        if (CollectionUtil.isEmpty(vos)) {
            throw new ServiceException("请选择结算单");
        }
        BigDecimal cashAmt = vos.stream().map(TradeAccAvailVo::getAvailAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (cashAmt.compareTo(channelsProperties.getWithdrawFeeAmt()) <= 0) {
            throw new ServiceException(String.format("金额必须大于 %s 元", channelsProperties.getWithdrawFeeAmt().toString()));
        }
        if (cashAmt.compareTo(channelsProperties.getWithdrawLimitAmt()) < 0) {
            throw new ServiceException(String.format("金额必须大于限额 %s 元", channelsProperties.getWithdrawLimitAmt().toString()));
        }
        TradeOrgBankRelaVo bankVo = validateOrgBank(dataVo.getCode(), dataVo.getType());
        TradeAccCashVo cashVo = tradeAccCashService.queryCashOrById(dataVo.getCode(), dataVo.getId(), dataVo.getType(), bo.getDeptId(), null);
        log.keyword(cashVo.getCashNo(), "withdrawCash").info("1 提现单读入结算");
        List<SupTradeAccSupCashAvaildsBo> cashVos = cashInit(vos, cashVo); // 读入结算单
        if (cashVos.size() == 0) {
            log.keyword(cashVo.getCashNo(), "withdrawCash").info("2 结算单未读入提现单");
            throw new ServiceException("结算单未读入提现单");
        }
        TradeAccCashBo cashBo = MapstructUtils.convert(cashVo, TradeAccCashBo.class);
        if (!isInf && channelsProperties.isWithdrawCheck()) {
            tradeAccTransService.updateCashCheck(cashBo.setId(cashVo.getId()).setCashNo(cashVo.getCashNo()));
            log.keyword(cashVo.getCashNo(), "withdrawCash").info("2 提现单转待审核");
        } else {
            try {
                withdrawCash(tradeAccCashService.queryById(cashVo.getId()), bankVo);
            } catch (ServiceException se) {
                // 如果直接提现异常，进入待审核状态
                tradeAccTransService.updateCashCheck(cashBo.setId(cashVo.getId()).setCashNo(cashVo.getCashNo()));
                log.keyword(cashVo.getCashNo(), "withdrawCash").info("2 提现单转待审核");
                throw se;
            }
            log.keyword(cashVo.getCashNo(), "withdrawCash").info("3 提现单完成提现");
        }
    }
    /*
        提现转待审核
     */
    @Lock4j(name= TradeCacheNames.LOCK_WITHDRAW_CASH, keys = "#cashId", expire = 30000, acquireTimeout = 1000)
    public void updateCashUpdate(Long cashId) {
        TradeAccCashVo cashVo = tradeAccCashService.queryById(cashId);
        if (cashVo == null) {
            throw new ServiceException("提现单不存在");
        }
        if (!CashInfStatusEnum.INF_INIT.getCode().equals(cashVo.getInfStatus())) {
            throw new ServiceException(String.format("提现单 %s 不初始状态", cashVo.getCashNo()));
        }
        tradeAccTransService.updateCashCheck(new TradeAccCashBo().setId(cashVo.getId()).setCashNo(cashVo.getCashNo()));
    }
    /*
        提现审核后调用银行接口
     */
    @Lock4j(name= TradeCacheNames.LOCK_WITHDRAW_CASH, keys = "#cashId", expire = 30000, acquireTimeout = 1000)
    public void withdrawCashCheck(Long cashId) {
        TradeAccCashVo cashVo = tradeAccCashService.queryById(cashId);
        if (cashVo == null) {
            throw new ServiceException("提现单不存在");
        }
        if (!CashInfStatusEnum.CHECK.getCode().equals(cashVo.getInfStatus())) {
            throw new ServiceException(String.format("提现单 %s 不是待审核", cashVo.getCashNo()));
        }
        validateCash(cashVo);
        withdrawCash(cashVo, validateOrgBank(cashVo.getOrgCode(), cashVo.getOrgType()));
    }

    @Lock4j(name= TradeCacheNames.LOCK_WITHDRAW_CASH, keys = "#cashId", expire = 30000, acquireTimeout = 1000)
    public void withdrawCashDelete(Long cashId, Long supplierId) {
        TradeAccCashVo cashVo = tradeAccCashService.queryById(cashId);
        if (cashVo == null) {
            throw new ServiceException("提现单不存在");
        }
        if (supplierId != null && !supplierId.equals(cashVo.getOrgId())) {
            throw new ServiceException("提现单不存在");
        }
        if (!CashInfStatusEnum.CHECK.getCode().equals(cashVo.getInfStatus())) {
            throw new ServiceException(String.format("提现单 %s 不是待审核", cashVo.getCashNo()));
        }
        tradeAccTransService.deleteCash(MapstructUtils.convert(cashVo, TradeAccCashBo.class));
    }

    private TradeOrgBankRelaVo validateOrgBank(String orgCode, Integer orgType) {
        TradeOrgBankRelaVo bankVo = tradeOrgRelationService.queryBankByCode(orgCode, orgType);
        if (bankVo == null || !AccountOrgBindEnum.BIND.getCode().equals(bankVo.getStatus())) {
            throw new ServiceException(String.format("%s %s 未绑定银行",
                    AccountOrgTypeEnum.getDescByCode(orgType), orgCode));
        }
        return bankVo;
    }

    private void validateCash(TradeAccCashVo cashVo) {
        if (CashInfStatusEnum.SUCCESS.getCode().equals(cashVo.getInfStatus())) {
            throw new ServiceException(String.format("提现单 %s 已完成提现", cashVo.getCashNo()));
        }
        if (cashVo.getInfTime() != null && cashVo.getInfTime().compareTo(DateUtil.offsetSecond(new Date(), -5)) > 0) {
            throw new ServiceException(String.format("提现单 %s 执行中", cashVo.getCashNo()));
        }
        if (cashVo.getAvailCount() == 0) {
            throw new ServiceException(String.format("提现单 %s 未选择结算单", cashVo.getCashNo()));
        }
        if (cashVo.getCashAmt().compareTo(cashVo.getOutFeeAmt()) <= 0) {
            throw new ServiceException(String.format("提现单 %s 金额必须大于 %s 元",
                    cashVo.getCashNo(), cashVo.getOutFeeAmt().toString()));
        }
        if (cashVo.getCashAmt().compareTo(channelsProperties.getWithdrawLimitAmt()) < 0) {
            throw new ServiceException(String.format("提现单 %s 金额必须大于限额 %s 元",
                    cashVo.getCashNo(), channelsProperties.getWithdrawLimitAmt().toString()));
        }
    }

    private void withdrawCash(TradeAccCashVo cashVo, TradeOrgBankRelaVo bankVo) {
        TradeAccCashVo accVo = tradeAccTransService.updateCashCheckByBo(MapstructUtils.convert(cashVo, TradeAccCashBo.class), bankVo);
        try {
            TradeAccCashBo respBo = accountService.withdrawCash(accVo, bankVo);
            tradeAccTransService.updateCashInfData(respBo, accVo);
            BeanUtil.copyProperties(respBo, accVo, Constants.BeanCopyIgnoreNullValue);
            log.keyword(accVo.getCashNo(), "withdrawCash").info("提现接口完成");
        } catch (ServiceException se) {
            tradeAccTransService.updateCashInfFail(
                    AccountOrgTypeEnum.SUPPLIER.getCode().equals(accVo.getOrgType()) ?
                            CashInfStatusEnum.CHECK.getCode() : CashInfStatusEnum.INF_FAIL.getCode(), accVo, se);
            log.keyword(accVo.getCashNo(), "withdrawCash").warn("提现接口异常", se);
            throw se;
        }
    }

    public void withdrawQuery(Long cashId) {
        TradeAccCashVo cashVo = tradeAccCashService.queryById(cashId);
        if (cashVo == null) {
            throw new ServiceException("提现单不存在");
        }
        if (!CashInfStatusEnum.PROCESSING.getCode().equals(cashVo.getInfStatus())) {
            throw new ServiceException(String.format("提现单 %s 状态改变", cashVo.getCashNo()));
        }
        TradeAccCashBo respBo = accountService.withdrawQuery(cashVo);
        tradeAccTransService.updateCashInfData(respBo, cashVo);
        log.keyword(cashVo.getCashNo(), "withdrawQuery").info("提现查询接口完成");
    }

    public void refreshPage(List<TradeOrgBankAmtVo> bankAmtVos) {
        List<TradeOrgBankAmtVo> acctBankAmtVos = bankAmtVos.stream()
                .filter(vo -> !AccountOrgBindEnum.CLOSE.getCode().equals(vo.getStatus())
                        && BankOrgTypeEnum.ACCT_CODE.getCode().equals(vo.getOutOrgType())).toList();
        List<TradeOrgBankAmtVo> orgBankAmtVos = bankAmtVos.stream()
                .filter(vo -> !AccountOrgBindEnum.CLOSE.getCode().equals(vo.getStatus())
                        && !BankOrgTypeEnum.ACCT_CODE.getCode().equals(vo.getOutOrgType())).toList();
        // 1 查询功能子账户
        if (acctBankAmtVos.size() > 0) {
            Map<String, TradeOrgBankAmtBo> boMap = accountService.queryBankAmt();
            for (Map.Entry<String, TradeOrgBankAmtBo> entry : boMap.entrySet()) {
                tradeOrgBankService.updateAmtByBo(entry.getValue());
            }
        }
        // 2 查询普通子账户
        for (TradeOrgBankAmtVo amtVo : orgBankAmtVos) {
            TradeOrgBankAmtBo bo = accountService.queryBankAmt(amtVo.getOutOrgCode(), amtVo.getOutAcctCode());
            if (bo != null) {
                tradeOrgBankService.updateAmtByBo(bo);
            }
        }
    }


    public BigDecimal getSalaExpectedAmt(BigDecimal cashAmt) {
        if (cashAmt.compareTo(channelsProperties.getWithdrawFeeAmt()) <= 0) {
            throw new ServiceException(String.format("提现金额必须大于 %s 元", channelsProperties.getWithdrawFeeAmt()));
        }
        /*
            结算单明细实发佣金×（1+服务费比例），保留两位小数（四舍五入），所有加和后保留两位小数（四舍五入），即充值金额
            以下是单条的计算方法， 可能会出现 transferAmt > deductAmt 金额的情况， 一般会差 1 分
        BigDecimal deductRate = youweiProperties.getCommissionRate().add(BigDecimal.ONE);
        BigDecimal transferAmt = cashAmt.subtract(channelsProperties.getWithdrawFeeAmt()); // 转入有为账户金额
        BigDecimal expectedAmt = transferAmt.divide(deductRate, 2, RoundingMode.HALF_UP); // 用户到账金额
        BigDecimal deductAmt = expectedAmt.multiply(deductRate).setScale(2, RoundingMode.HALF_UP); // 有为扣费金额
        if (deductAmt.compareTo(transferAmt) > 0) {
            expectedAmt = expectedAmt.subtract(new BigDecimal("0.01"));
        }
        */
        return cashAmt.subtract(channelsProperties.getWithdrawFeeAmt());
    }

    @Lock4j(name= TradeCacheNames.LOCK_WITHDRAW_CASH, keys = "'comm_' + #bo.customerId", expire = 30000, acquireTimeout = 1000)
    public TradeCommCashVo commWithdraw(TradePlatformCustQueryBo bo) {
        // 根据余额创建提现单
        TradeCustAccountVo accountVo = tradeCustAccountService.queryAccount(bo.getCustomerId());
        TradeCommBankVo bankVo = tradeCommBankService.queryByCustomerId(bo.getCustomerId());
        if (bankVo == null || bankVo.getInfStatus() != 1) {
            throw new ServiceException("提现前必须先绑银行卡");
        }
        Integer signChannel = bankVo.getSignChannel();
        if (accountVo.getSalaAvailAmt() == null || !commServiceHelper.getService(signChannel).isCashAmtAllow(accountVo.getSalaAvailAmt())) {
            throw new ServiceException(String.format("佣金可提现余额 %s, 金额不足",
                    accountVo.getSalaAvailAmt() == null ? "0" : accountVo.getSalaAvailAmt()));
        }

        String openId = bankVo.getOpenId();
        TradeCommCashBo cashBo = new TradeCommCashBo().setCashType(AccountTransTypeEnum.CH.getCode())
                .setCashAmt(accountVo.getCommAvailAmt()).setSalaryAmt(accountVo.getSalaAvailAmt())
                .setOutFeeAmt(commServiceHelper.getService(signChannel).getOutFeeAmt()) // SignChannelEnum.YZH.eq(signChannel) ? BigDecimal.ZERO : channelsProperties.getWithdrawFeeAmt()
                .setCustomerId(bo.getCustomerId()).setCustomerCode(bankVo.getCustomerCode())
                .setSignChannel(signChannel).setOpenId(openId);
        //  微信提现 跳过平安接口调用
        cashBo.setPinganWithdrawSkip(SignChannelEnum.YZH.eq(signChannel));
        TradeCommCashVo cashVo = tradeCommCashService.insertByBo(cashBo, accountVo.getCommAvailDate());
        return commWithdrawCheck(cashVo.getId(), cashVo.getCustomerId());
    }

    @Lock4j(name= TradeCacheNames.LOCK_WITHDRAW_CASH, keys = "'commId_' + #cashId", expire = 30000, acquireTimeout = 1000)
    public TradeCommCashVo commWithdrawCheck(Long cashId, Long customerId) {
        TradeCommCashVo cashVo = tradeCommCashService.queryById(cashId);
        if (cashVo == null || !CommCashTypeEnum.CASH.getCode().equals(cashVo.getCashType())) {
            throw new ServiceException("提现单不存在");
        }
        if (customerId != null && !customerId.equals(cashVo.getCustomerId())) {
            throw new ServiceException("提现单不存在");
        }
        if (CashInfStatusEnum.CHECK.getCode().equals(cashVo.getInfStatus())
            || CashInfStatusEnum.FAIL.getCode().equals(cashVo.getInfStatus())) {
            // 2 执行平安提现
            TradeOrgBankRelaVo commOrgVo = tradeOrgRelationService.queryBankByCode(
                    commServiceHelper.getCommAccOrgCode(), AccountOrgTypeEnum.REGION_WH.getCode());
            cashVo = tradeCommCashService.updateCheckByBo(MapstructUtils.convert(cashVo, TradeCommCashBo.class), commOrgVo);
            try {
                TradeAccCashBo accCashBo = accountService.withdrawCash(new TradeAccCashVo().setId(cashVo.getId()).setCashNo(cashVo.getCashNo())
                        .setCashAmt(cashVo.getCashAmt()).setOutFeeAmt(cashVo.getOutFeeAmt()).setSplitNo(cashVo.getSplitNo()), commOrgVo);
                cashVo = tradeCommCashService.updateInfData(new TradeCommCashBo().setId(accCashBo.getId()).setOutTradeNo(accCashBo.getOutTradeNo())
                        .setInfStatus(accCashBo.getInfStatus()).setInfReason(accCashBo.getInfReason()), cashVo);
                log.keyword(cashVo.getCashNo(), "commWithdrawCheck").info("提现接口完成");
            } catch (ServiceException se) {
                tradeCommCashService.updateInfFail(cashVo, se);
                log.keyword(cashVo.getCashNo(), "commWithdrawCheck").warn("提现接口异常", se);
                throw se;
            }
        }
        if (CashInfStatusEnum.SUCCESS.getCode().equals(cashVo.getInfStatus())) {
            if (CashInfStatusEnum.CHECK.getCode().equals(cashVo.getYwInfStatus())
                    || CashInfStatusEnum.FAIL.getCode().equals(cashVo.getYwInfStatus())) {
                TradeCommBankVo bankVo = tradeCommBankService.queryByCustomerId(cashVo.getCustomerId());
                cashVo = tradeCommCashService.updateCommCheckByBo(MapstructUtils.convert(cashVo, TradeCommCashBo.class));
                try {
                    if (bankVo == null || bankVo.getInfStatus() != 1) {
                        throw new ServiceException("银行卡未绑定");
                    }

                    TradeCommCashBo cashBo = commServiceHelper.getService(bankVo.getSignChannel()).wagePay(cashVo, bankVo);
//                    if (YNStatusEnum.DISABLE.getCode().equals(cashVo.getYwOrderStatus())) {
//                        youweiCommService.wagePayApply(cashVo, bankVo);
//                    } else {
//                        if (cashVo.getBankAccount().equals(bankVo.getBankAccount())) {
//                            throw new ServiceException("原银行卡转账失败，请更换新的银行卡");
//                        }
//                        youweiCommService.wagePayReApply(cashVo, bankVo);
//                    }
//                    TradeCommCashBo cashBo = new TradeCommCashBo();
//                    cashBo.setId(cashVo.getId());
//                    cashBo.setYwInfStatus(cashVo.getYwInfStatus());
//                    cashBo.setPersonName(bankVo.getPersonName());
//                    cashBo.setBankAccount(bankVo.getBankAccount());
//                    cashBo.setBankBranch(bankVo.getBankBranch());

                    cashVo = tradeCommCashService.updateCommInfData(cashBo, cashVo);
                    log.keyword(cashVo.getCashNo(), "commWithdrawCheck").info("发薪接口完成");
                } catch (ServiceException se) {
                    tradeCommCashService.updateCommInfFail(cashVo, se);
                    log.keyword(cashVo.getCashNo(), "commWithdrawCheck").warn("发薪接口异常", se);
                    throw se;
                }
            }
        }
        return cashVo;
    }

    @Lock4j(name= TradeCacheNames.LOCK_WITHDRAW_CASH, keys = "'commId_' + #cashId", expire = 30000, acquireTimeout = 1000)
    public TradeCommCashVo commWithdrawQuery(Long cashId) {
        TradeCommCashVo cashVo = tradeCommCashService.queryById(cashId);
        if (cashVo == null || !CommCashTypeEnum.CASH.getCode().equals(cashVo.getCashType())) {
            throw new ServiceException("提现单不存在");
        }
        if (CashInfStatusEnum.PROCESSING.getCode().equals(cashVo.getInfStatus()) && cashVo.getInfRetries() > 0) {
            // 1 查询平安是否提现成功
            try {
                TradeAccCashBo accCashBo = accountService.withdrawQuery(new TradeAccCashVo().setId(cashVo.getId()).setCashNo(cashVo.getCashNo())
                        .setCashAmt(cashVo.getCashAmt()).setOutFeeAmt(cashVo.getOutFeeAmt()).setSplitNo(cashVo.getSplitNo()));
                if (channelsProperties.isCommWithdrawSkip()) {
                    // 仅测试环境上使用
                    accCashBo.setInfStatus(CashInfStatusEnum.SUCCESS.getCode());
                    accCashBo.setOutTradeNo(cashVo.getCashNo());
                    accCashBo.setCashTime(Convert.toDate(DateUtil.now()));
                    accCashBo.setOutActualAmt(cashVo.getCashAmt().subtract(cashVo.getOutFeeAmt()));
                    accCashBo.setInfReason("平安提现跳过");
                }
                TradeCommCashBo respBo = new TradeCommCashBo().setId(accCashBo.getId()).setOutTradeNo(accCashBo.getOutTradeNo())
                        .setInfStatus(accCashBo.getInfStatus()).setInfReason(accCashBo.getInfReason()).setOutCashTime(accCashBo.getCashTime())
                        .setOutActualAmt(accCashBo.getOutActualAmt());
                cashVo = tradeCommCashService.updateInfData(respBo, cashVo);
                log.keyword(cashVo.getCashNo(), "commWithdrawQuery").info("提现查询接口完成");
            } catch (ServiceException se) {
                log.keyword(cashVo.getCashNo(), "commWithdrawQuery").warn("提现查询接口异常", se);
                throw se;
            }
        }
        if (CashInfStatusEnum.SUCCESS.getCode().equals(cashVo.getInfStatus())) {
            if (CashInfStatusEnum.PROCESSING.getCode().equals(cashVo.getYwInfStatus()) && cashVo.getYwInfRetries() > 0) {
                try {
                    TradeCommCashBo respBo = commServiceHelper.getService(cashVo.getSignChannel()).wagePayQuery(cashVo);
                    if (respBo == null) {
                        log.keyword(cashVo.getCashNo(), "commWithdrawQuery").warn("发薪查询接口完成, 发薪单未查到");
                    } else {
                        cashVo = tradeCommCashService.updateCommInfData(respBo, cashVo);
                        log.keyword(cashVo.getCashNo(), "commWithdrawQuery").info("发薪查询接口完成");
                    }
                } catch (ServiceException se) {
                    log.keyword(cashVo.getCashNo(), "commWithdrawQuery").warn("发薪查询接口异常", se);
                    throw se;
                }
            }
            if (CashInfStatusEnum.CHECK.getCode().equals(cashVo.getYwInfStatus())) {
                cashVo = commWithdrawCheck(cashId, cashVo.getCustomerId());
            }
        }
        return cashVo;
    }

    @Lock4j(name= TradeCacheNames.LOCK_WITHDRAW_CASH, keys = "'commId_' + #cashId", expire = 30000, acquireTimeout = 1000)
    public TradeCommCashVo commWithdrawReCheck(Long cashId, BigDecimal salaryAmt) {
        TradeCommCashVo cashVo = tradeCommCashService.queryById(cashId);
        if (cashVo == null || !CommCashTypeEnum.CASH.getCode().equals(cashVo.getCashType())) {
            throw new ServiceException("提现单不存在");
        }
        if (!CashInfStatusEnum.SUCCESS.getCode().equals(cashVo.getInfStatus())
                || !CashInfStatusEnum.FAIL.getCode().equals(cashVo.getYwInfStatus())
                || !YNStatusEnum.ENABLE.getCode().equals(cashVo.getYwOrderStatus())) {
            throw new ServiceException("提现单状态错误");
        }
        try {
            youweiCommService.wagePayCancel(cashVo);
            TradeCommCashBo respBo = new TradeCommCashBo().setId(cashVo.getId()).setSalaryAmt(salaryAmt)
                    .setYwInfStatus(CashInfStatusEnum.FAIL.getCode()).setYwOrderStatus(YNStatusEnum.DISABLE.getCode());
            cashVo = tradeCommCashService.updateCommInfData(respBo, cashVo);
            log.keyword(cashVo.getCashNo(), "commWithdrawReCheck").info("提现取消接口完成");
        } catch (ServiceException se) {
            log.keyword(cashVo.getCashNo(), "commWithdrawReCheck").warn("提现取消接口异常", se);
            throw se;
        }
        return commWithdrawCheck(cashId, cashVo.getCustomerId());
    }

    public void commWithdrawExpire(LocalDate date) {
        LocalDate expireDate = date != null ? date : LocalDate.now().plusDays(youweiProperties.getCommExpireDays());
        TradeCommCashVo cashVo = tradeCommCashService.insertExpireCash(expireDate);
        if (cashVo == null) {
            return;
        }
        TradeOrgBankRelaVo commOrgVo = tradeOrgRelationService.queryBankByCode(
                commServiceHelper.getCommAccOrgCode(), AccountOrgTypeEnum.REGION_WH.getCode());
        try {
            TradeCommCashBo respBo = accountService.commCashExpire(cashVo, commOrgVo);
            tradeCommCashService.updatetExpireInfData(respBo);
            log.keyword("commWithdrawExpire").info("佣金过期接口完成");
        } catch (ServiceException se) {
            log.keyword("commWithdrawExpire").warn("佣金过期接口异常", se);
            throw se;
        }
    }

}