package cn.xianlink.trade.channel.pingancloud;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.xianlink.common.api.enums.trade.PayInfStatusEnum;
import cn.xianlink.common.api.enums.trade.RefundInfStatusEnum;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.trade.api.domain.bo.OrderPayBo;
import cn.xianlink.trade.api.domain.bo.OrderRefundBo;
import cn.xianlink.trade.channel.PaymentService;
import cn.xianlink.trade.channel.pingancloud.constant.PinganCloudClientApiEnum;
import cn.xianlink.trade.channel.pingancloud.constant.PinganCloudPayInfStatusEnum;
import cn.xianlink.trade.channel.pingancloud.constant.PinganCloudRefundInfStatusEnum;
import cn.xianlink.trade.domain.bo.TradePayBo;
import cn.xianlink.trade.domain.bo.TradePayRefundBo;
import cn.xianlink.trade.domain.vo.TradeAccSplitVo;
import cn.xianlink.trade.domain.vo.TradePayRefundVo;
import cn.xianlink.trade.domain.vo.TradePayVo;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.pingan.openbank.api.sdk.common.helper.SdkSignature;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.CustomLog;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 平安云收款对接 -- 微信小程序 --
 *      推送单号都加 orderType 作为前缀
 * <AUTHOR>
 * @date 2024-05-27
 */
@CustomLog
@Component
public class PinganCloudWXPayServiceImpl implements PaymentService {

    private final static String RemarkString = "下单商品数太多，请拆分下单";

    @Resource
    private transient PinganCloudClientService client;

    @Override
    public int payValidate(OrderPayBo bo) {
        if(StringUtils.isEmpty(bo.getPayerId())){
            throw new ServiceException("openId不能为空");
        }
        if(StringUtils.isEmpty(bo.getAppId())){
            throw new ServiceException("wxAppId不能为空");
        }
        if(CollectionUtil.isEmpty(bo.getSplits())){
            throw new ServiceException("分账数据不能为空");
        }
        return client.config.getPayRemarkMaxSplit();
    }


    @Override
    public TradePayBo pay(TradePayVo vo) {
        String orderRemark = getPayRemark(vo.getSplits()).toJSONString();
        if (orderRemark.length() > client.config.getPayRemarkMaxLen()) {
            throw new ServiceException(RemarkString);
        }
        if (client.config.getPayRemarkMaxSplit() > 0 && vo.getSplits().size() > client.config.getPayRemarkMaxSplit()) {
            throw new ServiceException(RemarkString);
        }
        if (client.config.get_paySplitLimitAmts().contains(vo.getPaySplitAmt())) {
            // 是平安测试环境调账 KFEJZB6147 接口的触发金额 ！！
            throw new ServiceException(String.format("%s元是平安异常触发金额，请更换测试金额", vo.getPaySplitAmt().toString()));
        }
        if (vo.getPaySplitAmt().compareTo(BigDecimal.ZERO) == 0) {
            TradePayBo respBo = new TradePayBo();
            respBo.setPayInfStatus(PayInfStatusEnum.SUCCESS.getCode());
            respBo.setOutSuccessTime(Convert.toDate(DateUtil.now()));
            respBo.setCallbackWeixin(new JSONObject()
                    .fluentPut("channel", vo.getChannel())
                    .fluentPut("params", new JSONObject()).toJSONString());
            return respBo.setId(vo.getId());
        } else {
            JSONObject body = new JSONObject();
            body.put("TraderOrderNo", vo.getTradeNo());
            body.put("OrderName", vo.getOrderNo());
            body.put("TranAmt", PinganCloudUtil.yuanToFen(vo.getPaySplitAmt()));
            body.put("OrderSendTime", PinganCloudUtil.timeToStr(vo.getInfTime()));
            body.put("UserSubId", vo.getPayerId());
            body.put("SubTraderPublicId", vo.getAppId());
            body.put("PayModeNo", client.config.getPayModeNo());
            // 订单系统中是 30 分钟
            body.put("PayOverdueTime", PinganCloudUtil.timeToStr(DateUtil.offsetMinute(new Date(), 40)));
            body.put("OrderRemark", orderRemark);
            if (StringUtils.isNotBlank(client.config.getCallbackUrl())) {
                body.put("CallBackNoticeUrl", client.config.getCallbackUrl());
            }
            try {
                // 两种异常
                JSONObject resp = client.post(vo.getOrderNo(), "pay", PinganCloudClientApiEnum.PAY, body);
                // 仅返回要更新的值！！！！！ 新创建对象， 更新数据库
                TradePayBo respBo = new TradePayBo();
                respBo.setId(vo.getId());
                respBo.setInfStatus(PinganCloudPayInfStatusEnum.getInfStatus(resp.getString("OrderStatus")));
                respBo.setPayInfStatus(respBo.getInfStatus());
                respBo.setInfReason("");
                respBo.setOutTradeNo(resp.getString("BankOrderNo"));
                respBo.setOutChannelNo(resp.getString("ChannelOrderNo"));
                respBo.setCallbackWeixin(new JSONObject()
                        .fluentPut("channel", vo.getChannel())
                        .fluentPut("params", new JSONObject()
                                .fluentPut("appId", resp.getString("AppId"))
                                .fluentPut("timeStamp", resp.getString("TimeStamp"))
                                .fluentPut("nonceStr", resp.getString("RandomSting"))
                                .fluentPut("package", resp.getString("ExtendString"))
                                .fluentPut("signType", resp.getString("SignatureMode"))
                                .fluentPut("paySign", resp.getString("Signature"))).toJSONString());
                return respBo;
            } catch (ServiceException se) {
                if (se.getMessage().contains("见证宝商户remark长度超出限制[T6527]")) {
                    // {"Errors":[{"ErrorCode":"T6527","ErrorMessage":"参数不合法：见证宝商户remark长度超出限制"}]
                    se.setMessage(RemarkString);
                }
                throw se;
            }
        }
    }

    private JSONArray getPayRemark(List<TradeAccSplitVo> splitVos) {
        JSONArray arrays = new JSONArray();
        for (TradeAccSplitVo splitVo : splitVos) {
            if (splitVo.getTransAmt().compareTo(BigDecimal.ZERO) > 0) {
                arrays.add(new JSONObject()
                                .fluentPut("PayModel", "1")
                                .fluentPut("SubAccNo", splitVo.getOutAcctCode())
                                .fluentPut("TranFee", "0") // splitVo.getFeeAmt().setScale(2, RoundingMode.HALF_UP).toString()
                                .fluentPut("subamount", splitVo.getTransAmt().setScale(2, RoundingMode.HALF_UP).toString())
                                .fluentPut("suborderId", splitVo.getSplitNo())
                        // .fluentPut("object", splitVo.getRelateNo()) // CZ文件中的备注，为了缩小remark长度暂时注释
                );
            }
        }
        return new JSONArray().fluentAdd(new JSONObject()
                // .fluentPut("version", "2.0")
                .fluentPut("SFJOrdertype", "1")
                .fluentPut("remarktype", "JHS0100000")
                .fluentPut("plantCode", client.config.getMrchCode())
                .fluentPut("oderlist", arrays)
        );
    }
    @Override
    public TradePayBo payQuery(TradePayVo vo) {
        JSONObject body = new JSONObject();
        body.put("TraderOrderNo", vo.getTradeNo());
        body.put("OrderSendTime", PinganCloudUtil.timeToStr(vo.getInfTime()));
        try {
            JSONObject resp = client.post(vo.getOrderNo(), "payQuery", PinganCloudClientApiEnum.PAY_QUERY, body);
            // 仅返回要更新的值！！！！！ 新创建对象， 更新数据库
            return payRespToBo(resp).setId(vo.getId());
        } catch (ServiceException se) {
            if (se.getMessage().contains("[T6510]")) {
                // 订单不存在[T6510] , 返回null
                // 已关闭的订单能查询到 ！
                log.keyword(vo.getOrderNo(), "payQuery").warn("[T6510] 转异常", se);
                return null;
            }
            throw se;
        }
    }

    private TradePayBo payRespToBo(JSONObject resp) {
        TradePayBo respBo = new TradePayBo();
        respBo.setInfStatus(PinganCloudPayInfStatusEnum.getInfStatus(resp.getString("OrderStatus")));
        respBo.setPayInfStatus(respBo.getInfStatus());
        respBo.setInfReason("");
        respBo.setOutTradeNo(resp.getString("BankOrderNo"));
        respBo.setOutChannelNo(resp.getString("ChannelOrderNo"));
        respBo.setOutFeeAmt(PinganCloudUtil.fenToYuan(resp.getString("Fee")));
        respBo.setOutDiscountAmt(PinganCloudUtil.fenToYuan(resp.getString("DiscountAmount")));
        respBo.setOutCashAmt(PinganCloudUtil.fenToYuan(resp.getString("CashAmount")));
        respBo.setOutCouponAmt(PinganCloudUtil.fenToYuan(resp.getString("CouponAmount")));
        respBo.setOutSuccessTime(PinganCloudUtil.timeToDate(resp.getString("PaySuccessTime")));  // query 才有
        // OrderType  PayeeAccNo  PayerAccountNO  PayeeBankNo  PayerAccountBankNo
        return respBo;
    }

    @Override
    public TradePayBo payClose(TradePayVo vo) {
        JSONObject body = new JSONObject();
        body.put("OldMerOrderNo", vo.getTradeNo());
        body.put("OldOrderSendTime", PinganCloudUtil.timeToStr(vo.getInfTime()));
        TradePayBo respBo = new TradePayBo();
        // 仅返回要更新的值！！！！！ 新创建对象， 更新数据库
        try {
            JSONObject resp = client.post(vo.getOrderNo(), "payClose", PinganCloudClientApiEnum.PAY_CLOSE, body);
            respBo.setInfStatus(PinganCloudPayInfStatusEnum.getInfStatus(resp.getString("OldOrderStatus")));
            respBo.setPayInfStatus(respBo.getInfStatus());
            if (PayInfStatusEnum.CANCEL.getCode().equals(respBo.getPayInfStatus())) {
                respBo.setCloseTime(Convert.toDate(DateUtil.now()));
            }
        } catch (ServiceException se) {
            if (se.getMessage().contains("[T6519]") || se.getMessage().contains("[T6535]")) {
                // 原支付订单已关闭,不能申请撤销[T6519], 交易已关闭,订单不能重复关闭 [T6535], 返回关闭状态
                log.keyword(vo.getOrderNo(), "payClose").warn("[T6519] 转异常", se);
                respBo.setInfStatus(PayInfStatusEnum.CANCEL.getCode());
                respBo.setPayInfStatus(respBo.getInfStatus());
                respBo.setCloseTime(Convert.toDate(DateUtil.now()));
            } else {
                throw se;
            }
        }
        respBo.setId(vo.getId());
        respBo.setInfReason("");
        return respBo;
    }

    @Override
    public JSONObject callback(String reqBody, HttpServletRequest request) {
        log.keyword("payCallback").info("平安回调 {}", reqBody);
        /* 1 先验签 2 再转码获取数据
            返回 4 种情况
                1 正常
                    1 验签失败，数据是订单     查询一次
                2 验签成功，数据不是订单    返回null
                3 验签失败                返回异常
         */
        String sign = getSignature(reqBody);
        String body = getSignatureContent(reqBody);
        JSONObject resp = getParamsMap(request);
        String orderType = resp.getString("OrderType");
        String traderOrderNo = resp.getString("TraderOrderNo");
        String orderSendTime = resp.getString("OrderSendTime");
        if (SdkSignature.verifySign(sign, client.config.getPublicKey(), body)) {
            if ("1".equals(orderType)) { // 1支付 2退款 3撤销
                log.keyword(traderOrderNo, "payCallback").info("平安回调验签成功");
                return resp;
            } else {
                log.keyword("payCallback").warn("平安回调其他类型");
                return null;
            }
        } else if ("1".equals(orderType) && StringUtils.isNotBlank(traderOrderNo) && StringUtils.isNotBlank(orderSendTime)) {
            // 解决 PromotionDetail 字段中有空格的情况   （两种情况1 有空格验签失败  2 无空格可以验签）
            log.keyword(traderOrderNo, "payCallback").warn("平安回调验签失败，发起查询");
            TradePayVo payVo = new TradePayVo();
            payVo.setTradeNo(traderOrderNo);
            payVo.setInfTime(PinganCloudUtil.timeToDate(orderSendTime));
            TradePayBo backBo = payQuery(payVo);
            if (backBo != null) {
                return resp;
            }
        }
        throw new ServiceException("平安回调验签失败");
    }

    @Override
    public TradePayBo payCallback(JSONObject resp) {
        String traderOrderNo = resp.getString("TraderOrderNo");
        return payRespToBo(resp).setTradeNo(traderOrderNo);
    }

    private JSONObject getParamsMap(HttpServletRequest request){
        Map<String, Object> map = new HashMap<>();
        for (Map.Entry<String, String[]> entry: request.getParameterMap().entrySet()) {
            map.put(entry.getKey(), entry.getValue().length == 1 ? entry.getValue()[0] : Arrays.asList(entry.getValue()));
        }
        return new JSONObject(map);
    }

    private String getSignature(String str) {
        int index = str.lastIndexOf('=');
        if (index != -1 && index < str.length() - 1) {
            return str.substring(index + 1);
        }
        return "";
    }

    private String getSignatureContent(String str) {
        int index = str.lastIndexOf('&');
        if (index != -1 && index < str.length() - 1) {
            return str.substring(0, index);
        }
        return "";
    }

    @Override
    public int refundValidate(OrderRefundBo bo, TradePayVo payVo) {
        if(CollectionUtil.isEmpty(bo.getSplits())){
            throw new ServiceException("分账数据不能为空");
        }
        return client.config.getRefundRemarkMaxSplit();
    }

    @Override
    public TradePayRefundBo refund(TradePayRefundVo vo) {
        /*
            T6510   原支付订单不存在
            T6514   可退款余额不足
            T6518   交易失败，请重新更换商户订单号重发 交易失败，请重新更换商户订单号重发
            T6534   退款订单号已存在
            T6520   原支付订单未支付成功
            T9100   交易通讯超时，请发起查询交易
         */
        if (client.config.getRefundRemarkMaxSplit() > 0 && vo.getSplits().size() > client.config.getRefundRemarkMaxSplit()) {
            throw new ServiceException("退单商品数太多，请拆分下单");
        }
        if (vo.getRefundSplitAmt().compareTo(BigDecimal.ZERO) == 0) {
            TradePayRefundBo respBo = new TradePayRefundBo();
            respBo.setRefundInfStatus(RefundInfStatusEnum.SUCCESS.getCode());
            respBo.setOutSuccessTime(Convert.toDate(DateUtil.now()));
            return respBo.setId(vo.getId());
        } else {
            JSONObject body = new JSONObject();
            body.put("OldMerOrderNo", vo.getTradeNo());
            body.put("OldOrderSendTime", PinganCloudUtil.timeToStr(vo.getOrderInfTime()));
            body.put("ReturnOrderNo", vo.getTradeRefundNo());
            body.put("ReturnOrderSendTime", PinganCloudUtil.timeToStr(vo.getInfTime()));
            body.put("ReturnAmt", PinganCloudUtil.yuanToFen(vo.getRefundSplitAmt()));
            body.put("RefundRemark", getRefundRemark(vo.getSplits()).toJSONString());
            try {
                // 两种异常
                JSONObject resp = client.post(vo.getRefundNo(), "refund", PinganCloudClientApiEnum.PAY_REFUND, body);
                // 仅返回要更新的值！！！！！ 新创建对象， 更新数据库
                TradePayRefundBo respBo = new TradePayRefundBo();
                respBo.setId(vo.getId());
                refundRespToBo(respBo, resp);
                return respBo;
            } catch (ServiceException se) {
            /*
            if (se.getMessage().contains("[T9100]")) {
                // 交易通讯超时,请发起查询交易[T9100], 进行中可以使用 job 补充查询处理
                log.keyword(vo.getRefundNo(), "refundQuery").warn("[T9100] 转异常", se);
                TradePayRefundBo respBo = new TradePayRefundBo();
                respBo.setId(vo.getId());
                respBo.setInfStatus(RefundInfStatusEnum.PROCESSING.getCode());
                respBo.setRefundInfStatus(respBo.getInfStatus());
                respBo.setInfReason(se.getMessage());
                return respBo;
            }*/
                if (se.getMessage().contains("见证宝商户remark长度超出限制[T6527]")) {
                    // {"Errors":[{"ErrorCode":"T6527","ErrorMessage":"参数不合法：见证宝商户remark长度超出限制"}]
                    se.setMessage("退单商品数太多，请拆分下单");
                }
                throw se;
            }
        }
    }
    private JSONArray getRefundRemark(List<TradeAccSplitVo> splitVos) {
        JSONArray arrays = new JSONArray();
        for (TradeAccSplitVo splitVo : splitVos) {
            if (splitVo.getTransAmt().compareTo(BigDecimal.ZERO) > 0) {
                arrays.add(new JSONObject()
                                .fluentPut("refundModel", "1")
                                .fluentPut("SubAccNo", splitVo.getOutAcctCode())
                                .fluentPut("RefundTranFee", "0.00") // splitVo.getFeeAmt().setScale(2, RoundingMode.HALF_UP).toString()
                                .fluentPut("subrefundamt", splitVo.getTransAmt().setScale(2, RoundingMode.HALF_UP).toString())
                                .fluentPut("suborderId", splitVo.getSplitRelNo())
                                .fluentPut("subrefundId", splitVo.getSplitNo())
                        // .fluentPut("object", splitVo.getSplitRelNo()) // TX文件中的备注，为了缩小remark长度暂时注释
                );
            }
        }
        return new JSONArray().fluentAdd(new JSONObject()
                // .fluentPut("version", "2.0")
                .fluentPut("SFJOrdertype", "1")
                .fluentPut("remarktype", "JHT0100000")
                .fluentPut("plantCode", client.config.getMrchCode())
                .fluentPut("oderlist", arrays)
        );
    }

    private void refundRespToBo(TradePayRefundBo respBo, JSONObject resp) {
        respBo.setInfStatus(PinganCloudRefundInfStatusEnum.getInfStatus(resp.getString("ReturnOrderStatus")));
        respBo.setRefundInfStatus(respBo.getInfStatus());
        respBo.setInfReason("");
        respBo.setOutTradeNo(resp.getString("BankOrderNo"));
        respBo.setOutChannelNo(resp.getString("ChannelOrderNo"));
        respBo.setOutDiscountAmt(PinganCloudUtil.fenToYuan(resp.getString("DiscountAmount")));
        respBo.setOutCashAmt(PinganCloudUtil.fenToYuan(resp.getString("CashAmount")));
        respBo.setOutCouponAmt(PinganCloudUtil.fenToYuan(resp.getString("CouponAmount")));
        respBo.setOutSuccessTime(PinganCloudUtil.timeToDate(resp.getString("ReturnSuccessTime")));  // refundQuery 才有
        if (respBo.getOutSuccessTime() == null && RefundInfStatusEnum.SUCCESS.getCode().equals(respBo.getInfStatus())) {
            respBo.setOutSuccessTime(Convert.toDate(DateUtil.now()));
        }
        // RechargeRefundFlag
    }
    @Override
    public TradePayRefundBo refundQuery(TradePayRefundVo vo) {
        JSONObject body = new JSONObject();
        body.put("ReturnOrderNo", vo.getTradeRefundNo());
        body.put("ReturnOrderSendTime", PinganCloudUtil.timeToStr(vo.getInfTime()));
        try {
            // 查不到
            JSONObject resp = client.post(vo.getRefundNo(), "refundQuery", PinganCloudClientApiEnum.PAY_REFUND_QUERY, body);
            // 仅返回要更新的值！！！！！ 新创建对象， 更新数据库
            TradePayRefundBo respBo = new TradePayRefundBo();
            respBo.setId(vo.getId());
            refundRespToBo(respBo, resp);
            return respBo;
        } catch (ServiceException se) {
            if (se.getMessage().contains("[T6510]")) {
                // 订单不存在[T6510] , 返回null
                log.keyword(vo.getRefundNo(), "refundQuery").warn("[T6510] 转异常", se);
                return null;
            }
            throw se;
        }
    }

}
