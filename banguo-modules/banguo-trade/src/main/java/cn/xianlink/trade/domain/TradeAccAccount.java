package cn.xianlink.trade.domain;

import cn.xianlink.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;

import java.io.Serial;

/**
 * 账务总对象 trade_acc_account
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("trade_acc_account")
public class TradeAccAccount extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id主键
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 账务机构id
     */
    private Long acctOrgId;
    /**
     * 0 总仓 1 城市仓 2 供应商
     */
    private Integer orgType;

    /**
     * 分账机构id
     */
    private Long orgId;

    /**
     * 分账机构代码
     */
    private String orgCode;
    /**
     * 部门id
     */
    private Long deptId;
    /**
     *
     */
    private String outOrgCode;
    /**
     * 子账号
     */
    private String outAcctCode;
    /**
     * 昨日待结算金额
     */
    private BigDecimal ydaFreezeAmt;

    /**
     * 昨日可提现金额
     */
    private BigDecimal ydaAvailAmt;
    /**
     * 昨日可用金额
     */
    private BigDecimal ydaAcctAvailAmt;
    /**
     * 待结算金额
     */
    private BigDecimal freezeAmt;

    /**
     * 可提现金额
     */
    private BigDecimal availAmt;

    /**
     * 待结算划转入金额
     */
    private BigDecimal freezeInAmt;

    /**
     * 待结算划转出金额
     */
    private BigDecimal freezeOutAmt;

    /**
     * 待结算订单金额
     */
    private BigDecimal freezePayAmt;

    /**
     * 待结算退款总金额
     */
    private BigDecimal freezeRefundAmt;

    /**
     * 退货退款
     */
    private BigDecimal freezeRefundAmt1;

    /**
     * 差额退款
     */
    private BigDecimal freezeRefundAmt2;

    /**
     * 报损退款
     */
    private BigDecimal freezeRefundAmt3;

    /**
     * 未使用
     */
    private BigDecimal freezeRefundAmt4;

    /**
     * 可提现划转入金额
     */
    private BigDecimal availInAmt;

    /**
     * 可提现划转出金额
     */
    private BigDecimal availOutAmt;

    /**
     * 可提现订单金额
     */
    private BigDecimal availPayAmt;

    /**
     * 可提现退款总金额
     */
    private BigDecimal availRefundAmt;

    /**
     * 退货退款
     */
    private BigDecimal availRefundAmt1;

    /**
     * 差额退款
     */
    private BigDecimal availRefundAmt2;

    /**
     * 报损退款
     */
    private BigDecimal availRefundAmt3;

    /**
     * 未使用
     */
    private BigDecimal availRefundAmt4;
    /**
     * 已提金额
     */
    private BigDecimal cashAmt;
    /**
     * 已提现划转入金额
     */
    private BigDecimal cashInAmt;

    /**
     * 已提现划转出金额
     */
    private BigDecimal cashOutAmt;

    /**
     * 已提现订单金额
     */
    private BigDecimal cashPayAmt;

    /**
     * 已提现退款总金额
     */
    private BigDecimal cashRefundAmt;

    /**
     * 已提退货退款
     */
    private BigDecimal cashRefundAmt1;

    /**
     * 已提差额退款
     */
    private BigDecimal cashRefundAmt2;

    /**
     * 已提报损退款
     */
    private BigDecimal cashRefundAmt3;

    /**
     * 已提未使用
     */
    private BigDecimal cashRefundAmt4;
    /**
     * 提现待审核金额
     */
    private BigDecimal checkAmt;
    /**
     * 提现待审核划转入金额
     */
    private BigDecimal checkInAmt;

    /**
     * 提现待审核划转出金额
     */
    private BigDecimal checkOutAmt;

    /**
     * 提现待审核订单金额
     */
    private BigDecimal checkPayAmt;

    /**
     * 提现待审核退款总金额
     */
    private BigDecimal checkRefundAmt;

    /**
     * 提现待审核退货退款
     */
    private BigDecimal checkRefundAmt1;

    /**
     * 提现待审核差额退款
     */
    private BigDecimal checkRefundAmt2;

    /**
     * 提现待审核报损退款
     */
    private BigDecimal checkRefundAmt3;

    /**
     * 提现待审核未使用
     */
    private BigDecimal checkRefundAmt4;
    /**
     * 佣金待结算余额
     */
    private BigDecimal freezeCommAmt;
    /**
     * 佣金余额
     */
    private BigDecimal availCommAmt;
    /**
     * 佣金提现金额
     */
    private BigDecimal cashCommAmt;
    /**
     * 佣金提现待审核金额
     */
    private BigDecimal checkCommAmt;


    /**
     * 删除标志（0代表存在 存入这条数据的主键代表删除）
     */
    @TableLogic(delval = "id")
    private Long delFlag;


}
