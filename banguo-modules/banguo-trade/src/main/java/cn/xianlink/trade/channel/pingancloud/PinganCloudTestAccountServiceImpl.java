package cn.xianlink.trade.channel.pingancloud;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.xianlink.common.api.enums.trade.*;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.trade.domain.bo.TradeAccCashBo;
import cn.xianlink.trade.domain.bo.TradeAccTransBo;
import cn.xianlink.trade.domain.bo.TradeAccTransferBo;
import cn.xianlink.trade.domain.bo.comm.TradeCommCashBo;
import cn.xianlink.trade.domain.vo.*;
import cn.xianlink.trade.domain.vo.comm.TradeCommCashVo;
import cn.xianlink.trade.domain.vo.org.TradeOrgBankRelaVo;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * 平安见证宝对接
 *
 * <AUTHOR>
 * @date 2024-05-27
*/
@Service
@ConditionalOnProperty(prefix = "channels", name = "debug-local", havingValue = "true", matchIfMissing = false)
public class PinganCloudTestAccountServiceImpl extends PinganCloudAccountServiceImpl {

    @Override
    public TradeAccTransferBo transferOut(TradeAccTransferVo vo) {
        return transferIn(vo);
    }

    @Override
    public TradeAccTransferBo transferOutRefund(TradeAccTransferVo vo) {
        return transferIn(vo);
    }

    @Override
    public TradeAccTransferBo transferIn(TradeAccTransferVo vo) {
        TradeAccTransferBo respBo = new TradeAccTransferBo();
        respBo.setId(vo.getId());
        respBo.setOutTradeNo("********");
        respBo.setInfStatus(TransferInfStatusEnum.SUCCESS.getCode());
        respBo.setInfReason("");
        respBo.setOutSuccessTime(Convert.toDate(DateUtil.now()));
        return respBo;
    }

    @Override
    public TradeAccTransferBo transferInRefund(TradeAccTransferVo vo) {
        return transferIn(vo);
    }

    /*
        营销转账：  营销子账户 -> 商户子账户
     */
    @Override
    public TradeAccTransBo discountPay(TradeAccTransVo vo) {
        return discountRefund(vo);
    }

    @Override
    public TradeAccTransBo discountRefund(TradeAccTransVo vo) {
        TradeAccTransBo respBo = new TradeAccTransBo();
        respBo.setOutTradeNo("********");
        respBo.setInfStatus(DiscountInfStatusEnum.SUCCESS.getCode());
        respBo.setInfReason("");
        return respBo;
    }

    @Override
    public TradeAccCashBo withdrawCash(TradeAccCashVo vo, TradeOrgBankRelaVo bvo) {
        if (vo.getId() == null || StringUtils.isEmpty(vo.getCashNo()) || StringUtils.isEmpty(vo.getSplitNo())
                || StringUtils.isEmpty(bvo.getOutOrgCode()) || StringUtils.isEmpty(bvo.getOutAcctCode())
                || StringUtils.isEmpty(bvo.getReprName()) || StringUtils.isEmpty(bvo.getReprGlobalId())
                || StringUtils.isEmpty(bvo.getBankAccount()) || StringUtils.isEmpty(bvo.getBankBranchName())
                || vo.getCashAmt() == null || vo.getCashAmt().compareTo(BigDecimal.ZERO) == 0
                || vo.getOutFeeAmt() == null) {
            throw new ServiceException(String.format("提现参数不合法，%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s",
                    vo.getId(), vo.getCashNo(), vo.getSplitNo(), bvo.getOutOrgCode(), bvo.getOutAcctCode(),
                    bvo.getReprName(), bvo.getReprGlobalId(), bvo.getBankAccount(), bvo.getBankBranchName(),
                    vo.getCashAmt(), vo.getOutFeeAmt()));
        }
        TradeAccCashBo respBo = new TradeAccCashBo();
        respBo.setId(vo.getId());
        respBo.setOutTradeNo("********");
        respBo.setInfStatus(CashInfStatusEnum.PROCESSING.getCode());
        respBo.setInfReason("");
        return respBo;
    }

    @Override
    public TradeAccCashBo withdrawQuery(TradeAccCashVo vo) {
        if (vo.getId() == null || StringUtils.isEmpty(vo.getCashNo()) || StringUtils.isEmpty(vo.getSplitNo())
                || vo.getCashAmt() == null || vo.getCashAmt().compareTo(BigDecimal.ZERO) == 0
                || vo.getOutFeeAmt() == null) {
            throw new ServiceException(String.format("提现查询参数不合法，%s,%s,%s,%s,%s",
                    vo.getId(), vo.getCashNo(), vo.getSplitNo(), vo.getCashAmt(), vo.getOutFeeAmt()
            ));
        }
        TradeAccCashBo respBo = new TradeAccCashBo();
        respBo.setId(vo.getId());
        respBo.setInfStatus(CashInfStatusEnum.PROCESSING.getCode());  // 测试时更换状态
        respBo.setOutTradeNo("********");
        if (CashInfStatusEnum.SUCCESS.getCode().equals(respBo.getInfStatus())) {
            respBo.setCashTime(Convert.toDate(DateUtil.now()));
            respBo.setStatus(AccountStatusEnum.CASH.getCode());
            respBo.setOutActualAmt(vo.getCashAmt().subtract(vo.getOutFeeAmt()));
            respBo.setInfReason("");
        } else if (CashInfStatusEnum.FAIL.getCode().equals(respBo.getInfStatus())) {
            respBo.setCashTime(Convert.toDate(DateUtil.now()));
            respBo.setOutActualAmt(BigDecimal.ZERO);
            respBo.setInfReason("FailMsg");
        }
        return respBo;
    }

    @Override
    public TradeCommCashBo commCashExpire(TradeCommCashVo vo, TradeOrgBankRelaVo bvo) {
        if (StringUtils.isEmpty(vo.getCustomerCode()) || StringUtils.isEmpty(vo.getCashNo()) || StringUtils.isEmpty(vo.getSplitNo())
                || StringUtils.isEmpty(bvo.getOutOrgCode()) || StringUtils.isEmpty(bvo.getOutAcctCode())
                || vo.getCashAmt() == null || vo.getCashAmt().compareTo(BigDecimal.ZERO) == 0) {
            throw new ServiceException(String.format("提现过期参数不合法，%s,%s,%s,%s,%s,%s",
                    vo.getCustomerCode(), vo.getCashNo(), vo.getSplitNo(), bvo.getOutOrgCode(),
                    bvo.getOutAcctCode(), vo.getCashAmt()));
        }
        TradeCommCashBo respBo = new TradeCommCashBo();
        respBo.setOutTradeNo("********");
        respBo.setInfStatus(CashInfStatusEnum.SUCCESS.getCode());
        respBo.setInfReason("");
        respBo.setOutCashTime(Convert.toDate(DateUtil.now()));
        respBo.setCashTime(respBo.getOutCashTime());
        respBo.setOutActualAmt(vo.getCashAmt());
        respBo.setCustomerCode(vo.getCustomerCode());
        return respBo;
    }
}
