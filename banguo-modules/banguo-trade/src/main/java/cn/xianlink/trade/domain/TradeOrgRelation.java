package cn.xianlink.trade.domain;

import cn.xianlink.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.time.LocalDate;

/**
 * 机构子账户绑定对象 trade_org_relation
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("trade_org_relation")
public class TradeOrgRelation extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 0 总仓 1 城市仓 2 供应商 3 门店
     */
    private Integer orgType;

    /**
     * 分账机构id
     */
    private Long orgId;

    /**
     * 分账机构代码 对外代码是 org_code+"_"+org_type
     */
    private String orgCode;
        /**
     * 分账机构名称（企业名称）
     */
    private String orgName;
    /**
     * 分账机构子账户
     */
    private String outOrgCode;
    /**
     * 分账机构子账户
     */
    private String outAcctCode;
    /**
     * 账户属性  SH 收款账户，00 付款账户
     */
    private String outOrgProperty;
    /**
     * 账单日期
     */
    private LocalDate accountDate;
    /**
     * 删除标志（0代表存在 存入这条数据的主键代表删除）
     */
    @TableLogic(delval = "id")
    private Long delFlag;

}
