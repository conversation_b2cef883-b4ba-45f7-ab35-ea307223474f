package cn.xianlink.trade.channel.pingancloud;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.xianlink.common.api.enums.trade.*;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.DateUtils;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.trade.channel.pingancloud.constant.PinganCloudClientApiEnum;
import cn.xianlink.trade.domain.bo.*;
import cn.xianlink.trade.domain.vo.*;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import jakarta.annotation.Resource;
import lombok.CustomLog;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 平安见证宝对接
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@CustomLog
@Component
public class PinganCloudPayRecvService {

    @Resource
    private transient PinganCloudClientService client;

    public TradePayRecvBo payRecv(TradePayRecvVo vo) {
        String orderNo = StringUtils.isBlank(vo.getOrderNo()) ? vo.getRecvNo() : vo.getOrderNo();
        try {
            JSONObject body = new JSONObject();
            body.put("FunctionFlag", "1");
            body.put("MemberSubAcctNo", vo.getCustomerAcctCode());
            body.put("TranNetMemberCode", vo.getCustomerOutCode());
            // body.put("OldThirdSeqNo", vo.getSplitNo());   查询时使用创建的 CnsmrSeqNo
            body.put("OrderNo", vo.getRecvNo());
            // body.put("SerialNo", "123456");
            body.put("CreateType", "01");
            body.put("RecvAmt", vo.getRecvAmt().toString());
            body.put("InAcctNum", "1");
            body.put("RecvCodeInvalidDate", PinganCloudUtil.dateToStr(LocalDate.now().plusDays(2)));
            body.put("Remark", String.format("客户 %s", vo.getCustomerCode()));
            body.put("CnsmrSeqNo", vo.getRecvNo()); // 不单独再生成一个单号了   vo.getSplitNo()
            JSONObject resp = client.post(orderNo, "payRecv", PinganCloudClientApiEnum.LARGE_PAY, body);
            body = new JSONObject();
            body.put("FunctionFlag", "2");
            body.put("OldThirdSeqNo", vo.getRecvNo());
            for (int i = 1; StringUtils.isEmpty(resp.getString("OrderRecvCode")) && i <= 20; i++) {
                Thread.sleep(i == 1 ? 200L : 100L);
                try {
                    // 一次查询， 发起到返回 200 毫秒
                    resp = client.post(orderNo, "payRecv", PinganCloudClientApiEnum.LARGE_PAY, body);
                } catch (ServiceException se) {
                    if (se.getMessage().contains("发送到AFASVC失败")) {
                        log.keyword(orderNo, "pay").warn("发送到AFASVC失败", se);
                    } else {
                        throw se;
                    }
                }
            }
            if (StringUtils.isEmpty(resp.getString("OrderRecvCode"))) {
                throw new ServiceException("收款单生成异常，请稍后重试");
            }
            TradePayRecvBo respBo = new TradePayRecvBo();
            respBo.setId(vo.getId());
            respBo.setRecvNo(vo.getRecvNo()); // 大额支付时使用
            respBo.setInfStatus(PayRecvInfStatusEnum.UNPAID.getCode());
            respBo.setOutRecvCode(resp.getString("OrderRecvCode"));
            respBo.setOutRecvAccName(resp.getString("OrderRecvCodeAcctName"));
            TradePayBo bo = new TradePayBo();
            respBo.setTradePayBo(bo);
            bo.setTradePayRecvBo(respBo);
            bo.setPayInfStatus(respBo.getInfStatus());
            bo.setOutTradeNo(respBo.getOutRecvCode());
            bo.setCallbackWeixin(new JSONObject()
                    .fluentPut("channel", PayChannelEnum.PAY_PINGAN_CLOUD_LARGE.getCode())
                    .fluentPut("params", new JSONObject()
                            // 转账时：  户名：收款编码户名   账户：收款编码
                            .fluentPut("bankAccount", respBo.getOutRecvCode())
                            .fluentPut("bankAccName", respBo.getOutRecvAccName())
                            .fluentPut("bankName", "平安银行深圳分行")
                            .fluentPut("recvAmt", vo.getRecvAmt())).toJSONString());
            return respBo;
        } catch (InterruptedException ie) {
            throw new ServiceException(ie);
        }
    }

    public TradePayRecvBo payRecvQuery(TradePayRecvVo vo) {
        String orderNo = StringUtils.isBlank(vo.getOrderNo()) ? vo.getRecvNo() : vo.getOrderNo();
        JSONObject body = new JSONObject();
        body.put("FunctionFlag", "2"); // 1 关闭    2 查询
        body.put("MemberSubAcctNo", vo.getCustomerAcctCode());
        body.put("TranNetMemberCode", vo.getCustomerOutCode());
        body.put("OrderNo", vo.getRecvNo());
        body.put("OrderRecvCode", vo.getOutRecvCode());
        try {
            // 1 基于6277关闭查询， 确定当前编号的状态
            JSONObject resp = client.post(orderNo, "payRecvQuery", PinganCloudClientApiEnum.LARGE_PAY_CLOSE, body);
            TradePayRecvBo respBo = new TradePayRecvBo();
            respBo.setId(vo.getId());
            respBo.setRecvNo(vo.getRecvNo()); // 大额支付时使用
            respBo.setInfStatus(PayRecvInfStatusEnum.UNPAID.getCode());
            if (resp.getString("CancelStatus").equals("2")) {
                if (!resp.getString("CancellType").equals("3")) {
                    respBo.setInfStatus(PayRecvInfStatusEnum.CANCEL.getCode());
                    respBo.setCloseTime(Convert.toDate(DateUtil.now()));
                } else {
                    paySuccessResp(respBo, vo);
                }
            }
            return payRespToBo(respBo, vo);
        } catch (ServiceException se) {
            if (se.getMessage().contains("[E10117]")) {
                // 订单收款子编码不存在[E10117]
                log.keyword(orderNo, "payRecvQuery").warn("[E10117] 转异常", se);
                return null;
            }
            throw se;
        }
    }

    private void paySuccessResp(TradePayRecvBo respBo, TradePayRecvVo vo) {
        String orderNo = StringUtils.isBlank(vo.getOrderNo()) ? vo.getRecvNo() : vo.getOrderNo();
        // 2 基于6279查询转账的具体信息
        JSONObject body = new JSONObject();
        body.put("FunctionFlag", "0");
        body.put("PageNum", "1");
        body.put("StartDate", PinganCloudUtil.dateToStr(vo.getInfTime()));
        body.put("EndDate", PinganCloudUtil.dateToStr(DateUtils.addHours(vo.getInfTime(), 48)));
        body.put("ThirdOrderNo", vo.getRecvNo());
        JSONObject resp = client.post(orderNo, "payRecvQuery", PinganCloudClientApiEnum.LARGE_PAY_QUERY, body);
        JSONArray tranItemArray = resp.getJSONArray("TranItemArray");
        if (tranItemArray.size() > 0) {
            resp = tranItemArray.getJSONObject(0);
            respBo.setInfStatus(PayRecvInfStatusEnum.SUCCESS.getCode());
            respBo.setOutTradeNo(resp.getString("ThirdSeqNo"));
            respBo.setInAccount(resp.getString("InAcctNo"));
            respBo.setInAccName(resp.getString("InAcctName"));
            respBo.setInBankName(resp.getString("BankName"));
            respBo.setInBankEicon(resp.getString("BankBranchId"));
            // 3 基于6110查询转账时间
            body = new JSONObject();
            body.put("FunctionFlag", "4");
            body.put("TranNetSeqNo", respBo.getOutTradeNo());
            resp = client.post(orderNo, "payRecvQuery", PinganCloudClientApiEnum.ACC_TRADE_QUERY, body);
            respBo.setOutCashAmt(PinganCloudUtil.yuanToYuan(resp.getString("TranAmt")));
            respBo.setOutSuccessTime(PinganCloudUtil.timeToDate(resp.getString("TranDate") + resp.getString("TranTime")));
        }
    }
    private TradePayRecvBo payRespToBo(TradePayRecvBo respBo, TradePayRecvVo vo) {
        TradePayBo bo = new TradePayBo();
        bo.setTradePayRecvBo(respBo);
        respBo.setTradePayBo(bo);
        bo.setPayInfStatus(respBo.getInfStatus());
        if (PayRecvInfStatusEnum.SUCCESS.getCode().equals(bo.getPayInfStatus())) {
            bo.setOutChannelNo(respBo.getOutTradeNo());
            bo.setCallbackWeixin(new JSONObject()
                    .fluentPut("channel", PayChannelEnum.PAY_PINGAN_CLOUD_LARGE.getCode())
                    .fluentPut("params", new JSONObject()
                            // 转账时：  户名：收款编码户名   账户：收款编码
                            .fluentPut("bankAccount", vo.getOutRecvCode())
                            .fluentPut("bankAccName", "已收款")
                            .fluentPut("bankName", "平安银行深圳分行")
                            .fluentPut("recvAmt", respBo.getOutCashAmt())
                            .fluentPut("inAccount", respBo.getInAccount())
                            .fluentPut("inAccName", respBo.getInAccName())
                            .fluentPut("inBankName", respBo.getInBankName())
                            .fluentPut("inBankEicon", respBo.getInBankEicon())
                    ).toJSONString());
            bo.setOutFeeAmt(BigDecimal.ZERO);
            bo.setOutCouponAmt(BigDecimal.ZERO);
            bo.setOutCashAmt(respBo.getOutCashAmt());
            bo.setOutSuccessTime(respBo.getOutSuccessTime());
        } else if (PayRecvInfStatusEnum.CANCEL.getCode().equals(bo.getPayInfStatus())) {
            bo.setInfReason("");
            bo.setInfStatus(bo.getPayInfStatus());
            bo.setCloseTime(Convert.toDate(DateUtil.now()));
        } else if (bo.getPayInfStatus() == null){
            throw new ServiceException("收款单查询异常，请稍后重试");
        }
        return respBo;
    }
    public TradePayRecvBo payRecvClose(TradePayRecvVo vo) {
        String orderNo = StringUtils.isBlank(vo.getOrderNo()) ? vo.getRecvNo() : vo.getOrderNo();
        JSONObject body = new JSONObject();
        body.put("FunctionFlag", "1"); // 1 关闭    2 查询
        body.put("MemberSubAcctNo", vo.getCustomerAcctCode());
        body.put("TranNetMemberCode", vo.getCustomerOutCode());
        body.put("OrderNo", vo.getRecvNo());
        body.put("OrderRecvCode", vo.getOutRecvCode());
        TradePayRecvBo respBo = new TradePayRecvBo();
        respBo.setId(vo.getId());
        respBo.setRecvNo(vo.getRecvNo()); // 大额支付时使用
        respBo.setInfReason("");
        try {
            JSONObject resp = client.post(orderNo, "payRecvClose", PinganCloudClientApiEnum.LARGE_PAY_CLOSE, body);
            body.put("FunctionFlag", "2");
            for (int i = 1; !resp.getString("CancelStatus").equals("2") && i <= 20; i++) {
                Thread.sleep(i == 1 ? 200L : 100L);
                resp = client.post(orderNo, "payRecvClose", PinganCloudClientApiEnum.LARGE_PAY_CLOSE, body);
            }
            if (!resp.getString("CancelStatus").equals("2")) {
                throw new ServiceException("支付单关闭异常，请稍后重试");
            }
            if (!resp.getString("CancellType").equals("3")) {
                respBo.setInfStatus(PayRecvInfStatusEnum.CANCEL.getCode());
                respBo.setCloseTime(Convert.toDate(DateUtil.now()));
            } else {
                paySuccessResp(respBo, vo);
            }
        } catch (ServiceException se) {
            if (se.getMessage().contains("[E10117]")) {
                // 原支付订单已关闭,不能申请撤销[E10117], 交易已关闭,订单不能重复关闭 [T6535], 返回关闭状态
                log.keyword(orderNo, "payRecvClose").warn("[E10117] 转异常", se);
                respBo.setInfStatus(PayRecvInfStatusEnum.CANCEL.getCode());
                respBo.setCloseTime(Convert.toDate(DateUtil.now()));
            } else {
                throw se;
            }
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        return payRespToBo(respBo, vo);
    }
}
