package cn.xianlink.trade.channel.pingancloud;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.xianlink.common.api.enums.trade.*;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.trade.channel.AccountService;
import cn.xianlink.trade.channel.pingancloud.constant.PinganCloudTradeStatusEnum;
import cn.xianlink.trade.channel.pingancloud.constant.PinganCloudClientApiEnum;
import cn.xianlink.trade.channel.pingancloud.constant.PinganCloudSplitAcctStatusEnum;
import cn.xianlink.trade.domain.bo.*;
import cn.xianlink.trade.domain.bo.comm.TradeCommCashBo;
import cn.xianlink.trade.domain.bo.org.TradeOrgBankAmtBo;
import cn.xianlink.trade.domain.vo.*;
import cn.xianlink.trade.domain.vo.comm.TradeCommCashVo;
import cn.xianlink.trade.domain.vo.excel.ExcelDataInfo;
import cn.xianlink.trade.domain.vo.org.TradeOrgBankBindVo;
import cn.xianlink.trade.domain.vo.org.TradeOrgBankRelaVo;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import jakarta.annotation.Resource;
import lombok.CustomLog;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;

/**
 * 平安见证宝对接
 *
 * <AUTHOR>
 * @date 2024-05-27
*/
@CustomLog
@Service
@ConditionalOnProperty(prefix = "channels", name = "debug-local", havingValue = "false", matchIfMissing = true)
public class PinganCloudAccountServiceImpl implements AccountService {

    @Resource
    private transient PinganCloudClientService client;

    @Override
    public void init(Properties properties) {
        client.init(properties);
    }

    @Override
    public String getDefaultOutCode(Integer orgType){
        return client.getDefaultOutCode(orgType);
    }

    @Override
    public String getMarketingAcctCode() {
        return client.config.getMarketingAcctCode();
    }

    @Override
    public String getServiceFeeAcctCode() {
        return client.config.getServiceFeeAcctCode();
    }

    @Override
    public TradeOrgBankAmtBo queryBankAmt(String outOrgCode, String outAcctCode) {
        // 查询子账户 6010
        JSONObject body = new JSONObject();
        body.put("PageNum", "1");
        body.put("SubAcctNo", outAcctCode);
        body.put("QueryFlag", "2");
        JSONObject resp = client.post(outOrgCode, "queryBankAmt", PinganCloudClientApiEnum.ACC_AVAIL_BYACCT_QUERY, body);
        if (resp.getString("ResultNum").equals("0")) {
            return null;
        }
        JSONArray arrays = resp.getJSONArray("AcctArray");
        for (int i = 0; i < arrays.size(); i++) {
            JSONObject row = arrays.getJSONObject(i);
            if (row.getString("TranNetMemberCode").equals(outOrgCode)) {
                return queryBankAmt(row);
            }
        }
        return null;
    }

    @Override
    public Map<String, TradeOrgBankAmtBo> queryBankAmt() {
        // 查询功能子账户
        JSONObject body = new JSONObject();
        body.put("PageNum", "1");
        body.put("QueryFlag", "3");
        JSONObject resp = client.post("queryBankAmt", "queryBankAmt", PinganCloudClientApiEnum.ACC_AVAIL_BYACCT_QUERY, body);
        Map<String, TradeOrgBankAmtBo> map = new HashMap<>();
        JSONArray arrays = resp.getJSONArray("AcctArray");
        for (int i = 0; i < arrays.size(); i++) {
            TradeOrgBankAmtBo amtBo = queryBankAmt(arrays.getJSONObject(i));
            map.put(amtBo.getOutOrgCode(), amtBo);
        }
        return map;
    }

    private TradeOrgBankAmtBo queryBankAmt(JSONObject row) {
        TradeOrgBankAmtBo respBo = new TradeOrgBankAmtBo();
        respBo.setOutOrgCode(row.getString("TranNetMemberCode"));
        // respBo.setOutAcctCode(row.getString("SubAcctNo"));
        // "SubAcctName":"公司2","MaintenanceDate":"********","SubAcctProperty":"1"
        respBo.setBankAvailAmt(new BigDecimal(row.getString("AcctAvailBal")));
        respBo.setBankCashAmt(new BigDecimal(row.getString("CashAmt")));
        respBo.setBankAmtDate(PinganCloudUtil.dateToDate(row.getString("MaintenanceDate")));
        respBo.setBankAmtTime(Convert.toDate(DateUtil.now()));
        return respBo;
    }
    @Override
    public TradeOrgBankAmtBo queryBankHistoryAmt(String outOrgCode, String outAcctCode) {
        // 该接口生产无权限 ！！！！！
        List<TradeOrgBankAmtBo> list = queryBankHistoryAmt(outOrgCode, outAcctCode, LocalDate.now(), 9);
        return list == null || list.size() == 0 ? null : list.get(0);
    }
    @Override
    public List<TradeOrgBankAmtBo> queryBankHistoryAmt(String outOrgCode, String outAcctCode, LocalDate dateEnd) {
        return queryBankHistoryAmt(outOrgCode, outAcctCode, dateEnd, 9);
    }
    private List<TradeOrgBankAmtBo> queryBankHistoryAmt(String outOrgCode, String outAcctCode, LocalDate dateEnd, int day) {
        // 查询子账户, 只能间隔10天， 日期不传，查昨日之前的 9 行， 传日期是倒序， 不传日期正序
        JSONObject body = new JSONObject();
        body.put("PageNum", "1");
        body.put("SubAcctNo", outAcctCode);
        body.put("StartDate", PinganCloudUtil.dateToStr(LocalDate.now().minusDays(day)));
        body.put("EndDate", PinganCloudUtil.dateToStr(dateEnd));
        try {
            JSONObject resp = client.post(outOrgCode, "queryBankHistoryAmt", PinganCloudClientApiEnum.ACC_AVAIL_HISTORY_QUERY, body);
            if (resp.getString("ResultNum").equals("0")) {
                return new ArrayList<>();
            }
            List<TradeOrgBankAmtBo> list = new ArrayList<>();
            JSONArray arrays = resp.getJSONArray("AcctArray");
            for (int i = 0; i < arrays.size(); i++) {
                JSONObject row = arrays.getJSONObject(i);
                TradeOrgBankAmtBo respBo = new TradeOrgBankAmtBo();
                respBo.setOutOrgCode(row.getString("TranNetMemberCode"));
                // respBo.setOutAcctCode(row.getString("SubAcctNo"));
                //  DayFreezeBal ,   DayWaitCashBal + DayCashBal = DayAcctAvailBal
                respBo.setBankOccurAmt(new BigDecimal(row.getString("DayCashOccurAmt")));
                respBo.setBankWaitAmt(new BigDecimal(row.getString("DayWaitCashBal")));
                respBo.setBankCashAmt(new BigDecimal(row.getString("DayCashBal")));
                respBo.setBankAvailAmt(new BigDecimal(row.getString("DayAcctAvailBal")));
                respBo.setBankAmtDate(PinganCloudUtil.dateToDate(row.getString("Date")));
                respBo.setBankAmtTime(Convert.toDate(DateUtil.now()));
                list.add(respBo);
            }
            return list;
        } catch (ServiceException se) {
            // {"ErrorCode":"ERR020","ErrorMessage":"没有找到符合条件的记录，请检查查询条件!"}
            if (se.getMessage().contains("[ERR020]")) {  // 10 天内没有数据，报异常
                // 无该笔流水记录
                log.keyword(outOrgCode, "queryBankHistoryAmt").warn("[ERR020] 转异常", se);
                return null;
            }
            throw se;
        }
    }

    /*
        同名账户转账： 商户子账户 -> 普通子账户
     */
    @Override
    public TradeAccTransferBo transferSource(TradeAccTransferVo vo, TradeOrgBankBindVo sourceBindVo) {
        JSONObject body = new JSONObject();
        body.put("FunctionFlag", "1");
        body.put("OutSubAcctNo", sourceBindVo.getOutAcctCode());
        body.put("OutMemberCode", sourceBindVo.getOutOrgCode());
        body.put("InSubAcctNo", sourceBindVo.getCommAcctCode());
        body.put("InMemberCode", sourceBindVo.getCommOrgCode());
        body.put("TranAmt", vo.getSplitAmt().toString());
        body.put("TranFee", vo.getFeeAmt().toString());
        body.put("AccountMode", "T0");
        body.put("Ccy", "RMB");
        body.put("OrderNo", vo.getTransNo()); //
        body.put("CnsmrSeqNo", vo.getSourceSplitNo());
        JSONObject resp = client.post(vo.getTransNo(), "transferSource", PinganCloudClientApiEnum.ACC_TRANSFER_SAME, body);
        TradeAccTransferBo respBo = new TradeAccTransferBo();
        respBo.setId(vo.getId());
        // CnsmrSeqNo 对应数据文件中的交易网流水号
        // JzbPlatformSeqNo 对应数据文件中的银行见证单号
        respBo.setOutSourceNo(resp.getString("JzbPlatformSeqNo"));
        respBo.setInfSource(TransferInfStatusEnum.SUCCESS.getCode());
        respBo.setInfReason("");
        return respBo;
    }
    /*
        不同名账户转账：  普通子账户 -> 商户子账户
     */
    @Override
    public TradeAccTransferBo transferPay(TradeAccTransferVo vo, TradeOrgBankBindVo sourceBindVo,
                                          TradeOrgBankBindVo bindVo) {
        JSONObject body = new JSONObject();
        body.put("FunctionFlag", "6"); // 6 是 t+1，  9 是 t+0
        body.put("OutSubAcctNo", sourceBindVo.getCommAcctCode());
        body.put("OutMemberCode", sourceBindVo.getCommOrgCode());
        // body.put("OutSubAcctName", sourceBindVo.getBankAccName());
        body.put("InSubAcctNo", bindVo.getOutAcctCode());
        body.put("InMemberCode", bindVo.getOutOrgCode());
        // body.put("InSubAcctName", bindVo.getBankAccName());
        body.put("TranAmt", vo.getSplitAmt().toString());
        body.put("TranFee", vo.getFeeAmt().toString());
        body.put("TranType", "01");
        body.put("Ccy", "RMB");
        body.put("OrderNo", vo.getTransNo()); //
        body.put("Remark", vo.getRemark());
        body.put("CnsmrSeqNo", vo.getSplitNo());
        JSONObject resp = client.post(vo.getTransNo(), "transferPay", PinganCloudClientApiEnum.ACC_TRANSFER_PAY, body);
        TradeAccTransferBo respBo = new TradeAccTransferBo();
        respBo.setId(vo.getId());
        respBo.setTransNo(vo.getTransNo()); // 因要回填 split 表
        // CnsmrSeqNo 对应数据文件中的交易网流水号
        // FrontSeqNo 对应数据文件中的银行见证单号
        respBo.setOutTradeNo(resp.getString("FrontSeqNo"));
        respBo.setInfStatus(TransferInfStatusEnum.SUCCESS.getCode());
        respBo.setInfReason("");
        respBo.setOutSuccessTime(Convert.toDate(DateUtil.now()));
        return respBo;
    }

    /*
        营销转账：  公司监管账户（公司的银行卡） -> 营销子账户
     */
    @Override
    public TradeAccChargeBo recharge(TradeAccChargeVo vo) {
        JSONObject body = new JSONObject();
        // 固定营销账户转入
        body.put("SubAcctNo", vo.getOutAcctCode());
        body.put("TranNetMemberCode", vo.getOutOrgCode());
        body.put("OrderNo", vo.getChargeNo());
        body.put("SuspendAmt", vo.getChargeAmt());
        body.put("TranFee", "0.00");
        body.put("Remark", vo.getRemark());
        body.put("CnsmrSeqNo", vo.getSplitNo());
        JSONObject resp = client.post(vo.getChargeNo(), "recharge", PinganCloudClientApiEnum.ACC_CREDIT_PAY, body);
        TradeAccChargeBo respBo = new TradeAccChargeBo();
        respBo.setId(vo.getId());
        // CnsmrSeqNo 对应数据文件中的交易网流水号
        // FrontSeqNo 对应数据文件中的银行见证单号
        respBo.setOutTradeNo(resp.getString("FrontSeqNo"));
        respBo.setInfStatus(ChargeInfStatusEnum.SUCCESS.getCode());
        respBo.setInfReason("");
        respBo.setOutSuccessTime(Convert.toDate(DateUtil.now()));
        return respBo;
    }
    /*
        营销转账退款：  公司监管账户（公司的银行卡） <- 营销子账户
     */
    @Override
    public TradeAccChargeBo rechargeRefund(TradeAccChargeVo vo) {
        JSONObject body = new JSONObject();
        // 固定营销账户转入
        body.put("SubAcctNo", vo.getOutAcctCode());
        body.put("TranNetMemberCode", vo.getOutOrgCode());
        body.put("OldOrderNo", vo.getRelateNo());
        body.put("CancelAmt", vo.getRefundAmt());
        body.put("TranFee", "0.00");
        body.put("Remark", vo.getRemark());
        // body.put("ReservedMsg", ""); // 退款交易时间超过三个月以上的保留域字段为必输
        body.put("CnsmrSeqNo", vo.getSplitNo());
        JSONObject resp = client.post(vo.getRelateNo(), "rechargeRefund", PinganCloudClientApiEnum.ACC_CREDIT_REFUND, body);
        TradeAccChargeBo respBo = new TradeAccChargeBo();
        respBo.setId(vo.getId());
        // CnsmrSeqNo 对应数据文件中的交易网流水号
        // FrontSeqNo 对应数据文件中的银行见证单号
        respBo.setOutTradeNo(resp.getString("FrontSeqNo"));
        respBo.setInfStatus(ChargeInfStatusEnum.SUCCESS.getCode());
        respBo.setInfReason("");
        respBo.setOutSuccessTime(Convert.toDate(DateUtil.now()));
        return respBo;
    }

    /*
        供应商扣款：  商户子账户 -> 手续费子账户
     */
    @Override
    public TradeAccTransferBo transferOut(TradeAccTransferVo vo) {
        JSONObject body = new JSONObject();
        body.put("FunctionFlag", "7");
        body.put("OutSubAcctNo", vo.getSourceOutAcctCode());
        body.put("OutMemberCode", vo.getSourceOutOrgCode());
        body.put("InSubAcctNo", getServiceFeeAcctCode());
        body.put("InMemberCode", getServiceFeeAcctCode());
        body.put("TranAmt", vo.getSplitAmt().toString());
        body.put("TranFee", "0.0");
        body.put("TranType", "01");
        body.put("Ccy", "RMB");
        body.put("OrderNo", vo.getTransNo()); //
        body.put("Remark", "供应商扣款");
        body.put("CnsmrSeqNo", vo.getSourceSplitNo()); // 调入方的分账单号
        JSONObject resp = client.post(vo.getTransNo(), "transferOut", PinganCloudClientApiEnum.ACC_TRANSFER_PAY, body);
        TradeAccTransferBo respBo = new TradeAccTransferBo();
        respBo.setId(vo.getId());
        respBo.setOutTradeNo(resp.getString("FrontSeqNo"));
        respBo.setInfStatus(DiscountInfStatusEnum.SUCCESS.getCode());
        respBo.setInfReason("");
        respBo.setOutSuccessTime(Convert.toDate(DateUtil.now()));
        return respBo;
    }

    @Override
    public TradeAccTransferBo transferOutRefund(TradeAccTransferVo vo) {
        // 6164-2
        JSONObject body = new JSONObject();
        body.put("FunctionFlag", "2");
        body.put("OldTranSeqNo", vo.getSourceSplitNo());
        body.put("OldOrderNo", vo.getTransNo());
        body.put("OldOutSubAcctNo", vo.getSourceOutAcctCode());
        body.put("OldOutMemberCode", vo.getSourceOutOrgCode());
        body.put("OldInSubAcctNo", getServiceFeeAcctCode());
        body.put("OldInMemberCode", getServiceFeeAcctCode());
        body.put("ReturnAmt", vo.getSplitAmt().toString()); // 包含退款手续费
        body.put("ReturnCommission", "0.0"); // 6034-6 时填 0.0
        body.put("Remark", "供应商扣款");
        // body.put("CnsmrSeqNo", vo.getSplitDisNo());
        JSONObject resp = client.post(vo.getTransNo(), "transferOutRefund", PinganCloudClientApiEnum.ACC_TRANSFER_REFUND, body);
        TradeAccTransferBo respBo = new TradeAccTransferBo();
        respBo.setId(vo.getId());
        respBo.setOutTradeNo(resp.getString("FrontSeqNo"));
        respBo.setInfStatus(DiscountInfStatusEnum.SUCCESS.getCode());
        respBo.setInfReason("");
        respBo.setOutSuccessTime(Convert.toDate(DateUtil.now()));
        return respBo;
    }
    /*
        供应商加款：  营销子账户 -> 商户子账户
     */
    @Override
    public TradeAccTransferBo transferIn(TradeAccTransferVo vo) {
        JSONObject body = new JSONObject();
        body.put("FunctionFlag", "6"); // 6 是 t+1，  9 是 t+0
        // 固定营销账户转出
        body.put("OutSubAcctNo", getMarketingAcctCode());
        body.put("OutMemberCode", getMarketingAcctCode());
        // body.put("OutSubAcctName", sourceBindVo.getBankAccName());
        body.put("InSubAcctNo", vo.getOutAcctCode());
        body.put("InMemberCode", vo.getOutOrgCode());
        // body.put("InSubAcctName", bindVo.getBankAccName());
        body.put("TranAmt", vo.getSplitAmt().toString());
        body.put("TranFee", "0.0");
        body.put("TranType", "01");
        body.put("Ccy", "RMB");
        body.put("OrderNo", vo.getTransNo()); //
        body.put("Remark", "供应商加款");
        body.put("CnsmrSeqNo", vo.getSplitNo()); // 调入方的分账单号
        JSONObject resp = client.post(vo.getTransNo(), "transferIn", PinganCloudClientApiEnum.ACC_TRANSFER_PAY, body);
        TradeAccTransferBo respBo = new TradeAccTransferBo();
        respBo.setId(vo.getId());
        respBo.setOutTradeNo(resp.getString("FrontSeqNo"));
        respBo.setInfStatus(DiscountInfStatusEnum.SUCCESS.getCode());
        respBo.setInfReason("");
        respBo.setOutSuccessTime(Convert.toDate(DateUtil.now()));
        return respBo;
    }

    @Override
    public TradeAccTransferBo transferInRefund(TradeAccTransferVo vo) {
        // 6164-2
        JSONObject body = new JSONObject();
        body.put("FunctionFlag", "2");
        body.put("OldTranSeqNo", vo.getSplitNo());
        body.put("OldOrderNo", vo.getTransNo());
        body.put("OldOutSubAcctNo", getMarketingAcctCode());
        body.put("OldOutMemberCode", getMarketingAcctCode());
        body.put("OldInSubAcctNo", vo.getOutAcctCode());
        body.put("OldInMemberCode", vo.getOutOrgCode());
        body.put("ReturnAmt", vo.getSplitAmt().toString()); // 包含退款手续费
        body.put("ReturnCommission", "0.0"); // 6034-6 时填 0.0
        body.put("Remark", "供应商加款");
        // body.put("CnsmrSeqNo", vo.getSplitDisNo());
        JSONObject resp = client.post(vo.getTransNo(), "transferInRefund", PinganCloudClientApiEnum.ACC_TRANSFER_REFUND, body);
        TradeAccTransferBo respBo = new TradeAccTransferBo();
        respBo.setId(vo.getId());
        respBo.setOutTradeNo(resp.getString("FrontSeqNo"));
        respBo.setInfStatus(DiscountInfStatusEnum.SUCCESS.getCode());
        respBo.setInfReason("");
        respBo.setOutSuccessTime(Convert.toDate(DateUtil.now()));
        return respBo;
    }

    /*
        营销转账：  营销子账户 -> 商户子账户
     */
    @Override
    public TradeAccTransBo discountPay(TradeAccTransVo vo) {
        JSONObject body = new JSONObject();
        body.put("FunctionFlag", "6"); // 6 是 t+1，  9 是 t+0
        // 固定营销账户转出
        body.put("OutSubAcctNo", getMarketingAcctCode());
        body.put("OutMemberCode", getMarketingAcctCode());
        // body.put("OutSubAcctName", sourceBindVo.getBankAccName());
        body.put("InSubAcctNo", vo.getSplitAcctCode());
        body.put("InMemberCode", vo.getSplitOrgCode());
        // body.put("InSubAcctName", bindVo.getBankAccName());
        body.put("TranAmt", vo.getSplitAmt().toString());
        body.put("TranFee", "0.0");
        body.put("TranType", "01");
        body.put("Ccy", "RMB");
        body.put("OrderNo", vo.getRelateNo()); //
        body.put("Remark", "营销转账");
        body.put("CnsmrSeqNo", vo.getSplitDisNo());
        JSONObject resp = client.post(vo.getTransNo(), "discountPay", PinganCloudClientApiEnum.ACC_TRANSFER_PAY, body);
        TradeAccTransBo respBo = new TradeAccTransBo();
        // CnsmrSeqNo 对应数据文件中的交易网流水号
        // FrontSeqNo 对应数据文件中的银行见证单号
        respBo.setOutTradeNo(resp.getString("FrontSeqNo"));
        respBo.setInfStatus(DiscountInfStatusEnum.SUCCESS.getCode());
        respBo.setInfReason("");
        return respBo;
    }
    /*
        营销转账退款：  营销子账户 <- 商户子账户     退款不能超过 3 个月！！！！！！
            1.退款流水的原交易日期trandate（trandate需要以电商见证宝系统记录交易的日期为准，在跨日极端情况下trandate需要以原交易返回前置流水号的前六位记录的日期为准。）
            2.若平台调用6164接口报错：“未检索到原交易，正在尝试查询历史数据，请稍后再试”，需平台间隔一段时间再次尝试发起退款（建议间隔5分钟）。
     */
    @Override
    public TradeAccTransBo discountRefund(TradeAccTransVo vo) {
        JSONObject body = new JSONObject();
        body.put("FunctionFlag", "2"); // 2 是针对 6034-6或-9 的退款操作
        body.put("OldTranSeqNo", vo.getSplitRelNo());
        body.put("OldOrderNo", vo.getRelateNo());
        // 固定营销账户转出
        body.put("OldOutSubAcctNo", getMarketingAcctCode());
        body.put("OldOutMemberCode", getMarketingAcctCode());
        // body.put("OutSubAcctName", sourceBindVo.getBankAccName());
        body.put("OldInSubAcctNo", vo.getSplitAcctCode());
        body.put("OldInMemberCode", vo.getSplitOrgCode());
        // body.put("InSubAcctName", bindVo.getBankAccName());
        body.put("ReturnAmt", vo.getSplitAmt().negate().toString()); // 包含退款手续费
        body.put("ReturnCommission", "0.0"); // 6034-6 时填 0.0
        body.put("Remark", "营销转账");
        body.put("CnsmrSeqNo", vo.getSplitDisNo());
        JSONObject resp = client.post(vo.getTransNo(), "discountRefund", PinganCloudClientApiEnum.ACC_TRANSFER_REFUND, body);
        TradeAccTransBo respBo = new TradeAccTransBo();
        respBo.setOutTradeNo(resp.getString("FrontSeqNo"));
        respBo.setInfStatus(DiscountInfStatusEnum.SUCCESS.getCode());
        respBo.setInfReason("");
        return respBo;
    }
    /*
        提现
     */
    @Override
    public TradeAccCashBo withdrawCash(TradeAccCashVo vo, TradeOrgBankRelaVo bvo) {
        JSONObject body = new JSONObject();
        body.put("TranNetMemberCode", bvo.getOutOrgCode());
        body.put("SubAcctNo", bvo.getOutAcctCode());
        body.put("MemberName", bvo.getReprName()); // 提现使用法人
        body.put("MemberGlobalType", "1");
        body.put("MemberGlobalId", bvo.getReprGlobalId());
        body.put("TakeCashAcctNo", bvo.getBankAccount()); // 银行卡号
        body.put("OutAmtAcctName", bvo.getBankBranchName()); // 银行卡户名
        body.put("Ccy", "RMB");
        // CashAmt 是实际到绑定银行卡的金额， 平安在跨行转账时也不收费用
        body.put("CashAmt", vo.getCashAmt().subtract(vo.getOutFeeAmt()).toString());
        // 提现手续费，格式 0.00， 转入手续费子账户中
        body.put("ReservedMsg", vo.getOutFeeAmt().toString());
        body.put("Remark", vo.getCashNo()); // 建议可送订单号，可在TX对账文件的备注字段获取到。   如果不填，TX对账文件的备注是 CnsmrSeqNo
        body.put("CnsmrSeqNo", vo.getSplitNo());
        try {
            JSONObject resp = client.post(vo.getCashNo(), "withdrawCash", PinganCloudClientApiEnum.ACC_CASH, body);
            TradeAccCashBo respBo = new TradeAccCashBo();
            respBo.setId(vo.getId());
            respBo.setOutTradeNo(resp.getString("FrontSeqNo"));
            // respBo.setOutTradeNo(resp.getString("CnsmrSeqNo"));
            // BigDecimal feeAmt = PinganCloudUtil.yuanToYuan(resp.getString("TransferFee")); //
            // respBo.setOutFeeAmt(feeAmt); CashAmt
            respBo.setInfStatus(CashInfStatusEnum.PROCESSING.getCode());
            respBo.setInfReason("");
            return respBo;
        } catch (ServiceException se) {
            if (se.getMessage().contains("[ERR044]")) {
                // 可提现余额不足[ERR044]
                TradeOrgBankAmtBo amtBo = queryBankAmt(vo.getOutOrgCode(), vo.getOutAcctCode());
                if (amtBo != null && amtBo.getBankAvailAmt().compareTo(vo.getCashAmt()) > 0
                        && amtBo.getBankCashAmt().compareTo(vo.getCashAmt()) < 0) {
                    se.setMessage(String.format("银行总余额%s，可提现余额%s，部分金额银行未结账，暂时无法提现！",
                            amtBo.getBankAvailAmt(), amtBo.getBankCashAmt()));
                }
            }
            throw se;
        }
    }

    /*
        提现查询
     */
    @Override
    public TradeAccCashBo withdrawQuery(TradeAccCashVo vo) {
        JSONObject body = new JSONObject();
        body.put("FunctionFlag", "3");
        body.put("TranNetSeqNo", vo.getSplitNo());
        JSONObject resp = client.post(vo.getCashNo(), "withdrawQuery", PinganCloudClientApiEnum.ACC_TRADE_QUERY, body);
        TradeAccCashBo respBo = new TradeAccCashBo();
        respBo.setId(vo.getId());
        // （0：成功，1：失败，2：待确认, 5：待处理，6：处理中）
        respBo.setInfStatus(PinganCloudTradeStatusEnum.getInfStatus(resp.getString("TranStatus")));
        respBo.setOutTradeNo(resp.getString("OldTranFrontSeqNo"));
        if (CashInfStatusEnum.SUCCESS.getCode().equals(respBo.getInfStatus())) {
            respBo.setCashTime(PinganCloudUtil.timeToDate(resp.getString("TranDate") + resp.getString("TranTime")));
            respBo.setStatus(AccountStatusEnum.CASH.getCode());
            respBo.setOutActualAmt(vo.getCashAmt().subtract(vo.getOutFeeAmt()));
            respBo.setInfReason("");
        } else if (CashInfStatusEnum.FAIL.getCode().equals(respBo.getInfStatus())) {
            respBo.setCashTime(PinganCloudUtil.timeToDate(resp.getString("TranDate") + resp.getString("TranTime")));
            respBo.setOutActualAmt(BigDecimal.ZERO);
            respBo.setInfReason(resp.getString("FailMsg"));
        }
        return respBo;
    }

    @Override
    public TradeCommCashBo commCashExpire(TradeCommCashVo vo, TradeOrgBankRelaVo bvo) {
        // 客户过期的佣金，转到手续费账户
        JSONObject body = new JSONObject();
        body.put("FunctionFlag", "7");
        body.put("OutSubAcctNo", bvo.getOutAcctCode());
        body.put("OutMemberCode", bvo.getOutOrgCode());
        body.put("InSubAcctNo", getServiceFeeAcctCode());
        body.put("InMemberCode", getServiceFeeAcctCode());
        body.put("TranAmt", vo.getCashAmt().toString());
        body.put("TranFee", "0.0");
        body.put("TranType", "01");
        body.put("Ccy", "RMB");
        body.put("OrderNo", vo.getCashNo()); //
        body.put("Remark", "佣金过期");
        body.put("CnsmrSeqNo", vo.getSplitNo()); // 调入方的分账单号
        JSONObject resp = client.post(vo.getCashNo(), "commCashExpire", PinganCloudClientApiEnum.ACC_TRANSFER_PAY, body);
        TradeCommCashBo respBo = new TradeCommCashBo();
        respBo.setOutTradeNo(resp.getString("FrontSeqNo"));
        respBo.setInfStatus(CashInfStatusEnum.SUCCESS.getCode());
        respBo.setInfReason("");
        respBo.setOutCashTime(PinganCloudUtil.timeToDate(resp.getString("TranDate") + resp.getString("TranTime")));
        respBo.setCashTime(respBo.getOutCashTime());
        respBo.setOutActualAmt(vo.getCashAmt());
        respBo.setCustomerCode(vo.getCustomerCode());
        return respBo;
    }

    /*
        分账查询（仅支付单分账，退款分账不行， 未对接， 下载对账数据文件后，对异常数据查询分账接口）
     */
    @Override
    public TradeAccSplitVo splitQuery(String transNo, String splitNo) {
        JSONObject body = new JSONObject();
        body.put("AcquiringChannelType", "YST1");
        body.put("OrderNo", splitNo);
        try {
            JSONObject resp = client.post(transNo, "splitQuery", PinganCloudClientApiEnum.ACC_SPLIT_QUERY, body);
            // 仅返回要更新的值！！！！！ 新创建对象， 更新数据库
            TradeAccSplitVo respBo = new TradeAccSplitVo();
            respBo.setSplitNo(splitNo);
            respBo.setTransDate(PinganCloudUtil.dateToDate(resp.getString("TranDate")));
            respBo.setTransAmt(PinganCloudUtil.yuanToYuan(resp.getString("TranAmt")));
            respBo.setFeeAmt(PinganCloudUtil.yuanToYuan(resp.getString("CommissionAmt")));
            respBo.setOutAcctCode(PinganCloudUtil.toString(resp.getString("OrderActInSubAcctNo")));  // 实际子账号  和后面啥区别 ？ OrderInSubAcctNo()
            respBo.setAcctStatus(PinganCloudSplitAcctStatusEnum.getAcctStatus(resp.getString("TranStatus")));
            String desc = PinganCloudUtil.toString(resp.getString("TranDesc"));
            respBo.setAcctReason(StringUtils.isEmpty(desc) && "3".equals(resp.getString("TranStatus")) ? "冲正" : desc);
            // OrderActInSubAcctName  OrderInSubAcctName  FrontSeqNo  PayMode（0-冻结支付 1-普通支付）    TranTime
            return respBo;
        } catch (ServiceException se) {
            if (se.getMessage().contains("[ERR020]")) {
                // 无该笔流水记录
                log.keyword(transNo, "splitQuery").warn("[ERR020] 转异常", se);
                return null;
            }
            throw se;
        }
    }
    /*
        调账  6145 （变更子账号） 对前一天异常处理（仅支付单号，退款不行， 未对接）， 这2个接口都是"弥补措施" 而不是"主动操作"
     */
    @Override
    public TradeAccSplitVo splitAdjust(String transNo, String splitNo) {
        return null;
    }
    /*
        补账  6147  对前一天异常处理（仅支付单号，退款不行， 未对接）， 这2个接口都是"弥补措施" 而不是"主动操作"
     */
    @Override
    public boolean splitSupply(String orderNo, String tradeNo, BigDecimal payAmt) {
        JSONObject body = new JSONObject();
        body.put("AcquiringChannelType", "YST1");
        body.put("OrderNo", tradeNo);
        body.put("Amt", payAmt);
        try {
            client.post(orderNo, "splitSupply", PinganCloudClientApiEnum.ACC_SPLIT_SUPPLY, body);
            // resp.getString("FrontSeqNo");
            // resp.getString("SubAcctNo");
            // resp.getString("Amt");
            // 无错误就认为是成功的
            return true;
        } catch (ServiceException se) {
            if (se.getMessage().contains("[ERR282]")) { // 单据存在， 不用再处理
                // {"ErrorCode":"ERR282","ErrorMessage":"该订单号已经报送成功，无需进行平台补账"}
                log.keyword(orderNo, "splitSupply").warn("[ERR282] 转异常", se);
                return true;
            } else if (se.getMessage().contains("[ERR283]")) { // 单据不存在
                // {"ErrorCode":"ERR283","ErrorMessage":"订单状态失败或异常"}
                log.keyword(orderNo, "splitSupply").warn("[ERR283] 转异常", se);
                return false;
            }
            throw se;
        }
    }


    @Override
    public <T> String fileUpload(ExcelDataInfo<T> data) {
        String fileDate = PinganCloudUtil.dateToStr(data.getTransDate());
        String downloadKey;
        try {
            downloadKey = client.fileUpload(data.getFileName(), data.getFile());
        } catch (InvocationTargetException | IllegalAccessException ie) {
            log.keyword(data.getFileType(), "fileUpload").error("对账文件上传失败 fileName = " + data.getFileName(), ie);
            return null;
        }
        log.keyword(data.getFileType(), "fileUpload").info(String.format("对账文件上传成功 fileName = %s, downloadKey = %s", data.getFileName(), downloadKey));
        JSONObject body = new JSONObject();
        body.put("FunctionFlag", "01");
        body.put("PayChannelType", "0001");
        body.put("MerchantCode", data.getMerchantId());
        body.put("ThirdPayFileName", data.getFileName());
        body.put("ThirdPayFileDate", fileDate);
        body.put("DownloadKey", downloadKey);
        JSONObject resp = client.post(data.getFileType(), "fileUpload", PinganCloudClientApiEnum.ACC_FILE_UPLOAD_NOFITY, body);
        String seqNo = resp.getString("JzbAcceptSeqNo");
        log.keyword(data.getFileType(), "fileUpload").info(String.format("对账文件上传通知成功 fileName = %s, seqNo = %s", data.getFileName(), seqNo));
        // resp.getString("JzbAcceptSeqNo")
        return seqNo;
    }

    /*
        对账文件下载
     */
    @Override
    public <T> ExcelDataInfo<T> fileDownload(String fileType, LocalDate fileDate) {
        String date = LocalDateTimeUtil.format(fileDate, "yyyyMMdd");
        JSONObject body = new JSONObject();
        body.put("FileType", fileType);
        body.put("FileDate", date);
        JSONObject resp;
        try {
            resp = client.post(fileType, "fileDownload", PinganCloudClientApiEnum.ACC_FILE_QUERY, body);
        } catch (ServiceException se) {
            if (se.getMessage().contains("[ERR020]")) {
                // 无该笔流水记录
                log.keyword(fileType, "fileDownload").warn("[ERR020] 转异常", se);
                return null;
            }
            throw se;
        }
        // 一天，一个类型就一个文件
        JSONArray tranItemArray = resp.getJSONArray("TranItemArray");
        for (int j = 0; j < tranItemArray.size(); j++) {
            JSONObject file = tranItemArray.getJSONObject(j);
            String fileName = file.getString("FileName");
            if ("none.txt".equals(fileName) || fileName.contains("ok.enc") || !fileName.endsWith(".enc")) {
                continue;
            }
            fileName = fileName.substring(0, fileName.length() - 4); // 截掉 .enc
            log.keyword(fileType, "fileDownload").info(date + " 下载文件 " + fileName);
            try {
                return new ExcelDataInfo<T>().setTransDate(fileDate).setFileType(fileType).setFileName(fileName).setMerchantId("")
                        .setFile(client.fileDownLoad(file.getString("FilePath"), file.getString("RandomPassword")));
            } catch (InvocationTargetException | IllegalAccessException ie) {
                log.keyword(fileType, "fileDownload").error("", ie);
            }
        }
        log.keyword(fileType, "fileDownload").info(date + " 暂无可下载的文件");
        return null;
    }

}
