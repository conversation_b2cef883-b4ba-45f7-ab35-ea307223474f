package cn.xianlink.trade.mapper;

import cn.xianlink.common.mybatis.core.mapper.BaseMapperPlus;
import cn.xianlink.trade.domain.TradeAccAvailTrans;
import cn.xianlink.trade.domain.TradeAccTrans;
import cn.xianlink.trade.domain.bo.sup.SupTradeAccSupAvailTransExportBo;
import cn.xianlink.trade.domain.bo.sup.SupTradeAccSupAvailTransSkuOrderQueryBo;
import cn.xianlink.trade.domain.bo.sup.SupTradeAccSupAvailTransSkuQueryBo;
import cn.xianlink.trade.domain.bo.sup.SupTradeAccSupDeptAcctDetailQueryBo;
import cn.xianlink.trade.domain.vo.TradeAccAvailTransVo;
import cn.xianlink.trade.domain.vo.sup.SupTradeAccSupDeptAvailTransOrderVo;
import cn.xianlink.trade.domain.vo.sup.SupTradeAccSupDeptAvailTransSkuVo;
import cn.xianlink.trade.domain.vo.sup.SupTradeAccSupDeptAvailTransVo;
import cn.xianlink.trade.domain.vo.sup.excel.SupTradeAccSupDeptAcctRefundExcelVo;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * 结算单Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
public interface TradeAccAvailTransMapper extends BaseMapperPlus<TradeAccAvailTrans, TradeAccAvailTransVo> {


    Page<SupTradeAccSupDeptAvailTransVo> customSupUndonePageList(
            @Param("page") Page<TradeAccTrans> page, @Param("dates") List<LocalDate> dates, @Param("bo") SupTradeAccSupDeptAcctDetailQueryBo bo);

    Page<SupTradeAccSupDeptAvailTransVo> customSupAvailPageList(
            @Param("page") Page<TradeAccAvailTrans> page, @Param("dates") List<LocalDate> dates, @Param("bo") SupTradeAccSupDeptAcctDetailQueryBo bo);

    Page<SupTradeAccSupDeptAvailTransSkuVo> customSupUndoneSkuPageList(
            @Param("page") Page<TradeAccAvailTrans> page, @Param("bo") SupTradeAccSupAvailTransSkuQueryBo bo, @Param("symbol") String symbol);

    List<SupTradeAccSupDeptAvailTransSkuVo> customSupUndoneSkuTransIds(@Param("bo") SupTradeAccSupAvailTransSkuQueryBo bo, @Param("skuIds") List<Long> skuIds);

    SupTradeAccSupDeptAvailTransSkuVo customSupUndoneSkuPageTotal(@Param("bo") SupTradeAccSupAvailTransSkuQueryBo bo, @Param("symbol") String symbol);

    Page<SupTradeAccSupDeptAvailTransSkuVo> customSupAvailSkuPageList(
            @Param("page") Page<TradeAccAvailTrans> page, @Param("bo") SupTradeAccSupAvailTransSkuQueryBo bo, @Param("symbol") String symbol);

    List<SupTradeAccSupDeptAvailTransSkuVo> customSupAvailSkuTransIds(@Param("bo") SupTradeAccSupAvailTransSkuQueryBo bo, @Param("skuIds") List<Long> skuIds);

    SupTradeAccSupDeptAvailTransSkuVo customSupAvailSkuPageTotal(@Param("bo") SupTradeAccSupAvailTransSkuQueryBo bo, @Param("symbol") String symbol);

    Page<SupTradeAccSupDeptAvailTransOrderVo> customSupUndoneOrderPageList(
            @Param("page") Page<TradeAccAvailTrans> page, @Param("bo") SupTradeAccSupAvailTransSkuOrderQueryBo bo, @Param("symbol") String symbol);

    SupTradeAccSupDeptAvailTransOrderVo customSupUndoneOrderPageTotal(@Param("bo") SupTradeAccSupAvailTransSkuOrderQueryBo bo, @Param("symbol") String symbol);

    Page<SupTradeAccSupDeptAvailTransOrderVo> customSupAvailOrderPageList(
            @Param("page") Page<TradeAccAvailTrans> page, @Param("bo") SupTradeAccSupAvailTransSkuOrderQueryBo bo, @Param("symbol") String symbol);

    SupTradeAccSupDeptAvailTransOrderVo customSupAvailOrderPageTotal(@Param("bo") SupTradeAccSupAvailTransSkuOrderQueryBo bo, @Param("symbol") String symbol);


    List<SupTradeAccSupDeptAcctRefundExcelVo> customSupAvailTransExportList(@Param("bo") SupTradeAccSupAvailTransExportBo bo);

    int insertSupUndonePay(@Param("orgCode") String orgCode, @Param("transDate") LocalDate transDate, @Param("availDate") LocalDate availDate);
    int insertSupUndoneOccupy(@Param("orgCode") String orgCode, @Param("transDate") LocalDate transDate, @Param("availDate") LocalDate availDate);
    int insertSupOverdue(@Param("availIds") List<Long> availIds, @Param("transDate") LocalDate transDate, @Param("availDate") LocalDate availDate);

    int updateSupUndoneBatch(@Param("transVos") List<TradeAccAvailTransVo> transVos);
}
