package cn.xianlink.trade.domain.convert.comm;

import cn.xianlink.trade.domain.vo.comm.TradeCommCashVo;
import cn.xianlink.trade.domain.vo.platform.TradePlatformCommCashVo;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.*;


/**
 * 数据转换器
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TradePlatformCommCashVoConvert extends BaseMapper<TradeCommCashVo, TradePlatformCommCashVo> {


}
