package cn.xianlink.trade.mapper;

import cn.xianlink.common.mybatis.core.mapper.BaseMapperPlus;
import cn.xianlink.trade.api.domain.bo.OrderPayJobQueryBo;
import cn.xianlink.trade.api.domain.bo.RemoteAccTransItemBo;
import cn.xianlink.trade.domain.TradeAccAvailTrans;
import cn.xianlink.trade.domain.TradeAccTrans;
import cn.xianlink.trade.domain.bo.TradeAccTransAvaildsBo;
import cn.xianlink.trade.domain.bo.TradeAccTransBo;
import cn.xianlink.trade.domain.vo.TradeAccAccountVo;
import cn.xianlink.trade.domain.vo.TradeAccTransVo;
import cn.xianlink.trade.domain.vo.sup.SupTradeAccSupDeptAcctDateVo;
import cn.xianlink.trade.domain.vo.sup.SupTradeAccSupDeptAvailTransVo;
import cn.xianlink.trade.domain.vo.sup.SupTradeAccSupDeptTransVo;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * 分账变更流水Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
public interface TradeAccTransMapper extends BaseMapperPlus<TradeAccTrans, TradeAccTransVo> {

    Page<TradeAccAccountVo> customSupList(@Param("page") Page<TradeAccTrans> page, @Param("ew") Wrapper<TradeAccTrans> wrapper);

    List<TradeAccAccountVo> customSupList(@Param("ew") Wrapper<TradeAccTrans> wrapper);

    Page<SupTradeAccSupDeptAcctDateVo> customSupDeptAcctDatePageList(
            @Param("page") Page<TradeAccTrans> page, @Param("dates") List<LocalDate> dates, @Param("ew") Wrapper<TradeAccTrans> wrapper);

    Page<SupTradeAccSupDeptTransVo> customSupDeptTransPageList(@Param("page") Page<TradeAccTrans> page, @Param("ew") Wrapper<TradeAccTrans> wrapper);

    Page<TradeAccTransVo> customSupAccountTransPageList(@Param("page") Page<TradeAccTrans> page, @Param("ew") Wrapper<TradeAccTrans> wrapper);

    List<TradeAccTransVo> customSupAccountTransIdsList(@Param("ew") Wrapper<TradeAccTrans> wrapper);

    List<TradeAccTransVo> customSupAccountTransSumList(@Param("ew") Wrapper<TradeAccTrans> wrapper);

    List<TradeAccTransVo> queryStatusListByRelate(
            @Param("relateNo") String relateNo, @Param("relateType") String relateType, @Param("orgs") List<TradeAccTransAvaildsBo> orgs);

    List<TradeAccTransVo> queryOrderRelateList(@Param("relateNo") String relateNo, @Param("transTypes") List<String> transTypes, @Param("orgs") List<TradeAccTrans> orgs);

    List<TradeAccTransVo> querySupplierFreezeTrans(@Param("relateNo") String relateNo, @Param("relateType") String relateType);

    List<TradeAccTransVo> queryPayCancelTrans(@Param("isZero") Boolean isZero, @Param("orderNo") String orderNo, @Param("refundNos") List<String> refundNos);

    List<TradeAccTransVo> queryDiscountChecking(@Param("bo") OrderPayJobQueryBo bo, @Param("infMaxRetries") Integer infMaxRetries);

    List<TradeAccTransVo> queryDiscountTrans(@Param("bo") TradeAccTransBo bo);

    int updateInfStatus(@Param("transNo") String transNo, @Param("transTypes") List<String> transTypes, @Param("splitNos") List<String> splitNos);

    int updateRefundInfStatus(@Param("transNo") String transNo, @Param("splitNos") List<String> splitNos);


    List<TradeAccTransVo> queryOrderItemIds(@Param("bos") List<RemoteAccTransItemBo> bos);
}
