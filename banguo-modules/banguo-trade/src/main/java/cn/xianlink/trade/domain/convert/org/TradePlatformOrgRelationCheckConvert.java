package cn.xianlink.trade.domain.convert.org;

import cn.xianlink.trade.domain.bo.platform.TradePlatformOrgRelationCheckBo;
import cn.xianlink.trade.domain.vo.org.TradeOrgBankRelaCheckVo;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

/**
 * 数据转换器
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TradePlatformOrgRelationCheckConvert extends BaseMapper<TradePlatformOrgRelationCheckBo, TradeOrgBankRelaCheckVo> {
}
