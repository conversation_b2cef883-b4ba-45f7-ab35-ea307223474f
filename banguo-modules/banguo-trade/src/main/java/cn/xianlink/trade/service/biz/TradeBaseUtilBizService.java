package cn.xianlink.trade.service.biz;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.HexUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.xianlink.common.api.enums.trade.PayBusiTypeEnum;
import cn.xianlink.common.api.enums.trade.RefundBusiTypeEnum;
import cn.xianlink.common.api.enums.trade.TransferBusiTypeEnum;
import cn.xianlink.common.api.vo.RemoteBaseDataVo;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.redis.utils.RedisUtils;
import cn.xianlink.common.tenant.helper.TenantHelper;
import cn.xianlink.trade.api.domain.bo.RetailInfoBo;
import cn.xianlink.trade.api.domain.vo.RetailInfoVo;
import cn.xianlink.trade.channel.weixinb2b.WeixinB2bPayServiceImpl;
import cn.xianlink.trade.config.properties.ChannelsProperties;
import cn.xianlink.trade.constant.TradeCacheNames;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.redisson.api.RAtomicLong;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

/**
 * 公用代码
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@CustomLog
@RequiredArgsConstructor
@Service
public class TradeBaseUtilBizService implements InitializingBean {
    private final transient ChannelsProperties channelsProperties;
    private final transient TradeBaseDataBizService tradeBaseDataBizService;
    private final transient WeixinB2bPayServiceImpl weixinB2bPayServiceImpl;

    private transient Set<String> allFields;

    @Override
    public void afterPropertiesSet() throws Exception {
        allFields = Arrays.stream(PayBusiTypeEnum.class.getEnumConstants()).map(PayBusiTypeEnum::getField)
                .filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        allFields.addAll(Arrays.stream(RefundBusiTypeEnum.class.getEnumConstants()).map(RefundBusiTypeEnum::getField)
                .filter(StringUtils::isNotBlank).collect(Collectors.toSet()));
        allFields.addAll(Arrays.stream(TransferBusiTypeEnum.class.getEnumConstants()).map(TransferBusiTypeEnum::getInField)
                .filter(StringUtils::isNotBlank).collect(Collectors.toSet()));
        allFields.addAll(Arrays.stream(TransferBusiTypeEnum.class.getEnumConstants()).map(TransferBusiTypeEnum::getOutField)
                .filter(StringUtils::isNotBlank).collect(Collectors.toSet()));
    }

    public Set<String> getAllFields() {
        return allFields;
    }

    @Async
    public void getRetailInfo(String appId, String openId, String orderNo, BigDecimal feeRate) {
        weixinB2bPayServiceImpl.switchover(appId);
        RetailInfoVo retailInfoVo = weixinB2bPayServiceImpl.getRetailInfo(new RetailInfoBo().setAppId(appId).setPayerId(openId));
        BigDecimal orderFeeRate = new BigDecimal("0.006");
        if (retailInfoVo != null && retailInfoVo.getStatus() == 1) {
            orderFeeRate = new BigDecimal("0.0022");
        }
        if (feeRate != null && orderFeeRate.compareTo(feeRate) == 0) {
            log.keyword(orderNo).info(String.format("费率 %s, %s", feeRate, orderFeeRate));
        } else {
            log.keyword(orderNo).warn(String.format("费率 %s, %s", feeRate, orderFeeRate));
        }
    }

    /**
     * 基于 code ，查询出对应 id 数组
     */
    public Set<Long> queryIdsByCodes(List<String> codes, Integer type) {
        return codes.stream().map(code -> {
            RemoteBaseDataVo vo = tradeBaseDataBizService.queryByCode(code, type);
            return vo == null ? -1 : vo.getId();
        }).collect(Collectors.toSet());
    }

    /**
     * 基于 code ，查询出对应 id 数组
     */
    public List<RemoteBaseDataVo> queryBaseListByIds(List<Long> ids, Integer type) {
        return ids.stream().map(id -> tradeBaseDataBizService.queryById(id, type)).filter(Objects::nonNull).toList();
    }


    /*
        支付单单号 = 10 + 183AF + 240701 + 7位随机字符串
     */
    public String getNextTradeNo(LocalDate date) {
        String dateStr = DateUtil.format(date.atStartOfDay(), "yyMMdd");
        return String.format("%s10%s%s%s", channelsProperties.getAccountNoPrefix(),
                getTenantId(), dateStr,  RandomUtil.randomStringUpper(7));
    }

    private String getTenantId() {
        return StrUtil.padPre(HexUtil.toHex(Integer.parseInt(TenantHelper.getTenantId())).toUpperCase(), 5, "0");
    }
    /*
        退款单单号  = 90 + 183AF + 240701 + 7位随机字符串
     */
    public String getNextTradeRefundNo(LocalDate date) {
        String dateStr = DateUtil.format(date.atStartOfDay(), "yyMMdd");
        return String.format("%s90%s%s%s", channelsProperties.getAccountNoPrefix(),
                getTenantId(), dateStr,  RandomUtil.randomStringUpper(7));
    }

    /*
        分账流水号 = ******** + 12位随机数字
     */
    public String getNextSplitNo(LocalDate date) {
        String dateStr = DateUtil.format(date.atStartOfDay(), "yyyyMMdd");
        return String.format("%s%s%s", channelsProperties.getAccountNoPrefix(), dateStr,  RandomUtil.randomNumbers(12));
    }

    /*
        结算单 = ******** + 8位随机数字
     */
    public String getNextAvailNo(LocalDate date) {
        String dateStr = DateUtil.format(date.atStartOfDay(), "yyyyMMdd");
        return String.format("%s%s%s", channelsProperties.getAccountNoPrefix(), dateStr,  RandomUtil.randomNumbers(8));
    }

    /*
        提现单 = ******** + 4位顺序号 + 3位随机数字
     */
    public String getNextCashNo(LocalDate date) {
        String dateStr = DateUtil.format(date.atStartOfDay(), "yyyyMMdd");
        String redisKey = TradeCacheNames.NO_ACC_CASH + dateStr;
        RAtomicLong atomicLong = RedisUtils.getClient().getAtomicLong(redisKey);
        long no = atomicLong.incrementAndGet();
        if (no == 1) {
            RedisUtils.getClient().getBucket(redisKey).expire(Duration.ofDays(30));
        }
        return String.format("%s%s%04d%s", channelsProperties.getAccountNoPrefix(), dateStr, no, RandomUtil.randomNumbers(3));
    }
    /*
        佣金提现单 = Y******** + 4位顺序号 + 3位随机数字
     */
    public String getNextCommCashNo(LocalDate date) {
        String dateStr = DateUtil.format(date.atStartOfDay(), "yyyyMMdd");
        String redisKey = TradeCacheNames.NO_COMM_CASH + dateStr;
        RAtomicLong atomicLong = RedisUtils.getClient().getAtomicLong(redisKey);
        long no = atomicLong.incrementAndGet();
        if (no == 1) {
            RedisUtils.getClient().getBucket(redisKey).expire(Duration.ofDays(30));
        }
        return String.format("%sY%s%04d%s", channelsProperties.getAccountNoPrefix(), dateStr, no, RandomUtil.randomNumbers(3));
    }
    /*
        加扣款单 = KD ******** + 5位随机数字
     */
    public String getNextDeductionNo(LocalDate date) {
        String dateStr = DateUtil.format(date.atStartOfDay(), "yyyyMMdd");
        return String.format("KD%s%S", dateStr, RandomUtil.randomNumbers(5));
    }
    /*
        换供应商 = HS 240701 + 8位随机数字
     */
    public String getNextTransferChangeNo(LocalDate date) {
        String dateStr = DateUtil.format(date.atStartOfDay(), "yyMMdd");
        return String.format("HS%s%S", dateStr, RandomUtil.randomNumbers(8));
    }
    /*
        充值单 = CZ 240701 + 4位随机数字
     */
    public String getNextChargeNo(LocalDate date) {
        String dateStr = DateUtil.format(date.atStartOfDay(), "yyMMdd");
        return String.format("CZ%s%S", dateStr, RandomUtil.randomNumbers(4));
    }

    /*
        平安端关联代码
     */
    public String getNextOutOrgCode() {
        return "KH" + RandomUtil.randomNumbers(10);
    }
}
