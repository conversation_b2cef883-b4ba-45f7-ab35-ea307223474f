package cn.xianlink.trade.domain;

import cn.xianlink.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * 划转流水对象 trade_acc_transfer
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("trade_acc_transfer")
public class TradeAccTransfer extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 业务id
     */
    private Long transId;

    /**
     * 流水单号，对应业务单号
     */
    private String transNo;
    /**
     * 记账日期
     */
    private LocalDate transDate;
    /**
     * 账务机构id
     */
    private Long acctOrgId;
    /**
     * 业务机构id
     */
    private Long transOrgId;
    /**
     * 0 总仓 1 城市仓 2 供应商
     */
    private Integer orgType;

    /**
     * 收入分账机构id
     */
    private Long orgId;

    /**
     * 收入分账机构代码
     */
    private String orgCode;
    /**
     * 部门id
     */
    private Long deptId;
    /**
     * 划转单类型 TA换供应商;TB供应商扣款;TC城市仓扣款;TD总仓自罚单;
     */
    private String busiType;
    /**
     * 累加支付单的退款金额字段 单维度
     */
    private String busiField;
    /**
     * 对接外部代码
     */
    private String outOrgCode;
    /**
     * 对接外部账户代码
     */
    private String outAcctCode;

    /**
     * 0 总仓 1 城市仓 2 供应商 3
     */
    private Integer sourceOrgType;

    /**
     * 支出分账机构id
     */
    private Long sourceOrgId;

    /**
     * 分账机构代码
     */
    private String sourceOrgCode;
    /**
     * 部门id
     */
    private Long sourceDeptId;

    /**
     * 支出累加字段 单维度统计
     */
    private String sourceBusiField;
    /**
     * 对接外部账户代码
     */
    private String sourceOutOrgCode;
    /**
     * 对接外部账户代码
     */
    private String sourceOutAcctCode;
    /**
     * 出账分账单号
     */
    private String sourceSplitNo;
    /**
     * 出账结算单号
     */
    private String sourceAvailNo;
    /**
     * 分账单号
     */
    private String splitNo;
    /**
     * 结算单号
     */
    private String availNo;
    /**
     * 关联id
     */
    private Long relateId;
    /**
     * 关联单号
     */
    private String relateNo;
    /**
     * 订单单号
     */
    private String orderNo;
    /**
     * 调整金额
     */
    private BigDecimal transAmt;
    /**
     * 费用金额
     */
    private BigDecimal feeAmt;
    /**
     * 实际金额
     */
    private BigDecimal splitAmt;
    /**
     * 操作状态 0 未处理 1 已确认
     */
    private Integer operStatus;
    /**
     * 支付发起时间
     */
    private Date infTime;
    /**
     * 重试次数
     */
    private Integer infRetries;
    /**
     * 接口状态 0 未支付;1 支付完成 ;2 支付取消; 10 接口未调用; 11 接口失败
     */
    private Integer infSource;
    /**
     * 接口状态 0 未支付;1 支付完成 ;2 支付取消;  3同名转成功 10 接口未调用; 11 接口失败
     */
    private Integer infStatus;

    /**
     * 接口失败原因
     */
    private String infReason;
    /**
     * 6250同名银行流水号JzbPlatformSeqNo
     */
    private String outSourceNo;
    /**
     * 银行流水号BankOrderNo 回执的
     */
    private String outTradeNo;
    /**
     * 对接渠道支付完成时间 回执的
     */
    private Date outSuccessTime;
    /**
     * 分账状态 0未结 1结算
     */
    private Integer status;
    /**
     * 备注
     */
    private String remark;
    /**
     * 删除标志（0代表存在 存入这条数据的主键代表删除）
     */
    // @TableLogic(delval = "id")
    private Long delFlag;

}
