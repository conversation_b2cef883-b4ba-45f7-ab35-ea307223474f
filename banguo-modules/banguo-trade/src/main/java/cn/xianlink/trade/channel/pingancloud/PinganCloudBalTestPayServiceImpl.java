package cn.xianlink.trade.channel.pingancloud;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.xianlink.common.api.enums.trade.PayInfStatusEnum;
import cn.xianlink.common.api.enums.trade.RefundInfStatusEnum;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.trade.domain.bo.TradePayBo;
import cn.xianlink.trade.domain.vo.TradeAccSplitVo;
import cn.xianlink.trade.domain.vo.TradePayRefundVo;
import cn.xianlink.trade.domain.vo.TradePayVo;
import com.alibaba.fastjson.JSONObject;
import lombok.CustomLog;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * <AUTHOR>
 * @date 2025-02-25
 */
@CustomLog
@Component
@ConditionalOnProperty(prefix = "channels", name = "debug-local", havingValue = "true", matchIfMissing = false)
public class PinganCloudBalTestPayServiceImpl extends PinganCloudBalPayServiceImpl {

    @Override
    public TradePayBo pay(TradePayVo vo) {
        // throw new ServiceException("余额不足，不能支付");
        TradePayBo respBo = payQuery(vo);
        respBo.setCallbackWeixin(new JSONObject()
                .fluentPut("channel", vo.getChannel())
                .fluentPut("params", new JSONObject()).toJSONString());
        return respBo;
    }

    @Override
    public void paySplit(String merchantNo, TradePayVo vo) {
        if (vo.getSplits().size() != 1) {
            throw new ServiceException(String.format("分账参数不合法，%s", vo.getSplits().size()));
        }
        Date infTime = Convert.toDate(DateUtil.now());
        for (TradeAccSplitVo splitVo : vo.getSplits()) {
            splitVo.setInfStatus(PayInfStatusEnum.SUCCESS.getCode());
            splitVo.setInfReason("");
            splitVo.setInfTime(infTime);
            splitVo.setOutSuccessTime(infTime);
            splitVo.setOutTradeNo("11111111");
            if (splitVo.getTransAmt().compareTo(BigDecimal.ZERO) == 0) {
                splitVo.setInfReason("分帐金额为零");
            }
        }
    }

    @Override
    public void refundSplit(String outChannelNo, TradePayRefundVo vo) {
        if (vo.getSplits().size() != 1) {
            throw new ServiceException(String.format("分账参数不合法，%s", vo.getSplits().size()));
        }
        // throw new ServiceException(String.format("[ERR180]，%s,%s", outChannelNo, vo.getSplits().size()));
        Date infTime = Convert.toDate(DateUtil.now());
        for (TradeAccSplitVo splitVo : vo.getSplits()) {
            splitVo.setInfStatus(RefundInfStatusEnum.SUCCESS.getCode());
            splitVo.setInfReason("");
            splitVo.setInfTime(infTime);
            splitVo.setOutSuccessTime(infTime);
            splitVo.setOutTradeNo("11111111");
            if (splitVo.getTransAmt().compareTo(BigDecimal.ZERO) == 0) {
                splitVo.setInfReason("分帐金额为零");
            }
        }
    }


}
