package cn.xianlink.trade.domain.bo.sup;

import cn.xianlink.common.mybatis.core.page.PageQuery;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.time.LocalDate;


/**
 * 客户订单详情
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@Data
@Accessors(chain = true)
public class SupTradeAccSupDeptAcctQueryBo extends PageQuery {

    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 供应商id（内部使用）
     */
    private Long supplierId;
    /**
     * 供应商代码（内部使用）
     */
    private String supplierCode;
    /**
     * 提现开始日期
     */
    @NotNull(message = "开始日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate transDateStart;
    /**
     * 提现结束日期
     */
    @NotNull(message = "结束日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate transDateEnd;

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 结算状态
     * 0-待结算（未全部完结）
     * 1-已结算（未全部完结）
     * 2-已结算（全部完结）
     */
    private Integer settleStatus;

    /**
     * 提现状态，未提现，待审核，处理中，提现完成
     * cn.xianlink.common.api.enums.trade.AccountStatusEnum
     */
    private Integer accountStatus;
}
