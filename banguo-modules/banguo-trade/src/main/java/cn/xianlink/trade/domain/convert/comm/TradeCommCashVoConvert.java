package cn.xianlink.trade.domain.convert.comm;

import cn.xianlink.trade.api.domain.vo.RemoteCashJobVo;
import cn.xianlink.trade.domain.vo.comm.TradeCommCashVo;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

/**
 * 数据转换器
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TradeCommCashVoConvert extends BaseMapper<TradeCommCashVo, RemoteCashJobVo> {
}
