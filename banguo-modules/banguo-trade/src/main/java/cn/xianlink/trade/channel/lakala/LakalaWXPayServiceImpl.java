package cn.xianlink.trade.channel.lakala;


import cn.binarywang.wx.miniapp.util.WxMaConfigHolder;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.extra.ftp.Ftp;
import cn.xianlink.common.api.enums.trade.AccountFileTypeEnum;
import cn.xianlink.common.api.enums.trade.PayInfStatusEnum;
import cn.xianlink.common.api.enums.trade.RefundInfStatusEnum;
import cn.xianlink.common.core.constant.HttpStatus;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.trade.api.domain.bo.OrderPayBo;
import cn.xianlink.trade.api.domain.bo.OrderRefundBo;
import cn.xianlink.trade.channel.PaymentService;
import cn.xianlink.trade.channel.lakala.constant.LakalaPayApiEnum;
import cn.xianlink.trade.channel.lakala.constant.LakalaPayInfStatusEnum;
import cn.xianlink.trade.channel.weixinb2b.WeixinB2bPayServiceImpl;
import cn.xianlink.trade.constant.Constants;
import cn.xianlink.trade.domain.bo.TradePayBo;
import cn.xianlink.trade.domain.bo.TradePayRefundBo;
import cn.xianlink.trade.domain.vo.TradePayRefundVo;
import cn.xianlink.trade.domain.vo.TradePayVo;
import cn.xianlink.trade.domain.vo.excel.ExcelDataInfo;
import cn.xianlink.trade.utils.ZipFileUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.lkl.laop.sdk.Config;
import com.lkl.laop.sdk.LKLSDK;
import com.lkl.laop.sdk.exception.SDKException;
import jakarta.servlet.http.HttpServletRequest;
import lombok.CustomLog;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.*;

/*
    支付服务费：  订单金额x技术服务费费率   （四舍五入）
    退款服务费：  退款金额x技术服务费费率   （向下取整， 如 0.238 ，取整后 0.23）
                 全部退款后（可多次）， 最后退款总费用是全部退回的
 */
@CustomLog
@Component
public class LakalaWXPayServiceImpl extends WeixinB2bPayServiceImpl {

    final transient Map<String, LakalaWXConfig> wxConfigs = new HashMap<>();
    final transient Map<String, String> wxAppIds = new HashMap<>();

    @Override
    public void init(String wxAppId, Properties properties) {
        LakalaWXConfig wxConfig = new LakalaWXConfig();
        BeanUtil.copyProperties(properties, wxConfig, Constants.BeanCopyIgnoreNullValue);
        wxConfig.setRegionWhIps(new HashMap<>());
        String ipsKey = "regionWhIps.";
        for (String key : properties.stringPropertyNames()) {
            if (key.startsWith(ipsKey)) {
                // 移除带哦
                List<String> ips = Arrays.stream(properties.getProperty(key).split(";"))
                        .filter(s -> s.trim().length() > 2)
                        .map(s -> s.trim().substring(0, s.trim().length() - 1)).toList();
                wxConfig.getRegionWhIps().put(key.substring(ipsKey.length()), ips);
            }
        }
        wxConfigs.put(wxAppId, wxConfig);
        // wxAppId, path, merchantId 是同渠道下， 一对一 对一 的
        this.wxAppIds.put(wxConfig.getCallbackPath(), wxAppId);
        this.wxAppIds.put(wxConfig.getMerchantId(), wxAppId);
        try {
            Config config = new Config();
            config.setAppId(wxConfig.getAppId()); // 不是小程序的appId
            config.setSerialNo(wxConfig.getSerialNo());
            config.setPriKeyPath(wxConfig.getPriKeyPath());
            config.setLklCerPath(wxConfig.getLklCerPath());
            config.setLklNotifyCerPath(wxConfig.getLklNotifyCerPath());
            config.setSm4Key(wxConfig.getSm4Key());
            config.setServerUrl(wxConfig.getServerUrl());
            LKLSDK.init(config);
        } catch (SDKException se) {
            throw new ServiceException(se);
        }
    }

    @Override
    public List<String> getWxAppIds() {
        return wxConfigs.keySet().stream().toList();
    }

    @Override
    public void switchover(String wxAppId) {
        if (!this.wxAppIds.containsKey(wxAppId)) {
            throw new ServiceException("无法找到对应小程序配置信息");
        }
        WxMaConfigHolder.set(wxAppId);
    }

    @Override
    public String switchByPath(String path) {
        String wxAppId = this.wxAppIds.get(path);
        if (StringUtils.isNotBlank(wxAppId)) {
            switchover(wxAppId);
            return wxAppId;
        } else {
            throw new ServiceException("无法找到对应小程序配置信息");
        }
    }

    @Override
    public void switchByMerchant(String merchantId) {
        String wxAppId = this.wxAppIds.get(merchantId);
        if (StringUtils.isNotBlank(wxAppId)) {
            switchover(wxAppId);
        } else {
            throw new ServiceException("无法找到对应小程序配置信息");
        }
    }
    /*
     *  不同客户，商户号不同（用于线下）
     */
    @Override
    public String getMerchantId() {
        return wxConfigs.get(WxMaConfigHolder.get()).getMerchantId();
    };

    @Override
    public int payValidate(OrderPayBo bo) {
        if(StringUtils.isEmpty(bo.getPayerId())){
            throw new ServiceException("openId不能为空");
        }
        if(StringUtils.isEmpty(bo.getAppId())){
            throw new ServiceException("wxAppId不能为空");
        }
        if(!wxConfigs.containsKey(bo.getAppId())) {
            throw new ServiceException("不支持的wxAppId");
        }
        if(CollectionUtil.isEmpty(bo.getSplits())){
            throw new ServiceException("分账数据不能为空");
        }
        if(StringUtils.isEmpty(bo.getPayerIp())){
            // 设置城市仓默认ip， 如果获取不到， 报错
            Map<String, List<String>> whIps = wxConfigs.get(bo.getAppId()).getRegionWhIps();
            List<String> ips = whIps.get(String.valueOf(bo.getAcctOrgId()));
            if (CollectionUtil.isEmpty(ips)) {
                ips = whIps.values().iterator().next(); // 要保证第一个不为空
            }
            bo.setPayerIp(ips.get(RandomUtil.randomInt(ips.size())) + RandomUtil.randomInt(20, 240));
        }
        return 0;
    }

    @Override
    public TradePayBo pay(TradePayVo vo) {
        if (vo.getPaySplitAmt().compareTo(BigDecimal.ZERO) == 0) {
            TradePayBo respBo = new TradePayBo();
            respBo.setPayInfStatus(PayInfStatusEnum.SUCCESS.getCode());
            respBo.setOutSuccessTime(Convert.toDate(DateUtil.now()));
            respBo.setCallbackWeixin(new JSONObject()
                    .fluentPut("channel", vo.getChannel())
                    .fluentPut("params", new JSONObject()).toJSONString());
            return respBo.setId(vo.getId());
        } else {
            String wxAppId = WxMaConfigHolder.get();
            LakalaWXConfig wxConfig = wxConfigs.get(wxAppId);
            JSONObject body = new JSONObject();
            body.put("merchant_no", wxConfig.getMerchantId());
            body.put("term_no", wxConfig.getTermNo());
            body.put("out_trade_no", vo.getTradeNo());
            body.put("account_type", "WECHAT");
            body.put("trans_type", "71");
            body.put("total_amount", LakalaUtil.yuanToFen(vo.getPaySplitAmt()));
            body.put("location_info", new JSONObject().fluentPut("request_ip", vo.getPayerIp()));
            // body.put("subject", vo.getOrderNo());
            body.put("remark", vo.getOrderNo());
            if (StringUtils.isNotBlank(wxConfig.getCallbackUrl())) {
                body.put("notify_url", wxConfig.getCallbackUrl());
            }
            body.put("acc_busi_fields", new JSONObject()
                    .fluentPut("sub_appid", wxAppId)
                    .fluentPut("user_id", vo.getPayerId()));
            try {
                // 两种异常
                JSONObject resp = post(vo.getOrderNo(), "pay", LakalaPayApiEnum.PAY, body);
                TradePayBo respBo = new TradePayBo();
                respBo.setId(vo.getId());
                respBo.setPayInfStatus(PayInfStatusEnum.UNPAID.getCode());
                respBo.setOutTradeNo(resp.getString("trade_no"));
                respBo.setCallbackWeixin(new JSONObject()
                        .fluentPut("channel", vo.getChannel())
                        .fluentPut("params", new JSONObject()
                                .fluentPut("appId", resp.getString("app_id"))
                                .fluentPut("timeStamp", resp.getString("time_stamp"))
                                .fluentPut("nonceStr", resp.getString("nonce_str"))
                                .fluentPut("package", resp.getString("package"))
                                .fluentPut("signType", resp.getString("sign_type"))
                                .fluentPut("paySign", resp.getString("pay_sign"))).toJSONString());
                return respBo;
            } catch (ServiceException se) {
                throw se;
            }
        }
    }

    @Override
    public TradePayBo payClose(TradePayVo vo) {
        LakalaWXConfig wxConfig = wxConfigs.get(WxMaConfigHolder.get());
        JSONObject body = new JSONObject();
        body.put("merchant_no", wxConfig.getMerchantId());
        body.put("term_no", wxConfig.getTermNo());
        body.put("origin_out_trade_no", vo.getTradeNo());
        body.put("location_info", new JSONObject().fluentPut("request_ip", vo.getPayerIp()));
        TradePayBo respBo = new TradePayBo();
        // 仅返回要更新的值！！！！！ 新创建对象， 更新数据库
        try {
            JSONObject resp = post(vo.getOrderNo(), "payClose", LakalaPayApiEnum.PAY_CLOSE, body);
            respBo.setInfStatus(LakalaPayInfStatusEnum.getInfStatus(resp.getString("OldOrderStatus")));
            respBo.setPayInfStatus(respBo.getInfStatus());
            respBo.setCloseTime(Convert.toDate(DateUtil.now()));
        } catch (ServiceException se) {
            if (se.getMessage().contains("[T6519]") || se.getMessage().contains("[T6535]")) {
                // 原支付订单已关闭,不能申请撤销[T6519], 交易已关闭,订单不能重复关闭 [T6535], 返回关闭状态
                log.keyword(vo.getOrderNo(), "payClose").warn("[T6519] 转异常", se);
                respBo.setInfStatus(PayInfStatusEnum.CANCEL.getCode());
                respBo.setPayInfStatus(respBo.getInfStatus());
            } else {
                throw se;
            }
        }
        respBo.setId(vo.getId());
        respBo.setInfReason("");
        return respBo;
    }

    @Override
    public TradePayBo payQuery(TradePayVo vo) {
        LakalaWXConfig wxConfig = wxConfigs.get(WxMaConfigHolder.get());
        JSONObject body = new JSONObject();
        body.put("mchid", wxConfig.getMerchantId());
        body.put("out_trade_no", vo.getTradeNo());
        try {
            JSONObject resp = post(vo.getOrderNo(), "payQuery", LakalaPayApiEnum.PAY_QUERY, body);
            return payRespToBo(vo.getId(), null, resp);
        } catch (ServiceException se) {
            if (se.getMessage().contains("[BBS11114]")) {
                // 订单不存在[9403201] , 返回null
                log.keyword(vo.getOrderNo(), "payQuery").warn("[BBS11114] 转异常", se);
                return null;
            }
            throw se;
        }
    }

    private TradePayBo payRespToBo(Long payId, String payNo, JSONObject resp) {
        LakalaWXConfig wxConfig = wxConfigs.get(WxMaConfigHolder.get());
        if (wxConfig == null) {
            log.keyword("payCallback").warn("拉卡拉回调不是本环境数据");
            return null;
        }
        TradePayBo respBo = new TradePayBo();
        // TODO    trade_status  trade_state
        respBo.setPayInfStatus(LakalaPayInfStatusEnum.getInfStatus(resp.getString("trade_state")));
        respBo.setInfReason("");
        respBo.setOutTradeNo(resp.getString("trade_no")); //
        respBo.setOutChannelNo(resp.getString("acc_trade_no")); // 拉卡拉支付订单号
        respBo.setOutFeeAmt(BigDecimal.ZERO);
        respBo.setOutCouponAmt(BigDecimal.ZERO);
        BigDecimal payerAmount = LakalaUtil.fenToYuan(resp.getString("payer_amount"));
        if (PayInfStatusEnum.SUCCESS.getCode().equals(respBo.getPayInfStatus())) {
            respBo.setOutDiscountAmt(LakalaUtil.fenToYuan(resp.getString("total_amount")).subtract(payerAmount));
        } else {
            respBo.setOutDiscountAmt(BigDecimal.ZERO);
        }
        respBo.setOutCashAmt(payerAmount);
        respBo.setOutSuccessTime(LakalaUtil.timeToDate(resp.getString("trade_time")));  // query 才有
        respBo.setPayerId(resp.getString("user_id2"));
        // 两种主键
        respBo.setId(payId);
        respBo.setTradeNo(payNo);
        return respBo;
    }

    @Override
    public JSONObject callback(String reqBody, HttpServletRequest request) {
        LakalaWXConfig wxConfig = wxConfigs.get(WxMaConfigHolder.get());
        String authorization = request.getHeader("Authorization");
        try {
            LKLSDK.notificationHandle(reqBody, authorization, wxConfig.getAppId());
            String body = LKLSDK.sm4Decrypt(reqBody, wxConfig.getAppId());
            log.keyword("payCallback").info("拉卡拉回调 {}", body);
            return JSON.parseObject(body);
        } catch (SDKException se) {
            throw new ServiceException(se);
        }
    }

    @Override
    public TradePayBo payCallback(JSONObject resp) {
        return payRespToBo(null, resp.getString("out_trade_no"), resp);
    }

    @Override
    public int refundValidate(OrderRefundBo bo, TradePayVo payVo) {
        if(CollectionUtil.isEmpty(bo.getSplits())){
            throw new ServiceException("分账数据不能为空");
        }
        // 退款先同步订单的 OutChannelNo 字段， 后续退款完成修改成退款对应的 拉卡拉退单号
        bo.setOutChannelNo(payVo.getOutChannelNo());
        return 0;
    }

    @Override
    public TradePayRefundBo refund(TradePayRefundVo vo) {
        /*
            a、订单如需多次退款，请等待前一次退款完成后再发起调用
            b、退款接口只是发起退款请求，不表示退款成功，请2分钟后调用退款查询结果轮询退款状态
         */
        if (vo.getRefundSplitAmt().compareTo(BigDecimal.ZERO) == 0) {
            TradePayRefundBo respBo = new TradePayRefundBo();
            respBo.setRefundInfStatus(RefundInfStatusEnum.SUCCESS.getCode());
            respBo.setOutSuccessTime(Convert.toDate(DateUtil.now()));
            return respBo.setId(vo.getId());
        } else {
            LakalaWXConfig wxConfig = wxConfigs.get(WxMaConfigHolder.get());
            JSONObject body = new JSONObject();
            body.put("merchant_no", wxConfig.getMerchantId());
            body.put("term_no", wxConfig.getTermNo());
            body.put("origin_out_trade_no", vo.getTradeNo()); // 对应订单号
            body.put("out_trade_no", vo.getTradeRefundNo()); // 同一退款单号多次请求只退一笔
            body.put("refund_amount", LakalaUtil.yuanToFen(vo.getRefundSplitAmt()));
            body.put("location_info", new JSONObject().fluentPut("request_ip", vo.getPayerIp()));
            JSONObject resp = post(vo.getRefundNo(), "refund", LakalaPayApiEnum.PAY_REFUND, body);
            TradePayRefundBo respBo = new TradePayRefundBo();
            respBo.setId(vo.getId());
            refundRespToBo(respBo, resp);
            return respBo;
        }
    }

    private void refundRespToBo(TradePayRefundBo respBo, JSONObject resp) {
        respBo.setOutTradeNo(resp.getString("trade_no")); //  B2b退单号
        respBo.setOutCouponAmt(BigDecimal.ZERO);
        respBo.setOutDiscountAmt(BigDecimal.ZERO);
        Integer infStatus = LakalaPayInfStatusEnum.getInfStatus(resp.getString("trade_state"));
        Date tradeTime = LakalaUtil.timeToDate(resp.getString("trade_time"));
        if (infStatus == -1) {
            respBo.setRefundInfStatus(RefundInfStatusEnum.PROCESSING.getCode());
            respBo.setInfReason("");
        } else {
            respBo.setRefundInfStatus(infStatus);
            respBo.setInfReason(RefundInfStatusEnum.FAIL.getCode().equals(respBo.getRefundInfStatus()) ?
                    LakalaUtil.toString(resp.getString("trade_state_desc")) : "");
            respBo.setOutChannelNo(resp.getString("acc_trade_no"));
            respBo.setOutCashAmt(LakalaUtil.fenToYuan(resp.getString("total_amount")));
            respBo.setOutSuccessTime(tradeTime);
        }
    }

    @Override
    public TradePayRefundBo refundQuery(TradePayRefundVo vo) {
        LakalaWXConfig wxConfig = wxConfigs.get(WxMaConfigHolder.get());
        JSONObject body = new JSONObject();
        body.put("merchant_no", wxConfig.getMerchantId());
        body.put("term_no", wxConfig.getTermNo());
        body.put("out_trade_no", vo.getTradeRefundNo());
        try {
            JSONObject resp = post(vo.getRefundNo(), "refundQuery", LakalaPayApiEnum.PAY_REFUND_QUERY, body);
            TradePayRefundBo respBo = new TradePayRefundBo();
            respBo.setId(vo.getId());
            JSONArray refundSplitInfo = resp.getJSONArray("refund_split_info");
            if (refundSplitInfo != null ) {
                for (int i = 0; i < refundSplitInfo.size(); i++) {
                    JSONObject split = refundSplitInfo.getJSONObject(i);
                    if (vo.getTradeRefundNo().equals(split.getString("sub_trade_no"))) {
                        respBo.setInfReason("");
                        respBo.setOutTradeNo(resp.getString("sub_trade_no"));
                        respBo.setOutCouponAmt(BigDecimal.ZERO);
                        respBo.setOutDiscountAmt(BigDecimal.ZERO);
                        respBo.setRefundInfStatus(LakalaPayInfStatusEnum.getInfStatus(resp.getString("trade_state")));
                        respBo.setOutCashAmt(LakalaUtil.fenToYuan(resp.getString("refund_amount")));
                        respBo.setOutSuccessTime(LakalaUtil.timeToDate(resp.getString("trade_time")));
                        return respBo;
                    }
                }
            }
            return null;
        } catch (ServiceException se) {
            if (se.getMessage().contains("[BBS11114]")) {
                // 订单不存在[9403201] , 返回null
                log.keyword(vo.getRefundNo(), "refundQuery").warn("[BBS11114] 转异常", se);
                return null;
            }
            throw se;
        }
    }

    private JSONObject post(String orderNo, String refundNo, LakalaPayApiEnum constant, JSONObject body) {
        try {
            LakalaWXConfig wxConfig = wxConfigs.get(WxMaConfigHolder.get());
            String bodyStr = body.toJSONString();
            log.keyword(orderNo, refundNo).info("request = " + bodyStr);
            String url = wxConfig.getServerUrl() + constant.getUrl();
            bodyStr = LKLSDK.sm4Encrypt(bodyStr, wxConfig.getAppId());
            String respStr = LKLSDK.httpPost(url, bodyStr, wxConfig.getAppId());
            JSONObject resp = JSON.parseObject(respStr);
            if (!"BBS00000".equals(resp.getString("code"))) {
                log.keyword(orderNo, refundNo).warn("response = " + respStr);
                throw new ServiceException(String.format("%s[%s]",
                        resp.getString("msg"), resp.getString("code")), HttpStatus.WARN);
            } else {
                log.keyword(orderNo, refundNo).info("response = " + respStr);
                return resp.getJSONObject("resp_data");
            }
        } catch (ServiceException se) {
            throw se;
        } catch (SDKException sdke) {
            log.keyword(orderNo, refundNo).warn(sdke.getMessage(), sdke);
            throw new ServiceException(String.format("%s[%s]", sdke.getMessage(), sdke.getCode()), HttpStatus.WARN);
        } catch (Exception e) {
            log.keyword(orderNo, refundNo).error(e.getMessage(), e);
            throw new ServiceException("拉卡拉支付服务异常，请联系管理员", R.FAIL);
        }
    }

    @Override
    public <T> ExcelDataInfo<T> fileDownload(String fileType, LocalDate fileDate)  {
        /*
        LakalaWXConfig wxConfig = wxConfigs.get(WxMaConfigHolder.get());
        String date = LakalaUtil.dateToDate(fileDate);
        String[] hosts = wxConfig.getFtpUrl().split(";");
        try (Ftp ftp = new Ftp(hosts[0], Integer.parseInt(hosts[1]), hosts[2], hosts[3])) {
            // ftp.setMode(FtpMode.Passive);  切为被动， 默认是主动模式
            // ftp.cd("/opt/upload");
            // 商户号、交易日期、交易时间、终端号、网点名称、交易类型、拉卡拉流水号、原拉卡拉流水号、卡号、支付渠道、银行名称、交易金额、手续费、结算金额、商户订单号、支付端订单号、外部流水号、系统参考号、备注、支付方式
            String fileName = ftp.ls("/opt/upload").stream()
                    .filter(vo -> vo.equals(String.format("JY_%s_%s.csv", wxConfig.getAppId(), date))).findFirst().orElse(null);
            if (fileName == null) {
                return null;
            }
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ftp.download("/opt/upload", fileName, baos, StandardCharsets.UTF_8);
            baos.close();
            byte[] file = ZipFileUtils.unzip(baos.toByteArray());
            return new ExcelDataInfo<T>().setFile(file).setTransDate(fileDate)
                    .setFileType(AccountFileTypeEnum.LCZ.getCode()).setMerchantId(wxConfig.getMerchantId())
                    .setFileName(String.format("lakala_%s_%s.csv", wxConfig.getMerchantId(), date));
        } catch (Exception e) {
            log.error("拉卡拉", e);
            throw new ServiceException("拉卡拉文件下载异常", R.FAIL);
        }

         */
        return null;
    }


}
