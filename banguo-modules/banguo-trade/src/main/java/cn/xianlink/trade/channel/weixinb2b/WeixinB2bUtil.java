package cn.xianlink.trade.channel.weixinb2b;

import cn.hutool.core.date.DateUtil;
import cn.xianlink.common.core.utils.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

public class WeixinB2bUtil {
    public static BigDecimal fenToYuan(String fen){
        BigDecimal bigFen = new BigDecimal(StringUtils.isEmpty(fen) ? "0" : fen);
        BigDecimal percent = new BigDecimal("100");
        return bigFen.divide(percent, 2, RoundingMode.HALF_UP);
    }
    public static Integer yuanToFen(BigDecimal yuan){
        BigDecimal percent = new BigDecimal("100");
        return yuan.multiply(percent).intValueExact();
    }
    public static Date timeToDate(String date) {
        try {
            return DateUtil.parse(date, "yyyy-MM-dd HH:mm:ss").toJdkDate();
        } catch (Exception e) {
            return null;
        }
    }

    public static String toString(String str) {
        return str == null ? "" : str;
    }
}
