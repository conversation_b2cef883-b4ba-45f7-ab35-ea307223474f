package cn.xianlink.trade.channel.weixinb2b.constant;

import cn.hutool.core.util.ObjectUtil;

public enum WeixinB2bRefundInfStatusEnum {
    // 退款状态对应关系   转成 RefundInfStatusEnum 枚举值
    REFUND_INIT("REFUND_INIT", "退款单初始化", 0),
    REFUND_PROCESSING("REFUND_PROCESSING", "退款处理中", 0),
    REFUND_SUCC("REFUND_SUCC", "成功", 1),
    REFUND_FAIL("REFUND_FAIL", "失败", 2);

    private final String status;
    private final Integer infStatus;
    private final String desc;

    WeixinB2bRefundInfStatusEnum(String status, String desc, Integer infStatus) {
        this.status = status;
        this.infStatus = infStatus;
        this.desc = desc;
    }

    public String getStatus() {
        return status;
    }

    public Integer getInfStatus() {
        return infStatus;
    }

    public String getDesc() {
        return desc;
    }

    public static Integer getInfStatus(String status) {
        for (WeixinB2bRefundInfStatusEnum e : values()) {
            if (ObjectUtil.equal(e.getStatus(), status)) {
                return e.getInfStatus();
            }
        }
        return -1;
    }
}
