package cn.xianlink.trade.domain;

import cn.xianlink.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 分账变更流水对象 trade_acc_trans
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("trade_comm_cash_detail")
public class TradeCommCashDetail extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id主键    //差额退
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 业务id
     */
    private Long commTransId;
    /**
     * 业务类型 CH 提现; CE 过期;
     */
    private String cashType;
    /**
     * 提现id
     */
    private Long cashId;
    /**
     * 佣金金额
     */
    private BigDecimal commissionAmt;
    /**
     * 发薪金额
     */
    private BigDecimal salaryAmt;
    /**
     * 删除标志（0代表存在 存入这条数据的主键代表删除）
     */
    @TableLogic(delval = "id")
    private Long delFlag;


}
