package cn.xianlink.trade.channel.pingancloud.constant;

import cn.hutool.core.util.ObjectUtil;

public enum PinganCloudSplitAcctStatusEnum {
    // 分账查询状态  KFEJZB6146 	（0：成功，1：失败，2：异常,3:冲正，5：待处理）   转成 SplitAcctStatusEnum 枚举值
    SPLIT_ING("5", "待处理", 0),
    SPLIT_SUCCESS("0", "成功", 1),
    SPLIT_FAIL("1", "失败", 2),
    SPLIT_EXCEPTION("2", "异常", 2),
    SPLIT_CLOSE("3", "冲正", 1);

    private final String status;
    private final Integer acctStatus;
    private final String desc;

    PinganCloudSplitAcctStatusEnum(String status, String desc, Integer acctStatus) {
        this.status = status;
        this.desc = desc;
        this.acctStatus = acctStatus;
    }

    public String getStatus() {
        return status;
    }

    public Integer getAcctStatus() {
        return acctStatus;
    }

    public String getDesc() {
        return desc;
    }

    public static Integer getAcctStatus(String status) {
        for (PinganCloudSplitAcctStatusEnum e : values()) {
            if (ObjectUtil.equal(e.getStatus(), status)) {
                return e.getAcctStatus();
            }
        }
        return -1;
    }
}
