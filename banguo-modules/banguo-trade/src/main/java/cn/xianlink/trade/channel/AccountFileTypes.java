package cn.xianlink.trade.channel;

import cn.xianlink.common.api.enums.trade.AccountFileTypeEnum;
import cn.xianlink.trade.domain.vo.excel.*;

import java.util.List;
import java.util.Map;

public interface AccountFileTypes {

    /*
        交易网流水号是调用接口时  CnsmrSeqNo， 目前系统中没有记录，都使用具体的分账单号来对账
            1 CZ 支付， 分账支付单号 = 分账单号（接口 remark 中 suborderId）
                 充值： 同支付，分账记录仅有总仓
                 6276  最后一个字段， 对应系统中的订单号

            2 TX 退款  分账单号 = 分账退单号（接口 remark 中 subrefundId）
                 提现  分账单号 = CnsmrSeqNo（也是分账单号）， 备注 = 提现单号（接口中remark）    （不传remark时，备注 = 交易网流水号,）   提现类型无业务单号
                        提现接口 CashAmt （不含手续费），TX文件中交易金额（含手续费）， trade_acc_split.trans_amt （含手续费的）
            3 JY 6250 转账  分账单号 = CnsmrSeqNo   （接口中 OrderNo， 是业务单号 ）
                 6034 转账  分账单号 = CnsmrSeqNo    （接口中 OrderNo ， 是业务单号 ）
                 6164 转账退款  分账单号 = CnsmrSeqNo   （接口中 OrderNo， 是业务单号 ）
        微信 B2b 文件

     */
    Map<String, List<String>> EXCEL_HEAD = Map.of(
            AccountFileTypeEnum.CZ.getCode(), List.of("序号,交易网会员代码|@,子账户|@,子账户名称,交易金额|0.00,手续费|0.00,交易日期|DATE,交易时间|TIME,银行流水号|@,交易网流水号,备注,记账描述（在途充值、见证+收单充值、转账充值、挂账充值、见证+收单充值）,分账单号".split(",")),
            AccountFileTypeEnum.TX.getCode(), List.of("序号,交易网会员代码|@,子账户|@,子账户名称,交易金额|0.00,手续费|0.00,交易日期|DATE,交易时间|TIME,银行流水号|@,分账单号,备注,记账类型（提现、见证+收单退款、见证+收单退款撤销）,业务单号".split(",")),
            AccountFileTypeEnum.JY.getCode(), List.of("序号,记账标志(详见右侧，交易文件记账标志说明) ,转出交易网会员代码|@,转出子账户|@,转出子账户名称,转入交易网会员代码|@,转入子账户|@,转入子账户名称,交易金额|0.00,手续费|0.00,交易日期|DATE,交易时间|TIME,银行流水号|@,分账单号,备注,业务单号".split(",")),
            AccountFileTypeEnum.YE.getCode(), List.of("序号,交易网会员代码|@,子账户|@,可用金额|0.00".split(","))
//            AccountFileTypeEnum.JQ.getCode(), List.of("鉴权申请时间,鉴权类型(MI-小额转账鉴权申请、UN-银联鉴权申请),第三方交易流水号,见证流水号,交易网会员代码,见证子账户的账号,申请绑定的提现账户的账号".split(",")),
//            AccountFileTypeEnum.POS.getCode(), List.of("交易网代码,第三方流水,交易金额|0.00,状态(0:成功,1:失败,2:异常,5:处理中),商户模式(0-独立商户,1-集团商户),交易模式(0: 手续费 1: 手工退款),交易子帐户,对账日期,对账文件日期,交易流水,备注".split(",")),
//            AccountFileTypeEnum.JG.getCode(), List.of("交易日期,客户账号,借贷标志,交易金额|0.00,账面余额|0.00,对方账号,对方户名,核心流水号,业务流水号,交易流水号,摘要描述,备注".split(",")),
//            AccountFileTypeEnum.GJ.getCode(), List.of("账户所属机构,客户账号,核心流水号,业务流水号,交易流水号,交易日期,借贷标志,交易金额|0.00,账面余额|0.00,对方账号,对方户名,摘要描述,备注".split(","))
    );

    Map<String, Class<?>> EXCEL_COLUMN_CLAZZ = Map.of(
            AccountFileTypeEnum.CZ.getCode(), CZExcelColumnVo.class,
            AccountFileTypeEnum.TX.getCode(), TXExcelColumnVo.class,
            AccountFileTypeEnum.JY.getCode(), JYExcelColumnVo.class,
            AccountFileTypeEnum.YE.getCode(), YEExcelColumnVo.class,
            // 以下基本无用
//            AccountFileTypeEnum.JQ.getCode(), JQExcelColumnVo.class,
//            AccountFileTypeEnum.POS.getCode(), POSExcelColumnVo.class,
//            AccountFileTypeEnum.JG.getCode(), JGExcelColumnVo.class,
//            AccountFileTypeEnum.GJ.getCode(), GJExcelColumnVo.class
            AccountFileTypeEnum.WJY.getCode(), WJYExcelColumnVo.class,
            AccountFileTypeEnum.WZJ.getCode(), WZJExcelColumnVo.class
            // AccountFileTypeEnum.WYE.getCode(), WYEExcelColumnVo.class
    );

}
