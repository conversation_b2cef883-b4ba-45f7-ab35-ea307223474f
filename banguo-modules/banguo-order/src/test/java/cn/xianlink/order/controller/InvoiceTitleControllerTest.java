package cn.xianlink.order.controller;

import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.order.controller.platform.InvoiceTitleController;
import cn.xianlink.order.domain.bo.InvoiceTitleBo;
import cn.xianlink.order.domain.vo.InvoiceTitleVo;
import cn.xianlink.order.service.IInvoiceTitleService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 发票抬头控制器测试类
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@ExtendWith(MockitoExtension.class)
class InvoiceTitleControllerTest {

    @Mock
    private IInvoiceTitleService invoiceTitleService;

    @InjectMocks
    private InvoiceTitleController invoiceTitleController;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(invoiceTitleController).build();
        objectMapper = new ObjectMapper();
    }

    @Test
    void testQueryList() throws Exception {
        // 准备测试数据
        InvoiceTitleVo vo = new InvoiceTitleVo();
        vo.setId(1L);
        vo.setCustomerId(1001L);
        vo.setTitleName("深圳XXYY科技有限公司");
        vo.setTaxNumber("91440011223344XXCC");
        vo.setTitleType("company");

        TableDataInfo<InvoiceTitleVo> tableData = new TableDataInfo<>();
        tableData.setRows(Arrays.asList(vo));
        tableData.setTotal(1L);

        when(invoiceTitleService.queryPageList(any(InvoiceTitleBo.class), any(PageQuery.class)))
                .thenReturn(tableData);

        // 执行测试
        mockMvc.perform(get("/invoiceTitle/list")
                        .param("customerId", "1001")
                        .param("pageNum", "1")
                        .param("pageSize", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.rows").isArray())
                .andExpect(jsonPath("$.total").value(1));
    }

    @Test
    void testGetInfo() throws Exception {
        // 准备测试数据
        InvoiceTitleVo vo = new InvoiceTitleVo();
        vo.setId(1L);
        vo.setCustomerId(1001L);
        vo.setTitleName("深圳XXYY科技有限公司");
        vo.setTaxNumber("91440011223344XXCC");
        vo.setTitleType("company");

        when(invoiceTitleService.queryById(anyLong())).thenReturn(vo);

        // 执行测试
        mockMvc.perform(get("/invoiceTitle/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.id").value(1))
                .andExpect(jsonPath("$.data.titleName").value("深圳XXYY科技有限公司"));
    }

    @Test
    void testAdd() throws Exception {
        // 准备测试数据
        InvoiceTitleBo bo = new InvoiceTitleBo();
        bo.setCustomerId(1001L);
        bo.setTitleName("深圳XXYY科技有限公司");
        bo.setTaxNumber("91440011223344XXCC");
        bo.setTitleType("company");
        bo.setAddress("深圳市宝安区西乡街道XXYY路YXXX号XXYY大厦");
        bo.setPhone("0755-xxxxxxxx");

        when(invoiceTitleService.insertByBo(any(InvoiceTitleBo.class))).thenReturn(true);

        // 执行测试
        mockMvc.perform(post("/invoiceTitle")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(bo)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    void testEdit() throws Exception {
        // 准备测试数据
        InvoiceTitleBo bo = new InvoiceTitleBo();
        bo.setId(1L);
        bo.setCustomerId(1001L);
        bo.setTitleName("深圳XXYY科技有限公司");
        bo.setTaxNumber("91440011223344XXCC");
        bo.setTitleType("company");
        bo.setAddress("深圳市宝安区西乡街道XXYY路YXXX号XXYY大厦");
        bo.setPhone("0755-xxxxxxxx");

        when(invoiceTitleService.updateByBo(any(InvoiceTitleBo.class))).thenReturn(true);

        // 执行测试
        mockMvc.perform(put("/invoiceTitle")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(bo)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    void testRemove() throws Exception {
        when(invoiceTitleService.deleteWithValidByIds(any(List.class), any(Boolean.class)))
                .thenReturn(true);

        // 执行测试
        mockMvc.perform(delete("/invoiceTitle/1,2,3"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }
}
