package cn.xianlink.order.service;

import cn.xianlink.order.service.impl.OrderServiceImpl;
import cn.xianlink.product.api.domain.vo.RemoteSkuVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * 购物车+订单商品去重功能测试
 */
@Slf4j
@SpringBootTest
public class CartAndOrderSkuListTest {

    @Autowired
    private IOrderService orderService;

    @Test
    public void testGetRecentProductList() {
        // 测试参数
        Long regionWhId = 1L; // 请替换为实际的总仓ID
        Long testCustomerId = 1L; // 请替换为实际的客户ID
        Integer days = 7;
        String keyword = null; // 可以设置为具体的关键词进行测试

        try {
            List<RemoteSkuVo> result = orderService.getRecentProductList(regionWhId, testCustomerId, days, keyword);
            
            log.info("测试结果：");
            log.info("总仓ID: {}", regionWhId);
            log.info("客户ID: {}", testCustomerId);
            log.info("查询天数: {}", days);
            log.info("关键词: {}", keyword);
            log.info("返回商品数量: {}", result.size());

            // 打印前5个商品信息（按时间排序）
            for (int i = 0; i < Math.min(5, result.size()); i++) {
                RemoteSkuVo sku = result.get(i);
                log.info("商品 {} (按时间排序): ID={}, 名称={}, 规格={}",
                    i + 1, sku.getId(), sku.getSpuName(), sku.getSpuStandards());
            }
            
        } catch (Exception e) {
            log.error("测试失败", e);
        }
    }

    @Test
    public void testGetRecentProductListWithKeyword() {
        // 测试带关键词的查询
        Long regionWhId = 1L; // 请替换为实际的总仓ID
        Long testCustomerId = 1L; // 请替换为实际的客户ID
        Integer days = 7;
        String keyword = "苹果"; // 测试关键词

        try {
            List<RemoteSkuVo> result = orderService.getRecentProductList(regionWhId, testCustomerId, days, keyword);
            
            log.info("带关键词测试结果：");
            log.info("总仓ID: {}", regionWhId);
            log.info("客户ID: {}", testCustomerId);
            log.info("查询天数: {}", days);
            log.info("关键词: {}", keyword);
            log.info("返回商品数量: {}", result.size());

            // 验证返回的商品名称是否包含关键词，并检查排序
            for (int i = 0; i < result.size(); i++) {
                RemoteSkuVo sku = result.get(i);
                log.info("商品 {} (按时间排序): ID={}, 名称={}", i + 1, sku.getId(), sku.getSpuName());
                if (sku.getSpuName() != null && !sku.getSpuName().contains(keyword)) {
                    log.warn("警告：商品名称不包含关键词: {}", sku.getSpuName());
                }
            }
            
        } catch (Exception e) {
            log.error("带关键词测试失败", e);
        }
    }
}
