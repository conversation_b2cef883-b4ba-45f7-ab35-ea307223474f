# 客户端发票申请模块设计方案

## 1. 概述

本文档描述了基于RuoYi-Cloud-Plus框架在现有`platform/InvoiceController`中实现的客户端发票申请模块设计方案。该模块为客户端（采购商）提供发票申请、查询、管理等功能。

## 2. 技术架构

### 2.1 技术栈
- **基础框架**: RuoYi-Cloud-Plus 微服务通用权限管理系统
- **后端技术**: Spring Boot 3.x + Spring Cloud 2023.x + Spring Cloud Alibaba 2022.x
- **数据库**: MySQL 8.0+
- **缓存**: Redis 6.0+
- **JDK版本**: 17+

### 2.2 模块结构
```
banguo-modules/banguo-order/
├── controller/platform/           # 平台控制器（扩展客户端接口）
├── service/                       # 业务服务层
├── domain/
│   ├── bo/platform/              # 平台业务对象
│   └── vo/platform/              # 平台视图对象
└── resources/docs/               # 设计文档
```

## 3. 数据模型

### 3.1 核心实体关系
```
Customer (客户) 1:N Invoice (发票)
Invoice (发票) 1:N InvoiceItem (发票项)
InvoiceItem (发票项) N:1 OrderItem (订单项)
Invoice (发票) N:1 Supplier (供应商)
Invoice (发票) N:1 InvoiceTitle (发票抬头)
```

### 3.2 主要字段说明

#### Invoice (发票表)
- `customer_id`: 客户ID
- `supplier_id`: 供应商ID
- `invoice_title_id`: 发票抬头ID
- `invoice_amount`: 发票金额（从发票项汇总）
- `status`: 状态（applied申请中, uploaded已上传, audited_approved审核通过, audited_rejected审核拒绝）
- `invoice_type`: 发票类型（normal普票, special专票）
- `invoice_format`: 发票格式（electronic电子, paper纸质）
- `email_address`: 邮箱地址（电子发票）
- `mailing_address`: 邮寄地址（纸质发票）

#### InvoiceItem (发票项表)
- `invoice_id`: 发票ID
- `order_item_id`: 订单项ID
- `customer_id`: 客户ID

## 4. 接口设计

### 4.1 接口路径规范
- 基础路径: `/order/invoice/client`
- 遵循RESTful设计原则
- 使用Swagger/OpenAPI 3.0文档

### 4.2 核心接口

#### 4.2.1 未开票列表接口
```
GET /order/invoice/client/uninvoiced
```
**功能**: 统计指定时间范围内客户的订单项数据，按供应商维度分组
**计算规则**: 开票金额 = 实付金额 - 所有退款/少货/缺货等逆向金额
**特殊说明**: 此接口仅预留TODO标记，需要手动实现复杂的统计逻辑

#### 4.2.2 发票列表接口
```
GET /order/invoice/client/list
```
**功能**: 根据发票状态返回客户申请的发票列表，支持状态筛选
**实现状态**: 已完整实现

#### 4.2.3 申请开票接口
```
POST /order/invoice/client/apply
```
**功能**: 基于未开票列表数据生成发票，支持批量选择订单项
**实现状态**: 已完整实现，包含完整的校验逻辑

#### 4.2.4 发票详情接口
```
GET /order/invoice/client/{invoiceId}
```
**功能**: 返回发票详细信息，包括金额、类型、抬头、文件、物流信息、商品明细
**实现状态**: 已完整实现

#### 4.2.5 发票邮箱更新接口
```
PUT /order/invoice/client/{invoiceId}/email
```
**功能**: 允许编辑发票详情中的邮箱地址
**实现状态**: 已完整实现

## 5. 业务规则

### 5.1 权限控制
- 使用`LoginHelper.getLoginUser().getRelationId()`获取当前登录客户ID
- 客户只能查看和操作自己的发票数据
- 通过`customerId`进行数据隔离

### 5.2 数据校验
- 使用JSR-303注解进行参数校验
- 分组校验：`AddGroup`、`EditGroup`
- 邮箱格式校验：`@Email`
- 发票格式与地址匹配校验：
  - 电子发票必须填写邮箱地址
  - 纸质发票必须填写邮寄地址

### 5.3 系统字段处理
- **系统内置字段**：`createCode`、`createName`、`updateCode`、`updateName`、`createTime`、`updateTime`
- **自动维护**：这些字段由RuoYi-Cloud-Plus框架自动维护，不需要前端传参
- **禁止手动设置**：后端代码中不应手动设置这些字段的值
- **审计功能**：系统会自动记录创建人、修改人、创建时间、修改时间等信息

### 5.4 业务约束
- 订单项只能开票一次
- 发票申请后状态为"申请中"
- 只有特定状态的发票允许修改邮箱（applied、uploaded）
- 纸质发票必须填写邮寄地址
- 电子发票必须填写邮箱地址

## 6. 性能要求

### 6.1 响应时间
- 接口响应时间需控制在0.5秒以内
- 使用分页查询避免大数据量查询
- 合理使用索引优化查询性能

### 6.2 并发处理
- 使用`@RepeatSubmit()`防止重复提交
- 使用乐观锁处理并发更新

## 7. 实现状态

### 7.1 已完成的工作

#### 数据模型
- ✅ `ClientUnInvoicedVo` - 未开票列表视图对象
- ✅ `ClientUnInvoicedItemVo` - 未开票订单项视图对象
- ✅ `ClientInvoiceListVo` - 客户端发票列表视图对象
- ✅ `ClientInvoiceDetailVo` - 客户端发票详情视图对象
- ✅ `InvoiceItemDetailVo` - 发票商品明细视图对象
- ✅ `ClientUnInvoicedBo` - 未开票列表查询业务对象
- ✅ `ClientInvoiceListBo` - 客户端发票列表查询业务对象
- ✅ `ClientApplyInvoiceBo` - 申请开票业务对象
- ✅ `ClientUpdateInvoiceEmailBo` - 更新发票邮箱业务对象

#### 服务层
- ✅ `IInvoiceService` 接口扩展
- ✅ `InvoiceServiceImpl` 实现类扩展
- ✅ 完整的业务方法框架
- ✅ 详细的TODO标记和实现指导

#### 控制器层
- ✅ 在现有`platform/InvoiceController`中添加客户端接口
- ✅ 5个核心接口全部实现
- ✅ 完整的参数校验和权限控制
- ✅ Swagger/OpenAPI 3.0文档注解

### 7.2 接口实现状态

| 接口名称 | 路径 | 方法 | 状态 | 说明 |
|---------|------|------|------|------|
| 未开票列表 | `/client/uninvoiced` | GET | 🟡 待实现 | 框架完成，复杂统计逻辑待实现 |
| 发票列表 | `/client/list` | GET | ✅ 已完成 | 完整实现 |
| 申请开票 | `/client/apply` | POST | ✅ 已完成 | 完整实现，包含校验逻辑 |
| 发票详情 | `/client/{invoiceId}` | GET | ✅ 已完成 | 完整实现 |
| 更新邮箱 | `/client/{invoiceId}/email` | PUT | ✅ 已完成 | 完整实现 |

## 8. 核心业务逻辑

### 8.1 未开票列表统计（待实现）
```java
// TODO: 需要实现的核心逻辑
// 1. 查询指定时间范围内客户的订单项数据
// 2. 按供应商维度分组
// 3. 计算开票金额 = 实付金额 - 所有退款/少货/缺货等逆向金额
// 4. 统计商品件数、订单数量等信息
// 5. 生成订单日期区间描述
// 6. 判断是否可开票（金额大于0）
```

### 8.2 申请开票流程（已实现）
1. 校验订单项是否属于当前客户
2. 校验订单项是否已开票
3. 校验发票抬头是否属于当前客户
4. 生成发票记录
5. 生成发票项记录
6. 更新订单项开票状态

### 8.3 发票详情查询（已实现）
1. 校验发票是否属于当前客户
2. 查询发票基本信息
3. 查询发票抬头信息
4. 查询供应商信息
5. 查询发票文件信息
6. 查询物流信息（纸质发票）
7. 查询发票项明细
8. 计算汇总信息

## 9. 异常处理

### 9.1 业务异常
- 用户未登录异常
- 客户信息不完整异常
- 数据权限异常
- 业务规则校验异常
- 发票状态不允许修改异常

### 9.2 系统异常
- 使用全局异常处理器
- 统一异常响应格式

## 10. 日志记录

### 10.1 操作日志
- 使用`@Log`注解记录关键操作
- 记录申请开票、更新邮箱等操作

### 10.2 业务日志
- 记录发票状态变更
- 记录异常情况

## 11. 扩展性设计

### 11.1 状态扩展
- 发票状态支持扩展
- 支持自定义业务流程

### 11.2 字段扩展
- 预留扩展字段
- 支持自定义属性

## 12. 下一步工作

### 12.1 高优先级
1. **实现未开票列表统计逻辑**：这是最复杂的业务逻辑，需要跨表查询和复杂计算
2. **完善服务层的TODO方法**：实现所有标记为TODO的辅助方法
3. **数据库优化**：添加必要的索引，优化查询性能

### 12.2 中优先级
1. **集成测试**：端到端功能测试
2. **性能测试**：验证响应时间要求
3. **安全测试**：验证权限控制

### 12.3 低优先级
1. **监控告警**：配置接口监控和异常告警
2. **文档完善**：补充API使用示例
3. **代码优化**：重构和优化代码结构

## 13. 系统字段处理最佳实践

### 13.1 系统字段说明
RuoYi-Cloud-Plus框架内置了以下系统字段，用于审计和追踪：
- `createCode`: 创建人编码
- `createName`: 创建人姓名
- `updateCode`: 修改人编码
- `updateName`: 修改人姓名
- `createTime`: 创建时间
- `updateTime`: 修改时间

### 13.2 处理原则
1. **自动维护**：这些字段由框架的AOP切面自动维护
2. **禁止手动设置**：代码中不应手动设置这些字段的值
3. **前端无感知**：前端不需要传递这些字段
4. **数据库约束**：数据库层面这些字段通常设置为NOT NULL

### 13.3 代码示例

#### ✅ 正确的做法
```java
// 创建实体时，不设置系统字段
Invoice invoice = new Invoice();
invoice.setCustomerId(bo.getCustomerId());
invoice.setSupplierId(bo.getSupplierId());
// 系统字段由框架自动设置
baseMapper.insert(invoice);

// 更新实体时，不设置系统字段
Invoice updateInvoice = new Invoice();
updateInvoice.setId(invoiceId);
updateInvoice.setEmailAddress(newEmail);
// updateCode, updateName, updateTime 由框架自动设置
baseMapper.updateById(updateInvoice);
```

#### ❌ 错误的做法
```java
// 不要手动设置系统字段
Invoice invoice = new Invoice();
invoice.setCreateCode("user123");  // ❌ 错误
invoice.setCreateName("张三");      // ❌ 错误
invoice.setCreateTime(new Date()); // ❌ 错误
```

### 13.4 BO类设计
在业务对象(BO)类中：
- 继承`BaseEntity`获得系统字段
- 不在构造方法或setter中设置系统字段
- 在类注释中说明系统字段的处理方式

## 14. 总结

客户端发票申请模块已在现有的`platform/InvoiceController`中成功实现，严格遵循了RuoYi-Cloud-Plus框架的编码规范。特别注意了系统字段的正确处理方式，确保框架的自动审计功能正常工作。除了未开票列表的复杂统计逻辑需要进一步实现外，其他4个接口都已完整实现。整体架构设计合理，扩展性良好，为后续的功能完善奠定了坚实的基础。
