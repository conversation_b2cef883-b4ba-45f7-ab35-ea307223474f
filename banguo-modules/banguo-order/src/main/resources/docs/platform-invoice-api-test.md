# 客户端发票申请模块API测试文档

## 接口测试说明

本文档提供了在`platform/InvoiceController`中实现的客户端发票申请模块各个接口的测试用例和示例。

## 1. 未开票列表接口

### 接口信息
- **URL**: `GET /order/invoice/client/uninvoiced`
- **描述**: 查询客户未开票的订单项数据，按供应商分组
- **实现状态**: 🟡 框架完成，业务逻辑待实现

### 请求参数
```json
{
  "startTime": "2025-01-01 00:00:00",
  "endTime": "2025-07-30 23:59:59",
  "supplierName": "测试供应商",
  "minAmount": 100.00,
  "onlyInvoiceable": true,
  "orderBy": "invoiceAmount",
  "orderDirection": "desc"
}
```

### 响应示例
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "total": 10,
    "rows": [
      {
        "supplierId": 1001,
        "supplierName": "测试供应商A",
        "supplierCode": "SUP001",
        "invoiceAmount": 15000.00,
        "actualAmount": 16000.00,
        "reverseAmount": 1000.00,
        "totalItems": 50,
        "orderCount": 5,
        "earliestOrderTime": "2025-01-15 10:30:00",
        "latestOrderTime": "2025-07-20 16:45:00",
        "orderDateRange": "2025.01.15-2025.07.20",
        "canInvoice": true,
        "orderItems": [...]
      }
    ]
  }
}
```

## 2. 发票列表接口

### 接口信息
- **URL**: `GET /order/invoice/client/list`
- **描述**: 查询客户的发票列表（开票中和已开票）
- **实现状态**: ✅ 已完成

### 请求参数
```json
{
  "statusList": ["applied", "uploaded", "audited_approved"],
  "supplierName": "测试供应商",
  "invoiceType": "normal",
  "invoiceFormat": "electronic",
  "applyStartTime": "2025-01-01 00:00:00",
  "applyEndTime": "2025-07-30 23:59:59",
  "orderBy": "applyDate",
  "orderDirection": "desc"
}
```

### 响应示例
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "total": 5,
    "rows": [
      {
        "id": 1001,
        "invoiceNumber": "INV202507300001",
        "supplierId": 1001,
        "supplierName": "测试供应商A",
        "invoiceAmount": 15000.00,
        "invoiceType": "normal",
        "invoiceFormat": "electronic",
        "status": "applied",
        "statusDesc": "申请中",
        "invoiceTitle": "测试公司",
        "totalItems": 50,
        "applyDate": "2025-07-30 10:00:00",
        "orderDateRange": "2025.01.15-2025.07.20",
        "emailAddress": "<EMAIL>",
        "hasInvoiceFile": false,
        "fileCount": 0
      }
    ]
  }
}
```

## 3. 申请开票接口

### 接口信息
- **URL**: `POST /order/invoice/client/apply`
- **描述**: 申请开票，基于未开票列表数据生成发票
- **实现状态**: ✅ 已完成

### 请求参数
```json
{
  "supplierId": 1001,
  "invoiceTitleId": 2001,
  "invoiceType": "normal",
  "invoiceFormat": "electronic",
  "emailAddress": "<EMAIL>",
  "orderItemIds": [10001, 10002, 10003],
  "invoiceAmount": 15000.00,
  "totalItems": 50,
  "remark": "申请开票备注"
}
```

**注意**：
- 不需要传递系统字段：`createCode`、`createName`、`createTime`等
- `customerId`由系统根据登录用户自动设置
- 系统字段由RuoYi-Cloud-Plus框架自动维护

### 业务校验规则
- 电子发票必须填写邮箱地址
- 纸质发票必须填写邮寄地址
- 订单项必须属于当前客户
- 订单项不能重复开票

### 响应示例
```json
{
  "code": 200,
  "msg": "申请开票成功",
  "data": null
}
```

## 4. 发票详情接口

### 接口信息
- **URL**: `GET /order/invoice/client/{invoiceId}`
- **描述**: 查询发票详细信息
- **实现状态**: ✅ 已完成

### 响应示例
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "id": 1001,
    "invoiceNumber": "INV202507300001",
    "supplierInfo": {
      "supplierId": 1001,
      "supplierName": "测试供应商A",
      "supplierCode": "SUP001"
    },
    "invoiceTitleInfo": {
      "invoiceTitleId": 2001,
      "invoiceTitle": "测试公司",
      "taxNumber": "91110000********9X",
      "address": "北京市朝阳区测试街道123号",
      "bankAccount": "********90********9",
      "phone": "010-********"
    },
    "invoiceBasicInfo": {
      "invoiceAmount": 15000.00,
      "invoiceType": "normal",
      "invoiceFormat": "electronic",
      "status": "applied",
      "statusDesc": "申请中",
      "applyDate": "2025-07-30 10:00:00",
      "emailAddress": "<EMAIL>",
      "orderDateRange": "2025.01.15-2025.07.20"
    },
    "invoiceFiles": [
      {
        "fileName": "发票-测试供应商-399.98.pdf",
        "fileUrl": "https://example.com/invoice.pdf",
        "fileType": "pdf",
        "fileSize": 1024000,
        "uploadTime": "2025-07-30 15:00:00"
      }
    ],
    "logisticsInfo": null,
    "summaryInfo": {
      "totalAmount": 15000.00,
      "totalItems": 50,
      "orderCount": 5,
      "earliestOrderTime": "2025-01-15 10:30:00",
      "latestOrderTime": "2025-07-20 16:45:00",
      "actualAmount": 16000.00,
      "reverseAmount": 1000.00
    },
    "itemDetails": [
      {
        "invoiceItemId": 3001,
        "orderItemId": 10001,
        "orderId": 5001,
        "orderCode": "A2013456789O",
        "spuName": "石墨山小狗罐蛋【高质量】",
        "skuSpec": "规格1",
        "categoryName": "测试分类",
        "count": 4,
        "price": 100.00,
        "finalPrice": 95.00,
        "productAmount": 380.00,
        "actualAmount": 380.00,
        "reverseAmount": 0.00,
        "invoiceAmount": 380.00,
        "orderTime": "2025-01-15 10:30:00",
        "payTime": "2025-01-15 10:35:00",
        "imgUrl": "https://example.com/product.jpg",
        "supplierSpuCode": "SUP001_001"
      }
    ]
  }
}
```

## 5. 更新发票邮箱接口

### 接口信息
- **URL**: `PUT /order/invoice/client/{invoiceId}/email`
- **描述**: 更新发票的邮箱地址
- **实现状态**: ✅ 已完成

### 请求参数
```json
{
  "emailAddress": "<EMAIL>",
  "remark": "更新邮箱地址"
}
```

**注意**：
- 不需要传递系统字段：`updateCode`、`updateName`、`updateTime`等
- `invoiceId`和`customerId`由系统根据路径参数和登录用户自动设置
- 系统字段由RuoYi-Cloud-Plus框架自动维护

### 业务校验规则
- 只有"申请中"和"已上传"状态的发票允许修改邮箱
- 邮箱格式必须正确
- 发票必须属于当前客户

### 响应示例
```json
{
  "code": 200,
  "msg": "邮箱更新成功",
  "data": null
}
```

## 错误响应示例

### 用户未登录
```json
{
  "code": 401,
  "msg": "用户未登录或客户信息不完整",
  "data": null
}
```

### 参数校验失败
```json
{
  "code": 400,
  "msg": "参数校验失败：发票类型不能为空",
  "data": null
}
```

### 业务异常
```json
{
  "code": 500,
  "msg": "电子发票必须填写邮箱地址",
  "data": null
}
```

### 权限异常
```json
{
  "code": 403,
  "msg": "当前发票状态不允许修改邮箱地址",
  "data": null
}
```

## 测试注意事项

1. **认证要求**: 所有接口都需要用户登录，需要在请求头中携带有效的Token
2. **权限控制**: 客户只能查看和操作自己的发票数据
3. **数据隔离**: 通过`customerId`进行数据隔离，确保数据安全
4. **响应时间**: 接口响应时间应控制在0.5秒以内
5. **重复提交**: 申请开票和更新邮箱接口有防重复提交保护
6. **业务校验**: 严格的业务规则校验，确保数据一致性
7. **系统字段**: 不要在请求中传递系统字段(`createCode`、`createName`、`updateCode`、`updateName`等)
8. **自动审计**: 系统会自动记录创建人、修改人、创建时间、修改时间等信息

## 测试环境配置

- **测试环境**: test
- **基础URL**: `http://test-api.example.com`
- **认证方式**: Bearer Token
- **Content-Type**: `application/json`

## 接口实现状态总结

| 接口 | 状态 | 说明 |
|------|------|------|
| 未开票列表 | 🟡 待实现 | 框架完成，复杂统计逻辑待实现 |
| 发票列表 | ✅ 已完成 | 完整实现，可直接测试 |
| 申请开票 | ✅ 已完成 | 完整实现，包含完整校验逻辑 |
| 发票详情 | ✅ 已完成 | 完整实现，可直接测试 |
| 更新邮箱 | ✅ 已完成 | 完整实现，包含状态校验 |
