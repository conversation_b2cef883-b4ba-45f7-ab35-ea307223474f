# 发票抬头模块实现说明

## 概述
本模块实现了完整的发票抬头管理功能，包括增删改查、用户信息自动填充、性能优化等特性。

## 功能特性

### 1. 基础CRUD功能
- ✅ 新增发票抬头
- ✅ 修改发票抬头
- ✅ 删除发票抬头（逻辑删除）
- ✅ 查询发票抬头列表（分页）
- ✅ 查询发票抬头详情

### 2. 用户信息自动填充
- ✅ 使用 `LoginHelper.getLoginUser()` 获取当前登录用户
- ✅ 自动设置客户ID（customerId）
- ✅ 自动设置创建用户信息（createCode, createName）
- ✅ 自动设置修改用户信息（updateCode, updateName）

### 3. 数据验证
- ✅ 抬头类型差异化验证
  - 公司类型：税号必填
  - 个人类型：姓名必填
- ✅ 同一客户下抬头名称唯一性校验
- ✅ 基础字段验证（抬头名称、抬头类型必填）

### 4. 便民接口
- ✅ 获取当前用户发票抬头列表 `/my-titles`
- ✅ 获取默认发票抬头 `/default`
- ✅ 按抬头类型查询 `/by-type/{titleType}`

### 5. 性能优化
- ✅ 查询条件优化（使用右模糊匹配）
- ✅ 索引建议（详见SQL优化文档）
- ✅ 查询结果按创建时间倒序排列
- ✅ 分页查询优化

## API接口说明

### 基础接口

#### 1. 查询发票抬头列表
```http
GET /invoiceTitle/list?pageNum=1&pageSize=10&titleType=company
```

#### 2. 获取发票抬头详情
```http
GET /invoiceTitle/{id}
```

#### 3. 新增发票抬头
```http
POST /invoiceTitle
Content-Type: application/json

{
  "titleName": "深圳XXYY科技有限公司",
  "titleType": "company",
  "taxNumber": "91440011223344XXCC",
  "address": "深圳市宝安区西乡街道XXYY路YYXX号XXYY大厦",
  "bankAccount": "778899112233445566",
  "phone": "0755-xxxxxxxx",
  "emailAddress": "<EMAIL>",
  "contactPhone": "深圳XXYY科技有限公司",
  "region": "广东省 深圳市 龙岗区",
  "detailedAddress": "深圳市宝安区西乡街道XXYY路YYXX号XXYY大厦",
  "recipientName": "专用发票"
}
```

#### 4. 修改发票抬头
```http
PUT /invoiceTitle
Content-Type: application/json

{
  "id": 1,
  "titleName": "深圳XXYY科技有限公司",
  "titleType": "company",
  // ... 其他字段
}
```

#### 5. 删除发票抬头
```http
DELETE /invoiceTitle/1,2,3
```

### 便民接口

#### 1. 获取当前用户发票抬头列表
```http
GET /invoiceTitle/my-titles
```

#### 2. 获取默认发票抬头
```http
GET /invoiceTitle/default
```

#### 3. 按类型查询发票抬头
```http
GET /invoiceTitle/by-type/company
GET /invoiceTitle/by-type/personal
```

## 数据模型

### 发票抬头类型
- `company`: 公司
- `personal`: 个人

### 主要字段说明
- `titleName`: 抬头名称（公司名或个人姓名）
- `titleType`: 抬头类型（company/personal）
- `taxNumber`: 税号（公司类型必填）
- `recipientName`: 收件人姓名（个人类型必填）
- `customerId`: 客户ID（自动填充）
- `delFlag`: 删除标志（逻辑删除）

## 性能优化

### 1. 数据库索引建议
执行以下SQL创建索引以提升查询性能：

```sql
-- 主要查询索引
CREATE INDEX idx_inv_invoice_title_customer_type_time 
ON inv_invoice_title (customer_id, title_type, create_time DESC);

-- 唯一性校验索引
CREATE INDEX idx_inv_invoice_title_customer_name 
ON inv_invoice_title (customer_id, title_name);

-- 税号查询索引
CREATE INDEX idx_inv_invoice_title_tax_number 
ON inv_invoice_title (tax_number);
```

### 2. 查询优化策略
- 使用右模糊匹配（`LIKE 'xxx%'`）而不是全模糊匹配
- 优先使用客户ID过滤
- 合理的分页大小（建议10-50条/页）
- 按创建时间倒序排列

### 3. 预期性能提升
- 查询响应时间从 >0.5秒 降低到 <0.1秒
- 分页查询性能提升80%以上
- 统计查询性能提升90%以上

## 使用示例

### 前端调用示例（JavaScript）
```javascript
// 获取发票抬头列表
const getInvoiceTitles = async (params) => {
  const response = await fetch('/invoiceTitle/list?' + new URLSearchParams(params));
  return response.json();
};

// 新增公司发票抬头
const addCompanyTitle = async (data) => {
  const response = await fetch('/invoiceTitle', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      titleName: '深圳XXYY科技有限公司',
      titleType: 'company',
      taxNumber: '91440011223344XXCC',
      // ... 其他字段
    })
  });
  return response.json();
};

// 新增个人发票抬头
const addPersonalTitle = async (data) => {
  const response = await fetch('/invoiceTitle', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      titleName: '张三',
      titleType: 'personal',
      recipientName: '张三',
      contactPhone: '13800138000',
      // ... 其他字段
    })
  });
  return response.json();
};
```

## 测试说明

### 单元测试
运行测试类 `InvInvoiceTitleControllerTest` 验证功能：
```bash
mvn test -Dtest=InvInvoiceTitleControllerTest
```

### 性能测试
1. 执行SQL优化脚本创建索引
2. 使用性能测试工具（如JMeter）测试接口响应时间
3. 监控数据库查询执行计划

## 注意事项

1. **权限控制**: 所有接口都需要相应的权限，确保用户已登录且有相应权限
2. **数据隔离**: 系统自动根据登录用户的客户ID进行数据隔离
3. **逻辑删除**: 删除操作为逻辑删除，数据不会物理删除
4. **唯一性约束**: 同一客户下的发票抬头名称不能重复
5. **类型验证**: 公司类型必须填写税号，个人类型必须填写姓名

## 后续优化建议

1. **缓存优化**: 对于不经常变化的发票抬头数据，可以考虑使用Redis缓存
2. **批量操作**: 可以添加批量导入、批量修改等功能
3. **审核流程**: 可以添加发票抬头的审核流程
4. **历史记录**: 可以记录发票抬头的修改历史
5. **模板功能**: 可以提供发票抬头模板功能，方便用户快速创建
