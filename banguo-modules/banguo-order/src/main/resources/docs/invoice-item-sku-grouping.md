# 发票项按SKU分组聚合查询实现文档

## 功能概述
实现 `InvoiceItemServiceImpl.queryInvoiceitemlist` 方法，根据发票ID查询发票项表，按SKU ID分组，对指定字段进行求和聚合。

## 实现方案

### 1. 业务需求
- 根据发票ID查询发票项数据
- 按SKU ID进行分组
- 对以下字段进行SUM聚合：
  - 商品件数 (count)
  - 商品金额 (productAmount)
  - 商品总毛重 (spuGrossWeightTotal)
  - 商品总净重 (spuNetWeightTotal)
  - 开票代采服务费 (platformServiceAmount)
  - 开票平台运费金额 (platformFreightAmount)
  - 开票基采购运费金额 (baseFreightAmount)
  - 免税金额 (taxFreeAmount)
  - 免税重量 (taxFreeWeight)
  - 剩余免税重量 (remainTaxFreeWeight)
- 将同一SKU的多个订单编码合并为列表

### 2. 技术实现

#### 2.1 Mapper接口扩展
在 `InvoiceItemMapper.java` 中添加方法：
```java
Page<InvoiceItemSkuVo> queryInvoiceItemListGroupBySku(@Param("page") Page<InvoiceItemSkuVo> page, @Param("bo") InvoiceItemBo bo);
```

#### 2.2 SQL查询实现
在 `InvoiceItemMapper.xml` 中添加SQL查询：
- 使用 `GROUP BY ii.sku_id` 按SKU分组
- 使用 `SUM()` 函数对数值字段进行聚合
- 使用 `GROUP_CONCAT()` 合并订单编码
- 支持多种查询条件过滤
- 支持分页查询

#### 2.3 Service层实现
在 `InvoiceItemServiceImpl.java` 中实现业务逻辑：
1. 参数校验
2. 构建分页对象
3. 执行分组聚合查询
4. 处理订单编码列表（将逗号分隔字符串转换为List）
5. 返回分页结果

### 3. 关键特性

#### 3.1 查询条件支持
- 发票ID (invoiceId) - 必填
- 客户ID (customerId)
- 供应商ID (supplierId)
- 总仓ID (regionWhId)
- SKU ID (skuId)
- SPU ID (spuId)
- 订单编码 (orderCode)
- 商品名称 (spuName) - 模糊查询
- 创建者相关字段

#### 3.2 数据聚合
- 数值字段使用SUM函数聚合
- 订单编码使用GROUP_CONCAT合并，避免重复
- 保留分组维度的基础信息（SKU信息、商品信息等）

#### 3.3 分页支持
- 使用MyBatis Plus的Page对象
- 支持PageQuery参数传入
- 返回TableDataInfo包装的分页结果

### 4. 数据流程
1. 接收InvoiceItemBo查询条件和PageQuery分页参数
2. 构建MyBatis Plus分页对象
3. 执行SQL分组聚合查询
4. 后处理：将订单编码字符串转换为List
5. 包装为TableDataInfo返回

### 5. 性能考虑
- 在inv_invoice_item表的sku_id字段上建议添加索引
- GROUP BY操作会对性能有一定影响，建议监控查询性能
- 大数据量时考虑添加适当的查询条件限制

### 6. 错误处理
- 参数为null时返回空的TableDataInfo
- SQL异常由框架统一处理
- 遵循项目异常处理规范

## 使用示例

```java
// 构建查询条件
InvoiceItemBo bo = new InvoiceItemBo();
bo.setInvoiceId(12345L);
bo.setCustomerId(67890L);

// 构建分页参数
PageQuery pageQuery = new PageQuery();
pageQuery.setPageNum(1);
pageQuery.setPageSize(10);

// 执行查询
TableDataInfo<InvoiceItemSkuVo> result = invoiceItemService.queryInvoiceitemlist(bo, pageQuery);
```

## 注意事项
1. 该方法主要用于发票详情页面的商品汇总展示
2. 返回的orderCodes字段包含该SKU对应的所有订单编码
3. 聚合字段均为求和结果，适用于统计场景
4. 遵循项目编码规范，使用了标准的分页和查询模式
