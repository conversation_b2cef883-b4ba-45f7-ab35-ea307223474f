<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.order.mapper.InvoiceMapper">
    <select id="queryWaitInvoice" resultType="cn.xianlink.order.domain.vo.InvoiceQueryVo">
        SELECT
        it.region_wh_id
        ,it.supplier_id
        ,CASE
            WHEN MAX(CASE WHEN provide_invoice = 3 THEN 1 ELSE 0 END) = 1 THEN 3
            ELSE 2
        END AS provide_invoice  <!-- 供应商可供发票类型 -->
        ,sum(it.count) as totalItems  <!-- 商品件数总和 -->
        ,count(DISTINCT it.sku_id) as product_category_count <!-- 商品种类数 -->
        ,sum(it.spu_gross_weight * it.count) as spu_gross_weight_total<!-- 商品总毛重 -->
        ,sum(it.spu_net_weight * it.count) as spu_net_weight_total <!-- 商品总净重 -->
        ,SUM(it.product_amount - it.product_free_amount - COALESCE(rd.refund_product_amount_total, 0)) AS invoice_amount  <!-- 开票商品金额(元) -->
        ,SUM(it.platform_service_amount - it.platform_service_free_amount - COALESCE(rd.refund_service_amount_total, 0)) AS platform_service_amount_total <!-- 开票代采服务费(元)  -->
        ,SUM(it.platform_freight_amount - it.platform_freight_free_amount - COALESCE(rd.refund_platform_freight_total, 0)) AS platform_freight_amount_total <!-- 开票平台运费金额(元) -->
        ,SUM(it.base_freight_amount - it.base_freight_free_amount - COALESCE(rd.refund_base_freight_total, 0)) AS base_freight_amount_total <!-- 开票基采运费金额(元) -->
        ,min(od.create_time) AS order_time_start
        ,max(od.create_time) AS order_time_end
        FROM
        order_item it
        LEFT JOIN order od ON it.order_id = od.id AND od.del_flag = 0
        LEFT JOIN (
            SELECT
            order_item_id,
            SUM(refund_product_amount) AS refund_product_amount_total,
            SUM(refund_service_amount) AS refund_service_amount_total,
            SUM(refund_platform_freight) AS refund_platform_freight_total,
            SUM(refund_base_freight) AS refund_base_freight_total
            FROM
            refund_product_detail
            WHERE
            refund_status IN (0, 1)
            AND del_flag = 0
            GROUP BY order_item_id
        ) rd
        ON rd.order_item_id = it.id
        WHERE
        <include refid="waitInvoiceWhere"/>
    </select>

    <sql id="waitInvoiceWhere">
        it.del_flag = 0
        AND it.create_time &gt;= #{bo.orderTimeStart}
        AND it.create_time &lt;= #{bo.orderTimeEnd}
        AND it.customer_id = #{bo.customerId}
<!--        AND it.settle_count > 0-->
<!--        AND it.invoice_situation = 1-->
<!--        and it.`provide_invoice` != 1-->
        <if test="bo.provideInvoice != null">
            and it.`provide_invoice` = #{bo.provideInvoice}
        </if>
        GROUP BY it.region_wh_id, it.supplier_id
        <if test="bo.invoicePickBoList != null and bo.invoicePickBoList.size() > 0">
            HAVING (it.region_wh_id, it.supplier_id) IN
            <foreach item="item" collection="bo.invoicePickBoList" open="(" separator="," close=")">
                (#{item.regionWhId}, #{item.supplierId})
            </foreach>
        </if>
    </sql>

    <select id="queryInvoiceOverview" resultType="cn.xianlink.order.domain.vo.InvoiceOverviewVo">
        SELECT
        COUNT(DISTINCT it.region_wh_id, it.supplier_id) AS invoiceCnt  <!-- 开票项 -->
        ,CASE
            WHEN MAX(CASE WHEN it.provide_invoice = 2 THEN 1 ELSE 0 END) = 1 THEN 2
            ELSE 3
        END AS provide_invoice  <!-- 可供发票类型 -->
        ,SUM(it.product_amount - it.product_free_amount - COALESCE(rd.refund_product_amount_total, 0)) AS product_amount_total  <!-- 开票商品金额(元) -->
        FROM
        order_item it
        LEFT JOIN (
        SELECT
            order_item_id,
            SUM(refund_product_amount) AS refund_product_amount_total
            FROM
            refund_product_detail
            WHERE
            refund_status IN (0, 1)
            AND del_flag = 0
            GROUP BY order_item_id
        ) rd
        ON rd.order_item_id = it.id
        WHERE
        <include refid="waitInvoiceWhere"/>
    </select>

    <select id="queryProvideInvoice" resultType="cn.xianlink.order.domain.vo.InvoiceOverviewVo">
        SELECT
        CASE
            WHEN MAX(CASE WHEN it.provide_invoice = 2 THEN 1 ELSE 0 END) = 1 THEN 2
            ELSE 3
        END AS provide_invoice  <!-- 可供发票类型 -->
        FROM
        order_item it
        WHERE
        <include refid="waitInvoiceWhere"/>
    </select>
</mapper>
