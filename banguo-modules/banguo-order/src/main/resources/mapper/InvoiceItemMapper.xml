<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.order.mapper.InvoiceItemMapper">

    <select id="queryWaitInvoiceItem" resultType="cn.xianlink.order.domain.vo.InvoiceItemVo">
        select
        ,it.customer_id
        ,it.region_wh_id
        ,it.supplier_id
        ,it.order_code
        ,it.id as order_item_id
        ,it.category_id_level2
        ,it.spu_id
        ,it.sku_id
        ,it.supplier_sku_id
        ,it.img_url
        ,it.count
        ,it.provide_invoice
        ,sum(it.spu_gross_weight) as spu_gross_weight_total
        ,sum(it.spu_net_weight) as spu_net_weight_total
        ,it.product_amount - it.product_free_amount - rd.refund_product_amount_total as product_amount
        ,it.platform_service_amount - it.platform_service_free_amount - rd.refund_service_amount_total as platform_service_amount
        ,it.platform_freight_amount - it.platform_freight_free_amount - rd.refund_platform_freight_total as platform_freight_amount
        ,it.base_freight_amount - it.base_freight_free_amount - rd.refund_base_freight_total as base_freight_amount
        from order_item it
        left join (
            select order_item_id,
            sum(refund_product_amount) as refund_product_amount_total,
            sum(refund_service_amount) as refund_service_amount_total,
            sum(refund_platform_freight) as refund_platform_freight_total,
            sum(refund_base_freight) as refund_base_freight_total
            from refund_product_detail
            where refund_status in (0,1)
            GROUP BY order_item_id
        ) rd on rd.order_item_id = it.id and rd.del_flag = 0
        WHERE it.del_flag = 0
        AND it.create_time &gt;= #{bo.orderTimeStart}
        AND it.create_time &lt;= #{bo.orderTimeEnd}
        AND it.customer_id = #{bo.customerId}
        AND it.settle_count > 0
        AND it.invoice_situation = 1
        and it.`provide_invoice` != 1
        <if test="bo.provideInvoice != null">
            and it.`provide_invoice` = #{bo.provideInvoice}
        </if>
        <if test="bo.invoicePickBoList != null and bo.invoicePickBoList.size() > 0">
            and (it.region_wh_id, it.supplier_id) IN
            <foreach item="item" collection="bo.invoicePickBoList" open="(" separator="," close=")">
                (#{item.regionWhId}, #{item.supplierId})
            </foreach>
        </if>
    </select>
</mapper>
