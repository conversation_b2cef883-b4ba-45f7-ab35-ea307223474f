<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.order.mapper.InvoiceItemMapper">

    <select id="queryWaitInvoiceItem" resultType="cn.xianlink.order.domain.vo.InvoiceItemVo">
        select
        ,it.customer_id
        ,it.region_wh_id
        ,it.supplier_id
        ,it.order_code
        ,it.id as order_item_id
        ,it.category_id_level2
        ,it.spu_id
        ,it.sku_id
        ,it.supplier_sku_id
        ,it.img_url
        ,it.count
        ,it.provide_invoice
        ,(it.spu_gross_weight * it.count) as spu_gross_weight_total
        ,(it.spu_net_weight * it.count) as spu_net_weight_total
        ,it.product_amount - it.product_free_amount - rd.refund_product_amount_total as product_amount
        ,it.platform_service_amount - it.platform_service_free_amount - rd.refund_service_amount_total as platform_service_amount
        ,it.platform_freight_amount - it.platform_freight_free_amount - rd.refund_platform_freight_total as platform_freight_amount
        ,it.base_freight_amount - it.base_freight_free_amount - rd.refund_base_freight_total as base_freight_amount
        from order_item it
        left join (
            select order_item_id,
            sum(refund_product_amount) as refund_product_amount_total,
            sum(refund_service_amount) as refund_service_amount_total,
            sum(refund_platform_freight) as refund_platform_freight_total,
            sum(refund_base_freight) as refund_base_freight_total
            from refund_product_detail
            where refund_status in (0,1)
            GROUP BY order_item_id
        ) rd on rd.order_item_id = it.id and rd.del_flag = 0
        WHERE it.del_flag = 0
        AND it.create_time &gt;= #{bo.orderTimeStart}
        AND it.create_time &lt;= #{bo.orderTimeEnd}
        AND it.customer_id = #{bo.customerId}
<!--        AND it.settle_count > 0-->
<!--        AND it.invoice_situation = 1-->
<!--        and it.`provide_invoice` != 1-->
        <if test="bo.provideInvoice != null">
            and it.`provide_invoice` = #{bo.provideInvoice}
        </if>
        <if test="bo.invoicePickBoList != null and bo.invoicePickBoList.size() > 0">
            and (it.region_wh_id, it.supplier_id) IN
            <foreach item="item" collection="bo.invoicePickBoList" open="(" separator="," close=")">
                (#{item.regionWhId}, #{item.supplierId})
            </foreach>
        </if>
    </select>
    <!-- 根据发票id查询发票项表，按skuid分组并聚合统计 -->
    <select id="queryInvoiceItemListGroupBySku" resultType="cn.xianlink.order.domain.vo.invoice.InvoiceItemSkuVo">
        SELECT
            ii.invoice_id,
            ii.customer_id,
            ii.region_wh_id,
            ii.supplier_id,
            ii.category_id_level2,
            ii.spu_id,
            ii.sku_id,
            ii.spu_name,
            ii.img_url,
            SUM(ii.count) as count,
            SUM(ii.product_amount) as productAmount,
            SUM(ii.spu_gross_weight_total) as spuGrossWeightTotal,
            SUM(ii.spu_net_weight_total) as spuNetWeightTotal,
            SUM(ii.platform_service_amount) as platformServiceAmount,
            SUM(ii.platform_freight_amount) as platformFreightAmount,
            SUM(ii.base_freight_amount) as baseFreightAmount,
            SUM(ii.tax_free_amount) as taxFreeAmount,
            SUM(ii.tax_free_weight) as taxFreeWeight,
            SUM(ii.remain_tax_free_weight) as remainTaxFreeWeight,
            GROUP_CONCAT(DISTINCT ii.order_code SEPARATOR ',') as orderCode,
            ii.create_code,
            ii.create_name,
            ii.update_code,
            ii.update_name
        FROM inv_invoice_item ii
        <where>
            ii.del_flag = 0
            <if test="bo.invoiceId != null">
                AND ii.invoice_id = #{bo.invoiceId}
            </if>
            <if test="bo.customerId != null">
                AND ii.customer_id = #{bo.customerId}
            </if>
            <if test="bo.supplierId != null">
                AND ii.supplier_id = #{bo.supplierId}
            </if>
            <if test="bo.regionWhId != null">
                AND ii.region_wh_id = #{bo.regionWhId}
            </if>
            <if test="bo.skuId != null">
                AND ii.sku_id = #{bo.skuId}
            </if>
            <if test="bo.spuId != null">
                AND ii.spu_id = #{bo.spuId}
            </if>
            <if test="bo.orderCode != null and bo.orderCode != ''">
                AND ii.order_code = #{bo.orderCode}
            </if>
            <if test="bo.spuName != null and bo.spuName != ''">
                AND ii.spu_name LIKE CONCAT('%', #{bo.spuName}, '%')
            </if>
            <if test="bo.createCode != null and bo.createCode != ''">
                AND ii.create_code = #{bo.createCode}
            </if>
            <if test="bo.createName != null and bo.createName != ''">
                AND ii.create_name LIKE CONCAT('%', #{bo.createName}, '%')
            </if>
        </where>
        GROUP BY ii.sku_id, ii.invoice_id, ii.customer_id, ii.region_wh_id, ii.supplier_id,
                 ii.category_id_level2, ii.spu_id, ii.spu_name, ii.img_url,
                 ii.create_code, ii.create_name, ii.update_code, ii.update_name
        ORDER BY ii.sku_id ASC
    </select>


    <select id="queryFreeTaxFruit" resultType="cn.xianlink.order.domain.vo.FreeTaxFruitVo">
        SELECT
        itm.sku_id
        ,itm.category_id_level2
        ,itm.spu_name
        ,SUM(itm.product_amount) AS taxFreeAmount
        ,LEAST(SUM(itm.spu_net_weight_total), dfg.`weight`) AS taxFreeWeight
        ,dfg.`weight` AS remainTaxFreeWeight
        ,dfg.id AS dutyFreeGoodsId
        FROM
        inv_invoice_item itm
        INNER JOIN `order` o ON itm.order_code = o.`code` AND o.del_flag = 0
        INNER JOIN inv_duty_free_goods dfg ON itm.category_id_level2 = dfg.category_id AND dfg.`weight` > 0
            AND (o.create_time BETWEEN dfg.free_date_start AND dfg.free_date_end)
        WHERE
            itm.invoice_id = #{id}
        GROUP BY itm.sku_id, itm.category_id_level2
    </select>
</mapper>
