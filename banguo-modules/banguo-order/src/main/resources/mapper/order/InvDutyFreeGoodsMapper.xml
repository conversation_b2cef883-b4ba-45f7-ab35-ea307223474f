<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.order.mapper.InvDutyFreeGoodsMapper">

    <resultMap type="cn.xianlink.order.domain.InvDutyFreeGoods" id="InvDutyFreeGoodsResult">
        <result property="id"               column="id"               />
        <result property="weight"           column="weight"           />
        <result property="freeDateStart"    column="free_date_start"  />
        <result property="freeDateEnd"      column="free_date_end"    />
        <result property="purchUserCode"    column="purch_user_code"  />
        <result property="purchUserName"    column="purch_user_name"  />
        <result property="categoryId"       column="category_id"      />
        <result property="categoryName"     column="category_name"    />
        <result property="status"           column="status"           />
        <result property="remark"           column="remark"           />
        <result property="tenantId"         column="tenant_id"        />
        <result property="delFlag"          column="del_flag"         />
        <result property="createCode"       column="create_code"      />
        <result property="createName"       column="create_name"      />
        <result property="createTime"       column="create_time"      />
        <result property="updateCode"       column="update_code"      />
        <result property="updateName"       column="update_name"      />
        <result property="updateTime"       column="update_time"      />
    </resultMap>

    <sql id="selectInvDutyFreeGoodsVo">
        select id, weight, free_date_start, free_date_end, purch_user_code, purch_user_name, 
               category_id, category_name, status, remark, tenant_id, del_flag, 
               create_code, create_name, create_time, update_code, update_name, update_time
        from inv_duty_free_goods
    </sql>

    <!-- 根据采购人员和分类查询有效的免税商品 -->
    <select id="selectValidByPurchUserAndCategory" resultType="cn.xianlink.order.domain.vo.InvDutyFreeGoodsVo">
        <include refid="selectInvDutyFreeGoodsVo"/>
        where del_flag = 0
          and purch_user_code = #{purchUserCode}
          and category_id = #{categoryId}
          and status = 1
          and free_date_start &lt;= #{currentDate}
          and free_date_end &gt;= #{currentDate}
        order by create_time desc
    </select>

    <!-- 根据分类ID查询有效的免税商品 -->
    <select id="selectValidByCategory" resultType="cn.xianlink.order.domain.vo.InvDutyFreeGoodsVo">
        <include refid="selectInvDutyFreeGoodsVo"/>
        where del_flag = 0
          and category_id = #{categoryId}
          and status = 1
          and free_date_start &lt;= #{currentDate}
          and free_date_end &gt;= #{currentDate}
        order by create_time desc
    </select>

    <!-- 查询即将过期的免税商品 -->
    <select id="selectExpiringSoon" resultType="cn.xianlink.order.domain.vo.InvDutyFreeGoodsVo">
        <include refid="selectInvDutyFreeGoodsVo"/>
        where del_flag = 0
          and status = 1
          and free_date_end &gt;= #{currentDate}
          and free_date_end &lt;= DATE_ADD(#{currentDate}, INTERVAL #{expireDays} DAY)
        order by free_date_end asc, create_time desc
    </select>

</mapper>
