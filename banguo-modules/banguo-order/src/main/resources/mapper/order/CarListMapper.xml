<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.order.mapper.CarListMapper">

    <resultMap type="cn.xianlink.order.domain.CarList" id="CarListResult">
        <result property="id"           column="id"           />
        <result property="model"        column="model"        />
        <result property="regionWhId"   column="region_wh_id" />
        <result property="carryWeight"  column="carry_weight" />
        <result property="entruckFee"   column="entruck_fee"  />
        <result property="createTime"   column="create_time"  />
        <result property="updateTime"   column="update_time"  />
        <result property="tenantId"     column="tenant_id"    />
    </resultMap>

    <sql id="selectCarListVo">
        select id, model, region_wh_id, carry_weight, entruck_fee, create_time, update_time, tenant_id
        from car_list
    </sql>

</mapper>
