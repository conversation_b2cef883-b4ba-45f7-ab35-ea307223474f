<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.order.mapper.ActivityRecordMapper">

    <select id="getActivityRecords" resultType="cn.xianlink.order.domain.vo.ActivityRecordVo">
        select
            order_code, order_id, a.customer_id, `type`, activity_id, sum(a.free_amount) as freeAmount,
            max(coalesce(o.total_amount,0) - coalesce(o.financial_service_amount,0)) as orderAmount
        from activity_record a join `order` o on a.order_id=o.id
        <where>
            o.del_flag = 0 and (o.status = 'ALREADY' or o.status = 'FINISH' or (o.status = 'CANCEL' and (o.cancel_type = 'OUT' or o.cancel_type = 'FEW')) )
            and type=#{type}
            <if test="activityIds != null and activityIds.size() > 0">
                and activity_id in
                <foreach collection="activityIds" item="activityId" open="(" close=")" separator=",">
                    #{activityId}
                </foreach>
            </if>
        </where>
        group by a.activity_id
    </select>

    <select id="getMkDataStat" resultType="cn.xianlink.order.api.vo.RemoteMkDataStatVo">
        SELECT
        COUNT(DISTINCT o.id) AS orderQty,
        COUNT(DISTINCT o.customer_id) AS custQty,
        COUNT(DISTINCT o.sku_id) AS skuQty,
        (
        SELECT COALESCE(SUM(t.total_amount), 0)
        FROM (
        SELECT DISTINCT o.id, o.total_amount
        FROM (
        SELECT o.id, i.id AS order_item_id, o.total_amount
        FROM `order` o
        JOIN `order_item` i
        ON i.order_id = o.id
        AND (o.cancel_type IN (
        'REPLACE_SUPPLIER', 'OUT', 'FEW', 'SKU_DOWN', 'LOGISTICS_OUT', 'STOCK_OUT', 'REGION'
        ) OR o.cancel_type IS NULL)
        AND (o.status IN ('ALREADY', 'FINISH', 'CANCEL', 'WANT') OR o.status IS NULL)
        and o.sale_date &gt;= #{startDate}
        and o.sale_date &lt;= #{endDate}
        ) o
        JOIN `activity_record` r
        ON r.order_item_id = o.order_item_id
        WHERE r.activity_id = #{activityId}
        AND r.type = #{type}
        ) t
        ) AS orderAmount,
        COALESCE(SUM(r.free_amount), 0) AS freeAmount
        FROM (
        SELECT o.id, i.id AS order_item_id, i.sku_id, o.total_amount, o.customer_id
        FROM `order` o
        JOIN `order_item` i
        ON i.order_id = o.id
        AND (o.cancel_type IN (
        'REPLACE_SUPPLIER', 'OUT', 'FEW', 'SKU_DOWN', 'LOGISTICS_OUT', 'STOCK_OUT', 'REGION'
        ) OR o.cancel_type IS NULL)
        AND (o.status IN ('ALREADY', 'FINISH', 'CANCEL', 'WANT') OR o.status IS NULL)
        and o.sale_date &gt;= #{startDate}
        and o.sale_date &lt;= #{endDate}
        ) o
        JOIN `activity_record` r
        ON r.order_item_id = o.order_item_id
        WHERE r.activity_id = #{activityId}
        AND r.type = #{type}
    </select>

    <select id="getActivityLeftOrder" resultType="cn.xianlink.order.domain.order.vo.OrderVo">
        SELECT
            o.create_time,o.`code`,o.total_amount,o.customer_id,o.sale_date
        FROM `order` o
        LEFT JOIN activity_record ar
        ON ar.order_id = o.id
        and (o.cancel_type IN ('REPLACE_SUPPLIER','OUT','FEW','SKU_DOWN','LOGISTICS_OUT','STOCK_OUT','REGION') OR o.cancel_type IS NULL)
        and (o.status IN ('ALREADY','FINISH','CANCEL','WANT') OR o.status IS NULL)
        WHERE ar.activity_id = #{bo.activityId} AND ar.type = #{bo.type}
        <if test="bo.orderNo != null and bo.orderNo != ''">
            and o.code like concat('%', #{bo.orderNo}, '%')
        </if>
        <if test="bo.orderTimeStart != null">
            and o.sale_date &gt;= #{bo.orderTimeStart}
        </if>
        <if test="bo.orderTimeEnd != null">
            and o.sale_date &lt;= #{bo.orderTimeEnd}
        </if>
        <if test="bo.custIdList != null and bo.custIdList.size() > 0">
            and ar.customer_id in
            <foreach collection="bo.custIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="bo.skuIdList != null and bo.skuIdList.size() > 0">
            and ar.supplier_sku_id in
            <foreach collection="bo.skuIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        group by o.id
        ORDER BY o.create_time desc
    </select>


   <!-- 营销活动数据统计, 统计缺货、少货以及取消的订单-->
    <select id="mkDataStatic" resultType="cn.xianlink.order.api.vo.RemoteMkDataStatVo">
        SELECT
            (COALESCE(sum(pd.refund_amount), 0) + COALESCE(sum(srd.refund_product_amount), 0))
                + COALESCE(sum(srd.refund_other_amount), 0) as refundaAmount,
            COALESCE(SUM(
                             case
                                 when (oi.refund_out_count + oi.refund_few_count) = 0 then r.free_amount
                                 else r.free_amount - r.free_amount / oi.count * (oi.count - oi.refund_out_count - oi.refund_few_count)
                                 end
                     ), 0) AS freeActAmount,
            COALESCE(SUM(oi.total_amount), 0)-COALESCE(sum(pd.refund_amount), 0) AS orderActAmount
        FROM activity_record r
                JOIN order_item oi ON oi.id = r.order_item_id
                left join refund_product_detail pd
                on pd.order_item_id=oi.id and refund_type in(2,3,4) and pd.refund_status=1
                left join stockout_record_detail srd
                          on srd.order_item_id = oi.id and srd.`status` in(1,2)
        WHERE
            r.activity_id = #{activityId}
            AND r.type = #{type}
            AND oi.sale_date &gt;= #{startDate}
            AND oi.sale_date &lt;= #{endDate}
            AND oi.status IN ('ALREADY','FINISH','CANCEL')
            AND (oi.cancel_type IN ('OUT','FEW') or oi.cancel_type is null)

    </select>

    <select id="mkDataFreeStatic" resultType="cn.xianlink.order.api.vo.RemoteMkDataStatVo">
        SELECT  COALESCE(sum(free_act_amount), 0) AS freeActAmount
        FROM
        (
        SELECT
        oi.id,
        COALESCE(case
            when (oi.refund_out_count + oi.refund_few_count) = 0 then r.free_amount
            else r.free_amount - r.free_amount / oi.count * (oi.count - oi.refund_out_count - oi.refund_few_count)
            end, 0) as free_act_amount
        FROM
        activity_record r
        JOIN order_item oi ON oi.id = r.order_item_id
        WHERE
            r.activity_id = #{activityId}
          AND r.type = #{type}
          AND oi.sale_date &gt;= #{startDate}
          AND oi.sale_date &lt;= #{endDate}
          AND oi.status IN ('ALREADY','FINISH','CANCEL')
          AND (oi.cancel_type IN ('OUT','FEW') or oi.cancel_type is null)
        GROUP BY oi.id
        ) t
        JOIN order_item oi ON oi.id = t.id
        LEFT JOIN refund_product_detail pd ON pd.order_item_id = oi.id
        AND refund_type=4  AND pd.refund_status = 1
    </select>
</mapper>