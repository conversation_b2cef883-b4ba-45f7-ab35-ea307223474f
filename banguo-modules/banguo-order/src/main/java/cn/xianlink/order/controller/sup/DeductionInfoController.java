package cn.xianlink.order.controller.sup;

import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.common.api.enums.system.SysUserTypeEnum;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.api.constant.DeductionInfoStatusEnum;
import cn.xianlink.order.api.constant.DeductionInfoEnum;
import cn.xianlink.order.domain.bo.DeductionInfoBo;
import cn.xianlink.order.domain.vo.DeductionInfoVo;
import cn.xianlink.order.domain.vo.DeductionStatusCountVO;
import cn.xianlink.order.service.IDeductionInfoService;
import cn.xianlink.system.api.model.LoginUser;
import io.swagger.annotations.ApiOperation;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import lombok.extern.apachecommons.CommonsLog;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 扣款单
 * 前端访问路由地址为:/system/info
 *
 * <AUTHOR>
 * @date 2024-06-01
 * @folder 供应商端(小程序)/订单/扣款单
 */
@Validated
@RequiredArgsConstructor
@RestController("supDeductionInfoController")
@RequestMapping("/order/sup/deduction")
@CustomLog
public class DeductionInfoController extends BaseController {

    private final IDeductionInfoService deductionInfoService;


    /**
     * 查询扣款单列表
     */
    @PostMapping("/list")
    @ApiOperation("分页查询扣款单列表")
    public R<TableDataInfo<DeductionInfoVo>> list(@RequestBody DeductionInfoBo bo, PageQuery pageQuery) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (ObjectUtil.isEmpty(loginUser) || ObjectUtil.isEmpty(loginUser.getRelationId())) {
            throw new ServiceException("用户未登录");
        }
        if (ObjectUtil.isNotEmpty(loginUser) && ObjectUtil.isEmpty(bo.getSupplierDeptId())) {
            if (SysUserTypeEnum.SUPPLIER_USER.getType().equals(loginUser.getUserType()) && loginUser.getDeptId() != 0L) {
                bo.setSupplierDeptId(loginUser.getDeptId());
                log.keyword("DeductionInfoController", "list","扣款单分页查询档口信息")
                        .info("扣款单分页查询档口信息，供应商用户查询，入参：{}，loginUser：{}", bo, loginUser);
            }
        } else {
            if (bo.getSupplierDeptId() == 0L) {
                bo.setSupplierDeptId(null);
            }
            log.keyword("DeductionInfoController", "list","扣款单分页查询档口信息")
                    .info("扣款单分页查询档口信息无用户信息，入参：{}", bo);
        }
        bo.setCommonId(loginUser.getRelationId());
        bo.setType(DeductionInfoEnum.SUPPLIER.getCode());
        return R.ok(deductionInfoService.queryPageList(bo, pageQuery));
    }



    /**
     * 获取扣款单详细信息
     *
     * @param id 主键
     */
    @GetMapping("/getDeductionInfoById")
    @ApiOperation("查询扣款单详情")
    public R<DeductionInfoVo> getDeductionInfoById(@RequestParam("id") Long id) {
        return R.ok(deductionInfoService.queryById(id));
    }


    @GetMapping("/getBillInfoByTypeAndCode")
    @ApiOperation("根据关联单据类型和关联单据查询不同得单据信息")
    public R<Object> getBillInfoByTypeAndCode(@RequestParam("billType") Integer billType,
                                              @RequestParam("billCode") String billCode) {
        return R.ok(deductionInfoService.getBillInfoByTypeAndCode(billType, billCode));
    }

    /**
     * 获取不同扣款单待结算状态的数量
     *
     */
    @PostMapping("/getDeductionStatusCount")
    @ApiOperation("获取不同扣款单待结算状态的数量")
    public R<DeductionStatusCountVO> getDeductionStatusCount(@RequestParam(value = "regionWhId", required = false) Long regionWhId,
                                                             @RequestParam("type") Integer type,
                                                             @RequestParam(value = "supplierDeptId", required = false) Long supplierDeptId) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (ObjectUtil.isEmpty(loginUser) || ObjectUtil.isEmpty(loginUser.getRelationId())) {
            throw new ServiceException("用户未登录");
        }
        DeductionInfoBo bo = new DeductionInfoBo();
        bo.setSupplierDeptId(supplierDeptId);
        if (ObjectUtil.isNotEmpty(loginUser) && ObjectUtil.isEmpty(supplierDeptId)) {
            if (SysUserTypeEnum.SUPPLIER_USER.getType().equals(loginUser.getUserType()) && loginUser.getDeptId() != 0L) {
                bo.setSupplierDeptId(loginUser.getDeptId());
                log.keyword("DeductionInfoServiceImpl", "getDeductionStatusCount","扣款单分页查询档口信息")
                        .info("扣款单分页查询档口信息，供应商用户查询，loginUser：{}", loginUser);
            }
        } else {
            if (supplierDeptId == 0L) {
                bo.setSupplierDeptId(null);
            }
            log.keyword("DeductionInfoServiceImpl", "getDeductionStatusCount","扣款单分页查询档口信息")
                    .info("扣款单分页查询档口信息无用户信息，");
        }

        bo.setType(DeductionInfoEnum.loadByCode(type).getCode());
        if (ObjectUtil.isNotEmpty(regionWhId)) {
            bo.setRegionWhId(regionWhId);
        }
        bo.setCommonId(loginUser.getRelationId());
        bo.setStatus(DeductionInfoStatusEnum.COMMITTED.getCode());

        log.info("获取不同扣款单待结算状态的数量bo:{}",bo);
        System.err.println(loginUser);
        return R.ok(deductionInfoService.getDeductionStatusCount(bo));
    }

}
