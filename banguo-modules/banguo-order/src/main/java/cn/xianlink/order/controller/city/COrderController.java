package cn.xianlink.order.controller.city;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import cn.xianlink.basic.api.RemoteBasCustomerService;
import cn.xianlink.basic.api.RemoteCityWhPlaceService;
import cn.xianlink.basic.api.RemoteCityWhService;
import cn.xianlink.basic.api.RemoteRegionLogisticsService;
import cn.xianlink.basic.api.domain.bo.RemoteRegionLogisticsQueryBo;
import cn.xianlink.basic.api.domain.vo.RemoteCityWhPlaceVo;
import cn.xianlink.basic.api.domain.vo.RemoteCityWhVo;
import cn.xianlink.basic.api.domain.vo.RemoteCustomerVo;
import cn.xianlink.basic.api.domain.vo.RemoteRegionLogisticsVo;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.domain.PublicId;
import cn.xianlink.order.domain.excel.LogisticsOrderExportBo;
import cn.xianlink.order.domain.excel.LogisticsOrderExportDto;
import cn.xianlink.order.domain.order.bo.QueryOrderPageBo;
import cn.xianlink.order.domain.order.vo.GetInfoVo;
import cn.xianlink.order.domain.order.vo.QueryPageVo;
import cn.xianlink.order.service.IOrderItemService;
import cn.xianlink.order.service.IOrderService;
import cn.xianlink.order.util.excel.ExcelFileUtil;
import cn.xianlink.order.util.excel.TemplatePathEnum;
import cn.xianlink.product.api.RemoteSupplierSkuService;
import cn.xianlink.product.api.domain.vo.RemoteSupplierSkuFundsInfoVo;
import cn.xianlink.resource.api.RemoteFileService;
import cn.xianlink.resource.api.domain.RemoteFile;
import cn.xianlink.system.api.model.LoginUser;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 订单
 * 前端访问路由地址为:/order/city/order
 *
 * <AUTHOR>
 * @date 2024-05-27
 * @folder 城市仓端(小程序)/订单/订单
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/order/city/order")
public class COrderController extends BaseController {

    private final IOrderService orderService;
    private final IOrderItemService orderItemService;

    @DubboReference(timeout = 300000)
    private RemoteFileService remoteFileService;
    @DubboReference
    private RemoteBasCustomerService remoteBasCustomerService;
    @DubboReference
    private RemoteSupplierSkuService remoteSupplierSkuService;
    @DubboReference
    private RemoteCityWhService remoteCityWhService;
    @DubboReference
    private RemoteRegionLogisticsService remoteRegionLogisticsService;

    @DubboReference
    private RemoteCityWhPlaceService remoteCityWhPlaceService;

    @PostMapping("/queryCityPage")
    @Operation(summary = "订单列表查询")
    public R<TableDataInfo<QueryPageVo>> queryCityPage(@RequestBody QueryOrderPageBo bo) {
        LoginUser user = LoginHelper.getLoginUser();
        if (user == null) {
            throw new ServiceException("非法请求");
        }
        bo.setCityWhId(user.getRelationId());
        List<Long> logisticsIds = remoteRegionLogisticsService.queryAllIdByCityUser(LoginHelper.getLoginUser().getUserId());
        if(CollectionUtils.isNotEmpty(bo.getLogisticsIds())) {
            if(CollectionUtils.isNotEmpty(logisticsIds)) {
                logisticsIds = bo.getLogisticsIds().stream().filter(logisticsIds::contains).toList();
            }else {
                logisticsIds = bo.getLogisticsIds();
            }
        }
        bo.setLogisticsIds(logisticsIds);

        List<Long> placeIdsByUser = remoteCityWhPlaceService.getPlaceIdsByUser(user.getUserId());
        if (CollectionUtils.isNotEmpty(bo.getPlaceIdList())){
            List<Long> finalPlaceIdsByUser = placeIdsByUser;
            placeIdsByUser = bo.getPlaceIdList().stream().filter(item -> CollectionUtils.isEmpty(finalPlaceIdsByUser)
                    || finalPlaceIdsByUser.contains(item)).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(placeIdsByUser)){
            List<RemoteCityWhPlaceVo> remoteCityWhPlaceVos = remoteCityWhPlaceService.queryList(placeIdsByUser);
            List<String> placeLists = remoteCityWhPlaceVos.stream().map(RemoteCityWhPlaceVo::getPath).collect(Collectors.toList());
            bo.setPlacePaths(placeLists);
        }

        return R.ok(orderService.queryPage(bo));
    }

    @PostMapping("/getInfo")
    @Operation(summary = "获取订单详细信息")
    public R<GetInfoVo> getInfo(@RequestBody PublicId id) {
        return R.ok(orderService.queryById(id.getId()));
    }


    /**
     * 导出物流线的订单表格
     *
     * @param bo
     * @return cn.xianlink.common.core.domain.R<java.lang.String>
     * <AUTHOR> on 2024/11/19:17:59
     */
    @PostMapping("/exportLogisticsOrder")
    @RepeatSubmit()
    public R<String> exportLogisticsOrder(@RequestBody LogisticsOrderExportBo bo) {
        if (CollUtil.isEmpty(bo.getLogisticsIds())) {
            return R.fail("物流线必传");
        }

        List<Long> placeIdsByUser = remoteCityWhPlaceService.getPlaceIdsByUser(LoginHelper.getUserId());
        if (CollectionUtils.isNotEmpty(bo.getPlaceIdList())){
            List<Long> finalPlaceIdsByUser = placeIdsByUser;
            placeIdsByUser = bo.getPlaceIdList().stream().filter(item -> CollectionUtils.isEmpty(finalPlaceIdsByUser)
                    || finalPlaceIdsByUser.contains(item)).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(placeIdsByUser)){
            List<RemoteCityWhPlaceVo> remoteCityWhPlaceVos = remoteCityWhPlaceService.queryList(placeIdsByUser);
            List<String> placeLists = remoteCityWhPlaceVos.stream().map(RemoteCityWhPlaceVo::getPath).collect(Collectors.toList());
            bo.setPlacePaths(placeLists);
        }

        List<LogisticsOrderExportDto> dtos = orderItemService.selectLogisticsOrderForExport(bo.getLogisticsIds(), bo.getSaleDate(),bo.getPlacePaths());
        if (CollUtil.isEmpty(dtos)) {
            return R.fail("无数据");
        }
        Set<Long> customerIdSet = new HashSet<>();
        Set<Long> skuIdSet = new HashSet<>();
        Set<Long> cityIdSet = new HashSet<>();
        Set<Long> logisticsIdSet = new HashSet<>();
        for (LogisticsOrderExportDto dto : dtos) {
            customerIdSet.add(dto.getCustomerId());
            skuIdSet.add(dto.getSupplierSkuId());
            cityIdSet.add(dto.getCityWhId());
            logisticsIdSet.add(dto.getLogisticsId());
        }
        //城市仓
        Map<Long, RemoteCityWhVo> cityMap = remoteCityWhService.queryList(Lists.newArrayList(cityIdSet)).stream().collect(Collectors.toMap(RemoteCityWhVo::getId, Function.identity()));
        Map<Long, RemoteSupplierSkuFundsInfoVo> skuMap = remoteSupplierSkuService.queryInfoListByFunds(Lists.newArrayList(skuIdSet)).stream().collect(Collectors.toMap(RemoteSupplierSkuFundsInfoVo::getSkuId, Function.identity()));

        //查客户
        Map<Long, RemoteCustomerVo> customerVoMap = remoteBasCustomerService.getByIds(Lists.newArrayList(customerIdSet)).stream().collect(Collectors.toMap(RemoteCustomerVo::getId, Function.identity()));
        //查物流
        RemoteRegionLogisticsQueryBo logisticsQueryBo = new RemoteRegionLogisticsQueryBo();
        logisticsQueryBo.setLogisticsIds(Lists.newArrayList(logisticsIdSet));
        Map<Long,RemoteRegionLogisticsVo> logisticsMap= remoteRegionLogisticsService.queryList(logisticsQueryBo).stream().collect(Collectors.toMap(RemoteRegionLogisticsVo::getId, Function.identity()));

        for (LogisticsOrderExportDto dto : dtos) {
            dto.setCreateTimeStr(DateUtil.format(dto.getCreateTime(), DatePattern.NORM_DATETIME_MINUTE_PATTERN));

            RemoteCustomerVo customerVo = customerVoMap.get(dto.getCustomerId());
            if (customerVo != null) {
                if (StrUtil.isNotBlank(customerVo.getAlias())) {
                    dto.setCustomerName(customerVo.getAlias() + "【" + customerVo.getName() + "】");
                } else {
                    dto.setCustomerName(customerVo.getName());
                }
            }
            RemoteSupplierSkuFundsInfoVo skuFundsInfoVo = skuMap.get(dto.getSupplierSkuId());
            if (skuFundsInfoVo != null) {
                String producer = "";
                //产地根据-分割取最后一个
                if (StrUtil.isNotBlank(skuFundsInfoVo.getProducer())) {
                    String[] split = skuFundsInfoVo.getProducer().split(StrPool.DASHED);
                    producer = split[split.length-1] + "-";
                }
                String brand = StrUtil.isNotBlank(skuFundsInfoVo.getBrand()) ? skuFundsInfoVo.getBrand() + "-":"";
                dto.setSpuName(brand + producer + skuFundsInfoVo.getSkuName() + "-" + skuFundsInfoVo.getSpuStandards());

                dto.setTotalGrossWight(skuFundsInfoVo.getSpuGrossWeight().multiply(BigDecimal.valueOf(dto.getCount())));
                dto.setSpuGrossWeight(skuFundsInfoVo.getSpuGrossWeight());
                dto.setSpuNetWeight(skuFundsInfoVo.getSpuNetWeight());
                dto.setPrice(skuFundsInfoVo.getPrice());
                dto.setPackageWord(skuFundsInfoVo.getPackageWord());
                dto.setSpuStandards(skuFundsInfoVo.getSpuStandards());
                dto.setSpuGrade(skuFundsInfoVo.getSpuGrade());
                dto.setProducer(skuFundsInfoVo.getProducer());
            }
            RemoteCityWhVo cityWhVo = cityMap.get(dto.getCityWhId());
            if (cityWhVo != null) {
                dto.setCityWhName(cityWhVo.getName());
            }

            RemoteRegionLogisticsVo logisticsVo = logisticsMap.get(dto.getLogisticsId());
            if (logisticsVo != null) {
                dto.setLogisticsName(logisticsVo.getLogisticsName());
            }
        }
        String title = "销售日：" + LocalDate.now() + "订单明细";
        ExcelFileUtil excelFileUtil = new ExcelFileUtil();
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        excelFileUtil.exportExcel(title, dtos, TemplatePathEnum.LOGISTICS_ORDER_RESULT.getValue(), outputStream);

//        String filePath = "example2.xlsx"; // 要创建的文件路径
//        FileOutputStream fileOutputStream = null;
//        try {
//            fileOutputStream = new FileOutputStream(filePath);
//            fileOutputStream.write(outputStream.toByteArray());
//            fileOutputStream.close();
//            System.out.println("文件创建成功，内容已写入。");
//        } catch (FileNotFoundException e) {
//            throw new RuntimeException(e);
//        } catch (IOException e) {
//            throw new RuntimeException(e);
//        }
//        R<String> r = R.ok();
//        r.setData("aaa");
//        return r;

        RemoteFile remoteFile = remoteFileService.uploadTempFile("提货单导出", "提货单导出.xlsx", ContentType.OCTET_STREAM.getValue(), outputStream.toByteArray(),1);
        R<String> r =  R.ok();
        r.setData(remoteFile.getUrl());
        return r;
    }
}
