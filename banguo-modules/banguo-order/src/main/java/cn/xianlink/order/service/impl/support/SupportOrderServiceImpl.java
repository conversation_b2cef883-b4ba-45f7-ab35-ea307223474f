package cn.xianlink.order.service.impl.support;

import cn.hutool.core.collection.CollectionUtil;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.order.domain.Order;
import cn.xianlink.order.domain.OrderItem;
import cn.xianlink.order.domain.order.vo.OriginOrderDetailVo;
import cn.xianlink.order.domain.order.vo.OriginOrderItemDetailVo;
import cn.xianlink.order.domain.order.vo.OriginOrderItemVo;
import cn.xianlink.order.domain.vo.CartItemVo;
import cn.xianlink.order.mapper.OrderMapper;
import cn.xianlink.order.service.ICartItemService;
import cn.xianlink.order.service.IOrderItemService;
import cn.xianlink.order.service.support.ISupportOrderService;
import cn.xianlink.product.api.RemoteSkuService;
import cn.xianlink.product.api.domain.vo.RemoteSkuVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 订单支持服务实现类
 * 
 * <AUTHOR>
 * @date 2025-01-31
 */
@RequiredArgsConstructor
@Service
@CustomLog
public class SupportOrderServiceImpl extends ServiceImpl<OrderMapper, Order> implements ISupportOrderService {

    private final IOrderItemService orderItemService;
    private final ICartItemService iCartItemService;

    @DubboReference
    private RemoteSkuService remoteSkuService;

    /**
     * 根据订单号查询订单ID
     */
    @Override
    public Long getIdByCode(String code){
        LambdaQueryWrapper<Order> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Order::getCode, code);
        wrapper.eq(Order::getDelFlag, 0);
        wrapper.select(Order::getId);
        Order orderInfo = this.getOne(wrapper);
        if(orderInfo == null) {
            return null;
        }
        return orderInfo.getId();
    }

    /**
     * 查询某个人最近N天的订单列表
     *
     * @param customerId 客户ID
     * @param days       最近天数
     * @return 订单列表
     */
    @Override
    public List<OriginOrderDetailVo> getRecentOrderList(Long customerId, Integer days) {
        if (customerId == null) {
            throw new ServiceException("客户ID不能为空");
        }
        if (days == null || days <= 0) {
            throw new ServiceException("天数必须大于0");
        }

        // 计算开始日期
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -days);
        Date startDate = calendar.getTime();

        // 使用 MyBatis Plus 查询订单列表
        List<Order> orderList = this.list(new LambdaQueryWrapper<Order>()
                .eq(Order::getCustomerId, customerId)
                .ge(Order::getSaleDate, startDate)
                .orderByDesc(Order::getId));

        if (CollectionUtil.isEmpty(orderList)) {
            return new ArrayList<>();
        }

        // 转换为 OriginOrderDetailVo
        List<OriginOrderDetailVo> result = new ArrayList<>();
        List<Long> orderIdList = orderList.stream().map(Order::getId).toList();

        // 查询订单项
        List<OrderItem> orderItems = orderItemService.list(new LambdaQueryWrapper<OrderItem>()
                .in(OrderItem::getOrderId, orderIdList)
                .orderByDesc(OrderItem::getId));

        // 按订单ID分组订单项
        Map<Long, List<OrderItem>> orderItemMap = orderItems.stream()
                .collect(Collectors.groupingBy(OrderItem::getOrderId));

        // 组装结果
        for (Order order : orderList) {
            OriginOrderDetailVo vo = new OriginOrderDetailVo();
            BeanUtils.copyProperties(order, vo);

            // 设置订单项列表
            List<OrderItem> items = orderItemMap.get(order.getId());
            if (CollectionUtil.isNotEmpty(items)) {
                List<OriginOrderItemVo> itemVos = items.stream()
                        .map(item -> {
                            OriginOrderItemVo itemVo = new OriginOrderItemVo();
                            BeanUtils.copyProperties(item, itemVo);
                            return itemVo;
                        })
                        .toList();
                vo.setOrderItemList(itemVos);
            } else {
                vo.setOrderItemList(new ArrayList<>());
            }

            result.add(vo);
        }

        return result;
    }

    /**
     * 查询某个客户最近N天的订单项列表（包含sku信息）
     *
     * @param customerId 客户ID
     * @param days       最近天数
     * @param keyword    商品名称关键词，用于模糊搜索spu_name字段
     * @return 订单项列表
     */
    @Override
    public List<OriginOrderItemDetailVo> getRecentOrderItemList(Long customerId, Integer days, String keyword) {
        if (customerId == null) {
            throw new ServiceException("客户ID不能为空");
        }
        if (days == null || days <= 0) {
            throw new ServiceException("天数必须大于0");
        }

        // 计算开始日期
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -days);
        Date startDate = calendar.getTime();

        // 构建查询条件
        LambdaQueryWrapper<OrderItem> queryWrapper = new LambdaQueryWrapper<OrderItem>()
                .eq(OrderItem::getCustomerId, customerId)
                .ge(OrderItem::getSaleDate, startDate);

        // 如果有关键词，添加模糊搜索条件
        if (keyword != null && !keyword.trim().isEmpty()) {
            queryWrapper.like(OrderItem::getSpuName, keyword.trim());
        }

        queryWrapper.orderByDesc(OrderItem::getId);

        // 查询订单项列表
        List<OrderItem> orderItems = orderItemService.list(queryWrapper);

        if (CollectionUtil.isEmpty(orderItems)) {
            return new ArrayList<>();
        }

        // 转换为 OriginOrderItemDetailVo
        List<OriginOrderItemDetailVo> result = new ArrayList<>();

        // 获取 skuId 列表并过滤掉 0L 的值
        List<Long> skuIds = orderItems.stream()
                .map(OrderItem::getSkuId)
                .filter(item -> !Objects.equals(item, 0L))
                .distinct()
                .toList();

        // 过滤掉 skuId 为 0L 的订单项
        List<OrderItem> filteredOrderItems = orderItems.stream()
                .filter(item -> !Objects.equals(item.getSkuId(), 0L))
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(skuIds)) {
            return new ArrayList<>();
        }

        // 使用 RemoteSkuService 获取 sku 信息
        List<RemoteSkuVo> supplierSkus = remoteSkuService.getByIds(skuIds);

        // 创建 skuId 到 RemoteSkuVo 的映射
        Map<Long, RemoteSkuVo> skuMap = supplierSkus.stream()
                .collect(Collectors.toMap(RemoteSkuVo::getId, Function.identity(), (existing, replacement) -> existing));

        // 组装结果
        for (OrderItem orderItem : filteredOrderItems) {
            OriginOrderItemDetailVo vo = new OriginOrderItemDetailVo();
            BeanUtils.copyProperties(orderItem, vo);

            // 设置 sku 信息
            RemoteSkuVo skuInfo = skuMap.get(orderItem.getSkuId());
            if (skuInfo != null) {
                vo.setSkuInfo(skuInfo);
            }

            result.add(vo);
        }

        return result;
    }


    /**
     * 按购物车+订单商品维度去重展示商品
     *
     * @param regionWhId 总仓ID
     * @param customerId 客户ID
     * @param days       最近天数
     * @param keyword    商品名称关键词，用于模糊搜索
     * @return RemoteSkuVo列表
     */
    @Override
    public List<RemoteSkuVo> getRecentProductList(Long regionWhId, Long customerId, Integer days, String keyword) {
        if (customerId == null) {
            throw new ServiceException("客户ID不能为空");
        }
        if (days == null || days <= 0) {
            throw new ServiceException("天数必须大于0");
        }

        // 使用 Map 来存储 skuId 和对应的最早时间（购物车时间优先）
        Map<Long, Date> skuTimeMap = new HashMap<>();

        try {
            // 获取购物车中的 skuId 和时间
            List<CartItemVo> cartItems = iCartItemService.getCartItemList(customerId, regionWhId);
            if (CollectionUtil.isNotEmpty(cartItems)) {
                for (CartItemVo cartItem : cartItems) {
                    if (cartItem.getSkuId() != null && !Objects.equals(cartItem.getSkuId(), 0L)) {
                        // 如果有关键词，进行商品名称过滤
                        if (keyword == null || keyword.trim().isEmpty() ||
                                (cartItem.getSpuName() != null && cartItem.getSpuName().contains(keyword.trim()))) {

                            Long skuId = cartItem.getSkuId();
                            Date createTime = cartItem.getCreateTime();

                            // 购物车时间优先，如果已存在则比较时间取最早的
                            if (!skuTimeMap.containsKey(skuId) ||
                                    (createTime != null && createTime.before(skuTimeMap.get(skuId)))) {
                                skuTimeMap.put(skuId, createTime);
                            }
                        }
                    }
                }
            }

            // 获取订单商品中的 skuId 和时间
            // 计算开始日期
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, -days);
            Date startDate = calendar.getTime();

            // 构建查询条件
            LambdaQueryWrapper<OrderItem> queryWrapper = new LambdaQueryWrapper<OrderItem>()
                    .eq(OrderItem::getCustomerId, customerId)
                    .ge(OrderItem::getSaleDate, startDate);

            // 如果有关键词，添加模糊搜索条件
            if (keyword != null && !keyword.trim().isEmpty()) {
                queryWrapper.like(OrderItem::getSpuName, keyword.trim());
            }

            // 查询订单项列表
            List<OrderItem> orderItems = orderItemService.list(queryWrapper);
            if (CollectionUtil.isNotEmpty(orderItems)) {
                for (OrderItem orderItem : orderItems) {
                    if (orderItem.getSkuId() != null && !Objects.equals(orderItem.getSkuId(), 0L)) {
                        Long skuId = orderItem.getSkuId();
                        Date createTime = orderItem.getCreateTime();

                        // 只有当购物车中不存在该商品时，才添加订单商品的时间
                        // 或者订单时间更早时更新时间
                        if (!skuTimeMap.containsKey(skuId)) {
                            skuTimeMap.put(skuId, createTime);
                        } else if (createTime != null && createTime.before(skuTimeMap.get(skuId))) {
                            skuTimeMap.put(skuId, createTime);
                        }
                    }
                }
            }

            // 如果没有找到任何 skuId，返回空列表
            if (skuTimeMap.isEmpty()) {
                return new ArrayList<>();
            }

            // 按时间排序 skuId（最新的在前面）
            List<Long> sortedSkuIds = skuTimeMap.entrySet().stream()
                    .sorted((entry1, entry2) -> {
                        Date time1 = entry1.getValue();
                        Date time2 = entry2.getValue();
                        // 处理空时间的情况
                        if (time1 == null && time2 == null) return 0;
                        if (time1 == null) return 1;  // 空时间排在后面
                        if (time2 == null) return -1;
                        return time2.compareTo(time1); // 倒序：最新的在前面
                    })
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toList());

            // 调用产品服务获取 RemoteSkuVo 信息
            List<RemoteSkuVo> allSkuList = remoteSkuService.getByIds(sortedSkuIds);

            // 创建映射并按排序顺序构建结果
            if (CollectionUtil.isEmpty(allSkuList)) {
                return new ArrayList<>();
            }

            Map<Long, RemoteSkuVo> skuMap = allSkuList.stream()
                    .filter(Objects::nonNull)
                    .filter(sku -> sku.getId() != null)
                    .collect(Collectors.toMap(RemoteSkuVo::getId, sku -> sku));

            List<RemoteSkuVo> result = sortedSkuIds.stream()
                    .map(skuMap::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            return result;

        } catch (Exception e) {
            log.error("查询购物车+订单商品列表失败，客户ID: {}, 天数: {}, 关键词: '{}'", customerId, days, keyword, e);
            throw new ServiceException("查询商品列表失败: " + e.getMessage());
        }
    }
}
