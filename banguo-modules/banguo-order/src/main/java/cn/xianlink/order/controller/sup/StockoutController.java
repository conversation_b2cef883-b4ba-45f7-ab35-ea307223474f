package cn.xianlink.order.controller.sup;

import cn.xianlink.common.api.enums.order.AccountTypeStatusEnum;
import cn.xianlink.common.api.enums.order.BlameStatusEnum;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.domain.bo.stockOut.BatchCreateStockoutBO;
import cn.xianlink.order.domain.bo.stockOut.StockoutPageBO;
import cn.xianlink.order.domain.vo.common.CommonStatusCountVO;
import cn.xianlink.order.domain.vo.stockOut.StockoutDetailVO;
import cn.xianlink.order.domain.vo.stockOut.StockoutPageVO;
import cn.xianlink.order.domain.vo.stockOut.StockoutRecordDetailVO;
import cn.xianlink.order.enums.ResponsibilityTypeEnum;
import cn.xianlink.order.service.IStockoutRecordService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 供应商小程序-缺货少货
 *
 * <AUTHOR>
 * @date 2024-05-25
 */
@Validated
@RequiredArgsConstructor
@RestController("supStockoutController")
@RequestMapping("/order/sup/stockout")
public class StockoutController extends BaseController {

    private final IStockoutRecordService stockoutRecordService;

    @PostMapping("/stockoutPage")
    @ApiOperation(value = "缺货少货列表")
    public R<TableDataInfo<StockoutPageVO>> stockoutPage(@RequestBody StockoutPageBO bo){
        if (bo.getSupplierId() == null && LoginHelper.getLoginUser().getRelationId() == null) {
            return R.ok(TableDataInfo.build());
        }else {
            bo.setSupplierId(LoginHelper.getLoginUser().getRelationId());
        }
        return R.ok(stockoutRecordService.stockoutPage(bo));
    }

    @GetMapping("/getStockoutById/{id}")
    @ApiOperation(value = "缺货少货详情")
    public R<StockoutDetailVO> getStockoutById(@PathVariable("id") Long id){
        StockoutDetailVO stockoutById = stockoutRecordService.getStockoutById(id);
        if (stockoutById != null && stockoutById.getBlameStatus().equals(BlameStatusEnum.HAS_BLAME.getCode())) {
            if (stockoutById.getBlameRecordDetailVOList().get(0).getResponsibilityType().equals(ResponsibilityTypeEnum.SUPPLIER.getCode())) {
                stockoutById.setIsConfirm(1);
            }
        }
        return R.ok(stockoutById);
    }

    @GetMapping("/getStockoutByCode/{code}")
    @ApiOperation(value = "缺货少货详情")
    public R<StockoutDetailVO> getStockoutByCode(@PathVariable("code") String code){
        return R.ok(stockoutRecordService.getStockoutByCode(code));
    }

    @GetMapping("/getStockoutOrder/{id}")
    @ApiOperation(value = "查询关联订单")
    public R<List<StockoutRecordDetailVO>> getStockoutOrder(@PathVariable("id") Long id){
        return R.ok(stockoutRecordService.getStockoutOrder(id,AccountTypeStatusEnum.SUPPLIER.getCode()));
    }

    @GetMapping("/getStockoutCount")
    @ApiOperation(value = "获取缺货少货单各状态数量")
    public R<List<CommonStatusCountVO>> getStockoutCount(){
        return R.ok(stockoutRecordService.getStockoutCount(AccountTypeStatusEnum.SUPPLIER.getCode()));
    }

//    @PostMapping("/createStockout")
//    @ApiOperation(value = "(不送货)创建缺货单")
//    public R<Void> createStockout(@RequestBody List<BatchCreateStockoutBO> bo){
//        stockoutRecordService.createStockout(bo);
//        return R.ok();
//    }

}
