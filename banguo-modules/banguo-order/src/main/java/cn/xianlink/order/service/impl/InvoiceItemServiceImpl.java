package cn.xianlink.order.service.impl;

import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.order.domain.vo.invoice.InvoiceItemSkuVo;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import cn.xianlink.order.domain.bo.InvoiceItemBo;
import cn.xianlink.order.domain.vo.InvoiceItemVo;
import cn.xianlink.order.domain.InvoiceItem;
import cn.xianlink.order.mapper.InvoiceItemMapper;
import cn.xianlink.order.service.IInvoiceItemService;

import java.util.List;
import java.util.Map;
import java.util.Collection;
import java.util.Arrays;

/**
 * 发票项Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@RequiredArgsConstructor
@Service
public class InvoiceItemServiceImpl implements IInvoiceItemService {

    private final InvoiceItemMapper baseMapper;

    /**
     * 查询发票项
     */
    @Override
    public InvoiceItemVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询发票项列表
     */
    @Override
    public TableDataInfo<InvoiceItemVo> queryPageList(InvoiceItemBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<InvoiceItem> lqw = buildQueryWrapper(bo);
        Page<InvoiceItemVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询发票项列表
     */
    @Override
    public List<InvoiceItemVo> queryList(InvoiceItemBo bo) {
        LambdaQueryWrapper<InvoiceItem> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<InvoiceItem> buildQueryWrapper(InvoiceItemBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<InvoiceItem> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getInvoiceId() != null, InvoiceItem::getInvoiceId, bo.getInvoiceId());
        lqw.eq(bo.getOrderItemId() != null, InvoiceItem::getOrderItemId, bo.getOrderItemId());
        lqw.eq(bo.getCustomerId() != null, InvoiceItem::getCustomerId, bo.getCustomerId());
        lqw.eq(bo.getSupplierId() != null, InvoiceItem::getSupplierId, bo.getSupplierId());
        lqw.eq(bo.getOrderCode() != null, InvoiceItem::getOrderCode, bo.getOrderCode());
        lqw.eq(bo.getSkuId() != null, InvoiceItem::getSkuId, bo.getSkuId());
        lqw.eq(bo.getSpuId() != null, InvoiceItem::getSpuId, bo.getSpuId());
        lqw.eq(StringUtils.isNotBlank(bo.getCreateCode()), InvoiceItem::getCreateCode, bo.getCreateCode());
        lqw.like(StringUtils.isNotBlank(bo.getCreateName()), InvoiceItem::getCreateName, bo.getCreateName());
        lqw.eq(StringUtils.isNotBlank(bo.getUpdateCode()), InvoiceItem::getUpdateCode, bo.getUpdateCode());
        lqw.like(StringUtils.isNotBlank(bo.getUpdateName()), InvoiceItem::getUpdateName, bo.getUpdateName());
        return lqw;
    }

    /**
     * 新增发票项
     */
    @Override
    public Boolean insertByBo(InvoiceItemBo bo) {
        InvoiceItem add = MapstructUtils.convert(bo, InvoiceItem.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改发票项
     */
    @Override
    public Boolean updateByBo(InvoiceItemBo bo) {
        InvoiceItem update = MapstructUtils.convert(bo, InvoiceItem.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(InvoiceItem entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除发票项
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     *  根据发票id 查询发票项表
     *  按skuid 分组，商品件数，商品金额，商品总毛重，商品总净重，剩余免税重量，剩余免税重量，开票代采服务费，开票平台运费金额，开票基采购运费金额，免税金额，免税重量，求和
     */
    @Override
    public TableDataInfo<InvoiceItemSkuVo> queryInvoiceitemlist(InvoiceItemBo bo, PageQuery pageQuery) {
        // 1. 参数校验
        if (bo == null) {
            return TableDataInfo.build();
        }

        // 2. 构建分页对象
        Page<InvoiceItemSkuVo> page = pageQuery.build();

        // 3. 执行分组聚合查询
        Page<InvoiceItemSkuVo> result = baseMapper.queryInvoiceItemListGroupBySku(page, bo);

        // 4. 处理订单编码列表
        if (result.getRecords() != null && !result.getRecords().isEmpty()) {
            for (InvoiceItemSkuVo vo : result.getRecords()) {
                // 将逗号分隔的订单编码字符串转换为List
                if (StringUtils.isNotBlank(vo.getOrderCode())) {
                    String[] orderCodes = vo.getOrderCode().split(",");
                    vo.setOrderCodes(List.of(orderCodes));
                }
            }
        }

        // 5. 返回分页结果
        return TableDataInfo.build(result);
    }
}
