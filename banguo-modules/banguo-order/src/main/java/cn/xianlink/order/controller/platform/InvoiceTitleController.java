package cn.xianlink.order.controller.platform;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.log.annotation.Log;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.validate.AddGroup;
import cn.xianlink.common.core.validate.EditGroup;
import cn.xianlink.common.log.enums.BusinessType;
import cn.xianlink.common.excel.utils.ExcelUtil;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.order.domain.vo.InvoiceTitleVo;
import cn.xianlink.order.domain.bo.InvoiceTitleBo;
import cn.xianlink.order.service.IInvoiceTitleService;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;

/**
 * 发票抬头管理
 * 前端访问路由地址为:/order/invoiceTitle
 *
 * <AUTHOR>
 * @date 2025-07-29
 * @folder 采集平台(小程序)/发票/发票抬头
 */
@Tag(name = "发票抬头管理", description = "发票抬头的增删改查操作")
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/order/platform/invoiceTitle")
public class InvoiceTitleController extends BaseController {

    private final IInvoiceTitleService invoiceTitleService;

    /**
     * 查询发票抬头列表
     */
    @Operation(
        summary = "查询发票抬头列表",
        description = "分页查询发票抬头信息，支持按客户ID、抬头名称、税号、类型等条件筛选"
    )
    @PostMapping("/list")
    public R<TableDataInfo<InvoiceTitleVo>> list(
        @Parameter(description = "查询条件", schema = @Schema(implementation = InvoiceTitleBo.class)) @RequestBody InvoiceTitleBo bo
    ) {

        // 设置当前登录用户信息
        var loginUser = LoginHelper.getLoginUser();
        if (loginUser != null) {
            bo.setCustomerId(loginUser.getRelationId());
        }
        return R.ok(invoiceTitleService.queryPageList(bo));
    }

    /**
     * 导出发票抬头列表
     */
    @Operation(
        summary = "导出发票抬头列表",
        description = "根据查询条件导出发票抬头数据到Excel文件"
    )
    @Log(title = "发票抬头", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(
        @Parameter(description = "查询条件", schema = @Schema(implementation = InvoiceTitleBo.class)) @RequestBody InvoiceTitleBo bo,
        @Parameter(description = "HTTP响应对象", hidden = true) HttpServletResponse response
    ) {
        // 设置当前登录用户信息
        var loginUser = LoginHelper.getLoginUser();
        if (loginUser != null) {
            bo.setCustomerId(loginUser.getRelationId());
        }
        List<InvoiceTitleVo> list = invoiceTitleService.queryList(bo);
        ExcelUtil.exportExcel(list, "发票抬头", InvoiceTitleVo.class, response);
    }

    /**
     * 获取发票抬头详细信息
     *
     * @param id 主键
     */
    @Operation(
        summary = "获取发票抬头详细信息",
        description = "根据发票抬头ID查询单个发票抬头的详细信息"
    )
    @GetMapping("/{id}")
    public R<InvoiceTitleVo> getInfo(
        @Parameter(description = "发票抬头ID", required = true, example = "1")
        @NotNull(message = "主键不能为空")
        @PathVariable Long id
    ) {
        return R.ok(invoiceTitleService.queryById(id));
    }

    /**
     * 新增发票抬头
     */
    @Operation(
        summary = "新增发票抬头",
        description = "创建新的发票抬头信息，支持公司和个人两种类型"
    )
    @Log(title = "发票抬头", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(
        @Parameter(description = "发票抬头信息", required = true, schema = @Schema(implementation = InvoiceTitleBo.class))
        @Validated(AddGroup.class) @RequestBody InvoiceTitleBo bo
    ) {
        // 设置当前登录用户信息
        var loginUser = LoginHelper.getLoginUser();
        if (loginUser != null) {
            bo.setId(null);// 清空ID
            bo.setCustomerId(loginUser.getRelationId());
            bo.setCreateCode(loginUser.getUserCode());
            bo.setCreateName(loginUser.getRealName());
        }
        return toAjax(invoiceTitleService.insertByBo(bo));
    }

    /**
     * 修改发票抬头
     */
    @Operation(
        summary = "修改发票抬头",
        description = "根据ID更新发票抬头信息，支持部分字段更新"
    )
    @Log(title = "发票抬头", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(
        @Parameter(description = "发票抬头信息", required = true, schema = @Schema(implementation = InvoiceTitleBo.class))
        @Validated(EditGroup.class) @RequestBody InvoiceTitleBo bo
    ) {
        // 设置当前登录用户信息
        var loginUser = LoginHelper.getLoginUser();
        if (loginUser != null) {
            bo.setCustomerId(loginUser.getRelationId());
            bo.setUpdateCode(loginUser.getUserCode());
            bo.setUpdateName(loginUser.getRealName());
        }
        return toAjax(invoiceTitleService.updateByBo(bo));
    }

    /**
     * 删除发票抬头（逻辑删除）
     *
     * @param ids 主键串
     */
    @Operation(
        summary = "删除发票抬头",
        description = "逻辑删除发票抬头信息，通过del_flag字段标记删除，支持批量删除"
    )
    @Log(title = "发票抬头", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(
        @Parameter(description = "发票抬头ID数组，多个ID用逗号分隔", required = true, example = "1,2,3")
        @NotEmpty(message = "主键不能为空")
        @PathVariable Long[] ids
    ) {
        return toAjax(invoiceTitleService.deleteWithValidByIds(List.of(ids), true));
    }
}
