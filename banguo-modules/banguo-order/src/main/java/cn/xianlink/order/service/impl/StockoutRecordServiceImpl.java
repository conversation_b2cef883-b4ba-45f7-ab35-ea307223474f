package cn.xianlink.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.xianlink.basic.api.*;
import cn.xianlink.basic.api.domain.bo.RemoteMessageNotifyV2Bo;
import cn.xianlink.basic.api.domain.bo.RemoteRegionLogisticsQueryBo;
import cn.xianlink.basic.api.domain.vo.*;
import cn.xianlink.basic.api.enums.MsgNotifyTemplateV2Enum;
import cn.xianlink.basic.api.enums.NotifyObjectTypeEnum;
import cn.xianlink.common.api.constant.BanguoCommonConstant;
import cn.xianlink.common.api.enums.order.AccountTypeStatusEnum;
import cn.xianlink.common.api.enums.order.BlameSourceTypeEnum;
import cn.xianlink.common.api.enums.order.BlameStatusEnum;
import cn.xianlink.common.api.enums.order.DeliveryStatusEnum;
import cn.xianlink.common.api.enums.order.OrderCancelTypeEnum;
import cn.xianlink.common.api.enums.order.OrderLogSourceTypeEnum;
import cn.xianlink.common.api.enums.order.OrderStatusEnum;
import cn.xianlink.common.api.enums.order.OrderWorkStatusEnum;
import cn.xianlink.common.api.enums.order.ReceiveGoodsStatusEnum;
import cn.xianlink.common.api.enums.order.RefundStatusEnum;
import cn.xianlink.common.api.enums.order.RefundTypeEnum;
import cn.xianlink.common.api.enums.order.ReportLossStatusEnum;
import cn.xianlink.common.api.enums.order.SortGoodsStatusEnum;
import cn.xianlink.common.api.enums.order.StockOutCreateSceneEnum;
import cn.xianlink.common.api.enums.order.StockOutRefundStatusEnum;
import cn.xianlink.common.api.enums.order.StockOutStatusEnum;
import cn.xianlink.common.api.enums.order.StockoutAuditStatusEnum;
import cn.xianlink.common.api.enums.order.ToDosEnum;
import cn.xianlink.common.api.enums.product.SupplierSkuBusinessTypeEnum;
import cn.xianlink.common.api.enums.product.TransSourceTypeEnum;
import cn.xianlink.common.api.enums.product.TransTypeCodeEnum;
import cn.xianlink.common.api.enums.product.TransTypeIoFlagEnum;
import cn.xianlink.common.api.util.AfterSaleUtil;
import cn.xianlink.common.api.util.SaleDateUtil;
import cn.xianlink.common.core.enums.AuditEnum;
import cn.xianlink.common.core.enums.YNStatusEnum;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.SpringUtils;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.json.utils.JsonUtils;
import cn.xianlink.common.log.enums.BusinessType;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.order.config.OrderProperties;
import cn.xianlink.order.constant.OrderCacheNames;
import cn.xianlink.order.controller.CustomToolService;
import cn.xianlink.order.controller.region.RegionEntruckAffairService;
import cn.xianlink.order.domain.*;
import cn.xianlink.order.domain.bo.blameRecord.CreateBlameRecordBO;
import cn.xianlink.order.domain.bo.stockOut.BatchCreateStockoutBO;
import cn.xianlink.order.domain.bo.stockOut.CreateStockoutRecordBO;
import cn.xianlink.order.domain.bo.stockOut.StockoutCreateBO;
import cn.xianlink.order.domain.bo.stockOut.StockoutPageBO;
import cn.xianlink.order.domain.bo.stockOut.StockoutSearchBO;
import cn.xianlink.order.domain.bo.stockOut.StockoutUpdateBO;
import cn.xianlink.order.domain.entruck.bo.RwWaitEntruckQueryBo;
import cn.xianlink.order.domain.entruck.vo.RwEntruckDepartVo;
import cn.xianlink.order.domain.entruck.vo.RwEntruckGoodsVo;
import cn.xianlink.order.domain.entruck.vo.RwEntruckRecordVo;
import cn.xianlink.order.domain.entruck.vo.RwEntruckVo;
import cn.xianlink.order.domain.order.bo.QueryDistributionBo;
import cn.xianlink.order.domain.order.vo.QueryDeliveryNumberVo;
import cn.xianlink.order.domain.order.vo.QueryDistributionItemVo;
import cn.xianlink.order.domain.order.vo.QueryDistributionVo;
import cn.xianlink.order.domain.vo.ToDosCommentVO;
import cn.xianlink.order.domain.vo.blameRecord.BlameRecordDetailVO;
import cn.xianlink.order.domain.vo.blameRecord.BlameRecordInfoVO;
import cn.xianlink.order.domain.vo.common.CommonStatusCountVO;
import cn.xianlink.order.domain.vo.common.OrderLogVO;
import cn.xianlink.order.domain.vo.receiveGoods.ReceiveGoodsDetailVO;
import cn.xianlink.order.domain.vo.refundRecord.RefundBySourceVO;
import cn.xianlink.order.domain.vo.stockOut.StockoutDetailVO;
import cn.xianlink.order.domain.vo.stockOut.StockoutPageVO;
import cn.xianlink.order.domain.vo.stockOut.StockoutRecordDetailVO;
import cn.xianlink.order.mapper.*;
import cn.xianlink.order.mq.producer.CreateStockoutProducer;
import cn.xianlink.order.mq.producer.StockBatchInfoPriceProducer;
import cn.xianlink.order.service.*;
import cn.xianlink.order.util.CustomNoUtil;
import cn.xianlink.order.util.DeliveryNumberUtil;
import cn.xianlink.product.api.RemoteCwStockService;
import cn.xianlink.product.api.RemoteSupplierSkuService;
import cn.xianlink.product.api.domain.bo.RemoteCwTransDetailDTO;
import cn.xianlink.product.api.domain.bo.RemoteCwTransHeadDTO;
import cn.xianlink.product.api.domain.bo.RemoteQueryInfoListBo;
import cn.xianlink.product.api.domain.bo.RemoteTransDetailPriceDTO;
import cn.xianlink.product.api.domain.vo.RemoteSupplierSkuInfoVo;
import cn.xianlink.system.api.RemoteDeptService;
import cn.xianlink.system.api.RemoteUserService;
import cn.xianlink.system.api.domain.bo.RemoteUserBo;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 缺货少货单Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-25
 */
@RefreshScope
@RequiredArgsConstructor
@Service
@CustomLog
public class StockoutRecordServiceImpl implements IStockoutRecordService {

    private final StockoutRecordMapper baseMapper;

    private final StockoutRecordDetailMapper stockoutRecordDetailMapper;

    @DubboReference
    private final RemoteCityWhService remoteCityWhService;

    @DubboReference
    private final RemoteRegionWhService remoteRegionWhService;

    @DubboReference
    private final RemoteSupplierService remoteSupplierService;

    @DubboReference
    private final RemoteRegionLogisticsService remoteRegionLogisticsService;

    private final OrderLogMapper orderLogMapper;

    private final IOrderService orderService;

    private final OrderItemMapper orderItemMapper;

    private final OrderMapper orderMapper;

    @DubboReference
    private final RemoteSupplierSkuService remoteSupplierSkuService;

    @DubboReference
    private final RemoteUserService remoteUserService;

    private final IBlameRecordService blameRecordService;

    private final IRefundRecordService refundRecordService;

    private final RefundRecordMapper refundRecordMapper;

    private final ICustomerStatisticsService customerStatisticsService;

    private final StockoutSkuRecordMapper stockoutSkuRecordMapper;

    private final SortGoodsMapper sortGoodsMapper;

    private final SortGoodsDetailMapper sortGoodsDetailMapper;

    private final ReceiveGoodsRecordDetailMapper receiveGoodsRecordDetailMapper;

    private final ReceiveGoodsRecordMapper receiveGoodsRecordMapper;

    private final RefundProductDetailMapper refundProductDetailMapper;

    private final ReportLossOrderMapper reportLossOrderMapper;

    @DubboReference
    private RemoteMessageNotifyService remoteMessageNotifyService;

    private final RegionEntruckAffairService regionEntruckAffairService;

    private final transient IRwDepartService rwDepartService;

    private final SortGoodsRecordMapper sortGoodsRecordMapper;
    @DubboReference
    private final transient RemoteBasCustomerService remoteBasCustomerService;
    @DubboReference
    private final transient RemoteDeptService remoteDeptService;
    @Resource
    private final CreateStockoutProducer createStockoutProducer;
    private final IStockoutSkuRecordService stockoutSkuRecordService;
    private final transient CustomToolService customToolService;

    private final transient ISortGoodsService sortGoodsService;
    @DubboReference
    private final transient RemoteCwStockService remoteCwStockService;

    private final transient IRwEntruckRecordService rwEntruckRecordService;

    private final BlameRecordMapper blameRecordMapper;

    private final StockBatchInfoPriceProducer stockBatchInfoPriceProducer;
    //少货单创建时同步创建判责单,0否,1是
//    @Value("${stockout.create.createBlame:0}")
//    private Integer SHORTAGE_SYNC_CREATE_BLAME;
    @DubboReference
    private RemoteCityWhPlaceService remoteCityWhPlaceService;

    @Resource
    private transient final RwEntruckGoodsMapper rwEntruckGoodsMapper;

    private final OrderProperties orderProperties;
    /**
     * 缺货少货单分页查询
     */
    @Override
    public TableDataInfo<StockoutPageVO> stockoutPage(StockoutPageBO bo) {
        //采购员看自己的数据
        if (ObjectUtil.isNotNull(bo.getOwn()) && bo.getOwn()) {
            bo.setBuyerId(LoginHelper.getLoginUser().getUserId());
        }
        Page<StockoutPageVO> page = baseMapper.stockoutPage(bo, bo.build());
        Map<Long, String> parkingNoMap = new HashMap<>();
        Map<String, Integer> recordMap = new HashMap<>();
        if (page.getRecords().size() > 0) {
            //少货单要获取判责单拼接相关信息
            if (bo.getType().equals(2)){
                List<String> codes = page.getRecords().stream().map(StockoutPageVO::getCode).distinct().toList();
                List<Long> logisticsIds = page.getRecords().stream().map(StockoutPageVO::getLogisticsId).distinct().toList();
                List<RemoteRegionWhParkingVo> parkingVos = remoteRegionWhService.queryParkingByLogisticsId(logisticsIds);
                if (CollectionUtil.isNotEmpty(parkingVos)) {
                    parkingNoMap = parkingVos.stream().collect(Collectors.toMap(RemoteRegionWhParkingVo::getLogisticsId, RemoteRegionWhParkingVo::getParkingNo, (v1, v2) -> v1));
                }
                LambdaQueryWrapper<BlameRecord> qw = new LambdaQueryWrapper<>();
                qw.in(BlameRecord::getSourceCode, codes).eq(BlameRecord::getDelFlag,0);
                List<BlameRecord> blameRecords = blameRecordMapper.selectList(qw);
                if (CollectionUtil.isNotEmpty(blameRecords)) {
                    recordMap = blameRecords.stream().collect(Collectors.toMap(BlameRecord::getSourceCode, BlameRecord::getResponsibilityType, (v1, v2) -> v1));
                }
            }
            List<Long> cityWhIds = page.getRecords().stream().map(StockoutPageVO::getCityWhId).distinct().toList();
            List<Long> supplierIds = page.getRecords().stream().map(StockoutPageVO::getSupplierId).distinct().toList();
            List<Long> supplierDeptIds = page.getRecords().stream().map(StockoutPageVO::getSupplierDeptId).filter(supplierDeptId -> supplierDeptId > 0).distinct().toList();
            List<Long> supplierSkuIds = page.getRecords().stream().map(StockoutPageVO::getSupplierSkuId).filter(ObjectUtil::isNotEmpty).distinct().toList();
            Map<Long, RemoteSupplierSkuInfoVo> skuInfoVoMap = new HashMap<>();
            if (ObjectUtil.isNotEmpty(supplierSkuIds)) {
                RemoteQueryInfoListBo listBo = new RemoteQueryInfoListBo();
                listBo.setSupplierSkuIdList(supplierSkuIds);
                List<RemoteSupplierSkuInfoVo> skuInfoVoList = remoteSupplierSkuService.queryInfoList(listBo);
                if (ObjectUtil.isNotEmpty(skuInfoVoList)) {
                    skuInfoVoMap = skuInfoVoList.stream().collect(Collectors.toMap(RemoteSupplierSkuInfoVo::getId, u -> u, (n1, n2) -> n1));
                }

            }

            //城市仓名称
            List<RemoteCityWhVo> remoteCityWhVos = remoteCityWhService.queryList(cityWhIds);
            Map<Long, String> cityMap = remoteCityWhVos.stream().collect(Collectors.toMap(RemoteCityWhVo::getId, RemoteCityWhVo::getName));
            //总仓名称
            List<RemoteRegionWhVo> remoteRegionWhVos = remoteRegionWhService.queryList();
            Map<Long, String> regionMap = remoteRegionWhVos.stream().collect(Collectors.toMap(RemoteRegionWhVo::getId, RemoteRegionWhVo::getRegionWhName));
            //供应商名称
            Map<Long, RemoteSupplierVo> supplierMap = remoteSupplierService.getSupplierByIds(supplierIds).stream().collect(Collectors.toMap(RemoteSupplierVo::getId, Function.identity()));
            Map<Long, String> deptNameMap = supplierDeptIds.size() > 0 ? remoteDeptService.getDeptNameMap(supplierDeptIds) : new HashMap<>();
            //二级物流线
            List<Long> placeIdLv2s = page.getRecords().stream().map(StockoutPageVO::getPlaceIdLevel2).filter(Objects::nonNull).distinct().toList();
            Map<Long, String> placeIdLv2Map = new HashMap<>();
            if (CollectionUtil.isNotEmpty(placeIdLv2s)) {
                List<RemoteCityWhPlaceVo> placeVos = remoteCityWhPlaceService.queryList(placeIdLv2s);
                if (CollectionUtil.isNotEmpty(placeVos)){
                    placeIdLv2Map = placeVos.stream().collect(Collectors.toMap(RemoteCityWhPlaceVo::getId, RemoteCityWhPlaceVo::getPlaceName));
                }
            }
            Map<Long, RemoteSupplierSkuInfoVo> finalSkuInfoVoMap = skuInfoVoMap;
            Map<Long, String> finalParkingNoMap = parkingNoMap;
            Map<Long, String> finalPlaceIdLv2Map = placeIdLv2Map;
            Map<String, Integer> finalRecordMap = recordMap;
            page.getRecords().forEach(item -> {
                item.setCityWhName(cityMap.get(item.getCityWhId()));
                if (ObjectUtil.isNotEmpty(finalSkuInfoVoMap.get(item.getSupplierSkuId()))) {
                    RemoteSupplierSkuInfoVo skuInfoVo = finalSkuInfoVoMap.get(item.getSupplierSkuId());
                    item.setProducer(skuInfoVo.getProducer());
                    item.setSpuStandards(skuInfoVo.getSpuStandards());
                    // 产地简称
                    item.setAreaCode(skuInfoVo.getAreaCode());
                    item.setBrand(skuInfoVo.getBrand());
                    item.setShortProducer(skuInfoVo.getShortProducer());
                }
                item.setParkingNo(finalParkingNoMap.getOrDefault(item.getLogisticsId(), "-"));
                if (item.getPlaceIdLevel2() != 0){
                    item.setPlaceNameLevel2(finalPlaceIdLv2Map.getOrDefault(item.getPlaceIdLevel2(),"-"));
                }
                item.setResponsibilityType(finalRecordMap.getOrDefault(item.getCode(), 0));
                item.setRegionWhName(regionMap.get(item.getRegionWhId()));
                item.setSupplierName(supplierMap.getOrDefault(item.getSupplierId(), new RemoteSupplierVo()).getName());
                item.setSupplierAlias(supplierMap.getOrDefault(item.getSupplierId(), new RemoteSupplierVo()).getAlias());
                item.setSupplierDeptName(deptNameMap.get(item.getSupplierDeptId()));
            });
        }
        return TableDataInfo.build(page);
    }

    /**
     * 缺货少货单详情
     */
    @Override
    public StockoutDetailVO getStockoutById(Long id) {
        //获取详情
        StockoutDetailVO stockoutDetailVO = baseMapper.getInfoById(id);
        if (stockoutDetailVO != null) {
            if (ObjectUtil.isNotEmpty(stockoutDetailVO.getSupplierSkuId())) {
                RemoteQueryInfoListBo listBo = new RemoteQueryInfoListBo();
                listBo.setSupplierSkuIdList(Lists.newArrayList(stockoutDetailVO.getSupplierSkuId()));
                List<RemoteSupplierSkuInfoVo> skuInfoVoList = remoteSupplierSkuService.queryInfoList(listBo);
                if (ObjectUtil.isNotEmpty(skuInfoVoList)) {
                    RemoteSupplierSkuInfoVo skuInfoVo = skuInfoVoList.get(0);
                    stockoutDetailVO.setProducer(skuInfoVo.getProducer());
                    stockoutDetailVO.setSpuStandards(skuInfoVo.getSpuStandards());
                }
            }

            //获取城市仓名称
            RemoteCityWhVo remoteCityWhVo = remoteCityWhService.queryById(stockoutDetailVO.getCityWhId());
            if (ObjectUtil.isNotNull(remoteCityWhVo)) {
                stockoutDetailVO.setCityWhName(remoteCityWhVo.getName());
            }
            //获取供应商名称
            RemoteSupplierVo supplierVo = remoteSupplierService.getSupplierById(stockoutDetailVO.getSupplierId());
            if (supplierVo != null) {
                stockoutDetailVO.setSupplierName(supplierVo.getName());
                stockoutDetailVO.setSupplierAlias(supplierVo.getAlias());
            }
            if (stockoutDetailVO.getSupplierDeptId() != null && stockoutDetailVO.getSupplierDeptId() > 0) {
                stockoutDetailVO.setSupplierDeptName(remoteDeptService.selectDeptNameByIds(stockoutDetailVO.getSupplierDeptId().toString()));
            }

            RemoteRegionWhVo remoteRegionWhVo = remoteRegionWhService.queryById(stockoutDetailVO.getRegionWhId());
            if (ObjectUtil.isNotNull(remoteRegionWhVo)) {
                stockoutDetailVO.setRegionWhName(remoteRegionWhVo.getRegionWhName());
            }
            //二级提货点
            if (stockoutDetailVO.getPlaceIdLevel2() != null && stockoutDetailVO.getPlaceIdLevel2() > 0) {
                RemoteCityWhPlaceVo remoteCityWhPlaceVo = remoteCityWhPlaceService.queryById(stockoutDetailVO.getPlaceIdLevel2());
                if (remoteCityWhPlaceVo != null) {
                    stockoutDetailVO.setPlaceNameLevel2(remoteCityWhPlaceVo.getPlaceName());
                }
            }
            //作废不显示退款状态
            if (!stockoutDetailVO.getAuditStatus().equals(StockoutAuditStatusEnum.HAS_CONFIRM.getCode())) {
                stockoutDetailVO.setRefundStatus(null);
                stockoutDetailVO.setRefundTime(null);
            }
            //获取操作记录
            LambdaQueryWrapper<OrderLog> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(OrderLog::getSourceType, OrderLogSourceTypeEnum.STOCKOUT.getCode()).eq(OrderLog::getSourceId, id).eq(OrderLog::getDelFlag, 0).orderByDesc(OrderLog::getCreateTime);
            List<OrderLogVO> orderLogList = orderLogMapper.selectVoList(queryWrapper);
            if (CollectionUtil.isNotEmpty(orderLogList)) {
                stockoutDetailVO.setOrderLogVOList(orderLogList);
            }

            //获取判责单信息
            if (stockoutDetailVO.getType().equals(2) && stockoutDetailVO.getAuditStatus().equals(StockoutAuditStatusEnum.HAS_CONFIRM.getCode())) {
                BlameRecordInfoVO blameBySource = blameRecordService.getBlameBySource(stockoutDetailVO.getCode());
                if (ObjectUtil.isNotNull(blameBySource)) {
                    stockoutDetailVO.setParkingNo(blameBySource.getParkingNo());
                    stockoutDetailVO.setEntruckTimeList(blameBySource.getEntruckTimeList());
                    stockoutDetailVO.setTotalDeliveryQuantity(blameBySource.getTotalDeliveryQuantity());
                    stockoutDetailVO.setBlameExplain(blameBySource.getBlameExplain());
                    stockoutDetailVO.setBlameRecordDetailVOList(blameBySource.getBlameRecordDetailVOList());
                    stockoutDetailVO.getOrderLogVOList().addAll(blameBySource.getOrderLogList());
                    stockoutDetailVO.setFileList(blameBySource.getFileList());
                    stockoutDetailVO.setBlameId(blameBySource.getId());
                    List<BlameRecordDetailVO> blameRecordDetailList = blameBySource.getBlameRecordDetailVOList();
                    Calendar calendar = Calendar.getInstance();
                    // 加天数  根据状态来
                    int amount = 0;
                    if (CollectionUtil.isNotEmpty(blameRecordDetailList)) {
                        //剩余处理时间
                        Date today = blameRecordDetailList.get(0).getCreateTime();
                        calendar.setTime(today);
                        if (stockoutDetailVO.getBlameStatus().equals(BlameStatusEnum.HAS_BLAME.getCode())) {
                            amount = 24;
                        }
                    } else {
                        calendar.setTime(stockoutDetailVO.getCreateTime());
                        amount = 60;
                    }
                    calendar.add(Calendar.HOUR_OF_DAY, amount);
                    // 获取修改后的时间
                    Date tomorrow = calendar.getTime();
                    long l = tomorrow.getTime() - System.currentTimeMillis();
                    stockoutDetailVO.setConfirmSecond(Math.max(l, 0L));
                }
            }
            // 装车记录时间
            RwWaitEntruckQueryBo queryBo = new RwWaitEntruckQueryBo();
            queryBo.setSaleDate(stockoutDetailVO.getSaleDate());
            queryBo.setRegionWhId(stockoutDetailVO.getRegionWhId());
            queryBo.setLogisticsId(stockoutDetailVO.getLogisticsId());
            queryBo.setSupplierSkuId(stockoutDetailVO.getSupplierSkuId());
            List<RwEntruckGoodsVo> goodsVoList = rwEntruckGoodsMapper.customSkuIdEntruckList(queryBo);
            stockoutDetailVO.setEntruckTimeList(goodsVoList.stream().filter(f -> f.getStatus().intValue() != DeliveryStatusEnum.WAIT_INSPECT.getCode() && f.getEntruckTime() != null).collect(Collectors.toList()));
            stockoutDetailVO.setTotalDeliveryQuantity(goodsVoList.stream().mapToInt(RwEntruckGoodsVo::getDeliveryQuantity).sum());
            List<RemoteRegionWhParkingVo> parkingVos = remoteRegionWhService.queryParkingByLogisticsId(ListUtil.toList(stockoutDetailVO.getLogisticsId()));
            if (CollectionUtil.isNotEmpty(parkingVos)) {
                stockoutDetailVO.setParkingNo(parkingVos.get(0).getParkingNo());
            }
            //根据缺货少货单号查询退款信息
            RefundBySourceVO refundBySourceVO = refundRecordService.getRefundBySourceCode(stockoutDetailVO.getCode());
            if (ObjectUtil.isNotNull(refundBySourceVO)) {
                stockoutDetailVO.setRefundTime(refundBySourceVO.getRefundTime());
            }
            //日志倒序处理
            if (CollectionUtil.isNotEmpty(stockoutDetailVO.getOrderLogVOList())){
                stockoutDetailVO.getOrderLogVOList().sort(Comparator.comparing(OrderLogVO::getCreateTime).reversed());
            }
        }
        return stockoutDetailVO;
    }

    @Override
    public List<StockoutDetailVO> listStockoutByIds(List<Long> ids) {
        //获取详情
        List<StockoutDetailVO> stockoutDetailVOS = baseMapper.selectVoBatchIds(ids);
        if (ObjectUtil.isEmpty(stockoutDetailVOS)) {
            return Collections.emptyList();
        }
        List<Long> cityWhIds = stockoutDetailVOS.stream().map(StockoutDetailVO::getCityWhId).distinct().toList();
        List<Long> supplierIds = stockoutDetailVOS.stream().map(StockoutDetailVO::getSupplierId).distinct().toList();
        List<Long> supplierDeptIds = stockoutDetailVOS.stream().filter(f -> f.getSupplierDeptId() > 0).map(StockoutDetailVO::getSupplierDeptId).distinct().toList();
        List<Long> skuIdList = stockoutDetailVOS.stream().map(StockoutDetailVO::getSupplierSkuId).filter(ObjectUtil::isNotNull).distinct().collect(Collectors.toList());
        //城市仓名称
        List<RemoteCityWhVo> remoteCityWhVos = remoteCityWhService.queryList(cityWhIds);
        Map<Long, String> cityMap = remoteCityWhVos.stream().collect(Collectors.toMap(RemoteCityWhVo::getId, RemoteCityWhVo::getName));
        //总仓名称
        List<RemoteRegionWhVo> remoteRegionWhVos = remoteRegionWhService.queryList();
        Map<Long, String> regionMap = remoteRegionWhVos.stream().collect(Collectors.toMap(RemoteRegionWhVo::getId, RemoteRegionWhVo::getRegionWhName));
        //供应商名称
        Map<Long, RemoteSupplierVo> supplierMap = remoteSupplierService.getSupplierByIds(supplierIds).stream().collect(Collectors.toMap(RemoteSupplierVo::getId, Function.identity()));

        Map<Long, String> deptNameMap = supplierDeptIds.size() > 0 ? remoteDeptService.getDeptNameMap(supplierDeptIds) : new HashMap<>();

        Map<Long, RemoteSupplierSkuInfoVo> skuInfoMap = customToolService.getSkuInfoMap(skuIdList);


        stockoutDetailVOS.forEach(item -> {
            item.setCityWhName(cityMap.get(item.getCityWhId()));
            item.setRegionWhName(regionMap.get(item.getRegionWhId()));
            item.setSupplierName(supplierMap.getOrDefault(item.getSupplierId(), new RemoteSupplierVo()).getName());
            item.setSupplierAlias(supplierMap.getOrDefault(item.getSupplierId(), new RemoteSupplierVo()).getAlias());
            item.setSupplierDeptName(deptNameMap.get(item.getSupplierDeptId()));

            RemoteSupplierSkuInfoVo sku = skuInfoMap.get(item.getSupplierSkuId());
            // 产地简称
            if(sku != null) {
                item.setAreaCode(sku.getAreaCode());
                item.setBrand(sku.getBrand());
                item.setShortProducer(sku.getShortProducer());
            }
        });
        return stockoutDetailVOS;
    }

    /**
     * 缺货少货单关联订单
     */
    @Override
    public List<StockoutRecordDetailVO> getStockoutOrder(Long id, Integer type) {
        LambdaQueryWrapper<StockoutRecordDetail> eq = Wrappers.lambdaQuery(StockoutRecordDetail.class)
                .eq(StockoutRecordDetail::getStockoutRecordId, id).eq(StockoutRecordDetail::getDelFlag, 0)
                .orderByDesc(StockoutRecordDetail::getCreateTime);
        List<StockoutRecordDetailVO> detailVOS = stockoutRecordDetailMapper.selectVoList(eq);
        if (detailVOS.size() == 0) {
            return detailVOS;
        }
        Map<Long, OrderItem> orderItemMap = new HashMap<>();
        Map<Long, RemoteSupplierSkuInfoVo> remoteSupplierSkuInfoVoMap = new HashMap<>();
        List<Long> orderItemIds = detailVOS.stream().map(StockoutRecordDetailVO::getOrderItemId).filter(ObjectUtil::isNotEmpty).distinct().toList();
        List<OrderItem> orderItems = orderItemMapper.selectBatchIds(orderItemIds);
        if (ObjectUtil.isNotEmpty(orderItems)) {
            orderItemMap = orderItems.stream().collect(Collectors.toMap(OrderItem::getId, u -> u, (n1, n2) -> n1));
        }

        if (ObjectUtil.isNotEmpty(orderItems)) {
            List<Long> supplierSkuIds = orderItems.stream().map(OrderItem::getSupplierSkuId).filter(ObjectUtil::isNotEmpty).distinct().toList();
            RemoteQueryInfoListBo listBo = new RemoteQueryInfoListBo();
            listBo.setSupplierSkuIdList(supplierSkuIds);
            List<RemoteSupplierSkuInfoVo> skuInfoVoList = remoteSupplierSkuService.queryInfoList(listBo);
            if (ObjectUtil.isNotEmpty(skuInfoVoList)) {
                remoteSupplierSkuInfoVoMap = skuInfoVoList.stream().collect(Collectors.toMap(RemoteSupplierSkuInfoVo::getId, u -> u, (n1, n2) -> n1));
            }
        }

        //供应商看到的商品金额=实退用户金额+补贴金额
        if (AccountTypeStatusEnum.SUPPLIER.getCode().equals(type)) {
            Map<Long, OrderItem> finalOrderItemMap = orderItemMap;
            Map<Long, RemoteSupplierSkuInfoVo> finalRemoteSupplierSkuInfoVoMap = remoteSupplierSkuInfoVoMap;
            detailVOS.forEach(l -> {
                l.setRefundProductAmount(l.getRefundProductAmount().add(l.getRefundSubsidyFreeAmount()));
                if (ObjectUtil.isNotEmpty(finalOrderItemMap.get(l.getOrderItemId()))) {
                    OrderItem orderItem = finalOrderItemMap.get(l.getOrderItemId());
                    RemoteSupplierSkuInfoVo skuInfoVo = finalRemoteSupplierSkuInfoVoMap.get(orderItem.getSupplierSkuId());
                    l.setProducer(ObjectUtil.isNotEmpty(skuInfoVo) ? skuInfoVo.getProducer() : null);
                    l.setSpuStandards(ObjectUtil.isNotEmpty(skuInfoVo) ? skuInfoVo.getSpuStandards() : null);
                }
            });
        }
        List<Long> customerIds = detailVOS.stream().map(StockoutRecordDetailVO::getCustomerId).toList();
        Map<Long, RemoteCustomerVo> customerMap = remoteBasCustomerService.getByIds(customerIds).stream().collect(Collectors.toMap(RemoteCustomerVo::getId, Function.identity()));
        detailVOS.forEach(l -> l.setCustomerAlias(customerMap.getOrDefault(l.getCustomerId(), new RemoteCustomerVo()).getAlias()));
        return detailVOS;
    }

    /**
     * 缺货少货单各状态数量
     */
    @Override
    public List<CommonStatusCountVO> getStockoutCount(Integer type) {
        List<CommonStatusCountVO> list = new ArrayList<>();
        //总仓返回未退款数量
        if (type.equals(AccountTypeStatusEnum.REGION.getCode())) {
            LambdaQueryWrapper<StockoutRecord> qw = Wrappers.lambdaQuery();
            qw.eq(StockoutRecord::getRegionWhId, LoginHelper.getLoginUser().getRelationId())
                    .eq(StockoutRecord::getRefundStatus, StockOutRefundStatusEnum.UN_REFUND.getCode())
                    .eq(StockoutRecord::getDelFlag, 0);
            Long count = baseMapper.selectCount(qw);
            CommonStatusCountVO vo = new CommonStatusCountVO();
            vo.setValue(StockOutRefundStatusEnum.UN_REFUND.getCode().longValue());
            vo.setCount(count);
            list.add(vo);
        } else {
            //城市仓和供应商返回待判责/已判责/部分确认
            LambdaQueryWrapper<StockoutRecord> qw = Wrappers.lambdaQuery();
            qw.eq(StockoutRecord::getDelFlag, 0).select(StockoutRecord::getBlameStatus, StockoutRecord::getId)
                    .in(StockoutRecord::getBlameStatus, BlameStatusEnum.STATE_BLAME.getCode(), BlameStatusEnum.HAS_BLAME.getCode(), BlameStatusEnum.PART_CONFIRM.getCode());
            if (type.equals(AccountTypeStatusEnum.CITY.getCode())) {
                qw.eq(StockoutRecord::getCityWhId, LoginHelper.getLoginUser().getRelationId());
            }
            if (type.equals(AccountTypeStatusEnum.SUPPLIER.getCode())) {
                qw.eq(StockoutRecord::getSupplierId, LoginHelper.getLoginUser().getRelationId());
            }
            List<StockoutRecord> stockoutRecords = baseMapper.selectList(qw);
            if (CollectionUtil.isNotEmpty(stockoutRecords)) {
                Map<Integer, List<StockoutRecord>> map = stockoutRecords.stream().collect(Collectors.groupingBy(StockoutRecord::getBlameStatus));
                map.forEach((k, v) -> {
                    CommonStatusCountVO vo = new CommonStatusCountVO();
                    vo.setValue(k.longValue());
                    vo.setCount((long) v.size());
                    list.add(vo);
                });
            }
        }
        return list;
    }

    /**
     * 获取未确认缺货少货单数量
     * @param type 类型 1缺货 2少货
     * @param id   采购员id  城市仓id
     */
    @Override
    public Long getUnConfirmStockOutCount(Integer type, Long id) {
        LambdaQueryWrapper<StockoutRecord> qw = Wrappers.lambdaQuery();
        qw.eq(type.equals(ToDosEnum.AUDIT_STOCKOUT.getCode()),StockoutRecord::getType,1)
                .eq(type.equals(ToDosEnum.AUDIT_LESS_GOODS.getCode()),StockoutRecord::getType,2)
                .eq(type.equals(ToDosEnum.AUDIT_STOCKOUT.getCode()),StockoutRecord::getBuyerId,id)
                .eq(type.equals(ToDosEnum.AUDIT_LESS_GOODS.getCode()),StockoutRecord::getCityWhId,id)
                .eq(StockoutRecord::getAuditStatus, StockoutAuditStatusEnum.STATE_CONFIRM.getCode())
                .eq(StockoutRecord::getDelFlag,0).select(StockoutRecord::getStockoutCount);
        List<StockoutRecord> stockoutRecords = baseMapper.selectList(qw);
        if (CollectionUtil.isNotEmpty(stockoutRecords)){
            return stockoutRecords.stream().mapToLong(StockoutRecord::getStockoutCount).sum();
        }
        return null;
    }

    /**
     * 获取未确认缺货少货单数量
     *
     * @param type 类型 1缺货 2少货
     */
    @Override
    public List<ToDosCommentVO> getUnConfirmStockOutList(Integer type, Long id) {
        List<ToDosCommentVO> list = new ArrayList<>();
        LambdaQueryWrapper<StockoutRecord> qw = Wrappers.lambdaQuery();
        qw.eq(type.equals(ToDosEnum.AUDIT_STOCKOUT.getCode()),StockoutRecord::getType,1)
                .eq(type.equals(ToDosEnum.AUDIT_LESS_GOODS.getCode()),StockoutRecord::getType,2)
                .eq(type.equals(ToDosEnum.AUDIT_STOCKOUT.getCode()),StockoutRecord::getBuyerId,id)
                .eq(type.equals(ToDosEnum.AUDIT_LESS_GOODS.getCode()),StockoutRecord::getCityWhId,id)
                .eq(StockoutRecord::getAuditStatus, StockoutAuditStatusEnum.STATE_CONFIRM.getCode())
                .eq(StockoutRecord::getDelFlag,0).last("limit 100");
        List<StockoutRecord> stockoutRecords = baseMapper.selectList(qw);
        if (CollectionUtils.isNotEmpty(stockoutRecords)){
            //获取商品信息
            List<Long> skuIds = stockoutRecords.stream().map(StockoutRecord::getSupplierSkuId).distinct().toList();
            List<RemoteSupplierSkuInfoVo> supplierSkuIds = remoteSupplierSkuService.querySimpleInfoList(skuIds);
            Map<Long, RemoteSupplierSkuInfoVo> map = supplierSkuIds.stream().collect(Collectors.toMap(RemoteSupplierSkuInfoVo::getId, Function.identity()));
            stockoutRecords.forEach(l ->{
                ToDosCommentVO vo = new ToDosCommentVO();
                vo.setId(l.getId());
                vo.setSupplierSkuId(l.getSupplierSkuId());
                vo.setSpuName(l.getSpuName());
                vo.setSkuCount(Long.valueOf(l.getStockoutCount()));
                vo.setSaleDate(l.getSaleDate());
                RemoteSupplierSkuInfoVo skuInfoVo = map.get(l.getSupplierSkuId());
                if (ObjectUtil.isNotNull(skuInfoVo)){
                    vo.setProducer(skuInfoVo.getProducer());
                    vo.setSpuGrade(skuInfoVo.getSpuGrade());
                    vo.setSpuStandards(skuInfoVo.getSpuStandards());
                    vo.setBuyerName(skuInfoVo.getBuyerName());
                }
                list.add(vo);
            });
        }
        return list;
    }


    public void createStockout(List<BatchCreateStockoutBO> boList) {
        log.keyword("createStockout").info("创建缺货少货单-开始：{}", JsonUtils.toJsonString(boList));
        //获取订单项信息
        QueryDistributionBo queryDistributionBo = new QueryDistributionBo();
        List<Long> supSkuIds = boList.stream().map(BatchCreateStockoutBO::getSupplierSkuId).filter(Objects::nonNull).toList();
        List<Long> orderItems = boList.stream().map(BatchCreateStockoutBO::getOrderItemId).filter(Objects::nonNull).toList();
        List<Long> logisticsIds = boList.stream().map(BatchCreateStockoutBO::getLogisticsId).filter(Objects::nonNull).toList();
        if (CollectionUtil.isNotEmpty(supSkuIds)) {
            queryDistributionBo.setSupplierSkuIdList(supSkuIds);
        }
        if (CollectionUtil.isNotEmpty(orderItems)) {
            queryDistributionBo.setOrderItemIdList(orderItems);
        }
        if (CollectionUtil.isNotEmpty(logisticsIds)) {
            queryDistributionBo.setLogisticsIdList(logisticsIds);
        }
        queryDistributionBo.setOrderItemStatus(OrderStatusEnum.ALREADY.getCode());
        List<QueryDistributionVo> orderList = orderService.queryDistribution(queryDistributionBo);
        log.keyword("createStockout").info("创建缺货少货单-查询订单项：count={}", orderList.size());
        IStockoutRecordService stockoutRecordService = SpringUtils.getBean(IStockoutRecordService.class);
        if (CollectionUtil.isNotEmpty(orderList)) {
            //缺货
            if (boList.stream().anyMatch(l -> l.getType().equals(1))) {
                log.keyword("createStockout").info("创建缺货少货单-缺货");
                Map<Long, List<QueryDistributionItemVo>> map = orderList.stream().flatMap(l -> l.getOrderItemList().stream())
                        .collect(Collectors.groupingBy(QueryDistributionItemVo::getSupplierSkuId));
                boList.forEach(l -> {
                    List<Long> list = map.get(l.getSupplierSkuId()).stream().map(QueryDistributionItemVo::getOrderId).toList();
                    List<QueryDistributionVo> orderItemList = orderList.stream().filter(l1 -> list.contains(l1.getId())).toList();
                    List<QueryDistributionVo> orderItemList1 = new ArrayList<>();
                    for (QueryDistributionVo item : orderItemList) {
                        QueryDistributionVo item1 = new QueryDistributionVo();
                        BeanUtil.copyProperties(item, item1);
                        List<QueryDistributionItemVo> voList = new ArrayList<>();
                        for (QueryDistributionItemVo itemVo : item.getOrderItemList()) {
                            if (itemVo.getSupplierSkuId().equals(l.getSupplierSkuId())) {
                                voList.add(itemVo);
                            }
                        }
                        item1.setOrderItemList(voList);
                        orderItemList1.add(item1);
                    }
                    log.keyword("createStockout").info("创建缺货少货单-缺货-开始：supplierSkuId={}, orderItemList.size={}", l, orderItemList1.size());
                    SpringUtil.getBean(StockoutRecordServiceImpl.class).stockout(l, orderItemList1);
                    log.keyword("createStockout").info("创建缺货少货单-缺货-结束：supplierSkuId={}", l);
                });
            }
            //少货
            else {
                log.keyword("createStockout").info("创建缺货少货单-少货");
                stockoutRecordService.createLessGoods(boList, orderList);
            }
        }
    }

    @Override
    @Lock4j(name = OrderCacheNames.STOCKOUT_CREATE_LOCK, keys = "#bo.supplierSkuId + '_' + #bo.logisticsId + '_' + #bo.saleDate", expire = 30000, acquireTimeout = 1000)
    public void createStockoutSingle(BatchCreateStockoutBO bo) {
        if (ObjectUtil.isNotNull(bo)){
            createStockout(ListUtil.toList(bo));
        }
    }


    @Override
    public void createSingleStockout(StockoutCreateBO bo) {
        log.keyword("城市仓新增少货单", "createSingleStockout").info("bo:{}", bo);

        List<ReceiveGoodsRecordDetail> goodsRecordDetails = receiveGoodsRecordDetailMapper.getByParam(bo);

        if (ObjectUtil.isEmpty(goodsRecordDetails)) {
            return;
        }
        List<Long> orderItemIds = goodsRecordDetails.stream().map(ReceiveGoodsRecordDetail::getOrderItemId).distinct().collect(Collectors.toList());

        List<OrderItem> orderItems = orderItemMapper.selectBatchIds(orderItemIds);
        if (ObjectUtil.isEmpty(orderItems)) {
            return;
        }
        Map<Long, OrderItem> orderItemMap = orderItems.stream().collect(Collectors.toMap(OrderItem::getId, u -> u, (n1, n2) -> n1));

        log.keyword("城市仓新增少货单", "createSingleStockout").info("bo:{}, goodsRecordDetails：{}， orderItems：{}", bo, goodsRecordDetails, orderItems);

        //根据id获取销售批次商品信息
        RemoteQueryInfoListBo infoListBo = new RemoteQueryInfoListBo();
        infoListBo.setSupplierSkuIdList(Lists.newArrayList(orderItems.get(0).getSupplierSkuId()));
        List<RemoteSupplierSkuInfoVo> skuInfoVo = remoteSupplierSkuService.queryInfoList(infoListBo);
        if (ObjectUtil.isEmpty(skuInfoVo)) {
            return;
        }
        List<BatchCreateStockoutBO> boList = new ArrayList<>();
        RemoteSupplierSkuInfoVo infoVo = skuInfoVo.get(0);
        BatchCreateStockoutBO bcs = new BatchCreateStockoutBO();
        Integer stockoutCount = bo.getStockoutCount();
        for (ReceiveGoodsRecordDetail detail : goodsRecordDetails) {
            OrderItem orderItem = orderItemMap.get(detail.getOrderItemId());
            if (ObjectUtil.isEmpty(orderItem)) {
                continue;
            }
            bcs.setStockoutCount(stockoutCount);
            bcs.setOrderItemId(detail.getOrderItemId());
            bcs.setType(BlameSourceTypeEnum.LESS_GOODS.getCode());
            bcs.setSupplierSkuId(detail.getSupplierSkuId());
            bcs.setSupplierSkuCode(infoVo.getCode());
            bcs.setSpuName(infoVo.getSpuName());
            bcs.setCreateScene(StockOutCreateSceneEnum.CITY_CREATE.getCode());
            bcs.setBuyerId(LoginHelper.getLoginUser().getUserId());
            bcs.setSaleDate(infoVo.getSaleDate());
            bcs.setOrderId(orderItem.getOrderId());
            bcs.setCityId(bo.getCityId());
            bcs.setCreateRemark(bo.getCreateRemark());
            bcs.setLogisticsId(orderItem.getLogisticsId());
            bcs.setSupplierId(infoVo.getSupplierId());
            bcs.setSkuSupplierDeptId(infoVo.getSupplierDeptId());
            bcs.setRegionWhId(infoVo.getRegionWhId());
            boList.add(bcs);
        }

        log.keyword("城市仓新增少货单", "createSingleStockout", "detailId:" + bo.getDetailId()).info("bo:{}， boList:{}, receiveGoodsRecordDetails:{}", bo, boList, goodsRecordDetails);
        stockoutSkuRecordService.insterBatchByBo(boList);
        createStockoutProducer.send(boList);
    }


    @Override
    public Integer getMaxStockCount(Long supplierSkuId, Long customerId) {
        if (ObjectUtil.isEmpty(supplierSkuId) || ObjectUtil.isEmpty(customerId)) {
            return null;
        }
        StockoutCreateBO bo = new StockoutCreateBO();
        bo.setSupplierSkuId(supplierSkuId);
        bo.setCustomerId(customerId);
        List<ReceiveGoodsRecordDetail> goodsRecordDetails = receiveGoodsRecordDetailMapper.getByParam(bo);
        log.keyword("城市仓新增少货单获取最大数量", "getMaxStockCount").info("receiveGoodsRecordDetails:{}", goodsRecordDetails);
        if (ObjectUtil.isEmpty(goodsRecordDetails)) {
            return 0;
        }
        List<Long> orderItemIds = goodsRecordDetails.stream().map(ReceiveGoodsRecordDetail::getOrderItemId).toList();
        List<OrderItem> orderItems = orderItemMapper.selectBatchIds(orderItemIds);
        Map<Long, OrderItem> orderItemMap = orderItems.stream().collect(Collectors.toMap(OrderItem::getId, u -> u, (n1, n2) -> n1));

        LambdaQueryWrapper<ReportLossOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ReportLossOrder::getOrderItemId, orderItemIds);
        wrapper.ne(ReportLossOrder::getLossStatus, ReportLossStatusEnum.CANCEL.getCode());
        List<ReportLossOrder> reportLossOrders = reportLossOrderMapper.selectList(wrapper);
        Integer lossCount = handleReportLossOrders(reportLossOrders, orderItemMap);

        Integer maxCount = goodsRecordDetails.stream().mapToInt(ReceiveGoodsRecordDetail::getActualCount).sum();

        log.keyword("城市仓新增少货单获取最大数量", "getMaxStockCount", supplierSkuId, customerId)
                .info("receiveGoodsRecordDetails:{}, orderItems:{}, reportLossOrders:{}, maxCount:{}, lossCount:{}", goodsRecordDetails, orderItemIds, reportLossOrders, maxCount, lossCount);

        return Math.max(maxCount - lossCount, 0);
    }

    public Integer getMaxStockCount(ReceiveGoodsDetailVO detailVO, OrderItem orderItem, List<StockoutRecordDetail> tmpRecordDetailList) {
        if (ObjectUtil.isEmpty(detailVO)) {
            return null;
        }
        log.keyword("城市仓新增少货单获取最大数量", "getMaxStockCount").info("detailVO:{}", detailVO);
        if (ObjectUtil.isEmpty(detailVO)) {
            return 0;
        }
        LambdaQueryWrapper<ReportLossOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ReportLossOrder::getOrderItemId, detailVO.getOrderItemId());
        wrapper.ne(ReportLossOrder::getLossStatus, ReportLossStatusEnum.CANCEL.getCode());
        List<ReportLossOrder> reportLossOrders = reportLossOrderMapper.selectList(wrapper);
        Map<Long, OrderItem> orderItemMap = new HashMap<>();
        orderItemMap.put(detailVO.getOrderItemId(), orderItem);

        Integer lossCount = handleReportLossOrders(reportLossOrders, orderItemMap);

        Integer maxCount = detailVO.getActualCount();

//        if (ObjectUtil.isNotEmpty(tmpRecordDetailList)) {
//            maxCount = maxCount - tmpRecordDetailList.stream().mapToInt(StockoutRecordDetail::getStockoutCount).sum();
//        }

        log.keyword("城市仓新增少货单获取最大数量", "getMaxStockCount", "detailId-" + detailVO.getDetailId())
                .info("detailVO:{}, orderItem:{}, reportLossOrders:{}, maxCount:{}, lossCount:{}, tmpRecordDetailList:{}", detailVO, orderItem, reportLossOrders, maxCount, lossCount, tmpRecordDetailList);

        return Math.max(maxCount - lossCount, 0);
    }

    private Integer handleReportLossOrders(List<ReportLossOrder> reportLossOrders, Map<Long, OrderItem> orderItemMap) {
        Integer lossCount = 0;
        for (ReportLossOrder reportLossOrder : reportLossOrders) {
            OrderItem orderItem = orderItemMap.get(reportLossOrder.getOrderItemId());
            if (ObjectUtil.isEmpty(orderItem)) {
                continue;
            }
            if (reportLossOrder.getLossStatus().equals(ReportLossStatusEnum.INTERVENTION.getCode())
                    || reportLossOrder.getLossStatus().equals(ReportLossStatusEnum.COMPLETE.getCode())) {
                BigDecimal divide = reportLossOrder.getRefundGoodsActualAmount().divide(orderItem.getFinalPrice(), 0, RoundingMode.CEILING);
                lossCount = lossCount + divide.intValue();
            } else {
                BigDecimal divide = reportLossOrder.getLossAmount().divide(orderItem.getFinalPrice(), 0, RoundingMode.CEILING);
                lossCount = lossCount + divide.intValue();
            }
        }
        return lossCount;
    }

    /**
     * 根据code查缺货少货单
     */
    @Override
    public StockoutDetailVO getStockoutByCode(String code) {
        LambdaQueryWrapper<StockoutRecord> qw = Wrappers.lambdaQuery();
        qw.eq(StockoutRecord::getCode, code).eq(StockoutRecord::getDelFlag, 0);
        //获取详情
        StockoutDetailVO stockoutDetailVO = baseMapper.selectVoOne(qw);
        if (stockoutDetailVO != null) {
            //获取城市仓名称
            RemoteCityWhVo remoteCityWhVo = remoteCityWhService.queryById(stockoutDetailVO.getCityWhId());
            if (ObjectUtil.isNotNull(remoteCityWhVo)) {
                stockoutDetailVO.setCityWhName(remoteCityWhVo.getName());
            }
            //获取供应商名称
            RemoteSupplierVo supplierVo = remoteSupplierService.getSupplierById(stockoutDetailVO.getSupplierId());
            if (supplierVo != null) {
                stockoutDetailVO.setSupplierName(supplierVo.getName());
                stockoutDetailVO.setSupplierAlias(supplierVo.getAlias());
            }
            if (stockoutDetailVO.getSupplierDeptId() != null && stockoutDetailVO.getSupplierDeptId() > 0) {
                stockoutDetailVO.setSupplierDeptName(remoteDeptService.selectDeptNameByIds(stockoutDetailVO.getSupplierDeptId().toString()));
            }

            RemoteRegionWhVo remoteRegionWhVo = remoteRegionWhService.queryById(stockoutDetailVO.getRegionWhId());
            if (ObjectUtil.isNotNull(remoteRegionWhVo)) {
                stockoutDetailVO.setRegionWhName(remoteRegionWhVo.getRegionWhName());
            }
            //根据缺货少货单号查询退款信息
            RefundBySourceVO refundBySourceVO = refundRecordService.getRefundBySourceCode(stockoutDetailVO.getCode());
            if (ObjectUtil.isNotNull(refundBySourceVO)) {
                stockoutDetailVO.setRefundTime(refundBySourceVO.getRefundTime());
            }
            //获取操作记录
            LambdaQueryWrapper<OrderLog> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(OrderLog::getSourceType, OrderLogSourceTypeEnum.STOCKOUT.getCode()).eq(OrderLog::getSourceId, stockoutDetailVO.getId())
                    .eq(OrderLog::getDelFlag, 0).orderByDesc(OrderLog::getCreateTime);
            List<OrderLogVO> orderLogList = orderLogMapper.selectVoList(queryWrapper);
            if (CollectionUtil.isNotEmpty(orderLogList)) {
                stockoutDetailVO.setOrderLogVOList(orderLogList);
            }
            //获取判责单信息
            if(stockoutDetailVO.getType().equals(2) && stockoutDetailVO.getAuditStatus().equals(StockoutAuditStatusEnum.HAS_CONFIRM.getCode())){
                BlameRecordInfoVO blameBySource = blameRecordService.getBlameBySource(stockoutDetailVO.getCode());
                if (ObjectUtil.isNotNull(blameBySource)){
                    stockoutDetailVO.setParkingNo(blameBySource.getParkingNo());
                    stockoutDetailVO.setEntruckTimeList(blameBySource.getEntruckTimeList());
                    stockoutDetailVO.setTotalDeliveryQuantity(blameBySource.getTotalDeliveryQuantity());
                    stockoutDetailVO.setBlameExplain(blameBySource.getBlameExplain());
                    stockoutDetailVO.setBlameRecordDetailVOList(blameBySource.getBlameRecordDetailVOList());
                    stockoutDetailVO.getOrderLogVOList().addAll(blameBySource.getOrderLogList());
                    stockoutDetailVO.setFileList(blameBySource.getFileList());
                }
            }
        }
        return stockoutDetailVO;
    }

    /**
     * 计算该次少货会退多少钱
     * 这个是按一个订单的维度  不用考虑复杂的场景
     */
    @Override
    public BigDecimal getConfirmRefundAmount(List<BatchCreateStockoutBO> boList) {
        BigDecimal amount = BigDecimal.ZERO;
        //获取订单项信息
        QueryDistributionBo queryDistributionBo = new QueryDistributionBo();
        List<Long> supSkuIds = boList.stream().map(BatchCreateStockoutBO::getSupplierSkuId).filter(Objects::nonNull).toList();
        List<Long> orderItems = boList.stream().map(BatchCreateStockoutBO::getOrderItemId).filter(Objects::nonNull).toList();
        if (CollectionUtil.isNotEmpty(supSkuIds)) {
            queryDistributionBo.setSupplierSkuIdList(supSkuIds);
        }
        if (CollectionUtil.isNotEmpty(orderItems)) {
            queryDistributionBo.setOrderItemIdList(orderItems);
        }
        queryDistributionBo.setOrderItemStatus(OrderStatusEnum.ALREADY.getCode());
        List<QueryDistributionVo> orderList = orderService.queryDistribution(queryDistributionBo);
        if (CollectionUtil.isNotEmpty(orderList)) {
            //获取订单退款信息
            List<Long> orderId = orderList.stream().map(QueryDistributionVo::getId).distinct().toList();
            LambdaQueryWrapper<RefundRecord> oqw = new LambdaQueryWrapper<>();
            oqw.in(RefundRecord::getOrderId, orderId).eq(RefundRecord::getDelFlag, 0);
            List<RefundRecord> refundRecords = refundRecordMapper.selectList(oqw);
            //退款金额
            Map<Long, BigDecimal> refundAmountMap = new HashMap<>();
            //退金融服务费金额
            Map<Long, BigDecimal> refundServiceFeeMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(refundRecords)) {
                refundRecords.stream().collect(Collectors.groupingBy(RefundRecord::getOrderId)).forEach((k, v) -> {
                    refundServiceFeeMap.put(k, v.stream().map(RefundRecord::getRefundFinancialServiceAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                    refundAmountMap.put(k, v.stream().map(RefundRecord::getRefundAmount).reduce(BigDecimal.ZERO, BigDecimal::add).subtract(refundServiceFeeMap.get(k)));
                });
            }
            List<QueryDistributionItemVo> list = orderList.stream().map(QueryDistributionVo::getOrderItemList).flatMap(List::stream).toList();
            Map<Long, QueryDistributionItemVo> itemVoMap = list.stream().collect(Collectors.toMap(QueryDistributionItemVo::getId, Function.identity()));
            List<Long> ids = list.stream().map(QueryDistributionItemVo::getId).toList();
            List<RefundProductDetail> refundDetails = refundRecordService.getRefundByItemId(ids);
            Map<Long, List<RefundProductDetail>> map = refundDetails.stream().collect(Collectors.groupingBy(RefundProductDetail::getOrderItemId));
            Map<Long, Integer> refundCount = refundDetails.stream().filter(r -> r.getRefundType().equals(RefundTypeEnum.LACK.getCode()) || r.getRefundType().equals(RefundTypeEnum.LESS.getCode()))
                    .collect(Collectors.groupingBy(RefundProductDetail::getOrderItemId, Collectors.summingInt(RefundProductDetail::getStockoutCount)));
            for (BatchCreateStockoutBO bo : boList) {
                QueryDistributionItemVo orderItem = itemVoMap.get(bo.getOrderItemId());
                int count = orderItem.getCount() - refundCount.getOrDefault(orderItem.getId(), 0);
                BigDecimal itemAmount = BigDecimal.ZERO;
                BigDecimal productAmount = BigDecimal.ZERO;
                //存在报损跟差额退顺序混乱的问题,这里先排除掉这两种情况的数量,再分别算出金额以免扣多了
                BigDecimal difDetail = BigDecimal.ZERO;
                //差额退 退档服务费
                BigDecimal difOtherAmount = BigDecimal.ZERO;
                BigDecimal loss = BigDecimal.ZERO;
                BigDecimal totalRefundAmount = BigDecimal.ZERO;
                BigDecimal refundFinancialServicePrice = BigDecimal.ZERO;
                //根据订单项查询退款记录
                List<RefundProductDetail> refundProductDetails = map.get(orderItem.getId());
                if (CollectionUtil.isNotEmpty(refundProductDetails)) {
                    for (RefundProductDetail detail : refundProductDetails) {
                        if (detail.getRefundType().equals(RefundTypeEnum.DIFFERENCE.getCode())) {
                            difDetail = difDetail.add(detail.getRefundProductAmount());
                            difOtherAmount = difOtherAmount.add(detail.getRefundServiceAmount());
                        } else if (detail.getRefundType().equals(RefundTypeEnum.REPORT_LOSS.getCode())) {
                            loss = loss.add(detail.getRefundProductAmount());
                        }
                        refundFinancialServicePrice = refundFinancialServicePrice.add(detail.getRefundFinancialServicePrice());
                    }
                }
                if (bo.getStockoutCount().equals(count)) {
                    //按全退计算
                    if (CollectionUtil.isNotEmpty(refundProductDetails)) {
                        //计算获取各项已退金额
                        BigDecimal refundProduct = refundProductDetails.stream().map(RefundProductDetail::getRefundProductAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                        BigDecimal refundPlatformFreight = refundProductDetails.stream().map(RefundProductDetail::getRefundPlatformFreight).reduce(BigDecimal.ZERO, BigDecimal::add);
                        BigDecimal refundBaseFreight = refundProductDetails.stream().map(RefundProductDetail::getRefundBaseFreight).reduce(BigDecimal.ZERO, BigDecimal::add);
                        BigDecimal refundRegionFreight = refundProductDetails.stream().map(RefundProductDetail::getRefundRegionFreight).reduce(BigDecimal.ZERO, BigDecimal::add);
                        BigDecimal refundServiceAmount = refundProductDetails.stream().map(RefundProductDetail::getRefundServiceAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                        //加上退商品金额+退非商品金额 非商品金额-已退非商品金额总和
                        productAmount = productAmount.add(orderItem.getProductAmount().subtract(refundProduct).subtract(orderItem.getSubsidyFreeAmount()).subtract(orderItem.getProductFreeAmount()));
                        itemAmount = itemAmount.add(productAmount);
                        itemAmount = itemAmount.add(orderItem.getTotalAmount().subtract(orderItem.getProductAmount()).add(orderItem.getSubsidyFreeAmount()).subtract(refundPlatformFreight.add(refundBaseFreight).add(refundRegionFreight)
                                .add(refundServiceAmount)).add(orderItem.getFinancialServiceAmount().subtract(refundFinancialServicePrice)).subtract(difOtherAmount));
                    } else {
                        //结算价会主动差额退款,除取消订单都取结算价
                        productAmount = productAmount.add(orderItem.getProductAmount().subtract(loss).subtract(difDetail).subtract(orderItem.getSubsidyFreeAmount()).subtract(orderItem.getProductFreeAmount()));
                        itemAmount = itemAmount.add(productAmount);
                        //退非商品金额 已包括金融服务费 全退
                        itemAmount = itemAmount.add(orderItem.getTotalAmount().subtract(orderItem.getProductAmount()).add(orderItem.getSubsidyFreeAmount()).add(orderItem.getProductFreeAmount()).subtract(difOtherAmount));
                    }
                    totalRefundAmount = totalRefundAmount.add(itemAmount);
                } else {
                    //1/3*1
                    //部分退
                    //等比例退非商品金额 各项非商品金额*少货数量/订单项数量 之和
//                    BigDecimal rat = new BigDecimal(bo.getStockoutCount()).divide(new BigDecimal(orderItem.getCount()), 2, RoundingMode.HALF_UP);
                    BigDecimal subFreeAmount = orderItem.getSubsidyFreeAmount().multiply(new BigDecimal(bo.getStockoutCount())).divide(new BigDecimal(orderItem.getCount()), 2, RoundingMode.HALF_UP);
                    //获取剩余商品金额  退不能超过剩余金额
                    BigDecimal subtract = orderItem.getProductAmount().subtract(loss).subtract(difDetail).subtract(orderItem.getProductFreeAmount());
                    productAmount = productAmount.add(orderItem.getPrice().multiply(new BigDecimal(bo.getStockoutCount())).subtract(subFreeAmount).subtract(difDetail).compareTo(subtract) > 0
                            ? subtract : orderItem.getPrice().multiply(new BigDecimal(bo.getStockoutCount())).subtract(subFreeAmount).subtract(difDetail));
                    BigDecimal platformServiceAmount = (orderItem.getPlatformServiceAmount().subtract(orderItem.getPlatformServiceFreeAmount())).multiply(new BigDecimal(bo.getStockoutCount())).divide(new BigDecimal(orderItem.getCount()), 2, RoundingMode.HALF_UP);
                    BigDecimal platformFreightAmount = (orderItem.getPlatformFreightAmount().subtract(orderItem.getPlatformFreightFreeAmount())).multiply(new BigDecimal(bo.getStockoutCount())).divide(new BigDecimal(orderItem.getCount()), 2, RoundingMode.HALF_UP);
                    BigDecimal baseFreightAmount = (orderItem.getBaseFreightAmount().subtract(orderItem.getBaseFreightFreeAmount())).multiply(new BigDecimal(bo.getStockoutCount())).divide(new BigDecimal(orderItem.getCount()), 2, RoundingMode.HALF_UP);
                    BigDecimal regionFreightAmount = (orderItem.getRegionFreightAmount().subtract(orderItem.getRegionFreightFreeAmount())).multiply(new BigDecimal(bo.getStockoutCount())).divide(new BigDecimal(orderItem.getCount()), 2, RoundingMode.HALF_UP);
                    //退金融服务费
                    BigDecimal apportionTotalAmount = orderItem.getTotalAmount().subtract(orderItem.getFinancialServiceAmount());
                    //计算金融手续费
                    BigDecimal refundFinancialPrice = refundRecordService.getApportionRefundService(apportionTotalAmount,orderItem.getFinancialServiceAmount(),itemAmount,refundFinancialServicePrice);

                    totalRefundAmount = totalRefundAmount.add(productAmount.add(platformServiceAmount).add(platformFreightAmount).add(baseFreightAmount).add(regionFreightAmount));
                    itemAmount = itemAmount.add(productAmount);
                    itemAmount = itemAmount.add(platformServiceAmount.add(platformFreightAmount).add(baseFreightAmount).add(regionFreightAmount).add(refundFinancialPrice));
                }
                amount = amount.add(itemAmount);
                if (refundAmountMap.containsKey(bo.getOrderId())) {
                    refundAmountMap.put(bo.getOrderId(), refundAmountMap.get(bo.getOrderId()).add(totalRefundAmount));
                } else {
                    refundAmountMap.put(bo.getOrderId(), totalRefundAmount);
                }
            }
        }
        return amount;
    }

    /**
     * 确认缺货少货单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @Lock4j(name = OrderCacheNames.STOCKOUT_CONFIRM, keys = "#id", expire = 5000, acquireTimeout = 3000)
    public void stockoutConfirm(Long id) {
        //获取缺货少货单
        StockoutRecord stockoutRecord = baseMapper.selectById(id);
        if (ObjectUtil.isNotNull(stockoutRecord) &&
                stockoutRecord.getAuditStatus().equals(StockoutAuditStatusEnum.STATE_CONFIRM.getCode())) {
            //获取缺货少货单详情
            List<StockoutRecordDetail> stockoutRecordDetailList = stockoutRecordDetailMapper.selectByRecordId(id);
            //少货单要产生判责单
            this.stockoutCreateBlame(stockoutRecord, stockoutRecordDetailList);
            //修改明细表的确认状态
            BigDecimal refundAmount = BigDecimal.ZERO;
            for (StockoutRecordDetail stockoutRecordDetail : stockoutRecordDetailList) {
                stockoutRecordDetail.setStatus(StockoutAuditStatusEnum.HAS_CONFIRM.getCode());
                refundAmount = refundAmount.add(stockoutRecordDetail.getRefundProductAmount().add(stockoutRecordDetail.getRefundOtherAmount()));
            }
            //更新缺货少货单
            stockoutRecordDetailMapper.updateBatchById(stockoutRecordDetailList);
            //更新缺货少货单审核状态
            stockoutRecord.setAuditStatus(StockoutAuditStatusEnum.HAS_CONFIRM.getCode());
            if (stockoutRecord.getType().equals(1) || refundAmount.compareTo(BigDecimal.ZERO) == 0){
                stockoutRecord.setBlameStatus(BlameStatusEnum.FINISH.getCode());
            }else {
                stockoutRecord.setBlameStatus(BlameStatusEnum.STATE_BLAME.getCode());
            }
            if (ObjectUtil.isNotNull(LoginHelper.getUserId())) {
                RemoteUserBo userInfo = remoteUserService.getUserInfo(LoginHelper.getUserId());
                //更新操作人
                stockoutRecord.setOperateName(userInfo.getRealName());
                stockoutRecord.setOperateCode(userInfo.getUserCode());
            }
            baseMapper.updateById(stockoutRecord);
            List<Long> orderItemList = stockoutRecordDetailList.stream().map(StockoutRecordDetail::getOrderItemId).distinct().toList();
            List<StockoutRecordDetail> auditStatusList = stockoutRecordDetailMapper.getAuditStatusList(orderItemList, Collections.singletonList(StockoutAuditStatusEnum.HAS_CONFIRM.getCode()));
            //缺货少货数量 = 订单项数量
            List<StockoutRecordDetail> orderIdList = auditStatusList.stream().filter(l -> l.getOrderCount().equals(l.getStockoutCount())).toList();
            //更新订单状态 订单项全部都取消的,订单状态改为取消
            if (CollectionUtil.isNotEmpty(orderIdList)) {
                List<OrderItem> orderItems = new ArrayList<>();
                orderIdList.forEach(l -> {
                    if (orderItemList.contains(l.getOrderItemId())) {
                        OrderItem item = new OrderItem();
                        item.setId(l.getOrderItemId());
                        item.setStatus(OrderStatusEnum.CANCEL.getCode());
                        if (stockoutRecord.getType().equals(1)) {
                            item.setCancelType(OrderCancelTypeEnum.OUT.getCode());
                        } else {
                            item.setCancelType(OrderCancelTypeEnum.FEW.getCode());
                        }
                        item.setCancelTime(new Date());
                        orderItems.add(item);
                    }
                });
                if (CollectionUtil.isNotEmpty(orderItems)) {
                    orderItemMapper.updateBatchById(orderItems);
                }
                //查询订单
                LambdaQueryWrapper<OrderItem> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.select(OrderItem::getOrderId, OrderItem::getStatus)
                        .in(OrderItem::getOrderId, orderIdList.stream().map(StockoutRecordDetail::getOrderId).toList())
                        .eq(OrderItem::getDelFlag, 0);
                List<OrderItem> itemList = orderItemMapper.selectList(queryWrapper);
                if (CollectionUtil.isNotEmpty(itemList)) {
                    Map<Long, List<OrderItem>> itemMap = itemList.stream().collect(Collectors.groupingBy(OrderItem::getOrderId));
                    List<Long> orderIds = new ArrayList<>();
                    itemMap.forEach((k, v) -> {
                        if (v.stream().allMatch(i -> i.getStatus().equals(OrderStatusEnum.CANCEL.getCode()))) {
                            orderIds.add(k);
                        }
                    });
                    //更新订单状态
                    if (CollectionUtil.isNotEmpty(orderIds)) {
                        LambdaUpdateWrapper<Order> updateWrapper = new LambdaUpdateWrapper<>();
                        updateWrapper.set(Order::getStatus, OrderStatusEnum.CANCEL.getCode())
                                .set(Order::getCancelType, stockoutRecord.getType().equals(1) ? OrderCancelTypeEnum.OUT.getCode() : OrderCancelTypeEnum.FEW.getCode())
                                .set(Order::getCancelTime, new Date())
                                .in(Order::getId, orderIds);
                        orderMapper.update(updateWrapper);
                    }
                }
            }
            //少货确认扣库存，缺货单确认释放暂用
            this.createCwStock(stockoutRecord,stockoutRecordDetailList);
            //缺货少货单生成退款单
            this.confirmOrderRefundAmount(stockoutRecord, stockoutRecordDetailList);
            try {
                if (!"配货商品".equals(stockoutRecord.getSpuName())) {
                    //发通知
                    stockoutRecordDetailList.forEach(l -> sendSmsOfConfirm(stockoutRecord, l, stockoutRecord.getSaleDate()));
                }
            } catch (Exception e) {
                log.keyword("sendSmsOfConfirm", id).error("缺货少货单确认id【{}】发送短信失败", id);
            }
        }
    }
    /**
     * 创建出库单 - 基采、产地 商品
     */
    public void createCwStock(StockoutRecord stockoutRecord,List<StockoutRecordDetail> stockoutRecordDetailList) {
        if (CollectionUtil.isEmpty(stockoutRecordDetailList)){
            return;
        }
        // 调用云仓出库单
        RemoteQueryInfoListBo listBo = new RemoteQueryInfoListBo();
        List<RemoteSupplierSkuInfoVo> remoteSupplierSkuInfoVos = remoteSupplierSkuService
                .queryInfoList(listBo.setSupplierSkuIdList(Lists.newArrayList(stockoutRecord.getSupplierSkuId())));
        RemoteSupplierSkuInfoVo skuInfoVo = remoteSupplierSkuInfoVos.get(0);
        List<Integer> types = CollectionUtil.newArrayList(SupplierSkuBusinessTypeEnum.BUSINESS_TYPE30.getCode()
                , SupplierSkuBusinessTypeEnum.BUSINESS_TYPE20.getCode());
        if (!types.contains(skuInfoVo.getBusinessType())){
            return;
        }
        if (Objects.equals(stockoutRecord.getType(),BlameSourceTypeEnum.STOCKOUT.getCode())) {
            if (!(!stockoutRecord.getCreateSceneCode().equals(StockOutCreateSceneEnum.NO_BUY_GOODS.getCode())
                    && !stockoutRecord.getCreateSceneCode().equals(StockOutCreateSceneEnum.AUTO_NO_BUY_GOODS.getCode())
                    && !stockoutRecord.getCreateSceneCode().equals(StockOutCreateSceneEnum.PICKING_ENTRUCK_GOODS.getCode()))){
                List<OrderItem> orderItems = orderItemMapper.selectBatchIds(stockoutRecordDetailList.stream()
                        .map(StockoutRecordDetail::getOrderItemId).toList());
                Map<Long, OrderItem> orderItemMap = orderItems.stream().collect(Collectors.toMap(OrderItem::getId, u -> u));

                List<RemoteCwTransDetailDTO> remoteCwTransDetailDTOS = stockoutRecordDetailList.stream().map(item -> {
                    OrderItem orderItem = orderItemMap.get(item.getOrderItemId());
                    //缺货确认释放锁定库存
                    RemoteCwTransDetailDTO sku = new RemoteCwTransDetailDTO();
                    sku.setSourceDetailId(stockoutRecord.getCode());
                    sku.setSkuId(skuInfoVo.getSkuId());
                    sku.setTransCode(TransTypeCodeEnum.QHQR.getCode());
                    sku.setIoQty(BigDecimal.ZERO.subtract(BigDecimal.valueOf(item.getStockoutCount())));
                    sku.setOrderItemId(item.getOrderItemId());
                    return sku;
                }).collect(Collectors.toList());
                remoteCwStockService.stockAllocation(remoteCwTransDetailDTOS);
            }
            else {

                RemoteCwTransHeadDTO  cwBo = new RemoteCwTransHeadDTO();
                cwBo.setRegionWhId(skuInfoVo.getRegionWhId());
                cwBo.setIoFlag(TransTypeIoFlagEnum.STATUS0.getCode());
                cwBo.setTransDate(new Date());
                cwBo.setTransCode(TransTypeCodeEnum.QHRK.getCode());
                cwBo.setRemark("缺货确认回滚库存");
                cwBo.setSourceType(TransSourceTypeEnum.STATUS60.getCode().longValue());
                cwBo.setSourceId(stockoutRecord.getCode());

                List<RemoteCwTransDetailDTO> cwTransDetailDTOList = stockoutRecordDetailList.stream().map(item -> {
                    RemoteCwTransDetailDTO sku = new RemoteCwTransDetailDTO();
                    BeanUtils.copyProperties(cwBo, sku);
                    sku.setSourceDetailId(stockoutRecord.getCode());
                    sku.setSkuId(skuInfoVo.getSkuId());
                    sku.setIoQty(BigDecimal.valueOf(item.getStockoutCount()));
                    return sku;
                }).collect(Collectors.toList());
                cwBo.setCwTransDetailDTOList(cwTransDetailDTOList);
                remoteCwStockService.insertByBo(cwBo);
            }

        }
        //自动创建的缺货单记录金额
        if (Objects.equals(stockoutRecord.getType(),BlameSourceTypeEnum.LESS_GOODS.getCode())
                && !Objects.equals(stockoutRecord.getCreateSceneCode(), 10)) {
            List<OrderItem> orderItems = orderItemMapper.selectBatchIds(stockoutRecordDetailList.stream()
                    .map(StockoutRecordDetail::getOrderItemId).toList());
            Map<Long, OrderItem> orderItemMap = orderItems.stream().collect(Collectors.toMap(OrderItem::getId, u -> u));

            List<RemoteTransDetailPriceDTO> remoteCwTransDetailDTOS = stockoutRecordDetailList.stream().map(item -> {
                OrderItem orderItem = orderItemMap.get(item.getOrderItemId());
                //缺货确认释放锁定库存
                RemoteTransDetailPriceDTO sku = new RemoteTransDetailPriceDTO();
                sku.setOrderId(item.getOrderId());
                sku.setOrderItemId(item.getOrderItemId());
                sku.setSaleDate(orderItem.getSaleDate());
                sku.setSupplierSkuId(orderItem.getSupplierSkuId());
                sku.setSkuId(skuInfoVo.getSkuId());
                sku.setTransCode(TransTypeCodeEnum.FJCK.getCode());
                sku.setIsLess(true);
                sku.setPrice(orderItem.getFinalPrice());
                sku.setCount(item.getStockoutCount());
                return sku;
            }).collect(Collectors.toList());

            stockBatchInfoPriceProducer.send(remoteCwTransDetailDTOS);
        }
    }
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void stockoutCreateBlame(StockoutRecord stockoutRecord, List<StockoutRecordDetail> stockoutRecordDetailList) {
        //重算一遍以防止中途有产生过退款
        CreateStockoutRecordBO bo = getStockoutAgain(stockoutRecord, stockoutRecordDetailList);
        if (stockoutRecord.getType().equals(BlameSourceTypeEnum.LESS_GOODS.getCode())) {
            //获取城市仓信息
            RemoteCityWhVo cityWhById = remoteCityWhService.queryById(stockoutRecord.getCityWhId());
            bo.setCityWhName(cityWhById.getName());
            bo.setCityWhCode(cityWhById.getCode());
            createBlameRecord(bo, stockoutRecordDetailList, stockoutRecord);
        }
    }

    /**
     * 缺货少货单确认时进行退款
     * <AUTHOR> on 2024/12/1:11:50
     * @param stockoutRecord
     * @param stockoutRecordDetailList
     * @return void
     */
    public void confirmOrderRefundAmount(StockoutRecord stockoutRecord, List<StockoutRecordDetail> stockoutRecordDetailList) {
        //少货和缺货不一样，少货可能会有总仓和单日退款的限额控制，比方成都仓单日退款超1000就需要人工审核退款
        if (stockoutRecord.getType().equals(BlameSourceTypeEnum.LESS_GOODS.getCode())) {
            refundRecordService.shortageRefund(stockoutRecord, stockoutRecordDetailList);
        } else {
            refundRecordService.stockoutConfirm(stockoutRecord, stockoutRecordDetailList, YNStatusEnum.ENABLE.getCode());
        }
    }

    /**
     * 作废缺货少货单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @Lock4j(name = OrderCacheNames.STOCKOUT_CONFIRM, keys = "#id", expire = 5000, acquireTimeout = 3000)
    public void stockoutInvalid(Long id) {
        //获取缺货少货单
        StockoutRecord stockoutRecord = baseMapper.selectById(id);
        if (ObjectUtil.isNotNull(stockoutRecord) &&
                stockoutRecord.getAuditStatus().equals(StockoutAuditStatusEnum.STATE_CONFIRM.getCode())) {
            //获取缺货少货单详情
            List<StockoutRecordDetail> stockoutRecordDetailList = stockoutRecordDetailMapper.selectList(new LambdaQueryWrapper<StockoutRecordDetail>()
                    .eq(StockoutRecordDetail::getStockoutRecordId, id).eq(StockoutRecordDetail::getDelFlag, 0));
            if (CollectionUtil.isNotEmpty(stockoutRecordDetailList)) {
                List<Long> orderItems = stockoutRecordDetailList.stream().map(StockoutRecordDetail::getOrderItemId).toList();
                List<OrderItem> orderItemList = orderItemMapper.selectBatchIds(orderItems);
                Map<Long, Integer> stockoutCountMap = stockoutRecordDetailList.stream()
                        .collect(Collectors.groupingBy(StockoutRecordDetail::getOrderItemId, Collectors.summingInt(StockoutRecordDetail::getStockoutCount)));
                //回退待装车||待送货
                List<RwEntruckVo> entruckVos = stockoutSkuRecordRecover(stockoutRecord, stockoutRecord.getStockoutCount());
                //回退待分货 返回新增了分货单的订单项
                List<OrderItem> addSortGoodList = sortGoodRecover(orderItemList, stockoutCountMap, stockoutRecord, entruckVos);
                //回退提货单数量
                List<ReceiveGoodsRecord> recordList = receiveGoodRecover(orderItems, stockoutCountMap, stockoutRecord, addSortGoodList, entruckVos);
                Map<Long, Integer> statusMap = recordList.stream().collect(Collectors.toMap(ReceiveGoodsRecord::getOrderId, ReceiveGoodsRecord::getStatus, (o1, o2) -> o2));
                //都要回退订单数量 更新订单项信息
                orderItemList.forEach(l -> {
                    //缺货 还原缺货数据
                    if (stockoutRecord.getType().equals(1)) {
                        l.setRefundOutCount(l.getRefundOutCount() - stockoutCountMap.get(l.getId()));
                    } else {
                        l.setRefundFewCount(l.getRefundFewCount() - stockoutCountMap.get(l.getId()));
                    }
                    //更新工作状态 待提货 已提货
                    if (statusMap.containsKey(l.getOrderId()) && statusMap.get(l.getOrderId()).equals(ReceiveGoodsStatusEnum.STAY.getCode())) {
                        l.setWorkStatus(OrderWorkStatusEnum.WAIT_EXTRACT.getCode());
                    }
                    if (statusMap.containsKey(l.getOrderId()) && statusMap.get(l.getOrderId()).equals(ReceiveGoodsStatusEnum.FINISH.getCode())) {
                        l.setWorkStatus(OrderWorkStatusEnum.EXTRACT_FINISH.getCode());
                    }
                    //还原订单项状态
                    l.setStatus(OrderStatusEnum.ALREADY.getCode());
                    l.setCancelType(null);
                    l.setCancelTime(null);
                });
                orderItemMapper.updateBatchById(orderItemList);
                //获取订单状态 防止给取消掉了
                List<Order> orders = orderMapper.selectBatchIds(orderItemList.stream().map(OrderItem::getOrderId).toList());
                orders.forEach(l -> l.setStatus(OrderStatusEnum.ALREADY.getCode()));
                orderMapper.updateBatchById(orders);
            }
            //作废缺货少货单状态
            stockoutRecord.setAuditStatus(StockoutAuditStatusEnum.INVALID.getCode());
            stockoutRecord.setBlameStatus(BlameStatusEnum.INVALID.getCode());
            if (ObjectUtil.isNotNull(LoginHelper.getUserId())) {
                RemoteUserBo userInfo = remoteUserService.getUserInfo(LoginHelper.getUserId());
                //更新操作人
                stockoutRecord.setOperateName(userInfo.getRealName());
                stockoutRecord.setOperateCode(userInfo.getUserCode());
            }
            baseMapper.updateById(stockoutRecord);
            stockoutRecordDetailMapper.stockoutInvalid(id);
            //同步作废退款占用单
            refundRecordService.stockoutInvalid(stockoutRecord, stockoutRecordDetailList, new HashMap<>());
        }
    }

    private List<RwEntruckVo> stockoutSkuRecordRecover(StockoutRecord stockoutRecord, Integer count) {
        List<StockoutSkuRecord> records = new ArrayList<>();
        //查询缺货少货商品记录
        List<StockoutSkuRecord> stockoutSkuRecordList = stockoutSkuRecordMapper.selectList(new LambdaQueryWrapper<StockoutSkuRecord>()
                .eq(StockoutSkuRecord::getRegionWhId, stockoutRecord.getRegionWhId()).eq(StockoutSkuRecord::getSupplierSkuId, stockoutRecord.getSupplierSkuId())
                .eq(StockoutSkuRecord::getLogisticsId, stockoutRecord.getLogisticsId()).eq(StockoutSkuRecord::getSaleDate, stockoutRecord.getSaleDate())
                .eq(StockoutSkuRecord::getType, 1).eq(StockoutSkuRecord::getCreateSceneCode, stockoutRecord.getCreateSceneCode()).eq(StockoutSkuRecord::getDelFlag, 0));
        List<RwEntruckVo> entruckVos = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(stockoutSkuRecordList)) {
            List<StockoutSkuRecord> updateList = new ArrayList<>();
            //遍历扣减,直到缺货数量为0
            for (StockoutSkuRecord s : stockoutSkuRecordList) {
                if (count > 0) {
                    StockoutSkuRecord s1 = BeanUtil.copyProperties(s, StockoutSkuRecord.class);
                    if (count >= s.getStockoutCount()) {
                        count -= s.getStockoutCount();
                        s.setStockoutCount(0);
                        s.setDelFlag(s.getId());
                    } else {
                        s.setStockoutCount(s.getStockoutCount() - count);
                        s1.setStockoutCount(count);
                        count = 0;
                    }
                    updateList.add(s);
                    records.add(s1);
                }
            }
            if (CollectionUtil.isNotEmpty(records)) {
                //待装车回退
                records.forEach(l -> {
                    if (!stockoutRecord.getCreateSceneCode().equals(StockOutCreateSceneEnum.NO_BUY_GOODS.getCode())
                            && !stockoutRecord.getCreateSceneCode().equals(StockOutCreateSceneEnum.AUTO_NO_BUY_GOODS.getCode())
                            && !stockoutRecord.getCreateSceneCode().equals(StockOutCreateSceneEnum.PICKING_ENTRUCK_GOODS.getCode())) {
                        RwEntruckVo entruckVo = regionEntruckAffairService.repairEntruckDiffQuantity(l.getRelationId(), l.getSupplierSkuId(), l.getStockoutCount());
                        //已经接车,要处理后续单据
                        if (ObjectUtil.isNotNull(entruckVo) && DeliveryStatusEnum.COMPLETE_RECEIVE.getCode().equals(entruckVo.getStatus())) {
                            entruckVos.add(entruckVo);
                        }
                    }
                });
            }
            if (CollectionUtil.isNotEmpty(updateList)) {
                //更新
                stockoutSkuRecordMapper.updateBatchById(updateList);
            }
        }
        return entruckVos;
    }

    private List<ReceiveGoodsRecord> receiveGoodRecover(List<Long> orderItems, Map<Long, Integer> stockoutCountMap,
                                                        StockoutRecord stockoutRecord, List<OrderItem> addSortGoodList, List<RwEntruckVo> entruckVos) {
        List<ReceiveGoodsRecord> returnList = new ArrayList<>();
        Map<Long, Integer> countMap = new HashMap<>();
        //获取提货单详情
        List<ReceiveGoodsRecordDetail> receiveGoodsRecordDetails = receiveGoodsRecordDetailMapper.selectList(new LambdaQueryWrapper<ReceiveGoodsRecordDetail>()
                .in(ReceiveGoodsRecordDetail::getOrderItemId, orderItems).eq(ReceiveGoodsRecordDetail::getDelFlag, 0));
        //获取城市仓配置
        //获取城市仓提货配置
        RemoteCityWhVo remoteCityWhVo = remoteCityWhService.queryById(stockoutRecord.getCityWhId());
        if (CollectionUtil.isNotEmpty(receiveGoodsRecordDetails)) {
            //获取提货单
            List<Long> receiveIds = receiveGoodsRecordDetails.stream().map(ReceiveGoodsRecordDetail::getReceiveGoodsRecordId).toList();
            List<ReceiveGoodsRecord> receiveGoodsRecords = receiveGoodsRecordMapper.selectBatchIds(receiveIds);
//            Map<Long, Integer> sortGoodsStatusMap = new HashMap<>();
            //更新提货单数量
            receiveGoodsRecordDetails.forEach(l -> {
                if (stockoutRecord.getCreateSceneCode().equals(StockOutCreateSceneEnum.RECEIVE_GOODS.getCode())
                        || stockoutRecord.getCreateSceneCode().equals(StockOutCreateSceneEnum.CITY_CREATE.getCode())) {
                    l.setActualCount(l.getActualCount() + stockoutCountMap.get(l.getOrderItemId()));
                    l.setStayCount(stockoutCountMap.get(l.getOrderItemId()) + l.getStayCount());
                    //更新售后时间
                    this.updateAfterSaleTime(l);
                    //恢复数量
                    if (countMap.containsKey(l.getReceiveGoodsRecordId())) {
                        countMap.put(l.getReceiveGoodsRecordId(), countMap.get(l.getReceiveGoodsRecordId()) + stockoutCountMap.get(l.getOrderItemId()));
                    } else {
                        countMap.put(l.getReceiveGoodsRecordId(), stockoutCountMap.get(l.getOrderItemId()));
                    }
                } else if (l.getSortGoodsStatus().equals(SortGoodsStatusEnum.FINISH.getCode())) {
                    l.setActualCount(stockoutCountMap.get(l.getOrderItemId()) + l.getActualCount());
                    //更新售后时间
                    this.updateAfterSaleTime(l);
                    l.setStayCount(stockoutCountMap.get(l.getOrderItemId()) + l.getStayCount());
                    //恢复数量
                    if (countMap.containsKey(l.getReceiveGoodsRecordId())) {
                        countMap.put(l.getReceiveGoodsRecordId(), countMap.get(l.getReceiveGoodsRecordId()) + stockoutCountMap.get(l.getOrderItemId()));
                    } else {
                        countMap.put(l.getReceiveGoodsRecordId(), stockoutCountMap.get(l.getOrderItemId()));
                    }
                }
//                sortGoodsStatusMap.put(l.getReceiveGoodsRecordId(), l.getSortGoodsStatus());
            });
            receiveGoodsRecordDetailMapper.updateBatchById(receiveGoodsRecordDetails);

            receiveGoodsRecords.forEach(l -> {
                if (!stockoutRecord.getCreateSceneCode().equals(StockOutCreateSceneEnum.RECEIVE_GOODS.getCode())
                        || !stockoutRecord.getCreateSceneCode().equals(StockOutCreateSceneEnum.CITY_CREATE.getCode())) {
                    if (stockoutRecord.getType().equals(1) &&
                            (l.getStatus().equals(ReceiveGoodsStatusEnum.FINISH.getCode()) || l.getStatus().equals(ReceiveGoodsStatusEnum.STAY.getCode()))) {
                        l.setStayCount(l.getStayCount() + countMap.getOrDefault(l.getId(), 0));
                    } else if (stockoutRecord.getType().equals(2)) {
                        l.setStayCount(l.getStayCount() + countMap.getOrDefault(l.getId(), 0));
                    }
                    if (l.getStatus().equals(ReceiveGoodsStatusEnum.UN_SORT.getCode())) {
                        if (remoteCityWhVo.getDeliveryCompleteRule() == 1) {
                            l.setStatus(ReceiveGoodsStatusEnum.FINISH.getCode());
                        } else {
                            l.setStatus(ReceiveGoodsStatusEnum.STAY.getCode());
                        }
                    }
//                if (sortGoodsStatusMap.containsKey(l.getId()) && sortGoodsStatusMap.get(l.getId()).equals(SortGoodsStatusEnum.FINISH.getCode())){
//                    l.setStatus(ReceiveGoodsStatusEnum.FINISH.getCode());
//                }
                } else {
                    l.setStayCount(l.getStayCount() + countMap.getOrDefault(l.getId(), 0));
                }
            });
            returnList.addAll(receiveGoodsRecords);
            receiveGoodsRecordMapper.updateBatchById(receiveGoodsRecords);
            //跟新提货数量
            sortGoodsService.writeOutStockPrice(CollectionUtil.newArrayList(receiveGoodsRecords),receiveGoodsRecordDetails,true);
        }
        //缺货 新增了分货单 就要新增提货单
        if (stockoutRecord.getType().equals(1) && CollectionUtil.isNotEmpty(addSortGoodList)) {
            List<Long> orderIds = new ArrayList<>(addSortGoodList.stream().map(OrderItem::getOrderId).distinct().toList());
            QueryDistributionBo bo = new QueryDistributionBo();
            bo.setOrderItemIdList(addSortGoodList.stream().map(OrderItem::getId).toList());
            List<QueryDistributionVo> orderList = orderService.queryDistribution(bo);
            Map<Long, QueryDistributionVo> orderMap = orderList.stream().collect(Collectors.toMap(QueryDistributionVo::getId, Function.identity()));
            List<Long> customerIdList = orderList.stream().map(QueryDistributionVo::getCustomerId).distinct().toList();
            //查询用户拍子号情况
            //根据物流线查询提货点id
            Long placeId = remoteRegionLogisticsService.getPlaceId(stockoutRecord.getLogisticsId());
            //销售日取值问题
            LocalDate date = stockoutRecord.getSaleDate().isAfter(LocalDate.now())?LocalDate.now():stockoutRecord.getSaleDate();
            List<SortGoodsDetail> sortGoodsDetails = sortGoodsDetailMapper.getDeliveryNumbe(customerIdList, LocalDate.now().atTime(LocalTime.MIDNIGHT), placeId);
            Map<Long, Long> customerDeliveryNumberMap = new HashMap<>();
            //获取最大拍子号
            if (CollectionUtils.isNotEmpty(sortGoodsDetails)) {
                for (SortGoodsDetail l : sortGoodsDetails) {
                    if (!customerDeliveryNumberMap.containsKey(l.getCustomerId())) {
                        customerDeliveryNumberMap.put(l.getCustomerId(), l.getDeliveryNumber());
                    }
                }
            }
            //先查询是否有提货单,有就直接新增提货单明细,没有就先新增提货单
            List<ReceiveGoodsRecord> receiveGoodsRecords = receiveGoodsRecordMapper.selectList(new LambdaQueryWrapper<ReceiveGoodsRecord>()
                    .in(ReceiveGoodsRecord::getOrderId, orderIds).eq(ReceiveGoodsRecord::getDelFlag, 0));
            if (CollectionUtil.isNotEmpty(receiveGoodsRecords)) {
                List<Long> list = receiveGoodsRecords.stream().map(ReceiveGoodsRecord::getOrderId).toList();
                orderIds.removeAll(list);
            }
            if (CollectionUtil.isNotEmpty(orderIds)) {
                List<ReceiveGoodsRecord> receiveGoodsRecordsAdd = new ArrayList<>();
                //获取用户信息 只是为了获取惯用名 --！
                List<RemoteCustomerVo> customerVos = remoteBasCustomerService.getByIds(orderList.stream().map(QueryDistributionVo::getCustomerId).toList());
                Map<Long, RemoteCustomerVo> userCodeMap = customerVos.stream().collect(Collectors.toMap(RemoteCustomerVo::getId, Function.identity()));
                orderIds.forEach(l -> {
                    QueryDistributionVo order = orderMap.get(l);
                    ReceiveGoodsRecord receiveGoodsRecord = new ReceiveGoodsRecord();
                    receiveGoodsRecord.setCustomerId(order.getCustomerId());
                    receiveGoodsRecord.setCustomerName(order.getCustomerName());
                    if (userCodeMap.containsKey(order.getCustomerId()) && StringUtils.isNotBlank(userCodeMap.get(order.getCustomerId()).getAlias())) {
                        receiveGoodsRecord.setCustomerAlias(userCodeMap.get(order.getCustomerId()).getAlias());
                    }
                    receiveGoodsRecord.setOrderId(order.getId());
                    receiveGoodsRecord.setOrderCode(order.getCode());
                    receiveGoodsRecord.setDeliveryNumber(customerDeliveryNumberMap.get(order.getCustomerId()));
                    receiveGoodsRecord.setOrderTime(order.getCreateTime());
                    receiveGoodsRecord.setPlaceId(order.getPlaceId());
                    receiveGoodsRecord.setPlaceName(order.getPlaceName());
                    receiveGoodsRecord.setAddress(order.getAddress());
                    receiveGoodsRecord.setLat(order.getLat());
                    receiveGoodsRecord.setLng(order.getLng());
                    receiveGoodsRecord.setCityWhId(stockoutRecord.getCityWhId());
                    receiveGoodsRecord.setOrderCount(order.getCount());
                    receiveGoodsRecord.setStayCount(0);
                    if (remoteCityWhVo.getDeliveryCompleteRule() == 1) {
                        receiveGoodsRecord.setStatus(ReceiveGoodsStatusEnum.FINISH.getCode());
                    } else {
                        receiveGoodsRecord.setStatus(ReceiveGoodsStatusEnum.STAY.getCode());
                    }
                    receiveGoodsRecord.setOrderType(order.getBusinessType());
                    receiveGoodsRecord.setCreateName(order.getCreateName());
                    receiveGoodsRecord.setReceiveTime(LocalDate.now());
                    receiveGoodsRecord.setSaleDate(order.getSaleDate());
                    receiveGoodsRecordsAdd.add(receiveGoodsRecord);
                });
                //新增详情单
                receiveGoodsRecordMapper.insertBatch(receiveGoodsRecordsAdd);
                receiveGoodsRecords.addAll(receiveGoodsRecordsAdd);
                returnList.addAll(receiveGoodsRecordsAdd);
            }
            Map<Long, ReceiveGoodsRecord> orderIdMap = receiveGoodsRecords.stream().collect(Collectors.toMap(ReceiveGoodsRecord::getOrderId, Function.identity()));
            //新增提货单详情
            List<ReceiveGoodsRecordDetail> receiveDetails = new ArrayList<>();
            List<ReceiveGoodsRecord> updateRecord = new ArrayList<>();
            List<OrderItem> updateItem = new ArrayList<>();
            for (QueryDistributionVo order : orderList) {
                order.getOrderItemList().forEach(o -> {
                    ReceiveGoodsRecordDetail receiveGoodsRecordDetail = new ReceiveGoodsRecordDetail();
                    ReceiveGoodsRecord record = orderIdMap.get(o.getOrderId());
                    receiveGoodsRecordDetail.setReceiveGoodsRecordId(record.getId());
                    receiveGoodsRecordDetail.setOrderItemId(o.getId());
                    receiveGoodsRecordDetail.setSupplierSkuId(o.getSupplierSkuId());
                    receiveGoodsRecordDetail.setSpuName(o.getSpuName());
                    receiveGoodsRecordDetail.setSpuGrossWeight(o.getSpuGrossWeight());
                    receiveGoodsRecordDetail.setSpuNetWeight(o.getSpuNetWeight());
                    receiveGoodsRecordDetail.setImgUrl(o.getImgUrl());
                    receiveGoodsRecordDetail.setCount(o.getCount());
                    receiveGoodsRecordDetail.setProdType(o.getProdType());
                    if (o.getAfterSaleDay() == 0 || o.getProdType().equals(2)) {
                        receiveGoodsRecordDetail.setAfterSaleStatus(1);
                    } else {
                        receiveGoodsRecordDetail.setAfterSaleStatus(2);
                    }
                    receiveGoodsRecordDetail.setPrice(o.getFinalPrice());
                    receiveGoodsRecordDetail.setAfterSaleDay(o.getAfterSaleDay());
                    receiveGoodsRecordDetail.setSortGoodsStatus(SortGoodsStatusEnum.STAY.getCode());
                    //提货单是原来就有的且已经提货了,待提货数量&&实际提货数量做处理
//                    if (!record.getStatus().equals(ReceiveGoodsStatusEnum.UN_SORT.getCode())) {
                    receiveGoodsRecordDetail.setStayCount(stockoutCountMap.get(o.getId()));
                    receiveGoodsRecordDetail.setActualCount(receiveGoodsRecordDetail.getStayCount());
                    receiveGoodsRecordDetail.setSortGoodsStatus(SortGoodsStatusEnum.FINISH.getCode());
                    //更新售后时间
                    receiveGoodsRecordDetail.setAfterSaleTime(AfterSaleUtil.getLastAfterSaleDay());
                    record.setStayCount(record.getStayCount() + receiveGoodsRecordDetail.getStayCount());

                    if (remoteCityWhVo.getDeliveryCompleteRule() == 1) {
                        record.setStatus(ReceiveGoodsStatusEnum.FINISH.getCode());
                    } else {
                        record.setStatus(ReceiveGoodsStatusEnum.STAY.getCode());
                    }
                    updateRecord.add(record);
                    OrderItem oi = new OrderItem();
                    oi.setId(o.getId());
                    oi.setWorkStatus(OrderWorkStatusEnum.EXTRACT_FINISH.getCode());
                    updateItem.add(oi);
//                    }
                    receiveDetails.add(receiveGoodsRecordDetail);
                });
            }
            if (CollectionUtil.isNotEmpty(receiveDetails)) {
                //新增详情单
                receiveGoodsRecordDetailMapper.insertBatch(receiveDetails);
            }
            if (CollectionUtil.isNotEmpty(updateRecord)) {
                //新增详情单
                receiveGoodsRecordMapper.updateBatchById(updateRecord);
                returnList.addAll(updateRecord);
            }
            if (CollectionUtil.isNotEmpty(updateItem)) {
                //新增详情单
                orderItemMapper.updateBatchById(updateItem);
            }
        }
        //查询缺货少货商品记录
        List<StockoutSkuRecord> stockoutSkuRecordList = stockoutSkuRecordMapper.selectList(new LambdaQueryWrapper<StockoutSkuRecord>()
                .in(StockoutSkuRecord::getOrderItemId, orderItems).eq(StockoutSkuRecord::getCreateSceneCode, StockOutCreateSceneEnum.SORT_GOODS_FINISH.getCode())
                .eq(StockoutSkuRecord::getDelFlag, 0));
        //删除相关记录
        if (CollectionUtil.isNotEmpty(stockoutSkuRecordList)) {
            stockoutSkuRecordList.forEach(l -> l.setDelFlag(l.getId()));
            stockoutSkuRecordMapper.updateBatchById(stockoutSkuRecordList);
        }
        return returnList;
    }

    private List<OrderItem> sortGoodRecover(List<OrderItem> orderItems, Map<Long, Integer> stockoutCountMap, StockoutRecord stockoutRecord, List<RwEntruckVo> entruckVos) {
        List<OrderItem> returnList = new ArrayList<>();
        List<Long> orderItemIds = orderItems.stream().map(OrderItem::getId).toList();
        //根据缺货少货单详情获取分货单详情
        List<SortGoodsDetail> sortGoodsDetails = sortGoodsDetailMapper.selectList(new LambdaQueryWrapper<SortGoodsDetail>()
                .in(SortGoodsDetail::getOrderItemId, orderItemIds).eq(SortGoodsDetail::getDelFlag, 0));
        //提货时不回退分货单数量
        if (CollectionUtil.isNotEmpty(sortGoodsDetails) && !stockoutRecord.getCreateSceneCode().equals(StockOutCreateSceneEnum.RECEIVE_GOODS.getCode())
                && !stockoutRecord.getCreateSceneCode().equals(StockOutCreateSceneEnum.CITY_CREATE.getCode())) {
            //获取分货单
            List<Long> sortGoodIds = sortGoodsDetails.stream().map(SortGoodsDetail::getSortGoodsId).toList();
            List<SortGoods> sortGoods = sortGoodsMapper.selectBatchIds(sortGoodIds);
            Map<Long, Integer> countMap = new HashMap<>();
            List<SortGoodsDetail> updateList = new ArrayList<>();
            List<SortGoodsRecord> sortGoodsRecordList = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(stockoutCountMap.keySet())) {
                sortGoodsDetails.forEach(l -> {
                    if (stockoutCountMap.containsKey(l.getOrderItemId())) {
                        if (countMap.containsKey(l.getSortGoodsId())) {
                            countMap.put(l.getSortGoodsId(), countMap.get(l.getSortGoodsId()) + stockoutCountMap.get(l.getOrderItemId()));
                        } else {
                            countMap.put(l.getSortGoodsId(), stockoutCountMap.get(l.getOrderItemId()));
                        }
                        l.setAllReceived(l.getAllReceived() + stockoutCountMap.get(l.getOrderItemId()));
                        //已发车和已完成的 直接分
                        if (l.getStatus().equals(SortGoodsStatusEnum.FINISH.getCode())) {
                            l.setReceived(l.getReceived() + stockoutCountMap.get(l.getOrderItemId()));
                        }
                        l.setUnreceived(l.getAllReceived() - l.getReceived());
                        updateList.add(l);
                        if (stockoutRecord.getType().equals(2)) {
                            //新增一条分货操作记录
                            SortGoodsRecord sortGoodsRecord = new SortGoodsRecord();
                            sortGoodsRecord.setSortGoodsId(l.getSortGoodsId());
                            sortGoodsRecord.setCustomerId(l.getCustomerId());
                            sortGoodsRecord.setCustomerName(l.getCustomerName());
                            sortGoodsRecord.setDeliveryNumber(l.getDeliveryNumber());
                            sortGoodsRecord.setStrDeliveryNumber(l.getPlaceLevel2Code()+l.getDeliveryNumber());
                            sortGoodsRecord.setSortCount(stockoutCountMap.get(l.getOrderItemId()));
                            //查询时会根据时间分组,这里做统一时间,避免新增数量过多时有时间差异
                            sortGoodsRecord.setCreateTime(new Date());
                            sortGoodsRecordList.add(sortGoodsRecord);
                        }
                    }
                });
            }
            if (CollectionUtil.isNotEmpty(sortGoodsRecordList)) {
                sortGoodsRecordMapper.insertBatch(sortGoodsRecordList);
            }
            //还原分货单数据
            sortGoods.forEach(l -> {
                //不是已完成状态,恢复都分货中  否则直接在已完成那直接还原对应数量
                if (l.getStatus().equals(SortGoodsStatusEnum.SORTYET.getCode())) {
                    l.setStatus(SortGoodsStatusEnum.SORTING.getCode());
                }
                if (countMap.containsKey(l.getId())) {
                    //应收还原
                    l.setAllReceived(l.getAllReceived() + countMap.get(l.getId()));
                    if (stockoutRecord.getType().equals(1)) {
                        l.setReceived(l.getReceived() + countMap.get(l.getId()));
                        l.setUnreceived(l.getAllReceived() - l.getReceived());
                    }
                }
                if (countMap.containsKey(l.getId()) && CollectionUtil.isNotEmpty(entruckVos) && l.getStatus().equals(SortGoodsStatusEnum.FINISH.getCode())) {
                    l.setActualReceived(l.getActualReceived() + countMap.get(l.getId()));
                }
                if (countMap.containsKey(l.getId()) && stockoutRecord.getType().equals(2) && l.getStatus().equals(SortGoodsStatusEnum.FINISH.getCode())) {
                    l.setActualReceived(l.getActualReceived() + countMap.get(l.getId()));
                }
                if (stockoutRecord.getType().equals(2)) {
                    l.setSortCount(l.getSortCount() + 1);
                }
            });
            sortGoodsMapper.updateBatchById(sortGoods);
            if (CollectionUtil.isNotEmpty(updateList)) {
                sortGoodsDetailMapper.updateBatchById(updateList);
            }
        }
        //缺货且已接车  看有没有没创建的分货单  没创建分货单相当于这个商品没上车,直接走接车流程
        if (stockoutRecord.getType().equals(1) && CollectionUtil.isNotEmpty(entruckVos)) {
            RwEntruckVo entruckInfo = entruckVos.get(0);
            List<RwEntruckGoodsVo> rwEntruckGoodsVos;
            RwEntruckRecordVo rwEntruckRecordVo = new RwEntruckRecordVo();
            //直属仓另外处理
            if (entruckInfo.getIsAffiliated().equals(1)){
                rwEntruckRecordVo = rwEntruckRecordService.queryReceiveGoodsList(entruckInfo.getId());
                rwEntruckGoodsVos = rwEntruckRecordVo.getGoodsList();
            }else {
                RwEntruckDepartVo rwEntruckDepartVo = rwDepartService.queryReceiveGoodsList(entruckInfo.getId());
                rwEntruckGoodsVos = rwEntruckDepartVo.getGoodsList();
            }

            Map<Long, RwEntruckGoodsVo> entruckGoodVoMap = rwEntruckGoodsVos.stream().collect(Collectors.toMap(RwEntruckGoodsVo::getSupplierSkuId, Function.identity()));
            //获取分货单 缺货单下都是同一个物流线及城市仓,也过滤出来
            List<Long> skuId = rwEntruckGoodsVos.stream().map(RwEntruckGoodsVo::getSupplierSkuId).toList();
            List<SortGoods> sortGoods = sortGoodsMapper.selectList(new LambdaQueryWrapper<SortGoods>().in(SortGoods::getSupplierSkuId, skuId).eq(SortGoods::getDelFlag, 0)
                    .eq(SortGoods::getCityWhId, orderItems.get(0).getCityWhId()).eq(SortGoods::getLogisticsId, orderItems.get(0).getLogisticsId()));
            List<OrderItem> addList = orderItems;
            if (CollectionUtil.isNotEmpty(sortGoods)) {
                addList = orderItems.stream().filter(l -> sortGoods.stream().noneMatch(m -> m.getSupplierSkuId().equals(l.getSupplierSkuId()))).toList();
            }
            //创建分货单
            if (CollectionUtil.isNotEmpty(addList)) {
                returnList = addList;
                QueryDistributionBo bo = new QueryDistributionBo();
                bo.setOrderItemIdList(addList.stream().map(OrderItem::getId).toList());
                List<QueryDistributionVo> orderList = orderService.queryDistribution(bo);
                List<QueryDistributionItemVo> orderItemList = new ArrayList<>();
                Map<Long, Date> orderCreateTimeMap = new HashMap<>();
                Map<Long, Long> customerPlaceMap = new HashMap<>();
                Map<String, QueryDistributionVo> orderMap = new HashMap<>();
                for (QueryDistributionVo vo : orderList) {
                    orderItemList.addAll(vo.getOrderItemList());
                    orderCreateTimeMap.put(vo.getId(), vo.getCreateTime());
                    customerPlaceMap.put(vo.getCustomerId(), vo.getPlaceIdLevel2());
                    orderMap.put(vo.getCustomerId().toString() + vo.getPlaceIdLevel2(), vo);
                }
                Map<Long, List<QueryDistributionItemVo>> orderItemMap = orderItemList.stream().collect(Collectors.groupingBy(QueryDistributionItemVo::getSupplierSkuId));
                //订单商品数量
                Map<Long, Integer> itemCountMap = new HashMap<>();
                Map<Long, Integer> itemRefundCountMap = new HashMap<>();
                orderItemMap.forEach((k, v) -> {
                    itemCountMap.put(k, v.stream().mapToInt(QueryDistributionItemVo::getCount).sum());
                    itemRefundCountMap.put(k, v.stream().mapToInt(QueryDistributionItemVo::getRefundCount).sum());
                });
                //查询用户拍子号情况
                //根据物流线查询提货点id
                Long place = remoteRegionLogisticsService.getPlaceId(entruckInfo.getLogisticsId());
                Long placeId = place == null ? orderList.get(0).getPlaceId() : place;
                String placeLv2Code;
                if (orderList.get(0).getPlaceIdLevel2() != 0L) {
                    RemoteCityWhPlaceVo placeVo = remoteCityWhPlaceService.queryById(orderList.get(0).getPlaceIdLevel2());
                    placeLv2Code = placeVo.getPlaceName();
                } else {
                    placeLv2Code = null;
                }
                List<SortGoodsDetail> sortGoodsDetailList = sortGoodsDetailMapper.getDeliveryNumbe(null,LocalDate.now().atTime(LocalTime.MIDNIGHT),placeId);
                Map<String, Long> customerDeliveryNumberMap = new HashMap<>();
                //获取最大拍子号
                final long[] deliveryNumber = {0L};
                if (CollectionUtils.isNotEmpty(sortGoodsDetailList)) {
                    for (SortGoodsDetail l : sortGoodsDetailList) {
                        String key = ObjectUtil.isNotNull(customerPlaceMap.get(l.getCustomerId())) ? customerPlaceMap.get(l.getCustomerId()).toString() : "";
                        if (!customerDeliveryNumberMap.containsKey(l.getCustomerId() + key)) {
                            customerDeliveryNumberMap.put(l.getCustomerId() + key, l.getDeliveryNumber());
                        }
                    }
                    deliveryNumber[0] = sortGoodsDetailList.stream().map(SortGoodsDetail::getDeliveryNumber).max(Long::compareTo).orElse(0L);
                }
                Long maxNumber = 0L;
                Map<Long, Long> childMaxNumberMap = new HashMap<>();
                List<String> regionsList = Arrays.stream(orderProperties.getDeliveryNumberAutoRegions().split(";")).toList();
                if (CollectionUtils.isNotEmpty(regionsList)){
                    QueryDistributionBo nuBo = new QueryDistributionBo();
                    nuBo.setLogisticsIdList(ListUtil.toList(entruckInfo.getLogisticsId()));
                    nuBo.setRegionsWhIdList(regionsList.stream().map(Long::valueOf).toList());
                    nuBo.setSaleDate(entruckInfo.getSaleDate());
                    List<String> statusList = new ArrayList<>();
                    statusList.add(OrderStatusEnum.ALREADY.getCode());
                    statusList.add(OrderStatusEnum.FINISH.getCode());
                    nuBo.setStatusList(statusList);
                    List<QueryDeliveryNumberVo> orderNumList = orderService.queryOrderDeliveryNumber(nuBo);
                    if (CollectionUtils.isNotEmpty(orderNumList)) {
                        for (QueryDeliveryNumberVo l : orderNumList){
                            //存在先占用
                            if (CollectionUtil.isNotEmpty(orderMap) &&orderMap.containsKey(l.getCustomerId().toString()+l.getPlaceIdLevel2())){
                                customerDeliveryNumberMap.put(l.getCustomerId().toString() + (l.getPlaceIdLevel2() == 0 ? "" : l.getPlaceIdLevel2()), l.getDeliveryNumber());
                                //去掉，剩的需要继续走占用逻辑
                                orderMap.remove(l.getCustomerId().toString()+l.getPlaceIdLevel2());
                            }
                            if (l.getPlaceIdLevel2() == 0 && maxNumber < l.getDeliveryNumber()){
                                maxNumber = l.getDeliveryNumber();
                            } else if (l.getPlaceIdLevel2() != 0 && childMaxNumberMap.containsKey(l.getPlaceIdLevel2())) {
                                childMaxNumberMap.put(l.getPlaceIdLevel2(), Math.max(childMaxNumberMap.get(l.getPlaceIdLevel2()), l.getDeliveryNumber()));
                            } else if (l.getPlaceIdLevel2() != 0) {
                                childMaxNumberMap.put(l.getPlaceIdLevel2(), l.getDeliveryNumber());
                            }
                        }
                        //获取这个总仓的销售日
                        RemoteRegionWhVo regionWhVo = remoteRegionWhService.queryById(entruckInfo.getRegionWhId());
                        LocalDate saleDate = SaleDateUtil.getSaleDate(regionWhVo.getSalesTimeEnd());
                        if (saleDate.equals(entruckInfo.getSaleDate()) && CollectionUtil.isNotEmpty(orderMap)){
                            int numberCount = 0;
                            Map<String, Integer> childNumberCountMap = new HashMap<>();
                            for (QueryDistributionVo vo : orderMap.values()){
                                if (vo.getPlaceIdLevel2() == 0L){
                                    numberCount++;
                                }else {
                                    childNumberCountMap.put(vo.getPlaceId()+";"+vo.getPlaceIdLevel2(), childNumberCountMap.getOrDefault(vo.getPlaceId()+";"+vo.getPlaceIdLevel2(), 0) + 1);
                                }
                            }
                            //先占位 后面再根据这个往前算
                            if (numberCount > 0) {
                                maxNumber = DeliveryNumberUtil.generateSerialNumbers(entruckInfo.getSaleDate().toString(), placeId.toString(), numberCount);
                            }
                            if (CollectionUtil.isNotEmpty(childNumberCountMap)) {
                                for (Map.Entry<String, Integer> longIntegerEntry : childNumberCountMap.entrySet()) {
                                    Long number = DeliveryNumberUtil.generateSerialNumbers(entruckInfo.getSaleDate().toString(), longIntegerEntry.getKey(), longIntegerEntry.getValue());
                                    String[] split = longIntegerEntry.getKey().split(";");
                                    childMaxNumberMap.put(Long.valueOf(split[1]), number);
                                }
                            }
                            //直接赋值
                            for (QueryDistributionVo vo : orderMap.values()){
                                if (!customerDeliveryNumberMap.containsKey(vo.getCustomerId().toString() + (vo.getPlaceIdLevel2() == 0 ? "" : vo.getPlaceIdLevel2()))){
                                    if (vo.getPlaceIdLevel2() == 0L){
                                        customerDeliveryNumberMap.put(vo.getCustomerId().toString(), maxNumber--);
                                    }else if (childMaxNumberMap.containsKey(vo.getPlaceIdLevel2())){
                                        customerDeliveryNumberMap.put(vo.getCustomerId().toString() + vo.getPlaceIdLevel2(), childMaxNumberMap.get(vo.getPlaceIdLevel2()));
                                        childMaxNumberMap.put(vo.getPlaceIdLevel2(), childMaxNumberMap.get(vo.getPlaceIdLevel2()) - 1);
                                    }
                                }
                            }
                        }
                    }
                }
                List<SortGoodsDetail> addDetailList = new ArrayList<>();
                List<OrderItem> updateList = new ArrayList<>();
                RwEntruckRecordVo finalRwEntruckRecordVo = rwEntruckRecordVo;
                orderItemList.stream().collect(Collectors.groupingBy(QueryDistributionItemVo::getSupplierSkuId)).forEach((k, v) -> {
                    SortGoods s = new SortGoods();
                    RwEntruckGoodsVo rwEntruckGoodsVo = entruckGoodVoMap.get(k);
                    s.setCityWhId(entruckInfo.getCityWhId());
                    s.setRwEntruckId(entruckInfo.getId());
                    s.setRwDepartId(entruckInfo.getDepartId());
                    s.setRwEntruckNo(entruckInfo.getEntruckNo());
                    s.setLogisticsId(entruckInfo.getLogisticsId());
                    s.setRegionWhId(entruckInfo.getRegionWhId());
                    s.setPlaceId(placeId);
                    s.setSupplierSkuId(k);
                    s.setSpuName(rwEntruckGoodsVo.getSpuName());
                    s.setImgUrl(rwEntruckGoodsVo.getImgUrl());
                    s.setSpuGrossWeight(rwEntruckGoodsVo.getSpuGrossWeight());
                    s.setSpuNetWeight(rwEntruckGoodsVo.getSpuNetWeight());
                    s.setOrderReceived(v.stream().mapToInt(QueryDistributionItemVo::getCount).sum());
                    //回退的数量
                    int stockoutCount = 0;
                    for (QueryDistributionItemVo l : v) {
                        if (stockoutCountMap.containsKey(l.getId())) {
                            stockoutCount += stockoutCountMap.get(l.getId());
                        }
                    }
                    if (itemRefundCountMap.get(rwEntruckGoodsVo.getSupplierSkuId()) != null) {
                        s.setAllReceived(itemCountMap.get(rwEntruckGoodsVo.getSupplierSkuId()) - itemRefundCountMap.get(rwEntruckGoodsVo.getSupplierSkuId()) + stockoutCount);
                    } else {
                        s.setAllReceived(itemCountMap.get(rwEntruckGoodsVo.getSupplierSkuId()));
                    }
                    s.setReceived(stockoutCount);
                    s.setUnreceived(itemCountMap.get(rwEntruckGoodsVo.getSupplierSkuId()) - rwEntruckGoodsVo.getEntruckQuantity());
                    s.setActualReceived(stockoutCount);
                    s.setSortCount(stockoutCount);
                    s.setSaleDate(entruckInfo.getSaleDate());
                    s.setStatus(SortGoodsStatusEnum.FINISH.getCode());
                    s.setSpuSearchName(rwEntruckGoodsVo.getSpuName());
                    if (entruckInfo.getIsAffiliated().equals(1)){
                        s.setCityWhId(finalRwEntruckRecordVo.getCityWhId());
                        s.setRwEntruckId(finalRwEntruckRecordVo.getId());
                        s.setRwDepartId(0L);
                        s.setRwEntruckNo(finalRwEntruckRecordVo.getEntruckNo());
                        s.setLogisticsId(finalRwEntruckRecordVo.getLogisticsId());
                        s.setRegionWhId(finalRwEntruckRecordVo.getRegionWhId());
                        s.setSaleDate(finalRwEntruckRecordVo.getSaleDate());
                    }
                    sortGoodsMapper.insert(s);
                    for (QueryDistributionItemVo item : v) {
                        SortGoodsDetail sortGoodsDetail = new SortGoodsDetail();
                        sortGoodsDetail.setSortGoodsId(s.getId());
                        sortGoodsDetail.setStatus(s.getStatus());
                        sortGoodsDetail.setCustomerId(item.getCustomerId());
                        sortGoodsDetail.setCustomerName(item.getCustomerName());
                        sortGoodsDetail.setOrderItemId(item.getId());
                        sortGoodsDetail.setOrderReceivable(item.getCount());
                        sortGoodsDetail.setAllReceived(stockoutCountMap.getOrDefault(item.getId(), 0));
                        sortGoodsDetail.setReceived(stockoutCountMap.getOrDefault(item.getId(), 0));
                        sortGoodsDetail.setUnreceived(item.getCount() - item.getRefundCount());
                        //拍子号
                        Long placeLv2 = customerPlaceMap.get(item.getCustomerId());
                        if (customerDeliveryNumberMap.containsKey(item.getCustomerId()+ placeLv2.toString())) {
                            sortGoodsDetail.setDeliveryNumber(customerDeliveryNumberMap.get(item.getCustomerId() + placeLv2.toString()));
                        } else {
                            sortGoodsDetail.setDeliveryNumber(deliveryNumber[0] + 1);
                            customerDeliveryNumberMap.put(item.getCustomerId() + placeLv2.toString(), sortGoodsDetail.getDeliveryNumber());
                            deliveryNumber[0]++;
                        }
                        if (placeLv2Code != null){
                            sortGoodsDetail.setPlaceLevel2Code(placeLv2Code);
                        }
                        sortGoodsDetail.setOrderTime(orderCreateTimeMap.get(item.getOrderId()));
                        addDetailList.add(sortGoodsDetail);

                        //修改订单项状态
                        OrderItem updateItem = new OrderItem();
                        updateItem.setWorkStatus(OrderWorkStatusEnum.SHARE_ING.getCode());
                        updateItem.setId(item.getId());
                        updateList.add(updateItem);
                    }
                });
                //新增分货单详情
                if (CollectionUtils.isNotEmpty(addDetailList)) {
                    sortGoodsDetailMapper.insertBatch(addDetailList);
                }
                //修改订单项状态
                if (CollectionUtils.isNotEmpty(updateList)) {
                    orderItemMapper.updateBatchById(updateList);
                }
            }
        }
        //查询缺货少货商品记录
        List<StockoutSkuRecord> stockoutSkuRecordList = stockoutSkuRecordMapper.selectList(new LambdaQueryWrapper<StockoutSkuRecord>()
                .in(StockoutSkuRecord::getOrderItemId, orderItems.stream().map(OrderItem::getId).toList()).eq(StockoutSkuRecord::getCreateSceneCode, StockOutCreateSceneEnum.SORT_GOODS_FINISH.getCode())
                .eq(StockoutSkuRecord::getDelFlag, 0));
        //删除相关记录
        if (CollectionUtil.isNotEmpty(stockoutSkuRecordList)) {
            stockoutSkuRecordList.forEach(l -> l.setDelFlag(l.getId()));
            stockoutSkuRecordMapper.updateBatchById(stockoutSkuRecordList);
        }
        return returnList;
    }

    /**
     * 修改缺货少货单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStockout(StockoutUpdateBO bo) {
        //获取缺货少货单
        StockoutRecord stockoutRecord = baseMapper.selectById(bo.getId());
        if (stockoutRecord.getAuditStatus().equals(StockoutAuditStatusEnum.STATE_CONFIRM.getCode())) {
            //获取缺货少货单详情  按id倒序(生成数据时按匹配规则优先缺货的在前面,这里用倒序,将后生成的先保证供货)
            List<StockoutRecordDetail> stockoutRecordDetailList = stockoutRecordDetailMapper.selectList(new LambdaQueryWrapper<StockoutRecordDetail>()
                    .eq(StockoutRecordDetail::getStockoutRecordId, bo.getId()).eq(StockoutRecordDetail::getDelFlag, 0).orderByDesc(StockoutRecordDetail::getId));
            List<Long> orderItems = new ArrayList<>();
            Map<Long, Integer> stockoutCountMap = new HashMap<>();

            Map<Long, Integer> newCountMap = new HashMap<>();
            //差异数量
            int stockoutCount = stockoutRecord.getStockoutCount() - bo.getStockoutCount();
            if (stockoutCount > 0) {
                List<StockoutRecordDetail> updateList = new ArrayList<>();
                //遍历到缺货少货数量为0为止
                for (StockoutRecordDetail s : stockoutRecordDetailList) {
                    if (stockoutCount > 0) {
                        if (stockoutCount >= s.getStockoutCount()) {
                            stockoutCount -= s.getStockoutCount();
                            stockoutCountMap.put(s.getOrderItemId(), s.getStockoutCount());
                            newCountMap.put(s.getId(), s.getStockoutCount());
                            s.setStockoutCount(0);
                            s.setDelFlag(s.getId());
                        } else {
                            stockoutCountMap.put(s.getOrderItemId(), stockoutCount);
                            s.setStockoutCount(s.getStockoutCount() - stockoutCount);
                            newCountMap.put(s.getId(), stockoutCount);
                            stockoutCount = 0;
                        }
                        orderItems.add(s.getOrderItemId());
                        updateList.add(s);
                    }
                }
                List<OrderItem> orderItemList = orderItemMapper.selectBatchIds(orderItems);
                //更新缺货少货商品记录表信息
                List<RwEntruckVo> entruckVos = stockoutSkuRecordRecover(stockoutRecord, stockoutRecord.getStockoutCount() - bo.getStockoutCount());
                //少货-回退待分货
                List<OrderItem> addSortGoodList = sortGoodRecover(orderItemList, stockoutCountMap, stockoutRecord, entruckVos);
                //提货少货-回退提货单数量
                List<ReceiveGoodsRecord> records = receiveGoodRecover(orderItems, stockoutCountMap, stockoutRecord, addSortGoodList, entruckVos);
                Map<Long, Integer> statusMap = records.stream().collect(Collectors.toMap(ReceiveGoodsRecord::getOrderId, ReceiveGoodsRecord::getStatus, (o1, o2) -> o2));
                List<StockoutRecordDetail> detailList = stockoutRecordDetailList.stream().filter(l -> l.getDelFlag().equals(0L)).toList();
                if (CollectionUtil.isNotEmpty(detailList)) {
                    //缺货少货单重算
                    getStockoutAgain(stockoutRecord, detailList);
                    stockoutRecordDetailMapper.updateBatchById(detailList);
                } else {
                    //缺货少货详情都没了,记录直接删掉
                    stockoutRecord.setDelFlag(stockoutRecord.getId());
                }
                //更新缺货少货单数量
                stockoutRecord.setStockoutCount(bo.getStockoutCount());
                if (ObjectUtil.isNotNull(LoginHelper.getUserId())) {
                    RemoteUserBo userInfo = remoteUserService.getUserInfo(LoginHelper.getUserId());
                    //更新操作人
                    stockoutRecord.setOperateName(userInfo.getRealName());
                    stockoutRecord.setOperateCode(userInfo.getUserCode());
                }
                //更新创建说明
                baseMapper.updateById(stockoutRecord);
                //更新缺货少货单详情
                if (CollectionUtil.isNotEmpty(updateList)) {
                    stockoutRecordDetailMapper.updateBatchById(updateList);
                }

                orderItemList.forEach(l -> {
                    if (stockoutCountMap.containsKey(l.getId())) {
                        //缺货 还原缺货数据
                        if (stockoutRecord.getType().equals(1)) {
                            l.setRefundOutCount(l.getRefundOutCount() - stockoutCountMap.get(l.getId()));
                        } else {
                            l.setRefundFewCount(l.getRefundFewCount() - stockoutCountMap.get(l.getId()));
                        }
                        //更新工作状态 待提货 已提货
                        if (statusMap.containsKey(l.getOrderId()) && statusMap.get(l.getOrderId()).equals(ReceiveGoodsStatusEnum.STAY.getCode())) {
                            l.setWorkStatus(OrderWorkStatusEnum.WAIT_EXTRACT.getCode());
                        }
                        if (statusMap.containsKey(l.getOrderId()) && statusMap.get(l.getOrderId()).equals(ReceiveGoodsStatusEnum.FINISH.getCode())) {
                            l.setWorkStatus(OrderWorkStatusEnum.EXTRACT_FINISH.getCode());
                        }
                        //还原订单项状态
                        l.setStatus(OrderStatusEnum.ALREADY.getCode());
                        l.setCancelType(null);
                        l.setCancelTime(null);
                    }
                });
                orderItemMapper.updateBatchById(orderItemList);
                //获取订单状态 防止给取消掉了
                List<Order> orders = orderMapper.selectBatchIds(orderItemList.stream().map(OrderItem::getOrderId).toList());
                orders.forEach(l -> l.setStatus(OrderStatusEnum.ALREADY.getCode()));
                orderMapper.updateBatchById(orders);

                //缺货少货修改完数量后，先取消占用，再重新占用
                refundRecordService.stockoutInvalid(stockoutRecord, stockoutRecordDetailList, newCountMap);
                //重新查最新的数据,发起新的占用
                refundRecordService.stockoutOccupy(stockoutRecordDetailMapper.selectList(new LambdaQueryWrapper<StockoutRecordDetail>()
                        .eq(StockoutRecordDetail::getStockoutRecordId, bo.getId()).eq(StockoutRecordDetail::getDelFlag, 0).orderByDesc(StockoutRecordDetail::getId)));
            } else if (stockoutCount < 0) {
                throw new ServiceException("修改数量不能大于原数量");
            }
        } else {
            throw new ServiceException(StockoutAuditStatusEnum.loadByCode(stockoutRecord.getAuditStatus()).getDesc() + "状态下不可修改");
        }
    }

    @Override
    public List<LocalDate> getSaleDateList(Long customerId) {
        if (ObjectUtil.isEmpty(customerId)) {
            return Collections.emptyList();
        }
        // 获取当前日期
        LocalDate today = LocalDate.now();
        // 计算第一个日期（今天）
        LocalDate firstDay = today;
        // 计算最后一个日期（今天之前的第六天）
        LocalDate lastSevenDay = today.minusDays(6);
        List<ReceiveGoodsDetailVO> detailList = receiveGoodsRecordDetailMapper.getSaleDateList(customerId, firstDay.toString(), lastSevenDay.toString());
        log.keyword("getSaleDateList").info("detailList:{}", detailList);
        if (ObjectUtil.isEmpty(detailList)) {
            return Collections.emptyList();
        }
        return detailList.stream().map(ReceiveGoodsDetailVO::getSaleDate).filter(ObjectUtil::isNotEmpty).toList();
    }

    @Override
    public List<Long> getCustomerList(StockoutSearchBO bo) {
        if (ObjectUtil.isEmpty(bo) || ObjectUtil.isEmpty(bo.getSaleDate())) {
            return Collections.emptyList();
        }
        List<ReceiveGoodsDetailVO> detailList = receiveGoodsRecordDetailMapper.listBySaleDateAndCustomerId(bo);
        if (ObjectUtil.isEmpty(detailList)) {
            return Collections.emptyList();
        }
        return detailList.stream().map(ReceiveGoodsDetailVO::getCustomerId).distinct().toList();
    }

    @Override
    public StockoutDetailVO getStockoutOrderCodeByCode(String billCode) {
        if (ObjectUtil.isEmpty(billCode)) {
            return null;
        }
        return baseMapper.getStockoutOrderCodeByCode(billCode);
    }


    @Override
    public List<ReceiveGoodsDetailVO> listBySaleDateAndCustomerId(StockoutSearchBO bo) {
        if (ObjectUtil.isEmpty(bo) || ObjectUtil.isEmpty(bo.getSaleDate()) || ObjectUtil.isEmpty(bo.getCustomerId())) {
            return Collections.emptyList();
        }
        List<ReceiveGoodsDetailVO> answer = new ArrayList<>();

        Map<Long, OrderItem> orderItemMap = new HashMap<>();
        Map<Long, List<StockoutRecordDetail>> detailMap = new HashMap<>();
        Map<Long, RemoteSupplierSkuInfoVo> remoteSupplierSkuInfoVoMap = new HashMap<>();

        List<ReceiveGoodsDetailVO> detailList = receiveGoodsRecordDetailMapper.listBySaleDateAndCustomerId(bo);
        if (ObjectUtil.isEmpty(detailList)) {
            return Collections.emptyList();
        }
        List<Long> orderItemIds = detailList.stream().map(ReceiveGoodsDetailVO::getOrderItemId).toList();
        List<OrderItem> orderItems = orderItemMapper.selectBatchIds(orderItemIds);
        orderItemMap = orderItems.stream().collect(Collectors.toMap(OrderItem::getId, u -> u, (n1, n2) -> n1));

        List<Long> supplierSkuIds = detailList.stream().map(ReceiveGoodsDetailVO::getSupplierSkuId).filter(ObjectUtil::isNotEmpty).distinct().toList();
        List<RemoteSupplierSkuInfoVo> remoteSupplierSkuInfoVos = remoteSupplierSkuService.queryInfoList(new RemoteQueryInfoListBo().setSupplierSkuIdList(Lists.newArrayList(supplierSkuIds)));
        if (ObjectUtil.isNotEmpty(remoteSupplierSkuInfoVos)) {
            remoteSupplierSkuInfoVoMap = remoteSupplierSkuInfoVos.stream().collect(Collectors.toMap(RemoteSupplierSkuInfoVo::getId, u -> u, (n1, n2) -> n1));
        }

        List<StockoutRecordDetail> stockoutRecordDetailList = stockoutRecordDetailMapper.selectCreareList(orderItemIds,StockOutCreateSceneEnum.CITY_CREATE.getCode());
        if (ObjectUtil.isNotEmpty(stockoutRecordDetailList)) {
            detailMap = stockoutRecordDetailList.stream().collect(Collectors.groupingBy(StockoutRecordDetail::getOrderItemId));
        }

        long time = System.currentTimeMillis();

        for (ReceiveGoodsDetailVO detailVO : detailList) {
            OrderItem orderItem = orderItemMap.get(detailVO.getOrderItemId());
            List<StockoutRecordDetail> tmpRecordDetailList = detailMap.get(detailVO.getOrderItemId());
            if (ObjectUtil.isNotEmpty(orderItem) && !OrderStatusEnum.ALREADY.getCode().equals(orderItem.getStatus())) {
                log.keyword("城市仓新增少货单", "listBySaleDateAndCustomerId").info("订单状态不是已支付状态：{}",detailVO);
                continue;
            }
            long t = System.currentTimeMillis();
            Integer maxStockCount = this.getMaxStockCount(detailVO, orderItem, tmpRecordDetailList);
            log.keyword("城市仓新增少货单查询时间花费", "listBySaleDateAndCustomerId").info("城市仓新增少货单查询可报数量时间花费：{}", System.currentTimeMillis() - t);
            if (maxStockCount == 0) {
                log.keyword("城市仓新增少货单", "listBySaleDateAndCustomerId").info("获取最大数量为0：{}",detailVO);
                continue;
            }
            RemoteSupplierSkuInfoVo skuInfoVo = remoteSupplierSkuInfoVoMap.get(detailVO.getSupplierSkuId());
            if (ObjectUtil.isNotEmpty(skuInfoVo)) {
                if (SupplierSkuBusinessTypeEnum.BUSINESS_TYPE40.getCode().equals(skuInfoVo.getBusinessType())) {
                    log.keyword("城市仓新增少货单", "listBySaleDateAndCustomerId").info("属于配货商品，跳过：{}， sku:{}",detailVO, skuInfoVo);
                    continue;
                }
                detailVO.setProducer(skuInfoVo.getProducer());
                detailVO.setPackageWord(skuInfoVo.getPackageWord());
                detailVO.setSpuStandards(skuInfoVo.getSpuStandards());
                detailVO.setShortProducer(skuInfoVo.getShortProducer());
                detailVO.setBrand(skuInfoVo.getBrand());
            }
            detailVO.setOrderItemCreateTime(ObjectUtil.isNotEmpty(orderItem) ? orderItem.getCreateTime() : null);
            detailVO.setMaxCount(maxStockCount);
            answer.add(detailVO);
        }

        log.keyword("城市仓新增少货单查询", "listBySaleDateAndCustomerId").info("detail:{}, 总的花费时间：{}", detailList, System.currentTimeMillis() - time);

        return answer;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean stockoutOccupy(List<StockoutRecordDetail> details) {
        if (CollUtil.isEmpty(details)) {
            return true;
        }
        return refundRecordService.stockoutOccupy(details);
    }

    @Override
    public List<StockoutRecordDetail> getStockoutByItemIdList(List<Long> orderItemIds, List<Integer> statusList) {
        LambdaQueryWrapper<StockoutRecordDetail> lqw = Wrappers.lambdaQuery();
        lqw.in(StockoutRecordDetail::getOrderItemId, orderItemIds);
        lqw.in(CollUtil.isNotEmpty(statusList), StockoutRecordDetail::getStatus, statusList);
        lqw.eq(StockoutRecordDetail::getDelFlag, BanguoCommonConstant.notDelFlag);
        return stockoutRecordDetailMapper.selectList(lqw);
    }

    /**
     * 缺货少货单重算
     */
    private CreateStockoutRecordBO getStockoutAgain(StockoutRecord stockoutRecord, List<StockoutRecordDetail> detailList) {
        CreateStockoutRecordBO bo = new CreateStockoutRecordBO();
        //重新计算金额 先获取订单项
        List<Long> orderItems = detailList.stream().map(StockoutRecordDetail::getOrderItemId).toList();
        QueryDistributionBo queryDistributionBo = new QueryDistributionBo();
        queryDistributionBo.setOrderItemIdList(orderItems);
        List<QueryDistributionVo> orderList = orderService.queryDistribution(queryDistributionBo);
        Map<Long, QueryDistributionItemVo> itemVoMap = orderList.stream().map(QueryDistributionVo::getOrderItemList).flatMap(List::stream).collect(Collectors.toMap(QueryDistributionItemVo::getId, Function.identity()));
        Map<Long, QueryDistributionVo> distributionVoMap = orderList.stream().collect(Collectors.toMap(QueryDistributionVo::getId, Function.identity()));
        //获取退款记录详情
        List<RefundProductDetail> refundDetails = refundProductDetailMapper.selectList(new LambdaQueryWrapper<RefundProductDetail>()
                .in(RefundProductDetail::getOrderItemId, orderItems).ne(RefundProductDetail::getRefundStatus, RefundStatusEnum.REFUND_FAIL.getCode()).eq(RefundProductDetail::getDelFlag, 0));
        //流程变更,实际已退商品数量要从退款详情里获取
        Map<Long, Integer> refundCount = new HashMap<>();
        Map<Long, List<RefundProductDetail>> refundMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(refundDetails)) {
            refundCount = refundDetails.stream().filter(l -> l.getRefundType().equals(RefundTypeEnum.LACK.getCode()) || l.getRefundType().equals(RefundTypeEnum.LESS.getCode()))
                    .collect(Collectors.groupingBy(RefundProductDetail::getOrderItemId, Collectors.summingInt(RefundProductDetail::getStockoutCount)));
            refundMap = refundDetails.stream().collect(Collectors.groupingBy(RefundProductDetail::getOrderItemId));
        }
        //获取订单退款记录 用于计算退金融服务费
        List<Long> orders = detailList.stream().map(StockoutRecordDetail::getOrderId).toList();
        List<RefundRecord> refundRecords = refundRecordMapper.selectList(new LambdaQueryWrapper<RefundRecord>().in(RefundRecord::getOrderId, orders));
        //退款金额
        Map<Long, BigDecimal> refundAmountMap = new HashMap<>();
        //退金融服务费金额
        Map<Long, BigDecimal> refundServiceFeeMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(refundRecords)) {
            refundRecords.stream().collect(Collectors.groupingBy(RefundRecord::getOrderId)).forEach((k, v) -> {
                refundServiceFeeMap.put(k, v.stream().map(RefundRecord::getRefundFinancialServiceAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                refundAmountMap.put(k, v.stream().map(RefundRecord::getRefundAmount).reduce(BigDecimal.ZERO, BigDecimal::add).subtract(refundServiceFeeMap.get(k)));
            });
        }
        BigDecimal serviceAmount = BigDecimal.ZERO;
        BigDecimal freightAmount = BigDecimal.ZERO;
        BigDecimal financialServicePrice = BigDecimal.ZERO;
        BigDecimal baseFreight = BigDecimal.ZERO;
        BigDecimal regionFreight = BigDecimal.ZERO;
        BigDecimal platformFreight = BigDecimal.ZERO;
        BigDecimal subsidyFreeAmount = BigDecimal.ZERO;
        for (StockoutRecordDetail l : detailList) {
            QueryDistributionItemVo itemVo = itemVoMap.get(l.getOrderItemId());
            //赋值一下类型
            if (ObjectUtil.isNotEmpty(itemVo) && ObjectUtil.isNull(bo.getBusinessType())){
                bo.setBusinessType(itemVo.getBusinessType());
            }
            int count = refundCount.getOrDefault(l.getOrderItemId(), 0);
            List<RefundProductDetail> refundProductDetailList = refundMap.get(l.getOrderItemId());
            //存在报损跟差额退顺序混乱的问题,这里先排除掉这两种情况的数量,再分别算出金额以免扣多了
            BigDecimal difDetail = BigDecimal.ZERO;
            //差额退 退档服务费
            BigDecimal difOtherAmount = BigDecimal.ZERO;
            BigDecimal refundProAmount = BigDecimal.ZERO;
            BigDecimal loss = BigDecimal.ZERO;
            BigDecimal totalRefundAmount = BigDecimal.ZERO;
            BigDecimal refundFinancialServicePrice = BigDecimal.ZERO;
            //订单没结算，不用处理佣金问题
            BigDecimal distributionAmount = itemVo.getSettleCount() >= 1 ? itemVo.getDistributionAmount() : BigDecimal.ZERO;
            if (CollectionUtil.isNotEmpty(refundProductDetailList)) {
                for (RefundProductDetail productDetail : refundProductDetailList) {
                    if (productDetail.getRefundType().equals(RefundTypeEnum.DIFFERENCE.getCode())) {
                        difDetail = difDetail.add(productDetail.getRefundProductAmount());
                        difOtherAmount = difOtherAmount.add(productDetail.getRefundServiceAmount());
                    } else if (productDetail.getRefundType().equals(RefundTypeEnum.REPORT_LOSS.getCode())) {
                        loss = loss.add(productDetail.getRefundProductAmount());
                    }else {
                        refundProAmount = refundProAmount.add(productDetail.getRefundProductAmount());
                    }
                    if ( itemVo.getSettleCount() >= 1) {
                        distributionAmount = distributionAmount.subtract(productDetail.getRefundDistributionAmount());
                    }
                    refundFinancialServicePrice = refundFinancialServicePrice.add(productDetail.getRefundFinancialServicePrice());
                }
            }
            //缺货少货数量=订单项数量 全退
            if (count == 0 && l.getStockoutCount().equals(itemVo.getCount())) {
                serviceAmount = serviceAmount.add(itemVo.getPlatformServiceAmount().subtract(itemVo.getPlatformServiceFreeAmount()).subtract(difOtherAmount));
                freightAmount = freightAmount.add(itemVo.getFreightTotalAmount().subtract(itemVo.getFreightTotalFreeAmount()));
                baseFreight = baseFreight.add(itemVo.getBaseFreightAmount().subtract(itemVo.getBaseFreightFreeAmount()));
                regionFreight = regionFreight.add(itemVo.getRegionFreightAmount().subtract(itemVo.getRegionFreightFreeAmount()));
                platformFreight = platformFreight.add(itemVo.getPlatformFreightAmount().subtract(itemVo.getPlatformFreightFreeAmount()));
                //重算前可能产生差额/报损,这里要重新算
                l.setRefundProductAmount(itemVo.getProductAmount().subtract(loss).subtract(difDetail).subtract(itemVo.getProductFreeAmount()));
                l.setRefundOtherAmount(itemVo.getTotalAmount().subtract(itemVo.getProductAmount()).add(itemVo.getProductFreeAmount()).subtract(difOtherAmount));
                l.setRefundSubsidyFreeAmount(itemVo.getRegionSubsidyAmount().add(itemVo.getSkuSubsidyAmount()));
                //分销金额>0说明已结算，这里要将分销金额扣减出来
                if (distributionAmount.compareTo(BigDecimal.ZERO) > 0){
                    l.setRefundProductAmount(l.getRefundProductAmount().subtract(distributionAmount));
                }
                totalRefundAmount = totalRefundAmount.add(l.getRefundProductAmount()).add(l.getRefundOtherAmount());
                financialServicePrice = itemVo.getFinancialServiceAmount();
            }
            //将剩余的退
            else if (count != 0 && l.getStockoutCount().equals(itemVo.getCount() + count)) {
                BigDecimal refundFreightAmount = BigDecimal.ZERO;
                BigDecimal refundServiceAmount = BigDecimal.ZERO;
                BigDecimal refundProduct = BigDecimal.ZERO;
                BigDecimal refundBaseFreight = BigDecimal.ZERO;
                BigDecimal refundPlatformFreight = BigDecimal.ZERO;
                BigDecimal refundRegionFreight = BigDecimal.ZERO;
                //已退优惠金额
                BigDecimal refundRegionSubsidyAmount = BigDecimal.ZERO;
                for (RefundProductDetail detail : refundProductDetailList) {
                    refundFreightAmount = refundFreightAmount.add(detail.getRefundFreightAmount());
                    refundServiceAmount = refundServiceAmount.add(detail.getRefundServiceAmount());
                    refundProduct = refundProduct.add(detail.getRefundProductAmount());
                    refundBaseFreight = refundBaseFreight.add(detail.getRefundBaseFreight());
                    refundPlatformFreight = refundPlatformFreight.add(detail.getRefundPlatformFreight());
                    refundRegionFreight = refundRegionFreight.add(detail.getRefundRegionFreight());
                    refundRegionSubsidyAmount = refundRegionSubsidyAmount.add(detail.getRefundSubsidyFreeAmount());
                }
                serviceAmount = serviceAmount.add(itemVo.getPlatformServiceAmount().subtract(itemVo.getPlatformServiceFreeAmount()).subtract(refundServiceAmount));
                freightAmount = freightAmount.add(itemVo.getFreightTotalAmount().subtract(itemVo.getFreightTotalFreeAmount()).subtract(refundFreightAmount));
                baseFreight = baseFreight.add(itemVo.getBaseFreightAmount().subtract(itemVo.getBaseFreightFreeAmount()).subtract(refundBaseFreight));
                regionFreight = regionFreight.add(itemVo.getRegionFreightAmount().subtract(itemVo.getRegionFreightFreeAmount()).subtract(refundRegionFreight));
                platformFreight = platformFreight.add(itemVo.getPlatformFreightAmount().subtract(itemVo.getPlatformFreightFreeAmount()).subtract(refundPlatformFreight));
                l.setRefundSubsidyFreeAmount(itemVo.getRegionSubsidyAmount().add(itemVo.getSkuSubsidyAmount()).subtract(refundRegionSubsidyAmount));
                l.setRefundProductAmount(itemVo.getProductAmount().subtract(refundProduct).subtract(itemVo.getProductFreeAmount()));
                if (distributionAmount.compareTo(BigDecimal.ZERO) > 0){
                    l.setRefundProductAmount(l.getRefundProductAmount().subtract(distributionAmount));
                }
                l.setRefundOtherAmount(itemVo.getOtherTotalAmount().subtract(itemVo.getFreeTotalAmount().subtract(itemVo.getProductFreeAmount()))
                        .subtract(refundFreightAmount).subtract(refundServiceAmount).add(itemVo.getFinancialServiceAmount().subtract(refundFinancialServicePrice)));
                totalRefundAmount = totalRefundAmount.add(l.getRefundProductAmount()).add(l.getRefundOtherAmount());
                financialServicePrice = itemVo.getFinancialServiceAmount().subtract(refundFinancialServicePrice);
            }
            //部分退
            else {
                BigDecimal refundServiceAmount = BigDecimal.ZERO;
                if (CollectionUtil.isNotEmpty(refundProductDetailList)) {
                    for (RefundProductDetail detail : refundProductDetailList) {
                        if (detail.getRefundType().equals(RefundTypeEnum.DIFFERENCE.getCode())) {
                            refundServiceAmount = refundServiceAmount.add(detail.getRefundServiceAmount());
                        }
                    }
                }
                //缺货数量<订单项数量-已退数量(有的话) 按比例算
                //缺货少货 非商品金额按比例退,先算比例,再按订单项记录金额进行计算赋值
//                BigDecimal rat = new BigDecimal(l.getStockoutCount()).divide(new BigDecimal(itemVo.getCount()), 2, RoundingMode.HALF_UP);
                BigDecimal productFreeAmount = itemVo.getProductFreeAmount().multiply(new BigDecimal(l.getStockoutCount())).divide(new BigDecimal(itemVo.getCount()), 2, RoundingMode.HALF_UP);
                BigDecimal subFreeAmount = itemVo.getRegionSubsidyAmount().add(itemVo.getSkuSubsidyAmount()).multiply(new BigDecimal(l.getStockoutCount())).divide(new BigDecimal(itemVo.getCount()), 2, RoundingMode.HALF_UP);
                //获取剩余实付商品金额(结算价*数量-优惠加)  退不能超过剩余金额
                BigDecimal subtract = itemVo.getProductAmount().subtract(loss).subtract(difDetail).subtract(itemVo.getProductFreeAmount()).subtract(refundProAmount);
                BigDecimal difPath = difDetail.multiply(BigDecimal.valueOf(l.getStockoutCount())).divide(BigDecimal.valueOf(itemVo.getCount()));
                //单价*数量-按比例得出的优惠金额 跟剩余实付金额比较,取小的值
                l.setRefundProductAmount(itemVo.getPrice().multiply(new BigDecimal(l.getStockoutCount())).subtract(productFreeAmount).subtract(difPath).compareTo(subtract) > 0
                        ? subtract : itemVo.getPrice().multiply(new BigDecimal(l.getStockoutCount())).subtract(productFreeAmount).subtract(difPath));
                BigDecimal platformFreightAmount = (itemVo.getPlatformFreightAmount().subtract(itemVo.getPlatformFreightFreeAmount())).multiply(new BigDecimal(l.getStockoutCount())).divide(new BigDecimal(itemVo.getCount()), 2, RoundingMode.HALF_UP);
                BigDecimal baseFreightAmount = (itemVo.getBaseFreightAmount().subtract(itemVo.getBaseFreightFreeAmount())).multiply(new BigDecimal(l.getStockoutCount())).divide(new BigDecimal(itemVo.getCount()), 2, RoundingMode.HALF_UP);
                BigDecimal regionFreightAmount = (itemVo.getRegionFreightAmount().subtract(itemVo.getRegionFreightFreeAmount())).multiply(new BigDecimal(l.getStockoutCount())).divide(new BigDecimal(itemVo.getCount()), 2, RoundingMode.HALF_UP);
                BigDecimal platformFreightAmountLevel2 = (itemVo.getPlatformFreightAmountLevel2().subtract(itemVo.getPlatformFreightFreeAmountLevel2())).multiply(new BigDecimal(l.getStockoutCount())).divide(new BigDecimal(itemVo.getCount()), 2, RoundingMode.HALF_UP);
                //运费总和
                BigDecimal freight = platformFreightAmount.add(baseFreightAmount).add(regionFreightAmount).add(platformFreightAmountLevel2);
                BigDecimal service = (itemVo.getPlatformServiceAmount().subtract(itemVo.getPlatformServiceFreeAmount()).subtract(refundServiceAmount)).multiply(new BigDecimal(l.getStockoutCount())).divide(new BigDecimal(itemVo.getCount()), 2, RoundingMode.HALF_UP);
                serviceAmount = serviceAmount.add(service);
                freightAmount = freightAmount.add(freight);
                platformFreight = platformFreight.add(platformFreightAmount);
                baseFreight = baseFreight.add(baseFreightAmount);
                regionFreight = regionFreight.add(regionFreightAmount);
                if (distributionAmount.compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal amount = subtract.subtract(l.getRefundProductAmount()).subtract(distributionAmount);
                    if (amount.compareTo(BigDecimal.ZERO) < 0) {
                        l.setRefundProductAmount(l.getRefundProductAmount().add(amount));
                    }
                }
                //退非商品金额 = 退运费总和+退金融服务费+退代采服务费
                l.setRefundOtherAmount(freight.add(service));
                totalRefundAmount = totalRefundAmount.add(l.getRefundProductAmount()).add(l.getRefundOtherAmount());
                //退优惠金额
                l.setRefundSubsidyFreeAmount(subFreeAmount);
                //退金融服务费
                BigDecimal apportionTotalAmount = itemVo.getTotalAmount().subtract(itemVo.getFinancialServiceAmount());
                //退款金额
                BigDecimal detailRefundAmount = l.getRefundProductAmount().add(l.getRefundOtherAmount());
                //计算金融手续费
                BigDecimal refundFinancialPrice = refundRecordService.getApportionRefundService(apportionTotalAmount,itemVo.getFinancialServiceAmount(),detailRefundAmount,refundFinancialServicePrice);
                financialServicePrice = refundFinancialPrice;
                l.setRefundOtherAmount(l.getRefundOtherAmount().add(refundFinancialPrice));
            }
            if (refundAmountMap.containsKey(l.getOrderId())) {
                refundAmountMap.put(l.getOrderId(), refundAmountMap.get(l.getOrderId()).add(totalRefundAmount));
            } else {
                refundAmountMap.put(l.getOrderId(), totalRefundAmount);
            }
            if (l.getRefundSubsidyFreeAmount().compareTo(BigDecimal.ZERO) > 0) {
                subsidyFreeAmount = subsidyFreeAmount.add(l.getRefundSubsidyFreeAmount());
            }
        }
        bo.setFinancialServicePrice(financialServicePrice);
        bo.setFreightAmount(freightAmount);
        bo.setServiceAmount(serviceAmount);
        bo.setSubsidyFreeAmount(subsidyFreeAmount);
        bo.setPlatformFreight(platformFreight);
        bo.setRegionFreight(regionFreight);
        bo.setBaseFreight(baseFreight);
        //组创建备注,先切第一个逗号的
        String desc;
        if (stockoutRecord.getCreateRemark().contains(delimiter)) {
            desc = stockoutRecord.getCreateRemark().split(delimiter)[0];
        } else {
            // 兼容老文案
            desc = stockoutRecord.getCreateRemark().split("，")[0];
        }
        String s = createDescAgain(detailList, stockoutRecord, serviceAmount, freightAmount, financialServicePrice);
        stockoutRecord.setCreateRemark(desc + delimiter + s);
        return bo;
    }

    private BigDecimal getRefundFinancialServicePrice(Map<Long, BigDecimal> refundAmountMap, Map<Long, BigDecimal> refundServiceFeeMap,
                                                      QueryDistributionVo queryDistributionVo, StockoutRecordDetail l,QueryDistributionItemVo itemVo) {
        BigDecimal refundServiceFee = refundServiceFeeMap.get(l.getOrderId()) == null ? BigDecimal.ZERO : refundServiceFeeMap.get(l.getOrderId());
        BigDecimal refundAmount = refundAmountMap.get(l.getOrderId()) == null ? BigDecimal.ZERO : refundAmountMap.get(l.getOrderId());
        //判断该商品全部退掉后,整个订单的退款金额是否全部退了,全部退的情况下,将剩余的金融服务费也退掉
        if ((queryDistributionVo.getTotalAmount().subtract(queryDistributionVo.getFinancialServiceAmount()))
                .compareTo(refundAmount.add(l.getRefundProductAmount().add(l.getRefundOtherAmount()))) == 0) {
            l.setRefundOtherAmount(l.getRefundOtherAmount().add(queryDistributionVo.getFinancialServiceAmount().subtract(refundServiceFee)));
            return queryDistributionVo.getFinancialServiceAmount().subtract(refundServiceFee);
        }
        //没全退,按计算来,要剩0.01
        else {
            //退金融服务费
            BigDecimal apportionTotalAmount = itemVo.getTotalAmount().subtract(itemVo.getFinancialServiceAmount());
            //退款金额
            BigDecimal detailRefundAmount = l.getRefundProductAmount().add(l.getRefundOtherAmount());
            //计算金融手续费
            BigDecimal refundFinancialPrice = refundRecordService.getApportionRefundService(apportionTotalAmount,itemVo.getFinancialServiceAmount(),detailRefundAmount,refundServiceFee);
            l.setRefundOtherAmount(l.getRefundOtherAmount().add(refundFinancialPrice));
            return refundFinancialPrice;
        }
    }

    /**
     * 产生缺货单方法
     * 销售批次商品 数量
     * 缺货单创建维度: 销售批次商品-城市仓-物流线
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
//    @Lock4j(name = OrderCacheNames.STOCKOUT_CREATE_LOCK, keys = "#bo.supplierSkuId + '_' + #bo.logisticsId + '_' + #bo.saleDate", expire = 30000, acquireTimeout = 1000)
    public void stockout(BatchCreateStockoutBO bo, List<QueryDistributionVo> orderList) {
        //同个销售批次商品同个用户可能下多个订单
        Map<Long, List<QueryDistributionVo>> distributionVoMap = orderList.stream().collect(Collectors.groupingBy(QueryDistributionVo::getCustomerId));
        List<Long> customerIds = distributionVoMap.keySet().stream().toList();
        //获取用户报损率
        var statisticsVoMap = customerStatisticsService.getLast3MonthReportLoss(customerIds);
        log.keyword("createStockout").info("创建缺货少货单-缺货：获取用户报损率 statisticsVoMap.size={}", statisticsVoMap.size());
        //先排序 报损率降序,相同则比较订单金额,下单时间保底
        if (CollectionUtil.isNotEmpty(statisticsVoMap)) {
            //按报损率分组
            Map<BigDecimal, List<RemoteRcsVo>> map = statisticsVoMap.values().stream().collect(Collectors.groupingBy(RemoteRcsVo::getAmountRate));
            List<BigDecimal> reatList = map.keySet().stream().sorted(Comparator.reverseOrder()).toList();
            Queue<Long> queue = new LinkedList<>();
            for (BigDecimal l : reatList) {
                //先获取报损率相同的用户
                List<RemoteRcsVo> userLossVos = map.get(l);
                //获取他们的订单金额排序
                List<QueryDistributionVo> compare = new ArrayList<>();
                userLossVos.forEach(u -> compare.add(distributionVoMap.get(u.getCustomerId()).get(0)));
                //根据金额 下单时间降序排序  todo 优惠金额大的先缺
                List<QueryDistributionVo> sortedOrders = compare.stream()
                        .sorted(Comparator.comparing(QueryDistributionVo::getTotalAmount, Comparator.reverseOrder())
                                .thenComparing(QueryDistributionVo::getCreateTime, Comparator.reverseOrder()))
                        .toList();
                //放进FIFO队列
                sortedOrders.forEach(o -> queue.offer(o.getCustomerId()));
            }
            //组装缺货单详情
            List<StockoutRecordDetail> stockoutRecordDetails = new ArrayList<>();
            //获取物流信息
            RemoteRegionLogisticsQueryBo logisticsQueryBo = new RemoteRegionLogisticsQueryBo();
            List<Long> logisticsIds = orderList.stream().map(QueryDistributionVo::getOrderItemList).flatMap(List::stream).map(QueryDistributionItemVo::getLogisticsId).distinct().toList();
            logisticsQueryBo.setLogisticsIds(logisticsIds);
            List<RemoteRegionLogisticsVo> logisticsVos = remoteRegionLogisticsService.queryList(logisticsQueryBo);
            Map<Long, RemoteRegionLogisticsVo> logisticsVoMap = logisticsVos.stream().collect(Collectors.toMap(RemoteRegionLogisticsVo::getId, Function.identity()));
            //订单id-code map
            Map<Long, String> orderCodeMap = orderList.stream().collect(Collectors.toMap(QueryDistributionVo::getId, QueryDistributionVo::getCode));
            Map<String, CreateStockoutRecordBO> stockoutRecordMap = new HashMap<>();
            List<OrderItem> updateItemList = new ArrayList<>();
            //获取订单退款信息
            List<Long> orderId = orderList.stream().map(QueryDistributionVo::getId).distinct().toList();
            LambdaQueryWrapper<RefundRecord> oqw = new LambdaQueryWrapper<>();
            oqw.in(RefundRecord::getOrderId, orderId).eq(RefundRecord::getDelFlag, 0);
            List<RefundRecord> refundRecords = refundRecordMapper.selectList(oqw);
            Map<Long, QueryDistributionVo> orderMap = orderList.stream().collect(Collectors.toMap(QueryDistributionVo::getId, Function.identity()));
            //退款金额
            Map<Long, BigDecimal> refundAmountMap = new HashMap<>();
            //退金融服务费金额
            Map<Long, BigDecimal> refundServiceFeeMap = new HashMap<>();
            log.keyword("createStockout").info("创建缺货少货单-缺货：获取订单退款信息 refundRecords.size={}", refundRecords.size());
            if (CollectionUtil.isNotEmpty(refundRecords)) {
                refundRecords.stream().collect(Collectors.groupingBy(RefundRecord::getOrderId)).forEach((k, v) -> {
                    refundServiceFeeMap.put(k, v.stream().map(RefundRecord::getRefundFinancialServiceAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                    refundAmountMap.put(k, v.stream().map(RefundRecord::getRefundAmount).reduce(BigDecimal.ZERO, BigDecimal::add).subtract(refundServiceFeeMap.get(k)));
                });
            }
            List<QueryDistributionItemVo> otherItems = orderList.stream().map(QueryDistributionVo::getOrderItemList).flatMap(Collection::stream)
                    .sorted(Comparator.comparing(QueryDistributionItemVo::getPayTime, Comparator.reverseOrder()))
                    .filter(l -> !l.getWorkStatus().equals(OrderWorkStatusEnum.WAIT_SEND.getCode())).toList();
            //需要回退数量订单项map <订单项id, 回退数量>
            Map<Long, Integer> recoverCount = new HashMap<>();
            //订单项状态处于非待送货状态,可缺货数量要扣减分货单分货数量
            Map<Long, Integer> receiveMap = new HashMap<>();
            Map<Long, Integer> auditMap = new HashMap<>();
            //手动创建少货单数量map
            Map<Long, Integer> handStockoutCountMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(otherItems)) {
                List<Long> ids = otherItems.stream().map(QueryDistributionItemVo::getId).toList();
                LambdaQueryWrapper<SortGoodsDetail> sor = new LambdaQueryWrapper<>();
                sor.in(SortGoodsDetail::getOrderItemId, ids).eq(SortGoodsDetail::getDelFlag, 0);
                //查询分货单详情
                List<SortGoodsDetail> sortGoodsDetails = sortGoodsDetailMapper.selectList(sor);
                receiveMap = sortGoodsDetails.stream().collect(Collectors.groupingBy(SortGoodsDetail::getOrderItemId, Collectors.summingInt(SortGoodsDetail::getReceived)));
                List<StockoutRecordDetail> auditStatusList = stockoutRecordDetailMapper.getAuditStatusList(ids, Collections.singletonList(StockoutAuditStatusEnum.STATE_CONFIRM.getCode()));
                auditMap = auditStatusList.stream().collect(Collectors.groupingBy(StockoutRecordDetail::getOrderItemId, Collectors.summingInt(StockoutRecordDetail::getStockoutCount)));
                List<StockoutRecordDetail> stockoutRecordDetailList = stockoutRecordDetailMapper.selectCreareList(ids,StockOutCreateSceneEnum.CITY_CREATE.getCode());
                handStockoutCountMap = stockoutRecordDetailList.stream().collect(Collectors.groupingBy(StockoutRecordDetail::getOrderItemId, Collectors.summingInt(StockoutRecordDetail::getStockoutCount)));
            }
            //开始匹配
            Integer stockoutCount = bo.getStockoutCount();
            log.keyword("createStockout").info("创建缺货少货单-缺货：开始匹配 queue.size={}", queue.size());
            for (Long q : queue) {
                List<QueryDistributionItemVo> orderItemList = distributionVoMap.get(q).stream().map(QueryDistributionVo::getOrderItemList).flatMap(Collection::stream)
                        .sorted(Comparator.comparing(QueryDistributionItemVo::getPayTime, Comparator.reverseOrder())).toList();
                List<Long> ids = orderItemList.stream().map(QueryDistributionItemVo::getId).toList();
                List<RefundProductDetail> refundDetails = refundRecordService.getRefundByItemId(ids);
                Map<Long, Integer> refundCount = refundDetails.stream().filter(r -> r.getRefundType().equals(RefundTypeEnum.LACK.getCode()) || r.getRefundType().equals(RefundTypeEnum.LESS.getCode()))
                        .collect(Collectors.groupingBy(RefundProductDetail::getOrderItemId, Collectors.summingInt(RefundProductDetail::getStockoutCount)));
                Map<Long, List<RefundProductDetail>> collect = refundDetails.stream().collect(Collectors.groupingBy(RefundProductDetail::getOrderItemId));
                log.keyword("createStockout").info("创建缺货少货单-缺货：开始匹配 orderItemList.size={}", orderItemList.size());
                for (QueryDistributionItemVo orderItem : orderItemList) {
                    //订单剩余数量 跳过没的退的 目前缺货少货可以锁定,避免重复锁定同个订单项上
                    //剩余数量 = 下单数 - 已缺货少货数 - 已分数量 + 手动创建数量 因为手动创建不会修改已分数量
                    int count = orderItem.getCount() - orderItem.getRefundCount() - receiveMap.getOrDefault(orderItem.getId(), 0) + handStockoutCountMap.getOrDefault(orderItem.getId(), 0);
                    if (count <= 0) {
                        continue;
                    }
                    if (stockoutCount > 0) {
                        QueryDistributionVo queryDistributionVo = orderMap.get(orderItem.getOrderId());
                        CreateStockoutRecordBO stockoutRecord = new CreateStockoutRecordBO();
                        //看该订单项下的城市仓+物流线+二级提货点 有没创建缺货单
                        if (stockoutRecordMap.get(orderItem.getCityWhId().toString() + orderItem.getLogisticsId() + queryDistributionVo.getPlaceIdLevel2()) != null) {
                            stockoutRecord = stockoutRecordMap.get(orderItem.getCityWhId().toString() + orderItem.getLogisticsId() + queryDistributionVo.getPlaceIdLevel2());
                        } else {
                            //创建缺货单
                            createStockoutRecord(stockoutRecord, bo, orderList);
                            stockoutRecord.setStockoutOrderCount(orderList.size());
                            stockoutRecord.setCityWhId(orderItem.getCityWhId());
                            stockoutRecord.setLogisticsId(logisticsVoMap.get(orderItem.getLogisticsId()).getId());
                            stockoutRecord.setLogisticsCode(logisticsVoMap.get(orderItem.getLogisticsId()).getLogisticsCode());
                            stockoutRecord.setLogisticsName(logisticsVoMap.get(orderItem.getLogisticsId()).getLogisticsName());
                            stockoutRecord.setPlaceIdLevel2(queryDistributionVo.getPlaceIdLevel2());
                        }
                        OrderItem item = new OrderItem();
                        StockoutRecordDetail detail = new StockoutRecordDetail();
                        item.setId(orderItem.getId());
                        //存在报损跟差额退顺序混乱的问题,这里先排除掉这两种情况的数量,再分别算出金额以免扣多了
                        BigDecimal difDetail = BigDecimal.ZERO;
                        //差额退 退档服务费
                        BigDecimal difOtherAmount = BigDecimal.ZERO;
                        BigDecimal refundProAmount = BigDecimal.ZERO;
                        BigDecimal loss = BigDecimal.ZERO;
                        BigDecimal totalRefundAmount = BigDecimal.ZERO;
                        BigDecimal refundFinancialServicePrice = BigDecimal.ZERO;
                        //根据订单项查询退款记录
                        List<RefundProductDetail> refundProductDetails = collect.get(orderItem.getId());
                        if (CollectionUtil.isNotEmpty(refundProductDetails)) {
                            for (RefundProductDetail d : refundProductDetails) {
                                if (d.getRefundType().equals(RefundTypeEnum.DIFFERENCE.getCode())) {
                                    difDetail = difDetail.add(d.getRefundProductAmount());
                                    difOtherAmount = difOtherAmount.add(d.getRefundServiceAmount());
                                } else if (d.getRefundType().equals(RefundTypeEnum.REPORT_LOSS.getCode())) {
                                    loss = loss.add(d.getRefundProductAmount());
                                }else {
                                    refundProAmount = refundProAmount.add(d.getRefundProductAmount());
                                }
                                refundFinancialServicePrice = refundFinancialServicePrice.add(d.getRefundFinancialServicePrice());
                            }
                        }
                        //缺货覆盖数量
                        if (count <= stockoutCount) {
                            //没分货没缺货少货卡着 全退
                            if (receiveMap.getOrDefault(orderItem.getId(), 0) == 0 && auditMap.getOrDefault(orderItem.getId(), 0) == 0) {
                                //存在过退款的,把剩下的金额全部退掉
                                if (refundCount.getOrDefault(orderItem.getId(), 0) != 0) {
                                    //少货前都是缺货 直接加
                                    item.setRefundOutCount(count + orderItem.getRefundOutCount());
                                    //计算获取各项已退金额
                                    BigDecimal refundProduct = refundProductDetails.stream().map(RefundProductDetail::getRefundProductAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                                    BigDecimal refundPlatformFreight = refundProductDetails.stream().map(RefundProductDetail::getRefundPlatformFreight).reduce(BigDecimal.ZERO, BigDecimal::add);
                                    BigDecimal refundBaseFreight = refundProductDetails.stream().map(RefundProductDetail::getRefundBaseFreight).reduce(BigDecimal.ZERO, BigDecimal::add);
                                    BigDecimal refundRegionFreight = refundProductDetails.stream().map(RefundProductDetail::getRefundRegionFreight).reduce(BigDecimal.ZERO, BigDecimal::add);
                                    BigDecimal refundServiceAmount = refundProductDetails.stream().map(RefundProductDetail::getRefundServiceAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                                    BigDecimal refundFreightAmount = refundProductDetails.stream().map(RefundProductDetail::getRefundFreightAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                                    BigDecimal refundSubsidyFreeAmount = refundProductDetails.stream().map(RefundProductDetail::getRefundSubsidyFreeAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                                    BigDecimal subsidyFreeAmount = orderItem.getSubsidyFreeAmount().add(orderItem.getSkuSubsidyAmount()).subtract(refundSubsidyFreeAmount);
                                    //退商品金额 = 订单项商品金额-已退商品金额总和-优惠金额
                                    detail.setRefundProductAmount(orderItem.getProductAmount().subtract(refundProduct).subtract(orderItem.getSubsidyFreeAmount()).subtract(orderItem.getProductFreeAmount()));
                                    //退服务费 = 订单项服务费-优惠服务费-已退服务费
                                    stockoutRecord.setServiceAmount(stockoutRecord.getServiceAmount().add(orderItem.getPlatformServiceAmount().subtract(orderItem.getPlatformServiceFreeAmount()).subtract(refundServiceAmount)));

                                    //退非商品金额 (实付金额-实付商品金额)-已退非商品金额总和
                                    detail.setRefundOtherAmount(orderItem.getTotalAmount().subtract(orderItem.getProductAmount()).add(orderItem.getSubsidyFreeAmount())
                                            .subtract(refundPlatformFreight.add(refundBaseFreight).add(refundRegionFreight).add(refundServiceAmount))
                                            .add(orderItem.getFinancialServiceAmount().subtract(refundFinancialServicePrice)));
                                    detail.setRefundSubsidyFreeAmount(orderItem.getSubsidyFreeAmount().add(orderItem.getSkuSubsidyAmount()).subtract(refundSubsidyFreeAmount));
                                    detail.setStockoutCount(count);
                                    stockoutRecord.setFreightAmount(stockoutRecord.getFreightAmount().add(orderItem.getFreightTotalAmount().subtract(orderItem.getFreightTotalFreeAmount()).subtract(refundFreightAmount)));
                                    //优惠商品金额 = 订单项优惠金额-已退优惠金额总和
                                    stockoutRecord.setSubsidyFreeAmount(stockoutRecord.getSubsidyFreeAmount().add(subsidyFreeAmount));
                                    stockoutRecord.setFinancialServicePrice(stockoutRecord.getFinancialServicePrice().add(orderItem.getFinancialServiceAmount().subtract(refundFinancialServicePrice)));
                                } else {
                                    //全部退
                                    item.setRefundOutCount(orderItem.getCount());
                                    //退商品金额 = 订单项商品金额-已退商品金额总和-报损金额-差额退金额-优惠金额
                                    detail.setRefundProductAmount(orderItem.getProductAmount().subtract(loss).subtract(difDetail).subtract(orderItem.getSubsidyFreeAmount()).subtract(orderItem.getProductFreeAmount()));
                                    //退非商品金额 已包括金融服务费 全退
                                    detail.setRefundOtherAmount(orderItem.getTotalAmount().subtract(orderItem.getProductAmount()).add(orderItem.getSubsidyFreeAmount()).add(orderItem.getProductFreeAmount())
                                            .subtract(difOtherAmount));
                                    detail.setRefundSubsidyFreeAmount(orderItem.getSubsidyFreeAmount().add(orderItem.getSkuSubsidyAmount()));
                                    detail.setStockoutCount(count);
                                    stockoutRecord.setServiceAmount(stockoutRecord.getServiceAmount().add(orderItem.getPlatformServiceAmount().subtract(orderItem.getPlatformServiceFreeAmount())).subtract(difOtherAmount));
                                    stockoutRecord.setFreightAmount(stockoutRecord.getFreightAmount().add(orderItem.getFreightTotalAmount().subtract(orderItem.getFreightTotalFreeAmount())));
                                    stockoutRecord.setSubsidyFreeAmount(stockoutRecord.getSubsidyFreeAmount().add(orderItem.getSubsidyFreeAmount()));
                                    stockoutRecord.setFinancialServicePrice(stockoutRecord.getFinancialServicePrice().add(orderItem.getFinancialServiceAmount()));
                                }
                                //存在分货的,对应的单据要做扣减
                                if (receiveMap.containsKey(orderItem.getId())) {
                                    recoverCount.put(orderItem.getId(), count);
                                }
                                totalRefundAmount = totalRefundAmount.add(detail.getRefundProductAmount()).add(detail.getRefundOtherAmount());
                            } else {
                                //部分退
                                totalRefundAmount = refundPart(recoverCount, receiveMap, count, orderItem, stockoutRecord, item, detail, refundFinancialServicePrice,
                                        difDetail, loss, totalRefundAmount, refundProAmount, refundProductDetails);
                            }
                        }
                        //部分退
                        else {
                            totalRefundAmount = refundPart(recoverCount, receiveMap, stockoutCount, orderItem, stockoutRecord, item, detail, refundFinancialServicePrice,
                                    difDetail, loss, totalRefundAmount, refundProAmount, refundProductDetails);
                        }
                        //扣减数量
                        bo.setStockoutCount(stockoutCount - count);
                        //处理缺货单详情
                        detail.setStockoutRecordId(stockoutRecord.getId());
                        detail.setOrderId(orderItem.getOrderId());
                        detail.setOrderItemId(orderItem.getId());
                        detail.setSupplierSkuId(orderItem.getSupplierSkuId());
                        detail.setOrderCode(orderCodeMap.get(orderItem.getOrderId()));
                        detail.setCustomerId(orderItem.getCustomerId());
                        detail.setCustomerName(orderItem.getCustomerName());
                        detail.setSpuName(orderItem.getSpuName());
                        //todo 代采币 补偿代采币暂时没有方案,先占位
                        detail.setRefundCoin(BigDecimal.ZERO);
                        detail.setOrderCount(orderItem.getCount());
                        detail.setType(bo.getType());
                        detail.setStatus(StockOutStatusEnum.STATUS_CONFIRM.getCode());
                        detail.setPayTime(orderItem.getPayTime());
                        stockoutRecordDetails.add(detail);
                        stockoutRecord.setProductAmount(stockoutRecord.getProductAmount().add(detail.getRefundProductAmount()));
                        stockoutRecord.setOtherAmount(stockoutRecord.getOtherAmount().add(detail.getRefundOtherAmount()));
                        stockoutRecordMap.put(stockoutRecord.getCityWhId().toString() + orderItem.getLogisticsId() + stockoutRecord.getPlaceIdLevel2(), stockoutRecord);
                        stockoutCount -= count;
                        updateItemList.add(item);
                        if (refundAmountMap.containsKey(orderItem.getOrderId())) {
                            refundAmountMap.put(orderItem.getOrderId(), refundAmountMap.get(orderItem.getOrderId()).add(totalRefundAmount));
                        } else {
                            refundAmountMap.put(orderItem.getOrderId(), totalRefundAmount);
                        }
                    }
                }
            }
            List<StockoutRecord> stockoutRecords = new ArrayList<>();
            Map<Long, List<StockoutRecordDetail>> collect = stockoutRecordDetails.stream().collect(Collectors.groupingBy(StockoutRecordDetail::getStockoutRecordId));
            //获取供应商信息
            List<Long> supplierIds = stockoutRecordMap.values().stream().map(CreateStockoutRecordBO::getSupplierId).distinct().toList();
            List<RemoteSupplierVo> supplierByIds = remoteSupplierService.getSupplierByIds(supplierIds);
            Map<Long, String> supMap = supplierByIds.stream().collect(Collectors.toMap(RemoteSupplierVo::getId, RemoteSupplierVo::getName));
            stockoutRecordMap.values().forEach(l -> {
                //更新创建说明
                l.setCreateRemark(createStockOutDesc(bo, l, supMap));
                //获取真实缺货少货数量
                int sum = collect.get(l.getId()).stream().mapToInt(StockoutRecordDetail::getStockoutCount).sum();
                l.setStockoutCount(sum);
                l.setStockoutOrderCount(collect.get(l.getId()).size());
                StockoutRecord record = new StockoutRecord();
                BeanUtil.copyProperties(l, record);
                stockoutRecords.add(record);
            });
            log.keyword("createStockout").info("创建缺货少货单-缺货：组装缺货单list stockoutRecords.size={}", stockoutRecords.size());
            //新增缺货单商品详情
            if (CollectionUtil.isNotEmpty(stockoutRecordDetails)) {
                stockoutRecordDetailMapper.insertBatch(stockoutRecordDetails);
            }
            baseMapper.updateBatchById(stockoutRecords);
            //更新分货单详情
            if (CollectionUtil.isNotEmpty(recoverCount)) {
                LambdaQueryWrapper<SortGoodsDetail> sor = new LambdaQueryWrapper<>();
                sor.in(SortGoodsDetail::getOrderItemId, recoverCount.keySet()).eq(SortGoodsDetail::getDelFlag, 0);
                //查询分货单详情
                List<SortGoodsDetail> sortGoodsDetails = sortGoodsDetailMapper.selectList(sor);
                //查询分货单
                List<Long> sortGoodsIdList = sortGoodsDetails.stream().map(SortGoodsDetail::getSortGoodsId).toList();
                List<SortGoods> sortGoods = sortGoodsMapper.selectBatchIds(sortGoodsIdList);
                Map<Long, SortGoods> sortGoodsMap = sortGoods.stream().collect(Collectors.toMap(SortGoods::getId, Function.identity()));
                Map<Long, SortGoodsDetail> sortGoodsDetailMap = sortGoodsDetails.stream().collect(Collectors.toMap(SortGoodsDetail::getOrderItemId, Function.identity()));
                recoverCount.forEach((k, v) -> {
                    if (sortGoodsDetailMap.containsKey(k)) {
                        SortGoodsDetail detail = sortGoodsDetailMap.get(k);
                        //修改数量
                        detail.setAllReceived(detail.getAllReceived() - v);
                        detail.setUnreceived(detail.getUnreceived() - v);
                        if(detail.getAllReceived() <= 0){
                            detail.setStatus(detail.getStatus() > SortGoodsStatusEnum.SORTYET.getCode() ? SortGoodsStatusEnum.FINISH.getCode() : SortGoodsStatusEnum.SORTYET.getCode());
                        }
                        SortGoods goods = sortGoodsMap.get(detail.getSortGoodsId());
                        goods.setAllReceived(goods.getAllReceived() - v);
                        goods.setUnreceived(goods.getAllReceived() - goods.getReceived());
                    }
                });
                log.keyword("createStockout").info("创建缺货少货单-缺货：更新分货单详情 sortGoodsMap.size={}", sortGoodsMap.size());
                sortGoodsDetailMapper.updateBatchById(sortGoodsDetailMap.values());
                sortGoodsMapper.updateBatchById(sortGoodsMap.values());
            }
            //更新订单项状态
            log.keyword("createStockout").info("创建缺货少货单-缺货：更新订单项状态 updateItemList.size={}", updateItemList.size());
            if (CollectionUtil.isNotEmpty(updateItemList)) {
                orderItemMapper.updateBatchById(updateItemList);
            }
        }
    }


    @NotNull
    private  BigDecimal refundPart(Map<Long, Integer> recoverCount, Map<Long, Integer> receiveMap, Integer stockoutCount,
                                         QueryDistributionItemVo orderItem, CreateStockoutRecordBO stockoutRecord,
                                         OrderItem item, StockoutRecordDetail detail, BigDecimal refundServiceFee, BigDecimal difDetail,
                                   BigDecimal loss, BigDecimal totalRefundAmount, BigDecimal refundProAmount,List<RefundProductDetail> refundProductDetails) {
        //计算获取各项已退金额
        BigDecimal refundServiceAmount = BigDecimal.ZERO;
        if (CollectionUtil.isNotEmpty(refundProductDetails)){
            for (RefundProductDetail productDetail : refundProductDetails) {
                if (productDetail.getRefundType().equals(RefundTypeEnum.DIFFERENCE.getCode())) {
                    refundServiceAmount = refundServiceAmount.add(productDetail.getRefundServiceAmount());
                }
            }
        }
        item.setRefundOutCount(orderItem.getRefundOutCount() + stockoutCount);
        detail.setStockoutCount(stockoutCount);
        //等比例退非商品金额 各项非商品金额 * 缺货数量/订单项数量 之和
//        BigDecimal rat = new BigDecimal(stockoutCount).divide(new BigDecimal(orderItem.getCount()), 2, RoundingMode.HALF_UP);
        //获取剩余商品金额  退不能超过剩余金额
        BigDecimal subtract = orderItem.getProductAmount().subtract(loss).subtract(difDetail).subtract(orderItem.getProductFreeAmount()).subtract(refundProAmount);
        BigDecimal refundSubsidyFreeAmount = (orderItem.getSubsidyFreeAmount().add(orderItem.getProductFreeAmount())).multiply(new BigDecimal(stockoutCount)).divide(new BigDecimal(orderItem.getCount()), 2, RoundingMode.HALF_UP);
        BigDecimal refundSkuSubsidyFreeAmount = (orderItem.getSubsidyFreeAmount().add(orderItem.getSkuSubsidyAmount())).multiply(new BigDecimal(stockoutCount)).divide(new BigDecimal(orderItem.getCount()), 2, RoundingMode.HALF_UP);
        BigDecimal difPath = difDetail.multiply(BigDecimal.valueOf(stockoutCount)).divide(BigDecimal.valueOf(orderItem.getCount()));
        detail.setRefundProductAmount(orderItem.getPrice().multiply(new BigDecimal(stockoutCount)).subtract(refundSubsidyFreeAmount).subtract(difPath).compareTo(subtract) > 0
                ? subtract : orderItem.getPrice().multiply(new BigDecimal(stockoutCount)).subtract(refundSubsidyFreeAmount).subtract(difPath));
        BigDecimal platformServiceAmount = (orderItem.getPlatformServiceAmount().subtract(orderItem.getPlatformServiceFreeAmount()).subtract(refundServiceAmount)).multiply(new BigDecimal(stockoutCount)).divide(new BigDecimal(orderItem.getCount()), 2, RoundingMode.HALF_UP);
        BigDecimal platformFreightAmount = (orderItem.getPlatformFreightAmount().subtract(orderItem.getPlatformFreightFreeAmount())).multiply(new BigDecimal(stockoutCount)).divide(new BigDecimal(orderItem.getCount()), 2, RoundingMode.HALF_UP);
        BigDecimal baseFreightAmount = (orderItem.getBaseFreightAmount().subtract(orderItem.getBaseFreightFreeAmount())).multiply(new BigDecimal(stockoutCount)).divide(new BigDecimal(orderItem.getCount()), 2, RoundingMode.HALF_UP);
        BigDecimal platformFreightAmountLevel2 = (orderItem.getPlatformFreightAmountLevel2().subtract(orderItem.getPlatformFreightFreeAmountLevel2())).multiply(new BigDecimal(stockoutCount)).divide(new BigDecimal(orderItem.getCount()), 2, RoundingMode.HALF_UP);
        BigDecimal regionFreightAmount = (orderItem.getRegionFreightAmount().subtract(orderItem.getRegionFreightFreeAmount())).multiply(new BigDecimal(stockoutCount)).divide(new BigDecimal(orderItem.getCount()), 2, RoundingMode.HALF_UP);
        detail.setRefundSubsidyFreeAmount(refundSkuSubsidyFreeAmount);
        detail.setRefundOtherAmount(platformServiceAmount.add(platformFreightAmount).add(baseFreightAmount)
                .add(regionFreightAmount).add(platformFreightAmountLevel2));
        stockoutRecord.setSubsidyFreeAmount(stockoutRecord.getSubsidyFreeAmount().add(refundSkuSubsidyFreeAmount));
        stockoutRecord.setServiceAmount(stockoutRecord.getServiceAmount().add(platformServiceAmount));

        stockoutRecord.setFreightAmount(stockoutRecord.getFreightAmount().add(platformFreightAmount.add(baseFreightAmount)
                .add(regionFreightAmount).add(platformFreightAmountLevel2)));
        totalRefundAmount = totalRefundAmount.add(detail.getRefundProductAmount()).add(detail.getRefundOtherAmount());
        //退金融服务费
        BigDecimal apportionTotalAmount = orderItem.getTotalAmount().subtract(orderItem.getFinancialServiceAmount());
        //退款金额
        BigDecimal detailRefundAmount = detail.getRefundProductAmount().add(detail.getRefundOtherAmount());

        BigDecimal refundFinancialPrice = refundRecordService.getApportionRefundService(apportionTotalAmount,orderItem.getFinancialServiceAmount(),detailRefundAmount,refundServiceFee);

        stockoutRecord.setFinancialServicePrice(stockoutRecord.getFinancialServicePrice().add(refundFinancialPrice));
        detail.setRefundOtherAmount(detail.getRefundOtherAmount().add(refundFinancialPrice));
        //存在分货,将要分数量扣减
        if (receiveMap.containsKey(orderItem.getId())) {
            recoverCount.put(orderItem.getId(), stockoutCount);
        }
        return totalRefundAmount;
    }

    /**
     * 批量产生少货单
     * 少货是明确知道少谁的 少多少,直接根据销售批次商品创建即可
     * 少货从城市仓发起的  按批次商品创建少货单都是一个城市仓,只需考虑物流线不同  维度:批次商品+物流线
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createLessGoods(List<BatchCreateStockoutBO> boList, List<QueryDistributionVo> orderList) {
        if (CollectionUtil.isNotEmpty(orderList)) {
            Map<Long, QueryDistributionVo> orderMap = orderList.stream().collect(Collectors.toMap(QueryDistributionVo::getId, Function.identity()));
            List<QueryDistributionItemVo> list = orderList.stream().map(QueryDistributionVo::getOrderItemList).flatMap(List::stream).toList();
            Map<Long, List<QueryDistributionItemVo>> map = list.stream().collect(Collectors.groupingBy(QueryDistributionItemVo::getSupplierSkuId));
            Map<Long, List<BatchCreateStockoutBO>> boMap = boList.stream().collect(Collectors.groupingBy(BatchCreateStockoutBO::getSupplierSkuId));
            //获取采购员信息
            List<String> buyerCodes = list.stream().map(QueryDistributionItemVo::getBuyerCode).distinct().toList();
            Map<String, RemoteUserBo> buyerMap = remoteUserService.getUsersByUserCodes(buyerCodes);
            //获取物流信息
            RemoteRegionLogisticsQueryBo logisticsQueryBo = new RemoteRegionLogisticsQueryBo();
            List<Long> logisticsIds = orderList.stream().map(QueryDistributionVo::getOrderItemList).flatMap(List::stream).map(QueryDistributionItemVo::getLogisticsId).distinct().toList();
            logisticsQueryBo.setLogisticsIds(logisticsIds);
            List<RemoteRegionLogisticsVo> logisticsVos = remoteRegionLogisticsService.queryList(logisticsQueryBo);
            Map<Long, RemoteRegionLogisticsVo> logisticsVoMap = logisticsVos.stream().collect(Collectors.toMap(RemoteRegionLogisticsVo::getId, Function.identity()));
            //获取订单退款信息
            List<Long> orderId = orderList.stream().map(QueryDistributionVo::getId).distinct().toList();
            LambdaQueryWrapper<RefundRecord> oqw = new LambdaQueryWrapper<>();
            oqw.in(RefundRecord::getOrderId, orderId).eq(RefundRecord::getDelFlag, 0);
            List<RefundRecord> refundRecords = refundRecordMapper.selectList(oqw);
            //退款金额
            Map<Long, BigDecimal> refundAmountMap = new HashMap<>();
            //退金融服务费金额
            Map<Long, BigDecimal> refundServiceFeeMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(refundRecords)) {
                refundRecords.stream().collect(Collectors.groupingBy(RefundRecord::getOrderId)).forEach((k, v) -> {
                    refundServiceFeeMap.put(k, v.stream().map(RefundRecord::getRefundFinancialServiceAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                    refundAmountMap.put(k, v.stream().map(RefundRecord::getRefundAmount).reduce(BigDecimal.ZERO, BigDecimal::add).subtract(refundServiceFeeMap.get(k)));
                });
            }
            //订单项状态处于非待送货状态,可缺货数量要扣减分货单分货数量
            Map<Long, Integer> recoverCount = new HashMap<>();
            //订单项状态处于非待送货状态,可缺货数量要扣减分货单分货数量
            Map<Long, Integer> receiveMap = new HashMap<>();
            Map<Long, Integer> auditMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(list)) {
                List<Long> itemIds = list.stream().map(QueryDistributionItemVo::getId).toList();
                LambdaQueryWrapper<SortGoodsDetail> sor = new LambdaQueryWrapper<>();
                sor.in(SortGoodsDetail::getOrderItemId, itemIds).eq(SortGoodsDetail::getDelFlag, 0);
                //查询分货单详情
                List<SortGoodsDetail> sortGoodsDetails = sortGoodsDetailMapper.selectList(sor);
                receiveMap = sortGoodsDetails.stream().collect(Collectors.groupingBy(SortGoodsDetail::getOrderItemId, Collectors.summingInt(SortGoodsDetail::getReceived)));
                List<StockoutRecordDetail> auditStatusList = stockoutRecordDetailMapper.getAuditStatusList(itemIds, Collections.singletonList(StockoutAuditStatusEnum.STATE_CONFIRM.getCode()));
                auditMap = auditStatusList.stream().collect(Collectors.groupingBy(StockoutRecordDetail::getOrderItemId, Collectors.summingInt(StockoutRecordDetail::getStockoutCount)));
            }
            //订单id-code map
            Map<Long, String> orderCodeMap = orderList.stream().collect(Collectors.toMap(QueryDistributionVo::getId, QueryDistributionVo::getCode));
            Map<String, CreateStockoutRecordBO> stockoutRecordMap = new HashMap<>();
            List<StockoutRecordDetail> stockoutRecordDetails = new ArrayList<>();
            List<OrderItem> updateItemList = new ArrayList<>();
            for (Long l : map.keySet()) {
                List<QueryDistributionItemVo> orderItemList = map.get(l);
                List<BatchCreateStockoutBO> bo = boMap.get(l);
                Map<Long, BatchCreateStockoutBO> stockoutMap = bo.stream().collect(Collectors.toMap(BatchCreateStockoutBO::getOrderItemId, Function.identity()));
                List<Long> ids = orderItemList.stream().map(QueryDistributionItemVo::getId).toList();
                List<RefundProductDetail> refundDetails = refundRecordService.getRefundByItemId(ids);
                Map<Long, List<RefundProductDetail>> collect = refundDetails.stream().collect(Collectors.groupingBy(RefundProductDetail::getOrderItemId));
                Map<Long, Integer> refundCount = refundDetails.stream().filter(r -> r.getRefundType().equals(RefundTypeEnum.LACK.getCode()) || r.getRefundType().equals(RefundTypeEnum.LESS.getCode()))
                        .collect(Collectors.groupingBy(RefundProductDetail::getOrderItemId, Collectors.summingInt(RefundProductDetail::getStockoutCount)));
                for (QueryDistributionItemVo orderItem : orderItemList) {
                    log.keyword("createLessGoods").info("计算金融服务费,订单项信息{}",orderItem);
                    QueryDistributionVo queryDistributionVo = orderMap.get(orderItem.getOrderId());
                    BatchCreateStockoutBO stockoutBO = stockoutMap.get(orderItem.getId());
                    CreateStockoutRecordBO stockoutRecord = new CreateStockoutRecordBO();
                    //看该订单项下的物流线有没创建少货单
                    if (stockoutRecordMap.get(orderItem.getSupplierSkuId().toString() + orderItem.getLogisticsId() + queryDistributionVo.getPlaceIdLevel2()) != null) {
                        stockoutRecord = stockoutRecordMap.get(orderItem.getSupplierSkuId().toString() + orderItem.getLogisticsId() + queryDistributionVo.getPlaceIdLevel2());
                    } else {
                        //创建少货单
                        stockoutRecord.setPlaceIdLevel2(queryDistributionVo.getPlaceIdLevel2());
                        createLessStockoutRecord(orderList, buyerMap, logisticsVoMap, l, orderItem, stockoutBO, stockoutRecord);
                    }
                    StockoutRecordDetail detail = new StockoutRecordDetail();
                    OrderItem item = new OrderItem();
                    item.setId(orderItem.getId());
                    //订单剩余数量
                    int refCount = refundCount.getOrDefault(orderItem.getId(), 0);
                    int count = orderItem.getCount() - orderItem.getRefundCount();
                    //存在报损跟差额退顺序混乱的问题,这里先排除掉这两种情况的数量,再分别算出金额以免扣多了
                    BigDecimal difDetail = BigDecimal.ZERO;
                    //差额退 退档服务费
                    BigDecimal difOtherAmount = BigDecimal.ZERO;
                    BigDecimal loss = BigDecimal.ZERO;
                    BigDecimal totalRefundAmount = BigDecimal.ZERO;
                    BigDecimal refundFinancialServicePrice = BigDecimal.ZERO;
                    BigDecimal refundProAmount = BigDecimal.ZERO;
                    //订单没结算，不用处理佣金问题
                    BigDecimal distributionAmount = orderItem.getSettleCount() >= 1 ? orderItem.getDistributionAmount().add(orderItem.getDistributionTaxAmount()) : BigDecimal.ZERO;
                    //根据订单项查询退款记录
                    List<RefundProductDetail> refundProductDetails = collect.get(orderItem.getId());
                    log.keyword("createLessGoods").info("退款单：{}",refundProductDetails);
                    if (CollectionUtil.isNotEmpty(refundProductDetails)) {
                        for (RefundProductDetail productDetail : refundProductDetails) {
                            if (productDetail.getRefundType().equals(RefundTypeEnum.DIFFERENCE.getCode())) {
                                difDetail = difDetail.add(productDetail.getRefundProductAmount());
                                difOtherAmount = difOtherAmount.add(productDetail.getRefundServiceAmount());
                            } else if (productDetail.getRefundType().equals(RefundTypeEnum.REPORT_LOSS.getCode())) {
                                loss = loss.add(productDetail.getRefundProductAmount());
                            }else{
                                refundProAmount = refundProAmount.add(productDetail.getRefundProductAmount());
                            }
                            if ( orderItem.getSettleCount() >= 1) {
                                distributionAmount = distributionAmount.subtract(productDetail.getRefundDistributionAmount());
                            }
                            refundFinancialServicePrice = refundFinancialServicePrice.add(productDetail.getRefundFinancialServicePrice());
                        }
                    }
                    if(count == 0){
                        log.keyword("createLessGoods"+orderItem.getId()).error("该订单项无法产生少货单");
                        continue;
                    }
                    //少货覆盖剩余数量,且没分货和缺货少货 全退
                    if (count == stockoutBO.getStockoutCount()) {
                        if ((stockoutBO.getCreateScene().equals(StockOutCreateSceneEnum.CITY_CREATE.getCode()) || receiveMap.getOrDefault(orderItem.getId(), 0) == 0)
                                && auditMap.getOrDefault(orderItem.getId(), 0) == 0) {
                            //存在过退款的,把剩下的金额全部退掉
                            if (refCount != 0) {
                                item.setRefundFewCount(count);
                                //计算获取各项已退金额
                                BigDecimal refundProduct = refundProductDetails.stream().map(RefundProductDetail::getRefundProductAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                                BigDecimal refundPlatformFreight = refundProductDetails.stream().map(RefundProductDetail::getRefundPlatformFreight).reduce(BigDecimal.ZERO, BigDecimal::add);
                                BigDecimal refundBaseFreight = refundProductDetails.stream().map(RefundProductDetail::getRefundBaseFreight).reduce(BigDecimal.ZERO, BigDecimal::add);
                                BigDecimal refundRegionFreight = refundProductDetails.stream().map(RefundProductDetail::getRefundRegionFreight).reduce(BigDecimal.ZERO, BigDecimal::add);
                                BigDecimal refundServiceAmount = refundProductDetails.stream().map(RefundProductDetail::getRefundServiceAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                                BigDecimal refundFreightAmount = refundProductDetails.stream().map(RefundProductDetail::getRefundFreightAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                                BigDecimal refundSubsidyFreeAmount = refundProductDetails.stream().map(RefundProductDetail::getRefundSubsidyFreeAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                                BigDecimal subsidyFreeAmount = orderItem.getSubsidyFreeAmount().add(orderItem.getSkuSubsidyAmount()).subtract(refundSubsidyFreeAmount);
                                detail.setRefundProductAmount(orderItem.getProductAmount().subtract(refundProduct).subtract(orderItem.getSubsidyFreeAmount()).subtract(orderItem.getProductFreeAmount()));
                                detail.setRefundSubsidyFreeAmount(subsidyFreeAmount);
                                //退服务费 = 订单项服务费-优惠服务费-已退服务费
                                stockoutRecord.setServiceAmount(stockoutRecord.getServiceAmount().add(orderItem.getPlatformServiceAmount().subtract(orderItem.getPlatformServiceFreeAmount()).subtract(refundServiceAmount)));
                                //退非商品金额 非商品金额-已退非商品金额总和
                                detail.setRefundOtherAmount(orderItem.getTotalAmount().subtract(orderItem.getProductAmount()).add(orderItem.getSubsidyFreeAmount())
                                        .add(orderItem.getProductFreeAmount()).subtract(refundPlatformFreight.add(refundBaseFreight).add(refundRegionFreight).add(refundServiceAmount))
                                        .add(orderItem.getFinancialServiceAmount().subtract(refundFinancialServicePrice)));
                                stockoutRecord.setFreightAmount(stockoutRecord.getFreightAmount().add(orderItem.getFreightTotalAmount().subtract(orderItem.getFreightTotalFreeAmount()).subtract(refundFreightAmount)));
                                stockoutRecord.setBaseFreight(stockoutRecord.getBaseFreight().add(orderItem.getBaseFreightAmount().subtract(orderItem.getBaseFreightFreeAmount()).subtract(refundBaseFreight)));
                                stockoutRecord.setPlatformFreight(stockoutRecord.getPlatformFreight().add(orderItem.getPlatformFreightAmount().subtract(orderItem.getPlatformFreightFreeAmount()).subtract(refundPlatformFreight)));
                                stockoutRecord.setRegionFreight(stockoutRecord.getRegionFreight().add(orderItem.getRegionFreightAmount().subtract(orderItem.getRegionFreightFreeAmount()).subtract(refundRegionFreight)));
                                stockoutRecord.setSubsidyFreeAmount(stockoutRecord.getSubsidyFreeAmount().add(subsidyFreeAmount));
                                stockoutRecord.setFinancialServicePrice(stockoutRecord.getFinancialServicePrice().add(orderItem.getFinancialServiceAmount().subtract(refundFinancialServicePrice)));
                            } else {
                                //全部退
                                item.setRefundFewCount(orderItem.getCount());
                                //结算价会主动差额退款,除取消订单都取结算价
                                detail.setRefundProductAmount(orderItem.getProductAmount().subtract(loss).subtract(difDetail).subtract(orderItem.getSubsidyFreeAmount()).subtract(orderItem.getProductFreeAmount()));
                                log.keyword("createLessGoods").info("退服务费计算后：{}",difOtherAmount);
                                //退非商品金额 已包括金融服务费 全退
                                detail.setRefundOtherAmount(orderItem.getTotalAmount().subtract(orderItem.getProductAmount()).add(orderItem.getSubsidyFreeAmount()).add(orderItem.getProductFreeAmount())
                                        .subtract(difOtherAmount));
                                //退优惠金额
                                detail.setRefundSubsidyFreeAmount(orderItem.getSubsidyFreeAmount().add(orderItem.getSkuSubsidyAmount()));
                                stockoutRecord.setServiceAmount(stockoutRecord.getServiceAmount().add(orderItem.getPlatformServiceAmount()).subtract(orderItem.getPlatformServiceFreeAmount()).subtract(difOtherAmount));
                                stockoutRecord.setFreightAmount(stockoutRecord.getFreightAmount().add(orderItem.getFreightTotalAmount()).subtract(orderItem.getFreightTotalFreeAmount()));
                                stockoutRecord.setBaseFreight(stockoutRecord.getBaseFreight().add(orderItem.getBaseFreightAmount()).subtract(orderItem.getBaseFreightFreeAmount()));
                                stockoutRecord.setPlatformFreight(stockoutRecord.getPlatformFreight().add(orderItem.getPlatformFreightAmount()).subtract(orderItem.getPlatformFreightFreeAmount()));
                                stockoutRecord.setRegionFreight(stockoutRecord.getRegionFreight().add(orderItem.getRegionFreightAmount()).subtract(orderItem.getRegionFreightFreeAmount()));
                                stockoutRecord.setSubsidyFreeAmount(stockoutRecord.getSubsidyFreeAmount().add(orderItem.getSubsidyFreeAmount()));
                                stockoutRecord.setFinancialServicePrice(stockoutRecord.getFinancialServicePrice().add(orderItem.getFinancialServiceAmount()));
                            }
                            totalRefundAmount = totalRefundAmount.add(detail.getRefundProductAmount()).add(detail.getRefundOtherAmount());
                            //分销金额>0说明已结算，这里要将分销金额扣减出来
                            if (distributionAmount.compareTo(BigDecimal.ZERO) > 0){
                                detail.setRefundProductAmount(detail.getRefundProductAmount().subtract(distributionAmount));
                            }
                            recoverCount.put(orderItem.getId(), count);
                        } else {
                            totalRefundAmount = refundPart(recoverCount, orderItem, stockoutBO, stockoutRecord, detail,
                                    item, refundFinancialServicePrice, difDetail, loss, totalRefundAmount,distributionAmount,refundProAmount,refundProductDetails);
                        }
                    }
                    //部分退
                    else {
                        log.keyword("createLessGoods").info("计算金融服务费,订单项信息{}",orderItem);
                        totalRefundAmount = refundPart(recoverCount, orderItem, stockoutBO, stockoutRecord, detail,
                                item, refundFinancialServicePrice, difDetail, loss, totalRefundAmount,distributionAmount,refundProAmount,refundProductDetails);
                    }
                    detail.setStockoutCount(stockoutBO.getStockoutCount());
                    detail.setStockoutRecordId(stockoutRecord.getId());
                    detail.setOrderId(orderItem.getOrderId());
                    detail.setOrderItemId(orderItem.getId());
                    detail.setSupplierSkuId(orderItem.getSupplierSkuId());
                    detail.setOrderCode(orderCodeMap.get(orderItem.getOrderId()));
                    detail.setCustomerId(orderItem.getCustomerId());
                    detail.setCustomerName(orderItem.getCustomerName());
                    detail.setSpuName(orderItem.getSpuName());
                    detail.setPayTime(orderItem.getPayTime());
                    //todo 代采币 补偿代采币暂时没有方案,先占位
                    detail.setRefundCoin(BigDecimal.ZERO);
                    detail.setOrderCount(orderItem.getCount());
                    detail.setType(bo.get(0).getType());
                    detail.setStatus(StockOutStatusEnum.STATUS_CONFIRM.getCode());
                    stockoutRecord.setProductAmount(stockoutRecord.getProductAmount().add(detail.getRefundProductAmount()));
                    stockoutRecord.setOtherAmount(stockoutRecord.getOtherAmount().add(detail.getRefundOtherAmount()));
                    detail.setId(null);
                    stockoutRecordDetails.add(detail);
                    stockoutRecordMap.put(orderItem.getSupplierSkuId().toString() + orderItem.getLogisticsId()+ queryDistributionVo.getPlaceIdLevel2(), stockoutRecord);
                    updateItemList.add(item);
                    //同个订单一起走少货,退款金额还是按之前算,导致金融手续费剩一分钱,这里将先同的给加上
                    if (refundAmountMap.containsKey(orderItem.getOrderId())) {
                        refundAmountMap.put(orderItem.getOrderId(), refundAmountMap.get(orderItem.getOrderId()).add(totalRefundAmount));
                    } else {
                        refundAmountMap.put(orderItem.getOrderId(), totalRefundAmount);
                    }
                }
            }
            //新增缺货单商品详情
            if (CollectionUtil.isNotEmpty(stockoutRecordDetails)) {
                stockoutRecordDetailMapper.insertBatch(stockoutRecordDetails);
            }
            List<StockoutRecord> stockoutRecords = new ArrayList<>();
            Map<Long, List<StockoutRecordDetail>> collect = stockoutRecordDetails.stream().collect(Collectors.groupingBy(StockoutRecordDetail::getStockoutRecordId));
            //获取供应商信息
            List<Long> supplierIds = stockoutRecordMap.values().stream().map(CreateStockoutRecordBO::getSupplierId).distinct().toList();
            List<RemoteSupplierVo> supplierByIds = remoteSupplierService.getSupplierByIds(supplierIds);
            Map<Long, String> supMap = supplierByIds.stream().collect(Collectors.toMap(RemoteSupplierVo::getId, RemoteSupplierVo::getName));
            stockoutRecordMap.values().forEach(l -> {
                //更新创建说明
                l.setCreateRemark(createStockOutDesc(boMap.get(l.getSupplierSkuId()).get(0), l, supMap));
                //获取真实缺货少货数量
                int sum = collect.get(l.getId()).stream().mapToInt(StockoutRecordDetail::getStockoutCount).sum();
                l.setStockoutCount(sum);
                l.setStockoutOrderCount(collect.get(l.getId()).size());
                StockoutRecord record = new StockoutRecord();
                BeanUtil.copyProperties(l, record);
                stockoutRecords.add(record);
            });
            baseMapper.updateBatchById(stockoutRecords);
            //更新分货单详情 提货的时候不会退分货数量
            if (CollectionUtil.isNotEmpty(recoverCount) && !boList.get(0).getCreateScene().equals(StockOutCreateSceneEnum.RECEIVE_GOODS.getCode())
                    && !boList.get(0).getCreateScene().equals(StockOutCreateSceneEnum.CITY_CREATE.getCode())) {
                log.keyword("城市仓手动新增少货单", "createLessGoods").info("城市仓手动新增少货单走到这个判断下面了， bo:{}", boList);
                LambdaQueryWrapper<SortGoodsDetail> sor = new LambdaQueryWrapper<>();
                sor.in(SortGoodsDetail::getOrderItemId, recoverCount.keySet()).eq(SortGoodsDetail::getDelFlag, 0);
                //查询分货单详情
                List<SortGoodsDetail> sortGoodsDetails = sortGoodsDetailMapper.selectList(sor);
                Map<Long, SortGoodsDetail> sortGoodsDetailMap = sortGoodsDetails.stream().collect(Collectors.toMap(SortGoodsDetail::getOrderItemId, Function.identity()));
                recoverCount.forEach((k, v) -> {
                    if (sortGoodsDetailMap.containsKey(k)) {
                        SortGoodsDetail detail = sortGoodsDetailMap.get(k);
                        //修改数量
                        detail.setAllReceived(detail.getAllReceived() - v);
                        detail.setUnreceived(detail.getUnreceived() - v);
                    }
                });
                sortGoodsDetailMapper.updateBatchById(sortGoodsDetailMap.values());
            }
            //更新订单项状态
            if (CollectionUtil.isNotEmpty(updateItemList)) {
                orderItemMapper.updateBatchById(updateItemList);
            }
        }
    }

    @NotNull
    private BigDecimal refundPart(Map<Long, Integer> recoverCount, QueryDistributionItemVo orderItem,
                                         BatchCreateStockoutBO stockoutBO, CreateStockoutRecordBO stockoutRecord, StockoutRecordDetail detail, OrderItem item,
                                         BigDecimal refundServiceFee, BigDecimal difDetail, BigDecimal loss, BigDecimal totalRefundAmount,
                                  BigDecimal distributionAmount, BigDecimal refundProAmount,List<RefundProductDetail> refundProductDetails) {
        //计算获取各项已退金额
        BigDecimal refundServiceAmount = BigDecimal.ZERO;
        if (CollectionUtil.isNotEmpty(refundProductDetails)){
            for (RefundProductDetail productDetail : refundProductDetails) {
                if (productDetail.getRefundType().equals(RefundTypeEnum.DIFFERENCE.getCode())) {
                    refundServiceAmount = refundServiceAmount.add(productDetail.getRefundServiceAmount());
                }
            }
        }
        item.setRefundFewCount(orderItem.getRefundFewCount() + stockoutBO.getStockoutCount());
        //等比例退非商品金额 各项非商品金额*少货数量/订单项数量 之和
//        BigDecimal rat = new BigDecimal(stockoutBO.getStockoutCount()).divide(new BigDecimal(orderItem.getCount()), 2, RoundingMode.HALF_UP);
        //退优惠金额
        BigDecimal refundSubsidyFreeAmount = (orderItem.getSubsidyFreeAmount().add(orderItem.getProductFreeAmount())).multiply(new BigDecimal(stockoutBO.getStockoutCount())).divide(new BigDecimal(orderItem.getCount()), 2, RoundingMode.HALF_UP);
        BigDecimal refundSkuSubsidyFreeAmount = (orderItem.getSubsidyFreeAmount().add(orderItem.getSkuSubsidyAmount())).multiply(new BigDecimal(stockoutBO.getStockoutCount())).divide(new BigDecimal(orderItem.getCount()), 2, RoundingMode.HALF_UP);
        //获取剩余商品金额  退不能超过剩余金额
        BigDecimal subtract = orderItem.getProductAmount().subtract(loss).subtract(difDetail).subtract(orderItem.getProductFreeAmount()).subtract(refundProAmount);
        BigDecimal difPath = difDetail.multiply(BigDecimal.valueOf(stockoutBO.getStockoutCount())).divide(BigDecimal.valueOf(orderItem.getCount()));
        detail.setRefundProductAmount(orderItem.getPrice().multiply(new BigDecimal(stockoutBO.getStockoutCount())).subtract(refundSubsidyFreeAmount).subtract(difPath).compareTo(subtract) > 0
                ? subtract : orderItem.getPrice().multiply(new BigDecimal(stockoutBO.getStockoutCount())).subtract(refundSubsidyFreeAmount).subtract(difPath));
        BigDecimal platformServiceAmount = (orderItem.getPlatformServiceAmount().subtract(orderItem.getPlatformServiceFreeAmount()).subtract(refundServiceAmount)).multiply(new BigDecimal(stockoutBO.getStockoutCount())).divide(new BigDecimal(orderItem.getCount()), 2, RoundingMode.HALF_UP);
        BigDecimal platformFreightAmount = (orderItem.getPlatformFreightAmount().subtract(orderItem.getPlatformFreightFreeAmount())).multiply(new BigDecimal(stockoutBO.getStockoutCount())).divide(new BigDecimal(orderItem.getCount()), 2, RoundingMode.HALF_UP);
        BigDecimal baseFreightAmount = (orderItem.getBaseFreightAmount().subtract(orderItem.getBaseFreightFreeAmount())).multiply(new BigDecimal(stockoutBO.getStockoutCount())).divide(new BigDecimal(orderItem.getCount()), 2, RoundingMode.HALF_UP);
        BigDecimal regionFreightAmount = (orderItem.getRegionFreightAmount().subtract(orderItem.getRegionFreightFreeAmount())).multiply(new BigDecimal(stockoutBO.getStockoutCount())).divide(new BigDecimal(orderItem.getCount()), 2, RoundingMode.HALF_UP);
        BigDecimal platformFreightAmountLevel2 = (orderItem.getPlatformFreightAmountLevel2().subtract(orderItem.getPlatformFreightFreeAmountLevel2())).multiply(new BigDecimal(stockoutBO.getStockoutCount())).divide(new BigDecimal(orderItem.getCount()), 2, RoundingMode.HALF_UP);
        detail.setRefundSubsidyFreeAmount(refundSkuSubsidyFreeAmount);
        detail.setRefundOtherAmount(platformServiceAmount.add(platformFreightAmount).add(baseFreightAmount).add(regionFreightAmount).add(platformFreightAmountLevel2));
        stockoutRecord.setSubsidyFreeAmount(stockoutRecord.getSubsidyFreeAmount().add(refundSkuSubsidyFreeAmount));
        //分销金额>0说明已结算，这里要将分销金额扣减出来
        if (distributionAmount.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal amount = subtract.subtract(detail.getRefundProductAmount()).subtract(distributionAmount);
            if (amount.compareTo(BigDecimal.ZERO) < 0) {
                detail.setRefundProductAmount(detail.getRefundProductAmount().add(amount));
            }
        }
        //退金融服务费
        BigDecimal apportionTotalAmount = orderItem.getTotalAmount().subtract(orderItem.getFinancialServiceAmount());
        //退款金额
        BigDecimal detailRefundAmount = detail.getRefundProductAmount().add(detail.getRefundOtherAmount());
        //计算金融手续费
        BigDecimal refundFinancialPrice = refundRecordService.getApportionRefundService(apportionTotalAmount,orderItem.getFinancialServiceAmount(),detailRefundAmount,refundServiceFee);
        stockoutRecord.setServiceAmount(stockoutRecord.getServiceAmount().add(platformServiceAmount));
        totalRefundAmount = totalRefundAmount.add(detail.getRefundProductAmount()).add(detail.getRefundOtherAmount());
        detail.setRefundOtherAmount(detail.getRefundOtherAmount().add(refundFinancialPrice));
        stockoutRecord.setFreightAmount(stockoutRecord.getFreightAmount().add(platformFreightAmount.add(baseFreightAmount).add(regionFreightAmount)).add(platformFreightAmountLevel2));
        stockoutRecord.setBaseFreight(stockoutRecord.getBaseFreight().add(baseFreightAmount));
        stockoutRecord.setPlatformFreight(stockoutRecord.getPlatformFreight().add(platformFreightAmount));
        stockoutRecord.setRegionFreight(stockoutRecord.getRegionFreight().add(regionFreightAmount));
        stockoutRecord.setFinancialServicePrice(stockoutRecord.getFinancialServicePrice().add(refundFinancialPrice));
        recoverCount.put(orderItem.getId(), stockoutBO.getStockoutCount());
        return totalRefundAmount;
    }

    /**
     * 创建判责单记录
     */
    private void createBlameRecord(CreateStockoutRecordBO createStockoutRecordBO, List<StockoutRecordDetail> collect, StockoutRecord l) {
        var t = blameRecordService.getBlameEntityBySource(l.getCode());
        if (t != null) {
            //避免重复创建判责单
            return;
        }
        CreateBlameRecordBO createBlameRecordBO = new CreateBlameRecordBO();
        createBlameRecordBO.setSourceType(BlameSourceTypeEnum.LESS_GOODS);
        createBlameRecordBO.setSourceId(l.getId());
        createBlameRecordBO.setProductAmount(collect.stream().map(StockoutRecordDetail::getRefundProductAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        createBlameRecordBO.setOtherAmount(collect.stream().map(StockoutRecordDetail::getRefundOtherAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        createBlameRecordBO.setServiceAmount(createStockoutRecordBO.getServiceAmount());
        createBlameRecordBO.setPlatformFreight(createStockoutRecordBO.getPlatformFreight());
        createBlameRecordBO.setBaseFreight(createStockoutRecordBO.getBaseFreight());
        createBlameRecordBO.setRegionFreight(createStockoutRecordBO.getRegionFreight());
        createBlameRecordBO.setFinancialServicePrice(createStockoutRecordBO.getFinancialServicePrice());
        createBlameRecordBO.setBlameAmount(createBlameRecordBO.getProductAmount().add(createBlameRecordBO.getOtherAmount()));
        createBlameRecordBO.setTotalCount(collect.stream().mapToInt(StockoutRecordDetail::getStockoutCount).sum());
        createBlameRecordBO.setCreateRemark(l.getCreateRemark());
        createBlameRecordBO.setCreateSceneCode(l.getCreateSceneCode());
        createBlameRecordBO.setCityWhCode(createStockoutRecordBO.getCityWhCode());
        createBlameRecordBO.setCityWhName(createStockoutRecordBO.getCityWhName());
        createBlameRecordBO.setSubsidyFreeAmount(createStockoutRecordBO.getSubsidyFreeAmount());
        createBlameRecordBO.setBusinessType(createStockoutRecordBO.getBusinessType());
        createBlameRecordBO.setPlaceIdLevel2(l.getPlaceIdLevel2());
        //没金额不用创建判责单
        if (createBlameRecordBO.getBlameAmount().compareTo(BigDecimal.ZERO) > 0) {
            blameRecordService.createBlameRecord(createBlameRecordBO);
        }
    }

    private void createLessStockoutRecord(List<QueryDistributionVo> orderList, Map<String, RemoteUserBo> buyerMap, Map<Long, RemoteRegionLogisticsVo> logisticsVoMap,
                                          Long l, QueryDistributionItemVo orderItem, BatchCreateStockoutBO stockoutBO, CreateStockoutRecordBO stockoutRecord) {
        stockoutRecord.setCode(CustomNoUtil.getLessNo(LocalDate.now()));
        stockoutRecord.setSupplierId(orderItem.getSupplierId());
        stockoutRecord.setSupplierDeptId(orderItem.getSupplierDeptId());
        stockoutRecord.setRegionWhId(orderItem.getRegionWhId());
        stockoutRecord.setSupplierSkuId(orderItem.getSupplierSkuId());
        stockoutRecord.setSupplierSkuCode(stockoutBO.getSupplierSkuCode());
        stockoutRecord.setSpuName(stockoutBO.getSpuName());
        stockoutRecord.setBuyerId(buyerMap.getOrDefault(orderItem.getBuyerCode(), new RemoteUserBo()).getUserId());
        stockoutRecord.setBuyerName(buyerMap.getOrDefault(orderItem.getBuyerCode(), new RemoteUserBo()).getRealName());
        stockoutRecord.setSaleDate(orderItem.getSaleDate());
        stockoutRecord.setType(BlameSourceTypeEnum.LESS_GOODS.getCode());
        stockoutRecord.setOrderType(orderList.get(0).getBusinessType());
        stockoutRecord.setBusinessType(orderItem.getBusinessType());
        stockoutRecord.setRefundStatus(StockOutRefundStatusEnum.UN_REFUND.getCode());
        stockoutRecord.setCreateSceneCode(stockoutBO.getCreateScene());
        stockoutRecord.setCityWhId(orderItem.getCityWhId());
        stockoutRecord.setLogisticsId(logisticsVoMap.get(orderItem.getLogisticsId()).getId());
        stockoutRecord.setLogisticsCode(logisticsVoMap.get(orderItem.getLogisticsId()).getLogisticsCode());
        stockoutRecord.setLogisticsName(logisticsVoMap.get(orderItem.getLogisticsId()).getLogisticsName());
        stockoutRecord.setAuditStatus(StockoutAuditStatusEnum.STATE_CONFIRM.getCode());
        stockoutRecord.setBlameStatus(BlameStatusEnum.STATE_CONFIRM.getCode());
        StockoutRecord sr = new StockoutRecord();
        BeanUtil.copyProperties(stockoutRecord, sr);
        if (ObjectUtil.equals(sr.getCreateSceneCode(), StockOutCreateSceneEnum.CITY_CREATE.getCode())) {
            sr.setIsAuto(0);
        }
        baseMapper.insert(sr);

        //需要在提货单中的数量减去对应手动创建的少货单的数量
        if (ObjectUtil.isNotEmpty(sr.getCreateSceneCode()) && StockOutCreateSceneEnum.CITY_CREATE.getCode().equals(sr.getCreateSceneCode())) {
            ReceiveGoodsRecordDetail recordDetail = getByOrderItemId(orderItem.getId());
            log.keyword("城市仓新增少货单", "createLessStockoutRecord", orderItem.getId())
                    .info("recordDetail:{}, orderItemId:{}, sr:{}, stockoutBO:{}", recordDetail, orderItem.getId(), sr, stockoutBO);
            if (ObjectUtil.isNotEmpty(recordDetail)) {
                ReceiveGoodsRecordDetail detail = new ReceiveGoodsRecordDetail();
                detail.setId(recordDetail.getId());
                detail.setStayCount(recordDetail.getStayCount() - stockoutBO.getStockoutCount());
                detail.setActualCount(recordDetail.getActualCount() - stockoutBO.getStockoutCount());
                receiveGoodsRecordDetailMapper.updateById(detail);

                ReceiveGoodsRecord rgr = receiveGoodsRecordMapper.selectById(recordDetail.getReceiveGoodsRecordId());
                if (ObjectUtil.isNotEmpty(rgr)) {
                    ReceiveGoodsRecord res = new ReceiveGoodsRecord();
                    res.setId(rgr.getId());
                    res.setStayCount(rgr.getStayCount() - stockoutBO.getStockoutCount());
                    receiveGoodsRecordMapper.updateById(res);
                }
            }
        }

        stockoutRecord.setId(sr.getId());
        //创建少货单操作记录
        OrderLog orderLog = new OrderLog();
        orderLog.setSourceId(sr.getId());
        orderLog.setSourceType(OrderLogSourceTypeEnum.STOCKOUT.getCode());
        orderLog.setOperate("少货自动生成");
        orderLog.setRemark(stockoutRecord.getCreateRemark());
        orderLogMapper.insert(orderLog);
    }

    private ReceiveGoodsRecordDetail getByOrderItemId(Long orderItemId) {
        LambdaQueryWrapper<ReceiveGoodsRecordDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ReceiveGoodsRecordDetail::getOrderItemId, orderItemId);
        List<ReceiveGoodsRecordDetail> receiveGoodsRecordDetails = receiveGoodsRecordDetailMapper.selectList(wrapper);
        if (ObjectUtil.isEmpty(receiveGoodsRecordDetails)) {
            return null;
        }
        return receiveGoodsRecordDetails.get(0);
    }

    /**
     * 创建缺货单
     */
    private void createStockoutRecord(CreateStockoutRecordBO stockoutRecord, BatchCreateStockoutBO bo, List<QueryDistributionVo> orderList) {
        QueryDistributionItemVo itemVo = orderList.get(0).getOrderItemList().get(0);
        //根据id获取销售批次商品信息
        RemoteSupplierSkuInfoVo skuInfoVo = remoteSupplierSkuService.getSimpleById(itemVo.getSupplierSkuId());
        //获取采购员信息
        List<String> buyerCodes = orderList.stream().map(QueryDistributionVo::getOrderItemList).flatMap(List::stream).map(QueryDistributionItemVo::getBuyerCode).toList();
        Map<String, RemoteUserBo> buyerMap = remoteUserService.getUsersByUserCodes(buyerCodes);
        //生成缺货单
        stockoutRecord.setCode(CustomNoUtil.getStockoutNo(LocalDate.now()));
        stockoutRecord.setSupplierId(itemVo.getSupplierId());
        stockoutRecord.setSupplierDeptId(itemVo.getSupplierDeptId());
        stockoutRecord.setCityWhId(itemVo.getCityWhId());
        stockoutRecord.setRegionWhId(itemVo.getRegionWhId());
        stockoutRecord.setSupplierSkuId(itemVo.getSupplierSkuId());
        stockoutRecord.setSupplierSkuCode(skuInfoVo.getCode());
        stockoutRecord.setSpuName(skuInfoVo.getSpuName());
        stockoutRecord.setBuyerId(buyerMap.getOrDefault(itemVo.getBuyerCode(), new RemoteUserBo()).getUserId());
        stockoutRecord.setBuyerName(buyerMap.getOrDefault(itemVo.getBuyerCode(), new RemoteUserBo()).getRealName());
        stockoutRecord.setSaleDate(itemVo.getSaleDate());
        stockoutRecord.setType(BlameSourceTypeEnum.STOCKOUT.getCode());
        stockoutRecord.setOrderType(orderList.get(0).getBusinessType());
        stockoutRecord.setRefundStatus(StockOutRefundStatusEnum.UN_REFUND.getCode());
        stockoutRecord.setCreateSceneCode(bo.getCreateScene());
        stockoutRecord.setBlameStatus(BlameStatusEnum.ALL_CONFIRM.getCode());
        stockoutRecord.setAuditStatus(StockoutAuditStatusEnum.STATE_CONFIRM.getCode());
        stockoutRecord.setBusinessType(itemVo.getBusinessType());
        StockoutRecord sr = new StockoutRecord();
        BeanUtil.copyProperties(stockoutRecord, sr);
        baseMapper.insert(sr);
        stockoutRecord.setId(sr.getId());
        //创建缺货单操作记录
        OrderLog orderLog = new OrderLog();
        orderLog.setSourceId(stockoutRecord.getId());
        orderLog.setSourceType(OrderLogSourceTypeEnum.STOCKOUT.getCode());
        orderLog.setOperate("缺货自动生成");
        orderLog.setRemark(stockoutRecord.getCreateRemark());
        orderLogMapper.insert(orderLog);
    }

    /**
     * 生成创建缺货少货单说明
     */
    private String delimiter = "；";

    private String createStockOutDesc(BatchCreateStockoutBO bo, CreateStockoutRecordBO recordBO, Map<Long, String> supMap) {
        StringBuilder sb = new StringBuilder();
        RemoteUserBo userInfo = new RemoteUserBo();
        BigDecimal total = recordBO.getProductAmount().add(recordBO.getOtherAmount());
//        if (bo.getSystemAuto() != null && bo.getSystemAuto()){
//            userInfo.setRealName(supMap.get(recordBO.getSupplierId()));
//        } else
        if (bo.getBuyerId() != null && bo.getBuyerId() > 0) {
            if (bo.getBuyerId().toString().length() == 11) {
                // 板车队，是临时用户，手机号
                userInfo.setRealName(bo.getBuyerId().toString());
            } else {
                userInfo = remoteUserService.getUserInfo(bo.getBuyerId());
            }
        } else if (bo.getSupplierId() != null) {
            userInfo = remoteUserService.getUserInfo(bo.getSupplierId());
        }
        switch (Objects.requireNonNull(StockOutCreateSceneEnum.loadByCode(bo.getCreateScene()))) {
            case NO_BUY_GOODS:
                sb.append("采购员").append(userInfo.getRealName()).append("于").append(DateUtil.now())
                        .append("提交不采购操作").append(delimiter).append(createStockOutDesc2(recordBO));
                break;
            case NO_DELIVERY:
                sb.append(bo.getBuyerId() != null ? "采购员" : "供应商").append(userInfo.getRealName()).append("于").append(DateUtil.now())
                        .append("对送货单的差异部分进行处理，决定“不再送货”").append(delimiter).append(createStockOutDesc2(recordBO));
                break;
            case SORT_GOODS_FINISH:
                sb.append("分货员").append(userInfo.getRealName()).append("完成分货时创建").append(delimiter).append(createStockOutDesc2(recordBO));
                break;
            case RECEIVE_GOODS:
                sb.append("客户提货时经城市仓确定少货").append(delimiter).append(createStockOutDesc2(recordBO));
                break;
            case BLAME_SPLIT:
                sb.append("在判责时由").append(userInfo.getRealName()).append("进行判责分拆。判责说明为").append(bo.getCreateRemark());
                break;
            case ENTRUCK_GOODS:
                sb.append(StrUtil.isBlank(userInfo.getUserCode()) ? "板车队" :"采购员").append(userInfo.getRealName()).append("于").append(DateUtil.now())
                        .append("提交装车缺货操作").append(delimiter).append(createStockOutDesc2(recordBO));
                break;
            case AUTO_NO_BUY_GOODS:
                sb.append("未送货商品超时未处理，定时任务于").append(DateUtil.now()).append("创建缺货单").append(delimiter).append(createStockOutDesc2(recordBO));
                break;
            case AUTO_NO_DELIVERY:
                sb.append("装车差异超时未处理，定时任务于").append(DateUtil.now()).append("创建缺货单").append(delimiter).append(createStockOutDesc2(recordBO));
                break;
            case PICKING_ENTRUCK_GOODS:
                sb.append("装卸员").append(userInfo.getRealName()).append("于").append(DateUtil.now())
                        .append("提交装车缺货操作").append(delimiter).append(createStockOutDesc2(recordBO));
                break;
            case CITY_CREATE:
                sb.append("城市仓手动创建少货单").append(delimiter).append(createStockOutDesc2(recordBO));
                break;
            default:
                sb.append("缺货单。");
                break;
        }
        return sb.toString();
    }

    private String createStockOutDesc2(CreateStockoutRecordBO recordBO) {
        BigDecimal total = recordBO.getProductAmount().add(recordBO.getOtherAmount());
        StringBuilder sb = new StringBuilder();
        sb.append("退客户金额共计").append(total).append("元")
                .append("，其中商品金额").append(recordBO.getProductAmount()).append("元")
                .append("，服务费").append(recordBO.getServiceAmount()).append("元")
                .append("，运费合计").append(recordBO.getFreightAmount()).append("元")
                .append("，金融服务费").append(recordBO.getFinancialServicePrice()).append("元");
//        if (!recordBO.getSubsidyFreeAmount().equals(BigDecimal.ZERO)) {
//            sb.append("，退给平台的优惠补贴").append(recordBO.getSubsidyFreeAmount()).append("元");
//        }
        sb.append("。");
        return sb.toString();
    }

    private String createDescAgain(List<StockoutRecordDetail> details, StockoutRecord record, BigDecimal serviceAmount, BigDecimal freightAmount, BigDecimal financialPrice) {
        CreateStockoutRecordBO recordBO = new CreateStockoutRecordBO();
        recordBO.setProductAmount(details.stream().map(StockoutRecordDetail::getRefundProductAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        recordBO.setOtherAmount(details.stream().map(StockoutRecordDetail::getRefundOtherAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        recordBO.setServiceAmount(serviceAmount);
        recordBO.setFreightAmount(freightAmount);
        recordBO.setFinancialServicePrice(financialPrice);
        recordBO.setSubsidyFreeAmount(details.stream().map(StockoutRecordDetail::getRefundSubsidyFreeAmount).reduce(BigDecimal.ZERO, BigDecimal::add));

        StringBuilder sb = new StringBuilder();
        StockOutCreateSceneEnum sceneEnum = Objects.requireNonNull(StockOutCreateSceneEnum.loadByCode(record.getCreateSceneCode()));
        if (sceneEnum == StockOutCreateSceneEnum.NO_BUY_GOODS
                || sceneEnum == StockOutCreateSceneEnum.NO_DELIVERY
                || sceneEnum == StockOutCreateSceneEnum.SORT_GOODS_FINISH
                || sceneEnum == StockOutCreateSceneEnum.RECEIVE_GOODS
                || sceneEnum == StockOutCreateSceneEnum.ENTRUCK_GOODS
                || sceneEnum == StockOutCreateSceneEnum.AUTO_NO_BUY_GOODS
                || sceneEnum == StockOutCreateSceneEnum.AUTO_NO_DELIVERY
                || sceneEnum == StockOutCreateSceneEnum.PICKING_ENTRUCK_GOODS
                || sceneEnum == StockOutCreateSceneEnum.CITY_CREATE) {
            sb.append(createStockOutDesc2(recordBO));
        } else {
            sb.append("缺货单。");
        }
        return sb.toString();
    }

    private void sendSmsOfConfirm(StockoutRecord stockoutRecord, StockoutRecordDetail detail, LocalDate date) {
        if (detail.getStockoutCount() != 0) {
            OrderItem orderItem = orderItemMapper.selectById(detail.getOrderItemId());

            Long productId = refundProductDetailMapper.getIdBySourceCodeAndItem(stockoutRecord.getCode(), detail.getOrderItemId());
            String customerParam = "";
            if (productId != null) {
                customerParam = productId.toString();
            }

            LinkedHashMap<String, String> params = new LinkedHashMap<>();
            params.put("1", String.valueOf(date));
            params.put("2", detail.getSpuName());
            params.put("3", String.valueOf(detail.getStockoutCount()));
            params.put("4", String.valueOf(detail.getRefundProductAmount().add(detail.getRefundOtherAmount())));

            params.put("orderCode", orderItem.getOrderCode());
            params.put("spuName", remoteSupplierSkuService.getSkuCombinationName(Lists.newArrayList(orderItem.getSupplierSkuId())).get(orderItem.getSupplierSkuId()));
            params.put("countNum", String.valueOf(detail.getStockoutCount()));
            params.put("submitTime", DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN));

            if (stockoutRecord.getType().equals(BlameSourceTypeEnum.LESS_GOODS.getCode())) {
                RemoteMessageNotifyV2Bo message1 = new RemoteMessageNotifyV2Bo(NotifyObjectTypeEnum.CUSTOMER, Lists.newArrayList(detail.getCustomerId().toString()), MsgNotifyTemplateV2Enum.order_stockout, params, customerParam);
                RemoteMessageNotifyV2Bo message2 = new RemoteMessageNotifyV2Bo(NotifyObjectTypeEnum.SUPPLIER, Lists.newArrayList(stockoutRecord.getSupplierId().toString()), MsgNotifyTemplateV2Enum.order_stockout, params, stockoutRecord.getId().toString());
                List<RemoteMessageNotifyV2Bo> messages = new ArrayList<>();
                messages.add(message1);
                messages.add(message2);
                remoteMessageNotifyService.sendMessagesV2(messages);
            } else {
                RemoteMessageNotifyV2Bo message1 = new RemoteMessageNotifyV2Bo(NotifyObjectTypeEnum.CUSTOMER, Lists.newArrayList(detail.getCustomerId().toString()), MsgNotifyTemplateV2Enum.order_quehuo, params, customerParam);
                RemoteMessageNotifyV2Bo message2 = new RemoteMessageNotifyV2Bo(NotifyObjectTypeEnum.SUPPLIER, Lists.newArrayList(stockoutRecord.getSupplierId().toString()), MsgNotifyTemplateV2Enum.order_quehuo, params, stockoutRecord.getId().toString());
                RemoteMessageNotifyV2Bo message3 = new RemoteMessageNotifyV2Bo(NotifyObjectTypeEnum.CITY_WH, Lists.newArrayList(orderItem.getCityWhId().toString()), MsgNotifyTemplateV2Enum.order_quehuo, params, stockoutRecord.getId().toString());

                List<RemoteMessageNotifyV2Bo> messages = new ArrayList<>();
                messages.add(message1);
                messages.add(message2);
                messages.add(message3);
                remoteMessageNotifyService.sendMessagesV2(messages);
            }

        }
    }


    private void updateAfterSaleTime(ReceiveGoodsRecordDetail record) {
        if (record.getAfterSaleTime() == null) {
            record.setAfterSaleTime(AfterSaleUtil.getLastAfterSaleDay());
        }
    }
}
