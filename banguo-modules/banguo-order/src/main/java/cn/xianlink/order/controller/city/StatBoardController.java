package cn.xianlink.order.controller.city;


import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.domain.bo.report.StatSaleSearchBo;
import cn.xianlink.order.domain.vo.report.CommodityStatisticsInfoVo;
import cn.xianlink.order.domain.vo.report.SaleCustomerCountVo;
import cn.xianlink.order.domain.vo.report.StatBoardVo;
import cn.xianlink.order.service.IStatBoardService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 城市仓小程序-看板
 *
 * <AUTHOR>
 * @date 2024-10-15
 * @folder 城市仓端(小程序)/看板
 */
@Validated
@RequiredArgsConstructor
@RestController("cityStatBoardController")
@RequestMapping("/order/city/stat_board/")
public class StatBoardController extends BaseController {

    private final IStatBoardService statBoardService;

    /**
     * 统计商品，客户，金额
     *
     * @param bo
     * @return
     */
    @PostMapping("/city_sale")
    public R<StatBoardVo> statCitySale(@RequestBody StatSaleSearchBo bo) {
        return R.ok(statBoardService.statCitySale(bo));
    }

    /**
     *商品统计列表
     * @param bo
     * @return
     */
    @PostMapping("/commodity_statistics_city")
    public R<TableDataInfo<CommodityStatisticsInfoVo>> commodityStatistics(@RequestBody StatSaleSearchBo bo) {
        return R.ok(statBoardService.commodityStatisticsCitySale(bo));
    }

    /**
     *商品统计合计
     * @param bo
     * @return
     */
    @PostMapping("/commodity_statistics_city_total")
    public R<CommodityStatisticsInfoVo> commodityStatisticsTotal(@RequestBody StatSaleSearchBo bo) {
        return R.ok(statBoardService.commodityStatisticsCitySaleTotal(bo));
    }

    /**
     *下单客户数
     * @param bo
     * @return
     */
    @PostMapping("/sale_customer_count")
    public R<TableDataInfo<SaleCustomerCountVo>> saleCustomerCount(@RequestBody StatSaleSearchBo bo) {
        return R.ok(statBoardService.saleCustomerCount(bo));
    }

    /**
     *下单客户数合计
     * @param bo
     * @return
     */
    @PostMapping("/sale_customer_count_total")
    public R<SaleCustomerCountVo> saleCustomerCountTotal(@RequestBody StatSaleSearchBo bo) {
        return R.ok(statBoardService.saleCustomerCountTotal(bo));
    }
}