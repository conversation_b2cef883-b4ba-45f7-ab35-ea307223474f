package cn.xianlink.order.service;

import cn.xianlink.order.api.bo.RemoteSupAccSubsidyBo;
import cn.xianlink.order.api.bo.RemoteSupAccTransQueryBo;
import cn.xianlink.order.api.vo.RemoteOrderSalesVO;
import cn.xianlink.order.api.vo.RemoteRegionSubMaxPriceVo;
import cn.xianlink.order.api.vo.RemoteSupBillVo;
import cn.xianlink.order.domain.OrderItem;
import cn.xianlink.order.domain.excel.LogisticsOrderExportDto;
import cn.xianlink.order.domain.order.vo.OrderItemSupplierSkuInfoVo;
import cn.xianlink.order.domain.order.vo.OrderVo;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 订单项Service接口
 *
 * <AUTHOR>
 * @date 2024-05-25
 */
public interface IOrderItemService {

    /**
     * 根据订单号和供应商id查询对应的商品批次信息
     * @param orderCode
     * @param supplierId
     * @return
     */
    List<OrderItemSupplierSkuInfoVo> getByOrderCodeAndSupplierId(String orderCode, Long supplierId);

    /**
     * 提货-结算
     * <AUTHOR> on 2024/6/24:15:13
     * @param itemIds
     * @return void
     */
    void notifySettle(List<Long> itemIds);

    /**
     * 结算订单项
     * <p>只给结算逻辑内部调用，外部触发使用notifySettle</p>
     * <AUTHOR> on 2024/6/26:15:16
     * @param itemId
     * @return void
     */
    void itemSettle(Long itemId, String triggerSource);


    List<OrderItem> getByOrderIds(Set<Long> orderIds, List<String> cancelTypes);

    /**
     * 根据物流线查询订单，用于导出
     * <AUTHOR> on 2024/11/19:18:08
     * @param logisticsIds
     * @return java.util.List
     */
    List<LogisticsOrderExportDto> selectLogisticsOrderForExport(List<Long> logisticsIds, LocalDate saleDate,List<String> placePaths);

    /**
     * 根据城市仓id查询订单销量信息
     */
    List<RemoteOrderSalesVO> getOrderSalesByCity(Long cityWhId);

    /**
     * 根据销售日期，获取有销售记录城市仓id列表
     */
    List<OrderVo> getCityWhIdListByExistSales(LocalDate saleDateStart, LocalDate saleDateEnd, Long notRunRegionWhId);


    List<OrderItem> selectListByIds(List<Long> itemIds);

    List<OrderItem> getByOrderId(Long orderId);

    List<RemoteSupBillVo> queryPlatformSubsidyByDate(RemoteSupAccTransQueryBo bo);

    List<RemoteSupBillVo> queryPlatformSubsidyList(RemoteSupAccSubsidyBo subSidyQueryBo);

    RemoteSupBillVo queryPlatformSubsidyTotal(RemoteSupAccSubsidyBo subSidyQueryBo);

    void fixProfitRule(Long ruleType, Long regionWhId, Long cityWhId, LocalDate saleDate, Long rulerdRecordId);

    List<RemoteRegionSubMaxPriceVo> selectMinPriceBySkuIds(Set<Long> supSkuIds);

    Map<String, Long> getSalesQuantityCount(Long skuId, LocalDate yesterday, LocalDate week, LocalDate saleDate);
}
