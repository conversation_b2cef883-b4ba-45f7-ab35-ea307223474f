package cn.xianlink.order.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 免税商品审核状态枚举
 *
 * <AUTHOR>
 * @date 2025-01-30
 */
@Getter
@AllArgsConstructor
public enum DutyFreeStatusEnum {

    /**
     * 待审核
     */
    PENDING(0, "待审核"),

    /**
     * 审核通过
     */
    APPROVED(1, "审核通过"),

    /**
     * 审核不通过
     */
    REJECTED(2, "审核不通过");

    private final Integer code;
    private final String desc;

    /**
     * 根据code获取枚举
     *
     * @param code 状态码
     * @return 枚举对象
     */
    public static DutyFreeStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (DutyFreeStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 判断是否为有效状态
     *
     * @param code 状态码
     * @return 是否有效
     */
    public static boolean isValid(Integer code) {
        return getByCode(code) != null;
    }

}
