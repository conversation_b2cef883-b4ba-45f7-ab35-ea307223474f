package cn.xianlink.order.domain.vo;

import cn.xianlink.common.api.enums.product.CategoryAfterTypeEnum;
import cn.xianlink.common.dict.annotation.DictConvertFiled;
import cn.xianlink.common.dict.enums.DictConvertTypeEnum;
import cn.xianlink.order.domain.CartItem;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.github.linpeilie.annotations.AutoMapper;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;


/**
 * 购物车视图对象 cart_item
 *
 * <AUTHOR>
 * @date 2024-05-25
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = CartItem.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CartItemVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("购物车主键id")
    private Long id;

    @ApiModelProperty("总仓id[外键]")
    private Long regionWhId;

    @ApiModelProperty("总仓编码")
    private String regionWhCode;

    @ApiModelProperty("总仓名称")
    private String regionWhName;

    @ApiModelProperty("供应商商品skuID")
    private Long supplierSkuId;

    @ApiModelProperty("供应商商品skuID")
    private Long skuId;

    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("购物车产品个数")
    private Integer count;

    @ApiModelProperty("售价，加入购物车时的商品价格")
    private BigDecimal priceFee;

    @ApiModelProperty(value = "总金额")
    private BigDecimal totalAmount;

    @ApiModelProperty("是否失效（0否1是）")
    private Integer isExpire;

    @ApiModelProperty("业务类型，1市采, 10地采, 20基采, 30产地，40配货")
    @DictConvertFiled(dictCode = "orderOrderBusinessType", filedName = "businessTypeName", type = DictConvertTypeEnum.ENUM)
    private Integer businessType;

    @ApiModelProperty("销售类型：1为般果代采，2为商家自营")
    private Integer saleType;

    @ApiModelProperty("批次类型")
    private Integer batchType;

    @ApiModelProperty("批次类型名称，1正常，2尾货，3后台")
    private String batchTypeName;

    @ApiModelProperty("供应商简码")
    private String supplierSimpleCode;

    @ApiModelProperty("是否战略合作品，0为不是，1为是")
    private Integer cooperation;

    @ApiModelProperty("商品图片地址")
    private String imgUrl;

    @ApiModelProperty("最小购买数量，为-1不限制")
    private Integer buyMin;

    @ApiModelProperty("最大购买数量，为-1不限制")
    private Integer buyMax;

    @ApiModelProperty("下单倍数")
    private Integer placeOrderMultiple;

    @ApiModelProperty("库存，为-9999则是无限库存")
    private Integer stock;

    @ApiModelProperty("锁定库存")
    private Integer lockStock;

    @ApiModelProperty("已售库存")
    private Integer sold;

    @ApiModelProperty("平台商品名称")
    private String spuName;

    @ApiModelProperty("平台商品净重(斤)")
    private BigDecimal spuNetWeight;

    @ApiModelProperty("平台商品等级，数据字典配置")
    @DictConvertFiled(dictCode = "BasePro_rank", filedName = "spuGradeName", dictRemark = true)
    private String spuGrade;

    @ApiModelProperty("平台商品规格")
    private String spuStandards;

    @ApiModelProperty("规格储存内容")
    private String spuStandardsName;

    @ApiModelProperty("商品等级描述")
    private String spuGradeDesc;

    @ApiModelProperty("平台规格列表")
    private List<KeyValueBO> skuStandardsBoList;

    @ApiModelProperty("产地来源：0国产 1进口")
    private Integer domestic;

    @ApiModelProperty("产地")
    private String producer;

    @ApiModelProperty("净单价")
    private BigDecimal netWeightPrice;

    /**
     * @see CategoryAfterTypeEnum
     */
    @ApiModelProperty("售后规则：0无售后、1正常售后、2库管验货")
    private Integer afterSaleType;
    private String afterSaleName;
    private String afterSaleDesc;


    @ApiModelProperty("售后天数：0无售后，其它暂时写死 1")
    private Integer afterSaleDay;

    @ApiModelProperty("商品金额")
    //商品金额
    private BigDecimal price;

    @ApiModelProperty("区域编码")
    private String areaCode;

    @ApiModelProperty("品牌")
    private String brand;

    @ApiModelProperty("产地简称")
    private String shortProducer;

    @ApiModelProperty("最大限购提示")
    private String maxBuyTip;

    @ApiModelProperty("是否支持配送 0否 1是")
    private Integer hasLogistics = 1;

    @ApiModelProperty("供货总仓名称")
    private String provideRegionWhName;

    @ApiModelProperty("spuId")
    private Long spuId;

    @ApiModelProperty("是否参与运费优惠 0否 1是")
    private Integer isFreightDiscount;

    @ApiModelProperty("运费优惠最小件数")
    private Integer freightDiscountMinNum;

    @ApiModelProperty("寿光蔬菜，0为否，1为是")
    private Integer shouguangVegetables;

    /**
     * 商品分类id
     */
    private Long categoryId;
    /**
     * 商品分类code
     */
    private String categoryCode;

    /**
     * 二级商品分类
     */
    private Long categoryIdLevel2;
    /**
     * 一级分类
     */
    private Long categoryIdLevel1;

    /**
     * 商品品种
     */
    private String categoryPathName;


    /**
     * 平台商品ID
     */
    private String spuCode;

    /**
     * 供应商skuId
     */
    private String skuCode;


    /**
     * 供应商商品id
     */
    private Long supplierSpuId;

    private Boolean selected;

    /**
     * 供应商商品id
     */
    private String supplierSpuCode;



    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * 档口
     */
    private Long supplierDeptId;

    /**
     * 采购员code
     */
    private String buyerCode;

    /**
     * 采购员name
     */
    private String buyerName;

    /**
     * 商品毛重
     */
    private BigDecimal spuGrossWeight = BigDecimal.ZERO;


    /**
     * 供货总仓
     */
    private Long provideRegionWhId = 0L;


    @ApiModelProperty("1无售后 2可售后 3已售后")
    private Integer afterSaleStatus = 1;
    @ApiModelProperty("前端隐藏商户编码和店铺入口")
    private Integer hideMerchant;
    /**
     * 供应商别名
     */
    private String supplierAlias;

    /**
     * 关联skuId
     */
    private Long relationSkuId;


    /**
     * 商品补贴金额
     */
    private BigDecimal skuSubsidy = BigDecimal.ZERO;



    public CartItemVo buildSkuStandardsList() {
        if (StringUtils.isNotBlank(spuStandards) && spuStandards.contains("|")) {
            String[] split = spuStandards.split("\\|");
            List<KeyValueBO> list = new ArrayList<>();
            for (String s : split) {
                if (StringUtils.isNotBlank(s) && s.contains(":")) {
                    list.add(new KeyValueBO(s.split(":")[0], s.split(":")[1]));
                }
            }
            this.skuStandardsBoList = list;
        }
        return this;
    }

    public void buildSpuStandardsName() {
        spuStandardsName = spuStandards;
        // 支持单规格和多规格
        spuStandards = KeyValueBO.parseSkuStandardsValuesName(spuStandards);
    }

}
