package cn.xianlink.order.service;

import cn.xianlink.order.domain.vo.InvoiceItemVo;
import cn.xianlink.order.domain.bo.InvoiceItemBo;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.order.domain.vo.invoice.InvoiceItemSkuVo;

import java.util.Collection;
import java.util.List;

/**
 * 发票项Service接口
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface IInvoiceItemService {

    /**
     * 查询发票项
     */
    InvoiceItemVo queryById(Long id);

    /**
     * 查询发票项列表
     */
    TableDataInfo<InvoiceItemVo> queryPageList(InvoiceItemBo bo, PageQuery pageQuery);

    /**
     * 查询发票项列表
     */
    List<InvoiceItemVo> queryList(InvoiceItemBo bo);

    /**
     * 新增发票项
     */
    Boolean insertByBo(InvoiceItemBo bo);

    /**
     * 修改发票项
     */
    Boolean updateByBo(InvoiceItemBo bo);

    /**
     * 校验并批量删除发票项信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    TableDataInfo<InvoiceItemSkuVo> queryInvoiceitemlist(InvoiceItemBo bo, PageQuery pageQuery);
}
