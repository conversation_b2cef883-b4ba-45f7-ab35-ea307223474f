package cn.xianlink.order.domain.bo;

import cn.xianlink.common.core.validate.AddGroup;
import cn.xianlink.common.core.validate.EditGroup;
import cn.xianlink.common.mybatis.core.domain.BaseEntity;
import cn.xianlink.order.domain.CarList;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 车型表业务对象 car_list
 *
 * <AUTHOR>
 * @date 2025-01-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = CarList.class, reverseConvertGenerate = false)
public class CarListBo extends BaseEntity {

    /**
     * 车型表信息主键id
     */
    @NotNull(message = "车型表信息主键id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 车辆型号
     */
    @NotBlank(message = "车辆型号不能为空", groups = { AddGroup.class, EditGroup.class })
    @Size(max = 64, message = "车辆型号长度不能超过64个字符")
    private String model;

    /**
     * 总仓id
     */
    @NotNull(message = "总仓id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long regionWhId;

    /**
     * 载重（单位：吨）
     */
    @NotNull(message = "载重不能为空", groups = { AddGroup.class, EditGroup.class })
    @DecimalMin(value = "0.01", message = "载重必须大于0")
    @Digits(integer = 16, fraction = 2, message = "载重格式不正确")
    private BigDecimal carryWeight;

    /**
     * 装车费（单位：元）
     */
    @NotNull(message = "装车费不能为空", groups = { AddGroup.class, EditGroup.class })
    @DecimalMin(value = "0.00", message = "装车费不能为负数")
    @Digits(integer = 16, fraction = 2, message = "装车费格式不正确")
    private BigDecimal entruckFee;

}
