package cn.xianlink.order.domain.vo;

import java.math.BigDecimal;
import cn.xianlink.order.domain.ActivityRecord;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import cn.xianlink.common.excel.annotation.ExcelDictFormat;
import cn.xianlink.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 活动记录视图对象 activity_record
 *
 * <AUTHOR>
 * @date 2024-11-15
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ActivityRecord.class)
public class ActivityRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 订单总表唯一编码
     */
    @ExcelProperty(value = "订单总表唯一编码")
    private String orderCode;

    /**
     * 订单总表id
     */
    @ExcelProperty(value = "订单总表id")
    private Long orderId;

    /**
     * 客户Id
     */
    @ExcelProperty(value = "客户Id")
    private Long customerId;

    /**
     * 平台商品名称
     */
    @ExcelProperty(value = "平台商品名称")
    private String spuName;

    /**
     * 供应商商品id
     */
    @ExcelProperty(value = "供应商商品id")
    private Long supplierSpuId;

    /**
     * 供应商商品销售批次id
     */
    @ExcelProperty(value = "供应商商品销售批次id")
    private Long supplierSkuId;

    /**
     * 订单总表id
     */
    @ExcelProperty(value = "订单总表id")
    private Long orderItemId;

    /**
     * 1：免运，2：免代
     */
    @ExcelProperty(value = "1：免运，2：免代")
    private Integer type;

    /**
     * 活动id
     */
    @ExcelProperty(value = "活动id")
    private Long activityId;

    /**
     * 优惠总金额
     */
    @ExcelProperty(value = "优惠总金额")
    private BigDecimal freeAmount;

    /**
     * 优惠总金额
     */
    @ExcelProperty(value = "订单金额")
    private BigDecimal orderAmount;

    /**
     * 创建用户代码
     */
    @ExcelProperty(value = "创建用户代码")
    private String createCode;

    /**
     * 创建用户名称
     */
    @ExcelProperty(value = "创建用户名称")
    private String createName;

    /**
     * 修改用户代码
     */
    @ExcelProperty(value = "修改用户代码")
    private String updateCode;

    /**
     * 修改用户名称
     */
    @ExcelProperty(value = "修改用户名称")
    private String updateName;


}