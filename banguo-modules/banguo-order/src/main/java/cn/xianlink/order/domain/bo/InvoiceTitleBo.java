package cn.xianlink.order.domain.bo;

import cn.xianlink.order.domain.InvoiceTitle;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.HashMap;
import java.util.Map;
import java.util.Date;
import java.io.Serializable;
import cn.xianlink.common.core.validate.AddGroup;
import cn.xianlink.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import jakarta.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 发票抬头业务对象 inv_invoice_title
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Schema(description = "发票抬头业务对象")
@Data
@AutoMapper(target = InvoiceTitle.class, reverseConvertGenerate = false)
public class InvoiceTitleBo implements Serializable {

    /**
     * 发票抬头ID (主键)
     */
    @Schema(description = "发票抬头ID", example = "1", accessMode = Schema.AccessMode.READ_ONLY)
    @NotNull(message = "发票抬头ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 客户Id
     */
    @Schema(description = "客户ID", example = "1001", required = true)
    @JsonIgnore
    private Long customerId;

    /**
     * 抬头名称 (如公司名)
     */
    @Schema(description = "发票抬头名称", example = "深圳XXYY科技有限公司", required = true)
    @NotBlank(message = "抬头名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String titleName;

    /**
     * 税号 (公司类型必填)
     */
    @Schema(description = "税号（公司类型必填）", example = "91440011223344XXCC")
    private String taxNumber;

    /**
     * 注册地址
     */
    @Schema(description = "注册地址", example = "深圳市宝安区西乡街道XXYY路YXXX号XXYY大厦")
    private String address;

    /**
     * 银行账号
     */
    @Schema(description = "银行账号", example = "*****************")
    private String bankAccount;

    /**
     * 银行名称
     */
    @Schema(description = "银行名称", example = "中国工商银行")
    private String bankName;

    /**
     * 注册电话
     */
    @Schema(description = "注册电话", example = "0755-xxxxxxxx")
    private String phone;

    /**
     * 邮箱地址
     */
    @Schema(description = "邮箱地址", example = "<EMAIL>")
    @Email(message = "邮箱格式不正确", groups = { AddGroup.class, EditGroup.class })
    private String emailAddress;

    /**
     * 抬头类型 (company公司, personal个人)
     */
    @Schema(description = "抬头类型", example = "company", allowableValues = {"company", "personal"}, required = true)
    @NotBlank(message = "抬头类型不能为空", groups = { AddGroup.class, EditGroup.class })
    @Pattern(regexp = "^(company|personal)$", message = "抬头类型只能是company或personal", groups = { AddGroup.class, EditGroup.class })
    private String titleType;

    /**
     * 发票类型 (normal普票, special专票)
     */
    @Schema(description = "发票类型", example = "normal", allowableValues = {"normal", "special"}, required = true)
    @NotBlank(message = "发票类型不能为空", groups = { AddGroup.class, EditGroup.class })
    @Pattern(regexp = "^(normal|special)$", message = "发票类型只能是normal或special", groups = { AddGroup.class, EditGroup.class })
    private String invoiceType;

    /**
     * 联系电话
     */
    @Schema(description = "联系电话", example = "13800138000", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "联系电话不能为空", groups = { AddGroup.class, EditGroup.class })
    private String contactPhone;

    /**
     * 所在地区
     */
    @Schema(description = "所在地区", example = "广东省 深圳市 龙岗区")
    private String region;

    /**
     * 详细地址
     */
    @Schema(description = "详细地址", example = "深圳市宝安区西乡街道XXYY路YXXX号XXYY大厦")
    private String detailedAddress;

    /**
     * 收件人姓名
     */
    @Schema(description = "收件人姓名", example = "张三", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "收件人姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String recipientName;

    /**
     * 纬度
     */
    @Schema(description = "纬度", example = "22.547")
    private String latitude;

    /**
     * 经度
     */
    @Schema(description = "经度", example = "114.085")
    private String longitude;

    /**
     * 创建用户代码（系统自动设置，不需要前端传入）
     */
    @Hidden
    @JsonIgnore
    @Schema(hidden = true)
    private String createCode;

    /**
     * 创建用户名称（系统自动设置，不需要前端传入）
     */
    @Hidden
    @JsonIgnore
    @Schema(hidden = true)
    private String createName;

    /**
     * 修改用户代码（系统自动设置，不需要前端传入）
     */
    @Hidden
    @JsonIgnore
    @Schema(hidden = true)
    private String updateCode;

    /**
     * 修改用户名称（系统自动设置，不需要前端传入）
     */
    @Hidden
    @JsonIgnore
    @Schema(hidden = true)
    private String updateName;

    /**
     * 创建时间（系统自动设置，不需要前端传入）
     */
    @Hidden
    @JsonIgnore
    @Schema(hidden = true)
    private Date createTime;

    /**
     * 修改时间（系统自动设置，不需要前端传入）
     */
    @Hidden
    @JsonIgnore
    @Schema(hidden = true)
    private Date updateTime;

    /**
     * 请求参数（用于查询条件扩展，不在API文档中显示）
     */
    @Hidden
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @TableField(exist = false)
    @Schema(hidden = true)
    private Map<String, Object> params = new HashMap<>();
}
