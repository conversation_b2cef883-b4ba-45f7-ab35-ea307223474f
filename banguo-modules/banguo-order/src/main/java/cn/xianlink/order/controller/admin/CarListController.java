package cn.xianlink.order.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.validate.AddGroup;
import cn.xianlink.common.core.validate.EditGroup;
import cn.xianlink.common.excel.utils.ExcelUtil;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.log.annotation.Log;
import cn.xianlink.common.log.enums.BusinessType;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.domain.bo.CarListBo;
import cn.xianlink.order.domain.vo.CarListVo;
import cn.xianlink.order.service.ICarListService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 车型表管理
 *
 * <AUTHOR>
 * @date 2025-01-30
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/order/carList")
@Tag(name = "车型表管理", description = "车型表管理")
public class CarListController extends BaseController {

    private final ICarListService carListService;

    /**
     * 查询车型表列表
     */
    @Operation(summary = "查询车型表列表")
    @SaCheckPermission("order:carList:list")
    @GetMapping("/list")
    public TableDataInfo<CarListVo> list(CarListBo bo, PageQuery pageQuery) {
        return carListService.queryPageList(bo, pageQuery);
    }

    /**
     * 根据总仓ID查询车型列表
     */
    @Operation(summary = "根据总仓ID查询车型列表")
    @SaCheckPermission("order:carList:list")
    @GetMapping("/listByRegionWh/{regionWhId}")
    public R<List<CarListVo>> listByRegionWh(@PathVariable("regionWhId") Long regionWhId) {
        List<CarListVo> list = carListService.queryByRegionWhId(regionWhId);
        return R.ok(list);
    }

    /**
     * 导出车型表列表
     */
    @Operation(summary = "导出车型表列表")
    @SaCheckPermission("order:carList:export")
    @Log(title = "车型表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(CarListBo bo, HttpServletResponse response) {
        List<CarListVo> list = carListService.queryList(bo);
        ExcelUtil.exportExcel(list, "车型表", CarListVo.class, response);
    }

    /**
     * 获取车型表详细信息
     */
    @Operation(summary = "获取车型表详细信息")
    @SaCheckPermission("order:carList:query")
    @GetMapping("/{id}")
    public R<CarListVo> getInfo(@NotNull(message = "主键不能为空")
                                @Parameter(description = "主键")
                                @PathVariable Long id) {
        return R.ok(carListService.queryById(id));
    }

    /**
     * 新增车型表
     */
    @Operation(summary = "新增车型表")
    @SaCheckPermission("order:carList:add")
    @Log(title = "车型表", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody CarListBo bo) {
        return toAjax(carListService.insertByBo(bo));
    }

    /**
     * 修改车型表
     */
    @Operation(summary = "修改车型表")
    @SaCheckPermission("order:carList:edit")
    @Log(title = "车型表", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody CarListBo bo) {
        return toAjax(carListService.updateByBo(bo));
    }

    /**
     * 删除车型表
     */
    @Operation(summary = "删除车型表")
    @SaCheckPermission("order:carList:remove")
    @Log(title = "车型表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @Parameter(description = "主键串")
                          @PathVariable Long[] ids) {
        return toAjax(carListService.deleteWithValidByIds(List.of(ids), true));
    }

}
