package cn.xianlink.order.controller.sup;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.xianlink.basic.api.RemoteSupplierService;
import cn.xianlink.common.api.enums.order.OrderBusinessTypeEnum;
import cn.xianlink.common.api.enums.order.OrderDeliverSourceEnum;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.log.annotation.Log;
import cn.xianlink.common.log.enums.BusinessType;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.controller.CustomToolService;
import cn.xianlink.order.controller.region.RegionDeliveryAffairService;
import cn.xianlink.order.domain.delivery.bo.*;
import cn.xianlink.order.domain.delivery.vo.SupDeliveryVo;
import cn.xianlink.order.domain.delivery.vo.SupWaitDeliveryGoodsVo;
import cn.xianlink.order.domain.entruck.bo.RwEntruckGoodsOrderQueryBo;
import cn.xianlink.order.domain.entruck.vo.RwEntruckGoodsOrderSimplifyVo;
import cn.xianlink.order.service.IRwEntruckGoodsService;
import cn.xianlink.order.service.ISupDeliveryGoodsService;
import cn.xianlink.order.service.ISupDeliveryService;
import cn.xianlink.system.api.RemoteDeptService;
import cn.xianlink.system.api.model.LoginUser;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 供应商-送货单
 *
 * <AUTHOR>
 * @date 2024-05-28
 * @folder 供应商端(小程序)/送货单
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/order/sup/delivery")
public class SupDeliveryController extends BaseController {

    private final transient ISupDeliveryService deliveryService;
    private final transient ISupDeliveryGoodsService deliveryGoodsService;
    private final transient SupDeliveryAffairService supDeliveryAffairService;
    private final transient RegionDeliveryAffairService regionDeliveryAffairService;
    private final transient IRwEntruckGoodsService entruckGoodsService;
    private final transient CustomToolService customToolService;
    @DubboReference
    private final transient RemoteSupplierService remoteSupplierService;
    @DubboReference
    private final transient RemoteDeptService remoteDeptService;
    /**
     * 首页-待送货列表-计数(待送货件数合计)
     */
    @GetMapping("/waitDeliveryCount/{regionWhId}")
    public R<Integer> waitDeliveryCount(@NotNull(message = "总仓id不能为空") @PathVariable Long regionWhId) {
        LoginUser user = LoginHelper.getLoginUser();
        SupWaitDeliveryQueryBo bo = new SupWaitDeliveryQueryBo();
        bo.setRegionWhId(regionWhId);
        bo.setSaleDate(customToolService.getLastSaleDate(bo.getRegionWhId(), null));
        bo.setSupplierId(user.getRelationId());
        if (user.getDeptId() != 0) {
            bo.setSupplierDeptId(user.getDeptId());
        }
        bo.setSource(OrderDeliverSourceEnum.SUPPLIER.getCode());
        bo.setBusinessTypes(ListUtil.toList(OrderBusinessTypeEnum.BUSINESS_TYPE1.getCode()));
        bo.setPassAudit(1);
        List<SupWaitDeliveryGoodsVo> list = supDeliveryAffairService.waitDeliveryList(bo,true);
        return R.ok(list.stream().mapToInt(SupWaitDeliveryGoodsVo::getWaitDeliveryQuantity).sum());
    }
//    /**
//     * 首页-暂不送货列表-计数(暂不送货件数合计)
//     */
//    @GetMapping("/notDeliveryCount/{regionWhId}")
//    public R<Integer> notDeliveryCount(@NotNull(message = "总仓id不能为空") @PathVariable Long regionWhId) {
//        LoginUser user = LoginHelper.getLoginUser();
//        SupWaitDeliveryQueryBo bo = new SupWaitDeliveryQueryBo();
//        bo.setRegionWhId(regionWhId);
//        bo.setSaleDate(customToolService.getLastSaleDate(bo.getRegionWhId(), null));
//        bo.setSupplierId(user.getRelationId());
//        if (user.getDeptId() != 0) {
//            bo.setSupplierDeptId(user.getDeptId());
//        }
//        bo.setSource(OrderDeliverSourceEnum.SUPPLIER.getCode());
//        bo.setBusinessTypes(ListUtil.toList(OrderBusinessTypeEnum.BUSINESS_TYPE1.getCode()));
//        bo.setPassAudit(1);
//        List<SupWaitDeliveryGoodsVo> list = supDeliveryAffairService.waitDeliveryList(bo,false);
//        return R.ok(list.stream().mapToInt(SupWaitDeliveryGoodsVo::getWaitDeliveryQuantity).sum());
//    }
//    /**
//     * 首页-送货单-计数(差异待处理单据数)
//     */
//    @GetMapping("/deliveryCount/{regionWhId}")
//    public R<Long> deliveryCount(@NotNull(message = "总仓id不能为空") @PathVariable Long regionWhId) {
//        LoginUser user = LoginHelper.getLoginUser();
//        SupDeliveryQueryBo bo = new SupDeliveryQueryBo();
//        bo.setRegionWhId(regionWhId);
//        bo.setSaleDate(customToolService.getLastSaleDate(bo.getRegionWhId(), null));
//        bo.setSupplierId(user.getRelationId());
//        if (user.getDeptId() != 0) {
//            bo.setSupplierDeptId(user.getDeptId());
//        }
//        return R.ok(deliveryService.deliveryCount(bo));
//    }

    /**
     * 查询-待送货列表
     */
    @PostMapping("/waitDeliveryList")
    public R<List<SupWaitDeliveryGoodsVo>> waitDeliveryList(@Validated @RequestBody SupWaitDeliveryQueryBo bo) {
        LoginUser user = LoginHelper.getLoginUser();
        bo.setSaleDate(customToolService.getLastSaleDate(bo.getRegionWhId(), bo.getSaleDate()));
        bo.setSupplierId(user.getRelationId());
        if (user.getDeptId() != 0) {
            bo.setSupplierDeptId(user.getDeptId());
        }
        bo.setSource(OrderDeliverSourceEnum.SUPPLIER.getCode());
        bo.setBusinessTypes(ListUtil.toList(OrderBusinessTypeEnum.BUSINESS_TYPE1.getCode()));
        bo.setPassAudit(1);
        return R.ok(supDeliveryAffairService.waitDeliveryList(bo,true));
    }

    /**
     * 查询-暂不送货列表
     */
    @PostMapping("/notDeliveryList")
    public R<List<SupWaitDeliveryGoodsVo>> notDeliveryList(@Validated @RequestBody SupWaitDeliveryQueryBo bo) {
        LoginUser user = LoginHelper.getLoginUser();
        bo.setSaleDate(customToolService.getLastSaleDate(bo.getRegionWhId(), bo.getSaleDate()));
        bo.setSupplierId(user.getRelationId());
        if (user.getDeptId() != 0) {
            bo.setSupplierDeptId(user.getDeptId());
        }
        bo.setSource(OrderDeliverSourceEnum.SUPPLIER.getCode());
        bo.setBusinessTypes(ListUtil.toList(OrderBusinessTypeEnum.BUSINESS_TYPE1.getCode()));
        bo.setPassAudit(1);
        return R.ok(supDeliveryAffairService.waitDeliveryList(bo,false));
    }

    /**
     * 查询-送货单列表
     */
    @PostMapping("/page")
    public R<TableDataInfo<SupDeliveryVo>> page(@Validated @RequestBody SupDeliveryQueryBo bo) {
        LoginUser user = LoginHelper.getLoginUser();
        bo.setSaleDate(customToolService.getLastSaleDate(bo.getRegionWhId(), bo.getSaleDate()));
        bo.setSupplierId(user.getRelationId());
        if (user.getDeptId() != 0) {
            bo.setSupplierDeptId(user.getDeptId());
        }
        if (StrUtil.isNotBlank(bo.getSpuName())) {
            List<Long> deliveryIds = deliveryGoodsService.deliveryIdListBySpuName(bo);
            if (deliveryIds.size() == 0) {
                return R.ok(TableDataInfo.build());
            }
            bo.setDeliveryIds(deliveryIds);
        }
        return R.ok(deliveryService.pageList(bo));
    }

    /**
     * 查询-送货单详情
     */
    @GetMapping("/info/{deliveryId}")
    public R<SupDeliveryVo> info(@NotNull(message = "主键不能为空") @PathVariable Long deliveryId) {
        return R.ok(deliveryService.queryById(deliveryId));
    }

    /**
     * 创建-送货单&装车记录
     */
    @Log(title = "创建-送货单&装车记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/create")
    public R<SupDeliveryAddBo> create(@Validated @RequestBody SupDeliveryAddBo bo) {
        LoginUser user = LoginHelper.getLoginUser();
        bo.setSupplierId(user.getRelationId());
        Map<Long, List<SupDeliveryGoodsAddBo>> deptGoodsListMap = bo.getGoodsList().stream().collect(Collectors.groupingBy(SupDeliveryGoodsAddBo::getSupplierDeptId, Collectors.toList()));
        if (user.getDeptId() > 0) {
            // 档口送货
            if (bo.getGoodsList().size() != deptGoodsListMap.getOrDefault(user.getDeptId(), new ArrayList<>()).size()) {
                return R.warn("档口只能送自己的商品-"+user.getDeptId());
            }
            bo.setSupplierDeptId(user.getDeptId());
        } else {
            // 供应商送货
            if (deptGoodsListMap.size() > 1) {
                return R.warn("不可以混合送货，只能送供应商或者单个档口的货-"+deptGoodsListMap.keySet());
            }
            bo.setSupplierDeptId(bo.getGoodsList().get(0).getSupplierDeptId());
        }
        bo.setSupplierName(remoteSupplierService.getSupplierById(bo.getSupplierId()).getName());
        if (bo.getSupplierDeptId() != null && bo.getSupplierDeptId() > 0) {
            bo.setSupplierDeptName(remoteDeptService.selectDeptNameByIds(bo.getSupplierDeptId().toString()));
        }
        supDeliveryAffairService.createDelivery(bo, null);
        SupDeliveryAddBo data = new SupDeliveryAddBo();
        data.setId(bo.getId());
        data.setDeliveryNo(bo.getDeliveryNo());
//        if (bo.getStatus().intValue() == DeliveryStatusEnum.COMPLETE_INSPECT.getCode()) {
//            // 直属城市仓物流 自动装车完成，自动接货
//            regionDeliveryAffairService.affiliatedAutoEntruckAndReceive(bo.getId());
//        }
        return R.ok(data);
    }


    /**
     * 取消送货单
     */
    @Log(title = "取消", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/cancel")
    public R<Void> cancel(@Validated @RequestBody SupDeliveryCancelBo bo) {
        return toAjax(supDeliveryAffairService.cancelDelivery(bo, OrderDeliverSourceEnum.SUPPLIER.getCode()));
    }

    /**
     * 打印送货单
     */
    @Log(title = "打印", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/print/{deliveryId}")
    public R<Void> print(@NotNull(message = "主键不能为空") @PathVariable Long deliveryId) {
        return toAjax(deliveryService.print(deliveryId));
    }

    /**
     * 差异处理-差异商品列表查询
     */
    @PostMapping("/diffGoodsList/{deliveryId}")
    public R<SupDeliveryVo> diffGoodsList(@NotNull(message = "主键不能为空") @PathVariable Long deliveryId) {
        SupDeliveryVo vo = deliveryService.selectAndCheckNullById(deliveryId);
        customToolService.loadDiffDealTimestamp(vo);
        vo.setDiffGoodsList(entruckGoodsService.customSumListByDeliveryId(vo.getId()));
        vo.getDiffGoodsList().forEach(g -> {
            g.setSupplierDeptName(vo.getSupplierDeptName());
            g.setBusinessType(vo.getBusinessType());
        });
        return R.ok(vo);
    }

    /**
     * 差异处理-提交
     */
    @Log(title = "已装车-差异处理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/diffHandle")
    public R<Void> diffHandle(@Validated @RequestBody SupDeliveryDiffBo bo) {
        Map<Long, Integer> diffStatusMap = bo.getGoodsList().stream().collect(Collectors.toMap(SupDeliveryDiffGoodsBo::getSupplierSkuId, SupDeliveryDiffGoodsBo::getDiffStatus, (key1, key2) -> key2));
        return toAjax(supDeliveryAffairService.diffHandle(1, bo.getDeliveryId(), diffStatusMap));
    }

    /**
     * 二维码扫码校验（装车）
     * @return
     */
    @PostMapping("/scanCodeCheck")
    public R<String> scanCodeCheck(@Validated @RequestBody SupDeliveryScanCodeCheckBo bo) {
        if (ObjectUtil.isNull(bo.getRegionWhId())) {
            throw new ServiceException("总仓id不能为空");
        }
        return R.ok("成功", regionDeliveryAffairService.scanCodeCheck(bo));
    }


    /**
     * 送货单商品标签打印查询
     */
    @PostMapping("/getDeliveryGoodsOrderLabelList")
    public R<List<RwEntruckGoodsOrderSimplifyVo>> getDeliveryGoodsOrderLabelList(@RequestBody RwEntruckGoodsOrderQueryBo bo) {
        if (ObjectUtil.isNull(bo.getDeliveryId())) {
            return R.warn("送货单id不能为空");
        }
        return R.ok(entruckGoodsService.getDeliveryGoodsOrderLabelSimplifyList(bo));
    }
}
