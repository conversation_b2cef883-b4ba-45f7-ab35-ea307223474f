package cn.xianlink.order.domain.vo.platform;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 客户端未开票订单项视图对象
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@ExcelIgnoreUnannotated
public class ClientUnInvoicedItemVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单项ID
     */
    @ExcelProperty(value = "订单项ID")
    private Long orderItemId;

    /**
     * 订单ID
     */
    @ExcelProperty(value = "订单ID")
    private Long orderId;

    /**
     * 订单编码
     */
    @ExcelProperty(value = "订单编码")
    private String orderCode;

    /**
     * 商品名称
     */
    @ExcelProperty(value = "商品名称")
    private String spuName;

    /**
     * 商品规格
     */
    @ExcelProperty(value = "商品规格")
    private String skuSpec;

    /**
     * 数量
     */
    @ExcelProperty(value = "数量")
    private Integer count;

    /**
     * 单价
     */
    @ExcelProperty(value = "单价")
    private BigDecimal price;

    /**
     * 最终价格
     */
    @ExcelProperty(value = "最终价格")
    private BigDecimal finalPrice;

    /**
     * 实付金额
     */
    @ExcelProperty(value = "实付金额")
    private BigDecimal actualAmount;

    /**
     * 逆向金额
     */
    @ExcelProperty(value = "逆向金额")
    private BigDecimal reverseAmount;

    /**
     * 可开票金额
     */
    @ExcelProperty(value = "可开票金额")
    private BigDecimal invoiceableAmount;

    /**
     * 订单时间
     */
    @ExcelProperty(value = "订单时间")
    private Date orderTime;

    /**
     * 支付时间
     */
    @ExcelProperty(value = "支付时间")
    private Date payTime;

    /**
     * 供应商ID
     */
    @ExcelProperty(value = "供应商ID")
    private Long supplierId;

    /**
     * 供应商名称
     */
    @ExcelProperty(value = "供应商名称")
    private String supplierName;

    /**
     * 是否已开票
     */
    @ExcelProperty(value = "是否已开票")
    private Boolean isInvoiced;
}
