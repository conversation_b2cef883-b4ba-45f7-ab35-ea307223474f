package cn.xianlink.order.controller.region;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.xianlink.basic.api.RemoteRegionWhService;
import cn.xianlink.common.api.enums.order.DeliveryStatusEnum;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.log.annotation.Log;
import cn.xianlink.common.log.enums.BusinessType;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.controller.CustomToolService;
import cn.xianlink.order.domain.RwDepart;
import cn.xianlink.order.domain.depart.bo.RwDepartAddBo;
import cn.xianlink.order.domain.depart.bo.RwDepartEditBo;
import cn.xianlink.order.domain.depart.bo.RwDepartEditRemarBo;
import cn.xianlink.order.domain.depart.bo.RwDepartQueryBo;
import cn.xianlink.order.domain.depart.vo.RwDepartLogisticsVo;
import cn.xianlink.order.domain.depart.vo.RwDepartVo;
import cn.xianlink.order.domain.entruck.bo.RwEntruckQueryBo;
import cn.xianlink.order.domain.entruck.vo.RwEntruckVo;
import cn.xianlink.order.service.IRwDepartService;
import cn.xianlink.order.service.IRwEntruckService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 总仓-发车单
 *
 * <AUTHOR>
 * @date 2024-05-28
 * @folder 总仓助手(小程序)/发车单
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/order/region/depart")
public class RegionDepartController extends BaseController {

    private final transient IRwDepartService departService;
    private final transient IRwEntruckService entruckService;
    private final transient RegionDepartAffairService regionDepartAffairService;
    private final transient CustomToolService customToolService;
    @DubboReference
    private final transient RemoteRegionWhService remoteRegionWhService;

//    /**
//     * 首页-发车单-计数(待接车单据数)
//     */
//    @GetMapping("/departCount/{regionWhId}")
//    public R<Long> departCount(@NotNull(message = "总仓id不能为空") @PathVariable Long regionWhId) {
//        RwEntruckQueryBo bo = new RwEntruckQueryBo();
//        bo.setRegionWhId(regionWhId);
//        bo.setArrivalDate(LocalDate.now());
//        return R.ok(entruckService.entruckCount(bo, DeliveryStatusEnum.COMPLETE_DEPART.getCode()));
//    }

    /**
     * 查询-已接车-装车单列表
     */
    @PostMapping("/entruckReceiveList")
    public R<List<RwEntruckVo>> entruckReceiveList(@Validated @RequestBody RwEntruckQueryBo bo) {
        if (bo.getReceiveDate() == null) {
            return R.warn("接车日期不能为空");
        }
        return R.ok(entruckService.customListByCityWhIdAndRegionId(bo.getCityWhId(), bo.getReceiveDate(), bo.getRegionWhId()));
    }

    /**
     * 查询-发车单列表
     */
    @PostMapping("/page")
    public R<TableDataInfo<RwEntruckVo>> page(@Validated @RequestBody RwEntruckQueryBo bo) {
        if (ObjectUtil.isNull(bo.getRegionWhId())) {
            return R.warn("总仓id不能为空");
        }
        bo.setSaleDate(customToolService.getLastSaleDate(bo.getRegionWhId(), bo.getSaleDate()));
        // 查全部
        if (CollectionUtil.isEmpty(bo.getStatusList())) {
            bo.setStatusList(CollectionUtil.toList(DeliveryStatusEnum.WAIT_DEPART.getCode(),
                    DeliveryStatusEnum.COMPLETE_DEPART.getCode(),
                    DeliveryStatusEnum.COMPLETE_RECEIVE.getCode()));
        }
        return R.ok(entruckService.customPageList(bo));
    }

    /**
     * 查询-发车单列表
     */
    @PostMapping("/carPage")
    public R<TableDataInfo<RwDepartVo>> carPage(@Validated @RequestBody RwDepartQueryBo bo) {
        if (ObjectUtil.isNull(bo.getRegionWhId())) {
            return R.warn("总仓id不能为空");
        }
        bo.setSaleDate(customToolService.getLastSaleDate(bo.getRegionWhId(), bo.getSaleDate()));
        // 查全部
        if (CollectionUtil.isEmpty(bo.getStatusList())) {
            bo.setStatusList(CollectionUtil.toList(DeliveryStatusEnum.WAIT_DEPART.getCode(),
                    DeliveryStatusEnum.COMPLETE_DEPART.getCode(),
                    DeliveryStatusEnum.COMPLETE_RECEIVE.getCode()));
        }
        return R.ok(departService.carPageList(bo));
    }

    /**
     * 查询发车单
     */
    @GetMapping("/carInfo/{departId}")
    public R<RwDepartVo> carInfo(@NotNull(message = "主键不能为空") @PathVariable Long departId) {
        RwDepartVo vo = departService.queryById(departId);
        if (vo.getStatus().intValue() != DeliveryStatusEnum.WAIT_DEPART.getCode()) {
            vo.getLogisticsList().forEach(e -> e.setIsRelatedEntruck(true));
        } else {
            Map<Long, RwEntruckVo> entruckMap = entruckService.queryByDepartId(vo.getId())
                    .stream().collect(Collectors.toMap(RwEntruckVo::getLogisticsId, Function.identity(), (key1, key2) -> key2));
            for (RwDepartLogisticsVo logVo : vo.getLogisticsList()) {
                logVo.setIsRelatedEntruck(entruckMap.containsKey(logVo.getLogisticsId()));
                if (logVo.getLogisticsId().intValue() == vo.getLogisticsId().intValue()) {
                    logVo.setIsRelatedEntruck(true);
                }
            }
        }
        return R.ok(vo);
    }

    /**
     * 查询发车单+装车单明细
     */
    @GetMapping("/info/{departId}")
    public R<RwDepartVo> info(@NotNull(message = "主键不能为空") @PathVariable Long departId) {
        RwDepartVo vo = departService.queryById(departId);
        List<RwEntruckVo> entruckVos = entruckService.queryByDepartId(vo.getId());
        if (vo.getStatus().intValue() != DeliveryStatusEnum.WAIT_DEPART.getCode()) {
            vo.setEntruckList(entruckVos);
            vo.getEntruckList().forEach(e -> e.setIsRelatedEntruck(true));
        } else {
            vo.setEntruckList(new ArrayList<>());
            Map<Long, RwEntruckVo> entruckMap = entruckVos.stream().collect(Collectors.toMap(RwEntruckVo::getLogisticsId, Function.identity(), (key1, key2) -> key2));
            for (RwDepartLogisticsVo logVo : vo.getLogisticsList()) {
                RwEntruckVo entruckVo = entruckMap.getOrDefault(logVo.getLogisticsId(), new RwEntruckVo());
                if (entruckVo.getId() == null) {
                    BeanUtils.copyProperties(logVo, entruckVo);
                    entruckVo.setId(null);
                }
                entruckVo.setIsRelatedEntruck(entruckMap.containsKey(entruckVo.getLogisticsId()));
                if (entruckVo.getLogisticsId().intValue() == vo.getLogisticsId().intValue()) {
                    entruckVo.setIsRelatedEntruck(true);
                }
                vo.getEntruckList().add(entruckVo);
            }
        }
        vo.setLogisticsList(null);
        return R.ok(vo);
    }

    /**
     * 创建发车单
     */
    @Log(title = "创建发车单", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/create")
    public R<Long> create(@Validated @RequestBody RwDepartAddBo bo) {
//        LocalDate saleDate = customToolService.getLastSaleDate(bo.getRegionWhId(), null);
//        if (bo.getSaleDate().compareTo(saleDate) <= 0) {
//            return R.warn("只允许创建下一销售日期车辆，当前销售日期：" + saleDate + "");
//        }
        departService.insert(bo);
        return R.ok();
    }

    /**
     * 发车
     */
    @Log(title = "发车", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/departCar")
    public R<Long> departCar(@Validated @RequestBody RwDepartEditBo bo) {
        regionDepartAffairService.departCar(bo);
        return R.ok(bo.getId());
    }

    /**
     * 修改发车单
     */
    @Log(title = "修改发车单", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/update")
    public R<Void> update(@Validated @RequestBody RwDepartEditBo bo) {
        regionDepartAffairService.updateDepart(bo);
        return R.ok();
    }

    /**
     * 修改发车单-备注
     */
    @Log(title = "修改发车单-备注", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/updateRemark")
    public R<Void> updateRemark(@Validated @RequestBody RwDepartEditRemarBo bo) {
        RwDepart entity = new RwDepart();
        entity.setId(bo.getId());
        entity.setRemark(bo.getRemark() != null ? bo.getRemark() : "");
        departService.update(entity);
        return R.ok();
    }

    /**
     * 删除发车单
     */
    @Log(title = "删除发车单", businessType = BusinessType.DELETE)
    @PostMapping("/delete/{id}")
    public R<Void> delete(@NotNull(message = "发车单id不能为空") @PathVariable Long id) {
        return toAjax(departService.deleteById(id));
    }
}
