package cn.xianlink.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.xianlink.basic.api.RemoteBasCustomerService;
import cn.xianlink.basic.api.RemoteCityWhService;
import cn.xianlink.basic.api.RemoteMessageNotifyService;
import cn.xianlink.basic.api.RemoteRegionLogisticsService;
import cn.xianlink.basic.api.RemoteRegionWhService;
import cn.xianlink.basic.api.RemoteSupplierService;
import cn.xianlink.basic.api.domain.bo.RemoteMessageNotifyV2Bo;
import cn.xianlink.basic.api.domain.bo.RemoteRegionLogisticsQueryBo;
import cn.xianlink.basic.api.domain.vo.RemoteCityWhVo;
import cn.xianlink.basic.api.domain.vo.RemoteCustomerVo;
import cn.xianlink.basic.api.domain.vo.RemoteRegionLogisticsVo;
import cn.xianlink.basic.api.domain.vo.RemoteRegionWhVo;
import cn.xianlink.basic.api.domain.vo.RemoteSupplierVo;
import cn.xianlink.basic.api.enums.MsgNotifyTemplateV2Enum;
import cn.xianlink.basic.api.enums.NotifyObjectTypeEnum;
import cn.xianlink.basic.api.enums.OssBusinessTypeEnum;
import cn.xianlink.basic.api.enums.OssTagEnum;
import cn.xianlink.common.api.constant.BanguoCommonConstant;
import cn.xianlink.common.api.enums.order.*;
import cn.xianlink.common.api.enums.system.SysUserTypeEnum;
import cn.xianlink.common.api.util.NoUtil;
import cn.xianlink.common.api.vo.RemoteSupplierSkuFileVo;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.core.utils.SpringUtils;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.redis.utils.RedisUtils;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.order.api.bo.DeductionInfoParamBo;
import cn.xianlink.order.api.bo.RemoteSupAccTransBo;
import cn.xianlink.order.api.bo.RemoteSupAccTransQueryBo;
import cn.xianlink.order.api.constant.AmountTypeEnum;
import cn.xianlink.order.api.constant.BillTypeEnum;
import cn.xianlink.order.api.constant.CreateTypeEnum;
import cn.xianlink.order.api.constant.DeductionInfoEnum;
import cn.xianlink.order.api.constant.DeductionInfoStatusEnum;
import cn.xianlink.order.api.constant.DeductionTypeEnum;
import cn.xianlink.order.api.vo.RemoteOrderBusiAmtVo;
import cn.xianlink.order.api.vo.RemoteSupTransRefundLossRecordVo;
import cn.xianlink.order.api.vo.RemoteSupTransRefundLossVo;
import cn.xianlink.order.domain.*;
import cn.xianlink.order.domain.bo.InvoiceItemBo;
import cn.xianlink.order.domain.bo.refundRecord.CreateRefundRecordBO;
import cn.xianlink.order.domain.bo.report.ReportLossDelayCheckBo;
import cn.xianlink.order.domain.bo.report.ReportLossMiniListSearchBo;
import cn.xianlink.order.domain.bo.report.ReportLossOrderBo;
import cn.xianlink.order.domain.bo.report.ReportLossOrderFlowBo;
import cn.xianlink.order.domain.bo.report.ReportLossPcListSearchBo;
import cn.xianlink.order.domain.im.bo.ImBo;
import cn.xianlink.order.domain.operation.ReportRefundDto;
import cn.xianlink.order.domain.vo.InvoiceItemVo;
import cn.xianlink.order.domain.vo.order.AdminOrderInfoVo;
import cn.xianlink.order.domain.vo.refundRecord.RefundRecordVO;
import cn.xianlink.order.domain.vo.report.ReportLossItemInfoVo;
import cn.xianlink.order.domain.vo.report.ReportLossMiniDetailVo;
import cn.xianlink.order.domain.vo.report.ReportLossMiniListVo;
import cn.xianlink.order.domain.vo.report.ReportLossOrderFlowVo;
import cn.xianlink.order.domain.vo.report.ReportLossOrderVo;
import cn.xianlink.order.domain.vo.report.ReportLossPcListVo;
import cn.xianlink.order.domain.vo.report.ReportNumStatisticsVo;
import cn.xianlink.order.enums.BizTypeEnum;
import cn.xianlink.order.enums.OccupyEnum;
import cn.xianlink.order.enums.ResponsibilityTypeEnum;
import cn.xianlink.order.mapper.*;
import cn.xianlink.order.mq.producer.ReportLossCheckProducer;
import cn.xianlink.order.service.*;
import cn.xianlink.order.service.im.IImService;
import cn.xianlink.product.api.RemoteSupplierSkuService;
import cn.xianlink.product.api.domain.bo.RemoteQueryInfoListBo;
import cn.xianlink.product.api.domain.bo.RemoteQuerySupplierSkuFileBo;
import cn.xianlink.product.api.domain.vo.RemoteSupplierSkuInfoVo;
import cn.xianlink.resource.api.RemoteFileService;
import cn.xianlink.resource.api.domain.RemoteOssBo;
import cn.xianlink.resource.api.domain.RemoteOssVo;
import cn.xianlink.system.api.RemoteDeptService;
import cn.xianlink.system.api.RemoteImAfterSalesService;
import cn.xianlink.system.api.RemoteImGroupService;
import cn.xianlink.system.api.RemoteUserService;
import cn.xianlink.system.api.domain.bo.RemoteImUserBo;
import cn.xianlink.system.api.domain.bo.RemoteUserBo;
import cn.xianlink.system.api.domain.vo.RemoteImAfterSalesVo;
import cn.xianlink.system.api.domain.vo.RemoteImGroupVo;
import cn.xianlink.system.api.model.LoginUser;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.xianlink.system.api.domain.bo.RemoteImUserBo.buildImUser;

/**
 * <AUTHOR> xiaodaibing on 2024-06-13 15:44
 */
@RequiredArgsConstructor
@Service
@CustomLog
public class ReportLossServiceImpl implements IReportLossService {

    private final ReportLossOrderMapper lossOrderMapper;
    private final ReportLossOrderFlowMapper lossOrderFlowMapper;
    private final OrderItemMapper orderItemMapper;
    private final ReceiveGoodsRecordDetailMapper receiveGoodsRecordDetailMapper;
    private final RwDepartMapper rwDepartMapper;
    private final IDeductionInfoService deductionInfoService;
    private final IOrderItemService orderItemService;
    private final ICustomerStatisticsService customerStatisticsService;
    private final RefundRecordMapper refundRecordMapper;
    private final RefundProductDetailMapper refundProductDetailMapper;
    private final StockoutRecordDetailMapper stockoutRecordDetailMapper;
    private final SupplierSaleSettleDateMapper supplySaleSettleDateMapper;
    private final OrderMapper orderMapper;
    private final SortGoodsDetailMapper sortGoodsDetailMapper;
    private final IImService imService;
    private IRefundRecordService refundRecordService;
    @DubboReference
    private final RemoteDeptService remoteDeptService;

    private IOrderService orderService;


    @DubboReference
    private RemoteRegionWhService remoteRegionWhService;
    @DubboReference
    private RemoteCityWhService remoteCityWhService;
    @DubboReference
    private RemoteFileService remoteFileService;
    @DubboReference
    private RemoteBasCustomerService remoteBasCustomerService;

    @DubboReference
    private RemoteSupplierService remoteSupplierService;
    @DubboReference
    private RemoteUserService remoteUserService;
    @DubboReference
    private RemoteSupplierSkuService remoteSupplierSkuService;
    @DubboReference
    private RemoteMessageNotifyService remoteMessageNotifyService;
    @DubboReference
    private RemoteRegionLogisticsService remoteRegionLogisticsService;
    @DubboReference
    private RemoteImGroupService remoteImGroupService;
    @DubboReference
    private RemoteImAfterSalesService imAfterSalesService;
    private final IInvoiceItemService invoiceItemService;
    // 自动审核时间 小时
    private static final Integer AUTO_PASS_TIME = 24;

    @Autowired
    public void setIRefundRecordService(@Lazy IRefundRecordService refundRecordService) {
        this.refundRecordService = refundRecordService;
    }

    @Autowired
    public void setIOrderService(@Lazy IOrderService orderService) {
        this.orderService = orderService;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void create(ReportLossOrderBo bo) {
        //校验报损金额是否超过最大提货实付金额
        if (bo.getLossAmount().compareTo(BigDecimal.ZERO) < 1) {
            throw new ServiceException("报损金额不能小于等于0元");
        }
        // 前端改字段 skuId -> supplierSkuId 之前也可以兼容
        OrderItem item = orderItemMapper.queryItemBySkuId(bo.getOrderId(), ObjectUtil.defaultIfNull(bo.getSupplierSkuId(), bo.getSkuId()));
        if (item.getAfterSaleDay() != null && item.getAfterSaleDay() == 0) {
            throw new ServiceException("该商品不能发起售后");
        }
        var rds = receiveGoodsRecordDetailMapper.selectByItems(Lists.newArrayList(item.getId()));
        if (rds.isEmpty()) {
            throw new ServiceException("该商品未提货");
        }
        ReceiveGoodsRecordDetail receiptDetail = rds.get(0);
        if (receiptDetail == null || !receiptDetail.getAfterSaleStatus().equals(AfterSaleStatusEnum.CAN_AFTER_SALE.getCode())) {
            throw new ServiceException("提货异常，该商品不能发起售后");
        }
        //获取分货
        SortGoodsDetail sortGoodsDetail = sortGoodsDetailMapper.selectByOrderItemId(item.getId());
        //校验是否产生差额退
        if (bo.getLossWeight() == null) {
            bo.setLossWeight(BigDecimal.ZERO);
        }
        //实际客户付款金额
        BigDecimal payment = orderService.checkLossAmount(item.getId(), bo.getLossWeight(), bo.getLossAmount());

        //同步修改提货单的售后状态，不能再次售后
        receiveGoodsRecordDetailMapper.updateAfterSale(item.getId(), AfterSaleStatusEnum.HAS_AFTER_SALE.getCode());
        //修改订单商品售后状态
        this.updateOrderItemAfterSale(item.getId(), ItemAfterSaleStatusEnum.REPORT_LOSS_HAND.getCode());
        Order order = orderMapper.selectById(bo.getOrderId());

        RemoteRegionWhVo regionWhVo = remoteRegionWhService.queryById(item.getRegionWhId());
        RemoteCityWhVo cityWhVo = remoteCityWhService.queryById(item.getCityWhId());

        ReportLossOrder lossOrder = MapstructUtils.convert(bo, ReportLossOrder.class);
        lossOrder.setReportLossNo(this.reportLossNo());
        lossOrder.setLossStatus(ReportLossStatusEnum.WAIT_AUDIT.getCode());
        lossOrder.setSkuId(item.getSkuId());
        lossOrder.setSupplierSkuId(item.getSupplierSkuId());
        lossOrder.setOrderItemId(item.getId());
        lossOrder.setCustomerId(item.getCustomerId());
        lossOrder.setOrderUserCode(item.getCreateCode());
        lossOrder.setSupplierId(item.getSupplierId());
        lossOrder.setOrderNo(item.getOrderCode());
        lossOrder.setSkuImgUrl(item.getImgUrl());
        lossOrder.setSpuName(item.getSpuName());
        lossOrder.setSpuId(item.getSpuId());
        lossOrder.setSupplierDeptId(item.getSupplierDeptId());
        lossOrder.setSkuAmount(item.getProductAmount());
        lossOrder.setSkuNumber(receiptDetail.getActualCount());
        lossOrder.setSubstituteAmount(item.getPlatformServiceAmount());
        lossOrder.setFreightAmount(item.getFreightTotalAmount().add(item.getBaseFreightAmount()));
        //计算申请报损金额，相对应的补贴金额
        lossOrder.setLossSubsidyFreeAmount(this.calculateSubsidyAmount(payment, item, bo.getLossAmount()));
//        lossOrder.setFinancialAmount(item.getFinancialServiceAmount());
//        lossOrder.setDeliveryDate();
        lossOrder.setSaleDate(item.getSaleDate());
        lossOrder.setDepartDate(rwDepartMapper.selectDepartTimeBySkuId(item.getSupplierSkuId()));
        lossOrder.setSkuPrice(bo.getSkuPrice());
        lossOrder.setSpuGrossWeight(item.getSpuGrossWeight());
        lossOrder.setSpuNetWeight(item.getSpuNetWeight());
        lossOrder.setCityWhId(cityWhVo.getId());
        lossOrder.setCityWhName(cityWhVo.getName());
        lossOrder.setRegionWhId(regionWhVo.getId());
        lossOrder.setRegionWhName(regionWhVo.getRegionWhName());
        lossOrder.setExpireTime(DateUtil.offsetHour(new Date(), AUTO_PASS_TIME).toJdkDate());
        lossOrder.setRemark(bo.getRemark());
        lossOrder.setPlaceId(order.getPlaceId());
        lossOrder.setPlaceIdLevel2(order.getPlaceIdLevel2());
        lossOrderMapper.insert(lossOrder);
        //插入默认流程
        this.insertDefaultFLows(lossOrder, item, receiptDetail, cityWhVo, sortGoodsDetail);

        ReportLossOrderFlow flow = new ReportLossOrderFlow();
        flow.setReportLossId(lossOrder.getId());
        flow.setLossStatus(ReportLossStatusEnum.WAIT_AUDIT.getCode());
        flow.setActionRemark(bo.getRemark());
        flow.setLossType(bo.getLossType());
        flow.setLossWeight(bo.getLossWeight());
        flow.setRefundGoodsAmount(bo.getLossAmount());
        flow.setFlowDesc(this.flowCustomerDesc(lossOrder, flow, BigDecimal.ZERO));
//        flow.setRefundSubstituteAmount();
//        flow.setRefundFreightAmount();
//        flow.setRefundFinancialAmount();
        lossOrderFlowMapper.insert(flow);

        //回写当前流程id
        ReportLossOrder upOrder = new ReportLossOrder();
        upOrder.setId(lossOrder.getId());
        upOrder.setFlowId(flow.getId());
        lossOrderMapper.updateById(upOrder);
        //超时未处理，自动退款
        ReportLossCheckProducer.send(new ReportLossDelayCheckBo(lossOrder.getId(), ReportLossStatusEnum.WAIT_AUDIT.getCode(), lossOrder.getExpireTime()), null);
        this.insertOss(bo.getImageUrls(), bo.getVideoUrls(), flow.getId());
        //触发统计
        customerStatisticsService.reportTrigger(lossOrder.getCustomerId());
        //发送短信通知
        try {
            this.sendSms(lossOrder.getId(), BigDecimal.ZERO);
        } catch (ServiceException se) {
            log.keyword("createLossOrder", bo.getOrderId()).warn("手机号未找到，报损的消息通知异常{}", bo);
        }
    }

    /**
     * 插入两条默认流程 下单、分货
     * <AUTHOR> on 2024/9/25:16:07
     * @param lossOrder
     * @param item
     * @param receiptDetail
     * @param cityWhVo
     * @return void
     */
    private void insertDefaultFLows(ReportLossOrder lossOrder, OrderItem item,
                                    ReceiveGoodsRecordDetail receiptDetail, RemoteCityWhVo cityWhVo,
                                    SortGoodsDetail sortGoodsDetail) {
        List<ReportLossOrderFlow> list = new ArrayList<>();

        ReportLossOrderFlow orderFlow = new ReportLossOrderFlow();
        orderFlow.setReportLossId(lossOrder.getId());
        orderFlow.setLossStatus(ReportLossStatusEnum.CREATE_ORDER.getCode());
        orderFlow.setCreateTime(item.getCreateTime());
        list.add(orderFlow);

        Long cityId = receiveGoodsRecordDetailMapper.queryCityId(receiptDetail.getId());
        if (cityId != null) {
            ReportLossOrderFlow cityFlow = new ReportLossOrderFlow();
            cityFlow.setReportLossId(lossOrder.getId());
            cityFlow.setLossStatus(ReportLossStatusEnum.RECEIVE.getCode());
            cityFlow.setFlowDesc(cityWhVo.getName() + ",完成分货");
            cityFlow.setCreateTime(sortGoodsDetail.getUpdateTime());
            list.add(cityFlow);
        }
        lossOrderFlowMapper.insertBatch(list);
    }

    @Lock4j(keys = "#bo.reportLossId")
    @Override
    public void processing(ReportLossOrderFlowBo bo) {
        log.keyword("processing", bo.getOrderItemId()).info("报损处理{}", bo);
        ReportLossOrder lossOrder = lossOrderMapper.selectByItemId(bo.getOrderItemId());
        //状态校验
        if (lossOrder.getLossStatus() >= bo.getLossStatus()) {
            if (ReportLossStatusEnum.COMPLETE.getCode().equals(lossOrder.getLossStatus())) {
                throw new ServiceException("已超过了申诉时间窗口，已不支持发起申诉");
            }
            if (ReportLossStatusEnum.FULL_PASS.getCode().equals(lossOrder.getLossStatus())) {
                throw new ServiceException("超时未处理，已自动全额通过");
            }
            throw new ServiceException("单据状态已变化，请刷新页面");
        }

        ReportLossOrderFlow flow = MapstructUtils.convert(bo, ReportLossOrderFlow.class);
        flow.setLossType(bo.getLossType() != null ? bo.getLossType() : lossOrder.getLossType());
        flow.setLossWeight(bo.getLossWeight() != null ? bo.getLossWeight() : lossOrder.getLossWeight());
        flow.setReportLossId(lossOrder.getId());

        ReportLossOrder upOrder = new ReportLossOrder();
        upOrder.setId(lossOrder.getId());

        upOrder.setLossStatus(bo.getLossStatus());

        OrderItem item = orderItemMapper.selectById(lossOrder.getOrderItemId());
        boolean insertDeduction = false;
        //退款总金额
        BigDecimal refundTotalAmount = new BigDecimal(0);
        //总退款补贴金额
        BigDecimal refundTotalFree = new BigDecimal(0);
        //当前退款补贴
        BigDecimal refundCurrentFree = new BigDecimal(0);;
        if (ReportLossStatusEnum.FULL_PASS.getCode().equals(bo.getLossStatus())) {
            this.checkRefundPrice(lossOrder, lossOrder.getLossAmount(), item);
            //全额通过，客户退款取申请金额
            upOrder.setRefundGoodsAmount(lossOrder.getLossAmount());
            //全额通过，实际退款取客户申请金额减去补贴金额
            upOrder.setRefundGoodsActualAmount(lossOrder.getLossAmount().subtract(lossOrder.getLossSubsidyFreeAmount()));
            upOrder.setRefundTime(DateUtil.date());
            refundTotalAmount = lossOrder.getLossAmount();
            refundTotalFree = lossOrder.getLossSubsidyFreeAmount();
            refundCurrentFree = refundTotalFree;
            orderItemMapper.updateAfterSaleStatus(item.getId(), ItemAfterSaleStatusEnum.REPORT_LOSS_END.getCode());
        } else if (ReportLossStatusEnum.SUBTRACT_PASS.getCode().equals(bo.getLossStatus())) {
            this.checkRefundPrice(lossOrder, bo.getLossAmount(), item);
            upOrder.setRefundGoodsAmount(this.calculateCustomerRefundAmount(bo.getLossAmount(), lossOrder, item));
            upOrder.setRefundGoodsActualAmount(bo.getLossAmount());
            upOrder.setRefundTime(DateUtil.date());
            refundTotalAmount = bo.getLossAmount();
            refundTotalFree = refundTotalAmount.subtract(upOrder.getRefundGoodsAmount());
            refundCurrentFree = refundTotalFree;
            upOrder.setExpireTime(DateUtil.offsetHour(new Date(), AUTO_PASS_TIME).toJdkDate());
            //超时未处理，自动完成
            ReportLossCheckProducer.send(new ReportLossDelayCheckBo(lossOrder.getId(), ReportLossStatusEnum.SUBTRACT_PASS.getCode(), upOrder.getExpireTime()), null);
        } else if (ReportLossStatusEnum.REJECT.getCode().equals(bo.getLossStatus())) {
            upOrder.setExpireTime(DateUtil.offsetHour(new Date(), AUTO_PASS_TIME).toJdkDate());
            //超时未处理，自动完成
            ReportLossCheckProducer.send(new ReportLossDelayCheckBo(lossOrder.getId(), ReportLossStatusEnum.REJECT.getCode(), upOrder.getExpireTime()), null);
        } else if (ReportLossStatusEnum.INTERVENTION.getCode().equals(bo.getLossStatus())) {
            this.checkRefundPrice(lossOrder, bo.getLossAmount(), item);

            upOrder.setRefundTime(DateUtil.date());
            upOrder.setRefundGoodsAmount(this.calculateCustomerRefundAmount(bo.getLossAmount(), lossOrder, item));

            //本次需退款金额 = 本次申请金额 - 供应商实际已退款金额  可能为0
            refundTotalAmount = bo.getLossAmount().subtract(lossOrder.getRefundGoodsActualAmount());
            //如果为0，则意味则不再额外退款，需要立即结算
            if (refundTotalAmount.compareTo(BigDecimal.ZERO) == 0) {
                orderItemService.notifySettle(Lists.newArrayList(lossOrder.getOrderItemId()));
            }

            if (Objects.isNull(bo.getResponsibilityType())) {
                throw new ServiceException("请选择担责方");
            } else if (bo.getResponsibilityType().equals(ResponsibilityTypeEnum.SUPPLIER.getCode())) {
                //如果是供应商的责任，也修改供应商的退款金额,并计算补贴金额
                upOrder.setRefundGoodsActualAmount(bo.getLossAmount());
                if (refundTotalAmount.compareTo(BigDecimal.ZERO) > 0) {
                    //退款总额减去已退商品金额，等于总退补贴金额
                    refundTotalFree = bo.getLossAmount().subtract(upOrder.getRefundGoodsAmount());
                    refundCurrentFree = refundTotalFree.subtract(lossOrder.getRefundSubsidyFreeAmount());
                }
            } else {
                //不是供应商的责任，创建扣款单，把钱从总仓转给供应商
                insertDeduction = true;
            }
            orderItemMapper.updateAfterSaleStatus(item.getId(), ItemAfterSaleStatusEnum.REPORT_LOSS_END.getCode());
        }
        //如果存在需要退的补贴，则要更新报损单
        if (refundCurrentFree.compareTo(BigDecimal.ZERO) > 0) {
            //流程的退补贴金额
            flow.setRefundSubsidyFreeAmount(refundCurrentFree);
            //累计退补贴
            upOrder.setRefundSubsidyFreeAmount(refundTotalFree);
        }
        if (refundTotalAmount.compareTo(BigDecimal.ZERO) > 0) {
            flow.setRefundGoodsAmount(refundTotalAmount.subtract(refundCurrentFree));
        }
        flow.setFlowDesc(this.flowCustomerDesc(lossOrder, flow, refundTotalAmount));
        lossOrderFlowMapper.insert(flow);
        upOrder.setFlowId(flow.getId());
        lossOrderMapper.updateById(upOrder);

        //先插入oss，不报错再退款
        this.insertOss(bo.getImageUrls(), bo.getVideoUrls(), flow.getId());

        log.keyword("processing").info("报损退款金额：{}", refundTotalAmount);
        //退款
        if (refundTotalAmount.compareTo(BigDecimal.ZERO) > 0) {
            List<CreateRefundRecordBO> refundList = new ArrayList<>();
            CreateRefundRecordBO refundBo = new CreateRefundRecordBO();
            refundBo.setSourceCode(lossOrder.getReportLossNo());
            refundBo.setOrderId(lossOrder.getOrderId());
            refundBo.setOrderItemId(item.getId());
            refundBo.setRefundProductAmount(refundTotalAmount.subtract(refundCurrentFree));
            refundBo.setRefundOtherAmount(BigDecimal.ZERO);
            refundBo.setRefundSubsidyFreeAmount(refundCurrentFree);
            refundBo.setType(RefundTypeEnum.REPORT_LOSS.getCode());
            refundList.add(refundBo);
            log.keyword("processing", bo.getOrderItemId()).info("报损退款客户：{} ；补贴:{}", refundBo.getRefundProductAmount(), refundBo.getRefundSubsidyFreeAmount());
            refundRecordService.lossCreateRecord(refundBo, null, lossOrderMapper.selectById(lossOrder.getId()));

            if (insertDeduction) {
                RemoteRegionWhVo regionWhVo = remoteRegionWhService.queryById(lossOrder.getRegionWhId());
                RemoteCityWhVo cityWhVo = remoteCityWhService.queryById(lossOrder.getCityWhId());
                RemoteSupplierVo supplierVo = remoteSupplierService.getSupplierById(lossOrder.getSupplierId());
                log.info("创建扣款单，给供应商垫付的钱补回去");
                //不是供应商的责任，创建扣款单，把钱从总仓转给供应商
                deductionInfoService.insertDeduction(buildDeductionInfoBo(lossOrder, regionWhVo, cityWhVo, flow.getId(), supplierVo, refundTotalAmount, DeductionTypeEnum.KKYY_RL));
            }
        } else {
            // 如果没有退款金额，判断是客服介入的话，需要释放占用
            if (ReportLossStatusEnum.INTERVENTION.getCode().equals(bo.getLossStatus())) {
                refundRecordService.lossCancel(lossOrder.getReportLossNo());
            }
        }
        //发送短信通知
        try {
            this.sendSms(lossOrder.getId(),refundTotalAmount);
        } catch (Exception e) {
            log.error("短信通知失败", e);
        }
        //申诉中和已介入加入配置的客服介入
        try {
            SpringUtils.getBean(ReportLossServiceImpl.class).addImUser(lossOrder.getId());
        } catch (Exception e) {
            log.error("加入配置的客服介入失败", e);
        }
    }

    /**
     * 加入配置的客服介入
     * @param id
     */
    //@Async
    public void addImUser(Long id) {
        //查询报损单
        ReportLossOrder lossOrder = lossOrderMapper.selectById(id);
        if (lossOrder == null) {
            return;
        }
        if (ObjectUtil.equals(ReportLossStatusEnum.APPEAL.getCode(), lossOrder.getLossStatus())
                || ObjectUtil.equals(ReportLossStatusEnum.INTERVENTION.getCode(), lossOrder.getLossStatus())) {
            //查询是否已经有群信息
            RemoteImGroupVo byBiz = remoteImGroupService.getByBiz(BizTypeEnum.LOSSORDER.getType(), lossOrder.getReportLossNo());
            if (byBiz == null) {
                return;
            }
            //查询配置的客服
            List<RemoteImUserBo> userList = new ArrayList<>();
            if (ObjectUtil.isEmpty(lossOrder.getRegionWhId())) {
                return;
            }
            List<RemoteImAfterSalesVo> list = imAfterSalesService.getByUserId(lossOrder.getRegionWhId());
            if (CollectionUtil.isEmpty(list)) {
                return;
            }
            log.keyword("获取总仓客服", lossOrder.getOrderItemId()+","+lossOrder.getRegionWhId()).info("获取总仓客服{}", JSON.toJSONString(list));
            list.forEach(vo -> userList.add(buildImUser(vo.getUserCode(), vo.getUserName())));
            ImBo imBo = new ImBo();
            imBo.setBizId(lossOrder.getReportLossNo());
            imBo.setBizType(BizTypeEnum.ADDMEMBER.getType());
            imBo.setGroupBizType(byBiz.getBizType());
            imBo.setUserList(userList);
            log.keyword("获取总仓客服", lossOrder.getOrderItemId()+","+lossOrder.getRegionWhId()).info("获取总仓客服{}", JSON.toJSONString(userList));
            imService.create(imBo);
        }
    }

    // 校验退款金额，避免因为差额退款导致超退
    private void checkRefundPrice(ReportLossOrder lossOrder, BigDecimal refundAmount, OrderItem item) {
        // 判断最新的单价和报损单价是否一致,校验是否产生差额退
        if (lossOrder.getSkuPrice().compareTo(item.getFinalPrice()) == 0) {
            return;
        }
        BigDecimal maxAmount;
        if (lossOrder.getLossType().equals(ReportLossTypeEnum.WEIGHT.getCode())) {
            BigDecimal difAmount = BigDecimal.ZERO;
            List<RefundProductDetail> refundByItemId = getRefundByItemId(item.getId(),RefundTypeEnum.DIFFERENCE.getCode());
            //查询差额退的退款单
            if(CollectionUtil.isNotEmpty(refundByItemId)) {
                for (RefundProductDetail r : refundByItemId){
                    difAmount = difAmount.add(r.getRefundProductAmount().divide(BigDecimal.valueOf(r.getStockoutCount())));
                }
            }
            //最终单价 = 原价*数量 - 优惠金额
            BigDecimal finalPrice = (item.getProductAmount().subtract(item.getProductFreeAmount())).divide(BigDecimal.valueOf(item.getCount()), 3, RoundingMode.DOWN);
            //报损毛重*商品单价/商品毛重 = 报损毛重对应的最大金额
            maxAmount = lossOrder.getLossWeight().multiply(finalPrice.subtract(difAmount)).divide(item.getSpuGrossWeight(), 2, RoundingMode.DOWN);
        } else {
            //客服提货商品的最终付款金额
            maxAmount = orderService.getCanLossAmount(item.getId()).getLossAmount();
        }
        if (maxAmount.compareTo(refundAmount) < 0) {
            throw new ServiceException("剩余最大可报损金额" + maxAmount + "，请修改");
        }
    }

    private List<RefundProductDetail> getRefundByItemId(Long orderItemId,Integer type) {
        LambdaQueryWrapper<RefundProductDetail> qw = Wrappers.lambdaQuery();
        qw.eq(RefundProductDetail::getOrderItemId, orderItemId).eq(RefundProductDetail::getDelFlag, 0)
                .eq(RefundProductDetail::getRefundType, type);
        qw.ne(RefundProductDetail::getRefundStatus, RefundStatusEnum.REFUND_FAIL.getCode());
        return refundProductDetailMapper.selectList(qw);
    }

    /**
     * 获取最大可报损金额，也就是最新的实付金额，扣除退款金额
     *
     * @param item
     * @return java.math.BigDecimal
     * <AUTHOR> on 2024/8/24:14:37
     */
    private BigDecimal getMaxLossAmount(OrderItem item) {
        //计算可报损金额
        List<RefundProductDetail> refundList = refundRecordService.getRefundByItemIdList(Lists.newArrayList(item.getId()));
        BigDecimal lossAmount = item.getProductAmount().subtract(item.getSubsidyFreeAmount());
        if (refundList != null && refundList.size() > 0) {
            for (RefundProductDetail refundProductDetail : refundList) {
                lossAmount = lossAmount.subtract(refundProductDetail.getRefundProductAmount());
            }
        }
        return lossAmount;
    }

    /**
     * 计算报损金额对应的补贴金额
     *
     * @param payment           实付金额
     * @param refundAmount      报损或退款金额
     * @return java.math.BigDecimal
     * <AUTHOR> on 2024/8/22:15:10
     */
    private BigDecimal calculateSubsidyAmount(BigDecimal payment, OrderItem item, BigDecimal refundAmount) {
        BigDecimal subsidyFreeAmount = item.getSubsidyFreeAmount();
        if (subsidyFreeAmount.compareTo(BigDecimal.ZERO) == 0) {
            return subsidyFreeAmount;
        }
        //如果报损金额等于最终实付款金额
        if (refundAmount.compareTo(payment) == 0) {
            //下单时的实付金额
            BigDecimal lossAmount = item.getProductAmount().subtract(item.getSubsidyFreeAmount());
            if (payment.compareTo(lossAmount) == 0) {
                //如果下单后，没有产生退款，直接返回商品的补贴金额
                return subsidyFreeAmount;
            }
            //有产生退款，则计算当前实付金额和支付时的总支付金额比例，乘以补贴金额，得到当前实付金额对应的补贴金额
            subsidyFreeAmount = payment.multiply(subsidyFreeAmount).divide(lossAmount, 2, RoundingMode.DOWN);
        } else {
            //计算可报损金额
            List<RefundProductDetail> refundList = refundRecordService.getRefundByItemIdList(Lists.newArrayList(item.getId()));
            subsidyFreeAmount = item.getSubsidyFreeAmount();
            if (refundList != null && refundList.size() > 0) {
                for (RefundProductDetail refundProductDetail : refundList) {
                    subsidyFreeAmount = subsidyFreeAmount.subtract(refundProductDetail.getRefundSubsidyFreeAmount());
                }
            }
            subsidyFreeAmount = refundAmount.multiply(subsidyFreeAmount).divide(payment, 2, RoundingMode.DOWN);
        }
        //大于等于一分钱才需要退补贴
        if (subsidyFreeAmount.compareTo(new BigDecimal("0.01")) >= 0) {
            return subsidyFreeAmount;
        } else {
            return BigDecimal.ZERO;
        }
    }

    /**
     * 根据审核的退款金额，计算总退款金额
     *
     * @param refundAmount
     * <AUTHOR> on 2024/8/24:15:08
     */
    private BigDecimal calculateCustomerRefundAmount(BigDecimal refundAmount, ReportLossOrder lossOrder, OrderItem item) {
        //没有补贴金额直接通过
        if (item.getSubsidyFreeAmount().compareTo(BigDecimal.ZERO) == 0 || lossOrder.getLossSubsidyFreeAmount().compareTo(BigDecimal.ZERO) == 0) {
            return refundAmount;
        }
        var lossAllAmount = lossOrder.getLossAmount().add(lossOrder.getLossSubsidyFreeAmount());
        //如果退款金额等于报损总金额，则直接返回客户报损金额
        if (lossAllAmount.compareTo(refundAmount) == 0) {
            return lossOrder.getLossAmount();
        }
        //已经退的总额如果等于这次通过报损的金额，就不计算补贴，实际不需要再额外退钱
        var alreadyRefundAmount = lossOrder.getRefundGoodsAmount().add(lossOrder.getRefundSubsidyFreeAmount());
        if (alreadyRefundAmount.compareTo(refundAmount) == 0) {
            return refundAmount;
        }
        //审核退款金额/报损总金额*报损补贴金额=退补贴金额
        var free = refundAmount.multiply(lossOrder.getLossSubsidyFreeAmount()).divide(lossAllAmount, 2, RoundingMode.DOWN);
        //如果优惠金额小于一分钱，则不退补贴金额,当成商品金额退给客户
        if (free.compareTo(new BigDecimal("0.01")) >= 0) {
            //审核金额减去补贴金额，得到客户金额
            return refundAmount.subtract(free);
        }
        return refundAmount;
    }


    @Lock4j(keys = "#id")
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delayCheck(Long id, Integer lossStatus) {
        log.keyword("reportLossCheck", id).info("报损延时检查，检查对象，id:{},lossStatus:{}", id, lossStatus);
        ReportLossOrder lossOrder = lossOrderMapper.selectById(id);
        log.keyword("reportLossCheck").info("报损延时检查，db：{}", lossOrder);
        if (lossOrder == null) {
            return;
        }
        if (!lossOrder.getLossStatus().equals(lossStatus)) {
            return;
        }

        ReportLossOrderFlow flow = new ReportLossOrderFlow();
        flow.setReportLossId(id);
        flow.setLossType(lossOrder.getLossType());
        flow.setLossWeight(lossOrder.getLossWeight());

        OrderItem item = orderItemMapper.selectById(lossOrder.getOrderItemId());
        ReportLossOrder up = new ReportLossOrder();
        up.setId(id);
        if (lossOrder.getLossStatus().equals(ReportLossStatusEnum.WAIT_AUDIT.getCode())) {
            //待审核的超时后直接全额退款
            //需要重新计算退款金额，避免先报损后差额退导致超退
            BigDecimal refundAmount = null;
            BigDecimal refundSubsidyFreeAmount = null;
            if (lossOrder.getSkuPrice().compareTo(item.getFinalPrice()) == 0) {
                refundAmount = lossOrder.getLossAmount();
                refundSubsidyFreeAmount = lossOrder.getLossSubsidyFreeAmount();
            } else if (lossOrder.getLossType().equals(ReportLossTypeEnum.WEIGHT.getCode())) {
                //报损毛重*商品单价/商品毛重 = 报损毛重对应的最大金额
                refundAmount = lossOrder.getLossWeight().multiply(item.getFinalPrice()).divide(item.getSpuGrossWeight(), 2, RoundingMode.DOWN);
                //报损退款*申请补贴/申请退款金额
                refundSubsidyFreeAmount = refundAmount.multiply(lossOrder.getLossSubsidyFreeAmount()).divide(lossOrder.getLossAmount(), 2, RoundingMode.DOWN);
            } else {
                //客服提货商品的最终付款金额
                refundAmount = orderService.getCanLossAmount(item.getId()).getLossAmount();
                //如果新计算出的大于用户申请金额，则取用户申请金额
                if (refundAmount.compareTo(lossOrder.getLossAmount()) > 0) {
                    refundAmount = lossOrder.getLossAmount();
                }
                //报损退款*申请补贴/申请退款金额
                refundSubsidyFreeAmount = refundAmount.multiply(lossOrder.getLossSubsidyFreeAmount()).divide(lossOrder.getLossAmount(), 2, RoundingMode.DOWN);
            }
            lossOrder.setLossAmount(refundAmount);

            flow.setLossStatus(ReportLossStatusEnum.FULL_PASS.getCode());
            flow.setRefundGoodsAmount(refundAmount);
            flow.setRefundSubsidyFreeAmount(refundSubsidyFreeAmount);
            flow.setFlowDesc(this.flowCustomerDesc(lossOrder, flow, BigDecimal.ZERO));
            lossOrderFlowMapper.insert(flow);

            up.setFlowId(flow.getId());
            up.setRefundTime(new Date());
            up.setLossStatus(ReportLossStatusEnum.FULL_PASS.getCode());
            up.setRefundGoodsAmount(refundAmount);
            up.setRefundGoodsActualAmount(refundAmount);
            up.setRefundSubsidyFreeAmount(refundSubsidyFreeAmount);
            lossOrderMapper.updateById(up);

            //退款
            CreateRefundRecordBO refundBo = new CreateRefundRecordBO();
            refundBo.setOrderId(lossOrder.getOrderId());
            refundBo.setOrderItemId(item.getId());
            refundBo.setRefundProductAmount(refundAmount);
            refundBo.setRefundOtherAmount(BigDecimal.ZERO);
            refundBo.setRefundSubsidyFreeAmount(refundSubsidyFreeAmount);
            refundBo.setType(RefundTypeEnum.REPORT_LOSS.getCode());
            refundBo.setSourceCode(lossOrder.getReportLossNo());
            log.keyword("delayCheck", lossOrder.getOrderId()).info("报损超时自动退款 {}", lossOrder.getLossAmount());
            refundRecordService.lossCreateRecord(refundBo, null, lossOrderMapper.selectById(lossOrder.getId()));

            orderItemMapper.updateAfterSaleStatus(item.getId(), ItemAfterSaleStatusEnum.REPORT_LOSS_END.getCode());
        } else if (lossOrder.getLossStatus().equals(ReportLossStatusEnum.SUBTRACT_PASS.getCode()) || lossOrder.getLossStatus().equals(ReportLossStatusEnum.REJECT.getCode())) {
            //减额通过或者驳回超时直接完成，不额外退款
            flow.setLossStatus(ReportLossStatusEnum.COMPLETE.getCode());
            flow.setRefundGoodsAmount(BigDecimal.ZERO);
            flow.setFlowDesc(this.flowCustomerDesc(lossOrder, flow, BigDecimal.ZERO));
            lossOrderFlowMapper.insert(flow);

            up.setFlowId(flow.getId());
            up.setLossStatus(ReportLossStatusEnum.COMPLETE.getCode());
            lossOrderMapper.updateById(up);
            orderItemMapper.updateAfterSaleStatus(item.getId(), ItemAfterSaleStatusEnum.REPORT_LOSS_END.getCode());
            //售后结束，进行结算
            orderItemService.notifySettle(Lists.newArrayList(lossOrder.getOrderItemId()));

            //释放占用
            refundRecordService.lossCancel(lossOrder.getReportLossNo());
        }
    }


    @Override
    public List<RemoteSupTransRefundLossVo> accTrans(RemoteSupAccTransQueryBo bo) {
        if (CollUtil.isEmpty(bo.getTrans())) {
            return new ArrayList<>();
        }
        //trans总量可能过多，所以按skuId分组
        Map<Long, List<Long>> refundMap = new HashMap<>();
        bo.getTrans().forEach(b -> {
            var list = refundMap.getOrDefault(b.getSkuId(), new ArrayList<>());
            list.add(b.getTransId());
            refundMap.put(b.getSkuId(), list);
        });
        Map<Long, Set<String>> lossCodeMap = new HashMap<>();
        for (Long skuId : refundMap.keySet()) {
            LambdaQueryWrapper<RefundRecord> wrapper = Wrappers.lambdaQuery();
            wrapper.in(RefundRecord::getId, refundMap.get(skuId));
            wrapper.select(RefundRecord::getSourceCode);
            lossCodeMap.put(skuId, refundRecordMapper.selectList(wrapper).stream().map(RefundRecord::getSourceCode).collect(Collectors.toSet()));
        }
        List<Long> supplierSkuIdList = Lists.newArrayList(refundMap.keySet());
        RemoteQueryInfoListBo listBo = new RemoteQueryInfoListBo();
        listBo.setSupplierSkuIdList(supplierSkuIdList);
        log.keyword("accTrans").info("查询skuInfo:{}", supplierSkuIdList);
        List<RemoteSupplierSkuInfoVo> skuInfoVos = remoteSupplierSkuService.queryInfoList(listBo);
        Map<Long, RemoteSupplierSkuInfoVo> skuMap = skuInfoVos.stream().collect(Collectors.toMap(RemoteSupplierSkuInfoVo::getId, Function.identity()));

        //获取包装图片
        RemoteQuerySupplierSkuFileBo fileBo = new RemoteQuerySupplierSkuFileBo();
        fileBo.setSupplierSkuIdList(supplierSkuIdList);
        log.keyword("accTrans").info("查询skuFile:{}", supplierSkuIdList);
        List<RemoteSupplierSkuFileVo> fileVos = remoteSupplierSkuService.querySupplierSkuFile(fileBo);
        log.keyword("accTrans").info("skuFile结果:{}", fileVos);

        //一次性查出所有报损单，并根据skuId分组
        List<ReportLossOrder> allLossOrders = lossOrderMapper.selectByAccTrans(lossCodeMap.values().stream().flatMap(Set::stream).collect(Collectors.toSet()), bo.getSupplierId(), bo.getSupplierDeptId());
        var skuLossMap = allLossOrders.stream().collect(Collectors.groupingBy(ReportLossOrder::getSupplierSkuId));

        Map<Long, RemoteSupAccTransBo> boMap = bo.getTrans().stream().collect(Collectors.toMap(RemoteSupAccTransBo::getSkuId, Function.identity(), (v1, v2) -> v1));

        List<RemoteSupTransRefundLossVo> result = new ArrayList<>();
        for (Long skuId : lossCodeMap.keySet()) {
            List<ReportLossOrder> list1 = skuLossMap.get(skuId);
            if (CollUtil.isEmpty(list1)) {
                //避免因数据问题报错
                continue;
            }
            Map<BigDecimal, List<ReportLossOrder>> priceOrderMap = list1.stream().collect(Collectors.groupingBy(ReportLossOrder::getSkuPrice));

            for (Map.Entry<BigDecimal, List<ReportLossOrder>> bigDecimalListEntry : priceOrderMap.entrySet()) {
                BigDecimal totalLossAmount = new BigDecimal(0);
                BigDecimal totalRefundAmount = new BigDecimal(0);
                BigDecimal totalInterventionAmount = new BigDecimal(0);
                Set<Long> customerIdSet = new HashSet<>();
                List<String> orderIdList = new ArrayList<>();

                List<ReportLossOrder> reportLossOrders = bigDecimalListEntry.getValue();
                for (ReportLossOrder reportLossOrder : reportLossOrders) {
                    totalLossAmount = totalLossAmount.add(reportLossOrder.getLossAmount()).add(reportLossOrder.getLossSubsidyFreeAmount());
                    totalRefundAmount = totalRefundAmount.add(reportLossOrder.getRefundGoodsAmount()).add(reportLossOrder.getRefundSubsidyFreeAmount());
                    //偷懒，借用sku_amount
                    if (reportLossOrder.getSkuAmount() != null) {
                        totalInterventionAmount = totalInterventionAmount.add(reportLossOrder.getSkuAmount());
                    }
                    customerIdSet.add(reportLossOrder.getCustomerId());
                    if (ObjectUtil.isNotEmpty(reportLossOrder.getOrderId())) {
                        orderIdList.add(reportLossOrder.getOrderId() + "");
                    }
                }
                log.keyword("我的资金列表查询order字段数据", "accTrans", "查询报损的订单id").info("reportLossOrders:{}, skuId:{}, orderIdList:{}", reportLossOrders, skuId, orderIdList);

                ReportLossOrder baseReport = reportLossOrders.get(0);

                RemoteSupTransRefundLossVo vo = new RemoteSupTransRefundLossVo();
                vo.setId(skuId);
                vo.setImgUrl(baseReport.getSkuImgUrl());
                vo.setSkuName(baseReport.getSpuName());
                vo.setSpuGrossWeight(baseReport.getSpuGrossWeight());
                vo.setSpuNetWeight(baseReport.getSpuNetWeight());
                vo.setSkuPrice(baseReport.getSkuPrice());
                vo.setTotalLossAmount(totalLossAmount);
                vo.setTotalRefundAmount(totalRefundAmount);
                vo.setSaleDate(baseReport.getSaleDate());
                if (ObjectUtil.isNotEmpty(orderIdList)) {
                    vo.setOrderIdList(String.join(";", orderIdList));
                }
                vo.setLossPeopleNum(customerIdSet.size());
                vo.setTotalInterventionAmount(totalInterventionAmount);

                RemoteSupplierSkuInfoVo sku = skuMap.get(skuId);
                if (sku != null) {
                    vo.setProducer(sku.getProducer());
                    vo.setSpuStandards(sku.getSpuStandards());
                    vo.setSpuGrade(sku.getSpuGrade());
                }
                if(boMap.containsKey(skuId)) {
                    vo.setTotalAmount(boMap.get(skuId).getTransAmt());
                }
                result.add(vo);
            }
        }
        //按销售日期倒序
        result.sort(Comparator.comparing(RemoteSupTransRefundLossVo::getSaleDate).reversed());
        return result;
    }

    @Override
    public List<RemoteSupTransRefundLossRecordVo> accTransRecord(RemoteSupAccTransQueryBo bo) {
        List<Long> refundMap = new ArrayList<>();
        bo.getTrans().forEach(b -> {
            refundMap.add(b.getTransId());
        });
        LambdaQueryWrapper<RefundRecord> wrapper = Wrappers.lambdaQuery();
        wrapper.in(RefundRecord::getId, refundMap);;
        List<RefundRecord> refundsList = refundRecordMapper.selectList(wrapper);
        Set<String> lossCodes = refundsList.stream().map(RefundRecord::getSourceCode).collect(Collectors.toSet());
        List<ReportLossOrder> reportLossOrders = lossOrderMapper.selectByAccTrans(lossCodes, bo.getSupplierId(), bo.getSupplierDeptId());
        if (CollUtil.isEmpty(reportLossOrders)) {
            return new ArrayList<>();
        }
        Map<String, ReportLossOrder> lossMap = new HashMap<>();
        List<Long> orderItemIds = new ArrayList<>();
        for (ReportLossOrder reportLossOrder : reportLossOrders) {
            lossMap.put(reportLossOrder.getReportLossNo(), reportLossOrder);
            orderItemIds.add(reportLossOrder.getOrderItemId());
        }

        List<OrderItem> orderItems = orderItemMapper.selectBatchIds(orderItemIds);
        Map<Long, OrderItem> orderItemMap = orderItems.stream().collect(Collectors.toMap(OrderItem::getId, u -> u,  (n1, n2) -> n1));

        List<RemoteSupTransRefundLossRecordVo> result = Lists.newArrayListWithCapacity(reportLossOrders.size());
        for (RefundRecord refundRecord : refundsList) {
            ReportLossOrder reportLossOrder = lossMap.get(refundRecord.getSourceCode());
            OrderItem orderItem = orderItemMap.get(reportLossOrder.getOrderItemId());
            RemoteSupTransRefundLossRecordVo vo = new RemoteSupTransRefundLossRecordVo();
            vo.setReportLossNo(reportLossOrder.getReportLossNo());
            vo.setOrderId(reportLossOrder.getOrderId());
            vo.setOrderCode(reportLossOrder.getOrderNo());
            vo.setSupplierSkuId(orderItem.getSupplierSkuId());
            vo.setSaleDate(orderItem.getSaleDate());
            vo.setRefundRecordId(refundRecord.getId());
            vo.setLossStatusName(ReportLossStatusEnum.loadByCode(reportLossOrder.getLossStatus()).getDesc());
            vo.setLossTypeName(ReportLossTypeEnum.loadByCode(reportLossOrder.getLossType()).getDesc());
            vo.setLossAmount(reportLossOrder.getLossAmount().add(reportLossOrder.getLossSubsidyFreeAmount()));
            vo.setRefundAmount(refundRecord.getRefundAmount());
            vo.setCreateTime(refundRecord.getCreateTime());
            vo.setSkuPrice(reportLossOrder.getSkuPrice());
            result.add(vo);
        }
        return result;
    }

    @Override
    public void refundCall(Long refundId) {
        RefundRecord refundRecord = refundRecordMapper.selectById(refundId);
        if (refundRecord == null || !refundRecord.getRefundType().equals(RefundTypeEnum.REPORT_LOSS.getCode())) {
            return;
        }
        if (StrUtil.isBlank(refundRecord.getSourceCode())) {
            return;
        }
        ReportLossOrder lossOrder = lossOrderMapper.selectByNo(refundRecord.getSourceCode());
        if (lossOrder == null) {
            return;
        }
        if (!ReportLossStatusEnum.isComplete(lossOrder.getLossStatus())) {
            return;
        }
        //退款成功后，通知商品可被结算
        orderItemService.notifySettle(Lists.newArrayList(lossOrder.getOrderItemId()));
    }

    @Lock4j(keys = "#reportId")
    @Override
    public void lossOccupy(Long reportId) {
        ReportLossOrder reportLossOrder = lossOrderMapper.selectById(reportId);
        if (reportLossOrder == null) {
            return;
        }
        BigDecimal refundProductAmount = reportLossOrder.getLossAmount();
        BigDecimal refundSubsidyFreeAmount = reportLossOrder.getLossSubsidyFreeAmount();
        if (ReportLossStatusEnum.WAIT_AUDIT.getCode().equals(reportLossOrder.getLossStatus())) {
            log.info("报损单还未处理，占用全部报损金额{}", refundProductAmount.add(refundSubsidyFreeAmount));
        } else if (reportLossOrder.getLossStatus() < ReportLossStatusEnum.INTERVENTION.getCode()) {
            refundProductAmount = reportLossOrder.getLossAmount().subtract(reportLossOrder.getRefundGoodsAmount());
            refundSubsidyFreeAmount = reportLossOrder.getLossSubsidyFreeAmount().subtract(reportLossOrder.getRefundSubsidyFreeAmount());
            log.info("报损客服还未处理，占用报损金额{}", refundProductAmount.add(refundSubsidyFreeAmount));
        }
        CreateRefundRecordBO refundBo = new CreateRefundRecordBO();
        refundBo.setSourceCode(reportLossOrder.getReportLossNo());
        refundBo.setOrderId(reportLossOrder.getOrderId());
        refundBo.setOrderItemId(reportLossOrder.getOrderItemId());
        refundBo.setRefundProductAmount(refundProductAmount);
        refundBo.setRefundOtherAmount(BigDecimal.ZERO);
        refundBo.setRefundSubsidyFreeAmount(refundSubsidyFreeAmount);
        refundBo.setType(RefundTypeEnum.REPORT_LOSS.getCode());
        refundRecordService.lossCreateRecord(refundBo, OccupyEnum.YES, reportLossOrder);
    }

    /**
     * 计算报损
     * @param orderCode
     * @return
     */
    @Override
    public AdminOrderInfoVo getLossInfoByOrderCode(String orderCode) {
        return lossOrderMapper.getLossInfoByOrderCode(orderCode);
    }

    /**
     * 获取申请报损金额
     * @param orderCode
     * @return
     */
    @Override
    public BigDecimal applyLossAmount(String orderCode) {
        return lossOrderMapper.applyLossAmount(orderCode);
    }

    @Override
    public BigDecimal getSkuLossRate(List<Long> skuIds, LocalDate saleDateStart, LocalDate saleDateEnd) {
        if (CollUtil.isEmpty(skuIds)) {
            return BigDecimal.ZERO;
        }
        LocalDateTime start = saleDateStart.atStartOfDay();
        LocalDateTime end = LocalDateTime.of(saleDateEnd, LocalTime.MAX);

        //1.根据sku查询没被取消的订单项，得到单价和下单数量
        LambdaQueryWrapper<OrderItem> lqw1 = Wrappers.lambdaQuery();
        lqw1.in(OrderItem::getSupplierSkuId, skuIds)
                .isNull(OrderItem::getCancelType)
                .eq(OrderItem::getDelFlag, BanguoCommonConstant.notDelFlag)
                .select(OrderItem::getFinalPrice, OrderItem::getId, OrderItem::getCount);
        List<OrderItem> orderItems = orderItemMapper.selectList(lqw1);
        List<Long> itemIds = orderItems.stream().map(OrderItem::getId).toList();
        if (CollUtil.isEmpty(itemIds)) {
            return BigDecimal.ZERO;
        }
        //2.查询报损单
        LambdaQueryWrapper<ReportLossOrder> lqw2 = Wrappers.lambdaQuery();
        lqw2.in(ReportLossOrder::getOrderItemId, itemIds)
                .eq(ReportLossOrder::getDelFlag, BanguoCommonConstant.notDelFlag)
                .select(ReportLossOrder::getId);
        List<ReportLossOrder> reportList = lossOrderMapper.selectList(lqw2);
        if (CollUtil.isEmpty(reportList)) {
            return BigDecimal.ZERO;
        }
        List<Long> reportIds = reportList.stream().map(ReportLossOrder::getId).toList();

        //3.查询报损处理详情
        List<Integer> refundStatus = Lists.newArrayList(ReportLossStatusEnum.FULL_PASS.getCode(), ReportLossStatusEnum.SUBTRACT_PASS.getCode(), ReportLossStatusEnum.INTERVENTION.getCode());
        LambdaQueryWrapper<ReportLossOrderFlow> lqw3 = Wrappers.lambdaQuery();
        lqw3.in(ReportLossOrderFlow::getReportLossId, reportIds)
                .in(ReportLossOrderFlow::getLossStatus, refundStatus)
                .between(ReportLossOrderFlow::getCreateTime, start, end)
                .eq(ReportLossOrderFlow::getDelFlag, BanguoCommonConstant.notDelFlag)
                .select(ReportLossOrderFlow::getRefundGoodsAmount);
        List<ReportLossOrderFlow> flows = lossOrderFlowMapper.selectList(lqw3);
        if (CollUtil.isEmpty(flows)) {
            return BigDecimal.ZERO;
        }
        BigDecimal refundAmount = flows.stream().map(ReportLossOrderFlow::getRefundGoodsAmount).reduce(BigDecimal::add).get();
        if (refundAmount.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        //4.查询订单的缺货少货
        LambdaQueryWrapper<StockoutRecordDetail> lqw4 = Wrappers.lambdaQuery();
        lqw4.in(StockoutRecordDetail::getOrderItemId, itemIds)
                .eq(StockoutRecordDetail::getStatus, StockOutStatusEnum.HAS_CONFIRM.getCode())
                .eq(StockoutRecordDetail::getDelFlag, BanguoCommonConstant.notDelFlag)
                .select(StockoutRecordDetail::getOrderItemId, StockoutRecordDetail::getStockoutCount);
        List<StockoutRecordDetail> stockoutDetails = stockoutRecordDetailMapper.selectList(lqw4);

        Map<Long,Integer> stockoutMap = stockoutDetails.stream()
                .collect(Collectors.groupingBy(StockoutRecordDetail::getOrderItemId, Collectors.summingInt(StockoutRecordDetail::getStockoutCount)));

        BigDecimal allAmount = new BigDecimal(0);
        for (OrderItem orderItem : orderItems) {
            Integer dCount = stockoutMap.getOrDefault(orderItem.getId(), 0);
            Integer count = orderItem.getCount() - dCount;
            allAmount = allAmount.add(orderItem.getFinalPrice().multiply(BigDecimal.valueOf(count)));
        }
        if (allAmount.compareTo(BigDecimal.ZERO) == 0) {
            //异常处理，有发生报损的情况下，正常不应该全缺货少货
            return BigDecimal.ZERO;
        }
        return refundAmount.divide(allAmount, 4, RoundingMode.HALF_UP);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void refundAgain(ReportRefundDto dto) {
        ReportLossOrder lossOrder = lossOrderMapper.selectById(dto.getReportId());
        if (lossOrder == null) {
            throw new ServiceException("报损单不存在");
        }

        List<ReportLossOrderFlow> flows = lossOrderFlowMapper.selectByReportLossId(lossOrder.getId());
        ReportLossOrderFlow flow = null;
        //确认好退款金额
        BigDecimal refundProductAmount = null;
        for (ReportLossOrderFlow flow1 : flows) {
            if (flow1.getLossStatus().equals(dto.getLossStatus())) {
                refundProductAmount = flow1.getRefundGoodsAmount();
                flow = flow1;
                break;
            }
        }
        boolean insertDeduction = false;
        if (dto.getLossStatus().equals(ReportLossStatusEnum.FULL_PASS.getCode())
                || dto.getLossStatus().equals(ReportLossStatusEnum.SUBTRACT_PASS.getCode())) {
            //
        } else if (dto.getLossStatus().equals(ReportLossStatusEnum.INTERVENTION.getCode())) {
            //不是供应商的责任，创建扣款单，把钱从总仓转给供应商
            if (!flow.getResponsibilityType().equals(ResponsibilityTypeEnum.SUPPLIER.getCode())){
                insertDeduction = true;
            }
        } else {
            throw new ServiceException("状态指定错误");
        }
        if(refundProductAmount == null || refundProductAmount.compareTo(BigDecimal.ZERO) == 0) {
            //不退钱直接取消占用
            refundRecordService.lossCancel(lossOrder.getReportLossNo());
            return;
        }
        log.keyword("refundAgain", lossOrder.getId()).info("手工触发报损退款");
        //1.取消占用 如果有占用则全释放不退钱
        refundRecordService.lossCancel(lossOrder.getReportLossNo());
        //2.通过确认好的退款金额进行普通退款
        List<CreateRefundRecordBO> refundList = new ArrayList<>();
        CreateRefundRecordBO refundBo = new CreateRefundRecordBO();
        refundBo.setSourceCode(lossOrder.getReportLossNo());
        refundBo.setOrderId(lossOrder.getOrderId());
        refundBo.setOrderItemId(lossOrder.getOrderItemId());
        refundBo.setRefundProductAmount(refundProductAmount);
        refundBo.setRefundOtherAmount(BigDecimal.ZERO);
        refundBo.setType(RefundTypeEnum.REPORT_LOSS.getCode());
        refundList.add(refundBo);
        log.keyword("refundAgain", lossOrder.getId()).info("报损退款客户：{} ；补贴:{}", refundBo.getRefundProductAmount(), refundBo.getRefundSubsidyFreeAmount());
        refundRecordService.lossCreateRecord(refundBo, OccupyEnum.NOT, lossOrderMapper.selectById(lossOrder.getId()));

        if (insertDeduction) {
            RemoteRegionWhVo regionWhVo = remoteRegionWhService.queryById(lossOrder.getRegionWhId());
            RemoteCityWhVo cityWhVo = remoteCityWhService.queryById(lossOrder.getCityWhId());
            RemoteSupplierVo supplierVo = remoteSupplierService.getSupplierById(lossOrder.getSupplierId());
            log.info("创建扣款单，给供应商垫付的钱补回去");
            //不是供应商的责任，创建扣款单，把钱从总仓转给供应商
            deductionInfoService.insertDeduction(buildDeductionInfoBo(lossOrder, regionWhVo, cityWhVo, flow.getId(), supplierVo, refundProductAmount, DeductionTypeEnum.KKYY_RL));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void reopenAfterSales(Long itemId) {
        OrderItem orderItem = orderItemMapper.selectById(itemId);
        if (orderItem==null) {
            throw new ServiceException("订单项不存在");
        }

        //查询是否开票
        InvoiceItemBo bo = new InvoiceItemBo();
        bo.setOrderCode(orderItem.getOrderCode());
        bo.setOrderItemId(orderItem.getId());
        bo.setSkuId(orderItem.getSkuId());
        List<InvoiceItemVo> invoiceItemVos = invoiceItemService.queryList(bo);
        if (!invoiceItemVos.isEmpty()) {
            throw new ServiceException("该商品已开具发票，不支持重新开启售后");
        }

        ReportLossOrder order = lossOrderMapper.selectByItemId(itemId);
        if (order != null) {
            if (!ReportLossStatusEnum.isComplete(order.getLossStatus())) {
                throw new ServiceException("报损未结束，不允许重新发起售后");
            }
            lossOrderMapper.deleteById(order.getId());
            lossOrderFlowMapper.deleteByReportLossId(order.getId());
        }
        orderItemMapper.reopenAfterSales(orderItem.getId());
        receiveGoodsRecordDetailMapper.reopenAfterSales(itemId);
        //重开结算
        supplySaleSettleDateMapper.reopenSettle(orderItem.getSaleDate(), orderItem.getSupplierId(), orderItem.getBusinessType());
    }



    @Lock4j(keys = "#reportId")
    @Override
    public void revoke(Long reportId, Long itemId) throws ServiceException {
        ReportLossOrder order = lossOrderMapper.selectById(reportId);

        //未审核的情况下，直接取消
        if (ReportLossStatusEnum.WAIT_AUDIT.getCode().equals(order.getLossStatus())) {
            lossOrderMapper.deleteById(order.getId());
            lossOrderFlowMapper.deleteByReportLossId(order.getId());
            //同步修改提货单的售后状态，可以再次售后
            receiveGoodsRecordDetailMapper.updateAfterSale(itemId, AfterSaleStatusEnum.CAN_AFTER_SALE.getCode());
            OrderItem item = orderItemMapper.selectById(itemId);
            this.updateOrderItemAfterSale(item.getId(), ItemAfterSaleStatusEnum.CAN_AFTER_SALE.getCode());
            //释放占用
            refundRecordService.lossCancel(order.getReportLossNo());
            return;
        }
        if (ReportLossStatusEnum.APPEAL.getCode().equals(order.getLossStatus())) {
            List<ReportLossOrderFlow> flows = lossOrderFlowMapper.selectByReportLossId(order.getId());
            //找出上一条减额或驳回的数据
            ReportLossOrderFlow previous = flows.get(1);
            Date preTime = DateUtil.offsetHour(previous.getCreateTime(), AUTO_PASS_TIME).toJdkDate();
            ReportLossOrder update = new ReportLossOrder();
            update.setId(order.getId());
            Date now = new Date();
            if (now.after(preTime)) {
                //已过期，自动完成
                update.setLossStatus(ReportLossStatusEnum.COMPLETE.getCode());
                lossOrderMapper.updateById(update);
                //释放占用
                refundRecordService.lossCancel(order.getReportLossNo());
            } else {
                lossOrderFlowMapper.deleteById(flows.get(0).getId());
                update.setFlowId(flows.get(1).getId());
                update.setLossStatus(ReportLossStatusEnum.REJECT.getCode());
                lossOrderMapper.updateById(update);
            }
        } else {
            throw new ServiceException("状态异常，不能撤回！");
        }
    }

    @Override
    public TableDataInfo<ReportLossMiniListVo> miniPage(ReportLossMiniListSearchBo bo) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        Page<ReportLossOrder> page = lossOrderMapper.miniPage(bo, bo.build());

        List<ReportLossOrder> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return TableDataInfo.build(new ArrayList<>());
        }
        List<ReportLossMiniListVo> result = Lists.newArrayListWithCapacity(records.size());
        List<Long> customerIds = new ArrayList<>();
        List<Long> flowsIds = new ArrayList<>();
        List<Long> skuIds = new ArrayList<>();
        List<Long> lossIds = new ArrayList<>();
        Set<Long> deptIds = new HashSet<>();
        for (ReportLossOrder record : records) {
            customerIds.add(record.getCustomerId());
            flowsIds.add(record.getFlowId());
            skuIds.add(record.getSupplierSkuId());
            lossIds.add(record.getId());
            if (record.getSupplierDeptId() > 0) {
                deptIds.add(record.getSupplierDeptId());
            }
        }

        Map<Long, String> deptNameMap = remoteDeptService.getDeptNameMap(Lists.newArrayList(deptIds));
        //查出所有相关用户
        Map<Long, RemoteCustomerVo> customerMap = remoteBasCustomerService
                .getByIds(customerIds)
                .stream().collect(Collectors.toMap(RemoteCustomerVo::getId, Function.identity()));
        Map<String, RemoteUserBo> userMap = remoteUserService.getUsersByUserCodes(customerMap.values().stream().map(RemoteCustomerVo::getUserCode).toList());

        //查出所有相关flow
        LambdaQueryWrapper<ReportLossOrderFlow> lqw = Wrappers.lambdaQuery();
        lqw.in(ReportLossOrderFlow::getReportLossId, lossIds);
        var allFlows = lossOrderFlowMapper.selectList(lqw);
        var lossFlowMap = allFlows.stream().collect(Collectors.groupingBy(ReportLossOrderFlow::getReportLossId));

        //查询所有sku和文件
        RemoteQueryInfoListBo listBo = new RemoteQueryInfoListBo();
        listBo.setSupplierSkuIdList(skuIds);
        List<RemoteSupplierSkuInfoVo> skuInfoVos = remoteSupplierSkuService.queryInfoList(listBo);
        Map<Long, RemoteSupplierSkuInfoVo> skuMap = skuInfoVos.stream().collect(Collectors.toMap(RemoteSupplierSkuInfoVo::getId, Function.identity()));
        //获取包装图片
        RemoteQuerySupplierSkuFileBo fileBo = new RemoteQuerySupplierSkuFileBo();
        fileBo.setSupplierSkuIdList(skuIds);
        List<RemoteSupplierSkuFileVo> fileVos = remoteSupplierSkuService.querySupplierSkuFile(fileBo);
        Map<Long, List<RemoteSupplierSkuFileVo>> skuFileMap = fileVos.stream().collect(Collectors.groupingBy(RemoteSupplierSkuFileVo::getSupplierSkuId, Collectors.toList()));

        records.forEach(record -> {
            ReportLossMiniListVo vo = MapstructUtils.convert(record, ReportLossMiniListVo.class);

            vo.setFlowDesc(this.flowDescCustomizer(record, lossFlowMap.get(record.getId()), record.getFlowId(), loginUser.getUserType()));
            this.listVoCustomizer(record, vo, loginUser.getUserType());
            RemoteCustomerVo customer = customerMap.get(record.getCustomerId());
            if (customer != null) {
                vo.setCustomerName(customer.getName());
                vo.setCustomerAlias(customer.getAlias());
                RemoteUserBo userBo = userMap.get(customer.getUserCode());
                if (userBo != null) {
                    vo.setCustomerPhone(userBo.getPhoneNo());
                }
            }
            if (ObjectUtil.isNotEmpty(deptNameMap)) {
                vo.setSupplierDeptName(deptNameMap.get(record.getSupplierDeptId()));
            }
            RemoteSupplierSkuInfoVo sku = skuMap.get(record.getSupplierSkuId());
            if (sku != null) {
                vo.setProducer(sku.getProducer());
                vo.setSpuStandards(sku.getSpuStandards());
                vo.setSpuGrade(sku.getSpuGrade());
                vo.setBusinessType(sku.getBusinessType());
                // 产地简称
                vo.setAreaCode(sku.getAreaCode());
                vo.setBrand(sku.getBrand());
                vo.setShortProducer(sku.getShortProducer());
                vo.setSaleType(sku.getSaleType());
            }
            List<RemoteSupplierSkuFileVo> files = skuFileMap.get(record.getSupplierSkuId());
            if (files != null) {
                vo.setFileList(files);
            }
            vo.setNetWeightPrice(record.getSkuPrice().divide(record.getSpuNetWeight(), 2, RoundingMode.CEILING));
            result.add(vo);
        });
        TableDataInfo tableDataInfo = TableDataInfo.build();
        tableDataInfo.setTotal(page.getTotal());
        tableDataInfo.setRows(result);
        return tableDataInfo;
    }

    @Override
    public ReportLossMiniDetailVo detail(Long orderItemId) {
        ReportLossOrder order = lossOrderMapper.selectByItemId(orderItemId);
        if (order == null) {
            throw new ServiceException("报损单不存在");
        }

        List<ReportLossOrderFlow> flows = lossOrderFlowMapper.selectByReportLossId(order.getId());

        ReportLossMiniDetailVo resultVo = MapstructUtils.convert(order, ReportLossMiniDetailVo.class);

        OrderItem orderItem = orderItemMapper.selectById(orderItemId);
        if (ObjectUtil.isNotEmpty(orderItem)) {
//            LambdaQueryWrapper<OrderItem> wrapper = new LambdaQueryWrapper<>();
//            wrapper.eq(OrderItem::getCustomerId, order.getCustomerId());
//            wrapper.eq(OrderItem::getSupplierSkuId, orderItem.getSupplierSkuId());
//            List<OrderItem> orderItemList = orderItemMapper.selectList(wrapper);
//            Integer totalCount = orderItemList.stream().mapToInt(OrderItem::getCount).sum();
//            resultVo.setCount(totalCount);
            resultVo.setCount(orderItem.getCount());
        }

        RemoteSupplierVo supplierById = remoteSupplierService.getSupplierById(order.getSupplierId());
        if (ObjectUtil.isNotEmpty(supplierById)) {
            resultVo.setSupplierName(supplierById.getName());
        }
        if (ObjectUtil.isNotEmpty(resultVo.getSupplierDeptId()) && resultVo.getSupplierDeptId() > 0) {
            Map<Long, String> deptNameMap = remoteDeptService.getDeptNameMap(Lists.newArrayList(resultVo.getSupplierDeptId()));
            if (ObjectUtil.isNotEmpty(deptNameMap)) {
                resultVo.setSupplierDeptName(deptNameMap.get(resultVo.getSupplierDeptId()));
            }
        }

        List<ReportLossOrderFlowVo> flowVos = new ArrayList<>();

        resultVo.setFlow(flowVos);

        RemoteCustomerVo remoteCustomerVo = remoteBasCustomerService.getById(resultVo.getCustomerId());
        resultVo.setCustomerName(remoteCustomerVo.getName());
        resultVo.setCustomerAlias(remoteCustomerVo.getAlias());

        if (ReportLossStatusEnum.isCountdown(order.getLossStatus())) {
            resultVo.setCountdown(DateUtil.between(new Date(), order.getExpireTime(), DateUnit.MS));
        }
        //详情接口是白名单接口，不登陆的情况下默认是客户类型
        LoginUser loginUser = LoginHelper.getLoginUser();
        String userType = loginUser == null ? SysUserTypeEnum.CUSTOMER_USER.getType(): loginUser.getUserType();
        for (ReportLossOrderFlow flow : flows) {
            ReportLossOrderFlowVo flowVo = MapstructUtils.convert(flow, ReportLossOrderFlowVo.class);
            var flowRole = ReportLossStatusEnum.loadByCode(flow.getLossStatus()).getFlowRole();
            flowVo.setFlowRole(flowRole.getKey());
            flowVo.setAction(flowRole.getValue());
            flowVo.setFlowDesc(this.flowDescCustomizer(order, flows, flow.getId(), userType));
            flowVos.add(flowVo);
        }
        //填充oss链接
        this.selectOss(flowVos);

        //查询所有sku和文件
        RemoteSupplierSkuInfoVo sku = remoteSupplierSkuService.getSimpleById(order.getSupplierSkuId());
        if (sku != null) {
            resultVo.setProducer(sku.getProducer());
            resultVo.setSpuStandards(sku.getSpuStandards());
            resultVo.setSpuGrade(sku.getSpuGrade());
            resultVo.setBusinessType(sku.getBusinessType());
            resultVo.setSnapshot(sku.getSnapshot());
            resultVo.setSaleType(sku.getSaleType());
        }
        //获取包装图片
        RemoteQuerySupplierSkuFileBo fileBo = new RemoteQuerySupplierSkuFileBo();
        fileBo.setSupplierSkuIdList(Lists.newArrayList(order.getSupplierSkuId()));
        List<RemoteSupplierSkuFileVo> files = remoteSupplierSkuService.querySupplierSkuFile(fileBo);
        if (files != null) {
            resultVo.setFileList(files);
        }
        //查询物流线
        RemoteRegionLogisticsQueryBo logisticsQueryBo = new RemoteRegionLogisticsQueryBo();
        logisticsQueryBo.setLogisticsId(orderItem.getLogisticsId());
        List<RemoteRegionLogisticsVo> logisticsVos = remoteRegionLogisticsService.queryList(logisticsQueryBo);
        if (CollUtil.isNotEmpty(logisticsVos)) {
            resultVo.setLogisticsName(logisticsVos.get(0).getLogisticsName());
        }
        //获取采购信息
        List<RemoteSupplierSkuInfoVo> skuInfoVoList = remoteSupplierSkuService.querySimpleInfoList(List.of(order.getSupplierSkuId()));
        if (CollUtil.isNotEmpty(skuInfoVoList)) {
            resultVo.setBuyerName(skuInfoVoList.get(0).getBuyerName());
        }

        this.detailVoCustomizer(order, resultVo, userType);
        return resultVo;
    }

    @Override
    public Long getReportLossId(Long orderItemId) {
        LambdaQueryWrapper<ReportLossOrder> lwq = Wrappers.lambdaQuery();
        lwq.eq(ReportLossOrder::getOrderItemId, orderItemId)
                .eq(ReportLossOrder::getDelFlag, 0)
                .select(ReportLossOrder::getId);
        return lossOrderMapper.selectOne(lwq).getId();
    }


    @Override
    public ReportLossOrderVo queryByCode(String reportLossNo) {
        LambdaQueryWrapper<ReportLossOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ReportLossOrder::getReportLossNo, reportLossNo);
        wrapper.eq(ReportLossOrder::getDelFlag, 0);
        ReportLossOrder reportLossOrder = lossOrderMapper.selectOne(wrapper);
        if (ObjectUtil.isEmpty(reportLossOrder)) {
            return null;
        }
        return BeanUtil.toBean(reportLossOrder, ReportLossOrderVo.class);
    }

    @Override
    public RemoteOrderBusiAmtVo selectRefundByOrderId(Long orderId) {
        RemoteOrderBusiAmtVo result = new RemoteOrderBusiAmtVo();

        LambdaQueryWrapper<ReportLossOrder> qw = Wrappers.lambdaQuery();
        qw.eq(ReportLossOrder::getOrderId, orderId);
        qw.gt(ReportLossOrder::getLossStatus, ReportLossStatusEnum.WAIT_AUDIT.getCode());
        qw.eq(ReportLossOrder::getDelFlag, 0);
        List<ReportLossOrder> lossOrders = lossOrderMapper.selectList(qw);
        if (CollUtil.isEmpty(lossOrders)) {
            result.setGoodsAmt(BigDecimal.ZERO);
            result.setServiceAmt(BigDecimal.ZERO);
            result.setFreightAmt(BigDecimal.ZERO);
            result.setCoinAmt(BigDecimal.ZERO);
            result.setBankFeeAmt(BigDecimal.ZERO);
            result.setAvailAmt(BigDecimal.ZERO);
        } else {
            BigDecimal goodsAmt = new BigDecimal(0);
            BigDecimal serviceAmt = new BigDecimal(0);
            BigDecimal freightAmt = new BigDecimal(0);
            BigDecimal bankFeeAmt = new BigDecimal(0);
            for (ReportLossOrder lossOrder : lossOrders) {
                goodsAmt = goodsAmt.add(lossOrder.getRefundGoodsAmount());
                serviceAmt = serviceAmt.add(lossOrder.getRefundSubstituteAmount());
                freightAmt = freightAmt.add(lossOrder.getRefundFreightAmount());
                bankFeeAmt = bankFeeAmt.add(lossOrder.getRefundFinancialAmount());
            }
            result.setGoodsAmt(goodsAmt);
            result.setServiceAmt(serviceAmt);
            result.setFreightAmt(freightAmt);
            result.setBankFeeAmt(bankFeeAmt);
            //todo 未来报损退代采币时需要改这里
            result.setCoinAmt(BigDecimal.ZERO);
            BigDecimal availAmt = result.getGoodsAmt().add(result.getServiceAmt()).add(result.getFreightAmt()).add(result.getCoinAmt()).add(result.getBankFeeAmt());
            result.setAvailAmt(availAmt);
        }
        return result;
    }

    @Override
    public List<ReportNumStatisticsVo> numStatistics(Long customerId, Long supplierId, Long cityWhId, Long regionWhId, Long supplierDeptId) {
        return lossOrderMapper.numStatistics(customerId, supplierId, cityWhId, regionWhId, supplierDeptId);
    }

    @Override
    public TableDataInfo<ReportLossPcListVo> refundPage(ReportLossPcListSearchBo searchBo) {

        searchBo.setLossStatus(ReportLossStatusEnum.reportRefundStatus(searchBo.getRefundType()));
        Page<ReportLossPcListVo> page = lossOrderMapper.refundPage(searchBo, searchBo.build());
        if (CollUtil.isEmpty(page.getRecords())) {
            return TableDataInfo.build(page);
        }
        List<ReportLossPcListVo> records = page.getRecords();

        //查出所有相关用户
        var customerMap = remoteBasCustomerService
                .getByIds(records.stream().map(ReportLossPcListVo::getCustomerId).toList())
                .stream().collect(Collectors.toMap(RemoteCustomerVo::getId, Function.identity(), (k1, k2) -> k1));
        //查询所有供应商信息
        var supMap = remoteSupplierService.getSupplierByIds(records.stream().map(ReportLossPcListVo::getSupplierId).toList())
                .stream().collect(Collectors.toMap(RemoteSupplierVo::getId, Function.identity(), (k1, k2) -> k1));
        records.forEach(record -> {
            record.setRefundType(ReportLossStatusEnum.INTERVENTION.getCode().equals(record.getLossStatus())
                    ? ReportLossRefundTypeEnum.CS.getCode() : ReportLossRefundTypeEnum.SUP.getCode());

            RemoteCustomerVo customerVo = customerMap.get(record.getCustomerId());
            if (customerVo != null) {
                record.setCustomerName(customerVo.getName());
            }
            RemoteSupplierVo supplierVo = supMap.get(record.getSupplierId());
            if (supplierVo != null) {
                record.setSupplierName(supplierVo.getName());
                record.setSupplierAlias(supplierVo.getAlias());
            }
        });
        return TableDataInfo.build(page);
    }

    @Override
    public List<ReportLossItemInfoVo> getByOrderItemId(Long orderItemId) {
        List<ReportLossItemInfoVo> list =  lossOrderMapper.getByOrderItemId(orderItemId);
        if (ObjectUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list;
    }


    private void listVoCustomizer(ReportLossOrder lossOrder, ReportLossMiniListVo vo, String userType) {
        if (userType.equals(SysUserTypeEnum.CUSTOMER_USER.getType()) || userType.equals(SysUserTypeEnum.CITY_USER.getType())) {
            vo.setLossAmount(lossOrder.getLossAmount());
        }
        if (userType.equals(SysUserTypeEnum.SUPPLIER_USER.getType()) || userType.equals(SysUserTypeEnum.SYS_USER.getType())) {
            vo.setLossAmount(lossOrder.getLossAmount().add(lossOrder.getLossSubsidyFreeAmount()));
        }
    }

    private void detailVoCustomizer(ReportLossOrder lossOrder, ReportLossMiniDetailVo vo, String userType) {
        if (lossOrder.getLossSubsidyFreeAmount().compareTo(BigDecimal.ZERO) == 0) {
            vo.setRefundSubsidyFreeAmount(null);
            return;
        }
        if (userType.equals(SysUserTypeEnum.CUSTOMER_USER.getType()) || userType.equals(SysUserTypeEnum.CITY_USER.getType())) {
            vo.setLossAmount(lossOrder.getLossAmount());
            vo.setRefundSubsidyFreeAmount(null);
        }
        if (userType.equals(SysUserTypeEnum.SUPPLIER_USER.getType())) {
            vo.setLossAmount(lossOrder.getLossAmount().add(lossOrder.getLossSubsidyFreeAmount()));
            vo.setRefundSubsidyFreeAmount(null);
        }
        if (userType.equals(SysUserTypeEnum.SYS_USER.getType())) {
            //实际上可以不用手动赋值，MapstructUtils已自动赋值
            vo.setLossAmount(lossOrder.getLossAmount());
            vo.setLossSubsidyFreeAmount(lossOrder.getLossSubsidyFreeAmount());
        }
    }


    /**
     * flow desc 默认写入客户看的版本
     *
     * @param order
     * @param flow
     * @return java.lang.String
     * <AUTHOR> on 2024/8/24:16:57
     */
    private String flowCustomerDesc(ReportLossOrder order, ReportLossOrderFlow flow, BigDecimal lossAmount) {
        String desc = "";
        if (ReportLossStatusEnum.WAIT_AUDIT.getCode().equals(flow.getLossStatus())) {
            desc += "报损金额" + order.getLossAmount() + "元";
            if (order.getLossType().equals(1)) {
                desc += "(重量：" + order.getLossWeight() + "斤)";
            }
        } else if (ReportLossStatusEnum.APPEAL.getCode().equals(flow.getLossStatus())) {
            desc += "申诉最大金额" + order.getLossAmount().subtract(order.getRefundGoodsAmount()) + "元，等待客服介入";
        } else if (ReportLossStatusEnum.REJECT.getCode().equals(flow.getLossStatus())) {
            //驳回默认显示备注，不展现flowDesc
        } else if (ReportLossStatusEnum.FULL_PASS.getCode().equals(flow.getLossStatus())) {
            desc += "已退商品金额" + order.getLossAmount() + "元";
        } else if (ReportLossStatusEnum.SUBTRACT_PASS.getCode().equals(flow.getLossStatus())) {
            desc += "已退商品金额" + flow.getRefundGoodsAmount() + "元";
        } else if (ReportLossStatusEnum.COMPLETE.getCode().equals(flow.getLossStatus())) {
            if (ReportLossStatusEnum.REJECT.getCode().equals(order.getLossStatus())) {
                desc += "未在规定时间" + AUTO_PASS_TIME + "小时内申诉";
            } else {
                desc += "已退商品金额" + order.getRefundGoodsAmount() + "元" + ",未在规定时间" + AUTO_PASS_TIME + "小时内申诉";
            }
        } else if (ReportLossStatusEnum.INTERVENTION.getCode().equals(flow.getLossStatus())) {
            var t = ResponsibilityTypeEnum.load(flow.getResponsibilityType());
            desc += "介入退款" + lossAmount + "元，判责:" + t.getDesc() + "承担客服介入部分";
        }
        return desc;
    }


    private String flowDescCustomizer(ReportLossOrder lossOrder, List<ReportLossOrderFlow> allFlows, Long flowId, String userType) {
        Map<Long, ReportLossOrderFlow> idFlowMap = new HashMap<>();
        Map<Integer, ReportLossOrderFlow> statusFlowMap = new HashMap<>();
        for (ReportLossOrderFlow flow : allFlows) {
            idFlowMap.put(flow.getId(), flow);
            statusFlowMap.put(flow.getLossStatus(), flow);
        }
        if (lossOrder.getLossSubsidyFreeAmount().compareTo(BigDecimal.ZERO) == 0) {
            return idFlowMap.get(flowId).getFlowDesc();
        }
        if (userType.equals(SysUserTypeEnum.CUSTOMER_USER.getType()) || userType.equals(SysUserTypeEnum.CITY_USER.getType())) {
            return idFlowMap.get(flowId).getFlowDesc();
        }
        if (userType.equals(SysUserTypeEnum.SUPPLIER_USER.getType())) {
            return flowDescSupCustomizer(lossOrder, statusFlowMap, idFlowMap.get(flowId));
        }
        if (userType.equals(SysUserTypeEnum.SYS_USER.getType())) {
            return flowDescRegionCustomizer(lossOrder, statusFlowMap, idFlowMap.get(flowId));
        }
        return "";
    }

    private String flowDescSupCustomizer(ReportLossOrder order, Map<Integer, ReportLossOrderFlow> statusFlowMap, ReportLossOrderFlow currentFlow) {
        String desc = "";
        var lossAmount = order.getLossAmount().add(order.getLossSubsidyFreeAmount());
        if (ReportLossStatusEnum.WAIT_AUDIT.getCode().equals(currentFlow.getLossStatus())) {
            desc += "报损金额" + lossAmount + "元";
            if (order.getLossType().equals(1)) {
                desc += "(重量：" + order.getLossWeight() + "斤)";
            }
        } else if (ReportLossStatusEnum.APPEAL.getCode().equals(currentFlow.getLossStatus())) {
            var pass = statusFlowMap.get(ReportLossStatusEnum.SUBTRACT_PASS.getCode());
            if (pass != null) {
                desc += "申诉最大金额" + lossAmount.subtract(pass.getRefundGoodsAmount()).subtract(pass.getRefundSubsidyFreeAmount()) + "元，等待客服介入";
            } else {
                desc += "申诉最大金额" + lossAmount + "元，等待客服介入";
            }
        } else if (ReportLossStatusEnum.FULL_PASS.getCode().equals(currentFlow.getLossStatus())) {
            desc += "已退商品金额" + lossAmount + "元";
        } else if (ReportLossStatusEnum.SUBTRACT_PASS.getCode().equals(currentFlow.getLossStatus())) {
            desc += "已退商品金额" + currentFlow.getRefundGoodsAmount().add(currentFlow.getRefundSubsidyFreeAmount()) + "元";
        } else if (ReportLossStatusEnum.COMPLETE.getCode().equals(currentFlow.getLossStatus())) {
            var pass = statusFlowMap.get(ReportLossStatusEnum.SUBTRACT_PASS.getCode());
            if (pass != null) {
                desc += "已退商品金额" + order.getRefundGoodsAmount().add(order.getRefundSubsidyFreeAmount()) + "元" + ",未在规定时间" + AUTO_PASS_TIME + "小时内申诉";
            } else {
                desc += "未在规定时间" + AUTO_PASS_TIME + "小时内申诉";
            }
        } else if (ReportLossStatusEnum.INTERVENTION.getCode().equals(currentFlow.getLossStatus())) {
            var t = ResponsibilityTypeEnum.load(currentFlow.getResponsibilityType());
            desc += "介入退款" + currentFlow.getRefundGoodsAmount().add(currentFlow.getRefundSubsidyFreeAmount()) + "元，判责:" + t.getDesc() + "承担客服介入部分";
        } else {
            desc += currentFlow.getFlowDesc();
        }
        return desc;
    }

    private String flowDescRegionCustomizer(ReportLossOrder order, Map<Integer, ReportLossOrderFlow> statusFlowMap, ReportLossOrderFlow currentFlow) {
        String desc = "";
        var lossAmount = order.getLossAmount().add(order.getLossSubsidyFreeAmount());
        if (ReportLossStatusEnum.WAIT_AUDIT.getCode().equals(currentFlow.getLossStatus())) {
            desc += "报损金额" + order.getLossAmount() + "元, 补贴金额" + order.getLossSubsidyFreeAmount() + "元";
            if (order.getLossType().equals(1)) {
                desc += "(重量：" + order.getLossWeight() + "斤)";
            }
        } else if (ReportLossStatusEnum.APPEAL.getCode().equals(currentFlow.getLossStatus())) {
            var pass = statusFlowMap.get(ReportLossStatusEnum.SUBTRACT_PASS.getCode());
            if (pass != null) {
                desc += "申诉最大商品金额" + order.getLossAmount().subtract(pass.getRefundGoodsAmount()) + "元，补贴金额" + order.getLossSubsidyFreeAmount().subtract(pass.getRefundSubsidyFreeAmount()) + "，等待客服介入";
            } else {
                desc += "申诉最大金额" + lossAmount + "元，等待客服介入";
            }
        } else if (ReportLossStatusEnum.FULL_PASS.getCode().equals(currentFlow.getLossStatus())) {
            desc += "已退商品金额" + order.getLossAmount() + "元";
        } else if (ReportLossStatusEnum.SUBTRACT_PASS.getCode().equals(currentFlow.getLossStatus())) {
            desc += "已退商品金额" + currentFlow.getRefundGoodsAmount() + "元，补贴金额" + currentFlow.getRefundSubsidyFreeAmount();
        } else if (ReportLossStatusEnum.COMPLETE.getCode().equals(currentFlow.getLossStatus())) {
            var pass = statusFlowMap.get(ReportLossStatusEnum.SUBTRACT_PASS.getCode());
            if (pass != null) {
                desc += "已退商品金额" + order.getRefundGoodsAmount() + "元,补贴金额" + order.getRefundSubsidyFreeAmount() + ",未在规定时间" + AUTO_PASS_TIME + "小时内申诉";
            } else {
                desc += "未在规定时间" + AUTO_PASS_TIME + "小时内申诉";
            }
        } else if (ReportLossStatusEnum.INTERVENTION.getCode().equals(currentFlow.getLossStatus())) {
            var t = ResponsibilityTypeEnum.load(currentFlow.getResponsibilityType());
            desc += "介入退款商品" + currentFlow.getRefundGoodsAmount() + "元,补贴金额" + currentFlow.getRefundSubsidyFreeAmount() + "元，判责:" + t.getDesc() + "承担客服介入部分";
        } else {
            desc += currentFlow.getFlowDesc();
        }
        return desc;
    }

    private void insertOss(List<String> imageUrls, List<String> videoUrls, Long flowId) {
        List<RemoteOssBo> ossBos = new ArrayList<>();
        if (CollUtil.isNotEmpty(imageUrls)) {
            imageUrls.forEach(url -> {
                RemoteOssBo ossBo = new RemoteOssBo();
                ossBo.setUrl(url);
                ossBo.setBusinessType(OssBusinessTypeEnum.REPORT_LOSS_FLOW.getCode());
                ossBo.setKeyword(flowId.toString());
                ossBo.setTag(OssTagEnum.IMAGE.getCode());
                ossBos.add(ossBo);
            });
        }
        if (CollUtil.isNotEmpty(videoUrls)) {
            videoUrls.forEach(videoUrl -> {
                RemoteOssBo ossBo = new RemoteOssBo();
                ossBo.setUrl(videoUrl);
                ossBo.setBusinessType(OssBusinessTypeEnum.REPORT_LOSS_FLOW.getCode());
                ossBo.setKeyword(flowId.toString());
                ossBo.setTag(OssTagEnum.VIDEO.getCode());
                ossBos.add(ossBo);
            });
        }
        if (CollUtil.isNotEmpty(ossBos)) {
            remoteFileService.batchInsert(ossBos);
        }
    }

    private void selectOss(List<ReportLossOrderFlowVo> flows) {
        if (CollUtil.isEmpty(flows)) {
            return;
        }
        Map<String, List<RemoteOssVo>> ossMap = remoteFileService.selectMap(OssBusinessTypeEnum.REPORT_LOSS_FLOW.getCode(),
                flows.stream().map(flow -> flow.getId().toString()).toList());
        if (CollUtil.isEmpty(ossMap)) {
            return;
        }
        for (ReportLossOrderFlowVo flow : flows) {
            List<RemoteOssVo> list = ossMap.get(flow.getId().toString());
            if (CollUtil.isEmpty(list)) {
                //供应商可以不用传文件
                continue;
            }
            var tagMap = list.stream().collect(Collectors.groupingBy(RemoteOssVo::getTag));
            if (tagMap.containsKey(OssTagEnum.VIDEO.getCode())) {
                flow.setVideoUrls(tagMap.get(OssTagEnum.VIDEO.getCode()).stream().map(RemoteOssVo::getUrl).toList());
            }
            if (tagMap.containsKey(OssTagEnum.IMAGE.getCode())) {
                flow.setImageUrls(tagMap.get(OssTagEnum.IMAGE.getCode()).stream().map(RemoteOssVo::getUrl).toList());
            }
        }

    }


    private DeductionInfoParamBo buildDeductionInfoBo(ReportLossOrder lossOrder, RemoteRegionWhVo regionWhVo, RemoteCityWhVo cityWhVo, Long flowId, RemoteSupplierVo supplierVo, BigDecimal refundAmount, DeductionTypeEnum deductionTypeEnum) {
        DeductionInfoParamBo bo = new DeductionInfoParamBo();
        bo.setRegionWhId(regionWhVo.getId());
        bo.setRegionWhName(regionWhVo.getRegionWhName());
        bo.setCommonId(supplierVo.getId());
        bo.setCommonName(supplierVo.getName());
        bo.setType(DeductionInfoEnum.SUPPLIER.getCode());
        //不是供应商的责任，创建扣款单，把钱从总仓转给供应商， 加扣款方式：加款，说明供应商加钱， 总仓扣钱
        bo.setAmountType(AmountTypeEnum.ADD.getCode());
        bo.setCode(getDeductionInfoCode(DeductionInfoEnum.SUPPLIER.getColCode()));
        bo.setBillTime(new Date());
        bo.setAmount(refundAmount);
        bo.setDeductionType(deductionTypeEnum.getCode());
        bo.setBillCode(lossOrder.getReportLossNo());
        bo.setBillType(BillTypeEnum.REPORT_LOSS_ORDER.getCode());
        bo.setSupplierSkuId(lossOrder.getSupplierSkuId().toString());
        bo.setSaleDate(lossOrder.getSaleDate());
        bo.setSpuName(lossOrder.getSpuName());
        bo.setDeductionReason(deductionTypeEnum.getDesc());
        bo.setStatus(DeductionInfoStatusEnum.COMMITTED.getCode());
        bo.setCreateType(CreateTypeEnum.AUTO.getCode());
        return bo;
    }

    /**
     * 获取唯一编号
     *
     * @see
     */
    private String getDeductionInfoCode(String title) {
        // 创建SimpleDateFormat对象并设置日期格式
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        String dataFormat = dateFormat.format(new Date());
        String random = RandomUtil.randomNumbers(5);
        return (title + dataFormat + random);
    }


    private void updateOrderItemAfterSale(Long itemId, Integer newStatus) {
        orderItemMapper.updateAfterSaleStatus(itemId, newStatus);
//        if (count == 0) {
//            log.keyword("updateOrderItemAfterSale").error("售后状态异常");
//            throw new ServiceException("售后状态异常");
//        }
    }


    private String reportLossNo() {
        String key = "BS" + NoUtil.dateCodeShort();
        long atomicValue = RedisUtils.incrAtomicValue(key, Duration.ofDays(2));
        return key + String.format("%05d", atomicValue);
    }


    private void sendSms(long lossOrderId,BigDecimal refundTotalAmount) {
        ReportLossOrder lossOrder = lossOrderMapper.selectById(lossOrderId);
        String spuName = remoteSupplierSkuService.getSkuCombinationName(Lists.newArrayList(lossOrder.getSupplierSkuId())).get(lossOrder.getSupplierSkuId());
        OrderItem item = orderItemMapper.selectById(lossOrder.getOrderItemId());
        String orderCreatedTime = DateUtil.format(item.getCreateTime(), DatePattern.NORM_DATETIME_PATTERN);
        ReportLossStatusEnum lossStatusEnum = ReportLossStatusEnum.loadByCode(lossOrder.getLossStatus());
        switch (lossStatusEnum) {
            case REJECT:
                this.sendSmsOfReject(lossOrder, spuName, orderCreatedTime);
                break;
            case WAIT_AUDIT:
                this.sendSmsOfApply(lossOrder, spuName, orderCreatedTime);
                break;
            case FULL_PASS:
            case SUBTRACT_PASS:
                this.sendSmsOfPass(lossOrder, spuName, orderCreatedTime);
                break;
            case INTERVENTION:
                this.sendSmsOfIntervention(lossOrder, spuName, orderCreatedTime, refundTotalAmount);
                break;
            default:
                break;
        }
    }


    /**
     * 客户申请报损
     *
     * @param lossOrder
     * @return void
     * <AUTHOR> on 2024/9/2:15:04
     */
    private void sendSmsOfApply(ReportLossOrder lossOrder, String spuName, String orderCreatedTime) {
        RemoteCustomerVo customerVo = remoteBasCustomerService.getById(lossOrder.getCustomerId());

        LinkedHashMap<String, String> params = new LinkedHashMap<>();
        params.put("1", customerVo.getName());
        params.put("2", lossOrder.getSpuName());
        params.put("3", lossOrder.getLossAmount().add(lossOrder.getLossSubsidyFreeAmount()).toPlainString());

        params.put("orderCode", lossOrder.getOrderNo());
        params.put("spuName", spuName);
        params.put("orderAmount", lossOrder.getLossAmount().add(lossOrder.getLossSubsidyFreeAmount()).toPlainString());
        params.put("submitTime", orderCreatedTime);

        NotifyObjectTypeEnum objectType = null;
        List<String> objectIds = new ArrayList<>();
        if (lossOrder.getSupplierDeptId() > 0) {
            objectType = NotifyObjectTypeEnum.DEPT;
            objectIds.add(lossOrder.getSupplierDeptId().toString());
        } else {
            objectType = NotifyObjectTypeEnum.SUPPLIER;
            objectIds.add(lossOrder.getSupplierId().toString());
        }
        RemoteMessageNotifyV2Bo message = new RemoteMessageNotifyV2Bo(objectType, objectIds, MsgNotifyTemplateV2Enum.loss_approval, params, lossOrder.getOrderItemId().toString());
        remoteMessageNotifyService.sendMessageV2(message);
    }

    /**
     * 供应商驳回
     *
     * @param lossOrder
     * @return void
     * <AUTHOR> on 2024/9/2:15:04
     */
    private void sendSmsOfReject(ReportLossOrder lossOrder, String spuName, String orderCreatedTime) {
        LinkedHashMap<String, String> params = new LinkedHashMap<>();
        params.put("1", lossOrder.getSpuName());
        params.put("2", lossOrder.getLossAmount().toPlainString());

        params.put("orderCode", lossOrder.getOrderNo());
        params.put("spuName", spuName);
        params.put("refundAmount", "0");
        params.put("submitTime", orderCreatedTime);

        RemoteMessageNotifyV2Bo message = new RemoteMessageNotifyV2Bo(NotifyObjectTypeEnum.CUSTOMER, Lists.newArrayList(lossOrder.getCustomerId().toString()), MsgNotifyTemplateV2Enum.loss_disagree, params, lossOrder.getOrderItemId().toString());
        remoteMessageNotifyService.sendMessageV2(message);
    }

    /**
     * 供应商全额或减额通过
     *
     * @param lossOrder
     * @return void
     * <AUTHOR> on 2024/9/2:15:25
     */
    private void sendSmsOfPass(ReportLossOrder lossOrder, String spuName, String orderCreatedTime) {
        LinkedHashMap<String, String> params = new LinkedHashMap<>();
        params.put("1", lossOrder.getSpuName());
        params.put("2", lossOrder.getLossAmount().toPlainString());
        params.put("3", lossOrder.getRefundGoodsAmount().toPlainString());

        params.put("orderCode", lossOrder.getOrderNo());
        params.put("spuName", spuName);
        params.put("refundAmount", lossOrder.getRefundGoodsAmount().toPlainString());
        params.put("submitTime", orderCreatedTime);
        if (lossOrder.getLossStatus().equals(ReportLossStatusEnum.FULL_PASS.getCode())) {
            RemoteMessageNotifyV2Bo message = new RemoteMessageNotifyV2Bo(NotifyObjectTypeEnum.CUSTOMER, Lists.newArrayList(lossOrder.getCustomerId().toString()), MsgNotifyTemplateV2Enum.loss_all_agree, params, lossOrder.getOrderItemId().toString());
            remoteMessageNotifyService.sendMessageV2(message);
        } else if (lossOrder.getLossStatus().equals(ReportLossStatusEnum.SUBTRACT_PASS.getCode())) {
            RemoteMessageNotifyV2Bo message = new RemoteMessageNotifyV2Bo(NotifyObjectTypeEnum.CUSTOMER, Lists.newArrayList(lossOrder.getCustomerId().toString()), MsgNotifyTemplateV2Enum.loss_some_agree, params, lossOrder.getOrderItemId().toString());
            remoteMessageNotifyService.sendMessageV2(message);
        }
    }

    private void sendSmsOfIntervention(ReportLossOrder lossOrder, String spuName, String orderCreatedTime, BigDecimal refundTotalAmount) {
        LinkedHashMap<String, String> params = new LinkedHashMap<>();
        params.put("1", lossOrder.getSpuName());
        params.put("2", lossOrder.getLossAmount().toPlainString());
        params.put("3", refundTotalAmount.toPlainString());

        params.put("orderCode", lossOrder.getOrderNo());
        params.put("spuName", spuName);
        params.put("refundAmount", refundTotalAmount.toPlainString());
        params.put("submitTime", orderCreatedTime);
        RemoteMessageNotifyV2Bo message1 = new RemoteMessageNotifyV2Bo(NotifyObjectTypeEnum.CUSTOMER, Lists.newArrayList(lossOrder.getCustomerId().toString()), MsgNotifyTemplateV2Enum.loss_service, params, lossOrder.getOrderItemId().toString());
        remoteMessageNotifyService.sendMessageV2(message1);

        ReportLossOrderFlow flow = lossOrderFlowMapper.selectById(lossOrder.getFlowId());
        if (ResponsibilityTypeEnum.SUPPLIER.getCode().equals(flow.getResponsibilityType())) {
            //客服介入判责是供应商的责任，这时才需要通知供应商
            RemoteCustomerVo customerVo = remoteBasCustomerService.getById(lossOrder.getCustomerId());
            params.put("1", customerVo.getName());
            params.put("2", lossOrder.getSpuName());
            params.put("3", lossOrder.getLossAmount().add(lossOrder.getLossSubsidyFreeAmount()).toPlainString());
            params.put("4", lossOrder.getRefundGoodsActualAmount().toPlainString());

            NotifyObjectTypeEnum objectType = null;
            List<String> objectIds = new ArrayList<>();
            if (lossOrder.getSupplierDeptId() > 0) {
                objectType = NotifyObjectTypeEnum.DEPT;
                objectIds.add(lossOrder.getSupplierDeptId().toString());
            } else {
                objectType = NotifyObjectTypeEnum.SUPPLIER;
                objectIds.add(lossOrder.getSupplierId().toString());
            }
            RemoteMessageNotifyV2Bo message2 = new RemoteMessageNotifyV2Bo(objectType, objectIds, MsgNotifyTemplateV2Enum.loss_service, params, lossOrder.getOrderItemId().toString());
            remoteMessageNotifyService.sendMessageV2(message2);
        }
    }


    /**
     * IM报损单列表
     *
     * @param bo
     */
    @Override
    public TableDataInfo<ReportLossMiniListVo> lossOrderlist(ReportLossMiniListSearchBo bo) {
        bo.setCustomerId(LoginHelper.getLoginUser().getRelationId());

        // 设置7天内的默认时间范围
        bo.setSaleTimeStart(LocalDate.now().minusDays(7));

        // 群ID 查询相关的 用户，供应商，城市仓
        if (ObjectUtils.isNotEmpty(bo.getGroupId())) {
            //查询群聊groupId查询对应的用户筛选订单
            RemoteImGroupVo imGroup = remoteImGroupService.getByGroupId(bo.getGroupId());
            if (imGroup == null) {
                throw new ServiceException("群组不存在");
            }
            List<String> userList = JSONUtil.toList(imGroup.getGroupUser(), String.class);
            if (CollectionUtil.isEmpty(userList)) {
                throw new ServiceException("群组成员为空");
            }
            // 查询用户
            Map<String, RemoteUserBo> userMap = Optional.ofNullable(remoteUserService.getUsersByUserCodes(userList))
                    .orElseThrow(() -> new ServiceException("用户不存在"));

            // 客户、供应商、城市仓用户ID列表初始化
            List<String> supplierUserCodeList = new ArrayList<>();
            List<String> cityWhUserCodeList = new ArrayList<>();
            userMap.forEach((userId, user) -> {
                String userType = user.getUserType();
                if (ObjectUtil.equals(userType, SysUserTypeEnum.SUPPLIER_USER.getType())) {
                    supplierUserCodeList.add(userId);
                } else if (ObjectUtil.equals(userType, SysUserTypeEnum.CITY_USER.getType())) {
                    cityWhUserCodeList.add(userId);
                }
            });
            if (CollectionUtil.isNotEmpty(supplierUserCodeList)) {
                bo.setSupplierIdList(remoteSupplierService.getSupplierByAdminCode(supplierUserCodeList));
            }
            if (CollectionUtil.isNotEmpty(cityWhUserCodeList)) {
                bo.setCityWhIdList(remoteCityWhService.getListByAdminCode(cityWhUserCodeList));
            }
        }

        // 根据用户Code 查询相关的 用户，供应商，城市仓
        if (ObjectUtils.isNotEmpty(bo.getUserCode())) {
            RemoteUserBo userInfo = remoteUserService.getUserByUserCode(bo.getUserCode());
            if (userInfo != null) {
                if (ObjectUtil.equals(userInfo.getUserType(), SysUserTypeEnum.SUPPLIER_USER.getType())) {
                    bo.setSupplierIdList(remoteSupplierService.getSupplierByAdminCode(Collections.singletonList(bo.getUserCode())));
                } else if (ObjectUtil.equals(userInfo.getUserType(), SysUserTypeEnum.CITY_USER.getType())) {
                    bo.setCityWhIdList(remoteCityWhService.getListByAdminCode(Collections.singletonList(bo.getUserCode())));
                }
            }
        }
        return miniPage(bo);
    }
}
