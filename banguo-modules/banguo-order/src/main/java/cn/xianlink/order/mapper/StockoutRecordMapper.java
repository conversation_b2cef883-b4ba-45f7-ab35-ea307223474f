package cn.xianlink.order.mapper;


import cn.xianlink.common.mybatis.core.mapper.BaseMapperPlus;
import cn.xianlink.order.domain.StockoutRecord;
import cn.xianlink.order.domain.bo.stockOut.StockoutPageBO;
import cn.xianlink.order.domain.vo.stockOut.StockoutDetailVO;
import cn.xianlink.order.domain.vo.stockOut.StockoutPageVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

/**
 * 缺货少货单Mapper接口
 * <AUTHOR>
 * @date 2024-05-25
 */
public interface StockoutRecordMapper extends BaseMapperPlus<StockoutRecord, StockoutDetailVO> {

    /**
     * 缺货少货单分页查询
     */
    Page<StockoutPageVO> stockoutPage(@Param("bo") StockoutPageBO bo, @Param("page") Page<StockoutPageVO> page);

    StockoutRecord selectByCode(@Param("code") String code);

    StockoutDetailVO getStockoutOrderCodeByCode(@Param("billCode") String billCode);

    StockoutDetailVO getInfoById(@Param("id") Long id);

    StockoutDetailVO getInfoByCode(@Param("code") String code);
}
