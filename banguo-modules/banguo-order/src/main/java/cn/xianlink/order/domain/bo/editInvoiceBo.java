package cn.xianlink.order.domain.bo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 供应商编辑发票BO
 */
@Data
public class editInvoiceBo implements Serializable {
    
    /**
     * 发票ID (主键)
     */
    @NotNull(message = "发票ID不能为空")
    private Long id;
    
    /**
     * 发票格式 (electronic电子, paper纸质)
     */
    @NotNull(message = "发票格式不能为空")
    private String invoiceFormat;
    
    /**
     * 快递单号 (纸质发票供应商填写)
     */
    private String expressNumber;
    
    /**
     * 文件URL列表 (JSON数组，支持多张)
     */
    @NotNull(message = "文件URL列表不能为空")
    private List<InvoiceUploadFileBo> invoiceFileBoList;

    /**
     * 免税水果
     */
    @NotNull(message = "免税水果不能为空")
    private List<FreeTaxFruitBo> freeTaxFruitBoList;
}
