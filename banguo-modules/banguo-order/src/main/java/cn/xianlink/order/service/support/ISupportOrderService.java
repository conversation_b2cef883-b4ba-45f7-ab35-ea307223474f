package cn.xianlink.order.service.support;

import cn.xianlink.order.domain.Order;
import cn.xianlink.order.domain.order.vo.OriginOrderDetailVo;
import cn.xianlink.order.domain.order.vo.OriginOrderItemDetailVo;
import cn.xianlink.product.api.domain.vo.RemoteSkuVo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 订单服务接口 - 客服专用
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
public interface ISupportOrderService extends IService<Order> {

    /**
     * 根据订单号查询订单ID
     *
     * @param code 订单号
     * @return 订单ID
     */
    Long getIdByCode(String code);

    /**
     * 查询某个人最近N天的订单列表
     * @param customerId 客户ID
     * @param days 最近天数
     * @return 订单列表
     */
    List<OriginOrderDetailVo> getRecentOrderList(Long customerId, Integer days);

    /**
     * 查询某个客户最近N天的订单项列表（包含sku信息）
     * @param customerId 客户ID
     * @param days 最近天数
     * @param keyword 商品名称关键词，用于模糊搜索spu_name字段
     * @return 订单项列表
     */
    List<OriginOrderItemDetailVo> getRecentOrderItemList(Long customerId, Integer days, String keyword);

    /**
     * 按购物车+订单商品维度去重展示商品
     * @param regionWhId 总仓ID
     * @param customerId 客户ID
     * @param days 最近天数
     * @param keyword 商品名称关键词，用于模糊搜索
     * @return RemoteSkuVo列表
     */
    List<RemoteSkuVo> getRecentProductList(Long regionWhId,Long customerId, Integer days, String keyword);

}
