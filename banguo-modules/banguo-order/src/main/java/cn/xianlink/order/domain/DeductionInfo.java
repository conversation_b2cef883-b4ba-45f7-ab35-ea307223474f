package cn.xianlink.order.domain;

import cn.xianlink.common.tenant.core.TenantEntity;
import cn.xianlink.order.api.constant.*;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 扣款单对象 deduction_info
 *
 * <AUTHOR>
 * @date 2024-06-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("deduction_info")
public class DeductionInfo extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 扣款单信息主键id
     */
    @TableId(value = "id")
    private Long id;


    @ApiModelProperty("扣款单编号")
    private String code;

    @ApiModelProperty("总仓id")
    private Long regionWhId;

    @ApiModelProperty("总仓名称")
    private String regionWhName;

    /**
     * 通用id[外键]（根据type判断是供应商id，城市仓id，总仓id）
     */
    private Long commonId;

    @ApiModelProperty(value = "（供应商部门id）档口id")
    private Long supplierDeptId;

    /**
     * 通用名称（根据type判断是供应商名称，城市仓名称，总仓名称）
     */
    private String commonName;

    /**
     * 扣款单类型（1:供应商扣款单 2:城市仓加扣款单 3:总仓扣款单）
     */
    private Integer type;


    /**
     * @see AmountTypeEnum
     */
    @ApiModelProperty("加扣款类型（1:加款 2:扣款")
    private Integer amountType;

    /**
     * 单据日期
     */
    private Date billTime;

    /**
     * 金额（单位：元）
     */
    private BigDecimal amount;

    /**
     * 扣款原因（例如：1=少货，具体类型值请查看数据字典中供应商扣款原因）
     * @see DeductionTypeEnum
     */
    private Integer deductionType;

    /**
     * 关联单据(关联判责单的来源单据，如：判责单对应的少货单)
     */
    private String billCode;

    /**
     * 关联单据类型(单据类型：少货单，缺货单等)
     * @see BillTypeEnum
     */
    private Integer billType;

    /**
     * 结算单号
     */
    private String divideCode;

    @ApiModelProperty("结算id")
    private Long divideId;

    @ApiModelProperty("分账单号")
    private String sourceSplitNo;

    /**
     * 结算日期
     */
    private Date divideTime;

    /**
     * 销售批次（扣款原因属于盘库可为空）
     */
    private String supplierSkuId;

    /**
     * 销售周期（扣款原因属于盘库可为空）
     */
    private LocalDate saleTime;

    /**
     * 商品id
     */
    private String spuId;

    /**
     * 商品名称
     */
    private String spuName;

    /**
     * 扣款原因文字
     */
    private String deductionReason;

    /**
     * 凭证(多个图片地址用,分隔)
     */
    private String certificateUrl;

    /**
     * @see CreateTypeEnum
     */
    @ApiModelProperty("创建方式（0=自动，1=手动）")
    private Integer createType;

    /**
     * 状态(0:待提交 1:待结算 2:已结算)
     * @see DeductionInfoStatusEnum
     */
    private Integer status;

    /**
     * 删除标志（0代表存在 存入这条数据的主键代表删除）
     */
    private Long delFlag;

    /**
     * 物流id
     */
    private Long logisticsId;

    /**
     * 二级提货点id
     */
    private Long placeIdLevel2;

}
