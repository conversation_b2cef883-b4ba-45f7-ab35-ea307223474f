package cn.xianlink.order.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.basic.api.RemoteCustomerService;
import cn.xianlink.basic.api.RemoteRegionWhService;
import cn.xianlink.basic.api.domain.bo.RemoteCustomerQueryInfoBo;
import cn.xianlink.basic.api.domain.vo.RemoteCustomerInfoVo;
import cn.xianlink.basic.api.domain.vo.RemoteRegionWhVo;
import cn.xianlink.common.api.util.SaleDateUtil;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.order.api.bo.RemoteMkOrderDataStatBo;
import cn.xianlink.order.api.vo.RemoteMkDataStatVo;
import cn.xianlink.order.api.vo.RemoteMkOrderDataStatVo;
import cn.xianlink.order.domain.ActivityRecord;
import cn.xianlink.order.domain.bo.ActivityRecordBo;
import cn.xianlink.order.domain.order.vo.OrderVo;
import cn.xianlink.order.domain.vo.ActivityRecordVo;
import cn.xianlink.order.mapper.ActivityRecordMapper;
import cn.xianlink.order.mapper.OrderMapper;
import cn.xianlink.order.service.IActivityRecordService;
import cn.xianlink.product.api.RemoteSupplierSkuService;
import cn.xianlink.product.api.domain.bo.RemoteQueryInfoBo;
import cn.xianlink.product.api.domain.vo.RemoteSupplierSkuInfoVo;
import cn.xianlink.system.api.RemoteUserService;
import cn.xianlink.system.api.domain.bo.RemoteUserBo;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.security.Key;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 活动记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-15
 */
@RequiredArgsConstructor
@Service
@CustomLog
public class ActivityRecordServiceImpl implements IActivityRecordService {

    private final ActivityRecordMapper baseMapper;

    private final OrderMapper orderMapper;

    @DubboReference
    private final RemoteSupplierSkuService remoteSupplierSkuService;

    @DubboReference
    private final RemoteCustomerService remoteCustomerService;

    @DubboReference
    private final RemoteUserService remoteUserService;

    @DubboReference
    private final RemoteRegionWhService remoteRegionWhService;

    /**
     * 查询活动记录
     */
    @Override
    public ActivityRecordVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询活动记录列表
     */
    @Override
    public TableDataInfo<ActivityRecordVo> queryPageList(ActivityRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ActivityRecord> lqw = buildQueryWrapper(bo);
        Page<ActivityRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询活动记录列表
     */
    @Override
    public List<ActivityRecordVo> queryList(ActivityRecordBo bo) {
        LambdaQueryWrapper<ActivityRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ActivityRecord> buildQueryWrapper(ActivityRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ActivityRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getOrderCode()), ActivityRecord::getOrderCode, bo.getOrderCode());
        lqw.eq(bo.getOrderId() != null, ActivityRecord::getOrderId, bo.getOrderId());
        lqw.eq(bo.getCustomerId() != null, ActivityRecord::getCustomerId, bo.getCustomerId());
        lqw.like(StringUtils.isNotBlank(bo.getSpuName()), ActivityRecord::getSpuName, bo.getSpuName());
        lqw.eq(bo.getSupplierSpuId() != null, ActivityRecord::getSupplierSpuId, bo.getSupplierSpuId());
        lqw.eq(bo.getSupplierSkuId() != null, ActivityRecord::getSupplierSkuId, bo.getSupplierSkuId());
        lqw.eq(bo.getOrderItemId() != null, ActivityRecord::getOrderItemId, bo.getOrderItemId());
        lqw.eq(bo.getType() != null, ActivityRecord::getType, bo.getType());
        lqw.eq(bo.getActivityId() != null, ActivityRecord::getActivityId, bo.getActivityId());
        lqw.eq(bo.getFreeAmount() != null, ActivityRecord::getFreeAmount, bo.getFreeAmount());
        lqw.eq(StringUtils.isNotBlank(bo.getCreateCode()), ActivityRecord::getCreateCode, bo.getCreateCode());
        lqw.like(StringUtils.isNotBlank(bo.getCreateName()), ActivityRecord::getCreateName, bo.getCreateName());
        lqw.eq(StringUtils.isNotBlank(bo.getUpdateCode()), ActivityRecord::getUpdateCode, bo.getUpdateCode());
        lqw.like(StringUtils.isNotBlank(bo.getUpdateName()), ActivityRecord::getUpdateName, bo.getUpdateName());
        return lqw;
    }

    /**
     * 新增活动记录
     */
    @Override
    public Boolean insertByBo(ActivityRecordBo bo) {
        ActivityRecord add = MapstructUtils.convert(bo, ActivityRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改活动记录
     */
    @Override
    public Boolean updateByBo(ActivityRecordBo bo) {
        ActivityRecord update = MapstructUtils.convert(bo, ActivityRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ActivityRecord entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除活动记录
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public List<ActivityRecordVo> getActivityRecords(List<Long> activityIds, Integer type) {
        return baseMapper.getActivityRecords(activityIds, type);
    }

    /**
     * 营销活动数据统计
     * @param startDate
     * @param endDate
     * @return
     */
    @Override
    public RemoteMkDataStatVo getMkDataStat(Long activityId, Integer type, Integer ownerId, LocalDateTime startDate, LocalDateTime endDate) {
        RemoteMkDataStatVo mkDataStat = baseMapper.getMkDataStat(activityId, type, startDate, endDate, null, null);
        RemoteMkDataStatVo dataStatic = baseMapper.mkDataStatic(activityId, type, startDate, endDate, null, null);
        RemoteMkDataStatVo freeMkDataStat = baseMapper.mkDataFreeStatic(activityId, type, startDate, endDate, null, null);
        if (dataStatic != null) {
           // mkDataStat.setFreeActAmount(dataStatic.getFreeActAmount());
            mkDataStat.setOrderActAmount(mkDataStat.getOrderAmount().subtract(dataStatic.getRefundaAmount()));
        }
        if (freeMkDataStat != null) {
            mkDataStat.setFreeActAmount(freeMkDataStat.getFreeActAmount());
        }
        log.keyword("营销活动数据统计").info("mkDataStat={},dataStatic={},freeMkDataStat={}",
                JSON.toJSONString(mkDataStat), JSON.toJSONString(dataStatic), JSON.toJSONString(freeMkDataStat));
        return mkDataStat;
    }

    /**
     * 营销订单数据统计
     */
    @Override
    public TableDataInfo<RemoteMkOrderDataStatVo> getMkOrderDataStat(RemoteMkOrderDataStatBo bo) {
        //总仓
        /*RemoteRegionWhVo remoteRegionWhVo = remoteRegionWhService.queryById(Long.valueOf(bo.getOwnerId()));
        if (ObjectUtil.isNotNull(remoteRegionWhVo)) {
            LocalDate saleDate = SaleDateUtil.getSaleDate(remoteRegionWhVo.getSalesTimeEnd());
            bo.setOrderTimeStart(saleDate.atStartOfDay());
            bo.setSalesTimeEnd(saleDate.atTime(23, 59, 59));
        }*/
        //根据客户名称模糊查询客户集合
        List<Long> custIdList;
        if(ObjectUtils.isNotEmpty(bo.getCustomerName()) || ObjectUtils.isNotEmpty(bo.getCustomerAlias())) {
            RemoteCustomerQueryInfoBo custBo = new RemoteCustomerQueryInfoBo();
            custBo.setCustomerName(bo.getCustomerName());
            custBo.setCustomerAlias(bo.getCustomerAlias());
            List<RemoteCustomerInfoVo> customerInfoVos = remoteCustomerService.queryCustomerInfoList(custBo);
            if (CollectionUtils.isEmpty(customerInfoVos)) {
                return TableDataInfo.build();
            }
            custIdList = customerInfoVos.stream().map(RemoteCustomerInfoVo::getId).toList();
            bo.setCustIdList(custIdList);
        }

        //根据商品分类和商品名称查询商品集合
        List<Long> skuIdList;
        if(ObjectUtils.isNotEmpty(bo.getSkuName()) || CollectionUtils.isNotEmpty(bo.getCategoryIds())) {
            RemoteQueryInfoBo skuBo = new RemoteQueryInfoBo();
            skuBo.setSpuName(bo.getSkuName());
            skuBo.setCategoryIdList(CollectionUtils.isNotEmpty(bo.getCategoryIds())
                    ? bo.getCategoryIds().stream().distinct().toList() : null);
            List<RemoteSupplierSkuInfoVo> skuInfoVos = remoteSupplierSkuService.querySkuInfoList(skuBo);
            if (CollectionUtils.isEmpty(skuInfoVos)) {
                return TableDataInfo.build();
            }
            skuIdList = skuInfoVos.stream().map(RemoteSupplierSkuInfoVo::getId).toList();
            bo.setSkuIdList(skuIdList);
        }
        //查询活动营销关联订单
        Page<OrderVo> page = baseMapper.getActivityLeftOrder(bo, bo.build());
        if(page.getRecords() == null || page.getRecords().isEmpty()){
            return TableDataInfo.build();
        }
        List<OrderVo> orderVos = page.getRecords();
        //查询客户信息
        RemoteCustomerQueryInfoBo custBo = new RemoteCustomerQueryInfoBo();
        custBo.setCustomerIdList(orderVos.stream().map(OrderVo::getCustomerId).distinct().toList());
        Map<Long, RemoteCustomerInfoVo> custMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(custBo.getCustomerIdList())) {
            custMap = remoteCustomerService.queryCustomerInfoList(custBo).stream().filter(item -> item != null && ObjectUtils.isNotEmpty(item.getId()))
                    .collect(Collectors.toMap(RemoteCustomerInfoVo::getId, item -> item, (k1, k2) -> k1));
        }
        // 询用户信息
        Map<String, RemoteUserBo> userMap = new HashMap<>();
        if(!custMap.isEmpty()) {
            List<String> userCodes = custMap.values().stream().map(RemoteCustomerInfoVo::getAdminCode).filter(Objects::nonNull).distinct().toList();
            userMap = remoteUserService.getUsersByUserCodes(userCodes);
        }

        Map<String, RemoteUserBo> finalUserMap = userMap;
        Map<Long, RemoteCustomerInfoVo> finalCustMap = custMap;
        List<RemoteMkOrderDataStatVo> list = orderVos.stream().map(item -> {
            RemoteCustomerInfoVo cust = finalCustMap.get(item.getCustomerId());
            RemoteUserBo user = finalUserMap.get(cust.getAdminCode());
            RemoteMkOrderDataStatVo vo = new RemoteMkOrderDataStatVo();
            vo.setCustomerName(ObjectUtils.isNotEmpty(cust) ? cust.getName() : "");
            vo.setCustomerAlias(ObjectUtils.isNotEmpty(cust) ? cust.getAlias() : "");
            vo.setPhone(ObjectUtils.isNotEmpty(user) ? user.getPhoneNo() : "");
            vo.setOrderTime(item.getCreateTime());
            vo.setOrderNo(item.getCode());
            vo.setOrderAmount(item.getTotalAmount());
            ZoneId zone = ZoneId.systemDefault();
            Date date = Date.from(item.getSaleDate().atStartOfDay(zone).toInstant());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String dateStr = sdf.format(date);
            vo.setSaleDate(dateStr);
            return vo;
        }).toList();
        return TableDataInfo.build(list, page.getTotal());
    }
}