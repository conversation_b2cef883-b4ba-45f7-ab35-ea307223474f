package cn.xianlink.order.domain.order.bo;

import cn.xianlink.order.enums.OrderItemSettleTypeEnum;
import cn.xianlink.order.mq.consumer.SupItemSettleConsumer;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> xiaodaibing on 2024-06-28 14:46
 */
@Data
public class OrderItemSettleMqBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;
    //OrderItemSettleTypeEnum
    private Integer settleType;

    private List<Long> itemIds;

    private Long itemId;

    private Long skuId;

    private List<Long> skuIds;

    private Long orderId;

    private Long supplierId;

    /**
     * 分账单号
     */
    private String splitNo;
    /**
     * 结算单号
     */
    private String availNo;

    /**
     * 触发源头（结算定时器/退款）
     */
    private String triggerSource;

    public OrderItemSettleMqBo () {}

    /**
     * 批量结算
     * <AUTHOR> on 2024/8/23:10:28
     * @param itemIds
     * @return cn.xianlink.order.domain.order.bo.OrderItemSettleMqBo
     */
    public static OrderItemSettleMqBo itemAllSettleBuild(List<Long> itemIds) {
        OrderItemSettleMqBo bo = new OrderItemSettleMqBo();
        bo.settleType = OrderItemSettleTypeEnum.ITEM_ALL.getCode();
        bo.itemIds = itemIds;
        return bo;
    }

    public static OrderItemSettleMqBo jobSettleBuild(List<Long> itemIds) {
        OrderItemSettleMqBo bo = new OrderItemSettleMqBo();
        bo.settleType = OrderItemSettleTypeEnum.ITEM_ALL.getCode();
        bo.itemIds = itemIds;
        bo.setTriggerSource(SupItemSettleConsumer.jobSettleSource);
        return bo;
    }

    /**
     * 商品结算成功，刷新业务单据
     * <AUTHOR> on 2024/8/23:10:29
     * @param orderId
     * @param supplierId
     * @param itemId
     * @param splitNo
     * @param availNo
     * @return cn.xianlink.order.domain.order.bo.OrderItemSettleMqBo
     */
    public static OrderItemSettleMqBo orderBuild(Long orderId, Long supplierId, Long itemId, String splitNo, String availNo) {
        OrderItemSettleMqBo bo = new OrderItemSettleMqBo();
        bo.settleType = OrderItemSettleTypeEnum.ORDER.getCode();
        bo.orderId = orderId;
        bo.supplierId = supplierId;
        bo.splitNo = splitNo;
        bo.availNo = availNo;
        bo.itemId = itemId;
        return bo;
    }

    /**
     * 被分割后，真正进行结算
     * <AUTHOR> on 2024/8/23:10:30
     * @param itemId
     * @return cn.xianlink.order.domain.order.bo.OrderItemSettleMqBo
     */
    public static OrderItemSettleMqBo itemSettleBuild(Long itemId, String triggerSource) {
        OrderItemSettleMqBo bo = new OrderItemSettleMqBo();
        bo.settleType = OrderItemSettleTypeEnum.ITEM.getCode();
        bo.itemId = itemId;
        bo.triggerSource = triggerSource;
        return bo;
    }
}
