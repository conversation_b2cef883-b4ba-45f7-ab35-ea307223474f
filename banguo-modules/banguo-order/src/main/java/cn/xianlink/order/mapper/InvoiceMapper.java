package cn.xianlink.order.mapper;

import cn.xianlink.common.mybatis.core.mapper.BaseMapperPlus;
import cn.xianlink.order.domain.Invoice;
import cn.xianlink.order.domain.bo.InvoiceQueryBo;
import cn.xianlink.order.domain.vo.InvoiceOverviewVo;
import cn.xianlink.order.domain.vo.InvoiceQueryVo;
import cn.xianlink.order.domain.vo.InvoiceVo;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 发票Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface InvoiceMapper extends BaseMapperPlus<Invoice, InvoiceVo> {
    /**
     * 发票-查询待开发票的列表-分页
     */
    Page<InvoiceQueryVo> queryWaitInvoice(@Param("bo") InvoiceQueryBo bo, @Param("page")Page<Object> page);

    /**
     * 发票-查询待开发票的列表-不分页
     */
    List<InvoiceQueryVo> queryWaitInvoice(@Param("bo") InvoiceQueryBo bo);

    /**
     * 勾选待开发票的概况
     */
    InvoiceOverviewVo queryInvoiceOverview(@Param("bo") InvoiceQueryBo bo);
}
