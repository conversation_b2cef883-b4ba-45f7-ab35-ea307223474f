package cn.xianlink.order.domain.bo;

import cn.xianlink.order.domain.Invoice;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.HashMap;
import java.util.Map;
import cn.xianlink.common.mybatis.core.domain.BaseEntity;
import cn.xianlink.common.core.validate.AddGroup;
import cn.xianlink.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 发票业务对象 inv_invoice
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = Invoice.class, reverseConvertGenerate = false)
public class InvoiceBo extends BaseEntity {

    /**
     * 发票ID (主键)
     */
    @NotNull(message = "发票ID (主键)不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 发票号 (唯一)
     */
    @NotBlank(message = "发票号 (唯一)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String invoiceNumber;

    /**
     * 总仓id
     */
    @NotNull(message = "总仓id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long regionWhId;

    /**
     * 供应商ID[外键]
     */
    @NotNull(message = "供应商ID[外键]不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long supplierId;

    /**
     * 发票抬头ID[外键]
     */
    @NotNull(message = "发票抬头ID[外键]不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long invoiceTitleId;

    /**
     * 客户Id
     */
    @NotNull(message = "客户Id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long customerId;

    /**
     * 发票金额 (从发票项汇总)
     */
    @NotNull(message = "发票金额 (从发票项汇总)不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal invoiceAmount;

    /**
     * 开票日期
     */
    @NotNull(message = "开票日期不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date issueDate;

    /**
     * 状态 (applied申请中, uploaded已上传, audited_approved审核通过, audited_rejected审核拒绝)
     */
    @NotBlank(message = "状态 (applied申请中, uploaded已上传, audited_approved审核通过, audited_rejected审核拒绝)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    /**
     * 场景 (supplier供应商开票, general_warehouse总仓发票, city_warehouse城市仓发票等)
     */
    @NotBlank(message = "场景 (supplier供应商开票, general_warehouse总仓发票, city_warehouse城市仓发票等)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String scene;

    /**
     * 发票类型 (normal普票, special专票)
     */
    @NotBlank(message = "发票类型 (normal普票, special专票)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String invoiceType;

    /**
     * 发票格式 (electronic电子, paper纸质)
     */
    @NotBlank(message = "发票格式 (electronic电子, paper纸质)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String invoiceFormat;

    /**
     * 抬头类型 (company公司, personal个人)
     */
    @NotBlank(message = "抬头类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String titleType;

    /**
     * 供应商名称快照 (冗余)
     */
    private String supplierNameSnapshot;

    /**
     * 发票抬头快照 (冗余)
     */
    private String invoiceTitleSnapshot;

    /**
     * 税号快照 (冗余)
     */
    private String taxNumberSnapshot;

    /**
     * 地址快照 (冗余)
     */
    private String addressSnapshot;

    /**
     * 银行账号快照 (冗余)
     */
    private String bankAccountSnapshot;

    /**
     * 银行名称快照
     */
    private String bankNameSnapshot;

    /**
     * 电话快照 (冗余)
     */
    private String phoneSnapshot;

    /**
     * 邮箱地址快照 (冗余)
     */
    private String emailAddressSnapshot;

    /**
     * 快递_联系电话
     */
    private String contactPhone;

    /**
     * 快递_所在地区
     */
    private String region;

    /**
     * 快递_详细地址
     */
    private String detailedAddress;

    /**
     * 快递_姓名
     */
    private String recipientName;

    /**
     * 快递_纬度
     */
    private BigDecimal latitude;

    /**
     * 快递_经度
     */
    private BigDecimal longitude;

    /**
     * 快递单号 (纸质发票供应商填写)
     */
    private String expressNumber;

    /**
     * 文件URL列表 (JSON数组，支持多张)
     */
    private String fileUrls;

    /**
     * 申请日期
     */
    @NotNull(message = "申请日期不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date applyDate;

    /**
     * 审核日期
     */
    private Date auditDate;

    /**
     * 商户编码
     */
    private String merchantCode;

    /**
     * 商品合计件数
     */
    private Integer totalItems;

    /**
     * 开票订单区间
     */
    private String orderDateRange;

    /**
     * 创建用户代码
     */
    private String createCode;

    /**
     * 创建用户名称
     */
    private String createName;

    /**
     * 修改用户代码
     */
    private String updateCode;

    /**
     * 修改用户名称
     */
    private String updateName;


    /**
     * 请求参数
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @TableField(exist = false)
    private Map<String, Object> params = new HashMap<>();
}
