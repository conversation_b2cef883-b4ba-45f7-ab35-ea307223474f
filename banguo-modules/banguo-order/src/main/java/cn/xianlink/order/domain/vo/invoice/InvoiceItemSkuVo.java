package cn.xianlink.order.domain.vo.invoice;

import cn.xianlink.order.domain.InvoiceItem;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


/**
 * 发票项视图对象 inv_invoice_item
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = InvoiceItem.class)
public class InvoiceItemSkuVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 发票ID[外键]
     */
    @ExcelProperty(value = "发票ID[外键]")
    private Long invoiceId;

    /**
     * 客户Id
     */
    @ExcelProperty(value = "客户Id")
    private Long customerId;

    /**
     * 总仓id
     */
    @ExcelProperty(value = "总仓id")
    private Long regionWhId;

    /**
     * 供应商ID[外键]
     */
    @ExcelProperty(value = "供应商ID[外键]")
    private Long supplierId;

    /**
     * 订单唯一编码
     */
    @ExcelProperty(value = "订单唯一编码")
    private String orderCode;

    /**
     * 开票商品金额
     */
    @ExcelProperty(value = "开票商品金额")
    private BigDecimal productAmount;

    /**
     * 二级分类id
     */
    @ExcelProperty(value = "二级分类id")
    private Long categoryIdLevel2;

    /**
     * 二级分类名称
     */
    @ExcelProperty(value = "二级分类名称")
    private String categoryIdLevel2Name;


    /**
     * 平台商品ID
     */
    @ExcelProperty(value = "平台商品ID")
    private Long spuId;

    /**
     * SKU ID
     */
    @ExcelProperty(value = "SKU ID")
    private Long skuId;

    /**
     * 商品名称
     */
    @ExcelProperty(value = "商品名称")
    private String spuName;

    /**
     * 商品主图片路径
     */
    @ExcelProperty(value = "商品主图片路径")
    private String imgUrl;

    /**
     * 商品件数
     */
    @ExcelProperty(value = "商品件数")
    private Integer count;

    /**
     * 商品总毛重
     */
    @ExcelProperty(value = "商品总毛重")
    private BigDecimal spuGrossWeightTotal;

    /**
     * 商品总净重
     */
    @ExcelProperty(value = "商品总净重")
    private BigDecimal spuNetWeightTotal;

    /**
     * 开票代采服务费(元)
     */
    @ExcelProperty(value = "开票代采服务费(元)")
    private BigDecimal platformServiceAmount;

    /**
     * 开票平台运费金额(元)
     */
    @ExcelProperty(value = "开票平台运费金额(元)")
    private BigDecimal platformFreightAmount;

    /**
     * 开票基采运费金额(元)
     */
    @ExcelProperty(value = "开票基采运费金额(元)")
    private BigDecimal baseFreightAmount;

    /**
     * 免税金额(元)
     */
    @ExcelProperty(value = "免税金额(元)")
    private BigDecimal taxFreeAmount;

    /**
     * 免税重量（斤）
     */
    @ExcelProperty(value = "免税重量（斤）")
    private BigDecimal taxFreeWeight;

    /**
     * 剩余免税重量（斤）
     */
    @ExcelProperty(value = "剩余免税重量（斤）")
    private BigDecimal remainTaxFreeWeight;

    /**
     * 创建用户代码
     */
    @ExcelProperty(value = "创建用户代码")
    private String createCode;

    /**
     * 创建用户名称
     */
    @ExcelProperty(value = "创建用户名称")
    private String createName;

    /**
     * 修改用户代码
     */
    @ExcelProperty(value = "修改用户代码")
    private String updateCode;

    /**
     * 修改用户名称
     */
    @ExcelProperty(value = "修改用户名称")
    private String updateName;

    /**
     * 订单唯一编码(合计项，多个，string数组)
     */
    @ExcelProperty(value = "订单唯一编码")
    private List<String> orderCodes;


}
