package cn.xianlink.order.domain;

import cn.xianlink.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;

import java.io.Serial;

/**
 * 发票项对象 inv_invoice_item
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("inv_invoice_item")
public class InvoiceItem extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 发票项ID (主键)
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 发票ID[外键]
     */
    private Long invoiceId;

    /**
     * 客户Id
     */
    private Long customerId;

    /**
     * 总仓id
     */
    private Long regionWhId;

    /**
     * 供应商ID[外键]
     */
    private Long supplierId;

    /**
     * 订单唯一编码
     */
    private String orderCode;

    /**
     * 订单项ID[外键]
     */
    private Long orderItemId;

    /**
     * 商品金额
     */
    private BigDecimal productAmount;

    /**
     * 二级分类id
     */
    private Long categoryIdLevel2;

    /**
     * 平台商品ID
     */
    private Long spuId;

    /**
     * SKU ID
     */
    private Long skuId;

    /**
     * 商品名称
     */
    private String spuName;

    /**
     * 商品主图片路径
     */
    private String imgUrl;

    /**
     * 商品件数
     */
    private Integer count;

    /**
     * 商品总毛重
     */
    private BigDecimal spuGrossWeightTotal;

    /**
     * 商品总净重
     */
    private BigDecimal spuNetWeightTotal;

    /**
     * 开票代采服务费(元)
     */
    private BigDecimal platformServiceAmount;

    /**
     * 开票平台运费金额(元)
     */
    private BigDecimal platformFreightAmount;

    /**
     * 开票基采运费金额(元)
     */
    private BigDecimal baseFreightAmount;

    /**
     * 免税金额(元)
     */
    private BigDecimal taxFreeAmount;

    /**
     * 免税重量（斤）
     */
    private BigDecimal taxFreeWeight;

    /**
     * 剩余免税重量（斤）
     */
    private BigDecimal remainTaxFreeWeight;

    /**
     * 删除标志（0代表存在 存入这条数据的主键代表删除）
     */
    @TableLogic
    private Long delFlag;

    /**
     * 创建用户代码
     */
    private String createCode;

    /**
     * 创建用户名称
     */
    private String createName;

    /**
     * 修改用户代码
     */
    private String updateCode;

    /**
     * 修改用户名称
     */
    private String updateName;


}
