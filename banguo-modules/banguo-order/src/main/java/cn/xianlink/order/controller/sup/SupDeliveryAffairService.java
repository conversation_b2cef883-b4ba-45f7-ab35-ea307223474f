package cn.xianlink.order.controller.sup;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.basic.api.domain.vo.RemoteRegionWhParkingVo;
import cn.xianlink.common.api.enums.order.BlameSourceTypeEnum;
import cn.xianlink.common.api.enums.order.DeliveryStatusEnum;
import cn.xianlink.common.api.enums.order.OrderDeliverSourceEnum;
import cn.xianlink.common.api.enums.order.StockOutCreateSceneEnum;
import cn.xianlink.common.api.vo.RemoteSupplierSkuFileVo;
import cn.xianlink.common.core.enums.YNStatusEnum;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.json.utils.JsonUtils;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.order.controller.CustomToolService;
import cn.xianlink.order.controller.region.RegionEntruckAffairService;
import cn.xianlink.order.domain.RwEntruckGoods;
import cn.xianlink.order.domain.bo.stockOut.BatchCreateStockoutBO;
import cn.xianlink.order.domain.delivery.bo.SupDeliveryAddBo;
import cn.xianlink.order.domain.delivery.bo.SupDeliveryCancelBo;
import cn.xianlink.order.domain.delivery.bo.SupWaitDeliveryQueryBo;
import cn.xianlink.order.domain.delivery.vo.SupDeliveryVo;
import cn.xianlink.order.domain.delivery.vo.SupWaitDeliveryGoodsVo;
import cn.xianlink.order.domain.entruck.bo.RwEntruckRecordAddBo;
import cn.xianlink.order.domain.entruck.vo.RwEntruckGoodsSumVo;
import cn.xianlink.order.domain.entruck.vo.RwEntruckGoodsVo;
import cn.xianlink.order.domain.entruck.vo.RwEntruckRecordVo;
import cn.xianlink.order.domain.entruck.vo.RwWaitEntruckLogistics2Vo;
import cn.xianlink.order.domain.order.bo.QueryDeliverBo;
import cn.xianlink.order.domain.order.vo.QueryDeliverVo;
import cn.xianlink.order.domain.vo.stockOut.StockoutSkuRecordVO;
import cn.xianlink.order.service.*;
import cn.xianlink.system.api.model.LoginUser;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Component
@CustomLog
public class SupDeliveryAffairService {

    private final transient ISupDeliveryService deliveryService;
    private final transient ISupDeliveryGoodsService deliveryGoodsService;
    private final transient IRwEntruckRecordService entruckRecordService;
    private final transient IRwEntruckGoodsService entruckGoodsService;
    private final transient IOrderService orderService;
    private final transient CustomToolService customToolService;
    private final transient IStockoutSkuRecordService stockoutSkuRecordService;
    private final transient RegionEntruckAffairService regionEntruckAffairService;

    /**
     * 查询-待送货列表
     *
     * @param bo
     * @param isDelivery 是否送货
     * @return
     */
    public List<SupWaitDeliveryGoodsVo> waitDeliveryList(SupWaitDeliveryQueryBo bo, boolean isDelivery) {
        // 查询总仓物流线
        LinkedHashMap<Long, RemoteRegionWhParkingVo> logisticsVoMap = customToolService.getDeliveryLogistics(bo.getRegionWhId(), isDelivery);
        if (logisticsVoMap.size() == 0) {
            return new ArrayList<>();
        }
        List<Long> logisticsIdList = logisticsVoMap.keySet().stream().toList();
        Map<Long, Integer> logisticsIsEarlyEndMap = customToolService.queryLogisticsIsEarlyEndMap(logisticsIdList, bo.getSaleDate());
        // 查询条件  提前发车
        if (YNStatusEnum.ENABLE.getCode().equals(bo.getIsEarlyEnd())) {
            logisticsIdList = logisticsIsEarlyEndMap.entrySet().stream().filter(entry -> YNStatusEnum.ENABLE.getCode().equals(entry.getValue())).map(Map.Entry::getKey).collect(Collectors.toList());
        }
        // 查询订单商品项
        QueryDeliverBo orderBo = new QueryDeliverBo();
        orderBo.setSource(bo.getSource());
        orderBo.setBusinessTypes(bo.getBusinessTypes());
        orderBo.setNotBusinessTypes(bo.getNotBusinessTypes());
        orderBo.setPassAudit(bo.getPassAudit());
        orderBo.setSaleDate(bo.getSaleDate());
        orderBo.setRegionWhId(bo.getRegionWhId());
        orderBo.setSupplierId(bo.getSupplierId());
        orderBo.setSupplierDeptId(bo.getSupplierDeptId());
        orderBo.setSpuName(bo.getSpuName());
        orderBo.setLogisticsIdList(logisticsIdList);
        LoginUser user = LoginHelper.getLoginUser();
        if (bo.getOwn() && user != null) {
            orderBo.setBuyerCodeList(ListUtil.toList(user.getUserCode()));
        }
        List<QueryDeliverVo> orderItemVos = orderService.queryDeliver(orderBo);
        if (CollectionUtil.isEmpty(orderItemVos)) {
            return new ArrayList<>();
        }
        List<Long> supplierSkuIds = orderItemVos.stream().map(QueryDeliverVo::getSupplierSkuId).distinct().collect(Collectors.toList());
        // 供应商送货中、已装车、差异补送
        bo.setSupplierSkuIds(supplierSkuIds);
        bo.setLogisticsIdList(orderBo.getLogisticsIdList());
        Map<String, RwEntruckGoodsSumVo> entruckListMap = entruckGoodsService.customLogisticsSumListBySupplierId(bo)
                .stream().collect(Collectors.toMap(RwEntruckGoodsSumVo::getLogisticsIdAndSupplierSkuIdKey, Function.identity()));
        // 不补送、不采购：生成缺货单
        Map<String, Integer> stockoutListMap = stockoutSkuRecordService.queryStockoutSkuCountByLogisticsId(bo.getSaleDate(), bo.getRegionWhId(), bo.getSupplierId(), orderBo.getLogisticsIdList())
                .stream().collect(Collectors.toMap(StockoutSkuRecordVO::getLogisticsIdAndSupplierSkuIdKey, StockoutSkuRecordVO::getStockoutCount));
        // 计算待送货= 需求数-已装车-缺货记录
        orderItemVos.forEach(item -> {
            item.setCount(NumberUtil.sub(item.getCount(),
                    entruckListMap.get(item.getLogisticsIdAndSupplierSkuIdKey()) != null ? entruckListMap.get(item.getLogisticsIdAndSupplierSkuIdKey()).totalQuantity() : 0,
                    stockoutListMap.getOrDefault(item.getLogisticsIdAndSupplierSkuIdKey(), 0)
            ).intValue());
            item.setIsEarlyEnd(logisticsIsEarlyEndMap.getOrDefault(item.getLogisticsId(), YNStatusEnum.DISABLE.getCode()));
        });
        // 装车记录 按skuid分组
        Map<Long, List<RwEntruckGoodsSumVo>> entruckSkuListMap = entruckListMap.values().stream().collect(Collectors.groupingBy(RwEntruckGoodsSumVo::getSupplierSkuId));
        // 按商品skuid分组
        Map<Long, List<QueryDeliverVo>> skuListMap = orderItemVos.stream().filter(f -> f.getCount() > 0).collect(Collectors.groupingBy(QueryDeliverVo::getSupplierSkuId));
        // 组装待送货列表
//        boolean isOrderItemDelivery = regionEntruckAffairService.checkIsOrderItemDelivery(bo.getRegionWhId(), bo.getSaleDate());
        List<SupWaitDeliveryGoodsVo> list = new ArrayList<>();
        skuListMap.forEach((supplierSkuId, skuList) -> {
            Integer totalCount = skuList.stream().mapToInt(QueryDeliverVo::getCount).sum();
            // 待送货数量小于等于0 不显示
            if (totalCount <= 0) {
                return;
            }
            OptionalInt isEarlyEnd = skuList.stream().mapToInt(QueryDeliverVo::getIsEarlyEnd).max();
            QueryDeliverVo itemVo = skuList.get(0);
            SupWaitDeliveryGoodsVo goodsVo = new SupWaitDeliveryGoodsVo();
            goodsVo.setLogisticsList(skuList.stream().filter(f -> f.getCount() > 0).map(e -> {
                RwWaitEntruckLogistics2Vo entity = new RwWaitEntruckLogistics2Vo();
                entity.setLogisticsId(e.getLogisticsId());
                entity.setLogisticsName(logisticsVoMap.get(e.getLogisticsId()).getLogisticsName());
                entity.setWaitDeliveryQuantity(e.getCount());
                return entity;
            }).collect(Collectors.toList()));
            goodsVo.setSaleDate(bo.getSaleDate());
            goodsVo.setSupplierSkuId(supplierSkuId);
            goodsVo.setSupplierDeptId(itemVo.getSupplierDeptId());
            goodsVo.setSupplierDeptName(itemVo.getSupplierDeptName());
            goodsVo.setBusinessType(itemVo.getBusinessType());
            goodsVo.setIsExemptInspect(itemVo.getIsCheck());
            goodsVo.setBatchType(itemVo.getBatchType());
            goodsVo.setBatchPrice(itemVo.getPrice());
            goodsVo.setBuyerCode(itemVo.getBuyerCode());
            goodsVo.setBuyerName(itemVo.getBuyerName());
            goodsVo.setSpuName(itemVo.getSpuName());
            goodsVo.setImgUrl(itemVo.getImgUrl());
            goodsVo.setSpuGrossWeight(itemVo.getSpuGrossWeight());
            goodsVo.setSpuNetWeight(itemVo.getSpuNetWeight());
            goodsVo.setFreightRatio(itemVo.getFreightRatio());
            goodsVo.setSpuGrade(itemVo.getSpuGrade());
            goodsVo.setSpuStandards(itemVo.getSpuStandards());
            goodsVo.setProducer(itemVo.getProducer());
            goodsVo.setPackageWord(itemVo.getPackageWord());
            goodsVo.setWaitDeliveryQuantity(totalCount);
            // 产地简称
            goodsVo.setAreaCode(itemVo.getAreaCode());
            goodsVo.setBrand(itemVo.getBrand());
            goodsVo.setShortProducer(itemVo.getShortProducer());
            goodsVo.setSaleType(itemVo.getSaleType());
            List<RwEntruckGoodsSumVo> sumVoList = entruckSkuListMap.get(supplierSkuId);
            if (CollUtil.isNotEmpty(sumVoList)) {
                goodsVo.setAlreadyDeliveryQuantity(sumVoList.stream().mapToInt(RwEntruckGoodsSumVo::getDeliveryQuantity).sum());
                goodsVo.setAlreadyEntruckQuantity(sumVoList.stream().mapToInt(RwEntruckGoodsSumVo::getEntruckQuantity).sum());
            }
            goodsVo.setIsEarlyEnd(isEarlyEnd.isPresent() ? isEarlyEnd.getAsInt() : YNStatusEnum.DISABLE.getCode());
            // 待送货>0，送货中=0。 允许操作不采购
            if (goodsVo.getWaitDeliveryQuantity() > 0) {
                goodsVo.setIsShowNoPurchase(1);
                if (goodsVo.getAlreadyDeliveryQuantity() > 0) {
                    goodsVo.setIsShowNoPurchase(2);
                }
//                if (isOrderItemDelivery) {
//                    goodsVo.setIsShowNoPurchase(2);
//                }
            }
            // 送货中=0，已装车=0，批次类型=正常，非换供应商的商品项。  允许更换供应商
            if (goodsVo.getAlreadyDeliveryQuantity() == 0
                    && goodsVo.getAlreadyEntruckQuantity() == 0
                    && goodsVo.getBatchType() == 1
                    && ObjectUtil.isNull(itemVo.getOriginalOrderItemId())) {
                goodsVo.setIsShowReplaceSupplier(1);
            }
            list.add(goodsVo);
        });
        // 加载商品文件列表
        Map<Long, List<RemoteSupplierSkuFileVo>> fileListMap = customToolService.getSkuFileListMap(list.stream().map(SupWaitDeliveryGoodsVo::getSupplierSkuId).collect(Collectors.toList()));
        list.forEach(e -> e.setFileList(fileListMap.get(e.getSupplierSkuId())));
        return list;
    }

    /**
     * 创建送货单&装车记录
     */
    @Transactional(rollbackFor = Throwable.class)
    public void createDelivery(SupDeliveryAddBo bo, List<RwEntruckRecordAddBo> recordBos) {
        if (CollUtil.isEmpty(recordBos)) {
            recordBos = regionEntruckAffairService.getEntruckRecordAddBoList(bo, OrderDeliverSourceEnum.SUPPLIER.getCode(), true);
            if (CollectionUtil.isEmpty(recordBos)) {
                throw new ServiceException("查询需求项失败");
            }
            bo.setStatus(DeliveryStatusEnum.WAIT_INSPECT.getCode());
            long exemptInspectCount = bo.getGoodsList().stream().filter(f -> f.getIsExemptInspect().intValue() == YNStatusEnum.ENABLE.getCode()).count();
            if (exemptInspectCount == bo.getGoodsList().size()) {
                // 明细商品全部为免检
                bo.setStatus(DeliveryStatusEnum.COMPLETE_INSPECT.getCode());
                recordBos.forEach(e -> e.setStatus(DeliveryStatusEnum.COMPLETE_INSPECT.getCode()));
            }
            // 加载关联订单项数据
            regionEntruckAffairService.loadDeliveryOrderItemList(recordBos);
        }
        // 创建送货单
        int rows = deliveryService.insert(bo);
        if (rows <= 0) {
            throw new ServiceException("创建送货单失败");
        }
        // 创建装车记录
        entruckRecordService.insert(bo.getId(), bo.getDeliveryNo(), recordBos);
    }

    /**
     * 取消送货单&装车记录
     */
    @Transactional(rollbackFor = Throwable.class)
    public int cancelDelivery(SupDeliveryCancelBo bo, String source) {
        SupDeliveryVo vo = deliveryService.selectAndCheckNullById(bo.getId());
        if (vo.getStatus().intValue() != DeliveryStatusEnum.WAIT_INSPECT.getCode()) {
            throw new ServiceException("非待质检状态，不允许取消");
        }
        if (deliveryGoodsService.isExistInspectRecord(bo.getId())) {
            throw new ServiceException("送货单商品质检中，不允许取消");
        }
        if (OrderDeliverSourceEnum.REGION.getCode().equals(source)
                && !LoginHelper.getLoginUser().getUserCode().equals(vo.getCreateCode())) {
            throw new ServiceException("只允许创建人操作取消");
        }
        int rows = deliveryService.cancelDelivery(bo);
        if (rows > 0) {
            entruckRecordService.updateStatusByDeliveryId(bo.getId(), DeliveryStatusEnum.CANCEL_DELIVERY.getCode());
        }
        return rows;
    }

    /**
     * 已装车-差异处理
     * @param source  调用源：1供应商、2总仓、3auto、4完成分货
     * @param deliveryId
     * @param diffStatusMap
     * @return
     */
    @Transactional(rollbackFor = Throwable.class)
    public int diffHandle(Integer source, Long deliveryId, Map<Long, Integer> diffStatusMap) {
        SupDeliveryVo deliveryVo = deliveryService.selectAndCheckNullById(deliveryId);
        if (deliveryVo.getDiffStatus() != DeliveryStatusEnum.ENTRUCK_HAVE_DIFF.getCode()) {
            throw new ServiceException("非差异待处理状态，不允许处理");
        }
        List<RwEntruckRecordVo>  recordVos = entruckRecordService.selectListByDeliveryId(deliveryId, null);
        List<RwEntruckGoods> entityList = new ArrayList<>();
        List<BatchCreateStockoutBO> stockoutBOList = new ArrayList<>();
        for (RwEntruckRecordVo recordVo : recordVos) {
            for (RwEntruckGoodsVo goodsVo : recordVo.getGoodsList()) {
                if (goodsVo.getDiffStatus().intValue() != DeliveryStatusEnum.ENTRUCK_HAVE_DIFF.getCode()) {
                    continue;
                }
                int diffStatus;
                if (diffStatusMap != null) {
                    // 人工处理
                    if (diffStatusMap.get(goodsVo.getSupplierSkuId()) == null) {
                        continue;
                    }
                    diffStatus = diffStatusMap.get(goodsVo.getSupplierSkuId());
                } else {
                    // 超时自动处理
                    diffStatus = DeliveryStatusEnum.DIFF_NO_DELIVERY.getCode();
                }
                RwEntruckGoods entity = new RwEntruckGoods();
                entity.setId(goodsVo.getId());
                entity.setDiffStatus(diffStatus);
                entity.setDiffDealTime(DateTime.now());
                entityList.add(entity);
                // 差异处理-不补送 生成缺货单
                if (entity.getDiffStatus().intValue() == DeliveryStatusEnum.DIFF_NO_DELIVERY.getCode()) {
                    BatchCreateStockoutBO stockoutBo = new BatchCreateStockoutBO();
                    stockoutBo.setRelationId(recordVo.getId());
                    stockoutBo.setSaleDate(recordVo.getSaleDate());
                    stockoutBo.setRegionWhId(recordVo.getRegionWhId());
                    stockoutBo.setSkuSupplierId(recordVo.getSupplierId());
                    stockoutBo.setSkuSupplierDeptId(recordVo.getSupplierDeptId());
                    stockoutBo.setLogisticsId(recordVo.getLogisticsId());
                    stockoutBo.setType(BlameSourceTypeEnum.STOCKOUT.getCode());
                    stockoutBo.setSupplierSkuId(goodsVo.getSupplierSkuId());
                    stockoutBo.setSpuName(goodsVo.getSpuName());
                    stockoutBo.setStockoutCount(NumberUtil.sub(goodsVo.getDeliveryQuantity(), goodsVo.getEntruckQuantity()).intValue());
                    stockoutBo.setCreateScene(StockOutCreateSceneEnum.NO_DELIVERY.getCode());
                    if (source == 1) {
                        stockoutBo.setSupplierId(LoginHelper.getLoginUser().getUserId());
                    } else if (source == 2) {
                        stockoutBo.setBuyerId(LoginHelper.getLoginUser().getUserId());
                    } else {
                        stockoutBo.setSystemAuto(true);
                        if (source == 3) {
                            stockoutBo.setCreateScene(StockOutCreateSceneEnum.AUTO_NO_DELIVERY.getCode());
                        } else { // 4
                            stockoutBo.setCreateScene(StockOutCreateSceneEnum.ENTRUCK_GOODS.getCode());
                            stockoutBo.setBuyerId(LoginHelper.getLoginUser().getUserId());
                        }
                    }
                    stockoutBOList.add(stockoutBo);
                }
            }
        }

        int rows = deliveryService.updateDiffStatus(deliveryId, DeliveryStatusEnum.DIFF_NO_DELIVERY.getCode());
        if (rows > 0 && entityList.size() > 0) {
            entruckGoodsService.updateEntruck(entityList);
            if (stockoutBOList.size() > 0) {
                // 创建缺货单
                log.keyword("diffHandle").info("createStockout {}", JsonUtils.toJsonString(stockoutBOList));
                regionEntruckAffairService.createStockout(stockoutBOList);
            }
        }
        return rows;
    }
}
