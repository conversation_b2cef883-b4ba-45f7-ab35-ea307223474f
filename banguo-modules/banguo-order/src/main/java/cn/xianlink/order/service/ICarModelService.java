package cn.xianlink.order.service;

import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.order.domain.bo.CarModelBo;
import cn.xianlink.order.domain.vo.CarModelVo;

import java.util.Collection;
import java.util.List;

/**
 * 车型Service接口
 *
 * <AUTHOR>
 * @date 2024-06-11
 */
public interface ICarModelService {

    /**
     * 查询车型
     */
    CarModelVo queryById(Long id);

    /**
     * 查询车型列表
     */
    TableDataInfo<CarModelVo> queryPageList(CarModelBo bo, PageQuery pageQuery);

    /**
     * 查询车型列表
     */
    List<CarModelVo> queryList(CarModelBo bo);

    /**
     * 新增车型
     */
    Boolean insertByBo(CarModelBo bo);

    /**
     * 修改车型
     */
    Boolean updateByBo(CarModelBo bo);

    /**
     * 校验并批量删除车型信息
     */
    Boolean deleteWithValidByIds(List<Long> ids);
}
