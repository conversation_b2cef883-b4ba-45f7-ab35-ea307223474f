package cn.xianlink.order.domain;

import cn.xianlink.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 车型表对象 car_list
 *
 * <AUTHOR>
 * @date 2025-01-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("car_list")
public class CarList extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 车型表信息主键id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 车辆型号
     */
    private String model;

    /**
     * 总仓id
     */
    private Long regionWhId;

    /**
     * 载重（单位：吨）
     */
    private BigDecimal carryWeight;

    /**
     * 装车费（单位：元）
     */
    private BigDecimal entruckFee;

}
