package cn.xianlink.order.controller.city;


import cn.hutool.core.collection.CollectionUtil;
import cn.xianlink.basic.api.RemoteCityWhPlaceService;
import cn.xianlink.basic.api.RemoteRegionLogisticsService;
import cn.xianlink.common.api.enums.order.DeliveryStatusEnum;
import cn.xianlink.common.api.enums.order.OrderDeliverSourceEnum;
import cn.xianlink.common.api.enums.order.SortGoodsStatusEnum;
import cn.xianlink.common.api.enums.order.ToDosEnum;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.controller.TodoService;
import cn.xianlink.order.domain.todo.bo.TodoBo;
import cn.xianlink.order.domain.todo.vo.TodoGoodsVo;
import cn.xianlink.order.domain.todo.vo.TodoVo;
import cn.xianlink.system.api.model.LoginUser;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 城市仓小程序-待办
 *
 * <AUTHOR>
 * @date 2025-01-05
 * @folder 城市仓端(小程序)/待办
 */
@Validated
@RequiredArgsConstructor
@RestController()
@RequestMapping("/order/city/todo/")
public class CityTodoController extends BaseController {
    private transient final TodoService todoService;
    @DubboReference
    private final transient RemoteRegionLogisticsService remoteRegionLogisticsService;

    @DubboReference
    private final transient RemoteCityWhPlaceService remoteCityWhPlaceService;

    /**
     * 待办列表
     */
    @PostMapping("/list")
    public R<List<TodoVo>> list(@RequestBody TodoBo bo) {
        LoginUser user = LoginHelper.getLoginUser();
        bo.setCacheUserId(user.getUserId());
        bo.setCityWhId(user.getRelationId());
        bo.setSource(OrderDeliverSourceEnum.CITY.getCode());
        if (CollectionUtil.isNotEmpty(bo.getPlaceIdList())) {
            bo.setLogisticsIds(remoteRegionLogisticsService.queryIdByCityUserFiled(LoginHelper.getLoginUser().getUserId(), bo.getPlaceIdList()));
        }else if (CollectionUtil.isEmpty(bo.getPlaceIdList()) && CollectionUtil.isEmpty(bo.getPlaceIdLevel2List())){
            bo.setLogisticsIds(remoteRegionLogisticsService.queryIdByCityUser(LoginHelper.getLoginUser().getUserId()));
            bo.setPlaceIdLevel2List(remoteCityWhPlaceService.getPlaceIdsByUser(LoginHelper.getLoginUser().getUserId()));
        }
        bo.setCashKey(bo.getCashValue(bo));
        List<TodoVo> list = new ArrayList<>();
        // 待接车
        list.add(new TodoVo(ToDosEnum.WAIT_PICKUP.getCode(), todoService.waitDepartCount(bo, DeliveryStatusEnum.COMPLETE_DEPART.getCode())));
        // 待分货
        list.add(new TodoVo(ToDosEnum.WAIT_SORT_GOODS.getCode(), todoService.waitConfirmSortList(bo, SortGoodsStatusEnum.STAY.getCode())));
        // 分货中
        list.add(new TodoVo(ToDosEnum.SORT_GOODS_IN.getCode(), todoService.waitConfirmSortList(bo, SortGoodsStatusEnum.SORTING.getCode())));
        // 待完成分货
        list.add(new TodoVo(ToDosEnum.WAIT_FINISH_SORT_GOODS.getCode(), todoService.waitConfirmSortList(bo, SortGoodsStatusEnum.SORTYET.getCode())));
        // 少货待确认
        list.add(new TodoVo(ToDosEnum.AUDIT_LESS_GOODS.getCode(), todoService.waitConfirmStockOutList(bo, 2)));

        return R.ok(list.stream().filter(f-> f.getSkuCount() > 0).collect(Collectors.toList()));
    }


    /**
     * 待办明细
     */
    @PostMapping("/details")
    public R<List<TodoGoodsVo>> details(@RequestBody TodoBo bo) {
        LoginUser user = LoginHelper.getLoginUser();
        bo.setCacheUserId(user.getUserId());
        bo.setCityWhId(user.getRelationId());
        List<TodoGoodsVo> list = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(bo.getPlaceIdList())) {
            bo.setLogisticsIds(remoteRegionLogisticsService.queryIdByCityUserFiled(LoginHelper.getLoginUser().getUserId(), bo.getPlaceIdList()));
        }else if (CollectionUtil.isEmpty(bo.getPlaceIdList()) && CollectionUtil.isEmpty(bo.getPlaceIdLevel2List())){
            bo.setLogisticsIds(remoteRegionLogisticsService.queryIdByCityUser(LoginHelper.getLoginUser().getUserId()));
            bo.setPlaceIdLevel2List(remoteCityWhPlaceService.getPlaceIdsByUser(LoginHelper.getLoginUser().getUserId()));
        }
        //  待分货、分货中、待完成分货
        Integer status = null;
        if (bo.getTodoCode().intValue() == ToDosEnum.WAIT_SORT_GOODS.getCode()) {
            status = SortGoodsStatusEnum.STAY.getCode();
        } else if (bo.getTodoCode().intValue() == ToDosEnum.SORT_GOODS_IN.getCode()) {
            status = SortGoodsStatusEnum.SORTING.getCode();
        } else if (bo.getTodoCode().intValue() == ToDosEnum.WAIT_FINISH_SORT_GOODS.getCode()) {
            status = SortGoodsStatusEnum.SORTYET.getCode();
        }
        if (status != null) {
            list = todoService.waitConfirmSortList(bo, status).getDataList();;
        }
        //  少货待确认
        else if (bo.getTodoCode().intValue() == ToDosEnum.AUDIT_LESS_GOODS.getCode()) {
            list = todoService.waitConfirmStockOutList(bo, 2).getDataList();;
        }
        return R.ok(list);
    }
}