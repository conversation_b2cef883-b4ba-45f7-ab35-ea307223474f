package cn.xianlink.order.domain.vo;

import cn.xianlink.order.domain.InvDutyFreeGoods;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.converters.date.DateStringConverter;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * 免税商品视图对象 inv_duty_free_goods
 *
 * <AUTHOR>
 * @date 2025-01-30
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = InvDutyFreeGoods.class)
public class InvDutyFreeGoodsVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 免税重量
     */
    @ExcelProperty(value = "免税重量（吨）")
    private BigDecimal weight;

    /**
     * 免税时间-开始
     */
    @ExcelProperty(value = "免税开始时间", converter = DateStringConverter.class)
    private LocalDate freeDateStart;

    /**
     * 免税时间-结束
     */
    @ExcelProperty(value = "免税结束时间", converter = DateStringConverter.class)
    private LocalDate freeDateEnd;

    /**
     * 采购人员代码
     */
    @ExcelProperty(value = "采购人员代码")
    private String purchUserCode;

    /**
     * 采购人员名称
     */
    @ExcelProperty(value = "采购人员名称")
    private String purchUserName;

    /**
     * 商品分类id
     */
    @ExcelProperty(value = "商品分类ID")
    private Long categoryId;

    /**
     * 商品分类名称
     */
    @ExcelProperty(value = "商品分类名称")
    private String categoryName;

    /**
     * 审核状态，0待审核；1审核通过；2审核不通过
     */
    @ExcelProperty(value = "审核状态")
    private Integer status;

    /**
     * 审核状态描述
     */
    @ExcelProperty(value = "审核状态描述")
    private String statusDesc;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 创建用户代码
     */
    @ExcelProperty(value = "创建用户代码")
    private String createCode;

    /**
     * 创建用户名称
     */
    @ExcelProperty(value = "创建用户名称")
    private String createName;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 修改用户代码
     */
    @ExcelProperty(value = "修改用户代码")
    private String updateCode;

    /**
     * 修改用户名称
     */
    @ExcelProperty(value = "修改用户名称")
    private String updateName;

    /**
     * 修改时间
     */
    @ExcelProperty(value = "修改时间")
    private Date updateTime;

    /**
     * 免税天数（计算字段）
     */
    @ExcelProperty(value = "免税天数")
    private Long freeDays;

    /**
     * 是否有效（计算字段）
     */
    @ExcelProperty(value = "是否有效")
    private Boolean isValid;

}
