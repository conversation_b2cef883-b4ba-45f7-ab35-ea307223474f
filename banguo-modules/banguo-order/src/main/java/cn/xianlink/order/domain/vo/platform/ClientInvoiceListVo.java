package cn.xianlink.order.domain.vo.platform;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 客户端发票列表视图对象（开票中和已开票）
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@ExcelIgnoreUnannotated
public class ClientInvoiceListVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 发票ID
     */
    @ExcelProperty(value = "发票ID")
    private Long id;

    /**
     * 发票号
     */
    @ExcelProperty(value = "发票号")
    private String invoiceNumber;

    /**
     * 供应商ID
     */
    @ExcelProperty(value = "供应商ID")
    private Long supplierId;

    /**
     * 供应商名称
     */
    @ExcelProperty(value = "供应商名称")
    private String supplierName;

    /**
     * 发票金额
     */
    @ExcelProperty(value = "发票金额")
    private BigDecimal invoiceAmount;

    /**
     * 发票类型 (normal普票, special专票)
     */
    @ExcelProperty(value = "发票类型")
    private String invoiceType;

    /**
     * 发票格式 (electronic电子, paper纸质)
     */
    @ExcelProperty(value = "发票格式")
    private String invoiceFormat;

    /**
     * 状态 (applied申请中, uploaded已上传, audited_approved审核通过, audited_rejected审核拒绝)
     */
    @ExcelProperty(value = "状态")
    private String status;

    /**
     * 状态描述
     */
    @ExcelProperty(value = "状态描述")
    private String statusDesc;

    /**
     * 发票抬头
     */
    @ExcelProperty(value = "发票抬头")
    private String invoiceTitle;

    /**
     * 商品合计件数
     */
    @ExcelProperty(value = "商品合计件数")
    private Integer totalItems;

    /**
     * 申请日期
     */
    @ExcelProperty(value = "申请日期")
    private Date applyDate;

    /**
     * 开票日期
     */
    @ExcelProperty(value = "开票日期")
    private Date issueDate;

    /**
     * 审核日期
     */
    @ExcelProperty(value = "审核日期")
    private Date auditDate;

    /**
     * 开票订单区间
     */
    @ExcelProperty(value = "开票订单区间")
    private String orderDateRange;

    /**
     * 邮箱地址
     */
    @ExcelProperty(value = "邮箱地址")
    private String emailAddress;

    /**
     * 邮寄地址
     */
    @ExcelProperty(value = "邮寄地址")
    private String mailingAddress;

    /**
     * 快递单号
     */
    @ExcelProperty(value = "快递单号")
    private String expressNumber;

    /**
     * 是否有发票文件
     */
    private Boolean hasInvoiceFile;

    /**
     * 发票文件数量
     */
    private Integer fileCount;
}
