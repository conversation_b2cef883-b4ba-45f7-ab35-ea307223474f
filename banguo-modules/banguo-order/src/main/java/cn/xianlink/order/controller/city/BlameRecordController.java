package cn.xianlink.order.controller.city;

import cn.hutool.core.collection.CollectionUtil;
import cn.xianlink.basic.api.RemoteCityWhPlaceService;
import cn.xianlink.basic.api.RemoteRegionLogisticsService;
import cn.xianlink.common.api.enums.order.AccountTypeStatusEnum;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.order.domain.bo.blameRecord.BlameRecordPageBO;
import cn.xianlink.order.domain.bo.blameRecord.ComplaintBlameRecordBO;
import cn.xianlink.order.domain.vo.blameRecord.BlameRecordInfoVO;
import cn.xianlink.order.domain.vo.blameRecord.BlameRecordPageVO;
import cn.xianlink.order.domain.vo.common.CommonStatusCountVO;
import cn.xianlink.order.service.IBlameRecordService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import cn.xianlink.common.web.core.BaseController;

import java.util.List;

/**
 * 城市仓小程序-判责单
 *
 * <AUTHOR>
 * @date 2024-05-25
 */
@Validated
@RequiredArgsConstructor
@RestController("cityBlameRecordController")
@RequestMapping("/order/city/blame")
public class BlameRecordController extends BaseController {

    private final IBlameRecordService blameRecordService;

    @DubboReference
    private final transient RemoteRegionLogisticsService remoteRegionLogisticsService;

    @DubboReference
    private final transient RemoteCityWhPlaceService remoteCityWhPlaceService;

    @PostMapping("/blameRecordPage")
    @ApiOperation(value = "判责单列表")
    public R<TableDataInfo<BlameRecordPageVO>> blameRecordPage(@RequestBody BlameRecordPageBO bo){
        if (bo.getCityWhId() == null && LoginHelper.getLoginUser().getRelationId() == null) {
            return R.ok(TableDataInfo.build());
        }else {
            bo.setCityWhId(LoginHelper.getLoginUser().getRelationId());
        }
        if (CollectionUtil.isNotEmpty(bo.getPlaceIdList())) {
            bo.setLogisticsIds(remoteRegionLogisticsService.queryIdByCityUserFiled(LoginHelper.getLoginUser().getUserId(), bo.getPlaceIdList()));
        } else if (CollectionUtil.isEmpty(bo.getPlaceIdList()) && CollectionUtil.isEmpty(bo.getPlaceIdLevel2List())) {
            bo.setLogisticsIds(remoteRegionLogisticsService.queryIdByCityUser(LoginHelper.getLoginUser().getUserId()));
            bo.setPlaceIdLevel2List(remoteCityWhPlaceService.getPlaceIdsByUser(LoginHelper.getLoginUser().getUserId()));
        }
        bo.setProductResType(2);
        return R.ok(blameRecordService.blameRecordDeatilPage(bo));
    }

    @GetMapping("/getBlameById/{id}")
    @ApiOperation(value = "判责单详情")
    public R<BlameRecordInfoVO> getBlameById(@PathVariable("id") Long id){
        return R.ok(blameRecordService.getBlameById(id,AccountTypeStatusEnum.CITY.getCode()));
    }

    @GetMapping("/getBlameBySource/{code}")
    @ApiOperation(value = "判责单详情")
    public R<BlameRecordInfoVO> getBlameBySource(@PathVariable("code") String code){
        return R.ok(blameRecordService.getBlameBySource(code));
    }

    @GetMapping("/confirm/{id}")
    @RepeatSubmit()
    @ApiOperation(value = "确认判责")
    public R<Void> confirm(@PathVariable("id") Long id){
        blameRecordService.confirm(id,AccountTypeStatusEnum.CITY.getCode(),true);
        return R.ok();
    }

    @GetMapping("/getBlameCount")
    @ApiOperation(value = "获取判责单各状态数量")
    public R<List<CommonStatusCountVO>> getBlameCount(){
        return R.ok(blameRecordService.getBlameCount(AccountTypeStatusEnum.CITY.getCode(), null));
    }

    @PostMapping("/complaint")
    @RepeatSubmit()
    @ApiOperation(value = "判责申诉")
    public R<Void> complaint(@RequestBody ComplaintBlameRecordBO bo){
        blameRecordService.complaint(bo);
        return R.ok();
    }
}
