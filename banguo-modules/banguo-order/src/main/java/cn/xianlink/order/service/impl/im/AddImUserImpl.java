package cn.xianlink.order.service.impl.im;

import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.order.domain.im.bo.ImBo;
import cn.xianlink.order.domain.im.vo.ImVo;
import cn.xianlink.order.enums.BizTypeEnum;
import cn.xianlink.order.service.im.IImStrategy;
import cn.xianlink.system.api.RemoteTencentImService;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
@CustomLog
public class AddImUserImpl implements IImStrategy {
    @DubboReference
    private final RemoteTencentImService tencentImService;

    @Override
    public ImVo handle(ImBo req) {
        try {
            if(ObjectUtil.isEmpty(req.getGroupBizType())
                    || ObjectUtil.isEmpty(req.getGroupBizType())
                    || CollectionUtils.isEmpty(req.getUserList())) {
                throw new ServiceException("参数不能为空");
            }
            // IM群组新增群成员
            tencentImService.addGroupMember(req.getGroupBizType(), req.getBizId(), req.getUserList());
        } catch (Exception e) {
            log.error("IM 群组新增群成员失败", e);
            throw new ServiceException("IM 群组新增群成员失败" + e);
        }
        return null;
    }

    @Override
    public Integer getType() {
        return BizTypeEnum.ADDMEMBER.getType();
    }
}
