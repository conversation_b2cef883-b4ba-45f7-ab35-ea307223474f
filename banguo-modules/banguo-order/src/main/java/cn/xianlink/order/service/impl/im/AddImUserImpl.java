package cn.xianlink.order.service.impl.im;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import cn.xianlink.basic.api.RemoteRegionWhService;
import cn.xianlink.basic.api.domain.vo.RemoteRegionWhVo;
import cn.xianlink.common.api.enums.system.SysImGroupBizTypeEnum;
import cn.xianlink.common.api.enums.system.SysUserTypeEnum;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.order.domain.ReportLossOrder;
import cn.xianlink.order.domain.im.bo.ImBo;
import cn.xianlink.order.domain.im.vo.ImVo;
import cn.xianlink.order.domain.vo.moreGoods.MoreGoodsDetailVO;
import cn.xianlink.order.domain.vo.stockOut.StockoutDetailVO;
import cn.xianlink.order.enums.BizTypeEnum;
import cn.xianlink.order.mapper.MoreGoodsRecordMapper;
import cn.xianlink.order.mapper.ReportLossOrderMapper;
import cn.xianlink.order.mapper.StockoutRecordMapper;
import cn.xianlink.order.service.im.IImStrategy;
import cn.xianlink.system.api.RemoteImGroupService;
import cn.xianlink.system.api.RemoteSubUserService;
import cn.xianlink.system.api.RemoteTencentImService;
import cn.xianlink.system.api.RemoteUserService;
import cn.xianlink.system.api.domain.bo.RemoteImUserBo;
import cn.xianlink.system.api.domain.bo.RemoteUserBo;
import cn.xianlink.system.api.domain.vo.RemoteImGroupVo;
import cn.xianlink.system.api.model.LoginUser;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

@RequiredArgsConstructor
@Service
@CustomLog
public class AddImUserImpl implements IImStrategy {
    @DubboReference
    private final RemoteTencentImService tencentImService;
    @DubboReference
    private final RemoteUserService userService;
    @DubboReference
    private final RemoteSubUserService remoteSubUserService;
    @DubboReference
    private final RemoteRegionWhService remoteRegionWhService;
    @DubboReference
    private final RemoteImGroupService remoteImGroupService;

    private final StockoutRecordMapper stockoutRecordMapper;
    private final MoreGoodsRecordMapper moreGoodsRecordMapper;
    private final ReportLossOrderMapper lossOrderMapper;

    public ImVo handle(ImBo req) {
        if (ObjectUtil.isEmpty(req.getGroupId())) {
            throw new ServiceException("群id参数不能为空");
        }
        // 城市仓和供应商拉人
        if (SysUserTypeEnum.CITY_USER.getType().equals(req.getUserType()) || SysUserTypeEnum.SUPPLIER_USER.getType().equals(req.getUserType())) {
            processCityOrSupplierUser(req);
        } else if (SysUserTypeEnum.SYS_USER.getType().equals(req.getUserType())) {
            processSysUser(req);
        }
        if (CollectionUtils.isEmpty(req.getUserList())) {
            throw new ServiceException("加入的群成员不能为空");
        }
        try {
            // IM群组新增群成员
            log.keyword("群ID：", req.getGroupId()).info("IM群组新增群成员：{}", JSONUtil.toJsonStr(req.getUserList()));
            tencentImService.addGroupMemberByGroupId(req.getGroupId(), req.getUserList());
        } catch (Exception e) {
            throw new ServiceException(e);
        }
        return null;
    }

    /**
     * 城市仓和供应商拉人验证
     *
     * @param req
     */
    private void processCityOrSupplierUser(ImBo req) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser == null) {
            throw new ServiceException("非法请求");
        }
        RemoteUserBo user = userService.getUserByPhoneNo(req.getPhoneNo(), req.getUserType());
        if (ObjectUtil.isEmpty(user)) {
            throw new ServiceException("不存在该用户");
        }
        List<RemoteUserBo> userList = userService.getUserByOrgAndDept(loginUser.getOrgId(), null);
        if (CollectionUtil.isEmpty(userList)) {
            throw new ServiceException("组织架构下无用户");
        }
        //查询用户是否在群中
        RemoteImGroupVo group = remoteImGroupService.getByGroupId(req.getGroupId());
        if (group == null) {
            throw new ServiceException("群信息不存在");
        }
        List<String> users = JSONUtil.toList(group.getGroupUser(), String.class);
        if (CollectionUtil.isNotEmpty(users) && users.contains(user.getUserCode())) {
            throw new ServiceException("该用户已在群中");
        }
        boolean exists = userList.stream().anyMatch(e -> ObjectUtil.equals(e.getUserCode(), user.getUserCode()));
        if (!exists) {
            throw new ServiceException("用户不存在(组织架构)");
        }
        RemoteImUserBo imUserBo = BeanUtil.copyProperties(user, RemoteImUserBo.class);
        req.setUserList(Collections.singletonList(imUserBo));
    }

    /**
     * 总仓拉人验证
     * @param req
     */
    private void processSysUser(ImBo req) {
        RemoteUserBo user = userService.getUserByPhoneNo(req.getPhoneNo(), req.getUserType());
        if (ObjectUtil.isEmpty(user)) {
            throw new ServiceException("不存在该用户");
        }
        // 查询群信息
        RemoteImGroupVo group = remoteImGroupService.getByGroupId(req.getGroupId());
        if (group == null) {
            throw new ServiceException("群信息不存在");
        }
        Long regionWhId = null;
        if(SysImGroupBizTypeEnum.LOSS_REPORT.getCode().equals(group.getBizType())){//报损单
            ReportLossOrder lossOrder = Optional.ofNullable(lossOrderMapper.selectById(group.getBizCode()))
                    .orElseThrow(() -> new ServiceException("报损单不存在"));
            regionWhId = lossOrder.getRegionWhId();
        }else if(SysImGroupBizTypeEnum.STOCKOUT.getCode().equals(group.getBizType())){//缺货少货
            StockoutDetailVO stockout = Optional.ofNullable(stockoutRecordMapper.selectVoById(group.getBizCode()))
                    .orElseThrow(() -> new ServiceException("缺货少货单不存在"));
            regionWhId = stockout.getRegionWhId();
        }else if(SysImGroupBizTypeEnum.MOREGOODS.getCode().equals(group.getBizType())){//多货
            MoreGoodsDetailVO moreGoods = Optional.ofNullable(moreGoodsRecordMapper.selectVoById(group.getBizCode()))
                    .orElseThrow(() -> new ServiceException("多货单不存在"));
            regionWhId = moreGoods.getRegionWhId();
        }
        if(ObjectUtil.isEmpty(regionWhId)){
            throw new ServiceException("总仓ID不存在");
        }
        RemoteRegionWhVo remoteRegionWhVo = Optional.ofNullable(remoteRegionWhService.queryById(regionWhId))
                .orElseThrow(() -> new ServiceException("总仓不存在"));
        if(ObjectUtil.isEmpty(remoteRegionWhVo.getDeptId())){
            throw new ServiceException("总仓部门不存在");
        }
        List<RemoteUserBo> userList = userService.getUserByOrgAndDept(null, Collections.singletonList(remoteRegionWhVo.getDeptId()));
        if (CollectionUtil.isEmpty(userList)) {
            throw new ServiceException("组织架构下无用户");
        }
        boolean exists = userList.stream().anyMatch(e -> ObjectUtil.equals(e.getUserCode(), user.getUserCode()));
        if (!exists) {
            throw new ServiceException("用户不存在(组织架构)");
        }
        //查询用户是否在群中
        List<String> users = JSONUtil.toList(group.getGroupUser(), String.class);
        if (CollectionUtil.isNotEmpty(users) && users.contains(user.getUserCode())) {
            throw new ServiceException("该用户已在群中");
        }
        RemoteImUserBo imUserBo = BeanUtil.copyProperties(user, RemoteImUserBo.class);
        req.setUserList(Collections.singletonList(imUserBo));
    }


    @Override
    public Integer getType() {
        return BizTypeEnum.ADDMEMBER.getType();
    }
}
