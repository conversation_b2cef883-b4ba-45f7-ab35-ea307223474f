package cn.xianlink.order.service;

import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.order.domain.bo.CarListBo;
import cn.xianlink.order.domain.vo.CarListVo;

import java.util.Collection;
import java.util.List;

/**
 * 车型表Service接口
 *
 * <AUTHOR>
 * @date 2025-01-30
 */
public interface ICarListService {

    /**
     * 查询车型表
     *
     * @param id 车型表主键
     * @return 车型表
     */
    CarListVo queryById(Long id);

    /**
     * 查询车型表列表
     *
     * @param bo 车型表
     * @return 车型表集合
     */
    TableDataInfo<CarListVo> queryPageList(CarListBo bo, PageQuery pageQuery);

    /**
     * 查询车型表列表
     *
     * @param bo 车型表
     * @return 车型表集合
     */
    List<CarListVo> queryList(CarListBo bo);

    /**
     * 根据总仓ID查询车型列表
     *
     * @param regionWhId 总仓ID
     * @return 车型列表
     */
    List<CarListVo> queryByRegionWhId(Long regionWhId);

    /**
     * 新增车型表
     *
     * @param bo 车型表
     * @return 结果
     */
    Boolean insertByBo(CarListBo bo);

    /**
     * 修改车型表
     *
     * @param bo 车型表
     * @return 结果
     */
    Boolean updateByBo(CarListBo bo);

    /**
     * 校验并批量删除车型表信息
     *
     * @param ids 需要删除的车型表主键集合
     * @param isValid 是否校验,true-删除前校验,false-不校验
     * @return 结果
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

}
