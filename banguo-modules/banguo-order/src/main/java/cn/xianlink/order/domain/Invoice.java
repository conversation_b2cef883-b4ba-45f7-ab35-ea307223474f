package cn.xianlink.order.domain;

import cn.xianlink.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 发票对象 inv_invoice
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("inv_invoice")
public class Invoice extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 发票ID (主键)
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 发票号 (唯一)
     */
    private String invoiceNumber;

    /**
     * 总仓id
     */
    private Long regionWhId;

    /**
     * 供应商ID[外键]
     */
    private Long supplierId;

    /**
     * 发票抬头ID[外键]
     */
    private Long invoiceTitleId;

    /**
     * 客户Id
     */
    private Long customerId;

    /**
     * 发票金额 (从发票项汇总)
     */
    private BigDecimal invoiceAmount;

    /**
     * 开票日期
     */
    private Date issueDate;

    /**
     * 状态 (applied申请中, uploaded已上传, audited_approved审核通过, audited_rejected审核拒绝)
     */
    private String status;

    /**
     * 场景 (supplier供应商开票, general_warehouse总仓发票, city_warehouse城市仓发票等)
     */
    private String scene;

    /**
     * 发票类型 (normal普票, special专票)
     */
    private String invoiceType;

    /**
     * 发票格式 (electronic电子, paper纸质)
     */
    private String invoiceFormat;

    /**
     * 抬头类型 (company公司, personal个人)
     */
    private String titleType;

    /**
     * 供应商名称快照 (冗余)
     */
    private String supplierNameSnapshot;

    /**
     * 发票抬头快照 (冗余)
     */
    private String invoiceTitleSnapshot;

    /**
     * 税号快照 (冗余)
     */
    private String taxNumberSnapshot;

    /**
     * 地址快照 (冗余)
     */
    private String addressSnapshot;

    /**
     * 银行账号快照 (冗余)
     */
    private String bankAccountSnapshot;

    /**
     * 银行名称快照
     */
    private String bankNameSnapshot;

    /**
     * 电话快照 (冗余)
     */
    private String phoneSnapshot;

    /**
     * 邮箱地址快照 (冗余)
     */
    private String emailAddressSnapshot;

    /**
     * 快递_联系电话
     */
    private String contactPhone;

    /**
     * 快递_所在地区
     */
    private String region;

    /**
     * 快递_详细地址
     */
    private String detailedAddress;

    /**
     * 快递_姓名
     */
    private String recipientName;

    /**
     * 快递_纬度
     */
    private BigDecimal latitude;

    /**
     * 快递_经度
     */
    private BigDecimal longitude;

    /**
     * 快递单号 (纸质发票供应商填写)
     */
    private String expressNumber;

    /**
     * 文件URL列表 (JSON数组，支持多张)
     */
    private String fileUrls;

    /**
     * 申请日期
     */
    private Date applyDate;

    /**
     * 审核日期
     */
    private Date auditDate;

    /**
     * 商户编码
     */
    private String merchantCode;

    /**
     * 商品合计件数
     */
    private Integer totalItems;

    /**
     * 开票订单区间
     */
    private String orderDateRange;

    /**
     * 删除标志（0代表存在 存入这条数据的主键代表删除）
     */
    @TableLogic
    private Long delFlag;

    /**
     * 创建用户代码
     */
    private String createCode;

    /**
     * 创建用户名称
     */
    private String createName;

    /**
     * 修改用户代码
     */
    private String updateCode;

    /**
     * 修改用户名称
     */
    private String updateName;


}
