package cn.xianlink.order.domain.order.vo;

import cn.xianlink.common.dict.annotation.DictConvertFiled;
import cn.xianlink.common.dict.enums.DictConvertTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

@Data
public class QueryItemListVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("订单项id")
    private Long id;

    @ApiModelProperty("订单id")
    private Long orderId;

    @ApiModelProperty("订单code")
    private String orderCode;

    @ApiModelProperty("商品名称")
    private String spuName;

    @ApiModelProperty("平台商品等级，数据字典配置")
    @DictConvertFiled(dictCode = "BasePro_rank", filedName = "spuGradeName", dictRemark = true)
    private String spuGrade;

    @ApiModelProperty("平台商品规格")
    private String spuStandards;

    @ApiModelProperty("产地")
    private String producer;

    @ApiModelProperty("供应商id")
    private Long supplierId;

    @ApiModelProperty("供应商子单位ID")
    private Long supplierDeptId;

    @ApiModelProperty("供应商短码")
    private String supplierSimpleCode;

    @ApiModelProperty("供应商名称")
    private String supplierName;
    private String supplierAlias;

    @ApiModelProperty("子供应商名称")
    private String supplierDeptName;

    @ApiModelProperty("销售批次id")
    private Long supplierSkuId;

    @ApiModelProperty("skuId")
    private Long skuId;

    @ApiModelProperty("关联skuId")
    private Long relationSkuId;

    @ApiModelProperty("销售日")
    private LocalDate saleDate;

    @ApiModelProperty("物流id")
    private Long logisticsId;

    @ApiModelProperty("件数")
    private Integer count;

    @ApiModelProperty("单件毛重")
    private BigDecimal spuGrossWeight;

    @ApiModelProperty("单件净重")
    private BigDecimal spuNetWeight;

    @ApiModelProperty("总毛重")
    private BigDecimal totalGrossWeight;

    @ApiModelProperty("总净重")
    private BigDecimal totalNetWeight;

    @ApiModelProperty("单价")
    private BigDecimal price;

    @ApiModelProperty("差额退后单价")
    private BigDecimal finalPrice;

    @ApiModelProperty("按重量（毛重）算的单价")
    private BigDecimal weightPrice;

    @ApiModelProperty("按重量（毛重）算的单价")
    private BigDecimal grossWeightPrice;

    @ApiModelProperty("按重量（净重）算的单价")
    private BigDecimal netWeightPrice;

    @ApiModelProperty("订单项状态,WAIT:待支付 ALREADY:已支付 FINISH:已完成 CANCEL:已取消 WANT:我想要")
    @DictConvertFiled(dictCode = "orderOrderStatus", filedName = "statusName", type = DictConvertTypeEnum.ENUM)
    private String status;

    @ApiModelProperty("订单取消原因 TIMEOUT-超时未支付 BUYER-买家取消 REPLACE_SUPPLIER-换供应商 OUT-缺货 FEW-少货")
    @DictConvertFiled(dictCode = "orderOrderCancelType", filedName = "cancelTypeName", type = DictConvertTypeEnum.ENUM)
    private String cancelType;

    @ApiModelProperty("工作状态,WAIT_SEND:待送货 SEND_ING:送货中 WAIT_SHARE:待分货 SHARE_ING:分货中 WAIT_EXTRACT:待提货 EXTRACT_FINISH:已提货")
    @DictConvertFiled(dictCode = "orderOrderWorkStatus", filedName = "workStatusName", type = DictConvertTypeEnum.ENUM)
    private String workStatus;

    @ApiModelProperty("显示状态名称")
    private String showStatusName;

    @ApiModelProperty("1无售后 2可售后 3已售后")
    private Integer afterSaleStatus = 1;

    @ApiModelProperty("件数")
    private Integer receiveCount;

    @ApiModelProperty("商品图片")
    private String imgUrl;

    @ApiModelProperty("优惠补贴金额")
    private BigDecimal subsidyFreeAmount;

    @ApiModelProperty("商品金额")
    private BigDecimal productAmount;

    @ApiModelProperty("结算时间")
    private Date settleTime;

    @ApiModelProperty(value = "业务类型，1市采, 10地采, 20基采, 30产地，40配货")
    @DictConvertFiled(dictCode = "orderOrderBusinessType", filedName = "businessTypeName", type = DictConvertTypeEnum.ENUM)
    private Integer businessType;


    @ApiModelProperty("区域编码")
    private String areaCode;

    @ApiModelProperty("品牌")
    private String brand;

    @ApiModelProperty("产地简称")
    private String shortProducer;

    @ApiModelProperty("总仓id")
    private Long regionWhId;

    @ApiModelProperty("城市仓ID")
    private Long cityWhId;
    private String buyerCode;
    private String buyerName;

    @ApiModelProperty("售后规则：0无售后、1正常售后、2库管验货")
    private Integer afterSaleType;

    @ApiModelProperty("寿光蔬菜，0为否，1为是")
    private Integer shouguangVegetables;

    /**
     * 代采服务费(元)
     */
    private BigDecimal platformServiceAmount;

    /**
     * 平台运费金额(元)
     */
    private BigDecimal platformFreightAmount;

    /**
     * 平台运费二级
     */
    private BigDecimal platformFreightAmountLevel2;

    /**
     * 基采运费金额(元)
     */
    private BigDecimal baseFreightAmount;

    /**
     * 地采运费金额(元)
     */
    private BigDecimal regionFreightAmount;

    /**
     * 商品类型，1普通商品，2赠送商品
     */
    private Integer prodType;
    /**
     * 商品优惠金额
     */
    private BigDecimal productFreeAmount;
    /**
     * 优惠后单价
     */
    private BigDecimal priceFree;

    /**
     * 销售类型：1为般果代采，2为商家自营
     */
    private Integer saleType;
}
