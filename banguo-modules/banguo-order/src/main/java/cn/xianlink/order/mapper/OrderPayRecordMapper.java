package cn.xianlink.order.mapper;

import cn.xianlink.order.domain.OrderPayRecord;
import cn.xianlink.order.domain.vo.pay.OrderPayRecordVo;
import cn.xianlink.common.mybatis.core.mapper.BaseMapperPlus;
import org.apache.ibatis.annotations.Param;

/**
 * 订单支付记录Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-29
 */
public interface OrderPayRecordMapper extends BaseMapperPlus<OrderPayRecord, OrderPayRecordVo> {

    /**
     * 删除旧的订单支付记录
     * <AUTHOR> on 2024/5/29:17:23
     * @param orderCode
     * @return int
     */
    int deleteByOrderCode(String orderCode);

    /**
     * 退款
     * <AUTHOR> on 2024/6/25:19:25
     */
    int refund(String orderCode);



    /**
     * 查询未支付订单数量
     * <AUTHOR> on 2024/5/29:18:46
     * @param orderCode
     * @return int
     */
    int selectUnPaidCount(String orderCode);

    /**
     * 查询支付订单数量
     * <AUTHOR> on 2024/5/31:15:22
     * @param orderCode
     * @return int
     */
    int selectPaidCount(String orderCode);


    OrderPayRecord selectUnPayByOrderCode(String orderCode);

    /**
     * 更新支付签名
     * <AUTHOR> on 2024/6/4:18:47
     * @param id
     * @param paySign
     * @return int
     */
    int updatePaySign(@Param("id") Long id, @Param("paySign") String paySign);

    /**
     * 完成支付
     * <AUTHOR> on 2024/6/25:19:32
     * @param orderCode
     * @return int
     */
    int completePay(@Param("orderCode") String orderCode);
}
