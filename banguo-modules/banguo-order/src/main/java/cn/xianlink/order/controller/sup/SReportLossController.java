package cn.xianlink.order.controller.sup;

import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.basic.api.domain.vo.RemoteRcsVo;
import cn.xianlink.common.api.enums.order.ReportLossStatusEnum;
import cn.xianlink.common.api.enums.system.SysUserTypeEnum;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.domain.bo.report.CustomerStatisticsSearchBo;
import cn.xianlink.order.domain.bo.report.ReportLossMiniListSearchBo;
import cn.xianlink.order.domain.bo.report.ReportLossOrderFlowBo;
import cn.xianlink.order.domain.vo.report.ReportLossMiniDetailVo;
import cn.xianlink.order.domain.vo.report.ReportLossMiniListVo;
import cn.xianlink.order.domain.vo.report.ReportNumStatisticsVo;
import cn.xianlink.order.service.ICustomerStatisticsService;
import cn.xianlink.order.service.IReportLossService;
import cn.xianlink.system.api.model.LoginUser;
import io.seata.spring.annotation.GlobalTransactional;
import jakarta.validation.Valid;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;

/**
 * 报损单
 *
 * <AUTHOR> xiaodaibing on 2024-05-30 17:29
 * @folder 供应商端(小程序)/报损单
 */
@CustomLog
@RequiredArgsConstructor
@RestController
@RequestMapping("/order/sup/report")
public class SReportLossController extends BaseController {
    private final IReportLossService reportLossService;

    private final ICustomerStatisticsService customerStatisticsService;

    /**
     * 列表
     *
     * @param bo
     * @return cn.xianlink.common.core.domain.R<TableDataInfo < ReportLossMiniListVo>>
     * <AUTHOR> on 2024/6/11:15:24
     */
    @PostMapping("/list")
    public R<TableDataInfo<ReportLossMiniListVo>> list(@RequestBody ReportLossMiniListSearchBo bo) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        bo.setSupplierId(loginUser.getRelationId());
        if (ObjectUtil.isNotEmpty(loginUser) && ObjectUtil.isEmpty(bo.getSupplierDeptId())) {
            if (SysUserTypeEnum.SUPPLIER_USER.getType().equals(loginUser.getUserType()) && loginUser.getDeptId() != 0L) {
                bo.setSupplierDeptId(loginUser.getDeptId());
                log.keyword("SReportLossController", "list","报损单查询档口信息list")
                        .info("报损单查询档口信息，供应商用户查询，入参：{}，loginUser：{}", bo, loginUser);
            }
        } else {
            if (bo.getSupplierDeptId() == 0L) {
                bo.setSupplierDeptId(null);
            }
            log.keyword("SReportLossController", "list","报损单查询档口信息list")
                    .info("报损单查询档口信息无用户信息，入参：{}", bo);
        }
        return R.ok(reportLossService.miniPage(bo));
    }


    /**
     * 获取详情
     *
     * @param id
     * @return cn.xianlink.common.core.domain.R<cn.xianlink.order.domain.vo.report.ReportLossMiniDetailVo>
     * <AUTHOR> on 2024/6/6:15:22
     */
    @GetMapping("/detail/{id}")
    public R<ReportLossMiniDetailVo> detail(@PathVariable("id") Long id) {
        ReportLossMiniDetailVo vo = reportLossService.detail(id);
        if (!vo.getSupplierId().equals(LoginHelper.getLoginUser().getRelationId())){
            return R.fail("无权限查看");
        }
        return R.ok(vo);
    }

    /**
     * 报损流程处理
     * 供应商审核、申诉、客服介入
     *
     * @param bo
     * @return void
     * <AUTHOR> on 2024/6/6:17:16
     */
    @RepeatSubmit()
    @GlobalTransactional
    @PostMapping("/processing")
    public R<Void> processing(@RequestBody @Valid ReportLossOrderFlowBo bo) {
        if (ReportLossStatusEnum.SUBTRACT_PASS.getCode().equals(bo.getLossStatus())) {
            if (bo.getLossAmount() == null || bo.getLossAmount().compareTo(BigDecimal.ZERO) < 1) {
                return R.fail("减额通过金额必须大于0");
            }
        }

        bo.setReportLossId(reportLossService.getReportLossId(bo.getOrderItemId()));
        reportLossService.processing(bo);
        return toAjax(Boolean.TRUE);
    }

    /**
     * 各状态数量统计
     * 目前只统计待审核、驳回、申诉中
     *
     * @param
     * @return cn.xianlink.common.core.domain.R<java.util.List < cn.xianlink.order.domain.vo.report.ReportNumStatisticsVo>>
     * <AUTHOR> on 2024/6/11:9:47
     */
    @GetMapping("/numStatistics")
    public R<List<ReportNumStatisticsVo>> numStatistics(@RequestParam(value = "supplierDeptId",required = false) Long supplierDeptId) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (ObjectUtil.isNotEmpty(loginUser) && ObjectUtil.isEmpty(supplierDeptId)) {
            if (SysUserTypeEnum.SUPPLIER_USER.getType().equals(loginUser.getUserType()) && loginUser.getDeptId() != 0L) {
                supplierDeptId = loginUser.getDeptId();
                log.keyword("SReportLossController", "numStatistics","报损单查询档口信息numStatistics")
                        .info("报损单查询档口信息，供应商用户查询，loginUser：{}", loginUser);
            }
        } else {
            if (supplierDeptId == 0L) {
                supplierDeptId = null;
            }
            log.keyword("SReportLossController", "numStatistics","报损单查询档口信息numStatistics")
                    .info("报损单查询档口信息, loginUser:{}", loginUser);
        }
        return R.ok(reportLossService.numStatistics(null, LoginHelper.getLoginUser().getRelationId(), null, null, supplierDeptId));
    }



    /**
     * 客户报损统计
     * <AUTHOR> on 2024/7/29:15:28
     * @param searchBo
     * @return cn.xianlink.common.core.domain.R<cn.xianlink.basic.api.domain.vo.RemoteRcsVo>
     */
    @PostMapping("/customerStatistics")
    public R<RemoteRcsVo> customerStatistics(@Valid @RequestBody CustomerStatisticsSearchBo searchBo) {
        if (searchBo.getCycle() > 12) {
            return R.ok(customerStatisticsService.getYearReportLoss(searchBo.getCustomerId(), searchBo.getCycle()));
        } else {
            return R.ok(customerStatisticsService.getMonthReportLoss(searchBo.getCustomerId(), searchBo.getCycle()));
        }
    }

}
