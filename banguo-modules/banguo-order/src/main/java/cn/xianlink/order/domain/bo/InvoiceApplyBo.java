package cn.xianlink.order.domain.bo;

import cn.xianlink.order.domain.Invoice;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 发票-申请开票对象
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@Data
@AutoMapper(target = Invoice.class, reverseConvertGenerate = false)
public class InvoiceApplyBo implements Serializable {
    /**
     * 发票抬头ID[外键]
     */
    @NotNull(message = "发票抬头ID[外键]不能为空")
    private Long invoiceTitleId;

    /**
     * 抬头类型 (company公司, personal个人)
     */
    @NotNull(message = "抬头类型不能为空")
    private String titleType;

    /**
     * 发票类型 (normal普票, special专票)
     */
    @NotNull(message = "发票类型不能为空")
    private String invoiceType;

    /**
     * 客户Id
     */
    private Long customerId;

    /**
     * 开票订单区间开始
     */
    private Date orderTimeStart;

    /**
     * 开票订单区间开始
     */
    private Date orderTimeEnd;

    /**
     * 勾选类型（1-本页全选，2-全部全选）
     */
    private Integer pickType;

    /**
     * 勾选类型为 本页全选 时必传
     */
    @Valid
    List<InvoicePickBo> invoicePickBoList;

    /**
     * 邮箱地址
     */
    @NotNull(message = "邮箱地址不能为空")
    private String emailAddress;

    /**
     * 联系电话
     */
    @NotNull(message = "联系电话不能为空")
    private String contactPhone;

    /**
     * 所在地区
     */
    @NotNull(message = "所在地区不能为空")
    private String region;

    /**
     * 详细地址
     */
    @NotNull(message = "详细地址不能为空")
    private String detailedAddress;

    /**
     * 收件人姓名
     */
    @NotNull(message = "收件人姓名不能为空")
    private String recipientName;

    /**
     * 纬度
     */
    @NotNull(message = "纬度不能为空")
    private BigDecimal latitude;

    /**
     * 经度
     */
    @NotNull(message = "经度不能为空")
    private BigDecimal longitude;
}
