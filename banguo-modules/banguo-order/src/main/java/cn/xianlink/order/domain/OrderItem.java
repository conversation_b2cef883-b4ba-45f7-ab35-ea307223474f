package cn.xianlink.order.domain;

import cn.xianlink.common.api.enums.product.CategoryAfterTypeEnum;
import cn.xianlink.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * 订单项对象 order_item
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("order_item")
public class OrderItem extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 订单总表id
     */
    private Long orderId;

    /**
     * 订单总表唯一编码
     */
    private String orderCode;

    /**
     * 客户Id
     */
    private Long customerId;

    /**
     * 商品分类id
     */
    private Long categoryId;
    private Long categoryIdLevel2;
    private Long categoryIdLevel1;

    /**
     * 商品品种
     */
    private String categoryPathName;

    /**
     * 平台商品ID
     */
    private Long spuId;
    private Long skuId;

    /**
     * 平台商品名称
     */
    private String spuName;

    /**
     * 供应商商品id
     */
    private Long supplierSpuId;

    /**
     * 供应商商品销售批次id
     */
    private Long supplierSkuId;

    /**
     * 总仓id
     */
    private Long regionWhId;

    /**
     * 城市仓id
     */
    private Long cityWhId;

    /**
     * 供应商id
     */
    private Long supplierId;

    private Long supplierDeptId;

    /**
     * 物流ID
     */
    private Long logisticsId;

    /**
     * 二级物流ID
     */
    private Long logisticsIdLevel2;

    /**
     * 提货点ID
     */
    private Long placeId;

    /**
     * 提货单名称
     */
    private String placeName;

    /**
     * 二级提货点ID
     */
    private Long placeIdLevel2;

    /**
     * 二级提货单名称
     */
    private String placeNameLevel2;

    /**
     * 提货点详细地址
     */
    private String address;

    /**
     * 业务类型，1市采，20基采，30产地
     */
    private Integer businessType;

    /**
     * 销售类型：1为般果代采，2为商家自营
     */
    private Integer saleType;

    /**
     * 采购员code
     */
    private String buyerCode;

    /**
     * 采购员name
     */
    private String buyerName;

    /**
     * 商品主图片路径
     */
    private String imgUrl;

    /**
     * 销售日期
     */
    private LocalDate saleDate;

    /**
     * 商品数量
     */
    private Integer count;

    /**
     * 缺货退款数量
     */
    private Integer refundOutCount;

    /**
     * 少货退款数量
     */
    private Integer refundFewCount;

    /**
     * 商品毛重
     */
    private BigDecimal spuGrossWeight;

    /**
     * 商品净重
     */
    private BigDecimal spuNetWeight;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 商品金额
     */
    private BigDecimal productAmount;

    /**
     * 代采服务费(元)
     */
    private BigDecimal platformServiceAmount;

    /**
     * 平台运费金额(元)
     */
    private BigDecimal platformFreightAmount;

    /**
     * 平台运费二级
     */
    private BigDecimal platformFreightAmountLevel2;

    /**
     * 基采运费金额(元)
     */
    private BigDecimal baseFreightAmount;

    /**
     * 地采运费金额(元)
     */
    private BigDecimal regionFreightAmount;

    /**
     * 运费总金额(元)
     */
    private BigDecimal freightTotalAmount;

    /**
     * 非商品金额总金额(元)
     */
    private BigDecimal otherTotalAmount;

    /**
     * 代采服务优惠金额(元)
     */
    private BigDecimal platformServiceFreeAmount;

    /**
     * 平台运费优惠金额(元)
     */
    private BigDecimal platformFreightFreeAmount;

    /**
     * 基采运费优惠金额(元)
     */
    private BigDecimal baseFreightFreeAmount;

    /**
     * 地采运费优惠金额(元)
     */
    private BigDecimal regionFreightFreeAmount;

    /**
     * 运费总优惠金额(元)
     */
    private BigDecimal freightTotalFreeAmount;

    /**
     * 代采币使用数量
     */
    private BigDecimal coinCount;

    private BigDecimal financialServiceAmount;
    /**
     * 代采币抵扣金额
     */
    private BigDecimal coinAmount;

    /**
     * 优惠补贴商品金额
     */
    private BigDecimal subsidyFreeAmount;

    /**
     * 优惠总金额
     */
    private BigDecimal freeTotalAmount;

    /**
     * 订单项总金额（需要支付的金额）
     */
    private BigDecimal totalAmount;

    /**
     * 订单状态,WAIT:待支付 ALREADY:已支付 FINISH:已完成 CANCEL:已取消 WANT:我想要
     */
    private String status;

    /**
     * 支付渠道类型
     */
    private String payChannel;

    /**
     * 付款时间
     */
    private Date payTime;

    /**
     * 订单取消原因 1-超时未支付 2-买家取消 3-换供应商，放数据字典
     */
    private String cancelType;

    /**
     * 取消时间
     */
    private Date cancelTime;

    /**
     * 最终单价
     */
    private BigDecimal finalPrice;

    /**
     * 工作状态,1:待送货 2:送货中 3:待装车 4:待发车 5:已发车.....数据字典
     */
    private String workStatus;

    /**
     * 售后服务，0表示没售后,其他数字代表多少天有售后
     */
    private Integer afterSaleDay;


    /**
     * 售后规则：0无售后、1正常售后、2库管验货
     *
     * @see CategoryAfterTypeEnum
     */
    private Integer afterSaleType;


    /**
     * 售后状态：1可以售后；2正在报损；3结束售后；4报损结束；5不可以售后；
     */
    @Deprecated
    private Integer afterSaleStatus;

    /**
     * 可以结算日期
     */
    private LocalDate canSettleDay;

    /**
     * 结算时间
     */
    private Date settleTime;

    /**
     * 换供应商订单的原始订单项id
     */
    private Long originalOrderItemId;

    /**
     * 换供应商订单的原始供应商id
     */
    private Long originalSupplierId;

    /**
     * 删除标志（0代表存在 存入这条数据的主键代表删除）
     */
    @TableLogic
    private Long delFlag;

    /**
     * 供货总仓
     */
    private Long provideRegionWhId;

    /**
     * 供货物流ID
     */
    private Long provideLogisticsId;

    /**
     * 二级提货点详细地址
     */
    private String addressLevel2;

    /**
     * 提货点层级关系
     */
    private String placePath;

    /**
     * 商品优惠金额
     */
    private BigDecimal productFreeAmount;

    /**
     * 平台运费二级优惠金额
     */
    private BigDecimal platformFreightFreeAmountLevel2;

    /**
     * 商品类型，1普通商品，2赠送商品
     */
    private Integer prodType;

    /**
     * 改价优惠
     */
    private BigDecimal fixPriceFreeAmount;

    /**
     * 分销金额
     */
    private BigDecimal distributionAmount;

    /**
     * 分销佣金对应的税费，供应商承担
     */
    private BigDecimal distributionTaxAmount;

    /**
     * 结算次数，每次结算加1
     */
    private Integer settleCount;

    /**
     * 拍子号
     */
    private Long deliveryNumber;

    /**
     * 总仓活动补贴商品金额
     */
    private BigDecimal regionSubsidyAmount;

    /**
     * 结算件数
     */
    private BigDecimal settleNumber;

    /**
     * 服务费分润规则记录id
     */
    private Long serviceRuleRecordId;

    /**
     * 运费分润规则记录id
     */
    private Long freightRuleRecordId;


    /**
     * 商品补贴金额
     */
    private BigDecimal skuSubsidyAmount;

    /**
     *  活动标签标识 true 为活动标签
     */
    @TableField(exist = false)
    private Integer hasLimitDiscount=0;

    /**
     * 商品实际可参与活动数量
     */
    @TableField(exist = false)
    private Long actDiscountNum;

    /**
     * 限时折扣活动销量目标数量
     */
    @TableField(exist = false)
    private Long targetQty;

    /**
     * 开票情况，1-未开发票，10-已开商品发票
     */
    private Integer invoiceSituation;

    /**
     * 供应商可提供发票类型（1-不提供，2-提供普通发票，3-提供普通发票+专用发票）
     */
    private Integer provideInvoice;

}