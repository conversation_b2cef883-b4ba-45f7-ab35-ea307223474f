package cn.xianlink.order.service;

import cn.xianlink.order.domain.ReceiveGoodsRecordDetail;
import cn.xianlink.order.domain.StockoutSkuRecord;
import cn.xianlink.order.domain.bo.stockOut.BatchCreateStockoutBO;
import cn.xianlink.order.domain.vo.stockOut.StockoutSkuRecordVO;

import java.time.LocalDate;
import java.util.List;

/**
 * 缺货少货商品记录单Service接口
 *
 * <AUTHOR>
 * @date 2024-07-16
 */
public interface IStockoutSkuRecordService {

    /**
     * 根据缺货少货单批量新增缺货少货商品记录单
     */
    void insterBatchByBo(List<BatchCreateStockoutBO> boList);

    StockoutSkuRecord selectById(Long id);

    /**
     * 查询缺货 - 商品计数 - by商品skuId
     * @param saleDate
     * @param regionWhId
     * @param skuSupplierId
     * @return
     */
    List<StockoutSkuRecordVO> queryStockoutSkuCountBySupplierId(LocalDate saleDate, Long regionWhId, Long skuSupplierId, List<Long> logisticsIdList);
    List<StockoutSkuRecordVO> queryStockoutSkuCountBySupplierId(LocalDate saleDateStart, LocalDate saleDateEnd, Long regionWhId, Long skuSupplierId);

    /**
     * 查询缺货 - 商品计数 - by供应商Id
     * @param saleDate
     * @param regionWhId
     * @return
     */
    List<StockoutSkuRecordVO> queryStockoutSkuCountByRegionWhId(LocalDate saleDate, Long regionWhId, Long skuSupplierId, List<Long> buyerSkuIds, List<Long> logisticsIdList);

    /**
     * 查询缺货 - 商品计数 - by物流线id
     * @param saleDate
     * @param regionWhId
     * @return
     */
    List<StockoutSkuRecordVO> queryStockoutSkuCountByLogisticsId(LocalDate saleDate, Long regionWhId, Long skuSupplierId, List<Long> logisticsIdList);
    List<StockoutSkuRecordVO> queryStockoutSkuCountByLogisticsIdDeptId(LocalDate saleDate, Long regionWhId, List<Long> logisticsIdList);


    /**
     * 获取商品售后时间
     * @param sortGoodsIds
     * @return
     */
    List<ReceiveGoodsRecordDetail> getSpuSaleTime(List<Long> sortGoodsIds, List<Long> sortGoodsDetailIds);

    void updateStatus(Long id, int i);
}
