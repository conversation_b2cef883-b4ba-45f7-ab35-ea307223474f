package cn.xianlink.order.mapper;

import cn.xianlink.common.mybatis.core.mapper.BaseMapperPlus;
import cn.xianlink.order.domain.InvoiceItem;
import cn.xianlink.order.domain.bo.InvoiceQueryBo;
import cn.xianlink.order.domain.vo.InvoiceItemVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 发票项Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface InvoiceItemMapper extends BaseMapperPlus<InvoiceItem, InvoiceItemVo> {
    /**
     * 查询发票项
     */
    List<InvoiceItemVo> queryWaitInvoiceItem(@Param("bo") InvoiceQueryBo bo);


}
