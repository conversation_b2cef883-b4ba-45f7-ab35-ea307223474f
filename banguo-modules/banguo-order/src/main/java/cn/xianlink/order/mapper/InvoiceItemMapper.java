package cn.xianlink.order.mapper;

import cn.xianlink.common.mybatis.core.mapper.BaseMapperPlus;
import cn.xianlink.order.domain.InvoiceItem;
import cn.xianlink.order.domain.bo.InvoiceQueryBo;
import cn.xianlink.order.domain.vo.FreeTaxFruitVo;
import cn.xianlink.order.domain.vo.InvoiceItemVo;
import cn.xianlink.order.domain.vo.invoice.InvoiceItemSkuVo;
import cn.xianlink.order.domain.bo.InvoiceItemBo;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 发票项Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface InvoiceItemMapper extends BaseMapperPlus<InvoiceItem, InvoiceItemVo> {
    /**
     * 查询发票项
     */
    List<InvoiceItemVo> queryWaitInvoiceItem(@Param("bo") InvoiceQueryBo bo);

    /**
     * 查询详情的免税水果
     * @param id 发票id
     */
    List<FreeTaxFruitVo> queryFreeTaxFruit(Long id);

    /**
     * 根据发票id查询发票项表，按skuid分组并聚合统计
     *
     * @param page 分页参数
     * @param bo 查询条件
     * @return 按SKU分组的发票项聚合数据
     */
    Page<InvoiceItemSkuVo> queryInvoiceItemListGroupBySku(@Param("page") Page<InvoiceItemSkuVo> page, @Param("bo") InvoiceItemBo bo);

}
