package cn.xianlink.order.domain.entruck.bo;

import cn.xianlink.common.mybatis.core.page.PageQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.List;

/**
 * 总仓装车单业务对象 rw_entruck
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class RwEntruckQueryBo extends PageQuery {
    /**
     * 销售日期
     */
    private LocalDate saleDate;
    /**
     * 总仓id
     */
    private Long regionWhId;


    private Boolean isCityWhQuery = false;
    /**
     * 城市仓id
     */
    private Long cityWhId;
    private String cityWhName;
    /**
     * 预计到仓日期
     */
    private LocalDate arrivalDate;
    /**
     * 城市仓接车日期
     */
    private LocalDate receiveDate;


    /**
     * 分页锚点id（送货单id），默认-1
     */
    private Long pageAnchorId = -1L;
    /**
     * 状态；40待发车，50已发车，60已接车
     */
    private List<Integer> statusList;
    /**
     * 车牌
     */
    private String licencePlate;
    /**
     * 基础物流id
     */
    private Long logisticsId;
    /**
     * 基础物流ids
     * 城市仓用户默认查询自己绑定的物流线，没有绑定就查询所有
     */
    private List<Long> logisticsIds;

    /**
     * 预计到仓日期 - 范围
     */
    private LocalDate arrivalDateStart;
    private LocalDate arrivalDateEnd;
}
