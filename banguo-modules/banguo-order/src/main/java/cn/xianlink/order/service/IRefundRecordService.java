package cn.xianlink.order.service;


import cn.xianlink.common.api.enums.order.AccountTypeStatusEnum;
import cn.xianlink.common.api.enums.order.RefundTypeEnum;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.order.api.bo.RemoteStrockBatchRefundBo;
import cn.xianlink.order.api.bo.RemoteSupAccTransQueryBo;
import cn.xianlink.order.api.vo.RemoteOrderBusiAmtVo;
import cn.xianlink.order.api.vo.RemoteSupBillVo;
import cn.xianlink.order.api.vo.RemoteSupTransRefundDiffVo;
import cn.xianlink.order.api.vo.RemoteSupTransRefundRecordVo;
import cn.xianlink.order.api.vo.RemoteSupTransRefundVo;
import cn.xianlink.order.domain.RefundProductDetail;
import cn.xianlink.order.domain.ReportLossOrder;
import cn.xianlink.order.domain.StockoutRecord;
import cn.xianlink.order.domain.StockoutRecordDetail;
import cn.xianlink.order.domain.bo.pay.SupPaymentSearchBo;
import cn.xianlink.order.domain.bo.refundRecord.CreateRefundRecordBO;
import cn.xianlink.order.domain.bo.refundRecord.DifferenceRefundPageBO;
import cn.xianlink.order.domain.bo.refundRecord.OrderRefundDTO;
import cn.xianlink.order.domain.bo.refundRecord.RefundPageAuditBO;
import cn.xianlink.order.domain.bo.refundRecord.RefundPageBO;
import cn.xianlink.order.domain.order.bo.SupplierSkuSearchBo;
import cn.xianlink.order.domain.order.vo.GetInfoVo;
import cn.xianlink.order.domain.vo.common.CommonStatusCountVO;
import cn.xianlink.order.domain.vo.order.AdminOrderInfoVo;
import cn.xianlink.order.domain.vo.order.SupPaymentOrderMiniListVo;
import cn.xianlink.order.domain.vo.pay.SupPaymentMiniListVo;
import cn.xianlink.order.domain.vo.refundRecord.DifferenceRefundDetailVO;
import cn.xianlink.order.domain.vo.refundRecord.DifferenceRefundPageVO;
import cn.xianlink.order.domain.vo.refundRecord.DifferenceRefundVO;
import cn.xianlink.order.domain.vo.refundRecord.RefundBySourceVO;
import cn.xianlink.order.domain.vo.refundRecord.RefundPageVO;
import cn.xianlink.order.domain.vo.refundRecord.RefundRecordDetailVO;
import cn.xianlink.order.domain.vo.refundRecord.RefundRecordPageVO;
import cn.xianlink.order.domain.vo.refundRecord.RefundRecordVO;
import cn.xianlink.order.domain.vo.refundRecord.SupRefundPageVO;
import cn.xianlink.order.domain.vo.refundRecord.SupRefundVO;
import cn.xianlink.order.enums.OccupyEnum;
import cn.xianlink.trade.api.domain.vo.OrderRefundQueryVo;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 退款记录Service接口
 *
 * <AUTHOR>
 * @date 2024-05-25
 */
public interface IRefundRecordService {

    RefundRecordVO queryById(Long id);
    /**
     * 差额退款分页查询
     */
    TableDataInfo<DifferenceRefundVO> difPage(DifferenceRefundPageBO bo);

    /**
     * 查询差额退款详情
     */
    DifferenceRefundDetailVO getDifById(Long id);

    /**
     * 获取差额退款单各状态数量
     * @param type 类型
     * @see AccountTypeStatusEnum
     */
    CommonStatusCountVO getDifRefundCount(Integer type);

    /**
     * 分页查询退货退款单列表
     */
    TableDataInfo<RefundRecordVO> refundPage(DifferenceRefundPageBO bo);

    /**
     * 获取退货退款单各状态数量
     * @param type 类型
     * @see AccountTypeStatusEnum
     */
    List<CommonStatusCountVO> getRefundCount(Integer type);

    /**
     * 查询退货退款单详情
     */
    RefundRecordDetailVO getRefundById(Long id);

    /**
     * 供应商查询退货退款列表
     */
    TableDataInfo<SupRefundPageVO> supRefundPage(RefundPageBO bo);

    /**
     * 供应商查询退货退款详情
     */
    SupRefundVO getSupRefundById(Long supplierSkuId);

    /**
     * 退货退款单分页查询 管理台
     */
    TableDataInfo<RefundRecordPageVO> adminRefundPage(DifferenceRefundPageBO bo);

    /**
     * 差额退款单分页查询 管理台
     */
    TableDataInfo<DifferenceRefundPageVO> adminDifPage(DifferenceRefundPageBO bo);

    /**
     * 差额退款单分页查询 管理台 差异管理
     */
    TableDataInfo<DifferenceRefundDetailVO> difErrorPage(DifferenceRefundPageBO bo);

    /**
     * 根据缺货记录产生退款记录
     */
    @Deprecated
    void createBlameRecordByStockout(StockoutRecord stockoutRecord, List<StockoutRecordDetail> stockoutRecordDetails);

    /**
     * 占用结算金额
     * <AUTHOR> on 2024/10/22:15:25
     * @param details
     * @return java.lang.Boolean
     */
    Boolean stockoutOccupy(List<StockoutRecordDetail> details);

    /**
     * 缺货少货确认
     * 退款和释放占用金额
     * @param infAuto 是否自动调用接口， 0-否，1-是 ，默认1，指定0就强制需要人工审核不管5000的限额校验， 1-内部还会校验5000限额
     */
    void stockoutConfirm(StockoutRecord stockoutRecord, List<StockoutRecordDetail> details, Integer infAuto);

    /**
     * 缺货少货-作废
     */
    void stockoutInvalid(StockoutRecord stockoutRecord, List<StockoutRecordDetail> details, Map<Long, Integer> newCountMap);


    /**
     * 少货确认退钱
     * <AUTHOR> on 2024/12/2:11:13
     * @param stockoutRecord
     * @param details
     * @return boolean
     */
    void shortageRefund(StockoutRecord stockoutRecord, List<StockoutRecordDetail> details);


    /**
     * 查询订单项退款记录=
     */
    List<RefundProductDetail> getRefundByItemId(List<Long> ids);

    /**
     * 根据订单项id创建差额退款单
     */
    void createDifferenceRecord(OrderRefundDTO orderRefundDTO);

    /**
     * 取消订单生成退款单并执行退款
     * @param orderId
     */
    void cancelCreateRecord(Long orderId);

    /**
     * 支付成功取消订单报错数据修复接口
     * @param orderIdList
     */
    void exceptionRefund(List<Long> orderIdList);

    /**
     * 报损产生退款单并执行退款
     * @param boList
     */
    @Deprecated
    void lossCreateRecord(List<CreateRefundRecordBO> boList);

    /**
     * 报损产生退款单并执行退款
     * 包含占用逻辑
     * <AUTHOR> on 2024/10/19:15:29
     * @param lossRefundBo
     * @param occupyEnum 可为空
     * @return void
     */
    void lossCreateRecord(CreateRefundRecordBO lossRefundBo, OccupyEnum occupyEnum, ReportLossOrder lossOrder);

    /**
     * 取消报损退款
     * 释放预占用的金额
     * <AUTHOR> on 2024/10/21:11:20
     * @param reportLossNo
     * @return void
     */
    void lossCancel(String reportLossNo);

    /**
     * 释放占用的金额
     * 不区分业务场景
     * <AUTHOR> on 2024/12/12:15:27
     * @param refundCode
     * @return void
     */
    void releaseOccupy(String refundCode);

    void closeRefund(String refundCode);


    /**
     * 供应商资金账户退款sku列表
     * <AUTHOR> on 2024/6/29:10:17
     * @param searchBo
     * @return cn.xianlink.common.mybatis.core.page.TableDataInfo<cn.xianlink.order.domain.vo.pay.SupPaymentMiniListVo>
     */
    TableDataInfo<SupPaymentMiniListVo> skuRefundPage(SupPaymentSearchBo searchBo);

    /**
     * 供应商资金账户退款单列表
     * <AUTHOR> on 2024/6/29:10:18
     * @param searchBo
     * @return java.util.List<cn.xianlink.order.domain.vo.order.SupPaymentOrderMiniListVo>
     */
    List<SupPaymentOrderMiniListVo> skuRefundList(SupplierSkuSearchBo searchBo);

    /**
     * 根据订单id获取退款金额详情
     */
    GetInfoVo getRefundAmountById(Long orderId);

    /**
     * 根据来源单号查询退款单信息 目前只用到退款时间
     */
    RefundBySourceVO getRefundBySourceCode(String code);

    /**
     * 超额退款 审核通过
     * @param id 退款单id
     */
    void auditPass(Long id);

    /**
     * 退款审核-分页查询退货退款列表
     */
    TableDataInfo<RefundPageVO> auditPage(RefundPageAuditBO bo);

    /**
     * 退款单进行退款并更新订单状态
     * @return 返回调用退款结果
     */
    void updateRefundStatus(OrderRefundQueryVo vo);

    /**
     * 资金账户退货退款单列表查询
     */
    List<RemoteSupTransRefundVo> queryRefund(RemoteSupAccTransQueryBo bo);

    /**
     * 资金账户差额退款列表查询
     */
    List<RemoteSupTransRefundDiffVo> queryRefundDiff(RemoteSupAccTransQueryBo bo);

    /**
     * 资金账户 根据订单id和类型查询退款金额
     */
    RemoteOrderBusiAmtVo getRefundByIdAndType(Long orderId, Integer code);

    /**
     * 资金账户 退货退款单退款记录列表查询
     */
    List<RemoteSupTransRefundRecordVo> queryProductRefundRecord(RemoteSupAccTransQueryBo bo);

    /**
     * 根据订单项id查询退款中的和退款完成的退款单
     */
    List<RefundProductDetail> getRefundByItemIdList(List<Long> orderItemIdList);
    /**
     * 查询已退款的退款单
     * <AUTHOR> on 2024/11/11:11:36
     * @param orderItemIdList
     * @return java.util.List<cn.xianlink.order.domain.RefundProductDetail>
     */
    List<RefundProductDetail> getRefundedByItemIdList(List<Long> orderItemIdList);

    /**
     * 管理台订单退款
     * @param orderCode
     * @return
     */
    AdminOrderInfoVo adminOrderRefund(String orderCode);

    /**
     * 分摊金融手续费
     * @param totalAmount 除金融手续费实付金额
     * @param serviceAmount 金融手续费
     * @param refundAmount 退款金额
     * @param refundServiceAmount 已经退了金融手续费
     * @return
     */
    BigDecimal getApportionRefundService(BigDecimal totalAmount, BigDecimal serviceAmount , BigDecimal refundAmount, BigDecimal refundServiceAmount);


    /**
     * 查询总仓下今日的指定退款类型的退款成功总金额
     * <AUTHOR> on 2024/12/1:14:52
     * @param type
     * @param regionWhId
     * @return java.math.BigDecimal
     */
    BigDecimal selectRefundTypeToDayAmount(RefundTypeEnum type, Long regionWhId);


    List<RemoteSupBillVo> queryRefundBillList(List<Long> refundId, Long supplierId, Long supplierDeptId, Long supplierSkuId);

    /**
     * 订单明细id查询报损金额
     * @param orderItemIds
     * @return
     */
    List<RemoteStrockBatchRefundBo> getBatchCodeRefund(List<Long> orderItemIds);

    /**
     * 运维用补充退款
     */
    void refundByAdmin(CreateRefundRecordBO bo);

    /**
     * 退款审核不通过
     * @param id
     */
    void auditReturn(Long id);
}
