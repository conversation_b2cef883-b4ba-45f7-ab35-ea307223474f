package cn.xianlink.order.domain.bo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 发票勾选对象
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Data
@NoArgsConstructor
public class InvoicePickBo implements Serializable {

    /**
     * 总仓id
     */
    @NotNull(message = "总仓id不能为空")
    private Long regionWhId;

    /**
     * 供应商ID[外键]
     */
    @NotNull(message = "总仓id不能为空")
    private Long supplierId;

    public InvoicePickBo(Long regionWhId, Long supplierId) {
        this.regionWhId = regionWhId;
        this.supplierId = supplierId;
    }
}
