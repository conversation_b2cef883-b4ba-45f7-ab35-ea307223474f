package cn.xianlink.order.domain.vo.platform;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 客户端发票详情视图对象
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@ExcelIgnoreUnannotated
public class ClientInvoiceDetailVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 发票ID
     */
    private Long id;

    /**
     * 发票号
     */
    private String invoiceNumber;

    /**
     * 供应商信息
     */
    private SupplierInfo supplierInfo;

    /**
     * 发票抬头信息
     */
    private InvoiceTitleInfo invoiceTitleInfo;

    /**
     * 发票基本信息
     */
    private InvoiceBasicInfo invoiceBasicInfo;

    /**
     * 发票文件信息
     */
    private List<InvoiceFileInfo> invoiceFiles;

    /**
     * 物流信息（纸质发票）
     */
    private LogisticsInfo logisticsInfo;

    /**
     * 发票汇总信息
     */
    private InvoiceSummaryInfo summaryInfo;

    /**
     * 商品明细列表
     */
    private List<InvoiceItemDetailVo> itemDetails;

    /**
     * 供应商信息
     */
    @Data
    public static class SupplierInfo {
        private Long supplierId;
        private String supplierName;
        private String supplierCode;
    }

    /**
     * 发票抬头信息
     */
    @Data
    public static class InvoiceTitleInfo {
        private Long invoiceTitleId;
        private String invoiceTitle;
        private String taxNumber;
        private String address;
        private String bankAccount;
        private String phone;
    }

    /**
     * 发票基本信息
     */
    @Data
    public static class InvoiceBasicInfo {
        private BigDecimal invoiceAmount;
        private String invoiceType;
        private String invoiceFormat;
        private String status;
        private String statusDesc;
        private Date applyDate;
        private Date issueDate;
        private Date auditDate;
        private String emailAddress;
        private String mailingAddress;
        private String orderDateRange;
    }

    /**
     * 发票文件信息
     */
    @Data
    public static class InvoiceFileInfo {
        private String fileName;
        private String fileUrl;
        private String fileType;
        private Long fileSize;
        private Date uploadTime;
    }

    /**
     * 物流信息
     */
    @Data
    public static class LogisticsInfo {
        private String expressNumber;
        private String expressCompany;
        private String mailingAddress;
        private Date sendTime;
        private String trackingStatus;
    }

    /**
     * 发票汇总信息
     */
    @Data
    public static class InvoiceSummaryInfo {
        private BigDecimal totalAmount;
        private Integer totalItems;
        private Integer orderCount;
        private Date earliestOrderTime;
        private Date latestOrderTime;
        private BigDecimal actualAmount;
        private BigDecimal reverseAmount;
    }
}
