package cn.xianlink.order.domain.vo.platform;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 发票商品明细视图对象
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@ExcelIgnoreUnannotated
public class InvoiceItemDetailVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 发票项ID
     */
    @ExcelProperty(value = "发票项ID")
    private Long invoiceItemId;

    /**
     * 订单项ID
     */
    @ExcelProperty(value = "订单项ID")
    private Long orderItemId;

    /**
     * 订单ID
     */
    @ExcelProperty(value = "订单ID")
    private Long orderId;

    /**
     * 订单编码
     */
    @ExcelProperty(value = "订单编码")
    private String orderCode;

    /**
     * 商品名称
     */
    @ExcelProperty(value = "商品名称")
    private String spuName;

    /**
     * 商品规格
     */
    @ExcelProperty(value = "商品规格")
    private String skuSpec;

    /**
     * 商品分类
     */
    @ExcelProperty(value = "商品分类")
    private String categoryName;

    /**
     * 数量
     */
    @ExcelProperty(value = "数量")
    private Integer count;

    /**
     * 单价
     */
    @ExcelProperty(value = "单价")
    private BigDecimal price;

    /**
     * 最终价格
     */
    @ExcelProperty(value = "最终价格")
    private BigDecimal finalPrice;

    /**
     * 商品总金额
     */
    @ExcelProperty(value = "商品总金额")
    private BigDecimal productAmount;

    /**
     * 实付金额
     */
    @ExcelProperty(value = "实付金额")
    private BigDecimal actualAmount;

    /**
     * 逆向金额
     */
    @ExcelProperty(value = "逆向金额")
    private BigDecimal reverseAmount;

    /**
     * 开票金额
     */
    @ExcelProperty(value = "开票金额")
    private BigDecimal invoiceAmount;

    /**
     * 订单时间
     */
    @ExcelProperty(value = "订单时间")
    private Date orderTime;

    /**
     * 支付时间
     */
    @ExcelProperty(value = "支付时间")
    private Date payTime;

    /**
     * 商品图片URL
     */
    @ExcelProperty(value = "商品图片URL")
    private String imgUrl;

    /**
     * 供应商商品编码
     */
    @ExcelProperty(value = "供应商商品编码")
    private String supplierSpuCode;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;
}
