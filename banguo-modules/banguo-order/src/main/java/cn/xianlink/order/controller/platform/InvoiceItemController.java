package cn.xianlink.order.controller.platform;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.log.annotation.Log;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.validate.AddGroup;
import cn.xianlink.common.core.validate.EditGroup;
import cn.xianlink.common.log.enums.BusinessType;
import cn.xianlink.common.excel.utils.ExcelUtil;
import cn.xianlink.order.domain.vo.InvoiceItemVo;
import cn.xianlink.order.domain.bo.InvoiceItemBo;
import cn.xianlink.order.service.IInvoiceItemService;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;

/**
 * 发票项
 * 前端访问路由地址为:/order/invoiceItem
 *
 * <AUTHOR>
 * @date 2025-07-29
 * @folder 采集平台(小程序)/发票/发票抬头
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/order/platform/invoiceItem")
public class InvoiceItemController extends BaseController {

    private final IInvoiceItemService invoiceItemService;

    /**
     * 查询发票项列表
     */
    @SaCheckPermission("order:invoiceItem:list")
    @GetMapping("/list")
    public TableDataInfo<InvoiceItemVo> list(InvoiceItemBo bo, PageQuery pageQuery) {
        return invoiceItemService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出发票项列表
     */
    @SaCheckPermission("order:invoiceItem:export")
    @Log(title = "发票项", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(InvoiceItemBo bo, HttpServletResponse response) {
        List<InvoiceItemVo> list = invoiceItemService.queryList(bo);
        ExcelUtil.exportExcel(list, "发票项", InvoiceItemVo.class, response);
    }

    /**
     * 获取发票项详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("order:invoiceItem:query")
    @GetMapping("/{id}")
    public R<InvoiceItemVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(invoiceItemService.queryById(id));
    }

    /**
     * 新增发票项
     */
    @SaCheckPermission("order:invoiceItem:add")
    @Log(title = "发票项", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody InvoiceItemBo bo) {
        return toAjax(invoiceItemService.insertByBo(bo));
    }

    /**
     * 修改发票项
     */
    @SaCheckPermission("order:invoiceItem:edit")
    @Log(title = "发票项", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody InvoiceItemBo bo) {
        return toAjax(invoiceItemService.updateByBo(bo));
    }

    /**
     * 删除发票项
     *
     * @param ids 主键串
     */
    @SaCheckPermission("order:invoiceItem:remove")
    @Log(title = "发票项", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(invoiceItemService.deleteWithValidByIds(List.of(ids), true));
    }
}
