package cn.xianlink.order.domain.vo;

import java.math.BigDecimal;

import cn.xianlink.common.mybatis.core.domain.BaseEntity;
import cn.xianlink.common.tenant.core.TenantEntity;
import cn.xianlink.order.domain.CarModel;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.github.linpeilie.annotations.AutoMapper;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 车型视图对象 car_model
 *
 * <AUTHOR>
 * @date 2024-06-11
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = CarModel.class)
public class CarModelVo extends TenantEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 车型表信息主键id
     */
    @ApiModelProperty(value = "车型表信息主键id")
    private Long id;

    /**
     * 车辆型号
     */
    @ApiModelProperty(value = "车辆型号")
    private String model;

    /**
     * 载重（单位：吨）
     */
    @ApiModelProperty(value = "载重(单位：吨)")
    private BigDecimal carryWeight;

    /**
     * 装车费（单位：元）
     */
    @ApiModelProperty(value = "装车费单位：元")
    private BigDecimal entruckFee;

    /**
     * 停车费（单位：元）
     */
    @ApiModelProperty(value = "停车费(单位：元)")
    private BigDecimal parkFee;

    /**
     * 保险费（单位：元）
     */
    @ApiModelProperty(value = "保险费（单位：元）")
    private BigDecimal insuranceFee;

    /**
     * 运费（单位：分）
     */
    @ApiModelProperty(value = "运费(单位：元)")
    private BigDecimal transportFee;



}
