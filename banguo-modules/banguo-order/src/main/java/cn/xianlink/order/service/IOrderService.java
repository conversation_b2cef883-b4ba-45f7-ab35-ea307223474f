package cn.xianlink.order.service;

import cn.dev33.satoken.exception.NotLoginException;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.order.api.bo.RemoteOrderQueryBo;
import cn.xianlink.order.api.bo.RemotePayUpdateStatusBo;
import cn.xianlink.order.api.bo.RemoteReplaceSupplierBo;
import cn.xianlink.order.api.bo.RemoteSupAccTransQueryBo;
import cn.xianlink.order.api.vo.*;
import cn.xianlink.order.domain.Order;
import cn.xianlink.order.domain.bo.OrderCompensateBo;
import cn.xianlink.order.domain.dto.ConfirmOrderDTO;
import cn.xianlink.order.domain.dto.CustomerOrderDTO;
import cn.xianlink.order.domain.dto.OrderFixPriceDTO;
import cn.xianlink.order.domain.dto.SubmitBo;
import cn.xianlink.order.domain.order.bo.*;
import cn.xianlink.order.domain.order.vo.*;
import cn.xianlink.order.domain.vo.order.AdminOrderInfoVo;
import cn.xianlink.order.domain.vo.order.CityOrderWeightVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 订单总Service接口
 *
 * <AUTHOR>
 * @date 2024-05-25
 */
public interface IOrderService extends IService<Order> {

    /**
     * 代采费收费标准
     * @return
     */
    BigDecimal getPlatformServiceStandard();
    /**
     * 获取优惠补贴金额
     * @param regionWhId
     * @return
     */
    BigDecimal getSubsidyFreeAmount(Long regionWhId);

    /**
     * 获取优惠补贴,配货总仓id
     * @return
     */
    Long getSubsidyRegionWhId();

    /**
     * 检查是否可以加购物车，是否可以下单
     * @return
     */
    void checkIfSale(String saleTimeStart, String saleTimeEnd);

    /**
     * 查询订单详情
     */
    GetInfoVo queryById(Long id);

    /**
     * 查询订单详情
     */
    GetInfoVo queryByCode(String code);
    /**
     * 查询订单列表
     */
    TableDataInfo<QueryPageVo> queryPage(QueryOrderPageBo bo);
    TableDataInfo<QueryDeliveryVo> queryPageDelivery(QueryOrderPageDeliveryBo bo);
    QueryDeliveryVo getDeliveryInfoByCode(String code, Boolean isQueryItemList);

    /**
     * 计算金额
     * @param bo
     * @return
     */
    CountAmountVo countAmount(AddOrderBo bo);

    /**
     * 计算订单项平台代采费
     * @param price
     * @return
     */
    BigDecimal getPlatformServiceAmt(BigDecimal price);

    /**
     * 前置检验
     * @param bo
     * @return
     */
    ValidateVo validate(AddOrderBo bo) throws NotLoginException;

    /**
     * 新增订单
     */
    AddOrderVo insertByBo(AddOrderBo bo);

    /**
     * 修改订单
     * @param bo
     */
    Boolean updateOrder(UpdateOrderBo bo);

    /**
     * 订单支付成功回调接口
     * @param bo
     */
    void payUpdateStatus(RemotePayUpdateStatusBo bo);

    /**
     * 支付成功后不管后面订单会不会取消，都要先加入支付信息
     * @param bo
     */
    void updatePayInfo(RemotePayUpdateStatusBo bo);

    /**
     * 取消订单
     */
    Boolean cancel(Long orderId, String cancelType);

    /**
     * 取消订单
     */
    Boolean cancelExecute (Long orderId, String cancelType);

    /**
     * 查询订单项信息（供应商待送货列表，创建送货单）
     * @param bo
     * @return
     */
    List<QueryDeliverVo> queryDeliver(QueryDeliverBo bo);

    /**
     * 查询订单项信息（总仓待送货列表）
     * @param bo
     * @return
     */
    List<QueryRegionDeliverVo> queryRegionDeliver(QueryDeliverBo bo);

    /**
     * 暂不装车列表
     * @param bo
     * @return
     */
    List<QueryDeliverLogisticsVo> queryDeliverLogistics(QueryDeliverBo bo);

    /**
     * 物流线需求分配清单
     * @param bo
     * @return
     */
    List<QueryDeliverLogisticsItemVo> queryDeliverLogisticsItem(QueryDeliverBo bo);

    /**
     * 查询待分货接口（后端调用）
     * @return
     */
    List<QueryDistributionVo> queryDistribution(QueryDistributionBo bo);

    /**
     * 查询订单拍子号
     * 根据销售日 物流线id  配置总仓
     * @return
     */
    List<QueryDeliveryNumberVo> queryOrderDeliveryNumber(QueryDistributionBo bo);

    /**
     * 换供应商，订单项处理，以及生成转单记录
     * @param bo
     * @return
     */
    Boolean replaceSupplier(RemoteReplaceSupplierBo bo);

    /**
     * 报损的订单商品详情
     * @param orderItemId
     * @return
     */
    GetLossInfoVo getLossInfo(Long orderItemId);

    /**
     * 计算最大可报损金额
     * @param orderItemId 订单项id
     * @return
     */
    GetLossInfoVo getCanLossAmount(Long orderItemId);

    /**
     * 检查报损金额是否正确(简单做法，不应该直接调用getLossInfo这个方法，会多两个无用的查询，但是影响不大，先这么做)
     * @param orderItemId 订单项ID
     * @param lossWeight 客户填的报损重量(客户没有填重量，就传BigDecimal.ZERO)
     * @param lossAmount 前端计算的报损金额
     */
    BigDecimal checkLossAmount(Long orderItemId, BigDecimal lossWeight, BigDecimal lossAmount);

    /**
     * 获取订单详细信息(白名单)
     * @param orderId
     * @return
     */
    GetWhiteInfoVo getWhiteInfo(Long orderId);

    /**
     * 未支付订单统计数量
     * @return
     */
    List<GetUnpaidCountVo> getUnpaidCount();

    /**
     * 差额退款定时任务
     */
    void refundOrderDifference(LocalDate saleDate, List<Long> supplierSkuIdList, List<Long> orderItemIds);

    /**
     * 根据销售批次id查询已支付订单货款
     * @param supplierSkuId
     * @return
     */
    BigDecimal queryReplaceSupplierAmount(Long supplierSkuId);

    /**
     * 根据订单编码模糊查询订单号
     * @param orderCode
     * @param supplierId
     * @return
     */
    List<String> listByOrderCode(String orderCode, Long supplierId, Long cityWhId, String saleDate, Long regionWhId);

    /**
     * PC端订单列表查询
     * @param bo
     * @return
     */
    TableDataInfo<QueryAdminPageVo> queryAdminPage(QueryAdminPageBo bo);

    /**
     * 供应商资金账户，查询订货列表
     * @param bo
     * @return
     */
    List<RemoteSupTransProductOrderVo> queryProductOrder(RemoteSupAccTransQueryBo bo);

    /**
     * 供应商资金账户，查询订货明细
     * @param bo
     * @return
     */
    List<RemoteSupTransProductOrderRecordVo> queryProductOrderRecord(RemoteSupAccTransQueryBo bo);

    /**
     * 根据订单id获取订单信息
     * @param orderId
     * @return
     */
    RemoteOrderInfoVo getOrderInfo(Long orderId);

    /**
     * 获取订单查看时间
     * @return
     */
    Date getDelayQueryTime();

    /**
     * 客户订单列表
     * @param bo
     * @return
     */
    TableDataInfo<CustomerOrderVO> customerOrderPage(CustomerOrderDTO bo);

    /**
     * 客户订单详细信息
     * @param orderCode
     * @return
     */
    CustomerOrderVO customerOrderInfo(String orderCode,Long supplierId);

    /**
     * 获取调价订单商品
     * @param orderCode
     * @param orderItemId
     * @return
     */
    List<CustomerOrderItemVO> fixPriceOrderInfo(String orderCode, Long orderItemId);

    /**
     * 计算调价信息
     * @param orderFixPriceDTO
     * @return
     */
    OrderFixPriceVO fixPriceInfo(OrderFixPriceDTO orderFixPriceDTO);

    /**
     * 确认调价
     * @param orderFixPriceDTO
     */
    void affirmFixPrice(OrderFixPriceDTO orderFixPriceDTO);

    /**
     * 订单信息
     * @param orderCode
     * @return
     */
    AdminOrderInfoVo adminOrderInfo(String orderCode);

    /**
     * 城市仓吨位查询
     * @param regionWhId
     * @param cityWhId
     * @param saleDate
     * @return
     */
    TableDataInfo<CityOrderWeightVO> cityWeight(Long regionWhId, Long cityWhId, LocalDate saleDate, PageQuery pageQuery);

    /**
     * 城市仓销售数据
     * @param regionWhId
     * @param cityWhId
     * @param saleDate
     * @param skuIds
     * @param pageQuery
     * @return
     */
    TableDataInfo<CityOrderWeightVO> citySaleData(Long regionWhId, Long cityWhId, LocalDate saleDate, List<Long> skuIds, PageQuery pageQuery);

    /**
     * 城市仓销售数据合计
     * @param regionWhId
     * @param cityWhId
     * @param saleDate
     * @param skuIds
     * @return
     */
    CityOrderWeightVO citySaleDataTotal(Long regionWhId, Long cityWhId, LocalDate saleDate, List<Long> skuIds);

    /**
     * list列表
     * @param searchBo
     * @return
     */
    List<OrderItemAggregateVO> listOrderInfo(OrderSearchBo searchBo);

    /**
     * 分页查询订单列表信息
     * @param searchBo
     * @return
     */
    TableDataInfo<OrderItemAggregateVO> getOrderInfoPage(OrderSearchBo searchBo);

    /**
     * 订单列表查询数量
     * @param bo
     * @return
     */
    Long queryPlatformCount(QueryOrderPageBo bo);

    /**
     * 查询销售账单
     * <AUTHOR> on 2025/1/21:16:08
     * @param
     * @return
     */
    List<RemoteSupBillVo> querySaleBillList(List<Long> orderIds, Long supplierId, Long supplierDeptId, Long supplierSkuId);


    /**
     * 确认订单页
     * @param confirmOrderDTO
     * @return
     */
    ConfirmOrderVo confirm(ConfirmOrderDTO confirmOrderDTO);

    AddOrderVo submit(SubmitBo submitBo);

    List<Map<String, BigDecimal>> getPlatformServiceStandardList();

    Boolean couponAutoCreateOrder(ConfirmOrderDTO confirmOrderDTO);

    /**
     *根据用户和总仓获取最近的订单信息
     * @param regionWhId
     * @return
     */
    Order getByRegionWhId(Long regionWhId, Long customerId);
    /**
     * 查询商品最近的一次购买
     *
     * @param customerId
     * @param skuIds
     * @return
     */
    RemoteOrderItemVo findLastBuySku(Long customerId, List<Long> skuIds);

    /**
     * 订单拍子号赋值
     * @param orderId
     */
    void setDeliveryNumber(Long orderId);

    /**
     * 订单异步执行逻辑
     * @param orderCode
     */
    void asyncAccept(Order order);

    /**
     * 订单查询
     * @param bo
     * @return
     */
    TableDataInfo<OpenOrderVo> selectOrderVoPage(RemoteOrderQueryBo bo);
    AddOrderVo submitLock(SubmitBo submitBo);

    /**
     * 下单回滚补偿
     */
    void rollbackCompensate(OrderCompensateBo bo);

    /**
     * 根据订单号查询订单ID
     */
    Long getIdByCode(String code);

    /**
     * 订单列表查询
     * @param bo
     * @return
     */
    TableDataInfo<QueryPageVo> orderList(QueryOrderPageBo bo);
}