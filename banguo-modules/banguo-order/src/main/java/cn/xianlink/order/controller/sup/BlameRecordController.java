package cn.xianlink.order.controller.sup;

import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.common.api.enums.order.AccountTypeStatusEnum;
import cn.xianlink.common.api.enums.system.SysUserTypeEnum;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.domain.bo.blameRecord.AddBlameRecordBO;
import cn.xianlink.order.domain.bo.blameRecord.BlameRecordPageBO;
import cn.xianlink.order.domain.vo.blameRecord.BlameRecordInfoVO;
import cn.xianlink.order.domain.vo.blameRecord.BlameRecordPageVO;
import cn.xianlink.order.domain.vo.common.CommonStatusCountVO;
import cn.xianlink.order.service.IBlameRecordService;
import cn.xianlink.system.api.model.LoginUser;
import io.swagger.annotations.ApiOperation;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 供应商小程序-判责单
 *
 * <AUTHOR>
 * @date 2024-05-25
 */
@Validated
@RequiredArgsConstructor
@RestController("supBlameRecordController")
@RequestMapping("/order/sup/blame")
@CustomLog
public class BlameRecordController extends BaseController {

    private final IBlameRecordService blameRecordService;

    @PostMapping("/blameRecordPage")
    @ApiOperation(value = "判责单列表")
    public R<TableDataInfo<BlameRecordPageVO>> blameRecordPage(@RequestBody BlameRecordPageBO bo){
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (ObjectUtil.isNotEmpty(loginUser) && ObjectUtil.isEmpty(bo.getSupplierDeptId())) {
            if (SysUserTypeEnum.SUPPLIER_USER.getType().equals(loginUser.getUserType()) && loginUser.getDeptId() != 0L) {
                bo.setSupplierDeptId(loginUser.getDeptId());
                log.keyword("BlameRecordServiceImpl", "blameRecordDeatilPage","判责单查询档口信息blameRecordDeatilPage")
                        .info("判责单查询档口信息，供应商用户查询，loginUser：{}, bo:{}", loginUser, bo);
            }
        } else {
            if (bo.getSupplierDeptId() == 0L) {
                bo.setSupplierDeptId(null);
            }
            log.keyword("BlameRecordServiceImpl", "blameRecordDeatilPage","判责单查询档口信息blameRecordDeatilPage")
                    .info("判责单查询档口信息无用户信息");
        }
        if (bo.getSupplierId() == null && loginUser.getRelationId() == null) {
            return R.ok(TableDataInfo.build());
        }else {
            bo.setSupplierId(LoginHelper.getLoginUser().getRelationId());
        }
        bo.setProductResType(1);
        return R.ok(blameRecordService.blameRecordDeatilPage(bo));
    }

    @GetMapping("/getBlameById/{id}")
    @ApiOperation(value = "判责单详情")
    public R<BlameRecordInfoVO> getBlameById(@PathVariable("id") Long id){
        return R.ok(blameRecordService.getBlameById(id, AccountTypeStatusEnum.SUPPLIER.getCode()));
    }

    @GetMapping("/confirm/{id}")
    @RepeatSubmit()
    @ApiOperation(value = "确认判责")
    public R<Void> confirm(@PathVariable("id") Long id){
        blameRecordService.confirm(id, AccountTypeStatusEnum.SUPPLIER.getCode(),true);
        return R.ok();
    }

    @GetMapping("/getBlameCount")
    @ApiOperation(value = "获取判责单各状态数量")
    public R<List<CommonStatusCountVO>> getBlameCount(@RequestParam(value = "supplierDeptId", required = false) Long supplierDeptId){
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (ObjectUtil.isNotEmpty(loginUser) && ObjectUtil.isEmpty(supplierDeptId)) {
            if (SysUserTypeEnum.SUPPLIER_USER.getType().equals(loginUser.getUserType()) && loginUser.getDeptId() != 0L) {
                supplierDeptId = loginUser.getDeptId();
                log.keyword("BlameRecordServiceImpl", "blameRecordDeatilPage","判责单查询档口信息blameRecordDeatilPage")
                        .info("判责单查询档口信息，供应商用户查询，loginUser：{}", loginUser);
            }
        } else {
            if (supplierDeptId == 0L) {
                supplierDeptId = null;
            }
            log.keyword("BlameRecordServiceImpl", "blameRecordDeatilPage","判责单查询档口信息blameRecordDeatilPage")
                    .info("判责单查询档口信息无用户信息");
        }
        return R.ok(blameRecordService.getBlameCount(AccountTypeStatusEnum.SUPPLIER.getCode(), supplierDeptId));
    }

    @PostMapping("/addBlame")
    @RepeatSubmit()
    @ApiOperation(value = "新增判责")
    public R<Void> addBlame(@RequestBody AddBlameRecordBO bo){
        bo.setSupId(LoginHelper.getLoginUser().getRelationId());
        bo.setRegionWhId(null);
        blameRecordService.addBlame(bo);
        return R.ok();
    }

    @GetMapping("/cancel/{id}")
    @RepeatSubmit()
    @ApiOperation(value = "撤回判责")
    public R<Void> cancel(@PathVariable("id") Long id){
        blameRecordService.cancel(id);
        return R.ok();
    }

}
