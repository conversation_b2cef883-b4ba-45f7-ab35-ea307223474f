package cn.xianlink.order.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 待开发票查询VO
 * @date 2025/7/30 14:56
 */
@Data
public class InvoiceQueryVo implements Serializable {

    /**
     * 发票类型:普通发票/专用发票
     */
    private String provideInvoiceName;

    /**
     * 开票合计金额
     */
    private BigDecimal invoiceAmount;

    /**
     * 总仓ID
     */
    private Long regionWhId;

    /**
     * 总仓名称
     */
    private String regionWhName;

    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 商户编码（供应商惯用名或简码）
     */
    private String merchantCode;

    /**
     * 供应商可供发票类型（1-不提供，2-提供普通发票，3-提供普通发票+专用发票）
     */
    private Integer provideInvoice;

    /**
     * 商品件数总和
     */
    private Integer totalItems;

    /**
     * 商品种类数
     */
    private Integer productCategoryCount;

    /**
     * 开票代采服务费(元)
     */
    private BigDecimal platformServiceAmount;

    /**
     * 开票平台运费金额(元)
     */
    private BigDecimal platformFreightAmount;

    /**
     * 开票基采运费金额(元)
     */
    private BigDecimal baseFreightAmount;

    /**
     * 订单开始时间
     */
    private Date orderTimeStart;

    /**
     * 订单结束时间
     */
    private Date orderTimeEnd;

    /**
     * 开票订单区间
     */
    private String orderDateRange;

    /**
     * 发票细项，根据订单号分组
     */
    List<InvoiceGroupOrderVo> invoiceGroupOrderVoList;

}
