package cn.xianlink.order.service.impl.im;
import cn.xianlink.basic.api.RemoteBasCustomerService;
import cn.xianlink.basic.api.RemoteCityWhService;
import cn.xianlink.basic.api.RemoteSupplierService;
import cn.xianlink.basic.api.domain.vo.RemoteCityWhVo;
import cn.xianlink.basic.api.domain.vo.RemoteCustomerVo;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.order.config.TencentImProperties;
import cn.xianlink.order.domain.Order;
import cn.xianlink.order.domain.im.bo.ImBo;
import cn.xianlink.order.domain.im.vo.ImVo;
import cn.xianlink.order.enums.BizTypeEnum;
import cn.xianlink.order.mapper.OrderMapper;
import cn.xianlink.order.service.im.IImStrategy;
import cn.xianlink.product.api.RemoteSkuService;
import cn.xianlink.system.api.RemoteTencentImService;
import cn.xianlink.system.api.RemoteUserService;
import cn.xianlink.system.api.domain.bo.RemoteImUserBo;
import cn.xianlink.system.api.domain.bo.RemoteUserBo;
import cn.xianlink.system.api.model.LoginUser;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static cn.xianlink.system.api.domain.bo.RemoteImUserBo.buildImUser;

/**
 * 订单(单聊)
 */
@RequiredArgsConstructor
@Service
@CustomLog
public class OrderImUserImpl implements IImStrategy {

    private final OrderMapper orderMapper;

    @DubboReference
    private final RemoteSkuService skuService;
    @DubboReference
    private final RemoteSupplierService supplierService;
    @DubboReference
    private final RemoteUserService userService;
    @DubboReference
    private final RemoteTencentImService tencentImService;
    @DubboReference
    private final RemoteCityWhService cityWhService;
    @DubboReference
    private final RemoteBasCustomerService customerService;

    private final TencentImProperties tencentImProperties;
    /**
     * 订单和城市仓聊
     * @param req 业务数据
     * @return
     */
    @Override
    public ImVo handle(ImBo req) {
        LoginUser user = Optional.ofNullable(LoginHelper.getLoginUser())
                .orElseThrow(() -> new ServiceException("用户未登入"));
        // 查询订单项对象
        Order order = Optional.ofNullable(orderMapper.selectById(req.getBizId()))
                .orElseThrow(() -> new ServiceException("订单项不存在"));
        // 查询城市仓表超管代码（用户编码）
        RemoteCityWhVo cityWh = Optional.ofNullable(cityWhService.queryById(order.getCityWhId()))
                .orElseThrow(() -> new ServiceException("城市仓不存在"));
        // 查询用户
        RemoteUserBo cityWhUser = Optional.ofNullable(userService.getUserByUserCode(cityWh.getAdminCode()))
                .orElseThrow(() -> new ServiceException("城市仓用户不存在"));

        // 查询客户表超管代码（用户编码）
        RemoteCustomerVo customer = Optional.ofNullable(customerService.getCustomerByAdminCode(user.getUserCode()))
                .orElseThrow(() -> new ServiceException("商家不存在"));

        List<RemoteImUserBo> userList = new ArrayList<>();
        userList.add(buildImUser(cityWh.getAdminCode(), cityWh.getName()));
        userList.add(buildImUser(user.getUserCode(), customer.getName()));

        try {
            // 创建单聊
            tencentImService.importUser(userList);
            // 发送欢迎语
            if(Objects.equals(tencentImProperties.getCityWelcomeMsg(), 1)){
                tencentImService.sendSingleChatMsg(cityWh.getAdminCode(), user.getUserCode(), String.format("我是%s负责人，电话：%s", cityWh.getName(), cityWhUser.getPhoneNo()));
            }
        } catch (Exception e) {
            // 记录日志并抛出异常
            log.error("IM 用户导入失败", e);
            throw new ServiceException("IM 用户导入失败");
        }
        return new ImVo(null , cityWh.getAdminCode());
    }

    @Override
    public Integer getType() {
        return BizTypeEnum.ORDER.getType();
    }
}