package cn.xianlink.order.constant;

/**
 * 缓存组名称常量
 * <p>
 * key 格式为 cacheNames#ttl#maxIdleTime#maxSize
 * <p>
 * ttl 过期时间 如果设置为0则不过期 默认为0
 * maxIdleTime 最大空闲时间 根据LRU算法清理空闲数据 如果设置为0则不检测 默认为0
 * maxSize 组最大长度 根据LRU算法清理溢出数据 如果设置为0则无限长 默认为0
 * <p>
 * 例子: test#60s、test#0#60s、test#0#1m#1000、test#1h#0#500
 *
 * <AUTHOR> Li
 */
public interface OrderCacheNames {
    //统计-客户-报损率-城市仓
    String STATISTICS_CUSTOMER_REPORT_CITY_KEY = "statistics:customer:reportLoss:city:";


    //统计-客户-报损率-城市仓-上次成功时间
//    String STATISTICS_CUSTOMER_REPORT_CITY_LAST_TIME_KEY = "statistics:customer:reportLoss:city:lastTime:";


    //统计-客户-报损率-今天已统计过的客户
    String STATISTICS_CUSTOMER_REPORT_TODAY_ALREADY_KEY = "statistics:customer:reportLoss:today:";
    //客户统计信息缓存
    String STATISTICS_CUSTOMER_REPORT_CACHE = "statistics:customer:reportLoss:cache#600s";
    //客户统计信息缓存锁
    String STATISTICS_CUSTOMER_LOCK_KEY = "statistics:customer:lock";


    String REGION_PICKING_NO_KEY = "region:picking:no:";
    String SUP_DELIVERY_NO_KEY = "sup:delivery:no:";
    String REGION_ENTRUCK_NO_KEY = "region:entruck:no:";
    String REGION_DEPART_NO_KEY = "region:depart:no:";





    String REPORT_GENERATE_CODE = "report_generate_code:";

    String BLAME_NO_KEY = "blame:no:";
    String STOCKOUT_NO_KEY = "stockout:no:";
    String LESS_GOODS_NO_KEY = "lessGoods:no:";
    String ORDER_NO_KEY = "order:no:";
    String REFUND_NO_KEY = "refund:no:";

    String DEDUCTION_NO_KEY = "deduction:no:";
    String MORE_GOODS_NO_KEY = "moreGoods:no:";

    String AUTO_NO_PURCHASE = "order:auto_no_purchase:";

    //分布式锁
    String AUTO_NO_PURCHASE_LOCK = "order:lock:auto_no_purchase:";

    //修改订单状态信息的方法需要分布式锁
    String UPDATE_ORDER_STATUS = "order:lock:update_order_status";

    //结算占用
    String SETTLE_OCCUPY_STOCKOUT = "settle:occupy:stockout:";

    String SETTLE_OCCUPY_LOSS = "settle:occupy:loss:";

    //不同销售日需结算的供应商id
    String SETTLE_SUPPLIERID_SALEDAY = "settle:supplierId:saleday:";

    //分布式锁
    String OPERATE_PICKING_ENTRUCK_LOCK = "order:lock:operate_picking_entruck:";
    //购物车列表锁
    String CART_LIST_LOCK = "CART_LIST_LOCK:";
    String CREATE_ENTRUCK_NO_LOCK = "order:lock:create_entruck_no:";


    //少货不退钱的少货单号
    String SH_ORDER_NO_REFUND = "stockout:sh:noRefund:";

    /**
     * 金融手续费是否认证
     */
    String FINANCIAL_SERVICE_IS_AUTH = "financialServiceIsAuth:";

    /**
     * 接车城市仓锁
     */
    String ENTRUCK_CITY_LOCK = "entruck_city_lock:";

    String CITY_TO_REGION_DAYS = "city:toRegion:days:#600s";

    /**
     * 提交分货
     */
    String SORT_GOOD_LOCK = "sortGood:lock:";

    /**
     * 一键分货
     */
    String AUTO_SORT_GOOD_LOCK = "autoSortGood:lock:";

    /**
     * 缺货少货确认
     */
    String STOCKOUT_CONFIRM = "stockout:confirm";

    /**
     * 少货单判责
     */
    String ADD_BLAME = "blame:add";

    /**
     * 生成缺货少货单锁
     */
    String STOCKOUT_CREATE_LOCK = "stockout_create_lock:";

}
