package cn.xianlink.order.controller.sup;

import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.validate.AddGroup;
import cn.xianlink.common.core.validate.EditGroup;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.log.annotation.Log;
import cn.xianlink.common.log.enums.BusinessType;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.domain.bo.InvDutyFreeGoodsBo;
import cn.xianlink.order.domain.vo.InvDutyFreeGoodsVo;
import cn.xianlink.order.service.IInvDutyFreeGoodsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 免税商品管理
 * @folder 供应商端(小程序)/免税商品管理
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/order/sup/dutyFreeGoods")
@Tag(name = "免税商品管理", description = "免税商品管理")
public class SupDutyFreeGoodsController extends BaseController {

    private final IInvDutyFreeGoodsService invDutyFreeGoodsService;

    /**
     * 查询免税商品列表
     */
    @Operation(summary = "查询免税商品列表")
    @PostMapping("/list")
    public R<TableDataInfo<InvDutyFreeGoodsVo>> list(@RequestBody InvDutyFreeGoodsBo bo) {
        return R.ok(invDutyFreeGoodsService.queryPageList(bo));
    }

    /**
     * 获取免税商品详细信息
     */
    @Operation(summary = "获取免税商品详细信息")
    @GetMapping("/{id}")
    public R<InvDutyFreeGoodsVo> getInfo(@NotNull(message = "主键不能为空")
                                         @Parameter(description = "主键")
                                         @PathVariable Long id) {
        return R.ok(invDutyFreeGoodsService.queryById(id));
    }

    /**
     * 新增免税商品
     */
    @Operation(summary = "新增免税商品")
    @Log(title = "免税商品", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody InvDutyFreeGoodsBo bo) {
        return toAjax(invDutyFreeGoodsService.insertByBo(bo));
    }

    /**
     * 修改免税商品
     */
    @Operation(summary = "修改免税商品")
    @Log(title = "免税商品", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody InvDutyFreeGoodsBo bo) {
        return toAjax(invDutyFreeGoodsService.updateByBo(bo));
    }

    /**
     * 删除免税商品
     */
    @Operation(summary = "删除免税商品")
    @Log(title = "免税商品", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @Parameter(description = "主键串")
                          @PathVariable Long[] ids) {
        return toAjax(invDutyFreeGoodsService.deleteWithValidByIds(List.of(ids), true));
    }

}
