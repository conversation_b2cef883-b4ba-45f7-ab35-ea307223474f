package cn.xianlink.order.util;

import cn.hutool.core.date.DateUtil;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.redis.utils.RedisUtils;
import cn.xianlink.common.tenant.helper.TenantHelper;
import cn.xianlink.order.constant.OrderCacheNames;
import org.redisson.api.RAtomicLong;

import java.time.Duration;
import java.time.LocalDate;

public class CustomNoUtil {
    /**
     * @param key 缓存key
     * @param prefix 单号前缀
     * @param date  日期
     * @param length 序号长度
     * @return 单号前缀+日期+序号
     */
    private static String getNoByDay(String key, String prefix, LocalDate date, int length) {
        if (length <= 0) {
            throw new ServiceException("");
        }
        String dateStr = DateUtil.format(date.atStartOfDay(), "yyMMdd");
        String redisKey = key + prefix + dateStr;
        RAtomicLong atomicLong = RedisUtils.getClient().getAtomicLong(redisKey);
        long no = atomicLong.incrementAndGet();
        if (no == 1) {
            RedisUtils.getClient().getBucket(redisKey).expire(Duration.ofHours(25));
        }
        return String.format("%s%s%0"+length+"d", prefix, dateStr, no);
    }
    /**
     * 拣货单号
     * @param saleDate
     * @return
     */
    public static String getPickingNo(LocalDate saleDate) {
        return getNoByDay(OrderCacheNames.REGION_PICKING_NO_KEY, "JH", LocalDate.now(), 5);
    }

    /**
     * 送货单号
     * @param saleDate
     * @return
     */
    public static String getDeliveryNo(LocalDate saleDate) {
        return getNoByDay(OrderCacheNames.SUP_DELIVERY_NO_KEY, "PS", LocalDate.now(), 5);
    }
    /**
     * 装车单号
     * @param saleDate
     * @return
     */
    public static String getEntruckNo(LocalDate saleDate) {
        return getNoByDay(OrderCacheNames.REGION_ENTRUCK_NO_KEY, "ZC", LocalDate.now(), 5);
    }
    /**
     * 发货单号
     * @param saleDate
     * @return
     */
    public static String getDepartNo(LocalDate saleDate) {
        return getNoByDay(OrderCacheNames.REGION_DEPART_NO_KEY, "FC", LocalDate.now(), 5);
    }
    /**
     * 判责单号
     * @param saleDate
     * @return
     */
    public static String getBlameNo(LocalDate saleDate) {
        return getNoByDay(OrderCacheNames.BLAME_NO_KEY, "PZ", LocalDate.now(), 5);
    }
    /**
     * 缺货单号
     * @param saleDate
     * @return
     */
    public static String getStockoutNo(LocalDate saleDate) {
        return getNoByDay(OrderCacheNames.STOCKOUT_NO_KEY, "QH", LocalDate.now(), 5);
    }
    /**
     * 少货单号
     * @param saleDate
     * @return
     */
    public static String getLessNo(LocalDate saleDate) {
        return getNoByDay(OrderCacheNames.LESS_GOODS_NO_KEY, "SH", LocalDate.now(), 5);
    }

    /**
     * 扣款单号
     * @param saleDate
     * @return
     */
    public static String getDeductionNo(LocalDate saleDate) {
        return getNoByDay(OrderCacheNames.DEDUCTION_NO_KEY, "KD", LocalDate.now(), 5);
    }
    /**
     * 多货单号
     * @param saleDate
     * @return
     */
    public static String getMoreGoodsNo(LocalDate saleDate) {
        return getNoByDay(OrderCacheNames.MORE_GOODS_NO_KEY, "DH", LocalDate.now(), 5);
    }


    /**
     * 生成订单号需要替换的数字
     */
    public static final int[][] replace = {
            {48, 61, 31, 39, 30, 5, 16, 20, 47, 73, 94, 22, 34, 9, 12, 87, 43, 11, 72, 45, 41, 78, 97, 51, 35, 3, 59, 40, 63, 36, 76, 29, 98, 85, 53, 67, 15, 75, 7, 88, 90, 68, 91, 32, 57, 79, 2, 50, 52, 86, 84, 44, 38, 95, 25, 49, 82, 28, 64, 60, 14, 0, 96, 19, 62, 81, 69, 23, 4, 24, 56, 66, 83, 58, 74, 92, 13, 65, 46, 37, 71, 89, 93, 6, 80, 54, 55, 10, 27, 99, 70, 77, 18, 33, 17, 42, 8, 21, 26, 1},
            {34, 42, 97, 50, 46, 17, 51, 9, 92, 73, 3, 27, 55, 72, 32, 29, 47, 82, 90, 89, 26, 16, 48, 61, 2, 28, 14, 68, 56, 77, 4, 40, 60, 93, 35, 54, 38, 23, 94, 64, 95, 88, 8, 10, 33, 1, 19, 52, 37, 25, 75, 65, 31, 11, 62, 79, 45, 18, 53, 36, 85, 81, 21, 76, 49, 78, 22, 13, 84, 43, 57, 58, 96, 63, 66, 24, 5, 20, 98, 80, 99, 83, 59, 39, 67, 86, 6, 15, 91, 30, 71, 12, 44, 87, 7, 69, 0, 41, 70, 74},
            {38, 62, 6, 7, 44, 36, 25, 27, 48, 24, 53, 70, 11, 76, 95, 39, 18, 80, 61, 51, 96, 85, 59, 19, 3, 99, 81, 10, 74, 26, 77, 57, 49, 20, 68, 78, 60, 56, 4, 12, 63, 54, 71, 87, 55, 15, 13, 28, 52, 0, 8, 30, 66, 2, 35, 67, 47, 32, 64, 22, 58, 75, 17, 5, 37, 46, 92, 83, 86, 73, 23, 91, 1, 16, 69, 93, 65, 41, 98, 79, 43, 14, 94, 88, 89, 50, 34, 90, 21, 42, 72, 40, 97, 84, 33, 9, 45, 82, 29, 31},
            {48, 42, 85, 95, 49, 52, 99, 69, 17, 41, 84, 66, 21, 60, 71, 25, 30, 57, 73, 50, 10, 16, 7, 39, 19, 90, 89, 67, 98, 55, 75, 13, 34, 36, 81, 1, 56, 27, 58, 74, 44, 53, 88, 37, 18, 83, 8, 14, 70, 72, 26, 12, 82, 80, 93, 6, 9, 78, 43, 62, 11, 31, 51, 3, 23, 28, 38, 77, 59, 4, 40, 29, 0, 32, 91, 68, 92, 5, 45, 35, 15, 24, 65, 87, 20, 64, 33, 2, 61, 97, 46, 54, 94, 79, 63, 22, 47, 76, 86, 96}
    };

    /**
     * 订单号
     * @param saleDate
     */
    public static String getOrderNo(LocalDate saleDate) {
        //日期转成字符串
        String dateStr = DateUtil.format(saleDate.atStartOfDay(), "yyMMdd");
        //十进制转成十六进制
        String prefix = Integer.toHexString(Integer.parseInt(TenantHelper.getTenantId())).toUpperCase();
        //组装redis的key
        String redisKey = OrderCacheNames.ORDER_NO_KEY + prefix + dateStr;
        //从redis获取原子自增数
        RAtomicLong atomicLong = RedisUtils.getClient().getAtomicLong(redisKey);
        long no = atomicLong.incrementAndGet();
        //如果是1，表明是今天第一次生成，给这个key设置过期时间
        if (no == 1) {
            RedisUtils.getClient().getBucket(redisKey).expire(Duration.ofHours(48));
        }
        //把数字变成恒8位的字符串
        String noString = String.format("%0"+8+"d", no);
        //把8个字符两两切割，比如00000001变成{00,00,00,01}
        String[] noSplit = noString.split("(?<=\\G..)");
        //replace的第一层下标
        int i = 0;
        StringBuilder result = new StringBuilder();
        for(String str : noSplit) {
            //根据切割的数字和时间获取replace的第二层下标
            int index = (Integer.parseInt(str)+Integer.parseInt(dateStr))%100;
            //组装字符串
            result.append(String.format("%0"+2+"d", replace[i][index]));
            i++;
        }
        //组装订单号
        return prefix + dateStr + result;
    }

    /**
     * 退款单号
     */
    public static String getRefundNo(LocalDate saleDate) {
        return getNoByDay(OrderCacheNames.REFUND_NO_KEY, Integer.toHexString(Integer.parseInt(TenantHelper.getTenantId())).toUpperCase(), saleDate, 7);
    }

    /**
     * 发票单号
     * @return
     */
    public static String getInvoiceNo() {
        return getNoByDay(OrderCacheNames.INVOICE_NO_KEY, "FP", LocalDate.now(), 5);
    }
}
