package cn.xianlink.order.controller.city;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.xianlink.basic.api.RemoteCityWhPlaceService;
import cn.xianlink.basic.api.RemoteRegionLogisticsService;
import cn.xianlink.common.api.enums.order.AccountTypeStatusEnum;
import cn.xianlink.common.api.enums.order.BlameStatusEnum;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.domain.StockoutSkuRecord;
import cn.xianlink.order.domain.bo.stockOut.*;
import cn.xianlink.order.domain.vo.common.CommonStatusCountVO;
import cn.xianlink.order.domain.vo.receiveGoods.ReceiveGoodsDetailVO;
import cn.xianlink.order.domain.vo.stockOut.StockoutDetailVO;
import cn.xianlink.order.domain.vo.stockOut.StockoutPageVO;
import cn.xianlink.order.domain.vo.stockOut.StockoutRecordDetailVO;
import cn.xianlink.order.enums.ResponsibilityTypeEnum;
import cn.xianlink.order.service.IStockoutRecordService;
import cn.xianlink.order.service.IStockoutSkuRecordService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 城市仓小程序-缺货少货
 *
 * <AUTHOR>
 * @date 2024-05-25
 */
@Validated
@RequiredArgsConstructor
@RestController("cityStockoutController")
@RequestMapping("/order/city/stockout")
public class StockoutController extends BaseController {

    @DubboReference
    private transient final RemoteRegionLogisticsService remoteRegionLogisticsService;
    private transient final IStockoutRecordService stockoutRecordService;
    private transient final IStockoutSkuRecordService stockoutSkuRecordService;
    @DubboReference
    private final transient RemoteCityWhPlaceService remoteCityWhPlaceService;

    @PostMapping("/stockoutPage")
    @ApiOperation(value = "缺货少货列表")
    public R<TableDataInfo<StockoutPageVO>> stockoutPage(@RequestBody StockoutPageBO bo){
        if (bo.getCityWhId() == null && LoginHelper.getLoginUser().getRelationId() == null) {
            return R.ok(TableDataInfo.build());
        }else {
            bo.setCityWhId(LoginHelper.getLoginUser().getRelationId());
        }
        if (CollectionUtil.isNotEmpty(bo.getPlaceIdList())) {
            bo.setLogisticsIds(remoteRegionLogisticsService.queryIdByCityUserFiled(LoginHelper.getLoginUser().getUserId(), bo.getPlaceIdList()));
        }else if (CollectionUtil.isEmpty(bo.getPlaceIdList()) && CollectionUtil.isEmpty(bo.getPlaceIdLevel2List())) {
            bo.setLogisticsIds(remoteRegionLogisticsService.queryIdByCityUser(LoginHelper.getLoginUser().getUserId()));
            bo.setPlaceIdLevel2List(remoteCityWhPlaceService.getPlaceIdsByUser(LoginHelper.getLoginUser().getUserId()));
        }
        return R.ok(stockoutRecordService.stockoutPage(bo));
    }

    @GetMapping("/getStockoutById/{id}")
    @ApiOperation(value = "缺货少货详情")
    public R<StockoutDetailVO> getStockoutById(@PathVariable("id") Long id){
        StockoutDetailVO stockoutById = stockoutRecordService.getStockoutById(id);
        if (stockoutById != null && stockoutById.getBlameStatus().equals(BlameStatusEnum.HAS_BLAME.getCode())) {
            if (stockoutById.getBlameRecordDetailVOList().get(0).getResponsibilityType().equals(ResponsibilityTypeEnum.CITY_WH.getCode())) {
                stockoutById.setIsConfirm(1);
            }
        }
        return R.ok(stockoutById);
    }

    @GetMapping("/getStockoutByCode/{code}")
    @ApiOperation(value = "缺货少货详情")
    public R<StockoutDetailVO> getStockoutByCode(@PathVariable("code") String code){
        return R.ok(stockoutRecordService.getStockoutByCode(code));
    }

    @GetMapping("/getStockoutOrder/{id}")
    @ApiOperation(value = "查询关联订单")
    public R<List<StockoutRecordDetailVO>> getStockoutOrder(@PathVariable("id") Long id){
        List<StockoutRecordDetailVO> stockoutOrder = stockoutRecordService.getStockoutOrder(id, AccountTypeStatusEnum.CITY.getCode());
        if (CollectionUtil.isNotEmpty(stockoutOrder)) {
            for (StockoutRecordDetailVO detailVO : stockoutOrder) {
                detailVO.setRefundSubsidyFreeAmount(BigDecimal.ZERO);
            }
        }
        return R.ok(stockoutOrder);
    }

    @GetMapping("/getStockoutCount")
    @ApiOperation(value = "获取缺货少货单各状态数量")
    public R<List<CommonStatusCountVO>> getStockoutCount(){
        return R.ok(stockoutRecordService.getStockoutCount(AccountTypeStatusEnum.CITY.getCode()));
    }

    @GetMapping("/stockoutConfirm/{id}")
    @RepeatSubmit()
    @ApiOperation(value = "缺货少货单确认")
    public R<Void> stockoutConfirm(@PathVariable("id") Long id){
        stockoutRecordService.stockoutConfirm(id);
        return R.ok();
    }

    @GetMapping("/stockoutInvalid/{id}")
    @RepeatSubmit()
    @ApiOperation(value = "缺货少货单作废")
    public R<Void> stockoutInvalid(@PathVariable("id") Long id){
        stockoutRecordService.stockoutInvalid(id);
        return R.ok();
    }

    @PostMapping("/updateStockout")
    @RepeatSubmit()
    @ApiOperation(value = "更新缺货少货单数量")
    public R<Void> updateStockout(@RequestBody StockoutUpdateBO bo){
        stockoutRecordService.updateStockout(bo);
        return R.ok();
    }

    @PostMapping("/getMaxStockCount")
    @ApiOperation(value = "获取新增少货单里面的最大少货件数")
    public R<Integer> getMaxStockCount(@RequestParam("supplierSkuId") Long supplierSkuId,
                                    @RequestParam("customerId") Long customerId){
        return R.ok(stockoutRecordService.getMaxStockCount(supplierSkuId, customerId));
    }

    @PostMapping("/createSingleStockout")
    @RepeatSubmit()
    @ApiOperation(value = "新增少货单")
    public R<Void> createSingleStockout(@RequestBody StockoutCreateBO bo){
        stockoutRecordService.createSingleStockout(bo);
        return R.ok();
    }

    @GetMapping("/getSaleDateList")
    @ApiOperation(value = "根据客户id和自然日获取对应日期数据")
    public R<List<LocalDate>> getSaleDateList(@RequestParam("customerId") Long customerId){
        return R.ok( stockoutRecordService.getSaleDateList(customerId));
    }

    @PostMapping("/listBySaleDateAndCustomerId")
    @ApiOperation(value = "根据销售日期和客户id查询少货商品")
    public R<List<ReceiveGoodsDetailVO>> listBySaleDateAndCustomerId(@RequestBody StockoutSearchBO bo){
        if (CollectionUtil.isNotEmpty(bo.getPlaceIdList())) {
            bo.setLogisticsIds(remoteCityWhPlaceService.getPlaceIdsByUser(LoginHelper.getLoginUser().getUserId()));
        }else if (CollectionUtil.isEmpty(bo.getPlaceIdList()) && CollectionUtil.isEmpty(bo.getPlaceIdLevel2List())){
            bo.setPlaceIdList(remoteRegionLogisticsService.queryAllPlaceIdByCUser(LoginHelper.getLoginUser().getUserId()));
            bo.setPlaceIdLevel2List(remoteCityWhPlaceService.getPlaceIdsByUser(LoginHelper.getLoginUser().getUserId()));
        }
        bo.setCityWhId(LoginHelper.getLoginUser().getRelationId());
        return R.ok( stockoutRecordService.listBySaleDateAndCustomerId(bo));
    }

    @PostMapping("/getCustomerList")
    @ApiOperation(value = "根据销售日期查下客户id列表")
    public R<List<Long>> getCustomerList(@RequestBody StockoutSearchBO bo){
        return R.ok( stockoutRecordService.getCustomerList(bo));
    }

    @GetMapping("/manualCreateStockout")
    @ApiOperation(value = "手动创建单缺货单 - 兜底")
    public R<Void> manualCreateStockout(@RequestParam("stockoutSkuRecordId") Long stockoutSkuRecordId) {
        StockoutSkuRecord skuRecord = stockoutSkuRecordService.selectById(stockoutSkuRecordId);
        if (skuRecord == null) {
            return R.warn("id不存在");
        }
//        if (skuRecord.getType() != 1) {
//            return R.warn("接口只能处理缺货");
//        }
        BatchCreateStockoutBO stockoutBo = new BatchCreateStockoutBO();
        stockoutBo.setSaleDate(skuRecord.getSaleDate());
        stockoutBo.setRegionWhId(skuRecord.getRegionWhId());
        stockoutBo.setSkuSupplierId(skuRecord.getSupplierId());
        stockoutBo.setSkuSupplierDeptId(skuRecord.getSkuSupplierDeptId());
        stockoutBo.setLogisticsId(skuRecord.getLogisticsId());
        stockoutBo.setType(skuRecord.getType());
        stockoutBo.setSupplierSkuId(skuRecord.getSupplierSkuId());
        stockoutBo.setSpuName(skuRecord.getSpuName());
        stockoutBo.setStockoutCount(skuRecord.getStockoutCount());
        stockoutBo.setSystemAuto(skuRecord.getSystemAuto() == 1);
        stockoutBo.setCreateScene(skuRecord.getCreateSceneCode());
        stockoutBo.setBuyerId(skuRecord.getBuyerId());
        stockoutBo.setSupplierId(skuRecord.getSupplierId());
        stockoutRecordService.createStockoutSingle(stockoutBo);
        return R.ok();
    }
}
