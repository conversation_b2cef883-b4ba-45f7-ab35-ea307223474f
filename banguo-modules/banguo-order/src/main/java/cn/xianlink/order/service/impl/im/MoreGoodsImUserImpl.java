package cn.xianlink.order.service.impl.im;


import cn.hutool.core.bean.BeanUtil;
import cn.xianlink.basic.api.*;
import cn.xianlink.basic.api.domain.vo.RemoteBasImUserVo;
import cn.xianlink.basic.api.enums.ImAfterSalesBizTypeEnum;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.order.domain.im.bo.ImBo;
import cn.xianlink.order.domain.im.vo.ImVo;
import cn.xianlink.order.domain.vo.moreGoods.MoreGoodsDetailVO;
import cn.xianlink.order.enums.BizTypeEnum;
import cn.xianlink.order.mapper.MoreGoodsRecordMapper;
import cn.xianlink.order.service.im.IImStrategy;
import cn.xianlink.system.api.RemoteDeptService;
import cn.xianlink.system.api.RemoteTencentImService;
import cn.xianlink.system.api.RemoteUserService;
import cn.xianlink.system.api.domain.bo.RemoteImGroupAddBo;
import cn.xianlink.system.api.domain.bo.RemoteImUserBo;
import cn.xianlink.system.api.domain.vo.RemoteImGroupVo;
import cn.xianlink.system.api.model.LoginUser;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

import static cn.xianlink.system.api.domain.bo.RemoteImGroupAddBo.buildImGroup;

/**
 * 多货
 */
@RequiredArgsConstructor
@Service
@CustomLog
public class MoreGoodsImUserImpl implements IImStrategy {

    private final MoreGoodsRecordMapper moreGoodsRecordMapper;
    @DubboReference
    private final RemoteBasImAfterSalesService afterSalesService;
    @DubboReference
    private final RemoteUserService userService;
    @DubboReference
    private final RemoteCityWhService cityWhService;
    @DubboReference
    private final RemoteSupplierService supplierService;
    @DubboReference
    private final RemoteBasCustomerService customerService;
    @DubboReference
    private final RemoteCityWhPlaceService cityWhPlaceService;
    @DubboReference
    private final RemoteRegionLogisticsService regionLogisticsService;
    @DubboReference
    private final RemoteDeptService remoteDeptService;
    @DubboReference
    private final RemoteTencentImService tencentImService;

    @Override
    public ImVo handle(ImBo req) {
        LoginUser user = Optional.ofNullable(LoginHelper.getLoginUser())
                .orElseThrow(() -> new ServiceException("用户未登入"));

        MoreGoodsDetailVO moreGoods = Optional.ofNullable(moreGoodsRecordMapper.selectVoById(req.getBizId()))
                .orElseThrow(() -> new ServiceException("多货单不存在"));

        LocalDateTime localDate = LocalDateTime.now().minusDays(7);
        LocalDateTime createTime = Optional.ofNullable(moreGoods.getCreateTime())
                .map(date -> date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime())
                .orElseThrow(() -> new ServiceException("创建时间为空，无法判断时效"));

        if (localDate.isAfter(createTime)) {
            throw new ServiceException("非常抱歉，由于您的多货单已超过7天，暂时无法为您开启专属群");
        }

        Map<String, Long> bizMap = new HashMap<>();
        bizMap.put(ImAfterSalesBizTypeEnum.PLACE2.getType(), moreGoods.getPlaceIdLevel2());
        bizMap.put(ImAfterSalesBizTypeEnum.PLACE1.getType(), regionLogisticsService.getPlaceId(moreGoods.getLogisticsId()));
        bizMap.put(ImAfterSalesBizTypeEnum.CITY.getType(), moreGoods.getCityWhId());
        bizMap.put(ImAfterSalesBizTypeEnum.REGION.getType(), moreGoods.getRegionWhId());
        bizMap.put(ImAfterSalesBizTypeEnum.DEPT.getType(), moreGoods.getSupplierDeptId());
        bizMap.put(ImAfterSalesBizTypeEnum.SUPPLIER.getType(), moreGoods.getSupplierId());
        List<RemoteBasImUserVo> userList = afterSalesService.queryImUserList(bizMap);
        if(CollectionUtils.isEmpty(userList)){
            throw new ServiceException("无售后人员");
        }
        // 客户
        userList.add(new RemoteBasImUserVo(user.getUserCode(), user.getRealName()));
        // 创建群聊
        String groupName = String.format("%s%s专属群", moreGoods.getCode(), moreGoods.getSpuName());
        String groupID;
        try {
            //判断是否存在群聊-存在直接加入否则创建群聊
            RemoteImGroupVo group = remoteImGroupService.getByBiz(req.getBizType(), req.getBizId());
            if (group != null) {
                // IM群组新增群成员
                RemoteImUserBo remoteImUserBo = RemoteImUserBo.buildImUser(user.getUserCode(), user.getRealName());
                log.keyword("多货单编码：", moreGoods.getCode()).info("BizType={},BizId={},groupName={},groupID={},remoteImUserBo={}",
                        req.getBizType(), req.getBizId(), groupName, group.getGroupId(), JSON.toJSONString(remoteImUserBo));
                tencentImService.addGroupMemberByGroupId(req.getGroupId(), Collections.singletonList(remoteImUserBo));
                groupID = group.getGroupId();
            } else {
                List<RemoteImUserBo> remoteImUserBos = BeanUtil.copyToList(userList, RemoteImUserBo.class);
                log.keyword("多货单编码：", moreGoods.getCode()).info("BizType={},BizId={},groupName={},remoteImUserBos={}", req.getBizType(), req.getBizId(), groupName, JSON.toJSONString(remoteImUserBos));
                RemoteImGroupAddBo bo = buildImGroup(req.getBizType(), req.getBizId(), groupName, remoteImUserBos);
                groupID = tencentImService.createGroup(bo);
            }
        } catch (Exception e) {
            log.error("创建IM群组失败", e);
            throw new ServiceException("创建IM群组失败:" + e);
        }
        return new ImVo(groupID, null);
    }

    @Override
    public Integer getType() {
        return BizTypeEnum.MOREGOODS.getType();
    }
}
