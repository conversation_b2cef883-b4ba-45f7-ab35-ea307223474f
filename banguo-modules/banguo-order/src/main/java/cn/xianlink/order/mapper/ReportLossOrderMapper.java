package cn.xianlink.order.mapper;

import cn.xianlink.common.mybatis.core.mapper.BaseMapperPlus;
import cn.xianlink.order.domain.ReportLossOrder;
import cn.xianlink.order.domain.bo.report.ReportLossMiniListSearchBo;
import cn.xianlink.order.domain.bo.report.ReportLossPcListSearchBo;
import cn.xianlink.order.domain.vo.order.AdminOrderInfoVo;
import cn.xianlink.order.domain.vo.report.ReportLossItemInfoVo;
import cn.xianlink.order.domain.vo.report.ReportLossOrderVo;
import cn.xianlink.order.domain.vo.report.ReportLossPcListVo;
import cn.xianlink.order.domain.vo.report.ReportNumStatisticsVo;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * 报损单Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-13
 */
public interface ReportLossOrderMapper extends BaseMapperPlus<ReportLossOrder, ReportLossOrderVo> {

    int deleteById(@Param("id") Long id);


    ReportLossOrder selectByItemId(@Param("itemId") Long itemId);


    /**
     * 小程序报损单分页查询
     *
     * @param reportLossOrder
     * @param page
     * @return java.util.List<cn.xianlink.order.domain.vo.report.ReportLossMiniListVo>
     * <AUTHOR> on 2024/6/15:11:47
     */
    Page<ReportLossOrder> miniPage(@Param("bo") ReportLossMiniListSearchBo reportLossOrder, @Param("page") Page page);

    /**
     * 资金账户场景下的查询功能
     * <AUTHOR> on 2024/7/25:11:05
     * @param nos
     * @param supplierId
     * @param supplierDeptId
     * @return java.util.List<cn.xianlink.order.domain.ReportLossOrder>
     */
    List<ReportLossOrder> selectByAccTrans(@Param("nos") Set<String> nos, @Param("supplierId") Long supplierId, @Param("supplierDeptId") Long supplierDeptId);

    /**
     * 查询退款列表
     * <AUTHOR> on 2024/6/17:14:16
     * @param searchBo
     * @param page
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<ReportLossPcListVo>
     */
    Page<ReportLossPcListVo> refundPage(@Param("bo") ReportLossPcListSearchBo searchBo, @Param("page") Page page);


    /**
     * 统计各状态数量
     *
     * @param customerId
     * @param supllierid
     * @param cityWhId
     * @param regionWhId
     * @param supplierDeptId
     * @return java.util.List<cn.xianlink.order.domain.vo.report.ReportNumStatisticsVo>
     * <AUTHOR> on 2024/6/15:15:37
     */
    List<ReportNumStatisticsVo> numStatistics(@Param("customerId") Long customerId, @Param("supplierId") Long supplierId
            , @Param("cityWhId") Long cityWhId, @Param("regionWhId") Long regionWhId, @Param("supplierDeptId") Long supplierDeptId);

    List<ReportLossOrder> queryLossAmount(@Param("customerId") Long customerId, @Param("orderIds") Set<Long> orderIds);

    /**
     * 刷新结算单号
     * <AUTHOR> on 2024/6/28:15:25
     * @param orderId
     * @param supplierId
     * @param splitBillNo
     * @param statementNo
     * @return int
     */
    int updateStatement(@Param("itemId") Long itemId, @Param("splitBillNo") String splitBillNo, @Param("statementNo") String statementNo);

    /**
     * 根据报损单号查询报损单
     * <AUTHOR> on 2024/8/3:20:36
     * @param reportLossNo
     * @return cn.xianlink.order.domain.ReportLossOrder
     */
    ReportLossOrder selectByNo(@Param("reportLossNo") String reportLossNo);

    AdminOrderInfoVo getLossInfoByOrderCode(@Param("orderCode")String orderCode);

    BigDecimal applyLossAmount(@Param("orderCode")String orderCode);

    /**
     * 根据订单项查询明细
     * @param orderItemId
     * @return
     */
    List<ReportLossItemInfoVo> getByOrderItemId(@Param("orderItemId") Long orderItemId);
}
