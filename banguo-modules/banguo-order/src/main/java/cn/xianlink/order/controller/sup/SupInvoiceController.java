package cn.xianlink.order.controller.sup;


import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.domain.bo.editInvoiceBo;
import cn.xianlink.order.service.IInvoiceService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 发票
 * 前端访问路由地址为:/order/sup/invoice
 *
 * <AUTHOR>
 * @date 2025-07-29
 * @folder 供应商端(小程序)/发票
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/order/sup/invoice")
public class SupInvoiceController extends BaseController {

    private final IInvoiceService invoiceService;

    /**
     * 供应商编辑发票
     */
    @PostMapping("/editInvoice")
    public R<Void> editInvoice(@Validated @RequestBody editInvoiceBo bo) {
        invoiceService.editInvoice(bo);
        return R.ok();
    }

    /**
     * 供应商上传文件
     */
    @PostMapping("/uploadInvoice")
    public R<Void> uploadInvoice(@RequestBody editInvoiceBo bo) {
        invoiceService.uploadInvoice(bo);
        return R.ok();
    }

}
