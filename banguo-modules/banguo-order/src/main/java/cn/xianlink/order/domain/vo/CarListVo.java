package cn.xianlink.order.domain.vo;

import cn.xianlink.order.domain.CarList;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 车型表视图对象 car_list
 *
 * <AUTHOR>
 * @date 2025-01-30
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = CarList.class)
public class CarListVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 车型表信息主键id
     */
    @ExcelProperty(value = "车型表信息主键id")
    private Long id;

    /**
     * 车辆型号
     */
    @ExcelProperty(value = "车辆型号")
    private String model;

    /**
     * 总仓id
     */
    @ExcelProperty(value = "总仓id")
    private Long regionWhId;

    /**
     * 总仓名称
     */
    @ExcelProperty(value = "总仓名称")
    private String regionWhName;

    /**
     * 载重（单位：吨）
     */
    @ExcelProperty(value = "载重（吨）")
    private BigDecimal carryWeight;

    /**
     * 装车费（单位：元）
     */
    @ExcelProperty(value = "装车费（元）")
    private BigDecimal entruckFee;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private Date updateTime;

}
