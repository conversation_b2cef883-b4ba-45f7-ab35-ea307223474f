package cn.xianlink.order.domain.bo;

import cn.xianlink.common.mybatis.core.page.PageQuery;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 待开发票查询入参
 * @date 2025/7/30 14:56
 */
@Data
public class InvoiceQueryBo extends PageQuery {
    /**
     * 订单创建日期开始
     */
    @NotNull(message = "订单创建日期开始不能为空")
    private Date orderTimeStart;
    /**
     * 订单创建日期结束
     */
    @NotNull(message = "订单创建日期结束不能为空")
    private Date orderTimeEnd;
    /**
     * 供应商可开发票类型（1-不提供，2-提供普通发票，3-提供普通发票+专用发票）
     */
    private Integer provideInvoice;
    /**
     * 客户ID
     */
    private Long customerId;
    /**
     * 勾选发票列表
     */
    @Valid
    List<InvoicePickBo> invoicePickBoList;
}
