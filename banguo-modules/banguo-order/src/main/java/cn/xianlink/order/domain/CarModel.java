package cn.xianlink.order.domain;

import cn.xianlink.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;

import java.io.Serial;

/**
 * 车型对象 car_model
 *
 * <AUTHOR>
 * @date 2024-06-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("car_model")
public class CarModel extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 车型表信息主键id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 车辆型号
     */
    private String model;

    /**
     * 总仓id
     */
    private Long regionWhId;

    /**
     * 载重（单位：吨）
     */
    private BigDecimal carryWeight;

    /**
     * 装车费（单位：元）
     */
    private BigDecimal entruckFee;

    /**
     * 停车费（单位：元）
     */
    private BigDecimal parkFee;

    /**
     * 保险费（单位：元）
     */
    private BigDecimal insuranceFee;

    /**
     * 运费（单位：元）
     */
    private BigDecimal transportFee;

    /**
     * 删除标志（0代表存在 存入这条数据的主键代表删除）
     */
    @TableLogic
    private Long delFlag;

    /**
     * 创建用户代码
     */
    private String createCode;

    /**
     * 创建用户名称
     */
    private String createName;

    /**
     * 修改用户代码
     */
    private String updateCode;

    /**
     * 修改用户名称
     */
    private String updateName;


}
