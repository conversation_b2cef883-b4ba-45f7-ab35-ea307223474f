package cn.xianlink.order.domain.vo.moreGoods;

import cn.xianlink.common.dict.annotation.DictConvertFiled;
import cn.xianlink.common.dict.enums.DictConvertTypeEnum;
import cn.xianlink.order.domain.MoreGoodsRecord;
import cn.xianlink.order.domain.vo.common.OrderFileVO;
import cn.xianlink.order.domain.vo.common.OrderLogVO;
import cn.xianlink.order.domain.vo.stockOut.StockoutDetailVO;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.github.linpeilie.annotations.AutoMapper;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * 多货单详情出参对象
 * <AUTHOR>
 * @date 2024-05-25
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MoreGoodsRecord.class)
public class MoreGoodsDetailVO implements Serializable {

    @ApiModelProperty("多货单id")
    private Long id;

    @ApiModelProperty("状态(1待确认 2已确认 3已撤销)")
    private Integer status;

    @ApiModelProperty("状态名称(1待确认 2已确认 3已撤销)")
    private String statusName;

    @ApiModelProperty("更新时间")
    private Date updateTime;

    @ApiModelProperty("多货单号")
    private String code;

    @ApiModelProperty("供应商id")
    private Long supplierId;

    @ApiModelProperty("供应商名称")
    private String supplierName;
    /**
     * 供应商别名
     */
    private String supplierAlias;

    @ApiModelProperty(value = "（供应商部门id）档口id")
    private Long supplierDeptId;

    @ApiModelProperty(value = "档口名称")
    private String supplierDeptName;

    @ApiModelProperty("物流id")
    private Long logisticsId;

    @ApiModelProperty("物流名称")
    private String logisticsName;

    @ApiModelProperty("总仓id")
    private Long regionWhId;

    @ApiModelProperty("发货总仓")
    private String regionWhName;

    @ApiModelProperty("城市仓id")
    private Long cityWhId;

    @ApiModelProperty("城市仓名称")
    private String cityWhName;

    @ApiModelProperty("批次商品id")
    private Long supplierSkuId;

    @ApiModelProperty("平台商品名称")
    private String spuName;

    @ApiModelProperty("平台商品毛重(斤)")
    private BigDecimal spuGrossWeight;

    @ApiModelProperty("平台商品净重(斤)")
    private BigDecimal spuNetWeight;

    @ApiModelProperty("总仓销售批次编码")
    private String supplierSkuCode;

    @ApiModelProperty("批次结算价格")
    private BigDecimal settlePrice;

    @ApiModelProperty("折后价格")
    private BigDecimal discountPrice;

    @ApiModelProperty("商品批次金额")
    private BigDecimal price;

    @ApiModelProperty("代卖销售批次编码")
    private String replaceSupplierSkuCode;

    @ApiModelProperty("代卖销售批次id")
    private Long replaceSupplierSkuId;

    @ApiModelProperty("销售折扣")
    private BigDecimal saleDiscount;

    @ApiModelProperty("多货数量(件)")
    private Integer count;

    @ApiModelProperty("装车时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date entruckTime;

    @ApiModelProperty("接车时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date meetCarTime;

    @ApiModelProperty("车牌号")
    private String licencePlate;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("关联少货单id")
    private String stockoutRecordId;

    @ApiModelProperty("产地")
    private String producer;

    @ApiModelProperty("业务类型，1市采, 10地采, 20基采, 30产地，40配货")
    @DictConvertFiled(dictCode = "orderOrderBusinessType", filedName = "businessTypeName", type = DictConvertTypeEnum.ENUM)
    private Integer businessType;

    @ApiModelProperty("平台商品规格")
    private String spuStandards;

    @ApiModelProperty("区域编码")
    private String areaCode;

    @ApiModelProperty("品牌")
    private String brand;

    @ApiModelProperty("产地简称")
    private String shortProducer;

    @ApiModelProperty("是否总仓创建0否1是")
    private Integer isRegionCreate;

    @ApiModelProperty("供应商承担比例")
    private BigDecimal supplierRatio;

    @ApiModelProperty("供应商加款金额")
    private BigDecimal supplierBlameAmount;

    @ApiModelProperty("城市仓承担比例")
    private BigDecimal cityRatio;

    @ApiModelProperty("城市仓扣款金额")
    private BigDecimal cityBlameAmount;

    @ApiModelProperty("总仓承担比例")
    private BigDecimal regionRatio;

    @ApiModelProperty("总仓扣款金额")
    private BigDecimal regionBlameAmount;

    @ApiModelProperty("用户选择的日期，用来记录是哪一天的多货，暂时没有实际的业务逻辑")
    private LocalDate saleDate;

    @ApiModelProperty("描述信息")
    private String extendInfo;

    @ApiModelProperty("少货单详情信息")
    private List<StockoutDetailVO> stockoutDetailVO;

    @ApiModelProperty("操作日志列表")
    private List<OrderLogVO> operationLogList;

    @ApiModelProperty("文件列表")
    private List<OrderFileVO> fileList;

    @ApiModelProperty("图片列表")
    private List<OrderFileVO> imageList;

    @ApiModelProperty("视频列表")
    private List<OrderFileVO> videoList;

    @ApiModelProperty("来源类型：1：总仓，2：城市仓 3：供应商")
    private Integer sourceType;
}
