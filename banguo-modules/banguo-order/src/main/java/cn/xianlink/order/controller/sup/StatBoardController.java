package cn.xianlink.order.controller.sup;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.xianlink.common.api.enums.system.OrgTypeEnum;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.domain.bo.report.StatSaleSearchBo;
import cn.xianlink.order.domain.bo.report.SupplierRankSearchBo;
import cn.xianlink.order.domain.vo.report.CommodityStatisticsInfoVo;
import cn.xianlink.order.domain.vo.report.StatBoardVo;
import cn.xianlink.order.domain.vo.report.SupplierRankVo;
import cn.xianlink.order.service.IStatBoardService;
import cn.xianlink.system.api.RemoteDeptService;
import cn.xianlink.system.api.domain.vo.RemoteDeptDetailVo;
import cn.xianlink.system.api.model.LoginUser;
import io.netty.handler.logging.LoggingHandler;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 供应商端(小程序)-看板
 *
 * <AUTHOR>
 * @date 2024-10-15
 * @folder 供应商端(小程序)/看板
 */
@Validated
@RequiredArgsConstructor
@RestController("spuStatBoardController")
@RequestMapping("/order/sup/stat_board/")
public class StatBoardController extends BaseController {
    private final IStatBoardService statBoardService;

    @DubboReference
    private final RemoteDeptService remoteDeptService;

    /**
     * 统计商品，金额
     *
     * @param bo
     * @return
     */
    @PostMapping("/sup_sale")
    public R<StatBoardVo> statSupSale(@RequestBody StatSaleSearchBo bo) {
        Boolean aBoolean = disposeParam(bo);
        if (!aBoolean){
            return R.ok(new StatBoardVo());
        }
        return R.ok(statBoardService.statSupSale(bo));
    }


    /**
     *商品统计列表
     * @param bo
     * @return
     */
    @PostMapping("/commodity_statistics_sup")
    public R<TableDataInfo<CommodityStatisticsInfoVo>> commodityStatistics(@RequestBody StatSaleSearchBo bo) {
        Boolean aBoolean = disposeParam(bo);
        if (!aBoolean){
            return R.ok(TableDataInfo.build());
        }
        return R.ok(statBoardService.commodityStatisticsSupSale(bo));
    }

    /**
     *商品统计合计
     * @param bo
     * @return
     */
    @PostMapping("/commodity_statistics_sup_total")
    public R<CommodityStatisticsInfoVo> commodityStatisticsTotal(@RequestBody StatSaleSearchBo bo) {
        Boolean aBoolean = disposeParam(bo);
        if (!aBoolean){
            return R.ok(new CommodityStatisticsInfoVo());
        }
        return R.ok(statBoardService.commodityStatisticsSupTotal(bo));
    }


    /**
     * 获取档口权限（true：按实际参数查询，false：直接返回）
     * @param bo
     * @return
     */
    private Boolean disposeParam(StatSaleSearchBo bo) {
        RemoteDeptDetailVo vo = remoteDeptService.getDetailByRelationId(bo.getSupplierId(), OrgTypeEnum.SUPPLIER);
        if (CollUtil.isEmpty(vo.getChildren())) {
            return true;
        }
        List<RemoteDeptDetailVo> children = vo.getChildren();
        Long deptId = LoginHelper.getLoginUser().getDeptId();
        List<Long> supplierDepts = children.stream().filter(item -> !(deptId > 0 && !item.getDeptId().equals(deptId)))
                .map(RemoteDeptDetailVo::getDeptId).collect(Collectors.toList());
        if (CollUtil.isEmpty(supplierDepts)){
            return false;
        }
        List<Long> supplierDeptList = bo.getSupplierDeptList();
        if (CollUtil.isNotEmpty(supplierDeptList)){
            List<Long> list = supplierDeptList.stream().filter(item -> supplierDepts.contains(item)).collect(Collectors.toList());
            if (CollUtil.isEmpty(list)){
                return false;
            }
            bo.setSupplierDeptList(list);
            return true;
        }
        if (!Objects.equals(deptId,0L)){
            bo.setSupplierDeptList(supplierDepts);
        }
        return true;
    }
}