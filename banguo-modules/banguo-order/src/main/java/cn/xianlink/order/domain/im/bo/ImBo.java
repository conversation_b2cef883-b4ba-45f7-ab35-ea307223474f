package cn.xianlink.order.domain.im.bo;

import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.system.api.domain.bo.RemoteImUserBo;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ImBo implements Serializable {
    /**
     * 业务Id
     */
    @NotBlank(message = "业务id不能为空")
    private String bizId;

    /**
     * 业务类型
     */
    @NotNull(message = "业务类型不能为空")
    private Integer bizType;


    /**
     * 群业务类型
     */
    private Integer groupBizType;

    /**
     * 加入的群成员
     */
    private List<RemoteImUserBo> userList;
}