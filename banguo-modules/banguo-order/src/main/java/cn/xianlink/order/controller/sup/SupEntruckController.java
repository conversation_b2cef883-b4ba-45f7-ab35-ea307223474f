package cn.xianlink.order.controller.sup;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.common.api.enums.order.DeliveryStatusEnum;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.log.annotation.Log;
import cn.xianlink.common.log.enums.BusinessType;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.controller.CustomToolService;
import cn.xianlink.order.controller.region.RegionEntruckAffairService;
import cn.xianlink.order.domain.entruck.bo.RwEntruckGoodsEditBo;
import cn.xianlink.order.domain.entruck.bo.RwEntruckRecordEditBo;
import cn.xianlink.order.domain.entruck.bo.RwEntruckRecordQueryBo;
import cn.xianlink.order.domain.entruck.vo.RwEntruckRecordVo;
import cn.xianlink.order.service.IRwEntruckRecordService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 供应商-装车单
 *
 * <AUTHOR>
 * @date 2024-05-28
 * @folder 供应商端(小程序)/装车单
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/order/sup/entruck")
public class SupEntruckController extends BaseController {

    private final transient IRwEntruckRecordService entruckRecordService;
    private final transient RegionEntruckAffairService regionEntruckAffairService;
    private final transient CustomToolService customToolService;

    /**
     * 查询-装车记录
     */
    @PostMapping("/recordPage")
    public R<TableDataInfo<RwEntruckRecordVo>> recordPage(@Validated @RequestBody RwEntruckRecordQueryBo bo) {
        if (ObjectUtil.isNull(bo.getRegionWhId())) {
            return R.warn("总仓id不能为空");
        }
        bo.setSaleDate(customToolService.getLastSaleDate(bo.getRegionWhId(), bo.getSaleDate()));
        bo.setSupplierId(LoginHelper.getLoginUser().getRelationId());
        // 查全部
        if (CollectionUtil.isEmpty(bo.getStatusList())) {
            bo.setStatusList(CollectionUtil.toList(DeliveryStatusEnum.COMPLETE_INSPECT.getCode(),
                    DeliveryStatusEnum.COMPLETE_ENTRUCK.getCode()));
        }
        return R.ok(entruckRecordService.pageList(bo));
    }

    /**
     * 查询-装车记录详情
     */
    @GetMapping("/recordInfo/{recordId}")
    public R<RwEntruckRecordVo> recordInfo(@NotNull(message = "装车记录id不能为空") @PathVariable Long recordId) {
        return R.ok(entruckRecordService.queryById(recordId));
    }

    /**
     * 装车操作-单条商品-提交
     */
    @Log(title = "装车操作-单条商品-提交", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/operateEntruckGoods")
    public R<Void> operateEntruckGoods(@Validated @RequestBody RwEntruckGoodsEditBo bo) {
        regionEntruckAffairService.operateEntruckGoods(bo);
        return R.ok();
    }

    /**
     * 装车操作-提交
     */
    @Log(title = "装车操作-提交", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/operateEntruck")
    public R<Void> operateEntruck(@Validated @RequestBody RwEntruckRecordEditBo bo) {
        RwEntruckRecordVo vo = entruckRecordService.selectAndCheckNullById(bo.getId());
        if (vo.getStatus().intValue() != DeliveryStatusEnum.COMPLETE_INSPECT.getCode()) {
            throw new ServiceException("非待装车状态，不允许装车操作");
        }
        bo.setDeliveryId(vo.getDeliveryId());
        regionEntruckAffairService.operateEntruck(bo, false);
        return R.ok();
    }
}
