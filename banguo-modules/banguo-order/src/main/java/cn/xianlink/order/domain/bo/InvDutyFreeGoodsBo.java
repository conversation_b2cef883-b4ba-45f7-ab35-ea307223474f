package cn.xianlink.order.domain.bo;

import cn.xianlink.common.core.validate.AddGroup;
import cn.xianlink.common.core.validate.EditGroup;
import cn.xianlink.common.mybatis.core.domain.BaseEntity;
import cn.xianlink.order.domain.InvDutyFreeGoods;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 免税商品业务对象 inv_duty_free_goods
 *
 * <AUTHOR>
 * @date 2025-01-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = InvDutyFreeGoods.class, reverseConvertGenerate = false)
public class InvDutyFreeGoodsBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 免税重量
     */
    @NotNull(message = "免税重量不能为空", groups = { AddGroup.class, EditGroup.class })
    @DecimalMin(value = "0.01", message = "免税重量必须大于0")
    @Digits(integer = 16, fraction = 2, message = "免税重量格式不正确")
    private BigDecimal weight;

    /**
     * 免税时间-开始
     */
    @NotNull(message = "免税开始时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private LocalDate freeDateStart;

    /**
     * 免税时间-结束
     */
    @NotNull(message = "免税结束时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private LocalDate freeDateEnd;

    /**
     * 采购人员代码
     */
    @NotBlank(message = "采购人员代码不能为空", groups = { AddGroup.class, EditGroup.class })
    @Size(max = 64, message = "采购人员代码长度不能超过64个字符")
    private String purchUserCode;

    /**
     * 采购人员名称
     */
    @NotBlank(message = "采购人员名称不能为空", groups = { AddGroup.class, EditGroup.class })
    @Size(max = 64, message = "采购人员名称长度不能超过64个字符")
    private String purchUserName;

    /**
     * 商品分类id
     */
    @NotNull(message = "商品分类id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long categoryId;

    /**
     * 商品分类名称
     */
    @NotBlank(message = "商品分类名称不能为空", groups = { AddGroup.class, EditGroup.class })
    @Size(max = 32, message = "商品分类名称长度不能超过32个字符")
    private String categoryName;

    /**
     * 审核状态，0待审核；1审核通过；2审核不通过
     */
    private Integer status;

    /**
     * 备注
     */
    @Size(max = 255, message = "备注长度不能超过255个字符")
    private String remark;

    /**
     * 自定义验证：免税结束时间必须大于开始时间
     */
    @AssertTrue(message = "免税结束时间必须大于开始时间", groups = { AddGroup.class, EditGroup.class })
    public boolean isValidDateRange() {
        if (freeDateStart == null || freeDateEnd == null) {
            return true; // 让@NotNull注解处理空值验证
        }
        return freeDateEnd.isAfter(freeDateStart) || freeDateEnd.isEqual(freeDateStart);
    }

}
