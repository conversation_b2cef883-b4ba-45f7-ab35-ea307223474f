package cn.xianlink.order.service;

import cn.xianlink.order.domain.delivery.bo.SupDeliveryGoodsAddBo;
import cn.xianlink.order.domain.delivery.bo.SupDeliveryGoodsEditBo;
import cn.xianlink.order.domain.delivery.bo.SupDeliveryQueryBo;
import cn.xianlink.order.domain.delivery.bo.SupWaitDeliveryQueryBo;
import cn.xianlink.order.domain.delivery.vo.SupDeliveryGoodsSumVo;
import cn.xianlink.order.domain.delivery.vo.SupDeliveryGoodsVo;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * 供应商送货单明细Service接口
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
public interface ISupDeliveryGoodsService {

    /**
     * 查询商品详情
     */
    SupDeliveryGoodsVo queryById(Long id);

    /**
     * 查询送货单明细列表
     */
    List<SupDeliveryGoodsVo> queryListByDeliveryIds(List<Long> deliveryIds);
    /**
     * 查询送货单明细列表
     */
    List<SupDeliveryGoodsVo> queryListByDeliveryId(Long deliveryId);

    List<Long> deliveryIdListBySpuName(SupDeliveryQueryBo bo);

    /**
     * 查询送货单明细汇总
     */
    List<SupDeliveryGoodsSumVo> customSumListBySupplierId(SupWaitDeliveryQueryBo bo);
    /**
     * 查询送货单sku汇总
     */
    List<SupDeliveryGoodsSumVo> customSumListByDeliveryId(Long deliveryId);
    List<SupDeliveryGoodsSumVo> customSumListByPickingNo(String pickingNo);

    /**
     * 送货单明细是否有质检操作
     */
    Boolean isExistInspectRecord(Long deliveryId);

    /**
     * 送货单明细是否有待质检
     */
    Boolean isExistWaitInspectRecord(Long deliveryId, Integer status);

    /**
     * 批量创建明细
     */
    Boolean insertBatch(Long deliveryId, List<SupDeliveryGoodsAddBo> bos);

    /**
     * 商品质检
     */
    int updateInspect(SupDeliveryGoodsEditBo bo);

    /**
     * 修改状态
     */
    int revokeInspect(Long deliveryId);

    /**
     * 自动质检
     */
    int autoCompleteInspect(List<Long> deliveryIds);

}
