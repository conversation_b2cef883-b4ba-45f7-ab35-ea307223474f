package cn.xianlink.order.controller.platform;


import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.order.domain.bo.InvoiceApplyBo;
import cn.xianlink.order.domain.bo.InvoiceItemBo;
import cn.xianlink.order.domain.bo.InvoiceQueryBo;
import cn.xianlink.order.domain.vo.InvoiceOverviewVo;
import cn.xianlink.order.domain.vo.InvoiceQueryVo;
import cn.xianlink.order.domain.vo.InvoiceVo;
import cn.xianlink.order.domain.vo.invoice.InvoiceItemSkuVo;
import cn.xianlink.order.service.IInvoiceItemService;
import lombok.RequiredArgsConstructor;
import jakarta.validation.constraints.*;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.log.annotation.Log;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.validate.AddGroup;
import cn.xianlink.common.core.validate.EditGroup;
import cn.xianlink.common.log.enums.BusinessType;
import cn.xianlink.order.domain.bo.platform.*;
import cn.xianlink.order.domain.vo.platform.*;
import cn.xianlink.order.service.IInvoiceService;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.system.api.model.LoginUser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;

/**
 * 发票
 * 前端访问路由地址为:/order/invoice
 *
 * <AUTHOR>
 * @date 2025-07-29
 * @folder 采集平台(小程序)/发票/发票
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/order/platform/invoice")
public class InvoiceController extends BaseController {

    private final IInvoiceService invoiceService;

    private final IInvoiceItemService  invoiceItemService;


    // ==================== 客户端发票申请相关接口 ====================

    /**
     * 1. 未开票列表接口
     * 统计指定时间范围内客户的订单项数据，按供应商维度分组
     * 计算：开票金额 = 实付金额 - 所有退款/少货/缺货等逆向金额
     */
    @Operation(summary = "查询客户端未开票列表", description = "统计指定时间范围内客户的订单项数据，按供应商维度分组")
    @GetMapping("/client/uninvoiced")
    public R<TableDataInfo<ClientUnInvoicedVo>> getClientUnInvoicedList(
            @Parameter(description = "查询条件") @RequestBody ClientUnInvoicedBo bo,
            @Parameter(description = "分页参数") PageQuery pageQuery) {

        // 获取当前登录用户信息
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser == null || loginUser.getRelationId() == null) {
            throw new RuntimeException("用户未登录或客户信息不完整");
        }

        // 设置客户ID
        bo.setCustomerId(loginUser.getRelationId());

        return R.ok(invoiceService.queryClientUnInvoicedList(bo, pageQuery));
    }

    /**
     * 2. 开票中和已开票列表接口
     * 根据发票状态（开票中/已开票）返回客户申请的发票列表
     * 支持按申请时间、发票类型筛选
     */
    @Operation(summary = "查询客户端发票列表", description = "根据发票状态返回客户申请的发票列表")
    @PostMapping("/client/list")
    public R<TableDataInfo<InvoiceVo>> getClientInvoiceList(
            @Parameter(description = "查询条件") @RequestBody ClientInvoiceListBo bo,
            @Parameter(description = "分页参数") PageQuery pageQuery) {

        // 获取当前登录用户信息
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser == null || loginUser.getRelationId() == null) {
            throw new RuntimeException("用户未登录或客户信息不完整");
        }

        // 设置客户ID
        bo.setCustomerId(loginUser.getRelationId());

        return R.ok(invoiceService.queryClientInvoiceList(bo, pageQuery));
    }

    /**
     * 3. 申请开票接口
     * 基于未开票列表接口返回的数据生成发票
     * 支持单选、批量选择开票项
     *
     * 注意：系统字段(createCode,createName,updateCode,updateName)由系统自动维护，
     * 前端不需要传参，后端不需要手动设置
     */
    @Operation(summary = "客户端申请开票", description = "基于未开票列表数据生成发票申请")
    @Log(title = "客户端发票申请", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/client/apply")
    public R<Void> clientApplyInvoice(
            @Parameter(description = "申请开票参数", required = true)
            @Validated(AddGroup.class) @RequestBody ClientApplyInvoiceBo bo) {

        // 获取当前登录用户信息
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser == null || loginUser.getRelationId() == null) {
            throw new RuntimeException("用户未登录或客户信息不完整");
        }

        // 设置客户ID（系统自动设置，不需要前端传参）
        bo.setCustomerId(loginUser.getRelationId());

        // 校验发票格式和地址的匹配性
        if ("electronic".equals(bo.getInvoiceFormat()) &&
            (bo.getEmailAddress() == null || bo.getEmailAddress().trim().isEmpty())) {
            throw new RuntimeException("电子发票必须填写邮箱地址");
        }

        if ("paper".equals(bo.getInvoiceFormat()) &&
            (bo.getDetailedAddress() == null || bo.getDetailedAddress().trim().isEmpty())) {
            throw new RuntimeException("纸质发票必须填写邮寄地址");
        }

        return toAjax(invoiceService.clientApplyInvoice(bo));
    }

    /**
     * 4. 发票详情查询接口
     * 返回内容：发票金额、类型、抬头、发票文件、纸质发票物流信息、发票汇总信息、商品明细
     */
    @Operation(summary = "发票详情查询接口", description = "返回发票的详细信息，包括金额、类型、抬头、文件、物流信息、商品明细等")
    @GetMapping("/client/{invoiceId}")
    public R<InvoiceVo> getClientInvoiceDetail(
            @Parameter(description = "发票ID", required = true)
            @NotNull(message = "发票ID不能为空") @PathVariable Long invoiceId) {

        // 获取当前登录用户信息
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser == null || loginUser.getRelationId() == null) {
            throw new RuntimeException("用户未登录或客户信息不完整");
        }

        InvoiceVo vo = invoiceService.queryById(invoiceId);
        if(vo == null || !vo.getCustomerId().equals(loginUser.getRelationId())){
            throw new RuntimeException("发票不存在或不属于当前客户");
        }
        return R.ok(vo);
    }

    /**
     *  根据发票id 查询按sku汇总发票项 列表查询
     */
    @Operation(summary = "发票详情发票项列表查询接口", description = "发票详情 发票项列表查询接口")
    @PostMapping("/invoiceitem/list/{invoiceId}")
    public R<TableDataInfo<InvoiceItemSkuVo>> invoiceitemlist(
            @Parameter(description = "发票ID", required = true)
            @NotNull(message = "发票ID不能为空") @PathVariable Long invoiceId ,
            @Parameter(description = "分页参数") PageQuery pageQuery) {

        // 获取当前登录用户信息
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser == null || loginUser.getRelationId() == null) {
            throw new ServiceException("用户未登录或客户信息不完整");
        }

        InvoiceVo vo = invoiceService.queryById(invoiceId);
        if(vo == null || !vo.getCustomerId().equals(loginUser.getRelationId())){
            throw new ServiceException("发票不存在或不属于当前客户");
        }
        InvoiceItemBo bo = new InvoiceItemBo();
        bo.setInvoiceId(invoiceId);
        return R.ok(invoiceItemService.queryInvoiceitemlist(bo, pageQuery));
    }

    /**
     * 5. 发票详情编辑接口
     * 允许编辑发票详情中的邮箱地址
     *
     * 注意：系统字段(updateCode,updateName,updateTime)由系统自动维护，
     * 前端不需要传参，后端不需要手动设置
     */
    @Operation(summary = "更新客户端发票邮箱", description = "允许编辑发票详情中的邮箱地址")
    @Log(title = "客户端发票邮箱更新", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/client/email")
    public R<Void> updateClientInvoiceEmail(
            @Parameter(description = "更新邮箱参数", required = true)
            @Validated(EditGroup.class) @RequestBody ClientUpdateInvoiceEmailBo bo) {

        // 获取当前登录用户信息
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser == null || loginUser.getRelationId() == null) {
            throw new ServiceException("用户未登录或客户信息不完整");
        }

        // 设置发票ID和客户ID（系统自动设置，不需要前端传参）
        bo.setCustomerId(loginUser.getRelationId());

        return toAjax(invoiceService.updateClientInvoiceEmail(bo));
    }

    /**
     * 分页查询待开发票列表
     */
    @PostMapping("/waitInvoicePage")
    public R<TableDataInfo<InvoiceQueryVo>> queryWaitInvoicePage(@Validated @RequestBody InvoiceQueryBo bo) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        bo.setCustomerId(loginUser.getRelationId());
        return R.ok(invoiceService.queryWaitInvoicePage(bo));
    }

    /**
     * 查询待开发票详情
     */
    @PostMapping("/waitInvoiceDetail")
    public R<InvoiceQueryVo> getWaitInvoiceDetail(@Validated @RequestBody InvoiceQueryBo bo) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        bo.setCustomerId(loginUser.getRelationId());
        return R.ok(invoiceService.getWaitInvoiceDetail(bo));
    }

    /**
     * 申请开票
     */
    @Log(title = "申请开票", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/applyInvoice")
    public R<InvoiceQueryVo> applyInvoice(@Validated @RequestBody InvoiceApplyBo bo) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        bo.setCustomerId(loginUser.getRelationId());
        invoiceService.applyInvoice(bo);
        return R.ok();
    }

    /**
     * 查询勾选发票的总金额、开票项
     */
    @PostMapping("/invoiceOverview")
    public R<InvoiceOverviewVo> invoiceOverview(@Validated @RequestBody InvoiceQueryBo bo) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        bo.setCustomerId(loginUser.getRelationId());
        return R.ok(invoiceService.queryInvoiceOverview(bo));
    }

    /**
     * 查询勾选发票的可开发票类型
     */
    @PostMapping("/provideInvoice")
    public R<InvoiceOverviewVo> provideInvoice(@Validated @RequestBody InvoiceQueryBo bo) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        bo.setCustomerId(loginUser.getRelationId());
        return R.ok(invoiceService.queryProvideInvoice(bo));
    }
}
