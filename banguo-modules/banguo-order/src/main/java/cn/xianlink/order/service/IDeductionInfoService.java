package cn.xianlink.order.service;

import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.order.api.bo.RemoteSupAccTransQueryBo;
import cn.xianlink.order.api.vo.RemoteSupTransDeductionVo;
import cn.xianlink.order.domain.DeductionInfo;
import cn.xianlink.order.domain.bo.DeductionInfoBackStageBO;
import cn.xianlink.order.domain.bo.DeductionInfoBo;
import cn.xianlink.order.api.bo.DeductionInfoParamBo;
import cn.xianlink.order.domain.vo.DeductionInfoVo;
import cn.xianlink.order.domain.vo.DeductionReasonVO;
import cn.xianlink.order.domain.vo.DeductionStatusCountVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import java.util.Collection;
import java.util.List;

/**
 * 扣款单Service接口
 *
 * <AUTHOR>
 * @date 2024-06-01
 */
public interface IDeductionInfoService {

    /**
     * 查询扣款单
     */
    DeductionInfoVo queryById(Long id);

    /**
     * 查询扣款单列表
     */
    TableDataInfo<DeductionInfoVo> queryPageList(DeductionInfoBo bo, PageQuery pageQuery);

    /**
     * 查询扣款单列表
     */
    List<DeductionInfoVo> queryList(DeductionInfoBo bo);

    LambdaQueryWrapper<DeductionInfo> buildQueryWrapper(DeductionInfoBo bo);

    /**
     * 新增扣款单
     */
    Boolean insertByBo(DeductionInfoBo bo);

    /**
     * 修改扣款单
     */
    Boolean updateByBo(DeductionInfoBo bo);

    /**
     * 根据code修改状态
     * @param bo
     * @return
     */
    Boolean updateDeductionStatusByCode(DeductionInfoBo bo);

    /**
     * 校验并批量删除扣款单信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据扣款单id删除数据
     * @param id
     * @return
     */
    Boolean deleteDeductionInfoById(Long id);

    /**
     * mq同步数据扣款单数据 注意**:新增数据需要扣款单编码添加唯一值  格式参考订单表的编码
     * @param deductionInfoParamBo
     */
    Boolean insertDeduction(DeductionInfoParamBo deductionInfoParamBo);

    /**
     * 统计扣款单状态数量接口
     * @param bo
     * @return
     */
    DeductionStatusCountVO getDeductionStatusCount(DeductionInfoBo bo);

    /**
     * 批量添加扣款单信息 注意**:新增数据需要扣款单编码添加唯一值  格式参考订单表的编码
     * @param infoBoList
     * @return
     */
    Boolean batchAddDeductionInfo(List<DeductionInfoBo> infoBoList);

    /**
     * 获取扣款原因字典数据
     * @return
     */
    List<DeductionReasonVO> listDeductionReasonVO(String dictType);

    /**
     * 同步扣款单数据到结算单接口
     * @param date
     * @param codeList
     * @return
     */
    Boolean saveUseDeductionInfo(String date, List<String> codeList);

    /**
     * 根据关联单据类型和关联单据查询不同得单据信息
     * @param billType
     * @param billCode
     * @return
     */
    Object getBillInfoByTypeAndCode(Integer billType, String billCode);

    /**
     * 扣款单同步到划转单mq
     * @param deductionInfoCode
     */
    void syncDeductionInfo(String deductionInfoCode);

    /**
     * 批量查询对应的扣款单信息
     * @param bo
     * @return
     */
    List<RemoteSupTransDeductionVo> queryDeduction(RemoteSupAccTransQueryBo bo);

    /**
     * 手动结算城市仓加款单
     */
    Integer divide(List<Long> ids);

    /**
     * 导出扣款单列表
     */
    void exportDeductionRecord(DeductionInfoBo bo);
}
