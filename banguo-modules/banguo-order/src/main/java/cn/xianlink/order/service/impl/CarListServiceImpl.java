package cn.xianlink.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.basic.api.RemoteRegionWhService;
import cn.xianlink.basic.api.domain.vo.RemoteRegionWhVo;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.order.domain.CarList;
import cn.xianlink.order.domain.bo.CarListBo;
import cn.xianlink.order.domain.vo.CarListVo;
import cn.xianlink.order.mapper.CarListMapper;
import cn.xianlink.order.service.ICarListService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 车型表Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-30
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class CarListServiceImpl implements ICarListService {

    private final CarListMapper baseMapper;

    @DubboReference
    private final RemoteRegionWhService remoteRegionWhService;

    /**
     * 查询车型表
     */
    @Override
    public CarListVo queryById(Long id) {
        CarListVo vo = baseMapper.selectVoById(id);
        if (vo != null) {
            enrichRegionWhInfo(List.of(vo));
        }
        return vo;
    }

    /**
     * 查询车型表列表
     */
    @Override
    public TableDataInfo<CarListVo> queryPageList(CarListBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<CarList> lqw = buildQueryWrapper(bo);
        Page<CarListVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        
        // 补充总仓信息
        enrichRegionWhInfo(result.getRecords());
        
        return TableDataInfo.build(result);
    }

    /**
     * 查询车型表列表
     */
    @Override
    public List<CarListVo> queryList(CarListBo bo) {
        LambdaQueryWrapper<CarList> lqw = buildQueryWrapper(bo);
        List<CarListVo> list = baseMapper.selectVoList(lqw);
        
        // 补充总仓信息
        enrichRegionWhInfo(list);
        
        return list;
    }

    /**
     * 根据总仓ID查询车型列表
     */
    @Override
    public List<CarListVo> queryByRegionWhId(Long regionWhId) {
        if (regionWhId == null) {
            return List.of();
        }
        
        LambdaQueryWrapper<CarList> lqw = Wrappers.lambdaQuery();
        lqw.eq(CarList::getRegionWhId, regionWhId);
        lqw.orderByAsc(CarList::getModel);
        
        List<CarListVo> list = baseMapper.selectVoList(lqw);
        enrichRegionWhInfo(list);
        
        return list;
    }

    private LambdaQueryWrapper<CarList> buildQueryWrapper(CarListBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<CarList> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getModel()), CarList::getModel, bo.getModel());
        lqw.eq(bo.getRegionWhId() != null, CarList::getRegionWhId, bo.getRegionWhId());
        lqw.ge(bo.getCarryWeight() != null, CarList::getCarryWeight, bo.getCarryWeight());
        lqw.orderByDesc(CarList::getCreateTime);
        return lqw;
    }

    /**
     * 补充总仓信息
     */
    private void enrichRegionWhInfo(List<CarListVo> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        
        try {
            List<Long> regionWhIds = list.stream()
                .map(CarListVo::getRegionWhId)
                .filter(ObjectUtil::isNotNull)
                .distinct()
                .collect(Collectors.toList());
            
            if (CollUtil.isNotEmpty(regionWhIds)) {
                List<RemoteRegionWhVo> regionWhList = remoteRegionWhService.queryByIds(regionWhIds);
                Map<Long, String> regionWhMap = regionWhList.stream()
                    .collect(Collectors.toMap(RemoteRegionWhVo::getId, RemoteRegionWhVo::getName, (k1, k2) -> k1));
                
                list.forEach(vo -> {
                    String regionWhName = regionWhMap.get(vo.getRegionWhId());
                    vo.setRegionWhName(regionWhName);
                });
            }
        } catch (Exception e) {
            log.warn("补充总仓信息失败: {}", e.getMessage());
        }
    }

    /**
     * 新增车型表
     */
    @Override
    public Boolean insertByBo(CarListBo bo) {
        CarList add = MapstructUtils.convert(bo, CarList.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改车型表
     */
    @Override
    public Boolean updateByBo(CarListBo bo) {
        CarList update = MapstructUtils.convert(bo, CarList.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(CarList entity) {
        // 校验车辆型号唯一性（同一总仓下）
        LambdaQueryWrapper<CarList> lqw = Wrappers.lambdaQuery();
        lqw.eq(CarList::getModel, entity.getModel());
        lqw.eq(CarList::getRegionWhId, entity.getRegionWhId());
        lqw.ne(entity.getId() != null, CarList::getId, entity.getId());
        
        if (baseMapper.exists(lqw)) {
            throw new ServiceException("该总仓下已存在相同型号的车辆");
        }
        
        // 校验总仓是否存在
        try {
            RemoteRegionWhVo regionWh = remoteRegionWhService.queryById(entity.getRegionWhId());
            if (regionWh == null) {
                throw new ServiceException("总仓信息不存在");
            }
        } catch (Exception e) {
            log.error("校验总仓信息失败: {}", e.getMessage());
            throw new ServiceException("校验总仓信息失败");
        }
    }

    /**
     * 批量删除车型表
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // TODO: 添加删除前的业务校验
            // 例如：检查是否有关联的订单使用了该车型
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

}
