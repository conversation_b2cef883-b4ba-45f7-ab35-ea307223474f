package cn.xianlink.order.enums;

import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.common.core.domain.vo.CoreEnumVo;
import cn.xianlink.common.core.enums.CoreEnumRow;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BizTypeEnum implements CoreEnumRow {
    ORDER(0, "订单"),
    LOSSORDER(1, "报损单"),
    PRODUCT(2, "商品"),
    ADDMEMBER(3, "加入成员");

    /**
     * 类型
     */
    private final Integer type;

    /**
     * 描述
     */
    private final String desc;

    @Override
    public CoreEnumVo getEnumRow() {
        return new CoreEnumVo(String.valueOf(this.type), this.desc);
    }

    /**
     * 根据类型加载枚举
     * @param type 类型
     * @return 枚举
     */
    public static BizTypeEnum loadByType(Integer type) {
        for (BizTypeEnum value : values()) {
            if (ObjectUtil.equal(value.getType(), type)) {
                return value;
            }
        }
        return null;
    }
}
