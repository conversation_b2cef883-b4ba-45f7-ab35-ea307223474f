package cn.xianlink.order.domain.bo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: 发票上传-免税水果
 * @date 2025/8/1 18:41
 */
@Data
public class FreeTaxFruitBo implements Serializable {
    /**
     * skuId
     */
    private Long skuId;

    /**
     * 二级分类ID
     */
    private Long categoryIdLevel2;

    /**
     * 免税金额(元)
     */
    private BigDecimal taxFreeAmount;

    /**
     * 免税重量（斤）
     */
    private BigDecimal taxFreeWeight;
}
