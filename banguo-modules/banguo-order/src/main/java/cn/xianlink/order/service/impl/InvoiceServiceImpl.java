package cn.xianlink.order.service.impl;

import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import cn.xianlink.order.domain.bo.InvoiceBo;
import cn.xianlink.order.domain.vo.InvoiceVo;
import cn.xianlink.order.domain.Invoice;
import cn.xianlink.order.domain.bo.platform.*;
import cn.xianlink.order.domain.vo.platform.*;
import cn.xianlink.order.mapper.InvoiceMapper;
import cn.xianlink.order.service.IInvoiceService;
import cn.xianlink.order.service.IInvoiceItemService;
import cn.xianlink.order.service.IOrderItemService;
import cn.xianlink.order.service.IInvoiceTitleService;
import cn.xianlink.order.domain.InvoiceItem;
import cn.xianlink.order.domain.OrderItem;
import cn.xianlink.order.domain.bo.InvoiceItemBo;
import cn.xianlink.order.domain.vo.InvoiceTitleVo;
import cn.xianlink.order.domain.vo.InvoiceItemVo;
import cn.xianlink.order.mapper.InvoiceItemMapper;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.system.api.model.LoginUser;
import cn.xianlink.common.core.exception.ServiceException;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.ArrayList;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 发票Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@RequiredArgsConstructor
@Service
public class InvoiceServiceImpl implements IInvoiceService {

    private final InvoiceMapper baseMapper;
    private final IInvoiceItemService invoiceItemService;
    private final IOrderItemService orderItemService;
    private final IInvoiceTitleService invoiceTitleService;
    private final InvoiceItemMapper invoiceItemMapper;
    private final InvoiceMapper invoiceMapper;

    /**
     * 查询发票
     */
    @Override
    public InvoiceVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询发票列表
     */
    @Override
    public TableDataInfo<InvoiceVo> queryPageList(InvoiceBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<Invoice> lqw = buildQueryWrapper(bo);
        Page<InvoiceVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询发票列表
     */
    @Override
    public List<InvoiceVo> queryList(InvoiceBo bo) {
        LambdaQueryWrapper<Invoice> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<Invoice> buildQueryWrapper(InvoiceBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Invoice> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getInvoiceNumber()), Invoice::getInvoiceNumber, bo.getInvoiceNumber());
        lqw.eq(bo.getSupplierId() != null, Invoice::getSupplierId, bo.getSupplierId());
        lqw.eq(bo.getInvoiceTitleId() != null, Invoice::getInvoiceTitleId, bo.getInvoiceTitleId());
        lqw.eq(bo.getCustomerId() != null, Invoice::getCustomerId, bo.getCustomerId());
        lqw.eq(bo.getInvoiceAmount() != null, Invoice::getInvoiceAmount, bo.getInvoiceAmount());
        lqw.eq(bo.getIssueDate() != null, Invoice::getIssueDate, bo.getIssueDate());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), Invoice::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getScene()), Invoice::getScene, bo.getScene());
        lqw.eq(StringUtils.isNotBlank(bo.getInvoiceType()), Invoice::getInvoiceType, bo.getInvoiceType());
        lqw.eq(StringUtils.isNotBlank(bo.getInvoiceFormat()), Invoice::getInvoiceFormat, bo.getInvoiceFormat());
        lqw.eq(StringUtils.isNotBlank(bo.getSupplierNameSnapshot()), Invoice::getSupplierNameSnapshot, bo.getSupplierNameSnapshot());
        lqw.eq(StringUtils.isNotBlank(bo.getInvoiceTitleSnapshot()), Invoice::getInvoiceTitleSnapshot, bo.getInvoiceTitleSnapshot());
        lqw.eq(StringUtils.isNotBlank(bo.getTaxNumberSnapshot()), Invoice::getTaxNumberSnapshot, bo.getTaxNumberSnapshot());
        lqw.eq(StringUtils.isNotBlank(bo.getAddressSnapshot()), Invoice::getAddressSnapshot, bo.getAddressSnapshot());
        lqw.eq(StringUtils.isNotBlank(bo.getBankAccountSnapshot()), Invoice::getBankAccountSnapshot, bo.getBankAccountSnapshot());
        lqw.eq(StringUtils.isNotBlank(bo.getPhoneSnapshot()), Invoice::getPhoneSnapshot, bo.getPhoneSnapshot());
        lqw.eq(StringUtils.isNotBlank(bo.getEmailAddressSnapshot()), Invoice::getEmailAddressSnapshot, bo.getEmailAddressSnapshot());
        lqw.eq(StringUtils.isNotBlank(bo.getDetailedAddress()), Invoice::getDetailedAddress, bo.getDetailedAddress());
        lqw.eq(StringUtils.isNotBlank(bo.getExpressNumber()), Invoice::getExpressNumber, bo.getExpressNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getFileUrls()), Invoice::getFileUrls, bo.getFileUrls());
        lqw.eq(bo.getApplyDate() != null, Invoice::getApplyDate, bo.getApplyDate());
        lqw.eq(bo.getAuditDate() != null, Invoice::getAuditDate, bo.getAuditDate());
        lqw.eq(StringUtils.isNotBlank(bo.getCreateCode()), Invoice::getCreateCode, bo.getCreateCode());
        lqw.like(StringUtils.isNotBlank(bo.getCreateName()), Invoice::getCreateName, bo.getCreateName());
        lqw.eq(StringUtils.isNotBlank(bo.getUpdateCode()), Invoice::getUpdateCode, bo.getUpdateCode());
        lqw.like(StringUtils.isNotBlank(bo.getUpdateName()), Invoice::getUpdateName, bo.getUpdateName());
        return lqw;
    }

    /**
     * 新增发票
     */
    @Override
    public Boolean insertByBo(InvoiceBo bo) {
        Invoice add = MapstructUtils.convert(bo, Invoice.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改发票
     */
    @Override
    public Boolean updateByBo(InvoiceBo bo) {
        Invoice update = MapstructUtils.convert(bo, Invoice.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(Invoice entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除发票
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    // ==================== 客户端发票申请相关接口实现 ====================

    /**
     * 查询客户端未开票列表
     * TODO: 手动实现，需要统计指定时间范围内客户的订单项数据，按供应商维度分组
     */
    @Override
    public TableDataInfo<ClientUnInvoicedVo> queryClientUnInvoicedList(ClientUnInvoicedBo bo, PageQuery pageQuery) {
        // TODO: 实现未开票列表查询逻辑
        // 1. 查询指定时间范围内客户的订单项数据
        // 2. 按供应商维度分组
        // 3. 计算开票金额 = 实付金额 - 所有退款/少货/缺货等逆向金额
        // 4. 统计商品件数、订单数量等信息
        // 5. 生成订单日期区间描述
        // 6. 判断是否可开票（金额大于0）
        throw new UnsupportedOperationException("未开票列表接口待实现");
    }

    /**
     * 查询客户端发票列表（开票中和已开票）
     */
    @Override
    public TableDataInfo<ClientInvoiceListVo> queryClientInvoiceList(ClientInvoiceListBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<Invoice> lqw = buildClientInvoiceQueryWrapper(bo);
        Page<InvoiceVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);

        // 转换为客户端发票列表VO
        List<ClientInvoiceListVo> clientInvoiceList = result.getRecords().stream()
            .map(this::convertToClientInvoiceListVo)
            .toList();

        return TableDataInfo.build(clientInvoiceList, result.getTotal());
    }

    /**
     * 客户端申请开票
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean clientApplyInvoice(ClientApplyInvoiceBo bo) {
        // 设置当前登录用户信息
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser == null) {
            throw new ServiceException("用户未登录");
        }
        bo.setCustomerId(loginUser.getRelationId());

        // 1. 校验订单项是否属于当前客户
        validateOrderItemsBelongToCustomer(bo.getOrderItemIds(), bo.getCustomerId());

        // 2. 校验订单项是否已开票
        validateOrderItemsNotInvoiced(bo.getOrderItemIds());

        // 3. 校验发票抬头是否属于当前客户
        validateInvoiceTitleBelongToCustomer(bo.getInvoiceTitleId(), bo.getCustomerId());

        // 4. 校验发票格式和必填字段
        validateInvoiceFormatRequiredFields(bo);

        // 5. 生成发票记录
        Invoice invoice = createInvoiceFromBo(bo);
        boolean invoiceCreated = baseMapper.insert(invoice) > 0;

        if (invoiceCreated) {
            // 6. 生成发票项记录
            createInvoiceItems(invoice.getId(), bo.getOrderItemIds());

            // 7. 更新订单项开票状态
            updateOrderItemsInvoiceStatus(bo.getOrderItemIds(), true);
        }

        return invoiceCreated;
    }

    /**
     * 查询客户端发票详情
     */
    @Override
    public ClientInvoiceDetailVo queryClientInvoiceDetail(Long invoiceId, Long customerId) {
        // 1. 校验发票是否属于当前客户
        Invoice invoice = invoiceMapper.selectById(invoiceId);
        if(invoice == null || invoice.getCustomerId() == null || !invoice.getCustomerId().equals(customerId)) {
            throw new ServiceException("发票不存在或不属于当前客户");
        }

        // 2. 构建发票详情VO
        ClientInvoiceDetailVo detailVo = new ClientInvoiceDetailVo();
        detailVo.setId(invoice.getId());
        detailVo.setInvoiceNumber(invoice.getInvoiceNumber());

        // 3. 查询并设置供应商信息
        detailVo.setSupplierInfo(getSupplierInfo(invoice.getSupplierId()));

        // 4. 查询并设置发票抬头信息
        detailVo.setInvoiceTitleInfo(getInvoiceTitleInfo(invoice.getInvoiceTitleId()));

        // 5. 设置发票基本信息
        detailVo.setInvoiceBasicInfo(getInvoiceBasicInfo(invoice));

        // 6. 查询并设置发票文件信息
        detailVo.setInvoiceFiles(getInvoiceFiles(invoiceId));

        // 7. 查询并设置物流信息（纸质发票）
        if ("paper".equals(invoice.getInvoiceFormat())) {
            detailVo.setLogisticsInfo(getLogisticsInfo(invoiceId));
        }

        // 8. 查询并设置发票项明细
        detailVo.setItemDetails(getInvoiceItemDetails(invoiceId));

        // 9. 计算并设置汇总信息
        detailVo.setSummaryInfo(calculateSummaryInfo(invoiceId));

        return detailVo;
    }

    /**
     * 更新客户端发票邮箱地址
     */
    @Override
    public Boolean updateClientInvoiceEmail(ClientUpdateInvoiceEmailBo bo) {
        // 1. 校验发票是否属于当前客户
        Invoice invoice = invoiceMapper.selectById(bo.getInvoiceId());
        if(invoice == null || invoice.getCustomerId() == null || !invoice.getCustomerId().equals(bo.getCustomerId())) {
            throw new ServiceException("发票不存在或不属于当前客户");
        }

        // 2. 校验发票状态是否允许修改
        validateInvoiceStatusForEmailUpdate(invoice.getStatus());

        // 3. 更新发票邮箱地址
        // 注意：系统字段(updateCode,updateName,updateTime)由框架自动维护，不需要手动设置
        Invoice updateInvoice = new Invoice();
        updateInvoice.setId(bo.getInvoiceId());
        updateInvoice.setEmailAddressSnapshot(bo.getEmailAddress());

        return baseMapper.updateById(updateInvoice) > 0;
    }

    // ==================== 私有辅助方法 ====================

    private LambdaQueryWrapper<Invoice> buildClientInvoiceQueryWrapper(ClientInvoiceListBo bo) {
        // TODO: 构建客户端发票查询条件
        return Wrappers.lambdaQuery();
    }

    private ClientInvoiceListVo convertToClientInvoiceListVo(InvoiceVo invoiceVo) {
        // TODO: 转换为客户端发票列表VO
        return new ClientInvoiceListVo();
    }

    private void validateOrderItemsBelongToCustomer(List<Long> orderItemIds, Long customerId) {
        if (orderItemIds == null || orderItemIds.isEmpty()) {
            throw new ServiceException("订单项ID列表不能为空");
        }

        List<OrderItem> orderItems = orderItemService.selectListByIds(orderItemIds);
        if (orderItems.size() != orderItemIds.size()) {
            throw new ServiceException("部分订单项不存在");
        }

        for (OrderItem orderItem : orderItems) {
            if (!customerId.equals(orderItem.getCustomerId())) {
                throw new ServiceException("订单项不属于当前客户，订单项ID：" + orderItem.getId());
            }
        }
    }

    private void validateOrderItemsNotInvoiced(List<Long> orderItemIds) {
        // 查询是否已有发票项记录
        LambdaQueryWrapper<InvoiceItem> wrapper = Wrappers.lambdaQuery();
        wrapper.in(InvoiceItem::getOrderItemId, orderItemIds);

        List<InvoiceItemVo> existingItems = invoiceItemMapper.selectVoList(wrapper);

        if (!existingItems.isEmpty()) {
            List<Long> invoicedOrderItemIds = existingItems.stream()
                .map(InvoiceItemVo::getOrderItemId)
                .toList();
            throw new ServiceException("以下订单项已开票，不能重复开票：" + invoicedOrderItemIds);
        }
    }

    private void validateInvoiceTitleBelongToCustomer(Long invoiceTitleId, Long customerId) {
        InvoiceTitleVo invoiceTitle = invoiceTitleService.queryById(invoiceTitleId);
        if (invoiceTitle == null) {
            throw new ServiceException("发票抬头不存在");
        }

        if (!customerId.equals(invoiceTitle.getCustomerId())) {
            throw new ServiceException("发票抬头不属于当前客户");
        }
    }

    private void validateInvoiceFormatRequiredFields(ClientApplyInvoiceBo bo) {
        if ("electronic".equals(bo.getInvoiceFormat())) {
            // 电子发票必须填写邮箱
            if (StringUtils.isBlank(bo.getEmailAddress())) {
                throw new ServiceException("电子发票必须填写邮箱地址");
            }
        } else if ("paper".equals(bo.getInvoiceFormat())) {
            // 纸质发票必须填写邮寄地址
            if (StringUtils.isBlank(bo.getDetailedAddress())) {
                throw new ServiceException("纸质发票必须填写邮寄地址");
            }
        }
    }

    private Invoice createInvoiceFromBo(ClientApplyInvoiceBo bo) {
        // 获取发票抬头信息用于快照
        InvoiceTitleVo invoiceTitle = invoiceTitleService.queryById(bo.getInvoiceTitleId());

        Invoice invoice = new Invoice();
        invoice.setCustomerId(bo.getCustomerId());
        invoice.setSupplierId(bo.getSupplierId());
        invoice.setInvoiceTitleId(bo.getInvoiceTitleId());
        invoice.setInvoiceType(bo.getInvoiceType());
        invoice.setInvoiceFormat(bo.getInvoiceFormat());
        invoice.setInvoiceAmount(bo.getInvoiceAmount());
        invoice.setTotalItems(bo.getTotalItems());
        invoice.setApplyDate(new Date());
        invoice.setStatus("applied"); // 申请中状态
        invoice.setScene("supplier"); // 供应商开票场景

        // 设置快照信息（冗余存储，避免关联查询）
        if (invoiceTitle != null) {
            invoice.setInvoiceTitleSnapshot(invoiceTitle.getTitleName());
            invoice.setTaxNumberSnapshot(invoiceTitle.getTaxNumber());
            invoice.setAddressSnapshot(invoiceTitle.getAddress());
            invoice.setBankAccountSnapshot(invoiceTitle.getBankAccount());
            invoice.setPhoneSnapshot(invoiceTitle.getPhone());
        }

        // 设置邮箱和邮寄地址
        invoice.setEmailAddressSnapshot(bo.getEmailAddress());
        invoice.setDetailedAddress(bo.getDetailedAddress());

        // 生成发票号（简单实现，实际可能需要更复杂的规则）
        invoice.setInvoiceNumber(generateInvoiceNumber());

        return invoice;
    }

    private void createInvoiceItems(Long invoiceId, List<Long> orderItemIds) {
        // 获取订单项详细信息
        List<OrderItem> orderItems = orderItemService.selectListByIds(orderItemIds);

        List<InvoiceItem> invoiceItems = new ArrayList<>();

        for (OrderItem orderItem : orderItems) {
            InvoiceItem invoiceItemBo = new InvoiceItem();
            invoiceItemBo.setInvoiceId(invoiceId);
            invoiceItemBo.setOrderItemId(orderItem.getId());
            invoiceItemBo.setCustomerId(orderItem.getCustomerId());

            // 判断当前登录用户是否为当前订单项的所属客户
            LoginUser loginUser = LoginHelper.getLoginUser();
            if (loginUser == null || loginUser.getRelationId() == orderItem.getCustomerId()) {
                throw new ServiceException("当前登录用户不是订单项的所属客户");
            }

            invoiceItems.add(invoiceItemBo);
        }

        // 批量插入发票项记录
        invoiceItemMapper.insertBatch(invoiceItems);
    }

    private void updateOrderItemsInvoiceStatus(List<Long> orderItemIds, boolean invoiced) {
        // 这里需要根据实际的OrderItem实体结构来更新开票状态
        // 如果OrderItem有invoiced字段，则更新该字段
        // 由于当前OrderItem实体中没有看到开票状态字段，这里先留空
        // 实际实现时需要根据业务需求添加相应字段和更新逻辑

        // 示例实现（需要根据实际字段调整）：
        /*
        List<OrderItem> orderItems = orderItemService.selectListByIds(orderItemIds);
        for (OrderItem orderItem : orderItems) {
            orderItem.setInvoiced(invoiced ? 1 : 0);
            orderItemService.updateById(orderItem);
        }
        */
    }

    private String generateInvoiceNumber() {
        // 简单的发票号生成规则：INV + 时间戳 + 随机数
        // 实际项目中可能需要更复杂的规则，如按供应商、日期等生成
        long timestamp = System.currentTimeMillis();
        int random = (int) (Math.random() * 1000);
        return String.format("INV%d%03d", timestamp, random);
    }

    private ClientInvoiceDetailVo.SupplierInfo getSupplierInfo(Long supplierId) {
        // TODO: 获取供应商信息
        return new ClientInvoiceDetailVo.SupplierInfo();
    }

    private ClientInvoiceDetailVo.InvoiceTitleInfo getInvoiceTitleInfo(Long invoiceTitleId) {
        // TODO: 获取发票抬头信息
        return new ClientInvoiceDetailVo.InvoiceTitleInfo();
    }

    private ClientInvoiceDetailVo.InvoiceBasicInfo getInvoiceBasicInfo(Invoice invoice) {
        // TODO: 获取发票基本信息
        return new ClientInvoiceDetailVo.InvoiceBasicInfo();
    }

    private List<ClientInvoiceDetailVo.InvoiceFileInfo> getInvoiceFiles(Long invoiceId) {
        // TODO: 获取发票文件信息
        return List.of();
    }

    private ClientInvoiceDetailVo.LogisticsInfo getLogisticsInfo(Long invoiceId) {
        // TODO: 获取物流信息
        return new ClientInvoiceDetailVo.LogisticsInfo();
    }

    private List<InvoiceItemDetailVo> getInvoiceItemDetails(Long invoiceId) {
        // TODO: 获取发票项明细
        return List.of();
    }

    private ClientInvoiceDetailVo.InvoiceSummaryInfo calculateSummaryInfo(Long invoiceId) {
        // TODO: 计算汇总信息
        return new ClientInvoiceDetailVo.InvoiceSummaryInfo();
    }

    private void validateInvoiceStatusForEmailUpdate(String status) {
        // TODO: 校验发票状态是否允许修改邮箱
        if (!"applied".equals(status) && !"uploaded".equals(status)) {
            throw new ServiceException("当前发票状态不允许修改邮箱地址");
        }
    }
}
