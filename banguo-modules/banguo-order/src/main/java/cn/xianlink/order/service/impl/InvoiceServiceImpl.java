package cn.xianlink.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.basic.api.RemoteRegionWhService;
import cn.xianlink.basic.api.RemoteSupplierService;
import cn.xianlink.basic.api.domain.vo.RemoteRegionWhVo;
import cn.xianlink.basic.api.domain.vo.RemoteSupplierVo;
import cn.xianlink.common.api.enums.basic.SupplierProvideInvoiceEnum;
import cn.xianlink.common.api.enums.order.*;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.order.domain.Invoice;
import cn.xianlink.order.domain.InvoiceAuditLog;
import cn.xianlink.order.domain.InvoiceItem;
import cn.xianlink.order.domain.OrderItem;
import cn.xianlink.order.domain.bo.*;
import cn.xianlink.order.domain.bo.platform.ClientApplyInvoiceBo;
import cn.xianlink.order.domain.bo.platform.ClientInvoiceListBo;
import cn.xianlink.order.domain.bo.platform.ClientUnInvoicedBo;
import cn.xianlink.order.domain.bo.platform.ClientUpdateInvoiceEmailBo;
import cn.xianlink.order.domain.vo.*;
import cn.xianlink.order.domain.vo.platform.ClientInvoiceDetailVo;
import cn.xianlink.order.domain.vo.platform.ClientInvoiceListVo;
import cn.xianlink.order.domain.vo.platform.ClientUnInvoicedVo;
import cn.xianlink.order.mapper.InvoiceAuditLogMapper;
import cn.xianlink.order.mapper.InvoiceItemMapper;
import cn.xianlink.order.mapper.InvoiceMapper;
import cn.xianlink.order.mapper.OrderItemMapper;
import cn.xianlink.order.service.IInvoiceItemService;
import cn.xianlink.order.service.IInvoiceService;
import cn.xianlink.order.service.IInvoiceTitleService;
import cn.xianlink.order.service.IOrderItemService;
import cn.xianlink.order.util.CustomNoUtil;
import cn.xianlink.product.api.RemoteSupplierSkuService;
import cn.xianlink.system.api.model.LoginUser;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 发票Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@RequiredArgsConstructor
@Service
public class InvoiceServiceImpl implements IInvoiceService {

    private final InvoiceMapper baseMapper;
    private final IInvoiceItemService invoiceItemService;
    private final IOrderItemService orderItemService;
    private final IInvoiceTitleService invoiceTitleService;
    private final InvoiceItemMapper invoiceItemMapper;
    private final InvoiceMapper invoiceMapper;

    private final InvoiceAuditLogMapper invoiceAuditLogMapper;

    @DubboReference
    private final transient RemoteSupplierService remoteSupplierService;

    @DubboReference
    private final transient RemoteRegionWhService remoteRegionWhService;

    @DubboReference
    private final transient RemoteSupplierSkuService remoteSupplierSkuService;



    /**
     * 查询发票
     */
    @Override
    public InvoiceVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询发票列表
     */
    @Override
    public TableDataInfo<InvoiceVo> queryPageList(InvoiceBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<Invoice> lqw = buildQueryWrapper(bo);
        Page<InvoiceVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询发票列表
     */
    @Override
    public List<InvoiceVo> queryList(InvoiceBo bo) {
        LambdaQueryWrapper<Invoice> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<Invoice> buildQueryWrapper(InvoiceBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Invoice> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getInvoiceNumber()), Invoice::getInvoiceNumber, bo.getInvoiceNumber());
        lqw.eq(bo.getSupplierId() != null, Invoice::getSupplierId, bo.getSupplierId());
        lqw.eq(bo.getInvoiceTitleId() != null, Invoice::getInvoiceTitleId, bo.getInvoiceTitleId());
        lqw.eq(bo.getCustomerId() != null, Invoice::getCustomerId, bo.getCustomerId());
        lqw.eq(bo.getInvoiceAmount() != null, Invoice::getInvoiceAmount, bo.getInvoiceAmount());
        lqw.eq(bo.getIssueDate() != null, Invoice::getIssueDate, bo.getIssueDate());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), Invoice::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getScene()), Invoice::getScene, bo.getScene());
        lqw.eq(StringUtils.isNotBlank(bo.getInvoiceType()), Invoice::getInvoiceType, bo.getInvoiceType());
        lqw.eq(StringUtils.isNotBlank(bo.getInvoiceFormat()), Invoice::getInvoiceFormat, bo.getInvoiceFormat());
        lqw.eq(StringUtils.isNotBlank(bo.getSupplierNameSnapshot()), Invoice::getSupplierNameSnapshot, bo.getSupplierNameSnapshot());
        lqw.eq(StringUtils.isNotBlank(bo.getInvoiceTitleSnapshot()), Invoice::getInvoiceTitleSnapshot, bo.getInvoiceTitleSnapshot());
        lqw.eq(StringUtils.isNotBlank(bo.getTaxNumberSnapshot()), Invoice::getTaxNumberSnapshot, bo.getTaxNumberSnapshot());
        lqw.eq(StringUtils.isNotBlank(bo.getAddressSnapshot()), Invoice::getAddressSnapshot, bo.getAddressSnapshot());
        lqw.eq(StringUtils.isNotBlank(bo.getBankAccountSnapshot()), Invoice::getBankAccountSnapshot, bo.getBankAccountSnapshot());
        lqw.eq(StringUtils.isNotBlank(bo.getPhoneSnapshot()), Invoice::getPhoneSnapshot, bo.getPhoneSnapshot());
        lqw.eq(StringUtils.isNotBlank(bo.getEmailAddressSnapshot()), Invoice::getEmailAddressSnapshot, bo.getEmailAddressSnapshot());
        lqw.eq(StringUtils.isNotBlank(bo.getDetailedAddress()), Invoice::getDetailedAddress, bo.getDetailedAddress());
        lqw.eq(StringUtils.isNotBlank(bo.getExpressNumber()), Invoice::getExpressNumber, bo.getExpressNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getFileUrls()), Invoice::getFileUrls, bo.getFileUrls());
        lqw.eq(bo.getApplyDate() != null, Invoice::getApplyDate, bo.getApplyDate());
        lqw.eq(bo.getAuditDate() != null, Invoice::getAuditDate, bo.getAuditDate());
        lqw.eq(StringUtils.isNotBlank(bo.getCreateCode()), Invoice::getCreateCode, bo.getCreateCode());
        lqw.like(StringUtils.isNotBlank(bo.getCreateName()), Invoice::getCreateName, bo.getCreateName());
        lqw.eq(StringUtils.isNotBlank(bo.getUpdateCode()), Invoice::getUpdateCode, bo.getUpdateCode());
        lqw.like(StringUtils.isNotBlank(bo.getUpdateName()), Invoice::getUpdateName, bo.getUpdateName());
        return lqw;
    }

    /**
     * 新增发票
     */
    @Override
    public Boolean insertByBo(InvoiceBo bo) {
        Invoice add = MapstructUtils.convert(bo, Invoice.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改发票
     */
    @Override
    public Boolean updateByBo(InvoiceBo bo) {
        Invoice update = MapstructUtils.convert(bo, Invoice.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(Invoice entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除发票
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    // ==================== 客户端发票申请相关接口实现 ====================

    /**
     * 查询客户端未开票列表
     * TODO: 手动实现，需要统计指定时间范围内客户的订单项数据，按供应商维度分组
     */
    @Override
    public TableDataInfo<ClientUnInvoicedVo> queryClientUnInvoicedList(ClientUnInvoicedBo bo, PageQuery pageQuery) {
        // TODO: 实现未开票列表查询逻辑
        // 1. 查询指定时间范围内客户的订单项数据
        // 2. 按供应商维度分组
        // 3. 计算开票金额 = 实付金额 - 所有退款/少货/缺货等逆向金额
        // 4. 统计商品件数、订单数量等信息
        // 5. 生成订单日期区间描述
        // 6. 判断是否可开票（金额大于0）
        throw new UnsupportedOperationException("未开票列表接口待实现");
    }

    /**
     * 查询客户端发票列表（开票中和已开票）
     */
    @Override
    public TableDataInfo<InvoiceVo> queryClientInvoiceList(ClientInvoiceListBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<Invoice> lqw = buildClientInvoiceQueryWrapper(bo);
        Page<InvoiceVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 客户端申请开票
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean clientApplyInvoice(ClientApplyInvoiceBo bo) {
        // 设置当前登录用户信息
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser == null) {
            throw new ServiceException("用户未登录");
        }
        bo.setCustomerId(loginUser.getRelationId());

        // 1. 校验订单项是否属于当前客户
        validateOrderItemsBelongToCustomer(bo.getOrderItemIds(), bo.getCustomerId());

        // 2. 校验订单项是否已开票
        validateOrderItemsNotInvoiced(bo.getOrderItemIds());

        // 3. 校验发票抬头是否属于当前客户
        validateInvoiceTitleBelongToCustomer(bo.getInvoiceTitleId(), bo.getCustomerId());

        // 4. 校验发票格式和必填字段
        validateInvoiceFormatRequiredFields(bo);

        // 5. 生成发票记录
        Invoice invoice = createInvoiceFromBo(bo);
        boolean invoiceCreated = baseMapper.insert(invoice) > 0;

        if (invoiceCreated) {
            // 6. 生成发票项记录
            createInvoiceItems(invoice.getId(), bo.getOrderItemIds());

            // 7. 更新订单项开票状态
            updateOrderItemsInvoiceStatus(bo.getOrderItemIds(), true);
        }

        return invoiceCreated;
    }

    /**
     * 查询客户端发票详情
     * 业务实现步骤：
     * 1. 校验发票是否属于当前客户
     * 2. 构建发票基本信息
     * 3. 查询并设置发票项明细
     * 4. 返回完整的发票详情信息
     */
    @Override
    public ClientInvoiceDetailVo queryClientInvoiceDetail(Long invoiceId, Long customerId) {
        // 1. 校验发票是否属于当前客户
        Invoice invoice = invoiceMapper.selectById(invoiceId);
        if(invoice == null || invoice.getCustomerId() == null || !invoice.getCustomerId().equals(customerId)) {
            throw new ServiceException("发票不存在或不属于当前客户");
        }

        // 2. 构建发票详情VO - 直接映射发票表字段
        ClientInvoiceDetailVo detailVo = new ClientInvoiceDetailVo();

        // 基本信息
        detailVo.setId(invoice.getId());
        detailVo.setInvoiceNumber(invoice.getInvoiceNumber());
        detailVo.setRegionWhId(invoice.getRegionWhId());
        detailVo.setSupplierId(invoice.getSupplierId());
        detailVo.setInvoiceTitleId(invoice.getInvoiceTitleId());
        detailVo.setCustomerId(invoice.getCustomerId());

        // 金额和日期信息
        detailVo.setInvoiceAmount(invoice.getInvoiceAmount());
        detailVo.setIssueDate(invoice.getIssueDate());
        detailVo.setApplyDate(invoice.getApplyDate());
        detailVo.setAuditDate(invoice.getAuditDate());

        // 发票类型和状态
        detailVo.setStatus(invoice.getStatus());
        detailVo.setScene(invoice.getScene());
        detailVo.setInvoiceType(invoice.getInvoiceType());
        detailVo.setInvoiceFormat(invoice.getInvoiceFormat());
        detailVo.setTitleType(invoice.getTitleType());

        // 快照信息（冗余字段）
        detailVo.setSupplierNameSnapshot(invoice.getSupplierNameSnapshot());
        detailVo.setInvoiceTitleSnapshot(invoice.getInvoiceTitleSnapshot());
        detailVo.setTaxNumberSnapshot(invoice.getTaxNumberSnapshot());
        detailVo.setAddressSnapshot(invoice.getAddressSnapshot());
        detailVo.setBankAccountSnapshot(invoice.getBankAccountSnapshot());
        detailVo.setBankNameSnapshot(invoice.getBankNameSnapshot());
        detailVo.setPhoneSnapshot(invoice.getPhoneSnapshot());
        detailVo.setEmailAddressSnapshot(invoice.getEmailAddressSnapshot());

        // 快递信息
        detailVo.setContactPhone(invoice.getContactPhone());
        detailVo.setRegion(invoice.getRegion());
        detailVo.setDetailedAddress(invoice.getDetailedAddress());
        detailVo.setRecipientName(invoice.getRecipientName());
        detailVo.setLatitude(invoice.getLatitude());
        detailVo.setLongitude(invoice.getLongitude());
        detailVo.setExpressNumber(invoice.getExpressNumber());

        // 其他信息
        detailVo.setFileUrls(invoice.getFileUrls());
        detailVo.setMerchantCode(invoice.getMerchantCode());
        detailVo.setTotalItems(invoice.getTotalItems());
        detailVo.setOrderDateRange(invoice.getOrderDateRange());

        // 系统字段
        detailVo.setCreateCode(invoice.getCreateCode());
        detailVo.setCreateName(invoice.getCreateName());
        detailVo.setUpdateCode(invoice.getUpdateCode());
        detailVo.setUpdateName(invoice.getUpdateName());

        // 3. 查询并设置发票项明细
        detailVo.setItemDetails(getInvoiceItemDetails(invoiceId));

        return detailVo;
    }

    /**
     * 更新客户端发票邮箱地址
     */
    @Override
    public Boolean updateClientInvoiceEmail(ClientUpdateInvoiceEmailBo bo) {
        // 1. 校验发票是否属于当前客户
        Invoice invoice = invoiceMapper.selectById(bo.getInvoiceId());
        if(invoice == null || invoice.getCustomerId() == null || !invoice.getCustomerId().equals(bo.getCustomerId())) {
            throw new ServiceException("发票不存在或不属于当前客户");
        }

        // 2. 校验发票状态是否允许修改
        validateInvoiceStatusForEmailUpdate(invoice.getStatus());

        // 3. 更新发票邮箱地址
        // 注意：系统字段(updateCode,updateName,updateTime)由框架自动维护，不需要手动设置
        Invoice updateInvoice = new Invoice();
        updateInvoice.setId(bo.getInvoiceId());
        updateInvoice.setEmailAddressSnapshot(bo.getEmailAddress());

        return baseMapper.updateById(updateInvoice) > 0;
    }

    // ==================== 私有辅助方法 ====================

    private LambdaQueryWrapper<Invoice> buildClientInvoiceQueryWrapper(ClientInvoiceListBo bo) {
        LambdaQueryWrapper<Invoice> lqw = Wrappers.lambdaQuery();

        // 1. 客户数据隔离：强制客户ID条件，确保数据安全
        lqw.eq(bo.getCustomerId() != null, Invoice::getCustomerId, bo.getCustomerId());

        // 2. 多维度筛选
        // 发票状态列表（支持多状态同时查询）
        if (bo.getStatusList() != null && !bo.getStatusList().isEmpty()) {
            lqw.in(Invoice::getStatus, bo.getStatusList());
        }

        // 供应商ID列表
        if (bo.getSupplierIds() != null && !bo.getSupplierIds().isEmpty()) {
            lqw.in(Invoice::getSupplierId, bo.getSupplierIds());
        }

        // 发票号精确匹配
        lqw.eq(StringUtils.isNotBlank(bo.getInvoiceNumber()), Invoice::getInvoiceNumber, bo.getInvoiceNumber());

        // 发票类型精确匹配
        lqw.eq(StringUtils.isNotBlank(bo.getInvoiceType()), Invoice::getInvoiceType, bo.getInvoiceType());

        // 发票格式精确匹配
        lqw.eq(StringUtils.isNotBlank(bo.getInvoiceFormat()), Invoice::getInvoiceFormat, bo.getInvoiceFormat());

        // 订单号精确匹配（通过子查询关联发票项表）
        if (StringUtils.isNotBlank(bo.getOrderCode())) {
            lqw.exists("SELECT 1 FROM inv_invoice_item ii WHERE ii.invoice_id = inv_invoice.id " +
                      "AND ii.order_code = {0} AND ii.del_flag = 0", bo.getOrderCode());
        }

        // 3. 时间范围查询
        // 申请时间范围（支持区间/起始/结束时间）
        if (bo.getApplyStartTime() != null && bo.getApplyEndTime() != null) {
            lqw.between(Invoice::getApplyDate, bo.getApplyStartTime(), bo.getApplyEndTime());
        } else if (bo.getApplyStartTime() != null) {
            lqw.ge(Invoice::getApplyDate, bo.getApplyStartTime());
        } else if (bo.getApplyEndTime() != null) {
            lqw.le(Invoice::getApplyDate, bo.getApplyEndTime());
        }

        // 开票时间范围（支持区间/起始/结束时间）
        if (bo.getIssueStartTime() != null && bo.getIssueEndTime() != null) {
            lqw.between(Invoice::getIssueDate, bo.getIssueStartTime(), bo.getIssueEndTime());
        } else if (bo.getIssueStartTime() != null) {
            lqw.ge(Invoice::getIssueDate, bo.getIssueStartTime());
        } else if (bo.getIssueEndTime() != null) {
            lqw.le(Invoice::getIssueDate, bo.getIssueEndTime());
        }

        // 4. 智能排序：默认按申请时间倒序
        if (StringUtils.isNotBlank(bo.getOrderBy())) {
            boolean isAsc = !"desc".equalsIgnoreCase(bo.getOrderDirection());
            switch (bo.getOrderBy()) {
                case "applyDate":
                    lqw.orderBy(true, isAsc, Invoice::getApplyDate);
                    break;
                case "issueDate":
                    lqw.orderBy(true, isAsc, Invoice::getIssueDate);
                    break;
                case "invoiceAmount":
                    lqw.orderBy(true, isAsc, Invoice::getInvoiceAmount);
                    break;
                case "auditDate":
                    lqw.orderBy(true, isAsc, Invoice::getAuditDate);
                    break;
                default:
                    // 默认按申请时间倒序
                    lqw.orderByDesc(Invoice::getApplyDate);
                    break;
            }
        } else {
            // 默认按申请时间倒序
            lqw.orderByDesc(Invoice::getApplyDate);
        }

        return lqw;
    }

    private ClientInvoiceListVo convertToClientInvoiceListVo(InvoiceVo invoiceVo) {
        // TODO: 转换为客户端发票列表VO
        return new ClientInvoiceListVo();
    }

    private void validateOrderItemsBelongToCustomer(List<Long> orderItemIds, Long customerId) {
        if (orderItemIds == null || orderItemIds.isEmpty()) {
            throw new ServiceException("订单项ID列表不能为空");
        }

        List<OrderItem> orderItems = orderItemService.selectListByIds(orderItemIds);
        if (orderItems.size() != orderItemIds.size()) {
            throw new ServiceException("部分订单项不存在");
        }

        for (OrderItem orderItem : orderItems) {
            if (!customerId.equals(orderItem.getCustomerId())) {
                throw new ServiceException("订单项不属于当前客户，订单项ID：" + orderItem.getId());
            }
        }
    }

    private void validateOrderItemsNotInvoiced(List<Long> orderItemIds) {
        // 查询是否已有发票项记录
        LambdaQueryWrapper<InvoiceItem> wrapper = Wrappers.lambdaQuery();
        wrapper.in(InvoiceItem::getOrderItemId, orderItemIds);

        List<InvoiceItemVo> existingItems = invoiceItemMapper.selectVoList(wrapper);

        if (!existingItems.isEmpty()) {
            List<Long> invoicedOrderItemIds = existingItems.stream()
                .map(InvoiceItemVo::getOrderItemId)
                .toList();
            throw new ServiceException("以下订单项已开票，不能重复开票：" + invoicedOrderItemIds);
        }
    }

    private void validateInvoiceTitleBelongToCustomer(Long invoiceTitleId, Long customerId) {
        InvoiceTitleVo invoiceTitle = invoiceTitleService.queryById(invoiceTitleId);
        if (invoiceTitle == null) {
            throw new ServiceException("发票抬头不存在");
        }

        if (!customerId.equals(invoiceTitle.getCustomerId())) {
            throw new ServiceException("发票抬头不属于当前客户");
        }
    }

    private void validateInvoiceFormatRequiredFields(ClientApplyInvoiceBo bo) {
        if ("electronic".equals(bo.getInvoiceFormat())) {
            // 电子发票必须填写邮箱
            if (StringUtils.isBlank(bo.getEmailAddress())) {
                throw new ServiceException("电子发票必须填写邮箱地址");
            }
        } else if ("paper".equals(bo.getInvoiceFormat())) {
            // 纸质发票必须填写邮寄地址
            if (StringUtils.isBlank(bo.getDetailedAddress())) {
                throw new ServiceException("纸质发票必须填写邮寄地址");
            }
        }
    }

    private Invoice createInvoiceFromBo(ClientApplyInvoiceBo bo) {
        // 获取发票抬头信息用于快照
        InvoiceTitleVo invoiceTitle = invoiceTitleService.queryById(bo.getInvoiceTitleId());

        Invoice invoice = new Invoice();
        invoice.setCustomerId(bo.getCustomerId());
        invoice.setSupplierId(bo.getSupplierId());
        invoice.setInvoiceTitleId(bo.getInvoiceTitleId());
        invoice.setInvoiceType(bo.getInvoiceType());
        invoice.setInvoiceFormat(bo.getInvoiceFormat());
        invoice.setInvoiceAmount(bo.getInvoiceAmount());
        invoice.setTotalItems(bo.getTotalItems());
        invoice.setApplyDate(new Date());
        invoice.setStatus("applied"); // 申请中状态
        invoice.setScene("supplier"); // 供应商开票场景

        // 设置快照信息（冗余存储，避免关联查询）
        if (invoiceTitle != null) {
            invoice.setInvoiceTitleSnapshot(invoiceTitle.getTitleName());
            invoice.setTaxNumberSnapshot(invoiceTitle.getTaxNumber());
            invoice.setAddressSnapshot(invoiceTitle.getAddress());
            invoice.setBankAccountSnapshot(invoiceTitle.getBankAccount());
            invoice.setPhoneSnapshot(invoiceTitle.getPhone());
        }

        // 设置邮箱和邮寄地址
        invoice.setEmailAddressSnapshot(bo.getEmailAddress());
        invoice.setDetailedAddress(bo.getDetailedAddress());

        // 生成发票号（简单实现，实际可能需要更复杂的规则）
        invoice.setInvoiceNumber(generateInvoiceNumber());

        return invoice;
    }

    private void createInvoiceItems(Long invoiceId, List<Long> orderItemIds) {
        // 获取订单项详细信息
        List<OrderItem> orderItems = orderItemService.selectListByIds(orderItemIds);

        List<InvoiceItem> invoiceItems = new ArrayList<>();

        for (OrderItem orderItem : orderItems) {
            InvoiceItem invoiceItemBo = new InvoiceItem();
            invoiceItemBo.setInvoiceId(invoiceId);
            invoiceItemBo.setOrderItemId(orderItem.getId());
            invoiceItemBo.setCustomerId(orderItem.getCustomerId());

            // 判断当前登录用户是否为当前订单项的所属客户
            LoginUser loginUser = LoginHelper.getLoginUser();
            if (loginUser == null || loginUser.getRelationId() == orderItem.getCustomerId()) {
                throw new ServiceException("当前登录用户不是订单项的所属客户");
            }

            invoiceItems.add(invoiceItemBo);
        }

        // 批量插入发票项记录
        invoiceItemMapper.insertBatch(invoiceItems);
    }

    private void updateOrderItemsInvoiceStatus(List<Long> orderItemIds, boolean invoiced) {
        // 这里需要根据实际的OrderItem实体结构来更新开票状态
        // 如果OrderItem有invoiced字段，则更新该字段
        // 由于当前OrderItem实体中没有看到开票状态字段，这里先留空
        // 实际实现时需要根据业务需求添加相应字段和更新逻辑

        // 示例实现（需要根据实际字段调整）：
        /*
        List<OrderItem> orderItems = orderItemService.selectListByIds(orderItemIds);
        for (OrderItem orderItem : orderItems) {
            orderItem.setInvoiced(invoiced ? 1 : 0);
            orderItemService.updateById(orderItem);
        }
        */
    }

    private String generateInvoiceNumber() {
        // 简单的发票号生成规则：INV + 时间戳 + 随机数
        // 实际项目中可能需要更复杂的规则，如按供应商、日期等生成
        long timestamp = System.currentTimeMillis();
        int random = (int) (Math.random() * 1000);
        return String.format("INV%d%03d", timestamp, random);
    }

    // 注意：由于ClientInvoiceDetailVo现在使用扁平化结构，不再需要以下嵌套对象的构建方法
    // 所有信息都直接从Invoice实体映射到ClientInvoiceDetailVo中

    private List<InvoiceItemVo> getInvoiceItemDetails(Long invoiceId) {
        if (invoiceId == null) {
            return List.of();
        }

        // 1. 查询发票项列表
        LambdaQueryWrapper<InvoiceItem> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(InvoiceItem::getInvoiceId, invoiceId);
        List<InvoiceItemVo> invoiceItems = invoiceItemMapper.selectVoList(wrapper);

        if (invoiceItems.isEmpty()) {
            return List.of();
        }
        return invoiceItems;
    }

    // 注意：扁平化的ClientInvoiceDetailVo结构中，汇总信息直接存储在发票表中
    // totalItems、invoiceAmount等字段已经在主方法中直接设置

    private void validateInvoiceStatusForEmailUpdate(String status) {
        // 校验发票状态是否允许修改邮箱
        if (!"applied".equals(status) && !"uploaded".equals(status)) {
            throw new ServiceException("当前发票状态不允许修改邮箱地址");
        }
    }

    // 注意：由于使用扁平化结构，大部分辅助方法已不再需要
    // 所有信息都直接从Invoice实体和InvoiceItem实体中获取

    /**
     * 分页查询待开发票列表
     */
    @Override
    public TableDataInfo<InvoiceQueryVo> queryWaitInvoicePage(InvoiceQueryBo bo) {
        //分页
        Page<InvoiceQueryVo> page = baseMapper.queryWaitInvoice(bo, bo.build());
        if(ObjectUtil.isEmpty(page.getRecords())){
            return TableDataInfo.build(page);
        }
        List<InvoiceQueryVo> records = page.getRecords();
        //转换
        this.buildInvoiceQueryVo(records);
        return TableDataInfo.build(page);
    }

    /**
     * 转换待开发票列表VO
     * @param records
     */
    private void buildInvoiceQueryVo(List<InvoiceQueryVo> records) {
        if(ObjectUtil.isEmpty(records)){
            return;
        }
        //供应商
        List<Long> supplierIdList = records.stream().map(InvoiceQueryVo::getSupplierId).toList();
        List<RemoteSupplierVo> supplierVoList = remoteSupplierService.getSupplierByIds(supplierIdList);
        Map<Long, RemoteSupplierVo> supplierVoMap = supplierVoList.stream().filter(Objects::nonNull).collect(Collectors.toMap(RemoteSupplierVo::getId, Function.identity(), (v1, v2) -> v1));
        //总仓
        List<Long> regionWhIdList = records.stream().map(InvoiceQueryVo::getRegionWhId).toList();
        List<RemoteRegionWhVo> regionWhVoList = remoteRegionWhService.selectRegionWhInfoByIds(regionWhIdList);
        Map<Long, RemoteRegionWhVo> regionWhVoMap = regionWhVoList.stream().filter(Objects::nonNull).collect(Collectors.toMap(RemoteRegionWhVo::getId, Function.identity(), (v1, v2) -> v1));
        //循环
        records.forEach(record -> {
            RemoteSupplierVo remoteSupplierVo = supplierVoMap.get(record.getSupplierId());
            RemoteRegionWhVo remoteRegionWhVo = regionWhVoMap.get(record.getRegionWhId());
            if(ObjectUtil.isNotEmpty(remoteSupplierVo)){
                record.setMerchantCode(ObjectUtil.isNotEmpty(remoteSupplierVo.getAlias()) ? remoteSupplierVo.getAlias() : remoteSupplierVo.getSimpleCode());
                record.setSupplierName(remoteSupplierVo.getName());
            }
            if (ObjectUtil.isNotEmpty(remoteRegionWhVo)) {
                record.setRegionWhName(remoteRegionWhVo.getRegionWhName());
            }
            record.setProvideInvoiceName(SupplierProvideInvoiceEnum.getDescByCode(record.getProvideInvoice()));
            // 开票订单区间
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
            String orderDateRange = formatter.format(record.getOrderTimeStart()) + "~" + formatter.format(record.getOrderTimeEnd());
            record.setOrderDateRange(orderDateRange);
        });
    }

    /**
     * 查询待开发票详情
     */
    @Override
    public InvoiceQueryVo getWaitInvoiceDetail(InvoiceQueryBo bo) {
        if(ObjectUtil.isEmpty(bo.getInvoicePickBoList())){
            throw new ServiceException("请选择待开发票");
        }
        //查询待开发票
        List<InvoiceQueryVo> invoiceQueryVoList = invoiceMapper.queryWaitInvoice(bo);
        if(ObjectUtil.isEmpty(invoiceQueryVoList)){
            throw new ServiceException("未找到待开发票信息");
        }
        this.buildInvoiceQueryVo(invoiceQueryVoList);
        InvoiceQueryVo invoiceQueryVo = invoiceQueryVoList.get(0);
        //查询待开发票细项
        List<InvoiceItemVo> invoiceItemList = invoiceItemMapper.queryWaitInvoiceItem(bo);
        if(ObjectUtil.isEmpty(invoiceItemList)){
            return invoiceQueryVo;
        }
        this.buildInvoiceItemVo(invoiceItemList, invoiceQueryVo);
        return invoiceQueryVo;
    }

    /**
     * 组装发票细项
     * @param invoiceItemList 发票细项
     * @param invoiceQueryVo 发票
     */
    private void buildInvoiceItemVo(List<InvoiceItemVo> invoiceItemList, InvoiceQueryVo invoiceQueryVo) {
        if(ObjectUtil.isEmpty(invoiceItemList)){
            return;
        }
        List<InvoiceItemVo> invoiceItemVoList = BeanUtil.copyToList(invoiceItemList, InvoiceItemVo.class);
        List<Long> supplierSkuIdList = invoiceItemVoList.stream().map(InvoiceItemVo::getSupplierSkuId).distinct().toList();
        //查询组合名称
        Map<Long, String> skuProductName = remoteSupplierSkuService.getSkuProductName(supplierSkuIdList);
        invoiceItemVoList.stream().forEach(itemVo -> {
            itemVo.setSpuName(skuProductName.get(itemVo.getSupplierSkuId()));
        });
        //待开发票细项通过订单号分组
        Map<String, List<InvoiceItemVo>> invoiceItemMap = invoiceItemVoList.stream().collect(Collectors.groupingBy(InvoiceItemVo::getOrderCode));
        List<InvoiceGroupOrderVo> invoiceGroupOrderVoList = new ArrayList<>();
        invoiceItemMap.forEach((key,value)->{
            InvoiceGroupOrderVo invoiceGroupOrderVo = new InvoiceGroupOrderVo();
            invoiceGroupOrderVo.setOrderCode(key);
            invoiceGroupOrderVo.setInvoiceItemVoList(value);
            invoiceGroupOrderVoList.add(invoiceGroupOrderVo);
        });
        invoiceQueryVo.setInvoiceGroupOrderVoList(invoiceGroupOrderVoList);
    }

    /**
     * 申请开票
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void applyInvoice(InvoiceApplyBo bo) {
        //查询待开发票
        InvoiceQueryBo invoiceQueryBo = BeanUtil.copyProperties(bo, InvoiceQueryBo.class);
        List<InvoiceQueryVo> invoiceQueryVoList = invoiceMapper.queryWaitInvoice(invoiceQueryBo);
        if(ObjectUtil.isEmpty(invoiceQueryVoList)){
            throw new ServiceException("未找到待开发票信息");
        }
        //查询待开发票细项
        List<InvoiceItemVo> invoiceItemVoList = invoiceItemMapper.queryWaitInvoiceItem(invoiceQueryBo);
        if(ObjectUtil.isEmpty(invoiceItemVoList)){
            return;
        }
        //校验申请开票
        this.checkApplyInvoice(bo, invoiceItemVoList);
        //填充发票列表对象
        this.buildInvoiceQueryVo(invoiceQueryVoList);
        //新增发票
        List<Invoice> addInvoiceList = this.addInvoiceList(bo, invoiceQueryVoList);
        //新增发票细项
        this.addInvoiceItemList(invoiceItemVoList, addInvoiceList);
        //更新订单项开票标识
        List<Long> orderItemIdList = invoiceItemVoList.stream().map(InvoiceItemVo::getOrderItemId).toList();
        orderItemService.updateInvoiceSituation(orderItemIdList, OrderItemInvoiceSituationEnum.PRODUCT_INVOICE.getCode());
    }
    /**
     * 新增发票细项
     * @param invoiceItemVoList 发票细项
     * @param addInvoiceList 发票
     */
    private void addInvoiceItemList(List<InvoiceItemVo> invoiceItemVoList, List<Invoice> addInvoiceList) {
        Map<String, List<InvoiceItemVo>> invoiceItemMap = invoiceItemVoList.stream().collect(Collectors.groupingBy(e -> getInvoiceGroupKey(e.getRegionWhId(), e.getSupplierId())));
        //查询组合名称
        List<Long> supplierSkuIdList = invoiceItemVoList.stream().map(InvoiceItemVo::getSupplierSkuId).distinct().toList();
        Map<Long, String> skuProductName = remoteSupplierSkuService.getSkuProductName(supplierSkuIdList);
        //组装发票细项
        List<InvoiceItem> addInvoiceItemList = new ArrayList<>();
        addInvoiceList.forEach(invoice -> {
            List<InvoiceItemVo> invoiceItemVoList1 = invoiceItemMap.get(getInvoiceGroupKey(invoice.getRegionWhId(), invoice.getSupplierId()));
            invoiceItemVoList1.forEach(invoiceItemVo -> {
                InvoiceItem invoiceItem = new InvoiceItem();
                BeanUtil.copyProperties(invoiceItemVo, invoiceItem);
                invoiceItem.setInvoiceId(invoice.getId());
                invoiceItem.setSpuName(skuProductName.get(invoiceItemVo.getSupplierSkuId()));
                addInvoiceItemList.add(invoiceItem);
            });
        });
        invoiceItemMapper.insertBatch(addInvoiceItemList);
    }

    /**
     * 新增发票
     * @param bo 入参
     * @param invoiceQueryVoList 待开发票列表
     * @return
     */
    private List<Invoice> addInvoiceList(InvoiceApplyBo bo, List<InvoiceQueryVo> invoiceQueryVoList) {
        //查询抬头
        InvoiceTitleVo invoiceTitleVo = invoiceTitleService.querySelfById(bo.getInvoiceTitleId());
        if(ObjectUtil.isEmpty(invoiceTitleVo)){
            throw new ServiceException("发票抬头不存在" + bo.getInvoiceTitleId());
        }
        Date date = new Date();
        //组装
        List<Invoice> addInvoiceList = invoiceQueryVoList.stream().map(invoiceQueryVo -> {
            Invoice addInvoice = new Invoice();
            addInvoice.setInvoiceNumber(CustomNoUtil.getInvoiceNo());
            addInvoice.setRegionWhId(invoiceQueryVo.getRegionWhId());
            addInvoice.setSupplierId(invoiceQueryVo.getSupplierId());
            addInvoice.setInvoiceTitleId(bo.getInvoiceTitleId());
            addInvoice.setCustomerId(bo.getCustomerId());
            addInvoice.setInvoiceAmount(invoiceQueryVo.getInvoiceAmount());
            addInvoice.setSpuGrossWeightTotal(invoiceQueryVo.getSpuGrossWeightTotal());
            addInvoice.setSpuNetWeightTotal(invoiceQueryVo.getSpuNetWeightTotal());
            addInvoice.setPlatformServiceAmountTotal(invoiceQueryVo.getPlatformServiceAmountTotal());
            addInvoice.setPlatformFreightAmountTotal(invoiceQueryVo.getPlatformFreightAmountTotal());
            addInvoice.setBaseFreightAmountTotal(invoiceQueryVo.getBaseFreightAmountTotal());
            addInvoice.setInvoiceType(bo.getInvoiceType());
            addInvoice.setTitleType(bo.getTitleType());
            addInvoice.setSupplierNameSnapshot(invoiceQueryVo.getSupplierName());
            addInvoice.setInvoiceTitleSnapshot(invoiceTitleVo.getTitleName());
            addInvoice.setTaxNumberSnapshot(invoiceTitleVo.getTaxNumber());
            addInvoice.setAddressSnapshot(invoiceTitleVo.getAddress());
            addInvoice.setBankAccountSnapshot(invoiceTitleVo.getBankAccount());
            addInvoice.setBankNameSnapshot(invoiceTitleVo.getBankName());
            addInvoice.setPhoneSnapshot(invoiceTitleVo.getPhone());
            addInvoice.setEmailAddressSnapshot(bo.getEmailAddress());
            addInvoice.setContactPhone(bo.getContactPhone());
            addInvoice.setRegion(bo.getRegion());
            addInvoice.setDetailedAddress(bo.getDetailedAddress());
            addInvoice.setRecipientName(bo.getRecipientName());
            addInvoice.setLatitude(bo.getLatitude());
            addInvoice.setLongitude(bo.getLongitude());
            addInvoice.setApplyDate(date);
            addInvoice.setMerchantCode(invoiceQueryVo.getMerchantCode());
            addInvoice.setTotalItems(invoiceQueryVo.getTotalItems());
            addInvoice.setOrderDateRange(invoiceQueryVo.getOrderDateRange());
            return addInvoice;
        }).collect(Collectors.toList());
        baseMapper.insertBatch(addInvoiceList);
        return addInvoiceList;
    }

    /**
     * 组装发票唯一标识
     */
    private String getInvoiceGroupKey(Long regionWhId, Long supplierId) {
        return regionWhId + "-" + supplierId;
    }

    /**
     * 开票前校验
     * @param bo
     * @param invoiceItemList
     */
    private void checkApplyInvoice(InvoiceApplyBo bo, List<InvoiceItemVo> invoiceItemList) {
        //校验想开的发票的合法性
        if(InvoiceTypeEnum.SPECIAL.getCode().equals(bo.getInvoiceType())){
            Optional<InvoiceItemVo> any = invoiceItemList.stream().filter(invoiceItemVo -> !SupplierProvideInvoiceEnum.GENERAL_AND_SPECIAL.getCode().equals(invoiceItemVo.getProvideInvoice())).findAny();
            if(any.isPresent()){
                throw new ServiceException("不支持开专用发票");
            }
        }
    }

    /**
     * 勾选待开发票的概况
     */
    @Override
    public InvoiceOverviewVo queryInvoiceOverview(InvoiceQueryBo bo) {
        return baseMapper.queryInvoiceOverview(bo);
    }

    /**
     * 查询勾选发票的可开发票类型
     */
    @Override
    public InvoiceOverviewVo queryProvideInvoice(InvoiceQueryBo bo) {
        return baseMapper.queryProvideInvoice(bo);
    }

    /**
     * 供应商编辑发票
     */
    @Override
    public void editInvoice(editInvoiceBo bo) {
        Invoice invoice = baseMapper.selectById(bo.getId());
        if(ObjectUtil.isEmpty(invoice)){
            throw new ServiceException("发票不存在" + bo.getId());
        }
        //校验
        this.checkEditInvoice(bo);
        //更新发票主表
        LambdaUpdateWrapper<Invoice> qw = Wrappers.lambdaUpdate();
        qw.eq(Invoice::getId, invoice.getId())
            .set(Invoice::getInvoiceFormat, bo.getInvoiceFormat())
            .set(Invoice::getExpressNumber, bo.getExpressNumber())
            .set(Invoice::getFileUrls, ObjectUtil.isEmpty(bo) ? JSON.toJSONString(Collections.emptyList()) : JSON.toJSONString(bo.getInvoiceFileBoList()))
            .set(Invoice::getIssueDate, new Date())
            .set(Invoice::getStatus, InvoiceStatusEnum.UPLOADED.getCode());
        baseMapper.update(qw);
        //更新发票细项
        this.updateInvoiceItem(bo);
    }

    /**
     * 更新发票明细表
     * @param bo
     */
    private void updateInvoiceItem(editInvoiceBo bo) {
        //更新发票明细表
        LambdaQueryWrapper<InvoiceItem> lqw = Wrappers.lambdaQuery();
        lqw.eq(InvoiceItem::getInvoiceId, bo.getId())
            .select(InvoiceItem::getId, InvoiceItem::getSkuId);
        List<InvoiceItem> invoiceItemList = invoiceItemMapper.selectList(lqw);
        if(ObjectUtil.isEmpty(invoiceItemList)){
            return;
        }
        //查询最新的免税水果的数据
        List<FreeTaxFruitVo> freeTaxFruitVoList = this.queryFreeTaxFruit(bo.getId());
        Map<String, FreeTaxFruitVo> freeTaxFruitVoMap = freeTaxFruitVoList.stream().collect(Collectors.toMap(e -> e.getSkuId() + "-" + e.getCategoryIdLevel2(), e -> e, (e1, e2) -> e1));
        //传进来的免税水果数据
        Map<String, FreeTaxFruitBo> freeTaxFruitBoMap = bo.getFreeTaxFruitBoList().stream().collect(Collectors.toMap(e -> e.getSkuId() + "-" + e.getCategoryIdLevel2(), e -> e, (e1, e2) -> e1));
        invoiceItemList.forEach(invoiceItem -> {
            FreeTaxFruitBo freeTaxFruitBo = freeTaxFruitBoMap.get(invoiceItem.getSkuId() + "-" + invoiceItem.getCategoryIdLevel2());
            if(ObjectUtil.isEmpty(freeTaxFruitBo) || ObjectUtil.isEmpty(freeTaxFruitBo.getTaxFreeWeight()) || ObjectUtil.isEmpty(freeTaxFruitBo.getTaxFreeAmount())){
                return;
            }
            //校验传进来的免税重量是否超出剩余的重量
            FreeTaxFruitVo freeTaxFruitVo = freeTaxFruitVoMap.get(invoiceItem.getSkuId() + "-" + invoiceItem.getCategoryIdLevel2());
            if(ObjectUtil.isEmpty(freeTaxFruitVo)){
                throw new ServiceException(String.format("商品（%s）未设置免税额度或已用完", invoiceItem.getSpuName()));
            }
            //传进来的大于剩余的，提示不足
            if(freeTaxFruitBo.getTaxFreeWeight().compareTo(freeTaxFruitVo.getRemainTaxFreeWeight()) > 0){
                throw new ServiceException(String.format("商品（%s）剩余免税重量不足，仅剩%s斤，请调整", invoiceItem.getSpuName(), freeTaxFruitVo.getRemainTaxFreeWeight()));
            }
            //传进来的小于剩余的，但是大于商品重量总和
            if(freeTaxFruitBo.getTaxFreeWeight().compareTo(freeTaxFruitVo.getRemainTaxFreeWeight()) < 0
                && freeTaxFruitBo.getTaxFreeWeight().compareTo(freeTaxFruitVo.getTaxFreeWeight()) > 0){
                throw new ServiceException(String.format("商品（%s）免税重量超出商品重量总和%s斤，请调整", invoiceItem.getSpuName(), freeTaxFruitVo.getTaxFreeWeight()));
            }
            invoiceItem.setTaxFreeAmount(freeTaxFruitBo.getTaxFreeAmount());
            invoiceItem.setTaxFreeWeight(freeTaxFruitBo.getTaxFreeWeight());
            invoiceItem.setRemainTaxFreeWeight(freeTaxFruitVo.getRemainTaxFreeWeight());
        });
        invoiceItemMapper.updateBatchById(invoiceItemList);
        //TODO 扣减免税重量
    }

    /**
     * 校验编辑发票
     * @param bo
     */
    private void checkEditInvoice(editInvoiceBo bo) {
        //校验发票格式
        if(InvoiceFormatEnum.PAPER.getCode().equals(bo.getInvoiceFormat())){
            if (ObjectUtil.isEmpty(bo.getExpressNumber())) {
                throw new ServiceException("纸质发票快递单号不能为空");
            }
        }
    }

    /**
     * 供应商上传发票
     */
    @Override
    public void uploadInvoice(editInvoiceBo bo) {
        Invoice invoice = baseMapper.selectById(bo.getId());
        if(ObjectUtil.isEmpty(invoice)){
            throw new ServiceException("发票不存在" + bo.getId());
        }
        invoice.setFileUrls(ObjectUtil.isEmpty(bo) ? JSON.toJSONString(Collections.emptyList()) : JSON.toJSONString(bo.getInvoiceFileBoList()));
        baseMapper.updateById(invoice);
    }

    /**
     * 审核发票
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditInvoice(InvoiceAuditBo bo) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        // 查询发票信息
        Invoice invoice = baseMapper.selectById(bo.getId());
        if (ObjectUtil.isEmpty(invoice)) {
            throw new ServiceException("发票不存在");
        }

        // 更新发票状态和审核时间
        String invoiceStatus = InvoiceAuditResultEnum.APPROVED.getCode().equals(bo.getAuditResult()) ? InvoiceStatusEnum.AUDITED_APPROVED.getCode() : InvoiceStatusEnum.AUDITED_REJECTED.getCode();
        LambdaUpdateWrapper<Invoice> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(Invoice::getId, bo.getId())
            .set(Invoice::getStatus, invoiceStatus)
            .set(Invoice::getAuditDate, new Date());
        baseMapper.update(updateWrapper);

        // 组装并插入审核日志
        InvoiceAuditLog auditLog = new InvoiceAuditLog();
        auditLog.setInvoiceId(bo.getId());
        auditLog.setAuditorId(loginUser.getUserId());
        auditLog.setAuditResult(bo.getAuditResult());
        auditLog.setRemarks(bo.getRemarks());
        auditLog.setAuditDate(new Date());
        invoiceAuditLogMapper.insert(auditLog);
    }

    /**
     * 查询详情的免税水果
     * @param  id 发票ID
     */
    @Override
    public List<FreeTaxFruitVo> queryFreeTaxFruit(Long id) {
        return invoiceItemMapper.queryFreeTaxFruit(id);
    }
}
