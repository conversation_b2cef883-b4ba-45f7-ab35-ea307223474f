package cn.xianlink.order.domain;

import cn.xianlink.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 发票审核日志对象 inv_invoice_audit_log
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("inv_invoice_audit_log")
public class InvoiceAuditLog extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 日志ID (主键)
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 发票ID[外键]
     */
    private Long invoiceId;

    /**
     * 审核人ID[外键]
     */
    private Long auditorId;

    /**
     * 审核结果 (approved通过, rejected拒绝)
     */
    private String auditResult;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 审核日期
     */
    private Date auditDate;

    /**
     * 删除标志（0代表存在 存入这条数据的主键代表删除）
     */
    @TableLogic
    private Long delFlag;

    /**
     * 创建用户代码
     */
    private String createCode;

    /**
     * 创建用户名称
     */
    private String createName;

    /**
     * 修改用户代码
     */
    private String updateCode;

    /**
     * 修改用户名称
     */
    private String updateName;


}
