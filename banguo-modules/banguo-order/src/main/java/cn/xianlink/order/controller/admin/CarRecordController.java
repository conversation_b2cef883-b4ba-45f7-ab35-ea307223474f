package cn.xianlink.order.controller.admin;

import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.domain.bo.CarRecordQueryBo;
import cn.xianlink.order.domain.vo.CarRecordVo;
import cn.xianlink.order.service.ICarRecordService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 车辆信息
 *
 * <AUTHOR>
 * @date 2024-06-11
 * @folder 般果管理中心/车辆信息
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/order/admin/carRecord")
public class CarRecordController extends BaseController {

    private transient final ICarRecordService carRecordService;

    /**
     * 查询车辆信息列表
     */
    @PostMapping("/page")
    @ApiOperation("分页查询")
    public R<TableDataInfo<CarRecordVo>> page(@RequestBody CarRecordQueryBo bo) {
        return R.ok(carRecordService.queryPageList(bo));
    }
}
