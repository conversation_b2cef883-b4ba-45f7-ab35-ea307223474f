package cn.xianlink.order.service;

import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.order.domain.RwEntruckGoods;
import cn.xianlink.order.domain.RwEntruckRecord;
import cn.xianlink.order.domain.delivery.bo.SupWaitDeliveryQueryBo;
import cn.xianlink.order.domain.entruck.bo.RwEntruckGoodsAddBo;
import cn.xianlink.order.domain.entruck.bo.RwEntruckGoodsOrderQueryBo;
import cn.xianlink.order.domain.entruck.bo.RwWaitEntruckQueryBo;
import cn.xianlink.order.domain.entruck.vo.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 总仓装车记录明细Service接口
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
public interface IRwEntruckGoodsService {

    /**
     * 查询装车记录明细列表
     */
    List<RwEntruckGoodsVo> queryListByRecordIds(List<Long> recordIds);
    /**
     * 查询装车记录明细列表
     */
    List<RwEntruckGoodsVo> queryListByDeliveryId(Long deliveryId);
    List<RwEntruckGoodsVo> queryListByIds(List<Long> ids);

    /**
     * 查询送货单明细汇总
     *
     * @param bo
     * @return 供应商：商品id、送货中、已装车、差异不补送
     */
    List<RwEntruckGoodsSumVo> customSkuSumListBySupplierId(SupWaitDeliveryQueryBo bo);
    /**
     * 查询送货单明细汇总
     *
     * @param bo
     * @return 物流id、送货中、已装车、差异不补送
     */
    List<RwEntruckGoodsSumVo> customLogisticsSumListBySupplierId(SupWaitDeliveryQueryBo bo);
    /**
     * 查询供应商送货数量汇总
     *
     * @param bo
     * @return 供应商id、送货中、已装车、差异不补送
     */
    List<RwEntruckGoodsSumVo> customSupplierSumListByRegionWhId(SupWaitDeliveryQueryBo bo);
    List<RwEntruckGoodsSumVo> customSupplierSumListByLogisticsId(SupWaitDeliveryQueryBo bo);

    /**
     * 查询送货单明细汇总
     *
     * @param deliveryIds
     * @return 送货单id，已装车
     */
    List<RwEntruckGoodsSumVo> customEntruckSumListByDeliveryId(List<Long> deliveryIds);

    /**
     * 查询送货单明细汇总
     *
     * @return  商品信息，sum(装车数)
     */
    List<RwEntruckGoodsCountVo> customSumListByDeliveryId(Long deliveryId);

    /**
     * 查询装车记录商品明细汇总
     *
     * @return  商品信息，供应商信息，sum(装车数)
     */
    List<RwEntruckGoodsCountVo> customEntruckGoodsSumListByRegionWhId(RwWaitEntruckQueryBo bo);

    /**
     * 查询装车记录商品明细汇总，按物流线汇总
     *
     * @return  物流线，sum(装车数)
     */
    List<RwEntruckRecordGoodsSumVo> customEntruckGoodsLogisticsSumListBySkuId(RwWaitEntruckQueryBo bo);
    /**
     * 查询-拣货单列表
     */
    TableDataInfo<RwEntruckRecordGoodsSumVo> pagePicking(RwWaitEntruckQueryBo bo);
    List<RwEntruckRecordGoodsSumVo> pickingList(List<String> pickingNos);
    /**
     * 查询-拣货单详情
     */
    RwEntruckRecordGoodsSumVo pickingInfo(String pickingNo, Boolean isLoadName);
    /**
     * 查询商品明细，条件装车数量不等于应装车数量
     *
     * @return
     */
    List<RwEntruckGoodsVo> customWaitEntruckGoodsListBySkuId(RwWaitEntruckQueryBo bo);
    /**
     * 查询商品明细，条件装车数量不等于应装车数量
     *
     * @return
     */
    List<RwEntruckGoodsCountVo> customWaitEntruckGoodsLogisticsList(RwWaitEntruckQueryBo bo);

    /**
     * 批量创建明细
     */
    Boolean insertBatch(RwEntruckRecord record, List<RwEntruckGoodsAddBo> bos);

    /**
     * 修改装车数据
     */
    Boolean updateEntruck(List<RwEntruckGoods> entityList);
    /**
     * 撤销-装车
     */
    int revokeEntruck(Long recordId);

    /**
     * 查询装车差异状态
     */
    Boolean isExistEntruckDiffStatus(Long deliveryId, Integer diffStatus);

    /**
     * 查询装车差异状态
     */
    List<Long>  getExistEntruckDiffStatus(List<Long> deliveryIds, Integer diffStatus);

    /**
     * 查询送货单是否存操作装车的记录
     */
    Boolean isExistEntruckQuantity(Long deliveryIds);

    /**
     * 查询待装车商品明细
     *
     * @param recordId
     */
    Boolean isExistWaitEntruckGoods(Long recordId);

    /**
     * 查询待装车商品明细
     *
     * @param recordIds
     */
    List<Long> getExistWaitEntruckGoodsMap(List<Long> recordIds);

    Integer deleteByIds(List<Long> ids);
    Integer deleteByRecordIdIds(List<Long> recordIds);

    /**
     * 待送货商品列表
     */
    List<RwWaitDeliveryGoodsVo> rwWaitDeliveryList(SupWaitDeliveryQueryBo bo);
    /**
     * 送货单、拣货单商品标签打印查询
     */
    List<RwEntruckGoodsOrderSimplifyVo> getDeliveryGoodsOrderLabelSimplifyList(RwEntruckGoodsOrderQueryBo bo);
}
