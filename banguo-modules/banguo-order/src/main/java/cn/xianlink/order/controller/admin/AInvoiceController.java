package cn.xianlink.order.controller.admin;


import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.domain.bo.InvoiceAuditBo;
import cn.xianlink.order.service.IInvoiceService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 发票
 * 前端访问路由地址为:/order/admin/invoice
 *
 * <AUTHOR>
 * @date 2025-08-02
 * @folder 般果管理中心/订单/发票
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/order/admin/invoice")
public class AInvoiceController extends BaseController {

    private final IInvoiceService invoiceService;

    /**
     * 审核发票
     */
    @RequestMapping("/audit")
    public R<Void> auditInvoice(@Validated @RequestBody InvoiceAuditBo bo) {
        invoiceService.auditInvoice(bo);
        return R.ok();
    }
}
