package cn.xianlink.order.controller.city;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.basic.api.RemoteCityWhPlaceService;
import cn.xianlink.basic.api.RemoteRegionLogisticsService;
import cn.xianlink.basic.api.domain.vo.RemoteRegionWhVo;
import cn.xianlink.common.api.enums.order.AccountTypeStatusEnum;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.order.api.constant.MoreGoodsStatusEnum;
import cn.xianlink.order.domain.bo.moreGoods.AddMoreGoodsBO;
import cn.xianlink.order.domain.bo.moreGoods.MoreGoodsPageBO;
import cn.xianlink.order.domain.vo.common.CommonStatusCountVO;
import cn.xianlink.order.domain.vo.moreGoods.MoreGoodsDetailVO;
import cn.xianlink.order.domain.vo.moreGoods.MoreGoodsPageVO;
import cn.xianlink.order.service.IMoreGoodsService;
import cn.xianlink.system.api.model.LoginUser;
import io.swagger.annotations.ApiOperation;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import cn.xianlink.common.web.core.BaseController;

import java.util.List;


/**
 * 城市仓小程序-多货
 *
 * <AUTHOR>
 * @date 2024-05-25
 * @folder 城市仓端(小程序)/订单/多货单
 */
@Validated
@RequiredArgsConstructor
@RestController("cityMoreGoodsController")
@RequestMapping("/order/city/moreGoods")
@CustomLog
public class MoreGoodsController extends BaseController {

    private final IMoreGoodsService iMoreGoodsService;

    @DubboReference
    private final transient RemoteRegionLogisticsService remoteRegionLogisticsService;

    @DubboReference
    private final transient RemoteCityWhPlaceService remoteCityWhPlaceService;

    @PostMapping("/moreGoodsPage")
    @ApiOperation(value = "多货单列表")
    public R<TableDataInfo<MoreGoodsDetailVO>> moreGoodsPage(@RequestBody MoreGoodsPageBO bo){
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (ObjectUtil.isEmpty(loginUser) || ObjectUtil.isEmpty(loginUser.getRelationId())) {
            throw new ServiceException("用户未登录");
        }
        log.keyword("moreGoodsPage").info("用户信息：{}", loginUser);
        bo.setCityWhId(loginUser.getRelationId());
        if (CollectionUtil.isNotEmpty(bo.getPlaceIdList())) {
            bo.setLogisticsIds(remoteRegionLogisticsService.queryIdByCityUserFiled(LoginHelper.getLoginUser().getUserId(), bo.getPlaceIdList()));
        }else if (CollectionUtil.isEmpty(bo.getPlaceIdList()) && CollectionUtil.isEmpty(bo.getPlaceIdLevel2List())){
            bo.setLogisticsIds(remoteRegionLogisticsService.queryIdByCityUser(LoginHelper.getLoginUser().getUserId()));
            bo.setPlaceIdLevel2List(remoteCityWhPlaceService.getPlaceIdsByUser(LoginHelper.getLoginUser().getUserId()));
        }
        return R.ok(iMoreGoodsService.moreGoodsPage(bo));
    }

    @GetMapping("/getMoreGoodsById/{id}")
    @ApiOperation(value = "多货单详情")
    public R<MoreGoodsDetailVO> getMoreGoodsById(@PathVariable("id") Long id){
        return R.ok(iMoreGoodsService.getMoreGoodsById(id));
    }

    @GetMapping("/getMoreGoodsByCode")
    @ApiOperation(value = "多货单详情，根据code查询")
    public R<MoreGoodsDetailVO> getMoreGoodsByCode(@RequestParam("code") String code){
        return R.ok(iMoreGoodsService.getMoreGoodsByCode(code));
    }

    @PostMapping("/addMoreGoods")
    @RepeatSubmit()
    @ApiOperation(value = "新建多货单")
    public R<Boolean> addMoreGoods(@RequestBody AddMoreGoodsBO bo){
        bo.setSourceType(AccountTypeStatusEnum.CITY.getCode());
        return R.ok(iMoreGoodsService.addMoreGoods(bo));
    }

    @PostMapping("/getByCityWhId")
    @ApiOperation(value = "根据城市仓id判断是否属地仓")
    public R<RemoteRegionWhVo> getByCityWhId(@RequestParam("cityWhId") Long cityWhId){
        return R.ok(iMoreGoodsService.getByCityWhId(cityWhId));
    }

    @GetMapping("/cancelMoreGoods/{id}")
    @RepeatSubmit()
    @ApiOperation(value = "撤销多货单")
    public R<Boolean> cancelMoreGoods(@PathVariable("id") Long id){
        return R.ok(iMoreGoodsService.cancelMoreGoods(id));
    }

    @GetMapping("/syncMoreGoodsSaleDate")
    @ApiOperation(value = "同步多货单的销售日期字段")
    public R<Void> syncMoreGoodsSaleDate(@RequestParam(value = "code", required = false) String code){
        iMoreGoodsService.syncMoreGoodsSaleDate(code);
        return R.ok();
    }

    @GetMapping("/getMoreGoodsCount")
    @ApiOperation(value = "获取多货单各状态数量")
    public R<CommonStatusCountVO> getMoreGoodsCount(@RequestParam(value = "regionWhId", required = false) Long regionWhId){
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (ObjectUtil.isEmpty(loginUser) || ObjectUtil.isEmpty(loginUser.getRelationId())) {
            throw new ServiceException("用户未登录");
        }
        AddMoreGoodsBO bo = new AddMoreGoodsBO();
        bo.setCityWhId(loginUser.getRelationId());
        bo.setStatus(MoreGoodsStatusEnum.UNCONFIRMED.getCode());
        return R.ok(iMoreGoodsService.getMoreGoodsCount(bo));
    }
}
