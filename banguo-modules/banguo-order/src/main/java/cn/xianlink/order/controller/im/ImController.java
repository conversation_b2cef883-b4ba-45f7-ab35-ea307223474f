package cn.xianlink.order.controller.im;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.order.domain.im.bo.ImBo;
import cn.xianlink.order.domain.im.vo.ImVo;
import cn.xianlink.order.service.im.IImService;
import cn.xianlink.system.api.RemoteImAfterSalesService;
import cn.xianlink.system.api.domain.vo.RemoteImAfterSalesVo;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/order/im")
@Api(tags = "IM即时通信")
public class ImController {

    private final IImService imService;

    /**
     * 创建IM聊天入口
     * @param req
     * @return
     */
    @PostMapping("/create")
    public R<ImVo> createGroupChat(@Validated @RequestBody ImBo req) {
        return R.ok(imService.create(req));
    }

    @PostMapping("/cleanupExpiredGroups")
    public R<Boolean> cleanupExpiredGroups() {
        return R.ok(imService.cleanupExpiredGroups());
    }
}