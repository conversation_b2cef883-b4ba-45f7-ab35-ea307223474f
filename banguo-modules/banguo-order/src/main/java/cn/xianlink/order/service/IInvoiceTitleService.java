package cn.xianlink.order.service;

import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.order.domain.bo.InvoiceTitleBo;
import cn.xianlink.order.domain.vo.InvoiceTitleVo;

import java.util.Collection;
import java.util.List;

/**
 * 发票抬头Service接口
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface IInvoiceTitleService {

    /**
     * 查询发票抬头
     */
    InvoiceTitleVo queryById(Long id);

    /**
     * 查询用户自己的发票抬头
     */
    InvoiceTitleVo querySelfById(Long id);

    /**
     * 查询发票抬头列表
     */
    TableDataInfo<InvoiceTitleVo> queryPageList(InvoiceTitleBo bo);

    /**
     * 查询发票抬头列表
     */
    List<InvoiceTitleVo> queryList(InvoiceTitleBo bo);

    /**
     * 新增发票抬头
     */
    Boolean insertByBo(InvoiceTitleBo bo);

    /**
     * 修改发票抬头
     */
    Boolean updateByBo(InvoiceTitleBo bo);

    /**
     * 校验并批量删除发票抬头信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
