package cn.xianlink.order.controller.support;

import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.domain.order.vo.OriginOrderDetailVo;
import cn.xianlink.order.domain.order.vo.OriginOrderItemDetailVo;
import cn.xianlink.order.service.support.ISupportOrderService;
import cn.xianlink.product.api.domain.vo.RemoteSkuVo;
import cn.xianlink.system.api.model.LoginUser;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 订单 - 客服专用
 *
 * <AUTHOR>
 * @date 2024-07-31
 * @folder 采集平台(小程序)/订单/客服
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/order/support/order")
public class SupportOrderController extends BaseController {

    private final ISupportOrderService supportOrderService;

    /**
     * 查询最近N天的订单列表
     *
     * @return 订单列表
     */
    @GetMapping("/recentOrderList")
    @Operation(summary = "查询最近N天的订单列表")
    public R<List<OriginOrderDetailVo>> getRecentOrderList() {

        LoginUser loginUser = LoginHelper.getLoginUser();
        Long customerId = loginUser.getRelationId();

        Integer days = 7;

        try {
            // 调用服务方法
            List<OriginOrderDetailVo> orders = supportOrderService.getRecentOrderList(customerId, days);

            // 返回结果统计
            int totalOrders = orders.size();
            int totalItems = orders.stream().mapToInt(order -> order.getOrderItemList().size()).sum();

            return R.ok(String.format("查询成功，共找到 %d 个订单，%d 个订单项", totalOrders, totalItems), orders);

        } catch (ServiceException e) {
            return R.fail("业务异常: " + e.getMessage());
        } catch (Exception e) {
            log.error("测试查询最近订单失败", e);
            return R.fail("系统异常: " + e.getMessage());
        }
    }

    /**
     * 查询最近N天的订单项列表（包含sku信息）
     *
     * @param keyword 商品名称关键词，用于模糊搜索spu_name字段
     * @return 订单项列表
     */
    @GetMapping("/recentOrderItemList")
    @Operation(summary = "查询最近N天的订单项列表（包含sku信息）")
    public R<List<OriginOrderItemDetailVo>> getRecentOrderItemList(@RequestParam(required = false) String keyword, @RequestParam(required = false) Long testCustomerId) {

        LoginUser loginUser = LoginHelper.getLoginUser();
        Long customerId = 0L;

        // 优先使用测试客户ID
        if (ObjectUtil.isNotEmpty(testCustomerId)) {
            customerId = testCustomerId;
        } else {
            // 从登录用户获取客户ID，处理空指针异常
            if (loginUser != null) {
                customerId = loginUser.getRelationId();
            }
        }

        // 验证客户ID是否有效
        if (customerId == null) {
            return R.fail("客户ID不能为空，请登录后重试或提供testCustomerId参数");
        }


        Integer days = 7;

        try {
            // 调用服务方法
            List<OriginOrderItemDetailVo> orderItems = supportOrderService.getRecentOrderItemList(customerId, days, keyword);

            // 返回结果统计
            int totalItems = orderItems.size();
            long itemsWithSku = orderItems.stream().filter(item -> item.getSkuInfo() != null).count();

            // 记录详细信息到日志
            if (keyword != null && !keyword.trim().isEmpty()) {
                log.info("查询最近订单项列表完成，客户ID: {}, 关键词: '{}', 共找到 {} 个订单项，其中 {} 个包含sku信息",
                        customerId, keyword, totalItems, itemsWithSku);
            } else {
                log.info("查询最近订单项列表完成，客户ID: {}, 共找到 {} 个订单项，其中 {} 个包含sku信息",
                        customerId, totalItems, itemsWithSku);
            }

            return R.ok("查询成功", orderItems);

        } catch (ServiceException e) {
            return R.fail("业务异常: " + e.getMessage());
        } catch (Exception e) {
            log.error("查询最近订单项失败", e);
            return R.fail("系统异常: " + e.getMessage());
        }
    }

    /**
     * 按购物车+订单商品维度去重展示商品
     *
     * @param regionWhId     总仓 ID
     * @param keyword        商品名称关键词，用于模糊搜索
     * @param testCustomerId 测试客户ID
     * @return RemoteSkuVo列表
     */
    @GetMapping("/recentProductList")
    @Operation(summary = "按购物车+订单商品维度去重展示商品")
    public R<List<RemoteSkuVo>> getRecentProductList(@RequestParam(required = false) Long regionWhId, @RequestParam(required = false) String keyword, @RequestParam(required = false) Long testCustomerId) {

        LoginUser loginUser = LoginHelper.getLoginUser();
        Long customerId = 0L;

        // 优先使用测试客户ID
        if (ObjectUtil.isNotEmpty(testCustomerId)) {
            customerId = testCustomerId;
        } else {
            // 从登录用户获取客户ID，处理空指针异常
            if (loginUser != null) {
                customerId = loginUser.getRelationId();
            }
        }

        // 验证客户ID是否有效
        if (customerId == null) {
            return R.fail("客户ID不能为空，请登录后重试或提供testCustomerId参数");
        }

        // 验证regionWhId是否有效
        if (regionWhId == null) {
            regionWhId = 0L;
        }

        Integer days = 7;

        try {
            // 调用服务方法
            List<RemoteSkuVo> skuList = supportOrderService.getRecentProductList(regionWhId,customerId, days, keyword);

            // 返回结果统计
            int totalSkus = skuList.size();

            // 记录详细信息到日志
            if (keyword != null && !keyword.trim().isEmpty()) {
                log.info("查询购物车+订单商品列表完成，客户ID: {}, 关键词: '{}', 共找到 {} 个去重商品",
                        customerId, keyword, totalSkus);
            } else {
                log.info("查询购物车+订单商品列表完成，客户ID: {}, 共找到 {} 个去重商品",
                        customerId, totalSkus);
            }

            return R.ok("查询成功", skuList);

        } catch (ServiceException e) {
            log.error("查询购物车+订单商品列表失败，客户ID: {}, 关键词: '{}', 错误信息: {}", customerId, keyword, e.getMessage());
            return R.fail("业务异常: " + e.getMessage());
        } catch (Exception e) {
            log.error("查询购物车+订单商品列表失败，客户ID: {}, 关键词: '{}'", customerId, keyword, e);
            return R.fail("系统异常: " + e.getMessage());
        }
    }


}
