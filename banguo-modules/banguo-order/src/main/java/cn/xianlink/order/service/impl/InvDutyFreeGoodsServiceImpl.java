package cn.xianlink.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.order.domain.InvDutyFreeGoods;
import cn.xianlink.order.domain.bo.InvDutyFreeGoodsBo;
import cn.xianlink.order.domain.vo.InvDutyFreeGoodsVo;
import cn.xianlink.order.enums.DutyFreeStatusEnum;
import cn.xianlink.order.mapper.InvDutyFreeGoodsMapper;
import cn.xianlink.order.service.IInvDutyFreeGoodsService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 免税商品Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-30
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class InvDutyFreeGoodsServiceImpl implements IInvDutyFreeGoodsService {

    private final InvDutyFreeGoodsMapper baseMapper;

    /**
     * 查询免税商品
     */
    @Override
    public InvDutyFreeGoodsVo queryById(Long id) {
        InvDutyFreeGoodsVo vo = baseMapper.selectVoById(id);
        if (vo != null) {
            enrichVoData(List.of(vo));
        }
        return vo;
    }

    /**
     * 查询免税商品列表
     */
    @Override
    public TableDataInfo<InvDutyFreeGoodsVo> queryPageList(InvDutyFreeGoodsBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<InvDutyFreeGoods> lqw = buildQueryWrapper(bo);
        Page<InvDutyFreeGoodsVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        
        // 补充计算字段
        enrichVoData(result.getRecords());
        
        return TableDataInfo.build(result);
    }

    /**
     * 查询免税商品列表
     */
    @Override
    public List<InvDutyFreeGoodsVo> queryList(InvDutyFreeGoodsBo bo) {
        LambdaQueryWrapper<InvDutyFreeGoods> lqw = buildQueryWrapper(bo);
        List<InvDutyFreeGoodsVo> list = baseMapper.selectVoList(lqw);
        
        // 补充计算字段
        enrichVoData(list);
        
        return list;
    }

    /**
     * 根据采购人员和分类查询有效的免税商品
     */
    @Override
    public List<InvDutyFreeGoodsVo> queryValidByPurchUserAndCategory(String purchUserCode, Long categoryId) {
        List<InvDutyFreeGoodsVo> list = baseMapper.selectValidByPurchUserAndCategory(
            purchUserCode, categoryId, LocalDate.now());
        enrichVoData(list);
        return list;
    }

    /**
     * 根据分类ID查询有效的免税商品
     */
    @Override
    public List<InvDutyFreeGoodsVo> queryValidByCategory(Long categoryId) {
        List<InvDutyFreeGoodsVo> list = baseMapper.selectValidByCategory(categoryId, LocalDate.now());
        enrichVoData(list);
        return list;
    }

    /**
     * 查询即将过期的免税商品
     */
    @Override
    public List<InvDutyFreeGoodsVo> queryExpiringSoon(int expireDays) {
        if (expireDays <= 0) {
            expireDays = 7; // 默认7天
        }
        List<InvDutyFreeGoodsVo> list = baseMapper.selectExpiringSoon(LocalDate.now(), expireDays);
        enrichVoData(list);
        return list;
    }

    /**
     * 计算免税重量
     */
    @Override
    public BigDecimal calculateDutyFreeWeight(String purchUserCode, Long categoryId, LocalDate targetDate) {
        List<InvDutyFreeGoodsVo> validGoods = baseMapper.selectValidByPurchUserAndCategory(
            purchUserCode, categoryId, targetDate);
        
        return validGoods.stream()
            .filter(goods -> DutyFreeStatusEnum.APPROVED.getCode().equals(goods.getStatus()))
            .map(InvDutyFreeGoodsVo::getWeight)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 检查是否在免税期内
     */
    @Override
    public boolean isInDutyFreePeriod(String purchUserCode, Long categoryId, LocalDate targetDate) {
        List<InvDutyFreeGoodsVo> validGoods = baseMapper.selectValidByPurchUserAndCategory(
            purchUserCode, categoryId, targetDate);
        
        return validGoods.stream()
            .anyMatch(goods -> DutyFreeStatusEnum.APPROVED.getCode().equals(goods.getStatus()));
    }

    private LambdaQueryWrapper<InvDutyFreeGoods> buildQueryWrapper(InvDutyFreeGoodsBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<InvDutyFreeGoods> lqw = Wrappers.lambdaQuery();
        lqw.ge(bo.getWeight() != null, InvDutyFreeGoods::getWeight, bo.getWeight());
        lqw.ge(bo.getFreeDateStart() != null, InvDutyFreeGoods::getFreeDateStart, bo.getFreeDateStart());
        lqw.le(bo.getFreeDateEnd() != null, InvDutyFreeGoods::getFreeDateEnd, bo.getFreeDateEnd());
        lqw.eq(StringUtils.isNotBlank(bo.getPurchUserCode()), InvDutyFreeGoods::getPurchUserCode, bo.getPurchUserCode());
        lqw.like(StringUtils.isNotBlank(bo.getPurchUserName()), InvDutyFreeGoods::getPurchUserName, bo.getPurchUserName());
        lqw.eq(bo.getCategoryId() != null, InvDutyFreeGoods::getCategoryId, bo.getCategoryId());
        lqw.like(StringUtils.isNotBlank(bo.getCategoryName()), InvDutyFreeGoods::getCategoryName, bo.getCategoryName());
        lqw.eq(bo.getStatus() != null, InvDutyFreeGoods::getStatus, bo.getStatus());
        lqw.orderByDesc(InvDutyFreeGoods::getCreateTime);
        return lqw;
    }

    /**
     * 补充VO数据
     */
    private void enrichVoData(List<InvDutyFreeGoodsVo> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        
        LocalDate now = LocalDate.now();
        list.forEach(vo -> {
            // 设置状态描述
            if (vo.getStatus() != null) {
                DutyFreeStatusEnum statusEnum = DutyFreeStatusEnum.getByCode(vo.getStatus());
                vo.setStatusDesc(statusEnum != null ? statusEnum.getDesc() : "未知状态");
            }
            
            // 计算免税天数
            if (vo.getFreeDateStart() != null && vo.getFreeDateEnd() != null) {
                long days = ChronoUnit.DAYS.between(vo.getFreeDateStart(), vo.getFreeDateEnd()) + 1;
                vo.setFreeDays(days);
            }
            
            // 判断是否有效
            if (vo.getFreeDateStart() != null && vo.getFreeDateEnd() != null) {
                boolean isValid = (now.isEqual(vo.getFreeDateStart()) || now.isAfter(vo.getFreeDateStart())) &&
                                 (now.isEqual(vo.getFreeDateEnd()) || now.isBefore(vo.getFreeDateEnd())) &&
                                 DutyFreeStatusEnum.APPROVED.getCode().equals(vo.getStatus());
                vo.setIsValid(isValid);
            } else {
                vo.setIsValid(false);
            }
        });
    }

    /**
     * 新增免税商品
     */
    @Override
    public Boolean insertByBo(InvDutyFreeGoodsBo bo) {
        InvDutyFreeGoods add = MapstructUtils.convert(bo, InvDutyFreeGoods.class);
        validEntityBeforeSave(add);
        
        // 设置默认状态为待审核
        add.setStatus(DutyFreeStatusEnum.PENDING.getCode());
        
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改免税商品
     */
    @Override
    public Boolean updateByBo(InvDutyFreeGoodsBo bo) {
        InvDutyFreeGoods update = MapstructUtils.convert(bo, InvDutyFreeGoods.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 审核免税商品
     */
    @Override
    public Boolean auditById(Long id, Integer status, String remark) {
        InvDutyFreeGoods entity = baseMapper.selectById(id);
        if (entity == null) {
            throw new ServiceException("免税商品不存在");
        }
        
        if (!DutyFreeStatusEnum.PENDING.getCode().equals(entity.getStatus())) {
            throw new ServiceException("只能审核待审核状态的免税商品");
        }
        
        if (!DutyFreeStatusEnum.APPROVED.getCode().equals(status) && 
            !DutyFreeStatusEnum.REJECTED.getCode().equals(status)) {
            throw new ServiceException("审核状态不正确");
        }
        
        entity.setStatus(status);
        if (StringUtils.isNotBlank(remark)) {
            entity.setRemark(remark);
        }
        
        return baseMapper.updateById(entity) > 0;
    }

    /**
     * 批量审核免税商品
     */
    @Override
    public Boolean batchAudit(Collection<Long> ids, Integer status, String remark) {
        if (CollUtil.isEmpty(ids)) {
            return false;
        }
        
        for (Long id : ids) {
            auditById(id, status, remark);
        }
        
        return true;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(InvDutyFreeGoods entity) {
        // 校验免税时间范围
        if (entity.getFreeDateStart() != null && entity.getFreeDateEnd() != null) {
            if (entity.getFreeDateEnd().isBefore(entity.getFreeDateStart())) {
                throw new ServiceException("免税结束时间不能早于开始时间");
            }
        }
        
        // 校验重复性（同一采购人员、同一分类、时间重叠）
        LambdaQueryWrapper<InvDutyFreeGoods> lqw = Wrappers.lambdaQuery();
        lqw.eq(InvDutyFreeGoods::getPurchUserCode, entity.getPurchUserCode());
        lqw.eq(InvDutyFreeGoods::getCategoryId, entity.getCategoryId());
        lqw.ne(entity.getId() != null, InvDutyFreeGoods::getId, entity.getId());
        
        // 检查时间重叠
        if (entity.getFreeDateStart() != null && entity.getFreeDateEnd() != null) {
            lqw.and(wrapper -> wrapper
                .le(InvDutyFreeGoods::getFreeDateStart, entity.getFreeDateEnd())
                .ge(InvDutyFreeGoods::getFreeDateEnd, entity.getFreeDateStart())
            );
            
            if (baseMapper.exists(lqw)) {
                throw new ServiceException("该采购人员在此分类下已存在时间重叠的免税商品");
            }
        }
    }

    /**
     * 批量删除免税商品
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验是否可以删除
            for (Long id : ids) {
                InvDutyFreeGoods entity = baseMapper.selectById(id);
                if (entity != null && DutyFreeStatusEnum.APPROVED.getCode().equals(entity.getStatus())) {
                    LocalDate now = LocalDate.now();
                    if (entity.getFreeDateStart() != null && entity.getFreeDateEnd() != null &&
                        (now.isEqual(entity.getFreeDateStart()) || now.isAfter(entity.getFreeDateStart())) &&
                        (now.isEqual(entity.getFreeDateEnd()) || now.isBefore(entity.getFreeDateEnd()))) {
                        throw new ServiceException("不能删除正在生效中的免税商品");
                    }
                }
            }
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

}
