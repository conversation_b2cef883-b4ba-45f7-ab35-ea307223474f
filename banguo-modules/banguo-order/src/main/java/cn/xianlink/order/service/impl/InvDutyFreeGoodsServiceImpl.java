package cn.xianlink.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.xianlink.basic.api.enums.OssBusinessTypeEnum;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.order.domain.InvDutyFreeGoods;
import cn.xianlink.order.domain.bo.InvDutyFreeGoodsBo;
import cn.xianlink.order.domain.vo.InvDutyFreeGoodsVo;
import cn.xianlink.order.enums.DutyFreeStatusEnum;
import cn.xianlink.order.mapper.InvDutyFreeGoodsMapper;
import cn.xianlink.order.service.IInvDutyFreeGoodsService;
import cn.xianlink.resource.api.RemoteFileService;
import cn.xianlink.resource.api.domain.RemoteOssBo;
import cn.xianlink.resource.api.domain.RemoteOssVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 免税商品Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-30
 */
@CustomLog
@RequiredArgsConstructor
@Service
public class InvDutyFreeGoodsServiceImpl implements IInvDutyFreeGoodsService {

    private final InvDutyFreeGoodsMapper baseMapper;

    @DubboReference
    private final RemoteFileService remoteFileService;

    /**
     * 查询免税商品
     */
    @Override
    public InvDutyFreeGoodsVo queryById(Long id) {
        InvDutyFreeGoodsVo vo = baseMapper.selectVoById(id);
        if (vo != null) {
            enrichVoData(List.of(vo));
            // 补充oss信息
            selectOss(vo);
        }
        return vo;
    }

    /**
     * 查询免税商品列表
     */
    @Override
    public TableDataInfo<InvDutyFreeGoodsVo> queryPageList(InvDutyFreeGoodsBo bo) {
        LambdaQueryWrapper<InvDutyFreeGoods> lqw = buildQueryWrapper(bo);
        Page<InvDutyFreeGoodsVo> result = baseMapper.selectVoPage(bo.build(), lqw);
        
        // 补充计算字段
        enrichVoData(result.getRecords());
        
        return TableDataInfo.build(result);
    }

    /**
     * 查询免税商品列表
     */
    @Override
    public List<InvDutyFreeGoodsVo> queryList(InvDutyFreeGoodsBo bo) {
        LambdaQueryWrapper<InvDutyFreeGoods> lqw = buildQueryWrapper(bo);
        List<InvDutyFreeGoodsVo> list = baseMapper.selectVoList(lqw);
        
        // 补充计算字段
        enrichVoData(list);
        
        return list;
    }

    /**
     * 根据采购人员和分类查询有效的免税商品
     */
    @Override
    public List<InvDutyFreeGoodsVo> queryValidByPurchUserAndCategory(String purchUserCode, Long categoryId) {
        List<InvDutyFreeGoodsVo> list = baseMapper.selectValidByPurchUserAndCategory(
            purchUserCode, categoryId, LocalDate.now());
        enrichVoData(list);
        return list;
    }

    /**
     * 根据分类ID查询有效的免税商品
     */
    @Override
    public List<InvDutyFreeGoodsVo> queryValidByCategory(Long categoryId) {
        List<InvDutyFreeGoodsVo> list = baseMapper.selectValidByCategory(categoryId, LocalDate.now());
        enrichVoData(list);
        return list;
    }

    /**
     * 查询即将过期的免税商品
     */
    @Override
    public List<InvDutyFreeGoodsVo> queryExpiringSoon(int expireDays) {
        if (expireDays <= 0) {
            expireDays = 7; // 默认7天
        }
        List<InvDutyFreeGoodsVo> list = baseMapper.selectExpiringSoon(LocalDate.now(), expireDays);
        enrichVoData(list);
        return list;
    }

    /**
     * 计算免税重量
     */
    @Override
    public BigDecimal calculateDutyFreeWeight(String purchUserCode, Long categoryId, LocalDate targetDate) {
        List<InvDutyFreeGoodsVo> validGoods = baseMapper.selectValidByPurchUserAndCategory(
            purchUserCode, categoryId, targetDate);
        
        return validGoods.stream()
            .filter(goods -> DutyFreeStatusEnum.APPROVED.getCode().equals(goods.getStatus()))
            .map(InvDutyFreeGoodsVo::getWeight)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 检查是否在免税期内
     */
    @Override
    public boolean isInDutyFreePeriod(String purchUserCode, Long categoryId, LocalDate targetDate) {
        List<InvDutyFreeGoodsVo> validGoods = baseMapper.selectValidByPurchUserAndCategory(
            purchUserCode, categoryId, targetDate);
        
        return validGoods.stream()
            .anyMatch(goods -> DutyFreeStatusEnum.APPROVED.getCode().equals(goods.getStatus()));
    }

    private LambdaQueryWrapper<InvDutyFreeGoods> buildQueryWrapper(InvDutyFreeGoodsBo bo) {
        LambdaQueryWrapper<InvDutyFreeGoods> lqw = Wrappers.lambdaQuery();
        lqw.le(bo.getFreeDateEnd() != null, InvDutyFreeGoods::getFreeDateStart, bo.getFreeDateEnd());
        lqw.ge(bo.getFreeDateStart() != null, InvDutyFreeGoods::getFreeDateEnd, bo.getFreeDateStart());
        lqw.eq(bo.getCategoryId() != null, InvDutyFreeGoods::getCategoryId, bo.getCategoryId());
        lqw.like(StringUtils.isNotBlank(bo.getCategoryName()), InvDutyFreeGoods::getCategoryName, bo.getCategoryName());
        lqw.eq(bo.getStatus() != null, InvDutyFreeGoods::getStatus, bo.getStatus());
        lqw.orderByDesc(InvDutyFreeGoods::getCreateTime);
        return lqw;
    }

    /**
     * 补充VO数据
     */
    private void enrichVoData(List<InvDutyFreeGoodsVo> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        
        LocalDate now = LocalDate.now();
        list.forEach(vo -> {
            // 设置状态描述
            if (vo.getStatus() != null) {
                DutyFreeStatusEnum statusEnum = DutyFreeStatusEnum.getByCode(vo.getStatus());
                vo.setStatusDesc(statusEnum != null ? statusEnum.getDesc() : "未知状态");
            }
            
            // 计算免税天数
            if (vo.getFreeDateStart() != null && vo.getFreeDateEnd() != null) {
                long days = ChronoUnit.DAYS.between(vo.getFreeDateStart(), vo.getFreeDateEnd()) + 1;
                vo.setFreeDays(days);
            }
            
            // 判断是否有效
            if (vo.getFreeDateStart() != null && vo.getFreeDateEnd() != null) {
                boolean isValid = (now.isEqual(vo.getFreeDateStart()) || now.isAfter(vo.getFreeDateStart())) &&
                                 (now.isEqual(vo.getFreeDateEnd()) || now.isBefore(vo.getFreeDateEnd())) &&
                                 DutyFreeStatusEnum.APPROVED.getCode().equals(vo.getStatus());
                vo.setIsValid(isValid);
            } else {
                vo.setIsValid(false);
            }
        });
    }

    /**
     * 新增免税商品
     */
    @GlobalTransactional
    @Override
    public Boolean insertByBo(InvDutyFreeGoodsBo bo) {
        InvDutyFreeGoods add = MapstructUtils.convert(bo, InvDutyFreeGoods.class);
        validEntityBeforeSave(add);
        // 设置默认状态为待审核
        add.setStatus(DutyFreeStatusEnum.PENDING.getCode());
        // 新增
        baseMapper.insert(add);
        // 插入文件
        insertOss(bo.getFileList(), add.getId());
        return Boolean.TRUE;
    }

    /**
     * 修改免税商品
     */
    @GlobalTransactional
    @Override
    public Boolean updateByBo(InvDutyFreeGoodsBo bo) {
        InvDutyFreeGoods update = MapstructUtils.convert(bo, InvDutyFreeGoods.class);
        update.setStatus(DutyFreeStatusEnum.PENDING.getCode());
        validEntityBeforeSave(update);
        // 更新
        baseMapper.updateById(update);
        // 删除文件
        remoteFileService.delete(OssBusinessTypeEnum.INV_DUTY_FREE_GOODS.getCode(), update.getId().toString());
        // 插入文件
        insertOss(bo.getFileList(), update.getId());
        return Boolean.TRUE;
    }

    /**
     * 审核免税商品
     */
    @Override
    public Boolean auditById(Long id, Integer status, String remark) {
        InvDutyFreeGoods entity = baseMapper.selectById(id);
        if (entity == null) {
            throw new ServiceException("免税商品不存在");
        }
        
        if (!DutyFreeStatusEnum.PENDING.getCode().equals(entity.getStatus())) {
            throw new ServiceException("只能审核待审核状态的免税商品");
        }
        
        if (!DutyFreeStatusEnum.APPROVED.getCode().equals(status) && 
            !DutyFreeStatusEnum.REJECTED.getCode().equals(status)) {
            throw new ServiceException("审核状态不正确");
        }
        
        entity.setStatus(status);
        if (StringUtils.isNotBlank(remark)) {
            entity.setRemark(remark);
        }
        
        return baseMapper.updateById(entity) > 0;
    }

    /**
     * 批量审核免税商品
     */
    @Override
    public Boolean batchAudit(Collection<Long> ids, Integer status, String remark) {
        if (CollUtil.isEmpty(ids)) {
            return false;
        }
        
        for (Long id : ids) {
            auditById(id, status, remark);
        }
        
        return true;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(InvDutyFreeGoods entity) {
        // 校验免税时间范围
        if (entity.getFreeDateStart() != null && entity.getFreeDateEnd() != null) {
            if (entity.getFreeDateEnd().isBefore(entity.getFreeDateStart())) {
                throw new ServiceException("免税结束时间不能早于开始时间");
            }
        }
        // 校验重复性（同一采购人员、同一分类、时间重叠）
        LambdaQueryWrapper<InvDutyFreeGoods> lqw = Wrappers.lambdaQuery();
        lqw.eq(InvDutyFreeGoods::getCategoryId, entity.getCategoryId());
        lqw.ne(entity.getId() != null, InvDutyFreeGoods::getId, entity.getId());
        // 检查时间重叠
        if (entity.getFreeDateStart() != null && entity.getFreeDateEnd() != null) {
            lqw.and(wrapper -> wrapper
                .le(InvDutyFreeGoods::getFreeDateStart, entity.getFreeDateEnd())
                .ge(InvDutyFreeGoods::getFreeDateEnd, entity.getFreeDateStart())
            );
            
            if (baseMapper.exists(lqw)) {
                throw new ServiceException("在此分类下已存在时间重叠的免税商品");
            }
        }
    }

    /**
     * 批量删除免税商品
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验是否可以删除
            for (Long id : ids) {
                InvDutyFreeGoods entity = baseMapper.selectById(id);
                if (entity != null && DutyFreeStatusEnum.APPROVED.getCode().equals(entity.getStatus())) {
                    LocalDate now = LocalDate.now();
                    if (entity.getFreeDateStart() != null && entity.getFreeDateEnd() != null &&
                        (now.isEqual(entity.getFreeDateStart()) || now.isAfter(entity.getFreeDateStart())) &&
                        (now.isEqual(entity.getFreeDateEnd()) || now.isBefore(entity.getFreeDateEnd()))) {
                        throw new ServiceException("不能删除正在生效中的免税商品");
                    }
                }
            }
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     *  插入OSS文件信息
     */
    private void insertOss(List<InvDutyFreeGoodsBo.FileInfo> fileList, Long id) {
        List<RemoteOssBo> ossBos = new ArrayList<>();
        if (CollUtil.isEmpty(fileList)) {
            return;
        }
        fileList.forEach(file -> {
            RemoteOssBo ossBo = new RemoteOssBo();
            ossBo.setUrl(file.getUrl());
            ossBo.setFileName(file.getName());
            ossBo.setBusinessType(OssBusinessTypeEnum.INV_DUTY_FREE_GOODS.getCode());
            ossBo.setKeyword(id.toString());
            ossBos.add(ossBo);
        });
        if (CollUtil.isNotEmpty(ossBos)) {
            remoteFileService.batchInsert(ossBos);
        }
    }

    /**
     * 查询OSS文件信息
     */
    private void selectOss(InvDutyFreeGoodsVo info) {
        if (info == null) {
            return;
        }
        List<RemoteOssVo> fileList = remoteFileService.select(OssBusinessTypeEnum.INV_DUTY_FREE_GOODS.getCode(), info.getId().toString());
        if (CollectionUtils.isEmpty(fileList)) {
            return;
        }
        info.setFileList(fileList);
    }

    /**
     * 原子性扣减重量，防止并发导致负数
     * @param goodsId 免税商品ID
     * @param reduceAmount 要扣减的重量
     * @return 实际扣减的重量
     */
    public BigDecimal atomicReduceWeight(Long goodsId, BigDecimal reduceAmount) {
        if (goodsId == null || reduceAmount == null || reduceAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }
        LambdaUpdateWrapper<InvDutyFreeGoods> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(InvDutyFreeGoods::getId, goodsId)
                .ge(InvDutyFreeGoods::getWeight, reduceAmount) // 确保重量足够
                .setSql("weight = weight - " + reduceAmount); // 原子扣减

        int updateCount = baseMapper.update(null, updateWrapper);

        if (updateCount > 0) {
            log.keyword("atomicReduceWeight:" + goodsId)
                    .info("免税商品数量原子扣减成功: goodsId={}, reduceAmount={}", goodsId, reduceAmount);
            return reduceAmount;
        } else {
            log.keyword("atomicReduceWeight:" + goodsId)
                    .info("免税商品数量原子扣减失败，重量不足: goodsId={}, reduceAmount={}", goodsId, reduceAmount);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 批量原子性扣减重量
     * @param reductionList 扣减列表
     * @return 实际扣减结果
     */
    public List<InvDutyFreeGoodsVo> batchAtomicReduceWeight(List<InvDutyFreeGoodsBo> reductionList) {
        if (CollUtil.isEmpty(reductionList)) {
            return List.of();
        }
        List<InvDutyFreeGoodsVo> resultList = new ArrayList<>();
        for (InvDutyFreeGoodsBo reduction : reductionList) {
            if (reduction.getId() == null || reduction.getWeight() == null) {
                continue;
            }
            BigDecimal actualReduced = atomicReduceWeight(reduction.getId(), reduction.getWeight());
            if (actualReduced.compareTo(BigDecimal.ZERO) > 0) {
                // 查询更新后的商品信息
                InvDutyFreeGoodsVo vo = queryById(reduction.getId());
                if (vo != null) {
                    vo.setWeight(actualReduced); // 设置为实际扣减的重量
                    vo.setRemark("扣减成功，实际扣减: " + actualReduced);
                    resultList.add(vo);
                }
            }
        }
        return resultList;
    }

}
