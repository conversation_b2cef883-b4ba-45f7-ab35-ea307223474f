package cn.xianlink.order.controller.region;

import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.validate.AddGroup;
import cn.xianlink.common.core.validate.EditGroup;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.log.annotation.Log;
import cn.xianlink.common.log.enums.BusinessType;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.domain.bo.InvDutyFreeGoodsBo;
import cn.xianlink.order.domain.vo.InvDutyFreeGoodsVo;
import cn.xianlink.order.service.IInvDutyFreeGoodsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 免税商品管理
 * @folder 总仓助手(小程序)/免税商品管理
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/order/region/dutyFreeGoods")
@Tag(name = "免税商品管理", description = "免税商品管理")
public class RegionDutyFreeGoodsController extends BaseController {

    private final IInvDutyFreeGoodsService invDutyFreeGoodsService;

    /**
     * 查询免税商品列表
     */
    @Operation(summary = "查询免税商品列表")
    @PostMapping("/list")
    public R<TableDataInfo<InvDutyFreeGoodsVo>> list(@RequestBody InvDutyFreeGoodsBo bo) {
        return R.ok(invDutyFreeGoodsService.queryPageList(bo));
    }

    /**
     * 获取免税商品详细信息
     */
    @Operation(summary = "获取免税商品详细信息")
    @GetMapping("/{id}")
    public R<InvDutyFreeGoodsVo> getInfo(@NotNull(message = "主键不能为空")
                                         @Parameter(description = "主键")
                                         @PathVariable Long id) {
        return R.ok(invDutyFreeGoodsService.queryById(id));
    }

    /**
     * 审核免税商品
     */
    @Operation(summary = "审核免税商品")
    @Log(title = "免税商品审核", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/audit/{id}")
    public R<Void> audit(@PathVariable Long id,
                        @RequestParam Integer status,
                        @RequestParam(required = false) String remark) {
        return toAjax(invDutyFreeGoodsService.auditById(id, status, remark));
    }

    /**
     * 批量审核免税商品
     */
    @Operation(summary = "批量审核免税商品")
    @Log(title = "免税商品批量审核", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/batchAudit")
    public R<Void> batchAudit(@RequestParam Long[] ids,
                             @RequestParam Integer status,
                             @RequestParam(required = false) String remark) {
        return toAjax(invDutyFreeGoodsService.batchAudit(List.of(ids), status, remark));
    }

    /**
     * 原子性扣减重量（防并发）
     */
    @Operation(summary = "原子性扣减重量")
    @Log(title = "免税商品扣减", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/atomicReduce/{id}")
    public R<BigDecimal> atomicReduceWeight(@PathVariable Long id,
                                           @RequestParam BigDecimal reduceAmount) {
        BigDecimal actualReduced = invDutyFreeGoodsService.atomicReduceWeight(id, reduceAmount);
        return R.ok(actualReduced);
    }

    /**
     * 批量原子性扣减重量（防并发）
     */
    @Operation(summary = "批量原子性扣减重量")
    @Log(title = "免税商品批量扣减", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/batchAtomicReduce")
    public R<List<InvDutyFreeGoodsVo>> batchAtomicReduceWeight(@RequestBody List<InvDutyFreeGoodsBo> reductionList) {
        List<InvDutyFreeGoodsVo> result = invDutyFreeGoodsService.batchAtomicReduceWeight(reductionList);
        return R.ok(result);
    }
}
