package cn.xianlink.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.order.domain.CarModel;
import cn.xianlink.order.domain.bo.CarModelBo;
import cn.xianlink.order.domain.vo.CarModelVo;
import cn.xianlink.order.mapper.CarModelMapper;
import cn.xianlink.order.service.ICarModelService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 车型Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-11
 */
@RequiredArgsConstructor
@Service
public class CarModelServiceImpl implements ICarModelService {

    private final CarModelMapper baseMapper;

    /**
     * 查询车型
     */
    @Override
    public CarModelVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询车型列表
     */
    @Override
    public TableDataInfo<CarModelVo> queryPageList(CarModelBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<CarModel> lqw = buildQueryWrapper(bo);
        Page<CarModelVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询车型列表
     */
    @Override
    public List<CarModelVo> queryList(CarModelBo bo) {
        LambdaQueryWrapper<CarModel> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<CarModel> buildQueryWrapper(CarModelBo bo) {
        LambdaQueryWrapper<CarModel> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getModel()), CarModel::getModel, bo.getModel());
        lqw.eq(bo.getCarryWeight() != null, CarModel::getCarryWeight, bo.getCarryWeight());
        lqw.eq(bo.getEntruckFee() != null, CarModel::getEntruckFee, bo.getEntruckFee());
        lqw.eq(bo.getParkFee() != null, CarModel::getParkFee, bo.getParkFee());
        lqw.eq(bo.getInsuranceFee() != null, CarModel::getInsuranceFee, bo.getInsuranceFee());
        lqw.eq(bo.getTransportFee() != null, CarModel::getTransportFee, bo.getTransportFee());
        lqw.eq(bo.getRegionWhId() != null, CarModel::getRegionWhId, bo.getRegionWhId());
        lqw.eq(StringUtils.isNotBlank(bo.getCreateCode()), CarModel::getCreateCode, bo.getCreateCode());
        lqw.like(StringUtils.isNotBlank(bo.getCreateName()), CarModel::getCreateName, bo.getCreateName());
        lqw.eq(StringUtils.isNotBlank(bo.getUpdateCode()), CarModel::getUpdateCode, bo.getUpdateCode());
        lqw.like(StringUtils.isNotBlank(bo.getUpdateName()), CarModel::getUpdateName, bo.getUpdateName());
        return lqw;
    }

    /**
     * 新增车型
     */
    @Override
    public Boolean insertByBo(CarModelBo bo) {
        CarModel add = BeanUtil.toBean(bo, CarModel.class);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改车型
     */
    @Override
    public Boolean updateByBo(CarModelBo bo) {
        CarModel update = BeanUtil.toBean(bo, CarModel.class);
        if (ObjectUtil.isEmpty(bo) || ObjectUtil.isEmpty(bo.getId())) {
            throw new ServiceException("更新失败，参数非法");
        }
        CarModel carModel = baseMapper.selectById(bo.getId());
        if (ObjectUtil.isEmpty(carModel)) {
            return true;
        }
        return baseMapper.updateById(update) > 0;
    }


    /**
     * 批量删除车型
     */
    @Override
    public Boolean deleteWithValidByIds(List<Long> ids) {
        if (ObjectUtil.isEmpty(ids)) {
            return true;
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
