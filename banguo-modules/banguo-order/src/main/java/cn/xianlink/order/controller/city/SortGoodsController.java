package cn.xianlink.order.controller.city;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.basic.api.RemoteCityWhPlaceService;
import cn.xianlink.basic.api.RemoteRegionLogisticsService;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.domain.bo.sortGoods.*;
import cn.xianlink.order.domain.excel.SortGoodsExportBo;
import cn.xianlink.order.domain.vo.common.CommonStatusCountVO;
import cn.xianlink.order.domain.vo.sortGoods.SortGoodsDetailVO;
import cn.xianlink.order.domain.vo.sortGoods.SortGoodsPageVO;
import cn.xianlink.order.domain.vo.sortGoods.SortGoodsRecordVO;
import cn.xianlink.order.domain.vo.sortGoods.SortGoodsStockoutVO;
import cn.xianlink.order.service.ISortGoodsService;
import io.swagger.annotations.ApiOperation;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

/**
 * 城市仓小程序-分货
 *
 * <AUTHOR>
 * @date 2024-05-25
 * @folder 城市仓端(小程序)/订单/分货
 */
@Validated
@RequiredArgsConstructor
@RestController("citySortGoodsController")
@RequestMapping("/order/city/sort")
@CustomLog
public class SortGoodsController extends BaseController {

    private final ISortGoodsService sortGoodsService;

    @DubboReference
    private final transient RemoteRegionLogisticsService remoteRegionLogisticsService;

    @DubboReference
    private final transient RemoteCityWhPlaceService remoteCityWhPlaceService;

    /**
     * 立即接车
     * @param id 发车单id
     */
    @GetMapping("/immediateReceive/{id}")
    @RepeatSubmit()
    @ApiOperation(value = "立即接车", notes = "id-发车单id")
    public R<Void> immediateReceive(@PathVariable("id") Long id){
        sortGoodsService.immediateReceive(id);
        return R.ok();
    }

    /**
     * 直管仓接货
     * @param id 送货单id
     */
    @GetMapping("/directImmediateReceive/{id}")
    @RepeatSubmit()
    @ApiOperation(value = "直管仓接货", notes = "id-送货单id")
    public R<Void> directImmediateReceive(@PathVariable("id") Long id){
        sortGoodsService.directImmediateReceive(id);
        return R.ok();
    }

    @PostMapping("/sortPage")
    @ApiOperation(value = "分页查询待分货列表")
    public R<TableDataInfo<SortGoodsPageVO>> sortPage(@RequestBody SortGoodsPageBO bo){
        if (bo.getCityWhId() == null && LoginHelper.getLoginUser().getRelationId() == null) {
            return R.ok(TableDataInfo.build());
        }else {
            bo.setCityWhId(LoginHelper.getLoginUser().getRelationId());
        }
        if (CollectionUtil.isNotEmpty(bo.getPlaceIdList())) {
            bo.setLogisticsIds(remoteRegionLogisticsService.queryIdByCityUserFiled(LoginHelper.getLoginUser().getUserId(), bo.getPlaceIdList()));
        }else if (CollectionUtil.isEmpty(bo.getPlaceIdList()) && CollectionUtil.isEmpty(bo.getPlaceIdLevel2List())){
            bo.setLogisticsIds(remoteRegionLogisticsService.queryIdByCityUser(LoginHelper.getLoginUser().getUserId()));
            bo.setPlaceIdLevel2List(remoteCityWhPlaceService.getPlaceIdsByUser(LoginHelper.getLoginUser().getUserId()));
        }
        return R.ok(sortGoodsService.sortPage(bo));
    }
    @PostMapping("/listBySortGoodsId")
    @ApiOperation(value = "根据分货主键id查询客户信息")
    public R<List<SortGoodsStockoutVO>> listBySortGoodsId(@RequestBody SortGoodsStockoutBO bo){
        if (CollectionUtil.isEmpty(bo.getPlaceIdList()) && CollectionUtil.isEmpty(bo.getPlaceIdLevel2List())){
            if (CollectionUtil.isNotEmpty(remoteRegionLogisticsService.queryAllPlaceIdByCUser(LoginHelper.getLoginUser().getUserId()))){
                bo.setPlaceIdList(ListUtil.toList(0L));
            }
            bo.setPlaceIdLevel2List(remoteCityWhPlaceService.getPlaceIdsByUser(LoginHelper.getLoginUser().getUserId()));
        }
        return R.ok(sortGoodsService.listBySortGoodsId(bo));
    }

    @PostMapping("/getSortById")
    @ApiOperation(value = "分货单单项详情")
    public R<SortGoodsDetailVO> getSortById(@RequestBody SortGoodsDetailBO bo){
        if (CollectionUtil.isEmpty(bo.getPlaceIdList()) && CollectionUtil.isEmpty(bo.getPlaceIdLevel2List())){
            if (CollectionUtil.isNotEmpty(remoteRegionLogisticsService.queryIdByCityUser(LoginHelper.getLoginUser().getUserId()))){
                bo.setPlaceIdList(ListUtil.toList(0L));
            }
            bo.setPlaceIdLevel2List(remoteCityWhPlaceService.getPlaceIdsByUser(LoginHelper.getLoginUser().getUserId()));
        }
        return R.ok(sortGoodsService.getSortById(bo));
    }

    @PostMapping("/getIsMoreGoods")
    @ApiOperation(value = "校验是否分货完成", notes = "code-销售批次商品code,0分货完成 1待分货列表没这个商品 2未完成")
    @Deprecated
    public R<Integer> getIsMoreGoods(@Validated @RequestBody SortGoodsScanCodeCheckBO bo){
        return R.ok(sortGoodsService.getIsMoreGoods(bo));
    }

    @PostMapping("/getIsMoreGoodsV2")
    @ApiOperation(value = "校验是否分货完成", notes = "code-销售批次商品code,0分货完成 1待分货列表没这个商品 2未完成")
    public R<Integer> getIsMoreGoodsV2(@Validated @RequestBody SortGoodsScanCodeCheckBO bo) {
        return R.ok(sortGoodsService.getIsMoreGoods(bo));
    }

    @PostMapping("/checkFinish")
    @ApiOperation(value = "检查待接车列表是否分货完成")
    public R<Boolean> checkFinish(@RequestBody SortGoodsScanCodeCheckBO bo){
        return R.ok(sortGoodsService.checkFinish(bo));
    }

    @PostMapping("/getSortByCode")
    @ApiOperation(value = "分货单单项详情-扫码分货",notes = "code-销售批次商品code")
    public R<SortGoodsDetailVO> getSortByCode(@RequestBody SortGoodsDetailBO bo){
        return R.ok(sortGoodsService.getSortByCode(bo));
    }

    @PostMapping("/submitSort")
    @RepeatSubmit()
    @ApiOperation(value = "提交一次分货")
    public R<Void> submitSort(@RequestBody SubmitSortBO bo){
        if (CollUtil.isEmpty(bo.getSortGoodsRecordInfoList())) {
            return R.warn("分货列表不能为空");
        }
        bo.getSortGoodsRecordInfoList().forEach(e -> {
            if (ObjectUtil.isNull(e.getSortNum())) {
                e.setSortNum(0);
            }
        });
        sortGoodsService.submitSort(bo);
        return R.ok();
    }

    @PostMapping("/autoSort")
    @RepeatSubmit()
    @ApiOperation(value = "一键分货")
    public R<Void> autoSort(@RequestBody SortGoodsDetailBO bo){
        sortGoodsService.autoSort(bo);
        return R.ok();
    }

    @PostMapping("/getSortRecord")
    @ApiOperation(value = "根据分货id获取每次分货记录")
    public R<List<SortGoodsRecordVO>> getSortRecord(@RequestBody SortGoodsDetailBO bo){
        return R.ok(sortGoodsService.getSortRecord(bo));
    }

    @PostMapping("/finishSort")
    @RepeatSubmit()
    @ApiOperation(value = "完成分货", notes = "该操作会将城市仓下未完成的分货列表全部完成")
    public R<Void> finishSort(@RequestBody SortGoodsDetailBO bo){
        sortGoodsService.finishSort(bo);
        return R.ok();
    }

    @PostMapping("/getSortDiff")
    @ApiOperation(value = "获取分货差异列表")
    public R<List<SortGoodsDetailVO>> getSortDiff(@RequestBody SortGoodsDetailBO bo){
        return R.ok(sortGoodsService.getSortDiff(bo));
    }

    @PostMapping("/cancelSort")
    @ApiOperation(value = "取消分货")
    @RepeatSubmit()
    public R<Void> cancelSort(@RequestBody SortGoodsDetailBO bo){
        if (CollectionUtil.isNotEmpty(remoteRegionLogisticsService.queryIdByCityUser(LoginHelper.getLoginUser().getUserId()))){
            bo.setPlaceIdList(ListUtil.toList(0L));
        }
        bo.setPlaceIdLevel2List(remoteCityWhPlaceService.getPlaceIdsByUser(LoginHelper.getLoginUser().getUserId()));
        sortGoodsService.cancelSort(bo);
        return R.ok();
    }

    @GetMapping("/finishSortById/{id}")
    @RepeatSubmit()
    @Deprecated
    @ApiOperation(value = "单项完成分货 入口打开了再改造", notes = "id-分货单id")
    public R<Void> finishSortById(@PathVariable("id") Long id){
        sortGoodsService.finishSortById(id);
        return R.ok();
    }
    @GetMapping("/getSortDiffById/{id}")
    @ApiOperation(value = "获取分货差异列表 入口打开了再改造")
    @Deprecated
    public R<List<SortGoodsDetailVO>> getSortDiffById(@PathVariable("id") Long id){
        return R.ok(sortGoodsService.getSortDiffById(id));
    }

    @GetMapping("/getIsMoreGoodsById/{id}")
    @Deprecated
    @ApiOperation(value = "校验是否分货完成 入口打开了再改造", notes = "code-销售批次商品code,0分货完成 1待分货列表没这个商品 2未完成")
    public R<Integer> getIsMoreGoodsById(@PathVariable("id") Long id){
        return R.ok(sortGoodsService.getIsMoreGoodsById(id));
    }

//    @GetMapping("/deleteSortRecordById/{recordId}")
//    @ApiOperation(value = "删除分货记录", notes = "recordId-分货记录id")
//    public R<Integer> deleteSortRecordById(@PathVariable("recordId") Long recordId){
//        return R.ok();
//    }

    @GetMapping("/getSortCount")
    @ApiOperation(value = "获取分货单各状态数量")
    public R<List<CommonStatusCountVO>> getSortCount(){
        return R.ok(sortGoodsService.getSortCount());
    }

    @PostMapping("/exportDeliveryNumber")
    @ApiOperation(value = "导出拍子号")
    @RepeatSubmit()
    public R<String> exportDeliveryNumber(@RequestBody SortGoodsExportBo bo) {
        return R.ok(null, sortGoodsService.exportDeliveryNumber(bo));
    }
}
