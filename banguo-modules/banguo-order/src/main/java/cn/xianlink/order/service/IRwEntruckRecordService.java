package cn.xianlink.order.service;

import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.order.domain.RwEntruckRecord;
import cn.xianlink.order.domain.entruck.bo.*;
import cn.xianlink.order.domain.entruck.vo.RwEntruckGoodsSupplierVo;
import cn.xianlink.order.domain.entruck.vo.RwEntruckGoodsVo;
import cn.xianlink.order.domain.entruck.vo.RwEntruckRecordSumVo;
import cn.xianlink.order.domain.entruck.vo.RwEntruckRecordVo;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 总仓装车记录Service接口
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
public interface IRwEntruckRecordService {
    /**
     * 查询装车记录信息
     */
    RwEntruckRecordVo selectAndCheckNullById(Long id);
    /**
     * 查询装车记录信息
     */
    List<RwEntruckRecord> selectListByIds(List<Long> ids);

    /**
     * 城市仓-直属仓待收货-计数
     */
    long waitReceiveDeliveryCount(Long regionWhId, Long cityWhId, LocalDate saleDate);

    List<RwEntruckRecordSumVo> customWaitEntruckSumListByDeliveryId(Long deliveryId);
    /**
     * 查询总仓装车记录列表
     */
    TableDataInfo<RwEntruckRecordVo> pageList(RwEntruckRecordQueryBo bo);

    /**
     * 查询装车记录列表，根据装车单id;
     */
    List<RwEntruckRecordVo> queryListByEntruckNo(Long entruckId, String entruckNo);
    /**
     * 查询装车记录列表，根据送货单id;
     */
    List<RwEntruckRecordVo> queryListByDeliveryId(Long deliveryId);
    /**
     * 查询装车记录列表，根据送货单id;
     */
    List<RwEntruckRecordVo> selectListByDeliveryId(Long deliveryId, Integer isAffiliated);
    /**
     * 查询装车记录列表，根据送货单id;
     */
    List<RwEntruckRecordVo> selectInfoListByDeliveryId(Long deliveryId);
    /**
     * 查询装车记录详情
     */
    RwEntruckRecordVo queryById(Long id);
    /**
     * 查询装车记录详情
     */
    List<RwEntruckGoodsVo> queryGoodsListByIds(List<Long> ids);

    void loadRwEntruckGoodsInfo(List<RwEntruckGoodsVo> goodsList);


    /**
     * 创建装车记录
     */
    int insert(Long deliveryId, String deliveryNo, List<RwEntruckRecordAddBo> bos);

    /**
     * 修改状态 - 完成质检
     */
    int updateStatusByDeliveryId(Long deliveryId, Integer status);
    int updateStatusByDeliveryIds(List<Long> deliveryIds, Integer status);
    int updateStatusById(List<Long> ids, Integer status);
    Boolean updateById(List<RwEntruckRecord> entityList);

    /**
     * 操作装车
     */
    int updateEntruck(RwEntruckRecordEditBo bo);
    /**
     * 操作装车，修改状态
     */
    int updateEntruck(List<Long> ids);
    /**
     * 撤销-装车
     */
    int revokeEntruck(Long id);

    /**
     * 查询-装车记录-计数(待装车单据数)
     */
    long waitEntruckRecordCount(RwWaitEntruckQueryBo bo);
    /**
     * 查询-已装车记录-合计
     */
    List<RwEntruckRecordSumVo> completeEntruckSumList(RwWaitEntruckQueryBo bo);
    /**
     * 查询-已装车记录-物流线计数
     */
    List<RwEntruckRecordSumVo> completeEntruckLogisticsCount(RwWaitEntruckQueryBo bo);
    /**
     * 查询-装车单-装车记录商品数量列表
     */
    List<RwEntruckRecordSumVo> customEntruckRecordGoodsQuantityListByEntruckNo(Long entruckId, String entruckNo, Long supplierSkuId);
    /**
     * 查询-装车单-供应商商品数量列表
     */
    void loadSumName(List<RwEntruckRecordSumVo> list);

    /**
     * 查询-已装车记录
     */
    List<RwEntruckRecordVo> completeEntruckList(Long regionWhId, Long logisticsId, LocalDate saleDate, Integer status);

    /**
     * 修改装车单号（生成装车单）
     */
    boolean updateEntruckNo(List<RwEntruckRecordEditEntruckBo> bos);

    /**
     * 查询装车记录状态
     */
    Boolean isExistEntruckRecordStatus(Long deliveryId, Integer status);
    /**
     * 查询装车记录状态
     */
    List<Long> getExistEntruckRecordStatus(List<Long> deliveryIds, Integer status);

    /**
     * 城市仓调用： 装车记录-接车-商品列表 （直属城市仓）
     */
    RwEntruckRecordVo queryReceiveGoodsList(Long recordId);
    /**
     * 城市仓调用： 装车记录-接车-状态更新 （直属城市仓）
     */
    @Transactional(rollbackFor = Throwable.class)
    void deliveryReceive(Long recordId);

    /**
     * 根据送货单id，查询装车记录的物流线id
     */
    Map<Long, List<Long>> selectWaitEntruckLogisticsIdByDeliveryIds(List<Long> deliveryIds);

    Integer deleteByIds(List<Long> ids);
}
