package cn.xianlink.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import cn.hutool.json.JSONUtil;
import cn.xianlink.basic.api.*;
import cn.xianlink.basic.api.domain.bo.RemoteRegionLogisticsQueryBo;
import cn.xianlink.basic.api.domain.vo.*;
import cn.xianlink.common.api.enums.basic.ExportModuleEnum;
import cn.xianlink.common.api.enums.order.RefundStatusEnum;
import cn.xianlink.common.api.enums.system.SysUserTypeEnum;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.oss.core.OssClient;
import cn.xianlink.common.oss.factory.OssFactory;
import cn.xianlink.order.domain.DeductionInfo;
import cn.xianlink.order.domain.OrderItem;
import cn.xianlink.order.domain.bo.DeductionInfoBo;
import cn.xianlink.order.domain.bo.refundRecord.RefundPageAuditBO;
import cn.xianlink.order.domain.dto.order.OrderItemExportSelectDTO;
import cn.xianlink.order.domain.order.bo.OrderSearchBo;
import cn.xianlink.order.domain.vo.DeductionInfoExportVo;
import cn.xianlink.order.domain.vo.DeductionInfoVo;
import cn.xianlink.order.domain.vo.common.OrderFileVO;
import cn.xianlink.order.domain.vo.order.OrderItemExportVo;
import cn.xianlink.order.domain.vo.refundRecord.RefundExportSelectDTO;
import cn.xianlink.order.domain.vo.refundRecord.RefundExportVo;
import cn.xianlink.order.enums.OccupyEnum;
import cn.xianlink.order.mapper.DeductionInfoMapper;
import cn.xianlink.order.mapper.OrderItemMapper;
import cn.xianlink.order.mapper.RefundRecordMapper;
import cn.xianlink.order.service.IDeductionInfoService;
import cn.xianlink.order.service.OrderExportService;
import cn.xianlink.product.api.RemoteSupplierSkuService;
import cn.xianlink.product.api.domain.vo.RemoteSupplierSkuFundsInfoVo;
import cn.xianlink.resource.api.RemoteFileService;
import cn.xianlink.system.api.RemoteUserService;
import cn.xianlink.system.api.domain.bo.RemoteUserBo;
import cn.xianlink.trade.api.RemoteTradeAccTransService;
import cn.xianlink.trade.api.domain.bo.RemoteAccTransItemBo;
import cn.xianlink.trade.api.domain.vo.RemoteTradeAccTransVo;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> xiaodaibing on 2025-01-09 15:38
 */
@RequiredArgsConstructor
@Service
@CustomLog
public class OrderExportServiceImpl implements OrderExportService {

    private final RefundRecordMapper recordMapper;


    private final OrderItemMapper itemMapper;

    private final DeductionInfoMapper deductionInfoMapper;

    @DubboReference
    private final RemoteExportJobService remoteExportJobService;

    @DubboReference(timeout = 300000)
    private final RemoteFileService remoteFileService;

    @DubboReference
    private RemoteSupplierSkuService remoteSupplierSkuService;
    @DubboReference
    private final RemoteSupplierService remoteSupplierService;
    @DubboReference
    private final RemoteCityWhService remoteCityWhService;
    @DubboReference
    private final RemoteRegionWhService remoteRegionWhService;
    @DubboReference
    private final RemoteBasCustomerService remoteBasCustomerService;
    @DubboReference
    private final RemoteRegionLogisticsService remoteRegionLogisticsService;
    @DubboReference
    private final RemoteTradeAccTransService remoteTradeAccTransService;
    @DubboReference
    private RemoteUserService remoteUserService;
    @Resource
    private IDeductionInfoService deductionInfoService;

    @Override
    public void submitOrderExport(OrderSearchBo searchBo) {
        searchBo.setPageNum(1);
        searchBo.setPageSize(1);
        if (StringUtils.isNotBlank(searchBo.getPhone())) {
            RemoteUserBo remoteUserBo = remoteUserService.getUserByPhoneNo(searchBo.getPhone(), SysUserTypeEnum.CUSTOMER_USER.getType());
            if (remoteUserBo == null) {
                searchBo.setCustomerId(-1L);
            } else {
                searchBo.setCustomerId(remoteUserService.getUserRelation(remoteUserBo.getUserId()));
            }
        }
        Page<OrderItemExportSelectDTO> page = itemMapper.selectOrderInfoExportPage(searchBo, searchBo.build());
        if (CollUtil.isEmpty(page.getRecords())) {
            throw new ServiceException("没有数据可导出");
        }
        if (page.getTotal() > 1000000) {
            throw new ServiceException("导出数据量过大，请缩小查询条件");
        }
        remoteExportJobService.addJob(ExportModuleEnum.ORDER_ITEM, JSONUtil.toJsonStr(searchBo));
    }


    @Override
    public String exportAdminOrderInfo(Long jobId, OrderSearchBo searchBo) {
        String fileUrl;
        if (StringUtils.isNotBlank(searchBo.getPhone())) {
            RemoteUserBo remoteUserBo = remoteUserService.getUserByPhoneNo(searchBo.getPhone(), SysUserTypeEnum.CUSTOMER_USER.getType());
            if (remoteUserBo == null) {
                searchBo.setCustomerId(-1L);
            } else {
                searchBo.setCustomerId(remoteUserService.getUserRelation(remoteUserBo.getUserId()));
            }
        }
        searchBo.setPageNum(1);
        searchBo.setPageSize(500);

        Map<Long, RemoteCityWhVo> cityMap = new HashMap<>();
        Map<Long, RemoteRegionWhVo> regionMap = new HashMap<>();
        Map<Long, RemoteRegionLogisticsVo> logisticsMap = new HashMap<>();
        Map<Long, RemoteCustomerVo> customerMap = new HashMap<>();
        Map<Long, RemoteSupplierSkuFundsInfoVo> skuMap = new HashMap<>();
        Map<String, RemoteTradeAccTransVo> itemTransMap = new HashMap<>();
        Map<Long, RemoteSupplierVo> supplierMap = new HashMap<>();

        String fileName = "订单明细导出" + jobId + ".xlsx";

        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
             ExcelWriter excelWriter = EasyExcel.write(outputStream, OrderItemExportVo.class).build()) {
            WriteSheet writeSheet = EasyExcel.writerSheet("Sheet1").build();

            boolean hasNext;
            do {
                Page<OrderItemExportSelectDTO> page = itemMapper.selectOrderInfoExportPage(searchBo, searchBo.build());
                if (page.getTotal() > 1000000) {
                    throw new ServiceException("导出数据量过大，请缩小查询条件");
                }
                if (CollUtil.isEmpty(page.getRecords())) {
                    break;
                }
                searchBo.setPageNum(searchBo.getPageNum() + 1);
                hasNext = page.hasNext();
                Set<Long> allCityIds = new HashSet<>();
                Set<Long> allRegionIds = new HashSet<>();
                Set<Long> allLogisticsIds = new HashSet<>();
                Set<Long> allCustomerIds = new HashSet<>();
                Set<Long> allSkuIds = new HashSet<>();
                Set<Long> allSupplierIds = new HashSet<>();
                List<RemoteAccTransItemBo> allItemTransBos = new LinkedList<>();

                page.getRecords().forEach(i -> {
                    allCityIds.add(i.getCityWhId());
                    allRegionIds.add(i.getRegionWhId());
                    allLogisticsIds.add(i.getLogisticsId());
                    allCustomerIds.add(i.getCustomerId());
                    allSkuIds.add(i.getSupplierSkuId());
                    allItemTransBos.add(new RemoteAccTransItemBo(i.getOrderCode(), i.getSupplierSkuId()));
                    allSupplierIds.add(i.getSupplierId());
                });

                this.fillCityMap(cityMap, allCityIds);
                this.fillRegionMap(regionMap, allRegionIds);
                this.fillLogisticsMap(logisticsMap, allLogisticsIds);
                this.fillCustomerMap(customerMap, allCustomerIds);
                this.fillSkuMap(skuMap, allSkuIds);
                this.fillItemTransMap(itemTransMap, allItemTransBos);
                this.fillSupplierMap(supplierMap, allSupplierIds);

                List<OrderItemExportVo> data = this.buildOrderItemExportVo(page.getRecords(), cityMap, regionMap, logisticsMap, customerMap, skuMap, supplierMap, itemTransMap);
                excelWriter.write(data, writeSheet);
            } while (hasNext);
            //完成写入
            excelWriter.finish();
            byte[] excelBytes = outputStream.toByteArray();
            fileUrl = remoteFileService.uploadTempFile(fileName, fileName, ContentType.OCTET_STREAM.getValue(), excelBytes, 1).getUrl();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return fileUrl;
    }


    private List<OrderItemExportVo> buildOrderItemExportVo(List<OrderItemExportSelectDTO> dtos,
                                                           Map<Long, RemoteCityWhVo> cityMap,
                                                           Map<Long, RemoteRegionWhVo> regionMap,
                                                           Map<Long, RemoteRegionLogisticsVo> logisticsMap,
                                                           Map<Long, RemoteCustomerVo> customerMap,
                                                           Map<Long, RemoteSupplierSkuFundsInfoVo> skuMap,
                                                           Map<Long, RemoteSupplierVo> supplierMap,
                                                           Map<String, RemoteTradeAccTransVo> itemTransMap) {
        List<OrderItemExportVo> result = new LinkedList<>();
        for (OrderItemExportSelectDTO d : dtos) {
            OrderItemExportVo resultVo = MapstructUtils.convert(d, OrderItemExportVo.class);
            result.add(resultVo);
            var city = cityMap.get(d.getCityWhId());
            if (city != null) {
                resultVo.setCityWhName(city.getName());
            }
            var region = regionMap.get(d.getRegionWhId());
            if (region != null) {
                resultVo.setRegionWhName(region.getRegionWhName());
            }
            var logisticsVo = logisticsMap.get(d.getLogisticsId());
            if (logisticsVo != null) {
                resultVo.setLogisticsName(logisticsVo.getLogisticsName());
            }
            var customerVo = customerMap.get(d.getCustomerId());
            if (customerVo != null) {
                resultVo.setCustomerName(customerVo.getName());
            }
            var sku = skuMap.get(d.getSupplierSkuId());
            if (sku != null) {
                resultVo.setSpuName(sku.getSkuName());
                resultVo.setSpuStandards(sku.getSpuStandardsName());
                resultVo.setBrand(sku.getBrand());
                resultVo.setSpuGrade(sku.getSpuGrade());
                resultVo.setProducer(sku.getProducer());
            }
            var supplierVo = supplierMap.get(d.getSupplierId());
            if (supplierVo != null) {
                resultVo.setSupplierName(supplierVo.getName());
            }
            //计算金融手续费
            //当前商品的金额*整单金融服务费/整单商品金额
            resultVo.setFinancialServiceAmount(d.getProductAmount().multiply(d.getFinancialServiceAmount()).divide(d.getTotalProductAmount(), 2, RoundingMode.HALF_UP));
            //提现
            RemoteTradeAccTransVo tradeAccTransVo = itemTransMap.get(d.getOrderCode() + "_" + d.getSupplierSkuId());
            if (tradeAccTransVo != null) {
                resultVo.setCashNo(tradeAccTransVo.getCashNo());
                if (StrUtil.isNotBlank(tradeAccTransVo.getCashNo())) {
                    resultVo.setCashTime(DateUtil.format(tradeAccTransVo.getCashTime(), DatePattern.NORM_DATETIME_FORMAT));
                }
            }
            resultVo.setTotalAmount(this.zeroConvert(d.getProductAmount())
                    .add(this.zeroConvert(d.getPlatformServiceAmount()))
                    .add(this.zeroConvert(d.getPlatformFreightAmount()))
                    .add(this.zeroConvert(d.getBaseFreightAmount()))
                    .subtract(this.zeroConvert(d.getPlatformServiceFreeAmount()))
                    .subtract(this.zeroConvert(d.getFreightTotalFreeAmount()))
                    .add(this.zeroConvert(resultVo.getFinancialServiceAmount()))
            );
        }
        return result;
    }

    @Override
    public void submitRefundPage(RefundPageAuditBO bo) {
        bo.setPageNum(1);
        bo.setPageSize(1);
        Page<RefundExportSelectDTO> page = recordMapper.selectRefundExportPage(bo, bo.build());
        if (CollUtil.isEmpty(page.getRecords())) {
            throw new ServiceException("没有数据可导出");
        }
        if (page.getTotal() > 100000) {
            throw new ServiceException("导出数据量过大，请缩小查询条件");
        }
        remoteExportJobService.addJob(ExportModuleEnum.REFUND_RECORD, JSONUtil.toJsonStr(bo));
    }

    @Override
    public String exportRefundPage(Long jobId, RefundPageAuditBO bo) {
        String fileUrl;

        bo.setPageNum(1);
        bo.setPageSize(100);
        String fileName = "退款导出" + jobId + ".xlsx";

        Map<Long, RemoteCityWhVo> cityMap = new HashMap<>();
        Map<Long, RemoteRegionWhVo> regionMap = new HashMap<>();
        Map<Long, RemoteRegionLogisticsVo> logisticsMap = new HashMap<>();
        Map<Long, RemoteCustomerVo> customerMap = new HashMap<>();
        Map<Long, RemoteSupplierVo> supplierMap = new HashMap<>();
        Map<Long, OrderItem> itemMap = new HashMap<>();
        Map<Long, RemoteSupplierSkuFundsInfoVo> skuMap = new HashMap<>();
        Map<String, RemoteTradeAccTransVo> itemTransMap = new HashMap<>();

        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
             ExcelWriter excelWriter = EasyExcel.write(outputStream, RefundExportVo.class).build()) {
            WriteSheet writeSheet = EasyExcel.writerSheet("Sheet1").build();

            boolean hasNext;
            do {
                Page<RefundExportSelectDTO> page = recordMapper.selectRefundExportPage(bo, bo.build());
                if (page.getTotal() > 1000000) {
                    throw new ServiceException("导出数据量过大，请缩小查询条件");
                }
                if (CollUtil.isEmpty(page.getRecords())) {
                    break;
                }
                bo.setPageNum(bo.getPageNum() + 1);
                hasNext = page.hasNext();
                Set<Long> allCityIds = new HashSet<>();
                Set<Long> allRegionIds = new HashSet<>();
                Set<Long> allCustomerIds = new HashSet<>();
                Set<Long> allSkuIds = new HashSet<>();
                Set<Long> allItemIds = new HashSet<>();
                Set<Long> allSupplierIds = new HashSet<>();
                List<RemoteAccTransItemBo> allItemTransBos = new LinkedList<>();

                page.getRecords().forEach(i -> {
                    allCityIds.add(i.getCityWhId());
                    allRegionIds.add(i.getRegionWhId());
                    allCustomerIds.add(i.getCustomerId());
                    allItemIds.add(i.getOrderItemId());
                    allSkuIds.add(i.getSupplierSkuId());
                    allSupplierIds.add(i.getSupplierId());
                    allItemTransBos.add(new RemoteAccTransItemBo(i.getOrderCode(), i.getSupplierSkuId()));
                });

                this.fillCityMap(cityMap, allCityIds);
                this.fillRegionMap(regionMap, allRegionIds);
                this.fillCustomerMap(customerMap, allCustomerIds);
                this.fillSkuMap(skuMap, allSkuIds);
                this.fillItemTransMap(itemTransMap, allItemTransBos);
                this.fillSupplierMap(supplierMap, allSupplierIds);
                this.fillItemMap(itemMap, allItemIds);

                List<RefundExportVo> data = this.buildRefundExportVo(page.getRecords(), cityMap, regionMap, logisticsMap, customerMap, skuMap, supplierMap, itemMap, itemTransMap);
                excelWriter.write(data, writeSheet);
            } while (hasNext);

            excelWriter.finish();
            byte[] excelBytes = outputStream.toByteArray();
            fileUrl = remoteFileService.uploadTempFile(fileName, fileName, ContentType.OCTET_STREAM.getValue(), excelBytes, 1).getUrl();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return fileUrl;
    }


    private List<RefundExportVo> buildRefundExportVo(List<RefundExportSelectDTO> dtos,
                                                     Map<Long, RemoteCityWhVo> cityMap,
                                                     Map<Long, RemoteRegionWhVo> regionMap,
                                                     Map<Long, RemoteRegionLogisticsVo> logisticsMap,
                                                     Map<Long, RemoteCustomerVo> customerMap,
                                                     Map<Long, RemoteSupplierSkuFundsInfoVo> skuMap,
                                                     Map<Long, RemoteSupplierVo> supplierMap,
                                                     Map<Long, OrderItem> itemMap,
                                                     Map<String, RemoteTradeAccTransVo> itemTransMap) {
        List<RefundExportVo> result = new LinkedList<>();
        for (RefundExportSelectDTO d : dtos) {
            RefundExportVo resultVo = MapstructUtils.convert(d, RefundExportVo.class);
            result.add(resultVo);
            var city = cityMap.get(d.getCityWhId());
            if (city != null) {
                resultVo.setCityWhName(city.getName());
            }
            var region = regionMap.get(d.getRegionWhId());
            if (region != null) {
                resultVo.setRegionWhName(region.getRegionWhName());
            }
            var logisticsVo = logisticsMap.get(d.getLogisticsId());
            if (logisticsVo != null) {
                resultVo.setLogisticsName(logisticsVo.getLogisticsName());
            }
            var customerVo = customerMap.get(d.getCustomerId());
            if (customerVo != null) {
                resultVo.setCustomerName(customerVo.getName());
            }
            var sku = skuMap.get(d.getSupplierSkuId());
            if (sku != null) {
                resultVo.setSpuName(sku.getSkuName());
                resultVo.setSpuStandards(sku.getSpuStandardsName());
                resultVo.setBrand(sku.getBrand());
                resultVo.setSpuGrade(sku.getSpuGrade());
                resultVo.setProducer(sku.getProducer());
            }
            var supplierVo = supplierMap.get(d.getSupplierId());
            if (supplierVo != null) {
                resultVo.setSupplierName(supplierVo.getName());
            }
            RemoteTradeAccTransVo tradeAccTransVo = itemTransMap.get(d.getOrderCode() + "_" + d.getSupplierSkuId());
            if (tradeAccTransVo != null) {
                resultVo.setCashNo(tradeAccTransVo.getCashNo());
                resultVo.setAvailNo(tradeAccTransVo.getAvailNo());
                resultVo.setCashStatus(tradeAccTransVo.getStatus());
                if (StrUtil.isNotBlank(tradeAccTransVo.getCashNo())) {
                    resultVo.setCashTime(DateUtil.format(tradeAccTransVo.getCashTime(), DatePattern.NORM_DATETIME_FORMAT));
                }
            }
            OrderItem orderItem = itemMap.get(d.getOrderItemId());
            if (orderItem != null) {
                resultVo.setBuyerName(orderItem.getBuyerName());
                resultVo.setSettleTime(DateUtil.format(orderItem.getSettleTime(), DatePattern.NORM_DATETIME_FORMAT));
                resultVo.setItemPrice(orderItem.getPrice());
            }
            //判断是占用还是普通退款
            if (d.getRefundStatus().equals(RefundStatusEnum.IN_REFUND.getCode())
                    && d.getOccupyStatus().equals(OccupyEnum.YES.getCode())) {
                resultVo.setRefundOccupyAmount(d.getRefundProductAmount());
            } else {
                resultVo.setRefundActualProductAmount(d.getRefundProductAmount());
            }
            if (d.getRefundTime() != null) {
                resultVo.setRefundTimeStr(DateUtil.format(d.getRefundTime(), DatePattern.NORM_DATETIME_FORMAT));
            }
            if (d.getSaleDate() != null) {
                resultVo.setSaleDateStr(d.getSaleDate().toString());
            }
            resultVo.setRefundNo(d.getCode());
            //实退合计：实际退款商品金额+退服务费+退运费+退基采运费+退金融手续费
            resultVo.setRefundActualTotalAmount(this.zeroConvert(resultVo.getRefundActualProductAmount())
                    .add(this.zeroConvert(resultVo.getRefundServiceAmount()))
                    .add(this.zeroConvert(resultVo.getRefundPlatformFreight()))
                    .add(this.zeroConvert(resultVo.getRefundBaseFreight()))
                    .add(this.zeroConvert(resultVo.getRefundFinancialServicePrice())));
            //退款合计：退商品金额+退服务费+退运费+退基采运费+退金融手续费
            resultVo.setRefundTotalAmount(this.zeroConvert(resultVo.getRefundProductAmount())
                    .add(this.zeroConvert(resultVo.getRefundServiceAmount()))
                    .add(this.zeroConvert(resultVo.getRefundPlatformFreight()))
                    .add(this.zeroConvert(resultVo.getRefundBaseFreight()))
                    .add(this.zeroConvert(resultVo.getRefundFinancialServicePrice())));
        }
        return result;
    }

    private void fillCityMap(Map<Long, RemoteCityWhVo> cityMap, Set<Long> cityIds) {
        //删除已有的id, 剩下的就是不存在的id
        cityIds.removeAll(cityMap.keySet());
        if (!cityIds.isEmpty()) {
            //分批次查询org信息
            List<List<Long>> l = Lists.partition(Lists.newArrayList(cityIds), 200);
            for (List<Long> city : l) {
                var t = remoteCityWhService.queryList(city).stream().collect(Collectors.toMap(RemoteCityWhVo::getId, Function.identity()));
                cityMap.putAll(t);
            }
        }
    }

    private void fillRegionMap(Map<Long, RemoteRegionWhVo> regionMap, Set<Long> regionIds) {
        //删除已有的id, 剩下的就是不存在的id
        regionIds.removeAll(regionMap.keySet());
        if (!regionIds.isEmpty()) {
            //分批次查询org信息
            List<List<Long>> regions = Lists.partition(Lists.newArrayList(regionIds), 200);
            for (List<Long> region : regions) {
                var t = remoteRegionWhService.selectRegionWhInfoByIds(region).stream().collect(Collectors.toMap(RemoteRegionWhVo::getId, Function.identity()));
                regionMap.putAll(t);
            }
        }
    }

    private void fillLogisticsMap(Map<Long, RemoteRegionLogisticsVo> logisticsMap, Set<Long> allLogistics) {
        //删除已有的id, 剩下的就是不存在的id
        allLogistics.removeAll(logisticsMap.keySet());
        if (!allLogistics.isEmpty()) {
            //分批次查询org信息
            List<List<Long>> t = Lists.partition(Lists.newArrayList(allLogistics), 200);
            for (List<Long> logistics : t) {
                RemoteRegionLogisticsQueryBo logisticsQueryBo = new RemoteRegionLogisticsQueryBo();
                logisticsQueryBo.setLogisticsIds(logistics);
                var tt = remoteRegionLogisticsService.queryList(logisticsQueryBo).stream().collect(Collectors.toMap(RemoteRegionLogisticsVo::getId, Function.identity()));
                logisticsMap.putAll(tt);
            }
        }
    }

    private void fillCustomerMap(Map<Long, RemoteCustomerVo> customerMap, Set<Long> allCustomerIds) {
        //删除已有的id, 剩下的就是不存在的id
        allCustomerIds.removeAll(customerMap.keySet());
        if (!allCustomerIds.isEmpty()) {
            //分批次查询org信息
            List<List<Long>> customerIds = Lists.partition(Lists.newArrayList(allCustomerIds), 200);
            for (List<Long> customerId : customerIds) {
                var t = remoteBasCustomerService.getByIds(customerId).stream().collect(Collectors.toMap(RemoteCustomerVo::getId, Function.identity()));
                customerMap.putAll(t);
            }
        }
    }

    private void fillSkuMap(Map<Long, RemoteSupplierSkuFundsInfoVo> skuMap, Set<Long> allSkuIds) {
        //删除已有的id, 剩下的就是不存在的id
        allSkuIds.removeAll(skuMap.keySet());
        if (!allSkuIds.isEmpty()) {
            //分批次查询org信息
            List<List<Long>> skuIds = Lists.partition(Lists.newArrayList(allSkuIds), 200);
            for (List<Long> skuId : skuIds) {
                var t = remoteSupplierSkuService.queryInfoListByFunds(skuId).stream().collect(Collectors.toMap(RemoteSupplierSkuFundsInfoVo::getSkuId, Function.identity()));
                skuMap.putAll(t);
            }
        }
    }

    private void fillItemTransMap(Map<String, RemoteTradeAccTransVo> itemTransMap, List<RemoteAccTransItemBo> allItemTransBos) {
        itemTransMap.clear();
        List<List<RemoteAccTransItemBo>> itemTransBos = Lists.partition(Lists.newArrayList(allItemTransBos), 200);
        for (List<RemoteAccTransItemBo> itemTransBo : itemTransBos) {
            itemTransMap.putAll(remoteTradeAccTransService.getByOrderItemIds(itemTransBo));
        }
    }

    private void fillItemMap(Map<Long, OrderItem> itemMap, Set<Long> allItemIds) {
        itemMap.clear();
        LambdaQueryWrapper<OrderItem> lqw = new LambdaQueryWrapper<>();
        lqw.in(OrderItem::getId, allItemIds);
        lqw.select(OrderItem::getId, OrderItem::getSettleTime, OrderItem::getBuyerName, OrderItem::getPrice);
        itemMap.putAll(itemMapper.selectList(lqw).stream().collect(Collectors.toMap(OrderItem::getId, Function.identity())));
    }


    private void fillSupplierMap(Map<Long, RemoteSupplierVo> supplierMap, Set<Long> allSupplierIds) {
        //删除已有的id, 剩下的就是不存在的id
        allSupplierIds.removeAll(supplierMap.keySet());
        if (!allSupplierIds.isEmpty()) {
            //分批次查询org信息
            List<List<Long>> supplierIds = Lists.partition(Lists.newArrayList(allSupplierIds), 200);
            for (List<Long> supplierId : supplierIds) {
                var t = remoteSupplierService.getSupplierByIds(supplierId).stream().collect(Collectors.toMap(RemoteSupplierVo::getId, Function.identity()));
                supplierMap.putAll(t);
            }
        }
    }

    private BigDecimal zeroConvert(BigDecimal amount) {
        if (amount == null) {
            return BigDecimal.ZERO;
        }
        return amount;
    }

    /**
     * 导出加扣款单列表
     *
     * @param jobId
     * @param bo
     * @return
     */
    @Override
    public String exportDeductionPage(Long jobId, DeductionInfoBo bo) {
        String fileUrl = "";
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
             ExcelWriter excelWriter = EasyExcel.write(outputStream, DeductionInfoExportVo.class).build()) {
            boolean hasNext;
            Long totalCount = 0L;
            Integer pageNum = 1;
            String sheetName = "Sheet";
            PageQuery pageQuery = new PageQuery();
            pageQuery.setPageSize(1000);
            do {
                pageQuery.setPageNum(pageNum);
                TableDataInfo<DeductionInfoVo> pageList = deductionInfoService.queryPageList(bo, pageQuery);
                List<DeductionInfoVo> records = pageList.getRows();
                totalCount += records.size();
                Long sheetNum = totalCount / 1000000;
                WriteSheet writeSheet = EasyExcel.writerSheet(sheetName + sheetNum).build();
                if (CollUtil.isEmpty(records)) {
                    break;
                }
                List<DeductionInfoExportVo> exportVos = new ArrayList<>();
                // 填充凭据图片
                for (DeductionInfoVo record : records) {
                    DeductionInfoExportVo exportVo = new DeductionInfoExportVo();
                    BeanUtil.copyProperties(record, exportVo);
                    if (record.getImageList() != null) {
                        String imageListStr = record.getImageList().stream()
                                .map(OrderFileVO::getUrl)
                                .collect(Collectors.joining(","));
                        exportVo.setImages(imageListStr);
                    } else {
                        exportVo.setImages("");
                    }
                    exportVos.add(exportVo);
                }
                // 判断下一页是否还有数据
                hasNext = records.size() == pageQuery.getPageSize();
                excelWriter.write(exportVos, writeSheet);
                pageNum++;
            } while (hasNext);
            excelWriter.finish();
            byte[] excelBytes = outputStream.toByteArray();
            String fileName = "供应商加扣款记录_" + DateUtil.format(new Date(), "yyyyMMddHHmmss") + ".xlsx";
            OssClient storage = OssFactory.instance();
            fileUrl = storage.uploadTempFile(fileName, ContentType.OCTET_STREAM.getValue(), excelBytes, 1).getUrl();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return fileUrl;
    }
}
