package cn.xianlink.order.domain;

import cn.xianlink.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 发票抬头对象 inv_invoice_title
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("inv_invoice_title")
public class InvoiceTitle extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 发票抬头ID (主键)
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 客户Id
     */
    private Long customerId;

    /**
     * 抬头名称 (如公司名)
     */
    private String titleName;

    /**
     * 税号 (公司类型必填)
     */
    private String taxNumber;

    /**
     * 地址
     */
    private String address;

    /**
     * 银行账号
     */
    private String bankAccount;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 电话
     */
    private String phone;

    /**
     * 邮箱地址
     */
    private String emailAddress;

    /**
     * 抬头类型 (company公司, personal个人)
     */
    private String titleType;

    /**
     * 发票类型 (normal普票, special专票)
     */
    private String invoiceType;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 所在地区
     */
    private String region;

    /**
     * 详细地址
     */
    private String detailedAddress;

    /**
     * 收件人姓名
     */
    private String recipientName;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 删除标志（0代表存在 存入这条数据的主键代表删除）
     */
    @TableLogic(delval = "id")
    private Long delFlag;
}
