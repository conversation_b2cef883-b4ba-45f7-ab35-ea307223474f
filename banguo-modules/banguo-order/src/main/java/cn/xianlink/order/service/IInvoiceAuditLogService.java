package cn.xianlink.order.service;

import cn.xianlink.order.domain.InvoiceAuditLog;
import cn.xianlink.order.domain.vo.InvoiceAuditLogVo;
import cn.xianlink.order.domain.bo.InvoiceAuditLogBo;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 发票审核日志Service接口
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface IInvoiceAuditLogService {

    /**
     * 查询发票审核日志
     */
    InvoiceAuditLogVo queryById(Long id);

    /**
     * 查询发票审核日志列表
     */
    TableDataInfo<InvoiceAuditLogVo> queryPageList(InvoiceAuditLogBo bo, PageQuery pageQuery);

    /**
     * 查询发票审核日志列表
     */
    List<InvoiceAuditLogVo> queryList(InvoiceAuditLogBo bo);

    /**
     * 新增发票审核日志
     */
    Boolean insertByBo(InvoiceAuditLogBo bo);

    /**
     * 修改发票审核日志
     */
    Boolean updateByBo(InvoiceAuditLogBo bo);

    /**
     * 校验并批量删除发票审核日志信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
