package cn.xianlink.order.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: 勾选待开发票的概况VO
 * @date 2025/7/30 14:56
 */
@Data
public class InvoiceOverviewVo implements Serializable {
    /**
     * 开票商品总金额
     */
    private BigDecimal productAmountTotal;
    /**
     * 开票项
     */
    private Integer invoiceCnt;
    /**
     * 可提供发票类型（1-不提供，2-提供普通发票，3-提供普通发票+专用发票）
     */
    private Integer provideInvoice;

}
