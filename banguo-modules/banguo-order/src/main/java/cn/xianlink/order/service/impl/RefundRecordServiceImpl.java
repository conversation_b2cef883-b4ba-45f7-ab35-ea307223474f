package cn.xianlink.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.text.CharPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.xianlink.basic.api.RemoteBasCustomerService;
import cn.xianlink.basic.api.RemoteCityWhService;
import cn.xianlink.basic.api.RemoteMessageNotifyService;
import cn.xianlink.basic.api.RemoteRegionLogisticsService;
import cn.xianlink.basic.api.RemoteRegionWhService;
import cn.xianlink.basic.api.RemoteSupplierService;
import cn.xianlink.basic.api.domain.bo.RemoteMessageNotifyV2Bo;
import cn.xianlink.basic.api.domain.bo.RemoteRegionLogisticsQueryBo;
import cn.xianlink.basic.api.domain.vo.RemoteCityWhVo;
import cn.xianlink.basic.api.domain.vo.RemoteCustomerVo;
import cn.xianlink.basic.api.domain.vo.RemoteRegionLogisticsVo;
import cn.xianlink.basic.api.domain.vo.RemoteRegionWhVo;
import cn.xianlink.basic.api.domain.vo.RemoteSupplierVo;
import cn.xianlink.basic.api.enums.MsgNotifyTemplateV2Enum;
import cn.xianlink.basic.api.enums.NotifyObjectTypeEnum;
import cn.xianlink.basic.api.enums.OssBusinessTypeEnum;
import cn.xianlink.common.api.enums.order.*;
import cn.xianlink.common.api.enums.trade.AccountOrgTypeEnum;
import cn.xianlink.common.api.enums.trade.RefundBusiTypeEnum;
import cn.xianlink.common.api.vo.RemoteSupplierSkuFileVo;
import cn.xianlink.common.core.enums.AuditEnum;
import cn.xianlink.common.core.enums.YNStatusEnum;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.SpringUtils;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.redis.utils.RedisUtils;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.marketing.api.RemoteDistributionService;
import cn.xianlink.marketing.api.vo.RemoteDistributionOrderInfoVo;
import cn.xianlink.marketing.api.vo.RemoteDistributionSkuVo;
import cn.xianlink.order.api.bo.DeductionInfoParamBo;
import cn.xianlink.order.api.bo.RemoteStrockBatchRefundBo;
import cn.xianlink.order.api.bo.RemoteSupAccTransBo;
import cn.xianlink.order.api.bo.RemoteSupAccTransQueryBo;
import cn.xianlink.order.api.constant.*;
import cn.xianlink.order.api.vo.RemoteOrderBusiAmtVo;
import cn.xianlink.order.api.vo.RemoteSupBillVo;
import cn.xianlink.order.api.vo.RemoteSupTransRefundDiffPriceVo;
import cn.xianlink.order.api.vo.RemoteSupTransRefundDiffVo;
import cn.xianlink.order.api.vo.RemoteSupTransRefundPriceVo;
import cn.xianlink.order.api.vo.RemoteSupTransRefundRecordVo;
import cn.xianlink.order.api.vo.RemoteSupTransRefundVo;
import cn.xianlink.order.constant.OrderCacheNames;
import cn.xianlink.order.domain.*;
import cn.xianlink.order.domain.bo.common.OrderFileBO;
import cn.xianlink.order.domain.bo.pay.SupPaymentSearchBo;
import cn.xianlink.order.domain.bo.refundRecord.CreateRefundRecordBO;
import cn.xianlink.order.domain.bo.refundRecord.DifferenceRefundPageBO;
import cn.xianlink.order.domain.bo.refundRecord.OrderRefundDTO;
import cn.xianlink.order.domain.bo.refundRecord.QueryRefundInfoBO;
import cn.xianlink.order.domain.bo.refundRecord.RefundPageAuditBO;
import cn.xianlink.order.domain.bo.refundRecord.RefundPageBO;
import cn.xianlink.order.domain.dto.OrderFixPriceItemDTO;
import cn.xianlink.order.domain.dto.trans.TransRefundItemDTO;
import cn.xianlink.order.domain.order.bo.QueryDistributionBo;
import cn.xianlink.order.domain.order.bo.SupplierSkuSearchBo;
import cn.xianlink.order.domain.order.vo.GetInfoVo;
import cn.xianlink.order.domain.order.vo.OrderItemVo;
import cn.xianlink.order.domain.order.vo.QueryDistributionItemVo;
import cn.xianlink.order.domain.order.vo.QueryDistributionVo;
import cn.xianlink.order.domain.vo.common.CommonStatusCountVO;
import cn.xianlink.order.domain.vo.order.AdminOrderInfoVo;
import cn.xianlink.order.domain.vo.order.SupPaymentOrderMiniListVo;
import cn.xianlink.order.domain.vo.pay.SupPaymentMiniListVo;
import cn.xianlink.order.domain.vo.refundRecord.DifferenceRefundAmountVO;
import cn.xianlink.order.domain.vo.refundRecord.DifferenceRefundDetailVO;
import cn.xianlink.order.domain.vo.refundRecord.DifferenceRefundPageVO;
import cn.xianlink.order.domain.vo.refundRecord.DifferenceRefundVO;
import cn.xianlink.order.domain.vo.refundRecord.RefundAmountVO;
import cn.xianlink.order.domain.vo.refundRecord.RefundBySourceVO;
import cn.xianlink.order.domain.vo.refundRecord.RefundPageVO;
import cn.xianlink.order.domain.vo.refundRecord.RefundRecordDetailVO;
import cn.xianlink.order.domain.vo.refundRecord.RefundRecordPageVO;
import cn.xianlink.order.domain.vo.refundRecord.RefundRecordVO;
import cn.xianlink.order.domain.vo.refundRecord.SupRefundDetailVO;
import cn.xianlink.order.domain.vo.refundRecord.SupRefundPageVO;
import cn.xianlink.order.domain.vo.refundRecord.SupRefundVO;
import cn.xianlink.order.enums.OccupyEnum;
import cn.xianlink.order.mapper.*;
import cn.xianlink.order.mq.producer.DeductionInfoCompleteProducer;
import cn.xianlink.order.service.IOrderItemService;
import cn.xianlink.order.service.IOrderService;
import cn.xianlink.order.service.IRefundRecordService;
import cn.xianlink.order.service.IStockoutRecordService;
import cn.xianlink.order.util.CustomNoUtil;
import cn.xianlink.product.api.RemoteSupplierSkuService;
import cn.xianlink.product.api.domain.bo.RemoteQueryInfoListBo;
import cn.xianlink.product.api.domain.bo.RemoteQuerySupplierSkuFileBo;
import cn.xianlink.product.api.domain.vo.RemoteSupplierSkuFundsInfoVo;
import cn.xianlink.product.api.domain.vo.RemoteSupplierSkuInfoVo;
import cn.xianlink.resource.api.RemoteFileService;
import cn.xianlink.resource.api.domain.RemoteOssVo;
import cn.xianlink.system.api.RemoteDeptService;
import cn.xianlink.system.api.RemoteDictService;
import cn.xianlink.system.api.domain.vo.RemoteSysDictVo;
import cn.xianlink.system.api.model.LoginUser;
import cn.xianlink.trade.api.RemotePaymentService;
import cn.xianlink.trade.api.RemoteTransferService;
import cn.xianlink.trade.api.domain.bo.*;
import cn.xianlink.trade.api.domain.vo.OrderRefundQueryVo;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 退款记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-25
 */
@RequiredArgsConstructor
@Service
@CustomLog
@RefreshScope
public class RefundRecordServiceImpl implements IRefundRecordService {

    private final RefundRecordMapper baseMapper;

    private final RefundProductDetailMapper refundProductDetailMapper;

    @DubboReference
    private final RemoteCityWhService remoteCityWhService;

    @DubboReference
    private final RemoteSupplierService remoteSupplierService;

    @DubboReference
    private final RemoteBasCustomerService remoteBasCustomerService;

    @DubboReference
    private final RemoteRegionWhService remoteRegionWhService;

    private IOrderService orderService;

    @Autowired
    public void setIOrderService(@Lazy IOrderService iOrderService) {
        this.orderService = iOrderService;
    }

    @DubboReference
    private final RemoteSupplierSkuService remoteSupplierSkuService;

    @DubboReference
    private RemoteMessageNotifyService remoteMessageNotifyService;

    @DubboReference
    private final RemoteRegionLogisticsService remoteRegionLogisticsService;

    @DubboReference(timeout = 10000)
    private final RemotePaymentService remotePaymentService;

    @DubboReference(timeout = 10000)
    private final RemoteTransferService remoteTransferService;

    private final OrderItemMapper orderItemMapper;

    private final OrderMapper orderMapper;

    private final StockoutRecordMapper stockoutRecordMapper;

    private final RemoteDictService remoteDictService;

    private final IOrderItemService orderItemService;

    private final StockoutRecordDetailMapper stockoutRecordDetailMapper;

    private final ReceiveGoodsRecordDetailMapper receiveGoodsRecordDetailMapper;

    private final ReceiveGoodsRecordMapper receiveGoodsRecordMapper;
    @DubboReference
    private final RemoteDeptService remoteDeptService;

    @DubboReference
    private final RemoteDistributionService distributionService;

    @Resource
    private transient DeductionInfoCompleteProducer deductionInfoCompleteProducer;

    @DubboReference
    private final RemoteFileService remoteFileService;

    private final ReportLossOrderMapper reportLossOrderMapper;

    private final BlameRecordMapper blameRecordMapper;

    private final BlameRecordDetailMapper blameRecordDetailMapper;

    private final DeductionInfoMapper deductionInfoMapper;


    private IStockoutRecordService stockoutRecordService;

    @Autowired
    public void setIStockoutRecordService(@Lazy IStockoutRecordService iStockoutRecordService) {
        this.stockoutRecordService = iStockoutRecordService;
    }

    /**
     * 超额审核金额
     */
    @Value("${banguo-order.refund_audi_amount}")
    private BigDecimal audiAmount;

    //少货确认不立即退钱的总仓ids,多个用“,”拼接 CharPool.COMMA
    @Value("${stockout.confirm.sh.regionWhIds}")
    private String SHORTAGE_CONFIRM_WITHOUT_REFUND_REGIONIDS;

    //少货确认不立即退钱的金额限制，为null或为0则不限制
    //后续要放开控制时，只需改为0，总仓配置不动
    @Value("${stockout.confirm.sh.limit:0}")
    private Integer SHORTAGE_CONFIRM_WITHOUT_REFUND_LIMIT;

    /**
     * 佣金提现收取税率，默认7% 略大于实际费率
     */
    @Value("${distributionTaxRate:0.07}")
    private BigDecimal distributionTaxRate;

    //缺货少货占用拓展信息的format,缺货少货单号-itemId
    private static final String stockoutExpandFormat = "{}-{}";

    //报损占用拓展信息的format,报损单号
    private static final String lossExpandFormat = "{}";

    @Override
    public RefundRecordVO queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 差额退款分页查询
     */
    @Override
    public TableDataInfo<DifferenceRefundVO> difPage(DifferenceRefundPageBO bo) {
        //先分页获取差额退款单
        bo.setRefundType(RefundTypeEnum.DIFFERENCE.getCode());
        Page<DifferenceRefundVO> tableDataInfo = baseMapper.difPage(bo, bo.build());
        //获取详情
        if (CollectionUtil.isNotEmpty(tableDataInfo.getRecords())){
            List<Long> customerIds = tableDataInfo.getRecords().stream().map(DifferenceRefundVO::getCustomerId).toList();
            Map<Long, RemoteCustomerVo> customerMap = remoteBasCustomerService.getByIds(customerIds).stream().collect(Collectors.toMap(RemoteCustomerVo::getId, Function.identity()));

            List<Long> ids = tableDataInfo.getRecords().stream().map(DifferenceRefundVO::getId).toList();
            LambdaQueryWrapper<RefundProductDetail> qw = Wrappers.lambdaQuery();
            qw.in(RefundProductDetail::getRefundRecordId, ids).eq(RefundProductDetail::getDelFlag, 0);
            List<RefundProductDetail> list = refundProductDetailMapper.selectList(qw);

            List<Long> sukIds = list.stream().map(RefundProductDetail::getSupplierSkuId).toList();
            //sku
            List<RemoteSupplierSkuFundsInfoVo> skuInfoVos = remoteSupplierSkuService.queryInfoListByFunds(sukIds);
            Map<Long, RemoteSupplierSkuFundsInfoVo> skuMap = skuInfoVos.stream().collect(Collectors.toMap(RemoteSupplierSkuFundsInfoVo::getSkuId, Function.identity()));


            //获取销售批次商品信息
            if (CollectionUtil.isNotEmpty(list)){
                List<Long> supplierDeptIds = list.stream().map(RefundProductDetail::getSupplierDeptId)
                        .filter(supplierDeptId -> ObjectUtil.isNotEmpty(supplierDeptId) && supplierDeptId > 0).distinct().toList();
                Map<Long, String> deptNameMap = new HashMap<>();
                if (ObjectUtil.isNotEmpty(supplierDeptIds)) {
                    deptNameMap = remoteDeptService.getDeptNameMap(Lists.newArrayList(supplierDeptIds));
                }
                Map<Long, List<RefundProductDetail>> map = list.stream().collect(Collectors.groupingBy(RefundProductDetail::getRefundRecordId));
                Map<Long, String> finalDeptNameMap = deptNameMap;
                tableDataInfo.getRecords().forEach(item -> {
                    item.setCustomerAlias(customerMap.getOrDefault(item.getCustomerId(), new RemoteCustomerVo()).getAlias());

                    List<RefundProductDetail> details = map.get(item.getId());
                    if (CollectionUtil.isNotEmpty(details)){
                        List<DifferenceRefundAmountVO> amountDetails = new ArrayList<>();
                        details.forEach(l->{
                            DifferenceRefundAmountVO amountVO = new DifferenceRefundAmountVO();
                            amountVO.setDetailId(l.getId());
                            amountVO.setSupplierSkuId(l.getSupplierSkuId());
                            amountVO.setSupplierDeptId(l.getSupplierDeptId());
                            amountVO.setSupplierDeptName(finalDeptNameMap.get(l.getSupplierDeptId()));
                            amountVO.setRefundAmount(l.getRefundAmount());
                            amountVO.setSpuName(l.getSpuName());
                            amountVO.setSpuStandards(l.getSpuStandards());
                            amountVO.setOrderPrice(l.getOrderPrice());
                            amountVO.setSettlePrice(l.getSettlePrice());
                            amountVO.setRefundStatus(l.getRefundStatus());
                            RemoteSupplierSkuFundsInfoVo sku = skuMap.get(l.getSupplierSkuId());
                            if(sku != null) {
                                if (ObjectUtil.isNotEmpty(bo.getSupFlag()) && bo.getSupFlag() && (!sku.getSupplierId().equals(bo.getSupplierId()))) {
                                    return;
                                }
                                amountVO.setBusinessType(sku.getBusinessType());
                                amountVO.setSpuStandards(sku.getSpuStandards());
                                amountVO.setProducer(sku.getProducer());
                                // 产地简称
                                amountVO.setAreaCode(sku.getAreaCode());
                                amountVO.setBrand(sku.getBrand());
                                amountVO.setShortProducer(sku.getShortProducer());
                            }
                            amountDetails.add(amountVO);
                        });
                        item.setAmountDetails(amountDetails);
                    }
                });
            }
        }
        return TableDataInfo.build(tableDataInfo);
    }

    /**
     * 查询差额退款详情
     */
    @Override
    public DifferenceRefundDetailVO getDifById(Long id) {
        DifferenceRefundDetailVO detailVO = new DifferenceRefundDetailVO();
        RefundProductDetail detail = refundProductDetailMapper.selectById(id);
        BeanUtils.copyProperties(detail, detailVO);

        if (ObjectUtil.isNotEmpty(detailVO.getSupplierDeptId()) && detailVO.getSupplierDeptId() > 0) {
            Map<Long, String> deptNameMap = remoteDeptService.getDeptNameMap(Lists.newArrayList(detailVO.getSupplierDeptId()));
            detailVO.setSupplierDeptName(deptNameMap.get(detailVO.getSupplierDeptId()));
        }
        RemoteSupplierSkuInfoVo byId = remoteSupplierSkuService.getById(detail.getSupplierSkuId());
        detailVO.setBusinessType(byId.getBusinessType());

        //查询订单code
        RefundRecord record = baseMapper.selectById(detail.getRefundRecordId());
        detailVO.setCode(record.getCode());
        detailVO.setOrderCode(record.getOrderCode());
        detailVO.setPayTime(record.getPayTime());
        detailVO.setDifRefundType(record.getDifRefundType());
        //获取城市仓名称
        RemoteCityWhVo remoteCityWhVo = remoteCityWhService.queryById(record.getCityWhId());
        if (ObjectUtil.isNotNull(remoteCityWhVo)){
            detailVO.setCityWhName(remoteCityWhVo.getName());
        }
        //获取供应商名称
        RemoteSupplierVo supplierVo = remoteSupplierService.getSupplierById(detail.getSupplierId());
        if (ObjectUtil.isNotNull(supplierVo)){
            detailVO.setSupplierName(supplierVo.getName());
            detailVO.setSupplierAlias(supplierVo.getAlias());
        }
        //获取用户名称
        RemoteCustomerVo customerVo = remoteBasCustomerService.getById(record.getCustomerId());
        if (ObjectUtil.isNotNull(customerVo)){
            detailVO.setCustomerName(customerVo.getName());
            detailVO.setCustomerAlias(customerVo.getAlias());
        }
        //获取总仓名称
        RemoteRegionWhVo regionWhVo = remoteRegionWhService.queryById(record.getRegionWhId());
        if (ObjectUtil.isNotNull(regionWhVo)){
            detailVO.setRegionWhName(regionWhVo.getRegionWhName());
        }
        //获取销售批次商品信息
        RemoteSupplierSkuInfoVo supplierSkuVo = remoteSupplierSkuService.getById(detail.getSupplierSkuId());
        if (ObjectUtil.isNotNull(supplierSkuVo)) {
            detailVO.setSpuStandards(supplierSkuVo.getSpuStandards());
            detailVO.setSpuGrade(supplierSkuVo.getSpuGrade());
            detailVO.setProducer(supplierSkuVo.getProducer());
            // 产地简称
            detailVO.setAreaCode(supplierSkuVo.getAreaCode());
            detailVO.setBrand(supplierSkuVo.getBrand());
            detailVO.setShortProducer(supplierSkuVo.getShortProducer());
            detailVO.setShouguangVegetables(supplierSkuVo.getShouguangVegetables());
            detailVO.setSaleType(supplierSkuVo.getSaleType());
        }
        //获取包装图片
        RemoteQuerySupplierSkuFileBo bo = new RemoteQuerySupplierSkuFileBo();
        bo.setSupplierSkuIdList(Collections.singletonList(detail.getSupplierSkuId()));
        List<RemoteSupplierSkuFileVo> fileVos = remoteSupplierSkuService.querySupplierSkuFile(bo);
        detailVO.setFileList(fileVos);
        return detailVO;
    }

    /**
     * 获取差额退款单各状态数量
     * @param type 类型
     * @see AccountTypeStatusEnum
     */
    @Override
    public CommonStatusCountVO getDifRefundCount(Integer type) {
        CommonStatusCountVO vo = new CommonStatusCountVO();
        //获取用户角色,定位查询范围
        LoginUser loginUser = LoginHelper.getLoginUser();
        LambdaQueryWrapper<RefundRecord> qw = Wrappers.lambdaQuery();
        qw.eq(RefundRecord::getDelFlag, 0).eq(RefundRecord::getRefundStatus, RefundStatusEnum.IN_REFUND.getCode())
                .eq(RefundRecord::getRefundType, RefundTypeEnum.DIFFERENCE.getCode());
        if (type.equals(AccountTypeStatusEnum.CUSTOMER.getCode())) {
            //用户
            qw.eq(RefundRecord::getCustomerId, loginUser.getRelationId());
        } else if (type.equals(AccountTypeStatusEnum.CITY.getCode())) {
            //城市仓
            qw.eq(RefundRecord::getCityWhId, loginUser.getRelationId());
        } else if (type.equals(AccountTypeStatusEnum.REGION.getCode())) {
            //总仓
            qw.eq(RefundRecord::getRegionWhId, loginUser.getRelationId());
        }
        Long count = baseMapper.selectCount(qw);
        vo.setCount(count);
        vo.setValue(RefundStatusEnum.IN_REFUND.getCode().longValue());
        return null;
    }

    /**
     * 分页查询退货退款单列表
     */
    @Override
    public TableDataInfo<RefundRecordVO> refundPage(DifferenceRefundPageBO bo) {
        Page<RefundRecordVO> tableDataInfo = baseMapper.refundPage(bo, bo.build());
        List<RefundRecordVO> records = tableDataInfo.getRecords();
        if (CollectionUtil.isNotEmpty(records)){
            List<Long> skuIds = records.stream().flatMap(item -> item.getAmountDetails().stream()
                    .map(RefundAmountVO::getSupplierSkuId)).distinct().collect(Collectors.toList());
            List<RemoteSupplierSkuFundsInfoVo> skuInfoVos = remoteSupplierSkuService.queryInfoListByFunds(skuIds);
            Map<Long, RemoteSupplierSkuFundsInfoVo> skuMap = skuInfoVos.stream().collect(Collectors.toMap(RemoteSupplierSkuFundsInfoVo::getSkuId, Function.identity()));
            records.stream().forEach(item -> item.getAmountDetails().forEach(a -> {
                RemoteSupplierSkuFundsInfoVo orDefault = skuMap.getOrDefault(a.getSupplierSkuId(),new RemoteSupplierSkuFundsInfoVo());
                a.setSpuGrade(orDefault.getSpuGrade());
                a.setSpuStandards(orDefault.getSpuStandards());
                a.setBusinessType(orDefault.getBusinessType());
                a.setProducer(orDefault.getProducer());
                // 产地简称
                a.setAreaCode(orDefault.getAreaCode());
                a.setBrand(orDefault.getBrand());
                a.setShortProducer(orDefault.getShortProducer());
            }));
        }
        return TableDataInfo.build(tableDataInfo);
    }

    /**
     * 获取退货退款单各状态数量
     * @param type 类型
     * @see AccountTypeStatusEnum
     */
    @Override
    public List<CommonStatusCountVO> getRefundCount(Integer type) {
        CommonStatusCountVO vo = new CommonStatusCountVO();
        //获取用户角色,定位查询范围
        LoginUser loginUser = LoginHelper.getLoginUser();
        LambdaQueryWrapper<RefundRecord> qw = Wrappers.lambdaQuery();
        qw.eq(RefundRecord::getDelFlag, 0).eq(RefundRecord::getRefundStatus, RefundStatusEnum.IN_REFUND.getCode())
                .in(RefundRecord::getRefundType, RefundTypeEnum.LACK.getCode(), RefundTypeEnum.LESS.getCode());
        if (type.equals(AccountTypeStatusEnum.CUSTOMER.getCode())) {
            //用户
            qw.eq(RefundRecord::getCustomerId, loginUser.getRelationId());
        } else if (type.equals(AccountTypeStatusEnum.CITY.getCode())) {
            //城市仓
            qw.eq(RefundRecord::getCityWhId, loginUser.getRelationId());
        } else if (type.equals(AccountTypeStatusEnum.REGION.getCode())) {
            //总仓
            qw.eq(RefundRecord::getRegionWhId, loginUser.getRelationId());
        }
        Long count = baseMapper.selectCount(qw);
        vo.setCount(count);
        vo.setValue(RefundStatusEnum.IN_REFUND.getCode().longValue());
        return null;
    }

    /**
     * 查询退货退款单详情
     * @param id 退货退款单详情d
     */
    @Override
    public RefundRecordDetailVO getRefundById(Long id) {
        RefundRecordDetailVO detailVO = new RefundRecordDetailVO();
        //先查详情
        RefundProductDetail detail = refundProductDetailMapper.selectById(id);
        BeanUtils.copyProperties(detail, detailVO);
        //通过详情反查退款单
        RefundRecord record = baseMapper.selectById(detail.getRefundRecordId());
        detailVO.setCode(record.getCode());
        detailVO.setOrderCode(record.getOrderCode());
        detailVO.setPayTime(record.getPayTime());
        //优惠金额 暂时只有优惠补贴金额,后面有再继续加  250320 新增商品优惠金额
        detailVO.setRefundFreeTotalAmount(detail.getRefundProductFreeAmount().subtract(detail.getRefundSubsidyFreeAmount()));
        //赋值城市仓 总仓 用户名称
        //获取城市仓名称
        RemoteCityWhVo remoteCityWhVo = remoteCityWhService.queryById(record.getCityWhId());
        if (ObjectUtil.isNotNull(remoteCityWhVo)){
            detailVO.setCityWhName(remoteCityWhVo.getName());
        }
        //总仓
        RemoteRegionWhVo remoteRegionWhVo = remoteRegionWhService.queryById(record.getRegionWhId());
        if (ObjectUtil.isNotNull(remoteRegionWhVo)) {
            detailVO.setRegionWhName(remoteRegionWhVo.getRegionWhName());
        }
        //获取用户名称
        RemoteCustomerVo customerVo = remoteBasCustomerService.getById(record.getCustomerId());
        if (ObjectUtil.isNotNull(customerVo)) {
            detailVO.setCustomerName(customerVo.getName());
        }
        RemoteSupplierSkuInfoVo byId = remoteSupplierSkuService.getById(detail.getSupplierSkuId());
        if (ObjectUtil.isNotNull(byId)){
            detailVO.setProducer(byId.getProducer());
            detailVO.setBusinessType(byId.getBusinessType());
            detailVO.setSpuStandards(byId.getSpuStandards());
            // 产地简称
            detailVO.setAreaCode(byId.getAreaCode());
            detailVO.setBrand(byId.getBrand());
            detailVO.setShortProducer(byId.getShortProducer());
            detailVO.setShouguangVegetables(byId.getShouguangVegetables());
            detailVO.setSaleType(byId.getSaleType());
        }
        return detailVO;
    }

    /**
     * 供应商查询退货退款列表
     */
    @Override
    public TableDataInfo<SupRefundPageVO> supRefundPage(RefundPageBO bo) {
        Page<SupRefundPageVO> page = refundProductDetailMapper.supRefundPage(bo, bo.build());
        if (ObjectUtil.isNotEmpty(page.getRecords())) {
            List<Long> supplierDeptIds = page.getRecords().stream().map(SupRefundPageVO::getSupplierDeptId)
                    .filter(supplierDeptId -> ObjectUtil.isNotEmpty(supplierDeptId) && supplierDeptId > 0).distinct().toList();
            Map<Long, String> deptNameMap = new HashMap<>();
            deptNameMap =  remoteDeptService.getDeptNameMap(Lists.newArrayList(supplierDeptIds));
            Map<Long, String> finalDeptNameMap = deptNameMap;
            page.getRecords().forEach(vo -> vo.setSupplierDeptName(finalDeptNameMap.get(vo.getSupplierDeptId())));
        }
        return TableDataInfo.build(page);
    }

    /**
     * 供应商查询退货退款详情
     */
    @Override
    public SupRefundVO getSupRefundById(Long supplierSkuId) {
        SupRefundVO supRefundVO = new SupRefundVO();
        //获取该销售批次下退货退款单详情 (不包括差额退/退款不成功的)
        List<RefundProductDetail> refundDetails = refundProductDetailMapper.selectList(Wrappers.lambdaQuery(RefundProductDetail.class)
                .eq(RefundProductDetail::getSupplierSkuId, supplierSkuId).notIn(RefundProductDetail::getRefundType, RefundTypeEnum.DIFFERENCE.getCode())
                .eq(RefundProductDetail::getRefundStatus, RefundStatusEnum.HAS_REFUND.getCode()).eq(RefundProductDetail::getDelFlag, 0));
        if (CollectionUtil.isNotEmpty(refundDetails)){
            //先给通用信息赋值
            SupRefundPageVO pageVO = new SupRefundPageVO();
            RefundProductDetail detail = refundDetails.get(0);
            pageVO.setSupplierSkuId(supplierSkuId);
            pageVO.setSpuId(detail.getSpuId());
            pageVO.setSpuName(detail.getSpuName());
            pageVO.setSpuStandards(detail.getSpuStandards());
            pageVO.setSpuGrossWeight(detail.getSpuGrossWeight());
            pageVO.setSpuNetWeight(detail.getSpuNetWeight());
            //统计总退货件数
            pageVO.setAllRefundCount(refundDetails.stream().mapToInt(RefundProductDetail::getOrderCount).sum());
            //统计总退款金额 = 退实际金额+退平台优惠金额
            BigDecimal refundAmount = refundDetails.stream().map(RefundProductDetail::getRefundAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal refundSubsidyFreeAmount = refundDetails.stream().map(RefundProductDetail::getRefundSubsidyFreeAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            pageVO.setRefundAmount(refundAmount.add(refundSubsidyFreeAmount));
            supRefundVO.setPageVO(pageVO);
            //具体详情信息
            List<SupRefundDetailVO> detailVOList = new ArrayList<>();
            //查询退款单获取订单code
            List<Long> refundRecordIds = refundDetails.stream().map(RefundProductDetail::getRefundRecordId).toList();
            List<RefundRecord> refundRecords = baseMapper.selectList(Wrappers.lambdaQuery(RefundRecord.class).in(RefundRecord::getId, refundRecordIds).select(RefundRecord::getOrderCode, RefundRecord::getId));
            Map<Long, String> codeMap = refundRecords.stream().collect(Collectors.toMap(RefundRecord::getId, RefundRecord::getOrderCode));
            refundDetails.forEach(l -> {
                SupRefundDetailVO detailVO = new SupRefundDetailVO();
                BeanUtils.copyProperties(l, detailVO);
                detailVO.setOrderCode(codeMap.get(l.getRefundRecordId()));
                //供应商看到的退款金额=实际退用户款金额+退平台补贴金额
                detailVO.setRefundAmount(l.getRefundAmount().add(l.getRefundSubsidyFreeAmount()));
                detailVOList.add(detailVO);
            });
            supRefundVO.setDetailVOList(detailVOList);
        }
        return supRefundVO;
    }

    /**
     * 退货退款单分页查询 管理台
     */
    @Override
    public TableDataInfo<RefundRecordPageVO> adminRefundPage(DifferenceRefundPageBO bo) {
        Page<RefundRecordPageVO> page = baseMapper.adminRefundPage(bo, bo.build());
        //赋值
        if (CollectionUtil.isNotEmpty(page.getRecords())) {
            List<Long> cityWhIds = page.getRecords().stream().map(RefundRecordPageVO::getCityWhId).distinct().toList();
            //城市仓名称
            List<RemoteCityWhVo> remoteCityWhVos = remoteCityWhService.queryList(cityWhIds);
            Map<Long, String> cityMap = remoteCityWhVos.stream().collect(Collectors.toMap(RemoteCityWhVo::getId, RemoteCityWhVo::getName));
            //总仓名称
            List<RemoteRegionWhVo> remoteRegionWhVos = remoteRegionWhService.queryList();
            Map<Long, String> regionMap = remoteRegionWhVos.stream().collect(Collectors.toMap(RemoteRegionWhVo::getId, RemoteRegionWhVo::getRegionWhName));
            //用户名称
            List<Long> customerIds = page.getRecords().stream().map(RefundRecordPageVO::getCustomerId).distinct().toList();
            List<RemoteCustomerVo> customerVos = remoteBasCustomerService.getByIds(customerIds);
            Map<Long, String> customerMap = customerVos.stream().collect(Collectors.toMap(RemoteCustomerVo::getId, RemoteCustomerVo::getName));
            //供应商名称
            List<Long> supplierIds = page.getRecords().stream().map(RefundRecordPageVO::getSupplierId).distinct().toList();
            List<RemoteSupplierVo> supplierVos = remoteSupplierService.getSupplierByIds(supplierIds);
            Map<Long, RemoteSupplierVo> supplierMap = supplierVos.stream().collect(Collectors.toMap(RemoteSupplierVo::getId, Function.identity()));
            page.getRecords().forEach(item -> {
                item.setCityWhName(cityMap.get(item.getCityWhId()));
                item.setRegionWhName(regionMap.get(item.getRegionWhId()));
                item.setCustomerName(customerMap.get(item.getCustomerId()));
                item.setSupplierName(supplierMap.getOrDefault(item.getSupplierId(), new RemoteSupplierVo()).getName());
                item.setSupplierAlias(supplierMap.getOrDefault(item.getSupplierId(), new RemoteSupplierVo()).getAlias());
            });
        }
        return TableDataInfo.build(page);
    }

    /**
     * 差额退款单分页查询 管理台
     */
    @Override
    public TableDataInfo<DifferenceRefundPageVO> adminDifPage(DifferenceRefundPageBO bo) {
        bo.setRefundType(RefundTypeEnum.DIFFERENCE.getCode());
        Page<DifferenceRefundPageVO> page = baseMapper.adminDifPage(bo, bo.build());
        //赋值
        if (CollectionUtil.isNotEmpty(page.getRecords())) {
            List<Long> cityWhIds = page.getRecords().stream().map(DifferenceRefundPageVO::getCityWhId).distinct().toList();
            //城市仓名称
            List<RemoteCityWhVo> remoteCityWhVos = remoteCityWhService.queryList(cityWhIds);
            Map<Long, String> cityMap = remoteCityWhVos.stream().collect(Collectors.toMap(RemoteCityWhVo::getId, RemoteCityWhVo::getName));
            //总仓名称
            List<RemoteRegionWhVo> remoteRegionWhVos = remoteRegionWhService.queryList();
            Map<Long, String> regionMap = remoteRegionWhVos.stream().collect(Collectors.toMap(RemoteRegionWhVo::getId, RemoteRegionWhVo::getRegionWhName));
            //用户名称
            List<Long> customerIds = page.getRecords().stream().map(DifferenceRefundPageVO::getCustomerId).distinct().toList();
            List<RemoteCustomerVo> customerVos = remoteBasCustomerService.getByIds(customerIds);
            Map<Long, String> customerMap = customerVos.stream().collect(Collectors.toMap(RemoteCustomerVo::getId, RemoteCustomerVo::getName));
            //供应商名称
            List<Long> supplierIds = page.getRecords().stream().map(DifferenceRefundPageVO::getSupplierId).distinct().toList();
            List<RemoteSupplierVo> supplierVos = remoteSupplierService.getSupplierByIds(supplierIds);
            Map<Long, RemoteSupplierVo> supplierMap = supplierVos.stream().collect(Collectors.toMap(RemoteSupplierVo::getId, Function.identity()));
            page.getRecords().forEach(item -> {
                item.setCityWhName(cityMap.get(item.getCityWhId()));
                item.setRegionWhName(regionMap.get(item.getRegionWhId()));
                item.setCustomerName(customerMap.get(item.getCustomerId()));
                item.setSupplierName(supplierMap.getOrDefault(item.getSupplierId(), new RemoteSupplierVo()).getName());
                item.setSupplierAlias(supplierMap.getOrDefault(item.getSupplierId(), new RemoteSupplierVo()).getAlias());
            });
        }
        return TableDataInfo.build(page);
    }

    /**
     * 差额退款单分页查询 管理台 差异管理
     * @param bo
     */
    @Override
    public TableDataInfo<DifferenceRefundDetailVO> difErrorPage(DifferenceRefundPageBO bo) {
        bo.setRefundType(RefundTypeEnum.DIFFERENCE.getCode());
        Page<DifferenceRefundDetailVO> page = baseMapper.difPageAdmin(bo, bo.build());
        //赋值名称
        if (CollectionUtil.isNotEmpty(page.getRecords())) {
            List<Long> cityWhIds = page.getRecords().stream().map(DifferenceRefundDetailVO::getCityWhId).distinct().toList();
            //城市仓名称
            List<RemoteCityWhVo> remoteCityWhVos = remoteCityWhService.queryList(cityWhIds);
            Map<Long, String> cityMap = remoteCityWhVos.stream().collect(Collectors.toMap(RemoteCityWhVo::getId, RemoteCityWhVo::getName));
            //总仓名称
            List<RemoteRegionWhVo> remoteRegionWhVos = remoteRegionWhService.queryList();
            Map<Long, String> regionMap = remoteRegionWhVos.stream().collect(Collectors.toMap(RemoteRegionWhVo::getId, RemoteRegionWhVo::getRegionWhName));
            //用户名称
            List<Long> customerIds = page.getRecords().stream().map(DifferenceRefundDetailVO::getCustomerId).distinct().toList();
            List<RemoteCustomerVo> customerVos = remoteBasCustomerService.getByIds(customerIds);
            Map<Long, String> customerMap = customerVos.stream().collect(Collectors.toMap(RemoteCustomerVo::getId, RemoteCustomerVo::getName));
            //供应商名称
            List<Long> supplierIds = page.getRecords().stream().map(DifferenceRefundDetailVO::getSupplierId).distinct().toList();
            List<RemoteSupplierVo> supplierVos = remoteSupplierService.getSupplierByIds(supplierIds);
            Map<Long, RemoteSupplierVo> supplierMap = supplierVos.stream().collect(Collectors.toMap(RemoteSupplierVo::getId, Function.identity()));
            page.getRecords().forEach(item -> {
                item.setCityWhName(cityMap.get(item.getCityWhId()));
                item.setRegionWhName(regionMap.get(item.getRegionWhId()));
                item.setCustomerName(customerMap.get(item.getCustomerId()));
                item.setSupplierName(supplierMap.getOrDefault(item.getSupplierId(), new RemoteSupplierVo()).getName());
                item.setSupplierAlias(supplierMap.getOrDefault(item.getSupplierId(), new RemoteSupplierVo()).getAlias());
            });
        }
        return TableDataInfo.build(page);
    }

    /**
     * 根据缺货记录产生退款记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createBlameRecordByStockout(StockoutRecord stockoutRecord, List<StockoutRecordDetail> stockoutRecordDetails) {
        //获取销售批次商品信息
        RemoteSupplierSkuInfoVo skuInfoVo = remoteSupplierSkuService.getById(stockoutRecord.getSupplierSkuId());
        //获取总仓
        RemoteRegionWhVo remoteRegionWhVo = remoteRegionWhService.queryById(stockoutRecord.getRegionWhId());
        //获取订单项信息
        QueryDistributionBo queryDistributionBo = new QueryDistributionBo();
        List<Long> itemId = stockoutRecordDetails.stream().map(StockoutRecordDetail::getOrderItemId).toList();
        List<Long> orderId = stockoutRecordDetails.stream().map(StockoutRecordDetail::getOrderId).toList();
        queryDistributionBo.setOrderItemIdList(itemId);
        List<QueryDistributionVo> orderList = orderService.queryDistribution(queryDistributionBo);
        Map<Long, QueryDistributionVo> distributionVoMap = orderList.stream().collect(Collectors.toMap(QueryDistributionVo::getId, Function.identity()));
        //订单项map
        Map<Long, QueryDistributionItemVo> itemVoMap = orderList.stream().map(QueryDistributionVo::getOrderItemList).flatMap(List::stream).collect(Collectors.toMap(QueryDistributionItemVo::getId, Function.identity()));
        //退款记录根据订单维度创建
        Map<Long, List<StockoutRecordDetail>> map = stockoutRecordDetails.stream().collect(Collectors.groupingBy(StockoutRecordDetail::getOrderId));
        //订单项下退款单
        List<RefundProductDetail> refundByItemIdList = getRefundByItemIdList(itemId);
        Map<Long, List<RefundProductDetail>> refundMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(refundByItemIdList)) {
            refundMap = refundByItemIdList.stream().collect(Collectors.groupingBy(RefundProductDetail::getOrderItemId));
        }
        //获取订单退款信息
        LambdaQueryWrapper<RefundRecord> oqw = new LambdaQueryWrapper<>();
        oqw.in(CollectionUtil.isNotEmpty(orderId), RefundRecord::getOrderId, orderId).eq(RefundRecord::getDelFlag, 0);
        List<RefundRecord> refundRecords = baseMapper.selectList(oqw);
        //退款金额
        Map<Long, BigDecimal> refundAmountMap = new HashMap<>();
        //退金融服务费金额
        Map<Long, BigDecimal> refundServiceFeeMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(refundRecords)) {
            refundRecords.stream().collect(Collectors.groupingBy(RefundRecord::getOrderId)).forEach((k, v) -> {
                refundServiceFeeMap.put(k, v.stream().map(RefundRecord::getRefundFinancialServiceAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                refundAmountMap.put(k, v.stream().map(RefundRecord::getRefundAmount).reduce(BigDecimal.ZERO, BigDecimal::add).subtract(refundServiceFeeMap.get(k)));
            });
        }
        //获取供应商
        List<Long> list = new ArrayList<>(orderList.stream().map(QueryDistributionVo::getOrderItemList).flatMap(List::stream).map(QueryDistributionItemVo::getSupplierId).toList());
        List<RemoteSupplierVo> remoteSupplierVos = remoteSupplierService.getSupplierByIds(list);
        Map<Long, String> stringMap = remoteSupplierVos.stream().collect(Collectors.toMap(RemoteSupplierVo::getId, RemoteSupplierVo::getCode, (v1, v2) -> v1));
        List<RefundRecord> updateRecords = new ArrayList<>();
        List<RefundProductDetail> addRefundProductDetails = new ArrayList<>();
        for (Long k : map.keySet()){
            List<RefundProductDetail> refundProductDetails = new ArrayList<>();
            List<StockoutRecordDetail> v = map.get(k);
            RefundRecord record = new RefundRecord();
            QueryDistributionVo queryDistributionVo = distributionVoMap.get(k);
            record.setCode(generateRefundCode());
            record.setOrderId(k);
            record.setOrderCode(v.get(0).getOrderCode());
            record.setSourceCode(stockoutRecord.getCode());
            record.setCustomerId(v.get(0).getCustomerId());
            record.setCustomerName(v.get(0).getCustomerName());
            record.setCityWhId(stockoutRecord.getCityWhId());
            record.setRegionWhId(stockoutRecord.getRegionWhId());
            record.setPlaceId(queryDistributionVo.getPlaceId());
            record.setPlaceIdLevel2(queryDistributionVo.getPlaceIdLevel2());
            record.setPlacePath(queryDistributionVo.getPlacePath());
            if (stockoutRecord.getType().equals(BlameSourceTypeEnum.STOCKOUT.getCode())){
                record.setRefundType(RefundTypeEnum.LACK.getCode());
            }else {
                record.setRefundType(RefundTypeEnum.LESS.getCode());
            }
            record.setRefundStatus(RefundStatusEnum.IN_REFUND.getCode());
            record.setPayTime(v.get(0).getPayTime());
            record.setAuditStatus(1);
            baseMapper.insert(record);
            //创建退款单详情记录
            for (StockoutRecordDetail l : v){
                RefundProductDetail detail = new RefundProductDetail();
                QueryDistributionItemVo itemVo = itemVoMap.get(l.getOrderItemId());
                detail.setRefundRecordId(record.getId());
                detail.setOrderItemId(l.getOrderItemId());
                detail.setSupplierSkuId(stockoutRecord.getSupplierSkuId());
                detail.setSupplierSkuCode(stockoutRecord.getSupplierSkuCode());
                detail.setSaleDate(stockoutRecord.getSaleDate());
                detail.setSupplierId(stockoutRecord.getSupplierId());
                detail.setSupplierDeptId(stockoutRecord.getSupplierDeptId());
                detail.setOriginalSupplierId(stockoutRecord.getSupplierId());
                detail.setLogisticsId(stockoutRecord.getLogisticsId());
                detail.setLogisticsName(stockoutRecord.getLogisticsName());
                detail.setSupplierDeptId(stockoutRecord.getSupplierDeptId());
                detail.setSpuId(skuInfoVo.getSpuId());
                detail.setSpuName(l.getSpuName());
                detail.setSpuStandards(skuInfoVo.getSpuStandards());
                detail.setSpuGrossWeight(skuInfoVo.getSpuGrossWeight());
                detail.setSpuNetWeight(skuInfoVo.getSpuNetWeight());
                detail.setOrderCount(l.getOrderCount());
                detail.setStockoutCount(l.getStockoutCount());
                detail.setRefundRemark(stockoutRecord.getCreateRemark());
                //服务单价 服务费÷总数量
                BigDecimal servicePrice = itemVo.getPlatformServiceAmount().divide(new BigDecimal(l.getOrderCount()), 2, RoundingMode.FLOOR);
                detail.setCollectAgentPrice(servicePrice);
                detail.setOrderPrice(itemVo.getPrice());
                detail.setSettlePrice(itemVo.getFinalPrice());
                detail.setRefundStatus(RefundStatusEnum.IN_REFUND.getCode());
                detail.setProdType(itemVo.getProdType());
                if (stockoutRecord.getType().equals(BlameSourceTypeEnum.STOCKOUT.getCode())){
                    detail.setRefundType(RefundTypeEnum.LACK.getCode());
                }else {
                    detail.setRefundType(RefundTypeEnum.LESS.getCode());
                }
                detail.setImgUrl(itemVo.getImgUrl());
                List<RefundProductDetail> refundProductDetailList = refundMap.get(l.getOrderItemId());
                //缺货、少货的数量
                int refundStockoutCount = 0;
                //存在报损跟差额退顺序混乱的问题,这里先排除掉这两种情况的数量,再分别算出金额以免扣多了
                //差额退款
                BigDecimal difDetail =  BigDecimal.ZERO;
                //报损退款
                BigDecimal loss = BigDecimal.ZERO;
                //总退款
                BigDecimal totalRefundAmount = BigDecimal.ZERO;
                if (CollectionUtil.isNotEmpty(refundProductDetailList)){
                    refundStockoutCount = refundProductDetailList.stream()
                            .filter(productDetail -> !productDetail.getRefundType().equals(RefundTypeEnum.DIFFERENCE.getCode()))
                            .filter(productDetail -> !productDetail.getRefundType().equals(RefundTypeEnum.REPORT_LOSS.getCode()))
                            .mapToInt(RefundProductDetail::getStockoutCount).sum();
                    List<RefundProductDetail> detailList = refundProductDetailList.stream().filter(productDetail -> productDetail.getRefundType().equals(RefundTypeEnum.DIFFERENCE.getCode())).toList();
                    if (CollectionUtil.isNotEmpty(detailList)){
                        difDetail = detailList.stream().map(RefundProductDetail::getRefundAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                    List<RefundProductDetail> detailList1 = refundProductDetailList.stream().filter(productDetail -> productDetail.getRefundType().equals(RefundTypeEnum.REPORT_LOSS.getCode())).toList();
                    if (CollectionUtil.isNotEmpty(detailList1)){
                        loss = detailList1.stream().map(RefundProductDetail::getRefundAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                }
                if (difDetail.compareTo(BigDecimal.ZERO) > 0) {
                    detail.setRefundPriceType(RefundPriceTypeEnum.STTLE_PRICE.getCode());
                } else {
                    detail.setRefundPriceType(RefundPriceTypeEnum.ORDER_PRICE.getCode());
                }
                //缺货数量=订单项数量 全退  防止进行了差额退,减掉
                if (refundStockoutCount == 0 && l.getOrderCount().equals(l.getStockoutCount())){
                    //防止进行了差额退,减差额退减报损,实付=商品金额-优惠
                    detail.setRefundProductAmount(itemVo.getProductAmount().subtract(difDetail).subtract(loss).subtract(itemVo.getSubsidyFreeAmount()).subtract(itemVo.getProductFreeAmount()));
                    detail.setRefundSubsidyFreeAmount(itemVo.getSubsidyFreeAmount().add(itemVo.getSkuSubsidyAmount()));
                    detail.setRefundProductFreeAmount(itemVo.getProductFreeAmount());
                    detail.setRefundOtherAmount(itemVo.getTotalAmount().subtract(itemVo.getProductAmount()).add(itemVo.getSubsidyFreeAmount()).add(itemVo.getProductFreeAmount()));
                    detail.setRefundPlatformFreight(itemVo.getPlatformFreightAmount().subtract(itemVo.getPlatformFreightFreeAmount()));
                    detail.setRefundPlatformFreightAmountLevel2(itemVo.getPlatformFreightAmountLevel2().subtract(itemVo.getPlatformFreightFreeAmountLevel2()));
                    detail.setRefundBaseFreight(itemVo.getBaseFreightAmount().subtract(itemVo.getBaseFreightFreeAmount()));
                    detail.setRefundRegionFreight(itemVo.getRegionFreightAmount().subtract(itemVo.getRegionFreightFreeAmount()));
                    detail.setRefundFreightAmount(itemVo.getFreightTotalAmount());
                    detail.setRefundServiceAmount(itemVo.getPlatformServiceAmount().subtract(itemVo.getPlatformServiceFreeAmount()));
                    totalRefundAmount = totalRefundAmount.add(detail.getRefundProductAmount()).add(detail.getRefundOtherAmount());
                    getRefundFinancialServicePrice(refundAmountMap, refundServiceFeeMap, queryDistributionVo, l, detail,itemVo);
                } else if (refundStockoutCount != 0 && itemVo.getCount() == l.getStockoutCount() + refundStockoutCount) {
                    //缺货数量=订单项数量-已退数量 按剩下多少算  其他金额 订单项金额-已退金额
                    //计算获取各项已退金额
                    BigDecimal refundProduct = refundProductDetailList.stream().map(RefundProductDetail::getRefundProductAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal refundPlatformFreight = refundProductDetailList.stream().map(RefundProductDetail::getRefundPlatformFreight).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal refundPlatformFreightAmountLevel2 = refundProductDetailList.stream().map(RefundProductDetail::getRefundPlatformFreightAmountLevel2).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal refundBaseFreight = refundProductDetailList.stream().map(RefundProductDetail::getRefundBaseFreight).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal refundRegionFreight = refundProductDetailList.stream().map(RefundProductDetail::getRefundRegionFreight).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal refundServiceAmount = refundProductDetailList.stream().map(RefundProductDetail::getRefundServiceAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal refundFreightAmount = refundProductDetailList.stream().map(RefundProductDetail::getRefundFreightAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    //已退商品优惠金额
                    BigDecimal refundProductFreeAmount = refundProductDetailList.stream().map(RefundProductDetail::getRefundProductFreeAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    detail.setRefundProductFreeAmount(itemVo.getProductFreeAmount().subtract(refundProductFreeAmount));
                    //已退优惠金额
                    BigDecimal refundSubsidyFreeAmount = refundProductDetailList.stream().map(RefundProductDetail::getRefundSubsidyFreeAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    detail.setRefundSubsidyFreeAmount(itemVo.getSubsidyFreeAmount().add(itemVo.getSkuSubsidyAmount()).subtract(refundSubsidyFreeAmount));
                    //退商品金额 = 订单项纪录金额 - 优惠金额 - 已退商品金额
                    detail.setRefundProductAmount(itemVo.getProductAmount().subtract(refundProduct).subtract(itemVo.getSubsidyFreeAmount()).subtract(itemVo.getProductFreeAmount()));
                    //退非商品金额=非商品金额-退运费总额-退服务金额 (订单项非商品金额不包括金融服务费)
                    detail.setRefundOtherAmount(itemVo.getOtherTotalAmount().subtract(itemVo.getFreeTotalAmount()).subtract(refundFreightAmount).subtract(refundServiceAmount));
                    detail.setRefundPlatformFreight(itemVo.getPlatformFreightAmount().subtract(itemVo.getPlatformFreightFreeAmount()).subtract(refundPlatformFreight));
                    detail.setRefundPlatformFreightAmountLevel2(itemVo.getPlatformFreightAmountLevel2().subtract(itemVo.getPlatformFreightFreeAmountLevel2()).subtract(refundPlatformFreightAmountLevel2));
                    detail.setRefundBaseFreight(itemVo.getBaseFreightAmount().subtract(itemVo.getBaseFreightFreeAmount()).subtract(refundBaseFreight));
                    detail.setRefundRegionFreight(itemVo.getRegionFreightAmount().subtract(itemVo.getRegionFreightFreeAmount()).subtract(refundRegionFreight));
                    detail.setRefundFreightAmount(itemVo.getFreightTotalAmount().subtract(refundFreightAmount));
                    detail.setRefundServiceAmount(itemVo.getPlatformServiceAmount().subtract(itemVo.getPlatformServiceFreeAmount()).subtract(refundServiceAmount));
                    totalRefundAmount = totalRefundAmount.add(detail.getRefundProductAmount()).add(detail.getRefundOtherAmount());
                    getRefundFinancialServicePrice(refundAmountMap, refundServiceFeeMap, queryDistributionVo, l, detail,itemVo);
                }else {
                    //缺货数量<订单项数量-已退数量(有的话) 按比例算
                    //缺货少货 非商品金额按比例退,先算比例,再按订单项记录金额进行计算赋值
                    BigDecimal rat = new BigDecimal(l.getStockoutCount()).divide(new BigDecimal(itemVo.getCount()), 2, RoundingMode.HALF_UP);
                    BigDecimal subFreeAmount = (itemVo.getSubsidyFreeAmount().add(itemVo.getSkuSubsidyAmount())).multiply(rat).setScale(2, RoundingMode.HALF_UP);
                    BigDecimal proFreeAmount = itemVo.getProductFreeAmount().multiply(rat).setScale(2, RoundingMode.HALF_UP);
                    //获取剩余实付商品金额(结算价*数量-优惠加)  退不能超过剩余金额
                    BigDecimal subtract = itemVo.getProductAmount().subtract(loss).subtract(difDetail).subtract(itemVo.getSubsidyFreeAmount()).subtract(itemVo.getProductFreeAmount());
                    //单价*数量-按比例得出的优惠金额 跟剩余实付金额比较,取小的值
                    detail.setRefundProductAmount(itemVo.getFinalPrice().multiply(new BigDecimal(l.getStockoutCount())).subtract(subFreeAmount).subtract(proFreeAmount).compareTo(subtract) > 0
                            ? subtract : itemVo.getFinalPrice().multiply(new BigDecimal(l.getStockoutCount())).subtract(subFreeAmount).subtract(proFreeAmount));
                    detail.setRefundPlatformFreight((itemVo.getPlatformFreightAmount().subtract(itemVo.getPlatformFreightFreeAmount())).multiply(rat).setScale(2, RoundingMode.HALF_UP));
                    detail.setRefundPlatformFreightAmountLevel2((itemVo.getPlatformFreightAmountLevel2().subtract(itemVo.getPlatformFreightFreeAmountLevel2())).multiply(rat).setScale(2, RoundingMode.HALF_UP));
                    detail.setRefundBaseFreight((itemVo.getBaseFreightAmount().subtract(itemVo.getBaseFreightFreeAmount())).multiply(rat).setScale(2, RoundingMode.HALF_UP));
                    detail.setRefundRegionFreight((itemVo.getRegionFreightAmount().subtract(itemVo.getRegionFreightFreeAmount())).multiply(rat).setScale(2, RoundingMode.HALF_UP));
                    //运费总和
                    detail.setRefundFreightAmount(detail.getRefundPlatformFreight().add(detail.getRefundBaseFreight())
                            .add(detail.getRefundRegionFreight()).add(detail.getRefundPlatformFreightAmountLevel2()));
                    detail.setRefundServiceAmount(itemVo.getPlatformServiceAmount().multiply(rat).setScale(2, RoundingMode.HALF_UP));
                    //退非商品金额 = 退运费总和+退金融服务费+退代采服务费
                    detail.setRefundOtherAmount(detail.getRefundFreightAmount().add(detail.getRefundServiceAmount()));
                    totalRefundAmount = totalRefundAmount.add(detail.getRefundProductAmount()).add(detail.getRefundOtherAmount());
                    //退优惠金额
                    detail.setRefundSubsidyFreeAmount(subFreeAmount);
                    detail.setRefundProductFreeAmount(proFreeAmount);
                    //退金融服务费
                    BigDecimal apportionTotalAmount = queryDistributionVo.getTotalAmount().subtract(queryDistributionVo.getFinancialServiceAmount());
                    //退款金额
                    BigDecimal detailRefundAmount = detail.getRefundProductAmount().add(detail.getRefundOtherAmount());
                    BigDecimal refundServiceFee = refundServiceFeeMap.get(l.getOrderId()) == null ? BigDecimal.ZERO : refundServiceFeeMap.get(l.getOrderId());
                    //计算金融手续费
                    BigDecimal refundFinancialPrice = getApportionRefundService(apportionTotalAmount,itemVo.getFinancialServiceAmount(),detailRefundAmount,refundServiceFee);
                    detail.setRefundFinancialServicePrice(refundFinancialPrice);
                    detail.setRefundOtherAmount(detail.getRefundOtherAmount().add(refundFinancialPrice));
                }
                detail.setRefundAmount(detail.getRefundProductAmount().add(detail.getRefundOtherAmount()));
                refundProductDetails.add(detail);
                if (refundAmountMap.containsKey(l.getOrderId())) {
                    refundAmountMap.put(l.getOrderId(), refundAmountMap.get(l.getOrderId()).add(totalRefundAmount));
                }else {
                    refundAmountMap.put(l.getOrderId(), totalRefundAmount);
                }
            }
            //计算退商品金额
            record.setRefundProductAmount(refundProductDetails.stream().map(RefundProductDetail::getRefundProductAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            //非商品金额(包括金融手续费)
            record.setRefundOtherAmount(refundProductDetails.stream().map(RefundProductDetail::getRefundOtherAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            //退平台补贴优惠商品金额
            record.setRefundSubsidyFreeAmount(refundProductDetails.stream().map(RefundProductDetail::getRefundSubsidyFreeAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            record.setRefundProductFreeAmount(refundProductDetails.stream().map(RefundProductDetail::getRefundProductFreeAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            record.setRefundAmount(record.getRefundOtherAmount().add(record.getRefundProductAmount()));
            //维护退金融服务费
            record.setRefundFinancialServiceAmount((refundProductDetails.stream().map(RefundProductDetail::getRefundFinancialServicePrice).reduce(BigDecimal.ZERO, BigDecimal::add)));
            OrderRefundBo orderRefundBo = new OrderRefundBo();
            orderRefundBo.setOrderId(k).setOrderNo(record.getOrderCode()).setRefundId(record.getId()).setRefundNo(record.getCode())
                    .setRefundAmt(record.getRefundProductAmount().add(record.getRefundOtherAmount()));
            if (record.getRefundAmount().compareTo(audiAmount) < 0) {
                orderRefundBo.setInfAuto(YNStatusEnum.ENABLE.getCode());
            } else {
                orderRefundBo.setInfAuto(YNStatusEnum.DISABLE.getCode());
                record.setAuditStatus(0);
            }
            if (stockoutRecord.getType().equals(1)) {
                orderRefundBo.setBusiType(RefundBusiTypeEnum.REFUND_SHORT.getCode());
            } else {
                orderRefundBo.setBusiType(RefundBusiTypeEnum.REFUND_LACK.getCode());
            }
            List<OrderPaySplitBo> splits = new ArrayList<>();
            //商品金额 扣供应商仓
            if (record.getRefundProductAmount().compareTo(BigDecimal.ZERO) > 0) {
                Map<Long, List<RefundProductDetail>> collect = refundProductDetails.stream().collect(Collectors.groupingBy(RefundProductDetail::getSupplierId));
                collect.forEach((supId, details) -> {
                    Map<Long, List<RefundProductDetail>> listMap = details.stream().collect(Collectors.groupingBy(RefundProductDetail::getSupplierSkuId));
                    listMap.forEach((skuId, spuDetails) -> {
                        BigDecimal reduce = spuDetails.stream().map(RefundProductDetail::getRefundProductAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                        if (reduce.compareTo(BigDecimal.ZERO) > 0) {
                            OrderPaySplitBo split = new OrderPaySplitBo();
                            split.setOrgId(supId).setOrgType(AccountOrgTypeEnum.SUPPLIER.getCode()).setOrgCode(stringMap.get(supId))
                                    .setTransAmt(reduce).setSkuId(skuId).setSplitAmt(reduce).setDeptId(spuDetails.get(0).getSupplierDeptId());
                            splits.add(split);
                        }
                    });
                });
            }
            //非商品金额 扣总仓
            if (record.getRefundOtherAmount().compareTo(BigDecimal.ZERO) > 0) {
                OrderPaySplitBo split = new OrderPaySplitBo();
                split.setOrgId(record.getRegionWhId()).setOrgType(AccountOrgTypeEnum.REGION_WH.getCode()).setOrgCode(remoteRegionWhVo.getRegionWhCode())
                        .setTransAmt(record.getRefundOtherAmount()).setSplitAmt(record.getRefundOtherAmount());
                splits.add(split);
            }
            if (CollectionUtil.isNotEmpty(splits)) {
                orderRefundBo.setSplits(splits);
                //都成功,退款成功改状态
                record.setRefundStatus(RefundStatusEnum.IN_REFUND.getCode());
                refundProductDetails.forEach(l -> {
                    l.setRefundStatus(RefundStatusEnum.IN_REFUND.getCode());
                    l.setRefundTime(null);
                });
                stockoutRecord.setRefundStatus(StockOutRefundStatusEnum.UN_REFUND.getCode());
                //真.调用退款了
                try {
                    orderRefundBo.setIsOccupy(OccupyEnum.NOT.getCode());
                    log.keyword("stockoutRefund", record.getCode(), record.getOrderId()).info("缺货少货调用退款入参【{}】", orderRefundBo);
                    remotePaymentService.refund(orderRefundBo);
                } catch (ServiceException e) {
                    record.setRefundStatus(RefundStatusEnum.REFUND_FAIL.getCode());
                    refundProductDetails.forEach(l -> l.setRefundStatus(RefundStatusEnum.REFUND_FAIL.getCode()));
                    log.keyword("stockoutRefund", record.getCode(), record.getOrderId()).error("缺货少货退款失败:", e);
                    record.setRemark("退款失败原因：" +e.getMessage());
                }
            }
            updateRecords.add(record);
            addRefundProductDetails.addAll(refundProductDetails);
        }
        baseMapper.updateBatchById(updateRecords);
        if(CollectionUtil.isNotEmpty(addRefundProductDetails)){
            refundProductDetailMapper.insertBatch(addRefundProductDetails);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean stockoutOccupy(List<StockoutRecordDetail> details) {
        Long itemId = details.get(0).getOrderItemId();
        log.keyword("stockoutOccupy", itemId).info("缺货少货单占用金额，itemId:{}, details :【{}】", itemId, details);
        //同一itemId，多条details，意味着这些记录分别属于各自的缺货、少货单
        Map<StockoutRecord, StockoutRecordDetail> stockoutRecordMap = new HashMap<>();
        for (StockoutRecordDetail detail : details) {
            StockoutRecord stockoutRecord = stockoutRecordMapper.selectById(detail.getStockoutRecordId());
            RefundRecord refundRecord = baseMapper.selectByExpand(stockoutRecord.getCode(), StrUtil.format(stockoutExpandFormat, stockoutRecord.getCode(), detail.getOrderItemId()));
            if (refundRecord != null) {
                //如果已经占用则不处理
                continue;
            }
            if (detail.getStockoutCount() == 0) {
                //如果缺货数量为0，则不处理  异常情况
                log.warn("缺货少货占用数量为0，不处理");
                continue;
            }
            stockoutRecordMap.put(stockoutRecord, detail);
        }
        if (stockoutRecordMap.isEmpty()) {
            return Boolean.TRUE;
        }

        OrderItem orderItem = orderItemMapper.selectById(details.get(0).getOrderItemId());
        Order order = orderMapper.selectById(orderItem.getOrderId());
        //获取销售批次商品信息
        RemoteSupplierSkuInfoVo skuInfo = remoteSupplierSkuService.getById(orderItem.getSupplierSkuId());
        //获取总仓
        RemoteRegionWhVo remoteRegionWhVo = remoteRegionWhService.queryById(orderItem.getRegionWhId());
        RemoteSupplierVo remoteSupplierVo = remoteSupplierService.getSupplierById(orderItem.getSupplierId());

        List<Long> errorList = new ArrayList<>();
        for (Map.Entry<StockoutRecord, StockoutRecordDetail> entry : stockoutRecordMap.entrySet()) {

            //获取订单退款信息
            LambdaQueryWrapper<RefundRecord> oqw = new LambdaQueryWrapper<>();
            oqw.in(RefundRecord::getOrderId, Lists.newArrayList(order.getId())).eq(RefundRecord::getDelFlag, 0);
            List<RefundRecord> refundRecords = baseMapper.selectList(oqw);
            //订单退款金额
            Map<Long, BigDecimal> refundAmountMap = new HashMap<>();
            //订单退金融服务费金额
            Map<Long, BigDecimal> refundServiceFeeMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(refundRecords)) {
                refundRecords.stream().collect(Collectors.groupingBy(RefundRecord::getOrderId)).forEach((k, v) -> {
                    refundServiceFeeMap.put(k, v.stream().map(RefundRecord::getRefundFinancialServiceAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                    refundAmountMap.put(k, v.stream().map(RefundRecord::getRefundAmount).reduce(BigDecimal.ZERO, BigDecimal::add).subtract(refundServiceFeeMap.get(k)));
                });
            }
            //所有未审和已退的退款
            List<RefundProductDetail> refundList = getRefundByItemIdList(Lists.newArrayList(orderItem.getId()));

            StockoutRecord stockoutRecord = entry.getKey();
            StockoutRecordDetail stockoutRecordDetail = entry.getValue();
            BlameSourceTypeEnum blameSourceTypeEnum = BlameSourceTypeEnum.loadByCode(stockoutRecordDetail.getType());

            //创建退款记录

            Pair<RefundRecord,RefundProductDetail> createDto = this.stockoutCreateRefund(stockoutRecord, stockoutRecordDetail, order, orderItem, refundList, OccupyEnum.YES,
                    refundServiceFeeMap.getOrDefault(orderItem.getOrderId(), BigDecimal.ZERO), refundAmountMap.getOrDefault(orderItem.getOrderId(), BigDecimal.ZERO));
            RefundRecord record = createDto.getKey();
            record.setExpand(StrUtil.format(stockoutExpandFormat, stockoutRecord.getCode(), stockoutRecordDetail.getOrderItemId()));
            record.setOccupyStatus(OccupyEnum.YES.getCode());
            RefundProductDetail refundProductDetail = createDto.getValue();
            baseMapper.insert(record);
            refundProductDetail.setRefundRecordId(record.getId());
            refundProductDetail.setSpuStandards(skuInfo.getSpuStandards());
            refundProductDetailMapper.insert(refundProductDetail);
            //根据退款记录和退款商品明细构建退款入参
            OrderRefundBo orderRefundBo = this.buildOrderRefundByStockout(record, refundProductDetail, blameSourceTypeEnum, remoteSupplierVo, skuInfo, remoteRegionWhVo.getRegionWhCode());
            if (CollUtil.isEmpty(orderRefundBo.getSplits())) {
                continue;
            }
            orderRefundBo.setIsOccupy(OccupyEnum.YES.getCode());
            orderRefundBo.setInfAuto(YNStatusEnum.ENABLE.getCode());
            String cacheKey = OrderCacheNames.SETTLE_OCCUPY_STOCKOUT + record.getId();
            RedisUtils.setCacheObject(cacheKey, orderRefundBo, Duration.ofDays(60));
            // 在事务提交后调用
            executeAfterTransaction(() -> {
                try {
                    //调用真实退款
                    log.keyword("stockoutOccupy").info("占用金额:{}", orderRefundBo);
                    remotePaymentService.refund(orderRefundBo);
                } catch (Exception e) {
                    RedisUtils.deleteObject(cacheKey);
                    log.keyword("stockoutOccupy").error("缺货少货占用退款失败:", e);
                    errorList.add(record.getId());
                    record.setRemark("退款失败原因：" +e.getMessage());
                    //如果占用失败就删除记录
                    baseMapper.deleteBatchIds(errorList);
                    refundProductDetailMapper.deleteByRefundIds(errorList);
                }
            });
        }
        return errorList.isEmpty();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void stockoutConfirm(StockoutRecord stockoutRecord, List<StockoutRecordDetail> details, Integer infAuto) {
        log.keyword("stockoutConfirm", stockoutRecord.getSupplierSkuId(), stockoutRecord.getCode()).info("缺货少货单确认退款,details:{},", details);
        List<Long> releaseRefundIds = new ArrayList<>();
        Map<StockoutRecordDetail, OrderRefundBo> releaseMap = new HashMap<>();
        List<StockoutRecordDetail> insertList = new ArrayList<>();
        List<Long> itemIdList = new ArrayList<>();
        List<Long> orderIdList = new ArrayList<>();
        for (StockoutRecordDetail detail : details) {
            orderIdList.add(detail.getOrderId());
            itemIdList.add(detail.getOrderItemId());
            //1.先校验这个缺货少货的item是否有退款单，没有退款单的情况下直接新增普通退款
            RefundRecord refundRecord = baseMapper.selectBySourceAndItemId(stockoutRecord.getCode(), detail.getOrderItemId());
            if (refundRecord == null) {
                insertList.add(detail);
                continue;
            }
            //2.有退款单的情况下，判断是否为占用，如果为占用则释放占用，否则continue
            RefundRecord occupyRecord = baseMapper.selectByExpand(stockoutRecord.getCode(), StrUtil.format(stockoutExpandFormat, stockoutRecord.getCode(), detail.getOrderItemId()));
            if (occupyRecord != null) {
                releaseMap.put(detail, RedisUtils.getCacheObject(OrderCacheNames.SETTLE_OCCUPY_STOCKOUT + refundRecord.getId()));
                log.info("释放占用，itemId【{}】", detail.getOrderItemId());
                continue;
            }
            log.info("缺货少货单确认退款，itemId【{}】,已存在退款记录且不是占用，属于重复退款，不处理", detail.getOrderItemId());
        }
        BlameSourceTypeEnum blameSourceTypeEnum = BlameSourceTypeEnum.loadByCode(stockoutRecord.getType());
        RemoteSupplierVo remoteSupplierVo = remoteSupplierService.getSupplierById(stockoutRecord.getSupplierId());
        //获取销售批次商品信息
        RemoteSupplierSkuInfoVo skuInfo = remoteSupplierSkuService.getById(stockoutRecord.getSupplierSkuId());

        Map<Long, OrderItem> itemMap = orderItemMapper.selectBatchIds(itemIdList).stream().collect(Collectors.toMap(OrderItem::getId, Function.identity()));
        Map<Long, Order> orderMap = orderMapper.selectBatchIds(orderIdList).stream().collect(Collectors.toMap(Order::getId, Function.identity()));

        //获取总仓
        RemoteRegionWhVo remoteRegionWhVo = remoteRegionWhService.queryById(stockoutRecord.getRegionWhId());

        if (!CollUtil.isEmpty(insertList)) {
            //所有未审和已退的退款
            List<RefundProductDetail> refundList = getRefundByItemIdList(itemIdList);
            Map<Long, List<RefundProductDetail>> refundMap = refundList.stream().collect(Collectors.groupingBy(RefundProductDetail::getOrderItemId));

            //新增普通退款
            for (StockoutRecordDetail stockoutRecordDetail : insertList) {
                //获取订单退款信息
                LambdaQueryWrapper<RefundRecord> oqw = new LambdaQueryWrapper<>();
                oqw.in(RefundRecord::getOrderId, orderIdList).eq(RefundRecord::getDelFlag, 0);
                List<RefundRecord> refundRecords = baseMapper.selectList(oqw);
                //订单退款金额
                Map<Long, BigDecimal> refundAmountMap = new HashMap<>();
                //订单退金融服务费金额
                Map<Long, BigDecimal> refundServiceFeeMap = new HashMap<>();
                if (CollectionUtil.isNotEmpty(refundRecords)) {
                    refundRecords.stream().collect(Collectors.groupingBy(RefundRecord::getOrderId)).forEach((k, v) -> {
                        refundServiceFeeMap.put(k, v.stream().map(RefundRecord::getRefundFinancialServiceAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                        refundAmountMap.put(k, v.stream().map(RefundRecord::getRefundAmount).reduce(BigDecimal.ZERO, BigDecimal::add).subtract(refundServiceFeeMap.get(k)));
                    });
                }

                Order order = orderMap.get(stockoutRecordDetail.getOrderId());
                OrderItem orderItem = itemMap.get(stockoutRecordDetail.getOrderItemId());
                //创建退款记录
                Pair<RefundRecord, RefundProductDetail> createDto = this.stockoutCreateRefund(stockoutRecord, stockoutRecordDetail, order, orderItem,
                        refundMap.getOrDefault(orderItem.getId(), new ArrayList<>()), OccupyEnum.YES, refundServiceFeeMap.getOrDefault(orderItem.getOrderId(), BigDecimal.ZERO), refundAmountMap.getOrDefault(orderItem.getOrderId(), BigDecimal.ZERO));
                RefundRecord record = createDto.getKey();
                RefundProductDetail refundProductDetail = createDto.getValue();
                //入参指定了要人工审核就强制人工审核，最高优先级
                if (infAuto != null && infAuto.equals(YNStatusEnum.DISABLE.getCode())) {
                    record.setAuditStatus(infAuto);
                }
                baseMapper.insert(record);
                refundProductDetail.setRefundRecordId(record.getId());
                refundProductDetail.setSpuStandards(skuInfo.getSpuStandards());
                refundProductDetailMapper.insert(refundProductDetail);
                //根据退款记录和退款商品明细构建退款入参
                OrderRefundBo orderRefundBo = this.buildOrderRefundByStockout(record, refundProductDetail, blameSourceTypeEnum, remoteSupplierVo, skuInfo, remoteRegionWhVo.getRegionWhCode());
                if (CollUtil.isEmpty(orderRefundBo.getSplits())) {
                    log.error("退款明细为空");
                    continue;
                }
                //入参指定了要人工审核就强制人工审核，最高优先级
                if (infAuto != null && infAuto.equals(YNStatusEnum.DISABLE.getCode())) {
                    orderRefundBo.setInfAuto(infAuto);
                }
                // 在事务提交后调用
                executeAfterTransaction(() -> {
                    try {
                        //调用真实退款
                        orderRefundBo.setIsOccupy(OccupyEnum.NOT.getCode());
                        log.keyword("stockoutConfirm").info("缺货少货普通退款：{}", orderRefundBo);
                        remotePaymentService.refund(orderRefundBo);
                    } catch (Exception e) {
                        log.keyword("stockoutConfirm").error("缺货少货普通退款失败:", e);
                        record.setRemark("退款失败原因：" +e.getMessage());
                        baseMapper.refundFail(Lists.newArrayList(orderRefundBo.getRefundId()));
                        refundProductDetailMapper.refundFail(Lists.newArrayList(orderRefundBo.getRefundId()));
                    }
                });
                BigDecimal commissionAmt = orderRefundBo.getSplits().stream().map(OrderPaySplitBo::getCommissionAmt).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                BigDecimal commissionTaxAmt = orderRefundBo.getSplits().stream().map(OrderPaySplitBo::getCommSalaryAmt).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                if (commissionAmt.compareTo(BigDecimal.ZERO) > 0){
                    distributionService.refundOrderItem(record.getOrderId(), refundProductDetail.getOrderItemId(), commissionTaxAmt, commissionAmt.subtract(commissionTaxAmt));
                }
                if (orderItem.getSettleCount() > 0 && stockoutRecordDetail.getType().equals(2)
                        && record.getRefundSubsidyFreeAmount().compareTo(BigDecimal.ZERO) > 0) {
                    createDeductionInfo(orderItem,record.getRefundSubsidyFreeAmount());
                }
            }
        }

        List<RefundRecord> updateRecords = new ArrayList<>();
        List<RefundProductDetail> updateProductDetails = new ArrayList<>();

        if (CollUtil.isNotEmpty(releaseMap)) {
            //所有未审和已退的退款
            List<RefundProductDetail> refundList = getRefundByItemIdList(itemIdList);
            Map<Long, List<RefundProductDetail>> refundMap = refundList.stream().collect(Collectors.groupingBy(RefundProductDetail::getOrderItemId));

            //把占用的退款释放占用，重新计算金额
            for (Map.Entry<StockoutRecordDetail, OrderRefundBo> stockoutRecordDetailOrderRefundBoEntry : releaseMap.entrySet()) {
                StockoutRecordDetail stockoutRecordDetail = stockoutRecordDetailOrderRefundBoEntry.getKey();
                OrderRefundBo oldRefundBo = stockoutRecordDetailOrderRefundBoEntry.getValue();
                oldRefundBo.setIsOccupy(OccupyEnum.RELEASE.getCode());

                Long refundId = oldRefundBo.getRefundId();
                Long refundProductDetailId = refundProductDetailMapper.selectIdByRefundId(oldRefundBo.getRefundId());
                String refundNo = oldRefundBo.getRefundNo();
                var refundProductDetails = refundMap.getOrDefault(stockoutRecordDetail.getOrderItemId(), new ArrayList<>());
                refundProductDetails = refundProductDetails.stream().filter(i -> !Objects.equals(i.getId(), refundProductDetailId)).collect(Collectors.toList());
                refundMap.put(stockoutRecordDetail.getOrderItemId(), refundProductDetails);

                //查询本订单的退款信息,排出当前退款记录
                LambdaQueryWrapper<RefundRecord> oqw = new LambdaQueryWrapper<>();
                oqw.eq(RefundRecord::getOrderId, stockoutRecordDetail.getOrderId())
                        .eq(RefundRecord::getDelFlag, 0)
                        .ne(RefundRecord::getId, refundId);

                List<RefundRecord> refundRecords = baseMapper.selectList(oqw);
                //订单退款金额
                Map<Long, BigDecimal> refundAmountMap = new HashMap<>();
                //订单退金融服务费金额
                Map<Long, BigDecimal> refundServiceFeeMap = new HashMap<>();
                if (CollectionUtil.isNotEmpty(refundRecords)) {
                    refundRecords.stream().collect(Collectors.groupingBy(RefundRecord::getOrderId)).forEach((k, v) -> {
                        refundServiceFeeMap.put(k, v.stream().map(RefundRecord::getRefundFinancialServiceAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                        refundAmountMap.put(k, v.stream().map(RefundRecord::getRefundAmount).reduce(BigDecimal.ZERO, BigDecimal::add).subtract(refundServiceFeeMap.get(k)));
                    });
                }

                Order order = orderMap.get(stockoutRecordDetail.getOrderId());
                OrderItem orderItem = itemMap.get(stockoutRecordDetail.getOrderItemId());

                Pair<RefundRecord, RefundProductDetail> createDto = this.stockoutCreateRefund(stockoutRecord, stockoutRecordDetail, order, orderItem,
                        refundMap.getOrDefault(orderItem.getId(), new ArrayList<>()), OccupyEnum.YES, refundServiceFeeMap.getOrDefault(orderItem.getOrderId(), BigDecimal.ZERO), refundAmountMap.getOrDefault(orderItem.getOrderId(), BigDecimal.ZERO));
                RefundRecord newRecord = createDto.getKey();
                newRecord.setOccupyStatus(OccupyEnum.RELEASE.getCode());
                RefundProductDetail newProductDetail = createDto.getValue();

                OrderRefundBo newRefundBo = this.buildOrderRefundByStockout(newRecord, newProductDetail, blameSourceTypeEnum, remoteSupplierVo, skuInfo, remoteRegionWhVo.getRegionWhCode());
                if (CollUtil.isEmpty(newRefundBo.getSplits())) {
                    //不退钱
                    oldRefundBo.setRefundAmt(BigDecimal.ZERO);
                    oldRefundBo.setInfAuto(YNStatusEnum.ENABLE.getCode());
                    for (OrderPaySplitBo split : oldRefundBo.getSplits()) {
                        split.setSplitAmt(BigDecimal.ZERO);
                        split.setTransAmt(BigDecimal.ZERO);
                    }
                    // 在事务提交后调用
                    executeAfterTransaction(() -> {
                        try {
                            log.keyword("stockoutConfirm").info("退款明细有变化，释放退款：{}", oldRefundBo);
                            remotePaymentService.refund(oldRefundBo);
                            releaseRefundIds.add(oldRefundBo.getRefundId());
                        } catch (Exception e) {
                            log.keyword("stockoutConfirm").error("缺货少货占用释放失败:", e);
                            baseMapper.refundFail(Lists.newArrayList(oldRefundBo.getRefundId()));
                            refundProductDetailMapper.refundFail(Lists.newArrayList(oldRefundBo.getRefundId()));
                        }
                    });
                    newRecord.setId(refundId);
                    newRecord.setAuditStatus(AuditEnum.PASS.getCode());
                    newRecord.setCode(refundNo);
                    newProductDetail.setId(refundProductDetailId);
                    newProductDetail.setRefundRecordId(refundId);
                    updateRecords.add(newRecord);
                    updateProductDetails.add(newProductDetail);
                } else {
                    oldRefundBo.setSplits(newRefundBo.getSplits());
                    oldRefundBo.setRefundAmt(newRefundBo.getRefundAmt());
                    if (oldRefundBo.getRefundAmt().compareTo(audiAmount) < 0) {
                        oldRefundBo.setInfAuto(YNStatusEnum.ENABLE.getCode());
                        newRecord.setAuditStatus(AuditEnum.PASS.getCode());
                    } else {
                        oldRefundBo.setInfAuto(YNStatusEnum.DISABLE.getCode());
                    }
                    //入参指定了要人工审核就强制人工审核，最高优先级
                    if (infAuto != null && infAuto.equals(YNStatusEnum.DISABLE.getCode())) {
                        oldRefundBo.setInfAuto(infAuto);
                    }
                    // 在事务提交后调用
                    executeAfterTransaction(() -> {
                        try {
                            log.keyword("stockoutConfirm").info("退款明细有变化，重新计算退款金额并退款：{}", oldRefundBo);
                            remotePaymentService.refund(oldRefundBo);
                        } catch (Exception e) {
                            log.keyword("stockoutConfirm").error("缺货少货占用释放失败:", e);
                            baseMapper.refundFail(Lists.newArrayList(oldRefundBo.getRefundId()));
                            refundProductDetailMapper.refundFail(Lists.newArrayList(oldRefundBo.getRefundId()));
                        }
                    });
                    newRecord.setId(refundId);
                    newRecord.setCode(refundNo);
                    //入参指定了要人工审核就强制人工审核，最高优先级
                    if (infAuto != null && infAuto.equals(YNStatusEnum.DISABLE.getCode())) {
                        newRecord.setAuditStatus(infAuto);
                    }
                    newProductDetail.setId(refundProductDetailId);
                    newProductDetail.setRefundRecordId(refundId);
                    updateRecords.add(newRecord);
                    updateProductDetails.add(newProductDetail);
                    BigDecimal commissionAmt = oldRefundBo.getSplits().stream().map(OrderPaySplitBo::getCommissionAmt).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                    BigDecimal commissionTaxAmt = oldRefundBo.getSplits().stream().map(OrderPaySplitBo::getCommSalaryAmt).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                    if (commissionAmt.compareTo(BigDecimal.ZERO) > 0){
                        distributionService.refundOrderItem(newRecord.getOrderId(), newProductDetail.getOrderItemId(), commissionTaxAmt, commissionAmt.subtract(commissionTaxAmt));
                    }
                }
                if (orderItem.getSettleCount() > 0 && stockoutRecordDetail.getType().equals(2)
                        && newRecord.getRefundSubsidyFreeAmount().compareTo(BigDecimal.ZERO) > 0) {
                    createDeductionInfo(orderItem, newRecord.getRefundSubsidyFreeAmount());
                }
            }
        }
        if (CollUtil.isNotEmpty(updateRecords)) {
            baseMapper.updateBatchById(updateRecords);
        }
        if (CollUtil.isNotEmpty(updateProductDetails)) {
            refundProductDetailMapper.updateBatchById(updateProductDetails);
        }
        if (CollUtil.isNotEmpty(releaseRefundIds)) {
            baseMapper.releaseRefund(releaseRefundIds);
            refundProductDetailMapper.releaseRefund(releaseRefundIds);
            var upList = releaseRefundIds.stream().map(releaseRefundId -> {
                RefundRecord upRefund = new RefundRecord();
                upRefund.setId(releaseRefundId);
                upRefund.setAuditStatus(AuditEnum.PASS.getCode());
                return upRefund;
            }).toList();
            baseMapper.updateBatchById(upList);
        }
//        if (!errorList.isEmpty()) {
//            baseMapper.refundFail(errorList);
//            refundProductDetailMapper.refundFail(errorList);
//        }
//
        log.keyword("stockoutConfirm").info("缺货少货确认 调用退款前操作已结束" );
    }

    /**
     * 通用方法，在事务提交后执行指定的操作
     */
    private void executeAfterTransaction(Runnable task) {
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                task.run();
            }
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void stockoutInvalid(StockoutRecord stockoutRecord, List<StockoutRecordDetail> details, Map<Long, Integer> newCountMap) {
        log.keyword(stockoutRecord.getCode()).info("缺货少货单作废，释放占用退款,details:{}", details);

        List<Long> releaseRefundIds = new ArrayList<>();
        for (StockoutRecordDetail detail : details) {
            RefundRecord refundRecord = baseMapper.selectByExpand(stockoutRecord.getCode(), StrUtil.format(stockoutExpandFormat, stockoutRecord.getCode(), detail.getOrderItemId()));
            if (refundRecord != null) {
                String cacheKey = OrderCacheNames.SETTLE_OCCUPY_STOCKOUT + refundRecord.getId();
                OrderRefundBo refundBo = RedisUtils.getCacheObject(cacheKey);
                refundBo.setIsOccupy(OccupyEnum.RELEASE.getCode());
                refundBo.setRefundAmt(BigDecimal.ZERO);
                for (OrderPaySplitBo split : refundBo.getSplits()) {
                    split.setSplitAmt(BigDecimal.ZERO);
                    split.setTransAmt(BigDecimal.ZERO);
                }
                try {
                    log.info("作废占用，释放占用金额{}", refundBo);
                    RefundRecord update = new RefundRecord();
                    update.setId(refundRecord.getId());
                    update.setOccupyStatus(OccupyEnum.RELEASE.getCode());
                    baseMapper.updateById(update);

                    remotePaymentService.refund(refundBo);
                    releaseRefundIds.add(refundBo.getRefundId());
                    RedisUtils.deleteObject(cacheKey);
                } catch (Exception e) {
                    RedisUtils.setCacheObject(cacheKey, refundBo, Duration.ofDays(60));
                    log.keyword("stockoutConfirmed").error("缺货少货占用释放失败:", e);
                }
                if (newCountMap != null && newCountMap.containsKey(detail.getId())) {
                    LambdaUpdateWrapper<RefundProductDetail> luw = new LambdaUpdateWrapper<>();
                    luw.eq(RefundProductDetail::getRefundRecordId, refundRecord.getId())
                            .set(RefundProductDetail::getStockoutCount, newCountMap.get(detail.getId()));
                    refundProductDetailMapper.update(luw);
                }
            }
        }
        if (!releaseRefundIds.isEmpty()) {
            baseMapper.releaseRefund(releaseRefundIds);
            refundProductDetailMapper.releaseRefund(releaseRefundIds);
        }
    }

    @Lock4j(keys = "#stockoutRecord.regionWhId", expire = 10000)
    @Override
    public void shortageRefund(StockoutRecord stockoutRecord, List<StockoutRecordDetail> stockoutRecordDetailList) {
        //默认自动退款，不需要人工审核
        Integer infAuto = YNStatusEnum.ENABLE.getCode();
        if(StrUtil.isNotBlank(SHORTAGE_CONFIRM_WITHOUT_REFUND_REGIONIDS)
                && stockoutRecord.getType().equals(BlameSourceTypeEnum.LESS_GOODS.getCode())
                && SHORTAGE_CONFIRM_WITHOUT_REFUND_LIMIT != null
                && SHORTAGE_CONFIRM_WITHOUT_REFUND_LIMIT > 0) {
            log.keyword("shortageRefund").info("总仓：{}，限额：{}", SHORTAGE_CONFIRM_WITHOUT_REFUND_REGIONIDS, SHORTAGE_CONFIRM_WITHOUT_REFUND_LIMIT);
            List<String> regionIds = StrUtil.split(SHORTAGE_CONFIRM_WITHOUT_REFUND_REGIONIDS, CharPool.COMMA);
            if (CollUtil.isNotEmpty(regionIds) && regionIds.contains(stockoutRecord.getRegionWhId().toString())) {
                //如果今日退款金额超过限额，以及今日退款加这笔退款超过限额就不退钱
                BigDecimal todayRefundAmount = this.selectRefundTypeToDayAmount(RefundTypeEnum.LESS, stockoutRecord.getRegionWhId());
                BigDecimal refundAmount = BigDecimal.ZERO;
                for (StockoutRecordDetail stockoutRecordDetail : stockoutRecordDetailList) {
                    refundAmount = refundAmount.add(stockoutRecordDetail.getRefundProductAmount().add(stockoutRecordDetail.getRefundOtherAmount()));
                }
                if (todayRefundAmount.add(refundAmount).compareTo(BigDecimal.valueOf(SHORTAGE_CONFIRM_WITHOUT_REFUND_LIMIT)) >= 0) {
                    infAuto = YNStatusEnum.DISABLE.getCode();
                    log.keyword("shortageRefund").info("今日退款金额超过限额，今日退款金额：{}，本次退款金额：{}，今日退款限额：{}，本次退款需审核", todayRefundAmount, refundAmount, SHORTAGE_CONFIRM_WITHOUT_REFUND_LIMIT);
                }
            }
        }
        SpringUtils.getBean(IRefundRecordService.class).stockoutConfirm(stockoutRecord, stockoutRecordDetailList, infAuto);
    }


    private OrderRefundBo buildOrderRefundByStockout(RefundRecord record, RefundProductDetail detail, BlameSourceTypeEnum blameSourceTypeEnum, RemoteSupplierVo remoteSupplierVo, RemoteSupplierSkuInfoVo skuInfo, String regionWhCode) {
        OrderRefundBo orderRefundBo = new OrderRefundBo();
        orderRefundBo.setOrderId(record.getOrderId())
                .setOrderNo(record.getOrderCode())
                .setRefundId(record.getId())
                .setRefundNo(record.getCode())
                .setRefundAmt(record.getRefundProductAmount().add(record.getRefundOtherAmount()));
        if (record.getRefundAmount().compareTo(audiAmount) < 0) {
            orderRefundBo.setInfAuto(YNStatusEnum.ENABLE.getCode());
        } else {
            orderRefundBo.setInfAuto(YNStatusEnum.DISABLE.getCode());
        }
        if (blameSourceTypeEnum.equals(BlameSourceTypeEnum.STOCKOUT)) {
            orderRefundBo.setBusiType(RefundBusiTypeEnum.REFUND_SHORT.getCode());
        } else {
            orderRefundBo.setBusiType(RefundBusiTypeEnum.REFUND_LACK.getCode());
        }

        List<OrderPaySplitBo> splits = new ArrayList<>();
        //商品金额 扣供应商仓
        if (record.getRefundProductAmount().compareTo(BigDecimal.ZERO) >= 0) {
            OrderPaySplitBo split = new OrderPaySplitBo();
            split.setOrgId(detail.getSupplierId()).setOrgType(AccountOrgTypeEnum.SUPPLIER.getCode()).setOrgCode(remoteSupplierVo.getCode())
                    .setTransAmt(record.getRefundProductAmount()).setSkuId(skuInfo.getId()).setSplitAmt(record.getRefundProductAmount()).setDeptId(skuInfo.getSupplierDeptId());
            if (ObjectUtil.isNotNull(detail.getRefundDistributionAmount()) && detail.getRefundDistributionAmount().compareTo(BigDecimal.ZERO) > 0) {
                //获取分销记录查询分销员id
                RemoteDistributionOrderInfoVo distributionOrderInfoVo = distributionService.getOrderInfo(detail.getOrderItemId());
                if (distributionOrderInfoVo != null) {
                    split.setCommCustomerId(distributionOrderInfoVo.getDistributorCustomerId());
                }
                split.setCommissionAmt(detail.getRefundDistributionAmount().add(detail.getRefundDistributionTaxAmount()));
                split.setCommSalaryAmt(detail.getRefundDistributionAmount());
            }
            splits.add(split);
        }
        //非商品金额 扣总仓
        if (record.getRefundOtherAmount().compareTo(BigDecimal.ZERO) >= 0) {
            OrderPaySplitBo split = new OrderPaySplitBo();
            split.setOrgId(record.getRegionWhId()).setOrgType(AccountOrgTypeEnum.REGION_WH.getCode()).setOrgCode(regionWhCode)
                    .setTransAmt(record.getRefundOtherAmount()).setSplitAmt(record.getRefundOtherAmount());
            splits.add(split);
        }
        orderRefundBo.setSplits(splits);
        return orderRefundBo;
    }


    private Pair<RefundRecord,RefundProductDetail> stockoutCreateRefund(StockoutRecord stockoutRecord, StockoutRecordDetail stockoutRecordDetail, Order order, OrderItem orderItem,
                                                                        List<RefundProductDetail> refundList, OccupyEnum occupyEnum, BigDecimal refundServiceFee, BigDecimal refundAmount) {
        RefundProductDetail detail = this.buildDetailByStockout(stockoutRecord, stockoutRecordDetail, order, orderItem, refundList, refundServiceFee, refundAmount);
        RefundRecord record = this.buildRecordByStockout(stockoutRecord, stockoutRecordDetail, detail, orderItem);
        return Pair.of(record,detail);
    }


    private RefundRecord buildRecordByStockout(StockoutRecord stockoutRecord, StockoutRecordDetail stockoutRecordDetail, RefundProductDetail detail, OrderItem orderItem){
        RefundRecord record = new RefundRecord();
        record.setCode(generateRefundCode());
        record.setOrderId(stockoutRecordDetail.getOrderId());
        record.setOrderCode(orderItem.getOrderCode());
        record.setSourceCode(stockoutRecord.getCode());
        record.setCustomerId(orderItem.getCustomerId());
        record.setCustomerName(stockoutRecordDetail.getCustomerName());
        record.setCityWhId(stockoutRecord.getCityWhId());
        record.setRegionWhId(stockoutRecord.getRegionWhId());
        if (stockoutRecord.getType().equals(BlameSourceTypeEnum.STOCKOUT.getCode())){
            record.setRefundType(RefundTypeEnum.LACK.getCode());
        }else {
            record.setRefundType(RefundTypeEnum.LESS.getCode());
        }
        record.setRefundStatus(RefundStatusEnum.IN_REFUND.getCode());
        record.setPayTime(orderItem.getPayTime());
        //计算退商品金额
        record.setRefundProductAmount(detail.getRefundProductAmount());
        //非商品金额(包括金融手续费)
        record.setRefundOtherAmount(detail.getRefundOtherAmount());
        //退平台补贴优惠商品金额
        record.setRefundSubsidyFreeAmount(detail.getRefundSubsidyFreeAmount());
        record.setRefundProductFreeAmount(detail.getRefundProductFreeAmount());
        record.setRefundAmount(record.getRefundOtherAmount().add(record.getRefundProductAmount()));
        //维护退金融服务费
        record.setRefundFinancialServiceAmount(detail.getRefundFinancialServicePrice());
        record.setPlaceId(orderItem.getPlaceId());
        record.setPlaceIdLevel2(orderItem.getPlaceIdLevel2());
        record.setPlacePath(orderItem.getPlacePath());
        //超过5000就要审核
        if (record.getRefundAmount().compareTo(audiAmount) >= 0) {
            record.setAuditStatus(0);
        } else {
            record.setAuditStatus(1);
        }
        return record;
    }


    private RefundProductDetail buildDetailByStockout(StockoutRecord stockoutRecord, StockoutRecordDetail stockoutRecordDetail, Order order, OrderItem orderItem,
                                                      List<RefundProductDetail> refundList, BigDecimal refundServiceFee, BigDecimal refundAmount) {
        RefundProductDetail detail = new RefundProductDetail();
        detail.setOrderItemId(orderItem.getId());
        detail.setSupplierSkuId(stockoutRecord.getSupplierSkuId());
        detail.setSupplierSkuCode(stockoutRecord.getSupplierSkuCode());
        detail.setSaleDate(stockoutRecord.getSaleDate());
        detail.setSupplierId(stockoutRecord.getSupplierId());
        detail.setSupplierDeptId(stockoutRecord.getSupplierDeptId());
        detail.setOriginalSupplierId(stockoutRecord.getSupplierId());
        detail.setLogisticsId(stockoutRecord.getLogisticsId());
        detail.setLogisticsName(stockoutRecord.getLogisticsName());
        detail.setSupplierDeptId(stockoutRecord.getSupplierDeptId());
        detail.setSpuId(orderItem.getSpuId());
        detail.setSpuName(orderItem.getSpuName());
        detail.setSpuGrossWeight(orderItem.getSpuGrossWeight());
        detail.setSpuNetWeight(orderItem.getSpuNetWeight());
        detail.setOrderCount(orderItem.getCount());
        detail.setStockoutCount(stockoutRecordDetail.getStockoutCount());
        detail.setRefundRemark(stockoutRecord.getCreateRemark());
        //服务单价 服务费÷总数量
        BigDecimal servicePrice = orderItem.getPlatformServiceAmount().divide(new BigDecimal(orderItem.getCount()), 2, RoundingMode.FLOOR);
        detail.setCollectAgentPrice(servicePrice);
        detail.setOrderPrice(orderItem.getRegionSubsidyAmount().add(orderItem.getSkuSubsidyAmount()).compareTo(BigDecimal.ZERO) > 0 ? orderItem.getFinalPrice() : orderItem.getPrice());
        detail.setSettlePrice(orderItem.getFinalPrice());
        detail.setRefundStatus(RefundStatusEnum.IN_REFUND.getCode());
        detail.setProdType(orderItem.getProdType());
        if (stockoutRecord.getType().equals(BlameSourceTypeEnum.STOCKOUT.getCode())){
            detail.setRefundType(RefundTypeEnum.LACK.getCode());
        }else {
            detail.setRefundType(RefundTypeEnum.LESS.getCode());
        }
        detail.setImgUrl(orderItem.getImgUrl());
        //缺货、少货的数量
        int refundStockoutCount = 0;
        //存在报损跟差额退顺序混乱的问题,这里先排除掉这两种情况的数量,再分别算出金额以免扣多了
        //差额退款
        BigDecimal difDetail =  BigDecimal.ZERO;
        //差额退 退档服务费
        BigDecimal difOtherAmount = BigDecimal.ZERO;
        BigDecimal refProductAmount = BigDecimal.ZERO;
        //报损退款
        BigDecimal loss = BigDecimal.ZERO;
        BigDecimal distributionAmount = orderItem.getSettleCount() >= 1 ? orderItem.getDistributionAmount().add(orderItem.getDistributionTaxAmount()) : BigDecimal.ZERO;
        //退金融手续费
        BigDecimal refundFinancialServicePrice = BigDecimal.ZERO;
        //计算获取各项已退金额
        BigDecimal refundProduct = BigDecimal.ZERO;
        BigDecimal refundPlatformFreight = BigDecimal.ZERO;
        BigDecimal refundPlatformFreightAmountLevel2 = BigDecimal.ZERO;
        BigDecimal refundBaseFreight = BigDecimal.ZERO;
        BigDecimal refundRegionFreight = BigDecimal.ZERO;
        BigDecimal refundServiceAmount = BigDecimal.ZERO;
        BigDecimal difRefundServiceAmount = BigDecimal.ZERO;
        BigDecimal refundFreightAmount = BigDecimal.ZERO;
        BigDecimal refundProductFreeAmount = BigDecimal.ZERO;
        BigDecimal refundDistributionAmount = BigDecimal.ZERO;
        BigDecimal refundDistributionTaxAmount = BigDecimal.ZERO;
        //优惠补贴金额
        BigDecimal refundSubsidyFreeAmount = BigDecimal.ZERO;
        if (CollectionUtil.isNotEmpty(refundList)){
            for (RefundProductDetail productDetail : refundList) {
                if (productDetail.getRefundAmount().compareTo(BigDecimal.ZERO) > 0) {
                    if (!productDetail.getRefundType().equals(RefundTypeEnum.DIFFERENCE.getCode()) &&
                            !productDetail.getRefundType().equals(RefundTypeEnum.REPORT_LOSS.getCode())) {
                        refundStockoutCount += productDetail.getStockoutCount();
                    }
                }
                if (productDetail.getRefundType().equals(RefundTypeEnum.DIFFERENCE.getCode())) {
                    difDetail = difDetail.add(productDetail.getRefundProductAmount());
                    difOtherAmount = difOtherAmount.add(productDetail.getRefundServiceAmount());
                } else if (productDetail.getRefundType().equals(RefundTypeEnum.REPORT_LOSS.getCode())) {
                    loss = loss.add(productDetail.getRefundProductAmount());
                }else {
                    refProductAmount = refProductAmount.add(productDetail.getRefundProductAmount());
                }
                if ( orderItem.getSettleTime()!= null) {
                    distributionAmount = distributionAmount.subtract(productDetail.getRefundDistributionAmount());
                }
                refundFinancialServicePrice = refundFinancialServicePrice.add(productDetail.getRefundFinancialServicePrice());
                refundProduct = refundProduct.add(productDetail.getRefundProductAmount());
                refundPlatformFreight = refundPlatformFreight.add(productDetail.getRefundPlatformFreight());
                refundPlatformFreightAmountLevel2 = refundPlatformFreightAmountLevel2.add(productDetail.getRefundPlatformFreightAmountLevel2());
                refundBaseFreight = refundBaseFreight.add(productDetail.getRefundBaseFreight());
                refundRegionFreight = refundRegionFreight.add(productDetail.getRefundRegionFreight());
                refundServiceAmount = refundServiceAmount.add(productDetail.getRefundServiceAmount());
                if (productDetail.getRefundType().equals(RefundTypeEnum.DIFFERENCE.getCode())) {
                    difRefundServiceAmount = difRefundServiceAmount.add(productDetail.getRefundServiceAmount());
                }
                refundFreightAmount = refundFreightAmount.add(productDetail.getRefundFreightAmount());
                refundProductFreeAmount = refundProductFreeAmount.add(productDetail.getRefundProductFreeAmount());
                refundDistributionAmount = refundDistributionAmount.add(productDetail.getRefundDistributionAmount());
                refundDistributionTaxAmount = refundDistributionTaxAmount.add(productDetail.getRefundDistributionTaxAmount());
                refundSubsidyFreeAmount = refundSubsidyFreeAmount.add(productDetail.getRefundSubsidyFreeAmount());
            }
        }
        if (difDetail.compareTo(BigDecimal.ZERO) > 0) {
            detail.setRefundPriceType(RefundPriceTypeEnum.STTLE_PRICE.getCode());
        } else {
            detail.setRefundPriceType(RefundPriceTypeEnum.ORDER_PRICE.getCode());
        }

        //一次性全退
        if (refundStockoutCount == 0 && orderItem.getCount().equals(stockoutRecordDetail.getStockoutCount())){
            //防止进行了差额退,减差额退减报损,实付=商品金额-优惠
            detail.setRefundProductAmount(orderItem.getProductAmount().subtract(difDetail).subtract(loss).subtract(orderItem.getProductFreeAmount()));
            detail.setRefundSubsidyFreeAmount(orderItem.getRegionSubsidyAmount().add(orderItem.getSkuSubsidyAmount()));
            detail.setRefundProductFreeAmount(orderItem.getProductFreeAmount());
            detail.setRefundOtherAmount(orderItem.getTotalAmount().subtract(orderItem.getProductAmount()).add(orderItem.getProductFreeAmount()).subtract(difOtherAmount));
            detail.setRefundPlatformFreight(orderItem.getPlatformFreightAmount().subtract(orderItem.getPlatformFreightFreeAmount()));
            detail.setRefundPlatformFreightAmountLevel2(orderItem.getPlatformFreightAmountLevel2().subtract(orderItem.getPlatformFreightFreeAmountLevel2()));
            detail.setRefundBaseFreight(orderItem.getBaseFreightAmount().subtract(orderItem.getBaseFreightFreeAmount()));
            detail.setRefundRegionFreight(orderItem.getRegionFreightAmount().subtract(orderItem.getRegionFreightFreeAmount()));
            detail.setRefundFreightAmount(orderItem.getFreightTotalAmount().subtract(orderItem.getFreightTotalFreeAmount()));
            detail.setRefundServiceAmount(orderItem.getPlatformServiceAmount().subtract(orderItem.getPlatformServiceFreeAmount()).subtract(difOtherAmount));
            detail.setRefundDistributionAmount(orderItem.getDistributionAmount());
            detail.setRefundDistributionTaxAmount(orderItem.getDistributionTaxAmount());
            detail.setRefundFinancialServicePrice(orderItem.getFinancialServiceAmount());
            if (distributionAmount.compareTo(BigDecimal.ZERO) > 0) {
                detail.setRefundProductAmount(detail.getRefundProductAmount().subtract(distributionAmount));
                detail.setRefundDistributionAmount(BigDecimal.ZERO);
                detail.setRefundDistributionTaxAmount(BigDecimal.ZERO);
            }
//            getRefundFinancialServicePriceV2(refundAmount, refundServiceFee, order, stockoutRecordDetail, detail,orderItem);
        } else if (refundStockoutCount != 0 && orderItem.getCount() == stockoutRecordDetail.getStockoutCount() + refundStockoutCount) {
            //缺货数量=订单项数量-已退数量 按剩下多少算  其他金额 订单项金额-已退金额
            detail.setRefundSubsidyFreeAmount(orderItem.getRegionSubsidyAmount().add(orderItem.getSkuSubsidyAmount()).subtract(refundSubsidyFreeAmount));
            detail.setRefundProductFreeAmount(orderItem.getProductFreeAmount().subtract(refundProductFreeAmount));
            //退商品金额 = 订单项纪录金额 - 优惠金额 - 已退商品金额
            detail.setRefundProductAmount(orderItem.getProductAmount().subtract(refundProduct).subtract(orderItem.getProductFreeAmount()));
            //退非商品金额=非商品金额-退运费总额-退服务金额 (订单项非商品金额不包括金融服务费)
            detail.setRefundOtherAmount(orderItem.getOtherTotalAmount().subtract(orderItem.getFreeTotalAmount().subtract(orderItem.getProductFreeAmount()))
                    .subtract(refundFreightAmount).subtract(refundServiceAmount).subtract(refundFinancialServicePrice));
            detail.setRefundPlatformFreight(orderItem.getPlatformFreightAmount().subtract(orderItem.getPlatformFreightFreeAmount()).subtract(refundPlatformFreight));
            detail.setRefundPlatformFreightAmountLevel2(orderItem.getPlatformFreightAmountLevel2().subtract(orderItem.getPlatformFreightFreeAmountLevel2()).subtract(refundPlatformFreightAmountLevel2));
            detail.setRefundBaseFreight(orderItem.getBaseFreightAmount().subtract(orderItem.getBaseFreightFreeAmount()).subtract(refundBaseFreight));
            detail.setRefundRegionFreight(orderItem.getRegionFreightAmount().subtract(orderItem.getRegionFreightFreeAmount()).subtract(refundRegionFreight));
            detail.setRefundFreightAmount(orderItem.getFreightTotalAmount().subtract(orderItem.getFreightTotalFreeAmount()).subtract(refundFreightAmount));
            detail.setRefundServiceAmount(orderItem.getPlatformServiceAmount().subtract(orderItem.getPlatformServiceFreeAmount()).subtract(refundServiceAmount));
            detail.setRefundDistributionAmount(orderItem.getDistributionAmount().subtract(refundDistributionAmount));
            detail.setRefundDistributionTaxAmount(orderItem.getDistributionTaxAmount().subtract(refundDistributionTaxAmount));
            if (distributionAmount.compareTo(BigDecimal.ZERO) > 0) {
                detail.setRefundProductAmount(detail.getRefundProductAmount().subtract(distributionAmount));
                detail.setRefundDistributionAmount(BigDecimal.ZERO);
                detail.setRefundDistributionTaxAmount(BigDecimal.ZERO);
            }
            detail.setRefundFinancialServicePrice(orderItem.getFinancialServiceAmount().subtract(refundFinancialServicePrice));
//            getRefundFinancialServicePriceV2(refundAmount, refundServiceFee, order, stockoutRecordDetail, detail,orderItem);
        } else {
            //缺货数量<订单项数量-已退数量(有的话) 按比例算
            //缺货少货 非商品金额按比例退,先算比例,再按订单项记录金额进行计算赋值
            BigDecimal subFreeAmount = (orderItem.getRegionSubsidyAmount().add(orderItem.getSkuSubsidyAmount())).multiply(new BigDecimal(stockoutRecordDetail.getStockoutCount())).divide(new BigDecimal(orderItem.getCount()), 2, RoundingMode.HALF_UP);
            BigDecimal proFreeAmount = orderItem.getProductFreeAmount().multiply(new BigDecimal(stockoutRecordDetail.getStockoutCount())).divide(new BigDecimal(orderItem.getCount()), 2, RoundingMode.HALF_UP);
            //获取剩余实付商品金额(结算价*数量-优惠加)  退不能超过剩余金额
            BigDecimal subtract = orderItem.getProductAmount().subtract(loss).subtract(difDetail).subtract(orderItem.getProductFreeAmount()).subtract(refProductAmount);
            BigDecimal difPath = difDetail.multiply(BigDecimal.valueOf(stockoutRecordDetail.getStockoutCount())).divide(BigDecimal.valueOf(orderItem.getCount()));
            //单价*数量-按比例得出的优惠金额 跟剩余实付金额比较,取小的值
            BigDecimal amount = orderItem.getPrice().multiply(new BigDecimal(stockoutRecordDetail.getStockoutCount())).subtract(proFreeAmount).subtract(difPath);
            detail.setRefundProductAmount(amount.compareTo(subtract) > 0 ? subtract : amount);
            var platformFreightAmount = orderItem.getPlatformFreightAmount().subtract(orderItem.getPlatformFreightFreeAmount()).subtract(refundPlatformFreight);
            if (platformFreightAmount.compareTo(BigDecimal.ZERO) > 0) {
                detail.setRefundPlatformFreight(platformFreightAmount.multiply(new BigDecimal(stockoutRecordDetail.getStockoutCount())).divide(new BigDecimal(orderItem.getCount()), 2, RoundingMode.HALF_UP));
            } else {
                detail.setRefundPlatformFreight(BigDecimal.ZERO);
            }
            var refundPlatformFreighLevel2 = orderItem.getPlatformFreightAmountLevel2().subtract(orderItem.getPlatformFreightFreeAmountLevel2()).subtract(refundPlatformFreightAmountLevel2);
            if (refundPlatformFreighLevel2.compareTo(BigDecimal.ZERO) > 0) {
                detail.setRefundPlatformFreightAmountLevel2((orderItem.getPlatformFreightAmountLevel2().subtract(orderItem.getPlatformFreightFreeAmountLevel2()))
                        .multiply(new BigDecimal(stockoutRecordDetail.getStockoutCount())).divide(new BigDecimal(orderItem.getCount()), 2, RoundingMode.HALF_UP));
            }else {
                detail.setRefundPlatformFreightAmountLevel2(BigDecimal.ZERO);
            }
            var refundBase = orderItem.getBaseFreightAmount().subtract(orderItem.getBaseFreightFreeAmount()).subtract(refundBaseFreight);
            if (refundBase.compareTo(BigDecimal.ZERO) > 0) {
                detail.setRefundBaseFreight(refundBase.multiply(new BigDecimal(stockoutRecordDetail.getStockoutCount())).divide(new BigDecimal(orderItem.getCount()), 2, RoundingMode.HALF_UP));
            } else {
                detail.setRefundBaseFreight(BigDecimal.ZERO);
            }
            var refundRegion = orderItem.getRegionFreightAmount().subtract(orderItem.getRegionFreightFreeAmount()).subtract(refundRegionFreight);
            if (refundRegion.compareTo(BigDecimal.ZERO) > 0) {
                detail.setRefundRegionFreight(refundRegion.multiply(new BigDecimal(stockoutRecordDetail.getStockoutCount())).divide(new BigDecimal(orderItem.getCount()), 2, RoundingMode.HALF_UP));
            } else {
                detail.setRefundRegionFreight(BigDecimal.ZERO);
            }
            var refundService = orderItem.getPlatformServiceAmount().subtract(orderItem.getPlatformServiceFreeAmount()).subtract(difRefundServiceAmount);
            if (refundService.compareTo(BigDecimal.ZERO) > 0) {
                detail.setRefundServiceAmount(refundService.multiply(new BigDecimal(stockoutRecordDetail.getStockoutCount())).divide(new BigDecimal(orderItem.getCount()), 2, RoundingMode.HALF_UP));
            } else {
                detail.setRefundServiceAmount(BigDecimal.ZERO);
            }
            //运费总和
            detail.setRefundFreightAmount(detail.getRefundPlatformFreight().add(detail.getRefundBaseFreight())
                    .add(detail.getRefundRegionFreight()).add(detail.getRefundPlatformFreightAmountLevel2()));
            //退非商品金额 = 退运费总和+退金融服务费+退代采服务费
            detail.setRefundOtherAmount(detail.getRefundFreightAmount().add(detail.getRefundServiceAmount()));
            //退优惠金额
            detail.setRefundSubsidyFreeAmount(subFreeAmount);
            detail.setRefundProductFreeAmount(proFreeAmount);
            if (distributionAmount.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal sAmounst = subtract.subtract(detail.getRefundProductAmount()).subtract(distributionAmount);
                if (sAmounst.compareTo(BigDecimal.ZERO) < 0) {
                    detail.setRefundProductAmount(detail.getRefundProductAmount().add(sAmounst));
                }
            }
            //退金融服务费
            BigDecimal apportionTotalAmount = orderItem.getTotalAmount().subtract(orderItem.getFinancialServiceAmount());
            //退款金额
            BigDecimal detailRefundAmount = detail.getRefundProductAmount().add(detail.getRefundOtherAmount());
            //退分销金额 对比退款的金额是否超出分销后的金额，超出部分从分销金额取
            if (orderItem.getDistributionAmount().compareTo(BigDecimal.ZERO) > 0) {
                //佣金-订单余额 > 0  ? 扣佣金 : 不处理
                BigDecimal distribution = orderItem.getDistributionAmount().add(orderItem.getDistributionTaxAmount()).subtract(refundDistributionAmount)
                        .subtract(refundDistributionTaxAmount).subtract(subtract).add(detail.getRefundProductAmount());
                BigDecimal refundDistribution = distribution.compareTo(BigDecimal.ZERO) > 0 ? distribution : BigDecimal.ZERO;
                detail.setRefundDistributionAmount(refundDistribution.divide(BigDecimal.ONE.add(distributionTaxRate), 2, RoundingMode.HALF_UP));
                detail.setRefundDistributionTaxAmount(refundDistribution.subtract(detail.getRefundDistributionAmount()));

            }
            //计算金融手续费
            BigDecimal refundFinancialPrice = getApportionRefundService(apportionTotalAmount,orderItem.getFinancialServiceAmount(),detailRefundAmount,refundFinancialServicePrice);

            detail.setRefundFinancialServicePrice(refundFinancialPrice);
            detail.setRefundOtherAmount(detail.getRefundOtherAmount().add(refundFinancialPrice));
        }
        detail.setRefundAmount(detail.getRefundProductAmount().add(detail.getRefundOtherAmount()));
        return detail;
    }

    /**
     * 调用退款通知更新退款单状态
     */
    @Override
    public void updateRefundStatus(OrderRefundQueryVo vo) {
        LambdaQueryWrapper<RefundRecord> qw = Wrappers.lambdaQuery();
        qw.eq(RefundRecord::getCode, vo.getRefundNo()).eq(RefundRecord::getDelFlag,0);
        RefundRecord refundRecord = baseMapper.selectOne(qw,false);
        if (refundRecord != null) {
            //已退款就不再处理
            if (!refundRecord.getRefundStatus().equals(RefundStatusEnum.HAS_REFUND.getCode())) {
                //更新退款单退款状态
                refundRecord.setAuditStatus(1);
                refundRecord.setRefundStatus(RefundStatusEnum.HAS_REFUND.getCode());
                if (refundRecord.getOccupyStatus().equals(OccupyEnum.YES.getCode())) {
                    refundRecord.setOccupyStatus(OccupyEnum.RELEASE.getCode());
                }
                //获取退款单详情
                List<RefundProductDetail> refundProductDetails = refundProductDetailMapper.selectList(Wrappers.lambdaQuery(RefundProductDetail.class)
                        .eq(RefundProductDetail::getRefundRecordId, refundRecord.getId()).eq(RefundProductDetail::getDelFlag, 0));
                Set<Long> settles = new HashSet<>();
                //更新退款单详情退款状态
                if (CollectionUtil.isNotEmpty(refundProductDetails)) {
                    refundProductDetails.forEach(l -> {
                        l.setRefundStatus(RefundStatusEnum.HAS_REFUND.getCode());
                        l.setRefundTime(new Date());
                    });
                    refundProductDetailMapper.updateBatchById(refundProductDetails);
                    //获取对应订单项数据
                    List<Long> orderItemIds = refundProductDetails.stream().map(RefundProductDetail::getOrderItemId).toList();
                    List<OrderItem> orderItems = orderItemMapper.selectBatchIds(orderItemIds);
                    List<Long> ids = orderItems.stream().filter(l -> l.getStatus().equals(OrderStatusEnum.CANCEL.getCode())).map(OrderItem::getId).toList();
                    settles.addAll(ids);
                }
                //更新来源单据退款状态 缺货少货单
                if (refundRecord.getRefundType().equals(RefundTypeEnum.LACK.getCode()) || refundRecord.getRefundType().equals(RefundTypeEnum.LESS.getCode())) {
                    //获取缺货少货单据
                    StockoutRecord stockoutRecord = stockoutRecordMapper.selectByCode(refundRecord.getSourceCode());
                    if (stockoutRecord != null) {
                        stockoutRecord.setRefundStatus(2);
                        stockoutRecordMapper.updateById(stockoutRecord);
                        settles.addAll(refundProductDetails.stream().map(RefundProductDetail::getOrderItemId).toList());
                    }
                }
                //取消订单,更新订单状态
                if (refundRecord.getRefundType().equals(RefundTypeEnum.CANCEL.getCode())) {
                    Order order = orderMapper.selectById(refundRecord.getOrderId());
                    if (order != null) {
                        order.setStatus(OrderStatusEnum.CANCEL.getCode());
                        orderMapper.updateById(order);
                    }
                }
                if (refundRecord.getRefundAmount().compareTo(BigDecimal.ZERO) == 0) {
                    //如果退款金额为0，则标记软删除
//                    refundRecord.setDelFlag(BanguoCommonConstant.delFlag);
                }
                baseMapper.updateById(refundRecord);
                //都退了,走结算
                if (CollectionUtil.isNotEmpty(settles)) {
                    orderItemService.notifySettle(Lists.newArrayList(settles));
                }

                this._sendMessage(refundRecord, refundProductDetails);
            }
        } else {
            log.error("退款单不存在");
        }

    }

    private void _sendMessage(RefundRecord refundRecord, List<RefundProductDetail> refundProductDetails) {
        for (RefundProductDetail refundProductDetail : refundProductDetails) {
            OrderItem orderItem = orderItemMapper.selectById(refundProductDetail.getOrderItemId());
            if (orderItem == null) {
                continue;
            }

            if (refundRecord.getRefundType().equals(RefundTypeEnum.DIFFERENCE.getCode())
                    && !Objects.equals(refundRecord.getDifRefundType(),DifRefundTypeEnum.FIX_PRICE.getCode())) {
                LinkedHashMap<String, String> params = new LinkedHashMap<>();

                params.put("orderCode", refundRecord.getOrderCode());
                params.put("spuName", remoteSupplierSkuService.getSkuCombinationName(com.google.common.collect.Lists.newArrayList(orderItem.getSupplierSkuId())).get(orderItem.getSupplierSkuId()));
                params.put("refundAmount", refundProductDetail.getRefundAmount().toPlainString());
                params.put("submitTime", DateUtil.format(orderItem.getCreateTime(), DatePattern.NORM_DATETIME_PATTERN));
                RemoteMessageNotifyV2Bo message1 = new RemoteMessageNotifyV2Bo(NotifyObjectTypeEnum.CUSTOMER,
                        com.google.common.collect.Lists.newArrayList(orderItem.getCustomerId().toString()),
                        refundRecord.getDifRefundType().equals(DifRefundTypeEnum.MARKET_PRICE.getCode()) ? MsgNotifyTemplateV2Enum.refund_market : MsgNotifyTemplateV2Enum.refund_scale,
                        params, refundProductDetail.getId().toString());
                remoteMessageNotifyService.sendMessageV2(message1);
            }
        }


    }

    private  void getRefundFinancialServicePrice(Map<Long, BigDecimal> refundAmountMap, Map<Long, BigDecimal> refundServiceFeeMap,
                                                 QueryDistributionVo queryDistributionVo, StockoutRecordDetail l, RefundProductDetail detail,
                                                 QueryDistributionItemVo itemVo) {
        BigDecimal refundServiceFee = refundServiceFeeMap.get(l.getOrderId()) == null ? BigDecimal.ZERO : refundServiceFeeMap.get(l.getOrderId());
        BigDecimal refundAmount = refundAmountMap.get(l.getOrderId()) == null ? BigDecimal.ZERO : refundAmountMap.get(l.getOrderId());
        //判断该商品全部退掉后,整个订单的退款金额是否全部退了,全部退的情况下,将剩余的金融服务费也退掉
        if ((queryDistributionVo.getTotalAmount().subtract(queryDistributionVo.getFinancialServiceAmount()))
                .compareTo(refundAmount.add(detail.getRefundProductAmount().add(detail.getRefundOtherAmount()))) == 0) {
            detail.setRefundFinancialServicePrice(queryDistributionVo.getFinancialServiceAmount().subtract(refundServiceFee));
            detail.setRefundOtherAmount(detail.getRefundOtherAmount().add(detail.getRefundFinancialServicePrice()));
        }
        //没全退,按计算来,要剩0.01
        else {
            //退金融服务费
            BigDecimal apportionTotalAmount = queryDistributionVo.getTotalAmount().subtract(queryDistributionVo.getFinancialServiceAmount());
            //退款金额
            BigDecimal detailRefundAmount = detail.getRefundProductAmount().add(detail.getRefundOtherAmount());
            //计算金融手续费
            BigDecimal refundFinancialPrice = getApportionRefundService(apportionTotalAmount,itemVo.getFinancialServiceAmount(),detailRefundAmount,refundServiceFee);

            detail.setRefundFinancialServicePrice(refundFinancialPrice);
            detail.setRefundOtherAmount(detail.getRefundOtherAmount().add(refundFinancialPrice));
        }
    }

    private  void getRefundFinancialServicePriceV2(BigDecimal refundAmount, BigDecimal refundServiceFee, Order order, StockoutRecordDetail l,
                                                   RefundProductDetail detail,OrderItem orderItem) {
        //判断该商品全部退掉后,整个订单的退款金额是否全部退了,全部退的情况下,将剩余的金融服务费也退掉
        if ((order.getTotalAmount().subtract(order.getFinancialServiceAmount()))
                .compareTo(refundAmount.add(detail.getRefundProductAmount().add(detail.getRefundOtherAmount()))) == 0) {
            detail.setRefundFinancialServicePrice(order.getFinancialServiceAmount().subtract(refundServiceFee));
            detail.setRefundOtherAmount(detail.getRefundOtherAmount().add(detail.getRefundFinancialServicePrice()));
        }
        //没全退,按计算来,要剩0.01
        else {
            //退金融服务费
            BigDecimal apportionTotalAmount = order.getTotalAmount().subtract(order.getFinancialServiceAmount());
            //退款金额
            BigDecimal detailRefundAmount = detail.getRefundProductAmount().add(detail.getRefundOtherAmount());
            //计算金融手续费
            BigDecimal refundFinancialPrice = getApportionRefundService(apportionTotalAmount,orderItem.getFinancialServiceAmount(),detailRefundAmount,refundServiceFee);
            detail.setRefundFinancialServicePrice(refundFinancialPrice);
            detail.setRefundOtherAmount(detail.getRefundOtherAmount().add(refundFinancialPrice));
        }
    }

    /**
     * 查询订单项退款记录
     */
    @Override
    public List<RefundProductDetail> getRefundByItemId(List<Long> orderItemIds) {
        LambdaQueryWrapper<RefundProductDetail> qw = Wrappers.lambdaQuery();
        qw.in(RefundProductDetail::getOrderItemId, orderItemIds).eq(RefundProductDetail::getDelFlag, 0);
        return refundProductDetailMapper.selectList(qw);
    }

    /**
     * 创建差额退款单
     * 只会进行一次差额退款 差额退款以订单为维度生成
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createDifferenceRecord(OrderRefundDTO orderRefundDTO) {
        log.keyword("createDifferenceRecord").info("开始差额退{}",orderRefundDTO.getOrderItemIdList());
        List<Long> itemIds = new ArrayList<>(orderRefundDTO.getOrderItemIdList().stream().map(OrderRefundDTO.OrderItemIdList::getOrderItemId).toList());
        Map<Long,Integer> orderItemIdMap = orderRefundDTO.getOrderItemIdList().stream().collect(Collectors.toMap(OrderRefundDTO.OrderItemIdList::getOrderItemId, OrderRefundDTO.OrderItemIdList::getDifRefundType));
        List<OrderFixPriceItemDTO> orderFixPriceItemDTOS = orderRefundDTO.getOrderFixPriceItemDTOS();
        Map<Long, OrderFixPriceItemDTO> orderFixPriceItemDTOMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(orderFixPriceItemDTOS)){
            orderFixPriceItemDTOMap = orderFixPriceItemDTOS.stream().collect(Collectors.toMap(OrderFixPriceItemDTO::getOrderItemId, item -> item));
        }
        //获取订单项信息
        if (CollectionUtil.isNotEmpty(itemIds)) {
            //根据订单项id查询差额退款类型退款记录,存在一处差额退款了就不处理
            List<RefundRecordDetailVO> selectList = refundProductDetailMapper.selectListByDifRefundType(itemIds,RefundTypeEnum.DIFFERENCE.getCode());
            if (CollectionUtil.isNotEmpty(selectList) && !orderRefundDTO.getIsFixPrice()){
                itemIds.removeAll(selectList.stream().filter(l -> !Objects.equals(l.getDifRefundType(),DifRefundTypeEnum.FIX_PRICE.getCode()))
                        .map(RefundRecordDetailVO::getOrderItemId).toList());
            }
            if (CollectionUtil.isNotEmpty(itemIds)) {
                //根据订单项id查询退款记录
                LambdaQueryWrapper<RefundProductDetail> lqw = Wrappers.lambdaQuery();
                lqw.in(RefundProductDetail::getOrderItemId, itemIds).eq(RefundProductDetail::getDelFlag, 0);
                List<RefundProductDetail> refundList = refundProductDetailMapper.selectList(lqw);
                Map<Long, BigDecimal> refundAmountMap = new HashMap<>();
                Map<Long, BigDecimal> refundDistributionAmountMap = new HashMap<>();
                Map<Long, BigDecimal> refundDistributionTaxAmountMap = new HashMap<>();
                Map<Long, BigDecimal> refundServiceMap = new HashMap<>();
                //流程变更,实际已退商品数量要从退款详情里获取
                Map<Long, Integer> refundCount = new HashMap<>();
                if (CollectionUtil.isNotEmpty(refundList)) {
                    refundList.stream().collect(Collectors.groupingBy(RefundProductDetail::getOrderItemId)).forEach((k, v) -> {
                        refundAmountMap.put(k, v.stream().map(RefundProductDetail::getRefundProductAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                        refundDistributionAmountMap.put(k, v.stream().map(RefundProductDetail::getRefundDistributionAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                        refundDistributionTaxAmountMap.put(k, v.stream().map(RefundProductDetail::getRefundDistributionTaxAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                        refundServiceMap.put(k, v.stream().map(RefundProductDetail::getRefundServiceAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                    });
                    refundCount = refundList.stream().filter(l -> l.getRefundType().equals(RefundTypeEnum.LACK.getCode()) || l.getRefundType().equals(RefundTypeEnum.LESS.getCode()))
                            .collect(Collectors.groupingBy(RefundProductDetail::getOrderItemId, Collectors.summingInt(RefundProductDetail::getStockoutCount)));
                }
                QueryDistributionBo queryDistributionBo = new QueryDistributionBo();
                queryDistributionBo.setOrderItemIdList(itemIds);
                List<QueryDistributionVo> orderList = orderService.queryDistribution(queryDistributionBo);
                Map<Long, QueryDistributionVo> orderMap = orderList.stream().collect(Collectors.toMap(QueryDistributionVo::getId, Function.identity()));
                List<QueryDistributionItemVo> list = orderList.stream().map(QueryDistributionVo::getOrderItemList).flatMap(List::stream).toList();
                Map<Long, List<QueryDistributionItemVo>> map = list.stream().collect(Collectors.groupingBy(QueryDistributionItemVo::getOrderId));
                //获取销售批次商品信息
                RemoteQueryInfoListBo bo = new RemoteQueryInfoListBo();
                bo.setSupplierSkuIdList(list.stream().map(QueryDistributionItemVo::getSupplierSkuId).distinct().toList());
                List<RemoteSupplierSkuInfoVo> skuInfoVo = remoteSupplierSkuService.queryInfoList(bo);
                Map<Long, RemoteSupplierSkuInfoVo> skuMap = skuInfoVo.stream().collect(Collectors.toMap(RemoteSupplierSkuInfoVo::getId, Function.identity()));
                //获取物流信息
                RemoteRegionLogisticsQueryBo queryBo = new RemoteRegionLogisticsQueryBo();
                queryBo.setLogisticsIds(list.stream().map(QueryDistributionItemVo::getLogisticsId).distinct().toList());
                List<RemoteRegionLogisticsVo> logisticsVos = remoteRegionLogisticsService.queryList(queryBo);
                Map<Long, RemoteRegionLogisticsVo> logisticsMap = logisticsVos.stream().collect(Collectors.toMap(RemoteRegionLogisticsVo::getId, Function.identity()));
                //获取供应商
                List<Long> supIdList = new ArrayList<>(orderList.stream().map(QueryDistributionVo::getOrderItemList).flatMap(List::stream).map(QueryDistributionItemVo::getSupplierId).toList());
                List<RemoteSupplierVo> remoteSupplierVos = remoteSupplierService.getSupplierByIds(supIdList);
                Map<Long, String> stringMap = remoteSupplierVos.stream().collect(Collectors.toMap(RemoteSupplierVo::getId, RemoteSupplierVo::getCode, (v1, v2) -> v1));
                //订单项需要修改结算价
                List<OrderItem> updateList = new ArrayList<>();
                List<RefundRecord> updateRecords = new ArrayList<>();
                List<RefundProductDetail> addRecords = new ArrayList<>();
                Map<Long, Integer> finalRefundCount = refundCount;
                Map<Long, OrderFixPriceItemDTO> finalOrderFixPriceItemDTOMap = orderFixPriceItemDTOMap;
                map.forEach((k, v) -> {
                    RefundRecord record = new RefundRecord();
                    QueryDistributionVo orderInfo = orderMap.get(k);
                    record.setCode(generateRefundCode());
                    record.setOrderId(k);
                    record.setSourceCode(orderInfo.getCode());
                    record.setOrderCode(orderInfo.getCode());
                    record.setPlaceId(orderInfo.getPlaceId());
                    record.setPlacePath(orderInfo.getPlacePath());
                    record.setPlaceIdLevel2(orderInfo.getPlaceIdLevel2());
                    record.setCustomerId(v.get(0).getCustomerId());
                    record.setCustomerName(orderInfo.getCustomerName());
                    record.setCityWhId(v.get(0).getCityWhId());
                    record.setRegionWhId(v.get(0).getRegionWhId());
                    record.setRefundType(orderRefundDTO.getRefundType());
                    record.setRefundStatus(RefundStatusEnum.IN_REFUND.getCode());
                    record.setPayTime(v.get(0).getPayTime());
                    //赋值差额退类型
                    if (orderItemIdMap.containsKey(v.get(0).getId())){
                        record.setDifRefundType(orderItemIdMap.get(v.get(0).getId()));
                    }
                    //计算退商品金额
                    BigDecimal productAmount = BigDecimal.ZERO;
                    //非商品金额
                    BigDecimal otherAmount = BigDecimal.ZERO;
                    List<RefundProductDetail> refundProductDetails = new ArrayList<>();
                    for (QueryDistributionItemVo l : v) {
                        BigDecimal servicePrice = BigDecimal.ZERO;
                        BigDecimal refundAmount = refundAmountMap.getOrDefault(l.getId(), BigDecimal.ZERO);
                        BigDecimal refundDistributionAmount = refundDistributionAmountMap.getOrDefault(l.getId(), BigDecimal.ZERO);
                        BigDecimal refundDistributionTaxAmount = refundDistributionTaxAmountMap.getOrDefault(l.getId(), BigDecimal.ZERO);
                        BigDecimal refundServiceAmount = refundServiceMap.getOrDefault(l.getId(), BigDecimal.ZERO);
                        int count = finalRefundCount.getOrDefault(l.getId(), 0);
                        //差额退商品金额 = (下单价-降价) * 数量
                        BigDecimal price = skuMap.get(l.getSupplierSkuId()).getPrice();
                        if (orderRefundDTO.getIsFixPrice()){
                            OrderFixPriceItemDTO orderFixPriceItemDTO = finalOrderFixPriceItemDTOMap.get(l.getId());
                            if (ObjectUtil.isNull(orderFixPriceItemDTO)){
                                throw new ServiceException("调价退款失败,请联系管理员!");
                            }
                            price = orderFixPriceItemDTO.getFixPrice();
                        }
                        if(l.getFinalPrice().compareTo(price) <= 0){
                            continue;
                        }
                        BigDecimal productPrice = (l.getFinalPrice().subtract(price))
                                .multiply(BigDecimal.valueOf(l.getCount() - count)).setScale(2, RoundingMode.HALF_UP);
                        //差额退商品金额>实付金额(商品金额-优惠金额),差额最大取实付金额
                        BigDecimal subtract = (l.getPrice().multiply(BigDecimal.valueOf(l.getCount())))
                                .subtract(l.getSubsidyFreeAmount()).subtract(l.getProductFreeAmount()).subtract(refundAmount);
                        productPrice = productPrice.compareTo(subtract) > 0 ? subtract : productPrice;
                        productAmount = productAmount.add(productPrice);
                        Integer difRefundType = orderRefundDTO.getOrderItemIdList().get(0).getDifRefundType();
                        //商品价格跳档,退代采服务费
                        if (orderService.getPlatformServiceAmt(l.getFinalPrice()).compareTo(orderService.getPlatformServiceAmt(price)) > 0) {
                            servicePrice = orderService.getPlatformServiceAmt(l.getFinalPrice())
                                    .subtract(orderService.getPlatformServiceAmt(price))
                                    .multiply(new BigDecimal(l.getCount() - count)).setScale(2, RoundingMode.HALF_UP);
                            servicePrice = l.getPlatformServiceAmount().subtract(refundServiceAmount).compareTo(servicePrice) > 0 ? servicePrice : l.getPlatformServiceAmount().subtract(refundServiceAmount);
                            otherAmount = otherAmount.add(servicePrice);
                        }
                        if(productPrice.add(servicePrice).compareTo(BigDecimal.ZERO) == 0){
                            continue;
                        }
                        OrderItem item = new OrderItem();
                        item.setId(l.getId());
                        item.setFinalPrice(price);
                        updateList.add(item);
                        RefundProductDetail detail = new RefundProductDetail();
                        detail.setOrderItemId(l.getId());
                        detail.setSupplierSkuId(l.getSupplierSkuId());
                        detail.setSupplierSkuCode(skuMap.get(l.getSupplierSkuId()).getCode());
                        detail.setSaleDate(l.getSaleDate());
                        detail.setSupplierId(l.getSupplierId());
                        detail.setSupplierDeptId(l.getSupplierDeptId());
                        detail.setOriginalSupplierId(l.getSupplierId());
                        detail.setLogisticsId(l.getLogisticsId());
                        detail.setLogisticsName(logisticsMap.get(l.getLogisticsId()).getLogisticsName());
                        detail.setSpuId(skuMap.get(l.getSupplierSkuId()).getSpuId());
                        detail.setSpuName(l.getSpuName());
                        detail.setSpuStandards(skuMap.get(l.getSupplierSkuId()).getSpuStandards());
                        detail.setSpuGrossWeight(l.getSpuGrossWeight());
                        detail.setSpuNetWeight(l.getSpuNetWeight());
                        detail.setOrderCount(l.getCount());
                        detail.setStockoutCount(l.getCount()-count);
                        //服务单价 服务费÷总数量
                        BigDecimal singServicePrice = l.getPlatformServiceAmount().divide(new BigDecimal(l.getCount()), 2, RoundingMode.FLOOR);
                        detail.setCollectAgentPrice(singServicePrice);
                        detail.setOrderPrice(l.getPrice());
                        detail.setSettlePrice(price);
                        //缺货少货阶段都是未结算,退价类型取下单价
                        detail.setRefundPriceType(RefundPriceTypeEnum.ORDER_PRICE.getCode());
                        detail.setRefundStatus(RefundStatusEnum.IN_REFUND.getCode());
                        detail.setRefundType(RefundTypeEnum.DIFFERENCE.getCode());
                        detail.setImgUrl(l.getImgUrl());
                        detail.setRefundProductAmount(productPrice);
                        detail.setRefundOtherAmount(servicePrice);
                        detail.setRefundServiceAmount(servicePrice);
                        detail.setRefundAmount(productPrice.add(servicePrice));
                        detail.setRefundSubsidyFreeAmount(BigDecimal.ZERO);
                        detail.setRefundProductFreeAmount(BigDecimal.ZERO);
                        detail.setProdType(l.getProdType());
                        //存在佣金 看商品金额-已退金额是否超过佣金  超过则佣金需要补充扣款
                        if (l.getDistributionAmount().compareTo(BigDecimal.ZERO) > 0) {
                            //佣金-订单余额 > 0  ? 扣佣金 : 不处理
                            BigDecimal distributionAmount = l.getDistributionAmount().add(l.getDistributionTaxAmount()).subtract(refundDistributionAmount)
                                    .subtract(refundDistributionTaxAmount).subtract(subtract).add(productAmount);
                            BigDecimal refundDistribution = distributionAmount.compareTo(BigDecimal.ZERO) > 0 ? distributionAmount : BigDecimal.ZERO;
                            detail.setRefundDistributionAmount(refundDistribution.divide(BigDecimal.ONE.add(distributionTaxRate), 2, RoundingMode.HALF_UP));
                            detail.setRefundDistributionTaxAmount(refundDistribution.subtract(detail.getRefundDistributionAmount()));
                        }else {
                            detail.setRefundDistributionAmount(BigDecimal.ZERO);
                            detail.setRefundDistributionTaxAmount(BigDecimal.ZERO);
                        }
                        refundProductDetails.add(detail);
                    }
                    if (productAmount.add(otherAmount).compareTo(BigDecimal.ZERO) <= 0){
                        return;
                    }
                    record.setRefundProductAmount(productAmount);
                    record.setRefundOtherAmount(otherAmount);
                    record.setRefundAmount(record.getRefundOtherAmount().add(record.getRefundProductAmount()));
                    baseMapper.insert(record);
                    refundProductDetails.forEach(l -> l.setRefundRecordId(record.getId()));

                    OrderRefundBo orderRefundBo = new OrderRefundBo();
                    orderRefundBo.setOrderId(k).setOrderNo(record.getOrderCode()).setRefundId(record.getId()).setRefundNo(record.getCode())
                            .setBusiType(RefundBusiTypeEnum.REFUND_BAL.getCode()).setRefundAmt(record.getRefundProductAmount().add(record.getRefundOtherAmount()));
                    if (record.getRefundAmount().compareTo(audiAmount) < 0) {
                        orderRefundBo.setInfAuto(YNStatusEnum.ENABLE.getCode());
                        record.setAuditStatus(1);
                    } else {
                        orderRefundBo.setInfAuto(YNStatusEnum.DISABLE.getCode());
                        record.setAuditStatus(0);
                    }
                    List<OrderPaySplitBo> splits = new ArrayList<>();
                    //商品金额 扣供应商仓
                    if (record.getRefundProductAmount().compareTo(BigDecimal.ZERO) > 0) {
                        Map<Long, List<RefundProductDetail>> collect = refundProductDetails.stream().collect(Collectors.groupingBy(RefundProductDetail::getSupplierId));
                        collect.forEach((supId, details) -> {
                            Map<Long, List<RefundProductDetail>> listMap = details.stream().collect(Collectors.groupingBy(RefundProductDetail::getSupplierSkuId));
                            listMap.forEach((skuId, spuDetails) -> {
                                BigDecimal reduce = spuDetails.stream().map(RefundProductDetail::getRefundProductAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                                BigDecimal subsidy = spuDetails.stream().map(RefundProductDetail::getRefundSubsidyFreeAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                                BigDecimal distribution = spuDetails.stream().map(RefundProductDetail::getRefundDistributionAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                                BigDecimal distributionTax = spuDetails.stream().map(RefundProductDetail::getRefundDistributionTaxAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                                if (reduce.compareTo(BigDecimal.ZERO) > 0 || subsidy.compareTo(BigDecimal.ZERO) > 0) {
                                    OrderPaySplitBo split = new OrderPaySplitBo();
                                    split.setOrgId(supId).setOrgType(AccountOrgTypeEnum.SUPPLIER.getCode()).setOrgCode(stringMap.get(supId))
                                            .setTransAmt(reduce.add(subsidy)).setSkuId(skuId).setSplitAmt(reduce).setDeptId(spuDetails.get(0).getSupplierDeptId());
                                    if (distribution.compareTo(BigDecimal.ZERO) > 0) {
                                        //获取分销记录查询分销员id
                                        RemoteDistributionOrderInfoVo distributionOrderInfoVo = distributionService.getOrderInfo(details.get(0).getOrderItemId());
                                        if (distributionOrderInfoVo != null) {
                                            split.setCommCustomerId(distributionOrderInfoVo.getDistributorCustomerId());
                                        }
                                        split.setCommissionAmt(distribution.add(distributionTax));
                                        split.setCommSalaryAmt(distribution);
                                    }
                                    splits.add(split);
                                }
                            });
                        });
                    }
                    //非商品金额 扣总仓
                    if (record.getRefundOtherAmount().compareTo(BigDecimal.ZERO) > 0) {
                        OrderPaySplitBo split = new OrderPaySplitBo();
                        //获取总仓
                        RemoteRegionWhVo remoteRegionWhVo = remoteRegionWhService.queryById(record.getRegionWhId());
                        split.setOrgId(record.getRegionWhId()).setOrgType(AccountOrgTypeEnum.REGION_WH.getCode()).setOrgCode(remoteRegionWhVo.getRegionWhCode())
                                .setTransAmt(record.getRefundOtherAmount()).setSplitAmt(record.getRefundOtherAmount());
                        splits.add(split);
                    }
                    orderRefundBo.setSplits(splits);
                    orderRefundBo.setIsOccupy(OccupyEnum.NOT.getCode());
                    //都成功,退款成功改状态
                    record.setRefundStatus(RefundStatusEnum.IN_REFUND.getCode());
                    refundProductDetails.forEach(l -> {
                        l.setRefundStatus(RefundStatusEnum.IN_REFUND.getCode());
                        l.setRefundTime(null);
                    });
                    executeAfterTransaction(() -> {
                        try {
                            log.keyword("difRefund", record.getCode(), record.getOrderId()).info("差额退款调用退款入参【{}】", orderRefundBo);
                            remotePaymentService.refund(orderRefundBo);
                        } catch (Exception e) {
                            record.setRefundStatus(RefundStatusEnum.REFUND_FAIL.getCode());
                            refundProductDetails.forEach(l -> l.setRefundStatus(RefundStatusEnum.REFUND_FAIL.getCode()));
                            log.keyword("difRefund", record.getCode(), record.getOrderId()).error("差额退款失败:{}", e.getMessage());
                            record.setRemark("退款失败原因：" +e.getMessage());
                            baseMapper.updateById(record);
                        }
                    });
                    BigDecimal commissionAmt = orderRefundBo.getSplits().stream().map(OrderPaySplitBo::getCommissionAmt).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                    BigDecimal commissionTaxAmt = orderRefundBo.getSplits().stream().map(OrderPaySplitBo::getCommSalaryAmt).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                    if (commissionAmt.compareTo(BigDecimal.ZERO) > 0){
                        distributionService.refundOrderItem(record.getOrderId(), refundProductDetails.get(0).getOrderItemId(), commissionTaxAmt, commissionAmt.subtract(commissionTaxAmt));
                    }
                    updateRecords.add(record);
                    if (CollectionUtil.isNotEmpty(refundProductDetails)) {
                        addRecords.addAll(refundProductDetails);
                    }
                });
                if (CollectionUtil.isNotEmpty(updateList)) {
                    orderItemMapper.updateBatchById(updateList);
                }
                if (CollectionUtil.isNotEmpty(updateRecords)){
                    baseMapper.updateBatchById(updateRecords);
                }
                if(CollectionUtil.isNotEmpty(addRecords)){
                    refundProductDetailMapper.insertBatch(addRecords);
                }
            }
        }
    }

    /**
     * 取消订单生成退款单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelCreateRecord(Long orderId) {
        //获取订单项信息
        if (orderId != null) {
            QueryDistributionBo queryDistributionBo = new QueryDistributionBo();
            queryDistributionBo.setStatus(OrderStatusEnum.CANCEL.getCode());
            queryDistributionBo.setOrderId(orderId);
            List<QueryDistributionVo> orderList = orderService.queryDistribution(queryDistributionBo);
            if (CollectionUtil.isNotEmpty(orderList)) {
                Map<Long, QueryDistributionVo> orderMap = orderList.stream().collect(Collectors.toMap(QueryDistributionVo::getId, Function.identity()));
                List<QueryDistributionItemVo> list = orderList.stream().map(QueryDistributionVo::getOrderItemList).flatMap(List::stream).toList();
                Map<Long, List<QueryDistributionItemVo>> map = list.stream().collect(Collectors.groupingBy(QueryDistributionItemVo::getOrderId));
                //获取销售批次商品信息
                RemoteQueryInfoListBo bo = new RemoteQueryInfoListBo();
                bo.setSupplierSkuIdList(list.stream().map(QueryDistributionItemVo::getSupplierSkuId).distinct().toList());
                List<RemoteSupplierSkuInfoVo> skuInfoVo = remoteSupplierSkuService.queryInfoList(bo);
                Map<Long, RemoteSupplierSkuInfoVo> skuMap = skuInfoVo.stream().collect(Collectors.toMap(RemoteSupplierSkuInfoVo::getId, Function.identity()));
                //获取物流信息
                RemoteRegionLogisticsQueryBo queryBo = new RemoteRegionLogisticsQueryBo();
                queryBo.setLogisticsIds(list.stream().map(QueryDistributionItemVo::getLogisticsId).distinct().toList());
                List<RemoteRegionLogisticsVo> logisticsVos = remoteRegionLogisticsService.queryList(queryBo);
                Map<Long, RemoteRegionLogisticsVo> logisticsMap = logisticsVos.stream().collect(Collectors.toMap(RemoteRegionLogisticsVo::getId, Function.identity()));
                map.forEach((k, v) -> {
                    RefundRecord record = new RefundRecord();
                    QueryDistributionVo orderInfo = orderMap.get(k);
                    record.setCode(generateRefundCode());
                    record.setOrderId(k);
                    record.setSourceCode(orderInfo.getCode());
                    record.setOrderCode(orderInfo.getCode());
                    record.setCustomerId(v.get(0).getCustomerId());
                    record.setCustomerName(orderInfo.getCustomerName());
                    record.setCityWhId(v.get(0).getCityWhId());
                    record.setRegionWhId(v.get(0).getRegionWhId());
                    record.setRefundType(RefundTypeEnum.CANCEL.getCode());
                    record.setRefundStatus(RefundStatusEnum.IN_REFUND.getCode());
                    record.setPayTime(v.get(0).getPayTime());
                    BigDecimal productAmount = v.stream().map(QueryDistributionItemVo::getProductAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal productFreeAmount = v.stream().map(QueryDistributionItemVo::getProductFreeAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal regionSubsidyAmount = v.stream().map(QueryDistributionItemVo::getRegionSubsidyAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal skuSubsidyAmount = v.stream().map(QueryDistributionItemVo::getSkuSubsidyAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    record.setRefundProductAmount(productAmount.subtract(productFreeAmount));
                    record.setRefundOtherAmount(orderInfo.getTotalAmount().subtract(record.getRefundProductAmount()));
                    record.setRefundAmount(orderInfo.getTotalAmount());
                    record.setPlaceId(orderInfo.getPlaceId());
                    record.setPlaceIdLevel2(orderInfo.getPlaceIdLevel2());
                    record.setPlacePath(orderInfo.getPlacePath());
                    record.setRefundSubsidyFreeAmount(regionSubsidyAmount.add(skuSubsidyAmount));
                    if (record.getRefundProductAmount().add(record.getRefundOtherAmount()).compareTo(BigDecimal.ZERO) == 0){
                        return;
                    }
                    baseMapper.insert(record);
                    List<RefundProductDetail> refundProductDetails = new ArrayList<>();
                    for (QueryDistributionItemVo l : v) {
                        RefundProductDetail detail = new RefundProductDetail();
                        detail.setRefundRecordId(record.getId());
                        detail.setOrderItemId(l.getId());
                        detail.setSupplierSkuId(l.getSupplierSkuId());
                        detail.setSupplierSkuCode(skuMap.get(l.getSupplierSkuId()).getCode());
                        detail.setSaleDate(l.getSaleDate());
                        detail.setSupplierId(l.getSupplierId());
                        detail.setSupplierDeptId(l.getSupplierDeptId());
                        detail.setLogisticsId(l.getLogisticsId());
                        detail.setLogisticsName(logisticsMap.get(l.getLogisticsId()).getLogisticsName());
                        detail.setSpuId(skuMap.get(l.getSupplierSkuId()).getSpuId());
                        detail.setSpuName(l.getSpuName());
                        detail.setSpuStandards(skuMap.get(l.getSupplierSkuId()).getSpuStandards());
                        detail.setSpuGrossWeight(l.getSpuGrossWeight());
                        detail.setSpuNetWeight(l.getSpuNetWeight());
                        detail.setOrderCount(l.getCount());
                        detail.setStockoutCount(l.getCount());
                        detail.setRefundSubsidyFreeAmount(l.getSkuSubsidyAmount().add(l.getSubsidyFreeAmount()).add(l.getRegionSubsidyAmount()));
                        //服务单价 服务费÷总数量
                        BigDecimal singServicePrice = l.getPlatformServiceAmount().divide(new BigDecimal(l.getCount()), 2, RoundingMode.FLOOR);
                        detail.setCollectAgentPrice(singServicePrice);
                        detail.setOrderPrice(l.getPrice());
                        detail.setSettlePrice(l.getFinalPrice());
                        //缺货少货阶段都是未结算,退价类型取下单价
                        detail.setRefundPriceType(RefundPriceTypeEnum.ORDER_PRICE.getCode());
                        detail.setRefundStatus(RefundStatusEnum.IN_REFUND.getCode());
                        detail.setRefundType(RefundTypeEnum.CANCEL.getCode());
                        detail.setImgUrl(l.getImgUrl());
                        detail.setRefundProductAmount(l.getProductAmount().subtract(l.getProductFreeAmount()));
                        detail.setRefundProductFreeAmount(l.getProductFreeAmount());
                        detail.setRefundServiceAmount(l.getPlatformServiceAmount().subtract(l.getPlatformServiceFreeAmount()));
                        detail.setRefundPlatformFreight(l.getPlatformFreightAmount().subtract(l.getPlatformFreightFreeAmount()));
                        detail.setRefundRegionFreight(l.getRegionFreightAmount().subtract(l.getRegionFreightFreeAmount()));
                        detail.setRefundFreightAmount(l.getFreightTotalAmount().subtract(l.getFreightTotalFreeAmount()));
                        detail.setRefundBaseFreight(l.getBaseFreightAmount().subtract(l.getBaseFreightFreeAmount()));
                        detail.setRefundPlatformFreightAmountLevel2(l.getPlatformFreightAmountLevel2());
                        detail.setProdType(l.getProdType());
                        detail.setRefundDistributionAmount(l.getDistributionAmount());
                        detail.setRefundDistributionTaxAmount(l.getDistributionTaxAmount());
                        detail.setRefundFinancialServicePrice(l.getFinancialServiceAmount());
                        detail.setRefundAmount(l.getTotalAmount());
                        detail.setRefundOtherAmount(l.getTotalAmount().subtract(l.getProductAmount()).add(l.getProductFreeAmount()));
                        detail.setRefundSubsidyFreeAmount(l.getRegionSubsidyAmount());
                        refundProductDetails.add(detail);
                    }
                    OrderPayCancelBo orderRefundBo = new OrderPayCancelBo();
                    //todo 分销金额处理
                    orderRefundBo.setOrderNo(orderInfo.getCode());
                    orderRefundBo.setRefundId(record.getId());
                    orderRefundBo.setRefundNo(record.getCode());
                    //都成功,退款成功改状态
                    record.setRefundStatus(RefundStatusEnum.IN_REFUND.getCode());
                    record.setAuditStatus(1);
                    refundProductDetails.forEach(l -> {
                        l.setRefundStatus(RefundStatusEnum.IN_REFUND.getCode());
                        l.setRefundTime(null);
                    });

                    executeAfterTransaction(() -> {
                        //真.调用退款了
                        try {
                            log.keyword("cancelRefund", record.getCode(), record.getOrderId()).info("用户取消订单调用退款入参【{}】", orderRefundBo);
                            remotePaymentService.payCancel(orderRefundBo);
                        } catch (ServiceException e) {
                            record.setRemark("退款失败原因：" +e.getMessage());
                            log.keyword("cancelRefund",record.getCode(), record.getOrderId()).error("用户取消订单退款失败:{}", e.getMessage());
                        }
                    });
                    baseMapper.updateById(record);
                    if(CollectionUtil.isNotEmpty(refundProductDetails)){
                        refundProductDetailMapper.insertBatch(refundProductDetails);
                    }
                });
            }
        }
    }

    /**
     * 支付成功取消订单报错数据修复接口
     * @param orderIdList
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void exceptionRefund(List<Long> orderIdList) {
        for(Long orderId : orderIdList) {
            QueryDistributionBo queryDistributionBo = new QueryDistributionBo();
            queryDistributionBo.setStatus(OrderStatusEnum.CANCEL.getCode());
            queryDistributionBo.setOrderId(orderId);
            List<QueryDistributionVo> orderList = orderService.queryDistribution(queryDistributionBo);
            if (CollectionUtil.isNotEmpty(orderList)) {
                Map<Long, QueryDistributionVo> orderMap = orderList.stream().collect(Collectors.toMap(QueryDistributionVo::getId, Function.identity()));
                List<QueryDistributionItemVo> list = orderList.stream().map(QueryDistributionVo::getOrderItemList).flatMap(List::stream).toList();
                Map<Long, List<QueryDistributionItemVo>> map = list.stream().collect(Collectors.groupingBy(QueryDistributionItemVo::getOrderId));
                //获取销售批次商品信息
                RemoteQueryInfoListBo bo = new RemoteQueryInfoListBo();
                bo.setSupplierSkuIdList(list.stream().map(QueryDistributionItemVo::getSupplierSkuId).distinct().toList());
                List<RemoteSupplierSkuInfoVo> skuInfoVo = remoteSupplierSkuService.queryInfoList(bo);
                Map<Long, RemoteSupplierSkuInfoVo> skuMap = skuInfoVo.stream().collect(Collectors.toMap(RemoteSupplierSkuInfoVo::getId, Function.identity()));
                //获取物流信息
                RemoteRegionLogisticsQueryBo queryBo = new RemoteRegionLogisticsQueryBo();
                queryBo.setLogisticsIds(list.stream().map(QueryDistributionItemVo::getLogisticsId).distinct().toList());
                List<RemoteRegionLogisticsVo> logisticsVos = remoteRegionLogisticsService.queryList(queryBo);
                Map<Long, RemoteRegionLogisticsVo> logisticsMap = logisticsVos.stream().collect(Collectors.toMap(RemoteRegionLogisticsVo::getId, Function.identity()));
                map.forEach((k, v) -> {
                    RefundRecord record = new RefundRecord();
                    QueryDistributionVo orderInfo = orderMap.get(k);
                    record.setCode(generateRefundCode());
                    record.setOrderId(k);
                    record.setSourceCode(orderInfo.getCode());
                    record.setOrderCode(orderInfo.getCode());
                    record.setCustomerId(v.get(0).getCustomerId());
                    record.setCustomerName(orderInfo.getCustomerName());
                    record.setPlaceId(orderInfo.getPlaceId());
                    record.setPlaceIdLevel2(orderInfo.getPlaceIdLevel2());
                    record.setPlacePath(orderInfo.getPlacePath());
                    record.setCityWhId(v.get(0).getCityWhId());
                    record.setRegionWhId(v.get(0).getRegionWhId());
                    record.setRefundType(RefundTypeEnum.CANCEL.getCode());
                    record.setPayTime(v.get(0).getPayTime());
                    record.setRefundProductAmount(v.stream().map(QueryDistributionItemVo::getProductAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                    record.setRefundOtherAmount(v.stream().map(item -> item.getOtherTotalAmount().subtract(item.getFreeTotalAmount())).reduce(BigDecimal.ZERO, BigDecimal::add));
                    record.setRefundAmount(orderInfo.getTotalAmount());
                    if (record.getRefundProductAmount().add(record.getRefundOtherAmount()).compareTo(BigDecimal.ZERO) == 0){
                        return;
                    }
                    record.setRefundStatus(RefundStatusEnum.HAS_REFUND.getCode());
                    record.setAuditStatus(1);
                    baseMapper.insert(record);
                    List<RefundProductDetail> refundProductDetails = new ArrayList<>();
                    BigDecimal refundServiceAmount = BigDecimal.ZERO;
                    Integer totalCount = 0;
                    for (QueryDistributionItemVo l : v) {
                        RefundProductDetail detail = new RefundProductDetail();
                        detail.setRefundRecordId(record.getId());
                        detail.setOrderItemId(l.getId());
                        detail.setSupplierSkuId(l.getSupplierSkuId());
                        detail.setSupplierSkuCode(skuMap.get(l.getSupplierSkuId()).getCode());
                        detail.setSaleDate(l.getSaleDate());
                        detail.setSupplierId(l.getSupplierId());
                        detail.setSupplierDeptId(l.getSupplierDeptId());
                        detail.setLogisticsId(l.getLogisticsId());
                        detail.setLogisticsName(logisticsMap.get(l.getLogisticsId()).getLogisticsName());
                        detail.setSpuId(skuMap.get(l.getSupplierSkuId()).getSpuId());
                        detail.setSpuName(l.getSpuName());
                        detail.setSpuStandards(skuMap.get(l.getSupplierSkuId()).getSpuStandards());
                        detail.setSpuGrossWeight(l.getSpuGrossWeight());
                        detail.setSpuNetWeight(l.getSpuNetWeight());
                        detail.setOrderCount(l.getCount());
                        detail.setStockoutCount(l.getCount());
                        //服务单价 服务费÷总数量
                        BigDecimal singServicePrice = l.getPlatformServiceAmount().divide(new BigDecimal(l.getCount()), 2, RoundingMode.FLOOR);
                        detail.setCollectAgentPrice(singServicePrice);
                        detail.setOrderPrice(l.getPrice());
                        detail.setSettlePrice(l.getFinalPrice());
                        //缺货少货阶段都是未结算,退价类型取下单价
                        detail.setRefundPriceType(RefundPriceTypeEnum.ORDER_PRICE.getCode());
                        detail.setRefundStatus(RefundStatusEnum.HAS_REFUND.getCode());
                        detail.setRefundType(RefundTypeEnum.CANCEL.getCode());
                        detail.setImgUrl(l.getImgUrl());
                        detail.setRefundProductAmount(l.getProductAmount());
                        detail.setRefundServiceAmount(l.getPlatformServiceAmount().subtract(l.getPlatformServiceFreeAmount()));
                        detail.setRefundPlatformFreight(l.getPlatformFreightAmount().subtract(l.getPlatformFreightFreeAmount()));
                        detail.setRefundPlatformFreightAmountLevel2(l.getPlatformFreightAmountLevel2());
                        detail.setRefundRegionFreight(l.getRegionFreightAmount().subtract(l.getRegionFreightFreeAmount()));
                        detail.setRefundFreightAmount(l.getFreightTotalAmount().subtract(l.getFreightTotalFreeAmount()));
                        detail.setRefundBaseFreight(l.getBaseFreightAmount().subtract(l.getBaseFreightFreeAmount()));
                        detail.setProdType(l.getProdType());
                        totalCount += l.getCount();
                        //分摊金融手续费
                        BigDecimal apportionRefundService = getApportionRefundService(orderInfo.getTotalAmount().subtract(orderInfo.getFinancialServiceAmount())
                                , l.getFinancialServiceAmount(), l.getTotalAmount(), refundServiceAmount);
                        if (ObjectUtil.equals(totalCount,orderInfo.getCount())) {
                            apportionRefundService = orderInfo.getFinancialServiceAmount().subtract(refundServiceAmount);
                        }
                        refundServiceAmount = refundServiceAmount.add(apportionRefundService);
                        detail.setRefundFinancialServicePrice(apportionRefundService);
                        detail.setRefundAmount(l.getTotalAmount().add(apportionRefundService));
                        detail.setRefundOtherAmount(l.getOtherTotalAmount().subtract(l.getFreeTotalAmount()).add(apportionRefundService));
                        detail.setRefundTime(l.getPayTime());
                        detail.setRefundStatus(RefundStatusEnum.HAS_REFUND.getCode());
                        refundProductDetails.add(detail);
                    }
                    if(CollectionUtil.isNotEmpty(refundProductDetails)){
                        refundProductDetailMapper.insertBatch(refundProductDetails);
                    }
                });
            }
        }
    }

    /**
     * 报损生成退款单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @Deprecated
    public void lossCreateRecord(List<CreateRefundRecordBO> boList) {
        //获取订单项信息
        if (CollectionUtil.isNotEmpty(boList)) {
            QueryDistributionBo queryDistributionBo = new QueryDistributionBo();
            List<Long> itemId = boList.stream().map(CreateRefundRecordBO::getOrderItemId).toList();
            queryDistributionBo.setOrderItemIdList(itemId);
            List<QueryDistributionVo> orderList = orderService.queryDistribution(queryDistributionBo);
            if (CollectionUtil.isNotEmpty(orderList)) {
                Map<Long, CreateRefundRecordBO> refundMap = boList.stream().collect(Collectors.toMap(CreateRefundRecordBO::getOrderItemId, Function.identity(), (k, v) -> v));
                Map<Long, QueryDistributionVo> orderMap = orderList.stream().collect(Collectors.toMap(QueryDistributionVo::getId, Function.identity()));
                List<QueryDistributionItemVo> list = orderList.stream().map(QueryDistributionVo::getOrderItemList).flatMap(List::stream).toList();
                Map<Long, List<QueryDistributionItemVo>> map = list.stream().collect(Collectors.groupingBy(QueryDistributionItemVo::getOrderId));
                //获取销售批次商品信息
                RemoteQueryInfoListBo bo = new RemoteQueryInfoListBo();
                bo.setSupplierSkuIdList(list.stream().map(QueryDistributionItemVo::getSupplierSkuId).distinct().toList());
                List<RemoteSupplierSkuInfoVo> skuInfoVo = remoteSupplierSkuService.queryInfoList(bo);
                Map<Long, RemoteSupplierSkuInfoVo> skuMap = skuInfoVo.stream().collect(Collectors.toMap(RemoteSupplierSkuInfoVo::getId, Function.identity()));
                //获取物流信息
                RemoteRegionLogisticsQueryBo queryBo = new RemoteRegionLogisticsQueryBo();
                queryBo.setLogisticsIds(list.stream().map(QueryDistributionItemVo::getLogisticsId).distinct().toList());
                List<RemoteRegionLogisticsVo> logisticsVos = remoteRegionLogisticsService.queryList(queryBo);
                Map<Long, RemoteRegionLogisticsVo> logisticsMap = logisticsVos.stream().collect(Collectors.toMap(RemoteRegionLogisticsVo::getId, Function.identity()));
                //获取供应商
                List<Long> supList = orderList.stream().map(QueryDistributionVo::getOrderItemList).flatMap(List::stream).map(QueryDistributionItemVo::getSupplierId).toList();
                List<RemoteSupplierVo> remoteSupplierVos = remoteSupplierService.getSupplierByIds(supList);
                Map<Long, String> stringMap = remoteSupplierVos.stream().collect(Collectors.toMap(RemoteSupplierVo::getId, RemoteSupplierVo::getCode, (v1, v2) -> v1));
                List<RefundRecord> updateRecords = new ArrayList<>();
                List<RefundProductDetail> addRecords = new ArrayList<>();
                map.forEach((k, v) -> {
                    RefundRecord record = new RefundRecord();
                    QueryDistributionVo orderInfo = orderMap.get(k);
                    record.setCode(generateRefundCode());
                    record.setOrderId(k);
                    record.setSourceCode(boList.get(0).getSourceCode());
                    record.setOrderCode(orderInfo.getCode());
                    record.setCustomerId(v.get(0).getCustomerId());
                    record.setCustomerName(orderInfo.getCustomerName());
                    record.setCityWhId(v.get(0).getCityWhId());
                    record.setRegionWhId(v.get(0).getRegionWhId());
                    record.setRefundType(RefundTypeEnum.REPORT_LOSS.getCode());
                    record.setRefundStatus(RefundStatusEnum.IN_REFUND.getCode());
                    record.setPlaceId(orderInfo.getPlaceId());
                    record.setPlaceIdLevel2(orderInfo.getPlaceIdLevel2());
                    record.setPlacePath(orderInfo.getPlacePath());
                    record.setPayTime(v.get(0).getPayTime());
                    List<RefundProductDetail> refundProductDetails = new ArrayList<>();
                    BigDecimal productAmount = BigDecimal.ZERO;
                    BigDecimal refundOtherAmount = BigDecimal.ZERO;
                    for (QueryDistributionItemVo l : v) {
                        RefundProductDetail detail = new RefundProductDetail();
                        detail.setRefundRecordId(record.getId());
                        detail.setOrderItemId(l.getId());
                        detail.setSupplierSkuId(l.getSupplierSkuId());
                        detail.setSupplierSkuCode(skuMap.get(l.getSupplierSkuId()).getCode());
                        detail.setSaleDate(l.getSaleDate());
                        detail.setSupplierId(l.getSupplierId());
                        detail.setSupplierDeptId(l.getSupplierDeptId());
                        detail.setOriginalSupplierId(l.getSupplierId());
                        detail.setLogisticsId(l.getLogisticsId());
                        detail.setLogisticsName(logisticsMap.get(l.getLogisticsId()).getLogisticsName());
                        detail.setSpuId(skuMap.get(l.getSupplierSkuId()).getSpuId());
                        detail.setSpuName(l.getSpuName());
                        detail.setSpuStandards(skuMap.get(l.getSupplierSkuId()).getSpuStandards());
                        detail.setSpuGrossWeight(l.getSpuGrossWeight());
                        detail.setSpuNetWeight(l.getSpuNetWeight());
                        detail.setOrderCount(l.getCount());
                        detail.setStockoutCount(0);
                        //服务单价 服务费÷总数量
                        BigDecimal singServicePrice = l.getPlatformServiceAmount().divide(new BigDecimal(l.getCount()), 2, RoundingMode.FLOOR);
                        detail.setCollectAgentPrice(singServicePrice);
                        detail.setOrderPrice(l.getPrice());
                        detail.setSettlePrice(l.getFinalPrice());
                        //缺货少货阶段都是未结算,退价类型取下单价
                        detail.setRefundPriceType(RefundPriceTypeEnum.ORDER_PRICE.getCode());
                        detail.setRefundStatus(RefundStatusEnum.IN_REFUND.getCode());
                        detail.setRefundType(RefundTypeEnum.REPORT_LOSS.getCode());
                        detail.setImgUrl(l.getImgUrl());
                        detail.setRefundProductAmount(refundMap.get(l.getId()).getRefundProductAmount());
                        detail.setRefundOtherAmount(refundMap.get(l.getId()).getRefundOtherAmount());
                        detail.setRefundAmount(detail.getRefundProductAmount().add(detail.getRefundOtherAmount()));
                        detail.setRefundSubsidyFreeAmount(refundMap.get(l.getId()).getRefundSubsidyFreeAmount());
                        detail.setRefundProductFreeAmount(BigDecimal.ZERO);
                        detail.setProdType(l.getProdType());
                        productAmount = productAmount.add(detail.getRefundProductAmount());
                        refundOtherAmount = refundOtherAmount.add(detail.getRefundOtherAmount());
                        refundProductDetails.add(detail);
                    }
                    record.setRefundProductAmount(productAmount);
                    record.setRefundOtherAmount(refundOtherAmount);
                    record.setRefundAmount(record.getRefundOtherAmount().add(record.getRefundProductAmount()));
                    record.setRefundSubsidyFreeAmount(refundProductDetails.stream().map(RefundProductDetail::getRefundSubsidyFreeAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                    record.setRefundProductFreeAmount(BigDecimal.ZERO);
                    baseMapper.insert(record);
                    refundProductDetails.forEach(l -> l.setRefundRecordId(record.getId()));
                    OrderRefundBo orderRefundBo = new OrderRefundBo();
                    orderRefundBo.setOrderId(k).setOrderNo(record.getOrderCode()).setRefundId(record.getId()).setRefundNo(record.getCode())
                            .setBusiType(RefundBusiTypeEnum.REFUND_LOSS.getCode()).setRefundAmt(record.getRefundProductAmount().add(record.getRefundOtherAmount()));
                    List<OrderPaySplitBo> splits = new ArrayList<>();
                    orderRefundBo.setInfAuto(YNStatusEnum.ENABLE.getCode());
                    //商品金额 扣供应商仓
                    if (record.getRefundProductAmount().compareTo(BigDecimal.ZERO) > 0) {
                        Map<Long, List<RefundProductDetail>> collect = refundProductDetails.stream().collect(Collectors.groupingBy(RefundProductDetail::getSupplierId));
                        collect.forEach((supId, details) -> {
                            Map<Long, List<RefundProductDetail>> listMap = details.stream().collect(Collectors.groupingBy(RefundProductDetail::getSupplierSkuId));
                            listMap.forEach((skuId, spuDetails) -> {
                                BigDecimal reduce = spuDetails.stream().map(RefundProductDetail::getRefundProductAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                                if (reduce.compareTo(BigDecimal.ZERO) > 0 ) {
                                    OrderPaySplitBo split = new OrderPaySplitBo();
                                    split.setOrgId(supId).setOrgType(AccountOrgTypeEnum.SUPPLIER.getCode()).setOrgCode(stringMap.get(supId))
                                            .setTransAmt(reduce).setSkuId(skuId).setSplitAmt(reduce).setDeptId(spuDetails.get(0).getSupplierDeptId());
                                    splits.add(split);
                                }
                            });
                        });
                    }
                    //非商品金额 扣总仓
                    if (record.getRefundOtherAmount().compareTo(BigDecimal.ZERO) > 0) {
                        OrderPaySplitBo split = new OrderPaySplitBo();
                        //获取总仓
                        RemoteRegionWhVo remoteRegionWhVo = remoteRegionWhService.queryById(record.getRegionWhId());
                        split.setOrgId(record.getRegionWhId()).setOrgType(AccountOrgTypeEnum.REGION_WH.getCode()).setOrgCode(remoteRegionWhVo.getRegionWhCode())
                                .setTransAmt(record.getRefundOtherAmount()).setSplitAmt(record.getRefundOtherAmount());
                        splits.add(split);
                    }
                    if (record.getRefundAmount().compareTo(audiAmount) < 0) {
                        orderRefundBo.setInfAuto(YNStatusEnum.ENABLE.getCode());
                        record.setAuditStatus(YNStatusEnum.ENABLE.getCode());
                    } else {
                        orderRefundBo.setInfAuto(YNStatusEnum.DISABLE.getCode());
                        record.setAuditStatus(YNStatusEnum.DISABLE.getCode());
                    }
                    orderRefundBo.setSplits(splits);
                    //都成功,退款成功改状态
                    record.setRefundStatus(RefundStatusEnum.IN_REFUND.getCode());
                    refundProductDetails.forEach(l -> {
                        l.setRefundStatus(RefundStatusEnum.IN_REFUND.getCode());
                        l.setRefundTime(null);
                    });
                    //真.调用退款了
                    try {
                        log.keyword("lossRefund", record.getCode(), record.getOrderId()).info("报损退款调用退款入参【{}】", orderRefundBo);
                        remotePaymentService.refund(orderRefundBo);
                    } catch (ServiceException e) {
                        record.setRefundStatus(RefundStatusEnum.REFUND_FAIL.getCode());
                        refundProductDetails.forEach(l -> l.setRefundStatus(RefundStatusEnum.REFUND_FAIL.getCode()));
                        log.keyword("lossRefund", record.getCode(), record.getOrderId()).error("报损退款失败:{}", e.getMessage());
                        record.setRemark("退款失败原因：" +e.getMessage());
                    }
                    updateRecords.add(record);
                    if (CollectionUtil.isNotEmpty(refundProductDetails)) {
                        addRecords.addAll(refundProductDetails);
                    }
                });
                if (CollectionUtil.isNotEmpty(updateRecords)) {
                    baseMapper.updateBatchById(updateRecords);
                }
                if (CollectionUtil.isNotEmpty(addRecords)) {
                    refundProductDetailMapper.insertBatch(addRecords);
                }
            }
        }
    }


    /**
     * 报损生成退款单
     *  @param lossRefundBo
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void lossCreateRecord(CreateRefundRecordBO lossRefundBo, OccupyEnum occupyEnum, ReportLossOrder lossOrder) {
        if (ObjectUtil.isNull(lossRefundBo)) {
            return;
        }
        String cacheKey = OrderCacheNames.SETTLE_OCCUPY_LOSS + lossRefundBo.getSourceCode();
        if (occupyEnum != null && occupyEnum == OccupyEnum.YES) {
            //如果是占用，则判断是否已经占用，占用就直接返回
            if (RedisUtils.isExistsObject(cacheKey)) {
                return;
            }
        }
        //未指定占用状态，则判断是否已经有占用，没有占用就执行下面的正常退款或占用逻辑
        if (occupyEnum == null) {
            //有占用的情况下，进行释放
            OrderRefundBo orderRefundBo = RedisUtils.getCacheObject(cacheKey);
            if (orderRefundBo != null) {
                RefundRecord recordUp = new RefundRecord();

                orderRefundBo.setIsOccupy(OccupyEnum.RELEASE.getCode());
                orderRefundBo.setRefundAmt(lossRefundBo.getRefundProductAmount().add(lossRefundBo.getRefundOtherAmount()));
                //根据最新的退款金额，判断是否需要自动调用退款接口，同步修改退款单的审核状态
                if (orderRefundBo.getRefundAmt().compareTo(audiAmount) < 0) {
                    orderRefundBo.setInfAuto(YNStatusEnum.ENABLE.getCode());
                    recordUp.setAuditStatus(YNStatusEnum.ENABLE.getCode());
                } else {
                    orderRefundBo.setInfAuto(YNStatusEnum.DISABLE.getCode());
                    recordUp.setAuditStatus(YNStatusEnum.DISABLE.getCode());
                }
                //报损只会有一条
                for (OrderPaySplitBo split : orderRefundBo.getSplits()) {
                    split.setSplitAmt(lossRefundBo.getRefundProductAmount());
                    split.setTransAmt(lossRefundBo.getRefundProductAmount().add(lossRefundBo.getRefundSubsidyFreeAmount()));
                }

                recordUp.setId(orderRefundBo.getRefundId());
                recordUp.setRefundProductAmount(lossRefundBo.getRefundProductAmount());
                recordUp.setRefundOtherAmount(lossRefundBo.getRefundOtherAmount());
                recordUp.setRefundAmount(lossRefundBo.getRefundProductAmount().add(lossRefundBo.getRefundOtherAmount()));
                baseMapper.updateById(recordUp);

                LambdaQueryWrapper<RefundProductDetail> lqw = Wrappers.lambdaQuery();
                lqw.eq(RefundProductDetail::getRefundRecordId, orderRefundBo.getRefundId());
                RefundProductDetail detailUp = new RefundProductDetail();
                detailUp.setRefundProductAmount(lossRefundBo.getRefundProductAmount());
                detailUp.setRefundOtherAmount(lossRefundBo.getRefundOtherAmount());
                detailUp.setRefundSubsidyFreeAmount(lossRefundBo.getRefundSubsidyFreeAmount());
                detailUp.setRefundAmount(lossRefundBo.getRefundProductAmount().add(lossRefundBo.getRefundOtherAmount()));
                getDistributionAmount(lossOrder, orderRefundBo, detailUp);
                refundProductDetailMapper.update(detailUp, lqw);
                executeAfterTransaction(() -> {
                    try {
                        log.keyword("lossRefund", lossRefundBo.getOrderItemId(), lossRefundBo.getOrderId()).info("报损退款调用退款入参【{}】", orderRefundBo);
                        remotePaymentService.refund(orderRefundBo);
                        RedisUtils.deleteObject(cacheKey);
                    } catch (Exception e) {
                        recordUp.setRefundStatus(RefundStatusEnum.REFUND_FAIL.getCode());
                        baseMapper.updateById(recordUp);
                        detailUp.setRefundStatus(RefundStatusEnum.REFUND_FAIL.getCode());
                        refundProductDetailMapper.update(detailUp, lqw);
                        log.keyword("lossRefund", lossRefundBo.getOrderItemId(), lossRefundBo.getOrderId()).error("报损退款失败:{}", e.getMessage());
                    }
                });
                if (ReportLossStatusEnum.isComplete(lossOrder.getLossStatus())) {
                    return;
                }
                //前面释放之后，重新计算占用金额，重新占用
                lossRefundBo.setRefundProductAmount(lossOrder.getLossAmount().subtract(lossOrder.getRefundGoodsAmount()));
                lossRefundBo.setRefundSubsidyFreeAmount(lossOrder.getLossSubsidyFreeAmount().subtract(lossOrder.getRefundSubsidyFreeAmount()));
                occupyEnum = OccupyEnum.YES;
            } else {
                //未占用的情况下，普通退款
                occupyEnum = OccupyEnum.NOT;
            }
        }


        QueryDistributionBo queryDistributionBo = new QueryDistributionBo();
        List<Long> itemId = Lists.newArrayList(lossRefundBo.getOrderItemId());
        queryDistributionBo.setOrderItemIdList(itemId);
        List<QueryDistributionVo> orderList = orderService.queryDistribution(queryDistributionBo);
        if (CollUtil.isEmpty(orderList)) {
            return;
        }
        QueryDistributionVo orderInfo = orderList.get(0);

        QueryDistributionItemVo itemVo = orderInfo.getOrderItemList().get(0);
        //获取销售批次商品信息
        RemoteQueryInfoListBo bo = new RemoteQueryInfoListBo();
        bo.setSupplierSkuIdList(Lists.newArrayList(itemVo.getSupplierSkuId()));

        RemoteSupplierSkuInfoVo skuInfoVo = remoteSupplierSkuService.queryInfoList(bo).get(0);

        //获取物流信息
        RemoteRegionLogisticsQueryBo queryBo = new RemoteRegionLogisticsQueryBo();
        queryBo.setLogisticsIds(Lists.newArrayList(itemVo.getLogisticsId()));
        List<RemoteRegionLogisticsVo> logisticsVos = remoteRegionLogisticsService.queryList(queryBo);
        Map<Long, RemoteRegionLogisticsVo> logisticsMap = logisticsVos.stream().collect(Collectors.toMap(RemoteRegionLogisticsVo::getId, Function.identity()));
        //处理补贴
        BigDecimal refundSubsidyFreeAmount = BigDecimal.ZERO;
        if (itemVo.getRegionSubsidyAmount().add(itemVo.getSkuSubsidyAmount()).compareTo(BigDecimal.ZERO) > 0){
            //获取退款记录详情
            LambdaQueryWrapper<RefundProductDetail> qw = Wrappers.lambdaQuery();
            qw.eq(RefundProductDetail::getOrderItemId, itemVo.getId()).eq(RefundProductDetail::getDelFlag, 0)
                    .notIn(RefundProductDetail::getRefundStatus, RefundStatusEnum.REFUND_FAIL.getCode());
            List<RefundProductDetail> selectList = refundProductDetailMapper.selectList(qw);
            if (CollectionUtil.isNotEmpty(selectList)) {
                //已报损金额
                BigDecimal refundAmount = selectList.stream().filter(l -> l.getRefundType().equals(RefundTypeEnum.REPORT_LOSS.getCode()))
                        .map(RefundProductDetail::getRefundProductAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                //退款超一半，全退掉
                if (itemVo.getFinalPrice().multiply(BigDecimal.valueOf(itemVo.getCount() - itemVo.getRefundCount())).multiply(BigDecimal.valueOf(0.5))
                        .compareTo(refundAmount.add(lossRefundBo.getRefundProductAmount())) <= 0) {
                    BigDecimal amount = selectList.stream().map(RefundProductDetail::getRefundSubsidyFreeAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                    refundSubsidyFreeAmount = itemVo.getRegionSubsidyAmount().add(itemVo.getSkuSubsidyAmount()).subtract(amount);
                }
            }
        }

        RefundProductDetail detail = new RefundProductDetail();
        detail.setOrderItemId(itemVo.getId());
        detail.setSupplierSkuId(itemVo.getSupplierSkuId());
        detail.setSupplierSkuCode(skuInfoVo.getCode());
        detail.setSaleDate(itemVo.getSaleDate());
        detail.setSupplierId(itemVo.getSupplierId());
        detail.setSupplierDeptId(itemVo.getSupplierDeptId());
        detail.setOriginalSupplierId(itemVo.getSupplierId());
        detail.setLogisticsId(itemVo.getLogisticsId());
        detail.setLogisticsName(logisticsMap.get(itemVo.getLogisticsId()).getLogisticsName());
        detail.setSpuId(skuInfoVo.getSpuId());
        detail.setSpuName(itemVo.getSpuName());
        detail.setSpuStandards(skuInfoVo.getSpuStandards());
        detail.setSpuGrossWeight(itemVo.getSpuGrossWeight());
        detail.setSpuNetWeight(itemVo.getSpuNetWeight());
        detail.setOrderCount(itemVo.getCount());
        detail.setStockoutCount(0);
        //服务单价 服务费÷总数量
        BigDecimal singServicePrice = itemVo.getPlatformServiceAmount().divide(new BigDecimal(itemVo.getCount()), 2, RoundingMode.FLOOR);
        detail.setCollectAgentPrice(singServicePrice);
        detail.setOrderPrice(itemVo.getPrice());
        detail.setSettlePrice(itemVo.getFinalPrice());
        //缺货少货阶段都是未结算,退价类型取下单价
        detail.setRefundPriceType(RefundPriceTypeEnum.ORDER_PRICE.getCode());
        detail.setRefundStatus(RefundStatusEnum.IN_REFUND.getCode());
        detail.setRefundType(RefundTypeEnum.REPORT_LOSS.getCode());
        detail.setImgUrl(itemVo.getImgUrl());
        detail.setRefundProductAmount(lossRefundBo.getRefundProductAmount());
        detail.setRefundOtherAmount(lossRefundBo.getRefundOtherAmount());
        detail.setRefundAmount(detail.getRefundProductAmount().add(detail.getRefundOtherAmount()));
        detail.setRefundSubsidyFreeAmount(refundSubsidyFreeAmount);
        detail.setProdType(itemVo.getProdType());

        RefundRecord record = new RefundRecord();
        record.setCode(generateRefundCode());
        record.setOrderId(orderInfo.getId());
        record.setSourceCode(lossRefundBo.getSourceCode());
        record.setOrderCode(orderInfo.getCode());
        record.setCustomerId(orderInfo.getCustomerId());
        record.setCustomerName(orderInfo.getCustomerName());
        record.setCityWhId(itemVo.getCityWhId());
        record.setRegionWhId(itemVo.getRegionWhId());
        record.setRefundType(RefundTypeEnum.REPORT_LOSS.getCode());
        record.setRefundStatus(RefundStatusEnum.IN_REFUND.getCode());
        record.setPayTime(itemVo.getPayTime());
        record.setRefundProductAmount(detail.getRefundProductAmount());
        record.setRefundOtherAmount(detail.getRefundOtherAmount());
        record.setRefundAmount(record.getRefundOtherAmount().add(record.getRefundProductAmount()));
        record.setRefundSubsidyFreeAmount(detail.getRefundSubsidyFreeAmount());
        record.setPlaceId(orderInfo.getPlaceId());
        record.setPlaceIdLevel2(orderInfo.getPlaceIdLevel2());
        record.setPlacePath(orderInfo.getPlacePath());
        //默认不需要审核
        record.setAuditStatus(YNStatusEnum.ENABLE.getCode());
        if (occupyEnum == OccupyEnum.YES) {
            record.setExpand(StrUtil.format(lossExpandFormat, lossOrder.getReportLossNo()));
            record.setOccupyStatus(OccupyEnum.YES.getCode());
        } else {
            //普通退款和释放的情况下需要判断是否要审核
            if (record.getRefundAmount().compareTo(audiAmount) >= 0) {
                record.setAuditStatus(YNStatusEnum.DISABLE.getCode());
            }
        }
        baseMapper.insert(record);
        detail.setRefundRecordId(record.getId());
        refundProductDetailMapper.insert(detail);


        OrderRefundBo orderRefundBo = this.buildRemoteRefundBo(record.getId());
        //计算佣金
        getDistributionAmount(lossOrder, orderRefundBo, detail);

        orderRefundBo.setIsOccupy(occupyEnum.getCode());
        if (occupyEnum == OccupyEnum.YES) {
            //如果是占用，则把信息放入缓存，方便释放时使用
            RedisUtils.setCacheObject(cacheKey, orderRefundBo, Duration.ofDays(60));
            //占用的情况下，只能是1
            orderRefundBo.setInfAuto(1);
        }

        try {
            log.keyword("lossRefund", record.getCode(), record.getOrderId()).info("报损退款调用退款入参【{}】", orderRefundBo);
            remotePaymentService.refund(orderRefundBo);
        } catch (Exception e) {
            RefundRecord recordUp = new RefundRecord();
            recordUp.setId(record.getId());
            recordUp.setRefundStatus(RefundStatusEnum.REFUND_FAIL.getCode());
            baseMapper.updateById(recordUp);

            RefundProductDetail detailUp = new RefundProductDetail();
            detailUp.setId(detail.getId());
            BigDecimal refundDistribution = ObjectUtil.isNotNull(detail.getRefundDistributionAmount()) ?
                    detail.getRefundDistributionAmount().add(detail.getRefundDistributionTaxAmount()) : BigDecimal.ZERO;
            detailUp.setRefundDistributionAmount(refundDistribution.divide(BigDecimal.ONE.add(distributionTaxRate), 2, RoundingMode.HALF_UP));
            detailUp.setRefundDistributionTaxAmount(refundDistribution.subtract(detailUp.getRefundDistributionAmount()));
            detailUp.setRefundStatus(RefundStatusEnum.REFUND_FAIL.getCode());
            refundProductDetailMapper.updateById(detailUp);

            log.keyword("lossRefund", record.getCode(), record.getOrderId()).error("报损退款失败:{}", e.getMessage());
        }
        if (record.getRefundSubsidyFreeAmount().compareTo(BigDecimal.ZERO) > 0 && occupyEnum == OccupyEnum.NOT
                && itemVo.getSettleCount() > 0){
            createDeductionInfo(itemVo,record.getRefundSubsidyFreeAmount(),lossOrder);
        }
    }

    private void getDistributionAmount(ReportLossOrder lossOrder, OrderRefundBo orderRefundBo, RefundProductDetail detailUp) {
        OrderItem item = orderItemMapper.selectById(lossOrder.getOrderItemId());
        if (item.getDistributionAmount().compareTo(BigDecimal.ZERO) > 0){
            LambdaQueryWrapper<RefundProductDetail> qw = Wrappers.lambdaQuery();
            qw.in(RefundProductDetail::getOrderItemId, lossOrder.getOrderItemId()).eq(RefundProductDetail::getDelFlag, 0);
            List<RefundProductDetail> refundList = refundProductDetailMapper.selectList(qw);
            BigDecimal refundDistributionAmount = BigDecimal.ZERO;
            BigDecimal refundDistributionTaxAmount = BigDecimal.ZERO;
            BigDecimal subtract = item.getProductAmount().subtract(item.getProductFreeAmount());
            if (CollectionUtil.isNotEmpty(refundList)){
                refundDistributionAmount = refundList.stream().map(RefundProductDetail::getRefundDistributionAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                refundDistributionTaxAmount = refundList.stream().map(RefundProductDetail::getRefundDistributionTaxAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                //在这之前已经产生退款单了  这里要排除
                subtract = subtract.subtract(refundList.stream().map(RefundProductDetail::getRefundProductAmount).reduce(BigDecimal.ZERO, BigDecimal::add))
                        .add(detailUp.getRefundProductAmount());
            }
            //佣金-订单余额 > 0  ? 扣佣金 : 不处理
            BigDecimal distributionAmount = item.getDistributionAmount().add(item.getDistributionTaxAmount()).subtract(refundDistributionAmount)
                    .subtract(refundDistributionTaxAmount).subtract(subtract).add(detailUp.getRefundProductAmount());
            if (distributionAmount.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal refundDistribution = distributionAmount.compareTo(BigDecimal.ZERO) > 0 ? distributionAmount : BigDecimal.ZERO;
                detailUp.setRefundDistributionAmount(refundDistribution.divide(BigDecimal.ONE.add(distributionTaxRate), 2, RoundingMode.HALF_UP));
                detailUp.setRefundDistributionTaxAmount(refundDistribution.subtract(detailUp.getRefundDistributionAmount()));
                //获取分销记录查询分销员id
                RemoteDistributionOrderInfoVo distributionOrderInfoVo = distributionService.getOrderInfo(item.getId());
                if (distributionOrderInfoVo != null) {
                    orderRefundBo.getSplits().get(0).setCommCustomerId(distributionOrderInfoVo.getDistributorCustomerId())
                            .setCommissionAmt(detailUp.getRefundDistributionAmount().add(detailUp.getRefundDistributionTaxAmount()))
                            .setCommSalaryAmt(detailUp.getRefundDistributionAmount());
                    distributionService.refundOrderItem(orderRefundBo.getOrderId(), detailUp.getOrderItemId(), distributionAmount,detailUp.getRefundDistributionTaxAmount());
                }
                refundProductDetailMapper.updateById(detailUp);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void lossCancel(String reportLossNo) {
        RefundRecord refundRecord = baseMapper.selectByExpand(reportLossNo, StrUtil.format(lossExpandFormat, reportLossNo));
        if (refundRecord != null) {
            String cacheKey = OrderCacheNames.SETTLE_OCCUPY_LOSS + reportLossNo;
            OrderRefundBo orderRefundBo = RedisUtils.getCacheObject(cacheKey);
            if (orderRefundBo == null) {
                //避开1.1.7里bug导致的脏数据
                return;
            }
            //本方法是不退钱的，写死自动退
            orderRefundBo.setInfAuto(YNStatusEnum.ENABLE.getCode());
            orderRefundBo.setIsOccupy(OccupyEnum.RELEASE.getCode());
            orderRefundBo.setRefundAmt(BigDecimal.ZERO);
            for (OrderPaySplitBo split : orderRefundBo.getSplits()) {
                split.setTransAmt(BigDecimal.ZERO);
                split.setSplitAmt(BigDecimal.ZERO);
            }

            RefundRecord upRefund = new RefundRecord();
            upRefund.setId(refundRecord.getId());
            upRefund.setAuditStatus(AuditEnum.PASS.getCode());
            upRefund.setOccupyStatus(OccupyEnum.RELEASE.getCode());
            baseMapper.updateById(upRefund);

            baseMapper.releaseRefund(Lists.newArrayList(refundRecord.getId()));
            refundProductDetailMapper.releaseRefund(Lists.newArrayList(refundRecord.getId()));

            try {
                remotePaymentService.refund(orderRefundBo);
                RedisUtils.deleteObject(cacheKey);
                log.info("释放报损占用结算金额成功");
            } catch (Exception e) {
                log.error("释放报损占用结算金额失败", e);
                throw e;
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void releaseOccupy(String refundCode) {
        log.keyword(refundCode).info("退款释放占用：{}", refundCode);
        RefundRecord refundRecord = baseMapper.selectByCode(refundCode);
        if (refundRecord == null) {
            throw new ServiceException("退款单不存在");
        }
        if (refundRecord.getOccupyStatus().equals(OccupyEnum.NOT.getCode())) {
            log.warn("退款单不是占用单据，不需要释放");
            return;
        }
        String cacheKey = OrderCacheNames.SETTLE_OCCUPY_LOSS + refundRecord.getSourceCode();
        OrderRefundBo orderRefundBo = RedisUtils.getCacheObject(cacheKey);
        if (orderRefundBo == null) {
            orderRefundBo = this.buildRemoteRefundBo(refundRecord.getId());
        }
        //不退钱写死自动退
        orderRefundBo.setInfAuto(YNStatusEnum.ENABLE.getCode());
        orderRefundBo.setIsOccupy(OccupyEnum.RELEASE.getCode());
        orderRefundBo.setRefundAmt(BigDecimal.ZERO);
        for (OrderPaySplitBo split : orderRefundBo.getSplits()) {
            split.setTransAmt(BigDecimal.ZERO);
            split.setSplitAmt(BigDecimal.ZERO);
        }
        RefundRecord upRefund = new RefundRecord();
        upRefund.setId(refundRecord.getId());
        upRefund.setAuditStatus(AuditEnum.PASS.getCode());
        upRefund.setOccupyStatus(OccupyEnum.RELEASE.getCode());
        baseMapper.updateById(upRefund);

        baseMapper.releaseRefund(Lists.newArrayList(refundRecord.getId()));
        refundProductDetailMapper.releaseRefund(Lists.newArrayList(refundRecord.getId()));
        try {
            remotePaymentService.refund(orderRefundBo);
            RedisUtils.deleteObject(cacheKey);
            log.keyword(refundCode).info("释放占用结算金额成功");
        } catch (Exception e) {
            log.keyword(refundCode).error("释放报损占用结算金额失败", e);
            throw e;
        }
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void closeRefund(String refundCode) {
        RefundRecord refundRecord = baseMapper.selectByCode(refundCode);
        if (refundRecord == null || refundRecord.getRefundStatus().equals(RefundStatusEnum.IN_REFUND.getCode())) {
            throw new ServiceException(refundCode + "退款单不存在");
        }
        if (!refundRecord.getRefundStatus().equals(RefundStatusEnum.HAS_REFUND.getCode())) {
            throw new ServiceException(refundCode + "已退款，不允许关闭");
        }

        refundProductDetailMapper.deleteByRefundId(refundRecord.getId());

        OrderRefundCheckBo orderRefund = new OrderRefundCheckBo();
        orderRefund.setRefundNo(refundCode);
        orderRefund.setOrderNo(refundRecord.getOrderCode());
        remotePaymentService.refundClose(orderRefund);
    }


    private OrderRefundBo buildRemoteRefundBo(Long refundId) {
        RefundRecord record = baseMapper.selectById(refundId);
        RefundProductDetail detail = refundProductDetailMapper.selectByRefundId(refundId).get(0);
        RemoteSupplierVo supplierVo = remoteSupplierService.getSupplierById(detail.getSupplierId());

        OrderRefundBo orderRefundBo = new OrderRefundBo();
        orderRefundBo.setOrderId(record.getOrderId()).setOrderNo(record.getOrderCode()).setRefundId(record.getId()).setRefundNo(record.getCode())
                .setBusiType(RefundBusiTypeEnum.REFUND_LOSS.getCode()).setRefundAmt(record.getRefundProductAmount().add(record.getRefundOtherAmount()));
        List<OrderPaySplitBo> splits = new ArrayList<>();
        orderRefundBo.setInfAuto(YNStatusEnum.ENABLE.getCode());

        //商品金额 扣供应商钱
        if (record.getRefundProductAmount().compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal reduce = detail.getRefundProductAmount();
            OrderPaySplitBo split = new OrderPaySplitBo();
            split.setOrgId(detail.getSupplierId()).setOrgType(AccountOrgTypeEnum.SUPPLIER.getCode()).setOrgCode(supplierVo.getCode())
                    .setTransAmt(reduce).setSkuId(detail.getSupplierSkuId()).setSplitAmt(reduce).setDeptId(detail.getSupplierDeptId());
            splits.add(split);
        }
        //非商品金额 扣总仓
        if (record.getRefundOtherAmount().compareTo(BigDecimal.ZERO) > 0) {
            OrderPaySplitBo split = new OrderPaySplitBo();
            //获取总仓
            RemoteRegionWhVo remoteRegionWhVo = remoteRegionWhService.queryById(record.getRegionWhId());
            split.setOrgId(record.getRegionWhId()).setOrgType(AccountOrgTypeEnum.REGION_WH.getCode()).setOrgCode(remoteRegionWhVo.getRegionWhCode())
                    .setTransAmt(record.getRefundOtherAmount()).setSplitAmt(record.getRefundOtherAmount());
            splits.add(split);
        }
        if (record.getRefundAmount().compareTo(audiAmount) < 0) {
            orderRefundBo.setInfAuto(YNStatusEnum.ENABLE.getCode());
        } else {
            orderRefundBo.setInfAuto(YNStatusEnum.DISABLE.getCode());
        }
        orderRefundBo.setSplits(splits);
        return orderRefundBo;
    }

    @Override
    public TableDataInfo<SupPaymentMiniListVo> skuRefundPage(SupPaymentSearchBo searchBo) {
        Page<SupPaymentMiniListVo> page = refundProductDetailMapper.skuRefundPage(searchBo, searchBo.build());
        List<SupPaymentMiniListVo> records = page.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            //填充sku信息
            Map<Long, RemoteSupplierSkuFundsInfoVo> skuMap = remoteSupplierSkuService.queryInfoListByFunds(records.stream().map(SupPaymentMiniListVo::getSkuId).toList())
                    .stream()
                    .collect(Collectors.toMap(RemoteSupplierSkuFundsInfoVo::getSkuId, Function.identity(), (k1, k2) -> k1));
            for (SupPaymentMiniListVo record : records) {
                RemoteSupplierSkuFundsInfoVo skuInfo = skuMap.get(record.getSkuId());
                if (skuInfo != null) {
                    record.setSkuImgUrl(skuInfo.getImgUrl());
                    record.setGrossWeight(skuInfo.getSpuGrossWeight());
                    record.setNetWeight(skuInfo.getSpuNetWeight());
                }
            }
        }
        return TableDataInfo.build(page);
    }

    @Override
    public List<SupPaymentOrderMiniListVo> skuRefundList(SupplierSkuSearchBo searchBo) {
        return refundProductDetailMapper.skuRefundList(searchBo);
    }

    @Override
    public GetInfoVo getRefundAmountById(Long orderId) {
        GetInfoVo vo = new GetInfoVo();
        LambdaQueryWrapper<RefundRecord> qw = Wrappers.lambdaQuery();
        qw.select(RefundRecord::getRefundAmount, RefundRecord::getRefundType)
                .eq(RefundRecord::getOrderId, orderId)
                .eq(RefundRecord::getRefundStatus, RefundStatusEnum.HAS_REFUND.getCode())
                .eq(RefundRecord::getDelFlag, 0);
        List<RefundRecord> details = baseMapper.selectList(qw);
        if (CollectionUtil.isNotEmpty(details)) {
            //区分类型
            Map<Integer, List<RefundRecord>> collect = details.stream().collect(Collectors.groupingBy(RefundRecord::getRefundType));
            //差额
            if (CollUtil.isNotEmpty(collect.get(RefundTypeEnum.DIFFERENCE.getCode()))) {
                vo.setDifferenceRefundAmount(collect.get(RefundTypeEnum.DIFFERENCE.getCode()).stream().map(RefundRecord::getRefundAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            }
            BigDecimal refundAmount = BigDecimal.ZERO;
            //退货退款
            if (CollUtil.isNotEmpty(collect.get(RefundTypeEnum.LACK.getCode()))) {
                refundAmount = refundAmount.add(collect.get(RefundTypeEnum.LACK.getCode()).stream().map(RefundRecord::getRefundAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            }
            if (CollUtil.isNotEmpty(collect.get(RefundTypeEnum.LESS.getCode()))) {
                refundAmount = refundAmount.add(collect.get(RefundTypeEnum.LESS.getCode()).stream().map(RefundRecord::getRefundAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            }
            vo.setRefundAmount(refundAmount);
            //售后
            if (CollUtil.isNotEmpty(collect.get(RefundTypeEnum.REPORT_LOSS.getCode()))) {
                vo.setLossAmount(collect.get(RefundTypeEnum.REPORT_LOSS.getCode()).stream().map(RefundRecord::getRefundAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            }
        } else {
            return null;
        }
        return vo;
    }

    /**
     * 根据来源单号查询退款单信息
     */
    @Override
    public RefundBySourceVO getRefundBySourceCode(String code) {
        return baseMapper.getRefundBySourceCode(code);
    }

    /**
     * 超额退款 审核通过
     * @param id 退款单id
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void auditPass(Long id) {
        //获取对应退款记录
        RefundRecord record = baseMapper.selectById(id);
        if (record != null && record.getAuditStatus().equals(0)
                && record.getRefundStatus().equals(RefundStatusEnum.IN_REFUND.getCode())){
            //获取退款单下其他未退款明细
            LambdaQueryWrapper<RefundProductDetail> qw = Wrappers.lambdaQuery();
            qw.eq(RefundProductDetail::getRefundRecordId, record.getId()).eq(RefundProductDetail::getDelFlag, 0);
            List<RefundProductDetail> refundProductDetails = refundProductDetailMapper.selectList(qw);
            if (CollectionUtil.isNotEmpty(refundProductDetails)) {

                boolean refundCheckFlag = true;
                if (StrUtil.isNotBlank(record.getExpand())) {
                    //如果是缺货、少货、报损占用，则直接进行释放，不调用refundCheck
                    if (record.getRefundType().equals(RefundTypeEnum.LACK.getCode()) || record.getRefundType().equals(RefundTypeEnum.LESS.getCode())) {
                        //缺货少货
                        String cacheKey = OrderCacheNames.SETTLE_OCCUPY_STOCKOUT + record.getId();
                        OrderRefundBo orderRefundBo = RedisUtils.getCacheObject(cacheKey);
                        if (orderRefundBo != null) {
                            orderRefundBo.setIsOccupy(OccupyEnum.RELEASE.getCode());
                            orderRefundBo.setInfAuto(YNStatusEnum.ENABLE.getCode());
                            RefundRecord recordUp = new RefundRecord();
                            recordUp.setId(record.getId());
                            recordUp.setAuditStatus(YNStatusEnum.ENABLE.getCode());
                            try {
                                baseMapper.updateById(recordUp);
                                log.keyword("auditPass").info("缺货、少货退款审核释放占用金额入参【{}】", orderRefundBo);
                                remotePaymentService.refund(orderRefundBo);
                                RedisUtils.deleteObject(cacheKey);
                            } catch (Exception e) {
                                log.keyword("auditPass").error("缺货、少货退款审核释放占用金额失败:{}", e.getMessage());
                                recordUp.setRefundStatus(RefundStatusEnum.IN_REFUND.getCode());
                                recordUp.setAuditStatus(YNStatusEnum.DISABLE.getCode());
                                recordUp.setRemark(e.getMessage());
                                baseMapper.updateById(recordUp);
                                refundProductDetails.forEach(detail -> {
                                    detail.setRefundStatus(RefundStatusEnum.IN_REFUND.getCode());
                                    detail.setRefundTime(null);
                                });
                                refundProductDetailMapper.updateBatchById(refundProductDetails);
                            }
                            refundCheckFlag = false;
                        }
                    }
                    if (record.getRefundType().equals(RefundTypeEnum.REPORT_LOSS.getCode())) {
                        //报损
                        String cacheKey = OrderCacheNames.SETTLE_OCCUPY_LOSS + record.getSourceCode();
                        OrderRefundBo orderRefundBo = RedisUtils.getCacheObject(cacheKey);
                        if (orderRefundBo != null) {
                            orderRefundBo.setIsOccupy(OccupyEnum.RELEASE.getCode());
                            orderRefundBo.setInfAuto(YNStatusEnum.ENABLE.getCode());
                            RefundRecord recordUp = new RefundRecord();
                            recordUp.setId(record.getId());
                            recordUp.setAuditStatus(YNStatusEnum.ENABLE.getCode());
                            try {
                                baseMapper.updateById(recordUp);
                                log.keyword("auditPass").info("报损退款审核释放占用金额入参【{}】", orderRefundBo);
                                remotePaymentService.refund(orderRefundBo);
                                RedisUtils.deleteObject(cacheKey);
                            } catch (Exception e) {
                                log.keyword("auditPass").error("报损退款审核释放占用金额失败:{}", e.getMessage());
                                recordUp.setRefundStatus(RefundStatusEnum.IN_REFUND.getCode());
                                recordUp.setAuditStatus(YNStatusEnum.DISABLE.getCode());
                                recordUp.setRemark(e.getMessage());
                                baseMapper.updateById(recordUp);
                                refundProductDetails.forEach(detail -> {
                                    detail.setRefundStatus(RefundStatusEnum.IN_REFUND.getCode());
                                    detail.setRefundTime(null);
                                });
                                refundProductDetailMapper.updateBatchById(refundProductDetails);
                            }
                            refundCheckFlag = false;
                        }
                    }
                }

                if (refundCheckFlag) {
                    OrderRefundCheckBo orderRefundBo = new OrderRefundCheckBo();
                    orderRefundBo.setOrderNo(record.getOrderCode()).setRefundNo(record.getCode());
                    record.setAuditStatus(1);
                    //真.调用退款了
                    try {
                        log.keyword("auditPass", id, record.getOrderId()).info("超额审核调用退款入参【{}】", orderRefundBo);
                        remotePaymentService.refundCheck(orderRefundBo);
                    } catch (ServiceException e) {
                        log.keyword("auditPass", id, record.getOrderId()).error("超额审核退款失败", e);
                        record.setRemark("退款失败原因：" +e.getMessage());
                        refundProductDetails.forEach(detail -> {
                            detail.setRefundStatus(RefundStatusEnum.IN_REFUND.getCode());
                            detail.setRefundTime(null);
                        });
                        record.setAuditStatus(0);
                        record.setRefundStatus(RefundStatusEnum.IN_REFUND.getCode());
                    }
                    refundProductDetailMapper.updateBatchById(refundProductDetails);
                    baseMapper.updateById(record);
                }

                if (record.getRefundStatus().equals(RefundStatusEnum.HAS_REFUND.getCode())
                        && (record.getRefundType().equals(RefundTypeEnum.LACK.getCode())
                        || record.getRefundType().equals(RefundTypeEnum.LESS.getCode()))){
                    //获取缺货少货单据
                    StockoutRecord stockoutRecord = stockoutRecordMapper.selectByCode(record.getSourceCode());
                    if(stockoutRecord != null){
                        stockoutRecord.setRefundStatus(2);
                        stockoutRecordMapper.updateById(stockoutRecord);
                    }
                }
            }
        }
    }

    /**
     * 退款审核-分页查询退货退款列表
     */
    @Override
    public TableDataInfo<RefundPageVO> auditPage(RefundPageAuditBO bo) {
        Page<RefundPageVO> page = baseMapper.auditPage(bo, bo.build());
        //赋值名称
        if (CollectionUtil.isNotEmpty(page.getRecords())){
            List<Long> cityWhIds = page.getRecords().stream().map(RefundPageVO::getCityWhId).toList();
            //城市仓名称
            List<RemoteCityWhVo> remoteCityWhVos = remoteCityWhService.queryList(cityWhIds);
            Map<Long, String> cityMap = remoteCityWhVos.stream().collect(Collectors.toMap(RemoteCityWhVo::getId, RemoteCityWhVo::getName));
            //总仓名称
            List<RemoteRegionWhVo> remoteRegionWhVos = remoteRegionWhService.queryList();
            Map<Long, String> regionMap = remoteRegionWhVos.stream().collect(Collectors.toMap(RemoteRegionWhVo::getId, RemoteRegionWhVo::getRegionWhName));
            //获取商品信息拼接
            List<Long> skuIds = page.getRecords().stream().map(RefundPageVO::getSupplierSkuId).toList();
            List<RemoteSupplierSkuInfoVo> skuInfoVo = remoteSupplierSkuService.querySimpleInfoList(skuIds);
            Map<Long, RemoteSupplierSkuInfoVo> skuMap = skuInfoVo.stream().collect(Collectors.toMap(RemoteSupplierSkuInfoVo::getId, Function.identity()));
            page.getRecords().forEach(item -> {
                item.setCityWhName(cityMap.get(item.getCityWhId()));
                item.setRegionWhName(regionMap.get(item.getRegionWhId()));
                //优惠金额现在只有补贴商品优惠
                item.setRefundFreeTotalAmount(item.getRefundSubsidyFreeAmount().add(item.getRefundProductFreeAmount()));
                if (skuMap.containsKey(item.getSupplierSkuId())){
                    RemoteSupplierSkuInfoVo sku = skuMap.get(item.getSupplierSkuId());
                    //商品名称拼接：品牌+产地+名称+规格
                    item.setFullName((StringUtils.isEmpty(sku.getBrand()) ? sku.getBrand() : sku.getBrand() + "-")
                            + (StringUtils.isEmpty(sku.getProducer()) ? sku.getProducer() : sku.getProducer() + "-")
                            + sku.getSpuName() + (StringUtils.isNotEmpty(sku.getSpuStandards()) ? "【" + sku.getSpuStandards() + "】" : ""));
                }
            });
        }
        return TableDataInfo.build(page);
    }

    /**
     * 资金账户退货退款单列表查询
     */
    @Override
    public List<RemoteSupTransRefundVo> queryRefund(RemoteSupAccTransQueryBo bo) {
        List<RemoteSupTransRefundVo> voList = new ArrayList<>();
        if (bo != null && bo.getTrans() != null && bo.getTrans().size() > 0) {
            List<Long> skuIdList = bo.getTrans().stream().map(RemoteSupAccTransBo::getSkuId).distinct().toList();
            //获取销售批次商品
            RemoteQueryInfoListBo listBo = new RemoteQueryInfoListBo();


            listBo.setSupplierSkuIdList(skuIdList);
            List<RemoteSupplierSkuInfoVo> skuInfoVos = remoteSupplierSkuService.queryInfoList(listBo);
            Map<Long, RemoteSupplierSkuInfoVo> skuMap = skuInfoVos.stream().collect(Collectors.toMap(RemoteSupplierSkuInfoVo::getId, Function.identity()));
            //获取包装图片
            RemoteQuerySupplierSkuFileBo fileBo = new RemoteQuerySupplierSkuFileBo();
            fileBo.setSupplierSkuIdList(skuIdList);
            //商品等级
            List<RemoteSysDictVo> baseProRank = remoteDictService.selectDictDataByType("BasePro_rank");
            Map<String, String> collect = baseProRank.stream().collect(Collectors.toMap(RemoteSysDictVo::getDictValue, RemoteSysDictVo::getDictName));

            Map<Long, RemoteSupAccTransBo> boMap = bo.getTrans().stream().collect(Collectors.toMap(RemoteSupAccTransBo::getSkuId, Function.identity(), (v1, v2) -> v1));
            Map<Long, List<Long>> boTransMap = bo.getTrans().stream().collect(Collectors
                    .groupingBy(RemoteSupAccTransBo::getSkuId, Collectors.mapping(RemoteSupAccTransBo::getTransId, Collectors.toList())));

            for(Long skuId : skuIdList) {
                RemoteSupTransRefundVo resultVo = new RemoteSupTransRefundVo();
                resultVo.setId(skuId);
                RemoteSupAccTransBo remoteSupAccTransBo = boMap.get(skuId);
                if(remoteSupAccTransBo != null) {
                    resultVo.setSaleDate(remoteSupAccTransBo.getTransDate());
                    resultVo.setTotalAmount(remoteSupAccTransBo.getTransAmt());
                }
                RemoteSupplierSkuInfoVo sku = skuMap.get(skuId);
                if(sku != null) {
                    resultVo.setSkuName(sku.getSpuName());
                    resultVo.setSpuGrossWeight(sku.getSpuGrossWeight());
                    resultVo.setSpuNetWeight(sku.getSpuNetWeight());
                    resultVo.setImgUrl(sku.getImgUrl());
                    if (StringUtils.isNotBlank(collect.get(sku.getSpuGrade()))){
                        resultVo.setSpuGradeName(collect.get(sku.getSpuGrade()));
                    }
                    resultVo.setSpuStandards(sku.getSpuStandards());
                    resultVo.setProducer(sku.getProducer());
//                    resultVo.setFileList(skuFileMap.get(skuId));
                }
                QueryRefundInfoBO infoBO = new QueryRefundInfoBO();
                infoBO.setSupplierSkuId(skuId);
                infoBO.setRefundRecordIdList(boTransMap.get(skuId));
                List<RemoteSupTransRefundPriceVo> priceVoList = refundProductDetailMapper.queryRefund(infoBO);
                resultVo.setPriceList(priceVoList);
                voList.add(resultVo);
            }
            log.keyword("我的资金列表查询order字段数据", "queryRefund").info("bo:{}， voList：{}", bo, voList);
        }
        if (ObjectUtil.isNotEmpty(voList)) {
            voList.sort(Comparator.comparing(RemoteSupTransRefundVo::getSaleDate).reversed());
        }
        return voList;
    }

    /**
     * 资金账户差额退款列表查询
     */
    @Override
    public List<RemoteSupTransRefundDiffVo> queryRefundDiff(RemoteSupAccTransQueryBo bo) {
        List<RemoteSupTransRefundDiffVo> voList = new ArrayList<>();
        if (bo != null && bo.getTrans() != null && bo.getTrans().size() > 0) {
            List<Long> skuIdList = bo.getTrans().stream().map(RemoteSupAccTransBo::getSkuId).distinct().toList();
            //获取销售批次商品
            RemoteQueryInfoListBo listBo = new RemoteQueryInfoListBo();
            log.keyword("我的资金列表查询order字段数据", "queryRefundDiff").info("bo:{}", bo);
            listBo.setSupplierSkuIdList(skuIdList);
            List<RemoteSupplierSkuInfoVo> skuInfoVos = remoteSupplierSkuService.queryInfoList(listBo);
            Map<Long, RemoteSupplierSkuInfoVo> skuMap = skuInfoVos.stream().collect(Collectors.toMap(RemoteSupplierSkuInfoVo::getId, Function.identity()));
            //获取包装图片
            RemoteQuerySupplierSkuFileBo fileBo = new RemoteQuerySupplierSkuFileBo();
            fileBo.setSupplierSkuIdList(skuIdList);
            //商品等级
            List<RemoteSysDictVo> baseProRank = remoteDictService.selectDictDataByType("BasePro_rank");
            Map<String, String> collect = baseProRank.stream().collect(Collectors.toMap(RemoteSysDictVo::getDictValue, RemoteSysDictVo::getDictName));

            Map<Long, RemoteSupAccTransBo> boMap = bo.getTrans().stream().collect(Collectors.toMap(RemoteSupAccTransBo::getSkuId, Function.identity(), (v1, v2) -> v1));
            Map<Long, List<Long>> boTransMap = bo.getTrans().stream().collect(Collectors
                    .groupingBy(RemoteSupAccTransBo::getSkuId, Collectors.mapping(RemoteSupAccTransBo::getTransId, Collectors.toList())));

            for(Long skuId : skuIdList) {
                RemoteSupTransRefundDiffVo resultVo = new RemoteSupTransRefundDiffVo();
                resultVo.setId(skuId);
                RemoteSupAccTransBo remoteSupAccTransBo = boMap.get(skuId);
                if(remoteSupAccTransBo != null) {
                    resultVo.setSaleDate(remoteSupAccTransBo.getTransDate());
                    resultVo.setTotalAmount(remoteSupAccTransBo.getTransAmt());
                }
                RemoteSupplierSkuInfoVo sku = skuMap.get(skuId);
                if(sku != null) {
                    resultVo.setSkuName(sku.getSpuName());
                    resultVo.setSpuGrossWeight(sku.getSpuGrossWeight());
                    resultVo.setSpuNetWeight(sku.getSpuNetWeight());
                    resultVo.setImgUrl(sku.getImgUrl());
                    if (StringUtils.isNotBlank(collect.get(sku.getSpuGrade()))){
                        resultVo.setSpuGradeName(collect.get(sku.getSpuGrade()));
                    }
                    resultVo.setSpuStandards(sku.getSpuStandards());
                    resultVo.setProducer(sku.getProducer());
//                    resultVo.setFileList(skuFileMap.get(skuId));
                }
                QueryRefundInfoBO infoBO = new QueryRefundInfoBO();
                infoBO.setSupplierSkuId(skuId);
                infoBO.setRefundRecordIdList(boTransMap.get(skuId));
                List<RemoteSupTransRefundDiffPriceVo> priceVoList = baseMapper.queryRefundDiff(infoBO);
                if (ObjectUtil.isNotEmpty(priceVoList)) {
                    for (RemoteSupTransRefundDiffPriceVo priceVo : priceVoList) {
                        RemoteSupplierSkuInfoVo skuInfoVo = skuMap.get(skuId);
                        if (ObjectUtil.isNotEmpty(skuInfoVo)) {
                            priceVo.setCurrentPrice(skuInfoVo.getPrice());
                        }
                    }
                }
                resultVo.setPriceList(priceVoList);
                voList.add(resultVo);
            }
            log.keyword("我的资金列表查询order字段数据", "queryRefundDiff").info("bo:{}， voList：{}", bo, voList);
        }
        if (ObjectUtil.isNotEmpty(voList)) {
            voList.sort(Comparator.comparing(RemoteSupTransRefundDiffVo::getSaleDate).reversed());
        }
        return voList;
    }


    /**
     * 资金账户 根据订单id和类型查询退款金额
     */
    @Override
    public RemoteOrderBusiAmtVo getRefundByIdAndType(Long orderId, Integer type) {
        //初始化出参
        RemoteOrderBusiAmtVo result = new RemoteOrderBusiAmtVo();
        BigDecimal goodsAmt = new BigDecimal(0);
        BigDecimal serviceAmt = new BigDecimal(0);
        BigDecimal freightAmt = new BigDecimal(0);
        BigDecimal bankFeeAmt = new BigDecimal(0);
        LambdaQueryWrapper<RefundRecord> qw = Wrappers.lambdaQuery();
        qw.eq(RefundRecord::getOrderId, orderId).eq(RefundRecord::getRefundType, type)
                .eq(RefundRecord::getDelFlag, 0).select(RefundRecord::getId);
        List<RefundRecord> refundRecords = baseMapper.selectList(qw);
        if (CollectionUtil.isNotEmpty(refundRecords)){
            List<Long> ids = refundRecords.stream().map(RefundRecord::getId).toList();
            LambdaQueryWrapper<RefundProductDetail> lqw = Wrappers.lambdaQuery();
            lqw.in(RefundProductDetail::getRefundRecordId, ids).eq(RefundProductDetail::getDelFlag, 0);
            List<RefundProductDetail> details = refundProductDetailMapper.selectList(lqw);
            if (CollectionUtil.isNotEmpty(details)){
                for (RefundProductDetail l : details){
                    goodsAmt = goodsAmt.add(l.getRefundProductAmount());
                    serviceAmt = serviceAmt.add(l.getRefundServiceAmount());
                    freightAmt = freightAmt.add(l.getRefundFreightAmount());
                    bankFeeAmt = bankFeeAmt.add(l.getRefundFinancialServicePrice());
                }
            }
        }
        result.setGoodsAmt(goodsAmt);
        result.setServiceAmt(serviceAmt);
        result.setFreightAmt(freightAmt);
        result.setCoinAmt(BigDecimal.ZERO);
        result.setBankFeeAmt(bankFeeAmt);
        result.setAvailAmt(result.getGoodsAmt().add(result.getServiceAmt()).add(result.getFreightAmt()).add(result.getCoinAmt()).add(result.getBankFeeAmt()));
        return result;
    }

    /**
     * 资金账户 退货退款单退款记录列表查询
     *
     * @param bo
     */
    @Override
    public List<RemoteSupTransRefundRecordVo> queryProductRefundRecord(RemoteSupAccTransQueryBo bo) {
        if (ObjectUtil.isEmpty(bo.getTrans())) {
            return Collections.emptyList();
        }
        List<RemoteSupTransRefundRecordVo> remoteSupTransRefundRecordVos = refundProductDetailMapper.queryProductRefundRecord(bo);
        if (ObjectUtil.isEmpty(remoteSupTransRefundRecordVos)) {
            return Collections.emptyList();
        }
        List<Long> skuIds = bo.getTrans().stream().map(RemoteSupAccTransBo::getSkuId).distinct().toList();
        RemoteQueryInfoListBo listBo = new RemoteQueryInfoListBo();
        listBo.setSupplierSkuIdList(skuIds);
        List<RemoteSupplierSkuInfoVo> skuInfoVos = remoteSupplierSkuService.queryInfoList(listBo);
        Map<Long, RemoteSupplierSkuInfoVo> map = skuInfoVos.stream().collect(Collectors.toMap(RemoteSupplierSkuInfoVo::getId, Function.identity(), (key1, key2) -> key2));
        for (RemoteSupTransRefundRecordVo refundRecordVo : remoteSupTransRefundRecordVos) {
            RemoteSupplierSkuInfoVo skuInfoVo = map.get(refundRecordVo.getSupplierSkuId());
            if (ObjectUtil.isNotEmpty(skuInfoVo)) {
                refundRecordVo.setSaleDate(skuInfoVo.getSaleDate());
                refundRecordVo.setCurrentPrice(skuInfoVo.getPrice());
            }
        }
        return remoteSupTransRefundRecordVos;
    }

    @Override
    public List<RefundProductDetail> getRefundByItemIdList(List<Long> orderItemIdList) {
        LambdaQueryWrapper<RefundProductDetail> qw = Wrappers.lambdaQuery();
        qw.in(RefundProductDetail::getOrderItemId, orderItemIdList).eq(RefundProductDetail::getDelFlag, 0);
        qw.ne(RefundProductDetail::getRefundStatus, RefundStatusEnum.REFUND_FAIL.getCode());
        return refundProductDetailMapper.selectList(qw);
    }

    @Override
    public List<RefundProductDetail> getRefundedByItemIdList(List<Long> orderItemIdList) {
        LambdaQueryWrapper<RefundProductDetail> qw = Wrappers.lambdaQuery();
        qw.in(RefundProductDetail::getOrderItemId, orderItemIdList).eq(RefundProductDetail::getDelFlag, 0);
        qw.eq(RefundProductDetail::getRefundStatus, RefundStatusEnum.HAS_REFUND.getCode());
        return refundProductDetailMapper.selectList(qw);
    }

    /**
     * 管理台订单退款
     * @param orderCode
     * @return
     */
    @Override
    public AdminOrderInfoVo adminOrderRefund(String orderCode) {
        return baseMapper.adminOrderRefund(orderCode);
    }

    /**
     * 分摊金融手续费
     * @param totalAmount
     * @param serviceAmount
     * @param refundAmount
     * @param refundServiceAmount
     * @return
     */
    @Override
    public BigDecimal getApportionRefundService(BigDecimal totalAmount, BigDecimal serviceAmount, BigDecimal refundAmount, BigDecimal refundServiceAmount) {
        if (totalAmount.compareTo(BigDecimal.ZERO) == 0){
            return BigDecimal.ZERO;
        }
        //分摊获取金融手续费
        BigDecimal apportionRefundService = refundAmount.divide(totalAmount, 3, RoundingMode.HALF_UP).multiply(serviceAmount).setScale(2, RoundingMode.HALF_UP);
        //如果超过直接用总的减去已分摊，没超过直接取
        return serviceAmount.compareTo(refundServiceAmount.add(apportionRefundService)) < 0 ? serviceAmount.subtract(refundServiceAmount) : apportionRefundService;
    }

    @Override
    public BigDecimal selectRefundTypeToDayAmount(RefundTypeEnum type, Long regionWhId) {
        Date now = new Date();
        BigDecimal totalAmount = baseMapper.selectRefundTypeToDayAmount(type.getCode(), regionWhId, DateUtil.beginOfDay(now), DateUtil.endOfDay(now));
        if (totalAmount == null) {
            totalAmount = BigDecimal.ZERO;
        }
        return totalAmount;
    }

    @Override
    public List<RemoteSupBillVo> queryRefundBillList(List<Long> refundId, Long supplierId, Long supplierDeptId, Long supplierSkuId) {
        List<RemoteSupBillVo> result = new ArrayList<>();
        List<TransRefundItemDTO> refundProductDetailList =  refundProductDetailMapper.queryRefundBillList(refundId, supplierId, supplierDeptId, supplierSkuId);
        if (CollUtil.isEmpty(refundProductDetailList)) {
            return result;
        }
        //查询sku名称    统计报表中已经获取名称， 这里不在查询 ！！
        // Map<Long, String> skuNameMap = remoteSupplierSkuService.getSkuCombinationName(Lists.newArrayList(supplierSkuId));
        List<Long> itemIds = refundProductDetailList.stream().map(TransRefundItemDTO::getOrderItemId).toList();
        Map<Long, RemoteDistributionSkuVo> itemDistributionMap = Optional.ofNullable(distributionService.getOrderItemDistribution(itemIds)).orElse(Collections.emptyMap());

        for (TransRefundItemDTO  orderItem: refundProductDetailList) {
            RemoteSupBillVo remoteSupBillVo = new RemoteSupBillVo();
            remoteSupBillVo.setSkuId(orderItem.getSupplierSkuId());
            // remoteSupBillVo.setSkuName(skuNameMap.get(orderItem.getSkuId()));
            remoteSupBillVo.setCount(orderItem.getStockoutCount());
            remoteSupBillVo.setAmount(orderItem.getRefundProductAmount());
            remoteSupBillVo.setSkuPrice(orderItem.getPrice());
            remoteSupBillVo.setBusinessNo(orderItem.getSourceCode());
            remoteSupBillVo.setBuyerName(orderItem.getBuyerName());
            remoteSupBillVo.setPayTime(orderItem.getPayTime());
            remoteSupBillVo.setSaleDate(orderItem.getSaleDate());
            remoteSupBillVo.setTransId(orderItem.getRefundId());
            Optional.ofNullable(itemDistributionMap.get(orderItem.getOrderItemId()))
                    .ifPresent(distribute -> remoteSupBillVo.setDistributionItemAmount(distribute.getDistributionItemAmount()));
            result.add(remoteSupBillVo);
        }
        return result;
    }

    /**
     * 订单明细id查询报损金额
     * @param orderItemIds
     * @return
     */
    @Override
    public List<RemoteStrockBatchRefundBo> getBatchCodeRefund(List<Long> orderItemIds) {
        List<RemoteStrockBatchRefundBo> batchCodeRefund = baseMapper.getBatchCodeRefund(orderItemIds);
        Map<Long, BigDecimal> refundMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(batchCodeRefund)){
            refundMap = batchCodeRefund.stream().collect(Collectors.toMap(RemoteStrockBatchRefundBo::getOrderItemId
                    , RemoteStrockBatchRefundBo::getRefundAmount, (value, value1) -> value));
        }

        Map<Long, RemoteStrockBatchRefundBo> lessRefundAmountMap = new HashMap<>();
        List<RemoteStrockBatchRefundBo> lessRefundAmountList = baseMapper.lessRefundAmount(orderItemIds);
        if (CollectionUtil.isNotEmpty(lessRefundAmountList)){
            lessRefundAmountMap = lessRefundAmountList.stream().collect(Collectors.toMap(RemoteStrockBatchRefundBo::getOrderItemId
                    , item -> item, (value, value1) -> value));
        }
        Map<Long, BigDecimal> finalRefundMap = refundMap;
        Map<Long, RemoteStrockBatchRefundBo> finalLessRefundAmountMap = lessRefundAmountMap;
        return orderItemIds.stream().map(item -> {
            RemoteStrockBatchRefundBo refundBo = new RemoteStrockBatchRefundBo();
            refundBo.setOrderItemId(item);
            refundBo.setRefundAmount(finalRefundMap.get(item));
            RemoteStrockBatchRefundBo remoteStrockBatchRefundBo = finalLessRefundAmountMap.get(item);
            if (Objects.nonNull(remoteStrockBatchRefundBo)){
                refundBo.setLessAmount(remoteStrockBatchRefundBo.getLessAmount());
                refundBo.setLessCount(remoteStrockBatchRefundBo.getLessCount());
            }
            return refundBo;
        }).collect(Collectors.toList());
    }

    /**
     * 运维用补充退款
     */
    @Override
    public void refundByAdmin(CreateRefundRecordBO refundRecordBO) {
        //获取订单项信息
        if (ObjectUtil.isNotNull(refundRecordBO)) {
            QueryDistributionBo queryDistributionBo = new QueryDistributionBo();
            List<Long> itemId = ListUtil.toList(refundRecordBO.getOrderItemId());
            queryDistributionBo.setOrderItemIdList(itemId);
            List<QueryDistributionVo> orderList = orderService.queryDistribution(queryDistributionBo);
            if (CollectionUtil.isNotEmpty(orderList)) {
                Map<Long, QueryDistributionVo> orderMap = orderList.stream().collect(Collectors.toMap(QueryDistributionVo::getId, Function.identity()));
                List<QueryDistributionItemVo> list = orderList.stream().map(QueryDistributionVo::getOrderItemList).flatMap(List::stream).toList();
                Map<Long, List<QueryDistributionItemVo>> map = list.stream().collect(Collectors.groupingBy(QueryDistributionItemVo::getOrderId));
                //获取销售批次商品信息
                RemoteQueryInfoListBo bo = new RemoteQueryInfoListBo();
                bo.setSupplierSkuIdList(list.stream().map(QueryDistributionItemVo::getSupplierSkuId).distinct().toList());
                List<RemoteSupplierSkuInfoVo> skuInfoVo = remoteSupplierSkuService.queryInfoList(bo);
                Map<Long, RemoteSupplierSkuInfoVo> skuMap = skuInfoVo.stream().collect(Collectors.toMap(RemoteSupplierSkuInfoVo::getId, Function.identity()));
                //获取物流信息
                RemoteRegionLogisticsQueryBo queryBo = new RemoteRegionLogisticsQueryBo();
                queryBo.setLogisticsIds(list.stream().map(QueryDistributionItemVo::getLogisticsId).distinct().toList());
                List<RemoteRegionLogisticsVo> logisticsVos = remoteRegionLogisticsService.queryList(queryBo);
                Map<Long, RemoteRegionLogisticsVo> logisticsMap = logisticsVos.stream().collect(Collectors.toMap(RemoteRegionLogisticsVo::getId, Function.identity()));
                //获取供应商
                List<Long> supList = orderList.stream().map(QueryDistributionVo::getOrderItemList).flatMap(List::stream).map(QueryDistributionItemVo::getSupplierId).toList();
                List<RemoteSupplierVo> remoteSupplierVos = remoteSupplierService.getSupplierByIds(supList);
                Map<Long, String> stringMap = remoteSupplierVos.stream().collect(Collectors.toMap(RemoteSupplierVo::getId, RemoteSupplierVo::getCode, (v1, v2) -> v1));
                List<RefundRecord> updateRecords = new ArrayList<>();
                List<RefundProductDetail> addRecords = new ArrayList<>();
                map.forEach((k, v) -> {
                    RefundRecord record = new RefundRecord();
                    QueryDistributionVo orderInfo = orderMap.get(k);
                    record.setCode(generateRefundCode());
                    record.setOrderId(k);
                    record.setSourceCode(refundRecordBO.getSourceCode());
                    record.setOrderCode(orderInfo.getCode());
                    record.setCustomerId(v.get(0).getCustomerId());
                    record.setCustomerName(orderInfo.getCustomerName());
                    record.setCityWhId(v.get(0).getCityWhId());
                    record.setRegionWhId(v.get(0).getRegionWhId());
                    record.setRefundType(refundRecordBO.getType());
                    record.setRefundStatus(RefundStatusEnum.IN_REFUND.getCode());
                    record.setPlaceId(orderInfo.getPlaceId());
                    record.setPlaceIdLevel2(orderInfo.getPlaceIdLevel2());
                    record.setPlacePath(orderInfo.getPlacePath());
                    record.setPayTime(v.get(0).getPayTime());
                    record.setRemark(refundRecordBO.getRemark());
                    record.setRefundFinancialServiceAmount(refundRecordBO.getRefundFinancialServicePrice());
                    List<RefundProductDetail> refundProductDetails = new ArrayList<>();
                    BigDecimal productAmount = BigDecimal.ZERO;
                    BigDecimal refundOtherAmount = BigDecimal.ZERO;
                    for (QueryDistributionItemVo l : v) {
                        RefundProductDetail detail = new RefundProductDetail();
                        detail.setRefundRecordId(record.getId());
                        detail.setOrderItemId(l.getId());
                        detail.setSupplierSkuId(l.getSupplierSkuId());
                        detail.setSupplierSkuCode(skuMap.get(l.getSupplierSkuId()).getCode());
                        detail.setSaleDate(l.getSaleDate());
                        detail.setSupplierId(l.getSupplierId());
                        detail.setSupplierDeptId(l.getSupplierDeptId());
                        detail.setOriginalSupplierId(l.getSupplierId());
                        detail.setLogisticsId(l.getLogisticsId());
                        detail.setLogisticsName(logisticsMap.get(l.getLogisticsId()).getLogisticsName());
                        detail.setSpuId(skuMap.get(l.getSupplierSkuId()).getSpuId());
                        detail.setSpuName(l.getSpuName());
                        detail.setSpuStandards(skuMap.get(l.getSupplierSkuId()).getSpuStandards());
                        detail.setSpuGrossWeight(l.getSpuGrossWeight());
                        detail.setSpuNetWeight(l.getSpuNetWeight());
                        detail.setOrderCount(l.getCount());
                        detail.setStockoutCount(refundRecordBO.getCount());
                        //服务单价 服务费÷总数量
                        BigDecimal singServicePrice = l.getPlatformServiceAmount().divide(new BigDecimal(l.getCount()), 2, RoundingMode.FLOOR);
                        detail.setCollectAgentPrice(singServicePrice);
                        detail.setOrderPrice(l.getPrice());
                        detail.setSettlePrice(l.getFinalPrice());
                        //缺货少货阶段都是未结算,退价类型取下单价
                        detail.setRefundPriceType(RefundPriceTypeEnum.STTLE_PRICE.getCode());
                        detail.setRefundStatus(RefundStatusEnum.IN_REFUND.getCode());
                        detail.setRefundType(refundRecordBO.getType());
                        detail.setImgUrl(l.getImgUrl());
                        detail.setRefundProductAmount(refundRecordBO.getRefundProductAmount());
                        detail.setRefundOtherAmount(refundRecordBO.getRefundOtherAmount());
                        detail.setRefundAmount(detail.getRefundProductAmount().add(detail.getRefundOtherAmount()));
                        detail.setRefundSubsidyFreeAmount(refundRecordBO.getRefundSubsidyFreeAmount());
                        detail.setRefundProductFreeAmount(BigDecimal.ZERO);
                        detail.setProdType(l.getProdType());
                        detail.setRefundDistributionAmount(refundRecordBO.getRefundDistributionAmount());
                        detail.setRefundDistributionTaxAmount(refundRecordBO.getRefundDistributionTaxAmount());
                        detail.setRefundFinancialServicePrice(refundRecordBO.getRefundFinancialServicePrice());
                        detail.setRefundPlatformFreight(refundRecordBO.getRefundPlatformFreight());
                        detail.setRefundPlatformFreightAmountLevel2(refundRecordBO.getRefundPlatformFreightAmountLevel2());
                        detail.setRefundBaseFreight(refundRecordBO.getRefundBaseFreight());
                        detail.setRefundRegionFreight(refundRecordBO.getRefundRegionFreight());
                        detail.setRefundFreightAmount(refundRecordBO.getRefundFreightAmount());
                        detail.setRefundServiceAmount(refundRecordBO.getRefundServiceAmount());
                        productAmount = productAmount.add(detail.getRefundProductAmount());
                        refundOtherAmount = refundOtherAmount.add(detail.getRefundOtherAmount());
                        refundProductDetails.add(detail);
                    }
                    record.setRefundProductAmount(productAmount);
                    record.setRefundOtherAmount(refundOtherAmount);
                    record.setRefundAmount(record.getRefundOtherAmount().add(record.getRefundProductAmount()));
                    record.setRefundSubsidyFreeAmount(refundProductDetails.stream().map(RefundProductDetail::getRefundSubsidyFreeAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                    record.setRefundProductFreeAmount(BigDecimal.ZERO);
                    baseMapper.insert(record);
                    refundProductDetails.forEach(l -> l.setRefundRecordId(record.getId()));
                    OrderRefundBo orderRefundBo = new OrderRefundBo();
                    orderRefundBo.setOrderId(k).setOrderNo(record.getOrderCode()).setRefundId(record.getId()).setRefundNo(record.getCode())
                            .setRefundAmt(record.getRefundProductAmount().add(record.getRefundOtherAmount()));
                    List<OrderPaySplitBo> splits = new ArrayList<>();
                    orderRefundBo.setInfAuto(YNStatusEnum.ENABLE.getCode());
                    //商品金额 扣供应商仓
                    if (record.getRefundProductAmount().compareTo(BigDecimal.ZERO) > 0) {
                        Map<Long, List<RefundProductDetail>> collect = refundProductDetails.stream().collect(Collectors.groupingBy(RefundProductDetail::getSupplierId));
                        collect.forEach((supId, details) -> {
                            Map<Long, List<RefundProductDetail>> listMap = details.stream().collect(Collectors.groupingBy(RefundProductDetail::getSupplierSkuId));
                            listMap.forEach((skuId, spuDetails) -> {
                                BigDecimal reduce = spuDetails.stream().map(RefundProductDetail::getRefundProductAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                                BigDecimal distribution = spuDetails.stream().map(RefundProductDetail::getRefundDistributionAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                                BigDecimal tax = spuDetails.stream().map(RefundProductDetail::getRefundDistributionTaxAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                                if (reduce.compareTo(BigDecimal.ZERO) > 0) {
                                    OrderPaySplitBo split = new OrderPaySplitBo();
                                    split.setOrgId(supId).setOrgType(AccountOrgTypeEnum.SUPPLIER.getCode()).setOrgCode(stringMap.get(supId))
                                            .setTransAmt(record.getRefundProductAmount().add(record.getRefundSubsidyFreeAmount())).setSkuId(skuId)
                                            .setSplitAmt(record.getRefundProductAmount()).setDeptId(skuInfoVo.get(0).getSupplierDeptId());
                                    if (refundRecordBO.getCommCustomerId() != null) {
                                        split.setCommissionAmt(distribution.add(tax));
                                        split.setCommSalaryAmt(distribution);
                                        split.setCommCustomerId(refundRecordBO.getCommCustomerId());
                                    }
                                    splits.add(split);
                                }
                            });
                        });
                    }
                    //非商品金额 扣总仓
                    if (record.getRefundOtherAmount().compareTo(BigDecimal.ZERO) > 0) {
                        OrderPaySplitBo split = new OrderPaySplitBo();
                        //获取总仓
                        RemoteRegionWhVo remoteRegionWhVo = remoteRegionWhService.queryById(record.getRegionWhId());
                        split.setOrgId(record.getRegionWhId()).setOrgType(AccountOrgTypeEnum.REGION_WH.getCode()).setOrgCode(remoteRegionWhVo.getRegionWhCode())
                                .setTransAmt(record.getRefundOtherAmount()).setSplitAmt(record.getRefundOtherAmount());
                        splits.add(split);
                    }
                    //保险起见  这里都走审核
                    orderRefundBo.setInfAuto(YNStatusEnum.DISABLE.getCode());
                    record.setAuditStatus(YNStatusEnum.DISABLE.getCode());
                    record.setOccupyStatus(OccupyEnum.NOT.getCode());
                    orderRefundBo.setIsOccupy(OccupyEnum.NOT.getCode());
                    if (refundRecordBO.getType().equals(RefundTypeEnum.LACK.getCode())) {
                        orderRefundBo.setBusiType(RefundBusiTypeEnum.REFUND_SHORT.getCode());
                    } else {
                        orderRefundBo.setBusiType(RefundBusiTypeEnum.REFUND_LACK.getCode());
                    }
                    orderRefundBo.setSplits(splits);
                    //都成功,退款成功改状态
                    record.setRefundStatus(RefundStatusEnum.IN_REFUND.getCode());
                    refundProductDetails.forEach(l -> {
                        l.setRefundStatus(RefundStatusEnum.IN_REFUND.getCode());
                        l.setRefundTime(null);
                    });
                    //真.调用退款了
                    try {
                        log.keyword("refundByAdmin", record.getCode(), record.getOrderId()).info("运维退款调用退款入参【{}】", orderRefundBo);
                        remotePaymentService.refund(orderRefundBo);
                    } catch (ServiceException e) {
                        record.setRefundStatus(RefundStatusEnum.REFUND_FAIL.getCode());
                        refundProductDetails.forEach(l -> l.setRefundStatus(RefundStatusEnum.REFUND_FAIL.getCode()));
                        log.keyword("refundByAdmin", record.getCode(), record.getOrderId()).error("运维退款失败:{}", e.getMessage());
                        record.setRemark(record.getRemark() + ";退款失败原因：" +e.getMessage());
                    }
                    updateRecords.add(record);
                    if (CollectionUtil.isNotEmpty(refundProductDetails)) {
                        addRecords.addAll(refundProductDetails);
                    }
                });
                if (CollectionUtil.isNotEmpty(updateRecords)) {
                    baseMapper.updateBatchById(updateRecords);
                }
                if (CollectionUtil.isNotEmpty(addRecords)) {
                    refundProductDetailMapper.insertBatch(addRecords);
                }
            }
        }
    }
    /**
     * 退款审核不通过
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditReturn(Long id) {
        RefundRecord record = baseMapper.selectById(id);
        if (ObjectUtil.isNull(record) || record.getRefundStatus().equals(RefundStatusEnum.HAS_REFUND.getCode())){
            throw new ServiceException("退款单不存在或已退款");
        }
        if (!record.getRefundStatus().equals(0)){
            throw new ServiceException("该退款单已审核");
        }
        //类型是取消订单的不给不通过，没办法处理逻辑
        if (record.getRefundType().equals(RefundTypeEnum.CANCEL.getCode())) {
            throw new ServiceException("取消订单的退款单不支持审核不通过");
        }
        //类型是差额退款，报损，直接修改状态,释放退款单，缺货少货先作废
        if (record.getRefundType().equals(RefundTypeEnum.LACK.getCode()) || record.getRefundType().equals(RefundTypeEnum.LESS.getCode())) {
            //根据code获取缺货少货单id
            StockoutRecord stockoutRecord = stockoutRecordMapper.selectByCode(record.getSourceCode());
            List<StockoutRecordDetail> recordDetails = stockoutRecordDetailMapper.selectByRecordId(stockoutRecord.getId());
            //只匹配一个订单
            if (recordDetails.size() == 1) {
                //直接改为待确认
                stockoutRecord.setAuditStatus(StockoutAuditStatusEnum.STATE_CONFIRM.getCode());
                stockoutRecordMapper.updateById(stockoutRecord);
                //作废少货单
                executeAfterTransaction(() -> stockoutRecordService.stockoutInvalid(stockoutRecord.getId()));
                //关闭退款
                closeRefund(record);
            }else {
                throw new ServiceException("该退款单对应的缺货少货单有多个订单，请人工处理");
            }
        } else if (record.getRefundType().equals(RefundTypeEnum.REPORT_LOSS.getCode())) {
            ReportLossOrder lossOrder = reportLossOrderMapper.selectByNo(record.getSourceCode());
            lossOrder.setRefundGoodsAmount(lossOrder.getRefundGoodsAmount().subtract(record.getRefundAmount()));
            reportLossOrderMapper.updateById(lossOrder);
            closeRefund(record);
        }else {
            throw new ServiceException("该退款单类型不支持审核不通过");
        }
    }

    /**
     * 关闭退款单
     */
    private void closeRefund(RefundRecord record) {
        record.setAuditStatus(2);
        record.setRefundStatus(RefundStatusEnum.REFUND_FAIL.getCode());
        List<RefundProductDetail> refundProductDetails = refundProductDetailMapper.selectList(new LambdaQueryWrapper<RefundProductDetail>()
                .eq(RefundProductDetail::getRefundRecordId, record.getId())
                .eq(RefundProductDetail::getDelFlag, 0).select(RefundProductDetail::getId, RefundProductDetail::getRefundStatus));
        if (CollectionUtil.isNotEmpty(refundProductDetails)) {
            refundProductDetails.forEach(l -> l.setRefundStatus(RefundStatusEnum.REFUND_FAIL.getCode()));
            refundProductDetailMapper.updateBatchById(refundProductDetails);
        }
        OrderRefundCheckBo orderRefundBo = new OrderRefundCheckBo();
        orderRefundBo.setOrderNo(record.getOrderCode()).setRefundNo(record.getCode());
        //释放退款单
        log.keyword("stockoutRefund", record.getCode(), record.getOrderId()).info("调用退款单关闭入参【{}】", orderRefundBo);
        remotePaymentService.refundClose(orderRefundBo);
        baseMapper.updateById(record);
    }

    private void createDeductionInfo(QueryDistributionItemVo orderItemVo, BigDecimal amount, ReportLossOrder lossOrder) {
        DeductionInfoParamBo bo = new DeductionInfoParamBo();
        //查总仓信息
        RemoteRegionWhVo remoteRegionWhVo = remoteRegionWhService.queryById(orderItemVo.getRegionWhId());
        //获取供应商信息
        RemoteSupplierVo supplier = remoteSupplierService.getSupplierById(orderItemVo.getSupplierId());
        //获取文件
        List<RemoteOssVo> ossVoList = remoteFileService.select(OssBusinessTypeEnum.REPORT_LOSS_FLOW.getCode(), lossOrder.getId().toString());
        //文件赋值
        List<OrderFileBO> fileList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(ossVoList)) {
            ossVoList.forEach(item -> {
                OrderFileBO file = new OrderFileBO();
                file.setUrl(item.getUrl());
                file.setTag(item.getTag());
                file.setSort(item.getSort());
                fileList.add(file);
            });
        }
        //供应商扣款
        bo.setCommonId(orderItemVo.getSupplierId());
        bo.setCommonName(supplier.getName());

        bo.setRegionWhId(orderItemVo.getRegionWhId());
        bo.setRegionWhName(remoteRegionWhVo.getRegionWhName());
        bo.setCode(CustomNoUtil.getDeductionNo(LocalDate.now()));
        //供应商承担商品金额,有优惠金额,扣款回来
        bo.setAmount(amount);
        bo.setType(DeductionInfoEnum.SUPPLIER.getCode());
        bo.setAmountType(AmountTypeEnum.LOSS.getCode());
        bo.setDeductionType(DeductionTypeEnum.KKYY_SPBTSH.getCode());
        bo.setBillType(BillTypeEnum.ORDER.getCode());

        bo.setBillCode(orderItemVo.getOrderCode());
        bo.setSupplierSkuId(orderItemVo.getSupplierSkuId().toString());
        bo.setSaleDate(orderItemVo.getSaleDate());
        bo.setSpuName(orderItemVo.getSpuName());
        bo.setFileList(BeanUtil.copyToList(fileList, cn.xianlink.order.api.bo.OrderFileBO.class));
        bo.setStatus(DeductionInfoStatusEnum.COMMITTED.getCode());
        bo.setCreateType(CreateTypeEnum.AUTO.getCode());
        bo.setDeductionReason("商品报损金额超50%，退回总仓商品补贴。");
        bo.setBillTime(new Date());
        bo.setLogisticsId(orderItemVo.getLogisticsId());
        bo.setPlaceIdLevel2(orderItemVo.getPlaceIdLevel2());
        log.keyword("createDeductionInfo","确认判责发送扣款单").info("入参：{}", bo);
        deductionInfoCompleteProducer.send(bo);
    }

    private void createDeductionInfo(OrderItem orderItemVo, BigDecimal amount) {
        DeductionInfoParamBo bo = new DeductionInfoParamBo();
        //查总仓信息
        RemoteRegionWhVo remoteRegionWhVo = remoteRegionWhService.queryById(orderItemVo.getRegionWhId());
        //获取供应商信息
        RemoteSupplierVo supplier = remoteSupplierService.getSupplierById(orderItemVo.getSupplierId());

        //供应商扣款
        bo.setCommonId(orderItemVo.getSupplierId());
        bo.setCommonName(supplier.getName());

        bo.setRegionWhId(orderItemVo.getRegionWhId());
        bo.setRegionWhName(remoteRegionWhVo.getRegionWhName());
        bo.setCode(CustomNoUtil.getDeductionNo(LocalDate.now()));
        //供应商承担商品金额,有优惠金额,扣款回来
        bo.setAmount(amount);
        bo.setType(DeductionInfoEnum.SUPPLIER.getCode());
        bo.setAmountType(AmountTypeEnum.LOSS.getCode());
        bo.setDeductionType(DeductionTypeEnum.KKYY_SPBTJSHSH.getCode());
        bo.setBillType(BillTypeEnum.ORDER.getCode());

        bo.setBillCode(orderItemVo.getOrderCode());
        bo.setSupplierSkuId(orderItemVo.getSupplierSkuId().toString());
        bo.setSaleDate(orderItemVo.getSaleDate());
        bo.setSpuName(orderItemVo.getSpuName());
        bo.setStatus(DeductionInfoStatusEnum.COMMITTED.getCode());
        bo.setCreateType(CreateTypeEnum.AUTO.getCode());
        bo.setDeductionReason("结算后少货，退回总仓商品补贴。");
        bo.setBillTime(new Date());
        bo.setLogisticsId(orderItemVo.getLogisticsId());
        bo.setPlaceIdLevel2(orderItemVo.getPlaceIdLevel2());
        log.keyword("createDeductionInfo","确认判责发送扣款单").info("入参：{}", bo);
        deductionInfoCompleteProducer.send(bo);
    }


    /**
     * 生成退款单号
     * 90 + 183AF + 240701 + 7位，字符串
     * 90 183AF 240701 102DK3JDF2
     */
    private String generateRefundCode() {
        return "90" + CustomNoUtil.getRefundNo(LocalDate.now());
    }
}