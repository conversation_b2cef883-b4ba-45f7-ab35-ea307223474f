package cn.xianlink.order.controller.test;

import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.domain.order.vo.OriginOrderDetailVo;
import cn.xianlink.order.service.IOrderService;
import cn.xianlink.order.service.support.ISupportOrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 订单测试控制器
 * 用于测试订单相关功能
 * 前端访问路由地址为:/order/test/order
 * 
 * <AUTHOR>
 * @date 2024-12-01
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/order/test/order")
@Tag(name = "订单测试接口", description = "用于测试订单相关功能的接口")
public class OrderTestController extends BaseController {

    private final ISupportOrderService supportOrderService;

    /**
     * 测试查询某个人最近N天的订单列表
     * @param customerId 客户ID
     * @param days 最近天数
     * @return 订单列表
     */
    @GetMapping("/testGetRecentOrders")
    @Operation(summary = "测试查询某个人最近N天的订单列表", description = "用于测试新开发的查询最近订单功能")
    public R<List<OriginOrderDetailVo>> testGetRecentOrdersByCustomer(
            @RequestParam Long customerId, 
            @RequestParam Integer days) {
        try {
            // 参数验证
            if (customerId == null || customerId <= 0) {
                return R.fail("客户ID必须大于0");
            }
            if (days == null || days <= 0) {
                return R.fail("天数必须大于0");
            }
            if (days > 365) {
                return R.fail("查询天数不能超过365天");
            }

            // 调用服务方法
            List<OriginOrderDetailVo> orders = supportOrderService.getRecentOrderList(customerId, days);
            
            // 返回结果统计
            int totalOrders = orders.size();
            int totalItems = orders.stream().mapToInt(order -> order.getOrderItemList().size()).sum();

            return R.ok(String.format("查询成功，共找到 %d 个订单，%d 个订单项", totalOrders, totalItems), orders);
            
        } catch (ServiceException e) {
            return R.fail("业务异常: " + e.getMessage());
        } catch (Exception e) {
            log.error("测试查询最近订单失败", e);
            return R.fail("系统异常: " + e.getMessage());
        }
    }

    /**
     * 测试查询最近7天订单（快捷方法）
     * @param customerId 客户ID
     * @return 最近7天订单列表
     */
    @GetMapping("/testGetRecentWeekOrders")
    @Operation(summary = "测试查询最近7天订单", description = "快捷测试方法，查询指定客户最近7天的订单")
    public R<List<OriginOrderDetailVo>> testGetRecentWeekOrders(@RequestParam Long customerId) {
        return testGetRecentOrdersByCustomer(customerId, 7);
    }

    /**
     * 测试查询最近30天订单（快捷方法）
     * @param customerId 客户ID
     * @return 最近30天订单列表
     */
    @GetMapping("/testGetRecentMonthOrders")
    @Operation(summary = "测试查询最近30天订单", description = "快捷测试方法，查询指定客户最近30天的订单")
    public R<List<OriginOrderDetailVo>> testGetRecentMonthOrders(@RequestParam Long customerId) {
        return testGetRecentOrdersByCustomer(customerId, 30);
    }

    /**
     * 批量测试多个客户的最近订单
     * @param customerIds 客户ID列表（逗号分隔）
     * @param days 最近天数
     * @return 测试结果
     */
    @GetMapping("/testBatchGetRecentOrders")
    @Operation(summary = "批量测试多个客户的最近订单", description = "用于批量测试多个客户的订单查询功能")
    public R<String> testBatchGetRecentOrders(
            @RequestParam String customerIds, 
            @RequestParam(defaultValue = "7") Integer days) {
        try {
            String[] idArray = customerIds.split(",");
            StringBuilder result = new StringBuilder();
            int totalOrders = 0;
            int totalItems = 0;
            
            for (String idStr : idArray) {
                try {
                    Long customerId = Long.parseLong(idStr.trim());
                    List<OriginOrderDetailVo> orders = supportOrderService.getRecentOrderList(customerId, days);
                    int orderCount = orders.size();
                    int itemCount = orders.stream().mapToInt(order -> order.getOrderItemList().size()).sum();
                    
                    totalOrders += orderCount;
                    totalItems += itemCount;
                    
                    result.append(String.format("客户ID %d: %d个订单, %d个订单项\n", 
                            customerId, orderCount, itemCount));
                } catch (NumberFormatException e) {
                    result.append(String.format("无效的客户ID: %s\n", idStr));
                } catch (Exception e) {
                    result.append(String.format("客户ID %s 查询失败: %s\n", idStr, e.getMessage()));
                }
            }
            
            result.append(String.format("\n总计: %d个订单, %d个订单项", totalOrders, totalItems));
            return R.ok(result.toString());
            
        } catch (Exception e) {
            log.error("批量测试失败", e);
            return R.fail("批量测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试方法性能
     * @param customerId 客户ID
     * @param days 最近天数
     * @return 性能测试结果
     */
    @GetMapping("/testPerformance")
    @Operation(summary = "测试查询性能", description = "测试查询最近订单方法的性能")
    public R<String> testPerformance(@RequestParam Long customerId, @RequestParam Integer days) {
        try {
            long startTime = System.currentTimeMillis();
            
            List<OriginOrderDetailVo> orders = supportOrderService.getRecentOrderList(customerId, days);
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            int orderCount = orders.size();
            int itemCount = orders.stream().mapToInt(order -> order.getOrderItemList().size()).sum();
            
            String result = String.format(
                "性能测试结果:\n" +
                "客户ID: %d\n" +
                "查询天数: %d\n" +
                "执行时间: %d ms\n" +
                "订单数量: %d\n" +
                "订单项数量: %d\n" +
                "平均每个订单处理时间: %.2f ms",
                customerId, days, duration, orderCount, itemCount,
                orderCount > 0 ? (double) duration / orderCount : 0
            );
            
            return R.ok(result);
            
        } catch (Exception e) {
            log.error("性能测试失败", e);
            return R.fail("性能测试失败: " + e.getMessage());
        }
    }
}
