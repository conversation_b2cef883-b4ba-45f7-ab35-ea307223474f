package cn.xianlink.order.service;

import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.order.domain.replaceSupplierRecord.bo.QueryReplacePageBo;
import cn.xianlink.order.domain.replaceSupplierRecord.vo.ReplaceSupplierRecordVo;

/**
 * 换供应商转单记录Service接口
 *
 * <AUTHOR>
 * @date 2024-06-17
 */
public interface IReplaceSupplierRecordService {

    /**
     * 查询换供应商转单记录
     */
    ReplaceSupplierRecordVo queryById(Long id);

    /**
     * 查询换供应商转单记录列表
     */
    TableDataInfo<ReplaceSupplierRecordVo> queryPage(QueryReplacePageBo bo);
}
