package cn.xianlink.order.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.common.satoken.utils.LoginHelper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import cn.xianlink.order.domain.bo.InvoiceTitleBo;
import cn.xianlink.order.domain.vo.InvoiceTitleVo;
import cn.xianlink.order.domain.InvoiceTitle;
import cn.xianlink.order.mapper.InvoiceTitleMapper;
import cn.xianlink.order.service.IInvoiceTitleService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 发票抬头Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class InvoiceTitleServiceImpl implements IInvoiceTitleService {

    private final InvoiceTitleMapper baseMapper;

    /**
     * 查询发票抬头
     */
    @Override
    public InvoiceTitleVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询发票抬头列表
     */
    @Override
    public TableDataInfo<InvoiceTitleVo> queryPageList(InvoiceTitleBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<InvoiceTitle> lqw = buildQueryWrapper(bo);
        Page<InvoiceTitleVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询发票抬头列表
     */
    @Override
    public List<InvoiceTitleVo> queryList(InvoiceTitleBo bo) {
        LambdaQueryWrapper<InvoiceTitle> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<InvoiceTitle> buildQueryWrapper(InvoiceTitleBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<InvoiceTitle> lqw = Wrappers.lambdaQuery();

        // 基础查询条件
        lqw.eq(bo.getCustomerId() != null, InvoiceTitle::getCustomerId, bo.getCustomerId());
        lqw.like(StringUtils.isNotBlank(bo.getTitleName()), InvoiceTitle::getTitleName, bo.getTitleName());
        lqw.eq(StringUtils.isNotBlank(bo.getTaxNumber()), InvoiceTitle::getTaxNumber, bo.getTaxNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getTitleType()), InvoiceTitle::getTitleType, bo.getTitleType());
        lqw.eq(StringUtils.isNotBlank(bo.getInvoiceType()), InvoiceTitle::getInvoiceType, bo.getInvoiceType());
        lqw.like(StringUtils.isNotBlank(bo.getRecipientName()), InvoiceTitle::getRecipientName, bo.getRecipientName());
        lqw.eq(StringUtils.isNotBlank(bo.getRegion()), InvoiceTitle::getRegion, bo.getRegion());

        // 联系信息查询条件
        lqw.eq(StringUtils.isNotBlank(bo.getPhone()), InvoiceTitle::getPhone, bo.getPhone());
        lqw.eq(StringUtils.isNotBlank(bo.getContactPhone()), InvoiceTitle::getContactPhone, bo.getContactPhone());
        lqw.eq(StringUtils.isNotBlank(bo.getEmailAddress()), InvoiceTitle::getEmailAddress, bo.getEmailAddress());

        // 地址信息查询条件
        lqw.like(StringUtils.isNotBlank(bo.getAddress()), InvoiceTitle::getAddress, bo.getAddress());
        lqw.like(StringUtils.isNotBlank(bo.getDetailedAddress()), InvoiceTitle::getDetailedAddress, bo.getDetailedAddress());

        // 银行信息查询条件
        lqw.eq(StringUtils.isNotBlank(bo.getBankAccount()), InvoiceTitle::getBankAccount, bo.getBankAccount());

        // 创建者和修改者查询条件
        lqw.eq(StringUtils.isNotBlank(bo.getCreateCode()), InvoiceTitle::getCreateCode, bo.getCreateCode());
        lqw.like(StringUtils.isNotBlank(bo.getCreateName()), InvoiceTitle::getCreateName, bo.getCreateName());
        lqw.eq(StringUtils.isNotBlank(bo.getUpdateCode()), InvoiceTitle::getUpdateCode, bo.getUpdateCode());
        lqw.like(StringUtils.isNotBlank(bo.getUpdateName()), InvoiceTitle::getUpdateName, bo.getUpdateName());

        // 时间范围查询
        if (params != null) {
            if (params.get("beginCreateTime") != null && params.get("endCreateTime") != null) {
                lqw.between(InvoiceTitle::getCreateTime, params.get("beginCreateTime"), params.get("endCreateTime"));
            }
            if (params.get("beginUpdateTime") != null && params.get("endUpdateTime") != null) {
                lqw.between(InvoiceTitle::getUpdateTime, params.get("beginUpdateTime"), params.get("endUpdateTime"));
            }
        }

        // 按创建时间倒序排列
        lqw.orderByDesc(InvoiceTitle::getCreateTime);

        return lqw;
    }

    /**
     * 新增发票抬头
     */
    @Override
    public Boolean insertByBo(InvoiceTitleBo bo) {
        InvoiceTitle add = MapstructUtils.convert(bo, InvoiceTitle.class);

        // 设置创建者信息
        var loginUser = LoginHelper.getLoginUser();
        if (loginUser != null) {
            add.setCreateCode(loginUser.getUserCode());
            add.setCreateName(loginUser.getRealName());
        }

        // 数据校验
        validEntityBeforeSave(add);

        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
            log.info("成功创建发票抬头，ID: {}, 抬头名称: {}", add.getId(), add.getTitleName());
        }
        return flag;
    }

    /**
     * 修改发票抬头
     */
    @Override
    public Boolean updateByBo(InvoiceTitleBo bo) {
        InvoiceTitle update = MapstructUtils.convert(bo, InvoiceTitle.class);

        // 设置修改者信息
        var loginUser = LoginHelper.getLoginUser();
        if (loginUser != null) {
            update.setUpdateCode(loginUser.getUserCode());
            update.setUpdateName(loginUser.getRealName());
        }

        // 数据校验
        validEntityBeforeSave(update);

        boolean result = baseMapper.updateById(update) > 0;
        if (result) {
            log.info("成功更新发票抬头，ID: {}, 抬头名称: {}", update.getId(), update.getTitleName());
        }
        return result;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(InvoiceTitle entity){
        // 1. 基础字段校验
        if (entity.getCustomerId() == null) {
            throw new ServiceException("客户ID不能为空");
        }
        if (StrUtil.isBlank(entity.getTitleName())) {
            throw new ServiceException("发票抬头名称不能为空");
        }
        if (StrUtil.isBlank(entity.getTitleType())) {
            throw new ServiceException("发票抬头类型不能为空");
        }
        if (StrUtil.isBlank(entity.getInvoiceType())) {
            throw new ServiceException("发票类型不能为空");
        }

        // 2. 枚举值校验
        if (!"company".equals(entity.getTitleType()) && !"personal".equals(entity.getTitleType())) {
            throw new ServiceException("发票抬头类型只能是company或personal");
        }
        if (!"normal".equals(entity.getInvoiceType()) && !"special".equals(entity.getInvoiceType())) {
            throw new ServiceException("发票类型只能是normal或special");
        }

        // 3. 公司类型必须填写税号
        if ("company".equals(entity.getTitleType()) && StrUtil.isBlank(entity.getTaxNumber())) {
            throw new ServiceException("公司类型发票抬头必须填写税号");
        }

        // 4. 专票必须是公司类型
        if ("special".equals(entity.getInvoiceType()) && !"company".equals(entity.getTitleType())) {
            throw new ServiceException("专用发票只能选择公司类型抬头");
        }

        log.debug("发票抬头数据校验通过，客户ID: {}, 抬头名称: {}, 抬头类型: {}, 发票类型: {}",
            entity.getCustomerId(), entity.getTitleName(), entity.getTitleType(), entity.getInvoiceType());
    }

    /**
     * 批量删除发票抬头（逻辑删除）
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (ids == null || ids.isEmpty()) {
            throw new ServiceException("删除的ID列表不能为空");
        }

        if (isValid) {
            // 业务校验：检查是否有发票抬头正在被使用
            for (Long id : ids) {
                InvoiceTitleVo invoiceTitle = baseMapper.selectVoById(id);
                if (invoiceTitle == null) {
                    throw new ServiceException("发票抬头不存在，ID: " + id);
                }

                // TODO: 这里可以添加更多业务校验，比如检查是否有关联的发票记录
                // 例如：检查 inv_invoice 表中是否有使用该发票抬头的记录
                log.debug("校验发票抬头删除权限，ID: {}, 抬头名称: {}", id, invoiceTitle.getTitleName());
            }
        }

        // 执行逻辑删除（MyBatis Plus会自动处理@TableLogic注解的字段）
        boolean result = baseMapper.deleteBatchIds(ids) > 0;
        if (result) {
            log.info("成功删除发票抬头，删除数量: {}, IDs: {}", ids.size(), ids);
        }
        return result;
    }
}
