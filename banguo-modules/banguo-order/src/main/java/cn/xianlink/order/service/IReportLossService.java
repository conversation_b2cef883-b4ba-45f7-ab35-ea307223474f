package cn.xianlink.order.service;

import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.order.api.bo.RemoteSupAccTransQueryBo;
import cn.xianlink.order.api.vo.RemoteOrderBusiAmtVo;
import cn.xianlink.order.api.vo.RemoteSupTransRefundLossRecordVo;
import cn.xianlink.order.api.vo.RemoteSupTransRefundLossVo;
import cn.xianlink.order.domain.bo.report.ReportLossMiniListSearchBo;
import cn.xianlink.order.domain.bo.report.ReportLossOrderBo;
import cn.xianlink.order.domain.bo.report.ReportLossOrderFlowBo;
import cn.xianlink.order.domain.bo.report.ReportLossPcListSearchBo;
import cn.xianlink.order.domain.operation.ReportRefundDto;
import cn.xianlink.order.domain.vo.order.AdminOrderInfoVo;
import cn.xianlink.order.domain.vo.report.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 报损单
 *
 * <AUTHOR> xiaodaibing on 2024-06-13 15:30
 */
public interface IReportLossService {
    void create(ReportLossOrderBo bo);

    void processing(ReportLossOrderFlowBo bo);

    /**
     * 撤销
     *
     * @param id
     * @return void
     * <AUTHOR> on 2024/6/13:19:21
     */
    void revoke(Long reportId, Long orderItemId) throws ServiceException;

    TableDataInfo<ReportLossMiniListVo> miniPage(ReportLossMiniListSearchBo bo);

    ReportLossMiniDetailVo detail(Long orderItemId);

    Long getReportLossId(Long orderItemId);

    ReportLossOrderVo queryByCode (String reportLossNo);

    /**
     * 根据订单id，查询报损退款金额
     * <AUTHOR> on 2024/6/27:17:39
     * @param orderId
     * @return RemoteOrderBusiAmtVo
     */
    RemoteOrderBusiAmtVo selectRefundByOrderId(Long orderId);

    List<ReportNumStatisticsVo> numStatistics(Long customerId, Long supplierId, Long cityWhId, Long regionWhId, Long supplierDeptId);

    /**
     * 查询退款列表
     * <AUTHOR> on 2024/6/17:14:15
     * @param searchBo
     * @return cn.xianlink.common.mybatis.core.page.TableDataInfo<cn.xianlink.order.domain.vo.report.ReportLossPcListVo>
     */
    TableDataInfo<ReportLossPcListVo> refundPage(ReportLossPcListSearchBo searchBo);

    /**
     * 通过订单项查询报损明细
     * @param orderItemId
     * @return
     */
    List<ReportLossItemInfoVo> getByOrderItemId (Long orderItemId);



    /**
     * 延时检查
     * <AUTHOR> on 2024/6/19:10:01
     * @param id
     * @param lossStatus
     * @return void
     */
    void delayCheck(Long id, Integer lossStatus);

    /**
     * 资金账户
     * <AUTHOR> on 2024/7/24:19:47
     * @param bo
     * @return java.util.List<cn.xianlink.order.api.vo.RemoteSupTransRefundLossVo>
     */
    List<RemoteSupTransRefundLossVo> accTrans(RemoteSupAccTransQueryBo bo);

    List<RemoteSupTransRefundLossRecordVo> accTransRecord(RemoteSupAccTransQueryBo bo);

    /**
     * 报损退款回调
     * <AUTHOR> on 2024/8/3:20:29
     * @param refundId
     * @return void
     */
    void refundCall(Long refundId);

    /**
     * 报损占用结算金额
     * <AUTHOR> on 2024/10/19:10:54
     * @param reportId
     * @return void
     */
    void lossOccupy(Long reportId);

    /**
     * 计算报损
     * @param orderCode
     * @return
     */
    AdminOrderInfoVo getLossInfoByOrderCode(String orderCode);

    /**
     * 获取申请报损金额
     * @param orderCode
     * @return
     */
    BigDecimal applyLossAmount(String orderCode);

    /**
     * 查询sku的报损率
     * 基于退款查询
     * <AUTHOR> on 2024/11/18:10:22
     * @param skuIds
     * @return java.math.BigDecimal
     */
    BigDecimal getSkuLossRate(List<Long> skuIds, LocalDate saleDateStart, LocalDate saleDateEnd);

    /**
     * 手工重试退款
     * 用来解决程序的退款失败问题
     * <AUTHOR> on 2024/12/12:11:09
     * @return void
     */
    void refundAgain(ReportRefundDto dto);
    /**
     * 重开售后
     */
    void reopenAfterSales(Long itemId);

    TableDataInfo<ReportLossMiniListVo> lossOrderlist(ReportLossMiniListSearchBo bo);
}
