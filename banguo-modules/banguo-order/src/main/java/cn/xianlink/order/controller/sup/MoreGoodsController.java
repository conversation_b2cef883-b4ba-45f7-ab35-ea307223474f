package cn.xianlink.order.controller.sup;

import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.common.api.enums.order.AccountTypeStatusEnum;
import cn.xianlink.common.api.enums.system.SysUserTypeEnum;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.api.constant.MoreGoodsStatusEnum;
import cn.xianlink.order.domain.bo.moreGoods.AddMoreGoodsBO;
import cn.xianlink.order.domain.bo.moreGoods.MoreGoodsConfirmBO;
import cn.xianlink.order.domain.bo.moreGoods.MoreGoodsPageBO;
import cn.xianlink.order.domain.vo.common.CommonStatusCountVO;
import cn.xianlink.order.domain.vo.moreGoods.MoreGoodsDetailVO;
import cn.xianlink.order.domain.vo.moreGoods.MoreGoodsPageVO;
import cn.xianlink.order.service.IMoreGoodsService;
import cn.xianlink.system.api.model.LoginUser;
import io.swagger.annotations.ApiOperation;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
 * 供应商小程序-多货
 *
 * <AUTHOR>
 * @date 2024-05-25
 */
@Validated
@RequiredArgsConstructor
@RestController("supMoreGoodsController")
@RequestMapping("/order/sup/moreGoods")
@CustomLog
public class MoreGoodsController extends BaseController {

    private final IMoreGoodsService iMoreGoodsService;

    @PostMapping("/moreGoodsPage")
    @ApiOperation(value = "多货单列表")
    public R<TableDataInfo<MoreGoodsDetailVO>> moreGoodsPage(@RequestBody MoreGoodsPageBO bo){
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (ObjectUtil.isNotEmpty(loginUser) && ObjectUtil.isEmpty(bo.getSupplierDeptId())) {
            if (SysUserTypeEnum.SUPPLIER_USER.getType().equals(loginUser.getUserType()) && loginUser.getDeptId() != 0L) {
                bo.setSupplierDeptId(loginUser.getDeptId());
                log.keyword("MoreGoodsController", "moreGoodsPage","判责单查询档口信息moreGoodsPage")
                        .info("多货单查询档口信息，供应商用户查询，loginUser：{}, bo:{}", loginUser, bo);
            }
        } else {
            if (bo.getSupplierDeptId() == 0L) {
                bo.setSupplierDeptId(null);
            }
            log.keyword("MoreGoodsController", "moreGoodsPage","判责单查询档口信息moreGoodsPage")
                    .info("多货单查询档口信息无用户信息");
        }
        if (ObjectUtil.isEmpty(loginUser) || ObjectUtil.isEmpty(loginUser.getRelationId())) {
            throw new ServiceException("用户未登录");
        }
        bo.setSupplierId(loginUser.getRelationId());
        return R.ok(iMoreGoodsService.moreGoodsPage(bo));
    }

    @GetMapping("/getMoreGoodsByCode")
    @ApiOperation(value = "多货单详情，根据code查询")
    public R<MoreGoodsDetailVO> getMoreGoodsByCode(@RequestParam("code") String code){
        return R.ok(iMoreGoodsService.getMoreGoodsByCode(code));
    }

    @GetMapping("/getMoreGoodsCount")
    @ApiOperation(value = "获取多货单各状态数量")
    public R<CommonStatusCountVO> getMoreGoodsCount(@RequestParam(value = "regionWhId", required = false) Long regionWhId,
                                                    @RequestParam(value = "supplierDeptId", required = false) Long supplierDeptId){
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (ObjectUtil.isNotEmpty(loginUser) && ObjectUtil.isEmpty(supplierDeptId)) {
            if (SysUserTypeEnum.SUPPLIER_USER.getType().equals(loginUser.getUserType()) && loginUser.getDeptId() != 0L) {
                supplierDeptId = loginUser.getDeptId();
                log.keyword("MoreGoodsController", "moreGoodsPage","判责单查询档口信息moreGoodsPage")
                        .info("多货单查询档口信息，供应商用户查询，loginUser：{}", loginUser);
            }
        } else {
            if (supplierDeptId == 0L) {
                supplierDeptId = null;
            }
            log.keyword("MoreGoodsController", "moreGoodsPage","判责单查询档口信息moreGoodsPage")
                    .info("多货单查询档口信息无用户信息");
        }
        if (ObjectUtil.isEmpty(loginUser) || ObjectUtil.isEmpty(loginUser.getRelationId())) {
            throw new ServiceException("用户未登录");
        }
        AddMoreGoodsBO bo = new AddMoreGoodsBO();
        bo.setCityWhId(loginUser.getRelationId());
        bo.setSupplierDeptId(supplierDeptId);
        bo.setStatus(MoreGoodsStatusEnum.UNCONFIRMED.getCode());
        return R.ok(iMoreGoodsService.getMoreGoodsCount(bo));
    }

    @PostMapping("/addMoreGoods")
    @RepeatSubmit()
    @ApiOperation(value = "新建多货单")
    public R<Boolean> addMoreGoods(@RequestBody AddMoreGoodsBO bo){
        bo.setSourceType(AccountTypeStatusEnum.SUPPLIER.getCode());
        return R.ok(iMoreGoodsService.addMoreGoods(bo));
    }

    @GetMapping("/cancelMoreGoods/{id}")
    @RepeatSubmit()
    @ApiOperation(value = "撤销多货单")
    public R<Boolean> cancelMoreGoods(@PathVariable("id") Long id){
        return R.ok(iMoreGoodsService.cancelMoreGoods(id));
    }

    @PostMapping("/confirm")
    @RepeatSubmit()
    @ApiOperation(value = "确认多货单")
    public R<Boolean> confirm(@RequestBody MoreGoodsConfirmBO bo){
        bo.setSourceType(AccountTypeStatusEnum.SUPPLIER.getCode());
        return R.ok(iMoreGoodsService.confirm(bo));
    }

}
