package cn.xianlink.order.dubbo;

import cn.hutool.core.bean.BeanUtil;
import cn.xianlink.order.api.RemoteInvoiceService;
import cn.xianlink.order.api.bo.RemoteInvoiceBo;
import cn.xianlink.order.api.vo.RemoteInvoiceVo;
import cn.xianlink.order.domain.bo.InvoiceBo;
import cn.xianlink.order.domain.vo.InvoiceVo;
import cn.xianlink.order.service.IInvoiceService;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.List;

@RequiredArgsConstructor
@Service
@DubboService
@CustomLog
public class RemoteInvoiceServiceImpl implements RemoteInvoiceService {
    private final IInvoiceService invoiceService;

    @Override
    public List<RemoteInvoiceVo> queryList(RemoteInvoiceBo bo) {
        List<InvoiceVo> list = invoiceService.queryList(BeanUtil.copyProperties(bo, InvoiceBo.class));
        return BeanUtil.copyToList(list, RemoteInvoiceVo.class);
    }
}
