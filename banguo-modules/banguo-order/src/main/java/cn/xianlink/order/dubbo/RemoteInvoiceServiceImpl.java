package cn.xianlink.order.dubbo;

import cn.hutool.core.bean.BeanUtil;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.order.api.RemoteInvoiceService;
import cn.xianlink.order.api.bo.RemoteInvoiceBo;
import cn.xianlink.order.api.vo.RemoteInvoiceVo;
import cn.xianlink.order.domain.bo.InvoiceQueryBo;
import cn.xianlink.order.domain.vo.InvoiceQueryVo;
import cn.xianlink.order.service.IInvoiceService;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.List;

@RequiredArgsConstructor
@Service
@DubboService
@CustomLog
public class RemoteInvoiceServiceImpl implements RemoteInvoiceService {
    private final IInvoiceService invoiceService;

    @Override
    public List<RemoteInvoiceVo> queryList(RemoteInvoiceBo bo) {
        InvoiceQueryBo invoiceQueryBo = new InvoiceQueryBo();
        invoiceQueryBo.setPageSize(1000);
        BeanUtil.copyProperties(bo, invoiceQueryBo);
        TableDataInfo<InvoiceQueryVo> dataInfo = invoiceService.queryWaitInvoicePage(invoiceQueryBo);
        List<InvoiceQueryVo> list = dataInfo.getRows();
        return BeanUtil.copyToList(list, RemoteInvoiceVo.class);
    }
}
