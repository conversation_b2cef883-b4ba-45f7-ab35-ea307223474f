package cn.xianlink.order.mapper;

import cn.xianlink.common.mybatis.core.mapper.BaseMapperPlus;
import cn.xianlink.order.domain.DeductionInfo;
import cn.xianlink.order.domain.vo.DeductionInfoVo;
import org.apache.ibatis.annotations.Param;

/**
 * 扣款单Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-01
 */
public interface DeductionInfoMapper extends BaseMapperPlus<DeductionInfo, DeductionInfoVo> {

    /**
     * 根据扣款单主键id删除数据
     * @param id
     */
    void deleteDeductionInfoById(@Param("id") Long id);
}
