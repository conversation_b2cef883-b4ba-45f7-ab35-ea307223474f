package cn.xianlink.order.service.impl;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.xianlink.basic.api.RemoteBasCustomerService;
import cn.xianlink.basic.api.RemoteCityWhPlaceService;
import cn.xianlink.basic.api.RemoteCityWhService;
import cn.xianlink.basic.api.RemoteFreightLogisticsService;
import cn.xianlink.basic.api.RemoteRegionLogisticsPlanService;
import cn.xianlink.basic.api.RemoteRegionLogisticsService;
import cn.xianlink.basic.api.RemoteRegionWhService;
import cn.xianlink.basic.api.RemoteRuleFreightServiceService;
import cn.xianlink.basic.api.RemoteSupplierService;
import cn.xianlink.basic.api.RemoteWhProfitRuleService;
import cn.xianlink.basic.api.domain.bo.RemoteBasCustomerUsedPlaceAddBo;
import cn.xianlink.basic.api.domain.bo.RemoteFreightLogisticsQueryBo;
import cn.xianlink.basic.api.domain.bo.RemoteQueryDeliverBo;
import cn.xianlink.basic.api.domain.bo.RemoteRegionLogisticsPlanOrderWeightBo;
import cn.xianlink.basic.api.domain.bo.RemoteRegionLogisticsPlanQueryBo;
import cn.xianlink.basic.api.domain.bo.RemoteRegionLogisticsQueryBo;
import cn.xianlink.basic.api.domain.bo.RemoteRuleFreightServiceQueryBo;
import cn.xianlink.basic.api.domain.bo.RemoteRuleFreightServiceSkuBo;
import cn.xianlink.basic.api.domain.bo.RemoteWhProfitRuleQueryBo;
import cn.xianlink.basic.api.domain.vo.RemoteCityWhPlaceVo;
import cn.xianlink.basic.api.domain.vo.RemoteCityWhVo;
import cn.xianlink.basic.api.domain.vo.RemoteCustomerVo;
import cn.xianlink.basic.api.domain.vo.RemoteFreightLogisticsVo;
import cn.xianlink.basic.api.domain.vo.RemoteRegionLogisticsPlanVo;
import cn.xianlink.basic.api.domain.vo.RemoteRegionLogisticsVo;
import cn.xianlink.basic.api.domain.vo.RemoteRegionWhParkingVo;
import cn.xianlink.basic.api.domain.vo.RemoteRegionWhVo;
import cn.xianlink.basic.api.domain.vo.RemoteRuleFreightPriceVo;
import cn.xianlink.basic.api.domain.vo.RemoteRuleServiceAmtVo;
import cn.xianlink.basic.api.domain.vo.RemoteSupplierVo;
import cn.xianlink.basic.api.domain.vo.RemoteWhProfitRuleVo;
import cn.hutool.json.JSONUtil;
import cn.xianlink.basic.api.*;
import cn.xianlink.basic.api.domain.bo.*;
import cn.xianlink.basic.api.domain.vo.*;
import cn.xianlink.common.api.constant.BanguoCommonConstant;
import cn.xianlink.common.api.enums.basic.CustomerAfterSaleStatusEnum;
import cn.xianlink.common.api.enums.basic.PriceModeEnum;
import cn.xianlink.common.api.enums.basic.SupplierProvideInvoiceEnum;
import cn.xianlink.common.api.enums.basic.WhProfitRuleTypeEnum;
import cn.xianlink.common.api.enums.marketing.SettleStatusEnum;
import cn.xianlink.common.api.enums.order.AfterSaleStatusEnum;
import cn.xianlink.common.api.enums.order.DeliveryStatusEnum;
import cn.xianlink.common.api.enums.order.DifRefundTypeEnum;
import cn.xianlink.common.api.enums.order.ItemAfterSaleStatusEnum;
import cn.xianlink.common.api.enums.order.OrderBusinessTypeEnum;
import cn.xianlink.common.api.enums.order.OrderCancelTypeEnum;
import cn.xianlink.common.api.enums.order.OrderDeliverSourceEnum;
import cn.xianlink.common.api.enums.order.OrderStatusEnum;
import cn.xianlink.common.api.enums.order.OrderWorkStatusEnum;
import cn.xianlink.common.api.enums.order.RefundStatusEnum;
import cn.xianlink.common.api.enums.order.RefundTypeEnum;
import cn.xianlink.common.api.enums.product.CategoryAfterTypeEnum;
import cn.xianlink.common.api.enums.product.SupplierSkuBusinessTypeEnum;
import cn.xianlink.common.api.enums.product.SupplierSkuSaleTypeEnum;
import cn.xianlink.common.api.enums.product.SupplierSkuStatusEnum;
import cn.xianlink.common.api.enums.system.SysUserTypeEnum;
import cn.xianlink.common.api.enums.trade.AccountStatusEnum;
import cn.xianlink.common.api.enums.trade.PayChannelEnum;
import cn.xianlink.common.api.util.SaleDateUtil;
import cn.xianlink.common.api.util.TransactionUtil;
import cn.xianlink.common.api.vo.RemoteSupplierSkuFileVo;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.enums.StatusEnum;
import cn.xianlink.common.core.enums.YNStatusEnum;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.DateUtils;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.core.utils.SpringUtils;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.json.utils.JsonUtils;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.redis.utils.RedisUtils;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.marketing.api.RemoteCouponUserService;
import cn.xianlink.marketing.api.RemoteDiscountService;
import cn.xianlink.marketing.api.RemoteDistributionService;
import cn.xianlink.marketing.api.RemoteMkActivityDiscountSummaryService;
import cn.xianlink.marketing.api.bo.RemoteMkActivitySkuBo;
import cn.xianlink.marketing.api.bo.RemoteMkActivitySkuDiscountBo;
import cn.xianlink.marketing.api.bo.RatioBo;
import cn.xianlink.marketing.api.constant.CouponLockStatusEnum;
import cn.xianlink.marketing.api.constant.CouponTypeEnum;
import cn.xianlink.marketing.api.constant.MarketingActivityTypeEnum;
import cn.xianlink.marketing.api.dto.RemoteMkActivityRecordDTO;
import cn.xianlink.marketing.api.dto.RemoteOrderCouponMatchItemDTO;
import cn.xianlink.marketing.api.dto.RemoteOrderDiscountMatchDTO;
import cn.xianlink.marketing.api.vo.RemoteMkActivityDiscountInfoVo;
import cn.xianlink.marketing.api.vo.RemoteMkActivitySkuDiscountVo;
import cn.xianlink.marketing.api.vo.OrderCoupnSkuInfoVO;
import cn.xianlink.marketing.api.vo.RemoteCouponLockVO;
import cn.xianlink.marketing.api.vo.RemoteCouponOrderVO;
import cn.xianlink.marketing.api.vo.RemoteCouponUserInfoVO;
import cn.xianlink.marketing.api.vo.RemoteDistributionSkuVo;
import cn.xianlink.marketing.api.vo.RemoteOrderResultSkuVO;
import cn.xianlink.marketing.api.vo.RemoteOrderResultVO;
import cn.xianlink.marketing.api.vo.RemoteOrderSkuVo;
import cn.xianlink.order.api.bo.RemoteOrderCancelBo;
import cn.xianlink.order.api.bo.RemoteOrderChangeBo;
import cn.xianlink.order.api.bo.RemoteOrderQueryBo;
import cn.xianlink.order.api.bo.RemotePayUpdateStatusBo;
import cn.xianlink.order.api.bo.RemoteReplaceSupplierBo;
import cn.xianlink.order.api.bo.RemoteSupAccTransBo;
import cn.xianlink.order.api.bo.RemoteSupAccTransQueryBo;
import cn.xianlink.order.api.constant.ActivityRecordTypeEnum;
import cn.xianlink.order.api.constant.OrderSubmitErrorEnum;
import cn.xianlink.order.api.constant.RedisConstant;
import cn.xianlink.order.api.constant.StockTypeEnum;
import cn.xianlink.order.api.vo.RemoteOrderInfoVo;
import cn.xianlink.order.api.vo.RemoteOrderItemVo;
import cn.xianlink.order.api.vo.RemoteSupBillVo;
import cn.xianlink.order.api.vo.RemoteSupTransProductOrderPriceVo;
import cn.xianlink.order.api.vo.RemoteSupTransProductOrderRecordVo;
import cn.xianlink.order.api.vo.RemoteSupTransProductOrderVo;
import cn.xianlink.order.config.OrderProperties;
import cn.xianlink.order.constant.OrderCacheNames;
import cn.xianlink.order.controller.CustomToolService;
import cn.xianlink.order.domain.ActivityRecord;
import cn.xianlink.order.domain.Order;
import cn.xianlink.order.domain.OrderItem;
import cn.xianlink.order.domain.ReceiveGoodsRecord;
import cn.xianlink.order.domain.ReceiveGoodsRecordDetail;
import cn.xianlink.order.domain.RefundProductDetail;
import cn.xianlink.order.domain.ReplaceSupplierRecord;
import cn.xianlink.order.domain.SortGoodsDetail;
import cn.xianlink.order.domain.StockoutRecordDetail;
import cn.xianlink.order.domain.bo.OrderCompensateBo;
import cn.xianlink.order.domain.bo.OrderSettkeInfBO;
import cn.xianlink.order.domain.bo.pay.OrderPaySignBo;
import cn.xianlink.order.domain.bo.refundRecord.OrderRefundDTO;
import cn.xianlink.order.domain.dto.ConfirmOrderDTO;
import cn.xianlink.order.domain.dto.CustomerOrderDTO;
import cn.xianlink.order.domain.dto.OrderFixPriceDTO;
import cn.xianlink.order.domain.dto.OrderFixPriceItemDTO;
import cn.xianlink.order.domain.dto.SubmitBo;
import cn.xianlink.order.domain.order.bo.AddOrderBo;
import cn.xianlink.order.domain.order.bo.OrderSearchBo;
import cn.xianlink.order.domain.order.bo.QueryAdminPageBo;
import cn.xianlink.order.domain.order.bo.QueryDeliverBo;
import cn.xianlink.order.domain.order.bo.QueryDistributionBo;
import cn.xianlink.order.domain.order.bo.QueryOrderBo;
import cn.xianlink.order.domain.order.bo.QueryOrderPageBo;
import cn.xianlink.order.domain.order.bo.QueryOrderPageDeliveryBo;
import cn.xianlink.order.domain.order.bo.RefundOrderDifferenceBo;
import cn.xianlink.order.domain.order.bo.SkuBo;
import cn.xianlink.order.domain.order.bo.UpdateOrderBo;
import cn.xianlink.order.domain.order.vo.*;
import cn.xianlink.order.domain.vo.CartItemVo;
import cn.xianlink.order.domain.vo.order.AdminOrderInfoVo;
import cn.xianlink.order.domain.vo.order.CityOrderWeightVO;
import cn.xianlink.order.domain.vo.order.OrderSkuCustomerVO;
import cn.xianlink.order.domain.vo.order.OrderStatusVo;
import cn.xianlink.order.domain.vo.receiveGoods.ReceiveGoodsDetailVO;
import cn.xianlink.order.domain.vo.receiveGoods.ReceiveGoodsVO;
import cn.xianlink.order.domain.vo.refundRecord.RefundRecordVO;
import cn.xianlink.order.domain.vo.report.ReportLossItemInfoVo;
import cn.xianlink.order.enums.OrderChannelEnum;
import cn.xianlink.order.enums.OrderProdTypeEnum;
import cn.xianlink.order.enums.ResponsibilityTypeEnum;
import cn.xianlink.order.mapper.ActivityRecordMapper;
import cn.xianlink.order.mapper.OrderItemMapper;
import cn.xianlink.order.mapper.OrderMapper;
import cn.xianlink.order.mapper.ReceiveGoodsRecordDetailMapper;
import cn.xianlink.order.mapper.ReceiveGoodsRecordMapper;
import cn.xianlink.order.mapper.RefundRecordMapper;
import cn.xianlink.order.mapper.ReplaceSupplierRecordMapper;
import cn.xianlink.order.mapper.SortGoodsDetailMapper;
import cn.xianlink.order.mapper.StockoutRecordDetailMapper;
import cn.xianlink.order.mq.producer.OrderCancelProducer;
import cn.xianlink.order.mq.producer.OrderChangeProducer;
import cn.xianlink.order.mq.producer.OrderCompensateProducer;
import cn.xianlink.order.mq.producer.OrderDeliveryNumberProducer;
import cn.xianlink.order.mq.producer.OrderMqProducer;
import cn.xianlink.order.service.ICartItemService;
import cn.xianlink.order.service.ICustomerStatisticsService;
import cn.xianlink.order.service.IOrderPayService;
import cn.xianlink.order.service.IOrderService;
import cn.xianlink.order.service.IReceiveGoodsService;
import cn.xianlink.order.service.IRefundRecordService;
import cn.xianlink.order.service.IReportLossService;
import cn.xianlink.order.util.CustomNoUtil;
import cn.xianlink.order.util.DeliveryNumberUtil;
import cn.xianlink.product.api.RemoteBuyerService;
import cn.xianlink.product.api.RemoteCategoryService;
import cn.xianlink.product.api.RemoteSkuDisableCityWhService;
import cn.xianlink.product.api.RemoteSkuService;
import cn.xianlink.product.api.RemoteSupplierSkuService;
import cn.xianlink.product.api.constant.SkuStockErrorEnum;
import cn.xianlink.product.api.domain.bo.RemoteQueryInfoListBo;
import cn.xianlink.product.api.domain.bo.RemoteQuerySupplierSkuFileBo;
import cn.xianlink.product.api.domain.bo.RemoteUpdateStockBo;
import cn.xianlink.product.api.domain.vo.*;
import cn.xianlink.system.api.*;
import cn.xianlink.product.api.domain.vo.RemoteCategoryVO;
import cn.xianlink.product.api.domain.vo.RemoteRefundDifferenceSkuVo;
import cn.xianlink.product.api.domain.vo.RemoteSkuVo;
import cn.xianlink.product.api.domain.vo.RemoteSupplierSkuInfoVo;
import cn.xianlink.product.api.domain.vo.RemoteSupplierSkuStockVo;
import cn.xianlink.system.api.RemoteConfigService;
import cn.xianlink.system.api.RemoteDeptService;
import cn.xianlink.system.api.RemoteDictService;
import cn.xianlink.system.api.RemoteUserService;
import cn.xianlink.system.api.domain.bo.RemoteUserBo;
import cn.xianlink.system.api.domain.vo.RemoteImGroupVo;
import cn.xianlink.system.api.domain.vo.RemoteSysConfigVo;
import cn.xianlink.system.api.domain.vo.RemoteSysDictVo;
import cn.xianlink.system.api.model.LoginUser;
import cn.xianlink.trade.api.RemotePaymentService;
import cn.xianlink.trade.api.RemoteTradeAccTransService;
import cn.xianlink.trade.api.RemoteTransferService;
import cn.xianlink.trade.api.domain.bo.RemoteTransferChangeBo;
import cn.xianlink.trade.api.domain.bo.RetailInfoBo;
import cn.xianlink.trade.api.domain.vo.RemoteTradeAccTransVo;
import cn.xianlink.trade.api.domain.vo.RetailInfoVo;
import cn.xianlink.trade.api.enums.RetailInfEnums;
import com.alibaba.fastjson.JSON;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import io.seata.spring.annotation.GlobalTransactional;
import jakarta.validation.constraints.NotNull;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 订单总Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-25
 */
@RequiredArgsConstructor
@Service
@CustomLog
public class OrderServiceImpl extends ServiceImpl<OrderMapper, Order> implements IOrderService {

    private final OrderMapper orderMapper;

    private final OrderItemMapper orderItemMapper;

    private final ReplaceSupplierRecordMapper replaceSupplierRecordMapper;

    private final ICustomerStatisticsService customerStatisticsService;

    private IOrderPayService orderPayService;

    @Autowired
    public void setIOrderPayService(@Lazy IOrderPayService iOrderPayService) {
        this.orderPayService = iOrderPayService;
    }

    private final IReportLossService iReportLossService;

    private final ICartItemService iCartItemService;

    private final ReceiveGoodsRecordDetailMapper receiveGoodsDetailMapper;

    private final OrderCancelProducer orderCancelProducer;

    private final OrderMqProducer orderMqProducer;

    private final OrderChangeProducer orderChangeProducer;

    private final OrderCompensateProducer orderCompensateProducer;

    private final IRefundRecordService iRefundRecordService;

    private final IReceiveGoodsService iReceiveGoodsService;

    private final RefundRecordMapper refundRecordMapper;

    private final IReportLossService reportLossService;

    private final SortGoodsDetailMapper sortGoodsDetailMapper;

    private final CustomToolService customToolService;

    private final ActivityRecordMapper activityRecordMapper;

    private final OrderProperties orderProperties;

    private final StockoutRecordDetailMapper stockoutRecordDetailMapper;

    private final ReceiveGoodsRecordMapper receiveGoodsRecordMapper;

    @DubboReference
    private RemoteSupplierSkuService remoteSupplierSkuService;

    @DubboReference
    private RemoteBasCustomerService remoteBasCustomerService;

    @DubboReference
    private RemoteRegionLogisticsService remoteRegionLogisticsService;

    @DubboReference
    private RemoteRegionLogisticsPlanService remoteRegionLogisticsPlanService;

    @DubboReference
    private RemoteRegionWhService remoteRegionWhService;

    @DubboReference
    private RemoteCityWhPlaceService remoteCityWhPlaceService;

    @DubboReference
    private RemoteTransferService remoteTransferService;

    @DubboReference
    private RemoteCityWhService remoteCityWhService;

    @DubboReference
    private RemoteSupplierService remoteSupplierService;

    @DubboReference
    private RemoteDictService remoteDictService;

    @DubboReference
    private RemoteUserService remoteUserService;

    @DubboReference
    private RemoteConfigService remoteConfigService;

    @DubboReference
    private RemoteDeptService remoteDeptService;

    @DubboReference
    private RemoteFreightLogisticsService remoteFreightLogisticsService;

    @DubboReference
    private RemoteCategoryService remoteCategoryService;

    @DubboReference
    private RemoteBuyerService remoteBuyerService;

    @DubboReference
    private RemoteRuleFreightServiceService remoteRuleFreightServiceService;

    @DubboReference
    private RemotePaymentService remotePaymentService;

    @DubboReference
    private RemoteTradeAccTransService remoteTradeAccTransService;

    @DubboReference
    private final RemoteDiscountService discountService;
    @DubboReference
    private final RemoteCouponUserService couponUserService;

    @DubboReference
    private final RemoteDistributionService distributionService;

    @DubboReference
    private RemoteSkuDisableCityWhService remoteSkuDisableCityWhService;

    @DubboReference
    private RemoteSkuService remoteSkuService;

    private final OrderDeliveryNumberProducer orderDeliveryNumberProducer;

    @DubboReference
    private RemoteWhProfitRuleService remoteWhProfitRuleService;

    @DubboReference
    private RemoteImGroupService remoteImGroupService;

    @DubboReference
    private RemoteMkActivityDiscountSummaryService remoteMkActivityDiscountSummaryService;

    @Override
    public BigDecimal getPlatformServiceStandard() {
        return orderProperties.getPlatformServiceStandard();
    }
    // 是否命中限时折扣标识
    private final static Integer HAS_LIMIT_DISCOUNT = 1;

    /**
     * 获取优惠补贴金额
     *
     * @param regionWhId
     * @return
     */
    @Override
    public BigDecimal getSubsidyFreeAmount(Long regionWhId) {
        if (regionWhId != null && regionWhId.equals(getSubsidyRegionWhId())) {
            RemoteSysConfigVo configVo = remoteConfigService.selectConfigByKey(orderProperties.getSubsidyKey());
            if (configVo != null && StatusEnum.ENABLE.getCode().equals(configVo.getStatus())) {
                if (configVo.getConfigValue() != null) {
                    try {
                        return new BigDecimal(configVo.getConfigValue());
                    } catch (Exception e) {
                        log.error("补贴金额设置有误");
                    }
                }
            }
        }
        return BigDecimal.ZERO;
    }

    /**
     * 获取优惠补贴金额
     */
    private BigDecimal getSubsidyFreeAmount() {
        RemoteSysConfigVo configVo = remoteConfigService.selectConfigByKey(orderProperties.getSubsidyKey());
        if (configVo != null && StatusEnum.ENABLE.getCode().equals(configVo.getStatus())) {
            if (configVo.getConfigValue() != null) {
                try {
                    return new BigDecimal(configVo.getConfigValue());
                } catch (Exception e) {
                    log.error("补贴金额设置有误");
                }
            }
        }
        return BigDecimal.ZERO;
    }

    /**
     * 获取优惠补贴,配货总仓id
     *
     * @return
     */
    @Override
    public Long getSubsidyRegionWhId() {
        if (StringUtils.isNotBlank(orderProperties.getSubsidyRegionWhId())) {
            try {
                return Long.valueOf(orderProperties.getSubsidyRegionWhId());
            } catch (Exception e) {
                log.error("优惠补贴总仓id设置有误");
            }
        }
        return -1L;
    }

    /**
     * 检查是否可以加购物车，是否可以下单
     *
     * @return
     */
    @Override
    public void checkIfSale(String saleTimeStartStr, String saleTimeEndStr) {
        if (saleTimeStartStr.equals("24:00")) {
            saleTimeStartStr = "23:59:59.999999";
        }
        if (saleTimeEndStr.equals("24:00")) {
            saleTimeEndStr = "23:59:59.999999";
        }
        LocalTime saleTimeStart = LocalTime.parse(saleTimeStartStr);
        LocalTime saleTimeEnd = LocalTime.parse(saleTimeEndStr);
        LocalTime now = LocalTime.now();
        // 处理跨午夜的情况（结束时间大于开始时间表示跨天）
        if (saleTimeStart.isBefore(saleTimeEnd)) {//这个条件已经要放到前面，如果设置两个时间一样，那就随便什么时候都不能下单了
            // 跨午夜范围：从结束时间到午夜 + 从午夜到开始时间  不能下单
            if (now.isBefore(saleTimeStart) || now.isAfter(saleTimeEnd)) {
                throw new ServiceException("未到销售时间，请在" + saleTimeStartStr + "开始下单");
            }
        } else {
            // 普通范围：在同一天内
            if (now.isBefore(saleTimeStart) && now.isAfter(saleTimeEnd)) {
                throw new ServiceException("未到销售时间，请在" + saleTimeStartStr + "开始下单");
            }
        }
    }

    /**
     * 查询订单详情
     */
    @Override
    public GetInfoVo queryById(Long id) {
        OrderVo orderVo = orderMapper.selectVoById(id);
        if (orderVo == null) {
            throw new ServiceException("订单不存在");
        }
        GetInfoVo vo = MapstructUtils.convert(orderVo, GetInfoVo.class);
        if (OrderStatusEnum.WAIT.getCode().equals(vo.getStatus())) {
            long residuePayMillisecond = getResiduePayMillisecond(orderVo.getPayChannel(), orderVo.getCreateTime());
            if (residuePayMillisecond > 0) {
                vo.setResiduePayMillisecond(residuePayMillisecond);
            }
        } else if (OrderStatusEnum.ALREADY.getCode().equals(vo.getStatus())) {
            long cancelMillisecond = orderProperties.getCancelTime() - (System.currentTimeMillis() - vo.getPayTime().getTime());
            if (cancelMillisecond > 0) {
                vo.setCancelMillisecond(cancelMillisecond);
            }
        }
        List<QueryItemListVo> itemListVoList = orderItemMapper.queryItemList(Lists.newArrayList(vo.getId()));
        if (itemListVoList == null || itemListVoList.size() == 0) {
            throw new ServiceException("订单项不存在");
        }
        //商品类型降序
        itemListVoList.sort((a, b) -> b.getProdType().compareTo(a.getProdType()));
        vo.setWorkStatus(itemListVoList.get(0).getWorkStatus());
        List<Long> orderItemIds = itemListVoList.stream().map(QueryItemListVo::getId).toList();
        //获取用户信息
        RemoteCustomerVo remoteCustomerVo = remoteBasCustomerService.getById(orderVo.getCustomerId());
        Map<Long, Integer> afterSaleStatusMap = new HashMap<>();
        if (remoteCustomerVo != null) {
            vo.setCustomerName(remoteCustomerVo.getName());
            vo.setCustomerAlias(remoteCustomerVo.getAlias());
            RemoteUserBo userInfo = remoteUserService.getUserByUserCode(remoteCustomerVo.getUserCode());
            if (userInfo != null) {
                vo.setCustomerPhone(userInfo.getPhoneNo());
                vo.setCustomerPhoneSensitive(userInfo.getPhoneNo());
            }
            if (CustomerAfterSaleStatusEnum.OPEN.getCode().equals(remoteCustomerVo.getAfterSaleStatus())
                    || (orderVo.getPayTime() != null && remoteCustomerVo.getUpdateAfterSaleTime().after(orderVo.getPayTime()))) {
                ReceiveGoodsVO afterSaleStatus = iReceiveGoodsService.getAfterSaleStatus(orderItemIds);
                if (ObjectUtil.isNotNull(afterSaleStatus)) {
                    vo.setAfterSaleTimeSeconds(afterSaleStatus.getAfterSaleTimeSeconds());
                    if (afterSaleStatus.getGoodsDetailList().size() > 0) {
                        afterSaleStatusMap = afterSaleStatus.getGoodsDetailList().stream().collect(Collectors.toMap(ReceiveGoodsDetailVO::getOrderItemId, ReceiveGoodsDetailVO::getAfterSaleStatus, (V1, V2) -> V1));
                    }
                }
            }
        }
        List<RefundProductDetail> refundProductDetailList = iRefundRecordService.getRefundByItemIdList(orderItemIds);
        Map<Long, List<RefundProductDetail>> refundProductDetailMap = new HashMap<>();
        if (refundProductDetailList != null && refundProductDetailList.size() > 0) {
            refundProductDetailMap = refundProductDetailList.stream().collect(Collectors.groupingBy(RefundProductDetail::getOrderItemId));
        }
        List<Long> supplierIdList = itemListVoList.stream().map(QueryItemListVo::getSupplierId).distinct().toList();
        List<RemoteSupplierVo> supplierVoList = remoteSupplierService.getSupplierByIds(supplierIdList);
        Map<Long, RemoteSupplierVo> supplierVoMap = new HashMap<>();
        if (supplierVoList != null && supplierVoList.size() > 0) {
            supplierVoMap = supplierVoList.stream().collect(Collectors.toMap(RemoteSupplierVo::getId, Function.identity()));
        }
        //sku信息
        List<Long> skuIdList = itemListVoList.stream().map(QueryItemListVo::getSkuId).toList();
        List<RemoteSkuVo> remoteSkuVos = remoteSkuService.getByIds(skuIdList);
        Map<Long, RemoteSkuVo> remoteSkuVoMap = remoteSkuVos.stream().collect(Collectors.toMap(RemoteSkuVo::getId, Function.identity(), (o1, o2) -> o1));
        // 档口信息
        List<Long> supplierDeptIdList = itemListVoList.stream().map(QueryItemListVo::getSupplierDeptId).filter(Objects::nonNull).filter(v -> v > 0L).distinct().collect(Collectors.toList());
        Map<Long, String> deptNameMap = getSupplierMap(supplierDeptIdList);
        RemoteQueryInfoListBo skuBo = new RemoteQueryInfoListBo();
        List<Long> skuSupplierIdList = itemListVoList.stream().map(QueryItemListVo::getSupplierSkuId).toList();
        skuBo.setSupplierSkuIdList(skuSupplierIdList);
        List<RemoteSupplierSkuInfoVo> skuList = remoteSupplierSkuService.queryInfoList(skuBo);
        Map<Long, RemoteSupplierSkuInfoVo> skuVoMap = new HashMap<>();
        if (skuList != null && skuList.size() > 0) {
            skuVoMap = skuList.stream().collect(Collectors.toMap(RemoteSupplierSkuInfoVo::getId, Function.identity()));
        }

        Map<Long, ActivityRecord> activityRecordMap = new HashMap<>();
        Map<Long, RemoteCouponUserInfoVO> couponUserInfoVOMap = new HashMap<>();
        // 订单详情跳转，新增限时折扣
        List<ActivityRecord> activityRecords = activityRecordMapper.selectList(new LambdaQueryWrapper<ActivityRecord>()
                .eq(ActivityRecord::getOrderId, id)
                .in(ActivityRecord::getType, Arrays.asList(ActivityRecordTypeEnum.COUPON.getCode(), ActivityRecordTypeEnum.TIME_DISCOUNT.getCode())));
        // 处理订单优惠券记录
        if (CollectionUtil.isNotEmpty(activityRecords)) {
            List<ActivityRecord> couponActivityRecords = activityRecords.stream().filter(activityRecord -> ActivityRecordTypeEnum.COUPON.getCode().equals(activityRecord.getType())).toList();
            if (CollectionUtil.isNotEmpty(couponActivityRecords)) {
                activityRecordMap = couponActivityRecords.stream().collect(Collectors.toMap(ActivityRecord::getOrderItemId, Function.identity(), (key1, key2) -> key2));
                List<Long> couponUserIds = couponActivityRecords.stream().map(ActivityRecord::getActivityId).collect(Collectors.toList());
                List<RemoteCouponUserInfoVO> couponUserInfoVOS = couponUserService.getCouponUser(couponUserIds);
                couponUserInfoVOMap = couponUserInfoVOS.stream()
                        .collect(Collectors.toMap(RemoteCouponUserInfoVO::getCouponUserId, Function.identity(), (key1, key2) -> key2));
            }
            // 设置是否限时折扣
            vo.setHasLimitDiscount(activityRecords.stream().filter(activityRecord -> ActivityRecordTypeEnum.TIME_DISCOUNT.getCode().equals(activityRecord.getType())).findAny().isPresent() ? HAS_LIMIT_DISCOUNT : BigDecimal.ZERO.intValue());
        }

        for (QueryItemListVo itemListVo : itemListVoList) {

            RemoteSupplierSkuInfoVo skuInfoVo = skuVoMap.get(itemListVo.getSupplierSkuId());
            if (skuInfoVo != null) {
                itemListVo.setSpuGrade(skuInfoVo.getSpuGrade());
                itemListVo.setSpuStandards(skuInfoVo.getSpuStandards());
                itemListVo.setProducer(skuInfoVo.getProducer());
                itemListVo.setBrand(skuInfoVo.getBrand());
                itemListVo.setAreaCode(skuInfoVo.getAreaCode());
                itemListVo.setShortProducer(skuInfoVo.getShortProducer());
                itemListVo.setAfterSaleType(skuInfoVo.getAfterSaleType());
                itemListVo.setShouguangVegetables(skuInfoVo.getShouguangVegetables());
                itemListVo.setSaleType(skuInfoVo.getSaleType());
            }
            RemoteSupplierVo supplierVo = supplierVoMap.get(itemListVo.getSupplierId());
            if (supplierVo != null) {
                itemListVo.setSupplierSimpleCode(supplierVo.getSimpleCode());
                itemListVo.setSupplierName(supplierVo.getName());
            }
            //获取关联skuId
            RemoteSkuVo remoteSkuVo = remoteSkuVoMap.get(itemListVo.getSkuId());
            if (ObjectUtil.isNotEmpty(remoteSkuVo)) {
                itemListVo.setRelationSkuId(remoteSkuVo.getRelationSkuId());
            }
            // 档口名称
            itemListVo.setSupplierDeptName(deptNameMap.get(itemListVo.getSupplierDeptId()));

            //计算售后状态
            if (afterSaleStatusMap.containsKey(itemListVo.getId())) {
                itemListVo.setAfterSaleStatus(afterSaleStatusMap.get(itemListVo.getId()));
            }
            List<RefundProductDetail> refundList = refundProductDetailMap.get(itemListVo.getId());
            BigDecimal lossAmount = itemListVo.getProductAmount().subtract(itemListVo.getSubsidyFreeAmount());
            if (refundList != null && refundList.size() > 0) {
                for (RefundProductDetail refundProductDetail : refundList) {
                    lossAmount = lossAmount.subtract(refundProductDetail.getRefundProductAmount());
                }
            }
            if (lossAmount.compareTo(BigDecimal.ZERO) <= 0 && AfterSaleStatusEnum.CAN_AFTER_SALE.getCode().equals(itemListVo.getAfterSaleStatus())) {
                itemListVo.setAfterSaleStatus(AfterSaleStatusEnum.NOT_AFTER_SALE.getCode());
            }

            //计算毛重单价
            BigDecimal grossWeightPrice = itemListVo.getFinalPrice().divide(itemListVo.getSpuGrossWeight(), 2, RoundingMode.HALF_UP);
            if (grossWeightPrice.compareTo(BigDecimal.ZERO) == 0) {
                grossWeightPrice = new BigDecimal("0.01");
            }
            itemListVo.setGrossWeightPrice(grossWeightPrice);
            //计算净重单价
            BigDecimal netWeightPrice = itemListVo.getFinalPrice().divide(itemListVo.getSpuNetWeight(), 2, RoundingMode.HALF_UP);
            if (netWeightPrice.compareTo(BigDecimal.ZERO) == 0) {
                netWeightPrice = new BigDecimal("0.01");
            }
            // todo Liyong：临时调整
            itemListVo.setWeightPrice(netWeightPrice);
            itemListVo.setNetWeightPrice(netWeightPrice);
            if (OrderStatusEnum.CANCEL.getCode().equals(itemListVo.getStatus())
                    || OrderStatusEnum.WAIT.getCode().equals(itemListVo.getStatus())) {
                itemListVo.setShowStatusName(OrderStatusEnum.getDescByCode(itemListVo.getStatus()));
            } else {
                itemListVo.setShowStatusName(OrderWorkStatusEnum.getDescByCode(itemListVo.getWorkStatus()));
            }
            if (itemListVo.getPrice().compareTo(itemListVo.getFinalPrice()) == 0) {
                itemListVo.setFinalPrice(null);
            }
            ActivityRecord activityRecord = activityRecordMap.get(itemListVo.getId());
            if (Objects.nonNull(activityRecord)) {
                RemoteCouponUserInfoVO couponUserInfoVO = couponUserInfoVOMap.getOrDefault(activityRecord.getActivityId(), new RemoteCouponUserInfoVO());
                if (Objects.equals(couponUserInfoVO.getIsHidden(), 1)) {
                    //如果是隐藏的优惠券，直接减去优惠金额，重置单价
                    BigDecimal productFreeAmount = activityRecord.getProductFreeAmount();
                    itemListVo.setPrice(itemListVo.getPrice().subtract(productFreeAmount.divide(BigDecimal.valueOf(itemListVo.getCount()), 2, RoundingMode.HALF_UP)));
                    vo.setProductFreeAmount(vo.getProductFreeAmount().subtract(productFreeAmount));
                    vo.setFreeTotalAmount(vo.getFreeTotalAmount().subtract(productFreeAmount));
                    vo.setProductAmount(vo.getProductAmount().subtract(productFreeAmount));
                }
            }
            //实付单价 = 实付金额/件数
            itemListVo.setPriceFree((itemListVo.getProductAmount().subtract(itemListVo.getProductFreeAmount()))
                    .divide(new BigDecimal(itemListVo.getCount()), 2, RoundingMode.HALF_UP));
        }
        vo.setOrderItemList(itemListVoList);
        //检测总仓
        RemoteRegionWhVo regionWhVo = remoteRegionWhService.queryById(vo.getRegionWhId());
        if (regionWhVo == null) {
            throw new ServiceException("总仓不存在");
        }
        vo.setDeliverName(regionWhVo.getRegionWhName());
        // 加载并检查提货点
        loadPlaceInfo(vo);
        //获取退款信息
        GetInfoVo refundAmountById = iRefundRecordService.getRefundAmountById(id);
        if (ObjectUtil.isNotNull(refundAmountById)) {
            vo.setDifferenceRefundAmount(refundAmountById.getDifferenceRefundAmount());
            vo.setLossAmount(refundAmountById.getLossAmount());
            vo.setRefundAmount(refundAmountById.getRefundAmount());
        }
        vo.setUnsettledAmount(vo.getTotalAmount());
        if (vo.getDifferenceRefundAmount() != null) {
            vo.setUnsettledAmount(vo.getUnsettledAmount().subtract(vo.getDifferenceRefundAmount()));
        }
        if (vo.getLossAmount() != null) {
            vo.setUnsettledAmount(vo.getUnsettledAmount().subtract(vo.getLossAmount()));
        }
        if (vo.getRefundAmount() != null) {
            vo.setUnsettledAmount(vo.getUnsettledAmount().subtract(vo.getRefundAmount()));
        }
        return vo;
    }

    private void loadPlaceInfo(GetInfoVo vo) {
        Long placeId = Objects.equals(0L, vo.getPlaceIdLevel2()) || Objects.isNull(vo.getPlaceIdLevel2()) ? vo.getPlaceId() : vo.getPlaceIdLevel2();
        RemoteCityWhPlaceVo placeVo = remoteCityWhPlaceService.queryById(placeId);
        vo.setAddress(placeVo.getAddress());
        vo.setPlaceName(placeVo.getPlaceName());
        vo.setLat(placeVo.getLat());
        vo.setLng(placeVo.getLng());
        vo.setPositionTitle(placeVo.getPositionTitle());
        vo.setContactName(placeVo.getContactName());
        vo.setContactPhone(placeVo.getContactPhone());
    }

    @Override
    public GetInfoVo queryByCode(String code) {
        LambdaQueryWrapper<Order> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Order::getCode, code);
        wrapper.eq(Order::getDelFlag, 0);
        Order order = orderMapper.selectOne(wrapper);
        if (order == null) {
            throw new ServiceException("订单不存在");
        }
        GetInfoVo vo = BeanUtil.toBean(order, GetInfoVo.class);
        if (OrderStatusEnum.WAIT.getCode().equals(vo.getStatus())) {
            long residuePayMillisecond = getResiduePayMillisecond(order.getPayChannel(), order.getCreateTime());
            if (residuePayMillisecond > 0) {
                vo.setResiduePayMillisecond(residuePayMillisecond);
            }
        } else if (OrderStatusEnum.ALREADY.getCode().equals(vo.getStatus())) {
            long cancelMillisecond = orderProperties.getCancelTime() - (System.currentTimeMillis() - vo.getPayTime().getTime());
            if (cancelMillisecond > 0) {
                vo.setCancelMillisecond(cancelMillisecond);
            }
        }
        vo.setOrderItemList(orderItemMapper.queryItemList(Lists.newArrayList(vo.getId())));
        //现在的基采物流都没有提货点信息，不知道后面怎么设计，先直接用总仓名称当做发货，提货点名称当做提货
        RemoteRegionWhVo regionWhVo = remoteRegionWhService.queryById(vo.getRegionWhId());
        if (regionWhVo == null) {
            throw new ServiceException("总仓不存在");
        }
        vo.setDeliverName(regionWhVo.getRegionWhName());
        // 加载并检查提货点
        loadPlaceInfo(vo);
        return vo;
    }

    /**
     * 根据订单号查询订单ID
     */
    @Override
   public Long getIdByCode(String code){
        LambdaQueryWrapper<Order> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Order::getCode, code);
        wrapper.eq(Order::getDelFlag, 0);
        wrapper.select(Order::getId);
        Order orderInfo = orderMapper.selectOne(wrapper);
        if(orderInfo == null) {
            return null;
        }
        return orderInfo.getId();
    }

    /**
     * 查询订单列表
     */
    @Override
    public TableDataInfo<QueryPageVo> queryPage(QueryOrderPageBo bo) {
        //未指定客户的情况下才能模糊匹配客户
        if (bo.getCustomerId() == null && StringUtils.isNotBlank(bo.getCustomerName())) {
            List<Long> customerIdList = remoteBasCustomerService.getByName(bo.getCustomerName());
            if (ObjectUtil.isEmpty(customerIdList)) {
                return new TableDataInfo<>();
            }
            bo.setCustomerIdList(customerIdList);
        }
        if (bo.getCustomerId() != null) {
            //查询指定客户
            if (bo.getCustomerIdList() == null) {
                bo.setCustomerIdList(new ArrayList<>());
            }
            bo.getCustomerIdList().add(bo.getCustomerId());
        }

        TableDataInfo<QueryPageVo> page = TableDataInfo.build(orderMapper.queryPage(bo, bo.build()));
        List<QueryPageVo> list = page.getRows();
        if (list != null && list.size() > 0) {
            //获取用户信息
            List<Long> customerList = list.stream().map(QueryPageVo::getCustomerId).distinct().toList();
            List<RemoteCustomerVo> customerVoList = remoteBasCustomerService.getByIds(customerList);
            if (customerVoList == null || customerVoList.size() == 0) {
                throw new ServiceException("订单用户不存在");
            }
            Map<Long, RemoteCustomerVo> customerVoMap = customerVoList.stream().collect(Collectors.toMap(RemoteCustomerVo::getId, Function.identity(), (key1, key2) -> key2));
            //获取订单项信息
            List<Long> orderIdList = list.stream().map(QueryPageVo::getId).toList();
            List<QueryItemListVo> itemList = orderItemMapper.queryItemList(orderIdList);
            //获取供应商信息进行过滤订单
            if(CollectionUtil.isNotEmpty(itemList) && CollectionUtil.isNotEmpty(bo.getSupplierIdList())){
                itemList = itemList.stream().filter(x-> bo.getSupplierIdList().contains(x.getSupplierId())).toList();
            }
            if (CollectionUtil.isNotEmpty(itemList)) {
                List<Long> supplierSkuIds = itemList.stream().map(QueryItemListVo::getSupplierSkuId).distinct().toList();
                List<RemoteSupplierSkuInfoVo> supplierSkuInfoVos = remoteSupplierSkuService.querySimpleInfoList(supplierSkuIds);
                Map<Long, Integer> map = Optional.ofNullable(supplierSkuInfoVos).orElse(Collections.emptyList()).stream().collect(Collectors.toMap(RemoteSupplierSkuInfoVo::getId, RemoteSupplierSkuInfoVo::getSaleType, (key1, key2) -> key2));
                itemList.forEach(e -> e.setSaleType(map.get(e.getSupplierSkuId())));
            }
            // 限时折扣活动调整，新增订单是否命中限时折扣活动标识
            List<ActivityRecord> activityRecords = activityRecordMapper.selectList(new LambdaQueryWrapper<ActivityRecord>()
                    .in(ActivityRecord::getOrderId, orderIdList)
                    .in(ActivityRecord::getType,Arrays.asList(ActivityRecordTypeEnum.COUPON.getCode(), ActivityRecordTypeEnum.TIME_DISCOUNT.getCode())));
            Map<Long, ActivityRecord> activityRecordMap = new HashMap<>();
            Map<Long, RemoteCouponUserInfoVO> couponUserInfoVOMap = new HashMap<>();
            // 假设 orders 是所有订单的集合，初始化一个默认值为 false 的 Map
            Map<Long, Integer> orderMacthDiscountInfoMap = list.stream().collect(Collectors.toMap(QueryPageVo::getId, order -> BigDecimal.ZERO.intValue(), (key1, key2) -> key2));
            if (CollectionUtil.isNotEmpty(activityRecords)) {
                // 处理订单优惠券记录
                List<ActivityRecord> couponActivityRecords = activityRecords.stream().filter(activityRecord -> ActivityRecordTypeEnum.COUPON.getCode().equals(activityRecord.getType())).toList();
                if (CollectionUtil.isNotEmpty(couponActivityRecords)) {
                    activityRecordMap = couponActivityRecords.stream().collect(Collectors.toMap(ActivityRecord::getOrderItemId, Function.identity(), (key1, key2) -> key2));
                    List<Long> couponUserIds = couponActivityRecords.stream().map(ActivityRecord::getActivityId).collect(Collectors.toList());
                    List<RemoteCouponUserInfoVO> couponUserInfoVOS = couponUserService.getCouponUser(couponUserIds);
                    couponUserInfoVOMap = couponUserInfoVOS.stream()
                            .collect(Collectors.toMap(RemoteCouponUserInfoVO::getCouponUserId, Function.identity(), (key1, key2) -> key2));
                }
                // 命中限时折扣的订单进行打标
                activityRecords.stream().filter(activityRecord -> ActivityRecordTypeEnum.TIME_DISCOUNT.getCode().equals(activityRecord.getType())).forEach(activityRecord -> orderMacthDiscountInfoMap.put(activityRecord.getOrderId(), HAS_LIMIT_DISCOUNT));
            }

            // 分组并排序
            Map<Long, List<QueryItemListVo>> itemMap = itemList.stream().collect(Collectors.groupingBy(QueryItemListVo::getOrderId,
                    Collectors.collectingAndThen(Collectors.toList(), list1 -> {
                        list1.sort((a, b) -> b.getProdType().compareTo(a.getProdType()));
                        return list1;
                    })));
            List<Long> regionWhIds = list.stream().map(QueryPageVo::getRegionWhId).collect(Collectors.toList());
            List<RemoteRegionWhVo> remoteRegionWhVos = remoteRegionWhService.selectRegionWhInfoByIds(regionWhIds);
            Map<Long, RemoteRegionWhVo> regionWhNameMap = remoteRegionWhVos.stream().collect(Collectors.toMap(RemoteRegionWhVo::getId, item -> item));

            for (QueryPageVo vo : list) {
                vo.setCustomerName(customerVoMap.get(vo.getCustomerId()).getName());
                vo.setCustomerAlias(customerVoMap.get(vo.getCustomerId()).getAlias());
                if (OrderStatusEnum.WAIT.getCode().equals(vo.getStatus())) {
                    long residuePayMillisecond = getResiduePayMillisecond(vo.getPayChannel(), vo.getCreateTime());
                    if (residuePayMillisecond > 0) {
                        vo.setResiduePayMillisecond(residuePayMillisecond);
                    }
                    //是否是截单前5分钟的订单
                    LocalTime localTime = vo.getCreateTime().toInstant()
                            .atZone(ZoneId.systemDefault())  // 转换为ZonedDateTime
                            .toLocalTime();
                    Boolean orderCancelTime = getOrderCancelTime(localTime, regionWhNameMap.get(vo.getRegionWhId()).getClearWhTime());
                    if(orderCancelTime) {
                        vo.setResiduePayMillisecond( orderProperties.getResidueCancelTime() - (System.currentTimeMillis() - vo.getCreateTime().getTime()));
                    }
                } else if (OrderStatusEnum.ALREADY.getCode().equals(vo.getStatus()) && vo.getPayTime() != null) {
                    long cancelMillisecond = orderProperties.getCancelTime() - (System.currentTimeMillis() - vo.getPayTime().getTime());
                    if (cancelMillisecond > 0) {
                        vo.setCancelMillisecond(cancelMillisecond);
                    }
                }
                // 是否命中限时折扣
                vo.setHasLimitDiscount(orderMacthDiscountInfoMap.get(vo.getId()));
                List<QueryItemListVo> itemListVoList = itemMap.get(vo.getId());
                if (itemListVoList != null && itemListVoList.size() > 0) {
                    vo.setWorkStatus(itemListVoList.get(0).getWorkStatus());
                    for (QueryItemListVo itemListVo : itemListVoList) {
                        ActivityRecord activityRecord = activityRecordMap.get(itemListVo.getId());
                        if (Objects.nonNull(activityRecord)) {
                            RemoteCouponUserInfoVO couponUserInfoVO = couponUserInfoVOMap.getOrDefault(activityRecord.getActivityId(), new RemoteCouponUserInfoVO());
                            if (Objects.equals(couponUserInfoVO.getIsHidden(), 1)) {
                                BigDecimal productFreeAmount = activityRecord.getProductFreeAmount();
                                vo.setFreeTotalAmount(vo.getFreeTotalAmount().subtract(productFreeAmount));
                            }
                        }
                        if (OrderStatusEnum.CANCEL.getCode().equals(itemListVo.getStatus())
                                || OrderStatusEnum.WAIT.getCode().equals(itemListVo.getStatus())) {
                            itemListVo.setShowStatusName(OrderStatusEnum.getDescByCode(itemListVo.getStatus()));
                        } else {
                            itemListVo.setShowStatusName(OrderWorkStatusEnum.getDescByCode(itemListVo.getWorkStatus()));
                        }
                    }
                    long count = itemListVoList.stream().map(QueryItemListVo::getSupplierSkuId).distinct().count();
                    vo.setSkuCount(count);
                    vo.setOrderItemList(itemListVoList);
                }
            }
        }
        return page;
    }

    @Override
    public TableDataInfo<QueryDeliveryVo> queryPageDelivery(QueryOrderPageDeliveryBo bo) {
        if (StrUtil.isNotBlank(bo.getCustomerPhoneNo())) {
            if (bo.getCustomerPhoneNo().length() != 11) {
                return TableDataInfo.build();
            }
            RemoteUserBo userByPhoneNo = remoteUserService.getUserByPhoneNo(bo.getCustomerPhoneNo(), SysUserTypeEnum.CUSTOMER_USER.getType());
            if (ObjectUtil.isNull(userByPhoneNo)) {
                return TableDataInfo.build();
            }
            List<Long> customerIds = remoteBasCustomerService.getListByAdminCode(userByPhoneNo.getUserCode());
            if (CollUtil.isEmpty(customerIds)) {
                return TableDataInfo.build();
            }
            bo.setCustomerIdList(customerIds);
        }
        bo.setBusinessType(OrderBusinessTypeEnum.BUSINESS_TYPE40.getCode());
        bo.setPayTime(getDelayQueryTime());
        TableDataInfo<QueryDeliveryVo> page = TableDataInfo.build(orderMapper.queryPageDelivery(bo, bo.build()));
        laodDeliveryOrderItemList(page.getRows());
        return page;
    }

    @Override
    public QueryDeliveryVo getDeliveryInfoByCode(String code, Boolean isQueryItemList) {
        QueryOrderPageDeliveryBo bo = new QueryOrderPageDeliveryBo();
        bo.setCode(code);
        List<QueryDeliveryVo> list = orderMapper.queryPageDelivery(bo);
        if (list.size() == 0) {
            throw new ServiceException("订单号不存在");
        }
        QueryDeliveryVo vo = list.get(0);
        if (vo.getBusinessType().intValue() != OrderBusinessTypeEnum.BUSINESS_TYPE40.getCode()) {
            throw new ServiceException("非配货订单，不支持操作");
        }
        if (vo.getPayTime().compareTo(getDelayQueryTime()) > 0) {
            throw new ServiceException("下单5分钟后才能查看");
        }
        if (isQueryItemList) {
            laodDeliveryOrderItemList(list);
            if (CollUtil.isEmpty(vo.getOrderItemList())) {
                throw new ServiceException("无订单项数据");
            }
            RemoteQueryInfoListBo skuBo = new RemoteQueryInfoListBo();
            skuBo.setSupplierSkuIdList(vo.getOrderItemList().stream().map(QueryItemListVo::getSupplierSkuId).toList());
            Map<Long, RemoteSupplierSkuInfoVo> skuVoMap = remoteSupplierSkuService.queryInfoList(skuBo).stream().collect(Collectors.toMap(RemoteSupplierSkuInfoVo::getId, Function.identity()));
            for (QueryItemListVo itemListVo : vo.getOrderItemList()) {
                RemoteSupplierSkuInfoVo skuInfoVo = skuVoMap.get(itemListVo.getSupplierSkuId());
                if (skuInfoVo != null) {
                    itemListVo.setSpuGrade(skuInfoVo.getSpuGrade());
                    itemListVo.setSpuStandards(skuInfoVo.getSpuStandards());
                    itemListVo.setProducer(skuInfoVo.getProducer());
                    itemListVo.setBrand(skuInfoVo.getBrand());
                    itemListVo.setAreaCode(skuInfoVo.getAreaCode());
                    itemListVo.setShortProducer(skuInfoVo.getShortProducer());
                }
            }
        }
        return vo;
    }

    private void laodDeliveryOrderItemList(List<QueryDeliveryVo> list) {
        if (list.size() > 0) {
            List<Long> orderIds = list.stream().map(QueryDeliveryVo::getId).toList();
            List<QueryItemListVo> itemList = orderItemMapper.queryItemList(orderIds);
            List<Long> supplierIdList = itemList.stream().map(QueryItemListVo::getSupplierId).distinct().toList();
            Map<Long, RemoteSupplierVo> supplierVoMap = remoteSupplierService.getSupplierByIds(supplierIdList).stream().collect(Collectors.toMap(RemoteSupplierVo::getId, Function.identity()));
            List<Long> supplierDeptIdList = itemList.stream().map(QueryItemListVo::getSupplierDeptId).filter(Objects::nonNull).filter(v -> v > 0L).distinct().collect(Collectors.toList());
            Map<Long, String> deptNameMap = getSupplierMap(supplierDeptIdList);
            for (QueryItemListVo itemVo : itemList) {
                itemVo.setSupplierName(supplierVoMap.getOrDefault(itemVo.getSupplierId(), new RemoteSupplierVo()).getName());
                itemVo.setSupplierAlias(supplierVoMap.getOrDefault(itemVo.getSupplierId(), new RemoteSupplierVo()).getAlias());
                itemVo.setSupplierDeptName(deptNameMap.get(itemVo.getSupplierDeptId()));
            }
            Map<Long, List<QueryItemListVo>> itemMap = itemList.stream().filter(f -> f.getBusinessType().intValue() == OrderBusinessTypeEnum.BUSINESS_TYPE40.getCode()).collect(Collectors.groupingBy(QueryItemListVo::getOrderId));

            //获取客户信息
            List<Long> customerIds = list.stream().map(QueryDeliveryVo::getCustomerId).distinct().toList();
            Map<Long, RemoteCustomerVo> customerVoMap = remoteBasCustomerService.getByIds(customerIds)
                    .stream().collect(Collectors.toMap(RemoteCustomerVo::getId, Function.identity(), (key1, key2) -> key2));
            // 用户信息
            Map<String, RemoteUserBo> userInfoMap = remoteUserService.getUsersByUserCodes(customerVoMap.values().stream().map(RemoteCustomerVo::getUserCode).toList());
            // 物流信息
            List<Long> logisticsIds = list.stream().map(QueryDeliveryVo::getLogisticsId).distinct().toList();
            Map<Long, String> logisticsNameMap = customToolService.queryLogisticsNameMap(logisticsIds);
            // 物流车位
            LinkedHashMap<Long, RemoteRegionWhParkingVo> logisticsVoMap = customToolService.getDeliveryLogistics(list.get(0).getRegionWhId(), true);
            for (QueryDeliveryVo vo : list) {
                if (itemMap.containsKey(vo.getId())) {
                    vo.setOrderItemList(itemMap.get(vo.getId()));
                    vo.setCount(vo.getOrderItemList().stream().mapToInt(QueryItemListVo::getCount).sum());
                    vo.setTotalGrossWeight(vo.getOrderItemList().stream().map(QueryItemListVo::getTotalGrossWeight).reduce(BigDecimal.ZERO, BigDecimal::add));
                    vo.setTotalNetWeight(vo.getOrderItemList().stream().map(QueryItemListVo::getTotalNetWeight).reduce(BigDecimal.ZERO, BigDecimal::add));
                }
                RemoteCustomerVo customerVo = customerVoMap.get(vo.getCustomerId());
                if (customerVo != null) {
                    vo.setCustomerName(customerVo.getName());
                    vo.setCustomerAlias(customerVo.getAlias());
                    vo.setCustomerPhoneNo(userInfoMap.getOrDefault(customerVo.getUserCode(), new RemoteUserBo()).getPhoneNo());
                }
                vo.setLogisticsName(logisticsNameMap.get(vo.getLogisticsId()));
                vo.setParkingNo(logisticsVoMap.getOrDefault(vo.getLogisticsId(), new RemoteRegionWhParkingVo()).getParkingNo());
                if (DeliveryStatusEnum.WAIT_INSPECT.getCode().equals(vo.getDeliveryStatus())) {
                    vo.setIsShowNoPurchase(1);
                }
            }
        }
    }

    @Override
    public CountAmountVo countAmount(AddOrderBo bo) {
        List<Long> skuIdList = bo.getSkuList().stream().map(SkuBo::getSkuId).toList();
        CountAmountVo countAmountVo = new CountAmountVo();
        if (CollectionUtil.isEmpty(skuIdList)) {
            return countAmountVo;
        }
        RemoteQueryInfoListBo skuBo = new RemoteQueryInfoListBo();
        skuBo.setSupplierSkuIdList(skuIdList);
        List<RemoteSupplierSkuInfoVo> skuList = remoteSupplierSkuService.queryInfoList(skuBo);
        if (skuList == null || skuList.size() == 0 || skuList.size() < skuIdList.size()) {
            throw new ServiceException("商品数据有误");
        }
        bo.setSaleDate(skuList.get(0).getSaleDate()); // 设置销售日期， 免运免代需要
        Map<Long, RemoteSupplierSkuInfoVo> skuMap = skuList.stream().collect(Collectors.toMap(RemoteSupplierSkuInfoVo::getId, Function.identity(), (key1, key2) -> key2));
        //一级物流
        RemoteRegionLogisticsVo logisticsVo = null;
        //二级物流
        RemoteRegionLogisticsVo logistics2Vo = null;

        if (bo.getPlaceId() != null) {
            RemoteCityWhPlaceVo remoteCityWhPlaceVo = remoteCityWhPlaceService.queryById(bo.getPlaceId());
            Long parentPlaceId = remoteCityWhPlaceVo.getParentPlaceId();
            RemoteRegionLogisticsQueryBo logisticsQueryBo = new RemoteRegionLogisticsQueryBo();
            logisticsQueryBo.setRegionWhId(bo.getRegionWhId());
            logisticsQueryBo.setCityWhId(bo.getCityWhId());
            logisticsQueryBo.setPlaceId(Objects.nonNull(parentPlaceId) ? parentPlaceId : bo.getPlaceId());
            logisticsQueryBo.setLogisticsType(0);
            List<RemoteRegionLogisticsVo> logisticsVoList = remoteRegionLogisticsService.queryList(logisticsQueryBo);
            if (logisticsVoList == null || logisticsVoList.size() == 0) {
                throw new ServiceException("物流线不存在");
            }
            logisticsVo = logisticsVoList.get(0);
            if (ObjectUtil.isNotEmpty(parentPlaceId)) {
                RemoteRegionLogisticsQueryBo logistics = new RemoteRegionLogisticsQueryBo();
                logistics.setCityWhId(bo.getCityWhId());
                logistics.setParentPlaceId(parentPlaceId);
                logistics.setPlaceId(bo.getPlaceId());
                logistics.setLogisticsType(1);
                List<RemoteRegionLogisticsVo> logistics2List = remoteRegionLogisticsService.queryList(logistics);
                if (logistics2List == null || logistics2List.size() == 0) {
                    throw new ServiceException("物流线不存在");
                }
                logistics2Vo = logistics2List.get(0);
            }
        }
        //获取基采物流
        Map<String, RemoteSupplierSkuInfoVo> skuInfoVoMap = skuList.stream().filter(item -> Objects.equals(item.getBusinessType()
                , SupplierSkuBusinessTypeEnum.BUSINESS_TYPE20.getCode())).collect(Collectors
                .toMap(key -> String.format("%s_%s", key.getProvideRegionWhId(), key.getRegionWhId()), item -> item, (key1, key2) -> key2));
        Map<String, RemoteFreightLogisticsVo> freightLogisticsVoMap = skuInfoVoMap.keySet().stream().collect(Collectors.toMap(key -> key, key -> {
            RemoteSupplierSkuInfoVo skuInfoVo = skuInfoVoMap.get(key);
            RemoteFreightLogisticsQueryBo logisticsQueryBo = new RemoteFreightLogisticsQueryBo();
            logisticsQueryBo.setProvideRegionWhId(skuInfoVo.getProvideRegionWhId());
            logisticsQueryBo.setRegionWhId(skuInfoVo.getRegionWhId());
            List<RemoteFreightLogisticsVo> remoteFreightLogisticsVos = remoteFreightLogisticsService.queryList(logisticsQueryBo);
            if (CollectionUtil.isEmpty(remoteFreightLogisticsVos)) {
                throw new ServiceException("基采物流不存在，请联系总仓工作人员");
            }
            return remoteFreightLogisticsVos.get(0);
        }));
        CountAmountDetailVo amountDetailVo = countAmountDetail(bo, skuMap, logisticsVo, logistics2Vo, freightLogisticsVoMap, null);
        CountAmountVo vo = MapstructUtils.convert(amountDetailVo, CountAmountVo.class);
        String payChannel = getPayChannel(vo.getTotalAmount());
        vo.setPayChannel(payChannel);
        if (PayChannelEnum.PAY_PINGAN_CLOUD_LARGE.getCode().equals(payChannel)) {
            vo.setTotalAmount(vo.getTotalAmountLarge());
            vo.setFinancialServiceAmount(BigDecimal.ZERO);
        }
        return vo;
    }

    public String getPayChannel(BigDecimal price) {
        if (price.compareTo(orderProperties.getPayAmountStepList().get(0)) < 0) {
            return PayChannelEnum.PAY_WEIXIN_B2B.getCode();
        }
        if (price.compareTo(orderProperties.getPayAmountStepList().get(1)) > 0) {
            return PayChannelEnum.PAY_PINGAN_CLOUD_LARGE.getCode();
        }
        return "free";
    }

    private CountAmountDetailVo countAmountDetail(AddOrderBo bo, Map<Long, RemoteSupplierSkuInfoVo> skuMap
            , RemoteRegionLogisticsVo logisticsVo, RemoteRegionLogisticsVo logistics2Vo, Map<String, RemoteFreightLogisticsVo> freightLogisticsVoMap, BigDecimal financialServiceRate) {
        // 免运免代的规则
        RemoteRuleFreightServiceQueryBo ruleQueryBo = new RemoteRuleFreightServiceQueryBo().setRegionWhId(bo.getRegionWhId())
                .setCityWhId(bo.getCityWhId()).setCustomerId(bo.getCustomerId()).setLogisticsId(logisticsVo == null ? null : logisticsVo.getId())
                .setPriceMode(logisticsVo == null ? null : logisticsVo.getPriceMode()).setSaleDate(bo.getSaleDate())
                .setSkuIds(bo.getSkuList().stream().map(vo -> new RemoteRuleFreightServiceSkuBo()
                        .setSkuId(vo.getSkuId()).setCount(vo.getCount()).setSpuId(skuMap.get(vo.getSkuId()).getSpuId())).toList());
        Map<Long, RemoteRuleFreightPriceVo> ruleFreightPriceVoMap = remoteRuleFreightServiceService.querySkuFreightPriceMap(ruleQueryBo);
        log.keyword("querySkuFreightPriceMap").info(JSON.toJSONString(ruleFreightPriceVoMap));
        Map<Long, RemoteRuleServiceAmtVo> ruleServiceAmtVoMap = remoteRuleFreightServiceService.querySkuServiceAmtMap(ruleQueryBo);
        log.keyword("querySkuServiceAmtMap").info(JSON.toJSONString(ruleServiceAmtVoMap));
        //活动数据
        List<ActivityRecord> activityRecords = new ArrayList<>();
        // 返回数据
        CountAmountDetailVo vo = new CountAmountDetailVo();
        //订单项金额详情
        List<CountAmountDetailVo> itemList = new ArrayList<>();
        vo.setItemList(itemList);
        //优惠补贴金额
        BigDecimal subsidyFreeAmount = getSubsidyFreeAmount();
        for (SkuBo sku : bo.getSkuList()) {
            //商品总件数
            vo.setCount(vo.getCount() + sku.getCount());
            //订单项
            CountAmountDetailVo detail = new CountAmountDetailVo();
            itemList.add(detail);
            detail.setCount(sku.getCount());
            detail.setId(sku.getSkuId());
            //获取商品单价
            RemoteSupplierSkuInfoVo remoteSku = skuMap.get(sku.getSkuId());
            BigDecimal price = remoteSku.getPrice();

            //订单项商品金额
            BigDecimal itemProductAmount = new BigDecimal(sku.getCount()).multiply(price);
            detail.setProductAmount(itemProductAmount);
            vo.setProductAmount(vo.getProductAmount().add(itemProductAmount));

            //订单项平台代采费
            BigDecimal platformServiceAmt = getPlatformServiceAmt(price);
            //配货商品不算代采费
            platformServiceAmt = Objects.equals(remoteSku.getBusinessType(), SupplierSkuBusinessTypeEnum.BUSINESS_TYPE40.getCode()) ? BigDecimal.ZERO : platformServiceAmt;
            //代采费原价
            BigDecimal originalItemPlatformServiceAmount = platformServiceAmt.multiply(new BigDecimal(sku.getCount()));
            //活动后代采费金额
            BigDecimal itemPlatformServiceAmount;
            //订单项平台代采费优惠
            BigDecimal itemPlatformServiceFreeAmount = BigDecimal.ZERO;
            RemoteRuleServiceAmtVo ruleServiceAmtVo = ruleServiceAmtVoMap.get(sku.getSkuId());
            if (ruleServiceAmtVo != null && ruleServiceAmtVo.getServiceAmt().compareTo(platformServiceAmt) < 0) {
                platformServiceAmt = ruleServiceAmtVo.getServiceAmt();
                itemPlatformServiceAmount = platformServiceAmt.multiply(new BigDecimal(sku.getCount()));
                itemPlatformServiceFreeAmount = originalItemPlatformServiceAmount.subtract(itemPlatformServiceAmount);
                //构建活动数据
                ActivityRecord activityRecord = buildActivityRecord(ruleServiceAmtVo.getId(), itemPlatformServiceFreeAmount
                        , ActivityRecordTypeEnum.FREE_SERVICE.getCode(), remoteSku.getId());
                activityRecords.add(activityRecord);
            }
            //代采费原价
            detail.setPlatformServiceAmount(originalItemPlatformServiceAmount);
            vo.setPlatformServiceAmount(vo.getPlatformServiceAmount().add(originalItemPlatformServiceAmount));

            //订单项运费
            //订单项平台运费-市采-活动后价格
            BigDecimal itemPlatformFreightAmount = BigDecimal.ZERO;
            //订单项平台运费-市采-原价
            BigDecimal originalItemPlatformFreightAmount = BigDecimal.ZERO;
            //订单项平台运费-基采
            BigDecimal itemBaseFreightAmount = BigDecimal.ZERO;
            //订单项平台运费-市采-优惠金额
            BigDecimal itemPlatformFreightFreeAmount = BigDecimal.ZERO;
            //订单项平台运费-市采-原价
            BigDecimal originalItemPlatformFreightAmountLevel2 = BigDecimal.ZERO;

            //市采运费
            if (logisticsVo != null) {
                BigDecimal cityFreightPrice = logisticsVo.getCityFreightPrice();
                if (logisticsVo.getPriceMode().equals(0)) {
                    originalItemPlatformFreightAmount = cityFreightPrice.multiply(new BigDecimal(sku.getCount())).setScale(2, RoundingMode.HALF_UP);
                } else if (logisticsVo.getPriceMode().equals(1)) {
                    originalItemPlatformFreightAmount = cityFreightPrice.multiply(remoteSku.getSpuGrossWeight()).multiply(new BigDecimal(sku.getCount())).setScale(2, RoundingMode.HALF_UP);
                }
                //计算的值等于0且设置的大于0  保底值为0.01
                if (originalItemPlatformFreightAmount.compareTo(BigDecimal.ZERO) == 0 && cityFreightPrice.compareTo(BigDecimal.ZERO) > 0) {
                    originalItemPlatformFreightAmount = new BigDecimal("0.01");
                }
                itemPlatformFreightAmount = originalItemPlatformFreightAmount;
                // 免运规则，  无物流的时候，不取免运规则
                RemoteRuleFreightPriceVo ruleFreightPriceVo = ruleFreightPriceVoMap.get(sku.getSkuId());
                detail.setFreightPrice(logisticsVo.getFreightPrice());
                if (ruleFreightPriceVo != null && ruleFreightPriceVo.getFreightPrice().compareTo(cityFreightPrice) < 0) {
                    //活动信息
                    detail.setIsFreightDiscount(1);
                    detail.setFreightDiscountMinNum(ruleFreightPriceVo.getMinNum());

                    cityFreightPrice = ruleFreightPriceVo.getFreightPrice();
                    if (logisticsVo.getPriceMode().equals(0)) {
                        itemPlatformFreightAmount = cityFreightPrice.multiply(new BigDecimal(sku.getCount())).setScale(2, RoundingMode.HALF_UP);
                    } else if (logisticsVo.getPriceMode().equals(1)) {
                        itemPlatformFreightAmount = cityFreightPrice.multiply(remoteSku.getSpuGrossWeight()).multiply(new BigDecimal(sku.getCount())).setScale(2, RoundingMode.HALF_UP);
                    }
                    //计算的值等于0且设置的大于0  保底值为0.01
                    if (itemPlatformFreightAmount.compareTo(BigDecimal.ZERO) == 0 && cityFreightPrice.compareTo(BigDecimal.ZERO) > 0) {
                        itemPlatformFreightAmount = new BigDecimal("0.01");
                    }
                    itemPlatformFreightFreeAmount = originalItemPlatformFreightAmount.subtract(itemPlatformFreightAmount);
                    //构建活动数据
                    ActivityRecord activityRecord = buildActivityRecord(ruleFreightPriceVo.getId(), itemPlatformFreightFreeAmount
                            , ActivityRecordTypeEnum.FREE_FREIGHT.getCode(), remoteSku.getId());
                    activityRecords.add(activityRecord);
                }
                //运费信息
                detail.setPriceMode(logisticsVo.getPriceMode());
                detail.setFreightPrice(cityFreightPrice);
            }
            //基采运费
            if (Objects.equals(remoteSku.getBusinessType(), OrderBusinessTypeEnum.BUSINESS_TYPE20.getCode())) {
                RemoteFreightLogisticsVo freightLogisticsVo = freightLogisticsVoMap.get(String.format("%s_%s", remoteSku.getProvideRegionWhId(), remoteSku.getRegionWhId()));
                BigDecimal freightPrice = freightLogisticsVo.getFreightPrice();
                if (freightLogisticsVo.getPriceMode().equals(0)) {
                    itemBaseFreightAmount = freightPrice.multiply(new BigDecimal(sku.getCount())).setScale(2, RoundingMode.HALF_UP);
                } else if (freightLogisticsVo.getPriceMode().equals(1)) {
                    itemBaseFreightAmount = freightPrice.multiply(remoteSku.getSpuGrossWeight()).multiply(new BigDecimal(sku.getCount())).setScale(2, RoundingMode.HALF_UP);
                }
                //计算的值等于0且设置的大于0  保底值为0.01
                if (itemBaseFreightAmount.compareTo(BigDecimal.ZERO) == 0 && freightPrice.compareTo(BigDecimal.ZERO) > 0) {
                    itemBaseFreightAmount = new BigDecimal("0.01");
                }
            }
            //二段运费（提货点到提货点）
            if (Objects.nonNull(logistics2Vo)) {
                BigDecimal freightPrice = logistics2Vo.getFreightPrice();
                if (logistics2Vo.getPriceMode().equals(0)) {
                    originalItemPlatformFreightAmountLevel2 = freightPrice.multiply(new BigDecimal(sku.getCount())).setScale(2, RoundingMode.HALF_UP);
                } else if (logistics2Vo.getPriceMode().equals(1)) {
                    originalItemPlatformFreightAmountLevel2 = freightPrice.multiply(remoteSku.getSpuGrossWeight()).multiply(new BigDecimal(sku.getCount())).setScale(2, RoundingMode.HALF_UP);
                }
            }
            //二段运费（提货点到提货点）
            detail.setPlatformFreightAmountLevel2(originalItemPlatformFreightAmountLevel2);
            vo.setPlatformFreightAmountLevel2(vo.getPlatformFreightAmountLevel2().add(originalItemPlatformFreightAmountLevel2));

            detail.setPlatformFreightAmount(originalItemPlatformFreightAmount);
            vo.setPlatformFreightAmount(vo.getPlatformFreightAmount().add(originalItemPlatformFreightAmount));

            //订单项基采运费
            detail.setBaseFreightAmount(itemBaseFreightAmount);
            vo.setBaseFreightAmount(vo.getBaseFreightAmount().add(itemBaseFreightAmount));

            //订单项地采运费，没数据，先写个0.00
            BigDecimal itemRegionFreightAmount = BigDecimal.ZERO;
            detail.setRegionFreightAmount(itemRegionFreightAmount);
            vo.setRegionFreightAmount(vo.getRegionFreightAmount().add(itemRegionFreightAmount));
            //订单项运费总金额
            BigDecimal itemFreightTotalAmount = originalItemPlatformFreightAmount.add(itemBaseFreightAmount)
                    .add(itemRegionFreightAmount).add(originalItemPlatformFreightAmountLevel2);
            detail.setFreightTotalAmount(itemFreightTotalAmount);
            vo.setFreightTotalAmount(vo.getFreightTotalAmount().add(itemFreightTotalAmount));

            //非商品金额总金额
            BigDecimal itemOtherTotalAmount = itemFreightTotalAmount.add(originalItemPlatformServiceAmount);
            detail.setOtherTotalAmount(itemOtherTotalAmount);
            vo.setOtherTotalAmount(vo.getOtherTotalAmount().add(itemOtherTotalAmount));

            //订单项运费优惠
            //订单项平台运费优惠，没数据，先写个0.00
            detail.setPlatformFreightFreeAmount(itemPlatformFreightFreeAmount);
            vo.setPlatformFreightFreeAmount(vo.getPlatformFreightFreeAmount().add(itemPlatformFreightFreeAmount));
            //订单项基采运费优惠，没数据，先写个0.00
            BigDecimal itemBaseFreightFreeAmount = BigDecimal.ZERO;
            detail.setBaseFreightFreeAmount(itemBaseFreightFreeAmount);
            vo.setBaseFreightFreeAmount(vo.getBaseFreightFreeAmount().add(itemBaseFreightFreeAmount));
            //订单项地采运费优惠，没数据，先写个0.00
            BigDecimal itemRegionFreightFreeAmount = BigDecimal.ZERO;
            detail.setRegionFreightFreeAmount(itemRegionFreightFreeAmount);
            vo.setRegionFreightFreeAmount(vo.getRegionFreightFreeAmount().add(itemRegionFreightFreeAmount));
            //订单项运费优惠总金额
            BigDecimal itemFreightTotalFreeAmount = itemPlatformFreightFreeAmount.add(itemBaseFreightFreeAmount).add(itemRegionFreightFreeAmount);
            detail.setFreightTotalFreeAmount(itemFreightTotalFreeAmount);
            vo.setFreightTotalFreeAmount(vo.getFreightTotalFreeAmount().add(itemFreightTotalFreeAmount));

            detail.setPlatformServiceFreeAmount(itemPlatformServiceFreeAmount);
            vo.setPlatformServiceFreeAmount(vo.getPlatformServiceFreeAmount().add(itemPlatformServiceFreeAmount));

            //订单项代采币抵扣
            BigDecimal itemCoinCount = BigDecimal.ZERO;
            detail.setCoinCount(itemCoinCount);
            vo.setCoinCount(vo.getCoinCount().add(itemCoinCount));
            BigDecimal itemCoinAmount = BigDecimal.ZERO;
            detail.setCoinAmount(itemCoinAmount);
            vo.setCoinAmount(vo.getCoinAmount().add(itemCoinAmount));

            //计算优惠补贴
            BigDecimal itemSubsidyFreeAmount = remoteSku.getSpuGrossWeight()
                    .multiply(new BigDecimal(sku.getCount()))
                    .multiply(bo.getRegionWhId().equals(getSubsidyRegionWhId()) ? subsidyFreeAmount : BigDecimal.ZERO)
                    .setScale(2, RoundingMode.HALF_UP);
            if (itemSubsidyFreeAmount.compareTo(itemProductAmount) >= 0) {
                itemSubsidyFreeAmount = itemProductAmount;
            }
            detail.setSubsidyFreeAmount(itemSubsidyFreeAmount);
            vo.setSubsidyFreeAmount(vo.getSubsidyFreeAmount().add(itemSubsidyFreeAmount));

            //订单项优惠总金额=订单项运费优惠总金额+订单项平台代采费优惠+订单项代采币抵扣+优惠补贴商品金额
            BigDecimal itemFreeTotalAmount = itemFreightTotalFreeAmount
                    .add(itemPlatformServiceFreeAmount)
                    .add(itemCoinAmount)
                    .add(itemSubsidyFreeAmount);
            detail.setFreeTotalAmount(itemFreeTotalAmount);
            vo.setFreeTotalAmount(vo.getFreeTotalAmount().add(itemFreeTotalAmount));

            //订单项总金额 = 商品金额+非商品金额-优惠金额

            BigDecimal itemTotalAmount = itemProductAmount.add(itemOtherTotalAmount).subtract(itemFreeTotalAmount);
            detail.setTotalAmount(itemTotalAmount);
            vo.setTotalAmount(vo.getTotalAmount().add(itemTotalAmount));
        }
        vo.setActivityRecordList(activityRecords);
        BigDecimal realRate;
        if (Objects.isNull(financialServiceRate)) {
            //计算真实费率
            realRate = this.getRealRate(bo, vo);
        } else {
            realRate = financialServiceRate;
        }
        vo.setFinancialServiceRate(realRate);
        //订单金融手续费
        BigDecimal financialServiceAmount = vo.getTotalAmount().multiply(realRate).divide(BigDecimal.ONE.subtract(realRate), 2, RoundingMode.HALF_UP);
        if (financialServiceAmount.compareTo(BigDecimal.ZERO) == 0) {
            financialServiceAmount = new BigDecimal("0.01");
        }
        //订单总金额 = 订单项总金额合计+金融手续费
        vo.setFinancialServiceAmount(financialServiceAmount);
        vo.setTotalAmount(vo.getTotalAmount().add(financialServiceAmount));
        //大额支付相关
        vo.setTotalAmountLarge(vo.getTotalAmount().subtract(vo.getFinancialServiceAmount()));
        return vo;
    }

    /**
     * 计算订单项平台代采费
     *
     * @param price
     * @return
     */
    @Override
    public BigDecimal getPlatformServiceAmt(BigDecimal price) {
        int step = 0;
        for (BigDecimal platformServiceStep : orderProperties.getPlatformServiceStepList()) {
            if (price.compareTo(platformServiceStep) > 0) {
                return orderProperties.getPlatformServiceAmountList().get(step);
            }
            step++;
        }
        return BigDecimal.ZERO;
    }

    /**
     * 返回代采费和结算件数
     *
     * @param price
     * @return
     */
    public OrderSettkeInfBO getPlatformServiceAmtBo(BigDecimal price) {
        OrderSettkeInfBO orderSettkeInfBO = new OrderSettkeInfBO();
        int step = 0;
        for (BigDecimal platformServiceStep : orderProperties.getPlatformServiceStepList()) {
            if (price.compareTo(platformServiceStep) > 0) {
                List<BigDecimal> platformServiceCountList = orderProperties.getPlatformServiceCountList();
                orderSettkeInfBO.setCount(platformServiceCountList.get(step));
                orderSettkeInfBO.setPrice(orderProperties.getPlatformServiceAmountList().get(step));
                return orderSettkeInfBO;
            }
            step++;
        }
        return orderSettkeInfBO;
    }


    /**
     * 计算真实费率
     */
    private BigDecimal getRealRate(AddOrderBo bo, CountAmountDetailVo vo) {
        if (!orderProperties.getRateSwitch()) {
            return orderProperties.getFinancialServiceRate();
        }
        BigDecimal minRateAmount = vo.getTotalAmount().multiply(orderProperties.getMinRate()).divide(BigDecimal.ONE.subtract(orderProperties.getMinRate()), 2, RoundingMode.HALF_UP);
        minRateAmount = minRateAmount.compareTo(BigDecimal.ZERO) == 0 ? new BigDecimal("0.01") : minRateAmount;
        BigDecimal maxRateAmount = vo.getTotalAmount().multiply(orderProperties.getMaxRate()).divide(BigDecimal.ONE.subtract(orderProperties.getMaxRate()), 2, RoundingMode.HALF_UP);
        maxRateAmount = maxRateAmount.compareTo(BigDecimal.ZERO) == 0 ? new BigDecimal("0.01") : maxRateAmount;
        vo.setRateFreeAmount(maxRateAmount.subtract(minRateAmount));
        if (!bo.getIsAuthorized()) {
            return orderProperties.getMaxRate();
        }
        //redis key
        String redisKey = String.format("%s%s", OrderCacheNames.FINANCIAL_SERVICE_IS_AUTH, LoginHelper.getUserId());
        Boolean cacheRateSwitch = RedisUtils.getCacheObject(redisKey);
        cacheRateSwitch = Optional.ofNullable(cacheRateSwitch).orElse(Boolean.FALSE);
        if (cacheRateSwitch && bo.getIsAuthorized()) {
            return orderProperties.getMinRate();
        }
        LoginUser loginUser = LoginHelper.getLoginUser();
        RetailInfoBo retailInfoBo = new RetailInfoBo();
        String openid = loginUser.getOpenid();
        if (StringUtils.isEmpty(openid)) {
            StpUtil.logout();
            throw new ServiceException("登录失效,请重新登录");
        }
        cacheRateSwitch = false;
        try {
            retailInfoBo.setPayerId(loginUser.getOpenid());
            retailInfoBo.setAppId(loginUser.getDeviceId());
            RetailInfoVo retailInfo = remotePaymentService.getRetailInfo(retailInfoBo);
            log.keyword("getRealRate", loginUser.getUserId()).info(JsonUtils.toJsonString(retailInfo));
            if (Objects.isNull(retailInfo)) {
                return orderProperties.getMaxRate();
            }
            if (Objects.equals(retailInfo.getStatus(), RetailInfEnums.AUTHENTICATED.getStatus())) {
                cacheRateSwitch = Boolean.TRUE;
                return orderProperties.getMinRate();
            }
        } catch (Exception e) {
            log.keyword("getRealRate", loginUser.getUserId()).error("获取用户认证失败：{}", ExceptionUtils.getStackTrace(e));
        }
        RedisUtils.setCacheObject(redisKey, cacheRateSwitch, Duration.ofSeconds(orderProperties.getIsAuthTime()));
        return orderProperties.getMaxRate();
    }

    /**
     * 构建活动记录
     *
     * @param id
     * @param freeAmount
     * @param type
     * @return
     */
    private ActivityRecord buildActivityRecord(Long id, BigDecimal freeAmount, Integer type, Long skuId) {
        ActivityRecord activityRecord = new ActivityRecord();
        activityRecord.setActivityId(id);
        activityRecord.setFreeAmount(freeAmount);
        activityRecord.setType(type);
        activityRecord.setSupplierSkuId(skuId);
        return activityRecord;
    }

    /**
     * 前置检验
     *
     * @param bo
     * @return
     */
    @Override
    public ValidateVo validate(AddOrderBo bo) throws NotLoginException {
        //查询总仓信息
        RemoteRegionWhVo regionWhVo = remoteRegionWhService.queryById(bo.getRegionWhId());
        if (regionWhVo == null) {
            throw new ServiceException("总仓不存在");
        }
        if (!StatusEnum.ENABLE.getCode().equals(regionWhVo.getStatus())) {
            throw new ServiceException("总仓已被禁用");
        }
        //检查是否可以加购物车，是否可以下单
        checkIfSale(regionWhVo.getSalesTimeStart(), regionWhVo.getSalesTimeEnd());

        List<Long> skuIdList = bo.getSkuList().stream().map(SkuBo::getSkuId).toList();
        RemoteQueryInfoListBo skuBo = new RemoteQueryInfoListBo();
        skuBo.setSupplierSkuIdList(skuIdList);
        List<RemoteSupplierSkuInfoVo> skuList = remoteSupplierSkuService.queryInfoList(skuBo);
        if (skuList == null || skuList.size() == 0 || skuList.size() < skuIdList.size()) {
            throw new ServiceException("商品数据有误");
        }
        Map<Long, RemoteSupplierSkuInfoVo> skuMap = skuList.stream().collect(Collectors.toMap(RemoteSupplierSkuInfoVo::getId, Function.identity(), (key1, key2) -> key2));
        //没有售后服务的商品数量
        Integer afterSaleSkuCount = 0;
        for (SkuBo sku : bo.getSkuList()) {
            //获取销售批次售后信息
            Integer afterSaleDay = skuMap.get(sku.getSkuId()).getAfterSaleDay();
            if (afterSaleDay == 0) {
                afterSaleSkuCount++;
            }
        }
        ValidateVo vo = new ValidateVo();
        vo.setAfterSaleSkuCount(afterSaleSkuCount);
        LoginUser user = LoginHelper.getLoginUser();
        if (user == null) {
            throw new ServiceException("非法请求");
        }
        Long customerId = user.getRelationId();
        if (bo.getCustomerId() != null) {
            //加强验证，防止前端绕过登录，没找出原因
            if (bo.getCustomerId().equals(0L)) {
                StpUtil.logout();
                throw new NotLoginException("登录异常，需要重新登录", user.getDeviceType(), user.getUserType());
            }
            customerId = bo.getCustomerId();
        }
        RemoteCustomerVo customerVo = remoteBasCustomerService.getById(customerId);
        if (customerVo == null) {
            throw new ServiceException("用户异常，请删掉小程序重新登录");
        }
        vo.setAfterSaleStatus(customerVo.getAfterSaleStatus());

        // 是否为榴莲商品
        vo.setHasDurian(0);
        //是否提前取消
        Boolean isAdvancedCancel = getOrderCancelTime(LocalTime.now(), regionWhVo.getClearWhTime());
        vo.setIsAdvancedCancel(isAdvancedCancel);

        List<Long> skuCategoryIdList = skuList.stream().map(RemoteSupplierSkuInfoVo::getCategoryId).distinct().collect(Collectors.toList());
        List<RemoteCategoryVO> remoteCategoryVOS = remoteCategoryService.queryListByIds(skuCategoryIdList);
        if (CollUtil.isNotEmpty(remoteCategoryVOS)) {
            for (RemoteCategoryVO categoryVO : remoteCategoryVOS) {
                if (StringUtils.isNotBlank(categoryVO.getPathName()) && categoryVO.getPathName().contains("榴莲")) {
                    String[] split = categoryVO.getPathName().split("/");
                    if ((split.length > 1 && split[1].contains("榴莲")
                            || (split.length > 2 && split[2].contains("榴莲")))) {
                        vo.setHasDurian(1);
                        break;
                    }
                }
            }
        }

        return vo;
    }

    @Transactional(rollbackFor = Exception.class)
    public AddOrderVo insertOrderByTransactional(AddOrderBo bo) {
        LoginUser user = LoginHelper.getLoginUser();
        if (user == null) {
            throw new ServiceException("非法请求");
        }
        if (bo.getCustomerId() == null) {
            bo.setCustomerId(user.getRelationId());
        } else {
            RemoteCustomerVo vo = remoteBasCustomerService.getById(bo.getCustomerId());
            if (vo == null) {
                throw new ServiceException("客户不存在");
            }
        }
        //查询总仓信息
        RemoteRegionWhVo regionWhVo = remoteRegionWhService.queryById(bo.getRegionWhId());
        if (regionWhVo == null) {
            throw new ServiceException("总仓不存在");
        }
        if (!StatusEnum.ENABLE.getCode().equals(regionWhVo.getStatus())) {
            throw new ServiceException("总仓已被禁用");
        }
        //检查是否可以加购物车，是否可以下单
        checkIfSale(regionWhVo.getSalesTimeStart(), regionWhVo.getSalesTimeEnd());
        //获取销售日
        LocalDate saleDate = SaleDateUtil.getSaleDate(regionWhVo.getSalesTimeEnd());
        //查询商品信息
        List<Long> skuIdList = bo.getSkuList().stream().map(SkuBo::getSkuId).toList();
        RemoteQueryInfoListBo skuBo = new RemoteQueryInfoListBo();
        skuBo.setSupplierSkuIdList(skuIdList);
        List<RemoteSupplierSkuInfoVo> skuList = remoteSupplierSkuService.queryInfoList(skuBo);
        if (skuList == null || skuList.size() == 0 || skuList.size() < skuIdList.size()) {
            throw new ServiceException("商品数据有误");
        }

        boolean anyBusinessType40 = skuList.stream().anyMatch(item -> Objects.equals(item.getBusinessType(), SupplierSkuBusinessTypeEnum.BUSINESS_TYPE40.getCode()));
        if (regionWhVo.getCityWhIdList().contains(bo.getCityWhId()) && anyBusinessType40) {
            throw new ServiceException("直属仓不能下配货商品！");
        }
        boolean allBusinessType40 = skuList.stream().allMatch(item -> Objects.equals(item.getBusinessType(), SupplierSkuBusinessTypeEnum.BUSINESS_TYPE40.getCode()));
        if (anyBusinessType40 && !allBusinessType40) {
            throw new ServiceException("配货商品不能与其他商品一起下单！");
        }
        List<Long> maxIdList = skuList.stream().filter(l -> l.getBuyMax() > 0).map(RemoteSupplierSkuInfoVo::getId).toList();
        Map<Long, Integer> skuCountMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(maxIdList)) {
            List<OrderSkuCustomerVO> orderSkuCustomerVOS = orderItemMapper.getSkuCountByCustomerId(maxIdList, saleDate, bo.getCustomerId(), null);
            if (CollectionUtil.isNotEmpty(orderSkuCustomerVOS)) {
                skuCountMap = orderSkuCustomerVOS.stream().collect(Collectors.toMap(OrderSkuCustomerVO::getSupplierSkuId
                        , OrderSkuCustomerVO::getCount, (key1, key2) -> key2));
            }
        }
        // 设置销售日期， 免运免代需要
        bo.setSaleDate(skuList.get(0).getSaleDate());
        Map<Long, RemoteSupplierSkuInfoVo> skuMap = skuList.stream().collect(Collectors.toMap(RemoteSupplierSkuInfoVo::getId, Function.identity(), (key1, key2) -> key2));
        List<SkuBo> addList = new ArrayList<>();
        List<CancelOrderItemVo> cancelList = new ArrayList<>();
        //如果购买的某个商品有问题，那么这个商品就不创建订单项，并且需要返回给前端提示用户
        for (SkuBo sku : bo.getSkuList()) {
            RemoteSupplierSkuInfoVo skuInfoVo = skuMap.get(sku.getSkuId());
            //总仓id是否一致
            if (!skuInfoVo.getRegionWhId().equals(bo.getRegionWhId())) {
                throw new ServiceException("商品：【" + skuInfoVo.getSpuName() + "】总仓有误");
            }

            //去除了校验（不同商品类型不能同时下单）

            //销售日有误
            if (!saleDate.isEqual(skuInfoVo.getSaleDate())) {
                throw new ServiceException("商品：【" + skuInfoVo.getSpuName() + "】销售日有误");
            }
            //是否下架
            if (!SupplierSkuStatusEnum.STATUS4.getCode().equals(skuInfoVo.getStatus())) {
                CancelOrderItemVo cancelVo = new CancelOrderItemVo();
                cancelVo.setSkuName(skuInfoVo.getSpuName());
                cancelVo.setRemark("已下架");
                cancelList.add(cancelVo);
                continue;
            }
            //是否售罄
            if (skuInfoVo.getStock() == 0) {
                CancelOrderItemVo cancelVo = new CancelOrderItemVo();
                cancelVo.setSkuName(skuInfoVo.getSpuName());
                cancelVo.setRemark("已售罄");
                cancelList.add(cancelVo);
                continue;
            }
            //是否库存不足
            if (skuInfoVo.getStock() < sku.getCount()) {
                CancelOrderItemVo cancelVo = new CancelOrderItemVo();
                cancelVo.setSkuName(skuInfoVo.getSpuName());
                cancelVo.setRemark("库存不足");
                cancelList.add(cancelVo);
                continue;
            }
            //销售批次最小购买数量
            if (skuInfoVo.getBuyMin() != -1 && sku.getCount().compareTo(skuInfoVo.getBuyMin()) < 0) {
                CancelOrderItemVo cancelVo = new CancelOrderItemVo();
                cancelVo.setSkuName(skuInfoVo.getSpuName());
                cancelVo.setRemark("最小起订" + skuInfoVo.getBuyMin() + "件");
                cancelList.add(cancelVo);
                continue;
            }
            Integer countTotal = skuCountMap.getOrDefault(sku.getSkuId(), 0) + sku.getCount();
            //销售批次最大购买数量
            if (skuInfoVo.getBuyMax() != -1 && skuInfoVo.getBuyMax() < countTotal) {
                CancelOrderItemVo cancelVo = new CancelOrderItemVo();
                cancelVo.setSkuName(skuInfoVo.getSpuName());
                cancelVo.setRemark("销售日最多订" + skuInfoVo.getBuyMax() + "件");
                cancelList.add(cancelVo);
                continue;
            }
            addList.add(sku);
        }
        //组装返回数据
        AddOrderVo addOrderVo = new AddOrderVo();
        addOrderVo.setCancelList(cancelList);
        //如果购买的商品全部都有问题，那么就不创建订单
        if (addList.size() == 0) {
            return addOrderVo;
        }
        bo.setSkuList(addList);
        RemoteCityWhPlaceVo remoteCityWhPlaceVo = remoteCityWhPlaceService.queryById(bo.getPlaceId());
        RemoteCityWhPlaceVo remoteCityWhPlaceLevel2 = null;
        Long parentPlaceId = remoteCityWhPlaceVo.getParentPlaceId();
        if (Objects.nonNull(parentPlaceId)) {
            remoteCityWhPlaceLevel2 = remoteCityWhPlaceVo;
            remoteCityWhPlaceVo = remoteCityWhPlaceService.queryById(parentPlaceId);
        }
        //查询物流线
        RemoteRegionLogisticsQueryBo logisticsQueryBo = new RemoteRegionLogisticsQueryBo();
        logisticsQueryBo.setRegionWhId(bo.getRegionWhId());
        logisticsQueryBo.setCityWhId(bo.getCityWhId());
        logisticsQueryBo.setPlaceId(Objects.nonNull(parentPlaceId) ? parentPlaceId : bo.getPlaceId());
        List<RemoteRegionLogisticsVo> logisticsVoList = remoteRegionLogisticsService.queryList(logisticsQueryBo);
        if (logisticsVoList == null || logisticsVoList.size() == 0) {
            throw new ServiceException("物流线不存在");
        }

        //二级物流
        RemoteRegionLogisticsVo logistics2Vo = null;
        if (ObjectUtil.isNotEmpty(remoteCityWhPlaceLevel2)) {
            RemoteRegionLogisticsQueryBo logistics = new RemoteRegionLogisticsQueryBo();
            logistics.setCityWhId(bo.getCityWhId());
            logistics.setParentPlaceId(remoteCityWhPlaceVo.getId());
            logistics.setPlaceId(remoteCityWhPlaceLevel2.getId());
            logistics.setLogisticsType(1);
            List<RemoteRegionLogisticsVo> logistics2List = remoteRegionLogisticsService.queryList(logistics);
            if (logistics2List == null || logistics2List.size() == 0) {
                throw new ServiceException("物流线不存在");
            }
            logistics2Vo = logistics2List.get(0);
        }
        RemoteRegionLogisticsVo logisticsVo = logisticsVoList.get(0);
        //获取基采物流
        Map<String, RemoteSupplierSkuInfoVo> skuInfoVoMap = skuList.stream().filter(item -> Objects.equals(item.getBusinessType()
                , SupplierSkuBusinessTypeEnum.BUSINESS_TYPE20.getCode())).collect(Collectors
                .toMap(key -> String.format("%s_%s", key.getProvideRegionWhId(), key.getRegionWhId()), item -> item, (key1, key2) -> key2));
        Map<String, RemoteFreightLogisticsVo> freightLogisticsVoMap = skuInfoVoMap.keySet().stream().collect(Collectors.toMap(key -> key, key -> {
            RemoteSupplierSkuInfoVo skuInfoVo = skuInfoVoMap.get(key);
            RemoteFreightLogisticsQueryBo queryBo = new RemoteFreightLogisticsQueryBo();
            queryBo.setProvideRegionWhId(skuInfoVo.getProvideRegionWhId());
            queryBo.setRegionWhId(skuInfoVo.getRegionWhId());
            List<RemoteFreightLogisticsVo> remoteFreightLogisticsVos = remoteFreightLogisticsService.queryList(queryBo);
            if (CollectionUtil.isEmpty(remoteFreightLogisticsVos)) {
                throw new ServiceException("基采物流不存在，请联系总仓工作人员！");
            }
            return remoteFreightLogisticsVos.get(0);
        }));
        //计算金额
        CountAmountDetailVo amountDetailVo = countAmountDetail(bo, skuMap, logisticsVo, logistics2Vo, freightLogisticsVoMap, null);
        Map<Long, CountAmountDetailVo> amountMap = amountDetailVo.getItemList().stream().collect(Collectors.toMap(CountAmountDetailVo::getId, Function.identity(), (key1, key2) -> key2));

        //创建订单实体
        Order add = MapstructUtils.convert(amountDetailVo, Order.class);
        //大额支付
        if (PayChannelEnum.PAY_PINGAN_CLOUD_LARGE.getCode().equals(bo.getPayChannel())) {
            add.setTotalAmount(amountDetailVo.getTotalAmountLarge());
            add.setFinancialServiceAmount(BigDecimal.ZERO);
            add.setFinancialServiceRate(BigDecimal.ZERO);
        }
        add.setPayChannel(bo.getPayChannel());
        add.setSaleDate(saleDate);
        add.setCustomerId(bo.getCustomerId());
        add.setRegionWhId(bo.getRegionWhId());
        add.setCityWhId(bo.getCityWhId());
        add.setLogisticsId(logisticsVo.getId());
        add.setRemark(bo.getRemark());
        //查询提货点信息
        if (remoteCityWhPlaceVo == null) {
            throw new ServiceException("提货点不存在");
        }
        add.setPlaceId(remoteCityWhPlaceVo.getId());
        add.setPlaceName(remoteCityWhPlaceVo.getPlaceName());
        add.setAddress(remoteCityWhPlaceVo.getAddress());
        add.setPlacePath(remoteCityWhPlaceVo.getPath());
        //毛重
        if (Objects.nonNull(logistics2Vo)) {
            add.setLogisticsIdLevel2(logistics2Vo.getId());
            add.setPlaceIdLevel2(logistics2Vo.getPlaceId());
            add.setPlaceNameLevel2(remoteCityWhPlaceLevel2.getPlaceName());
            add.setAddressLevel2(remoteCityWhPlaceLevel2.getAddress());
            add.setPlacePath(remoteCityWhPlaceLevel2.getPath());
        }
        BigDecimal totalGrossWeight = BigDecimal.ZERO;
        //净重
        BigDecimal totalNetWeight = BigDecimal.ZERO;
        //订单项
        List<OrderItem> orderItemList = new ArrayList<>();
        for (SkuBo sku : bo.getSkuList()) {
            RemoteSupplierSkuInfoVo skuInfoVo = skuMap.get(sku.getSkuId());
            //订单项相关
            CountAmountDetailVo itemAmountDetailVo = amountMap.get(sku.getSkuId());
            if (itemAmountDetailVo == null) {
                throw new ServiceException("计算金额有误");
            }
            OrderItem orderItem = MapstructUtils.convert(itemAmountDetailVo, OrderItem.class);
            if (Objects.nonNull(logistics2Vo)) {
                orderItem.setLogisticsIdLevel2(add.getLogisticsIdLevel2());
                orderItem.setPlaceIdLevel2(add.getPlaceIdLevel2());
                orderItem.setPlaceNameLevel2(add.getPlaceNameLevel2());
                orderItem.setAddressLevel2(add.getAddressLevel2());
            } else {
                orderItem.setLogisticsIdLevel2(0L);
                orderItem.setPlaceIdLevel2(0L);
                orderItem.setPlaceNameLevel2("");
                orderItem.setAddressLevel2("");
            }
            orderItem.setPlacePath(add.getPlacePath());
            orderItem.setId(null);
            orderItem.setRegionWhId(add.getRegionWhId());
            orderItem.setCityWhId(add.getCityWhId());
            orderItem.setLogisticsId(add.getLogisticsId());
            orderItem.setPlaceId(add.getPlaceId());
            orderItem.setPlaceName(add.getPlaceName());
            orderItem.setAddress(add.getAddress());
            orderItem.setCustomerId(add.getCustomerId());
            orderItem.setCategoryId(skuInfoVo.getCategoryId());
            orderItem.setCategoryIdLevel1(skuInfoVo.getCategoryIdLevel1());
            orderItem.setCategoryIdLevel2(skuInfoVo.getCategoryIdLevel2());
            orderItem.setCategoryPathName(skuInfoVo.getCategoryPathName());
            orderItem.setSkuId(skuInfoVo.getSkuId());
            orderItem.setSpuId(skuInfoVo.getSpuId());
            orderItem.setSpuName(skuInfoVo.getSpuName());
            orderItem.setSupplierSpuId(skuInfoVo.getSupplierSpuId());
            orderItem.setSupplierSkuId(skuInfoVo.getId());
            orderItem.setSupplierId(skuInfoVo.getSupplierId());
            orderItem.setSupplierDeptId(skuInfoVo.getSupplierDeptId());
            orderItem.setBusinessType(skuInfoVo.getBusinessType());
            orderItem.setBuyerCode(skuInfoVo.getBuyerCode());
            orderItem.setBuyerName(skuInfoVo.getBuyerName());
            orderItem.setImgUrl(skuInfoVo.getImgUrl());
            orderItem.setSaleDate(saleDate);
            orderItem.setSpuGrossWeight(skuInfoVo.getSpuGrossWeight());
            totalGrossWeight = totalGrossWeight.add(skuInfoVo.getSpuGrossWeight().multiply(new BigDecimal(orderItem.getCount())));
            orderItem.setSpuNetWeight(skuInfoVo.getSpuNetWeight());
            totalNetWeight = totalNetWeight.add(skuInfoVo.getSpuNetWeight().multiply(new BigDecimal(orderItem.getCount())));
            orderItem.setCount(sku.getCount());
            orderItem.setPrice(skuInfoVo.getPrice());
            orderItem.setFinalPrice(skuInfoVo.getPrice());
            orderItem.setProductAmount(skuInfoVo.getPrice().multiply(new BigDecimal(sku.getCount())));
            orderItem.setAfterSaleDay(skuInfoVo.getAfterSaleDay());
            orderItem.setAfterSaleType(skuInfoVo.getAfterSaleType());
            if (skuInfoVo.getAfterSaleDay() == null || skuInfoVo.getAfterSaleDay() == 0) {
                orderItem.setAfterSaleStatus(ItemAfterSaleStatusEnum.CAN_NOT_AFTER_SALE.getCode());
            } else {
                orderItem.setAfterSaleStatus(ItemAfterSaleStatusEnum.CAN_AFTER_SALE.getCode());
            }
            if (Objects.equals(skuInfoVo.getBusinessType(), SupplierSkuBusinessTypeEnum.BUSINESS_TYPE20.getCode())) {
                RemoteFreightLogisticsVo freightLogisticsVo = freightLogisticsVoMap.get(String.format("%s_%s", skuInfoVo.getProvideRegionWhId(), skuInfoVo.getRegionWhId()));
                orderItem.setProvideLogisticsId(freightLogisticsVo.getId());
                orderItem.setProvideRegionWhId(skuInfoVo.getProvideRegionWhId());
            } else {
                orderItem.setProvideLogisticsId(0L);
                orderItem.setProvideRegionWhId(0L);
            }
            orderItemList.add(orderItem);
        }

        add.setCode(CustomNoUtil.getOrderNo(saleDate));
        add.setCustomerId(user.getRelationId());
        add.setTotalGrossWeight(totalGrossWeight);
        add.setTotalNetWeight(totalNetWeight);
        //订单类型
        Set<Integer> businessTypeSet = orderItemList.stream().map(OrderItem::getBusinessType).collect(Collectors.toSet());
        if (Objects.equals(businessTypeSet.size(), 1)) {
            add.setBusinessType(businessTypeSet.stream().findFirst().orElse(0));
        } else {
            add.setBusinessType(OrderBusinessTypeEnum.BUSINESS_TYPE0.getCode());
        }

        //新增订单
        orderMapper.insert(add);
        //库存
        List<RemoteUpdateStockBo> updateStockList = new ArrayList<>();
        for (OrderItem orderItem : orderItemList) {
            orderItem.setOrderId(add.getId());
            orderItem.setOrderCode(add.getCode());
        }
        //新增订单项
        orderItemMapper.batchInsert(orderItemList);

        //插入活动信息
        insertActivityRecord(amountDetailVo.getActivityRecordList(), orderItemList);
        //支付单
        orderPayService.orderCreate(add.getId());

        //清除购物车
        iCartItemService.batchDeleteCartItemByRegionWhId(bo.getRegionWhId());
        //更新客户常用提货点
        RemoteBasCustomerUsedPlaceAddBo placeAddBo = new RemoteBasCustomerUsedPlaceAddBo();
        placeAddBo.setCustomerId(add.getCustomerId());
        placeAddBo.setPlaceId(add.getPlaceIdLevel2() != null && add.getPlaceIdLevel2() > 0 ? add.getPlaceIdLevel2() : add.getPlaceId());
        remoteBasCustomerService.addUsedPlace(placeAddBo);
        RemoteOrderCancelBo msg = new RemoteOrderCancelBo();
        msg.setOrderId(add.getId());
        msg.setCancelType(OrderCancelTypeEnum.TIMEOUT.getCode());
        msg.setPayChannel(add.getPayChannel());
        //发送一条30分钟（nacos动态配置）后执行的延时消息，执行取消订单操作，如果订单已经支付，不做处理，如果订单没有支付，取消订单
        orderCancelProducer.send(msg);

        //减吨位
        RemoteRegionLogisticsPlanOrderWeightBo logisticsPlan = new RemoteRegionLogisticsPlanOrderWeightBo();
        logisticsPlan.setOrderId(add.getId());
        logisticsPlan.setOrderCode(add.getCode());
        logisticsPlan.setLogisticsId(add.getLogisticsId());
        logisticsPlan.setPlanDate(saleDate);
        logisticsPlan.setOrderWeight(add.getTotalGrossWeight());

        //查询基础物流
        RemoteRegionLogisticsPlanQueryBo planBo = new RemoteRegionLogisticsPlanQueryBo();
        planBo.setPlanDate(saleDate);
        planBo.setLogisticsId(logisticsVo.getId());
        List<RemoteRegionLogisticsPlanVo> planVoList = remoteRegionLogisticsPlanService.queryList(planBo);
        if (planVoList == null || planVoList.size() == 0) {
            throw new ServiceException("未配置基础吨位，请联系城市仓人员进行吨位配置");
        }
        RemoteRegionLogisticsPlanVo planVo = planVoList.get(0);
        if (planVo.getPlanWeight().compareTo(planVo.getOrderWeight().add(totalGrossWeight)) < 0) {
            throw new ServiceException("基础吨位不足，请联系城市仓人员进行吨位配置");
        }

        List<RemoteSupplierSkuStockVo> remoteSupplierSkuStockVos = remoteSupplierSkuService.listSupplierSkuStockBySupplierIds(skuIdList);
        Map<Long, Integer> stockMap = remoteSupplierSkuStockVos.stream().collect(Collectors.toMap(RemoteSupplierSkuStockVo::getSupplierSkuId, RemoteSupplierSkuStockVo::getStock));
        boolean anyMatch = orderItemList.stream().anyMatch(item -> stockMap.getOrDefault(item.getSupplierSkuId(), 0) < item.getCount());
        if (anyMatch) {
            throw new ServiceException("库存不足，请重新下单");
        }
        //减吨位
        remoteRegionLogisticsPlanService.updateOrderWeight(logisticsPlan);
        for (OrderItem orderItem : orderItemList) {
            RemoteUpdateStockBo updateStockBo = new RemoteUpdateStockBo();
            updateStockBo.setSupplierSkuId(orderItem.getSupplierSkuId());
            updateStockBo.setOrderId(add.getId());
            updateStockBo.setOrderItemId(orderItem.getId());
            updateStockBo.setOrderCode(add.getCode());
//            updateStockBo.setSold(orderItem.getCount());
            updateStockBo.setBackStock(-orderItem.getCount());
            updateStockBo.setLockStock(orderItem.getCount());
            updateStockList.add(updateStockBo);
        }
        //批量减少库存，增加已购
        List<RemoteUpdateStockBo> remoteUpdateStockBos = remoteSupplierSkuService.batchUpdateStock(updateStockList, StockTypeEnum.ORDER.getCode());
        addOrderVo.setOrderId(add.getId());
        return addOrderVo;
    }


    /**
     * 新增订单
     */
    @Override
    @GlobalTransactional(lockRetryInterval = 100, lockRetryTimes = 30)
    public AddOrderVo insertByBo(AddOrderBo bo) {
        return SpringUtils.getBean(OrderServiceImpl.class).insertOrderByTransactional(bo);
    }

    /**
     * 插入活动信息
     *
     * @param activityRecordList
     * @param orderItemList
     */
    private void insertActivityRecord(List<ActivityRecord> activityRecordList, List<OrderItem> orderItemList) {
        if (CollectionUtil.isEmpty(activityRecordList)) {
            return;
        }
        Map<Long, OrderItem> orderItemMap = orderItemList.stream().collect(Collectors.toMap(OrderItem::getSupplierSkuId, item -> item, (v1, v2) -> v2));
        List<ActivityRecord> activityRecords = activityRecordList.stream().map(item -> {
            OrderItem orderItem = orderItemMap.get(item.getSupplierSkuId());
            if (Objects.isNull(orderItem)) {
                return null;
            }
            item.setSupplierSpuId(orderItem.getSupplierSpuId());
            item.setOrderItemId(orderItem.getId());
            item.setOrderCode(orderItem.getOrderCode());
            item.setOrderId(orderItem.getOrderId());
            item.setSpuName(orderItem.getSpuName());
            item.setCustomerId(orderItem.getCustomerId());
            return item;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        activityRecordMapper.insertBatch(activityRecords);

    }

    /**
     * 修改订单
     *
     * @param bo
     * @return
     */
    @Override
    public Boolean updateOrder(UpdateOrderBo bo) {
        Order selectById = orderMapper.selectById(bo.getOrderId());
        if (ObjectUtil.isNotNull(selectById)) {
            Order order = new Order();
            order.setId(bo.getOrderId());
            order.setRemark(bo.getRemark());
            orderMapper.updateById(order);
            //改分货单详情
            if (selectById.getBusinessType().equals(OrderBusinessTypeEnum.BUSINESS_TYPE40.getCode())) {
                LambdaQueryWrapper<OrderItem> lqw = Wrappers.lambdaQuery();
                lqw.eq(OrderItem::getOrderId, bo.getOrderId()).select(OrderItem::getId);
                List<OrderItem> orderItems = orderItemMapper.selectList(lqw);
                List<Long> orderItemIds = orderItems.stream().map(OrderItem::getId).toList();
                LambdaQueryWrapper<SortGoodsDetail> lqwd = Wrappers.lambdaQuery();
                lqwd.in(SortGoodsDetail::getOrderItemId, orderItemIds).select(SortGoodsDetail::getId);
                List<SortGoodsDetail> sortGoodsDetails = sortGoodsDetailMapper.selectList(lqwd);
                if (CollectionUtils.isNotEmpty(sortGoodsDetails)) {
                    sortGoodsDetails.forEach(l -> l.setRemark(bo.getRemark()));
                    sortGoodsDetailMapper.updateBatchById(sortGoodsDetails);
                }
            }
        }
        return true;
    }

    /**
     * 订单支付成功回调接口
     *
     * @param bo
     */
    @Override
    @GlobalTransactional(lockRetryInterval = 100, lockRetryTimes = 30)
    public void payUpdateStatus(RemotePayUpdateStatusBo bo) {
        //查询订单
        Order order = orderMapper.selectById(bo.getOrderId());
        if (order == null || order.getDelFlag() != 0) {
            log.keyword("OrderServiceImpl.payUpdateStatus").error("订单不存在:" + JsonUtils.toJsonString(bo));
            return;
        }
        if (OrderStatusEnum.WAIT.getCode().equals(order.getStatus())) {
            //查询订单项
            List<QueryItemListVo> itemList = orderItemMapper.queryItemList(Lists.newArrayList(bo.getOrderId()));
            if (itemList == null || itemList.size() == 0) {
                log.keyword("OrderServiceImpl.payUpdateStatus").error("订单项为空:" + JsonUtils.toJsonString(bo));
                return;
            }
            //检验批次
            List<Long> supplierSkuIdList = itemList.stream().map(QueryItemListVo::getSupplierSkuId).distinct().toList();
            RemoteQueryInfoListBo skuBo = new RemoteQueryInfoListBo();
            skuBo.setSupplierSkuIdList(supplierSkuIdList);
            List<RemoteSupplierSkuInfoVo> supplierSkuList = remoteSupplierSkuService.queryInfoList(skuBo);
            if (supplierSkuList == null || supplierSkuList.size() == 0) {
                log.keyword("OrderServiceImpl.payUpdateStatus").error("供应商商品销售批次不存在:" + JsonUtils.toJsonString(bo));
                return;
            }
//            for(RemoteSupplierSkuInfoVo sku : supplierSkuList) {
//                if(SupplierSkuStatusEnum.STATUS5.getCode().equals(sku.getStatus())) {
//                    log.keyword("OrderServiceImpl.payUpdateStatus").error("供应商商品销售批次已下架:" + JsonUtils.toJsonString(supplierSkuList));
//                    RemoteOrderCancelBo cancelBo = new RemoteOrderCancelBo();
//                    cancelBo.setOrderId(order.getId());
//                    cancelBo.setOrderCode(order.getCode());
//                    cancelBo.setCancelType(OrderCancelTypeEnum.SKU_DOWN.getCode());
//                    orderCancelProducer.send(cancelBo);
//                    return;
//                }
//            }

            //修改订单状态
            Order update = new Order();
            update.setId(bo.getOrderId());
            update.setStatus(OrderStatusEnum.ALREADY.getCode());
            update.setPayChannel(bo.getPayChannel());
            update.setPayTime(bo.getPayTime());
            orderMapper.updateById(update);
            orderItemMapper.payUpdateStatus(bo);

            //库存
            //批量减少库存，增加已购
            List<RemoteUpdateStockBo> updateStockList = new ArrayList<>();
            for (QueryItemListVo orderItem : itemList) {
                RemoteUpdateStockBo updateStockBo = new RemoteUpdateStockBo();
                updateStockBo.setSupplierSkuId(orderItem.getSupplierSkuId());
                updateStockBo.setOrderId(orderItem.getOrderId());
                updateStockBo.setOrderCode(orderItem.getOrderCode());
                updateStockBo.setOrderItemId(orderItem.getId());
                updateStockBo.setSold(orderItem.getCount());
//                updateStockBo.setBackStock(-orderItem.getCount());
                updateStockBo.setLockStock(-orderItem.getCount());
                updateStockList.add(updateStockBo);
            }
            remoteSupplierSkuService.batchUpdateStock(updateStockList, StockTypeEnum.OTHER.getCode());
            //优惠券锁定
            couponUserService.batchUpdateCouponUser(order.getCode());
            //回调，修改支付单状态
            orderPayService.payCall(order.getCode());
            //触发统计
            customerStatisticsService.orderTrigger(order.getCustomerId());
            //生成拍子号
            List<String> regionsList = Arrays.stream(orderProperties.getDeliveryNumberAutoRegions().split(";")).toList();
            if (CollectionUtil.isNotEmpty(regionsList) && regionsList.contains(order.getRegionWhId().toString())) {
                orderDeliveryNumberProducer.send(order.getId());
            }
        } else {
            log.keyword("OrderServiceImpl.payUpdateStatus").error("订单状态不是待支付:" + JsonUtils.toJsonString(bo));
        }
    }

    /**
     * 支付成功后不管后面订单会不会取消，都要先加入支付信息
     *
     * @param bo
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePayInfo(RemotePayUpdateStatusBo bo) {
        Order update = new Order();
        update.setId(bo.getOrderId());
        update.setPayChannel(bo.getPayChannel());
        update.setPayTime(bo.getPayTime());
        orderMapper.updateById(update);
        orderItemMapper.updatePayInfo(bo);
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelExecuteTransactional(Long orderId, String cancelType) {
        Order order = orderMapper.selectById(orderId);
        if (order == null || order.getDelFlag() != 0) {
            log.keyword("OrderServiceImpl.cancel").info("订单不存在:" + orderId);
            return false;
        }
        if (OrderStatusEnum.ALREADY.getCode().equals(order.getStatus())) {
            if (OrderCancelTypeEnum.TIMEOUT.getCode().equals(cancelType)) {
                log.keyword("OrderServiceImpl.cancel").info("已支付订单不能再超时未支付:" + orderId);
                return false;
            }
            if (System.currentTimeMillis() - order.getPayTime().getTime() > orderProperties.getCancelTime()) {
                log.keyword("OrderServiceImpl.cancel").info("超过订单取消时间:" + orderId);
                return false;
            }
        }
        if (OrderStatusEnum.CANCEL.getCode().equals(order.getStatus())) {
            log.keyword("OrderServiceImpl.cancel").info("订单已取消:" + orderId);
            return false;
        }
        if (OrderStatusEnum.FINISH.getCode().equals(order.getStatus())) {
            log.keyword("OrderServiceImpl.cancel").info("订单已完成:" + orderId);
            return false;
        }
        Order updateOrder = new Order();
        updateOrder.setId(orderId);
        updateOrder.setStatus(OrderStatusEnum.CANCEL.getCode());
        updateOrder.setCancelType(cancelType);
        updateOrder.setCancelTime(new Date());
        orderMapper.updateById(updateOrder);
        orderItemMapper.cancel(orderId, cancelType);
        //批量增加库存，减少已购
        List<QueryItemListVo> itemList = orderItemMapper.queryItemList(Lists.newArrayList(orderId));
        if (itemList == null || itemList.size() == 0) {
            log.keyword("OrderServiceImpl.payUpdateStatus").error("订单项为空:" + orderId);
            return false;
        }
        //检查取消订单是否需要回退库存
        boolean boo = checkCancelStock(order.getRegionWhId());
        //处理库存
        List<RemoteUpdateStockBo> updateStockList = new ArrayList<>();
        for (QueryItemListVo item : itemList) {
            RemoteUpdateStockBo updateStockBo = new RemoteUpdateStockBo();
            updateStockBo.setSupplierSkuId(item.getSupplierSkuId());
            updateStockBo.setOrderId(item.getOrderId());
            updateStockBo.setOrderCode(item.getOrderCode());
            updateStockBo.setOrderItemId(item.getId());
            if (OrderStatusEnum.WAIT.getCode().equals(order.getStatus())) {
                updateStockBo.setLockStock(-item.getCount());
            } else {
                updateStockBo.setSold(-item.getCount());
            }
            if (boo) {
                updateStockBo.setBackStock(item.getCount());
            }
            updateStockList.add(updateStockBo);
        }

        if (OrderStatusEnum.WAIT.getCode().equals(order.getStatus())) {
            if (OrderCancelTypeEnum.SKU_DOWN.getCode().equals(cancelType)
                    || OrderCancelTypeEnum.LOGISTICS_OUT.getCode().equals(cancelType)
                    || OrderCancelTypeEnum.STOCK_OUT.getCode().equals(cancelType)) {
                //关闭已支付订单，整单退款
                orderPayService.cancelOrderPay(order.getCode(), order.getId());
            } else {
                //关闭未支付订单
                orderPayService.closeOrderPay(order.getCode());
            }
        } else {
            //关闭已支付订单，整单退款
            orderPayService.cancelOrderPay(order.getCode(), order.getId());
        }
        //根据订单code回退优惠券
        couponUserService.restoreCouponByBizNo(order.getCode());
        //
        remoteSupplierSkuService.batchUpdateStock(updateStockList, StockTypeEnum.CANCEL.getCode());
        //判断是否加吨位

        //加吨位
        RemoteRegionLogisticsPlanOrderWeightBo logisticsPlan = new RemoteRegionLogisticsPlanOrderWeightBo();
        logisticsPlan.setOrderId(order.getId());
        logisticsPlan.setOrderCode(order.getCode());
        logisticsPlan.setLogisticsId(order.getLogisticsId());
        logisticsPlan.setPlanDate(order.getSaleDate());
        logisticsPlan.setOrderWeight(BigDecimal.ZERO.subtract(order.getTotalGrossWeight()));
        //检查取消订单是否需要回滚计划吨位(先去掉)
//        if (checkCancelPlanOrderWeight(order.getLogisticsId())){
//            logisticsPlan.setPlanWeight(BigDecimal.ZERO.subtract(order.getTotalGrossWeight()));
//        }
        remoteRegionLogisticsPlanService.updateOrderWeight(logisticsPlan);

        // 通知分销取消
        cancelDistributionOrder(order, cancelType);
        return true;
    }

    private void cancelDistributionOrder(Order order, String cancelType) {
        RemoteOrderChangeBo remoteOrderChangeBo = new RemoteOrderChangeBo(OrderStatusEnum.CANCEL.getCode(), order.getId(), order.getCode(), OrderStatusEnum.CANCEL.getCode(), cancelType);
        orderChangeProducer.send(remoteOrderChangeBo);
        TransactionUtil.afterCommit(() -> {
            try {
                distributionService.cancelOrder(order.getId(), order.getCustomerId(), order.getCancelTime());
            } catch (Exception e) {
                log.keyword("distributionService", "cancelOrder", order.getId()).error(e.getMessage(), e);
            }
        });
    }

    @GlobalTransactional(lockRetryInterval = 100, lockRetryTimes = 30)
    @Override
    public Boolean cancelExecute(Long orderId, String cancelType) {
        return SpringUtil.getBean(OrderServiceImpl.class).cancelExecuteTransactional(orderId, cancelType);
    }


    /**
     * 取消订单
     */
    @Override
    @Lock4j(name = OrderCacheNames.UPDATE_ORDER_STATUS, keys = "#orderId", expire = 30000, acquireTimeout = 1000)
    public Boolean cancel(Long orderId, String cancelType) {
        return SpringUtils.getBean(IOrderService.class).cancelExecute(orderId, cancelType);
    }

    /**
     * 检查取消订单是否需要回退库存
     *
     * @param regionWhId
     * @return
     */
    private boolean checkCancelStock(Long regionWhId) {
        RemoteRegionWhVo regionWhVo = remoteRegionWhService.queryById(regionWhId);
        if (regionWhVo == null) {
            log.keyword("OrderServiceImpl.checkCancelStock").error("总仓不存在id:" + regionWhId);
            return false;
        }
        String clearWhTimeStr = regionWhVo.getClearWhTime().replace(":", "");
        ;
        String salesTimeStartStr = regionWhVo.getSalesTimeStart().replace(":", "");
        String nowDateStr = DateUtils.getHHMM();
        if (nowDateStr.compareTo(clearWhTimeStr) >= 0 && nowDateStr.compareTo(salesTimeStartStr) < 0) {
            return false;
        }
        return true;
    }

    /**
     * 检查取消订单是否需要回滚吨位
     * @param logisticsId
     * @return
     */
    private boolean checkCancelPlanOrderWeight(Long logisticsId) {
        //查询物流线
        RemoteRegionLogisticsQueryBo logisticsQueryBo = new RemoteRegionLogisticsQueryBo();
        logisticsQueryBo.setLogisticsId(logisticsId);
        List<RemoteRegionLogisticsVo> logisticsVoList = remoteRegionLogisticsService.queryList(logisticsQueryBo);
        if (CollectionUtil.isEmpty(logisticsVoList)){
            return false;
        }
        RemoteRegionLogisticsVo remoteRegionLogisticsVo = logisticsVoList.get(0);
        if (StringUtils.isBlank(remoteRegionLogisticsVo.getSalesTimeEnd()) || Objects.isNull(remoteRegionLogisticsVo.getCurrentIsEarlyEnd())){
            return false;
        }
        Integer currentIsEarlyEnd  = remoteRegionLogisticsVo.getCurrentIsEarlyEnd() ;
        String salesTimeEnd = remoteRegionLogisticsVo.getSalesTimeEnd().replace(":", "");;
        String nowDateStr = DateUtils.getHHMM();
        if(Objects.equals(currentIsEarlyEnd, 1) && nowDateStr.compareTo(salesTimeEnd) >= 0) {
            return true;
        }
        return false;
    }

    /**
     * 支付后，获取待送货列表延时多久才能查到
     *
     * @return
     */
    @Override
    public Date getDelayQueryTime() {
        // 获取当前时间
        Calendar calendar = Calendar.getInstance();
        // 增加五分钟
        calendar.add(Calendar.MILLISECOND, (int) -orderProperties.getCancelTime());
        // 转换为Date对象
        return calendar.getTime();
    }

    /**
     * 客户订单列表
     *
     * @param bo
     * @return
     */
    @Override
    public TableDataInfo<CustomerOrderVO> customerOrderPage(CustomerOrderDTO bo) {
        //根据客户名称获取客户id
        if (bo.getCustomerId() == null && StringUtils.isNotBlank(bo.getCustomerShopName())) {
            List<Long> customerIdList = remoteBasCustomerService.getByName(bo.getCustomerShopName());
            if (ObjectUtil.isEmpty(customerIdList)) {
                return TableDataInfo.build();
            }
            bo.setCustomerIdList(customerIdList);
        }
        String customerPhoneNo = bo.getCustomerPhoneNo();
        //根据手机号码获取客户id
        if (bo.getCustomerId() == null && StringUtils.isNotBlank(customerPhoneNo)) {
            RemoteUserBo userByPhoneNo = remoteUserService.getUserByPhoneNo(customerPhoneNo, SysUserTypeEnum.CUSTOMER_USER.getType());
            if (ObjectUtil.isEmpty(userByPhoneNo)) {
                return TableDataInfo.build();
            }
            Long customerId = remoteBasCustomerService.getByAdminCode(userByPhoneNo.getUserCode());
            List<Long> customerIdList = Optional.ofNullable(bo.getCustomerIdList()).orElse(new ArrayList<>());
            customerIdList.add(customerId);
            bo.setCustomerIdList(customerIdList);
        }
        if (bo.getCustomerId() != null) {
            //查询指定客户
            if (bo.getCustomerIdList() == null) {
                bo.setCustomerIdList(new ArrayList<>());
            }
            bo.getCustomerIdList().add(bo.getCustomerId());
        }
        Page<CustomerOrderVO> page = orderMapper.customerOrderPage(bo, bo.build());
        List<CustomerOrderVO> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return TableDataInfo.build(page);
        }
        List<Long> regionWhIds = records.stream().map(CustomerOrderVO::getRegionWhId).collect(Collectors.toList());
        List<RemoteRegionWhVo> remoteRegionWhVos = remoteRegionWhService.selectRegionWhInfoByIds(regionWhIds);
        Map<Long, LocalDate> regionWhMap = remoteRegionWhVos.stream().collect(Collectors.toMap(RemoteRegionWhVo::getId, item -> SaleDateUtil.getSaleDate(item.getSalesTimeEnd())));
        LoginUser loginUser = LoginHelper.getLoginUser();
        //是否是管理员
        boolean isAdmin = loginUser.getRolePermission().stream().anyMatch(item -> item.endsWith(orderProperties.getFixPriceRoleKey()));
        List<String> buyerCodeList = isAdmin ? null : remoteBuyerService.getBuyerByUserCode(loginUser.getUserCode());

        List<QueryItemListVo> itemList = orderItemMapper.queryItemList(records.stream().map(CustomerOrderVO::getOrderId).collect(Collectors.toList()));
        Map<Long, List<QueryItemListVo>> itemMap = itemList.stream().collect(Collectors.groupingBy(QueryItemListVo::getOrderId));

        List<Long> customerIds = records.stream().map(CustomerOrderVO::getCustomerId).collect(Collectors.toList());
        List<RemoteCustomerVo> remoteCustomerVos = remoteBasCustomerService.getByIds(customerIds);
        Map<Long, RemoteCustomerVo> customerVoMap = remoteCustomerVos.stream().collect(Collectors.toMap(RemoteCustomerVo::getId, item -> item));
        for (CustomerOrderVO record : records) {
            List<QueryItemListVo> queryItemListVos = itemMap.get(record.getOrderId());
            List<CustomerOrderItemVO> customerOrderItemVOS = BeanUtil.copyToList(queryItemListVos, CustomerOrderItemVO.class);
            record.setCustomerOrderItemVOList(customerOrderItemVOS);
            long residuePayMillisecond = getResiduePayMillisecond(record.getPayChannel(), record.getCreateTime());
            if (residuePayMillisecond > 0) {
                record.setResiduePayMillisecond(residuePayMillisecond);
            }
            RemoteCustomerVo remoteCustomerVo = customerVoMap.getOrDefault(record.getCustomerId(), new RemoteCustomerVo());
            record.setCustomerShopName(remoteCustomerVo.getName());
            record.setCustomerAlias(remoteCustomerVo.getAlias());

            List<CustomerOrderItemVO> customerOrderItemVOList = record.getCustomerOrderItemVOList();
            //权限-组长可以调全部，组员只能调自己
            boolean role = customerOrderItemVOList.stream().anyMatch(item -> CollectionUtils.isEmpty(buyerCodeList) || buyerCodeList.contains(item.getBuyerCode()));
            LocalDate localDate = regionWhMap.get(record.getRegionWhId());
            //已支付和待支付才能调价,配货订单不能调价
            Boolean orderStatus = (Objects.equals(record.getStatus(), OrderStatusEnum.WAIT.getCode()) || Objects.equals(record.getStatus(), OrderStatusEnum.ALREADY.getCode()))
                    && !Objects.equals(record.getBusinessType(), OrderBusinessTypeEnum.BUSINESS_TYPE40.getCode());
            Boolean isShowButton = role && orderStatus && Objects.equals(localDate, record.getSaleDate());
            record.setIsShowButton(isShowButton);
        }
        return TableDataInfo.build(page);
    }

    private long getResiduePayMillisecond(String payChannel, Date createTime) {
        long residue = orderProperties.getResiduePayTime();
        if (PayChannelEnum.PAY_PINGAN_CLOUD_LARGE.getCode().equals(payChannel)) {
            residue = orderProperties.getResiduePayTimeLarge();
        }
        return residue - (System.currentTimeMillis() - createTime.getTime());
    }

    /**
     * 客户订单详细信息
     *
     * @param orderCode
     * @return
     */
    @Override
    public CustomerOrderVO customerOrderInfo(String orderCode,Long supplierId) {
        CustomerOrderVO customeredOrderVo  = orderMapper.customerOrderInfo(orderCode,null);
        if (Objects.isNull(customeredOrderVo)){
            return null;
        }
        RemoteCustomerVo customerVo = remoteBasCustomerService.getById(customeredOrderVo.getCustomerId());
        customeredOrderVo.setCustomerShopName(customerVo.getName());
        RemoteUserBo userByUserCode = remoteUserService.getUserByUserCode(customerVo.getUserCode());
        if (ObjectUtil.isNotEmpty(userByUserCode)) {
            customeredOrderVo.setCustomerPhoneNo(userByUserCode.getPhoneNo());
        }
        RemoteRegionWhVo remoteRegionWhVo = remoteRegionWhService.queryById(customeredOrderVo.getRegionWhId());
        customeredOrderVo.setRegionWhName(remoteRegionWhVo.getRegionWhName());
        LocalDate saleDate = SaleDateUtil.getSaleDate(remoteRegionWhVo.getSalesTimeEnd());
        Boolean isSaleDate = saleDate.equals(customeredOrderVo.getSaleDate());
        RemoteCityWhVo remoteCityWhVo = remoteCityWhService.queryById(customeredOrderVo.getCityWhId());
        customeredOrderVo.setCityWhName(remoteCityWhVo.getName());
        Long placeId = Objects.isNull(customeredOrderVo.getPlaceIdLevel2()) || Objects.equals(customeredOrderVo.getPlaceIdLevel2(), 0L)
                ? customeredOrderVo.getPlaceId() : customeredOrderVo.getPlaceIdLevel2();
        RemoteCityWhPlaceVo remoteCityWhPlaceVo = remoteCityWhPlaceService.queryById(placeId);
        customeredOrderVo.setPlaceName(remoteCityWhPlaceVo.getPlaceName());

        RemoteRegionLogisticsQueryBo logisticsQueryBo = new RemoteRegionLogisticsQueryBo();
        logisticsQueryBo.setLogisticsIds(CollectionUtil.newArrayList(customeredOrderVo.getLogisticsId()));
        List<RemoteRegionLogisticsVo> logisticsVoList = remoteRegionLogisticsService.queryList(logisticsQueryBo);
        if (CollectionUtil.isNotEmpty(logisticsVoList)) {
            customeredOrderVo.setLogisticsName(logisticsVoList.get(0).getLogisticsName());
        }

        //获取退款信息
        GetInfoVo refundAmountById = iRefundRecordService.getRefundAmountById(customeredOrderVo.getOrderId());
        if (ObjectUtil.isNotNull(refundAmountById)) {
            customeredOrderVo.setDifferenceRefundAmount(refundAmountById.getDifferenceRefundAmount());
            customeredOrderVo.setLossAmount(refundAmountById.getLossAmount());
            customeredOrderVo.setRefundAmount(refundAmountById.getRefundAmount());
        }

        List<CustomerOrderItemVO> customerOrderItemVOList = customeredOrderVo.getCustomerOrderItemVOList();
        if(Objects.nonNull(supplierId)){
            customerOrderItemVOList = customerOrderItemVOList.stream()
                    .filter(item -> supplierId.equals(item.getSupplierId())).collect(Collectors.toList());
        }
        if (CollectionUtil.isEmpty(customerOrderItemVOList)){
            return customeredOrderVo;
        }
        List<Long> skuIds = customerOrderItemVOList.stream().map(CustomerOrderItemVO::getSupplierSkuId).distinct().collect(Collectors.toList());
        RemoteQueryInfoListBo bo = new RemoteQueryInfoListBo();
        bo.setSupplierSkuIdList(skuIds);
        List<RemoteSupplierSkuInfoVo> remoteSupplierDisableCityWhVos = remoteSupplierSkuService.queryInfoList(bo);
        Map<Long, RemoteSupplierSkuInfoVo> cityWhVoMap = remoteSupplierDisableCityWhVos.stream()
                .collect(Collectors.toMap(RemoteSupplierSkuInfoVo::getId, item -> item, (v1, v2) -> v2));
        LoginUser loginUser = LoginHelper.getLoginUser();
        //是否是管理员
        boolean isAdmin = loginUser.getRolePermission().stream().anyMatch(item -> item.endsWith(orderProperties.getFixPriceRoleKey()));
        List<String> buyerCodeList = isAdmin ? null : remoteBuyerService.getBuyerByUserCode(loginUser.getUserCode());
        List<Long> supplierDeptIds = customerOrderItemVOList.stream().map(CustomerOrderItemVO::getSupplierDeptId).distinct().collect(Collectors.toList());
        Map<Long, String> deptNameMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(supplierDeptIds)) {
            deptNameMap = remoteDeptService.getDeptNameMap(com.alibaba.nacos.shaded.com.google.common.collect.Lists.newArrayList(supplierDeptIds));
        }
        List<Long> supplierIds = customerOrderItemVOList.stream().map(CustomerOrderItemVO::getSupplierId).distinct().collect(Collectors.toList());
        List<RemoteSupplierVo> supplierByIds = remoteSupplierService.getSupplierByIds(supplierIds);
        Map<Long, String> supplierMap = supplierByIds.stream().collect(Collectors.toMap(RemoteSupplierVo::getId, RemoteSupplierVo::getName, (v1, v2) -> v2));
        RemoteQuerySupplierSkuFileBo fileBo = new RemoteQuerySupplierSkuFileBo();
        fileBo.setSupplierSkuIdList(skuIds);
        List<RemoteSupplierSkuFileVo> fileVos = remoteSupplierSkuService.querySupplierSkuFile(fileBo);
        Map<Long, List<RemoteSupplierSkuFileVo>> skuFileMap = fileVos.stream().collect(Collectors.groupingBy(RemoteSupplierSkuFileVo::getSupplierSkuId, Collectors.toList()));

        //已支付和待支付才能调价
        Boolean orderStatus = (Objects.equals(customeredOrderVo.getStatus(), OrderStatusEnum.WAIT.getCode()) || Objects.equals(customeredOrderVo.getStatus(), OrderStatusEnum.ALREADY.getCode()))
                && !Objects.equals(customeredOrderVo.getBusinessType(), OrderBusinessTypeEnum.BUSINESS_TYPE40.getCode());
        Map<Long, RemoteCouponUserInfoVO> couponUserInfoVOMap = new HashMap<>();
        Map<Long, ActivityRecord> activityRecordMap = new HashMap<>();
        List<ActivityRecord> activityRecords = activityRecordMapper.selectList(new LambdaQueryWrapper<ActivityRecord>()
                .eq(ActivityRecord::getOrderId, customeredOrderVo.getOrderId())
                .eq(ActivityRecord::getType, ActivityRecordTypeEnum.COUPON.getCode()));
        if (CollectionUtil.isNotEmpty(activityRecords)) {
            activityRecordMap = activityRecords.stream().collect(Collectors.toMap(ActivityRecord::getOrderItemId, Function.identity(), (key1, key2) -> key2));
            List<Long> couponUserIds = activityRecords.stream().map(ActivityRecord::getActivityId).collect(Collectors.toList());
            List<RemoteCouponUserInfoVO> couponUserInfoVOS = couponUserService.getCouponUser(couponUserIds);
            couponUserInfoVOMap = couponUserInfoVOS.stream()
                    .collect(Collectors.toMap(RemoteCouponUserInfoVO::getCouponUserId, Function.identity(), (key1, key2) -> key2));
        }

        for (CustomerOrderItemVO item : customerOrderItemVOList) {

            ActivityRecord activityRecord = activityRecordMap.get(item.getId());
            if (Objects.nonNull(activityRecord)) {
                RemoteCouponUserInfoVO couponUserInfoVO = couponUserInfoVOMap.getOrDefault(activityRecord.getActivityId(), new RemoteCouponUserInfoVO());
                if (Objects.equals(couponUserInfoVO.getIsHidden(), 1)) {
                    //如果是隐藏的优惠券，直接减去优惠金额，重置单价
                    BigDecimal productFreeAmount = activityRecord.getProductFreeAmount();
                    item.setFinalPrice(item.getFinalPrice().subtract(productFreeAmount.divide(BigDecimal.valueOf(item.getCount()), 2, RoundingMode.HALF_UP)));
                    item.setProductAmount(item.getProductAmount().subtract(productFreeAmount));
                    customeredOrderVo.setProductFreeAmount(customeredOrderVo.getProductFreeAmount().subtract(productFreeAmount));
                    customeredOrderVo.setFreeTotalAmount(customeredOrderVo.getFreeTotalAmount().subtract(productFreeAmount));
                    customeredOrderVo.setProductAmount(customeredOrderVo.getProductAmount().subtract(productFreeAmount));
                }
            }

            RemoteSupplierSkuInfoVo sku = cityWhVoMap.get(item.getSupplierSkuId());
            item.setProducer(sku.getProducer());
            item.setSpuStandards(sku.getSpuStandards());
            item.setSupplierSpuName(sku.getSpuName());
            item.setSpuGrade(sku.getSpuGrade());
            item.setAfterSaleType(sku.getAfterSaleType());
            item.setSupplierDeptName(deptNameMap.get(item.getSupplierDeptId()));
            item.setBuyMin(sku.getBuyMin());
            item.setBuyMax(sku.getBuyMax());
            // 产地简称
            item.setAreaCode(sku.getAreaCode());
            item.setBrand(sku.getBrand());
            item.setShortProducer(sku.getShortProducer());
            item.setSaleType(sku.getSaleType());
            item.setSupplierName(supplierMap.get(item.getSupplierId()));
            item.setFileList(skuFileMap.get(item.getSupplierSkuId()));
            //权限-组长可以调全部，组员只能调自己
            boolean role = CollectionUtils.isEmpty(buyerCodeList) || buyerCodeList.contains(item.getBuyerCode());
            //商品实付
            BigDecimal productActual = item.getProductAmount().subtract(item.getProductFreeAmount());

            Boolean isShowButton = isSaleDate && role && orderStatus && productActual.compareTo(BigDecimal.ZERO) > 0;
            item.setIsShowButton(isShowButton);
        }
        return customeredOrderVo;
    }

    /**
     * 获取调价订单商品
     *
     * @param orderCode
     * @param orderItemId
     * @return
     */
    @Override
    public List<CustomerOrderItemVO> fixPriceOrderInfo(String orderCode, Long orderItemId) {
        CustomerOrderVO customeredOrderVo = orderMapper.customerOrderInfo(orderCode, orderItemId);
        if (Objects.isNull(customeredOrderVo)) {
            return null;
        }
        List<CustomerOrderItemVO> customerOrderItemVOList = customeredOrderVo.getCustomerOrderItemVOList();
        List<Long> skuIds = customerOrderItemVOList.stream().map(CustomerOrderItemVO::getSupplierSkuId).distinct().collect(Collectors.toList());
        RemoteQueryInfoListBo bo = new RemoteQueryInfoListBo();
        bo.setSupplierSkuIdList(skuIds);
        List<RemoteSupplierSkuInfoVo> remoteSupplierDisableCityWhVos = remoteSupplierSkuService.queryInfoList(bo);
        Map<Long, RemoteSupplierSkuInfoVo> cityWhVoMap = remoteSupplierDisableCityWhVos.stream()
                .collect(Collectors.toMap(RemoteSupplierSkuInfoVo::getId, item -> item, (v1, v2) -> v2));
        LoginUser loginUser = LoginHelper.getLoginUser();
        boolean isAdmin = loginUser.getRolePermission().stream().anyMatch(item -> item.endsWith(orderProperties.getFixPriceRoleKey()));
        List<String> buyerCodeList = isAdmin ? null : remoteBuyerService.getBuyerByUserCode(loginUser.getUserCode());
        //已支付和待支付才能调价
        Boolean orderStatus = (Objects.equals(customeredOrderVo.getStatus(), OrderStatusEnum.WAIT.getCode())
                || Objects.equals(customeredOrderVo.getStatus(), OrderStatusEnum.ALREADY.getCode()))
                && !Objects.equals(customeredOrderVo.getBusinessType(), OrderBusinessTypeEnum.BUSINESS_TYPE40.getCode());
        List<Long> supplierIds = customerOrderItemVOList.stream().map(CustomerOrderItemVO::getSupplierId).distinct().collect(Collectors.toList());
        List<RemoteSupplierVo> supplierByIds = remoteSupplierService.getSupplierByIds(supplierIds);
        Map<Long, String> supplierMap = supplierByIds.stream().collect(Collectors.toMap(RemoteSupplierVo::getId, RemoteSupplierVo::getName, (v1, v2) -> v2));
        List<Long> supplierDeptIds = customerOrderItemVOList.stream().map(CustomerOrderItemVO::getSupplierDeptId).distinct().collect(Collectors.toList());
        Map<Long, String> deptNameMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(supplierDeptIds)) {
            deptNameMap = remoteDeptService.getDeptNameMap(com.alibaba.nacos.shaded.com.google.common.collect.Lists.newArrayList(supplierDeptIds));
        }
        RemoteQuerySupplierSkuFileBo fileBo = new RemoteQuerySupplierSkuFileBo();
        fileBo.setSupplierSkuIdList(skuIds);
        List<RemoteSupplierSkuFileVo> fileVos = remoteSupplierSkuService.querySupplierSkuFile(fileBo);
        Map<Long, List<RemoteSupplierSkuFileVo>> skuFileMap = fileVos.stream().collect(Collectors.groupingBy(RemoteSupplierSkuFileVo::getSupplierSkuId, Collectors.toList()));
        Map<Long, String> finalDeptNameMap = deptNameMap;
        RemoteRegionWhVo remoteRegionWhVo = remoteRegionWhService.queryById(customeredOrderVo.getRegionWhId());
        LocalDate saleDate = SaleDateUtil.getSaleDate(remoteRegionWhVo.getSalesTimeEnd());
        Boolean isSaleDate = saleDate.equals(customeredOrderVo.getSaleDate());
        return customerOrderItemVOList.stream().map(item -> {
            if (Objects.equals(item.getProdType(), OrderProdTypeEnum.GIVE.getCode())) {
                return null;
            }
            RemoteSupplierSkuInfoVo sku = cityWhVoMap.get(item.getSupplierSkuId());
            item.setProducer(sku.getProducer());
            item.setSpuStandards(sku.getSpuStandards());
            item.setSupplierSpuName(sku.getSpuName());
            item.setSupplierDeptName(finalDeptNameMap.get(item.getSupplierDeptId()));
            item.setSpuGrade(sku.getSpuGrade());
            item.setAfterSaleType(sku.getAfterSaleType());
            item.setBuyMin(sku.getBuyMin());
            item.setBuyMax(sku.getBuyMax());
            //权限-组长可以调全部，组员只能调自己
            boolean role = CollectionUtils.isEmpty(buyerCodeList) || buyerCodeList.contains(item.getBuyerCode());

            Boolean isShowButton = role && orderStatus && isSaleDate;
            item.setIsShowButton(isShowButton);
            BigDecimal productActual = item.getProductAmount().subtract(item.getProductFreeAmount());
            item.setPriceFree(item.getFinalPrice().min(productActual.divide(BigDecimal.valueOf(item.getCount()), 2, RoundingMode.HALF_UP)));
            item.setSupplierName(supplierMap.get(item.getSupplierId()));
            item.setFileList(skuFileMap.get(item.getSupplierSkuId()));
            if (!isShowButton) {
                return null;
            }
            return item;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 计算调价信息
     *
     * @param orderFixPriceDTO
     * @return
     */
    @Override
    public OrderFixPriceVO fixPriceInfo(OrderFixPriceDTO orderFixPriceDTO) {
        Order order = orderMapper.selectOne(new LambdaQueryWrapper<Order>().eq(Order::getCode, orderFixPriceDTO.getOrderCode()));

        List<OrderItem> orderItems = orderItemMapper.selectList(new LambdaQueryWrapper<OrderItem>().eq(OrderItem::getOrderId, order.getId()));
        //校验
        checkFixPrice(orderFixPriceDTO, order, orderItems);
        List<OrderFixPriceItemDTO> orderFixPriceItemDTOS = orderFixPriceDTO.getOrderFixPriceItemDTOS();
        //计算价格
        return getOrderFixPriceInfoV2(orderFixPriceItemDTOS, orderItems, order);
    }

    /**
     * 调价计算
     *
     * @param orderFixPriceItemDTOS
     * @param orderItems
     * @param order
     * @return
     */
    private OrderFixPriceVO getOrderFixPriceInfoV2(List<OrderFixPriceItemDTO> orderFixPriceItemDTOS, List<OrderItem> orderItems, Order order) {
        OrderFixPriceVO orderFixPriceVO = new OrderFixPriceVO();
        orderFixPriceVO.setTotalAmount(order.getTotalAmount());
        orderFixPriceVO.setStatus(order.getStatus());
        orderFixPriceVO.setPlatformServiceAmount(order.getPlatformServiceAmount().subtract(order.getPlatformServiceFreeAmount()));
        orderFixPriceVO.setProductAmount(order.getProductAmount().subtract(order.getProductFreeAmount()));
        List<OrderFixPriceItemVO> fixPriceItemVOS = new ArrayList<>();
        Map<Long, OrderItem> orderItemMap = orderItems.stream().collect(Collectors.toMap(OrderItem::getId, item -> item));
        List<Long> orderItemIds = orderItems.stream().map(OrderItem::getId).collect(Collectors.toList());
        //查看退款信息
        List<RefundProductDetail> refundByItemIdList = iRefundRecordService.getRefundByItemIdList(orderItemIds);
        Map<Long, List<RefundProductDetail>> refundItemMap = refundByItemIdList.stream().collect(Collectors.groupingBy(RefundProductDetail::getOrderItemId));
        //修改价格的总优惠
        BigDecimal fixPriceFreeTotal = BigDecimal.ZERO;
        //修改价格的服务费总优惠
        BigDecimal platformServiceFreeAmountTotal = BigDecimal.ZERO;
        for (OrderFixPriceItemDTO orderFixPriceItemDTO : orderFixPriceItemDTOS) {
            OrderFixPriceItemVO priceItemVO = new OrderFixPriceItemVO();
            OrderItem orderItem = orderItemMap.get(orderFixPriceItemDTO.getOrderItemId());
            List<RefundProductDetail> refundProductDetails = refundItemMap.getOrDefault(orderFixPriceItemDTO.getOrderItemId(), Collections.emptyList());
            int stockoutCount = refundProductDetails.stream().filter(item -> Objects.equals(item.getRefundType(), RefundTypeEnum.LACK.getCode())
                    || Objects.equals(item.getRefundType(), RefundTypeEnum.LESS.getCode())).mapToInt(RefundProductDetail::getStockoutCount).sum();

            BigDecimal productActual = orderItem.getProductAmount().subtract(orderItem.getProductFreeAmount());
            BigDecimal price = productActual.divide(BigDecimal.valueOf(orderItem.getCount()), 2, RoundingMode.HALF_UP);
            BigDecimal minPrice = price.min(orderItem.getFinalPrice());
            if (minPrice.compareTo(priceItemVO.getFixPrice()) == 0) {
                continue;
            }
            if (Objects.isNull(orderItem)) {
                throw new ServiceException("调价失败,订单详情不存在!");
            }
            BigDecimal validCount = BigDecimal.valueOf(orderItem.getCount() - stockoutCount);
            //合计退款金额
            BigDecimal productRefundAmount = refundProductDetails.stream().map(RefundProductDetail::getRefundProductAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            //剩余金额
            BigDecimal productSurplusAmount = productActual.subtract(productRefundAmount);
            //商品实付
            BigDecimal productAuctual = orderItem.getProductAmount().subtract(orderItem.getProductFreeAmount());
            BigDecimal fixPrice = orderFixPriceItemDTO.getFixPrice();

            BigDecimal fixProductAuctual = fixPrice.multiply(validCount);
            if (productSurplusAmount.compareTo(minPrice.multiply(validCount).subtract(fixProductAuctual)) < 0) {
                throw new ServiceException("调价失败,0可退款金额小于售后金额！");
            }

            if (fixProductAuctual.compareTo(productAuctual) > 0) {
                throw new ServiceException("调价失败,调价金额不能大于商品实付金额!");
            }
            //改价优惠
            BigDecimal fixPriceFree = minPrice.multiply(validCount).subtract(fixProductAuctual);
            if (fixPriceFree.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            fixPriceFreeTotal = fixPriceFreeTotal.add(fixPriceFree);

            //订单项平台代采费
            OrderSettkeInfBO platformServiceAmtBo = getPlatformServiceAmtBo(fixPrice);
            OrderSettkeInfBO platformServiceFinalPrice = getPlatformServiceAmtBo(orderItem.getFinalPrice());
            BigDecimal settleNumber = orderItem.getSettleNumber();
            //判断改价是否跳级代采费
            BigDecimal platformServiceFreeAmount = BigDecimal.ZERO;
            if (platformServiceFinalPrice.getCount().compareTo(platformServiceAmtBo.getCount()) != 0) {
                BigDecimal fixPlatformService = platformServiceAmtBo.getPrice().multiply(validCount);
                BigDecimal platformServiceAmountAuctual = orderItem.getPlatformServiceAmount();
                platformServiceFreeAmount = platformServiceAmountAuctual.subtract(fixPlatformService);
                //服务费优惠不能大于服务实付
                platformServiceFreeAmount = platformServiceFreeAmount.compareTo(BigDecimal.ZERO) <= 0 ? BigDecimal.ZERO : platformServiceFreeAmount;
                orderItem.setPlatformServiceFreeAmount(orderItem.getPlatformServiceFreeAmount().add(platformServiceFreeAmount));
                orderItem.setFreeTotalAmount(orderItem.getFreeTotalAmount().add(platformServiceFreeAmount));
                orderItem.setTotalAmount(orderItem.getTotalAmount().subtract(platformServiceFreeAmount));
                platformServiceFreeAmountTotal = platformServiceFreeAmountTotal.add(platformServiceFreeAmount);
            }

            priceItemVO.setPrice(minPrice);
            priceItemVO.setFixPrice(fixPrice);

            orderItem.setFixPriceFreeAmount(fixPriceFree);
            orderItem.setProductFreeAmount(orderItem.getProductFreeAmount().add(fixPriceFree));
            orderItem.setFinalPrice(fixPrice);
            orderItem.setFreeTotalAmount(orderItem.getFreeTotalAmount().add(fixPriceFree));
            orderItem.setTotalAmount(orderItem.getTotalAmount().subtract(fixPriceFree));
            priceItemVO.setSupplierSpuName(orderItem.getSpuName());
            order.setFixPriceFreeAmount(order.getFixPriceFreeAmount().add(fixPriceFree));
            order.setProductFreeAmount(order.getProductFreeAmount().add(fixPriceFree));
            order.setPlatformServiceFreeAmount(order.getPlatformServiceFreeAmount().add(platformServiceFreeAmount));
            order.setFreeTotalAmount(order.getFreeTotalAmount().add(fixPriceFree).add(platformServiceFreeAmount));
            order.setTotalAmount(order.getTotalAmount().subtract(fixPriceFree).subtract(platformServiceFreeAmount));
            fixPriceItemVOS.add(priceItemVO);
        }
        if (!Objects.equals(order.getPayChannel(), PayChannelEnum.PAY_PINGAN_CLOUD_LARGE.getCode())) {
            BigDecimal totalAmount = order.getTotalAmount().subtract(order.getFinancialServiceAmount());
            BigDecimal realRate = order.getFinancialServiceRate();
            BigDecimal financialServiceAmount = totalAmount.multiply(realRate).divide(BigDecimal.ONE.subtract(realRate), 2, RoundingMode.HALF_UP);
            order.setFinancialServiceAmount(financialServiceAmount);
            order.setTotalAmount(totalAmount.add(financialServiceAmount));
        }
        //改价分摊金融手续费
        fixPriceFinancialServiceAmount(orderItems, order);
        orderFixPriceVO.setFixTotalAmount(order.getTotalAmount());
        orderFixPriceVO.setRefundProductAmount(fixPriceFreeTotal);
        orderFixPriceVO.setRefundPlatformServiceAmount(platformServiceFreeAmountTotal);
        orderFixPriceVO.setFixTproductAmount(order.getProductAmount().subtract(order.getProductFreeAmount()));
        orderFixPriceVO.setFixTplatformServiceAmount(order.getPlatformServiceAmount().subtract(order.getPlatformServiceFreeAmount()));
        orderFixPriceVO.setOrderFixPriceItemVOList(fixPriceItemVOS);
        return orderFixPriceVO;
    }

    /**
     * 改价分摊金融手续费
     */
    private void fixPriceFinancialServiceAmount(List<OrderItem> orderItems, Order order) {
//金融手续费和总金额为零的时候直接返回
        if (order.getTotalAmount().compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }
        BigDecimal financialServiceAmount = order.getFinancialServiceAmount();
        //金融手续费分摊
        orderItems.sort(Comparator.comparing(OrderItem::getTotalAmount));
        BigDecimal totalOrderAmount = order.getTotalAmount();
        BigDecimal distributedSum = BigDecimal.ZERO;
        // 先按照比例分配
        for (int i = 0; i < orderItems.size(); i++) {
            OrderItem item = orderItems.get(i);
            item.setTotalAmount(item.getTotalAmount().subtract(item.getFinancialServiceAmount()));
            item.setOtherTotalAmount(item.getOtherTotalAmount().subtract(item.getFinancialServiceAmount()));
            BigDecimal ratio = item.getTotalAmount().divide(totalOrderAmount, 10, RoundingMode.HALF_UP);
            BigDecimal amount = ratio.multiply(financialServiceAmount).setScale(2, RoundingMode.HALF_UP);
            //最后一项直接相减
            if (i == orderItems.size() - 1) {
                amount = financialServiceAmount.subtract(distributedSum);
            }
            item.setTotalAmount(item.getTotalAmount().add(amount));
            item.setOtherTotalAmount(item.getOtherTotalAmount().add(amount));
            item.setFinancialServiceAmount(amount);
            distributedSum = distributedSum.add(amount);
        }

    }

    /**
     * 确认调价
     *
     * @param orderFixPriceDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void affirmFixPrice(OrderFixPriceDTO orderFixPriceDTO) {
        Order order = orderMapper.selectOne(new LambdaQueryWrapper<Order>().eq(Order::getCode, orderFixPriceDTO.getOrderCode()));
//        if (!Objects.equals(order.getStatus(),  orderFixPriceDTO.getStatus())){
//            throw new ServiceException("刷新后重试，订单状态不一致");
//        }
        List<OrderItem> orderItems = orderItemMapper.selectList(new LambdaQueryWrapper<OrderItem>().eq(OrderItem::getOrderId, order.getId()));
        //校验
        checkFixPrice(orderFixPriceDTO, order, orderItems);

        List<OrderFixPriceItemDTO> orderFixPriceItemDTOS = orderFixPriceDTO.getOrderFixPriceItemDTOS();
        //计算价格
        getOrderFixPriceInfoV2(orderFixPriceItemDTOS, orderItems, order);
        //未支付改价
        if (Objects.equals(order.getStatus(), OrderStatusEnum.WAIT.getCode())) {
            this.notPayFixPrice(orderFixPriceDTO, order, orderItems);
        } else {
            //支付后改价
            this.payFixPrice(orderFixPriceDTO);
        }
    }

    public void payFixPrice(OrderFixPriceDTO orderFixPriceDTO) {
        OrderRefundDTO orderRefundDTO = new OrderRefundDTO();
        orderRefundDTO.setOrderFixPriceItemDTOS(orderFixPriceDTO.getOrderFixPriceItemDTOS());
        orderRefundDTO.setRefundType(RefundTypeEnum.DIFFERENCE.getCode());
        orderRefundDTO.setIsFixPrice(true);
        List<OrderRefundDTO.OrderItemIdList> orderItemIdLists = orderFixPriceDTO.getOrderFixPriceItemDTOS().stream().map(item -> {
            OrderRefundDTO.OrderItemIdList orderItemIdList = new OrderRefundDTO.OrderItemIdList();
            orderItemIdList.setOrderItemId(item.getOrderItemId());
            orderItemIdList.setDifRefundType(DifRefundTypeEnum.FIX_PRICE.getCode());
            return orderItemIdList;
        }).collect(Collectors.toList());
        orderRefundDTO.setOrderItemIdList(orderItemIdLists);
        iRefundRecordService.createDifferenceRecord(orderRefundDTO);
    }


    //未支付改价
    public void notPayFixPrice(OrderFixPriceDTO orderFixPriceDTO, Order order, List<OrderItem> orderItems) {
        log.keyword("orderFixPriceInfo").info(JSON.toJSONString(order));
        int update1 = orderMapper.update(order, new LambdaQueryWrapper<Order>()
                .eq(Order::getVersion, order.getVersion())
                .eq(Order::getId, order.getId()));
        if (update1 == 0) {
            throw new ServiceException("订单状态已改变，请重新退款！");
        }
        log.keyword("orderItemsFixPriceInfo").info(JSON.toJSONString(orderItems));
        List<Long> orderItemIds = new ArrayList<>();
        for (OrderItem orderItem : orderItems) {
            BigDecimal distributionAmount = orderItem.getDistributionAmount();
            //商品实付金额
            BigDecimal productActual = orderItem.getProductAmount().subtract(orderItem.getProductFreeAmount());
            if (productActual.compareTo(distributionAmount) < 0) {
                orderItemIds.add(orderItem.getId());
                orderItem.setDistributionAmount(BigDecimal.ZERO);
            }
            int update = orderItemMapper.update(orderItem, new LambdaQueryWrapper<OrderItem>()
                    .eq(OrderItem::getId, orderItem.getId())
                    .eq(OrderItem::getWorkStatus, orderItem.getWorkStatus()));
            if (update == 0) {
                throw new ServiceException("订单状态已改变，请重新退款！");
            }
        }
        //关闭支付单
        orderPayService.closeOrderPay(orderFixPriceDTO.getOrderCode());
        //取消分销信息
        if (CollectionUtil.isNotEmpty(orderItemIds)) {
            distributionService.cancelOrderItem(order.getId(), orderItemIds, SettleStatusEnum.PRICE_CHANGE_CANCELED.getCode());
        }
    }

    /**
     * 订单信息
     *
     * @param orderCode
     * @return
     */
    @Override
    public AdminOrderInfoVo adminOrderInfo(String orderCode) {
        Order order = orderMapper.selectOne(new LambdaQueryWrapper<Order>().eq(Order::getCode, orderCode));
        if (Objects.isNull(order)) {
            return null;
        }
        AdminOrderInfoVo adminOrderInfoVo = BeanUtil.copyProperties(order, AdminOrderInfoVo.class);
        adminOrderInfoVo.setOrderStatusList(this.getOrderStatus(order));
        RemoteCustomerVo customerVo = remoteBasCustomerService.getById(adminOrderInfoVo.getCustomerId());
        //店铺名称
        adminOrderInfoVo.setCustomerName(customerVo.getName());
        //总仓名称
        RemoteRegionWhVo remoteRegionWhVo = remoteRegionWhService.queryById(adminOrderInfoVo.getRegionWhId());
        adminOrderInfoVo.setRegionWhName(remoteRegionWhVo.getRegionWhName());
        //城市仓名称
        RemoteCityWhVo remoteCityWhVo = remoteCityWhService.queryById(adminOrderInfoVo.getCityWhId());
        adminOrderInfoVo.setCityWhName(remoteCityWhVo.getName());

        RemoteRegionLogisticsQueryBo logisticsQueryBo = new RemoteRegionLogisticsQueryBo();
        logisticsQueryBo.setLogisticsIds(CollectionUtil.newArrayList(adminOrderInfoVo.getLogisticsId()));
        List<RemoteRegionLogisticsVo> logisticsVoList = remoteRegionLogisticsService.queryList(logisticsQueryBo);
        if (CollectionUtil.isNotEmpty(logisticsVoList)) {
            //物流名称
            adminOrderInfoVo.setLogisticsName(logisticsVoList.get(0).getLogisticsName());
        }
        //获取结算件数
        Integer settleCount = orderItemMapper.getSettleCount(order.getCode(), orderProperties.getPlatformServiceStandard());
        adminOrderInfoVo.setSettleCount(settleCount);

        //计算退款金额
        AdminOrderInfoVo orderRefundInfo = iRefundRecordService.adminOrderRefund(order.getCode());
        adminOrderInfoVo.setRefundAmount(orderRefundInfo.getRefundAmount());
        adminOrderInfoVo.setLackCount(orderRefundInfo.getLackCount());
        adminOrderInfoVo.setLackAmount(orderRefundInfo.getLackAmount());
        adminOrderInfoVo.setShortCount(orderRefundInfo.getShortCount());
        adminOrderInfoVo.setShortAmount(orderRefundInfo.getShortAmount());
        adminOrderInfoVo.setDifCount(orderRefundInfo.getDifCount());
        adminOrderInfoVo.setDifAmount(orderRefundInfo.getDifAmount());
        adminOrderInfoVo.setLossAmount(orderRefundInfo.getLossAmount());
        adminOrderInfoVo.setRefundServiceAmount(orderRefundInfo.getRefundServiceAmount());
        adminOrderInfoVo.setRefundFinancialServicePrice(orderRefundInfo.getRefundFinancialServicePrice());
        adminOrderInfoVo.setRefundFreightAmount(orderRefundInfo.getRefundFreightAmount());

        //计算报损
        AdminOrderInfoVo lossInfo = reportLossService.getLossInfoByOrderCode(orderCode);
        adminOrderInfoVo.setSupLossAmount(lossInfo.getSupLossAmount());
        adminOrderInfoVo.setCityLossAmount(lossInfo.getCityLossAmount());
        adminOrderInfoVo.setRegionLossAmount(lossInfo.getRegionLossAmount());
        //计算申请报损金额
        BigDecimal applyLossAmount = reportLossService.applyLossAmount(orderCode);
        adminOrderInfoVo.setApplyLossAmount(applyLossAmount);
        return adminOrderInfoVo;
    }

    /**
     * 城市仓吨位查询
     *
     * @param regionWhId
     * @param cityWhId
     * @param saleDate
     * @param pageQuery
     * @return
     */
    @Override
    public TableDataInfo<CityOrderWeightVO> cityWeight(Long regionWhId, Long cityWhId, LocalDate saleDate, PageQuery pageQuery) {
        Page<Object> build = pageQuery.build();
        Date payTime = new Date();
        Page<CityOrderWeightVO> cityOrderWeightVOPage = orderMapper.cityWeight(regionWhId, cityWhId, saleDate, payTime, build);
        List<CityOrderWeightVO> records = cityOrderWeightVOPage.getRecords();
        if (CollectionUtil.isEmpty(records)) {
            return TableDataInfo.build(cityOrderWeightVOPage);
        }
        //城市仓id
        List<Long> cityWhIds = records.stream().map(CityOrderWeightVO::getCityWhId).collect(Collectors.toList());
        //物流线id
        List<Long> logisticsIds = records.stream().map(CityOrderWeightVO::getLogisticsId).collect(Collectors.toList());
        List<RemoteCityWhVo> remoteCityWhVos = remoteCityWhService.queryList(cityWhIds);
        Map<Long, String> cityMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(remoteCityWhVos)) {
            cityMap = remoteCityWhVos.stream().collect(Collectors.toMap(RemoteCityWhVo::getId, RemoteCityWhVo::getName));
        }
        Map<Long, String> parkingap = new HashMap<>();
        List<RemoteRegionWhParkingVo> remoteRegionWhParkingVos = remoteRegionWhService.queryParkingByLogisticsId(logisticsIds);
        if (CollectionUtil.isNotEmpty(remoteRegionWhParkingVos)) {
            parkingap = remoteRegionWhParkingVos.stream().collect(Collectors.toMap(RemoteRegionWhParkingVo::getLogisticsId, RemoteRegionWhParkingVo::getParkingNo));
        }
        RemoteRegionLogisticsQueryBo qb = new RemoteRegionLogisticsQueryBo();
        qb.setLogisticsIds(logisticsIds);
        List<RemoteRegionLogisticsVo> logisticsVoList = remoteRegionLogisticsService.queryList(qb);
        Map<Long, String> logisticsMap = Optional.ofNullable(logisticsVoList).orElseGet(ArrayList::new)
                .stream().collect(Collectors.toMap(RemoteRegionLogisticsVo::getId, RemoteRegionLogisticsVo::getLogisticsName));
        for (CityOrderWeightVO record : records) {
            record.setCityWhName(cityMap.get(record.getCityWhId()));
            record.setParkingNo(parkingap.get(record.getLogisticsId()));
            record.setSpuGrossWeight(record.getSpuGrossWeight().divide(new BigDecimal(2000), 2, RoundingMode.HALF_UP));
            record.setLogisticsName(logisticsMap.get(record.getLogisticsId()));
        }
        return TableDataInfo.build(cityOrderWeightVOPage);
    }

    /**
     * 城市仓销量查询
     *
     * @param regionWhId
     * @param cityWhId
     * @param saleDate
     * @param skuIds
     * @param pageQuery
     * @return
     */
    @Override
    public TableDataInfo<CityOrderWeightVO> citySaleData(Long regionWhId, Long cityWhId, LocalDate saleDate, List<Long> skuIds, PageQuery pageQuery) {
        Date payTime = new Date();
        //获取全部城市仓
        List<RemoteCityWhVo> cityAll = remoteCityWhService.getCityByRegionWhId(regionWhId, cityWhId);
        if (CollectionUtil.isEmpty(cityAll)) {
            return TableDataInfo.build();
        }
        //获取该总仓的全部销售数据
        List<CityOrderWeightVO> cityOrderWeightVOPage = orderMapper.citySaleData(regionWhId, cityWhId, saleDate, payTime, skuIds);

        List<Long> cityIdList = cityOrderWeightVOPage.stream().map(CityOrderWeightVO::getCityWhId).collect(Collectors.toList());
        //构建没有销售数据的对象
        List<CityOrderWeightVO> cityOrderWeightVOS = cityAll.stream().filter(city -> !cityIdList.contains(city.getId()))
                .sorted(Comparator.comparing(RemoteCityWhVo::getId).reversed()).map(item -> {
                    CityOrderWeightVO cityOrderWeightVO = new CityOrderWeightVO();
                    cityOrderWeightVO.setCityWhId(item.getId());
                    return cityOrderWeightVO;
                }).collect(Collectors.toList());
        //合并对象-全部数据
        Page<Object> page = pageQuery.build();
        if (CollUtil.isEmpty(cityOrderWeightVOPage)) {
            cityOrderWeightVOPage = cityOrderWeightVOS;
        } else {
            // 处理排序，默认降序
            if (CollUtil.isEmpty(page.orders()) || !page.orders().get(0).isAsc()) {
                cityOrderWeightVOPage.addAll(cityOrderWeightVOS);
            } else {
                cityOrderWeightVOPage.addAll(0, cityOrderWeightVOS);
            }
        }

        Integer pageNum = pageQuery.getPageNum();
        Integer pageSize = pageQuery.getPageSize();
        int size = cityOrderWeightVOPage.size();
        List<CityOrderWeightVO> rows = cityOrderWeightVOPage.subList(Math.min((pageNum - 1) * pageSize, size), Math.min(pageNum * pageSize, size));

        TableDataInfo<CityOrderWeightVO> tableDataInfo = TableDataInfo.build();
        tableDataInfo.setTotal(cityAll.size());
        tableDataInfo.setRows(rows);

        Map<Long, String> cityMap = cityAll.stream().collect(Collectors.toMap(RemoteCityWhVo::getId, RemoteCityWhVo::getName, (n1, n2) -> n1));

        for (CityOrderWeightVO record : rows) {
            record.setCityWhName(cityMap.get(record.getCityWhId()));
            record.setSpuGrossWeight(record.getSpuGrossWeight().divide(new BigDecimal(2000), 2, RoundingMode.HALF_UP));
        }
        return tableDataInfo;
    }

    /**
     * 城市仓销售数据合计
     *
     * @param regionWhId
     * @param cityWhId
     * @param saleDate
     * @param supplierSkuIds
     * @return
     */
    @Override
    public CityOrderWeightVO citySaleDataTotal(Long regionWhId, Long cityWhId, LocalDate saleDate, List<Long> supplierSkuIds) {
        Date payTime = new Date();
        CityOrderWeightVO cityOrderWeightVO = orderMapper.citySaleDataTotal(regionWhId, cityWhId, saleDate, payTime, supplierSkuIds);
        cityOrderWeightVO.setSpuGrossWeight(cityOrderWeightVO.getSpuGrossWeight().divide(new BigDecimal(2000), 2, RoundingMode.HALF_UP));
        return cityOrderWeightVO;
    }


    /**
     * 订单状态
     *
     * @param order
     * @return
     */
    private List<OrderStatusVo> getOrderStatus(Order order) {
        List<OrderStatusEnum> orderStatusEnums = OrderStatusEnum.getAdminOrderStatus();
        boolean cancel = Objects.equals(order.getStatus(), OrderStatusEnum.CANCEL.getCode());
        boolean finish = Objects.equals(order.getStatus(), OrderStatusEnum.FINISH.getCode());
        return orderStatusEnums.stream().map(item -> {
            OrderStatusVo orderStatusVo = new OrderStatusVo();
            orderStatusVo.setStatus(item.getCode());
            orderStatusVo.setStatusName(item.getAdminDesc());
            boolean status = Objects.equals(item.getCode(), order.getStatus());
            if (Objects.equals(item.getCode(), OrderStatusEnum.WAIT.getCode())) {
                orderStatusVo.setOperatorTime(order.getCreateTime());
            }
            if (status && Objects.equals(item.getCode(), OrderStatusEnum.ALREADY.getCode())) {
                orderStatusVo.setOperatorTime(order.getPayTime());
            }
            if (status && Objects.equals(item.getCode(), OrderStatusEnum.CANCEL.getCode())) {
                orderStatusVo.setOperatorTime(order.getCancelTime());
            }
            if (cancel && Objects.equals(item.getCode(), OrderStatusEnum.FINISH.getCode())) {
                return null;
            }
            if (!cancel && Objects.equals(item.getCode(), OrderStatusEnum.CANCEL.getCode())) {
                return null;
            }
            if (status && finish) {
                orderStatusVo.setOperatorTime(order.getUpdateTime());
            }
            return orderStatusVo;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 校验改价
     *
     * @param orderFixPriceDTO
     * @param order
     * @param orderItems
     */
    private void checkFixPrice(OrderFixPriceDTO orderFixPriceDTO, Order order, List<OrderItem> orderItems) {

        if (!(Objects.equals(order.getStatus(), OrderStatusEnum.WAIT.getCode()) || Objects.equals(order.getStatus(), OrderStatusEnum.ALREADY.getCode()))
                || Objects.equals(order.getBusinessType(), OrderBusinessTypeEnum.BUSINESS_TYPE40.getCode())) {
            throw new ServiceException("调价失败，只有待支付的订单能参与改价！");
        }
        LoginUser loginUser = LoginHelper.getLoginUser();
        //是否是管理员
        boolean isAdmin = loginUser.getRolePermission().stream().anyMatch(item -> item.endsWith(orderProperties.getFixPriceRoleKey()));
        List<String> buyerCodeList = isAdmin ? null : remoteBuyerService.getBuyerByUserCode(loginUser.getUserCode());
        List<OrderFixPriceItemDTO> orderFixPriceItemDTOS = orderFixPriceDTO.getOrderFixPriceItemDTOS();

        Map<Long, OrderItem> orderItemMap = orderItems.stream().collect(Collectors.toMap(OrderItem::getId, item -> item));
        boolean role = orderFixPriceItemDTOS.stream().allMatch(item -> (CollectionUtils.isEmpty(buyerCodeList)
                || buyerCodeList.contains(orderItemMap.get(item.getOrderItemId()).getBuyerCode())));
        if (!role) {
            throw new ServiceException("调价失败，有商品不符合调价条件！");
        }
        boolean anyMatch = orderFixPriceItemDTOS.stream().anyMatch(item -> {
            OrderItem orderItem = orderItemMap.get(item.getOrderItemId());
            BigDecimal productActual = orderItem.getProductAmount().subtract(orderItem.getProductFreeAmount());
            BigDecimal priceFree = productActual.divide(BigDecimal.valueOf(orderItem.getCount()), 2, RoundingMode.HALF_UP);
            return priceFree.compareTo(item.getFixPrice()) < 0 || priceFree.compareTo(BigDecimal.ZERO) <= 0;
        });
        if (anyMatch) {
            throw new ServiceException("调价失败，有商品调价金额不能大于商品原价或改价金额要大于0");
        }
        Map<Long, BigDecimal> finalPriceMap = orderItems.stream().collect(Collectors.toMap(OrderItem::getId, item -> {
            BigDecimal productActual = item.getProductAmount().subtract(item.getProductFreeAmount());
            return productActual.divide(BigDecimal.valueOf(item.getCount()), 2, RoundingMode.HALF_UP);
        }));
        boolean anyMatch1 = orderFixPriceItemDTOS.stream().allMatch(item -> item.getFixPrice()
                .compareTo(finalPriceMap.getOrDefault(item.getOrderItemId(), BigDecimal.ZERO)) == 0);
        if (anyMatch1) {
            throw new ServiceException("改价失败，请正确填写改价价格！");
        }
        LocalDate saleDate = remoteRegionWhService.getRegionSaleDateByDate(order.getRegionWhId(), new Date());
        if (!saleDate.equals(order.getSaleDate())) {
            throw new ServiceException("改价失败，跨销售日不允许改价！");
        }
    }

    /**
     * 计算价格
     *
     * @param orderFixPriceItemDTOS
     * @param orderItems
     * @param order
     * @param fixOrderItemIds
     * @return
     */
    private OrderFixPriceVO getOrderFixPriceInfo(List<OrderFixPriceItemDTO> orderFixPriceItemDTOS, List<OrderItem> orderItems, Order order, List<Long> fixOrderItemIds) {
        Map<Long, OrderItem> orderItemMap = orderItems.stream().collect(Collectors.toMap(OrderItem::getSupplierSkuId, item -> item));

        //构建参数 重新计算价格
        AddOrderBo bo = new AddOrderBo();
        bo.setCityWhId(order.getCityWhId());
        bo.setPlaceId(order.getPlaceId());
        bo.setRegionWhId(order.getRegionWhId());
        List<SkuBo> skuBos = orderItems.stream().map(item -> {
            SkuBo skuBo = new SkuBo();
            skuBo.setCount(item.getCount());
            skuBo.setSkuId(item.getSupplierSkuId());
            return skuBo;
        }).collect(Collectors.toList());
        bo.setSkuList(skuBos);
        bo.setCustomerId(order.getCustomerId());
        List<Long> skuIdList = orderItems.stream().map(OrderItem::getSupplierSkuId).collect(Collectors.toList());
        RemoteQueryInfoListBo skuBo = new RemoteQueryInfoListBo();
        skuBo.setSupplierSkuIdList(skuIdList);
        List<RemoteSupplierSkuInfoVo> skuList = remoteSupplierSkuService.queryInfoList(skuBo);
        if (skuList == null || skuList.size() == 0 || skuList.size() < skuIdList.size()) {
            throw new ServiceException("商品数据有误");
        }
        bo.setSaleDate(skuList.get(0).getSaleDate()); // 设置销售日期， 免运免代需要
        //改价信息
        Map<Long, OrderFixPriceItemDTO> fixItemMap = orderFixPriceItemDTOS.stream().collect(Collectors.toMap(OrderFixPriceItemDTO::getOrderItemId, item -> item));
        //把商品价格替换成改价价格
        Map<Long, RemoteSupplierSkuInfoVo> skuMap = skuList.stream().collect(Collectors.toMap(RemoteSupplierSkuInfoVo::getId, item -> {
            OrderItem orderItem = orderItemMap.get(item.getId());
            OrderFixPriceItemDTO orderFixPriceItemDTO = fixItemMap.get(orderItem.getId());
            item.setPrice(orderItem.getPrice());
            if (Objects.nonNull(orderFixPriceItemDTO)) {
                item.setPrice(orderFixPriceItemDTO.getFixPrice());
            }
            return item;
        }, (key1, key2) -> key2));
        //构建参数 重新计算价格
        RemoteRegionLogisticsVo logisticsVo = null;
        //二级物流
        RemoteRegionLogisticsVo logistics2Vo = null;
        if (bo.getPlaceId() != null) {
            RemoteCityWhPlaceVo remoteCityWhPlaceVo = remoteCityWhPlaceService.queryById(bo.getPlaceId());
            Long placeId = remoteCityWhPlaceVo.getId();
            Long parentPlaceId = remoteCityWhPlaceVo.getParentPlaceId();

            RemoteRegionLogisticsQueryBo logisticsQueryBo = new RemoteRegionLogisticsQueryBo();
            logisticsQueryBo.setRegionWhId(order.getRegionWhId());
            logisticsQueryBo.setCityWhId(order.getCityWhId());
            logisticsQueryBo.setPlaceId(Objects.nonNull(parentPlaceId) ? parentPlaceId : order.getPlaceId());
            List<RemoteRegionLogisticsVo> logisticsVoList = remoteRegionLogisticsService.queryList(logisticsQueryBo);
            if (logisticsVoList == null || logisticsVoList.size() == 0) {
                throw new ServiceException("物流线不存在");
            }
            logisticsVo = logisticsVoList.get(0);

            if (ObjectUtil.isNotEmpty(order.getPlaceIdLevel2()) && !Objects.equals(order.getPlaceIdLevel2(), 0L)) {
                RemoteRegionLogisticsQueryBo logistics = new RemoteRegionLogisticsQueryBo();
                logistics.setCityWhId(bo.getCityWhId());
                logistics.setParentPlaceId(bo.getPlaceId());
                logistics.setPlaceId(order.getPlaceIdLevel2());
                logistics.setLogisticsType(1);
                List<RemoteRegionLogisticsVo> logistics2List = remoteRegionLogisticsService.queryList(logistics);
                if (logistics2List == null || logistics2List.size() == 0) {
                    throw new ServiceException("物流线不存在");
                }
                logistics2Vo = logistics2List.get(0);
            }
        }
        //获取基采物流
        Map<String, RemoteSupplierSkuInfoVo> skuInfoVoMap = skuList.stream().filter(item -> Objects.equals(item.getBusinessType()
                , SupplierSkuBusinessTypeEnum.BUSINESS_TYPE20.getCode())).collect(Collectors
                .toMap(key -> String.format("%s_%s", key.getProvideRegionWhId(), key.getRegionWhId()), item -> item, (key1, key2) -> key2));
        Map<String, RemoteFreightLogisticsVo> freightLogisticsVoMap = skuInfoVoMap.keySet().stream().collect(Collectors.toMap(key -> key, key -> {
            RemoteSupplierSkuInfoVo skuInfoVo = skuInfoVoMap.get(key);
            RemoteFreightLogisticsQueryBo logisticsQueryBo = new RemoteFreightLogisticsQueryBo();
            logisticsQueryBo.setProvideRegionWhId(skuInfoVo.getProvideRegionWhId());
            logisticsQueryBo.setRegionWhId(skuInfoVo.getRegionWhId());
            List<RemoteFreightLogisticsVo> remoteFreightLogisticsVos = remoteFreightLogisticsService.queryList(logisticsQueryBo);
            if (CollectionUtil.isEmpty(remoteFreightLogisticsVos)) {
                throw new ServiceException("基采物流不存在，请联系总仓工作人员");
            }
            return remoteFreightLogisticsVos.get(0);
        }));
        //重新计算价格
        CountAmountDetailVo amountDetailVo = countAmountDetail(bo, skuMap, logisticsVo, logistics2Vo, freightLogisticsVoMap, order.getFinancialServiceRate());
        List<CountAmountDetailVo> itemList = amountDetailVo.getItemList();
        Map<Long, CountAmountDetailVo> countAmountDetailVoMap = itemList.stream().collect(Collectors.toMap(CountAmountDetailVo::getId, item -> item, (v1, v2) -> v1));
        //返回计算改价计算金额
        OrderFixPriceVO orderFixPriceVO = new OrderFixPriceVO();
        orderFixPriceVO.setProductAmount(order.getProductAmount());
        orderFixPriceVO.setPlatformServiceAmount(order.getPlatformServiceAmount().subtract(order.getPlatformServiceFreeAmount()));
        orderFixPriceVO.setTotalAmount(order.getTotalAmount());
        orderFixPriceVO.setFixTproductAmount(amountDetailVo.getProductAmount());
        orderFixPriceVO.setFixTplatformServiceAmount(amountDetailVo.getPlatformServiceAmount().subtract(amountDetailVo.getPlatformServiceFreeAmount()));
        orderFixPriceVO.setFixTotalAmount(amountDetailVo.getTotalAmount());
        amountDetailVo.setId(order.getId());
        BeanUtil.copyProperties(amountDetailVo, order);
        //大额支付
        if (PayChannelEnum.PAY_PINGAN_CLOUD_LARGE.getCode().equals(order.getPayChannel())) {
            order.setTotalAmount(amountDetailVo.getTotalAmountLarge());
            order.setFinancialServiceAmount(BigDecimal.ZERO);
            order.setFinancialServiceRate(BigDecimal.ZERO);
        }
        List<OrderFixPriceItemVO> list = new ArrayList<>();
        for (OrderItem orderItem : orderItems) {
            CountAmountDetailVo countAmountDetailVo = countAmountDetailVoMap.get(orderItem.getSupplierSkuId());
            countAmountDetailVo.setId(orderItem.getId());
            BeanUtil.copyProperties(countAmountDetailVo, orderItem);
            OrderFixPriceItemDTO orderFixPriceItemDTO = fixItemMap.get(orderItem.getId());
            if (Objects.nonNull(orderFixPriceItemDTO)) {
                OrderFixPriceItemVO fixPriceItemVO = new OrderFixPriceItemVO();
                fixPriceItemVO.setPrice(orderItem.getFinalPrice());
                fixPriceItemVO.setFixPrice(fixPriceItemVO.getFixPrice());
                list.add(fixPriceItemVO);
                orderItem.setPrice(orderFixPriceItemDTO.getFixPrice());
                orderItem.setFinalPrice(orderFixPriceItemDTO.getFixPrice());
            }
        }
        orderFixPriceVO.setOrderFixPriceItemVOList(list);
        return orderFixPriceVO;
    }


    /**
     * 查询订单项信息（待送货列表，创建送货单）
     *
     * @param bo
     * @return
     */
    @Override
    public List<QueryDeliverVo> queryDeliver(QueryDeliverBo bo) {
        if (!checkDeliver(bo)) {
            return null;
        }
        if (YNStatusEnum.ENABLE.getCode().equals(bo.getIsOrderItemDelivery())) {
            return orderItemMapper.queryDeliverItem(bo);
        }
        bo.setPayTime(getDelayQueryTime());
        List<QueryDeliverVo> queryDeliverList = orderItemMapper.queryDeliver(bo);
        if (CollUtil.isNotEmpty(queryDeliverList)) {
            List<Long> skuIdList = queryDeliverList.stream().map(QueryDeliverVo::getSupplierSkuId).distinct().toList();
            RemoteQueryInfoListBo skuBo = new RemoteQueryInfoListBo();
            skuBo.setSupplierSkuIdList(skuIdList).setPassAudit(bo.getPassAudit());
            List<RemoteSupplierSkuInfoVo> skuList = remoteSupplierSkuService.queryInfoList(skuBo);
            if (CollUtil.isEmpty(skuList)) {
                return null;
            }
            Map<Long, RemoteSupplierSkuInfoVo> skuMap = skuList.stream().collect(Collectors.toMap(RemoteSupplierSkuInfoVo::getId, Function.identity()));
            queryDeliverList = queryDeliverList.stream().filter(e -> skuMap.get(e.getSupplierSkuId()) != null).collect(Collectors.toList());
            if (CollUtil.isEmpty(queryDeliverList)) {
                return null;
            }
            for (QueryDeliverVo vo : queryDeliverList) {
                RemoteSupplierSkuInfoVo sku = skuMap.get(vo.getSupplierSkuId());
                if (sku == null) {
                    continue;
                }
                vo.setSpuName(sku.getSpuName());
                vo.setSpuGrade(sku.getSpuGrade());
                vo.setSpuStandards(sku.getSpuStandards());
                vo.setProducer(sku.getProducer());
                vo.setPackageWord(sku.getPackageWord());
                vo.setSpuGrossWeight(sku.getSpuGrossWeight());
                vo.setSpuNetWeight(sku.getSpuNetWeight());
                vo.setBuyerCode(sku.getBuyerCode());
                vo.setBuyerName(sku.getBuyerName());
                vo.setAfterSaleDay(sku.getAfterSaleDay());
                vo.setFreightRatio(sku.getFreightRatio());
                vo.setImgUrl(sku.getImgUrl());
                vo.setPrice(sku.getPrice());
                vo.setBatchType(sku.getBatchType());
                vo.setIsCheck(sku.getIsCheck());
//                vo.setBusinessType(sku.getBusinessType());
                vo.setBasPortageTeamId(sku.getBasPortageTeamId());
                // 产地简称
                vo.setAreaCode(sku.getAreaCode());
                vo.setBrand(sku.getBrand());
                vo.setShortProducer(sku.getShortProducer());
                vo.setSaleType(sku.getSaleType());
            }
            // 档口名称
            List<Long> supplierDeptIdList = queryDeliverList.stream().map(e -> e.getSupplierDeptId()).filter(Objects::nonNull).filter(v -> v > 0L).distinct().collect(Collectors.toList());
            Map<Long, String> deptNameMap = getSupplierMap(supplierDeptIdList);
            queryDeliverList.forEach(e -> {
                e.setSupplierDeptName(deptNameMap.get(e.getSupplierDeptId()));
            });
            return queryDeliverList;
        }
        return null;
    }

    private @NotNull Map<Long, String> getSupplierMap(List<Long> supplierDeptIdList) {
        if (CollUtil.isNotEmpty(supplierDeptIdList)) {
            Map<Long, String> deptNameMap = remoteDeptService.getDeptNameMap(supplierDeptIdList);
            return deptNameMap;
        }
        return new HashMap<>();
    }

    /**
     * 检验能不能查看待送货列表的数据,false不可以查看，true可以查看
     *
     * @param bo
     * @return
     */
    private Boolean checkDeliver(QueryDeliverBo bo) {
        if (orderProperties.isCheckDeliver() && OrderDeliverSourceEnum.SUPPLIER.getCode().equals(bo.getSource())) {
            RemoteQueryDeliverBo deliverBo = new RemoteQueryDeliverBo();
            deliverBo.setRegionWhId(bo.getRegionWhId());
            deliverBo.setSupplierId(bo.getSupplierId());
            deliverBo.setDeliverDate(bo.getSaleDate().toString());
            return remoteSupplierService.queryDeliver(deliverBo);
        }
        return true;
    }

    /**
     * 查询订单项信息（总仓待送货列表）
     *
     * @param bo
     * @return
     */
    @Override
    public List<QueryRegionDeliverVo> queryRegionDeliver(QueryDeliverBo bo) {
        if (!checkDeliver(bo)) {
            return null;
        }
        bo.setPayTime(getDelayQueryTime());
        List<QueryRegionDeliverVo> voList = orderItemMapper.queryRegionDeliver(bo);
        // 档口名称
        List<Long> supplierDeptIdList = voList.stream().map(e -> e.getSupplierDeptId()).filter(Objects::nonNull).filter(v -> v > 0L).distinct().collect(Collectors.toList());
        Map<Long, String> deptNameMap = getSupplierMap(supplierDeptIdList);
        voList.forEach(e -> {
            e.setSupplierDeptName(deptNameMap.get(e.getSupplierDeptId()));
        });
        return voList;
    }

    /**
     * 暂不装车列表
     *
     * @param bo
     * @return
     */
    @Override
    public List<QueryDeliverLogisticsVo> queryDeliverLogistics(QueryDeliverBo bo) {
        if (!checkDeliver(bo)) {
            return null;
        }
        bo.setPayTime(getDelayQueryTime());
        return orderItemMapper.queryDeliverLogistics(bo);
    }

    /**
     * 物流线需求分配清单
     *
     * @param bo
     * @return
     */
    @Override
    public List<QueryDeliverLogisticsItemVo> queryDeliverLogisticsItem(QueryDeliverBo bo) {
        if (!checkDeliver(bo)) {
            return null;
        }
        bo.setPayTime(getDelayQueryTime());
        return orderItemMapper.queryDeliverLogisticsItem(bo);
    }

    /**
     * 查询待分货接口（后端调用）
     *
     * @param bo
     * @return
     */
    @Override
    public List<QueryDistributionVo> queryDistribution(QueryDistributionBo bo) {
        if (bo.getOrderId() == null && CollUtil.isEmpty(bo.getOrderItemIdList())) {
            //未指定订单的情况下，如果未指定状态，就默认查询已支付状态
            if (StringUtils.isBlank(bo.getStatus()) && CollectionUtils.isEmpty(bo.getStatusList())) {
                bo.setStatus(OrderStatusEnum.ALREADY.getCode());
                bo.setPayTime(getDelayQueryTime());
            }
        }
        List<QueryDistributionVo> vos = orderMapper.queryDistribution(bo);
        //获取用户名
        if (CollectionUtils.isNotEmpty(vos)) {
            List<Long> userIds = vos.stream().map(QueryDistributionVo::getCustomerId).distinct().toList();
            List<RemoteCustomerVo> customerList = remoteBasCustomerService.getByIds(userIds);
            Map<Long, String> map = customerList.stream().collect(Collectors.toMap(RemoteCustomerVo::getId, RemoteCustomerVo::getName, (v1, v2) -> v1));
            vos.forEach(vo -> {
                vo.setCustomerName(map.get(vo.getCustomerId()));
                vo.getOrderItemList().forEach(l -> l.setCustomerName(vo.getCustomerName()));
            });
        }
        return vos;
    }

    @Override
    public List<QueryDeliveryNumberVo> queryOrderDeliveryNumber(QueryDistributionBo bo) {
        return orderMapper.queryOrderDeliveryNumber(bo);
    }

    /**
     * 换供应商，订单项处理，以及生成转单记录
     *
     * @param bo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean replaceSupplier(RemoteReplaceSupplierBo bo) {
        List<OrderItemVo> oldItemList = orderItemMapper.selectBySupplierSkuId(bo.getOldSupplierSkuId(),
                Lists.newArrayList(OrderStatusEnum.WAIT.getCode(), OrderStatusEnum.ALREADY.getCode()));
        if (oldItemList == null || oldItemList.size() == 0) {
            throw new ServiceException("没有订单项");
        }
        //旧订单项要改状态，并且逻辑删除
//        List<OrderItem> oldUpdateItemList = new ArrayList<>();
        //需要新增的新订单项
        List<OrderItem> newAddItemList = new ArrayList<>();
        int count = 0;
        for (OrderItemVo oldItem : oldItemList) {
            OrderItem oldUpdateItem = new OrderItem();
            oldUpdateItem.setId(oldItem.getId());
            oldUpdateItem.setStatus(OrderStatusEnum.CANCEL.getCode());
            oldUpdateItem.setCancelType(OrderCancelTypeEnum.REPLACE_SUPPLIER.getCode());
            oldUpdateItem.setDelFlag(oldItem.getId());
            oldUpdateItem.setOriginalSupplierId(0L);
            oldUpdateItem.setOriginalOrderItemId(0L);
            orderItemMapper.replaceSupplier(oldUpdateItem);
//            oldUpdateItemList.add(oldUpdateItem);

            if (OrderStatusEnum.WAIT.getCode().equals(oldItem.getStatus())) {
                continue;
            }

            count = count + oldItem.getCount();
            OrderItem newAddItem = MapstructUtils.convert(oldItem, OrderItem.class);
            newAddItemList.add(newAddItem);
            newAddItem.setId(null);
            newAddItem.setSupplierId(bo.getNewSupplierId());
            newAddItem.setSupplierSkuId(bo.getNewSupplierSkuId());
            newAddItem.setOriginalOrderItemId(oldItem.getId());
            newAddItem.setOriginalSupplierId(oldItem.getSupplierId());

            //生成划转单
            RemoteTransferChangeBo changeBo = new RemoteTransferChangeBo();
            changeBo.setSourceOrgId(oldItem.getSupplierId());
            changeBo.setOrgId(bo.getNewSupplierId());
            changeBo.setSourceSkuId(bo.getOldSupplierSkuId());
            changeBo.setSkuId(bo.getNewSupplierSkuId());
            changeBo.setOrderNo(oldItem.getOrderCode());
            changeBo.setTransAmt(oldItem.getProductAmount());
            changeBo.setTransId(oldItem.getId());
            remoteTransferService.transferChange(changeBo);
        }
//        orderItemMapper.updateBatchById(oldUpdateItemList, 1000);
        orderItemMapper.insertBatch(newAddItemList);
        ReplaceSupplierRecord record = new ReplaceSupplierRecord();
        OrderItemVo oldItem = oldItemList.get(0);
        record.setSpuName(oldItem.getSpuName());
        record.setRegionWhId(oldItem.getRegionWhId());
        record.setNewSupplierId(bo.getNewSupplierId());
        record.setOldSupplierId(oldItem.getSupplierId());
        record.setSpuGrossWeight(bo.getSpuGrossWeight());
        record.setSpuNetWeight(bo.getSpuNetWeight());
        record.setSpuStandards(bo.getSpuStandards());
        record.setNewSupplierSkuId(bo.getNewSupplierSkuId());
        record.setOldSupplierSkuId(bo.getOldSupplierSkuId());
        record.setCount(count);
        record.setPrice(bo.getPrice());
        record.setImgUrl(bo.getImgUrl());
        replaceSupplierRecordMapper.insert(record);
        orderPayService.replaceSupplier(bo);
        return true;
    }

    /**
     * 报损的订单商品详情
     *
     * @param orderItemId
     * @return
     */
    @Override
    public GetLossInfoVo getLossInfo(Long orderItemId) {
        GetLossInfoVo infoVo = getCanLossAmount(orderItemId);
        RemoteSupplierSkuInfoVo skuInfoVo = remoteSupplierSkuService.getSimpleById(infoVo.getSupplierSkuId());
        if (skuInfoVo == null) {
            throw new ServiceException("供应商商品批次不存在");
        }
        infoVo.setDeductibleSituation(skuInfoVo.getDeductibleSituation());
        infoVo.setAfterSaleExplain(skuInfoVo.getAfterSaleExplain());
        // 产地简称
        infoVo.setAreaCode(skuInfoVo.getAreaCode());
        infoVo.setBrand(skuInfoVo.getBrand());
        infoVo.setShortProducer(skuInfoVo.getShortProducer());
        infoVo.setSpuStandards(skuInfoVo.getSpuStandards());
        return infoVo;
    }

    /**
     * 计算最大可报损金额
     *
     * @param orderItemId 订单项id
     * @return
     */
    @Override
    public GetLossInfoVo getCanLossAmount(Long orderItemId) {
        OrderItemVo itemVo = orderItemMapper.selectVoById(orderItemId);
        if (itemVo == null) {
            throw new ServiceException("订单项不存在");
        }
        GetLossInfoVo infoVo = MapstructUtils.convert(itemVo, GetLossInfoVo.class);
        BigDecimal weightPrice = itemVo.getFinalPrice().divide(itemVo.getSpuGrossWeight(), 2, RoundingMode.HALF_UP);
        if (weightPrice.compareTo(BigDecimal.ZERO) == 0) {
            weightPrice = new BigDecimal("0.01");
        }
        infoVo.setWeightPrice(weightPrice);
        infoVo.setTotalGrossWeight(itemVo.getSpuGrossWeight().multiply(new BigDecimal(itemVo.getCount())));
        infoVo.setTotalNetWeight(itemVo.getSpuNetWeight().multiply(new BigDecimal(itemVo.getCount())));
        //计算提货数量和可报损重量
        LambdaQueryWrapper<ReceiveGoodsRecordDetail> receiveQuery = new LambdaQueryWrapper<>();
        receiveQuery.eq(ReceiveGoodsRecordDetail::getOrderItemId, itemVo.getId());
        receiveQuery.eq(ReceiveGoodsRecordDetail::getDelFlag, 0);
        List<ReceiveGoodsRecordDetail> receiveList = receiveGoodsDetailMapper.selectList(receiveQuery);
        int receiveCount = 0;
        if (receiveList != null && receiveList.size() > 0) {
            for (ReceiveGoodsRecordDetail receive : receiveList) {
                receiveCount = receiveCount + receive.getActualCount();
            }
            infoVo.setReceiveCount(receiveCount);
            infoVo.setLossWeight(itemVo.getSpuGrossWeight().multiply(new BigDecimal(receiveCount)));
        }
        BigDecimal amount = itemVo.getProductAmount().subtract(itemVo.getSubsidyFreeAmount()).subtract(itemVo.getProductFreeAmount());
        if (amount.compareTo(BigDecimal.ZERO) < 0) {
            amount = BigDecimal.ZERO;
        }
        infoVo.setAmount(amount);
        //计算补贴单价
        BigDecimal subsidyFreePrice = itemVo.getSubsidyFreeAmount().divide(new BigDecimal(itemVo.getCount()), 2, RoundingMode.HALF_UP);
        BigDecimal productFreeAmount = itemVo.getProductFreeAmount().divide(new BigDecimal(itemVo.getCount()), 2, RoundingMode.HALF_UP);
        //计算报损单价
        BigDecimal lossPrice = itemVo.getPrice().subtract(subsidyFreePrice).subtract(productFreeAmount);
        infoVo.setPrice(lossPrice);
        infoVo.setLossPrice(lossPrice);
        //计算最大可报损金额
        BigDecimal lossAmount;
        //全提 取实付金额
        if (itemVo.getCount().equals(receiveCount)) {
            lossAmount = itemVo.getProductAmount().subtract(itemVo.getProductFreeAmount());
        } else {
            lossAmount = lossPrice.multiply(new BigDecimal(receiveCount));
        }
        //获取缺货少货单
        List<StockoutRecordDetail> stockoutRecordDetails = stockoutRecordDetailMapper.selectByOrderItemId(orderItemId, Arrays.asList(1, 2));
        //找出报损退款
        List<RefundRecordVO> lossRefunds = refundRecordMapper.queryByItemId(orderItemId);
        BigDecimal refundDistributionAmount = BigDecimal.ZERO;
        if (CollUtil.isNotEmpty(lossRefunds)) {
            BigDecimal refundAmount = BigDecimal.ZERO;
            BigDecimal lossRefundAmount = BigDecimal.ZERO;

            for (RefundRecordVO refund : lossRefunds) {
                if (!refund.getRefundStatus().equals(RefundStatusEnum.REFUND_FAIL.getCode())) {
                    refundAmount = refundAmount.add(refund.getRefundProductAmount());

                    if (refund.getRefundType().equals(RefundTypeEnum.REPORT_LOSS.getCode()) ||
                            refund.getRefundType().equals(RefundTypeEnum.DIFFERENCE.getCode())) {
                        lossRefundAmount = lossRefundAmount.add(refund.getRefundProductAmount());
                    }
                    refundDistributionAmount = refundDistributionAmount.add(refund.getRefundDistributionAmount());
                }
            }
            if (CollectionUtil.isNotEmpty(stockoutRecordDetails)) {
                int count = stockoutRecordDetails.stream().mapToInt(StockoutRecordDetail::getStockoutCount).sum();
                if (count + receiveCount == itemVo.getCount()) {
                    lossAmount = itemVo.getProductAmount().subtract(refundAmount).subtract(itemVo.getProductFreeAmount());
                } else {
                    lossAmount = lossAmount.subtract(lossRefundAmount);
                }
            } else {
                //到这里就是报损部分取值
                lossAmount = lossAmount.subtract(lossRefundAmount);
            }
            infoVo.setRefundAmount(refundAmount);
        }
        //存在佣金且已结算，把佣金扣出来以免影响人家提现  这个是耿总定的，只实现
        if (itemVo.getSettleCount() >= 1 && itemVo.getDistributionAmount().compareTo(BigDecimal.ZERO) > 0) {
            lossAmount = lossAmount.subtract(itemVo.getDistributionAmount().subtract(refundDistributionAmount));
        }
        infoVo.setLossAmount(lossAmount);
        if (itemVo.getPrice().compareTo(itemVo.getFinalPrice()) == 0) {
            infoVo.setFinalPrice(null);
        }
        // 处理售后规则
        CategoryAfterTypeEnum afterTypeEnum = CategoryAfterTypeEnum.loadByCode(infoVo.getAfterSaleType());
        if (afterTypeEnum != null) {
            infoVo.setAfterSaleName(afterTypeEnum.getName());
            infoVo.setAfterSaleDesc(afterTypeEnum.getDesc());
        }
        return infoVo;
    }

    /**
     * 检查报损金额是否正确(简单做法，不应该直接调用getLossInfo这个方法，会多两个无用的查询，但是影响不大，先这么做)
     *
     * @param orderItemId 订单项ID
     * @param lossWeight  客户填的报损重量(客户没有填重量，就传BigDecimal.ZERO)
     * @param lossAmount  前端计算的报损金额
     */
    @Override
    public BigDecimal checkLossAmount(Long orderItemId, BigDecimal lossWeight, BigDecimal lossAmount) {
        log.info("入参orderItemId：" + orderItemId + "lossWeight：" + lossWeight + "lossAmount：" + lossAmount);
        //获取可报损金额，可报损单价，可报损重量
        GetLossInfoVo lossInfoVo = getLossInfo(orderItemId);
        log.info("后端查询结果:{}", lossInfoVo);
        if (lossInfoVo.getLossAmount().compareTo(lossAmount) < 0) {
            //如果可报损金额小于前端计算的报损金额，直接报错
            log.error("可报损金额小于前端计算的报损金额");
            throw new ServiceException("已产生退款数据，请重新填写报损内容");
        }
        if (lossInfoVo.getLossWeight().compareTo(lossWeight) < 0) {
            //如果可报损重量小于客户填的报损重量，直接报错
            log.error("可报损重量小于客户填的报损重量");
            throw new ServiceException("已产生退款数据，请重新填写报损内容");
        }
        if (BigDecimal.ZERO.equals(lossWeight)) {
            //如果客户填的报损数量是0，那么客户选择的就是金额报损，因为前面已经验证了，后面逻辑不需要走了，直接返回
            return lossInfoVo.getLossAmount();
        }
        //按照客户填的报损数量，计算出报损金额
        BigDecimal currentLossAmount = lossInfoVo.getLossPrice().multiply(lossWeight).divide(lossInfoVo.getSpuGrossWeight(), 2, RoundingMode.HALF_UP);
        if (currentLossAmount.compareTo(lossInfoVo.getLossAmount()) > 0) {
            //如果计算出的报损金额比可报损金额还要大，那就直接等于可报损金额
            currentLossAmount = lossInfoVo.getLossAmount();
        }
        if (currentLossAmount.compareTo(lossAmount) != 0) {
            //如果计算出的报损金额不和前端计算的报损金额相等，直接报错
            log.error("报损金额不和前端计算的报损金额相等");
            throw new ServiceException("已产生退款数据，请重新填写报损内容");
        }
        return lossInfoVo.getLossAmount();
    }

    /**
     * 获取订单详细信息(白名单)
     *
     * @param orderId
     * @return
     */
    @Override
    public GetWhiteInfoVo getWhiteInfo(Long orderId) {
        OrderVo orderVo = orderMapper.selectVoById(orderId);
        if (orderVo == null) {
            throw new ServiceException("订单不存在");
        }
        GetWhiteInfoVo vo = MapstructUtils.convert(orderVo, GetWhiteInfoVo.class);
        if (OrderStatusEnum.WAIT.getCode().equals(vo.getStatus())) {
            long residuePayMillisecond = getResiduePayMillisecond(orderVo.getPayChannel(), orderVo.getCreateTime());
            if (residuePayMillisecond > 0) {
                vo.setResiduePayMillisecond(residuePayMillisecond);
            }
        }
        RemoteCustomerVo customerVo = remoteBasCustomerService.getById(orderVo.getCustomerId());
        vo.setCustomerName(customerVo.getName());
        BigDecimal financialServiceRate = orderVo.getFinancialServiceRate();
        if (financialServiceRate.compareTo(orderProperties.getMinRate()) == 0) {
            vo.setSubsidy(Boolean.TRUE);
        }
        return vo;
    }

    /**
     * 未支付订单统计数量
     *
     * @return
     */
    @Override
    public List<GetUnpaidCountVo> getUnpaidCount() {
        LoginUser user = LoginHelper.getLoginUser();
        if (user == null) {
            throw new ServiceException("非法请求");
        }
        return orderMapper.getUnpaidCount(user.getRelationId(), OrderStatusEnum.WAIT.getCode());
    }

    /**
     * 差额退款定时任务
     */
    @Override
    public void refundOrderDifference(LocalDate saleDate, List<Long> supplierSkuIdList, List<Long> orderItemIds) {
        if (orderItemIds != null && orderItemIds.size() > 0) {
            List<QueryItemListVo> orderItemList = orderItemMapper.queryItemByIdList(orderItemIds);
            if (orderItemList != null && orderItemList.size() > 0) {
                supplierSkuIdList = orderItemList.stream().map(QueryItemListVo::getSupplierSkuId).distinct().toList();
            } else {
                return;
            }
        }
        List<RemoteRefundDifferenceSkuVo> voList = remoteSupplierSkuService.queryRefundDifferenceSku(saleDate, supplierSkuIdList);
        if (voList == null || voList.size() == 0) {
            return;
        }
        Map<Long, RemoteRefundDifferenceSkuVo> map = voList.stream().collect(Collectors.toMap(RemoteRefundDifferenceSkuVo::getId, Function.identity()));
        RefundOrderDifferenceBo differenceBo = new RefundOrderDifferenceBo();
        int offset = 0;
        int pageSize = 500;
        if (CollectionUtil.isNotEmpty(supplierSkuIdList)) {
            differenceBo.setSupplierSkuIdList(supplierSkuIdList);
        } else {
            differenceBo.setSaleDate(saleDate);
        }
        differenceBo.setOrderItemIdList(orderItemIds);
        differenceBo.setStatusList(Lists.newArrayList(OrderStatusEnum.ALREADY.getCode(), OrderStatusEnum.FINISH.getCode()));
        while (true) {
            differenceBo.setOffset(offset * pageSize);
            differenceBo.setPageSize(pageSize);
            List<QueryItemListVo> orderItemList = orderItemMapper.refundOrderDifference(differenceBo);
            log.keyword("refundOrderDifference").info("offset1: {}", offset);
            if (orderItemList != null && orderItemList.size() > 0) {
                log.keyword("refundOrderDifference").info("size: {}", orderItemList.size());
                List<OrderRefundDTO.OrderItemIdList> orderItemIdList = new ArrayList<>();
                for (QueryItemListVo orderItemVo : orderItemList) {
                    RemoteRefundDifferenceSkuVo skuVo = map.get(orderItemVo.getSupplierSkuId());
                    if (skuVo == null) {
                        continue;
                    }
                    if (orderItemVo.getPrice().compareTo(skuVo.getPrice()) <= 0) {
                        continue;
                    }
                    if (OrderStatusEnum.ALREADY.getCode().equals(orderItemVo.getStatus())
                            || OrderStatusEnum.FINISH.getCode().equals(orderItemVo.getStatus())) {
                        OrderRefundDTO.OrderItemIdList orderItemId = new OrderRefundDTO.OrderItemIdList();
                        //差额类型 重量有变化都算差称
                        if (orderItemVo.getSpuNetWeight().compareTo(skuVo.getSpuNetWeight()) != 0) {
                            orderItemId.setDifRefundType(2);
                        }
                        orderItemId.setOrderItemId(orderItemVo.getId());
                        orderItemIdList.add(orderItemId);
                    }
                }
                if (orderItemIdList.size() > 0) {
                    OrderRefundDTO orderRefundDTO = new OrderRefundDTO();
                    orderRefundDTO.setOrderItemIdList(orderItemIdList);
                    orderRefundDTO.setRefundType(RefundTypeEnum.DIFFERENCE.getCode());
                    iRefundRecordService.createDifferenceRecord(orderRefundDTO);
                }
            } else {
                break;
            }
            offset++;
            log.keyword("refundOrderDifference").info("offset2: {}", offset);
        }
    }


    @Override
    public List<OrderItemAggregateVO> listOrderInfo(OrderSearchBo searchBo) {
        List<OrderItemAggregateVO> orderItemAggregateVOS = orderItemMapper.listOrderInfo(searchBo);
        convertOrderInfo(orderItemAggregateVOS);
        return orderItemAggregateVOS;
    }

    @Override
    public TableDataInfo<OrderItemAggregateVO> getOrderInfoPage(OrderSearchBo searchBo) {
        Page<OrderItemAggregateVO> pageVo = orderItemMapper.getOrderInfoPage(searchBo, searchBo.build());

        log.keyword("订单查询明细").info("searchBo:{}, pageVo:{}", searchBo, pageVo);
        if (ObjectUtil.isEmpty(pageVo) || ObjectUtil.isEmpty(pageVo.getRecords())) {
            return TableDataInfo.build(Collections.emptyList());
        }

        convertOrderInfo(pageVo.getRecords());
        return TableDataInfo.build(pageVo);
    }

    /**
     * 订单列表查询数量
     *
     * @param bo
     * @return
     */
    @Override
    public Long queryPlatformCount(QueryOrderPageBo bo) {
        return orderMapper.selectCount(new LambdaQueryWrapper<Order>()
                .eq(Order::getCustomerId, bo.getCustomerId()).eq(Order::getStatus, bo.getStatus()).eq(Order::getDelFlag, 0));
    }

    @Override
    public List<RemoteSupBillVo> querySaleBillList(List<Long> orderIds, Long supplierId, Long supplierDeptId, Long supplierSkuId) {
        //因为有订单id，所以不用做订单是否取消的判断
        LambdaQueryWrapper<OrderItem> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper
                .in(OrderItem::getOrderId, orderIds)
                .eq(OrderItem::getSupplierId, supplierId)
                .eq(supplierDeptId != null, OrderItem::getSupplierDeptId, supplierDeptId)
                .eq(OrderItem::getSupplierSkuId, supplierSkuId);
        List<OrderItem> items = orderItemMapper.selectList(queryWrapper);
        if (CollectionUtil.isEmpty(items)) {
            return new ArrayList<>();
        }

        List<Long> itemIds = items.stream().map(OrderItem::getId).toList();
        Map<Long, RemoteDistributionSkuVo> itemDistributionMap = Optional.ofNullable(distributionService.getOrderItemDistribution(itemIds)).orElse(Collections.emptyMap());
        //查询sku名称  统计报表中已经获取名称， 这里不在查询 ！！
        // Map<Long, String> skuNameMap = remoteSupplierSkuService.getSkuCombinationName(Lists.newArrayList(supplierSkuId));
        //根据supplierSkuId分组

        Map<Long, ActivityRecord> activityRecordMap = new HashMap<>();
        Map<Long, RemoteCouponUserInfoVO> couponUserInfoVOMap = new HashMap<>();
        List<ActivityRecord> activityRecords = activityRecordMapper.selectList(new LambdaQueryWrapper<ActivityRecord>()
                .in(ActivityRecord::getOrderId, orderIds)
                .eq(ActivityRecord::getType, ActivityRecordTypeEnum.COUPON.getCode()));
        if (CollectionUtil.isNotEmpty(activityRecords)) {
            activityRecordMap = activityRecords.stream().collect(Collectors.toMap(ActivityRecord::getOrderItemId, Function.identity(), (key1, key2) -> key2));
            List<Long> couponUserIds = activityRecords.stream().map(ActivityRecord::getActivityId).collect(Collectors.toList());
            List<RemoteCouponUserInfoVO> couponUserInfoVOS = couponUserService.getCouponUser(couponUserIds);
            couponUserInfoVOMap = couponUserInfoVOS.stream()
                    .collect(Collectors.toMap(RemoteCouponUserInfoVO::getCouponUserId, Function.identity(), (key1, key2) -> key2));
        }
        List<RemoteSupBillVo> result = new ArrayList<>();
        for (OrderItem item : items) {
            RemoteSupBillVo remoteSupBillVo = new RemoteSupBillVo();
            remoteSupBillVo.setTransId(item.getOrderId());
            remoteSupBillVo.setSkuId(item.getSupplierSkuId());
            // remoteSupBillVo.setSkuName(skuNameMap.get(item.getSkuId()));
            remoteSupBillVo.setCount(item.getCount());
            remoteSupBillVo.setAmount(item.getProductAmount().subtract(item.getProductFreeAmount()));
            remoteSupBillVo.setSkuPrice(item.getFinalPrice());
            remoteSupBillVo.setSaleDate(item.getSaleDate());
            remoteSupBillVo.setBusinessNo(item.getOrderCode());
            remoteSupBillVo.setBuyerName(item.getBuyerName());
            remoteSupBillVo.setOrderCreateTime(item.getCreateTime());
            ActivityRecord activityRecord = activityRecordMap.get(item.getId());
            BigDecimal productFreeAmount = BigDecimal.ZERO;
            if (Objects.nonNull(activityRecord)) {
                RemoteCouponUserInfoVO couponUserInfoVO = couponUserInfoVOMap.getOrDefault(activityRecord.getActivityId(), new RemoteCouponUserInfoVO());
                if (Objects.equals(couponUserInfoVO.getIsHidden(), 1)) {
                    //如果是隐藏的优惠券，直接减去优惠金额，重置单价
                    productFreeAmount = activityRecord.getProductFreeAmount();

                }
            }
            remoteSupBillVo.setProductFreeAmount(item.getProductFreeAmount().subtract(productFreeAmount));
            Optional.ofNullable(itemDistributionMap.get(item.getId()))
                    .ifPresent(distribute -> remoteSupBillVo.setDistributionItemAmount(distribute.getDistributionItemAmount()));
            result.add(remoteSupBillVo);
        }
        return result;
    }


    /**
     * 确认订单
     *
     * @param confirmOrderDTO
     * @return
     */
    @Override
    public ConfirmOrderVo confirm(ConfirmOrderDTO confirmOrderDTO) {
        //打印每个方法需要的时间
        //初始化、校验商品
        long start = System.currentTimeMillis();
        ConfirmOrderVo confirmOrderVo = initConfirmOrder(confirmOrderDTO);
        long end = System.currentTimeMillis();
        log.keyword("confirm").info("initConfirmOrder耗时: {}ms", end - start);

        // todo 匹配限时折扣活动并且排除商品不参与优惠券计算
        //限时折扣活动匹配以及计算
        start = System.currentTimeMillis();
        confirmOrderVo = marketingCalculate(confirmOrderVo);
        end = System.currentTimeMillis();
        log.keyword("confirm").info("marketingCalculate耗时: {}ms,info:{}", end - start, JSON.toJSONString(confirmOrderVo.getConfirmOrderInfoVos()));

        //活动优惠券校验
        start = System.currentTimeMillis();
        confirmOrderVo = couponCalculate(confirmOrderVo);
        end = System.currentTimeMillis();
        log.keyword("confirm").info("couponCalculate耗时: {}ms,info:{}", end - start, JSON.toJSONString(confirmOrderVo));

        //计算运费、代采费
        start = System.currentTimeMillis();
        confirmOrderVo = freightCalculate(confirmOrderVo);
        end = System.currentTimeMillis();
        log.keyword("confirm").info("freightCalculate耗时: {}ms,info:{}", end - start, JSON.toJSONString(confirmOrderVo));

        //活动优惠
        start = System.currentTimeMillis();
        confirmOrderVo = activityCalculate(confirmOrderVo);
        end = System.currentTimeMillis();
        log.keyword("confirm").info("activityCalculate耗时: {}ms,info:{}", end - start, JSON.toJSONString(confirmOrderVo));

        //计算金额
        start = System.currentTimeMillis();
        confirmOrderVo = calculateTotalAmount(confirmOrderVo);
        end = System.currentTimeMillis();
        log.keyword("confirm").info("calculateTotalAmount耗时: {}ms,info:{}", end - start, JSON.toJSONString(confirmOrderVo));

        //金融手续费
        start = System.currentTimeMillis();
        confirmOrderVo = financeCalculate(confirmOrderVo);
        end = System.currentTimeMillis();
        log.keyword("confirm").info("financeCalculate耗时: {}ms", end - start);
        log.keyword("confirmInfo", confirmOrderVo.getCode()).info("confirmOrderVo: {}", confirmOrderVo);
        //存缓存
        insertRedis(confirmOrderVo);
        return confirmOrderVo;
    }

    /**
     * 提交订单去除分布式事务，（先减吨位再减库存，吨位用定时任务后置补偿（10分钟一次））
     *
     * @param submitBo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AddOrderVo submit(SubmitBo submitBo) {
        log.keyword("submit", submitBo.getOrderCode()).info("开始提交订单，参数:{}", submitBo);
        AddOrderVo addOrderVo = new AddOrderVo();
        String redisName = RedisConstant.ORDER_CONFIRM_INFO + submitBo.getOrderCode();
        ConfirmOrderVo confirmOrderVo = RedisUtils.getCacheObject(redisName);
        //校验订单商品
        List<CancelOrderItemVo> cancelOrderItemVos = checkOrderItemOrBulid(confirmOrderVo);
        List<ConfirmOrderInfoVo> confirmOrderInfoVos = confirmOrderVo.getConfirmOrderInfoVos();

        if (ObjectUtil.isNotEmpty(cancelOrderItemVos)) {
            //有商品异常返回
            addOrderVo.setCancelList(cancelOrderItemVos);
            return addOrderVo;
        }
        //检验结算件数是否一致
        checkOrderPieceCount(confirmOrderInfoVos);
        //校验订单优惠券
        List<RemoteCouponOrderVO> selectedCoupons = confirmOrderVo.getSelectedCoupons();
        checkOrderCoupon(selectedCoupons, confirmOrderVo.getSaleDate());
        //创建订单实体
        Order add = MapstructUtils.convert(confirmOrderVo, Order.class);
        //订单类型
        Set<Integer> businessTypeSet = confirmOrderInfoVos.stream().map(ConfirmOrderInfoVo::getBusinessType).collect(Collectors.toSet());
        if (Objects.equals(businessTypeSet.size(), 1)) {
            add.setBusinessType(businessTypeSet.stream().findFirst().orElse(0));
        } else {
            add.setBusinessType(OrderBusinessTypeEnum.BUSINESS_TYPE0.getCode());
        }
        if (Objects.equals(submitBo.getPayChannel(), PayChannelEnum.PAY_PINGAN_CLOUD_LARGE.getCode())) {
            add.setTotalAmount(confirmOrderVo.getTotalAmountLarge());
            add.setFinancialServiceAmount(BigDecimal.ZERO);
        }
        add.setPayChannel(submitBo.getPayChannel());
        add.setRemark(submitBo.getRemark());
        add.setCoinCount(BigDecimal.ZERO);
        add.setCoinAmount(BigDecimal.ZERO);
        add.setOrderChannel(confirmOrderVo.getOrderChannel());
        add.setFinancialServiceRate(confirmOrderVo.getFinancialServiceRate());
        //新增订单
        orderMapper.insert(add);
        List<OrderItem> orderItemList = MapstructUtils.convert(confirmOrderInfoVos, OrderItem.class);
        //获取供应商信息
        List<Long> supplierIds = confirmOrderInfoVos.stream().map(ConfirmOrderInfoVo::getSupplierId).distinct().collect(Collectors.toList());
        List<RemoteSupplierVo> supplierVoList = remoteSupplierService.getSupplierByIds(supplierIds);
        Map<Long, RemoteSupplierVo> supplierVoMap = supplierVoList.stream().collect(Collectors.toMap(RemoteSupplierVo::getId, Function.identity()));


        //库存
        List<RemoteUpdateStockBo> updateStockList = new ArrayList<>();
        for (OrderItem orderItem : orderItemList) {
            orderItem.setOrderId(add.getId());
            orderItem.setSaleDate(add.getSaleDate());
            orderItem.setPlacePath(add.getPlacePath());
            orderItem.setOrderCode(add.getCode());
            orderItem.setPayChannel(add.getPayChannel());
            orderItem.setPlaceId(add.getPlaceId());
            orderItem.setPlaceName(add.getPlaceName());
            orderItem.setPlaceIdLevel2(Optional.ofNullable(add.getPlaceIdLevel2()).orElse(0L));
            orderItem.setPlaceNameLevel2(Optional.ofNullable(add.getPlaceNameLevel2()).orElse(""));
            orderItem.setAddress(add.getAddress());
            orderItem.setAddressLevel2(Optional.ofNullable(add.getAddressLevel2()).orElse(""));
            orderItem.setFinalPrice(orderItem.getFinalPrice());
            orderItem.setLogisticsId(add.getLogisticsId());
            orderItem.setLogisticsIdLevel2(Optional.ofNullable(add.getLogisticsIdLevel2()).orElse(0L));
            orderItem.setCityWhId(add.getCityWhId());
            orderItem.setCustomerId(add.getCustomerId());
            orderItem.setCoinCount(BigDecimal.ZERO);
            orderItem.setCoinAmount(BigDecimal.ZERO);
            if (Objects.equals(submitBo.getPayChannel(), PayChannelEnum.PAY_PINGAN_CLOUD_LARGE.getCode())) {
                orderItem.setTotalAmount(orderItem.getTotalAmount().subtract(orderItem.getFinancialServiceAmount()));
                orderItem.setOtherTotalAmount(orderItem.getOtherTotalAmount().subtract(orderItem.getFinancialServiceAmount()));
                orderItem.setFinancialServiceAmount(BigDecimal.ZERO);
            }
            //供应商是否提供发票
            Integer provideInvoice = Optional.ofNullable(supplierVoMap.get(orderItem.getSupplierId()))
                    .map(RemoteSupplierVo::getProvideInvoice).orElse(SupplierProvideInvoiceEnum.NOT_PROVIDE.getCode());
            orderItem.setProvideInvoice(provideInvoice);
        }
        //新增订单项
        orderItemMapper.batchInsert(orderItemList);
        //重新检查库存和吨位
        chcekLogisticsStock(confirmOrderVo, orderItemList);

        //支付单
        orderPayService.orderCreate(add.getId());
        //更新客户常用提货点
        RemoteBasCustomerUsedPlaceAddBo placeAddBo = new RemoteBasCustomerUsedPlaceAddBo();
        placeAddBo.setCustomerId(add.getCustomerId());
        placeAddBo.setPlaceId(add.getPlaceIdLevel2() != null && add.getPlaceIdLevel2() > 0 ? add.getPlaceIdLevel2() : add.getPlaceId());
        remoteBasCustomerService.addUsedPlace(placeAddBo);
        RemoteOrderCancelBo msg = new RemoteOrderCancelBo();
        msg.setOrderId(add.getId());
        msg.setPayChannel(add.getPayChannel());
        msg.setCancelType(OrderCancelTypeEnum.TIMEOUT.getCode());
        msg.setIsAdvancedCancel(confirmOrderVo.getIsAdvancedCancel());

        //插入活动信息
        insertActivityRecordV2(add, orderItemList, confirmOrderInfoVos);

        //减吨位
        RemoteRegionLogisticsPlanOrderWeightBo logisticsPlan = new RemoteRegionLogisticsPlanOrderWeightBo();
        logisticsPlan.setOrderId(add.getId());
        logisticsPlan.setOrderCode(add.getCode());
        logisticsPlan.setLogisticsId(add.getLogisticsId());
        logisticsPlan.setPlanDate(confirmOrderVo.getSaleDate());
        logisticsPlan.setOrderWeight(add.getTotalGrossWeight());

        //减吨位
        remoteRegionLogisticsPlanService.updateOrderWeight(logisticsPlan);

        for (OrderItem orderItem : orderItemList) {
            RemoteUpdateStockBo updateStockBo = new RemoteUpdateStockBo();
            updateStockBo.setSupplierSkuId(orderItem.getSupplierSkuId());
            updateStockBo.setOrderId(add.getId());
            updateStockBo.setOrderItemId(orderItem.getId());
            updateStockBo.setOrderCode(add.getCode());
            updateStockBo.setBackStock(-orderItem.getCount());
            updateStockBo.setLockStock(orderItem.getCount());
            // 如果订单项参与了限时折扣活动，则设置相应的折扣数量和操作类型
            if (HAS_LIMIT_DISCOUNT.equals(orderItem.getHasLimitDiscount())) {
                updateStockBo.setActDiscountNum(orderItem.getActDiscountNum());
                updateStockBo.setTargetQty(orderItem.getTargetQty());
                updateStockBo.setOpType(BigDecimal.ONE.intValue());
            }
            updateStockList.add(updateStockBo);
        }
        try {
            //锁定优惠券
            lockOrderCouponUser(add, confirmOrderVo, selectedCoupons);

            // 处理分销活动
            distributeSkuActivity(add, orderItemList);

            //批量减少库存，增加已购 新增限时折扣超卖处理逻辑
            remoteSupplierSkuService.batchUpdateStock(updateStockList, StockTypeEnum.ORDER.getCode());

            //发送一条30分钟（nacos动态配置）后执行的延时消息，执行取消订单操作，如果订单已经支付，不做处理，如果订单没有支付，取消订单
            orderCancelProducer.send(msg);

            // 限时折扣写入营销活动参与记录
            joinMkActivityRecord(confirmOrderVo);

            //清除购物车
            iCartItemService.batchDeleteCartItemByRegionWhId(confirmOrderVo.getRegionWhId());

            //发送延时消息，异步处理的业务逻辑
            orderMqProducer.send(add);
        } catch (Exception e) {
            //去除分布式事务，失败补偿
            log.keyword("提交订单失败", submitBo.getOrderCode()).error("订单创建失败", e);
            //补偿mq
            try {
                OrderCompensateBo orderCompensateBo = new OrderCompensateBo();
                orderCompensateBo.setOrderId(add.getId());
                orderCompensateBo.setOrderCode(add.getCode());
                orderCompensateBo.setLogisticsId(logisticsPlan.getLogisticsId());
                orderCompensateBo.setPlanDate(logisticsPlan.getPlanDate());
                orderCompensateBo.setOrderWeight(logisticsPlan.getOrderWeight());
                orderCompensateBo.setRegionWhId(add.getRegionWhId());
                log.keyword("提交订单失败，发送订单补偿mq", submitBo.getOrderCode()).info("订单补偿mq消息：{}", JSON.toJSONString(orderCompensateBo));
                orderCompensateProducer.send(orderCompensateBo);
            } catch (Exception e1) {
                log.keyword("提交订单失败，发送订单补偿mq失败", submitBo.getOrderCode()).error("发送订单补偿mq失败", e1);
            }
            Throwable cause = e.getCause();
            if (cause != null && cause instanceof ServiceException) {
                ServiceException exp = (ServiceException) cause;
                if (SkuStockErrorEnum.TIME_DISCOUNT_STOCK_NOT_ENOUGH.getCode().equals(exp.getCode())) {
                    // 抛出业务异常code&msg
                    log.keyword("timeDiscountStockNotEnough").error("提交订单失败,限时折扣商品库存名额不足，e:{}", exp);
                    throw new ServiceException(OrderSubmitErrorEnum.SKU_PRICE_CHANGE.getMsg(), OrderSubmitErrorEnum.SKU_PRICE_CHANGE.getCode());
                }
            }
            throw e;
        }
        addOrderVo.setOrderId(add.getId());
        return addOrderVo;
    }


    /**
     * 重新检验库存和吨位
     *
     * @param confirmOrderVo
     * @param orderItemList
     */
    private void chcekLogisticsStock(ConfirmOrderVo confirmOrderVo, List<OrderItem> orderItemList) {
//查询基础物流
        RemoteRegionLogisticsPlanQueryBo planBo = new RemoteRegionLogisticsPlanQueryBo();
        planBo.setPlanDate(confirmOrderVo.getSaleDate());
        planBo.setLogisticsId(confirmOrderVo.getLogisticsId());
        List<RemoteRegionLogisticsPlanVo> planVoList = remoteRegionLogisticsPlanService.queryList(planBo);
        if (planVoList == null || planVoList.size() == 0) {
            throw new ServiceException("未配置基础吨位，请联系城市仓人员进行吨位配置");
        }
        RemoteRegionLogisticsPlanVo planVo = planVoList.get(0);
        if (planVo.getPlanWeight().compareTo(planVo.getOrderWeight().add(confirmOrderVo.getTotalGrossWeight())) < 0) {
            throw new ServiceException("基础吨位不足，请联系城市仓人员进行吨位配置");
        }
        List<Long> supplierSkuIds = confirmOrderVo.getConfirmOrderInfoVos().stream().map(item -> item.getSupplierSkuId()).collect(Collectors.toList());
        List<RemoteSupplierSkuStockVo> remoteSupplierSkuStockVos = remoteSupplierSkuService.listSupplierSkuStockBySupplierIds(supplierSkuIds);
        Map<Long, Integer> stockMap = remoteSupplierSkuStockVos.stream().collect(Collectors.toMap(RemoteSupplierSkuStockVo::getSupplierSkuId, RemoteSupplierSkuStockVo::getStock));
        boolean anyMatch = orderItemList.stream().anyMatch(item -> stockMap.getOrDefault(item.getSupplierSkuId(), 0) < item.getCount());
        if (anyMatch) {
            throw new ServiceException("库存不足，请重新下单");
        }


    }

    /**
     * 检验结算件数是否一致
     *
     * @param confirmOrderInfoVos
     */
    private void checkOrderPieceCount(List<ConfirmOrderInfoVo> confirmOrderInfoVos) {
        for (ConfirmOrderInfoVo confirmOrderInfoVo : confirmOrderInfoVos) {
            BigDecimal productActual = confirmOrderInfoVo.getProductAmount().subtract(confirmOrderInfoVo.getProductFreeAmount());
            BigDecimal priceFree = productActual.divide(BigDecimal.valueOf(confirmOrderInfoVo.getCount()), 2, RoundingMode.DOWN);
            //订单项平台代采费
            OrderSettkeInfBO platformServiceAmtBo = getPlatformServiceAmtBo(priceFree);
            BigDecimal count = platformServiceAmtBo.getCount().multiply(BigDecimal.valueOf(confirmOrderInfoVo.getCount()));
            if (count.compareTo(confirmOrderInfoVo.getSettleNumber()) != 0) {
                throw new ServiceException("代采费计算有误，请重新支付!");
            }
        }
    }

    @Override
    public List<Map<String, BigDecimal>> getPlatformServiceStandardList() {
        return orderProperties.getPlatformServiceStandardList();
    }

    /**
     * 优惠券自动生成订单
     *
     * @param confirmOrderDTO
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean couponAutoCreateOrder(ConfirmOrderDTO confirmOrderDTO) {
        try {

            //确认订单页
            ConfirmOrderVo confirmed = confirm(confirmOrderDTO);
            log.keyword("couponAutoCreateOrderCconfirm").info(JSON.toJSONString(confirmed));
            if (CollectionUtil.isEmpty(confirmed.getConfirmOrderInfoVos())) {
                log.keyword("useCouponGiveSkuFile").info("入参：{}", JSON.toJSONString(confirmOrderDTO));
                return true;
            }
            //提交订单
            SubmitBo submitBo = new SubmitBo();
            submitBo.setOrderCode(confirmed.getCode());
            submitBo.setPayChannel(PayChannelEnum.PAY_WEIXIN_B2B.getCode());
            AddOrderVo submit = submit(submitBo);
            log.keyword("couponAutoCreateOrderSubmit").info(JSON.toJSONString(submit));
            //支付
            OrderPaySignBo paySignBo = new OrderPaySignBo();
            paySignBo.setOrderId(submit.getOrderId());
            String orderPay = orderPayService.orderPayLock(paySignBo);
            log.keyword("couponAutoCreateOrderPay").info(JSON.toJSONString(orderPay));
            String redisKey = RedisConstant.ORDER_COUPON_GIVE_TAG + LoginHelper.getUserId();
            //保存标记（前端显示红点）
            RedisUtils.setCacheObject(redisKey, true, Duration.ofDays(3));
        } catch (Exception e) {
            log.keyword("couponAutoCreateOrderPayFile").error("优惠券自动下单失败，错误信息：{}", ExceptionUtils.getStackTrace(e));
            throw e;
        }
        return true;
    }

    /**
     * 获取指定的订单
     *
     * @param regionWhId
     * @param customerId
     * @return
     */
    @Override
    public Order getByRegionWhId(Long regionWhId, Long customerId) {
        List<Order> orderList = orderMapper.selectList(new LambdaQueryWrapper<Order>()
                .eq(Order::getRegionWhId, regionWhId)
                .eq(Order::getCustomerId, customerId)
                .orderByDesc(Order::getCreateTime)
                .last("LIMIT 1"));

        if (CollectionUtil.isEmpty(orderList)) {
            return null;
        }
        return orderList.get(0);
    }


    /**
     * 锁定优惠券
     *
     * @param confirmOrderVo
     * @param selectedCoupons
     */
    private void lockOrderCouponUser(Order add, ConfirmOrderVo confirmOrderVo, List<RemoteCouponOrderVO> selectedCoupons) {
        if (CollectionUtil.isEmpty(selectedCoupons)) {
            return;
        }
        List<ConfirmOrderInfoVo> confirmOrderInfoVos = confirmOrderVo.getConfirmOrderInfoVos();
        List<OrderCoupnSkuInfoVO> coupnSkuInfoVOS = confirmOrderInfoVos.stream().flatMap(item -> item.getParticipateCoupons().stream()).collect(Collectors.toList());
        Map<Long, BigDecimal> couponAmountMap = coupnSkuInfoVOS.stream().collect(Collectors.groupingBy(OrderCoupnSkuInfoVO::getCouponUserId
                , Collectors.reducing(BigDecimal.ZERO, OrderCoupnSkuInfoVO::getDiscountAmount, BigDecimal::add)));
        List<RemoteCouponLockVO> couponLockVOS = selectedCoupons.stream().map(item -> {
            RemoteCouponLockVO remoteCouponLockVO = new RemoteCouponLockVO();
            remoteCouponLockVO.setCouponUserId(item.getCouponUserId());
            remoteCouponLockVO.setCouponId(item.getCouponId());
            remoteCouponLockVO.setCouponCode(item.getCouponCode());
            remoteCouponLockVO.setStatus(CouponLockStatusEnum.WAIT.getCode());
            remoteCouponLockVO.setOrderId(add.getId());
            remoteCouponLockVO.setOrderCode(add.getCode());
            remoteCouponLockVO.setCustomerId(add.getCustomerId());
            remoteCouponLockVO.setUserId(confirmOrderVo.getUserId());
            remoteCouponLockVO.setCoupouFreeAmount(couponAmountMap.getOrDefault(item.getCouponUserId(), BigDecimal.ZERO));
            return remoteCouponLockVO;
        }).collect(Collectors.toList());
        couponUserService.lockOrderCouponUser(couponLockVOS);
        //异步发券
        couponUserService.asyncSendCoupon(couponLockVOS);

    }


    /**
     * 校验订单优惠券
     *
     * @param selectedCoupons
     */
    private void checkOrderCoupon(List<RemoteCouponOrderVO> selectedCoupons, LocalDate saleDate) {
        if (CollectionUtil.isEmpty(selectedCoupons)) {
            return;
        }
        List<Long> couponUserIds = selectedCoupons.stream().map(RemoteCouponOrderVO::getCouponUserId).collect(Collectors.toList());
        couponUserService.checkOrderCoupon(couponUserIds, saleDate);
    }

    /**
     * 插入活动信息
     *
     * @param add
     * @param orderItemList
     * @param confirmOrderInfoVos
     */
    private void insertActivityRecordV2(Order add, List<OrderItem> orderItemList, List<ConfirmOrderInfoVo> confirmOrderInfoVos) {
        List<ActivityRecord> activityRecords = confirmOrderInfoVos.stream().flatMap(item -> item.getActivityRecord().stream()).collect(Collectors.toList());
        Map<Long, OrderItem> orderItemMap = orderItemList.stream().collect(Collectors.toMap(OrderItem::getSupplierSkuId, item -> item, (v1, v2) -> v2));
        activityRecords = activityRecords.stream().map(item -> {
            OrderItem orderItem = orderItemMap.get(item.getSupplierSkuId());
            if (Objects.isNull(orderItem)) {
                return null;
            }
            item.setSupplierSpuId(orderItem.getSupplierSpuId());
            item.setOrderItemId(orderItem.getId());
            item.setOrderCode(orderItem.getOrderCode());
            item.setOrderId(orderItem.getOrderId());
            item.setSpuName(orderItem.getSpuName());
            item.setCustomerId(orderItem.getCustomerId());
            return item;
        }).filter(Objects::nonNull).collect(Collectors.toList());


        List<ActivityRecord> finalActivityRecords = activityRecords;
        confirmOrderInfoVos.forEach(item -> item.getParticipateCoupons().forEach(coupon -> {
            ActivityRecord activityRecord = new ActivityRecord();
            OrderItem orderItem = orderItemMap.get(item.getSupplierSkuId());
            if (Objects.isNull(orderItem)) {
                return;
            }
            BeanUtil.copyProperties(coupon, activityRecord);
            activityRecord.setSupplierSkuId(item.getSupplierSkuId());
            activityRecord.setSupplierSpuId(orderItem.getSupplierSpuId());
            activityRecord.setFreeAmount(coupon.getDiscountAmount());
            activityRecord.setOrderItemId(orderItem.getId());
            activityRecord.setActivityId(coupon.getCouponUserId());
            activityRecord.setType(ActivityRecordTypeEnum.COUPON.getCode());
            activityRecord.setOrderCode(orderItem.getOrderCode());
            activityRecord.setOrderId(orderItem.getOrderId());
            activityRecord.setSpuName(orderItem.getSpuName());
            activityRecord.setCustomerId(orderItem.getCustomerId());
            finalActivityRecords.add(activityRecord);
        }));
        activityRecordMapper.insertBatch(activityRecords);
    }

    private List<CancelOrderItemVo> checkOrderItemOrBulid(ConfirmOrderVo confirmOrderVo) {

        if (ObjectUtil.isEmpty(confirmOrderVo)) {
            throw new ServiceException("获取订单信息失败！");
        }
        log.keyword("submitOrderInfo", confirmOrderVo.getCode()).info("订单信息:{}", confirmOrderVo);

        List<ConfirmOrderInfoVo> confirmOrderInfoVos = confirmOrderVo.getConfirmOrderInfoVos();
        if (CollectionUtil.isEmpty(confirmOrderInfoVos)) {
            throw new ServiceException("暂无商品可下单！");
        }

        //查询总仓信息
        RemoteRegionWhVo regionWhVo = remoteRegionWhService.queryById(confirmOrderVo.getRegionWhId());
        if (regionWhVo == null) {
            throw new ServiceException("总仓不存在");
        }
        if (!StatusEnum.ENABLE.getCode().equals(regionWhVo.getStatus())) {
            throw new ServiceException("总仓已被禁用");
        }
        LocalDate saleDate = SaleDateUtil.getSaleDate(regionWhVo.getSalesTimeEnd());
        if (!Objects.equals(saleDate, confirmOrderVo.getSaleDate())) {
            throw new ServiceException("销售日期有误");
        }
        //指定时间取消
        confirmOrderVo.setIsAdvancedCancel(getOrderCancelTime(LocalTime.now(), regionWhVo.getClearWhTime()));

        //查询商品信息
        List<Long> supplierSkuIdList = confirmOrderInfoVos.stream().map(ConfirmOrderInfoVo::getSupplierSkuId).toList();
        List<Long> skuIdList = confirmOrderInfoVos.stream().map(ConfirmOrderInfoVo::getSkuId).toList();
        RemoteQueryInfoListBo skuBo = new RemoteQueryInfoListBo();
        skuBo.setSupplierSkuIdList(supplierSkuIdList);
        List<RemoteSupplierSkuInfoVo> skuList = remoteSupplierSkuService.queryInfoList(skuBo);
        if (skuList == null || skuList.size() == 0 || skuList.size() < supplierSkuIdList.size()) {
            throw new ServiceException("商品数据有误");
        }

        boolean anyBusinessType40 = skuList.stream().anyMatch(item -> Objects.equals(item.getBusinessType(), SupplierSkuBusinessTypeEnum.BUSINESS_TYPE40.getCode()));
        if (regionWhVo.getCityWhIdList().contains(confirmOrderVo.getCityWhId()) && anyBusinessType40) {
            throw new ServiceException("直属仓不能下配货商品！");
        }
        boolean allBusinessType40 = skuList.stream().allMatch(item -> Objects.equals(item.getBusinessType(), SupplierSkuBusinessTypeEnum.BUSINESS_TYPE40.getCode()));
        if (anyBusinessType40 && !allBusinessType40) {
            throw new ServiceException("配货商品不能与其他商品一起下单！");
        }
        List<Long> maxIdList = skuList.stream().filter(l -> l.getBuyMax() > 0).map(RemoteSupplierSkuInfoVo::getId).toList();
        Map<Long, Integer> skuCountMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(maxIdList)) {
            List<OrderSkuCustomerVO> orderSkuCustomerVOS = orderItemMapper.getSkuCountByCustomerId(maxIdList, saleDate, confirmOrderVo.getCustomerId(), null);
            if (CollectionUtil.isNotEmpty(orderSkuCustomerVOS)) {
                skuCountMap = orderSkuCustomerVOS.stream().collect(Collectors.toMap(OrderSkuCustomerVO::getSkuId
                        , OrderSkuCustomerVO::getCount, (key1, key2) -> key2));
            }
        }

        Map<Long, RemoteSupplierSkuInfoVo> skuMap = skuList.stream().collect(Collectors.toMap(RemoteSupplierSkuInfoVo::getId, Function.identity(), (key1, key2) -> key2));
        List<CancelOrderItemVo> cancelList = new ArrayList<>();
        List<Long> skuIdsByCityWhId = remoteSkuDisableCityWhService.getSkuIdsByCityWhId(confirmOrderVo.getCityWhId(), skuIdList);
        //  todo 限时折扣活动， confirmOrderVo.getConfirmOrderInfoVos()需要实时更新对应的商品总库存、商品锁定库存、商品已售库存
        confirmOrderVo.getConfirmOrderInfoVos().stream().filter(item -> null != item).forEach(item -> {
            RemoteSupplierSkuInfoVo skuInfoVo = skuMap.get(item.getSupplierSkuId());
            if (null != skuInfoVo) {
                if (null != skuInfoVo.getStock()) {
                    item.setStock(skuInfoVo.getStock());
                }
                if (null != skuInfoVo.getLockStock()) {
                    item.setLockStock(skuInfoVo.getLockStock());
                }
                if (null != skuInfoVo.getSold()) {
                    item.setSold(skuInfoVo.getSold());
                }
//                if (null != skuInfoVo.getPrice()) {
//                    item.setPrice(skuInfoVo.getPrice().subtract(skuInfoVo.getSubsidyAmount()).setScale(2, RoundingMode.HALF_UP));
//                    item.setFinalPrice(item.getPrice());
//                }
                if (null != skuInfoVo.getSupplierId()) {
                    item.setSupplierId(skuInfoVo.getSupplierId());
                }
                if (null != skuInfoVo.getSupplierSpuId()) {
                    item.setSupplierDeptId(skuInfoVo.getSupplierDeptId());
                }
                if (null != skuInfoVo.getCategoryId()) {
                    item.setCategoryId(skuInfoVo.getCategoryId());
                }
                if (null != skuInfoVo.getCategoryIdLevel1()) {
                    item.setCategoryIdLevel1(skuInfoVo.getCategoryIdLevel1());
                }
                if (null != skuInfoVo.getCategoryIdLevel2()) {
                    item.setCategoryIdLevel2(skuInfoVo.getCategoryIdLevel2());
                }
            }
        });
        log.keyword("checkOrderItemTimeDiscount").info("提交订单，实时更新商品库存信息，实时查询营销活动进行商品活动价格校验：{}", JSON.toJSONString(confirmOrderVo.getConfirmOrderInfoVos()));
        List<RemoteMkActivitySkuDiscountVo> activitySkuDiscountVos = qryMkActivityByOrderItem(confirmOrderVo);
        // 订单当前时间没有命中活动
        boolean currentTimeIsNotMkActivity = CollectionUtils.isEmpty(activitySkuDiscountVos);
        Map<Long, RemoteMkActivitySkuDiscountVo> skuDiscountVoMap = Map.of();
        if (CollectionUtils.isNotEmpty(activitySkuDiscountVos)) {
            skuDiscountVoMap = activitySkuDiscountVos.stream().collect(Collectors.toMap(RemoteMkActivitySkuDiscountVo::getSupplierSkuId, activitySkuDiscountVo -> activitySkuDiscountVo, (key1, key2) -> key2));
        }
        //如果购买的某个商品有问题，那么这个商品就不创建订单项，并且需要返回给前端提示用户
        for (ConfirmOrderInfoVo sku : confirmOrderInfoVos) {
            RemoteSupplierSkuInfoVo skuInfoVo = skuMap.get(sku.getSupplierSkuId());
            if (null == skuInfoVo) {
                CancelOrderItemVo cancelVo = new CancelOrderItemVo();
                cancelVo.setSkuName("商品已失效");
                cancelVo.setRemark("商品已失效");
                cancelList.add(cancelVo);
                continue;
            }
            String brand = StringUtils.isNotEmpty(skuInfoVo.getBrand()) ? skuInfoVo.getBrand() + "-" : "";
            String shortProducer = StringUtils.isNotEmpty(skuInfoVo.getShortProducer()) ? skuInfoVo.getShortProducer() + "-" : "";
            String spuName = String.format("%s%s%s-【%s】", brand, shortProducer, skuInfoVo.getSpuName(), skuInfoVo.getSpuStandards());
            //总仓id是否一致
            if (!skuInfoVo.getRegionWhId().equals(confirmOrderVo.getRegionWhId())) {
                CancelOrderItemVo cancelVo = new CancelOrderItemVo();
                cancelVo.setSkuName(spuName);
                cancelVo.setRemark("总仓有误");
                cancelList.add(cancelVo);
                continue;
            }
            // 是否城市仓禁用商品
            if (skuIdsByCityWhId.contains(sku.getSkuId())) {
                CancelOrderItemVo cancelVo = new CancelOrderItemVo();
                cancelVo.setSkuName(spuName);
                cancelVo.setRemark("已禁用");
                cancelList.add(cancelVo);
                continue;
            }
            //销售日有误
            if (!saleDate.isEqual(skuInfoVo.getSaleDate())) {
                CancelOrderItemVo cancelVo = new CancelOrderItemVo();
                cancelVo.setSkuName(spuName);
                cancelVo.setRemark("销售日有误");
                cancelList.add(cancelVo);
                continue;
            }
            //是否下架
            if (!SupplierSkuStatusEnum.STATUS4.getCode().equals(skuInfoVo.getStatus())) {
                CancelOrderItemVo cancelVo = new CancelOrderItemVo();
                cancelVo.setSkuName(spuName);
                cancelVo.setRemark("下架");
                cancelList.add(cancelVo);
                continue;
            }
            //是否售罄
            if (skuInfoVo.getStock() == 0) {
                CancelOrderItemVo cancelVo = new CancelOrderItemVo();
                cancelVo.setSkuName(spuName);
                cancelVo.setRemark("已售罄");
                cancelList.add(cancelVo);
                continue;
            }
            //商品价格有误
            if (null == skuInfoVo.getPrice() || !skuInfoVo.getPrice().subtract(skuInfoVo.getSubsidyAmount()).equals(sku.getPrice())) {
                log.keyword("checkOrderItem1").warn("商品：{} 价格有变动，加购前价格：{}| 目前最新价格：{}", skuInfoVo.getSkuId(), skuInfoVo.getPrice(), sku.getPrice());
                throw new ServiceException(spuName + " 下单商品价格存在调整，请再次确认后再进行下单");
            }
            //是否库存不足
            if (skuInfoVo.getStock() < sku.getCount()) {
                throw new ServiceException(spuName + "商品库存不足");
            }
            if (sku.getSpuGrossWeight().compareTo(skuInfoVo.getSpuGrossWeight()) != 0) {
                throw new ServiceException(spuName + "商品毛重更新，需重新计算价格");
            }

            boolean isGive = Objects.equals(sku.getProdType(), OrderProdTypeEnum.GIVE.getCode());
            //销售批次最小购买数量
            if (skuInfoVo.getBuyMin() != -1 && sku.getCount().compareTo(skuInfoVo.getBuyMin()) < 0 && !isGive) {
                throw new ServiceException(String.format("%s最小起订%s件", spuName, skuInfoVo.getBuyMin()));
            }

            Integer countTotal = skuCountMap.getOrDefault(sku.getSkuId(), 0) + sku.getCount();
            //是否赠品
            //销售批次最大购买数量
            if (skuInfoVo.getBuyMax() != -1 && skuInfoVo.getBuyMax() < countTotal) {
                throw new ServiceException(String.format("%s商品销售日最多订%s件", spuName, skuInfoVo.getBuyMax()));
            }
            sku.setCategoryIdLevel1(skuInfoVo.getCategoryIdLevel1());
            sku.setCategoryIdLevel2(skuInfoVo.getCategoryIdLevel2());
            sku.setImgUrl(skuInfoVo.getImgUrl());
            sku.setAfterSaleDay(skuInfoVo.getAfterSaleDay());
            sku.setAfterSaleType(skuInfoVo.getAfterSaleType());
            if (skuInfoVo.getAfterSaleDay() == null || skuInfoVo.getAfterSaleDay() == 0) {
                sku.setAfterSaleStatus(ItemAfterSaleStatusEnum.CAN_NOT_AFTER_SALE.getCode());
            } else {
                sku.setAfterSaleStatus(ItemAfterSaleStatusEnum.CAN_AFTER_SALE.getCode());
            }
            // 下单实时校验购物车商品营销活动价格，若不一致则返回提示前端后强制刷新
            Optional<ActivityRecord> recordOptional = Optional.ofNullable(sku.getActivityRecord())
                    .orElse(Collections.emptyList())
                    .stream()
                    .filter(record -> record != null && record.isHasLimitDiscount() && MarketingActivityTypeEnum.TIME_LIMIT_DISCOUNT.getCode().equals(record.getType()))
                    .findFirst();
            ActivityRecord activityRecord = recordOptional.orElse(null);
            ActivityRecord activityRecordByNow = null;
            try {
                activityRecordByNow = orderItemMarketingCalc4Record(skuDiscountVoMap, sku, confirmOrderVo);
            } catch (Exception e) {
                log.keyword("checkOrderItemTimeDiscount").error("下单实时计算营销活动异常", e);
            }
            //当前商品行之前有命中活动
            if (null != activityRecord) {
                log.keyword("checkOrderItemTimeDiscount").info("商品skuID：{} supplierSkuId:{} | 加购时命中活动：{}", skuInfoVo.getSkuId(), sku.getSupplierSkuId(), activityRecord.getActivityId());
                // 订单没有命中活动或活动不存在
                if (currentTimeIsNotMkActivity) {
                    log.keyword("checkOrderItemTimeDiscount").warn("下单那会没有命中活动，价格不一致");
                    throw new ServiceException(OrderSubmitErrorEnum.SKU_PRICE_CHANGE.getMsg(), OrderSubmitErrorEnum.SKU_PRICE_CHANGE.getCode());
                }
                //订单当前命中了活动，但还需判断商品行当前时间有没命中活动
                if (null == activityRecordByNow) {
                    log.keyword("checkOrderItemTimeDiscount").warn("下单那会没有命中活动，价格不一致");
                    throw new ServiceException(OrderSubmitErrorEnum.SKU_PRICE_CHANGE.getMsg(), OrderSubmitErrorEnum.SKU_PRICE_CHANGE.getCode());
                }
                if (!activityRecord.getActivityId().equals(activityRecordByNow.getActivityId()) || !activityRecord.getDiscountRate().equals(activityRecordByNow.getDiscountRate())) {
                    log.keyword("checkOrderItemTimeDiscount").warn("下单命中了另一个活动：{} , 需要重新刷新获取商品价格", activityRecordByNow.getActivityId());
                    throw new ServiceException(OrderSubmitErrorEnum.SKU_PRICE_CHANGE.getMsg(), OrderSubmitErrorEnum.SKU_PRICE_CHANGE.getCode());
                }
                log.keyword("checkOrderItemTimeDiscount").info("加购时命中活动：{} | 下单命中活动：{}，校验通过", activityRecord.getActivityId(), activityRecordByNow.getActivityId());
            } else {
                // 商品行之前没有命中活动,下单那会也没命中活动，则跳过
                if (currentTimeIsNotMkActivity) {
                    continue;
                }
                // 商品行之前没有命中活动,下单那会命中了活动
                if (null != activityRecordByNow) {
                    log.keyword("checkOrderItemTimeDiscount").warn("商品skuID：{} supplierSkuId:{} 加购时没命中活动，下单命中活动：{}， 价格不一致", skuInfoVo.getSkuId(), sku.getSupplierSkuId(), activityRecordByNow.getActivityId());
                    throw new ServiceException(OrderSubmitErrorEnum.SKU_PRICE_CHANGE.getMsg(), OrderSubmitErrorEnum.SKU_PRICE_CHANGE.getCode());
                }
            }
        }
        if (confirmOrderVo.getIsMkLimitQty()){
            CancelOrderItemVo cancelVo = new CancelOrderItemVo();
            cancelVo.setSkuName("");
            cancelVo.setRemark("限时优惠商品数量不足！");
            cancelList.add(cancelVo);
        }
        return cancelList;
    }

    private Boolean getOrderCancelTime(LocalTime localTime, String salesTimeEnd) {
        LocalTime endTime = LocalTime.parse(salesTimeEnd); // 12:30
        LocalTime fiveMinBeforeEnd = endTime.minusMinutes(5);
        if (!localTime.isBefore(fiveMinBeforeEnd) && localTime.isBefore(endTime)) {
            // 当前时间在 salesTimeEnd 前5分钟内
            return true;
        }
        return false;
    }

    /**
     * 存缓存
     *
     * @param confirmOrderVo
     */
    private void insertRedis(ConfirmOrderVo confirmOrderVo) {
        String redisName = RedisConstant.ORDER_CONFIRM_INFO + confirmOrderVo.getCode();
        RedisUtils.setCacheObject(redisName, confirmOrderVo, Duration.ofMinutes(60));
    }

    private ConfirmOrderVo calculateTotalAmount(ConfirmOrderVo confirmOrderVo) {
        List<ConfirmOrderInfoVo> confirmOrderInfoVos = confirmOrderVo.getConfirmOrderInfoVos();
        int sort = 0;
        for (ConfirmOrderInfoVo orderInfoVo : confirmOrderInfoVos) {
            //排序
            orderInfoVo.setSort(sort++);
            //商品实付
            BigDecimal productActual = orderInfoVo.getProductAmount().subtract(orderInfoVo.getProductFreeAmount());
            List<OrderCoupnSkuInfoVO> participateCoupons = orderInfoVo.getParticipateCoupons();
            boolean anyMatch = participateCoupons.stream().anyMatch(item -> Objects.equals(item.getIsHidden(), 1));
            orderInfoVo.setIsHiddenDiscount(anyMatch);
            BigDecimal regionSubsidy = participateCoupons.stream().filter(item -> Objects.equals(item.getCouponType()
                    , CouponTypeEnum.SUBSIDY_COUPON.getCode())).map(OrderCoupnSkuInfoVO::getDiscountAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal isHiddenRegionSubsidy = participateCoupons.stream().filter(item -> Objects.equals(item.getIsHidden(), 1))
                    .map(OrderCoupnSkuInfoVO::getDiscountAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            orderInfoVo.setRegionSubsidyAmount(regionSubsidy);
            orderInfoVo.setPriceFree(productActual.divide(BigDecimal.valueOf(orderInfoVo.getCount()), 2, RoundingMode.UP));
            //净果价
            orderInfoVo.setNetWeightPrice(orderInfoVo.getPriceFree().divide(orderInfoVo.getSpuNetWeight(),2, RoundingMode.HALF_UP));
            orderInfoVo.setFreightTotalAmount(orderInfoVo.getPlatformFreightAmount()
                    .add(orderInfoVo.getRegionFreightAmount()).add(orderInfoVo.getBaseFreightAmount()
                            .add(orderInfoVo.getPlatformFreightAmountLevel2())));
            if (regionSubsidy.compareTo(BigDecimal.ZERO) >= 0) {
                orderInfoVo.setFinalPrice(productActual.divide(BigDecimal.valueOf(orderInfoVo.getCount()), 2, RoundingMode.DOWN));
            }

            orderInfoVo.setFreightTotalFreeAmount(orderInfoVo.getPlatformFreightFreeAmount()
                    .add(orderInfoVo.getRegionFreightFreeAmount()).add(orderInfoVo.getBaseFreightFreeAmount())
                    .add(orderInfoVo.getPlatformFreightFreeAmountLevel2()));

            orderInfoVo.setFreeTotalAmount(orderInfoVo.getProductFreeAmount()
                    .add(orderInfoVo.getFreightTotalFreeAmount()).add(orderInfoVo.getPlatformServiceFreeAmount()));

            orderInfoVo.setOtherTotalAmount(orderInfoVo.getPlatformServiceAmount().add(orderInfoVo.getFreightTotalAmount()));
            orderInfoVo.setTotalAmount(orderInfoVo.getProductAmount()
                    .add(orderInfoVo.getOtherTotalAmount()).subtract(orderInfoVo.getFreeTotalAmount()));

            confirmOrderVo.setTotalGrossWeight(confirmOrderVo.getTotalGrossWeight().add(orderInfoVo.getSpuGrossWeight()
                    .multiply(BigDecimal.valueOf(orderInfoVo.getCount()))));
            confirmOrderVo.setTotalNetWeight(confirmOrderVo.getTotalNetWeight().add(orderInfoVo.getSpuNetWeight()
                    .multiply(BigDecimal.valueOf(orderInfoVo.getCount()))));
            confirmOrderVo.setProductAmount(confirmOrderVo.getProductAmount().add(orderInfoVo.getProductAmount()));
            confirmOrderVo.setProductFreeAmount(confirmOrderVo.getProductFreeAmount().add(orderInfoVo.getProductFreeAmount()));
            confirmOrderVo.setHiddenProductFreeAmount(confirmOrderVo.getHiddenProductFreeAmount().add(orderInfoVo.getProductFreeAmount().subtract(isHiddenRegionSubsidy)));

            confirmOrderVo.setBaseFreightAmount(confirmOrderVo.getBaseFreightAmount().add(orderInfoVo.getBaseFreightAmount()));
            confirmOrderVo.setBaseFreightFreeAmount(confirmOrderVo.getBaseFreightFreeAmount().add(orderInfoVo.getBaseFreightFreeAmount()));

            confirmOrderVo.setPlatformFreightAmount(confirmOrderVo.getPlatformFreightAmount().add(orderInfoVo.getPlatformFreightAmount()));
            confirmOrderVo.setPlatformFreightFreeAmount(confirmOrderVo.getPlatformFreightFreeAmount().add(orderInfoVo.getPlatformFreightFreeAmount()));

            confirmOrderVo.setPlatformFreightAmountLevel2(confirmOrderVo.getPlatformFreightAmountLevel2().add(orderInfoVo.getPlatformFreightAmountLevel2()));
            confirmOrderVo.setPlatformFreightFreeAmountLevel2(confirmOrderVo.getPlatformFreightFreeAmountLevel2().add(orderInfoVo.getPlatformFreightFreeAmountLevel2()));

            confirmOrderVo.setRegionFreightAmount(confirmOrderVo.getRegionFreightAmount().add(orderInfoVo.getRegionFreightAmount()));
            confirmOrderVo.setRegionFreightFreeAmount(confirmOrderVo.getRegionFreightFreeAmount().add(orderInfoVo.getRegionFreightFreeAmount()));

            confirmOrderVo.setPlatformServiceAmount(confirmOrderVo.getPlatformServiceAmount().add(orderInfoVo.getPlatformServiceAmount()));
            confirmOrderVo.setPlatformServiceFreeAmount(confirmOrderVo.getPlatformServiceFreeAmount().add(orderInfoVo.getPlatformServiceFreeAmount()));

            confirmOrderVo.setOtherTotalAmount(confirmOrderVo.getOtherTotalAmount().add(orderInfoVo.getOtherTotalAmount()));
            confirmOrderVo.setTotalAmount(confirmOrderVo.getTotalAmount().add(orderInfoVo.getTotalAmount()));
            confirmOrderVo.setCount(confirmOrderVo.getCount() + orderInfoVo.getCount());
            confirmOrderVo.setFreeTotalAmount(confirmOrderVo.getFreeTotalAmount().add(orderInfoVo.getFreeTotalAmount()));
            confirmOrderVo.setHiddenProductAmount(confirmOrderVo.getHiddenProductAmount().add(orderInfoVo.getProductAmount().subtract(isHiddenRegionSubsidy)));
            confirmOrderVo.setHiddenFreeTotalAmount(confirmOrderVo.getHiddenFreeTotalAmount().add(orderInfoVo.getFreeTotalAmount().subtract(isHiddenRegionSubsidy)));
            confirmOrderVo.setFreightTotalFreeAmount(confirmOrderVo.getFreightTotalFreeAmount().add(orderInfoVo.getFreightTotalFreeAmount()));
            confirmOrderVo.setFreightTotalAmount(confirmOrderVo.getFreightTotalAmount().add(orderInfoVo.getFreightTotalAmount()));

        }
        return confirmOrderVo;
    }

    /**
     * 金融手续费
     *
     * @param confirmOrderVo
     * @return
     */
    private ConfirmOrderVo financeCalculate(ConfirmOrderVo confirmOrderVo) {
        BigDecimal realRate;
        BigDecimal financialServiceRate = orderProperties.getFinancialServiceRate();
        if (Objects.isNull(confirmOrderVo.getFinancialServiceRate())) {
            //计算真实费率
            realRate = this.getRealRateV2(confirmOrderVo);
        } else {
            realRate = financialServiceRate;
        }
        //订单金融手续费
        BigDecimal financialServiceAmount = confirmOrderVo.getTotalAmount().multiply(realRate).divide(BigDecimal.ONE.subtract(realRate), 2, RoundingMode.HALF_UP);
        if (financialServiceAmount.compareTo(BigDecimal.ZERO) == 0) {
            financialServiceAmount = BigDecimal.ZERO;
        }
        //订单总金额 = 订单项总金额合计+金融手续费
        confirmOrderVo.setFinancialServiceAmount(financialServiceAmount);
        confirmOrderVo.setFinancialServiceRate(realRate);
        confirmOrderVo.setTotalAmount(confirmOrderVo.getTotalAmount().add(financialServiceAmount));

        String payChannel = getPayChannel(confirmOrderVo.getTotalAmount());
        confirmOrderVo.setPayChannel(payChannel);
        BigDecimal totalAmountLarge = confirmOrderVo.getTotalAmount().subtract(confirmOrderVo.getFinancialServiceAmount());
        confirmOrderVo.setTotalAmountLarge(totalAmountLarge);
        if (PayChannelEnum.PAY_PINGAN_CLOUD_LARGE.getCode().equals(payChannel)) {
            confirmOrderVo.setTotalAmount(totalAmountLarge);
            financialServiceAmount = BigDecimal.ZERO;
            confirmOrderVo.setFinancialServiceAmount(BigDecimal.ZERO);
        }

        //金融手续费分摊
        distributeFinancialServiceAmount(confirmOrderVo);
        return confirmOrderVo;
    }

    /**
     * 金融手续费分摊
     */
    private void distributeFinancialServiceAmount(ConfirmOrderVo confirmOrderVo) {
        //金融手续费和总金额为零的时候直接返回
        if (confirmOrderVo.getFinancialServiceAmount().compareTo(BigDecimal.ZERO) <= 0 || confirmOrderVo.getTotalAmount().compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }
        BigDecimal financialServiceAmount = confirmOrderVo.getFinancialServiceAmount();
        //金融手续费分摊
        List<ConfirmOrderInfoVo> confirmOrderInfoVos = confirmOrderVo.getConfirmOrderInfoVos();
        confirmOrderInfoVos.sort(Comparator.comparing(ConfirmOrderInfoVo::getTotalAmount));
        BigDecimal totalOrderAmount = confirmOrderVo.getTotalAmount();
        BigDecimal distributedSum = BigDecimal.ZERO;
        // 先按照比例分配
        for (int i = 0; i < confirmOrderInfoVos.size(); i++) {
            ConfirmOrderInfoVo item = confirmOrderInfoVos.get(i);
            BigDecimal ratio = item.getTotalAmount().divide(totalOrderAmount, 10, RoundingMode.HALF_UP);
            BigDecimal amount = ratio.multiply(financialServiceAmount).setScale(2, RoundingMode.HALF_UP);
            //最后一项直接相减
            if (i == confirmOrderInfoVos.size() - 1) {
                amount = financialServiceAmount.subtract(distributedSum);
            }
            item.setTotalAmount(item.getTotalAmount().add(amount));
            item.setOtherTotalAmount(item.getOtherTotalAmount().add(amount));
            item.setFinancialServiceAmount(amount);
            distributedSum = distributedSum.add(amount);
        }
        //重新排序
        confirmOrderInfoVos.sort(Comparator.comparing(ConfirmOrderInfoVo::getSort));
    }

    /**
     * 计算真实费率
     */
    private BigDecimal getRealRateV2(ConfirmOrderVo confirmOrderVo) {
        //开关
        Boolean rateSwitch = orderProperties.getRateSwitch();
        //真实费率
        BigDecimal financialServiceRate = orderProperties.getFinancialServiceRate();
        //最小金额
        BigDecimal minRate = orderProperties.getMinRate();
        //最大费率
        BigDecimal maxRate = orderProperties.getMaxRate();

        if (!rateSwitch) {
            return financialServiceRate;
        }
        BigDecimal minRateAmount = confirmOrderVo.getTotalAmount().multiply(minRate).divide(BigDecimal.ONE.subtract(minRate), 2, RoundingMode.HALF_UP);
        minRateAmount = minRateAmount.compareTo(BigDecimal.ZERO) == 0 ? new BigDecimal("0.01") : minRateAmount;
        BigDecimal maxRateAmount = confirmOrderVo.getTotalAmount().multiply(maxRate).divide(BigDecimal.ONE.subtract(maxRate), 2, RoundingMode.HALF_UP);
        maxRateAmount = maxRateAmount.compareTo(BigDecimal.ZERO) == 0 ? new BigDecimal("0.01") : maxRateAmount;
        confirmOrderVo.setRateFreeAmount(maxRateAmount.subtract(minRateAmount));
        if (!confirmOrderVo.getIsAuthorized()) {
            return maxRate;
        }
        //redis key
        String redisKey = String.format("%s%s", OrderCacheNames.FINANCIAL_SERVICE_IS_AUTH, LoginHelper.getUserId());
        Boolean cacheRateSwitch = RedisUtils.getCacheObject(redisKey);
        cacheRateSwitch = Optional.ofNullable(cacheRateSwitch).orElse(Boolean.FALSE);
        if (cacheRateSwitch && confirmOrderVo.getIsAuthorized()) {
            return minRate;
        }
        LoginUser loginUser = LoginHelper.getLoginUser();
        RetailInfoBo retailInfoBo = new RetailInfoBo();
        String openid = loginUser.getOpenid();
        if (StringUtils.isEmpty(openid)) {
            StpUtil.logout();
            throw new ServiceException("登录失效,请重新登录");
        }
        cacheRateSwitch = false;
        BigDecimal rate = maxRate;
        try {
            retailInfoBo.setPayerId(loginUser.getOpenid());
            retailInfoBo.setAppId(loginUser.getDeviceId());
            RetailInfoVo retailInfo = remotePaymentService.getRetailInfo(retailInfoBo);
            log.keyword("getRealRate", loginUser.getUserId()).info(JsonUtils.toJsonString(retailInfo));
            if (Objects.equals(retailInfo.getStatus(), RetailInfEnums.AUTHENTICATED.getStatus())) {
                cacheRateSwitch = Boolean.TRUE;
                rate = minRate;
            }
        } catch (Exception e) {
            log.keyword("getRealRate", loginUser.getUserId()).error("获取用户认证失败：{}", ExceptionUtils.getStackTrace(e));
        }
        RedisUtils.setCacheObject(redisKey, cacheRateSwitch, Duration.ofSeconds(orderProperties.getIsAuthTime()));
        return rate;
    }

    /**
     * 活动优惠券校验
     *
     * @param confirmOrderVo
     * @return
     */
    private ConfirmOrderVo couponCalculate(ConfirmOrderVo confirmOrderVo) {
        RemoteOrderDiscountMatchDTO remoteOrderDiscountMatchDTO = BeanUtil.copyProperties(confirmOrderVo, RemoteOrderDiscountMatchDTO.class);
        remoteOrderDiscountMatchDTO.setUserId(confirmOrderVo.getUserId());
        remoteOrderDiscountMatchDTO.setSelectCouponUserIds(confirmOrderVo.getSelectCouponUserIds());
        remoteOrderDiscountMatchDTO.setSaleDate(confirmOrderVo.getSaleDate());
        List<ConfirmOrderInfoVo> confirmOrderInfoVos = confirmOrderVo.getConfirmOrderInfoVos();
        if (CollectionUtil.isNotEmpty(confirmOrderInfoVos)) {
            // todo 如果商品已命中限时折扣活动，则剔除，不可参与用券
            List<RemoteOrderCouponMatchItemDTO> couponMatchItemDTOList = BeanUtil.copyToList(confirmOrderInfoVos.stream().filter(item -> !HAS_LIMIT_DISCOUNT.equals(item.getHasLimitDiscount())).toList(), RemoteOrderCouponMatchItemDTO.class);
            remoteOrderDiscountMatchDTO.setCouponMatchItemDTOList(couponMatchItemDTOList);
        }
        //匹配活动
        RemoteOrderResultVO discountMatch = discountService.orderDiscountMatch(remoteOrderDiscountMatchDTO);

        //计算优惠
        calculateCouponDiscountAmount(discountMatch, confirmOrderVo);

        return confirmOrderVo;
    }

    /**
     * 计算优惠
     *
     * @param discountMatch
     * @param confirmOrderVo
     */
    private void calculateCouponDiscountAmount(RemoteOrderResultVO discountMatch, ConfirmOrderVo confirmOrderVo) {
        //处理活动信息
        disposDiscount(confirmOrderVo, discountMatch);
        //处理赠送券
        disposeGiveCoupon(confirmOrderVo, discountMatch);
    }

    /**
     * 处理活动信息
     *
     * @param confirmOrderVo
     * @param discountMatch
     */
    private void disposDiscount(ConfirmOrderVo confirmOrderVo, RemoteOrderResultVO discountMatch) {
        List<RemoteOrderResultSkuVO> items = discountMatch.getItems();
        Map<Long, RemoteOrderResultSkuVO> resultSkuVOMap = items.stream().collect(Collectors.toMap(RemoteOrderResultSkuVO::getSkuId, item -> item));
        //
        List<RemoteCouponOrderVO> coupons = discountMatch.getCoupons();
        confirmOrderVo.setCoupons(coupons);
        List<RemoteCouponOrderVO> selectedCoupons = discountMatch.getSelectedCoupons();
        confirmOrderVo.setSelectedCoupons(selectedCoupons);
        List<RemoteCouponOrderVO> filteredCouponList = discountMatch.getFilteredCouponList();
        confirmOrderVo.setFilteredCouponList(filteredCouponList);

        List<ConfirmOrderInfoVo> confirmOrderInfoVos = confirmOrderVo.getConfirmOrderInfoVos();
        if (CollectionUtil.isEmpty(confirmOrderInfoVos)) {
            return;
        }
        for (ConfirmOrderInfoVo confirmOrderInfoVo : confirmOrderInfoVos) {
            RemoteOrderResultSkuVO remoteOrderResultSkuVO = resultSkuVOMap.get(confirmOrderInfoVo.getSkuId());
            if (Objects.isNull(remoteOrderResultSkuVO)) {
                continue;
            }
            confirmOrderInfoVo.setProductFreeAmount(confirmOrderInfoVo.getProductFreeAmount().add(remoteOrderResultSkuVO.getProductFreeAmount()));
            List<OrderCoupnSkuInfoVO> participateCoupons = remoteOrderResultSkuVO.getParticipateCoupons();
            participateCoupons.forEach(item -> item.setSkuId(confirmOrderInfoVo.getSkuId()));
            confirmOrderInfoVo.getParticipateCoupons().addAll(participateCoupons);
            BigDecimal hiddenFreeAmount = participateCoupons.stream().filter(item -> Objects.equals(item.getIsHidden(), 1))
                    .map(OrderCoupnSkuInfoVO::getDiscountAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            confirmOrderVo.setCouponFreeProductAmount(confirmOrderVo.getCouponFreeProductAmount().add(remoteOrderResultSkuVO.getProductFreeAmount()).subtract(hiddenFreeAmount));
        }
    }

    public void disposeGiveCoupon(ConfirmOrderVo confirmOrderVo, RemoteOrderResultVO discountMatch) {
        List<RemoteCouponOrderVO> coupons = discountMatch.getCoupons();

        List<RemoteCouponOrderVO> couponOrderVOS = coupons.stream().filter(item -> Objects.equals(item.getCouponType()
                , CouponTypeEnum.GIVE_COUPON.getCode())).collect(Collectors.toList());

        if (CollectionUtil.isEmpty(couponOrderVOS)) {
            return;
        }
        List<RemoteCouponOrderVO> selectedCoupons = discountMatch.getSelectedCoupons();
        List<Long> giveCouponUserIds = selectedCoupons.stream().filter(item -> Objects.equals(item.getCouponType()
                , CouponTypeEnum.GIVE_COUPON.getCode())).map(RemoteCouponOrderVO::getCouponUserId).collect(Collectors.toList());


        List<RemoteOrderResultSkuVO> remoteOrderResultSkuVOS = couponOrderVOS.stream().flatMap(item ->
                item.getGiveSku()
                        .stream().map(k -> {
                            OrderCoupnSkuInfoVO orderCoupnSkuInfoVO = new OrderCoupnSkuInfoVO();
                            orderCoupnSkuInfoVO.setCouponId(item.getCouponId());
                            orderCoupnSkuInfoVO.setCouponUserId(item.getCouponUserId());
                            orderCoupnSkuInfoVO.setSkuId(k.getSkuId());
                            orderCoupnSkuInfoVO.setFreePurchaseFee(item.getFreePurchaseFee());
                            orderCoupnSkuInfoVO.setFreeShippingFee(item.getFreeShippingFee());
                            orderCoupnSkuInfoVO.setCostAllocationRatio(item.getCostAllocationRatio());
                            k.getParticipateCoupons().add(orderCoupnSkuInfoVO);
                            return k;
                        })
        ).collect(Collectors.toList());

        List<Long> skuIds = remoteOrderResultSkuVOS.stream().map(RemoteOrderResultSkuVO::getSkuId).distinct().collect(Collectors.toList());
        List<RemoteSupplierSkuInfoVo> remoteSupplierSkuInfoVos = remoteSupplierSkuService.querySkuIds(skuIds);
        Map<Long, RemoteSupplierSkuInfoVo> skuInfoVoMap = remoteSupplierSkuInfoVos.stream().collect(Collectors.toMap(RemoteSupplierSkuInfoVo::getId, item -> item));
        Map<Long, List<RemoteOrderResultSkuVO>> skuMap = remoteOrderResultSkuVOS.stream().collect(Collectors.groupingBy(RemoteOrderResultSkuVO::getSkuId));
        for (Map.Entry<Long, List<RemoteOrderResultSkuVO>> entry : skuMap.entrySet()) {
            List<RemoteOrderResultSkuVO> resultSkuVOS = entry.getValue();

            RemoteOrderResultSkuVO remoteOrderResultSkuVO = resultSkuVOS.get(0);
            //商品信息
            RemoteSupplierSkuInfoVo skuInfoVo = skuInfoVoMap.get(entry.getKey());
            ConfirmOrderInfoVo giveProduct = new ConfirmOrderInfoVo();
            BeanUtil.copyProperties(skuInfoVo, giveProduct);
            //总件数
            int count = resultSkuVOS.stream().mapToInt(RemoteOrderResultSkuVO::getCount).sum();
            giveProduct.setCount(count);
            giveProduct.setPrice(skuInfoVo.getPrice());
            //商品金额
            giveProduct.setProductAmount(BigDecimal.valueOf(giveProduct.getCount()).multiply(giveProduct.getPrice()));
            giveProduct.setProductFreeAmount(BigDecimal.valueOf(giveProduct.getCount()).multiply(giveProduct.getPrice()));
            AtomicReference<Boolean> isSelected = new AtomicReference<>(false);
            List<OrderCoupnSkuInfoVO> coupnSkuInfoVOS = resultSkuVOS.stream().flatMap(item -> {
                List<OrderCoupnSkuInfoVO> participateCoupons = item.getParticipateCoupons();
                return participateCoupons.stream().map(k -> {
                    BigDecimal price = skuInfoVo.getPrice();
                    BigDecimal productDiscountAmount = price.multiply(BigDecimal.valueOf(remoteOrderResultSkuVO.getCount()));
                    k.setProductFreeAmount(productDiscountAmount);
                    RatioBo ratioBo = JSON.parseObject(k.getCostAllocationRatio(), RatioBo.class);
                    k.setProductAmountRegionAssume(productDiscountAmount.multiply(ratioBo.getCity().divide(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP)));
                    k.setProductAmountCityAssume(productDiscountAmount.multiply(ratioBo.getRegion().divide(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP)));
                    k.setProductAmountSupplierAssume(productDiscountAmount.subtract(k.getProductAmountRegionAssume()).subtract(k.getProductAmountCityAssume()));
                    k.setDiscountAmount(productDiscountAmount);
                    giveProduct.setFreeShippingFee(Objects.equals(k.getFreeShippingFee(), 1));
                    giveProduct.setFreePurchaseFee(Objects.equals(k.getFreePurchaseFee(), 1));
                    if (giveCouponUserIds.contains(k.getCouponUserId())) {
                        isSelected.set(true);
                    }
                    return k;
                });
            }).collect(Collectors.toList());
            giveProduct.setParticipateCoupons(coupnSkuInfoVOS);
            this.bulidOrderItem(giveProduct, skuInfoVo);
            giveProduct.setProdType(OrderProdTypeEnum.GIVE.getCode());
            BigDecimal productActual = giveProduct.getProductAmount().subtract(giveProduct.getProductFreeAmount());
            giveProduct.setPriceFree(productActual.divide(BigDecimal.valueOf(giveProduct.getCount()), 2, RoundingMode.UP));
            Boolean selected = isSelected.get();
            if (selected) {
                confirmOrderVo.getConfirmOrderInfoVos().add(giveProduct);
            }
            giveProduct.setSelected(selected);
            confirmOrderVo.getCouponConfirmOrderInfoVos().add(giveProduct);
        }
    }


    //设置运费和运费活动优惠
    private List<ConfirmOrderInfoVo> setFreightAndFreightActivity(ConfirmOrderVo confirmOrderVo, List<ConfirmOrderInfoVo> giveList) {
        List<ConfirmOrderInfoVo> notFree = new ArrayList<>();
        //获取基采物流
        List<Long> provideRegionWhIds = giveList.stream().filter(item -> Objects.equals(item.getBusinessType()
                , SupplierSkuBusinessTypeEnum.BUSINESS_TYPE20.getCode())).map(ConfirmOrderInfoVo::getProvideRegionWhId).distinct().collect(Collectors.toList());
        Map<Long, RemoteFreightLogisticsVo> remoteFreightLogisticsVoMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(provideRegionWhIds)) {
            RemoteFreightLogisticsQueryBo queryBo = new RemoteFreightLogisticsQueryBo();
            queryBo.setProvideRegionWhIds(provideRegionWhIds);
            queryBo.setRegionWhId(confirmOrderVo.getRegionWhId());
            List<RemoteFreightLogisticsVo> remoteFreightLogisticsVos = remoteFreightLogisticsService.queryList(queryBo);
            if (CollectionUtil.isEmpty(remoteFreightLogisticsVos)) {
                throw new ServiceException("基采物流不存在，请联系总仓工作人员！");
            }
            remoteFreightLogisticsVoMap = remoteFreightLogisticsVos.stream().collect(Collectors.toMap(RemoteFreightLogisticsVo
                    ::getProvideRegionWhId, item -> item, (v1, v2) -> v1));
        }

        for (ConfirmOrderInfoVo confirmOrderInfoVo : giveList) {
            OrderCoupnSkuInfoVO participateCoupons = confirmOrderInfoVo.getParticipateCoupons().get(0);

            boolean freePurchaseFee = Objects.equals(participateCoupons.getFreePurchaseFee(), 1);
            boolean freeShippingFee = Objects.equals(participateCoupons.getFreeShippingFee(), 1);

            //市采运费
            BigDecimal priceDate = Objects.equals(confirmOrderVo.getPriceMode(), PriceModeEnum.NUMBER.getCode()) ?
                    new BigDecimal(confirmOrderInfoVo.getCount()) :
                    confirmOrderInfoVo.getSpuGrossWeight().multiply(new BigDecimal(confirmOrderInfoVo.getCount()));
            BigDecimal platformFreightAmount = confirmOrderVo.getFreightPrice().multiply(priceDate).setScale(2, RoundingMode.HALF_UP);
            RatioBo ratioBo = JSON.parseObject(participateCoupons.getCostAllocationRatio(), RatioBo.class);
            confirmOrderInfoVo.setPlatformFreightAmount(platformFreightAmount);
            if (ObjectUtil.isNotEmpty(confirmOrderVo.getLogisticsIdLevel2())) {
                BigDecimal priceLevel2Date = Objects.equals(confirmOrderVo.getPriceModeLevel2(), PriceModeEnum.NUMBER.getCode()) ?
                        new BigDecimal(confirmOrderInfoVo.getCount()) :
                        confirmOrderInfoVo.getSpuGrossWeight().multiply(new BigDecimal(confirmOrderInfoVo.getCount()));
                BigDecimal platformFreightAmountLevel2 = confirmOrderVo.getFreightPriceLevel2().multiply(priceLevel2Date).setScale(2, RoundingMode.HALF_UP);
                confirmOrderInfoVo.setPlatformFreightAmountLevel2(platformFreightAmountLevel2);
                if (freeShippingFee) {
                    confirmOrderInfoVo.setPlatformFreightFreeAmountLevel2(platformFreightAmountLevel2);
                    participateCoupons.setPlatformFreightFreeAmountLevel2(platformFreightAmountLevel2);
                    participateCoupons.setPlatformFreightAmountLevel2CityAssume(platformFreightAmountLevel2.multiply(ratioBo.getCity().divide(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP)));
                    participateCoupons.setPlatformFreightAmountLevel2RegionAssume(platformFreightAmountLevel2.multiply(ratioBo.getRegion().divide(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP)));
                    participateCoupons.setPlatformFreightAmountLevel2SupplierAssume(platformFreightAmountLevel2.subtract(participateCoupons.getPlatformFreightAmountLevel2CityAssume()).subtract(participateCoupons.getPlatformFreightAmountLevel2RegionAssume()));
                }
            }
            //运费优惠承担
            if (freeShippingFee) {
                confirmOrderInfoVo.setPlatformFreightFreeAmount(platformFreightAmount);
                participateCoupons.setPlatformFreightFreeAmount(platformFreightAmount);
                participateCoupons.setPlatformFreightAmountCityAssume(platformFreightAmount.multiply(ratioBo.getCity().divide(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP)));
                participateCoupons.setPlatformFreightAmountRegionAssume(platformFreightAmount.multiply(ratioBo.getRegion().divide(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP)));
                participateCoupons.setPlatformFreightAmountSupplierAssume(platformFreightAmount.subtract(participateCoupons.getPlatformFreightAmountCityAssume()).subtract(participateCoupons.getPlatformFreightAmountRegionAssume()));
            }

            //基采运费
            RemoteFreightLogisticsVo remoteFreightLogisticsVo = remoteFreightLogisticsVoMap.get(confirmOrderInfoVo.getProvideRegionWhId());
            if (Objects.equals(confirmOrderInfoVo.getBusinessType(), SupplierSkuBusinessTypeEnum.BUSINESS_TYPE20.getCode())
                    && ObjectUtil.isNotEmpty(remoteFreightLogisticsVo)) {
                BigDecimal basePriceDate = Objects.equals(remoteFreightLogisticsVo.getPriceMode(), PriceModeEnum.NUMBER.getCode()) ?
                        new BigDecimal(confirmOrderInfoVo.getCount()) :
                        confirmOrderInfoVo.getSpuGrossWeight().multiply(new BigDecimal(confirmOrderInfoVo.getCount()));
                BigDecimal baseFreightAmount = remoteFreightLogisticsVo.getFreightPrice().multiply(basePriceDate).setScale(2, RoundingMode.HALF_UP);
                confirmOrderInfoVo.setBaseFreightAmount(baseFreightAmount);
                confirmOrderInfoVo.setProvideLogisticsId(remoteFreightLogisticsVo.getId());
                if (freeShippingFee) {
                    confirmOrderInfoVo.setBaseFreightFreeAmount(baseFreightAmount);
                    participateCoupons.setBaseFreightFreeAmount(baseFreightAmount);
                    participateCoupons.setBaseFreightAmountCityAssume(baseFreightAmount.multiply(ratioBo.getCity().divide(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP)));
                    participateCoupons.setBaseFreightAmountRegionAssume(baseFreightAmount.multiply(ratioBo.getRegion().divide(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP)));
                    participateCoupons.setBaseFreightAmountSupplierAssume(baseFreightAmount.subtract(participateCoupons.getBaseFreightAmountCityAssume()).subtract(participateCoupons.getBaseFreightAmountRegionAssume()));
                }
            }
            //订单项平台代采费
            BigDecimal platformServiceAmt = getPlatformServiceAmt(confirmOrderInfoVo.getPrice());
            //配货商品不算代采费
            platformServiceAmt = Objects.equals(confirmOrderInfoVo.getBusinessType(), SupplierSkuBusinessTypeEnum.BUSINESS_TYPE40.getCode()) ? BigDecimal.ZERO : platformServiceAmt;
            //代采费原价
            BigDecimal platformServiceAmount = platformServiceAmt.multiply(new BigDecimal(confirmOrderInfoVo.getCount()));
            confirmOrderInfoVo.setPlatformServiceAmount(platformServiceAmount);
            if (freePurchaseFee) {
                confirmOrderInfoVo.setPlatformServiceFreeAmount(platformServiceAmount);
                participateCoupons.setPlatformServiceFreeAmount(platformServiceAmount);
                participateCoupons.setPlatformServiceAmountCityAssume(platformServiceAmount.multiply(ratioBo.getCity().divide(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP)));
                participateCoupons.setPlatformServiceAmountRegionAssume(platformServiceAmount.multiply(ratioBo.getRegion().divide(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP)));
                participateCoupons.setPlatformServiceAmountSupplierAssume(platformServiceAmount.subtract(participateCoupons.getPlatformServiceAmountCityAssume()).subtract(participateCoupons.getPlatformServiceAmountRegionAssume()));
            }
            if (!freeShippingFee) {
                notFree.add(confirmOrderInfoVo);
            }
        }
        //非免运免代去匹配活动
        if (CollectionUtil.isNotEmpty(notFree)) {
            //匹配活动
            ConfirmOrderVo giveConfirm = BeanUtil.copyProperties(confirmOrderVo, ConfirmOrderVo.class);
            giveConfirm.setConfirmOrderInfoVos(giveList);
            this.activityCalculate(giveConfirm);
        }
        return giveList;
    }


    /**
     * 活动优惠
     *
     * @param confirmOrderVo
     * @return
     */
    private ConfirmOrderVo activityCalculate(ConfirmOrderVo confirmOrderVo) {
        if (CollectionUtil.isEmpty(confirmOrderVo.getConfirmOrderInfoVos())) {
            return confirmOrderVo;
        }
        //处理免运免代商品
        freeCalculate(confirmOrderVo);

        // 免运免代的规则
        RemoteRuleFreightServiceQueryBo ruleQueryBo = new RemoteRuleFreightServiceQueryBo().setRegionWhId(confirmOrderVo.getRegionWhId())
                .setCityWhId(confirmOrderVo.getCityWhId()).setCustomerId(confirmOrderVo.getCustomerId()).setLogisticsId(confirmOrderVo.getLogisticsId())
                .setPriceMode(confirmOrderVo.getPriceMode()).setSaleDate(confirmOrderVo.getSaleDate())
                .setSkuIds(confirmOrderVo.getConfirmOrderInfoVos().stream().filter(item -> {
                    //商品金额为零元算运费优惠
                    BigDecimal productActual = item.getProductAmount().subtract(item.getProductFreeAmount());
                    Boolean isZero = productActual.compareTo(BigDecimal.ZERO) != 0;
                    return !item.getFreeShippingFee() && isZero;
                }).map(vo -> new RemoteRuleFreightServiceSkuBo()
                        .setSkuId(vo.getSupplierSkuId()).setCount(vo.getCount()).setSpuId(vo.getSpuId())).toList());
        Map<Long, RemoteRuleFreightPriceVo> ruleFreightPriceVoMap = remoteRuleFreightServiceService.querySkuFreightPriceMap(ruleQueryBo);
        log.keyword("querySkuFreightPriceMap").info(JSON.toJSONString(ruleFreightPriceVoMap));

        //免代服务
        Map<Long, RemoteRuleServiceAmtVo> ruleServiceAmtVoMap = remoteRuleFreightServiceService.querySkuServiceAmtMap(ruleQueryBo);
        log.keyword("querySkuServiceAmtMap").info(JSON.toJSONString(ruleServiceAmtVoMap));

        List<ConfirmOrderInfoVo> confirmOrderInfoVos = confirmOrderVo.getConfirmOrderInfoVos();
        for (ConfirmOrderInfoVo orderInfoVo : confirmOrderInfoVos) {
            // todo 限时折扣迭代开发，直接取
            List<ActivityRecord> activityRecords = orderInfoVo.getActivityRecord();
            // 免运规则
            RemoteRuleFreightPriceVo ruleFreightPriceVo = ruleFreightPriceVoMap.get(orderInfoVo.getSupplierSkuId());
            if (Objects.nonNull(ruleFreightPriceVo) && ruleFreightPriceVo.getFreightPrice().compareTo(confirmOrderVo.getFreightPrice()) < 0
                    && !orderInfoVo.getFreeShippingFee()) {
                //活动信息
                BigDecimal ruleFreightPrice = Objects.equals(ruleFreightPriceVo.getPriceMode(), PriceModeEnum.NUMBER.getCode()) ?
                        new BigDecimal(orderInfoVo.getCount()) :
                        orderInfoVo.getSpuGrossWeight().multiply(new BigDecimal(orderInfoVo.getCount()));
                BigDecimal itemPlatformFreightAmount = ruleFreightPriceVo.getFreightPrice().multiply(ruleFreightPrice).setScale(2, RoundingMode.HALF_UP);

                BigDecimal platformFreightFree = orderInfoVo.getPlatformFreightAmount().subtract(itemPlatformFreightAmount);
                platformFreightFree = platformFreightFree.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : platformFreightFree;
                //构建活动数据
                ActivityRecord activityRecord = buildActivityRecord(ruleFreightPriceVo.getId(), platformFreightFree
                        , ActivityRecordTypeEnum.FREE_FREIGHT.getCode(), orderInfoVo.getSupplierSkuId());
                orderInfoVo.setPlatformFreightFreeAmount(platformFreightFree);
                activityRecord.setPlatformFreightFreeAmount(platformFreightFree);
                activityRecords.add(activityRecord);
                orderInfoVo.setFreightDiscountMinNum(ruleFreightPriceVo.getMinNum());
            }

            RemoteRuleServiceAmtVo ruleServiceAmtVo = ruleServiceAmtVoMap.get(orderInfoVo.getSupplierSkuId());
            BigDecimal platformServiceAmt = orderInfoVo.getPlatformServiceAmt();
            if (Objects.nonNull(ruleServiceAmtVo) && ruleServiceAmtVo.getServiceAmt().compareTo(platformServiceAmt) < 0
                    && !orderInfoVo.getFreeShippingFee()) {
                platformServiceAmt = ruleServiceAmtVo.getServiceAmt();
                BigDecimal platformServiceAmtFree = platformServiceAmt.multiply(new BigDecimal(orderInfoVo.getCount()));
                BigDecimal platformServiceFree = orderInfoVo.getPlatformServiceAmount().subtract(platformServiceAmtFree);
                platformServiceFree = platformServiceFree.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : platformServiceFree;
                //构建活动数据
                ActivityRecord activityRecord = buildActivityRecord(ruleServiceAmtVo.getId(), platformServiceFree
                        , ActivityRecordTypeEnum.FREE_SERVICE.getCode(), orderInfoVo.getSupplierSkuId());
                orderInfoVo.setPlatformServiceFreeAmount(platformServiceFree);
                activityRecord.setPlatformServiceFreeAmount(platformServiceFree);
                activityRecords.add(activityRecord);
            }
            orderInfoVo.setActivityRecord(activityRecords);
        }
        return confirmOrderVo;
    }

    /**
     * 处理免运免代商品
     *
     * @param confirmOrderVo
     * @return
     */
    private void freeCalculate(ConfirmOrderVo confirmOrderVo) {
        List<ConfirmOrderInfoVo> confirmOrderInfoVos = confirmOrderVo.getConfirmOrderInfoVos();
        if (CollectionUtils.isEmpty(confirmOrderInfoVos)) {
            return;
        }
        //免运免代只有再优惠券才有，赠品设置
        for (ConfirmOrderInfoVo orderInfoVo : confirmOrderInfoVos) {
            List<OrderCoupnSkuInfoVO> participateCoupons = orderInfoVo.getParticipateCoupons();
            if (CollectionUtil.isEmpty(participateCoupons)) {
                continue;
            }
            OrderCoupnSkuInfoVO participateCoupon = participateCoupons.get(0);
            //承担优惠
            RatioBo ratioBo = JSON.parseObject(participateCoupon.getCostAllocationRatio(), RatioBo.class);
            //商品金额为零元算运费优惠
            BigDecimal productActual = orderInfoVo.getProductAmount().subtract(orderInfoVo.getProductFreeAmount());
            Boolean isZero = productActual.compareTo(BigDecimal.ZERO) == 0;

            //免运
            if (orderInfoVo.getFreeShippingFee() || isZero) {
                log.keyword("getFreeShippingFee", confirmOrderVo.getCode(), orderInfoVo.getSupplierSkuId()).info("productZero:{},productActual:{}", JSON.toJSONString(orderInfoVo), productActual);
                BigDecimal platformFreightAmount = orderInfoVo.getPlatformFreightAmount();
                orderInfoVo.setPlatformFreightFreeAmount(platformFreightAmount);
                participateCoupon.setPlatformFreightFreeAmount(platformFreightAmount);
                participateCoupon.setDiscountAmount(participateCoupon.getDiscountAmount().add(platformFreightAmount));
                participateCoupon.setPlatformFreightAmountCityAssume(platformFreightAmount.multiply(ratioBo.getCity()
                        .divide(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP)));
                participateCoupon.setPlatformFreightAmountRegionAssume(platformFreightAmount.multiply(ratioBo.getRegion()
                        .divide(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP)));
                participateCoupon.setPlatformFreightAmountSupplierAssume(platformFreightAmount
                        .subtract(participateCoupon.getPlatformFreightAmountCityAssume()).subtract(participateCoupon.getPlatformFreightAmountRegionAssume()));

                BigDecimal platformFreightAmountLevel2 = orderInfoVo.getPlatformFreightAmountLevel2();
                if (platformFreightAmountLevel2.compareTo(BigDecimal.ZERO) > 0) {
                    orderInfoVo.setPlatformFreightFreeAmountLevel2(platformFreightAmountLevel2);
                    participateCoupon.setDiscountAmount(participateCoupon.getDiscountAmount().add(platformFreightAmountLevel2));
                    participateCoupon.setPlatformFreightFreeAmountLevel2(platformFreightAmountLevel2);
                    participateCoupon.setPlatformFreightAmountLevel2CityAssume(platformFreightAmountLevel2.multiply(ratioBo.getCity()
                            .divide(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP)));
                    participateCoupon.setPlatformFreightAmountLevel2RegionAssume(platformFreightAmountLevel2.multiply(ratioBo.getRegion()
                            .divide(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP)));
                    participateCoupon.setPlatformFreightAmountLevel2SupplierAssume(platformFreightAmountLevel2.subtract(participateCoupon
                            .getPlatformFreightAmountLevel2CityAssume()).subtract(participateCoupon.getPlatformFreightAmountLevel2RegionAssume()));
                }

                BigDecimal baseFreightAmount = orderInfoVo.getBaseFreightAmount();
                if (baseFreightAmount.compareTo(BigDecimal.ZERO) > 0) {
                    orderInfoVo.setBaseFreightFreeAmount(baseFreightAmount);
                    participateCoupon.setDiscountAmount(participateCoupon.getDiscountAmount().add(baseFreightAmount));
                    participateCoupon.setBaseFreightFreeAmount(baseFreightAmount);
                    participateCoupon.setBaseFreightAmountCityAssume(baseFreightAmount.multiply(ratioBo.getCity()
                            .divide(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP)));
                    participateCoupon.setBaseFreightAmountRegionAssume(baseFreightAmount.multiply(ratioBo.getRegion()
                            .divide(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP)));
                    participateCoupon.setBaseFreightAmountSupplierAssume(baseFreightAmount.subtract(participateCoupon
                            .getBaseFreightAmountCityAssume()).subtract(participateCoupon.getBaseFreightAmountRegionAssume()));
                }
            }
            //免代
            if (orderInfoVo.getFreePurchaseFee() || isZero) {
                log.keyword("getFreePurchaseFee", confirmOrderVo.getCode(), orderInfoVo.getSupplierSkuId()).info("productZero:{},productActual:{}", JSON.toJSONString(orderInfoVo), productActual);

                BigDecimal platformServiceAmount = orderInfoVo.getPlatformServiceAmount();
                orderInfoVo.setPlatformServiceFreeAmount(platformServiceAmount);
                participateCoupon.setDiscountAmount(participateCoupon.getDiscountAmount().add(platformServiceAmount));
                participateCoupon.setPlatformServiceFreeAmount(platformServiceAmount);
                participateCoupon.setPlatformServiceAmountCityAssume(platformServiceAmount.multiply(ratioBo.getCity()
                        .divide(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP)));
                participateCoupon.setPlatformServiceAmountRegionAssume(platformServiceAmount.multiply(ratioBo.getRegion()
                        .divide(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP)));
                participateCoupon.setPlatformServiceAmountSupplierAssume(platformServiceAmount.subtract(participateCoupon
                        .getPlatformServiceAmountCityAssume()).subtract(participateCoupon.getPlatformServiceAmountRegionAssume()));
            }
        }
    }

    /**
     * 计算运费
     *
     * @param confirmOrderVo
     * @return
     */
    private ConfirmOrderVo freightCalculate(ConfirmOrderVo confirmOrderVo) {
        //商品信息
        List<ConfirmOrderInfoVo> confirmOrderInfoVos = confirmOrderVo.getConfirmOrderInfoVos();


        //二级物流
        RemoteRegionLogisticsVo logistics2Vo = null;
        if (ObjectUtil.isNotEmpty(confirmOrderVo.getPlaceIdLevel2())) {
            RemoteRegionLogisticsQueryBo logistics = new RemoteRegionLogisticsQueryBo();
            logistics.setCityWhId(confirmOrderVo.getCityWhId());
            logistics.setParentPlaceId(confirmOrderVo.getPlaceId());
            logistics.setPlaceId(confirmOrderVo.getPlaceIdLevel2());
            logistics.setLogisticsType(1);
            logistics2Vo = remoteRegionLogisticsService.queryList(logistics)
                    .stream().findFirst().orElseThrow(() -> new ServiceException("二级物流不存在"));
            confirmOrderVo.setLogisticsIdLevel2(logistics2Vo.getId());
            confirmOrderVo.setPriceModeLevel2(logistics2Vo.getPriceMode());
            confirmOrderVo.setFreightPriceLevel2(logistics2Vo.getCityFreightPrice());
        }
        if (ObjectUtil.isNull(confirmOrderInfoVos)) {
            return confirmOrderVo;
        }
        //获取基采物流
        List<Long> provideRegionWhIds = confirmOrderInfoVos.stream().filter(item -> Objects.equals(item.getBusinessType()
                , SupplierSkuBusinessTypeEnum.BUSINESS_TYPE20.getCode())).map(ConfirmOrderInfoVo::getProvideRegionWhId).distinct().collect(Collectors.toList());
        Map<Long, RemoteFreightLogisticsVo> remoteFreightLogisticsVoMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(provideRegionWhIds)) {
            RemoteFreightLogisticsQueryBo queryBo = new RemoteFreightLogisticsQueryBo();
            queryBo.setProvideRegionWhIds(provideRegionWhIds);
            queryBo.setRegionWhId(confirmOrderVo.getRegionWhId());
            List<RemoteFreightLogisticsVo> remoteFreightLogisticsVos = remoteFreightLogisticsService.queryList(queryBo);
            if (CollectionUtil.isEmpty(remoteFreightLogisticsVos)) {
                throw new ServiceException("基采物流不存在，请联系总仓工作人员！");
            }
            remoteFreightLogisticsVoMap = remoteFreightLogisticsVos.stream().collect(Collectors.toMap(RemoteFreightLogisticsVo
                    ::getProvideRegionWhId, item -> item, (v1, v2) -> v1));
        }

        for (ConfirmOrderInfoVo confirmOrderInfoVo : confirmOrderInfoVos) {
            //市采运费
            BigDecimal priceDate = Objects.equals(confirmOrderVo.getPriceMode(), PriceModeEnum.NUMBER.getCode()) ?
                    new BigDecimal(confirmOrderInfoVo.getCount()) :
                    confirmOrderInfoVo.getSpuGrossWeight().multiply(new BigDecimal(confirmOrderInfoVo.getCount()));
            BigDecimal platformFreightAmount = confirmOrderVo.getFreightPrice().multiply(priceDate).setScale(2, RoundingMode.HALF_UP);
            confirmOrderInfoVo.setPlatformFreightAmount(platformFreightAmount);

            if (ObjectUtil.isNotEmpty(logistics2Vo)) {
                BigDecimal priceLevel2Date = Objects.equals(logistics2Vo.getPriceMode(), PriceModeEnum.NUMBER.getCode()) ?
                        new BigDecimal(confirmOrderInfoVo.getCount()) :
                        confirmOrderInfoVo.getSpuGrossWeight().multiply(new BigDecimal(confirmOrderInfoVo.getCount()));
                BigDecimal platformFreightAmountLevel2 = logistics2Vo.getCityFreightPrice().multiply(priceLevel2Date).setScale(2, RoundingMode.HALF_UP);
                confirmOrderInfoVo.setPlatformFreightAmountLevel2(platformFreightAmountLevel2);
            }
            //基采运费
            RemoteFreightLogisticsVo remoteFreightLogisticsVo = remoteFreightLogisticsVoMap.get(confirmOrderInfoVo.getProvideRegionWhId());
            if (Objects.equals(confirmOrderInfoVo.getBusinessType(), SupplierSkuBusinessTypeEnum.BUSINESS_TYPE20.getCode())
                    && ObjectUtil.isNull(remoteFreightLogisticsVo)) {
                throw new ServiceException("基采物流不存在，请联系总仓工作人员！");
            }
            if (Objects.equals(confirmOrderInfoVo.getBusinessType(), SupplierSkuBusinessTypeEnum.BUSINESS_TYPE20.getCode())) {
                BigDecimal basePriceDate = Objects.equals(remoteFreightLogisticsVo.getPriceMode(), PriceModeEnum.NUMBER.getCode()) ?
                        new BigDecimal(confirmOrderInfoVo.getCount()) :
                        confirmOrderInfoVo.getSpuGrossWeight().multiply(new BigDecimal(confirmOrderInfoVo.getCount()));
                BigDecimal baseFreightAmount = remoteFreightLogisticsVo.getFreightPrice().multiply(basePriceDate).setScale(2, RoundingMode.HALF_UP);
                confirmOrderInfoVo.setBaseFreightAmount(baseFreightAmount);
                confirmOrderInfoVo.setProvideLogisticsId(remoteFreightLogisticsVo.getId());
            }
            //商品实付
            BigDecimal productActual = confirmOrderInfoVo.getProductAmount().subtract(confirmOrderInfoVo.getProductFreeAmount());
            BigDecimal priceFree = productActual.divide(BigDecimal.valueOf(confirmOrderInfoVo.getCount()), 2, RoundingMode.DOWN);
            //订单项平台代采费
            OrderSettkeInfBO platformServiceAmtBo = getPlatformServiceAmtBo(priceFree);
            BigDecimal platformServiceAmt = platformServiceAmtBo.getPrice();
            //配货商品不算代采费
            platformServiceAmt = Objects.equals(confirmOrderInfoVo.getBusinessType(), SupplierSkuBusinessTypeEnum.BUSINESS_TYPE40.getCode())
                    || Objects.equals(confirmOrderInfoVo.getSaleType(), SupplierSkuSaleTypeEnum.SALE_TYPE2.getCode())
                    ? BigDecimal.ZERO : platformServiceAmt;
            //代采费原价
            BigDecimal platformServiceAmount = platformServiceAmt.multiply(new BigDecimal(confirmOrderInfoVo.getCount()));
            confirmOrderInfoVo.setPlatformServiceAmount(platformServiceAmount);
            //结算件数
            confirmOrderInfoVo.setSettleNumber(platformServiceAmtBo.getCount().multiply(new BigDecimal(confirmOrderInfoVo.getCount())));
            confirmOrderInfoVo.setPlatformServiceAmt(platformServiceAmt);
        }
        return confirmOrderVo;
    }

    private ConfirmOrderVo initConfirmOrder(ConfirmOrderDTO confirmOrderDTO) {
        //查询总仓信息
        RemoteRegionWhVo regionWhVo = remoteRegionWhService.queryById(confirmOrderDTO.getRegionWhId());
        if (regionWhVo == null) {
            throw new ServiceException("总仓不存在");
        }
        if (!StatusEnum.ENABLE.getCode().equals(regionWhVo.getStatus())) {
            throw new ServiceException("总仓已被禁用");
        }
        //检查是否可以加购物车，是否可以下单
        checkIfSale(regionWhVo.getSalesTimeStart(), regionWhVo.getSalesTimeEnd());
        ConfirmOrderVo confirmOrderVo = new ConfirmOrderVo();
        //客户信息
        Long customerId = remoteBasCustomerService.getIdById(confirmOrderDTO.getCustomerId());
        if (Objects.isNull(customerId)) {
            throw new ServiceException("客户不存在");
        }
        confirmOrderVo.setCustomerId(customerId);
        confirmOrderVo.setUserId(confirmOrderDTO.getUserId());
        confirmOrderVo.setIsAuthorized(confirmOrderDTO.getIsAuthorized());
        confirmOrderVo.setOrderChannel(confirmOrderDTO.getOrderChannel());

        confirmOrderVo.setRegionWhId(regionWhVo.getId());
        confirmOrderVo.setCityWhId(confirmOrderDTO.getCityWhId());
        LocalDate saleDate = SaleDateUtil.getSaleDate(regionWhVo.getSalesTimeEnd());
        confirmOrderVo.setSaleDate(saleDate);
        confirmOrderVo.setCode(CustomNoUtil.getOrderNo(saleDate));
        //购物车列表
        List<CartItemVo> cartItemVoList = new ArrayList<>();
        //后面抽出来根据下单渠道构建对应的购物车
        //赠品job下单是直接用的券的方式，所以要清空购物车
        if (!Objects.equals(confirmOrderDTO.getOrderChannel(), OrderChannelEnum.GIVE_JOB.getCode())) {
            //购物车列表
            cartItemVoList = iCartItemService.listCacheCartItemList(confirmOrderVo.getRegionWhId(), confirmOrderVo.getUserId(), confirmOrderVo.getCityWhId());
        }

        //查询商品信息
        RemoteCityWhPlaceVo remoteCityWhPlaceVo = remoteCityWhPlaceService.queryById(confirmOrderDTO.getPlaceId());
        RemoteCityWhPlaceVo remoteCityWhPlaceLevel2 = null;
        Long parentPlaceId = remoteCityWhPlaceVo.getParentPlaceId();
        if (Objects.nonNull(parentPlaceId)) {
            remoteCityWhPlaceLevel2 = remoteCityWhPlaceVo;
            remoteCityWhPlaceVo = remoteCityWhPlaceService.queryById(parentPlaceId);
        }
        confirmOrderVo.setPlaceId(remoteCityWhPlaceVo.getId());
        confirmOrderVo.setPlaceName(remoteCityWhPlaceVo.getPlaceName());
        confirmOrderVo.setAddress(remoteCityWhPlaceVo.getAddress());
        confirmOrderVo.setPlacePath(remoteCityWhPlaceVo.getPath());

        if (Objects.nonNull(remoteCityWhPlaceLevel2)) {
            confirmOrderVo.setPlaceIdLevel2(remoteCityWhPlaceLevel2.getId());
            confirmOrderVo.setPlaceNameLevel2(remoteCityWhPlaceLevel2.getPlaceName());
            confirmOrderVo.setAddressLevel2(remoteCityWhPlaceLevel2.getAddress());
            confirmOrderVo.setPlacePath(remoteCityWhPlaceLevel2.getPath());
        }

        confirmOrderVo.setSelectCouponUserIds(confirmOrderDTO.getSelectCouponUserIds());
        //失效的商品
        List<CartItemVo> disabledItems = cartItemVoList.stream().filter(item -> Objects.equals(item.getIsExpire(), 1)).collect(Collectors.toList());
        confirmOrderVo.setDisabledItems(disabledItems);


        //查询物流线
        RemoteRegionLogisticsQueryBo logisticsQueryBo = new RemoteRegionLogisticsQueryBo();
        logisticsQueryBo.setRegionWhId(confirmOrderVo.getRegionWhId());
        logisticsQueryBo.setCityWhId(confirmOrderVo.getCityWhId());
        logisticsQueryBo.setPlaceId(confirmOrderVo.getPlaceId());
        RemoteRegionLogisticsVo logisticsVo = remoteRegionLogisticsService.queryList(logisticsQueryBo)
                .stream().findFirst().orElseThrow(() -> new ServiceException("物流线不存在"));
        confirmOrderVo.setLogisticsId(logisticsVo.getId());
        confirmOrderVo.setPriceMode(logisticsVo.getPriceMode());
        confirmOrderVo.setFreightPrice(logisticsVo.getCityFreightPrice());

        //需要计算的接口
        List<CartItemVo> cartItemVos = cartItemVoList.stream().filter(item -> Objects.equals(item.getIsExpire(), 0)).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(cartItemVos)) {
            return confirmOrderVo;
        }

        //实付设置吨位
        List<Long> isConfig = remoteRegionLogisticsService.checkForSalePage(confirmOrderVo.getPlaceId(), Collections.singletonList(confirmOrderVo.getRegionWhId()));

        //计算的商品
        List<ConfirmOrderInfoVo> confirmOrderInfoVos = new ArrayList<>();
        for (CartItemVo cartItemVo : cartItemVos) {
            ConfirmOrderInfoVo confirmOrderInfoVo = new ConfirmOrderInfoVo();
            BeanUtil.copyProperties(cartItemVo, confirmOrderInfoVo);
            if (CollectionUtils.isEmpty(isConfig)) {
                confirmOrderInfoVo.setHasLogistics(0);
            }
            BigDecimal skuSubsidy = cartItemVo.getSkuSubsidy();
            BigDecimal price = cartItemVo.getPrice();
            //商品价格不能小于零
            if (price.compareTo(BigDecimal.ZERO) < 0){
                throw new ServiceException("商品价格有误");
            }
            //如果小于零，则设置为零
            skuSubsidy = (price.subtract(skuSubsidy)).compareTo(BigDecimal.ZERO) < 0 ? price.subtract(BigDecimal.valueOf(0.01)) : skuSubsidy;

            confirmOrderInfoVo.setFinalPrice(cartItemVo.getPrice().subtract(skuSubsidy));
            confirmOrderInfoVo.setPrice(cartItemVo.getPrice().subtract(skuSubsidy));
            confirmOrderInfoVo.setSkuSubsidyAmount(skuSubsidy.multiply(BigDecimal.valueOf(cartItemVo.getCount())));
            confirmOrderInfoVo.setCount(cartItemVo.getCount());
            confirmOrderInfoVo.setHideMerchant(cartItemVo.getHideMerchant());
            confirmOrderInfoVo.setSupplierId(cartItemVo.getSupplierId());
            confirmOrderInfoVo.setSupplierDeptId(cartItemVo.getSupplierDeptId());
            confirmOrderInfoVo.setCategoryId(cartItemVo.getCategoryId());
            confirmOrderInfoVo.setCategoryIdLevel1(cartItemVo.getCategoryIdLevel1());
            confirmOrderInfoVo.setCategoryIdLevel2(cartItemVo.getCategoryIdLevel2());
            confirmOrderInfoVo.setCategoryPathName(cartItemVo.getCategoryPathName());
            confirmOrderInfoVo.setProductAmount(confirmOrderInfoVo.getPrice().multiply(BigDecimal.valueOf(cartItemVo.getCount())));
            confirmOrderInfoVo.setSaleDate(saleDate);
            confirmOrderInfoVo.setSupplierAlias(cartItemVo.getSupplierAlias());
            confirmOrderInfoVo.setSkuId(cartItemVo.getSkuId());
            confirmOrderInfoVo.setCategoryCode(cartItemVo.getCategoryCode());
            confirmOrderInfoVo.setBusinessType(cartItemVo.getBusinessType());
            confirmOrderInfoVo.setSaleType(cartItemVo.getSaleType());
            confirmOrderInfoVo.setSupplierSkuId(cartItemVo.getSupplierSkuId());
            confirmOrderInfoVo.setSpuId(cartItemVo.getSpuId());
            confirmOrderInfoVo.setSpuCode(cartItemVo.getSpuCode());
            confirmOrderInfoVo.setSpuName(cartItemVo.getSpuName());
            confirmOrderInfoVo.setBuyerCode(cartItemVo.getBuyerCode());
            confirmOrderInfoVo.setBuyerName(cartItemVo.getBuyerName());
            confirmOrderInfoVo.setImgUrl(cartItemVo.getImgUrl());
            confirmOrderInfoVo.setSpuGrossWeight(cartItemVo.getSpuGrossWeight());
            confirmOrderInfoVo.setShouguangVegetables(cartItemVo.getShouguangVegetables());
            confirmOrderInfoVo.setSpuNetWeight(cartItemVo.getSpuNetWeight());
            confirmOrderInfoVo.setProvideRegionWhId(cartItemVo.getProvideRegionWhId());
            confirmOrderInfoVo.setRegionWhId(cartItemVo.getRegionWhId());
            confirmOrderInfoVo.setSkuCode(cartItemVo.getSkuCode());
            confirmOrderInfoVo.setSupplierSpuCode(cartItemVo.getSupplierSpuCode());
            confirmOrderInfoVo.setSupplierSpuId(cartItemVo.getSupplierSpuId());
            confirmOrderInfoVo.setStock(cartItemVo.getStock());
            confirmOrderInfoVo.setLockStock(cartItemVo.getLockStock());
            confirmOrderInfoVo.setSold(cartItemVo.getSold());
            confirmOrderInfoVos.add(confirmOrderInfoVo);
        }
        confirmOrderVo.setConfirmOrderInfoVos(confirmOrderInfoVos);

        return confirmOrderVo;
    }

    private void bulidOrderItem(ConfirmOrderInfoVo confirmOrderInfoVo, RemoteSupplierSkuInfoVo skuInfoVo) {
        confirmOrderInfoVo.setSkuId(skuInfoVo.getId());
        confirmOrderInfoVo.setCategoryCode(skuInfoVo.getCode());
        confirmOrderInfoVo.setBusinessType(skuInfoVo.getBusinessType());
        confirmOrderInfoVo.setSupplierSkuId(skuInfoVo.getSupplierSkuId());
        confirmOrderInfoVo.setSpuId(skuInfoVo.getSpuId());
        confirmOrderInfoVo.setSpuCode(skuInfoVo.getSpuCode());
        confirmOrderInfoVo.setSpuName(skuInfoVo.getSpuName());
        confirmOrderInfoVo.setBuyerCode(skuInfoVo.getBuyerCode());
        confirmOrderInfoVo.setBuyerName(skuInfoVo.getBuyerName());
        confirmOrderInfoVo.setImgUrl(skuInfoVo.getImgUrl());
        confirmOrderInfoVo.setFinalPrice(skuInfoVo.getPrice());
        confirmOrderInfoVo.setSpuGrossWeight(skuInfoVo.getSpuGrossWeight());
        confirmOrderInfoVo.setPrice(skuInfoVo.getPrice());
        confirmOrderInfoVo.setShouguangVegetables(skuInfoVo.getShouguangVegetables());
        confirmOrderInfoVo.setSpuNetWeight(skuInfoVo.getSpuNetWeight());
        confirmOrderInfoVo.setCategoryId(skuInfoVo.getCategoryId());
        confirmOrderInfoVo.setProvideRegionWhId(skuInfoVo.getProvideRegionWhId());
        confirmOrderInfoVo.setRegionWhId(skuInfoVo.getRegionWhId());
        confirmOrderInfoVo.setCategoryIdLevel2(skuInfoVo.getCategoryIdLevel2());
        confirmOrderInfoVo.setCategoryIdLevel1(skuInfoVo.getCategoryIdLevel1());
        confirmOrderInfoVo.setCategoryPathName(skuInfoVo.getCategoryPathName());
        confirmOrderInfoVo.setSkuCode(skuInfoVo.getCode());
        confirmOrderInfoVo.setSupplierId(skuInfoVo.getSupplierId());
        confirmOrderInfoVo.setSupplierDeptId(skuInfoVo.getSupplierDeptId());
        confirmOrderInfoVo.setSupplierSkuId(skuInfoVo.getSupplierSkuId());
        confirmOrderInfoVo.setSupplierSpuCode(skuInfoVo.getSupplierSpuCode());
        confirmOrderInfoVo.setSupplierSpuId(skuInfoVo.getSupplierSpuId());
    }

    private Boolean checkSku(RemoteSupplierSkuInfoVo skuInfoVo, ConfirmOrderInfoVo confirmOrderInfoVo
            , LocalDate saleDate, Long regionWhId, SkuBo sku, Map<Long, Integer> skuCountMap) {
        Boolean isDisabled = false;
        //总仓id是否一致
        if (!skuInfoVo.getRegionWhId().equals(regionWhId)) {
            isDisabled = true;
        }
        //销售日有误
        if (!saleDate.isEqual(skuInfoVo.getSaleDate())) {
            isDisabled = true;
        }
        //是否下架
        if (!SupplierSkuStatusEnum.STATUS4.getCode().equals(skuInfoVo.getStatus())) {
            confirmOrderInfoVo.setRemark("已下架");
            isDisabled = true;
        }
        //是否售罄
        if (skuInfoVo.getStock() == 0) {
            confirmOrderInfoVo.setRemark("已售罄");
            isDisabled = true;
        }
        //是否库存不足
        if (skuInfoVo.getStock() < sku.getCount()) {
            confirmOrderInfoVo.setRemark("库存不足");
            isDisabled = true;
        }
        //销售批次最小购买数量
        if (skuInfoVo.getBuyMin() != -1 && sku.getCount().compareTo(skuInfoVo.getBuyMin()) < 0) {
            confirmOrderInfoVo.setRemark("最小起订" + skuInfoVo.getBuyMin() + "件");
            isDisabled = true;
        }
        Integer countTotal = skuCountMap.getOrDefault(sku.getSkuId(), 0) + sku.getCount();
        //销售批次最大购买数量
        if (skuInfoVo.getBuyMax() != -1 && skuInfoVo.getBuyMax() < countTotal) {
            confirmOrderInfoVo.setRemark("销售日最多订" + skuInfoVo.getBuyMax() + "件");
            isDisabled = true;
        }
        return isDisabled;
    }

    private void convertOrderInfo(List<OrderItemAggregateVO> records) {
        Map<Long, RemoteSupplierSkuInfoVo> supplierSkuInfoVoMap = new HashMap<>();
        Map<Long, RemoteCityWhVo> remoteCityWhVoMap = new HashMap<>();
        Map<Long, RemoteRegionWhVo> regionWhVoMap = new HashMap<>();
        Map<Long, RemoteRegionLogisticsVo> remoteRegionLogisticsVoMap = new HashMap<>();
        Map<Long, RemoteSupplierVo> remoteSupplierVoMap = new HashMap<>();
        Map<Long, RemoteCustomerVo> customerVoMap = new HashMap<>();
        List<Long> supplierSkuIds = records.stream().map(OrderItemAggregateVO::getSupplierSkuId).filter(tmp -> ObjectUtil.isNotEmpty(tmp) && tmp != 0L).distinct().toList();
        List<Long> cityWhIds = records.stream().map(OrderItemAggregateVO::getCityWhId).filter(tmp -> ObjectUtil.isNotEmpty(tmp) && tmp != 0L).distinct().toList();
        List<Long> supplierIds = records.stream().map(OrderItemAggregateVO::getSupplierId).filter(tmp -> ObjectUtil.isNotEmpty(tmp) && tmp != 0L).distinct().toList();
        List<Long> regionIds = records.stream().map(OrderItemAggregateVO::getRegionWhId).filter(tmp -> ObjectUtil.isNotEmpty(tmp) && tmp != 0L).distinct().toList();
        List<Long> logisticsIds = records.stream().map(OrderItemAggregateVO::getLogisticsId).filter(tmp -> ObjectUtil.isNotEmpty(tmp) && tmp != 0L).distinct().toList();
        List<Long> customerIds = records.stream().map(OrderItemAggregateVO::getCustomerId).filter(tmp -> ObjectUtil.isNotEmpty(tmp) && tmp != 0L).distinct().toList();
        if (ObjectUtil.isNotEmpty(supplierSkuIds)) {
            RemoteQueryInfoListBo bo = new RemoteQueryInfoListBo();
            bo.setSupplierSkuIdList(supplierSkuIds);
            List<RemoteSupplierSkuInfoVo> remoteSupplierSkuInfoVos = remoteSupplierSkuService.queryInfoList(bo);
            if (ObjectUtil.isNotEmpty(remoteSupplierSkuInfoVos)) {
                supplierSkuInfoVoMap = remoteSupplierSkuInfoVos.stream().collect(Collectors.toMap(RemoteSupplierSkuInfoVo::getId, u -> u, (n1, n2) -> n1));
            }
        }
        if (ObjectUtil.isNotEmpty(cityWhIds)) {
            List<RemoteCityWhVo> remoteCityWhVos = remoteCityWhService.queryList(cityWhIds);
            if (ObjectUtil.isNotEmpty(remoteCityWhVos)) {
                remoteCityWhVoMap = remoteCityWhVos.stream().collect(Collectors.toMap(RemoteCityWhVo::getId, u -> u, (n1, n2) -> n1));
            }
        }
        if (ObjectUtil.isNotEmpty(regionIds)) {
            List<RemoteRegionWhVo> remoteRegionWhVos = remoteRegionWhService.selectRegionWhInfoByIds(regionIds);
            if (ObjectUtil.isNotEmpty(remoteRegionWhVos)) {
                regionWhVoMap = remoteRegionWhVos.stream().collect(Collectors.toMap(RemoteRegionWhVo::getId, u -> u, (n1, n2) -> n1));
            }
        }
        if (ObjectUtil.isNotEmpty(logisticsIds)) {
            RemoteRegionLogisticsQueryBo bo = new RemoteRegionLogisticsQueryBo();
            bo.setLogisticsIds(logisticsIds);
            List<RemoteRegionLogisticsVo> remoteRegionLogisticsVos = remoteRegionLogisticsService.queryList(bo);
            if (ObjectUtil.isNotEmpty(remoteRegionLogisticsVos)) {
                remoteRegionLogisticsVoMap = remoteRegionLogisticsVos.stream().collect(Collectors.toMap(RemoteRegionLogisticsVo::getId, u -> u, (n1, n2) -> n1));
            }
        }
        if (ObjectUtil.isNotEmpty(supplierIds)) {
            List<RemoteSupplierVo> remoteSupplierVos = remoteSupplierService.getSupplierByIds(supplierIds);
            if (ObjectUtil.isNotEmpty(remoteSupplierVos)) {
                remoteSupplierVoMap = remoteSupplierVos.stream().collect(Collectors.toMap(RemoteSupplierVo::getId, u -> u, (n1, n2) -> n1));
            }
        }
        if (ObjectUtil.isNotEmpty(customerIds)) {
            List<RemoteCustomerVo> remoteCustomerVos = remoteBasCustomerService.getByIds(customerIds);
            if (ObjectUtil.isNotEmpty(remoteCustomerVos)) {
                customerVoMap = remoteCustomerVos.stream().collect(Collectors.toMap(RemoteCustomerVo::getId, u -> u, (n1, n2) -> n1));
            }
        }


        for (OrderItemAggregateVO aggregateVO : records) {

            OrderItemAggregateVO tmpRefundInfoAggregateVO = refundRecordMapper.getRefundInfoByOrderItemId(aggregateVO.getOrderItemId());

            convertRefundInfo(aggregateVO, tmpRefundInfoAggregateVO);

            List<ReportLossItemInfoVo> infoVoList = iReportLossService.getByOrderItemId(aggregateVO.getOrderItemId());
            log.keyword("订单查询明细").info("查询报损信息：infoVoList:{}, getOrderItemId:{}", infoVoList, aggregateVO.getOrderItemId());

            if (ObjectUtil.isEmpty(infoVoList)) {
                initLossInfo(aggregateVO);
            } else {
                ReportLossItemInfoVo vo = infoVoList.get(0);
                aggregateVO.setApplyLossAmount(vo.getLossAmount());
                aggregateVO.setLossRefundAmount(vo.getRefundGoodsAmount());
                aggregateVO.setSupplierLossRefundAmount(vo.getRefundGoodsActualAmount());
                if (ResponsibilityTypeEnum.CITY_WH.getCode().equals(vo.getResponsibilityType())) {
                    aggregateVO.setCityLossRefundAmount(vo.getLossFlowRefundAmount());
                }
                if (ResponsibilityTypeEnum.REGION_WH.getCode().equals(vo.getResponsibilityType())) {
                    aggregateVO.setRegionLossRefundAmount(vo.getLossFlowRefundAmount());
                }
            }
            List<RemoteTradeAccTransVo> transVoList = remoteTradeAccTransService.getByOrderItemId(aggregateVO.getOrderCode(), aggregateVO.getSupplierSkuId());
            log.keyword("订单查询明细", aggregateVO.getOrderCode(), aggregateVO.getSupplierSkuId())
                    .info("getOrderCode:{}, aggregateVO.getSupplierSkuId:{}, transVoList：{}", aggregateVO.getOrderCode(), aggregateVO.getSupplierSkuId(), transVoList);
            if (ObjectUtil.isNotEmpty(transVoList)) {
                RemoteTradeAccTransVo vo = transVoList.get(0);
                aggregateVO.setAvailNo(vo.getAvailNo());
                aggregateVO.setCashNo(vo.getCashNo());
                aggregateVO.setAvailTime(vo.getAvailTime());
                aggregateVO.setCashTime(vo.getCashTime());
                aggregateVO.setTransStatus(vo.getStatus());
                aggregateVO.setTransStatusName(AccountStatusEnum.getDescByCode(vo.getStatus()));
            }
            aggregateVO.setSupplierName(ObjectUtil.isNotEmpty(remoteSupplierVoMap.get(aggregateVO.getSupplierId())) ? remoteSupplierVoMap.get(aggregateVO.getSupplierId()).getName() : null);
            aggregateVO.setLogisticsName(ObjectUtil.isNotEmpty(remoteRegionLogisticsVoMap.get(aggregateVO.getLogisticsId())) ? remoteRegionLogisticsVoMap.get(aggregateVO.getLogisticsId()).getLogisticsName() : null);
            aggregateVO.setCityWhName(ObjectUtil.isNotEmpty(remoteCityWhVoMap.get(aggregateVO.getCityWhId())) ? remoteCityWhVoMap.get(aggregateVO.getCityWhId()).getName() : null);
            aggregateVO.setRegionWhName(ObjectUtil.isNotEmpty(regionWhVoMap.get(aggregateVO.getRegionWhId())) ? regionWhVoMap.get(aggregateVO.getRegionWhId()).getRegionWhName() : null);
            aggregateVO.setCustomerName(ObjectUtil.isNotEmpty(customerVoMap.get(aggregateVO.getCustomerId())) ? customerVoMap.get(aggregateVO.getCustomerId()).getName() : null);
            //订单状态名称
            aggregateVO.setStatusName(OrderStatusEnum.getDescByCode(aggregateVO.getStatus()));

            if (ObjectUtil.isNotEmpty(supplierSkuInfoVoMap.get(aggregateVO.getSupplierSkuId()))) {
                RemoteSupplierSkuInfoVo skuInfoVo = supplierSkuInfoVoMap.get(aggregateVO.getSupplierSkuId());
                aggregateVO.setSpuGrade(skuInfoVo.getSpuGrade());
                aggregateVO.setPackageWord(skuInfoVo.getPackageWord());
                aggregateVO.setProducer(skuInfoVo.getProducer());
                aggregateVO.setDomestic(skuInfoVo.getDomestic());
                aggregateVO.setBuyerName(skuInfoVo.getBuyerName());
            }
//            aggregateVO.setExProductAmount(aggregateVO.getPlatformServiceAmount().add(aggregateVO.getPlatformFreightAmount())
//                    .add(aggregateVO.getFinancialServiceAmount()).subtract(aggregateVO.getPlatformServiceFreeAmount()).subtract(aggregateVO.getRegionFreightFreeAmount()));
            log.keyword("订单查询明细", "各个商品退款金额", aggregateVO.getOrderCode(), aggregateVO.getSupplierSkuId())
                    .info("aggregateVO.getLossRefundAmount():{}, aggregateVO.getLessRefundAmount():{}, aggregateVO.getLackRefundAmount()：{}" +
                                    ", aggregateVO.getLessRefundAmount()：{}, aggregateVO.getRefundFreightAmount()：{}, aggregateVO.getRefundServiceAmount()：{}, aggregateVO.getRefundFinancialServicePrice()：{}"
                            , aggregateVO.getLossRefundAmount(), aggregateVO.getLessRefundAmount(), aggregateVO.getLackRefundAmount()
                            , aggregateVO.getLessRefundAmount(), aggregateVO.getRefundFreightAmount(), aggregateVO.getRefundServiceAmount(), aggregateVO.getRefundFinancialServicePrice());
//
//            BigDecimal totalRefundAmount = aggregateVO.getLossRefundAmount().add(aggregateVO.getDiffRefundAmount())
//                    .add(aggregateVO.getLackRefundAmount()).add(aggregateVO.getLessRefundAmount())
//                    .add(aggregateVO.getRefundFreightAmount()).add(aggregateVO.getRefundServiceAmount())
//                    .add(aggregateVO.getRefundFinancialServicePrice());
            aggregateVO.setTotalRefundAmount(BigDecimal.ZERO);
        }
    }

    private void convertRefundInfo(OrderItemAggregateVO aggregateVO, OrderItemAggregateVO tmpRefundInfoAggregateVO) {
        aggregateVO.setRefundServiceAmount(tmpRefundInfoAggregateVO.getRefundServiceAmount());
        aggregateVO.setRefundFreightAmount(tmpRefundInfoAggregateVO.getRefundFreightAmount());
        aggregateVO.setRefundFinancialServicePrice(tmpRefundInfoAggregateVO.getRefundFinancialServicePrice());
//        aggregateVO.setLossRefundAmount(tmpRefundInfoAggregateVO.getLossRefundAmount());
        aggregateVO.setDiffRefundAmount(tmpRefundInfoAggregateVO.getDiffRefundAmount());
        aggregateVO.setLackRefundAmount(tmpRefundInfoAggregateVO.getLackRefundAmount());
        aggregateVO.setLessRefundAmount(tmpRefundInfoAggregateVO.getLessRefundAmount());
        aggregateVO.setDiffCount(tmpRefundInfoAggregateVO.getDiffCount());
        aggregateVO.setLackCount(tmpRefundInfoAggregateVO.getLackCount());
        aggregateVO.setLessCount(tmpRefundInfoAggregateVO.getLessCount());
        aggregateVO.setDiffPrice(tmpRefundInfoAggregateVO.getDiffPrice());
        aggregateVO.setLackPrice(tmpRefundInfoAggregateVO.getLackPrice());
        aggregateVO.setLessPrice(tmpRefundInfoAggregateVO.getLessPrice());
    }


    private void initLossInfo(OrderItemAggregateVO aggregateVO) {
        aggregateVO.setApplyLossAmount(BigDecimal.ZERO);
        aggregateVO.setLossRefundAmount(BigDecimal.ZERO);
        aggregateVO.setSupplierLossRefundAmount(BigDecimal.ZERO);
        aggregateVO.setCityLossRefundAmount(BigDecimal.ZERO);
        aggregateVO.setRegionLossRefundAmount(BigDecimal.ZERO);
    }

    /**
     * 根据销售批次id查询已支付订单货款
     *
     * @param supplierSkuId
     * @return
     */
    @Override
    public BigDecimal queryReplaceSupplierAmount(Long supplierSkuId) {
        List<OrderItemVo> oldItemList = orderItemMapper.selectBySupplierSkuId(supplierSkuId,
                Lists.newArrayList(OrderStatusEnum.ALREADY.getCode()));
        BigDecimal amount = BigDecimal.ZERO;
        if (oldItemList != null && oldItemList.size() > 0) {
            for (OrderItemVo item : oldItemList) {
                amount = amount.add(item.getProductAmount());
            }
        }
        return amount;
    }

    @Override
    public List<String> listByOrderCode(String orderCode, Long supplierId, Long cityWhId, String saleDate, Long regionWhId) {
        return orderMapper.listByOrderCode(orderCode, supplierId, cityWhId, saleDate, regionWhId);
    }

    /**
     * PC端订单列表查询
     *
     * @param bo
     * @return
     */
    @Override
    public TableDataInfo<QueryAdminPageVo> queryAdminPage(QueryAdminPageBo bo) {
        if (StringUtils.isNotBlank(bo.getPhone())) {
            RemoteUserBo remoteUserBo = remoteUserService.getUserByPhoneNo(bo.getPhone(), SysUserTypeEnum.CUSTOMER_USER.getType());
            if (remoteUserBo == null) {
                bo.setCustomerId(-1L);
            } else {
                bo.setCustomerId(remoteUserService.getUserRelation(remoteUserBo.getUserId()));
            }
        }
        Page<QueryAdminPageVo> page = orderMapper.queryAdminPage(bo, bo.build());
        List<QueryAdminPageVo> list = page.getRecords();
        if (list != null && list.size() > 0) {
            List<Long> customerIdList = list.stream().map(QueryAdminPageVo::getCustomerId).distinct().toList();
            List<Long> cityWhIdList = list.stream().map(QueryAdminPageVo::getCityWhId).distinct().toList();
            List<Long> logisticsIdList = list.stream().map(QueryAdminPageVo::getLogisticsId).distinct().toList();
            List<Long> regionWhIdList = list.stream().map(QueryAdminPageVo::getRegionWhId).distinct().toList();
            List<RemoteCustomerVo> customerVoList = remoteBasCustomerService.getByIds(customerIdList);
            List<RemoteCityWhVo> cityWhVoList = remoteCityWhService.queryList(cityWhIdList);
            List<RemoteRegionWhVo> regionWhVoList = remoteRegionWhService.selectRegionWhInfoByIds(regionWhIdList);
            RemoteRegionLogisticsQueryBo logisticsQueryBo = new RemoteRegionLogisticsQueryBo();
            logisticsQueryBo.setLogisticsIds(logisticsIdList);
            List<RemoteRegionLogisticsVo> logisticsVoList = remoteRegionLogisticsService.queryList(logisticsQueryBo);
            Map<Long, RemoteCustomerVo> customerVoMap = customerVoList.stream().collect(Collectors.toMap(RemoteCustomerVo::getId, Function.identity()));
            Map<Long, RemoteCityWhVo> cityWhVoMap = cityWhVoList.stream().collect(Collectors.toMap(RemoteCityWhVo::getId, Function.identity()));
            Map<Long, RemoteRegionWhVo> regionWhVoMap = regionWhVoList.stream().collect(Collectors.toMap(RemoteRegionWhVo::getId, Function.identity()));
            Map<Long, RemoteRegionLogisticsVo> logisticsVoMap = logisticsVoList.stream().collect(Collectors.toMap(RemoteRegionLogisticsVo::getId, Function.identity()));
            for (QueryAdminPageVo vo : list) {
                RemoteCustomerVo customerVo = customerVoMap.get(vo.getCustomerId());
                if (customerVo != null) {
                    vo.setCustomerName(customerVo.getName());
                }
                RemoteCityWhVo cityWhVo = cityWhVoMap.get(vo.getCityWhId());
                if (cityWhVo != null) {
                    vo.setCityWhName(cityWhVo.getName());
                }
                RemoteRegionWhVo regionWhVo = regionWhVoMap.get(vo.getRegionWhId());
                if (regionWhVo != null) {
                    vo.setRegionWhName(regionWhVo.getRegionWhName());
                }
                RemoteRegionLogisticsVo logisticsVo = logisticsVoMap.get(vo.getLogisticsId());
                if (logisticsVo != null) {
                    vo.setLogisticsName(logisticsVo.getLogisticsName());
                }
            }
        }
        return TableDataInfo.build(page);
    }

    /**
     * 供应商资金账户，查询订货列表
     *
     * @param bo
     * @return
     */
    @Override
    public List<RemoteSupTransProductOrderVo> queryProductOrder(RemoteSupAccTransQueryBo bo) {
        List<RemoteSupTransProductOrderVo> voList = new ArrayList<>();
        if (bo != null && bo.getTrans() != null && bo.getTrans().size() > 0) {
            List<Long> skuIdList = bo.getTrans().stream().map(RemoteSupAccTransBo::getSkuId).distinct().toList();
            //获取销售批次商品
            RemoteQueryInfoListBo listBo = new RemoteQueryInfoListBo();
            listBo.setSupplierSkuIdList(skuIdList);
            List<RemoteSupplierSkuInfoVo> skuInfoVos = remoteSupplierSkuService.queryInfoList(listBo);
            Map<Long, RemoteSupplierSkuInfoVo> skuMap = skuInfoVos.stream().collect(Collectors.toMap(RemoteSupplierSkuInfoVo::getId, Function.identity()));
            //获取包装图片
//            RemoteQuerySupplierSkuFileBo fileBo = new RemoteQuerySupplierSkuFileBo();
//            fileBo.setSupplierSkuIdList(skuIdList);
//            List<RemoteSupplierSkuFileVo> fileVos = remoteSupplierSkuService.querySupplierSkuFile(fileBo);
//            Map<Long, List<RemoteSupplierSkuFileVo>> skuFileMap = fileVos.stream().collect(Collectors.groupingBy(RemoteSupplierSkuFileVo::getSupplierSkuId, Collectors.toList()));
            //商品等级
            List<RemoteSysDictVo> baseProRank = remoteDictService.selectDictDataByType("BasePro_rank");
            Map<String, String> collect = baseProRank.stream().collect(Collectors.toMap(RemoteSysDictVo::getDictValue, RemoteSysDictVo::getDictName));
            Map<Long, List<Long>> map = bo.getTrans().stream()
                    .collect(Collectors.groupingBy(
                            RemoteSupAccTransBo::getSkuId,
                            Collectors.mapping(RemoteSupAccTransBo::getTransId, Collectors.toList())
                    ));
            Map<Long, RemoteSupAccTransBo> boMap = bo.getTrans().stream().collect(Collectors.toMap(RemoteSupAccTransBo::getSkuId, Function.identity(), (v1, v2) -> v1));
            for (Long skuId : skuIdList) {
                RemoteSupTransProductOrderVo resultVo = new RemoteSupTransProductOrderVo();
                resultVo.setId(skuId);
                RemoteSupAccTransBo remoteSupAccTransBo = boMap.get(skuId);
                if (remoteSupAccTransBo != null) {
                    resultVo.setSaleDate(remoteSupAccTransBo.getTransDate());
                    resultVo.setTotalAmount(remoteSupAccTransBo.getTransAmt());
                }
                RemoteSupplierSkuInfoVo sku = skuMap.get(skuId);
                if (sku != null) {
                    resultVo.setSkuName(sku.getSpuName());
                    resultVo.setSpuGrossWeight(sku.getSpuGrossWeight());
                    resultVo.setSpuNetWeight(sku.getSpuNetWeight());
                    resultVo.setImgUrl(sku.getImgUrl());
                    resultVo.setSpuGrade(sku.getSpuGrade());
                    if (StringUtils.isNotBlank(collect.get(sku.getSpuGrade()))) {
                        resultVo.setSpuGradeName(collect.get(sku.getSpuGrade()));
                    }
                    resultVo.setSpuStandards(sku.getSpuStandards());
                    resultVo.setProducer(sku.getProducer());
//                    resultVo.setFileList(skuFileMap.get(skuId));
                }
                QueryOrderBo orderBo = new QueryOrderBo();
                orderBo.setSupplierSkuId(skuId);
                orderBo.setOrderIdList(map.get(skuId));
                List<RemoteSupTransProductOrderPriceVo> priceList = orderItemMapper.queryProductOrder(orderBo);
                resultVo.setPriceList(priceList);
                voList.add(resultVo);
            }
            if (voList.size() > 0) {
                //手动排序
                voList = voList.stream().sorted(Comparator.comparing(RemoteSupTransProductOrderVo::getSaleDate).reversed()).collect(Collectors.toList());
            }
        }
        return voList;
    }

    /**
     * 供应商资金账户，查询订货明细
     *
     * @param bo
     * @return
     */
    @Override
    public List<RemoteSupTransProductOrderRecordVo> queryProductOrderRecord(RemoteSupAccTransQueryBo bo) {
        if (bo != null && bo.getTrans() != null && bo.getTrans().size() > 0) {
            return orderItemMapper.queryProductOrderRecord(bo);
        }
        return new ArrayList<>();
    }

    /**
     * 根据订单id获取订单信息
     *
     * @param orderId
     * @return
     */
    @Override
    public RemoteOrderInfoVo getOrderInfo(Long orderId) {
        OrderVo orderVo = orderMapper.selectVoById(orderId);
        return MapstructUtils.convert(orderVo, RemoteOrderInfoVo.class);
    }

    private void distributeSkuActivity(Order add, List<OrderItem> orderItemList) {
        List<RemoteOrderSkuVo> skuList = orderItemList.stream().map(item -> {
            RemoteOrderSkuVo skuVo = new RemoteOrderSkuVo();
            skuVo.setOrderId(add.getId());
            skuVo.setSkuId(item.getSkuId());
            skuVo.setOrderItemId(item.getId());
            skuVo.setCount(item.getCount());
            skuVo.setOrderCode(item.getOrderCode());
            skuVo.setRegionWhId(item.getRegionWhId());
            skuVo.setProductAuctualAmount(item.getProductAmount().subtract(item.getFreeTotalAmount()));
            return skuVo;
        }).collect(Collectors.toList());
        List<RemoteDistributionSkuVo> matchSkus = distributionService.matchOrderDistribution(add.getCustomerId(), add.getSaleDate(), add.getCreateTime(), skuList);
        if (CollUtil.isEmpty(matchSkus)) {
            return;
        }
        // 批量更新商品分佣金额
        List<OrderItem> updateList = matchSkus.stream()
                .map(vo -> {
                    OrderItem item = new OrderItem();
                    item.setId(vo.getOrderItemId());
                    item.setDistributionAmount(vo.getDistributionAmount());
                    item.setDistributionTaxAmount(vo.getDistributionTaxAmount());
                    return item;
                })
                .collect(Collectors.toList());
//        orderItemMapper.updateBatchById(updateList);
        orderItemMapper.updateDistribution(updateList);
    }

    @Override
    public RemoteOrderItemVo findLastBuySku(Long customerId, List<Long> skuIds) {
        List<String> cancelTypes = Arrays.asList(OrderCancelTypeEnum.FEW.getCode(), OrderCancelTypeEnum.OUT.getCode());
        OrderItemVo item = orderItemMapper.selectVoOne(
                new LambdaQueryWrapper<OrderItem>()
                        .eq(OrderItem::getCustomerId, customerId)
                        .in(CollectionUtil.isNotEmpty(skuIds), OrderItem::getSkuId, skuIds)
                        .and(w -> w.isNull(OrderItem::getCancelType).or().in(OrderItem::getCancelType, cancelTypes))
                        .orderByDesc(OrderItem::getOrderId)
                        .last(BanguoCommonConstant.limit1)
        );
        if (item != null) {
            return MapstructUtils.convert(item, RemoteOrderItemVo.class);
        }
        return null;
    }

    /**
     * 订单拍子号赋值
     */
    @Override
    public void setDeliveryNumber(Long orderId) {
        Order order = orderMapper.selectById(orderId);
        if (ObjectUtil.isNotNull(order) && OrderStatusEnum.ALREADY.getCode().equals(order.getStatus())) {
            //先查询提货单是否有数据
            ReceiveGoodsRecord record = receiveGoodsRecordMapper.getByOrderId(orderId);
            //获取订单信息，以免一个人下多个单
            List<OrderItem> orders = orderItemMapper.selectList(new LambdaQueryWrapper<OrderItem>()
                    .eq(OrderItem::getPlaceId, order.getPlaceId())
                    .eq(OrderItem::getPlaceIdLevel2, order.getPlaceIdLevel2())
                    .eq(OrderItem::getSaleDate, order.getSaleDate())
                    .eq(OrderItem::getStatus, OrderStatusEnum.ALREADY.getCode())
                    .eq(OrderItem::getCustomerId, order.getCustomerId())
                    .notIn(OrderItem::getOrderId, orderId)
                    .notIn(OrderItem::getDeliveryNumber, 0));
            if (ObjectUtil.isNotNull(record)) {
                orderMapper.updateDeliveryNumber(orderId, record.getDeliveryNumber());
            } else if (CollUtil.isNotEmpty(orders)) {
                orderMapper.updateDeliveryNumber(orderId, orders.get(0).getDeliveryNumber());
            } else {
                String placeKey = order.getPlaceId().toString() + order.getPlaceIdLevel2();
                Long aLong = DeliveryNumberUtil.generateSerialNumbers(order.getSaleDate().toString(), placeKey, 1);
                orderMapper.updateDeliveryNumber(orderId, aLong);
            }
        }
    }

    /**
     * 订单异步执行逻辑
     *
     * @param order
     */
    @Override
    public void asyncAccept(Order order) {
        Order newOrder = orderMapper.selectOne(new LambdaQueryWrapper<Order>().eq(Order::getCode, order.getCode()));
        if (ObjectUtil.isNull(newOrder)) {
            log.keyword("notFoundOrder").error("未找到订单：{}", order.getCode());
            throw new ServiceException(order.getCode());
            //回滚吨位
//            compensateWeight(order);
            //回滚优惠券
//            compensateCoupon(order);
        }
        RemoteWhProfitRuleQueryBo bo = new RemoteWhProfitRuleQueryBo();
        bo.setCityWhId(newOrder.getCityWhId());
        bo.setRegionWhId(newOrder.getRegionWhId());
        List<RemoteWhProfitRuleVo> remoteWhProfitRuleVos = remoteWhProfitRuleService.queryLastUpdateList(bo);
        if (CollUtil.isNotEmpty(remoteWhProfitRuleVos)) {
            Map<Long, Long> profitRuleMap = remoteWhProfitRuleVos.stream().collect(Collectors.toMap(RemoteWhProfitRuleVo::getRuleType, RemoteWhProfitRuleVo::getId));
            Long freightId = profitRuleMap.get(WhProfitRuleTypeEnum.FREIGHT_FEE.getCode());
            Long serviceId = profitRuleMap.get(WhProfitRuleTypeEnum.SERVICE_FEE.getCode());
            orderItemMapper.update(new LambdaUpdateWrapper<OrderItem>().eq(OrderItem::getOrderCode, newOrder.getCode())
                    .set(OrderItem::getFreightRuleRecordId, freightId).set(OrderItem::getServiceRuleRecordId, serviceId));
        }
    }

    /**
     * 查询订单列表
     *
     * @param bo
     * @return
     */
    @Override
    public TableDataInfo<OpenOrderVo> selectOrderVoPage(RemoteOrderQueryBo bo) {
        LambdaQueryWrapper<Order> lqw = Wrappers.lambdaQuery();
        lqw.in(CollectionUtils.isNotEmpty(bo.getStatusList()), Order::getStatus, bo.getStatusList());
        lqw.ge(ObjectUtil.isNotNull(bo.getUpdateStartTime()), Order::getUpdateTime, bo.getUpdateStartTime());
        lqw.le(ObjectUtil.isNotNull(bo.getUpdateEndTime()), Order::getUpdateTime, bo.getUpdateEndTime());

        IPage<OrderVo> orderVoIPage = orderMapper.selectVoPage(bo.build(), lqw);
        List<OrderVo> records = orderVoIPage.getRecords();
        long total = orderVoIPage.getTotal();

        List<OpenOrderVo> list = records.stream()
                .map(vo -> {
                    OpenOrderVo o = new OpenOrderVo();
                    BeanUtils.copyProperties(vo, o);
                    return o;
                })
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(list)) {
            List<Long> idList = list.stream().map(OpenOrderVo::getId).collect(Collectors.toList());

            LambdaQueryWrapper<OrderItem> lqwItem = Wrappers.lambdaQuery();
            lqwItem.in(CollectionUtils.isNotEmpty(idList), OrderItem::getOrderId, idList);

            Map<Long, List<OrderItem>> orderMap = orderItemMapper.selectList(lqwItem).stream()
                    .collect(Collectors.groupingBy(OrderItem::getOrderId));

            list.forEach(vo -> {
                List<OrderItem> items = orderMap.getOrDefault(vo.getId(), Collections.emptyList());
                if (CollUtil.isNotEmpty(items)) {
                    vo.setOrderItemList(items.stream().map(item -> {
                        OpenOrderItemVo o = new OpenOrderItemVo();
                        BeanUtils.copyProperties(item, o);
                        return o;
                    }).collect(Collectors.toList()));
                }
            });
        }
        return TableDataInfo.build(list, total);
    }

    @Override
    @Lock4j(name = "submitOrder", keys = "#submitBo.orderCode", expire = 10000, acquireTimeout = 1000)
    public AddOrderVo submitLock(SubmitBo submitBo) {
        return SpringUtils.getBean(OrderServiceImpl.class).submit(submitBo);
    }

    /**
     * 回滚优惠券
     *
     * @param order
     */
    private void compensateCoupon(Order order) {
        couponUserService.restoreCouponByBizNo(order.getCode());
    }

    /**
     * 补偿吨位
     */

    private void compensateWeight(Order order) {
        String orderCode = RedisUtils.getCacheObject(RedisConstant.ORDER_COMPENSATION_WEIGHT_TAG + order.getCode());
        if (StringUtils.isNotEmpty(orderCode)) {
            return;
        }
        //减吨位
        RemoteRegionLogisticsPlanOrderWeightBo logisticsPlan = new RemoteRegionLogisticsPlanOrderWeightBo();
        logisticsPlan.setOrderId(order.getId());
        logisticsPlan.setOrderCode(order.getCode());
        logisticsPlan.setLogisticsId(order.getLogisticsId());
        logisticsPlan.setPlanDate(order.getSaleDate());
        logisticsPlan.setOrderWeight(BigDecimal.ZERO.subtract(order.getTotalGrossWeight()));
        //减吨位
        remoteRegionLogisticsPlanService.updateOrderWeight(logisticsPlan);
        RedisUtils.setCacheObject(RedisConstant.ORDER_COMPENSATION_WEIGHT_TAG + order.getCode(), order.getCode(), Duration.ofDays(1));
    }

    /**
     * 下单回滚补偿
     *
     * @param bo
     */
    @Override
    public void rollbackCompensate(OrderCompensateBo bo) {
        //吨位补偿，吨位直接补偿，下单能走到发送mq说明吨位已经扣成功了
        this.compensateWeight(bo);
        //库存补偿
        this.compensateStock(bo);
        //优惠券补偿
        this.compensateCouponUser(bo);
        //分销补偿
        this.compensateDistribution(bo);
    }

    /**
     * 回滚补偿优惠券
     *
     * @param bo
     */
    private void compensateCouponUser(OrderCompensateBo bo) {
        String orderCode = RedisUtils.getCacheObject(RedisConstant.ORDER_COMPENSATION_COUPON_TAG + bo.getOrderCode());
        if (ObjectUtil.isNotEmpty(orderCode)) {
            return;
        }
        log.keyword("compensateCouponUser", bo.getOrderCode()).info("回滚补偿优惠券开始");
        couponUserService.restoreCouponByBizNo(bo.getOrderCode());
        log.keyword("compensateCouponUser", bo.getOrderCode()).info("回滚补偿优惠券结束");
        RedisUtils.setCacheObject(RedisConstant.ORDER_COMPENSATION_COUPON_TAG + bo.getOrderCode(), bo.getOrderCode(), Duration.ofDays(1));
    }

    /**
     * 回滚补偿库存
     *
     * @param bo
     */
    private void compensateStock(OrderCompensateBo bo) {
        String orderCode = RedisUtils.getCacheObject(RedisConstant.ORDER_COMPENSATION_STOCK_TAG + bo.getOrderCode());
        if (ObjectUtil.isNotEmpty(orderCode)) {
            return;
        }
        log.keyword("compensateStock", bo.getOrderCode()).info("回滚补偿库存开始");
        //检查取消订单是否需要回退上架库存
        boolean cancelStock = checkCancelStock(bo.getRegionWhId());
        remoteSupplierSkuService.compensateStock(bo.getOrderCode(), cancelStock);
        log.keyword("compensateStock", bo.getOrderCode()).info("回滚补偿库存结束");
        RedisUtils.setCacheObject(RedisConstant.ORDER_COMPENSATION_STOCK_TAG + bo.getOrderCode(), bo.getOrderCode(), Duration.ofDays(1));
    }

    /**
     * 回滚补偿分销
     *
     * @param bo
     */
    private void compensateDistribution(OrderCompensateBo bo) {
        String orderCode = RedisUtils.getCacheObject(RedisConstant.ORDER_COMPENSATION_DISTRIBUTION_TAG + bo.getOrderCode());
        if (StringUtils.isNotEmpty(orderCode)) {
            return;
        }
        log.keyword("compensateDistribution", bo.getOrderCode()).info("回滚补偿分销开始");
        distributionService.compensateDeleteByOrderCode(bo.getOrderCode());
        log.keyword("compensateDistribution", bo.getOrderCode()).info("回滚补偿分销结束");
        RedisUtils.setCacheObject(RedisConstant.ORDER_COMPENSATION_DISTRIBUTION_TAG + bo.getOrderCode(), bo.getOrderCode(), Duration.ofDays(1));
    }

    /**
     * 回滚补偿吨位
     *
     * @param bo
     */
    private void compensateWeight(OrderCompensateBo bo) {
        String orderCode = RedisUtils.getCacheObject(RedisConstant.ORDER_COMPENSATION_WEIGHT_TAG + bo.getOrderCode());
        if (StringUtils.isNotEmpty(orderCode)) {
            return;
        }
        log.keyword("compensateWeight", bo.getOrderCode()).info("回滚补偿吨位开始");
        //减吨位
        RemoteRegionLogisticsPlanOrderWeightBo logisticsPlan = new RemoteRegionLogisticsPlanOrderWeightBo();
        logisticsPlan.setOrderId(bo.getOrderId());
        logisticsPlan.setOrderCode(bo.getOrderCode());
        logisticsPlan.setLogisticsId(bo.getLogisticsId());
        logisticsPlan.setPlanDate(bo.getPlanDate());
        logisticsPlan.setOrderWeight(bo.getOrderWeight().negate());
        remoteRegionLogisticsPlanService.updateOrderWeight(logisticsPlan);
        log.keyword("compensateWeight", bo.getOrderCode()).info("回滚补偿吨位结束");
        RedisUtils.setCacheObject(RedisConstant.ORDER_COMPENSATION_WEIGHT_TAG + bo.getOrderCode(), bo.getOrderCode(), Duration.ofDays(1));
    }


    /**
     * 订单列表查询
     * @param bo
     * @return
     */
    @Override
    public TableDataInfo<QueryPageVo> orderList(QueryOrderPageBo bo) {
        LoginUser user = Optional.ofNullable(LoginHelper.getLoginUser())
                .orElseThrow(() -> new ServiceException("用户未登入"));
        // 设置用户ID
        bo.setCustomerId(user.getRelationId());
        // 设置订单取消原因
        bo.setCancelTypeList(Arrays.asList(
                OrderCancelTypeEnum.SKU_DOWN.getCode(),
                OrderCancelTypeEnum.LOGISTICS_OUT.getCode(), OrderCancelTypeEnum.STOCK_OUT.getCode(),
                OrderCancelTypeEnum.REGION.getCode(),
                OrderCancelTypeEnum.REPLACE_SUPPLIER.getCode(), OrderCancelTypeEnum.OUT.getCode(),
                OrderCancelTypeEnum.FEW.getCode()));
        // 设置订单业务类型
        bo.setBusinessTypeList(Arrays.asList(OrderBusinessTypeEnum.BUSINESS_TYPE0.getCode(),
                OrderBusinessTypeEnum.BUSINESS_TYPE1.getCode(),
                OrderBusinessTypeEnum.BUSINESS_TYPE10.getCode(),
                OrderBusinessTypeEnum.BUSINESS_TYPE20.getCode(),
                OrderBusinessTypeEnum.BUSINESS_TYPE30.getCode()));
        // 设置7天内的默认时间范围
        bo.setSaleDateStart(LocalDate.now().minusDays(7));

        // 根据群ID 设置相关的 供应商，城市仓
        if (ObjectUtils.isNotEmpty(bo.getGroupId())) {
            RemoteImGroupVo imGroup = remoteImGroupService.getByGroupId(bo.getGroupId());
            if (imGroup == null) {
                throw new ServiceException("群组不存在");
            }

            List<String> userList = JSONUtil.toList(imGroup.getGroupUser(), String.class);
            if (CollectionUtil.isEmpty(userList)) {
                throw new ServiceException("群组成员为空");
            }

            Map<String, RemoteUserBo> userMap = Optional.ofNullable(remoteUserService.getUsersByUserCodes(userList))
                    .orElseThrow(() -> new ServiceException("用户不存在"));

            List<String> supplierUserCodeList = new ArrayList<>();
            List<String> cityWhUserCodeList = new ArrayList<>();

            userMap.forEach((userId, x) -> {
                String userType = x.getUserType();
                if (ObjectUtil.equals(userType, SysUserTypeEnum.SUPPLIER_USER.getType())) {
                    supplierUserCodeList.add(userId);
                } else if (ObjectUtil.equals(userType, SysUserTypeEnum.CITY_USER.getType())) {
                    cityWhUserCodeList.add(userId);
                }
            });
            if (CollectionUtil.isNotEmpty(supplierUserCodeList)) {
                bo.setSupplierIdList(remoteSupplierService.getSupplierByAdminCode(supplierUserCodeList));
            }
            if (CollectionUtil.isNotEmpty(cityWhUserCodeList)) {
                bo.setCityWhIdList(remoteCityWhService.getListByAdminCode(cityWhUserCodeList));
            }
        }
        // 根据用户Code 设置相关的 供应商，城市仓
        if (ObjectUtils.isNotEmpty(bo.getUserCode())) {
            RemoteUserBo userInfo = remoteUserService.getUserByUserCode(bo.getUserCode());
            if (userInfo != null) {
                if (ObjectUtil.equals(userInfo.getUserType(), SysUserTypeEnum.SUPPLIER_USER.getType())) {
                    bo.setSupplierIdList(remoteSupplierService.getSupplierByAdminCode(Collections.singletonList(bo.getUserCode())));
                } else if (ObjectUtil.equals(userInfo.getUserType(), SysUserTypeEnum.CITY_USER.getType())) {
                    bo.setCityWhIdList(remoteCityWhService.getListByAdminCode(Collections.singletonList(bo.getUserCode())));
                }
            }
        }
        return queryPage(bo);
    }

    /**
     * 营销活动匹配以及计算
     *
     * @param confirmOrderVo
     * @return
     */
    private ConfirmOrderVo marketingCalculate(ConfirmOrderVo confirmOrderVo) {
        /**
         * 组装参数，远程调用marketing服务，传入订单商品行明细进行活动匹配
         * 处理接口返参，遍历商品行，当前商品行没命中活动则跳过，命中则进行活动规则校验以及优惠计算
         * 计算当前商品行优惠金额，若当前活动销售目标剩余2件，下单3件， 商品原件3元，活动打九折，最大优惠10元，
         * 单商品折扣价为：3*0.9=2.7 原总价：3*3=9  现总价 2.7*2+3=8.4 总优惠金额：9-8.4=0.6,计算活动剩余名额
         * onfirmOrderVO.confirmOrderInfoVos.activityRecord 记录命中的活动记录
         */
        List<RemoteMkActivitySkuDiscountVo> activitySkuDiscountVos = qryMkActivityByOrderItem(confirmOrderVo);
        if (CollectionUtils.isEmpty(activitySkuDiscountVos)) {
            log.keyword("mkTimeDiscountActivityMatch").info("当前订单:{}没有命中营销活动", confirmOrderVo.getCode());
            return confirmOrderVo;
        }
        List<ConfirmOrderInfoVo> confirmOrderInfoVos = confirmOrderVo.getConfirmOrderInfoVos();
        Map<Long, RemoteMkActivitySkuDiscountVo> skuDiscountVoMap = activitySkuDiscountVos.stream().filter(Objects::nonNull).collect(Collectors.toMap(RemoteMkActivitySkuDiscountVo::getSupplierSkuId, vo -> vo, (k1, k2) -> k2));
        if (skuDiscountVoMap.isEmpty()) {
            log.keyword("mkTimeDiscountActivityMatch").info("当前订单:{}没有命中营销活动", confirmOrderVo.getCode());
            return confirmOrderVo;
        }
        confirmOrderInfoVos.forEach(confirmOrderInfoVo -> {
            try {
                ActivityRecord record = orderItemMarketingCalc4Record(skuDiscountVoMap, confirmOrderInfoVo, confirmOrderVo);
                if (null != record) {
                    confirmOrderInfoVo.getActivityRecord().add(record);
                }
            } catch (Exception e) {
                log.keyword("mkTimeDiscountActivityMatch").error("商品skuId:{} supplierSkuId:{} | 营销计算异常：{}", confirmOrderInfoVo.getSkuId(), confirmOrderInfoVo.getSupplierSkuId(), e);
            }
        });
        return confirmOrderVo;
    }


    /**
     * 订单商品限时折扣活动计算
     * @param skuDiscountVoMap
     * @param confirmOrderInfoVo
     * @param confirmOrderVo
     * @return
     */
    private ActivityRecord orderItemMarketingCalc4Record(Map<Long, RemoteMkActivitySkuDiscountVo> skuDiscountVoMap, ConfirmOrderInfoVo confirmOrderInfoVo, ConfirmOrderVo confirmOrderVo) {
        confirmOrderInfoVo.setHasLimitDiscount(BigDecimal.ZERO.intValue());
        if (MapUtils.isEmpty(skuDiscountVoMap)) {
            log.keyword("mkTimeDiscountActivityMatch").info("orderNO:{} 未命中限时折扣活动", confirmOrderVo.getCode());
            return null;
        }
        RemoteMkActivitySkuDiscountVo mkActivitySkuDiscountVo = skuDiscountVoMap.get(confirmOrderInfoVo.getSupplierSkuId());
        if (null == mkActivitySkuDiscountVo || !HAS_LIMIT_DISCOUNT.equals(mkActivitySkuDiscountVo.getHasLimitDiscount()) || CollectionUtils.isEmpty(mkActivitySkuDiscountVo.getActivityInfoList())) {
            log.keyword("mkTimeDiscountActivityMatch").info("商品skuId:{} supplierSkuId：{} 未命中限时折扣", confirmOrderInfoVo.getSkuId(), confirmOrderInfoVo.getSupplierSkuId());
            return null;
        }
        /**
         * 挨个商品行进行遍历，每个商品行可能会命中多个限时折扣活动，需要根据商品加购数量，活动剩余可参与数，活动折扣率，活动单个商品最大优惠上限进行计算后取最优惠值进行计算
         */
//        confirmOrderInfoVo.setFreeTotalAmount(BigDecimal.ZERO);
        List<RemoteMkActivityDiscountInfoVo> activityInfoList = mkActivitySkuDiscountVo.getActivityInfoList().stream().filter(item -> null != item && MarketingActivityTypeEnum.TIME_LIMIT_DISCOUNT.getCode().equals(item.getActivityType()) && null != item.getDiscountRate() && item.getDiscountRate().compareTo(BigDecimal.ZERO) > 0).toList();
        if (CollectionUtils.isEmpty(activityInfoList)) {
            log.keyword("mkTimeDiscountActivityMatch").warn("限时折扣活动数据异常");
            return null;
        }
        ActivityRecord activityRecord;
        BigDecimal maxDiscountAmount = BigDecimal.ZERO;
        RemoteMkActivityDiscountInfoVo maxDiscountInfoVo = null;
        ConfirmOrderInfoVo tempOrderInfoVO=null;
        log.keyword("mkTimeDiscountActivityMatch").info("商品skuId：{} | supplierSkuId:{} | 总库存: {} | 锁定库存：{} | 已售库存：{}", confirmOrderInfoVo.getSkuId(), confirmOrderInfoVo.getSupplierSkuId(), confirmOrderInfoVo.getStock(), confirmOrderInfoVo.getLockStock(), confirmOrderInfoVo.getSold());
        // 获取计算前平台服务费
        BigDecimal otherDiscount = confirmOrderInfoVo.getFreeTotalAmount().subtract(confirmOrderInfoVo.getProductFreeAmount()).setScale(2, RoundingMode.HALF_UP);
        for (RemoteMkActivityDiscountInfoVo item : activityInfoList) {
            // 商品已售数量
            int productSold = (null == confirmOrderInfoVo.getLockStock() ? 0 : confirmOrderInfoVo.getLockStock()) + (null == confirmOrderInfoVo.getSold() ? 0 : confirmOrderInfoVo.getSold());
            Long targetQuantity = (null == item.getTargetQuantity() || item.getTargetQuantity() < 0) ? 0 : item.getTargetQuantity();
            // 此处先用活动原销售目标数进行判断，如果已售数量已经达到活动销售目标数，则不再参与优惠
            if (productSold >= targetQuantity) {
                log.keyword("mkTimeDiscountActivityMatch").warn("商品当前已售库存: {} 已达活动ID: {} 的最低销售目标: {} ，不再参与优惠，跳过当前活动计算", productSold, item.getActivityId(), targetQuantity);
                continue;
            }
            // 订单商品数量
            Integer count = (null == confirmOrderInfoVo.getCount() || confirmOrderInfoVo.getCount() < 0) ? 0 : confirmOrderInfoVo.getCount();
            // 可参与优惠商品数（同一笔订单允许超过活动剩余名额）
            long activityRemainingQty = targetQuantity - productSold;
            // 如果activityRemainingQty>实际库存则取实际库存
            activityRemainingQty = activityRemainingQty > confirmOrderInfoVo.getStock() ? confirmOrderInfoVo.getStock() : activityRemainingQty;
            // 该字段仅用于首笔订单超卖
            long actDiscountNum = activityRemainingQty >= count ? count : activityRemainingQty;
            //销售批次最小购买数量,按最小购买数量匹配
            long buyMin = actDiscountNum;
            if ((activityRemainingQty < count) && (confirmOrderInfoVo.getBuyMin() > activityRemainingQty)) {
                buyMin = confirmOrderInfoVo.getBuyMin();
            }
            // 活动剩余可参与数小于订单商品数量
            if (buyMin < count && !confirmOrderVo.getIsMkLimitQty()){
                confirmOrderVo.setIsMkLimitQty(true);
            }
            //加购数量大于限时活动数量 -- 修改购物车数量
            if (actDiscountNum > 0 && buyMin != count){
                fixCartItemNum(confirmOrderInfoVo, buyMin);
            }
            BigDecimal discountRate = item.getDiscountRate();
            // 活动设置的折扣率为0~1,判断折扣率是否合法
            if (discountRate.compareTo(BigDecimal.ONE) > 0) {
                log.keyword("mkTimeDiscountActivityMatch").warn("折扣率数据设置异常:{}，大于1，重置为1", discountRate);
                discountRate = BigDecimal.ONE;
            }
            BigDecimal discountAmount = calcDiscountAmount(confirmOrderInfoVo, item, discountRate);
            // 当前商品优惠金额,以商品加购数进行统计  7.17业务调整(同一笔订单，商品命中活动，不受活动折扣剩余名额限制)
            BigDecimal currentDiscountAmount = discountAmount.multiply(BigDecimal.valueOf(count)).setScale(2, RoundingMode.HALF_UP);
            // 计算最大优惠值
            if (maxDiscountAmount.compareTo(currentDiscountAmount) > 0) {
                continue;
            }
            tempOrderInfoVO = new ConfirmOrderInfoVo();
            maxDiscountAmount = currentDiscountAmount;
            maxDiscountInfoVo = item;
            // 设置商品优惠金额
            tempOrderInfoVO.setProductFreeAmount(maxDiscountAmount);
            // 商品优惠价
            tempOrderInfoVO.setPriceFree(confirmOrderInfoVo.getPrice().subtract(discountAmount).setScale(2, RoundingMode.HALF_UP));
            // 最终单价 = 商品优惠价（限时折扣活动与优惠券互斥，不可叠加使用）
            tempOrderInfoVO.setFinalPrice(tempOrderInfoVO.getPriceFree());
//            // 实付金额
//            tempOrderInfoVO.setTotalAmount(confirmOrderInfoVo.getProductAmount().add(confirmOrderInfoVo.getOtherTotalAmount())
//                    .subtract(tempOrderInfoVO.getFreeTotalAmount()).setScale(2, RoundingMode.HALF_UP));
            tempOrderInfoVO.setHasLimitDiscount(HAS_LIMIT_DISCOUNT);
            tempOrderInfoVO.setActDiscountNum(actDiscountNum);
            tempOrderInfoVO.setActivityRemainingQty(activityRemainingQty);
            tempOrderInfoVO.setTargetQty(targetQuantity);
        }
        if(null == tempOrderInfoVO){
            return null;
        }
        // 设置商品优惠金额
        confirmOrderInfoVo.setProductFreeAmount(tempOrderInfoVO.getProductFreeAmount());
        // 最终单价 = 商品单价 - 平台补贴-当前活动优惠金额
        confirmOrderInfoVo.setFinalPrice(tempOrderInfoVO.getFinalPrice());
        // 活动优惠总金额
        confirmOrderInfoVo.setFreeTotalAmount(tempOrderInfoVO.getProductFreeAmount().add(otherDiscount).setScale(2, RoundingMode.HALF_UP));
        // 实付金额
        // 实付金额
        confirmOrderInfoVo.setTotalAmount(confirmOrderInfoVo.getProductAmount().add(confirmOrderInfoVo.getOtherTotalAmount())
                .subtract(confirmOrderInfoVo.getFreeTotalAmount()).setScale(2, RoundingMode.HALF_UP));
//        confirmOrderInfoVo.setTotalAmount(tempOrderInfoVO.getTotalAmount());
        confirmOrderInfoVo.setHasLimitDiscount(tempOrderInfoVO.getHasLimitDiscount());
        confirmOrderInfoVo.setActDiscountNum(tempOrderInfoVO.getActDiscountNum());
        confirmOrderInfoVo.setActivityRemainingQty(tempOrderInfoVO.getActivityRemainingQty());
        confirmOrderInfoVo.setTargetQty(tempOrderInfoVO.getTargetQty());
        // 设置划线价
        confirmOrderInfoVo.setPriceFree(tempOrderInfoVO.getPriceFree());
        // 设置活动商品剩余可参与数量
        activityRecord = new ActivityRecord();
        activityRecord.setActivityId(maxDiscountInfoVo.getActivityId());
        activityRecord.setType(maxDiscountInfoVo.getActivityType());
        activityRecord.setCustomerId(confirmOrderVo.getCustomerId());
        activityRecord.setFreeAmount(confirmOrderInfoVo.getFreeTotalAmount());
        activityRecord.setOrderCode(confirmOrderVo.getCode());
        activityRecord.setSupplierSpuId(confirmOrderInfoVo.getSupplierSpuId());
        activityRecord.setSupplierSkuId(confirmOrderInfoVo.getSupplierSkuId());
        activityRecord.setSpuName(confirmOrderInfoVo.getSpuName());
        activityRecord.setProductFreeAmount(confirmOrderInfoVo.getFreeTotalAmount());
        activityRecord.setHasLimitDiscount(true);
        activityRecord.setDiscountRate(maxDiscountInfoVo.getDiscountRate());
        /**
         *  活动商品剩余库存：活动销量目标数(targetQuantity)-(商品sold+商品lockStock)
         * 商品实际可参与活动数量: 下单商品数量为10，由于活动剩余可参与数量为2，所以实际只有2件参与
         * 若商品加购数大于等于活动剩余库存，那么实际参与数则为activityRemainingQty，反之则是count，全部参与
         */
        activityRecord.setActDiscountNum((int) Math.min(Integer.MAX_VALUE, confirmOrderInfoVo.getActDiscountNum()));
        activityRecord.setActivityRemainingQty((int) Math.min(Integer.MAX_VALUE, confirmOrderInfoVo.getActivityRemainingQty()));
        activityRecord.setMaxDiscountAmount(maxDiscountInfoVo.getMaxDiscountAmount());
        activityRecord.setTargetQuantity(maxDiscountInfoVo.getTargetQuantity());
        return activityRecord;
    }

    /**
     * 修正购物车商品数量
     * @param confirmOrderInfoVo
     * @param actDiscountNum
     */
    private void fixCartItemNum(ConfirmOrderInfoVo confirmOrderInfoVo, long actDiscountNum) {
        iCartItemService.updateBySkuId(confirmOrderInfoVo.getUserId(),confirmOrderInfoVo.getSkuId(),actDiscountNum,confirmOrderInfoVo.getRegionWhId());
    }


    /**
     * 计算订单商品行限时折扣优惠金额
     * @param confirmOrderInfoVo
     * @param item
     * @param discountRate
     * @return
     */
    private BigDecimal calcDiscountAmount(ConfirmOrderInfoVo confirmOrderInfoVo, RemoteMkActivityDiscountInfoVo item, BigDecimal discountRate) {
        BigDecimal price = (null == confirmOrderInfoVo.getPrice()) ? BigDecimal.ZERO : confirmOrderInfoVo.getPrice();
        BigDecimal maxDiscountAmount = (null == item.getMaxDiscountAmount()) ? BigDecimal.ZERO : item.getMaxDiscountAmount();
        BigDecimal actDiscountRate = BigDecimal.ONE.subtract(discountRate).setScale(2, RoundingMode.HALF_UP);
        // 根据最优惠的活动，设置活动信息，商品的扣减平台补贴后价格乘以活动的折扣率得到活动价格后再跟活动设置的最大优惠金额进行判断，若超过最大优惠金额，则取最大优惠金额
        BigDecimal discountAmount = price.multiply(actDiscountRate).setScale(2, RoundingMode.HALF_UP);
        if (discountAmount.compareTo(maxDiscountAmount) > 0) {
            discountAmount = maxDiscountAmount;
        }
        if (discountAmount.compareTo(BigDecimal.ZERO) <= 0) {
            log.keyword("mkDiscountAmountCalc").info("限时折扣优惠金额小于等于0，设置0.01元");
            discountAmount = BigDecimal.valueOf(0.01d).setScale(2, RoundingMode.HALF_UP);
        }
        if (discountAmount.compareTo(price) > 0) {
            discountAmount = price;
        }
        return discountAmount;
    }


    /**
     * 获取订单商品行匹配的营销活动
     * @param confirmOrderVo
     * @return
     */
    private List<RemoteMkActivitySkuDiscountVo> qryMkActivityByOrderItem(ConfirmOrderVo confirmOrderVo) {
        if (null == confirmOrderVo || CollectionUtils.isEmpty(confirmOrderVo.getConfirmOrderInfoVos())) {
            log.keyword("mkTimeDiscountActivityMatch").warn("订单商品行为空，不予匹配活动");
            return null;
        }
        RemoteMkActivitySkuDiscountBo bo = new RemoteMkActivitySkuDiscountBo();
        List<ConfirmOrderInfoVo> confirmOrderInfoVos = confirmOrderVo.getConfirmOrderInfoVos();
        List<RemoteMkActivitySkuBo> supplierSkuList = confirmOrderInfoVos.stream().filter(orderItem -> null != orderItem && null != orderItem.getSold() && null != orderItem.getLockStock()).map(orderItem -> {
            RemoteMkActivitySkuBo mkActivitySkuBo = new RemoteMkActivitySkuBo();
            mkActivitySkuBo.setSupplierSkuId(orderItem.getSupplierSkuId());
            return mkActivitySkuBo;
        }).collect(Collectors.toList());
        bo.setSupplierSkuList(supplierSkuList);
        bo.setCustomerId(confirmOrderVo.getCustomerId());
        bo.setLogisticsId(confirmOrderVo.getLogisticsId());
        bo.setCityWhId(confirmOrderVo.getCityWhId());
        bo.setRegionId(confirmOrderVo.getRegionWhId());
        bo.setSaleDate(confirmOrderVo.getSaleDate());
        List<RemoteMkActivitySkuDiscountVo> skuDiscountVos = null;
        try {
            skuDiscountVos = remoteMkActivityDiscountSummaryService.getSkuDiscountIdForOrder(bo);
        } catch (Exception e) {
            log.keyword("mkTimeDiscountActivityMatch").error("调用营销中心匹配活动接口异常,降级处理，不能影响正常下单", e);
            return null;
        }
        log.keyword("mkTimeDiscountActivityMatch").info("订单商品行匹配的营销活动入参：{}，活动返参：{}", JSON.toJSONString(bo), JSON.toJSONString(skuDiscountVos));
        return skuDiscountVos;
    }


    /**
     *
     * 订单商品行匹配的营销活动
     * @param confirmOrderVo
     */
    private void joinMkActivityRecord(ConfirmOrderVo confirmOrderVo) {
        if (CollectionUtils.isEmpty(confirmOrderVo.getConfirmOrderInfoVos())) {
            return;
        }
        List<ActivityRecord> activityRecordList = confirmOrderVo.getConfirmOrderInfoVos().stream().
                filter(orderItem -> null != orderItem && CollectionUtils.isNotEmpty(orderItem.getActivityRecord())).
                flatMap(orderItem -> orderItem.getActivityRecord().stream()).
                filter(activityRecord -> null != activityRecord && (MarketingActivityTypeEnum.TIME_LIMIT_DISCOUNT.getCode().equals(activityRecord.getType()))).toList();
        if (CollectionUtils.isEmpty(activityRecordList)) {
            return;
        }
        // 按订单维度写入活动优惠总额，订单商品行命中相同的限时折扣活动则进行合并累计总优惠金额即可
        Map<Long, BigDecimal> discountAmountMap = activityRecordList.stream().collect(Collectors.groupingBy(ActivityRecord::getActivityId, Collectors.mapping(ActivityRecord::getFreeAmount, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        if (MapUtils.isEmpty(discountAmountMap)) {
            return;
        }
        List<RemoteMkActivityRecordDTO> mkActivityRecordDTOList = new LinkedList<>();
        discountAmountMap.forEach((activityId, discountAmount) -> {
            RemoteMkActivityRecordDTO dto = new RemoteMkActivityRecordDTO();
            dto.setActivityId(activityId);
            dto.setActivityType(Long.valueOf(MarketingActivityTypeEnum.TIME_LIMIT_DISCOUNT.getCode()));
            dto.setOrderNo(confirmOrderVo.getCode());
            dto.setOrderTime(new Date());
            dto.setCustomerId(String.valueOf(confirmOrderVo.getCustomerId()));
            dto.setCityWhId(String.valueOf(confirmOrderVo.getCityWhId()));
            dto.setRegionWhId(String.valueOf(confirmOrderVo.getRegionWhId()));
            dto.setOrderAmt(String.valueOf(confirmOrderVo.getTotalAmount()));
            dto.setPlaceId(String.valueOf(confirmOrderVo.getPlaceId()));
            dto.setLogisticsId(String.valueOf(confirmOrderVo.getLogisticsId()));
            dto.setSupplierId(String.valueOf(confirmOrderVo.getConfirmOrderInfoVos().get(0).getSupplierId()));
            dto.setJoinTime(dto.getOrderTime());
            dto.setDiscountAmount(discountAmount);
            mkActivityRecordDTOList.add(dto);
        });
        try {
            remoteMkActivityDiscountSummaryService.joinMkActivityRecordByOrder(mkActivityRecordDTOList);
        } catch (Exception e) {
            log.keyword("joinMkActivityRecordByOrder").error("写入营销活动记录异常", e);
        }
    }


}