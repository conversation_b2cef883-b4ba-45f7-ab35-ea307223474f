package cn.xianlink.order.service;


import cn.xianlink.basic.api.domain.vo.RemoteRcsVo;

import java.util.List;
import java.util.Map;

/**
 * 客户统计
 * <AUTHOR> xiaodaibing on 2024-07-11 11:12
 */
public interface ICustomerStatisticsService {

    /**
     * 客户下单触发
     *
     * @param customerId
     * @return void
     * <AUTHOR> on 2024/7/11:11:12
     */
    void orderTrigger(Long customerId);
    /**
     * 客户报损触发
     *
     * @param customerId
     * @return void
     * <AUTHOR> on 2024/7/11:11:12
     */
    void reportTrigger(Long customerId);

    /**
     * 定时器统计近3月数据
     * <AUTHOR> on 2024/7/12:17:04
     * @return void
     */
    void jobStatistics();

    /**
     * 统计该城市仓下客户的报损率
     * <AUTHOR> on 2024/7/13:18:30
     * @param cityWhId
     * @return void
     */
    void cityWhStatistics(Long cityWhId);

    /**
     * 查询客户最近3个月的报损统计
     * <AUTHOR> on 2024/7/11:11:17
     * @param customerIds
     * @return java.util.Map<java.lang.Long,cn.xianlink.basic.domain.vo.ReportCustomerStatisticsVo>
     */
    Map<Long, RemoteRcsVo> getLast3MonthReportLoss(List<Long> customerIds);

    /**
     * 查询客户最近几个月的报损统计
     * <AUTHOR> on 2024/7/15:14:14
     * @param customerIds
     * @param month 月维度 传 6
     * @return cn.xianlink.basic.domain.vo.ReportCustomerStatisticsVo
     */
    RemoteRcsVo getMonthReportLoss(Long customerId, Integer month);

    /**
     * 查询客户某年的报损统计
     * <AUTHOR> on 2024/7/15:14:21
     * @param customerIds
     * @param year 年维度 传2023、2024
     * @return cn.xianlink.basic.domain.vo.ReportCustomerStatisticsVo
     */
    RemoteRcsVo getYearReportLoss(Long customerId, Integer year);
}
