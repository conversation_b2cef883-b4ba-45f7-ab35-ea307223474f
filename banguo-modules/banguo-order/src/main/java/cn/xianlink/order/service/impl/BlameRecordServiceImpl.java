package cn.xianlink.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.xianlink.basic.api.*;
import cn.xianlink.basic.api.domain.bo.RemoteMessageNotifyV2Bo;
import cn.xianlink.basic.api.domain.bo.RemoteRegionLogisticsQueryBo;
import cn.xianlink.basic.api.domain.vo.*;
import cn.xianlink.basic.api.enums.ImAfterSalesBizTypeEnum;
import cn.xianlink.basic.api.enums.MsgNotifyTemplateV2Enum;
import cn.xianlink.basic.api.enums.NotifyObjectTypeEnum;
import cn.xianlink.common.api.enums.order.*;
import cn.xianlink.common.api.enums.product.SupplierSkuBusinessTypeEnum;
import cn.xianlink.common.api.enums.product.TransSourceTypeEnum;
import cn.xianlink.common.api.enums.product.TransTypeCodeEnum;
import cn.xianlink.common.api.enums.product.TransTypeIoFlagEnum;
import cn.xianlink.common.api.enums.trade.AccountOrgTypeEnum;
import cn.xianlink.common.api.enums.trade.TransferBusiTypeEnum;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.SpringUtils;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.order.api.bo.DeductionInfoParamBo;
import cn.xianlink.order.api.constant.*;
import cn.xianlink.order.constant.OrderCacheNames;
import cn.xianlink.order.controller.CustomToolService;
import cn.xianlink.order.domain.*;
import cn.xianlink.order.domain.bo.blameRecord.*;
import cn.xianlink.order.domain.bo.common.OrderFileBO;
import cn.xianlink.order.domain.entruck.bo.RwWaitEntruckQueryBo;
import cn.xianlink.order.domain.entruck.vo.RwEntruckGoodsVo;
import cn.xianlink.order.domain.im.bo.ImBo;
import cn.xianlink.order.domain.vo.blameRecord.BlameRecordAdminVO;
import cn.xianlink.order.domain.vo.blameRecord.BlameRecordDetailVO;
import cn.xianlink.order.domain.vo.blameRecord.BlameRecordInfoVO;
import cn.xianlink.order.domain.vo.blameRecord.BlameRecordPageVO;
import cn.xianlink.order.domain.vo.common.CommonStatusCountVO;
import cn.xianlink.order.domain.vo.common.OrderFileVO;
import cn.xianlink.order.domain.vo.common.OrderLogVO;
import cn.xianlink.order.domain.vo.stockOut.StockoutDetailVO;
import cn.xianlink.order.enums.BizTypeEnum;
import cn.xianlink.order.enums.ResponsibilityTypeEnum;
import cn.xianlink.order.mapper.*;
import cn.xianlink.order.mq.producer.BlameRecordConfirmProducer;
import cn.xianlink.order.mq.producer.DeductionInfoCompleteProducer;
import cn.xianlink.order.service.IBlameRecordService;
import cn.xianlink.order.service.IRefundRecordService;
import cn.xianlink.order.service.im.IImService;
import cn.xianlink.order.util.CustomNoUtil;
import cn.xianlink.product.api.RemoteCwStockService;
import cn.xianlink.product.api.RemoteSupplierSkuService;
import cn.xianlink.product.api.domain.bo.RemoteCwTransDetailDTO;
import cn.xianlink.product.api.domain.bo.RemoteCwTransHeadDTO;
import cn.xianlink.product.api.domain.bo.RemoteQueryInfoListBo;
import cn.xianlink.product.api.domain.vo.RemoteSupplierSkuFundsInfoVo;
import cn.xianlink.product.api.domain.vo.RemoteSupplierSkuInfoVo;
import cn.xianlink.resource.api.RemoteFileService;
import cn.xianlink.resource.api.domain.RemoteOssBo;
import cn.xianlink.resource.api.domain.RemoteOssVo;
import cn.xianlink.system.api.RemoteDeptService;
import cn.xianlink.system.api.RemoteImGroupService;
import cn.xianlink.system.api.RemoteUserService;
import cn.xianlink.system.api.domain.bo.RemoteImUserBo;
import cn.xianlink.system.api.domain.bo.RemoteUserBo;
import cn.xianlink.system.api.domain.vo.RemoteImGroupVo;
import cn.xianlink.trade.api.RemoteTransferService;
import cn.xianlink.trade.api.domain.bo.RemoteTransferCreateBo;
import cn.xianlink.trade.api.domain.bo.RemoteTransferQueryBo;
import cn.xianlink.trade.api.domain.vo.RemoteTransferVo;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.xianlink.system.api.domain.bo.RemoteImUserBo.buildImUser;

/**
 * 判责单Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-25
 */
@RequiredArgsConstructor
@RefreshScope
@Service
@CustomLog
public class BlameRecordServiceImpl implements IBlameRecordService {

    private final BlameRecordMapper baseMapper;

    private final BlameRecordDetailMapper blameRecordDetailMapper;

    @DubboReference
    private final RemoteCityWhService remoteCityWhService;

    @DubboReference
    private final RemoteRegionWhService remoteRegionWhService;

    @DubboReference
    private final RemoteSupplierService remoteSupplierService;

    @DubboReference
    private final RemoteFileService remoteFileService;

    private final OrderLogMapper orderLogMapper;

    private final StockoutRecordMapper stockoutRecordMapper;

    private final StockoutRecordDetailMapper stockoutRecordDetailMapper;

    private final MoreGoodsRecordMapper moreGoodsRecordMapper;

    private final IRefundRecordService refundRecordService;

    @DubboReference
    private final RemoteRegionLogisticsService remoteRegionLogisticsService;

    @DubboReference
    private final RemoteSupplierSkuService remoteSupplierSkuService;

    @DubboReference
    private final RemoteBasCustomerService remoteBasCustomerService;

    private final ReportLossOrderMapper reportLossOrderMapper;

    @Resource
    private transient DeductionInfoCompleteProducer deductionInfoCompleteProducer;

    @DubboReference
    private final RemoteUserService remoteUserService;
    @DubboReference
    private final RemoteDeptService remoteDeptService;

    @Resource
    private transient BlameRecordConfirmProducer blameRecordConfirmProducer;
    @Resource
    private transient final CustomToolService customToolService;
    @Resource
    private transient final RwEntruckGoodsMapper rwEntruckGoodsMapper;
    @DubboReference
    private  final RemoteCwStockService remoteCwStockService;
    @DubboReference
    private RemoteMessageNotifyService remoteMessageNotifyService;

    private final DeductionInfoMapper deductionInfoMapper;

    @DubboReference
    private final RemoteTransferService remoteTransferService;
    @DubboReference
    private final RemoteImGroupService remoteImGroupService;
    @DubboReference
    private final RemoteBasImAfterSalesService afterSalesService;
    private final IImService imService;

    // 自动审核时间 小时
    private static final Integer AUTO_CONFIRM_TIME = 24;
    private static final Integer AUTO_CONFIRM_TIME_48 = 60;
    /**
     * 分页查询判责单列表
     */
    @Override
    public TableDataInfo<BlameRecordPageVO> blameRecordPage(BlameRecordPageBO bo) {
        Page<BlameRecordPageVO> page = baseMapper.blameRecordPage(bo, bo.build());
        if (CollectionUtil.isNotEmpty(page.getRecords())) {
            List<Long> supplierDeptIds = page.getRecords().stream().map(BlameRecordPageVO::getSupplierDeptId)
                    .filter(supplierDeptId -> ObjectUtil.isNotEmpty(supplierDeptId) && supplierDeptId > 0).distinct().toList();
            //sku
            List<Long> sukIds = page.getRecords().stream().map(BlameRecordPageVO::getSupplierSkuId).toList();
            List<RemoteSupplierSkuFundsInfoVo> skuInfoVos = remoteSupplierSkuService.queryInfoListByFunds(sukIds);
            Map<Long, RemoteSupplierSkuFundsInfoVo> skuMap = skuInfoVos.stream().collect(Collectors.toMap(RemoteSupplierSkuFundsInfoVo::getSkuId, Function.identity()));
            Map<Long, String> deptNameMap = new HashMap<>();
            if (ObjectUtil.isNotEmpty(supplierDeptIds)) {
                deptNameMap = remoteDeptService.getDeptNameMap(supplierDeptIds);
            }

            List<Long> cityWhIds = page.getRecords().stream().map(BlameRecordPageVO::getCityWhId).distinct().toList();
            List<Long> supplierIds = page.getRecords().stream().map(BlameRecordPageVO::getSupplierId).distinct().toList();
            //城市仓名称
            List<RemoteCityWhVo> remoteCityWhVos = remoteCityWhService.queryList(cityWhIds);
            Map<Long, String> cityMap = remoteCityWhVos.stream().collect(Collectors.toMap(RemoteCityWhVo::getId, RemoteCityWhVo::getName));
            //总仓名称
            List<RemoteRegionWhVo> remoteRegionWhVos = remoteRegionWhService.queryList();
            Map<Long, String> regionMap = remoteRegionWhVos.stream().collect(Collectors.toMap(RemoteRegionWhVo::getId, RemoteRegionWhVo::getRegionWhName));
            //供应商名称
            List<RemoteSupplierVo> supplierByIds = remoteSupplierService.getSupplierByIds(supplierIds);
            Map<Long, RemoteSupplierVo> supplierMap = supplierByIds.stream().collect(Collectors.toMap(RemoteSupplierVo::getId, Function.identity()));
            Map<Long, String> finalDeptNameMap = deptNameMap;

            page.getRecords().forEach(item -> {
                item.setCityWhName(cityMap.get(item.getCityWhId()));
                item.setRegionWhName(regionMap.get(item.getRegionWhId()));
                item.setSupplierDeptName(finalDeptNameMap.get(item.getSupplierDeptId()));
                item.setSupplierName(supplierMap.getOrDefault(item.getSupplierId(), new RemoteSupplierVo()).getName());
                item.setSupplierAlias(supplierMap.getOrDefault(item.getSupplierId(), new RemoteSupplierVo()).getAlias());

                RemoteSupplierSkuFundsInfoVo sku = skuMap.get(item.getSupplierSkuId());
                if(sku != null) {
                    item.setSpuStandards(sku.getSpuStandards());
                    item.setProducer(sku.getProducer());
                    // 产地简称
                    item.setAreaCode(sku.getAreaCode());
                    item.setBrand(sku.getBrand());
                    item.setShortProducer(sku.getShortProducer());
                }
            });
        }
        return TableDataInfo.build(page);
    }

    @Override
    public TableDataInfo<BlameRecordPageVO> blameRecordDeatilPage(BlameRecordPageBO bo) {
        Page<BlameRecordPageVO> page = baseMapper.blameRecordDeatilPage(bo, bo.build());
        if (CollectionUtil.isNotEmpty(page.getRecords())) {
            List<Long> supplierDeptIds = page.getRecords().stream().map(BlameRecordPageVO::getSupplierDeptId).filter(supplierDeptId -> supplierDeptId > 0).distinct().toList();
            Map<Long, String> deptNameMap = new HashMap<>();
            if (ObjectUtil.isNotEmpty(supplierDeptIds)) {
                deptNameMap = remoteDeptService.getDeptNameMap(supplierDeptIds);
            }
            List<Long> sukIds = page.getRecords().stream().map(BlameRecordPageVO::getSupplierSkuId).toList();
            //sku
            List<RemoteSupplierSkuFundsInfoVo> skuInfoVos = remoteSupplierSkuService.queryInfoListByFunds(sukIds);
            Map<Long, RemoteSupplierSkuFundsInfoVo> skuMap = skuInfoVos.stream().collect(Collectors.toMap(RemoteSupplierSkuFundsInfoVo::getSkuId, Function.identity()));


            List<Long> cityWhIds = page.getRecords().stream().map(BlameRecordPageVO::getCityWhId).distinct().toList();
            List<Long> supplierIds = page.getRecords().stream().map(BlameRecordPageVO::getSupplierId).distinct().toList();
            //城市仓名称
            List<RemoteCityWhVo> remoteCityWhVos = remoteCityWhService.queryList(cityWhIds);
            Map<Long, String> cityMap = remoteCityWhVos.stream().collect(Collectors.toMap(RemoteCityWhVo::getId, RemoteCityWhVo::getName));
            //总仓名称
            List<RemoteRegionWhVo> remoteRegionWhVos = remoteRegionWhService.queryList();
            Map<Long, String> regionMap = remoteRegionWhVos.stream().collect(Collectors.toMap(RemoteRegionWhVo::getId, RemoteRegionWhVo::getRegionWhName));
            //供应商名称
            List<RemoteSupplierVo> supplierByIds = remoteSupplierService.getSupplierByIds(supplierIds);
            Map<Long, RemoteSupplierVo> supplierMap = supplierByIds.stream().collect(Collectors.toMap(RemoteSupplierVo::getId, Function.identity()));
            Map<Long, String> finalDeptNameMap = deptNameMap;
            page.getRecords().forEach(item -> {
                item.setCityWhName(cityMap.get(item.getCityWhId()));
                item.setRegionWhName(regionMap.get(item.getRegionWhId()));
                item.setSupplierDeptName(finalDeptNameMap.get(item.getSupplierDeptId()));
                item.setSupplierName(supplierMap.getOrDefault(item.getSupplierId(), new RemoteSupplierVo()).getName());
                item.setSupplierAlias(supplierMap.getOrDefault(item.getSupplierId(), new RemoteSupplierVo()).getAlias());

                RemoteSupplierSkuFundsInfoVo sku = skuMap.get(item.getSupplierSkuId());
                if(sku != null) {
                    item.setSpuStandards(sku.getSpuStandards());
                    item.setProducer(sku.getProducer());
                    // 产地简称
                    item.setAreaCode(sku.getAreaCode());
                    item.setBrand(sku.getBrand());
                    item.setShortProducer(sku.getShortProducer());
                }
            });
        }
        return TableDataInfo.build(page);
    }

    /**
     * 获取判责单详情
     */
    @Override
    public BlameRecordInfoVO getBlameById(Long id, Integer resType) {
        BlameRecordInfoVO detailVO = new BlameRecordInfoVO();
        //获取判责单信息
        BlameRecord blameRecord = baseMapper.selectById(id);
        BeanUtil.copyProperties(blameRecord, detailVO);
        if (ObjectUtil.isNotEmpty(detailVO.getSupplierDeptId()) && detailVO.getSupplierDeptId() > 0) {
            Map<Long, String> deptNameMap = remoteDeptService.getDeptNameMap(Lists.newArrayList(blameRecord.getSupplierDeptId()));
            detailVO.setSupplierDeptName(deptNameMap.get(blameRecord.getSupplierDeptId()));
        }
        //获取费用详情
        LambdaQueryWrapper<BlameRecordDetail> qw = Wrappers.lambdaQuery();
        qw.eq(BlameRecordDetail::getBlameRecordId, id).eq(BlameRecordDetail::getDelFlag, 0);
        if (ObjectUtil.isNotNull(resType)){
            if (resType.equals(AccountTypeStatusEnum.CITY.getCode())){
                qw.eq(BlameRecordDetail::getResponsibilityType, 2);
            }else if (resType.equals(AccountTypeStatusEnum.SUPPLIER.getCode())){
                qw.eq(BlameRecordDetail::getResponsibilityType, 1);
            }
        }else {
            detailVO.setProductAmount(detailVO.getProductAmount().add(blameRecord.getSubsidyFreeAmount()));
        }
        List<BlameRecordDetail> blameRecordDetailList = blameRecordDetailMapper.selectList(qw);
        //待判责状态下详情是没有的,要判空一下
        if (CollectionUtil.isNotEmpty(blameRecordDetailList)){
            List<BlameRecordDetailVO> detailVOS = new ArrayList<>();
            for (BlameRecordDetail l : blameRecordDetailList){
                BlameRecordDetailVO detail = new BlameRecordDetailVO();
                BeanUtil.copyProperties(l, detail);
                detail.setRecordDetailId(l.getId());
                detailVOS.add(detail);
                if (ObjectUtil.isNotNull(resType)){
                    if (l.getAmountType().equals(10)) {
                        detailVO.setProductAmount(l.getAmount());
                    }else {
                        detailVO.setOtherAmount(l.getAmount());
                    }
                }
            }
            detailVO.setBlameRecordDetailVOList(detailVOS);
            detailVO.setBlameExplain(blameRecordDetailList.get(0).getBlameExplain());
            //供应商,城市仓,判责状态取详情
            if (ObjectUtil.isNotNull(resType)) {
                if (resType.equals(AccountTypeStatusEnum.CITY.getCode()) || resType.equals(AccountTypeStatusEnum.SUPPLIER.getCode())) {
                    detailVO.setBlameDetailStatus(blameRecordDetailList.get(0).getBlameStatus());
                    if (CollectionUtil.isNotEmpty(blameRecordDetailList) && blameRecordDetailList.get(0).getBlameStatus().equals(BlameDetailStatusEnum.STATE_CONFIRM.getCode())) {
                        // 获取当前时间
                        Date today = blameRecordDetailList.get(0).getCreateTime();
                        Calendar calendar = Calendar.getInstance();
                        calendar.setTime(today);
                        // 加上一天
                        calendar.add(Calendar.DAY_OF_MONTH, 1);
                        // 获取修改后的时间
                        Date tomorrow = calendar.getTime();
                        long l = tomorrow.getTime() - System.currentTimeMillis();
                        detailVO.setConfirmSecond(Math.max(l, 0L));
                    }
                }
            }
        }
        //获取文件
        List<RemoteOssVo> ossVoList = remoteFileService.select("Blame_Record", id.toString());
        //文件赋值
        if (CollectionUtil.isNotEmpty(ossVoList)){
            List<OrderFileVO> fileList = new ArrayList<>();
            ossVoList.forEach(item -> {
                OrderFileVO file = new OrderFileVO();
                file.setUrl(item.getUrl());
                file.setTag(item.getTag());
                file.setSort(item.getSort());
                fileList.add(file);
            });
            detailVO.setFileList(fileList);
        }
        //获取城市仓名称
        RemoteCityWhVo remoteCityWhVo = remoteCityWhService.queryById(blameRecord.getCityWhId());
        if (ObjectUtil.isNotNull(remoteCityWhVo)){
            detailVO.setCityWhName(remoteCityWhVo.getName());
            detailVO.setCityWhCode(remoteCityWhVo.getCode());
        }
        //获取供应商名称
        RemoteSupplierVo supplierVo = remoteSupplierService.getSupplierById(blameRecord.getSupplierId());
        if (ObjectUtil.isNotNull(supplierVo)){
            detailVO.setSupplierName(supplierVo.getName());
            detailVO.setSupplierCode(supplierVo.getCode());
            detailVO.setSupplierAlias(supplierVo.getAlias());
        }
        //获取总仓名称
        RemoteRegionWhVo remoteRegionWhVo = remoteRegionWhService.queryById(blameRecord.getRegionWhId());
        if (ObjectUtil.isNotNull(remoteRegionWhVo)){
            detailVO.setRegionWhName(remoteRegionWhVo.getRegionWhName());
            detailVO.setRegionWhCode(remoteRegionWhVo.getRegionWhCode());
        }
        //计算处理时间
        Date now = new Date();
        if (detailVO.getBlameStatus().equals(BlameStatusEnum.ALL_CONFIRM.getCode()) || detailVO.getBlameStatus().equals(BlameStatusEnum.FINISH.getCode())){
            detailVO.setSeconds(0L);
        }else {
            detailVO.setSeconds(now.getTime() - detailVO.getCreateTime().getTime());
        }
        //获取操作记录
        LambdaQueryWrapper<OrderLog> queryWrapper = Wrappers.lambdaQuery(OrderLog.class)
                .eq(OrderLog::getSourceId, id).eq(OrderLog::getDelFlag, 0).eq(OrderLog::getSourceType, OrderLogSourceTypeEnum.BLAME.getCode());
        List<OrderLogVO> orderLogs = orderLogMapper.selectVoList(queryWrapper);
        detailVO.setOrderLogList(orderLogs);
        //获取订单编号
        if (ObjectUtil.isNotEmpty(blameRecord) && blameRecord.getSourceType().equals(BlameSourceTypeEnum.LESS_GOODS.getCode())) {
            List<StockoutRecordDetail> detailList = stockoutRecordDetailMapper.selectByStockoutRecordCode(blameRecord.getSourceCode(), detailVO.getSupplierSkuId());
            if (ObjectUtil.isNotEmpty(detailList)) {
                detailVO.setOrderCode(detailList.stream().map(StockoutRecordDetail::getOrderCode).filter(ObjectUtil::isNotEmpty).distinct().toList());
            }
        }

        // 商品详情
        Map<Long, RemoteSupplierSkuInfoVo> skuInfoMap = customToolService.getSkuInfoMap(ListUtil.toList(detailVO.getSupplierSkuId()));
        RemoteSupplierSkuInfoVo skuInfoVo = skuInfoMap.get(detailVO.getSupplierSkuId());
        if (ObjectUtil.isNotNull(skuInfoVo)) {
            detailVO.setSpuStandards(skuInfoVo.getSpuStandards());
            detailVO.setProducer(skuInfoVo.getProducer());
            detailVO.setSaleDate(skuInfoVo.getSaleDate());
            // 产地简称
            detailVO.setAreaCode(skuInfoVo.getAreaCode());
            detailVO.setBrand(skuInfoVo.getBrand());
            detailVO.setShortProducer(skuInfoVo.getShortProducer());
        }
        // 装车记录时间
        RwWaitEntruckQueryBo queryBo = new RwWaitEntruckQueryBo();
        queryBo.setSaleDate(blameRecord.getSaleDate());
        queryBo.setRegionWhId(blameRecord.getRegionWhId());
        queryBo.setLogisticsId(blameRecord.getLogisticsId());
        queryBo.setSupplierSkuId(blameRecord.getSupplierSkuId());
        List<RwEntruckGoodsVo> goodsVoList = rwEntruckGoodsMapper.customSkuIdEntruckList(queryBo);
        detailVO.setEntruckTimeList(goodsVoList.stream().filter(f -> f.getStatus().intValue() != DeliveryStatusEnum.WAIT_INSPECT.getCode() && f.getEntruckTime() != null).collect(Collectors.toList()));
        detailVO.setTotalDeliveryQuantity(goodsVoList.stream().mapToInt(RwEntruckGoodsVo::getDeliveryQuantity).sum());
        List<RemoteRegionWhParkingVo> parkingVos = remoteRegionWhService.queryParkingByLogisticsId(ListUtil.toList(blameRecord.getLogisticsId()));
        if (CollectionUtil.isNotEmpty(parkingVos)) {
            detailVO.setParkingNo(parkingVos.get(0).getParkingNo());
        }
        return detailVO;
    }

    /**
     * 新增判责单
     * 不支持手动新增,新增是产生缺货/少货/报损(目前逻辑只有少货报损才能新增,缺货是自动判责给供应商)
     * 这个方法可以理解为手动修改判责单担责主体承担金额
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @Lock4j(name = OrderCacheNames.ADD_BLAME, keys = "#bo.id", expire = 5000, acquireTimeout = 3000)
    public void addBlame(AddBlameRecordBO bo) {
        //查看判责单是否存在以及状态
        BlameRecord blameRecord = baseMapper.selectById(bo.getId());
        if (ObjectUtil.isNull(blameRecord)) {
            throw new ServiceException("判责单不存在");
        }
        if (!BlameStatusEnum.STATE_BLAME.getCode().equals(blameRecord.getBlameStatus())) {
            throw new ServiceException("判责单已判责");
        }
        //判责详情不能为空
        if (CollectionUtil.isEmpty(bo.getBlameRecordDetailVOList())) {
            throw new ServiceException("判责单详情不能为空");
        }
        // todo 目前只有少货判责，未来有更多类型的判责，需要修改此处代码
        //1.2.2 判责只判商品金额
        List<BlameDetailBO> details = new ArrayList<>();
        bo.getBlameRecordDetailVOList().forEach(item -> {
            if (item.getAmountType().equals(10)) {
                details.add(item);
            }
        });
        bo.setBlameRecordDetailVOList(details);

        //没传id的话,走的是新建少货单后再创建判责单
        if (bo.getIsSplit()){
            BlameRecord newBlame = new BlameRecord();
            BeanUtil.copyProperties(blameRecord, newBlame);
            newBlame.setId(null);
            newBlame.setTotalCount(bo.getBlameCount());
            BigDecimal productAmount = bo.getBlameRecordDetailVOList().stream().filter(l -> l.getAmountType().equals(10)).map(BlameDetailBO::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal otherAmount = bo.getBlameRecordDetailVOList().stream().filter(l -> l.getAmountType().equals(20)).map(BlameDetailBO::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            newBlame.setProductAmount(productAmount);
            newBlame.setOtherAmount(otherAmount);
            newBlame.setSupplierDeptId(blameRecord.getSupplierDeptId());
            newBlame.setBlameAmount(productAmount.add(otherAmount));
            newBlame.setBlameStatus(BlameStatusEnum.HAS_BLAME.getCode());
            newBlame.setCode(CustomNoUtil.getBlameNo(LocalDate.now()));
            baseMapper.insert(newBlame);
            //原判责单减少数量/金额
            blameRecord.setTotalCount(blameRecord.getTotalCount() - newBlame.getTotalCount());
            blameRecord.setProductAmount(blameRecord.getProductAmount().subtract(newBlame.getProductAmount()));
            blameRecord.setOtherAmount(blameRecord.getOtherAmount().subtract(newBlame.getOtherAmount()));
            blameRecord.setBlameAmount(blameRecord.getProductAmount().add(blameRecord.getOtherAmount()));
            blameRecord.setResponsibilityType(bo.getBlameRecordDetailVOList().get(0).getResponsibilityType());
            //更新原判责单信息
            baseMapper.updateById(blameRecord);
            createBlameRecordDetail(bo, newBlame);
        }else {
            //先查询是否有已经存在的判责详情
            List<BlameRecordDetail> list = blameRecordDetailMapper.selectList(Wrappers.lambdaQuery(BlameRecordDetail.class)
                    .eq(BlameRecordDetail::getBlameRecordId, bo.getId())
                    .eq(BlameRecordDetail::getDelFlag, 0));
            //存在的话,判断下金额是否超标
            if (CollectionUtil.isNotEmpty(list)) {
                BigDecimal reduce = list.stream().map(BlameRecordDetail::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                //可能填的是数量,先处理一下
                bo.getBlameRecordDetailVOList().forEach(l -> {
                    if (l.getBlameCount() != null) {
                        //拆分产生新的判责单
                        BigDecimal amount;
                        if (l.getAmountType().equals(10)) {
                            amount = new BigDecimal(l.getBlameCount()).divide(new BigDecimal(blameRecord.getTotalCount()), 2, RoundingMode.HALF_UP)
                                    .multiply(blameRecord.getProductAmount());
                        } else {
                            amount = new BigDecimal(l.getBlameCount()).divide(new BigDecimal(blameRecord.getTotalCount()), 2, RoundingMode.HALF_UP)
                                    .multiply(blameRecord.getOtherAmount());
                        }
                        l.setAmount(amount);
                    }
                });
                BigDecimal totalAmount = reduce.add(bo.getBlameRecordDetailVOList().stream().map(BlameDetailBO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                if (blameRecord.getBlameAmount().compareTo(totalAmount) < 0) {
                    throw new ServiceException("判责金额不能超过判责单金额");
                }
            }
            blameRecord.setBlameStatus(BlameStatusEnum.HAS_BLAME.getCode());
            blameRecord.setResponsibilityType(bo.getBlameRecordDetailVOList().get(0).getResponsibilityType());
            baseMapper.updateById(blameRecord);
            createBlameRecordDetail(bo, blameRecord);
        }
        //更新来源单据 已判责
        LambdaQueryWrapper<StockoutRecord> queryWrapper = Wrappers.lambdaQuery(StockoutRecord.class);
        queryWrapper.select(StockoutRecord::getId).eq(StockoutRecord::getCode, blameRecord.getSourceCode()).eq(StockoutRecord::getDelFlag, 0);
        List<StockoutRecord> stockoutRecords = stockoutRecordMapper.selectList(queryWrapper);
        if (CollectionUtil.isNotEmpty(stockoutRecords)){
            stockoutRecords.forEach(l->l.setBlameStatus(blameRecord.getBlameStatus()));
            stockoutRecordMapper.updateBatchById(stockoutRecords);
        }
    }

    private void createBlameRecordDetail(AddBlameRecordBO bo, BlameRecord blameRecord) {
        //创建新判责单明细
        List<BlameRecordDetail> newDetailList = new ArrayList<>();
        for (BlameDetailBO detail : bo.getBlameRecordDetailVOList()) {
            BlameRecordDetail blameRecordDetail = new BlameRecordDetail();
            BeanUtil.copyProperties(detail, blameRecordDetail);
            blameRecordDetail.setBlameRecordId(blameRecord.getId());
            blameRecordDetail.setSourceType(blameRecord.getSourceType());
            blameRecordDetail.setSourceCode(blameRecord.getSourceCode());
            blameRecordDetail.setBlameExplain(bo.getBlameExplain());
            blameRecordDetail.setBlameStatus(BlameDetailStatusEnum.STATE_CONFIRM.getCode());
            blameRecordDetail.setCreateRemark(bo.getCreateRemark());
            blameRecordDetail.setBlameCount(blameRecord.getTotalCount());
            if (detail.getResponsibilityType().equals(0)) {
                blameRecordDetail.setBlameStatus(BlameDetailStatusEnum.HAS_CONFIRM.getCode());
            }
            if (detail.getAmountType().equals(20)) {
                blameRecordDetail.setPlatformFreight(blameRecord.getPlatformFreight());
                blameRecordDetail.setBaseFreight(blameRecord.getBaseFreight());
                blameRecordDetail.setRegionFreight(blameRecord.getRegionFreight());
                blameRecordDetail.setFinancialServicePrice(blameRecord.getFinancialServicePrice());
                blameRecordDetail.setServiceAmount(blameRecord.getServiceAmount());
            }
            newDetailList.add(blameRecordDetail);
        }
        //新增操作日志
        OrderLog orderLog = new OrderLog();
        orderLog.setSourceId(blameRecord.getId());
        orderLog.setSourceType(OrderLogSourceTypeEnum.BLAME.getCode());
        String remark = "";
        if (ObjectUtil.isNotNull(bo.getSupId())){
            orderLog.setOperate("供应商判责");
        }else {
            orderLog.setOperate("总仓判责");
        }
        if (blameRecord.getResponsibilityType().equals(ResponsibilityAllTypeEnum.CITY_WH.getCode())){
            RemoteCityWhVo remoteCityWhVo = remoteCityWhService.queryById(blameRecord.getCityWhId());
            remark = "承担方：城市仓-" + remoteCityWhVo.getName() + "\n"
                    + "金额:" + blameRecord.getProductAmount() + "元";

        }else if (blameRecord.getResponsibilityType().equals(ResponsibilityAllTypeEnum.SUPPLIER.getCode())){
            RemoteSupplierVo supplierVo = remoteSupplierService.getSupplierById(blameRecord.getSupplierId());
            remark = "承担方：供应商-" + supplierVo.getName() + "\n"
                    + "金额:" + blameRecord.getProductAmount() + "元";
        }
        orderLog.setRemark(remark);
        orderLog.setCreateTime(new Date());
        blameRecordDetailMapper.insertBatch(newDetailList);
        orderLogMapper.insert(orderLog);
        //新增文件
        createFile(bo.getFileList(), blameRecord);
        //都无担责方,直接确认
        if (CollectionUtil.isNotEmpty(newDetailList) && newDetailList.stream().allMatch(l -> l.getResponsibilityType().equals(0))) {
            blameRecord.setBlameStatus(BlameStatusEnum.FINISH.getCode());
            baseMapper.updateById(blameRecord);
        }
        //判责详情担责方有总仓,直接确认
        else if (CollectionUtil.isNotEmpty(newDetailList) && newDetailList.stream().allMatch(l -> l.getResponsibilityType().equals(3))) {
            confirm(blameRecord.getId(), AccountTypeStatusEnum.REGION.getCode(),true);
            blameRecord.setBlameStatus(BlameStatusEnum.FINISH.getCode());
        }else if (ObjectUtil.isNotNull(bo.getSupId()) && blameRecord.getResponsibilityType().equals(ResponsibilityAllTypeEnum.SUPPLIER.getCode())){
            confirm(blameRecord.getId(), AccountTypeStatusEnum.SUPPLIER.getCode(),true);
            blameRecord.setBlameStatus(BlameStatusEnum.FINISH.getCode());
        }
        //总仓直接判责市采类型的  完成
        else if (ObjectUtil.isNotNull(bo.getRegionWhId()) && blameRecord.getBusinessType().equals(OrderBusinessTypeEnum.BUSINESS_TYPE1.getCode())){
            confirm(blameRecord.getId(), blameRecord.getResponsibilityType(),false);
            blameRecord.setBlameStatus(BlameStatusEnum.FINISH.getCode());
        }
        else {
            //存在待确认判责方,发送延时消息
            BlameConfirmMessageBO messageBO = new BlameConfirmMessageBO();
            messageBO.setId(blameRecord.getId());
            if (ObjectUtil.isNotNull(bo.getSupId())){
                messageBO.setExpireTime(DateUtil.offsetHour(new Date(), AUTO_CONFIRM_TIME_48).toJdkDate());
            }else {
                messageBO.setExpireTime(DateUtil.offsetHour(new Date(), AUTO_CONFIRM_TIME).toJdkDate());
            }
            blameRecordConfirmProducer.send(messageBO, null);
            //获取少货单
            StockoutRecord stockoutRecord = stockoutRecordMapper.selectByCode(blameRecord.getSourceCode());
            //发公众号通知
            this._sendMessage(blameRecord,stockoutRecord);
        }

    }

    /**
     * 新增文件
     */
    private void createFile(List<OrderFileBO> bo, BlameRecord blameRecord) {
        if (CollectionUtil.isNotEmpty(bo)) {
            List<RemoteOssBo> bos = new ArrayList<>();
            bo.forEach(l -> {
                RemoteOssBo orderFile = new RemoteOssBo();
                orderFile.setBusinessType("Blame_Record");
                orderFile.setKeyword(blameRecord.getId().toString());
                orderFile.setUrl(l.getUrl());
                orderFile.setTag(l.getTag());
                bos.add(orderFile);
            });
            remoteFileService.batchInsert(bos);
        }
    }

    /**
     * 撤回判责
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancel(Long id) {
        //先确认判责单是否已经确认  确认了不能撤回
        BlameRecord blameRecord = baseMapper.selectById(id);
        if (!BlameStatusEnum.HAS_BLAME.getCode().equals(blameRecord.getBlameStatus())) {
            throw new ServiceException("判责单已确认,不能撤回");
        }
        //删掉相关详情即可,回退判责主单状态
        LambdaQueryWrapper<BlameRecordDetail> queryWrapper = Wrappers.lambdaQuery(BlameRecordDetail.class);
        queryWrapper.eq(BlameRecordDetail::getBlameRecordId, id).eq(BlameRecordDetail::getDelFlag, 0);
        List<BlameRecordDetail> details = blameRecordDetailMapper.selectList(queryWrapper);
        if (CollectionUtil.isNotEmpty(details)){
            details.forEach(l->l.setDelFlag(l.getId()));
            blameRecordDetailMapper.updateBatchById(details);
        }
        blameRecord.setBlameStatus(BlameStatusEnum.STATE_BLAME.getCode());
        baseMapper.updateById(blameRecord);
        //清除操作日志
        LambdaQueryWrapper<OrderLog> qw = Wrappers.lambdaQuery(OrderLog.class)
                .eq(OrderLog::getSourceId, id).eq(OrderLog::getDelFlag, 0).eq(OrderLog::getSourceType, OrderLogSourceTypeEnum.BLAME.getCode())
                .orderByDesc(OrderLog::getCreateTime);
        List<OrderLog> orderLogs = orderLogMapper.selectList(qw);
        if (CollectionUtil.isNotEmpty(orderLogs)) {
            OrderLog orderLog = orderLogs.get(0);
            orderLogMapper.deleteById(orderLog);
        }
        //删除原文件
        remoteFileService.delete("Blame_Record",id.toString());
        //更新来源单据 待判责
        LambdaQueryWrapper<StockoutRecord> qws = Wrappers.lambdaQuery(StockoutRecord.class);
        qws.select(StockoutRecord::getId).eq(StockoutRecord::getCode, blameRecord.getSourceCode()).eq(StockoutRecord::getDelFlag, 0);
        List<StockoutRecord> stockoutRecords = stockoutRecordMapper.selectList(qws);
        if (CollectionUtil.isNotEmpty(stockoutRecords)){
            stockoutRecords.forEach(l->l.setBlameStatus(BlameStatusEnum.STATE_BLAME.getCode()));
            stockoutRecordMapper.updateBatchById(stockoutRecords);
        }
    }

    /**
     * 修改判责单信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBlame(AddBlameRecordBO bo) {
        //查看状态是否真的没确认
        List<Long> detailId = bo.getBlameRecordDetailVOList().stream().map(BlameDetailBO::getBlameRecordDetailId).toList();
        if (CollectionUtil.isNotEmpty(detailId)) {
            LambdaQueryWrapper<BlameRecordDetail> queryWrapper = Wrappers.lambdaQuery(BlameRecordDetail.class);
            queryWrapper.in(BlameRecordDetail::getId, detailId).eq(BlameRecordDetail::getDelFlag, 0);
            List<BlameRecordDetail> details = blameRecordDetailMapper.selectList(queryWrapper);
            Map<Long, BlameRecordDetail> map = details.stream().collect(Collectors.toMap(BlameRecordDetail::getId, Function.identity()));
            List<BlameRecordDetail> updateList = new ArrayList<>();
            bo.getBlameRecordDetailVOList().forEach(l -> {
                BlameRecordDetail detail = map.get(l.getBlameRecordDetailId());
                if (detail != null && detail.getBlameStatus().equals(BlameDetailStatusEnum.HAS_CONFIRM.getCode())) {
                    throw new ServiceException("判责单详情已确认,不能修改");
                }
                //修改只修改承担方  直接改
                BeanUtil.copyProperties(l, detail);
                updateList.add(detail);
            });
            blameRecordDetailMapper.updateBatchById(updateList);
            //删除原文件
            remoteFileService.delete("Blame_Record",bo.getId().toString());
            //新增文件
            BlameRecord blameRecord = new BlameRecord();
            blameRecord.setId(bo.getId());
            createFile(bo.getFileList(), blameRecord);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changBlame(AddBlameRecordBO bo) {
        //获取判责单
        BlameRecord blameRecord = baseMapper.selectById(bo.getId());
        if (ObjectUtil.isNull(blameRecord)){
            throw new ServiceException("判责单不存在");
        }
        //获取少货单详情
        StockoutDetailVO stockoutRecord = stockoutRecordMapper.getInfoByCode(blameRecord.getSourceCode());
        if (ObjectUtil.isNull(stockoutRecord)){
            throw new ServiceException("少货单不存在");
        }
        //校验状态
        if (!stockoutRecord.getBlameStatus().equals(BlameStatusEnum.APPEAL.getCode())){
            throw new ServiceException("少货单判责状态异常");
        }
        List<BlameRecordDetail> detailList = new ArrayList<>();
        LambdaQueryWrapper<BlameRecordDetail> queryWrapper = Wrappers.lambdaQuery(BlameRecordDetail.class);
        queryWrapper.eq(BlameRecordDetail::getBlameRecordId, blameRecord.getId()).eq(BlameRecordDetail::getDelFlag, 0);
        List<BlameRecordDetail> details = blameRecordDetailMapper.selectList(queryWrapper);
        //介入流程 介入后直接完成
        if (bo.getBlameType().equals(1)){
            List<BlameRecordDetail> instertList = new ArrayList<>();
            bo.getBlameRecordDetailVOList().forEach(l -> {
                BlameRecordDetail detail = new BlameRecordDetail();
                //直接改
                BeanUtil.copyProperties(details.get(0), detail);
                BeanUtil.copyProperties(l, detail);
                detail.setBlameExplain(bo.getBlameExplain());
                detail.setBlameRecordId(blameRecord.getId());
                detail.setId(null);
                instertList.add(detail);
                blameRecord.setResponsibilityType(l.getResponsibilityType());
            });
            //删除原有的
            details.forEach(l->l.setDelFlag(l.getId()));
            blameRecordDetailMapper.updateBatchById(details);
            blameRecordDetailMapper.insertBatch(instertList);
            detailList.addAll(instertList);
        }else {
            detailList.addAll(details);
        }
        //驳回跟介入新增的操作日志都一样的，驳回没其他操作。可以不用处理
        OrderLog orderLog = new OrderLog();
        orderLog.setSourceId(blameRecord.getId());
        orderLog.setSourceType(OrderLogSourceTypeEnum.BLAME.getCode());
        orderLog.setOperate("总仓判责");
        //获取承担方信息
        String name;
        //获取城市仓名称
        switch (Objects.requireNonNull(ResponsibilityAllTypeEnum.load(blameRecord.getResponsibilityType()))) {
            case SUPPLIER -> {
                RemoteSupplierVo supplierVo = remoteSupplierService.getSupplierById(blameRecord.getSupplierId());
                name = supplierVo.getName();
            }
            case REGION_WH -> {
                RemoteRegionWhVo remoteRegionWhVo = remoteRegionWhService.queryById(blameRecord.getRegionWhId());
                name = remoteRegionWhVo.getRegionWhName();
            }
            case CITY_WH -> {
                RemoteCityWhVo remoteCityWhVo = remoteCityWhService.queryById(blameRecord.getCityWhId());
                name = remoteCityWhVo.getName();
            }
            default -> name = "";
        }
        name = "承担方：" + Objects.requireNonNull(ResponsibilityAllTypeEnum.load(blameRecord.getResponsibilityType())).getDesc()
                + "-" + name + "\n" + "金额：" + blameRecord.getProductAmount() + "\n" + "判责说明：" + bo.getBlameExplain();
        orderLog.setRemark(name);
        orderLogMapper.insert(orderLog);
        //新增关联文档
        if (CollectionUtil.isNotEmpty(bo.getFileList())) {
            List<RemoteOssBo> bos = new ArrayList<>();
            bo.getFileList().forEach(l -> {
                RemoteOssBo orderFile = new RemoteOssBo();
                orderFile.setBusinessType("Blame_Record_Log");
                orderFile.setKeyword(orderLog.getId().toString());
                orderFile.setUrl(l.getUrl());
                orderFile.setTag(l.getTag());
                bos.add(orderFile);
            });
            remoteFileService.batchInsert(bos);
        }
        //最终流程都是走完成流程 调用新的确认判责方法
        config(blameRecord, detailList);
        //更新单据
        StockoutRecord stockout = new StockoutRecord();
        stockout.setId(stockoutRecord.getId());
        stockout.setBlameStatus(BlameStatusEnum.FINISH.getCode());
        stockoutRecordMapper.updateById(stockout);
        blameRecord.setBlameStatus(BlameStatusEnum.FINISH.getCode());
        baseMapper.updateById(blameRecord);
        this._sendMessage(blameRecord,stockout);
    }

    /**
     * 总仓判责直接确认入口
     * @param blameRecord
     * @param detailList
     */
    private void config(BlameRecord blameRecord, List<BlameRecordDetail> detailList) {
        //少货确定总仓判责数量
        AtomicReference<Integer> sumSHCount = new AtomicReference<>(0);
        AtomicReference<BigDecimal> amountTotal = new AtomicReference<>(BigDecimal.ZERO);
        //判断产生扣款单(区分商品非商品金额)
        detailList.forEach(l -> {
            //承担方是供应商 承担商品金额  且商品优惠补贴金额>0, 供应商->总仓
            if (l.getResponsibilityType().equals(ResponsibilityAllTypeEnum.SUPPLIER.getCode()) && l.getAmountType().equals(10)
                    && blameRecord.getSubsidyFreeAmount().compareTo(BigDecimal.ZERO) > 0) {
                //供应商扣款
                supCreateDedyctionInfo(blameRecord.getId(), blameRecord, l,AmountTypeEnum.LOSS.getCode(),DeductionTypeEnum.KKYY_PRO_SUB.getCode());
            }
//            //商品金额且承担方是总仓 总仓->供应商
            else if (l.getResponsibilityType().equals(ResponsibilityAllTypeEnum.REGION_WH.getCode()) && l.getAmountType().equals(10)){
                //供应商加款
                supCreateDedyctionInfo(blameRecord.getId(), blameRecord, l,AmountTypeEnum.ADD.getCode(),DeductionTypeEnum.JKYY_SHPZ.getCode());
            }
            //城市仓
            else if (l.getResponsibilityType().equals(ResponsibilityAllTypeEnum.CITY_WH.getCode())){
                //城市仓扣款
                DeductionInfoParamBo paramBo = new DeductionInfoParamBo();
                //获取城市仓信息
                RemoteCityWhVo cityWhVo = remoteCityWhService.queryById(blameRecord.getCityWhId());
                paramBo.setCommonId(blameRecord.getCityWhId());
                paramBo.setCommonName(cityWhVo.getName());
                paramBo.setType(DeductionInfoEnum.CITY.getCode());
                paramBo.setAmountType(AmountTypeEnum.LOSS.getCode());
                paramBo.setBillType(BillTypeEnum.STOCKOUT_RECORD.getCode());
                paramBo.setBillCode(l.getSourceCode());
                paramBo.setDeductionType(DeductionTypeEnum.KKYY_SHPZ.getCode());
                createDedyctionInfo(blameRecord.getId(), blameRecord, l, paramBo);
                //商品金额:供应商加款
                if (l.getAmountType().equals(10)){
                    supCreateDedyctionInfo(blameRecord.getId(), blameRecord, l, AmountTypeEnum.ADD.getCode(),DeductionTypeEnum.JKYY_SHPZ.getCode());
                    //优惠金额不为0,总仓加款
                    if (blameRecord.getSubsidyFreeAmount().compareTo(BigDecimal.ZERO) > 0){
                        DeductionInfoParamBo ipbo = new DeductionInfoParamBo();
                        ipbo.setType(DeductionInfoEnum.REGION.getCode());
                        ipbo.setAmountType(AmountTypeEnum.ADD.getCode());
                        ipbo.setBillType(BillTypeEnum.STOCKOUT_RECORD.getCode());
                        ipbo.setBillCode(l.getSourceCode());
                        ipbo.setDeductionType(DeductionTypeEnum.JKYY_PRO_SUB.getCode());
                        createDedyctionInfo(blameRecord.getId(), blameRecord, l, ipbo);
                        //城市仓扣款
                        DeductionInfoParamBo cpbo = new DeductionInfoParamBo();
                        cpbo.setCommonId(blameRecord.getCityWhId());
                        cpbo.setCommonName(cityWhVo.getName());
                        cpbo.setType(DeductionInfoEnum.CITY.getCode());
                        cpbo.setAmountType(AmountTypeEnum.LOSS.getCode());
                        cpbo.setBillType(BillTypeEnum.STOCKOUT_RECORD.getCode());
                        cpbo.setBillCode(l.getSourceCode());
                        cpbo.setDeductionType(DeductionTypeEnum.KKYY_PRO_SUB.getCode());
                        createDedyctionInfo(blameRecord.getId(), blameRecord, l, cpbo);
                    }
                }
            }
            //其他的不做新增扣款单处理  直接修改判责单状态
            l.setBlameStatus(BlameDetailStatusEnum.HAS_CONFIRM.getCode());
            if (ObjectUtil.isNotNull(LoginHelper.getLoginUser())) {
                l.setConfirmId(LoginHelper.getLoginUser().getUserId());
                l.setConfirmName(LoginHelper.getLoginUser().getRealName());
            } else {
                l.setConfirmId(0L);
                l.setConfirmName("系统自动确认");
            }
            l.setConfirmTime(new Date());
            if (l.getResponsibilityType().equals(ResponsibilityAllTypeEnum.REGION_WH.getCode())
                    && Objects.equals(BlameSourceTypeEnum.LESS_GOODS.getCode(), l.getSourceType())
                    && (blameRecord.getBusinessType().equals(SupplierSkuBusinessTypeEnum.BUSINESS_TYPE30.getCode())
                    || blameRecord.getBusinessType().equals(SupplierSkuBusinessTypeEnum.BUSINESS_TYPE20.getCode()))) {
                if (l.getAmountType().equals(10)){
                    amountTotal.set(amountTotal.get().add(l.getAmount()));
                }
                sumSHCount.updateAndGet(v -> v + l.getBlameCount());
            }
        });
        blameRecordDetailMapper.updateBatchById(detailList);
        if (sumSHCount.get() != 0){
            Long supplierSkuId = blameRecord.getSupplierSkuId();

            RemoteQueryInfoListBo listBo = new RemoteQueryInfoListBo();
            List<RemoteSupplierSkuInfoVo> remoteSupplierSkuInfoVos = remoteSupplierSkuService
                    .queryInfoList(listBo.setSupplierSkuIdList(Lists.newArrayList(supplierSkuId)));
            RemoteSupplierSkuInfoVo skuInfoVo = remoteSupplierSkuInfoVos.get(0);
            List<Integer> types = CollectionUtil.newArrayList(SupplierSkuBusinessTypeEnum.BUSINESS_TYPE30.getCode()
                    , SupplierSkuBusinessTypeEnum.BUSINESS_TYPE20.getCode());
            if (types.contains(skuInfoVo.getBusinessType())){
                RemoteCwTransHeadDTO  cwBo = new RemoteCwTransHeadDTO();
                cwBo.setRegionWhId(skuInfoVo.getRegionWhId());
                cwBo.setIoFlag(TransTypeIoFlagEnum.STATUS0.getCode());
                cwBo.setTransDate(new Date());
                cwBo.setTransCode(TransTypeCodeEnum.SHRK.getCode());
                cwBo.setRemark(String.format("少货单：%s", blameRecord.getSourceCode()));
                cwBo.setSourceType(TransSourceTypeEnum.STATUS50.getCode().longValue());
                cwBo.setSourceId(blameRecord.getSourceCode());

                RemoteCwTransDetailDTO sku = new RemoteCwTransDetailDTO();
                BeanUtils.copyProperties(cwBo, sku);
                sku.setSourceDetailId(blameRecord.getSourceCode());
                sku.setSaleDate(blameRecord.getSaleDate());
                sku.setSkuId(skuInfoVo.getSkuId());
                sku.setIoQty(BigDecimal.valueOf(sumSHCount.get()));
                sku.setIoAmt(amountTotal.get());
                sku.setIoPrice(amountTotal.get().divide(BigDecimal.valueOf(sumSHCount.get()), 2, BigDecimal.ROUND_HALF_UP));
                cwBo.setCwTransDetailDTOList(CollectionUtil.newArrayList(sku));
                remoteCwStockService.insertByBo(cwBo);
            }
        }
    }

    /**
     * 获取判责单各状态数量
     */
    @Override
    public List<CommonStatusCountVO> getBlameCount(Integer type, Long supplierDeptId) {
        CommonStatusCountVO commonStatusCountVO = new CommonStatusCountVO();
        //城市仓和供应商显示 待确认
        if (type.equals(AccountTypeStatusEnum.CITY.getCode()) || type.equals(AccountTypeStatusEnum.SUPPLIER.getCode())){
            LambdaQueryWrapper<BlameRecordDetail> qw = Wrappers.lambdaQuery();
            qw.select(BlameRecordDetail::getBlameRecordId).eq(BlameRecordDetail::getDelFlag, 0);
            if (ObjectUtil.isNotNull(type)){
                if (type.equals(AccountTypeStatusEnum.CITY.getCode())){
                    qw.eq(BlameRecordDetail::getResponsibilityType, 2);
                }else if (type.equals(AccountTypeStatusEnum.SUPPLIER.getCode())){
                    qw.eq(BlameRecordDetail::getResponsibilityType, 1);
                }
            }
            qw.groupBy(BlameRecordDetail::getBlameRecordId);
            //过滤其他档口的数据

            List<BlameRecordDetail> details = blameRecordDetailMapper.selectList(qw);
            List<Long> blameRecordIdList = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(details)) {
                List<Long> blameRecordIds = details.stream().map(BlameRecordDetail::getBlameRecordId).distinct().toList();
                LambdaQueryWrapper<BlameRecord> wrapper = new LambdaQueryWrapper<>();
                if (ObjectUtil.isNotEmpty(supplierDeptId)) {
                    wrapper.eq(BlameRecord::getSupplierDeptId, supplierDeptId);
                }
                wrapper.in(BlameRecord::getId, blameRecordIds);
                List<BlameRecord> blameRecords = baseMapper.selectList(wrapper);
                blameRecordIdList = blameRecords.stream().map(BlameRecord::getId).collect(Collectors.toList());
            }
            log.keyword("判责单档口筛选", "getBlameCount").info("blameRecordIdList:{},supplierDeptId:{}", blameRecordIdList, supplierDeptId);
            List<Long> finalBlameRecordIdList = blameRecordIdList;
            details = details.stream().filter(tmp -> !finalBlameRecordIdList.contains(tmp.getBlameRecordId())).collect(Collectors.toList());

            commonStatusCountVO.setCount(CollectionUtil.isNotEmpty(details)?details.size():0L);
            commonStatusCountVO.setStatusName(BlameDetailStatusEnum.STATE_CONFIRM.getDesc());
            commonStatusCountVO.setValue(Long.valueOf(BlameDetailStatusEnum.STATE_CONFIRM.getCode()));
        }
        //总仓显示 待判责
        else{
            LambdaQueryWrapper<BlameRecord> qw = Wrappers.lambdaQuery();
            qw.eq(BlameRecord::getRegionWhId, LoginHelper.getLoginUser().getRelationId())
                    .eq(BlameRecord::getBlameStatus, BlameStatusEnum.STATE_BLAME.getCode())
                    .eq(BlameRecord::getDelFlag, 0);
            if (ObjectUtil.isNotEmpty(supplierDeptId)) {
                qw.eq(BlameRecord::getSupplierDeptId, supplierDeptId);
            }
            commonStatusCountVO.setCount(baseMapper.selectCount(qw));
            commonStatusCountVO.setStatusName(BlameStatusEnum.STATE_BLAME.getDesc());
            commonStatusCountVO.setValue(Long.valueOf(BlameStatusEnum.STATE_BLAME.getCode()));
        }
        return Collections.singletonList(commonStatusCountVO);
    }

    /**
     * 生成判责单
     * @param bo
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createBlameRecord(CreateBlameRecordBO bo){
        BlameRecord blameRecord = new BlameRecord();
        //根据类型处理
        switch (bo.getSourceType()) {
            case STOCKOUT,LESS_GOODS:
                //缺货产生判责单
                StockoutRecord stockoutRecord = stockoutRecordMapper.selectById(bo.getSourceId());
                getBlameRecordByStockoutInfo(stockoutRecord, bo, blameRecord);
                break;
            case MORE_GOODS:
                //处理多货产生判责单
                MoreGoodsRecord moreGoodsRecord = moreGoodsRecordMapper.selectById(bo.getSourceId());
                getBlameRecordByMoreGoodsInfo(moreGoodsRecord, bo, blameRecord);
                break;
            case LOSS_ORDER:
                ReportLossOrder lossOrder = reportLossOrderMapper.selectById(bo.getSourceId());
                getBlameRecordByLossOrderInfo(lossOrder, bo, blameRecord);
                break;
            case STOCK_CONFIRM:
                break;
            default:
                throw new ServiceException("判责类型错误");
        }
        //新增文件
        createFile(bo.getFileList(), blameRecord);
    }

    private void getBlameRecordByLossOrderInfo(ReportLossOrder lossOrder, CreateBlameRecordBO bo, BlameRecord blameRecord) {
        //获取销售批次商品信息
        RemoteSupplierSkuInfoVo skuInfoVo = remoteSupplierSkuService.getSimpleById(lossOrder.getSupplierSkuId());
        RemoteUserBo userBo = remoteUserService.getUserByUserCode(skuInfoVo.getBuyerCode());
        //获取物流信息
        RemoteRegionLogisticsQueryBo queryBo = new RemoteRegionLogisticsQueryBo();
        queryBo.setCityWhId(lossOrder.getCityWhId());
        List<RemoteRegionLogisticsVo> logisticsVos = remoteRegionLogisticsService.queryList(queryBo);
        RemoteRegionLogisticsVo logisticsVo = logisticsVos.get(0);
        blameRecord.setCode(getBlameCode());
        blameRecord.setSourceCode(lossOrder.getReportLossNo());
        blameRecord.setCityWhId(lossOrder.getCityWhId());
        blameRecord.setSupplierId(lossOrder.getSupplierId());
        blameRecord.setRegionWhId(lossOrder.getRegionWhId());
        blameRecord.setLogisticsId(logisticsVo.getId());
        blameRecord.setLogisticsCode(logisticsVo.getLogisticsCode());
        blameRecord.setLogisticsName(logisticsVo.getLogisticsName());
        blameRecord.setSupplierSkuCode(skuInfoVo.getCode());
        blameRecord.setSaleDate(skuInfoVo.getSaleDate());
        blameRecord.setSpuName(lossOrder.getSpuName());
        blameRecord.setProductAmount(bo.getProductAmount());
        blameRecord.setOtherAmount(bo.getOtherAmount());
        blameRecord.setBlameAmount(bo.getBlameAmount());
        blameRecord.setTotalCount(bo.getTotalCount());
        blameRecord.setSupplierDeptId(skuInfoVo.getSupplierDeptId());
        blameRecord.setBuyerId(userBo.getUserId());
        blameRecord.setBuyerName(skuInfoVo.getBuyerName());
        blameRecord.setSourceType(BlameSourceTypeEnum.STOCKOUT.getCode());
        blameRecord.setOrderType(skuInfoVo.getBatchType());
        blameRecord.setBlameStatus(BlameStatusEnum.STATE_BLAME.getCode());
        blameRecord.setCreateRemark(lossOrder.getRemark());
        baseMapper.insert(blameRecord);
        //创建新判责单明细
        List<BlameRecordDetail> newDetailList = new ArrayList<>();
        for (BlameDetailBO detail : bo.getBlameRecordDetailVOList()) {
            BlameRecordDetail blameRecordDetail = new BlameRecordDetail();
            BeanUtil.copyProperties(detail, blameRecordDetail);
            blameRecordDetail.setBlameRecordId(blameRecord.getId());
            blameRecordDetail.setSourceType(blameRecord.getSourceType());
            blameRecordDetail.setSourceCode(blameRecord.getSourceCode());
            blameRecordDetail.setBlameExplain(bo.getBlameExplain());
            blameRecordDetail.setBlameStatus(BlameDetailStatusEnum.STATE_CONFIRM.getCode());
            blameRecordDetail.setCreateRemark(lossOrder.getRemark());
            newDetailList.add(blameRecordDetail);
        }
        blameRecordDetailMapper.insertBatch(newDetailList);
    }

    private void getBlameRecordByMoreGoodsInfo(MoreGoodsRecord moreGoodsRecord, CreateBlameRecordBO bo, BlameRecord blameRecord) {
        //获取销售批次商品信息
        RemoteSupplierSkuInfoVo skuInfoVo = remoteSupplierSkuService.getSimpleById(moreGoodsRecord.getSupplierSkuId());
        RemoteUserBo userBo = remoteUserService.getUserByUserCode(skuInfoVo.getBuyerCode());
        //获取物流信息
        RemoteRegionLogisticsQueryBo queryBo = new RemoteRegionLogisticsQueryBo();
        queryBo.setCityWhId(moreGoodsRecord.getCityWhId());
        List<RemoteRegionLogisticsVo> logisticsVos = remoteRegionLogisticsService.queryList(queryBo);
        RemoteRegionLogisticsVo logisticsVo = logisticsVos.get(0);
        blameRecord.setCode(getBlameCode());
        blameRecord.setSourceCode(moreGoodsRecord.getCode());
        blameRecord.setSupplierSkuId(moreGoodsRecord.getSupplierSkuId());
        blameRecord.setCityWhId(moreGoodsRecord.getCityWhId());
        blameRecord.setSupplierId(moreGoodsRecord.getSupplierId());
        blameRecord.setRegionWhId(moreGoodsRecord.getRegionWhId());
        blameRecord.setLogisticsId(logisticsVo.getId());
        blameRecord.setLogisticsCode(logisticsVo.getLogisticsCode());
        blameRecord.setLogisticsName(logisticsVo.getLogisticsName());
        blameRecord.setSupplierSkuCode(moreGoodsRecord.getSupplierSkuCode());
        blameRecord.setSupplierDeptId(skuInfoVo.getSupplierDeptId());
        blameRecord.setSaleDate(skuInfoVo.getSaleDate());
        blameRecord.setSpuName(moreGoodsRecord.getSpuName());
        blameRecord.setProductAmount(bo.getProductAmount());
        blameRecord.setOtherAmount(bo.getOtherAmount());
        blameRecord.setBlameAmount(bo.getBlameAmount());
        blameRecord.setTotalCount(bo.getTotalCount());
        blameRecord.setBuyerId(userBo.getUserId());
        blameRecord.setBuyerName(skuInfoVo.getBuyerName());
        blameRecord.setSourceType(BlameSourceTypeEnum.MORE_GOODS.getCode());
        blameRecord.setOrderType(skuInfoVo.getBatchType());
        blameRecord.setBlameStatus(BlameStatusEnum.STATE_BLAME.getCode());
        blameRecord.setCreateRemark(moreGoodsRecord.getRemark());
        baseMapper.insert(blameRecord);
    }

    /**
     * 确认判责
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void confirm(Long id, Integer resType, Boolean log) {
        //查看详情
        BlameRecord blameRecord = baseMapper.selectById(id);
        List<BlameRecordDetail> details = blameRecordDetailMapper.selectList(Wrappers.lambdaQuery(BlameRecordDetail.class)
                .eq(BlameRecordDetail::getBlameRecordId, id).eq(BlameRecordDetail::getDelFlag, 0));
        //判断是否确认,确认过了就过了,避免重复判责产生扣款单
        if (blameRecord.getBlameStatus().equals(BlameStatusEnum.ALL_CONFIRM.getCode())) {
            throw new ServiceException("判责单已确认,请勿重复提交");
        }
        if (details.stream().allMatch(l -> l.getBlameStatus().equals(BlameDetailStatusEnum.HAS_CONFIRM.getCode()))) {
            throw new ServiceException("判责单已确认,请勿重复提交");
        }
        OrderLog orderLog = new OrderLog();
        //少货确定总仓判责数量
        AtomicReference<Integer> sumSHCount = new AtomicReference<>(0);
        AtomicReference<BigDecimal> amountTotal = new AtomicReference<>(BigDecimal.ZERO);
        //判断产生扣款单(区分商品非商品金额)
        details.forEach(l -> {
            //承担方是供应商 只有非商品金额承担才产生扣款单  供应商->总仓
            if (l.getResponsibilityType().equals(1) && l.getAmountType().equals(20) && resType.equals(AccountTypeStatusEnum.SUPPLIER.getCode())) {
                //供应商扣款
                supCreateDedyctionInfo(id, blameRecord, l,AmountTypeEnum.LOSS.getCode(),DeductionTypeEnum.KKYY_SHPZ.getCode());
            }
            //承担方是供应商 承担商品金额  且商品优惠补贴金额>0, 供应商->总仓
            else if (l.getResponsibilityType().equals(1) && l.getAmountType().equals(10)
                    && blameRecord.getSubsidyFreeAmount().compareTo(BigDecimal.ZERO) > 0 && resType.equals(AccountTypeStatusEnum.SUPPLIER.getCode())) {
                //除非是库房货，不会扣款
//                if(!(blameRecord.getBusinessType().equals(OrderBusinessTypeEnum.BUSINESS_TYPE20.getCode()) ||
//                        blameRecord.getBusinessType().equals(OrderBusinessTypeEnum.BUSINESS_TYPE30.getCode()))){
//                    //供应商扣款
//                    supCreateDedyctionInfo(id, blameRecord, l,AmountTypeEnum.LOSS.getCode(),DeductionTypeEnum.KKYY_PRO_SUB.getCode());
//                }
            }
            //商品金额且承担方是总仓 总仓->供应商
            else if (l.getResponsibilityType().equals(3) && l.getAmountType().equals(10) && resType.equals(AccountTypeStatusEnum.REGION.getCode())){
                //除非是库房货，不会扣款
                if(
//                        !(blameRecord.getBusinessType().equals(OrderBusinessTypeEnum.BUSINESS_TYPE20.getCode()) ||
//                        blameRecord.getBusinessType().equals(OrderBusinessTypeEnum.BUSINESS_TYPE30.getCode()))
//                &&
                    blameRecord.getSubsidyFreeAmount().compareTo(BigDecimal.ZERO)>0){
                    //供应商加款
                    supCreateDedyctionInfo(id, blameRecord, l,AmountTypeEnum.ADD.getCode(),DeductionTypeEnum.JKYY_SHPZ.getCode());
                }
            }
            //城市仓
            else if (l.getResponsibilityType().equals(2) && resType.equals(AccountTypeStatusEnum.CITY.getCode())){
                //城市仓扣款
                DeductionInfoParamBo bo = new DeductionInfoParamBo();
                //获取城市仓信息
                RemoteCityWhVo cityWhVo = remoteCityWhService.queryById(blameRecord.getCityWhId());
                bo.setCommonId(blameRecord.getCityWhId());
                bo.setCommonName(cityWhVo.getName());
                bo.setType(DeductionInfoEnum.CITY.getCode());
                bo.setAmountType(AmountTypeEnum.LOSS.getCode());
                bo.setBillType(BillTypeEnum.STOCKOUT_RECORD.getCode());
                bo.setBillCode(l.getSourceCode());
                bo.setDeductionType(DeductionTypeEnum.KKYY_SHPZ.getCode());
                createDedyctionInfo(id, blameRecord, l, bo);
                //商品金额:供应商加款
                if (l.getAmountType().equals(10)){
                    supCreateDedyctionInfo(id, blameRecord, l, AmountTypeEnum.ADD.getCode(),DeductionTypeEnum.JKYY_SHPZ.getCode());
                    //优惠金额不为0,供应商加款，城市仓扣款
                    if (blameRecord.getSubsidyFreeAmount().compareTo(BigDecimal.ZERO) > 0){
                        RemoteSupplierVo supplierVo = remoteSupplierService.getSupplierById(blameRecord.getSupplierId());
                        DeductionInfoParamBo ipbo = new DeductionInfoParamBo();
                        ipbo.setType(DeductionInfoEnum.SUPPLIER.getCode());
                        ipbo.setCommonId(blameRecord.getSupplierId());
                        ipbo.setCommonName(supplierVo.getName());
                        ipbo.setAmountType(AmountTypeEnum.ADD.getCode());
                        ipbo.setBillType(BillTypeEnum.STOCKOUT_RECORD.getCode());
                        ipbo.setBillCode(l.getSourceCode());
                        ipbo.setDeductionType(DeductionTypeEnum.KKYY_PRO_SUB.getCode());
                        createDedyctionInfo(id, blameRecord, l, ipbo);
                        //城市仓扣款
                        DeductionInfoParamBo cpbo = new DeductionInfoParamBo();
                        cpbo.setCommonId(blameRecord.getCityWhId());
                        cpbo.setCommonName(cityWhVo.getName());
                        cpbo.setType(DeductionInfoEnum.CITY.getCode());
                        cpbo.setAmountType(AmountTypeEnum.LOSS.getCode());
                        cpbo.setBillType(BillTypeEnum.STOCKOUT_RECORD.getCode());
                        cpbo.setBillCode(l.getSourceCode());
                        cpbo.setDeductionType(DeductionTypeEnum.KKYY_PRO_SUB.getCode());
                        createDedyctionInfo(id, blameRecord, l, cpbo);
                    }
                }
                //非商品金额:总仓加款
                else if (l.getAmountType().equals(20)){
                    DeductionInfoParamBo pbo = new DeductionInfoParamBo();
                    pbo.setType(DeductionInfoEnum.REGION.getCode());
                    pbo.setAmountType(AmountTypeEnum.ADD.getCode());
                    pbo.setBillType(BillTypeEnum.STOCKOUT_RECORD.getCode());
                    pbo.setBillCode(l.getSourceCode());
                    pbo.setDeductionType(DeductionTypeEnum.JKYY_SHPZ.getCode());
                    createDedyctionInfo(id, blameRecord, l, pbo);
                }
            }
            //能操作的进来才改状态,不然把别的判责单给改了
            if ((l.getResponsibilityType().equals(3) && resType.equals(AccountTypeStatusEnum.REGION.getCode()))
                    || (l.getResponsibilityType().equals(1) && resType.equals(AccountTypeStatusEnum.SUPPLIER.getCode()))
                    || (l.getResponsibilityType().equals(2) && resType.equals(AccountTypeStatusEnum.CITY.getCode()))) {
                //其他的不做新增扣款单处理  直接修改判责单状态
                l.setBlameStatus(BlameDetailStatusEnum.HAS_CONFIRM.getCode());
                if (ObjectUtil.isNotNull(LoginHelper.getLoginUser())) {
                    l.setConfirmId(LoginHelper.getLoginUser().getUserId());
                    l.setConfirmName(LoginHelper.getLoginUser().getRealName());
                } else {
                    l.setConfirmId(0L);
                    l.setConfirmName("系统自动确认");
                }
                l.setConfirmTime(new Date());
            }
            String realName = ObjectUtil.isNull(LoginHelper.getRealName()) ? "系统自动确认" : LoginHelper.getRealName();
            if (l.getResponsibilityType().equals(3) && resType.equals(AccountTypeStatusEnum.REGION.getCode())) {
                //查总仓信息
                RemoteRegionWhVo remoteRegionWhVo = remoteRegionWhService.queryById(blameRecord.getRegionWhId());
                orderLog.setOperate("总仓确认判责");
                orderLog.setRemark(remoteRegionWhVo.getRegionWhName() + ":" + realName);
            }
            if (l.getResponsibilityType().equals(3) && resType.equals(AccountTypeStatusEnum.REGION.getCode())
                    && Objects.equals(BlameSourceTypeEnum.LESS_GOODS.getCode(),l.getSourceType())){
                if (l.getAmountType().equals(10)){
                    amountTotal.set(amountTotal.get().add(l.getAmount()));
                }
                sumSHCount.updateAndGet(v -> v + l.getBlameCount());
            }
            if (l.getResponsibilityType().equals(1) && resType.equals(AccountTypeStatusEnum.SUPPLIER.getCode())) {
                //获取供应商信息
                RemoteSupplierVo supplierVo = remoteSupplierService.getSupplierById(blameRecord.getSupplierId());
                orderLog.setOperate("供应商确认判责");
                orderLog.setRemark(supplierVo.getName() + ":" + realName);
            }
            if (l.getResponsibilityType().equals(2) && resType.equals(AccountTypeStatusEnum.CITY.getCode())) {
                RemoteCityWhVo remoteCityWhVo = remoteCityWhService.queryById(blameRecord.getCityWhId());
                orderLog.setOperate("城市仓确认判责");
                orderLog.setRemark(remoteCityWhVo.getName() + ":" + realName);
            }
        });
        blameRecordDetailMapper.updateBatchById(details);
        //新增操作日志
        orderLog.setSourceId(blameRecord.getId());
        orderLog.setSourceType(OrderLogSourceTypeEnum.BLAME.getCode());
        if (log) {
            orderLogMapper.insert(orderLog);
        }
        //更新来源单据 全部确认
        LambdaQueryWrapper<StockoutRecord> qws = Wrappers.lambdaQuery(StockoutRecord.class);
        qws.select(StockoutRecord::getId).eq(StockoutRecord::getCode, blameRecord.getSourceCode()).eq(StockoutRecord::getDelFlag, 0);
        List<StockoutRecord> stockoutRecords = stockoutRecordMapper.selectList(qws);
        if (CollectionUtil.isNotEmpty(stockoutRecords)){
            stockoutRecords.forEach(l->l.setBlameStatus(BlameStatusEnum.FINISH.getCode()));
        }
        //都确认了改判责单状态
        if (details.stream().allMatch(l -> l.getBlameStatus().equals(BlameDetailStatusEnum.HAS_CONFIRM.getCode()))){
            blameRecord.setBlameStatus(BlameStatusEnum.FINISH.getCode());
            baseMapper.updateById(blameRecord);
            //更新来源单据 全部确认
            if (CollectionUtil.isNotEmpty(stockoutRecords)){
                stockoutRecords.forEach(l->l.setBlameStatus(BlameStatusEnum.FINISH.getCode()));
            }
        }else {
            blameRecord.setBlameStatus(BlameStatusEnum.PART_CONFIRM.getCode());
            baseMapper.updateById(blameRecord);
        }
        if (CollectionUtil.isNotEmpty(stockoutRecords)) {
            stockoutRecordMapper.updateBatchById(stockoutRecords);
        }
        if (sumSHCount.get() != 0){
            Long supplierSkuId = blameRecord.getSupplierSkuId();

            RemoteQueryInfoListBo listBo = new RemoteQueryInfoListBo();
            List<RemoteSupplierSkuInfoVo> remoteSupplierSkuInfoVos = remoteSupplierSkuService
                    .queryInfoList(listBo.setSupplierSkuIdList(Lists.newArrayList(supplierSkuId)));
            RemoteSupplierSkuInfoVo skuInfoVo = remoteSupplierSkuInfoVos.get(0);
            List<Integer> types = CollectionUtil.newArrayList(SupplierSkuBusinessTypeEnum.BUSINESS_TYPE30.getCode()
                    , SupplierSkuBusinessTypeEnum.BUSINESS_TYPE20.getCode());
            if (types.contains(skuInfoVo.getBusinessType())){
                RemoteCwTransHeadDTO  cwBo = new RemoteCwTransHeadDTO();
                cwBo.setRegionWhId(skuInfoVo.getRegionWhId());
                cwBo.setIoFlag(TransTypeIoFlagEnum.STATUS0.getCode());
                cwBo.setTransDate(new Date());
                cwBo.setTransCode(TransTypeCodeEnum.SHRK.getCode());
                cwBo.setRemark(String.format("少货单：%s",blameRecord.getSourceCode()));
                cwBo.setSourceType(TransSourceTypeEnum.STATUS50.getCode().longValue());
                cwBo.setSourceId(blameRecord.getSourceCode());

                RemoteCwTransDetailDTO sku = new RemoteCwTransDetailDTO();
                BeanUtils.copyProperties(cwBo, sku);
                sku.setSourceDetailId(blameRecord.getSourceCode());
                sku.setSaleDate(blameRecord.getSaleDate());
                sku.setSkuId(skuInfoVo.getSkuId());
                sku.setIoQty(BigDecimal.valueOf(sumSHCount.get()));
                sku.setIoAmt(amountTotal.get());
                sku.setIoPrice(amountTotal.get().divide(BigDecimal.valueOf(sumSHCount.get()), 2, BigDecimal.ROUND_HALF_UP));
                cwBo.setCwTransDetailDTOList(CollectionUtil.newArrayList(sku));
                remoteCwStockService.insertByBo(cwBo);
            }

        }
    }

    /**
     * 供应商加扣款
     */
    private void supCreateDedyctionInfo(Long id, BlameRecord blameRecord, BlameRecordDetail l, Integer amountType,Integer type) {
        DeductionInfoParamBo pbo = new DeductionInfoParamBo();
        //获取供应商信息
        RemoteSupplierVo supplierVo = remoteSupplierService.getSupplierById(blameRecord.getSupplierId());
        pbo.setCommonId(blameRecord.getSupplierId());
        pbo.setCommonName(supplierVo.getName());
        pbo.setType(DeductionInfoEnum.SUPPLIER.getCode());
        pbo.setAmountType(amountType);
        pbo.setBillType(BillTypeEnum.STOCKOUT_RECORD.getCode());
        pbo.setBillCode(l.getSourceCode());
        pbo.setDeductionType(type);
        createDedyctionInfo(id, blameRecord, l, pbo);
    }

    /**
     * 判责分页列表  管理台
     */
    @Override
    public TableDataInfo<BlameRecordAdminVO> blameAdminPage(BlameRecordPageBO bo) {
        //先根据条件查询主表,后续再补充子表信息,因为有些条件会筛选掉一些子表信息
        Page<BlameRecordAdminVO> page = baseMapper.blameAdminPage(bo, bo.build());
        if (CollectionUtil.isNotEmpty(page.getRecords())) {
            List<Long> cityWhIds = page.getRecords().stream().map(BlameRecordAdminVO::getCityWhId).distinct().toList();
            List<Long> supplierIds = page.getRecords().stream().map(BlameRecordAdminVO::getSupplierId).distinct().toList();
            //城市仓名称
            List<RemoteCityWhVo> remoteCityWhVos = remoteCityWhService.queryList(cityWhIds);
            Map<Long, String> cityMap = remoteCityWhVos.stream().collect(Collectors.toMap(RemoteCityWhVo::getId, RemoteCityWhVo::getName));
            //总仓名称
            List<RemoteRegionWhVo> remoteRegionWhVos = remoteRegionWhService.queryList();
            Map<Long, String> regionMap = remoteRegionWhVos.stream().collect(Collectors.toMap(RemoteRegionWhVo::getId, RemoteRegionWhVo::getRegionWhName));
            //供应商名称
            List<RemoteSupplierVo> supplierByIds = remoteSupplierService.getSupplierByIds(supplierIds);
            Map<Long, RemoteSupplierVo> supplierMap = supplierByIds.stream().collect(Collectors.toMap(RemoteSupplierVo::getId, Function.identity()));
            //获取子表信息
            LambdaQueryWrapper<BlameRecordDetail> qw = Wrappers.lambdaQuery();
            List<Long> recordIds = page.getRecords().stream().map(BlameRecordAdminVO::getBlameRecordId).toList();
            qw.in(BlameRecordDetail::getBlameRecordId, recordIds).eq(BlameRecordDetail::getDelFlag, 0);
            List<BlameRecordDetail> details = blameRecordDetailMapper.selectList(qw);
            Map<Long, List<BlameRecordDetail>> map = details.stream().collect(Collectors.groupingBy(BlameRecordDetail::getBlameRecordId));
            page.getRecords().forEach(item -> {
                if (CollectionUtil.isNotEmpty(map.get(item.getBlameRecordId()))){
                    //后面还有其他金额类型,现在这样写
                    map.get(item.getBlameRecordId()).forEach(detail -> {
                        //商品金额
                        if (detail.getAmountType().equals(10)){
                            item.setProResId(detail.getResponsibilityId());
                            if (detail.getResponsibilityType().equals(0)){
                                item.setProResName("无承担方");
                            }else {
                                item.setProResName(detail.getResponsibilityName());
                            }
                            item.setProDeptId(detail.getDeptId());
                            item.setProDeptName(detail.getDeptName());
                            item.setProBlameStatus(detail.getBlameStatus());
                            item.setProConfirmTime(detail.getConfirmTime());
                            item.setProductAmount(detail.getAmount());
                            item.setProConfirmName(detail.getConfirmName());
                        }
                        //非商品金额类型赋值
                        else if(detail.getAmountType().equals(20)){
                            item.setOthResId(detail.getResponsibilityId());
                            if (detail.getResponsibilityType().equals(0)){
                                item.setOthResName("无承担方");
                            }else {
                                item.setOthResName(detail.getResponsibilityName());
                            }
                            item.setOthDeptId(detail.getDeptId());
                            item.setOthDeptName(detail.getDeptName());
                            item.setOthBlameStatus(detail.getBlameStatus());
                            item.setOthConfirmTime(detail.getConfirmTime());
                            item.setOtherAmount(detail.getAmount());
                            item.setServiceAmount(detail.getServiceAmount());
                            item.setPlatformFreight(detail.getPlatformFreight());
                            item.setBaseFreight(detail.getBaseFreight());
                            item.setRegionFreight(detail.getRegionFreight());
                            item.setFinancialServicePrice(detail.getFinancialServicePrice());
                            item.setOthConfirmName(detail.getConfirmName());
                        }
                        item.setBlameExplain(detail.getBlameExplain());
                    });
                }
                item.setCityWhName(cityMap.get(item.getCityWhId()));
                item.setRegionWhName(regionMap.get(item.getRegionWhId()));
                item.setSupplierName(supplierMap.getOrDefault(item.getSupplierId(), new RemoteSupplierVo()).getName());
                item.setSupplierAlias(supplierMap.getOrDefault(item.getSupplierId(), new RemoteSupplierVo()).getAlias());
            });
        }
        return TableDataInfo.build(page);
    }

    /**
     * 自动确认超时未处理判责
     */
    @Lock4j(keys = "#bo.id")
    @Override
    public Integer autoConfirm(BlameConfirmMessageBO bo) {
        //查看详情
        BlameRecord blameRecord = baseMapper.selectById(bo.getId());
        if (blameRecord.getBlameStatus().equals(BlameStatusEnum.FINISH.getCode())
                || blameRecord.getBlameStatus().equals(BlameStatusEnum.APPEAL.getCode())
                || blameRecord.getBlameStatus().equals(BlameStatusEnum.INVALID.getCode())) {
            return 0;
        }
        List<BlameRecordDetail> details = blameRecordDetailMapper.selectList(Wrappers.lambdaQuery(BlameRecordDetail.class)
                .eq(BlameRecordDetail::getBlameStatus, BlameDetailStatusEnum.STATE_CONFIRM.getCode())
                .eq(BlameRecordDetail::getBlameRecordId, bo.getId()).eq(BlameRecordDetail::getDelFlag, 0));
        //为空自动判责到供应商
        if (CollectionUtil.isEmpty(details)) {
            BlameRecordDetail detail = new BlameRecordDetail();
            detail.setBlameRecordId(blameRecord.getId());
            detail.setSourceCode(blameRecord.getCode());
            detail.setSourceType(blameRecord.getSourceType());
            detail.setResponsibilityType(ResponsibilityTypeEnum.SUPPLIER.getCode());
            //获取供应商信息
            RemoteSupplierVo supplierVo = remoteSupplierService.getSupplierById(blameRecord.getSupplierId());
            detail.setResponsibilityId(blameRecord.getSupplierId());
            detail.setResponsibilityName(supplierVo.getName());
            detail.setResponsibilityCode(supplierVo.getCode());
            detail.setAmountType(10);
            detail.setAmount(blameRecord.getProductAmount());
            detail.setResponsibilityExplain("超时未处理自动确认");
            detail.setBlameExplain("超时未处理自动确认");
            detail.setBlameCount(blameRecord.getTotalCount());
            detail.setCreateRemark("超时未处理自动确认");
            detail.setConfirmId(0L);
            detail.setConfirmName("系统自动确认");
            detail.setConfirmTime(new Date());
            blameRecordDetailMapper.insert(detail);
            details.add(detail);
        }
        if (CollectionUtil.isNotEmpty(details)) {
            //判断是否确认,确认过了就过了,避免重复判责产生扣款单
            if (blameRecord.getBlameStatus().equals(BlameStatusEnum.ALL_CONFIRM.getCode())) {
                return 0;
            }
            if (details.stream().allMatch(l -> l.getBlameStatus().equals(BlameDetailStatusEnum.HAS_CONFIRM.getCode()))) {
                return 0;
            }
            OrderLog orderLog = new OrderLog();
            //判断产生扣款单(区分商品非商品金额)
            details.forEach(l -> {
                //承担方是供应商 只有非商品金额承担才产生扣款单  供应商->总仓
                if (l.getResponsibilityType().equals(1) && l.getAmountType().equals(20)) {
                    //供应商扣款
                    supCreateDedyctionInfo(bo.getId(), blameRecord, l, AmountTypeEnum.LOSS.getCode(),DeductionTypeEnum.KKYY_SHPZ.getCode());
                }
                //承担方是供应商 承担商品金额  且商品优惠补贴金额>0, 供应商->总仓
                else if (l.getResponsibilityType().equals(1) && l.getAmountType().equals(10) && blameRecord.getSubsidyFreeAmount().compareTo(BigDecimal.ZERO) > 0) {
                    //供应商扣款
                    supCreateDedyctionInfo(bo.getId(), blameRecord, l,AmountTypeEnum.LOSS.getCode(),DeductionTypeEnum.KKYY_PRO_SUB.getCode());
                }
                //商品金额且承担方是总仓 总仓->供应商
                else if (l.getResponsibilityType().equals(3) && l.getAmountType().equals(10)) {
                    //供应商加款
                    supCreateDedyctionInfo(bo.getId(), blameRecord, l, AmountTypeEnum.ADD.getCode(),DeductionTypeEnum.JKYY_SHPZ.getCode());
                }
                //城市仓
                else if (l.getResponsibilityType().equals(2)) {
                    //城市仓扣款
                    DeductionInfoParamBo pbo = new DeductionInfoParamBo();
                    //获取城市仓信息
                    RemoteCityWhVo cityWhVo = remoteCityWhService.queryById(blameRecord.getCityWhId());
                    pbo.setCommonId(blameRecord.getCityWhId());
                    pbo.setCommonName(cityWhVo.getName());
                    pbo.setType(DeductionInfoEnum.CITY.getCode());
                    pbo.setAmountType(AmountTypeEnum.LOSS.getCode());
                    pbo.setBillType(BillTypeEnum.STOCKOUT_RECORD.getCode());
                    pbo.setBillCode(l.getSourceCode());
                    pbo.setDeductionType(DeductionTypeEnum.KKYY_SHPZ.getCode());
                    createDedyctionInfo(bo.getId(), blameRecord, l, pbo);
                    //商品金额:供应商加款
                    if (l.getAmountType().equals(10)) {
                        supCreateDedyctionInfo(bo.getId(), blameRecord, l, AmountTypeEnum.ADD.getCode(),DeductionTypeEnum.JKYY_SHPZ.getCode());
                        //优惠金额不为0,总仓加款
                        if (blameRecord.getSubsidyFreeAmount().compareTo(BigDecimal.ZERO) > 0){
                            DeductionInfoParamBo ipbo = new DeductionInfoParamBo();
                            ipbo.setType(DeductionInfoEnum.REGION.getCode());
                            ipbo.setAmountType(AmountTypeEnum.ADD.getCode());
                            ipbo.setBillType(BillTypeEnum.STOCKOUT_RECORD.getCode());
                            ipbo.setBillCode(l.getSourceCode());
                            ipbo.setDeductionType(DeductionTypeEnum.JKYY_PRO_SUB.getCode());
                            createDedyctionInfo(bo.getId(), blameRecord, l, ipbo);
                            //城市仓扣款
                            DeductionInfoParamBo cpbo = new DeductionInfoParamBo();
                            cpbo.setCommonId(blameRecord.getCityWhId());
                            cpbo.setCommonName(cityWhVo.getName());
                            cpbo.setType(DeductionInfoEnum.CITY.getCode());
                            cpbo.setAmountType(AmountTypeEnum.LOSS.getCode());
                            cpbo.setBillType(BillTypeEnum.STOCKOUT_RECORD.getCode());
                            cpbo.setBillCode(l.getSourceCode());
                            cpbo.setDeductionType(DeductionTypeEnum.KKYY_PRO_SUB.getCode());
                            createDedyctionInfo(bo.getId(), blameRecord, l, cpbo);
                        }
                    }
                    //非商品金额:总仓加款
                    else if (l.getAmountType().equals(20)) {
                        DeductionInfoParamBo ipbo = new DeductionInfoParamBo();
                        ipbo.setType(DeductionInfoEnum.REGION.getCode());
                        ipbo.setAmountType(AmountTypeEnum.ADD.getCode());
                        ipbo.setBillType(BillTypeEnum.STOCKOUT_RECORD.getCode());
                        ipbo.setBillCode(l.getSourceCode());
                        ipbo.setDeductionType(DeductionTypeEnum.JKYY_SHPZ.getCode());
                        createDedyctionInfo(bo.getId(), blameRecord, l, ipbo);
                    }
                }
                //其他的不做新增扣款单处理  直接修改判责单状态
                l.setBlameStatus(BlameDetailStatusEnum.HAS_CONFIRM.getCode());
                if (ObjectUtil.isNotNull(LoginHelper.getLoginUser())) {
                    l.setConfirmId(LoginHelper.getLoginUser().getUserId());
                    l.setConfirmName(LoginHelper.getLoginUser().getRealName());
                } else {
                    l.setConfirmId(0L);
                    l.setConfirmName("系统自动确认");
                }
                l.setConfirmTime(new Date());
                orderLog.setOperate("确认判责");
                int hour = 24;
                if(blameRecord.getBusinessType().equals(OrderBusinessTypeEnum.BUSINESS_TYPE1.getCode())){
                    hour = 60;
                }
                orderLog.setRemark(hour + "小时后自动确认判责结果");

            });
            blameRecordDetailMapper.updateBatchById(details);
            //新增操作日志
            orderLog.setSourceId(blameRecord.getId());
            orderLog.setSourceType(OrderLogSourceTypeEnum.BLAME.getCode());
            orderLogMapper.insert(orderLog);
            //更新来源单据 全部确认
            LambdaQueryWrapper<StockoutRecord> qws = Wrappers.lambdaQuery(StockoutRecord.class);
            qws.select(StockoutRecord::getId).eq(StockoutRecord::getCode, blameRecord.getSourceCode()).eq(StockoutRecord::getDelFlag, 0);
            List<StockoutRecord> stockoutRecords = stockoutRecordMapper.selectList(qws);
            if (CollectionUtil.isNotEmpty(stockoutRecords)) {
                stockoutRecords.forEach(l -> l.setBlameStatus(BlameStatusEnum.PART_CONFIRM.getCode()));
            }
            //都确认了改判责单状态
            if (details.stream().allMatch(l -> l.getBlameStatus().equals(BlameDetailStatusEnum.HAS_CONFIRM.getCode()))) {
                blameRecord.setBlameStatus(BlameStatusEnum.FINISH.getCode());
                baseMapper.updateById(blameRecord);
                //更新来源单据 全部确认
                if (CollectionUtil.isNotEmpty(stockoutRecords)) {
                    stockoutRecords.forEach(l -> l.setBlameStatus(BlameStatusEnum.FINISH.getCode()));
                }
            }
            if (CollectionUtil.isNotEmpty(stockoutRecords)) {
                stockoutRecordMapper.updateBatchById(stockoutRecords);
            }
        }
        return details.size();
    }

    /**
     * 根据来源单号查询判责单详情
     */
    @Override
    public BlameRecordInfoVO getBlameBySource(String code) {
        BlameRecordInfoVO detailVO = new BlameRecordInfoVO();
        //获取判责单信息
        LambdaQueryWrapper<BlameRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(BlameRecord::getSourceCode, code).eq(BlameRecord::getDelFlag, 0);
        BlameRecord blameRecord = baseMapper.selectOne(lqw,false);
        if (ObjectUtil.isNull(blameRecord)){
            return null;
        }
        BeanUtil.copyProperties(blameRecord, detailVO);
        //获取费用详情
        LambdaQueryWrapper<BlameRecordDetail> qw = Wrappers.lambdaQuery();
        qw.eq(BlameRecordDetail::getBlameRecordId, blameRecord.getId()).eq(BlameRecordDetail::getDelFlag, 0);
        detailVO.setProductAmount(detailVO.getProductAmount().add(blameRecord.getSubsidyFreeAmount()));
        List<BlameRecordDetail> blameRecordDetailList = blameRecordDetailMapper.selectList(qw);
        //待判责状态下详情是没有的,要判空一下
        if (CollectionUtil.isNotEmpty(blameRecordDetailList)){
            List<BlameRecordDetailVO> detailVOS = new ArrayList<>();
            for (BlameRecordDetail l : blameRecordDetailList){
                BlameRecordDetailVO detail = new BlameRecordDetailVO();
                BeanUtil.copyProperties(l, detail);
                detail.setRecordDetailId(l.getId());
                detailVOS.add(detail);
            }
            detailVO.setBlameRecordDetailVOList(detailVOS);
            detailVO.setBlameExplain(blameRecordDetailList.get(0).getBlameExplain());
        }
        //获取文件
        List<RemoteOssVo> ossVoList = remoteFileService.select("Blame_Record", String.valueOf(blameRecord.getId()));
        //文件赋值
        if (CollectionUtil.isNotEmpty(ossVoList)){
            List<OrderFileVO> fileList = new ArrayList<>();
            ossVoList.forEach(item -> {
                OrderFileVO file = new OrderFileVO();
                file.setUrl(item.getUrl());
                file.setTag(item.getTag());
                file.setSort(item.getSort());
                fileList.add(file);
            });
            detailVO.setFileList(fileList);
        }
        //获取城市仓名称
        RemoteCityWhVo remoteCityWhVo = remoteCityWhService.queryById(blameRecord.getCityWhId());
        if (ObjectUtil.isNotNull(remoteCityWhVo)){
            detailVO.setCityWhName(remoteCityWhVo.getName());
            detailVO.setCityWhCode(remoteCityWhVo.getCode());
        }
        //获取供应商名称
        RemoteSupplierVo supplierVo = remoteSupplierService.getSupplierById(blameRecord.getSupplierId());
        if (ObjectUtil.isNotNull(supplierVo)){
            detailVO.setSupplierName(supplierVo.getName());
            detailVO.setSupplierCode(supplierVo.getCode());
            detailVO.setSupplierAlias(supplierVo.getAlias());
        }
        //获取总仓名称
        RemoteRegionWhVo remoteRegionWhVo = remoteRegionWhService.queryById(blameRecord.getRegionWhId());
        if (ObjectUtil.isNotNull(remoteRegionWhVo)){
            detailVO.setRegionWhName(remoteRegionWhVo.getRegionWhName());
            detailVO.setRegionWhCode(remoteRegionWhVo.getRegionWhCode());
        }
        //计算处理时间
        Date now = new Date();
        if (detailVO.getBlameStatus().equals(BlameStatusEnum.ALL_CONFIRM.getCode()) || detailVO.getBlameStatus().equals(BlameStatusEnum.FINISH.getCode())){
            detailVO.setSeconds(0L);
        }else {
            detailVO.setSeconds(now.getTime() - detailVO.getCreateTime().getTime());
        }
        //获取操作记录
        LambdaQueryWrapper<OrderLog> queryWrapper = Wrappers.lambdaQuery(OrderLog.class)
                .eq(OrderLog::getSourceId, blameRecord.getId()).eq(OrderLog::getDelFlag, 0).eq(OrderLog::getSourceType, OrderLogSourceTypeEnum.BLAME.getCode());
        List<OrderLogVO> orderLogs = orderLogMapper.selectVoList(queryWrapper);
        if (CollectionUtil.isNotEmpty(orderLogs)) {
            List<String> ids = orderLogs.stream().map(l -> l.getId().toString()).toList();
            List<RemoteOssVo> ossVos = remoteFileService.select("Blame_Record_Log", ids);
            if (CollectionUtil.isNotEmpty(ossVos)) {
                Map<String, List<RemoteOssVo>> map = ossVos.stream().collect(Collectors.groupingBy(RemoteOssVo::getKeyword));
                for (OrderLogVO orderLog : orderLogs) {
                    if (map.containsKey(orderLog.getId().toString())) {
                        List<OrderFileVO> fileList = new ArrayList<>();
                        map.get(orderLog.getId().toString()).forEach(item -> {
                            OrderFileVO file = new OrderFileVO();
                            file.setUrl(item.getUrl());
                            file.setTag(item.getTag());
                            file.setSort(item.getSort());
                            fileList.add(file);
                        });
                        orderLog.setFileList(fileList);
                    }
                }
            }
            detailVO.setOrderLogList(orderLogs);
        }

        return detailVO;
    }

    @Override
    public BlameRecord getBlameEntityBySource(String code) {
        LambdaQueryWrapper<BlameRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(BlameRecord::getSourceCode, code).eq(BlameRecord::getDelFlag, 0);
        BlameRecord blameRecord = baseMapper.selectOne(lqw,false);
        return blameRecord;
    }

    /**
     * 城市仓判责申诉
     */
    @Override
    public void complaint(ComplaintBlameRecordBO bo) {
        //获取少货单详情
        StockoutRecord stockoutRecord = stockoutRecordMapper.selectByCode(bo.getCode());
        if (ObjectUtil.isNull(stockoutRecord)){
            throw new ServiceException("少货单不存在");
        }
        //校验状态
        if (stockoutRecord.getBlameStatus() >= BlameStatusEnum.FINISH.getCode()){
            throw new ServiceException("少货单判责状态异常");
        }
        //获取判责单
        BlameRecord blameRecord = getBlameEntityBySource(bo.getCode());
        //判责单存操作日志
        OrderLog orderLog = new OrderLog();
        orderLog.setSourceId(blameRecord.getId());
        orderLog.setSourceType(OrderLogSourceTypeEnum.BLAME.getCode());
        orderLog.setOperate("城市仓申诉");
        orderLog.setRemark("介入原因：" + bo.getCreateRemark());
        orderLogMapper.insert(orderLog);
        //新增关联文档
        if (CollectionUtil.isNotEmpty(bo.getFileList())){
            List<RemoteOssBo> bos = new ArrayList<>();
            bo.getFileList().forEach(l -> {
                RemoteOssBo orderFile = new RemoteOssBo();
                orderFile.setBusinessType("Blame_Record_Log");
                orderFile.setKeyword(orderLog.getId().toString());
                orderFile.setUrl(l.getUrl());
                orderFile.setTag(l.getTag());
                bos.add(orderFile);
            });
            remoteFileService.batchInsert(bos);
        }
        //修改状态
        stockoutRecord.setBlameStatus(BlameStatusEnum.APPEAL.getCode());
        stockoutRecordMapper.updateById(stockoutRecord);
        blameRecord.setBlameStatus(BlameStatusEnum.APPEAL.getCode());
        baseMapper.updateById(blameRecord);
        //发通知总仓处理申诉
        this._sendMessage(blameRecord,stockoutRecord);
    }


    /**
     * 运维接口-撤回已确认判责单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void adminCancel(String code) {
        //获取少货单据
        StockoutRecord stockoutRecord = stockoutRecordMapper.selectByCode(code);
        if (ObjectUtil.isNotNull(stockoutRecord)){
            //获取判责单
            LambdaQueryWrapper<BlameRecord> lqw = Wrappers.lambdaQuery();
            lqw.eq(BlameRecord::getSourceCode, stockoutRecord.getCode()).eq(BlameRecord::getDelFlag, 0);
            BlameRecord blameRecord = baseMapper.selectOne(lqw,false);

            //申诉中不处理
            if (!stockoutRecord.getBlameStatus().equals(BlameStatusEnum.FINISH.getCode())){
                throw new ServiceException("少货单未完成中，请手动处理");
            }
            //已完成 处理加扣款单
            if (blameRecord != null){
                //获取加扣款单
                LambdaQueryWrapper<DeductionInfo> qw = new LambdaQueryWrapper<>();
                qw.eq(DeductionInfo::getBillCode, stockoutRecord.getCode()).eq(DeductionInfo::getDelFlag, 0);
                List<DeductionInfo> deductionInfos = deductionInfoMapper.selectList(qw);
                if (CollectionUtil.isNotEmpty(deductionInfos)){
                    List<DeductionInfo> updateList = new ArrayList<>();


                    //查询是否已结算，结算了要新增加扣款单抵消
                    for (DeductionInfo deductionInfo : deductionInfos){
                        RemoteTransferQueryBo bo = new RemoteTransferQueryBo();
                        bo.setTransNo(deductionInfo.getCode());
                        RemoteTransferVo transferVo = remoteTransferService.transferQuery(bo);
                        if (ObjectUtil.isNull(transferVo)){
                            deductionInfo.setDelFlag(deductionInfo.getId());
                            updateList.add(deductionInfo);
                            continue;
                        }
                        //城市仓&&单据时间不是今天
                        LocalDate billDate = deductionInfo.getBillTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                        LocalDate today = LocalDate.now();
                        boolean b = deductionInfo.getType().equals(2) && !billDate.equals(today);
                        //未结算  直接关闭
                        if (transferVo.getStatus().equals(1) && !b){
                            remoteTransferService.transferClose(bo);
                            deductionInfo.setDelFlag(deductionInfo.getId());
                            updateList.add(deductionInfo);
                        }
                        //生成抵消单据
                        else {
                            DeductionInfo infoVo = new DeductionInfo();
                            BeanUtil.copyProperties(deductionInfo, infoVo);
                            infoVo.setId(null);
                            infoVo.setCode(CustomNoUtil.getDeductionNo(LocalDate.now()));
                            infoVo.setAmountType(deductionInfo.getAmountType().equals(AmountTypeEnum.ADD.getCode())
                                    ? AmountTypeEnum.LOSS.getCode() : AmountTypeEnum.ADD.getCode());
                            infoVo.setStatus(DeductionInfoStatusEnum.COMMITTED.getCode());
                            infoVo.setCreateTime(new Date());
                            infoVo.setUpdateTime(new Date());
                            infoVo.setBillTime(new Date());
                            deductionInfoMapper.insert(infoVo);
                            RemoteTransferCreateBo createBo = new RemoteTransferCreateBo();
                            StockoutDetailVO detailVO = stockoutRecordMapper.getStockoutOrderCodeByCode(infoVo.getBillCode());
                            convertRemoteTransferCreateBo(infoVo,createBo, detailVO);
                            remoteTransferService.create(createBo);
                        }
                    }
                    if (CollectionUtil.isNotEmpty(updateList)){
                        deductionInfoMapper.updateBatchById(updateList);
                    }
                }
                blameRecord.setBlameStatus(BlameStatusEnum.STATE_BLAME.getCode());
                blameRecord.setResponsibilityType(0);
                LambdaQueryWrapper<BlameRecordDetail> lqwr = Wrappers.lambdaQuery();
                lqwr.eq(BlameRecordDetail::getBlameRecordId, blameRecord.getId()).eq(BlameRecordDetail::getDelFlag, 0);
                List<BlameRecordDetail> details = blameRecordDetailMapper.selectList(lqwr);
                if (CollectionUtil.isNotEmpty(details)){
                    details.forEach(l -> l.setDelFlag(l.getId()));
                    blameRecordDetailMapper.updateBatchById(details);
                }
                baseMapper.updateById(blameRecord);
            }
            stockoutRecord.setBlameStatus(BlameStatusEnum.STATE_BLAME.getCode());
            stockoutRecordMapper.updateById(stockoutRecord);
            List<RemoteSupplierSkuInfoVo> remoteSupplierSkuInfoVos = remoteSupplierSkuService
                    .querySimpleInfoList(Lists.newArrayList(stockoutRecord.getSupplierSkuId()));
            //处理云仓库存
            createCwStock(stockoutRecord, remoteSupplierSkuInfoVos.get(0), blameRecord.getProductAmount(),
                    TransTypeCodeEnum.SHCXCK.getCode(), TransTypeIoFlagEnum.STATUS1.getCode());
        }
    }

    public void createCwStock(StockoutRecord stockoutRecord, RemoteSupplierSkuInfoVo skuInfoVo,
                              BigDecimal amount, String transCode,Integer ioFlag) {
        // 调用云仓出库单
        List<Integer> types = CollectionUtil.newArrayList(SupplierSkuBusinessTypeEnum.BUSINESS_TYPE30.getCode()
                , SupplierSkuBusinessTypeEnum.BUSINESS_TYPE20.getCode());
        if (!types.contains(skuInfoVo.getBusinessType())){
            return;
        }
        RemoteCwTransHeadDTO cwBo = new RemoteCwTransHeadDTO();
        cwBo.setRegionWhId(stockoutRecord.getRegionWhId());
        cwBo.setIoFlag(ioFlag);
        cwBo.setTransDate(new Date());
        cwBo.setTransCode(transCode);
        cwBo.setRemark(String.format("少货单：%s",stockoutRecord.getCode()));
        cwBo.setSourceType(TransSourceTypeEnum.STATUS50.getCode().longValue());
        cwBo.setSourceId(stockoutRecord.getId().toString());

        RemoteCwTransDetailDTO sku = new RemoteCwTransDetailDTO();
        BeanUtils.copyProperties(cwBo, sku);
        sku.setSourceDetailId(stockoutRecord.getCode());
        sku.setSkuId(skuInfoVo.getSkuId());
        sku.setSaleDate(stockoutRecord.getSaleDate());
        sku.setIoQty(BigDecimal.valueOf(stockoutRecord.getStockoutCount()));
        sku.setIoPrice(amount.divide(BigDecimal.valueOf(stockoutRecord.getStockoutCount()), 2, BigDecimal.ROUND_HALF_UP));
        sku.setIoAmt(amount);

        cwBo.setCwTransDetailDTOList(CollectionUtil.newArrayList(sku));
        remoteCwStockService.insertByBo(cwBo);
    }

    private void convertRemoteTransferCreateBo(DeductionInfo infoVo, RemoteTransferCreateBo createBo, StockoutDetailVO detail) {
        createBo.setTransId(infoVo.getId());
        createBo.setTransNo(infoVo.getCode());
        if (DeductionInfoEnum.SUPPLIER.getCode().equals(infoVo.getType())
                && AmountTypeEnum.LOSS.getCode().equals(infoVo.getAmountType())) {
            createBo.setBusiType(TransferBusiTypeEnum.TRANSFER_SUPPLIER.getCode());
        } else if (DeductionInfoEnum.SUPPLIER.getCode().equals(infoVo.getType())
                && AmountTypeEnum.ADD.getCode().equals(infoVo.getAmountType())) {
            createBo.setBusiType(TransferBusiTypeEnum.TRANSFER_SUPPLIER_ADD.getCode());
        } else if (DeductionInfoEnum.CITY.getCode().equals(infoVo.getType())
                && AmountTypeEnum.LOSS.getCode().equals(infoVo.getAmountType())) {
            createBo.setBusiType(TransferBusiTypeEnum.TRANSFER_CITY.getCode());
        } else if (DeductionInfoEnum.CITY.getCode().equals(infoVo.getType())
                && AmountTypeEnum.ADD.getCode().equals(infoVo.getAmountType())) {
            createBo.setBusiType(TransferBusiTypeEnum.TRANSFER_CITY_ADD.getCode());
        } else if (DeductionInfoEnum.REGION.getCode().equals(infoVo.getType())) {
            createBo.setBusiType(TransferBusiTypeEnum.TRANSFER_REGION.getCode());
        }
        if (ObjectUtil.isNotEmpty(infoVo.getBillTime())) {
            LocalDate localDate = infoVo.getBillTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            createBo.setTransDate(localDate);
        }
        createBo.setAcctOrgId(infoVo.getRegionWhId());

        if (DeductionInfoEnum.SUPPLIER.getCode().equals(infoVo.getType())) {
            createBo.setOrgType(AccountOrgTypeEnum.SUPPLIER.getCode());
        } else if (DeductionInfoEnum.CITY.getCode().equals(infoVo.getType())) {
            createBo.setOrgType(AccountOrgTypeEnum.CITY_WH.getCode());
        } else if (DeductionInfoEnum.REGION.getCode().equals(infoVo.getType())) {
            createBo.setOrgType(AccountOrgTypeEnum.REGION_WH.getCode());
        }
        createBo.setOrgId(infoVo.getCommonId());
        createBo.setDeptId(infoVo.getSupplierDeptId());

        //默认总仓先
        createBo.setSourceOrgType(AccountOrgTypeEnum.REGION_WH.getCode());
        createBo.setSourceOrgId(infoVo.getRegionWhId());

        createBo.setRelateNo(infoVo.getBillCode());
        createBo.setOrderNo(detail.getOrderCode());
        createBo.setTransOrgId(detail.getCityWhId());
        createBo.setRelateId(detail.getId());
        if (AmountTypeEnum.LOSS.getCode().equals(infoVo.getAmountType())) {
            createBo.setTransAmt(infoVo.getAmount().negate());
        } else {
            createBo.setTransAmt(infoVo.getAmount());
        }
        if (ObjectUtil.isEmpty(infoVo.getDeductionReason())) {
            createBo.setRemark(ObjectUtil.isEmpty(DeductionTypeEnum.loadByCode(infoVo.getDeductionType())) ? null
                    : DeductionTypeEnum.loadByCode(infoVo.getDeductionType()).getDesc());
        } else {
            createBo.setRemark(infoVo.getDeductionReason());
        }
    }

    //创建加扣款单
    private void createDedyctionInfo(Long id, BlameRecord blameRecord, BlameRecordDetail l, DeductionInfoParamBo bo) {
        //查总仓信息
        RemoteRegionWhVo remoteRegionWhVo = remoteRegionWhService.queryById(blameRecord.getRegionWhId());
        //获取文件
        List<RemoteOssVo> ossVoList = remoteFileService.select("Blame_Record", id.toString());
        //文件赋值
        List<OrderFileBO> fileList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(ossVoList)) {
            ossVoList.forEach(item -> {
                OrderFileBO file = new OrderFileBO();
                file.setUrl(item.getUrl());
                file.setTag(item.getTag());
                file.setSort(item.getSort());
                fileList.add(file);
            });
        }
        if (bo.getCommonId() == null){
            bo.setCommonId(blameRecord.getRegionWhId());
            bo.setCommonName(remoteRegionWhVo.getRegionWhName());
        }
        bo.setRegionWhId(blameRecord.getRegionWhId());
        bo.setRegionWhName(remoteRegionWhVo.getRegionWhName());
        bo.setCode(CustomNoUtil.getDeductionNo(LocalDate.now()));
        //供应商承担商品金额,有优惠金额,扣款回来
        if (l.getResponsibilityType().equals(1) && l.getAmountType().equals(10) && blameRecord.getSubsidyFreeAmount().compareTo(BigDecimal.ZERO) > 0) {
            bo.setAmount(blameRecord.getSubsidyFreeAmount());
        }
        //城市仓担责商品金额,有优惠金额,扣款金额为优惠金额,加扣款类型为优惠金额扣款
        else if (l.getResponsibilityType().equals(2) && l.getAmountType().equals(10) && blameRecord.getSubsidyFreeAmount().compareTo(BigDecimal.ZERO) > 0
                && bo.getDeductionType().equals(DeductionTypeEnum.KKYY_PRO_SUB.getCode())) {
            bo.setAmount(blameRecord.getSubsidyFreeAmount());
        }
        //总仓担责商品金额,有优惠金额,扣款金额为优惠金额 类型是总仓
        else if (l.getResponsibilityType().equals(3) && l.getAmountType().equals(10) && blameRecord.getSubsidyFreeAmount().compareTo(BigDecimal.ZERO) > 0) {
            bo.setAmount(blameRecord.getSubsidyFreeAmount());
        } else {
            bo.setAmount(l.getAmount());
        }
        bo.setBillCode(blameRecord.getSourceCode());
        bo.setSupplierSkuId(blameRecord.getSupplierSkuId().toString());
        bo.setSaleDate(blameRecord.getSaleDate());
        bo.setSpuName(blameRecord.getSpuName());
        bo.setFileList(BeanUtil.copyToList(fileList, cn.xianlink.order.api.bo.OrderFileBO.class));
        bo.setStatus(DeductionInfoStatusEnum.COMMITTED.getCode());
        bo.setCreateType(CreateTypeEnum.AUTO.getCode());
        bo.setDeductionReason(l.getBlameExplain());
        bo.setBillTime(new Date());
        bo.setLogisticsId(blameRecord.getLogisticsId());
        bo.setPlaceIdLevel2(blameRecord.getPlaceIdLevel2());
        log.keyword("createDedyctionInfo","确认判责发送扣款单").info("入参：{}", bo);
        deductionInfoCompleteProducer.send(bo);
    }

    /**
     * 组装判责单数据
     */
    private void getBlameRecordByStockoutInfo(StockoutRecord stockoutRecord, CreateBlameRecordBO bo, BlameRecord blameRecord) {
        blameRecord.setCode(CustomNoUtil.getBlameNo(LocalDate.now()));
        blameRecord.setSourceCode(stockoutRecord.getCode());
        blameRecord.setCityWhId(stockoutRecord.getCityWhId());
        blameRecord.setSupplierId(stockoutRecord.getSupplierId());
        blameRecord.setRegionWhId(stockoutRecord.getRegionWhId());
        blameRecord.setLogisticsId(stockoutRecord.getLogisticsId());
        blameRecord.setLogisticsCode(stockoutRecord.getLogisticsCode());
        blameRecord.setLogisticsName(stockoutRecord.getLogisticsName());
        blameRecord.setSupplierSkuId(stockoutRecord.getSupplierSkuId());
        blameRecord.setSupplierSkuCode(stockoutRecord.getSupplierSkuCode());
        blameRecord.setSaleDate(stockoutRecord.getSaleDate());
        blameRecord.setSpuName(stockoutRecord.getSpuName());
        blameRecord.setProductAmount(bo.getProductAmount());
        blameRecord.setOtherAmount(bo.getOtherAmount());
        blameRecord.setSupplierDeptId(stockoutRecord.getSupplierDeptId());
        blameRecord.setBlameAmount(bo.getBlameAmount());
        blameRecord.setTotalCount(bo.getTotalCount());
        blameRecord.setBuyerId(stockoutRecord.getBuyerId());
        blameRecord.setBuyerName(stockoutRecord.getBuyerName());
        blameRecord.setSourceType(bo.getSourceType().getCode());
        blameRecord.setOrderType(stockoutRecord.getOrderType());
        blameRecord.setBlameStatus(BlameStatusEnum.STATE_BLAME.getCode());
        blameRecord.setCreateRemark(bo.getCreateRemark());
        blameRecord.setPlatformFreight(bo.getPlatformFreight());
        blameRecord.setBaseFreight(bo.getBaseFreight());
        blameRecord.setRegionFreight(bo.getRegionFreight());
        blameRecord.setFinancialServicePrice(bo.getFinancialServicePrice());
        blameRecord.setServiceAmount(bo.getServiceAmount());
        blameRecord.setSubsidyFreeAmount(bo.getSubsidyFreeAmount());
        blameRecord.setBusinessType(bo.getBusinessType());
        blameRecord.setPlaceIdLevel2(bo.getPlaceIdLevel2());
        baseMapper.insert(blameRecord);
        OrderLog orderLog = new OrderLog();
        //新增判责操作记录单
        orderLog.setSourceId(blameRecord.getId());
        orderLog.setSourceType(OrderLogSourceTypeEnum.BLAME.getCode());
        orderLog.setOperate(bo.getSourceType().getDesc()+"自动产生判责单");
        orderLog.setRemark(bo.getCreateRemark());
        orderLogMapper.insert(orderLog);
        //城市仓提货时少货,按提示直接判责给城市仓
        if (bo.getCreateSceneCode().equals(StockOutCreateSceneEnum.RECEIVE_GOODS.getCode())){
            //创建新判责单明细
            List<BlameRecordDetail> newDetailList = new ArrayList<>();
            if(bo.getProductAmount().compareTo(BigDecimal.ZERO) > 0) {
                BlameRecordDetail blameRecordDetail = new BlameRecordDetail();
                blameRecordDetail.setBlameRecordId(blameRecord.getId());
                blameRecordDetail.setSourceType(blameRecord.getSourceType());
                blameRecordDetail.setSourceCode(blameRecord.getSourceCode());
                blameRecordDetail.setResponsibilityType(ResponsibilityTypeEnum.CITY_WH.getCode());
                blameRecordDetail.setResponsibilityId(stockoutRecord.getCityWhId());
                blameRecordDetail.setResponsibilityCode(bo.getCityWhCode());
                blameRecordDetail.setResponsibilityName(bo.getCityWhName());
                blameRecordDetail.setAmountType(10);
                blameRecordDetail.setAmount(bo.getProductAmount());
                blameRecordDetail.setResponsibilityExplain(bo.getBlameExplain());
                blameRecordDetail.setBlameExplain(bo.getCreateRemark());
                blameRecordDetail.setBlameCount(stockoutRecord.getStockoutCount());
                blameRecordDetail.setBlameStatus(BlameDetailStatusEnum.STATE_CONFIRM.getCode());
                blameRecordDetail.setCreateRemark(bo.getCreateRemark());
                blameRecordDetail.setConfirmId(0L);
                blameRecordDetail.setConfirmName(blameRecord.getCreateName());
                blameRecordDetail.setConfirmTime(new Date());
                newDetailList.add(blameRecordDetail);
            }
            if(bo.getOtherAmount().compareTo(BigDecimal.ZERO) > 0) {
                BlameRecordDetail blameRecordDetail = new BlameRecordDetail();
                blameRecordDetail.setBlameRecordId(blameRecord.getId());
                blameRecordDetail.setSourceType(blameRecord.getSourceType());
                blameRecordDetail.setSourceCode(blameRecord.getSourceCode());
                blameRecordDetail.setResponsibilityType(ResponsibilityTypeEnum.CITY_WH.getCode());
                blameRecordDetail.setResponsibilityId(stockoutRecord.getCityWhId());
                blameRecordDetail.setResponsibilityCode(bo.getCityWhCode());
                blameRecordDetail.setResponsibilityName(bo.getCityWhName());
                blameRecordDetail.setAmountType(20);
                blameRecordDetail.setAmount(bo.getOtherAmount());
                blameRecordDetail.setResponsibilityExplain(bo.getBlameExplain());
                blameRecordDetail.setBlameExplain(bo.getCreateRemark());
                blameRecordDetail.setBlameCount(stockoutRecord.getStockoutCount());
                blameRecordDetail.setBlameStatus(BlameDetailStatusEnum.STATE_CONFIRM.getCode());
                blameRecordDetail.setCreateRemark(bo.getCreateRemark());
                blameRecordDetail.setConfirmId(0L);
                blameRecordDetail.setConfirmName(blameRecord.getCreateName());
                blameRecordDetail.setConfirmTime(new Date());
                blameRecordDetail.setPlatformFreight(blameRecord.getPlatformFreight());
                blameRecordDetail.setBaseFreight(blameRecord.getBaseFreight());
                blameRecordDetail.setRegionFreight(blameRecord.getRegionFreight());
                blameRecordDetail.setFinancialServicePrice(blameRecord.getFinancialServicePrice());
                blameRecordDetail.setServiceAmount(blameRecord.getServiceAmount());
                newDetailList.add(blameRecordDetail);
            }
            //新增操作日志
            OrderLog orderLog1 = new OrderLog();
            orderLog1.setSourceId(blameRecord.getId());
            orderLog1.setSourceType(OrderLogSourceTypeEnum.BLAME.getCode());
            orderLog1.setOperate("判责");
            orderLog1.setRemark("城市仓 "+ bo.getCityWhName() + "\n" + bo.getCreateRemark());
            orderLog1.setCreateTime(new Date());
            blameRecordDetailMapper.insertBatch(newDetailList);
            orderLogMapper.insert(orderLog1);
            if (bo.getCreateSceneCode().equals(StockOutCreateSceneEnum.RECEIVE_GOODS.getCode())) {
                confirm(blameRecord.getId(), AccountTypeStatusEnum.CITY.getCode(), true);
            }
        }else {
            this._sendMessage(blameRecord,stockoutRecord);
            if (bo.getBusinessType().equals(OrderBusinessTypeEnum.BUSINESS_TYPE1.getCode())){
                //发送延时消息
                BlameConfirmMessageBO messageBO = new BlameConfirmMessageBO();
                messageBO.setId(blameRecord.getId());
                messageBO.setExpireTime(DateUtil.offsetHour(new Date(), AUTO_CONFIRM_TIME_48).toJdkDate());
                blameRecordConfirmProducer.send(messageBO, null);
            }
        }
    }

    private void _sendMessage(BlameRecord blameRecord, StockoutRecord stockoutRecord) {
        LinkedHashMap<String, String> params = new LinkedHashMap<>();
        //params.put("stockoutStatus", "少货单（" + Objects.requireNonNull(BlameStatusEnum.loadByCode(blameRecord.getBlameStatus())).getDesc() + "）");
        params.put("stockoutCode", blameRecord.getSourceCode());
        params.put("spuName", remoteSupplierSkuService.getSkuCombinationName(Lists.newArrayList(blameRecord.getSupplierSkuId())).get(blameRecord.getSupplierSkuId()));
        params.put("count", blameRecord.getTotalCount().toString());
        params.put("submitTime", DateUtil.format(blameRecord.getCreateTime(), DatePattern.NORM_DATETIME_PATTERN));
        //最多发给两方
        RemoteMessageNotifyV2Bo message = new RemoteMessageNotifyV2Bo();
        message.setParams(params);
        message.setPagePathParam(stockoutRecord.getId().toString());
        RemoteMessageNotifyV2Bo message1 = new RemoteMessageNotifyV2Bo();
        BeanUtil.copyProperties(message,message1);
        switch (Objects.requireNonNull(BlameStatusEnum.loadByCode(blameRecord.getBlameStatus()))) {
            case STATE_BLAME -> {
                //待判责 总仓 市采还要发到供应商
                message.setObjectType(NotifyObjectTypeEnum.REGION_WH);
                message.setTemplate(MsgNotifyTemplateV2Enum.few_goods_responsibility_state);
                RemoteUserBo userBo = remoteUserService.getUserInfo(blameRecord.getBuyerId());
                message.setObjectIds(Lists.newArrayList(userBo.getUserCode()));
                if (blameRecord.getBusinessType().equals(OrderBusinessTypeEnum.BUSINESS_TYPE1.getCode())) {
                    message1.setObjectType(NotifyObjectTypeEnum.SUPPLIER);
                    message1.setTemplate(MsgNotifyTemplateV2Enum.few_goods_responsibility_state);
                    message1.setObjectIds(Lists.newArrayList(blameRecord.getSupplierId().toString()));
                }
            }
            case HAS_BLAME -> {
                //已判责 根据担责方
                if (blameRecord.getResponsibilityType().equals(ResponsibilityTypeEnum.CITY_WH.getCode())) {
                    message.setObjectType(NotifyObjectTypeEnum.CITY_WH);
                    message.setObjectIds(Lists.newArrayList(blameRecord.getCityWhId().toString()));
                }
                if (blameRecord.getResponsibilityType().equals(ResponsibilityTypeEnum.SUPPLIER.getCode())) {
                    message.setObjectType(NotifyObjectTypeEnum.SUPPLIER);
                    message.setObjectIds(Lists.newArrayList(blameRecord.getSupplierId().toString()));
                }
                message.setTemplate(MsgNotifyTemplateV2Enum.few_goods_responsibility_has);
            }
            case FINISH -> {
                //根据担责方
                if (blameRecord.getResponsibilityType().equals(ResponsibilityTypeEnum.CITY_WH.getCode())) {
                    message.setObjectType(NotifyObjectTypeEnum.CITY_WH);
                    message.setObjectIds(Lists.newArrayList(blameRecord.getCityWhId().toString()));
                }
                if (blameRecord.getResponsibilityType().equals(ResponsibilityTypeEnum.SUPPLIER.getCode())) {
                    message.setObjectType(NotifyObjectTypeEnum.SUPPLIER);
                    message.setObjectIds(Lists.newArrayList(blameRecord.getSupplierId().toString()));
                }
                message.setTemplate(MsgNotifyTemplateV2Enum.few_goods_responsibility_finish);
            }
            case APPEAL -> {
                message.setObjectType(NotifyObjectTypeEnum.REGION_WH);
                RemoteUserBo userBo = remoteUserService.getUserInfo(blameRecord.getBuyerId());
                message.setObjectIds(Lists.newArrayList(userBo.getUserCode()));
                message.setTemplate(MsgNotifyTemplateV2Enum.few_goods_responsibility_appeal);
            }
            default -> {
            }
        }
        //有具体对象才发送
        if (ObjectUtil.isNotNull(message.getObjectType())){
            remoteMessageNotifyService.sendMessageV2(message);
        }
        if (ObjectUtil.isNotNull(message1.getObjectType())){
            remoteMessageNotifyService.sendMessageV2(message1);
        }
    }

    /**
     * 获取判责单编号
     */
    private String getBlameCode(){
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        String dataFormat = dateFormat.format(new Date());
        String random = RandomUtil.randomNumbers(5);
        return ("PZ" + dataFormat + random);
    }



    /**
     * 判责退款
     * <AUTHOR> on 2024/12/1:16:16
     * @param
     * @return void
     */
    private void blameRefundAmount(BlameRecord blameRecord) {
        if (blameRecord.getBlameStatus().equals(BlameStatusEnum.STATE_BLAME.getCode())) {
            //未判责的情况下不需要退款
            return;
        }
        //少货
//        if(blameRecord.getSourceType().equals(BlameSourceTypeEnum.LESS_GOODS.getCode())) {
//            //只要是少货判责，就都调用少货退款，通过退款里的重复校验控制一笔少货单只产生一次退款，这样简单兼容少货确认时可能产生退款，判责也得退款的需求
//            StockoutRecord stockoutRecord = stockoutRecordMapper.selectByCode(blameRecord.getSourceCode());
//            if (stockoutRecord == null) {
//                log.keyword("confirmBlameRefundAmount").error("少货单判责完要退款时找不到少货单【{}】", blameRecord.getSourceCode());
//                return;
//            }
//            //转调少货确认，走原逻辑，不额外写代码处理状态等东西
//            SpringUtils.getBean(IStockoutRecordService.class).stockoutConfirm(stockoutRecord.getId());
//        }
    }
}
