package cn.xianlink.order.domain.vo.platform;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 客户端未开票列表视图对象
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@ExcelIgnoreUnannotated
public class ClientUnInvoicedVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 供应商ID
     */
    @ExcelProperty(value = "供应商ID")
    private Long supplierId;

    /**
     * 供应商名称
     */
    @ExcelProperty(value = "供应商名称")
    private String supplierName;

    /**
     * 供应商编码
     */
    @ExcelProperty(value = "供应商编码")
    private String supplierCode;

    /**
     * 开票金额（实付金额 - 逆向金额）
     */
    @ExcelProperty(value = "开票金额")
    private BigDecimal invoiceAmount;

    /**
     * 实付金额
     */
    @ExcelProperty(value = "实付金额")
    private BigDecimal actualAmount;

    /**
     * 逆向金额（退款/少货/缺货等）
     */
    @ExcelProperty(value = "逆向金额")
    private BigDecimal reverseAmount;

    /**
     * 商品合计件数
     */
    @ExcelProperty(value = "商品合计件数")
    private Integer totalItems;

    /**
     * 订单数量
     */
    @ExcelProperty(value = "订单数量")
    private Integer orderCount;

    /**
     * 最早订单时间
     */
    @ExcelProperty(value = "最早订单时间")
    private Date earliestOrderTime;

    /**
     * 最晚订单时间
     */
    @ExcelProperty(value = "最晚订单时间")
    private Date latestOrderTime;

    /**
     * 订单日期区间描述
     */
    @ExcelProperty(value = "订单日期区间")
    private String orderDateRange;

    /**
     * 订单项详情列表
     */
    private List<ClientUnInvoicedItemVo> orderItems;

    /**
     * 是否可开票（金额大于0）
     */
    private Boolean canInvoice;

    /**
     * 备注
     */
    private String remark;
}
