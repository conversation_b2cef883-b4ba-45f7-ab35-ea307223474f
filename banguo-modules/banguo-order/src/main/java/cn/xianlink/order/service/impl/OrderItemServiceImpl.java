package cn.xianlink.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.xianlink.basic.api.*;
import cn.xianlink.basic.api.domain.vo.RemoteSupplierVo;
import cn.xianlink.common.api.constant.BanguoCommonConstant;
import cn.xianlink.common.api.enums.basic.WhProfitRuleTypeEnum;
import cn.xianlink.common.api.enums.order.OrderCancelTypeEnum;
import cn.xianlink.common.api.enums.order.OrderStatusEnum;
import cn.xianlink.common.api.enums.order.RefundStatusEnum;
import cn.xianlink.common.api.enums.product.SupplierSkuBatchTypeEnum;
import cn.xianlink.common.api.enums.trade.AccountOrgTypeEnum;
import cn.xianlink.common.api.enums.trade.AccountTransTypeEnum;
import cn.xianlink.marketing.api.RemoteDistributionService;
import cn.xianlink.order.api.bo.RemoteSupAccSubsidyBo;
import cn.xianlink.order.api.bo.RemoteSupAccTransBo;
import cn.xianlink.order.api.bo.RemoteSupAccTransQueryBo;
import cn.xianlink.order.api.vo.RemoteOrderSalesVO;
import cn.xianlink.order.api.vo.RemoteRegionSubMaxPriceVo;
import cn.xianlink.order.api.vo.RemoteSupBillVo;
import cn.xianlink.order.domain.Order;
import cn.xianlink.order.domain.OrderItem;
import cn.xianlink.order.domain.RefundProductDetail;
import cn.xianlink.order.domain.excel.LogisticsOrderExportDto;
import cn.xianlink.order.domain.order.bo.OrderItemSettleMqBo;
import cn.xianlink.order.domain.order.vo.OrderItemSupplierSkuInfoVo;
import cn.xianlink.order.domain.order.vo.OrderItemVo;
import cn.xianlink.order.domain.order.vo.OrderVo;
import cn.xianlink.order.domain.vo.SkuSalesQuantityCount;
import cn.xianlink.order.mapper.OrderItemMapper;
import cn.xianlink.order.mapper.OrderMapper;
import cn.xianlink.order.mapper.RefundProductDetailMapper;
import cn.xianlink.order.mapper.RefundRecordMapper;
import cn.xianlink.order.mq.consumer.SupItemSettleConsumer;
import cn.xianlink.order.mq.producer.RefreshOrderSettleProducer;
import cn.xianlink.order.service.IOrderItemService;
import cn.xianlink.product.api.RemoteSupplierSkuService;
import cn.xianlink.product.api.domain.bo.RemoteQueryInfoListBo;
import cn.xianlink.product.api.domain.vo.RemoteSupplierSkuInfoVo;
import cn.xianlink.resource.api.RemoteFileService;
import cn.xianlink.trade.api.RemoteTradeAccTransService;
import cn.xianlink.trade.api.RemoteTransAvailService;
import cn.xianlink.trade.api.domain.bo.RemoteTransAvailBo;
import cn.xianlink.trade.api.domain.bo.RemoteTransAvailOrgBo;
import cn.xianlink.trade.api.domain.vo.RemoteTransAvailOrgVo;
import cn.xianlink.trade.api.domain.vo.RemoteTransAvailVo;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 订单项Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-25
 */
@RequiredArgsConstructor
@Service
@CustomLog
@RefreshScope
public class OrderItemServiceImpl implements IOrderItemService {

    private final OrderItemMapper baseMapper;

    private final OrderMapper orderMapper;

    private final RefundRecordMapper refundsRecordMapper;

    private final RefundProductDetailMapper refundProductDetailMapper;


    private final RefreshOrderSettleProducer reorderSettleProducer;

    @DubboReference
    private RemoteSupplierSkuService remoteSupplierSkuService;
    @DubboReference
    private RemoteTransAvailService remoteTransAvailService;
    @DubboReference
    private final RemoteSupplierService remoteSupplierService;
    @DubboReference
    private final RemoteCityWhService remoteCityWhService;
    @DubboReference
    private final RemoteRegionWhService remoteRegionWhService;
    @DubboReference
    private final RemoteBasCustomerService remoteBasCustomerService;
    @DubboReference
    private final RemoteRegionLogisticsService remoteRegionLogisticsService;
    @DubboReference
    private final RemoteTradeAccTransService remoteTradeAccTransService;
    @DubboReference
    private final RemoteExportJobService remoteExportJobService;
    @DubboReference(timeout = 300000)
    private final RemoteFileService remoteFileService;
    @DubboReference
    private final RemoteDistributionService remoteDistributionService;

    @Value("${SalesDayBefore:30}")
    private Integer salesDayBefore;
    @Value("${SalesDayCustomerBefore:3}")
    private Integer salesDayCustomerBefore;

    @Value("${sup.settle.preAvailDateSeconds:9000}")
    private Long preAvailDateSeconds;

    @Override
    public List<OrderItemSupplierSkuInfoVo> getByOrderCodeAndSupplierId(String orderCode, Long supplierId) {
        if (ObjectUtil.isEmpty(orderCode)) {
            return Collections.emptyList();
        }
        List<OrderItemSupplierSkuInfoVo> result = new ArrayList<>();
        LambdaQueryWrapper<Order> orderWrapper = new LambdaQueryWrapper<>();
        orderWrapper.eq(Order::getCode, orderCode);
        Order order = orderMapper.selectOne(orderWrapper);
        if (ObjectUtil.isEmpty(order)) {
            log.keyword("getByOrderCodeAndSupplierId").info("未查到订单数据");
            return Collections.emptyList();
        }

        LambdaQueryWrapper<OrderItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderItem::getOrderId, order.getId());
        if (ObjectUtil.isNotEmpty(supplierId)) {
            wrapper.eq(OrderItem::getSupplierId, supplierId);
        }
        List<OrderItemVo> orderItemVos = baseMapper.selectVoList(wrapper);
        if (ObjectUtil.isEmpty(orderItemVos)) {
            return Collections.emptyList();
        }
        Map<Long, RemoteSupplierSkuInfoVo> map = new HashMap<>();

        List<Long> supplierSkuIds = orderItemVos.stream().map(OrderItemVo::getSupplierSkuId).distinct().collect(Collectors.toList());
        RemoteQueryInfoListBo listBo = new RemoteQueryInfoListBo();
        listBo.setSupplierSkuIdList(supplierSkuIds);
        List<RemoteSupplierSkuInfoVo> remoteSupplierSkuInfoVos = remoteSupplierSkuService.queryInfoList(listBo);
        if (ObjectUtil.isNotEmpty(remoteSupplierSkuInfoVos)) {
            map = remoteSupplierSkuInfoVos.stream().collect(Collectors.toMap(RemoteSupplierSkuInfoVo::getId, u -> u, (n1, n2) -> n1));
        }
        List<Long> supplierIds = orderItemVos.stream().map(OrderItemVo::getSupplierId).distinct().toList();
        Map<Long, RemoteSupplierVo> supplierMap = remoteSupplierService.getSupplierByIds(supplierIds).stream().collect(Collectors.toMap(RemoteSupplierVo::getId, Function.identity()));
        for (OrderItemVo itemVo : orderItemVos) {
            OrderItemSupplierSkuInfoVo vo = BeanUtil.toBean(itemVo, OrderItemSupplierSkuInfoVo.class);
            RemoteSupplierSkuInfoVo skuInfoVo = map.get(itemVo.getSupplierSkuId());
            if (ObjectUtil.isEmpty(skuInfoVo)) {
                result.add(vo);
                continue;
            }
            if (ObjectUtil.isNotEmpty(SupplierSkuBatchTypeEnum.loadByCode(skuInfoVo.getBatchType()))) {
                vo.setBatchTypeName(SupplierSkuBatchTypeEnum.loadByCode(skuInfoVo.getBatchType()).getDesc());
            }
            vo.setSupplierName(skuInfoVo.getSupplierName());
            vo.setSupplierAlias(supplierMap.getOrDefault(vo.getSupplierId(), new RemoteSupplierVo()).getAlias());
            vo.setSupplierSkuCreateTime(skuInfoVo.getCreateTime());
            result.add(vo);
        }
        return result;
    }

    @Override
    public void notifySettle(List<Long> itemIds) {
        // 业务处理完不立即进行结算
//        if (CollUtil.isNotEmpty(itemIds)) {
//            var list = Lists.partition(itemIds, 100);
//            for (List<Long> longs : list) {
//                reorderSettleProducer.send(OrderItemSettleMqBo.itemAllSettleBuild(longs));
//            }
//        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    @Lock4j(keys = {"#itemId"}, expire = 5000, acquireTimeout = 6000)
    public void itemSettle(Long itemId, String triggerSource) {
        //通过订单加供应商找出未结算的订单项
        OrderItem item = baseMapper.selectById(itemId);
        if (item == null) {
            return;
        }
        if(item.getSettleTime() != null) {
            log.keyword("itemSettle", itemId).error("订单：{}，订单项：{} 已经通知交易服务做结算，不能二次通知", item.getOrderId(), itemId);
            return;
        }
        log.keyword("itemSettle", itemId).info("订单：{}，订单项：{}可以结算", item.getOrderId(), itemId);

        LambdaQueryWrapper<Order> orderWrapper = new LambdaQueryWrapper<>();
        orderWrapper.eq(Order::getId, item.getOrderId());
        orderWrapper.select(Order::getId, Order::getCode);
        Order order = orderMapper.selectOne(orderWrapper);

        RemoteTransAvailBo bo = new RemoteTransAvailBo();
        bo.setTransType(AccountTransTypeEnum.OP.getCode());
        bo.setTransId(order.getId());
        bo.setTransNo(order.getCode());
        List<RemoteTransAvailOrgBo> trans = new ArrayList<>();
        RemoteTransAvailOrgBo org = new RemoteTransAvailOrgBo();
        org.setOrgId(item.getSupplierId());
        org.setOrgType(AccountOrgTypeEnum.SUPPLIER.getCode());
        org.setOrgCode(remoteSupplierService.getSupplierById(item.getSupplierId()).getCode());
        org.setSkuId(item.getSupplierSkuId());
        trans.add(org);
        bo.setTrans(trans);

        //指定结算日处理
        if (StrUtil.isNotBlank(triggerSource) && triggerSource.equals(SupItemSettleConsumer.jobSettleSource)) {
            //如果是通过0:30的job触发的结算，并且是在3:00的转提现Job之前执行，可以指定结算日为昨天，这样今天就能提现
            //3:00之后不能再指定结算日为昨天，否则会导致产生多张结算单，保险起见实际配置时间会早于3:00

            // 获取当前的 LocalDateTime
            LocalDateTime now = LocalDateTime.now();
            // 获取今天的零点
            LocalDateTime todayStart = now.toLocalDate().atStartOfDay();
            // 计算从今天零点到当前时间的秒数
            long seconds = ChronoUnit.SECONDS.between(todayStart, now);
            //因为环境问题，改为直接判断秒数
            if (seconds <= preAvailDateSeconds) {
                bo.setAvailDate(LocalDate.now().minusDays(1));
            }
        }

        //更新结算时间，可以重复更新
        OrderItem upItem = new OrderItem();
        upItem.setId(item.getId());
        upItem.setSettleTime(new Date());
        upItem.setSettleCount(Optional.ofNullable(item.getSettleCount()).orElse(0) + 1);
        baseMapper.updateById(upItem);

        //分开更新状态值，避免先全缺货少货，退款触发结算后把状态值给覆盖掉
        LambdaUpdateWrapper<OrderItem> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(OrderItem::getId, item.getId())
                .eq(OrderItem::getStatus, OrderStatusEnum.ALREADY.getCode())
                .set(OrderItem::getStatus, OrderStatusEnum.FINISH.getCode());
        baseMapper.update(updateWrapper);

        RemoteTransAvailVo transAvailVo = remoteTransAvailService.updateTransToAvail(bo);
        if (transAvailVo != null && CollUtil.isNotEmpty(transAvailVo.getTrans())) {
            RemoteTransAvailOrgVo orgVo = transAvailVo.getTrans().get(0);
            //通知更新业务单据的结算单
            log.keyword("itemSettle", itemId).info("订单：{}，订单项：{} 结算完毕，通知更新业务单据的结算单号", item.getOrderId(), itemId);
            reorderSettleProducer.send(OrderItemSettleMqBo.orderBuild(item.getOrderId(), item.getSupplierId(), item.getId(), orgVo.getSplitNo(), orgVo.getAvailNo()));
            // 通知商品分销结算
            if (item.getDistributionAmount() != null &&  !item.getDistributionAmount().equals(BigDecimal.ZERO)) {
                remoteDistributionService.itemSettle(item.getOrderId(), item.getId(), upItem.getSettleTime());
            }
        }
    }

    @Override
    public List<OrderItem> getByOrderIds(Set<Long> orderIds, List<String> cancelTypes) {
        LambdaQueryWrapper<OrderItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(OrderItem::getOrderId, orderIds);
        if (CollUtil.isNotEmpty(cancelTypes)) {
            wrapper.and(w -> w.isNull(OrderItem::getCancelType).or().in(OrderItem::getCancelType, cancelTypes));
        }
        wrapper.eq(OrderItem::getDelFlag, BanguoCommonConstant.notDelFlag);
        return baseMapper.selectList(wrapper);
    }

    @Override
    public List<LogisticsOrderExportDto> selectLogisticsOrderForExport(List<Long> logisticsIds, LocalDate saleDate,List<String> placePaths) {
        return baseMapper.selectLogisticsOrderForExport(logisticsIds, saleDate,placePaths);
    }

    /**
     * 根据城市仓id查询订单销量信息
     */
    @Override
    public List<RemoteOrderSalesVO> getOrderSalesByCity(Long cityWhId) {
        //todo 按道理是要根据城市仓对应总仓获取当前销售日
        LocalDate saleDateStart = LocalDate.now().minusDays(salesDayBefore + 1);
        LocalDate saleDateEnd = LocalDate.now().minusDays(1);
        List<RemoteOrderSalesVO> result = baseMapper.getOrderSalesByCity(cityWhId, saleDateStart, saleDateEnd);
        //处理一下取消状态不为缺货少货的
        if (CollectionUtil.isNotEmpty(result)){
            //获取城市仓下该sku近3天下过单的用户id
            LocalDate saleDate = LocalDate.now().minusDays(salesDayCustomerBefore);
            List<Long> categoryIds = result.stream().map(RemoteOrderSalesVO::getCategoryIdLevel2).distinct().toList();
            List<RemoteOrderSalesVO> salesVOS = baseMapper.getOrderSalesCustomer(cityWhId, categoryIds, saleDate, saleDateEnd);
            Map<Long, String> customers = new HashMap<>();
            if (CollectionUtil.isNotEmpty(salesVOS)) {
                for (RemoteOrderSalesVO vo : salesVOS) {
                    if ((StringUtils.isNotEmpty(vo.getCancelType()) && !OrderCancelTypeEnum.haveWorking(vo.getCancelType()))
                            || StringUtils.isEmpty(vo.getCustomerIds())) {
                        continue;
                    }
                    if (customers.containsKey(vo.getCategoryIdLevel2())) {
                        //去重
                        customers.put(vo.getCategoryIdLevel2(), getCustomerIds(customers.get(vo.getCategoryIdLevel2()), vo.getCustomerIds()));
                    } else {
                        customers.put(vo.getCategoryIdLevel2(), vo.getCustomerIds());
                    }
                }
            }
            //有取消状态的已经排序到前面了，会先处理那些数据
            Map<Long, RemoteOrderSalesVO> map = new HashMap<>();
            for (RemoteOrderSalesVO vo : result) {
                if (StringUtils.isNotEmpty(vo.getCancelType()) && !OrderCancelTypeEnum.haveWorking(vo.getCancelType())) {
                    continue;
                }
                if (StringUtils.isNotEmpty(vo.getCancelType()) && OrderCancelTypeEnum.haveWorking(vo.getCancelType())) {
                    if (map.containsKey(vo.getSkuId())) {
                        Integer thiSalesCount = map.get(vo.getSkuId()).getThiSalesCount() + vo.getThiSalesCount();
                        vo.setThiSalesCount(thiSalesCount);
                        map.put(vo.getSkuId(), vo);
                    }
                    map.put(vo.getSkuId(), vo);
                }else {
                    if (map.containsKey(vo.getSkuId())) {
                        Integer thiSalesCount = map.get(vo.getSkuId()).getThiSalesCount() + vo.getThiSalesCount();
                        vo.setThiSalesCount(thiSalesCount);
                        map.put(vo.getSkuId(), vo);
                    }
                    map.put(vo.getSkuId(), vo);
                }
                vo.setCustomerIds(customers.getOrDefault(vo.getCategoryIdLevel2(), "0"));
            }
            return map.values().stream().toList();
        }
        return result;
    }

    /**
     * 两个用。拼接的字符串拆分去重合并出新的字符串
     */
    private String getCustomerIds(String customersOld, String customersNew) {
        Set<String> set = new HashSet<>(Arrays.asList(customersOld.split(",")));
        set.addAll(Arrays.asList(customersNew.split(",")));
        return set.stream().sorted().collect(Collectors.joining(","));
    }

    /**
     * 根据销售日期，获取有销售记录城市仓id列表
     */
    @Override
    public List<OrderVo> getCityWhIdListByExistSales(LocalDate saleDateStart, LocalDate saleDateEnd, Long notRunRegionWhId) {
        QueryWrapper<Order> qw = Wrappers.query();
        qw.eq("del_flag", 0).between("sale_date", saleDateStart, saleDateEnd)
                .ne(ObjectUtil.isNotNull(notRunRegionWhId), "region_wh_id", notRunRegionWhId);
        qw.select("city_wh_id",  "max(id) as id").groupBy("city_wh_id");
        List<Long> ids = orderMapper.selectList(qw).stream().map(Order::getId).collect(Collectors.toList());

        return orderMapper.selectVoBatchIds(ids);
    }

    @Override
    public List<OrderItem> selectListByIds(List<Long> itemIds) {
        return baseMapper.selectBatchIds(itemIds);
    }

    @Override
    public List<OrderItem> getByOrderId(Long orderId) {
        LambdaQueryWrapper<OrderItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderItem::getOrderId, orderId);
        wrapper.eq(OrderItem::getDelFlag, BanguoCommonConstant.notDelFlag);
        return baseMapper.selectList(wrapper);
    }

    @Override
    public List<RemoteSupBillVo> queryPlatformSubsidyByDate(RemoteSupAccTransQueryBo bo) {
        List<RemoteSupBillVo> result = new ArrayList<>();
        //查询订单详情
        List<OrderItem> orderItems = baseMapper.queryPlatformSubsidyByDate(bo,null,null);
        if (CollectionUtil.isNotEmpty(orderItems)) {
            List<Long> ids = orderItems.stream().map(OrderItem::getId).distinct().toList();
            //查询退款信息
            List<RefundProductDetail> refunds = refundProductDetailMapper.selectList(new LambdaQueryWrapper<RefundProductDetail>()
                    .eq(RefundProductDetail::getDelFlag, BanguoCommonConstant.notDelFlag)
                    .in(RefundProductDetail::getRefundStatus, RefundStatusEnum.IN_REFUND.getCode(), RefundStatusEnum.HAS_REFUND.getCode())
                    .in(RefundProductDetail::getOrderItemId, ids));
            Map<LocalDate, List<RefundProductDetail>> refundMap = refunds.stream().collect(Collectors.groupingBy(RefundProductDetail::getSaleDate));
            orderItems.stream().collect(Collectors.groupingBy(OrderItem::getSaleDate)).forEach((k, v) -> {
                RemoteSupBillVo billVo = new RemoteSupBillVo();
                billVo.setSaleDate(k);
                BigDecimal subsidyAmt;
                BigDecimal refundAmount = BigDecimal.ZERO;
                BigDecimal orderAmount = v.stream().map(OrderItem::getRegionSubsidyAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                BigDecimal skuSubsidyAmount = v.stream().map(OrderItem::getSkuSubsidyAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                if (refundMap.containsKey(k)) {
                    refundAmount = refundMap.get(k).stream().map(RefundProductDetail::getRefundSubsidyFreeAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                }
                subsidyAmt = orderAmount.add(skuSubsidyAmount).subtract(refundAmount);
                billVo.setSubsidyAmt(subsidyAmt);
                result.add(billVo);
            });
        }
        return result;
    }

    @Override
    public List<RemoteSupBillVo> queryPlatformSubsidyList(RemoteSupAccSubsidyBo subSidyQueryBo) {
        List<RemoteSupBillVo> result = new ArrayList<>();
        RemoteSupAccTransQueryBo bo = new RemoteSupAccTransQueryBo().setSupplierId(subSidyQueryBo.getSupplierId()).setSupplierDeptId(subSidyQueryBo.getSupplierDeptId());
        List<RemoteSupAccTransBo> trans = new ArrayList<>();
        RemoteSupAccTransBo transBo = new RemoteSupAccTransBo();
        trans.add(transBo.setTransDate(subSidyQueryBo.getTransDate()));
        bo.setTrans(trans);
        int i = (subSidyQueryBo.getPageNum() - 1) * subSidyQueryBo.getPageSize();
        List<OrderItem> orderItems = baseMapper.queryPlatformSubsidyByDate(bo, i, subSidyQueryBo.getPageSize());
        if (CollectionUtil.isNotEmpty(orderItems)) {
            List<Long> ids = orderItems.stream().map(OrderItem::getId).distinct().toList();
            //查询退款信息
            List<RefundProductDetail> refunds = refundProductDetailMapper.selectList(new LambdaQueryWrapper<RefundProductDetail>()
                    .eq(RefundProductDetail::getDelFlag, BanguoCommonConstant.notDelFlag)
                    .in(RefundProductDetail::getRefundStatus, RefundStatusEnum.IN_REFUND.getCode(), RefundStatusEnum.HAS_REFUND.getCode())
                    .in(RefundProductDetail::getOrderItemId, ids));
            Map<Long, List<RefundProductDetail>> refundMap = refunds.stream().collect(Collectors.groupingBy(RefundProductDetail::getOrderItemId));
            List<Long> supplierSkuIds = orderItems.stream().map(OrderItem::getSupplierSkuId).distinct().collect(Collectors.toList());
            RemoteQueryInfoListBo listBo = new RemoteQueryInfoListBo();
            listBo.setSupplierSkuIdList(supplierSkuIds);
            List<RemoteSupplierSkuInfoVo> remoteSupplierSkuInfoVos = remoteSupplierSkuService.queryInfoList(listBo);
            Map<Long, RemoteSupplierSkuInfoVo> map = remoteSupplierSkuInfoVos.stream().collect(Collectors.toMap(RemoteSupplierSkuInfoVo::getId, v -> v));
            for (OrderItem item : orderItems){
                RemoteSupBillVo billVo = new RemoteSupBillVo();
                billVo.setBuyerName(item.getBuyerName());
                billVo.setCount(item.getCount() - item.getRefundOutCount() - item.getRefundFewCount());
                billVo.setSubsidyAmt(item.getRegionSubsidyAmount().add(item.getSkuSubsidyAmount()));
                billVo.setSubsidyItemAmt(item.getRegionSubsidyAmount().add(item.getSkuSubsidyAmount()).divide(BigDecimal.valueOf(item.getCount())));
                billVo.setOrderCreateTime(item.getCreateTime());
                billVo.setBusinessNo(item.getOrderCode());
                billVo.setSkuName(item.getSpuName());
                billVo.setSupplierSkuId(item.getSupplierSkuId());
                if (refundMap.containsKey(item.getId())){
                    billVo.setSubsidyAmt(item.getRegionSubsidyAmount().add(item.getSkuSubsidyAmount()).subtract(refundMap.get(item.getId()).stream().map(RefundProductDetail::getRefundSubsidyFreeAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO)));
                }
                if (map.containsKey(item.getSupplierSkuId())){
                    RemoteSupplierSkuInfoVo sku = map.get(item.getSupplierSkuId());
                    String name =(cn.xianlink.common.core.utils.StringUtils.isEmpty(sku.getBrand()) ? sku.getBrand() : sku.getBrand() + "-")
                            + (cn.xianlink.common.core.utils.StringUtils.isEmpty(sku.getShortProducer()) ? sku.getShortProducer() : sku.getShortProducer() + "-")
                            + sku.getSpuName() + (cn.xianlink.common.core.utils.StringUtils.isNotEmpty(sku.getSpuStandards()) ? "【" + sku.getSpuStandards() + "】" : "");
                    billVo.setSkuName(name);
                }
                result.add(billVo);
            }
        }
        return result;
    }

    @Override
    public RemoteSupBillVo queryPlatformSubsidyTotal(RemoteSupAccSubsidyBo subSidyQueryBo) {
        return baseMapper.queryPlatformSubsidyTotal(subSidyQueryBo);
    }

    /**
     * 修改订单分润规则
     * @param ruleType
     * @param regionWhId
     * @param cityWhId
     * @param saleDate
     * @param rulerdRecordId
     */
    @Override
    public void fixProfitRule(Long ruleType, Long regionWhId, Long cityWhId, LocalDate saleDate, Long rulerdRecordId) {
        log.keyword("fixProfitRule").info("修改订单分润规则,ruleType:{},regionWhId:{},cityWhId:{},saleDate:{},rulerdRecordId:{}"
                ,  ruleType, regionWhId, cityWhId, saleDate, rulerdRecordId);
        baseMapper.update(new LambdaUpdateWrapper<OrderItem>()
                .eq(OrderItem::getRegionWhId, regionWhId)
                .eq(OrderItem::getCityWhId, cityWhId)
                .and((wrapper -> wrapper
                        .eq(OrderItem::getSaleDate, saleDate)
                        .or()
                        .isNull(Objects.equals(ruleType, WhProfitRuleTypeEnum.SERVICE_FEE.getCode())
                                ,OrderItem::getServiceRuleRecordId)
                        .isNull(Objects.equals(ruleType, WhProfitRuleTypeEnum.FREIGHT_FEE.getCode())
                                ,OrderItem::getFreightRuleRecordId)))
                .set(Objects.equals(ruleType, WhProfitRuleTypeEnum.SERVICE_FEE.getCode())
                        ,OrderItem::getServiceRuleRecordId, rulerdRecordId)
                .set(Objects.equals(ruleType, WhProfitRuleTypeEnum.FREIGHT_FEE.getCode())
                        ,OrderItem::getFreightRuleRecordId, rulerdRecordId));
    }

    @Override
    public List<RemoteRegionSubMaxPriceVo> selectMinPriceBySkuIds(Set<Long> supSkuIds) {
        return baseMapper.selectMinPriceBySkuIds(supSkuIds);
    }

    @Override
    public Map<String, Long> getSalesQuantityCount(Long skuId, LocalDate yesterday, LocalDate week, LocalDate saleDate) {
        SkuSalesQuantityCount salesQuantityCount = baseMapper.getSalesQuantityCount(skuId, yesterday, week, saleDate);
        long yesterdayCount = salesQuantityCount != null && salesQuantityCount.getYesterdaySalesQuantity() != null
                ? salesQuantityCount.getYesterdaySalesQuantity() : 0L;
        long weekCount = salesQuantityCount != null && salesQuantityCount.getWeekSalesQuantity() != null
                ? salesQuantityCount.getWeekSalesQuantity() : 0L;
        Map<String, Long> result = new HashMap<>();
        result.put("yesterday", yesterdayCount);
        result.put("week", weekCount);
        return result;
    }
}
