package cn.xianlink.order.controller.sup;

import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.common.api.enums.system.SysUserTypeEnum;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.domain.bo.pay.SupPaymentSearchBo;
import cn.xianlink.order.domain.order.bo.SupplierSkuSearchBo;
import cn.xianlink.order.domain.order.vo.CustomerOrderVO;
import cn.xianlink.order.domain.vo.order.SupPaymentOrderMiniListVo;
import cn.xianlink.order.domain.vo.pay.SupPaymentMiniListVo;
import cn.xianlink.order.service.IOrderPayService;
import cn.xianlink.order.service.IOrderService;
import cn.xianlink.order.service.IRefundRecordService;
import cn.xianlink.system.api.model.LoginUser;
import io.swagger.v3.oas.annotations.Operation;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.security.auth.login.LoginContext;
import java.util.List;

/**
 * 资金明细
 * 前端访问路由地址为:/order/sup/order
 * <AUTHOR>
 * @date 2024-05-28
 * @folder 供应商端(小程序)/订单/资金明细
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/order/sup/order")
@CustomLog
public class SOrderController extends BaseController {

    private final IOrderPayService orderPayService;

    private final IOrderService orderService;

    private final IRefundRecordService refundRecordService;

    /**
     * 订货收款列表
     * <AUTHOR> on 2024/6/28:19:18
     * @param searchBo
     * @return cn.xianlink.common.mybatis.core.page.TableDataInfo<cn.xianlink.order.domain.vo.pay.SupPaymentMiniListVo>
     */
    @PostMapping("/skuCollectionPage")
    public R<TableDataInfo<SupPaymentMiniListVo>> skuCollectionPage(@RequestBody SupPaymentSearchBo searchBo) {
        return R.ok(orderPayService.skuCollectionPage(searchBo));
    }

    /**
     * 商品收款订单列表
     * <AUTHOR> on 2024/6/28:19:22
     * @param searchBo 
     * @return java.util.List<cn.xianlink.order.domain.vo.order.SupPaymentOrderMiniListVo>
     */
    @PostMapping("/skuOrderList")
    public R<List<SupPaymentOrderMiniListVo>> skuOrderList(@RequestBody SupplierSkuSearchBo searchBo) {
        return R.ok(orderPayService.skuOrderList(searchBo));
    }

    /**
     * 退货退款列表
     * <AUTHOR> on 2024/6/28:19:18
     * @param searchBo
     * @return cn.xianlink.common.mybatis.core.page.TableDataInfo<cn.xianlink.order.domain.vo.pay.SupPaymentMiniListVo>
     */
    @PostMapping("/skuRefundPage")
    public R<TableDataInfo<SupPaymentMiniListVo>> skuRefundPage(@RequestBody SupPaymentSearchBo searchBo) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (ObjectUtil.isNotEmpty(loginUser) && ObjectUtil.isEmpty(searchBo.getSupplierDeptId())) {
            if (SysUserTypeEnum.SUPPLIER_USER.getType().equals(loginUser.getUserType()) && loginUser.getDeptId() != 0L) {
                searchBo.setSupplierDeptId(loginUser.getDeptId());
                log.keyword("SOrderController", "skuRefundPage","sku退款列表查询档口信息skuRefundPage")
                        .info("判责单查询档口信息，供应商用户查询，loginUser：{}, bo:{}", loginUser, searchBo);
            }
        } else {
            if (searchBo.getSupplierDeptId() == 0L) {
                searchBo.setSupplierDeptId(null);
            }
            log.keyword("SOrderController", "skuRefundPage","sku退款列表查询档口信息skuRefundPage")
                    .info("判责单查询档口信息seachBo:{}", searchBo);
        }
        return R.ok(refundRecordService.skuRefundPage(searchBo));
    }

    /**
     * 商品退款单列表
     * <AUTHOR> on 2024/6/28:19:22
     * @param searchBo
     * @return java.util.List<cn.xianlink.order.domain.vo.order.SupPaymentOrderMiniListVo>
     */
    @PostMapping("/skuRefundList")
    public R<List<SupPaymentOrderMiniListVo>> skuRefundList(@RequestBody SupplierSkuSearchBo searchBo) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (ObjectUtil.isNotEmpty(loginUser) && ObjectUtil.isEmpty(searchBo.getSupplierDeptId())) {
            if (SysUserTypeEnum.SUPPLIER_USER.getType().equals(loginUser.getUserType()) && loginUser.getDeptId() != 0L) {
                searchBo.setSupplierDeptId(loginUser.getDeptId());
                log.keyword("SOrderController", "skuRefundList","skuRefundList退款列表查询档口信息skuRefundList")
                        .info("退款列表查询档口信息，供应商用户查询，loginUser：{}, bo:{}", loginUser, searchBo);
            }
        } else {
            if (searchBo.getSupplierDeptId() == 0L) {
                searchBo.setSupplierDeptId(null);
            }
            log.keyword("SOrderController", "skuRefundList","sku退款列表查询档口信息skuRefundList")
                    .info("退款列表查询档口信息,searchBo:{}", searchBo);
        }
        return R.ok(refundRecordService.skuRefundList(searchBo));
    }


    @GetMapping("/customer_order_info/{orderCode}")
    @Operation(summary = "客户订单详细信息")
    public R<CustomerOrderVO> customerOrderInfo(@PathVariable("orderCode") String orderCode) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        Long supplierId  = loginUser.getRelationId();
        return R.ok(orderService.customerOrderInfo(orderCode,supplierId));
    }
}
