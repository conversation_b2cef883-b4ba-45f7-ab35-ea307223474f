package cn.xianlink.order.domain.bo.platform;

import cn.xianlink.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 客户端发票列表查询业务对象
 *
 * 注意：系统字段(createCode,createName,updateCode,updateName)由系统自动维护，
 * 不需要前端传参，也不需要手动设置
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ClientInvoiceListBo extends BaseEntity {

    /**
     * 客户ID（从登录用户获取，系统自动设置）
     */
    private Long customerId;

    /**
     * 发票状态列表
     * applied申请中, uploaded已上传, audited_approved审核通过, audited_rejected审核拒绝
     */
    private List<String> statusList;

    /**
     * 供应商ID列表（可选，用于筛选特定供应商）
     */
    private List<Long> supplierIds;

    /**
     * 供应商名称（模糊查询）
     */
    private String supplierName;

    /**
     * 发票号（模糊查询）
     */
    private String invoiceNumber;

    /**
     * 发票类型 (normal普票, special专票)
     */
    private String invoiceType;

    /**
     * 发票格式 (electronic电子, paper纸质)
     */
    private String invoiceFormat;

    /**
     * 申请开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date applyStartTime;

    /**
     * 申请结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date applyEndTime;

    /**
     * 开票开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date issueStartTime;

    /**
     * 开票结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date issueEndTime;

    /**
     * 最小发票金额
     */
    private BigDecimal minAmount;

    /**
     * 最大发票金额
     */
    private BigDecimal maxAmount;

    /**
     * 排序字段（applyDate, issueDate, invoiceAmount等）
     */
    private String orderBy;

    /**
     * 排序方向（asc, desc）
     */
    private String orderDirection;

    /**
     * 请求参数
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @TableField(exist = false)
    private Map<String, Object> params = new HashMap<>();
}
