package cn.xianlink.order.service.impl.im;

import cn.xianlink.basic.api.RemoteBasCustomerService;
import cn.xianlink.basic.api.RemoteSupplierService;
import cn.xianlink.basic.api.domain.vo.RemoteCustomerVo;
import cn.xianlink.basic.api.domain.vo.RemoteSupplierVo;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.order.config.TencentImProperties;
import cn.xianlink.order.domain.im.bo.ImBo;
import cn.xianlink.order.domain.im.vo.ImVo;
import cn.xianlink.order.enums.BizTypeEnum;
import cn.xianlink.order.service.im.IImStrategy;
import cn.xianlink.product.api.RemoteSupplierSkuService;
import cn.xianlink.product.api.domain.vo.RemoteSupplierSkuInfoVo;
import cn.xianlink.system.api.RemoteTencentImService;
import cn.xianlink.system.api.RemoteUserService;
import cn.xianlink.system.api.domain.bo.RemoteImUserBo;
import cn.xianlink.system.api.domain.bo.RemoteUserBo;
import cn.xianlink.system.api.model.LoginUser;
import jodd.util.StringUtil;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static cn.xianlink.system.api.domain.bo.RemoteImUserBo.buildImUser;

/**
 * 产品(单聊)
 */
@RequiredArgsConstructor
@Service
@CustomLog
public class ProductImUserImpl implements IImStrategy {

    @DubboReference
    private final RemoteSupplierService supplierService;
    @DubboReference
    private final RemoteUserService userService;
    @DubboReference
    private final RemoteTencentImService tencentImService;
    @DubboReference
    private final RemoteSupplierSkuService remoteSupplierSkuService;
    @DubboReference
    private final RemoteBasCustomerService customerService;

    private final TencentImProperties tencentImProperties;

    /**
     * 产品和供应商聊
     * @param req 业务数据
     * @return
     */
    @Override
    public ImVo handle(ImBo req) {
        LoginUser user = Optional.ofNullable(LoginHelper.getLoginUser())
                .orElseThrow(() -> new ServiceException("用户未登入"));
        // 查询产品
        RemoteSupplierSkuInfoVo remoteSkuVo = Optional.ofNullable(remoteSupplierSkuService.getById(Long.valueOf(req.getBizId())))
                .orElseThrow(() ->new ServiceException("产品不存在"));
        // 查询供应商
        RemoteSupplierVo supplier = Optional.ofNullable(supplierService.querySupplierById(remoteSkuVo.getSupplierId()))
                .orElseThrow(() -> new ServiceException("供应商不存在"));
        // 查询用户
        RemoteUserBo supplierUser = Optional.ofNullable(userService.getUserByUserCode(supplier.getAdminCode()))
                .orElseThrow(() -> new ServiceException("供应商用户不存在"));

        // 查询客户表超管代码（用户编码）
        RemoteCustomerVo customer = Optional.ofNullable(customerService.getCustomerByAdminCode(user.getUserCode()))
                .orElseThrow(() -> new ServiceException("商家不存在"));

        List<RemoteImUserBo> userList = new ArrayList<>();
        String supplierName = supplier.getAlias();
        if (StringUtil.isBlank(supplierName)){
            supplierName = String.format("商铺：%s",supplier.getSimpleCode());
        }
        userList.add(buildImUser(supplier.getAdminCode(), supplierName));
        userList.add(buildImUser(user.getUserCode(), customer.getName()));
        try {
            // 创建单聊
            tencentImService.importUser(userList);
            // 发送欢迎语
            if(Objects.equals(tencentImProperties.getSupplierWelcomeMsg(), 1)){
                tencentImService.sendSingleChatMsg(supplier.getAdminCode(), user.getUserCode(), String.format("我是%s负责人，电话：%s", supplierName, supplierUser.getPhoneNo()));
            }
        } catch (Exception e) {
            // 记录日志并抛出异常
            log.error("IM 用户导入失败", e);
            throw new ServiceException("IM 用户导入失败");
        }
        return new ImVo( null, supplier.getAdminCode());
    }

    @Override
    public Integer getType() {
        return BizTypeEnum.PRODUCT.getType();
    }
}