package cn.xianlink.order.service;

import cn.xianlink.order.domain.Invoice;
import cn.xianlink.order.domain.bo.InvoiceApplyBo;
import cn.xianlink.order.domain.bo.InvoiceQueryBo;
import cn.xianlink.order.domain.vo.InvoiceOverviewVo;
import cn.xianlink.order.domain.vo.InvoiceQueryVo;
import cn.xianlink.order.domain.vo.InvoiceVo;
import cn.xianlink.order.domain.bo.InvoiceBo;
import cn.xianlink.order.domain.bo.platform.*;
import cn.xianlink.order.domain.vo.platform.*;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 发票Service接口
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface IInvoiceService {

    /**
     * 查询发票
     */
    InvoiceVo queryById(Long id);

    /**
     * 查询发票列表
     */
    TableDataInfo<InvoiceVo> queryPageList(InvoiceBo bo, PageQuery pageQuery);

    /**
     * 查询发票列表
     */
    List<InvoiceVo> queryList(InvoiceBo bo);

    /**
     * 新增发票
     */
    Boolean insertByBo(InvoiceBo bo);

    /**
     * 修改发票
     */
    Boolean updateByBo(InvoiceBo bo);

    /**
     * 校验并批量删除发票信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    // ==================== 客户端发票申请相关接口 ====================

    /**
     * 查询客户端未开票列表
     * 统计指定时间范围内客户的订单项数据，按供应商维度分组
     *
     * @param bo 查询条件
     * @param pageQuery 分页参数
     * @return 未开票列表
     */
    TableDataInfo<ClientUnInvoicedVo> queryClientUnInvoicedList(ClientUnInvoicedBo bo, PageQuery pageQuery);

    /**
     * 查询客户端发票列表（开票中和已开票）
     * 根据发票状态返回客户申请的发票列表
     *
     * @param bo 查询条件
     * @param pageQuery 分页参数
     * @return 发票列表
     */
    TableDataInfo<InvoiceVo> queryClientInvoiceList(ClientInvoiceListBo bo, PageQuery pageQuery);

    /**
     * 客户端申请开票
     * 基于未开票列表接口返回的数据生成发票，支持批量选择
     *
     * @param bo 申请开票参数
     * @return 申请结果
     */
    Boolean clientApplyInvoice(ClientApplyInvoiceBo bo);

    /**
     * 查询客户端发票详情
     * 返回发票金额、类型、抬头、发票文件、纸质发票物流信息、发票汇总信息、商品明细
     *
     * @param invoiceId 发票ID
     * @param customerId 客户ID
     * @return 发票详情
     */
    ClientInvoiceDetailVo queryClientInvoiceDetail(Long invoiceId, Long customerId);

    /**
     * 更新客户端发票邮箱地址
     * 允许编辑发票详情中的邮箱地址
     *
     * @param bo 更新参数
     * @return 更新结果
     */
    Boolean updateClientInvoiceEmail(ClientUpdateInvoiceEmailBo bo);

    /**
     * 分页查询待开发票列表
     */
    TableDataInfo<InvoiceQueryVo> queryWaitInvoicePage(InvoiceQueryBo bo);

    /**
     * 查询待开发票详情
     */
    InvoiceQueryVo getWaitInvoiceDetail(InvoiceQueryBo bo);

    /**
     * 申请开票
     */
    void applyInvoice(InvoiceApplyBo bo);

    /**
     * 勾选待开发票的概况
     */
    InvoiceOverviewVo queryInvoiceOverview(InvoiceQueryBo bo);
}
