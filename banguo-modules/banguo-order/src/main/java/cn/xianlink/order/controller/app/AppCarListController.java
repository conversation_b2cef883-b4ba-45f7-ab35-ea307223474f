package cn.xianlink.order.controller.app;

import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.domain.vo.CarListVo;
import cn.xianlink.order.service.ICarListService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 车型表应用端
 *
 * <AUTHOR>
 * @date 2025-01-30
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/order/carList")
@Tag(name = "车型表应用端", description = "车型表应用端接口")
public class AppCarListController extends BaseController {

    private final ICarListService carListService;

    /**
     * 根据总仓ID查询车型列表
     */
    @Operation(summary = "根据总仓ID查询车型列表")
    @GetMapping("/listByRegionWh/{regionWhId}")
    public R<List<CarListVo>> listByRegionWh(@NotNull(message = "总仓ID不能为空")
                                             @Parameter(description = "总仓ID")
                                             @PathVariable Long regionWhId) {
        List<CarListVo> list = carListService.queryByRegionWhId(regionWhId);
        return R.ok(list);
    }

    /**
     * 获取车型详细信息
     */
    @Operation(summary = "获取车型详细信息")
    @GetMapping("/{id}")
    public R<CarListVo> getInfo(@NotNull(message = "主键不能为空")
                                @Parameter(description = "主键")
                                @PathVariable Long id) {
        return R.ok(carListService.queryById(id));
    }

}
