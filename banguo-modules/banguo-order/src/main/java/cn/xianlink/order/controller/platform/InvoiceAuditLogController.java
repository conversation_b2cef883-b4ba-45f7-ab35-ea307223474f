package cn.xianlink.order.controller.platform;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.log.annotation.Log;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.validate.AddGroup;
import cn.xianlink.common.core.validate.EditGroup;
import cn.xianlink.common.log.enums.BusinessType;
import cn.xianlink.common.excel.utils.ExcelUtil;
import cn.xianlink.order.domain.vo.InvoiceAuditLogVo;
import cn.xianlink.order.domain.bo.InvoiceAuditLogBo;
import cn.xianlink.order.service.IInvoiceAuditLogService;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;

/**
 * 发票审核日志
 * 前端访问路由地址为:/order/invoiceAuditLog
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/order/platform/invoiceAuditLog")
public class InvoiceAuditLogController extends BaseController {

    private final IInvoiceAuditLogService invoiceAuditLogService;

    /**
     * 查询发票审核日志列表
     */
    @SaCheckPermission("order:invoiceAuditLog:list")
    @GetMapping("/list")
    public TableDataInfo<InvoiceAuditLogVo> list(InvoiceAuditLogBo bo, PageQuery pageQuery) {
        return invoiceAuditLogService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出发票审核日志列表
     */
    @SaCheckPermission("order:invoiceAuditLog:export")
    @Log(title = "发票审核日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(InvoiceAuditLogBo bo, HttpServletResponse response) {
        List<InvoiceAuditLogVo> list = invoiceAuditLogService.queryList(bo);
        ExcelUtil.exportExcel(list, "发票审核日志", InvoiceAuditLogVo.class, response);
    }

    /**
     * 获取发票审核日志详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("order:invoiceAuditLog:query")
    @GetMapping("/{id}")
    public R<InvoiceAuditLogVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(invoiceAuditLogService.queryById(id));
    }

    /**
     * 新增发票审核日志
     */
    @SaCheckPermission("order:invoiceAuditLog:add")
    @Log(title = "发票审核日志", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody InvoiceAuditLogBo bo) {
        return toAjax(invoiceAuditLogService.insertByBo(bo));
    }

    /**
     * 修改发票审核日志
     */
    @SaCheckPermission("order:invoiceAuditLog:edit")
    @Log(title = "发票审核日志", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody InvoiceAuditLogBo bo) {
        return toAjax(invoiceAuditLogService.updateByBo(bo));
    }

    /**
     * 删除发票审核日志
     *
     * @param ids 主键串
     */
    @SaCheckPermission("order:invoiceAuditLog:remove")
    @Log(title = "发票审核日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(invoiceAuditLogService.deleteWithValidByIds(List.of(ids), true));
    }
}
