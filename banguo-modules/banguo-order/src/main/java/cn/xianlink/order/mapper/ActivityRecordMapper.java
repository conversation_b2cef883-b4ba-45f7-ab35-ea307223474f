package cn.xianlink.order.mapper;

import cn.xianlink.common.mybatis.core.mapper.BaseMapperPlus;
import cn.xianlink.order.api.bo.RemoteMkOrderDataStatBo;
import cn.xianlink.order.api.vo.RemoteMkDataStatVo;
import cn.xianlink.order.domain.ActivityRecord;
import cn.xianlink.order.domain.order.vo.OrderVo;
import cn.xianlink.order.domain.vo.ActivityRecordVo;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 活动记录Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-15
 */
public interface ActivityRecordMapper extends BaseMapperPlus<ActivityRecord, ActivityRecordVo> {

    List<ActivityRecordVo> getActivityRecords(@Param("activityIds") List<Long> activityIds, @Param("type") Integer type);

    RemoteMkDataStatVo getMkDataStat(@Param("activityId") Long activityId, @Param("type") Integer type,
                                     @Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate
            ,@Param("salesStartDate") LocalDateTime salesStartDate,@Param("salesEndDate") LocalDateTime salesEndDate);

    Page<OrderVo> getActivityLeftOrder(@Param("bo") RemoteMkOrderDataStatBo bo, @Param("page") Page<OrderVo> page);


    /**
     * 营销活动数据统计, 统计缺货、少货以及取消的订单
     * @param activityId
     * @param startDate
     * @param endDate
     * @return
     */
    RemoteMkDataStatVo mkDataStatic(@Param("activityId") Long activityId,@Param("type") Integer type, @Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate
            ,@Param("salesStartDate") LocalDateTime salesStartDate, @Param("salesEndDate") LocalDateTime salesEndDate);

    /**
     * 营销活动数据统计, 统计优惠
     * @param activityId
     * @param type
     * @param startDate
     * @param endDate
     * @param o
     * @param o1
     * @return
     */
    RemoteMkDataStatVo mkDataFreeStatic(Long activityId, Integer type, LocalDateTime startDate, LocalDateTime endDate, Object o, Object o1);
}