package cn.xianlink.order.controller.sup;

import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.common.api.enums.order.AccountTypeStatusEnum;
import cn.xianlink.common.api.enums.system.SysUserTypeEnum;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.order.domain.bo.refundRecord.DifferenceRefundPageBO;
import cn.xianlink.order.domain.bo.refundRecord.RefundPageBO;
import cn.xianlink.order.domain.vo.common.CommonStatusCountVO;
import cn.xianlink.order.domain.vo.refundRecord.*;
import cn.xianlink.order.service.IRefundRecordService;
import cn.xianlink.system.api.model.LoginUser;
import io.swagger.annotations.ApiOperation;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 供应商小程序-订单退款&&差额退款
 *
 * <AUTHOR>
 * @date 2024-05-25
 */
@Validated
@RequiredArgsConstructor
@RestController("supOrderRefundController")
@RequestMapping("/order/sup/refund")
@CustomLog
public class OrderRefundController extends BaseController {

    private final IRefundRecordService refundRecordService;

    @PostMapping("/difPage")
    @ApiOperation(value = "分页查询差额退款列表")
    public R<TableDataInfo<DifferenceRefundVO>> difPage(@RequestBody DifferenceRefundPageBO bo){
        bo.setSupFlag(true);
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (ObjectUtil.isNotEmpty(loginUser) && ObjectUtil.isEmpty(bo.getSupplierDeptId())) {
            if (SysUserTypeEnum.SUPPLIER_USER.getType().equals(loginUser.getUserType()) && loginUser.getDeptId() != 0L) {
                bo.setSupplierDeptId(loginUser.getDeptId());
                log.keyword("OrderRefundController", "difPage","difPage退款列表查询档口信息difPage")
                        .info("退款列表查询档口信息，供应商用户查询，loginUser：{}, bo:{}", loginUser, bo);
            }
        } else {
            if (bo.getSupplierDeptId() == 0L) {
                bo.setSupplierDeptId(null);
            }
            log.keyword("OrderRefundController", "difPage","difPage退款列表查询档口信息difPage")
                    .info("退款列表查询档口信息,bo:{}", bo);
        }
        if (bo.getSupplierId() == null && LoginHelper.getLoginUser().getRelationId() == null) {
            return R.ok(TableDataInfo.build());
        }else {
            bo.setSupplierId(LoginHelper.getLoginUser().getRelationId());
        }
        return R.ok(refundRecordService.difPage(bo));
    }

    @PostMapping("/refundPage")
    @ApiOperation(value = "分页查询退货列表")
    public R<TableDataInfo<SupRefundPageVO>> refundPage(@RequestBody RefundPageBO bo){
        LoginUser loginUser = LoginHelper.getLoginUser();
        bo.setSupplierId(loginUser.getRelationId());
        if (ObjectUtil.isNotEmpty(loginUser) && ObjectUtil.isEmpty(bo.getSupplierDeptId())) {
            if (SysUserTypeEnum.SUPPLIER_USER.getType().equals(loginUser.getUserType()) && loginUser.getDeptId() != 0L) {
                bo.setSupplierDeptId(loginUser.getDeptId());
                log.keyword("OrderRefundController", "refundPage","供应商查询退货退款列表查询档口信息refundPage")
                        .info("供应商查询退货退款列表查询档口信息，供应商用户查询，loginUser：{}, bo:{}", loginUser, bo);
            }
        } else {
            if (bo.getSupplierDeptId() == 0L) {
                bo.setSupplierDeptId(null);
            }
            log.keyword("OrderRefundController", "refundPage","供应商查询退货退款列表查询档口信息refundPage")
                    .info("供应商查询退货退款列表查询档口信息,bo:{}", bo);
        }
        return R.ok(refundRecordService.supRefundPage(bo));
    }

    @GetMapping("/getRefundById/{supplierSkuId}")
    @ApiOperation(value = "获取退货明细详情", notes = "supplierSkuId为供应商销售批次商品id(这个供应商下唯一)")
    public R<SupRefundVO> getRefundById(@PathVariable("supplierSkuId") Long supplierSkuId){
        return R.ok(refundRecordService.getSupRefundById(supplierSkuId));
    }

    @GetMapping("/getDifById/{id}")
    @ApiOperation(value = "获取差额退款单项详情")
    public R<DifferenceRefundDetailVO> getDifById(@PathVariable("id") Long id){
        return R.ok(refundRecordService.getDifById(id));
    }

    @GetMapping("/getDifRefundCount")
    @ApiOperation(value = "获取差额退款单各状态数量")
    public R<List<CommonStatusCountVO>> getDifRefundCount(){
        return R.ok(refundRecordService.getRefundCount(AccountTypeStatusEnum.SUPPLIER.getCode()));
    }

}
