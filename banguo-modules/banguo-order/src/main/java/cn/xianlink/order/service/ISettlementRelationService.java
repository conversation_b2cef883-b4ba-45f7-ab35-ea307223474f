package cn.xianlink.order.service;

import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.order.domain.bo.SettlementRelationAddBo;
import cn.xianlink.order.domain.bo.SettlementRelationBo;
import cn.xianlink.order.domain.bo.SettlementRelationPageBo;
import cn.xianlink.order.domain.vo.SettlementRelationOrderInfoVo;
import cn.xianlink.order.domain.vo.SettlementRelationVo;

import java.util.Collection;
import java.util.List;

/**
 * 结算关联Service接口
 *
 * <AUTHOR>
 * @date 2024-11-07
 */
public interface ISettlementRelationService {

    /**
     * 查询结算关联
     */
    SettlementRelationVo queryById(Long id);

    /**
     * 查询结算关联列表
     */
    TableDataInfo<SettlementRelationVo> queryPageList(SettlementRelationBo bo, PageQuery pageQuery);
    /**
     * 分页查询城市仓结算
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<SettlementRelationOrderInfoVo> getPage(SettlementRelationPageBo bo, PageQuery pageQuery);

    /**
     * 查询结算关联列表
     */
    List<SettlementRelationVo> queryList(SettlementRelationBo bo);

    /**
     * 新增结算关联
     */
    Boolean insertByBo(SettlementRelationAddBo bo);

    /**
     * 修改结算关联
     */
    Boolean updateByBo(SettlementRelationBo bo);

    /**
     * 校验并批量删除结算关联信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

}
