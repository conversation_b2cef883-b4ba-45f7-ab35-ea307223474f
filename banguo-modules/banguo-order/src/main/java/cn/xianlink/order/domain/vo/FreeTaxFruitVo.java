package cn.xianlink.order.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: 发票详情-免税水果
 * @date 2025/8/1 18:41
 */
@Data
public class FreeTaxFruitVo implements Serializable {
    /**
     * skuId
     */
    private Long skuId;

    /**
     * 商品名称
     */
    private String spuName;

    /**
     * 二级分类ID
     */
    private Long categoryIdLevel2;

    /**
     * 免税金额(元)
     */
    private BigDecimal taxFreeAmount;

    /**
     * 免税重量（斤）
     */
    private BigDecimal taxFreeWeight;

    /**
     * 剩余免税重量（斤）
     */
    private BigDecimal remainTaxFreeWeight;

    /**
     * 免税资质id
     */
    private Long dutyFreeGoodsId;
}
