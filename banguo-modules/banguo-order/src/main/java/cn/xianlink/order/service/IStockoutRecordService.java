package cn.xianlink.order.service;

import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.order.domain.StockoutRecord;
import cn.xianlink.order.domain.StockoutRecordDetail;
import cn.xianlink.order.domain.bo.stockOut.BatchCreateStockoutBO;
import cn.xianlink.order.domain.bo.stockOut.StockoutCreateBO;
import cn.xianlink.order.domain.bo.stockOut.StockoutPageBO;
import cn.xianlink.order.domain.bo.stockOut.StockoutSearchBO;
import cn.xianlink.order.domain.bo.stockOut.StockoutUpdateBO;
import cn.xianlink.order.domain.order.vo.QueryDistributionVo;
import cn.xianlink.order.domain.todo.bo.TodoBo;
import cn.xianlink.order.domain.todo.vo.TodoGoodsVo;
import cn.xianlink.order.domain.vo.ToDosCommentVO;
import cn.xianlink.order.domain.vo.common.CommonStatusCountVO;
import cn.xianlink.order.domain.vo.receiveGoods.ReceiveGoodsDetailVO;
import cn.xianlink.order.domain.vo.stockOut.StockoutDetailVO;
import cn.xianlink.order.domain.vo.stockOut.StockoutPageVO;
import cn.xianlink.order.domain.vo.stockOut.StockoutRecordDetailVO;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 缺货少货单Service接口
 *
 * <AUTHOR>
 * @date 2024-05-25
 */
public interface IStockoutRecordService {

    /**
     * 缺货少货单分页查询
     **/
    TableDataInfo<StockoutPageVO> stockoutPage(StockoutPageBO bo);

    /**
     * 缺货少货单详情
     **/
    StockoutDetailVO getStockoutById(Long id);

    /**
     * 缺货少货单详情列表
     **/
    List<StockoutDetailVO> listStockoutByIds(List<Long> ids);

    /**
     * 缺货少货单关联订单
     **/
    List<StockoutRecordDetailVO> getStockoutOrder(Long id,Integer type);

    /**
     * 缺货少货单各状态数量
     **/
    List<CommonStatusCountVO> getStockoutCount(Integer type);

    /**
     * 获取未确认缺货少货单数量
     * @see cn.xianlink.common.api.enums.order.ToDosEnum
     * @param type 类型 31缺货 63少货
     * @param id 采购员id  城市仓id
     */
    Long getUnConfirmStockOutCount(Integer type, Long id);

    /**
     * 获取未完成分货列表
     * @see cn.xianlink.common.api.enums.order.ToDosEnum
     * @param type 类型 31缺货 63少货
     * @param id 采购员id  城市仓id
     */
    List<ToDosCommentVO> getUnConfirmStockOutList(Integer type, Long id);

    void createStockoutSingle(BatchCreateStockoutBO bo);

    /**
     * 根据code查缺货少货单
     * @param code
     * @return
     */
    StockoutDetailVO getStockoutByCode(String code);

    /**
     * 计算该次少货会退多少钱
     */
    BigDecimal getConfirmRefundAmount(List<BatchCreateStockoutBO> boList);

    /**
     * 确认缺货少货单
     */
    void stockoutConfirm(Long id);

    /**
     * 确认少货时
     * <AUTHOR> on 2024/12/2:16:49
     * @param stockoutRecord
     * @param stockoutRecordDetailList
     * @return void
     */
    void stockoutCreateBlame(StockoutRecord stockoutRecord, List<StockoutRecordDetail> stockoutRecordDetailList);

    /**
     * 作废缺货少货单
     */
    void stockoutInvalid(Long id);

    /**
     * 修改缺货少货单
     */
    void updateStockout(StockoutUpdateBO bo);

    /**
     * 根据itemId占用结算金额
     * <AUTHOR> on 2024/10/19:11:45
     * @param details
     * @return void
     */
    Boolean stockoutOccupy(List<StockoutRecordDetail> details);

    /**
     * 根据itemId 查询缺货少货单
     * @param orderItemIds
     * @return
     */
    List<StockoutRecordDetail> getStockoutByItemIdList(List<Long> orderItemIds, List<Integer> statusList);

    /**
     * 根据客户id和自然日获取对应日期数据
     * @param customerId
     * @return
     */
    List<LocalDate> getSaleDateList(Long customerId);

    /**
     * 根据销售日期和客户id查询少货商品
     * @param bo
     * @return
     */
    List<ReceiveGoodsDetailVO> listBySaleDateAndCustomerId(StockoutSearchBO bo);

    /**
     * 新增少货单
     * @param bo
     */
    void createSingleStockout(StockoutCreateBO bo);

    /**
     * 获取新增少货单里面的最大少货件数
     * @param supplierSkuId
     * @param customerId
     * @return
     */
    Integer getMaxStockCount(Long supplierSkuId, Long customerId);
    /**
     * 产生缺货单方法
     * 销售批次商品 数量
     * 缺货单创建维度: 销售批次商品-城市仓-物流线
     */
    void stockout(BatchCreateStockoutBO bo, List<QueryDistributionVo> orderList);

    /**
     * 批量产生少货单
     * 少货是明确知道少谁的 少多少,直接根据销售批次商品创建即可
     * 少货从城市仓发起的  按批次商品创建少货单都是一个城市仓,只需考虑物流线不同  维度:批次商品+物流线
     */
    void createLessGoods(List<BatchCreateStockoutBO> boList, List<QueryDistributionVo> orderList);

    /**
     * 根据销售日期查下客户id列表
     * @param bo
     * @return
     */
    List<Long> getCustomerList(StockoutSearchBO bo);

    /**
     * 根据bill
     * @param billCode
     * @return
     */
    StockoutDetailVO getStockoutOrderCodeByCode(String billCode);
}
