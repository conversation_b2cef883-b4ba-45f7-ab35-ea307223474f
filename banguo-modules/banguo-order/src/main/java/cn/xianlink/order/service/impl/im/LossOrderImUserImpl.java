package cn.xianlink.order.service.impl.im;

import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.basic.api.RemoteBasCustomerService;
import cn.xianlink.basic.api.RemoteCityWhService;
import cn.xianlink.basic.api.RemoteSupplierService;
import cn.xianlink.basic.api.domain.vo.RemoteCityWhVo;
import cn.xianlink.basic.api.domain.vo.RemoteCustomerVo;
import cn.xianlink.basic.api.domain.vo.RemoteSupplierVo;
import cn.xianlink.common.api.enums.order.AccountTypeStatusEnum;
import cn.xianlink.common.api.enums.order.ReportLossStatusEnum;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.order.domain.OrderItem;
import cn.xianlink.order.domain.ReportLossOrder;
import cn.xianlink.order.domain.im.bo.ImBo;
import cn.xianlink.order.domain.im.vo.ImVo;
import cn.xianlink.order.enums.BizTypeEnum;
import cn.xianlink.order.mapper.OrderItemMapper;
import cn.xianlink.order.mapper.ReportLossOrderMapper;
import cn.xianlink.order.service.im.IImStrategy;
import cn.xianlink.system.api.RemoteImAfterSalesService;
import cn.xianlink.system.api.RemoteTencentImService;
import cn.xianlink.system.api.RemoteUserService;
import cn.xianlink.system.api.domain.bo.RemoteImGroupAddBo;
import cn.xianlink.system.api.domain.bo.RemoteImUserBo;
import cn.xianlink.system.api.domain.bo.RemoteUserBo;
import cn.xianlink.system.api.domain.vo.RemoteImAfterSalesVo;
import cn.xianlink.system.api.model.LoginUser;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import jodd.util.StringUtil;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static cn.xianlink.system.api.domain.bo.RemoteImGroupAddBo.buildImGroup;
import static cn.xianlink.system.api.domain.bo.RemoteImUserBo.buildImUser;

/**
 * 报损单实现类(群聊)
 */
@RequiredArgsConstructor
@Service
@CustomLog
public class LossOrderImUserImpl implements IImStrategy {

    private final ReportLossOrderMapper lossOrderMapper;
    @DubboReference
    private final RemoteUserService userService;
    @DubboReference
    private final RemoteTencentImService tencentImService;
    @DubboReference
    private final RemoteCityWhService cityWhService;
    @DubboReference
    private final RemoteImAfterSalesService imAfterSalesService;
    @DubboReference
    private final RemoteSupplierService supplierService;
    @DubboReference
    private final RemoteBasCustomerService customerService;

    @Override
    public ImVo handle(ImBo req) {
        LoginUser user = Optional.ofNullable(LoginHelper.getLoginUser())
                .orElseThrow(() -> new ServiceException("用户未登入"));
        // 查询报损单对象
        ReportLossOrder lossOrder = Optional.ofNullable(lossOrderMapper.selectByNo(req.getBizId()))
                .orElseThrow(() -> new ServiceException("报损单不存在"));


        LocalDateTime localDate = LocalDateTime.now().minusDays(7);
        LocalDateTime createTime = lossOrder.getCreateTime().toInstant()
                .atZone(ZoneId.systemDefault()).toLocalDateTime();
        if (localDate.isAfter(createTime)) {
            throw new ServiceException("超过7天时间不能发起聊天");
        }

        // 查询城市仓表超管代码（用户编码）
        RemoteCityWhVo cityWh = Optional.ofNullable(cityWhService.queryById(lossOrder.getCityWhId()))
                .orElseThrow(() -> new ServiceException("城市仓不存在"));
        // 查询供应商
        RemoteSupplierVo supplier = Optional.ofNullable(supplierService.querySupplierById(lossOrder.getSupplierId()))
                .orElseThrow(() -> new ServiceException("供应商不存在"));

        // 查询客户表超管代码（用户编码）
        RemoteCustomerVo customer = Optional.ofNullable(customerService.getById(lossOrder.getCustomerId()))
                .orElseThrow(() -> new ServiceException("商家不存在"));

        List<RemoteImUserBo> userList = new ArrayList<>();
        userList.add(buildImUser(user.getUserCode(), customer.getName()));
        userList.add(buildImUser(cityWh.getAdminCode(), cityWh.getName()));

        String supplierName = supplier.getAlias();
        if (StringUtil.isBlank(supplierName)){
            supplierName = String.format("商铺：%s",supplier.getSimpleCode());
        }

        userList.add(buildImUser(supplier.getAdminCode(), supplierName));

        //申诉中和已介入 加入配置的售后人员
        if (ObjectUtil.equals(ReportLossStatusEnum.APPEAL.getCode(), lossOrder.getLossStatus())
                || ObjectUtil.equals(ReportLossStatusEnum.INTERVENTION.getCode(), lossOrder.getLossStatus())) {
            if(ObjectUtil.isNotEmpty(lossOrder.getRegionWhId())) {
                List<RemoteImAfterSalesVo> list = imAfterSalesService.getByUserId(lossOrder.getRegionWhId());
                if (CollectionUtils.isNotEmpty(list)) {
                    list.forEach(vo -> userList.add(buildImUser(vo.getUserCode(), vo.getUserName())));
                }
            }
        }
        //群名
        LocalDateTime now = LocalDateTime.now();
        String monthDay = now.format(DateTimeFormatter.ofPattern("MMdd"));
        String groupName = String.format("%s%s售后专属群", monthDay, customer.getName());
        String groupID;
        try {
            RemoteImGroupAddBo bo = buildImGroup(req.getBizType(), req.getBizId(), groupName, userList);
            // 创建群聊
            groupID = tencentImService.createGroup(bo);
        } catch (Exception e) {
            // 记录日志并抛出异常
            log.error("创建IM群组失败", e);
            throw new ServiceException("创建IM群组失败");
        }
        return new ImVo(groupID, null);
    }


    @Override
    public Integer getType() {
        return BizTypeEnum.LOSSORDER.getType();
    }
}