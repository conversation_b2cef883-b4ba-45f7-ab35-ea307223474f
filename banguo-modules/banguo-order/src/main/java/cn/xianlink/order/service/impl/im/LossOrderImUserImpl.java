package cn.xianlink.order.service.impl.im;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.basic.api.RemoteBasCustomerService;
import cn.xianlink.basic.api.RemoteBasImAfterSalesService;
import cn.xianlink.basic.api.RemoteCityWhService;
import cn.xianlink.basic.api.RemoteSupplierService;
import cn.xianlink.basic.api.domain.vo.RemoteBasImUserVo;
import cn.xianlink.basic.api.enums.ImAfterSalesBizTypeEnum;
import cn.xianlink.common.api.enums.order.BlameStatusEnum;
import cn.xianlink.common.api.enums.order.ReportLossStatusEnum;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.order.domain.ReportLossOrder;
import cn.xianlink.order.domain.im.bo.ImBo;
import cn.xianlink.order.domain.im.vo.ImVo;
import cn.xianlink.order.enums.BizTypeEnum;
import cn.xianlink.order.mapper.ReportLossOrderMapper;
import cn.xianlink.order.service.im.IImStrategy;
import cn.xianlink.system.api.RemoteTencentImService;
import cn.xianlink.system.api.RemoteUserService;
import cn.xianlink.system.api.domain.bo.RemoteImGroupAddBo;
import cn.xianlink.system.api.domain.bo.RemoteImUserBo;
import cn.xianlink.system.api.model.LoginUser;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static cn.xianlink.system.api.domain.bo.RemoteImGroupAddBo.buildImGroup;

/**
 * 报损单实现类(群聊)
 */
@RequiredArgsConstructor
@Service
@CustomLog
public class LossOrderImUserImpl implements IImStrategy {

    private final ReportLossOrderMapper lossOrderMapper;
    @DubboReference
    private final RemoteUserService userService;
    @DubboReference
    private final RemoteTencentImService tencentImService;
    @DubboReference
    private final RemoteCityWhService cityWhService;
    /*    @DubboReference
        private final RemoteImAfterSalesService imAfterSalesService;*/
    @DubboReference
    private final RemoteSupplierService supplierService;
    @DubboReference
    private final RemoteBasCustomerService customerService;
    @DubboReference
    private final RemoteBasImAfterSalesService afterSalesService;

    @Override
    public ImVo handle(ImBo req) {
        LoginUser user = Optional.ofNullable(LoginHelper.getLoginUser())
                .orElseThrow(() -> new ServiceException("用户未登入"));
        // 查询报损单对象
        ReportLossOrder lossOrder = Optional.ofNullable(lossOrderMapper.selectByNo(req.getBizId()))
                .orElseThrow(() -> new ServiceException("报损单不存在"));

        LocalDateTime localDate = LocalDateTime.now().minusDays(7);
        LocalDateTime createTime = Optional.ofNullable(lossOrder.getCreateTime())
                .map(date -> date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime())
                .orElseThrow(() -> new ServiceException("创建时间为空，无法判断时效"));

        if (localDate.isAfter(createTime)) {
            throw new ServiceException("非常抱歉，由于您的报损单已超过7天，暂时无法为您开启专属售后群");
        }

        Map<String, Long> bizMap = new HashMap<>();
        bizMap.put(ImAfterSalesBizTypeEnum.PLACE2.getType(), lossOrder.getPlaceIdLevel2());
        bizMap.put(ImAfterSalesBizTypeEnum.PLACE1.getType(), lossOrder.getPlaceId());
        bizMap.put(ImAfterSalesBizTypeEnum.CITY.getType(), lossOrder.getCityWhId());
        bizMap.put(ImAfterSalesBizTypeEnum.REGION.getType(), lossOrder.getRegionWhId());
        bizMap.put(ImAfterSalesBizTypeEnum.DEPT.getType(), lossOrder.getSupplierDeptId());
        bizMap.put(ImAfterSalesBizTypeEnum.SUPPLIER.getType(), lossOrder.getSupplierId());
        List<RemoteBasImUserVo> userList = afterSalesService.queryImUserList(bizMap);
        if (CollectionUtils.isEmpty(userList)) {
            throw new ServiceException("无售后人员");
        }
        // 客户
        userList.add(new RemoteBasImUserVo(user.getUserCode(), user.getRealName()));

        // 申诉中,已介入加入总仓售后人员
        if (ObjectUtil.equals(BlameStatusEnum.APPEAL.getCode(), lossOrder.getLossStatus())
                || ObjectUtil.equals(ReportLossStatusEnum.INTERVENTION.getCode(), lossOrder.getLossStatus())) {
            bizMap.clear();
            bizMap.put(ImAfterSalesBizTypeEnum.REGION.getType(), lossOrder.getRegionWhId());
            userList.addAll(afterSalesService.queryImUserList(bizMap));
        }
        // 创建群聊
        String groupName = String.format("%s%s专属群", lossOrder.getOrderNo(), lossOrder.getSpuName());
        String groupID;
        try {
            List<RemoteImUserBo> remoteImUserBos = BeanUtil.copyToList(userList, RemoteImUserBo.class);
            log.keyword("报损单编码：", lossOrder.getReportLossNo()).info("BizType={},BizId={},groupName={},remoteImUserBos={}", req.getBizType(), req.getBizId(), groupName, JSON.toJSONString(remoteImUserBos));
            RemoteImGroupAddBo bo = buildImGroup(req.getBizType(), req.getBizId(), groupName, remoteImUserBos);
            groupID = tencentImService.createGroup(bo);
        } catch (Exception e) {
            throw new ServiceException("创建IM群组失败");
        }

        return new ImVo(groupID, null);
    }

    @Override
    public Integer getType() {
        return BizTypeEnum.LOSSORDER.getType();
    }
}