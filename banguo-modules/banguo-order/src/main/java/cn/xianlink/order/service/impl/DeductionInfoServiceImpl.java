package cn.xianlink.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import cn.xianlink.basic.api.RemoteExportJobService;
import cn.xianlink.basic.api.RemoteMessageNotifyService;
import cn.xianlink.basic.api.RemoteRegionWhService;
import cn.xianlink.basic.api.RemoteSupplierService;
import cn.xianlink.basic.api.domain.bo.RemoteMessageNotifyV2Bo;
import cn.xianlink.basic.api.domain.vo.RemoteRegionWhVo;
import cn.xianlink.basic.api.domain.vo.RemoteSupplierVo;
import cn.xianlink.basic.api.enums.MsgNotifyTemplateV2Enum;
import cn.xianlink.basic.api.enums.NotifyObjectTypeEnum;
import cn.xianlink.common.api.enums.basic.ExportModuleEnum;
import cn.xianlink.common.api.enums.order.OrderLogSourceTypeEnum;
import cn.xianlink.common.api.enums.trade.AccountOrgTypeEnum;
import cn.xianlink.common.api.enums.trade.TransferBusiTypeEnum;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.service.DictService;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.order.api.bo.DeductionInfoParamBo;
import cn.xianlink.order.api.bo.RemoteSupAccTransBo;
import cn.xianlink.order.api.bo.RemoteSupAccTransQueryBo;
import cn.xianlink.order.api.constant.AmountTypeEnum;
import cn.xianlink.order.api.constant.BillTypeEnum;
import cn.xianlink.order.api.constant.CreateTypeEnum;
import cn.xianlink.order.api.constant.DeductionInfoEnum;
import cn.xianlink.order.api.constant.DeductionInfoStatusEnum;
import cn.xianlink.order.api.constant.DeductionTypeEnum;
import cn.xianlink.order.api.constant.MediaEnum;
import cn.xianlink.order.api.vo.RemoteSupTransDeductionVo;
import cn.xianlink.order.domain.DeductionInfo;
import cn.xianlink.order.domain.OrderLog;
import cn.xianlink.order.domain.bo.DeductionInfoBo;
import cn.xianlink.order.domain.bo.DeductionInfoSyncBo;
import cn.xianlink.order.domain.bo.common.OrderFileBO;
import cn.xianlink.order.domain.order.vo.GetInfoVo;
import cn.xianlink.order.domain.vo.DeductionInfoVo;
import cn.xianlink.order.domain.vo.DeductionReasonVO;
import cn.xianlink.order.domain.vo.DeductionStatusCountVO;
import cn.xianlink.order.domain.vo.common.OrderFileVO;
import cn.xianlink.order.domain.vo.moreGoods.MoreGoodsDetailVO;
import cn.xianlink.order.domain.vo.stockOut.StockoutDetailVO;
import cn.xianlink.order.mapper.DeductionInfoMapper;
import cn.xianlink.order.mapper.OrderLogMapper;
import cn.xianlink.order.mq.producer.DeductionInfoSyncCompleteProducer;
import cn.xianlink.order.service.IDeductionInfoService;
import cn.xianlink.order.service.IMoreGoodsService;
import cn.xianlink.order.service.IOrderService;
import cn.xianlink.order.service.IReportLossService;
import cn.xianlink.order.service.IStockoutRecordService;
import cn.xianlink.order.util.CustomNoUtil;
import cn.xianlink.product.api.RemoteSupplierSkuService;
import cn.xianlink.product.api.domain.bo.RemoteQueryInfoListBo;
import cn.xianlink.product.api.domain.vo.RemoteSupplierSkuInfoVo;
import cn.xianlink.resource.api.RemoteFileService;
import cn.xianlink.resource.api.domain.RemoteOssBo;
import cn.xianlink.resource.api.domain.RemoteOssVo;
import cn.xianlink.system.api.RemoteDeptService;
import cn.xianlink.system.api.model.LoginUser;
import cn.xianlink.trade.api.RemoteTransferService;
import cn.xianlink.trade.api.domain.bo.RemoteTransferCreateBo;
import cn.xianlink.trade.api.domain.vo.RemoteTransferVo;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 扣款单Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-01
 */
@RequiredArgsConstructor
@Service
@CustomLog
public class DeductionInfoServiceImpl implements IDeductionInfoService {

    private final DeductionInfoMapper baseMapper;

    private final transient DictService dictService;

    private IMoreGoodsService iMoreGoodsService;

    private final OrderLogMapper orderLogMapper;

    @Resource
    private final DeductionInfoSyncCompleteProducer deductionInfoSyncCompleteProducer;

    @Autowired
    public void setIMoreGoodsService(@Lazy IMoreGoodsService iMoreGoodsService) {
        this.iMoreGoodsService = iMoreGoodsService;
    }

    private IStockoutRecordService iStockoutRecordService;

    @Autowired
    public void setIStockoutRecordService(@Lazy IStockoutRecordService iStockoutRecordService) {
        this.iStockoutRecordService = iStockoutRecordService;
    }

    private IOrderService iOrderService;

    @Autowired
    public void setIOrderService(@Lazy IOrderService iOrderService) {
        this.iOrderService = iOrderService;
    }

    private IReportLossService iReportLossService;

    @Autowired
    public void setIReportLossService(@Lazy IReportLossService iReportLossService) {
        this.iReportLossService = iReportLossService;
    }

    @DubboReference
    private final RemoteSupplierSkuService remoteSupplierSkuService;

    @DubboReference
    private final RemoteFileService remoteFileService;

    @DubboReference
    private final RemoteTransferService remoteTransferService;

    @DubboReference
    private final RemoteRegionWhService remoteRegionWhService;

    @DubboReference
    private final RemoteDeptService remoteDeptService;
    @DubboReference
    private final RemoteSupplierService remoteSupplierService;
    @DubboReference
    private RemoteMessageNotifyService remoteMessageNotifyService;
    @DubboReference(timeout = 30000)
    private RemoteExportJobService remoteExportJobService;


    private final static String DEDUCTION_INFO_TABLE = "deduction_info";

    //扣款原
    private final static String DEDUCTION_TYPE = "gyskkyy";

    /**
     * 查询扣款单
     */
    @Override
    public DeductionInfoVo queryById(Long id) {
        DeductionInfoVo deductionInfoVo = baseMapper.selectVoById(id);
        if (ObjectUtil.isNotEmpty(deductionInfoVo) && ObjectUtil.isNotEmpty(deductionInfoVo.getDeductionType())) {
            Map<Integer, DeductionReasonVO> map = new HashMap<>();
            List<DeductionReasonVO> deductionReasonVOS = this.listDeductionReasonVO(DEDUCTION_TYPE);
            if (ObjectUtil.isNotEmpty(deductionReasonVOS)) {
                map = deductionReasonVOS.stream().collect(Collectors.toMap(DeductionReasonVO::getValue, u -> u, (n1, n2) -> n1));
            }
            deductionInfoVo.setDeductionTypeValue(ObjectUtil.isEmpty(map.get(deductionInfoVo.getDeductionType())) ? null : map.get(deductionInfoVo.getDeductionType()).getName());
        }
        Map<Long, RemoteRegionWhVo> regionWhVoMap = new HashMap<>();
        List<RemoteRegionWhVo> remoteRegionWhVos = remoteRegionWhService.selectRegionWhInfoByIds(Lists.newArrayList(deductionInfoVo.getRegionWhId()));
        if (ObjectUtil.isNotEmpty(remoteRegionWhVos)) {
            regionWhVoMap = remoteRegionWhVos.stream().collect(Collectors.toMap(RemoteRegionWhVo::getId, u -> u, (n1, n2) -> n1));
        }
        if (ObjectUtil.isNotEmpty(deductionInfoVo.getSupplierDeptId()) && deductionInfoVo.getSupplierDeptId() > 0) {
            Map<Long, String> deptNameMap = remoteDeptService.getDeptNameMap(Lists.newArrayList(deductionInfoVo.getSupplierDeptId()));
            deductionInfoVo.setSupplierDeptName(deptNameMap.get(deductionInfoVo.getSupplierDeptId()));
        }

        //文件图片赋值
        List<RemoteOssVo> ossVos = remoteFileService.select(DEDUCTION_INFO_TABLE, deductionInfoVo.getCode());
        if (ObjectUtil.isNotEmpty(ossVos)) {
            List<OrderFileVO> tmp = BeanUtil.copyToList(ossVos, OrderFileVO.class);
            List<OrderFileVO> imgList = tmp.stream().filter(t -> ObjectUtil.isNotEmpty(t) && ObjectUtil.equals(t.getTag(), MediaEnum.IMG.getCode())).toList();
            List<OrderFileVO> vidList = tmp.stream().filter(t -> ObjectUtil.isNotEmpty(t) && ObjectUtil.equals(t.getTag(), MediaEnum.VID.getCode())).toList();
            deductionInfoVo.setImageList(imgList);
            deductionInfoVo.setVideoList(vidList);
        }

        if (ObjectUtil.isNotEmpty(deductionInfoVo.getStatus())) {
            DeductionInfoStatusEnum deductionInfoStatusEnum = DeductionInfoStatusEnum.loadByCode(deductionInfoVo.getStatus());
            deductionInfoVo.setStatusName(ObjectUtil.isNotEmpty(deductionInfoStatusEnum) ? deductionInfoStatusEnum.getDesc() : null);
        }
        if (ObjectUtil.isNotEmpty(deductionInfoVo.getRegionWhId())) {
            deductionInfoVo.setRegionWhName(regionWhVoMap.getOrDefault(deductionInfoVo.getRegionWhId(), new RemoteRegionWhVo()).getRegionWhName());
        }

        if (ObjectUtil.isNotEmpty(deductionInfoVo.getAmountType())) {
            AmountTypeEnum amountTypeEnum = AmountTypeEnum.loadByCode(deductionInfoVo.getAmountType());
            deductionInfoVo.setAmountTypeName(ObjectUtil.isNotEmpty(amountTypeEnum) ? amountTypeEnum.getDesc() : null);
        }
        if (deductionInfoVo.getType().intValue() == DeductionInfoEnum.SUPPLIER.getCode()) {
            deductionInfoVo.setSupplierAlias(remoteSupplierService.getSupplierById(deductionInfoVo.getCommonId()).getAlias());
        }
        return deductionInfoVo;
    }

    /**
     * 查询扣款单列表
     */
    @Override
    public TableDataInfo<DeductionInfoVo> queryPageList(DeductionInfoBo bo, PageQuery pageQuery) {

        LambdaQueryWrapper<DeductionInfo> lqw = buildQueryWrapper(bo);
        Page<DeductionInfoVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        Map<Integer, DeductionReasonVO> map = new HashMap<>();

        List<DeductionReasonVO> deductionReasonVOS = this.listDeductionReasonVO(DEDUCTION_TYPE);
        if (ObjectUtil.isNotEmpty(deductionReasonVOS)) {
            map = deductionReasonVOS.stream().collect(Collectors.toMap(DeductionReasonVO::getValue, u -> u, (n1, n2) -> n1));
        }

        if (ObjectUtil.isNotEmpty(result) && CollUtil.isNotEmpty(result.getRecords())) {
            Map<Long, RemoteRegionWhVo> regionWhVoMap = new HashMap<>();
            Map<Long, RemoteSupplierSkuInfoVo> supplierSkuInfoMap = new HashMap<>();
            Map<Long, String> supplierAliasMap = new HashMap<>();
            List<Long> supplierDeptIds = result.getRecords().stream().map(DeductionInfoVo::getSupplierDeptId).filter(supplierDeptId -> supplierDeptId > 0).distinct().toList();
            Map<Long, String> deptNameMap = CollUtil.isNotEmpty(supplierDeptIds) ? remoteDeptService.getDeptNameMap(supplierDeptIds) : new HashMap<>();

            List<Long> supplierIds = result.getRecords().stream().filter(f -> f.getType().intValue() == DeductionInfoEnum.SUPPLIER.getCode()).map(DeductionInfoVo::getCommonId).distinct().toList();
            if (ObjectUtil.isNotEmpty(supplierIds)) {
                List<RemoteSupplierVo> remoteSupplierList = remoteSupplierService.getSupplierByIds(supplierIds);
                if (ObjectUtil.isNotEmpty(remoteSupplierList)) {
                    supplierAliasMap = remoteSupplierList.stream().collect(Collectors.toMap(RemoteSupplierVo::getId
                            , item -> Optional.ofNullable(item.getAlias()).orElse("")));
                }
            }

            List<Long> regionWhIds = result.getRecords().stream().map(DeductionInfoVo::getRegionWhId).distinct().toList();
            List<RemoteRegionWhVo> remoteRegionWhVos = remoteRegionWhService.selectRegionWhInfoByIds(regionWhIds);
            if (ObjectUtil.isNotEmpty(remoteRegionWhVos)) {
                regionWhVoMap = remoteRegionWhVos.stream().collect(Collectors.toMap(RemoteRegionWhVo::getId, u -> u, (n1, n2) -> n1));
            }

            List<Long> supplierSkuIds = result.getRecords().stream()
                    .filter(t -> ObjectUtil.isNotEmpty(t.getSupplierSkuId()))
                    .flatMap(t -> Arrays.stream(t.getSupplierSkuId().split(",")).map(Long::valueOf)).distinct()
                    .collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(supplierSkuIds)) {
                RemoteQueryInfoListBo listBo = new RemoteQueryInfoListBo();
                listBo.setSupplierSkuIdList(supplierSkuIds);
                List<RemoteSupplierSkuInfoVo> skuInfoVoList = remoteSupplierSkuService.queryInfoList(listBo);
                supplierSkuInfoMap = skuInfoVoList.stream().collect(Collectors.toMap(RemoteSupplierSkuInfoVo::getId, u -> u, (n1, n2) -> n1));
            }


            for (DeductionInfoVo infoVo : result.getRecords()) {
                if (infoVo.getType().intValue() == DeductionInfoEnum.SUPPLIER.getCode()) {
                    infoVo.setSupplierAlias(supplierAliasMap.get(infoVo.getCommonId()));
                }
                if (ObjectUtil.isNotEmpty(infoVo.getDeductionType()) && ObjectUtil.isNotEmpty(map.get(infoVo.getDeductionType()))) {
                    infoVo.setDeductionTypeValue(map.get(infoVo.getDeductionType()).getName());
                }
                //文件图片赋值
                List<RemoteOssVo> ossVos = remoteFileService.select(DEDUCTION_INFO_TABLE, infoVo.getCode());
                if (ObjectUtil.isNotEmpty(ossVos)) {
                    List<OrderFileVO> tmp = BeanUtil.copyToList(ossVos, OrderFileVO.class);
                    List<OrderFileVO> imgList = tmp.stream().filter(t -> ObjectUtil.isNotEmpty(t) && ObjectUtil.equals(t.getTag(), MediaEnum.IMG.getCode())).toList();
                    List<OrderFileVO> vidList = tmp.stream().filter(t -> ObjectUtil.isNotEmpty(t) && ObjectUtil.equals(t.getTag(), MediaEnum.VID.getCode())).toList();
                    infoVo.setImageList(imgList);
                    infoVo.setVideoList(vidList);
                }
                if (ObjectUtil.isNotEmpty(infoVo.getStatus())) {
                    DeductionInfoStatusEnum deductionInfoStatusEnum = DeductionInfoStatusEnum.loadByCode(infoVo.getStatus());
                    infoVo.setStatusName(ObjectUtil.isNotEmpty(deductionInfoStatusEnum) ? deductionInfoStatusEnum.getDesc() : null);
                }
                if (ObjectUtil.isNotEmpty(infoVo.getAmountType())) {
                    AmountTypeEnum amountTypeEnum = AmountTypeEnum.loadByCode(infoVo.getAmountType());
                    infoVo.setAmountTypeName(ObjectUtil.isNotEmpty(amountTypeEnum) ? amountTypeEnum.getDesc() : null);
                }
                infoVo.setSupplierDeptName(deptNameMap.get(infoVo.getSupplierDeptId()));
                if (ObjectUtil.isNotEmpty(infoVo.getRegionWhId()) && ObjectUtil.isNotEmpty(regionWhVoMap.get(infoVo.getRegionWhId()))) {
                    infoVo.setRegionWhName(regionWhVoMap.get(infoVo.getRegionWhId()).getRegionWhName());
                }
                if (ObjectUtil.isNotEmpty(infoVo.getSupplierSkuId()) && ObjectUtil.isNotEmpty(supplierSkuInfoMap.get(Long.valueOf(infoVo.getSupplierSkuId())))) {
                    RemoteSupplierSkuInfoVo skuInfoVo = supplierSkuInfoMap.get(Long.valueOf(infoVo.getSupplierSkuId()));
                    infoVo.setProducer(skuInfoVo.getProducer());
                    infoVo.setBrand(skuInfoVo.getBrand());
                    infoVo.setSpuStandards(skuInfoVo.getSpuStandards());
                }
            }
        }
        return TableDataInfo.build(result);
    }

    /**
     * 查询扣款单列表
     */
    @Override
    public List<DeductionInfoVo> queryList(DeductionInfoBo bo) {
        LambdaQueryWrapper<DeductionInfo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    @Override
    public LambdaQueryWrapper<DeductionInfo> buildQueryWrapper(DeductionInfoBo bo) {
        LambdaQueryWrapper<DeductionInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getCommonId() != null, DeductionInfo::getCommonId, bo.getCommonId());
        lqw.like(StringUtils.isNotBlank(bo.getCommonName()), DeductionInfo::getCommonName, bo.getCommonName());
        lqw.eq(bo.getType() != null, DeductionInfo::getType, bo.getType());
        lqw.eq(bo.getBillTime() != null, DeductionInfo::getBillTime, bo.getBillTime());
        lqw.eq(bo.getAmount() != null, DeductionInfo::getAmount, bo.getAmount());
        lqw.eq(bo.getDeductionType() != null, DeductionInfo::getDeductionType, bo.getDeductionType());
        lqw.eq(StringUtils.isNotBlank(bo.getBillCode()), DeductionInfo::getBillCode, bo.getBillCode());
        lqw.eq(bo.getBillType() != null, DeductionInfo::getBillType, bo.getBillType());
        lqw.eq(StringUtils.isNotBlank(bo.getDivideCode()), DeductionInfo::getDivideCode, bo.getDivideCode());
        lqw.eq(bo.getDivideTime() != null, DeductionInfo::getDivideTime, bo.getDivideTime());
        lqw.eq(bo.getSupplierSkuId() != null, DeductionInfo::getSupplierSkuId, bo.getSupplierSkuId());
        lqw.eq(bo.getRegionWhId() != null, DeductionInfo::getRegionWhId, bo.getRegionWhId());
        lqw.eq(bo.getSaleTime() != null, DeductionInfo::getSaleTime, bo.getSaleTime());
        lqw.eq(bo.getSpuId() != null, DeductionInfo::getSpuId, bo.getSpuId());
        lqw.eq(bo.getCreateType() != null, DeductionInfo::getCreateType, bo.getCreateType());
        lqw.like(StringUtils.isNotBlank(bo.getSpuName()), DeductionInfo::getSpuName, bo.getSpuName());
        lqw.eq(StringUtils.isNotBlank(bo.getDeductionReason()), DeductionInfo::getDeductionReason, bo.getDeductionReason());
        lqw.eq(StringUtils.isNotBlank(bo.getCode()), DeductionInfo::getCode, bo.getCode());
        lqw.eq(bo.getStatus() != null, DeductionInfo::getStatus, bo.getStatus());
        lqw.eq(bo.getAmountType() != null, DeductionInfo::getAmountType, bo.getAmountType());
        lqw.like(StringUtils.isNotBlank(bo.getCreateName()), DeductionInfo::getCreateName, bo.getCreateName());
        lqw.eq(StringUtils.isNotBlank(bo.getCreateCode()), DeductionInfo::getCreateCode, bo.getCreateCode());
        lqw.eq(bo.getSupplierDeptId() != null, DeductionInfo::getSupplierDeptId, bo.getSupplierDeptId());
        lqw.eq(DeductionInfo::getDelFlag, 0);
        if (CollectionUtil.isNotEmpty(bo.getLogisticsIdList()) && CollectionUtil.isNotEmpty(bo.getPlaceIdLevel2List())) {
            bo.getLogisticsIdList().add(0L);
            lqw.in(DeductionInfo::getLogisticsId, bo.getLogisticsIdList());
            bo.getPlaceIdLevel2List().add(0L);
            lqw.in(DeductionInfo::getPlaceIdLevel2, bo.getPlaceIdLevel2List());
        }
        if (CollectionUtil.isNotEmpty(bo.getLogisticsIdList()) && CollectionUtil.isEmpty(bo.getPlaceIdLevel2List())) {
            lqw.in(DeductionInfo::getLogisticsId, bo.getLogisticsIdList());
            lqw.eq(DeductionInfo::getPlaceIdLevel2, 0);
        }
        if (CollectionUtil.isEmpty(bo.getLogisticsIdList()) && CollectionUtil.isNotEmpty(bo.getPlaceIdLevel2List())) {
            lqw.in(DeductionInfo::getPlaceIdLevel2, bo.getPlaceIdLevel2List());
        }
        if (ObjectUtil.isNotEmpty(bo.getStartTime()) && ObjectUtil.isNotEmpty(bo.getEndTime())) {
            lqw.ge(DeductionInfo::getCreateTime, bo.getStartTime());
            lqw.le(DeductionInfo::getCreateTime, bo.getEndTime());
        }
        if (ObjectUtil.isNotEmpty(bo.getSaleStartTime()) && ObjectUtil.isNotEmpty(bo.getSaleEndTime())) {
            lqw.ge(DeductionInfo::getSaleTime, bo.getSaleStartTime());
            lqw.le(DeductionInfo::getSaleTime, bo.getSaleEndTime());
        }
        if (ObjectUtil.isNotEmpty(bo.getSaleTimeStart()) && ObjectUtil.isNotEmpty(bo.getSaleTimeEnd())) {
            lqw.ge(DeductionInfo::getSaleTime, bo.getSaleTimeStart());
            lqw.le(DeductionInfo::getSaleTime, bo.getSaleTimeEnd());
        }
        if (ObjectUtil.isNotEmpty(bo.getSaleDayStart()) && ObjectUtil.isNotEmpty(bo.getSaleDayEnd())) {
            lqw.ge(DeductionInfo::getSaleTime, bo.getSaleDayStart());
            lqw.le(DeductionInfo::getSaleTime, bo.getSaleDayEnd());
        }
        if (ObjectUtil.isNotEmpty(bo.getUpdateTimeStart()) && ObjectUtil.isNotEmpty(bo.getUpdateTimeEnd())) {
            lqw.ge(DeductionInfo::getUpdateTime, bo.getUpdateTimeStart());
            lqw.le(DeductionInfo::getUpdateTime, bo.getUpdateTimeEnd());
        }
        if (ObjectUtil.isNotEmpty(bo.getBillTimeStart()) && ObjectUtil.isNotEmpty(bo.getBillTimeEnd())) {
            lqw.ge(DeductionInfo::getBillTime, bo.getBillTimeStart());
            lqw.le(DeductionInfo::getBillTime, bo.getBillTimeEnd());
        }

        lqw.orderByDesc(DeductionInfo::getCreateTime);
        return lqw;
    }

    /**
     * 新增扣款单
     */
    @Override
    public Boolean insertByBo(DeductionInfoBo bo) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (ObjectUtil.isEmpty(loginUser)) {
            throw new ServiceException("新增扣款单失败，用户未登录");
        }
        DeductionInfo add = BeanUtil.toBean(bo, DeductionInfo.class);

        RemoteQueryInfoListBo listBo = new RemoteQueryInfoListBo();
        if (ObjectUtil.isNotEmpty(bo.getSupplierSkuId())) {
            List<String> supplierSkuIds = Arrays.asList(bo.getSupplierSkuId().split(StringUtils.SEPARATOR));
            List<RemoteSupplierSkuInfoVo> remoteSupplierSkuInfoVos =
                    remoteSupplierSkuService.queryInfoList(listBo.setSupplierSkuIdList(supplierSkuIds.stream().map(Long::valueOf).distinct().toList()));
            log.keyword("DeductionInfoServiceImpl", "insertByBo", bo.getSupplierSkuId(), bo.getCode())
                    .info("入参：{}，查询供应商批次商品信息：{}", bo, remoteSupplierSkuInfoVos);
            if (ObjectUtil.isNotEmpty(remoteSupplierSkuInfoVos)) {
                RemoteSupplierSkuInfoVo skuInfoVo = remoteSupplierSkuInfoVos.get(0);
                List<Long> tmpSpuIds = remoteSupplierSkuInfoVos.stream().map(RemoteSupplierSkuInfoVo::getSpuId).toList();
                List<String> tmpSpuNameList = remoteSupplierSkuInfoVos.stream().map(RemoteSupplierSkuInfoVo::getSpuName).toList();
                add.setSaleTime(skuInfoVo.getSaleDate());
                add.setSpuId(StringUtils.join(tmpSpuIds, StringUtils.SEPARATOR));
                add.setSpuName(StringUtils.join(tmpSpuNameList, StringUtils.SEPARATOR));
            }
        }
        if (ObjectUtil.isNotEmpty(add.getRegionWhId())) {
            RemoteRegionWhVo regionWhVo = remoteRegionWhService.queryById(add.getRegionWhId());
            add.setRegionWhName(ObjectUtil.isEmpty(regionWhVo) ? null : regionWhVo.getRegionWhName());
        }

        //设置一个唯一的编码
        add.setCode(CustomNoUtil.getDeductionNo(LocalDate.now()));
        //创建方式是手动的
        add.setCreateType(CreateTypeEnum.HAND.getCode());
        //这个入口默认是关联单据类型是订单
        if (ObjectUtil.isEmpty(bo.getBillCode())){
            add.setBillType(BillTypeEnum.LOSS_RECORD.getCode());
        }else {
            add.setBillType(BillTypeEnum.ORDER.getCode());
        }
        if (ObjectUtil.isEmpty(add.getBillCode())) {
            add.setBillCode("");
        } else {
            //补充档口信息

        }

        boolean flag = baseMapper.insert(add) > 0;
        if (!flag) {
            throw new ServiceException("新增扣款单失败");
        }
        List<OrderFileBO> tmp = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(bo.getImageList())) {
            tmp.addAll(bo.getImageList());
        }
        if (ObjectUtil.isNotEmpty(bo.getVideoList())) {
            tmp.addAll(bo.getVideoList());
        }
        if (ObjectUtil.isEmpty(tmp) && ObjectUtil.isNotEmpty(bo.getFileList())) {
            tmp.addAll(bo.getFileList());
        }
        if (ObjectUtil.isNotEmpty(tmp)) {
            List<RemoteOssBo> result = new ArrayList<>();
            //使用公共的存文件图片表
            for (OrderFileBO fileBO : tmp) {
                RemoteOssBo ossBo = new RemoteOssBo();
                ossBo.setUrl(fileBO.getUrl());
                ossBo.setSort(fileBO.getSort());
                ossBo.setBusinessType(DEDUCTION_INFO_TABLE);
                ossBo.setKeyword(add.getCode());
                ossBo.setTag(ObjectUtil.isEmpty(fileBO.getTag()) ? MediaEnum.IMG.getCode() : fileBO.getTag());
                result.add(ossBo);
            }
            remoteFileService.batchInsert(result);
        }
        return flag;
    }


    /**
     * 修改扣款单
     */
    @Override
    public Boolean updateByBo(DeductionInfoBo bo) {
        DeductionInfo update = BeanUtil.toBean(bo, DeductionInfo.class);
        RemoteQueryInfoListBo listBo = new RemoteQueryInfoListBo();
        if (ObjectUtil.isNotEmpty(bo.getSupplierSkuId())) {
            List<String> supplierSkuIds = Arrays.asList(bo.getSupplierSkuId().split(StringUtils.SEPARATOR));
            List<RemoteSupplierSkuInfoVo> remoteSupplierSkuInfoVos =
                    remoteSupplierSkuService.queryInfoList(listBo.setSupplierSkuIdList(supplierSkuIds.stream().map(Long::valueOf).distinct().toList()));
            log.keyword("DeductionInfoServiceImpl", "insertByBo", bo.getSupplierSkuId())
                    .info("入参：{}，查询供应商批次商品信息：{}", bo, remoteSupplierSkuInfoVos);
            if (ObjectUtil.isNotEmpty(remoteSupplierSkuInfoVos)) {
                RemoteSupplierSkuInfoVo skuInfoVo = remoteSupplierSkuInfoVos.get(0);
                List<Long> tmpSpuIds = remoteSupplierSkuInfoVos.stream().map(RemoteSupplierSkuInfoVo::getSpuId).toList();
                List<String> tmpSpuNameList = remoteSupplierSkuInfoVos.stream().map(RemoteSupplierSkuInfoVo::getSpuName).toList();
                update.setSaleTime(skuInfoVo.getSaleDate());
                update.setSpuId(StringUtils.join(tmpSpuIds, StringUtils.SEPARATOR));
                update.setSpuName(StringUtils.join(tmpSpuNameList, StringUtils.SEPARATOR));
            }
        }
        //先删除再添加
        remoteFileService.delete(DEDUCTION_INFO_TABLE, bo.getCode());

        List<OrderFileBO> tmp = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(bo.getImageList())) {
            tmp.addAll(bo.getImageList());
        }
        if (ObjectUtil.isNotEmpty(bo.getVideoList())) {
            tmp.addAll(bo.getVideoList());
        }
        if (ObjectUtil.isEmpty(tmp) && ObjectUtil.isNotEmpty(bo.getFileList())) {
            tmp.addAll(bo.getFileList());
        }
        if (ObjectUtil.isNotEmpty(tmp)) {
            List<RemoteOssBo> result = new ArrayList<>();
            //使用公共的存文件图片表
            for (OrderFileBO fileBO : tmp) {
                RemoteOssBo ossBo = new RemoteOssBo();
                ossBo.setUrl(fileBO.getUrl());
                ossBo.setSort(fileBO.getSort());
                ossBo.setBusinessType(DEDUCTION_INFO_TABLE);
                ossBo.setKeyword(bo.getCode());
                ossBo.setTag(ObjectUtil.isEmpty(fileBO.getTag()) ? MediaEnum.IMG.getCode() : fileBO.getTag());
                result.add(ossBo);
            }
            remoteFileService.batchInsert(result);
        }

        update.setUpdateTime(new Date());
        if (ObjectUtil.isEmpty(update.getBillCode())) {
            update.setBillCode("");
        }
        return baseMapper.updateById(update) > 0;
    }


    @Override
    public Boolean updateDeductionStatusByCode(DeductionInfoBo bo) {
        if (ObjectUtil.isEmpty(bo) || ObjectUtil.isEmpty(bo.getCode()) || ObjectUtil.isEmpty(bo.getStatus())) {
            log.keyword("updateDeductionStatusByCode", "扣款mq更新状态").warn("扣款mq更新状态，失败了，入参为空");
            return false;
        }
        LambdaQueryWrapper<DeductionInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DeductionInfo::getCode, bo.getCode());
        wrapper.eq(DeductionInfo::getStatus, DeductionInfoStatusEnum.COMMITTED.getCode());
        wrapper.eq(DeductionInfo::getDelFlag, 0);
        DeductionInfo deductionInfo = baseMapper.selectOne(wrapper);
        if (ObjectUtil.isEmpty(deductionInfo)) {
            log.keyword("updateDeductionStatusByCode", "扣款mq更新状态", bo.getCode()).warn("扣款mq更新状态，根据code查询为空，code:{}", bo.getCode());
            return true;
        }
        DeductionInfo newDate = new DeductionInfo();
        newDate.setId(deductionInfo.getId());
        if (ObjectUtil.isNotEmpty(bo.getDivideCode())) {
            newDate.setDivideCode(bo.getDivideCode());
        }
        if (ObjectUtil.isNotEmpty(bo.getSourceSplitNo())) {
            newDate.setSourceSplitNo(bo.getSourceSplitNo());
        }
        newDate.setStatus(bo.getStatus());

        int flag = baseMapper.updateById(newDate);
        if (flag == 0) {
            log.keyword("updateDeductionStatusByCode", "扣款mq更新状态", bo.getCode()).error("扣款mq更新状态，更新到数据库失败，入参:{}", bo);
            return false;
        }

        return true;
    }


    /**
     * 批量删除扣款单
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return baseMapper.deleteBatchIds(ids) > 0;
    }


    @Override
    public Boolean deleteDeductionInfoById(Long id) {
        if (ObjectUtil.isEmpty(id)) {
            return true;
        }
        DeductionInfo deductionInfo = baseMapper.selectById(id);
        if (ObjectUtil.isEmpty(deductionInfo)) {
            return true;
        }
        remoteFileService.delete(DEDUCTION_INFO_TABLE, id.toString());

        if (!DeductionInfoStatusEnum.UNCOMMITTED.getCode().equals(deductionInfo.getStatus())) {
            throw new ServiceException("只能删除未提交的扣款单");
        }
        baseMapper.deleteById(id);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertDeduction(DeductionInfoParamBo deductionInfoParamBo) {
        if (ObjectUtil.isEmpty(deductionInfoParamBo)) {
            log.keyword("DeductionInfoServiceImpl", deductionInfoParamBo.getCommonId(), deductionInfoParamBo.getBillCode())
                    .info("通用扣款单，参数为空deductionInfoBo：{}", deductionInfoParamBo);
            saveOrderLogInfo(1L, deductionInfoParamBo.getCode(), deductionInfoParamBo.getDeductionType());
            return false;
        }
        DeductionInfoBo tmpBo = new DeductionInfoBo();
        tmpBo.setCode(deductionInfoParamBo.getCode());
        LambdaQueryWrapper<DeductionInfo> lqw = buildQueryWrapper(tmpBo);
        DeductionInfo deductionInfo = baseMapper.selectOne(lqw);
        if (ObjectUtil.isNotEmpty(deductionInfo)) {
            log.keyword("DeductionInfoServiceImpl", deductionInfoParamBo.getCommonId(), deductionInfoParamBo.getBillCode())
                    .info("通用扣款单，已经新增该数据：{}", deductionInfoParamBo);
            saveOrderLogInfo(deductionInfo.getId(), deductionInfoParamBo.getCode(), deductionInfoParamBo.getDeductionType());
            return true;
        }

        DeductionInfo add = BeanUtil.toBean(deductionInfoParamBo, DeductionInfo.class);
        add.setSaleTime(deductionInfoParamBo.getSaleDate());
        //创建方式是自动的
        add.setCreateType(CreateTypeEnum.AUTO.getCode());
        //后端创建的都是待结算状态，直接调用同步到划转单 xdb
        add.setStatus(DeductionInfoStatusEnum.COMMITTED.getCode());

        if (ObjectUtil.isNotEmpty(deductionInfoParamBo.getSupplierSkuId())) {
            List<String> supplierSkuIds = Arrays.asList(deductionInfoParamBo.getSupplierSkuId().split(StringUtils.SEPARATOR));
            //添加供应商数据
            RemoteQueryInfoListBo listBo = new RemoteQueryInfoListBo();
            List<RemoteSupplierSkuInfoVo> remoteSupplierSkuInfoVos =
                    remoteSupplierSkuService.queryInfoList(listBo.setSupplierSkuIdList(supplierSkuIds.stream().map(Long::valueOf).distinct().toList()));
            log.keyword("DeductionInfoServiceImpl", "insertDeduction", add.getSupplierSkuId())
                    .info("入参：{}，远程调用扣款单接口，查询供应商批次商品信息：{}", deductionInfoParamBo, remoteSupplierSkuInfoVos);
            if (ObjectUtil.isNotEmpty(remoteSupplierSkuInfoVos)) {
                RemoteSupplierSkuInfoVo skuInfoVo = remoteSupplierSkuInfoVos.get(0);
                List<Long> tmpSpuIds = remoteSupplierSkuInfoVos.stream().map(RemoteSupplierSkuInfoVo::getSpuId).toList();
                List<String> tmpSpuNameList = remoteSupplierSkuInfoVos.stream().map(RemoteSupplierSkuInfoVo::getSpuName).toList();
                add.setSaleTime(skuInfoVo.getSaleDate());
                add.setSupplierDeptId(skuInfoVo.getSupplierDeptId());
                add.setSpuId(StringUtils.join(tmpSpuIds, StringUtils.SEPARATOR));
                add.setSpuName(StringUtils.join(tmpSpuNameList, StringUtils.SEPARATOR));
            }
        }
        if (ObjectUtil.isEmpty(add.getBillCode())) {
            add.setBillCode("");
        }
        boolean flag = baseMapper.insert(add) > 0;

        saveOrderLogInfo(add.getId(), deductionInfoParamBo.getCode(), deductionInfoParamBo.getDeductionType());
        if (!flag) {
            saveOrderLogInfo(add.getId(), deductionInfoParamBo.getCode(), deductionInfoParamBo.getDeductionType());
            throw new ServiceException("通用扣款单，添加数据失败，入参：" + JSONObject.toJSONString(deductionInfoParamBo));
        }
        if (ObjectUtil.isNotEmpty(deductionInfoParamBo.getFileList())) {
            List<RemoteOssBo> result = new ArrayList<>();
            //使用公共的存文件图片表
            for (cn.xianlink.order.api.bo.OrderFileBO fileBO : deductionInfoParamBo.getFileList()) {
                RemoteOssBo ossBo = new RemoteOssBo();
                ossBo.setUrl(fileBO.getUrl());
                ossBo.setSort(fileBO.getSort());
                ossBo.setBusinessType(DEDUCTION_INFO_TABLE);
                ossBo.setKeyword(deductionInfoParamBo.getCode());
                ossBo.setTag(ObjectUtil.isEmpty(fileBO.getTag()) ? MediaEnum.IMG.getCode() : fileBO.getTag());
                result.add(ossBo);
            }
            remoteFileService.batchInsert(result);
        }

        if (deductionInfoParamBo.getIsSend()) {
            //后端自动生成的已提交扣款单直接同步到划转单
            log.keyword("DeductionInfoServiceImpl", "其他服务调用新增扣款单", add.getCode()).info("后端调用新增扣款单接口：{}", add);
            DeductionInfoSyncBo syncBo = new DeductionInfoSyncBo();
            syncBo.setDeductionInfoCode(add.getCode());
            deductionInfoSyncCompleteProducer.send(syncBo);

            //发送公众号消息
            sendNotifyInfo(add);
        }

        return true;
    }

    /**
     * 发送公众号消息
     *
     * @param add
     */
    private void sendNotifyInfo(DeductionInfo add) {
        if (ObjectUtil.isEmpty(add) || !BillTypeEnum.MORE_GOODS_RECORD.getCode().equals(add.getBillType())) {
            return;
        }

        LinkedHashMap<String, String> params = new LinkedHashMap<>();
        String spuName = remoteSupplierSkuService.getSkuCombinationName(Lists.newArrayList(Long.valueOf(add.getSupplierSkuId()))).get(Long.valueOf(add.getSupplierSkuId()));
        params.put("orderCode", add.getCode());
        params.put("spuName", spuName);
        params.put("submitTime", DateUtil.format(add.getBillTime(), DatePattern.NORM_DATETIME_PATTERN));

        NotifyObjectTypeEnum objectType = null;
        List<String> objectIds = new ArrayList<>();
        if (add.getType().equals(DeductionInfoEnum.SUPPLIER.getCode())) {
            objectType = NotifyObjectTypeEnum.SUPPLIER;
            objectIds.add(add.getCommonId().toString());
            RemoteMessageNotifyV2Bo message = new RemoteMessageNotifyV2Bo(objectType, objectIds, MsgNotifyTemplateV2Enum.more_goods_add, params, add.getId().toString());
            log.keyword("sendNotifyInfo").info("多货发送一条公众号消息：{}", message);
            remoteMessageNotifyService.sendMessageV2(message);
        }
        if (add.getType().equals(DeductionInfoEnum.CITY.getCode())) {
            objectType = NotifyObjectTypeEnum.CITY_WH;
            objectIds.add(add.getCommonId().toString());
            RemoteMessageNotifyV2Bo message = new RemoteMessageNotifyV2Bo(objectType, objectIds, MsgNotifyTemplateV2Enum.more_goods_loss, params, add.getId().toString());
            log.keyword("sendNotifyInfo").info("多货发送一条公众号消息：{}", message);
            remoteMessageNotifyService.sendMessageV2(message);
        }
    }

    private void saveOrderLogInfo(long id, String code, Integer deductionType) {
        //创建扣款单操作记录
        OrderLog orderLog = new OrderLog();
        orderLog.setSourceId(id);
        orderLog.setSourceType(OrderLogSourceTypeEnum.DEDUCTION_INFO.getCode());
        orderLog.setOperate("远程调用保存扣款单信息");
        orderLog.setRemark("远程调用保存扣款单信息，扣款单编码：" + code + ",扣款单原因:" + deductionType);
        orderLogMapper.insert(orderLog);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchAddDeductionInfo(List<DeductionInfoBo> infoBoList) {
        List<DeductionInfo> batchAddList = new ArrayList<>();
        List<RemoteOssBo> ossBoList = new ArrayList<>();

        Map<Long, RemoteSupplierSkuInfoVo> map = new HashMap<>();

        List<Long> supplierSkuIds = new ArrayList<>();
        List<String> tmpList = infoBoList.stream().map(DeductionInfoBo::getSupplierSkuId).filter(ObjectUtil::isNotEmpty).distinct().toList();
        for (String supplierSkuId : tmpList) {
            if (ObjectUtil.isNotEmpty(supplierSkuId)) {
                List<String> tmpSkuIds = Arrays.asList(supplierSkuId.split(StringUtils.SEPARATOR));
                supplierSkuIds.addAll(tmpSkuIds.stream().map(Long::parseLong).toList());
            }
        }
        if (CollectionUtil.isNotEmpty(supplierSkuIds)) {
            RemoteQueryInfoListBo listBo = new RemoteQueryInfoListBo();
            List<RemoteSupplierSkuInfoVo> remoteSupplierSkuInfoVos = remoteSupplierSkuService.queryInfoList(listBo.setSupplierSkuIdList(supplierSkuIds));
            log.keyword("DeductionInfoServiceImpl", "insertDeduction", supplierSkuIds)
                    .info("入参：{}，远程调用扣款单接口，查询供应商批次商品信息：{}", infoBoList, remoteSupplierSkuInfoVos);
            if (ObjectUtil.isNotEmpty(remoteSupplierSkuInfoVos)) {
                map = remoteSupplierSkuInfoVos.stream().collect(Collectors.toMap(RemoteSupplierSkuInfoVo::getId, u -> u, (n1, n2) -> n1));
            }
        }


        for (DeductionInfoBo infoBo : infoBoList) {
            DeductionInfoBo tmpBo = new DeductionInfoBo();
            tmpBo.setCode(infoBo.getCode());
            LambdaQueryWrapper<DeductionInfo> lqw = buildQueryWrapper(tmpBo);
            DeductionInfo deductionInfo = baseMapper.selectOne(lqw);
            if (ObjectUtil.isNotEmpty(deductionInfo)) {
                log.keyword("DeductionInfoServiceImpl", "batchAddDeductionInfo", infoBo.getCommonId(), infoBo.getBillCode())
                        .warn("通用扣款单，已经新增该数据：{}", deductionInfo);
                continue;
            }
            DeductionInfo newData = BeanUtil.toBean(infoBo, DeductionInfo.class);
            //设置一个唯一的编码
            newData.setCode(CustomNoUtil.getDeductionNo(LocalDate.now()));
            //组装赋值多个批次商品信息
            if (ObjectUtil.isNotEmpty(infoBo.getSupplierSkuId())) {
                convertSupplierSkuInfo(infoBo.getSupplierSkuId(), newData, map);
            }

            batchAddList.add(newData);
            if (ObjectUtil.isNotEmpty(infoBo.getFileList())) {
                //使用公共的存文件图片表
                for (OrderFileBO fileBO : infoBo.getFileList()) {
                    RemoteOssBo ossBo = new RemoteOssBo();
                    ossBo.setUrl(fileBO.getUrl());
                    ossBo.setSort(fileBO.getSort());
                    ossBo.setBusinessType(DEDUCTION_INFO_TABLE);
                    ossBo.setKeyword(infoBo.getCode());
                    ossBo.setTag(ObjectUtil.isEmpty(fileBO.getTag()) ? MediaEnum.IMG.getCode() : fileBO.getTag());
                    ossBoList.add(ossBo);
                }
            }
        }
        baseMapper.insertBatch(batchAddList);

        remoteFileService.batchInsert(ossBoList);

        return true;
    }

    @Override
    public List<DeductionReasonVO> listDeductionReasonVO(String dictType) {
        System.err.println(dictService.getDictLabel("gyskkyy", "1", ","));

        Map<String, String> map = dictService.getAllDictByDictType(dictType);
        List<DeductionReasonVO> list = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(map)) {
            map.forEach((key, value) -> {
                DeductionReasonVO vo = new DeductionReasonVO();
                List<String> tmpList = Arrays.asList(value.split(StringUtils.SEPARATOR));
                if (tmpList.size() != 2) {
                    return;
                }
                vo.setName(tmpList.get(0));
                vo.setDesc(tmpList.get(1));
                vo.setValue(Integer.valueOf(key));
                list.add(vo);
            });
        }
        System.err.println(list);
        return list;
    }

    @Override
    public Boolean saveUseDeductionInfo(String date, List<String> codeList) {
        LocalDate statementDate = LocalDate.now().plusDays(-1);
        if (ObjectUtil.isNotEmpty(date)) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            statementDate = LocalDate.parse(date, formatter);
        }
        String[] split = statementDate.toString().split("-");
        Calendar calendar = Calendar.getInstance();
        int year = Integer.parseInt(split[0]);
        int month = Integer.valueOf(split[1]) - 1;
        int day = Integer.valueOf(split[2]);
        // 设置开始日期
        calendar.set(year, month, day);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date startDate = DateUtil.beginOfDay(calendar.getTime());
        // 设置结束日期
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        Date endDate = calendar.getTime();

        System.err.println("开始时间" + startDate + "结束时间" + endDate);

        if (ObjectUtil.isEmpty(codeList)) {
            List<DeductionInfoVo> infoVoList = this.getDeductionInfoByParam(startDate, endDate);
            if (ObjectUtil.isEmpty(infoVoList)) {
                log.keyword("saveUseDeductionInfo", "扣款单同步到结算单", date).warn("扣款单同步到结算单，获取扣款单数据为空");
                return true;
            }
            log.keyword("saveUseDeductionInfo", "扣款单同步到结算单", date).info("需要同步数量：{},infoVoList:{}", infoVoList.size(), infoVoList);
            for (DeductionInfoVo infoVo : infoVoList) {
                DeductionInfoSyncBo syncBo = new DeductionInfoSyncBo();
                syncBo.setDeductionInfoCode(infoVo.getCode());
                deductionInfoSyncCompleteProducer.send(syncBo);
            }
        } else {
            for (String code : codeList) {
                DeductionInfoSyncBo syncBo = new DeductionInfoSyncBo();
                syncBo.setDeductionInfoCode(code);
                deductionInfoSyncCompleteProducer.send(syncBo);
            }
        }

        return true;
    }

    @Override
    public Object getBillInfoByTypeAndCode(Integer billType, String billCode) {
        if (ObjectUtil.isEmpty(billType) || ObjectUtil.isEmpty(billCode)) {
            return null;
        }
        if (ObjectUtil.equals(billType, BillTypeEnum.STOCKOUT_RECORD.getCode())
                || ObjectUtil.equals(billType, BillTypeEnum.LOSS_RECORD.getCode())) {
            StockoutDetailVO stockoutByCode = iStockoutRecordService.getStockoutByCode(billCode);
            return stockoutByCode;
        } else if (ObjectUtil.equals(billType, BillTypeEnum.MORE_GOODS_RECORD.getCode())) {
            return iMoreGoodsService.getMoreGoodsByCode(billCode);
        } else if (ObjectUtil.equals(billType, BillTypeEnum.ORDER.getCode())) {
            return iOrderService.queryByCode(billCode);
        } else if (ObjectUtil.equals(billType, BillTypeEnum.REPORT_LOSS_ORDER.getCode())) {
            return iReportLossService.queryByCode(billCode);
        }
        return null;
    }

    @Override
    public void syncDeductionInfo(String deductionInfoCode) {
        LambdaQueryWrapper<DeductionInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DeductionInfo::getCode, deductionInfoCode);
        wrapper.eq(DeductionInfo::getStatus, DeductionInfoStatusEnum.COMMITTED.getCode());
        wrapper.eq(DeductionInfo::getDelFlag, 0);
        wrapper.isNull(DeductionInfo::getDivideId);
        DeductionInfo deductionInfo = baseMapper.selectOne(wrapper);
        if (ObjectUtil.isEmpty(deductionInfo)) {
            log.keyword("saveUseDeductionInfo", "扣款单同步到划转单mq", deductionInfoCode).warn("扣款单同步到划转单mq，根据code查询为空，code:{}", deductionInfoCode);
            return;
        }
        if (deductionInfo.getAmount().compareTo(BigDecimal.ZERO) == 0) {
            log.keyword("saveUseDeductionInfo", "扣款单同步到划转单金额为0", deductionInfoCode).warn("扣款单同步到划转单mq，扣款单同步到划转单金额为0，code:{}", deductionInfoCode);
            return;
        }
        DeductionInfoVo infoVo = BeanUtil.toBean(deductionInfo, DeductionInfoVo.class);
        RemoteTransferCreateBo createBo = new RemoteTransferCreateBo();
        convertRemoteTransferCreateBo(infoVo, createBo);
        //判断下 关联单号是否为null，如果为null就设置为空串
        if (ObjectUtil.isEmpty(createBo.getRelateNo())) {
            createBo.setRelateId(0L);
            createBo.setRelateNo("");
        }
        try {
            RemoteTransferVo remoteTransferVo = remoteTransferService.create(createBo);
            DeductionInfo info = new DeductionInfo();
            info.setId(infoVo.getId());
            info.setDivideCode(remoteTransferVo.getAvailNo());
            info.setDivideId(remoteTransferVo.getId());
            info.setSourceSplitNo(remoteTransferVo.getSplitNo());
            baseMapper.updateById(info);
            log.keyword("saveUseDeductionInfo", "扣款单同步到划转单mq", deductionInfoCode).info("扣款单同步到划转单mq，更新扣款单成功info:{}，remoteTransferVo：{}", info, remoteTransferVo);
        } catch (Exception e) {
            log.keyword("saveUseDeductionInfo", "扣款单同步到划转单mq", infoVo.getCode()).error("扣款单同步到划转单mq，同步失败，报错e:{}", ExceptionUtils.getStackTrace(e));

            //创建扣款单操作记录
            OrderLog orderLog = new OrderLog();
            orderLog.setSourceId(infoVo.getId());
            orderLog.setSourceType(OrderLogSourceTypeEnum.DEDUCTION_INFO.getCode());
            orderLog.setOperate("扣款单同步到划转单");
            orderLog.setRemark("扣款单同步划转单报错，扣款单编码：" + infoVo.getCode() + ",e:" + e.getMessage());
            orderLogMapper.insert(orderLog);
        }
    }

    @Override
    public List<RemoteSupTransDeductionVo> queryDeduction(RemoteSupAccTransQueryBo bo) {

        log.keyword("queryDeduction", "资金账户查询扣款单明细", bo.getSupplierId()).info("资金账户查询扣款单明细，bo:{}", bo);
        List<RemoteSupAccTransBo> trans = bo.getTrans();
        List<Long> ids = trans.stream().map(RemoteSupAccTransBo::getTransId).toList();
        LambdaQueryWrapper<DeductionInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(DeductionInfo::getId, ids);

        List<DeductionInfoVo> deductionInfoVos = baseMapper.selectVoList(wrapper);
        log.keyword("queryDeduction", "资金账户查询扣款单明细", bo.getSupplierId()).info("资金账户查询扣款单明细，deductionInfoVos:{}", deductionInfoVos);
        if (ObjectUtil.isEmpty(deductionInfoVos)) {
            return Collections.emptyList();
        }
        List<Long> supplierDeptIds = deductionInfoVos.stream().map(DeductionInfoVo::getSupplierDeptId)
                .filter(supplierDeptId -> ObjectUtil.isNotEmpty(supplierDeptId) && supplierDeptId > 0).distinct().toList();
        Map<Long, String> deptNameMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(supplierDeptIds)) {
            deptNameMap = remoteDeptService.getDeptNameMap(supplierDeptIds);
        }
        if (ObjectUtil.isNotEmpty(bo.getSupplierDeptId())) {
            log.keyword("queryDeduction", "资金账户查询扣款单明细", "扣款单档口信息查询", bo.getSupplierId()).info("资金账户查询扣款单明细，deductionInfoVos:{}", deductionInfoVos);
            deductionInfoVos = deductionInfoVos.stream().filter(tmp -> ObjectUtil.equals(bo.getSupplierDeptId(), tmp.getSupplierDeptId())).collect(Collectors.toList());
        }

        Map<Integer, DeductionReasonVO> map = new HashMap<>();

        List<DeductionReasonVO> deductionReasonVOS = this.listDeductionReasonVO(DEDUCTION_TYPE);
        if (ObjectUtil.isNotEmpty(deductionReasonVOS)) {
            map = deductionReasonVOS.stream().collect(Collectors.toMap(DeductionReasonVO::getValue, u -> u, (n1, n2) -> n1));
        }

        Map<Long, RemoteRegionWhVo> regionWhVoMap = new HashMap<>();
        List<Long> regionWhIds = deductionInfoVos.stream().map(DeductionInfoVo::getRegionWhId).distinct().toList();
        if (ObjectUtil.isNotEmpty(regionWhIds)) {
            List<RemoteRegionWhVo> remoteRegionWhVos = remoteRegionWhService.selectRegionWhInfoByIds(regionWhIds);
            if (ObjectUtil.isNotEmpty(remoteRegionWhVos)) {
                regionWhVoMap = remoteRegionWhVos.stream().collect(Collectors.toMap(RemoteRegionWhVo::getId, u -> u, (n1, n2) -> n1));
            }
        }

        Map<Long, RemoteSupplierSkuInfoVo> supplierSkuInfoMap = new HashMap<>();
        List<Long> supplierSkuIds = deductionInfoVos.stream().filter(t -> ObjectUtil.isNotEmpty(t.getSupplierSkuId())).map(tmp -> Long.valueOf(tmp.getSupplierSkuId())).distinct().toList();
        if (ObjectUtil.isNotEmpty(supplierSkuIds)) {
            RemoteQueryInfoListBo listBo = new RemoteQueryInfoListBo();
            listBo.setSupplierSkuIdList(supplierSkuIds);
            List<RemoteSupplierSkuInfoVo> skuInfoVoList = remoteSupplierSkuService.queryInfoList(listBo);
            supplierSkuInfoMap = skuInfoVoList.stream().collect(Collectors.toMap(RemoteSupplierSkuInfoVo::getId, u -> u, (n1, n2) -> n1));
        }

        for (DeductionInfoVo infoVo : deductionInfoVos) {
            if (ObjectUtil.isNotEmpty(infoVo.getDeductionType()) && ObjectUtil.isNotEmpty(map.get(infoVo.getDeductionType()))) {
                infoVo.setDeductionTypeValue(map.get(infoVo.getDeductionType()).getName());
            }
            if (ObjectUtil.isNotEmpty(infoVo.getStatus())) {
                DeductionInfoStatusEnum deductionInfoStatusEnum = DeductionInfoStatusEnum.loadByCode(infoVo.getStatus());
                infoVo.setStatusName(ObjectUtil.isNotEmpty(deductionInfoStatusEnum) ? deductionInfoStatusEnum.getDesc() : null);
            }
            infoVo.setSupplierDeptName(deptNameMap.get(infoVo.getSupplierDeptId()));
            if (ObjectUtil.isNotEmpty(infoVo.getAmountType())) {
                AmountTypeEnum amountTypeEnum = AmountTypeEnum.loadByCode(infoVo.getAmountType());
                infoVo.setAmountTypeName(ObjectUtil.isNotEmpty(amountTypeEnum) ? amountTypeEnum.getDesc() : null);
            }
            if (ObjectUtil.isNotEmpty(infoVo.getRegionWhId()) && ObjectUtil.isNotEmpty(regionWhVoMap.get(infoVo.getRegionWhId()))) {
                infoVo.setRegionWhName(regionWhVoMap.get(infoVo.getRegionWhId()).getRegionWhName());
            }
            if (ObjectUtil.isNotEmpty(infoVo.getSupplierSkuId()) && ObjectUtil.isNotEmpty(supplierSkuInfoMap.get(Long.valueOf(infoVo.getSupplierSkuId())))) {
                RemoteSupplierSkuInfoVo skuInfoVo = supplierSkuInfoMap.get(Long.valueOf(infoVo.getSupplierSkuId()));
                infoVo.setProducer(skuInfoVo.getProducer());
                infoVo.setBrand(skuInfoVo.getBrand());
                infoVo.setSpuStandards(skuInfoVo.getSpuStandards());
            }
            if (BillTypeEnum.STOCKOUT_RECORD.getCode().equals(infoVo.getBillType())) {
                StockoutDetailVO detailVO = iStockoutRecordService.getStockoutOrderCodeByCode(infoVo.getBillCode());
                if (ObjectUtil.isNotEmpty(detailVO)) {
                    infoVo.setOrderNo(detailVO.getOrderCode());
                }
            }
        }
        List<RemoteSupTransDeductionVo> remoteSupTransDeductionVos = BeanUtil.copyToList(deductionInfoVos, RemoteSupTransDeductionVo.class);
        log.keyword("queryDeduction", "资金账户查询扣款单明细", "扣款单档口信息查询", bo.getSupplierId()).info("资金账户查询扣款单明细，remoteSupTransDeductionVos:{}", remoteSupTransDeductionVos);
        return remoteSupTransDeductionVos.stream().sorted(Comparator.comparing(RemoteSupTransDeductionVo::getCreateTime, Comparator.reverseOrder())).toList();
    }

    /**
     * 手动结算城市仓加款单
     */
    @Override
    public Integer divide(List<Long> ids) {
        //先获取详情，看是否城市仓的未结算的
        LambdaQueryWrapper<DeductionInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(DeductionInfo::getId, ids);
        wrapper.eq(DeductionInfo::getStatus, DeductionInfoStatusEnum.COMMITTED.getCode());
        wrapper.eq(DeductionInfo::getDelFlag, 0);
        List<DeductionInfo> deductionInfoList = baseMapper.selectList(wrapper);
        if (ObjectUtil.isNotEmpty(deductionInfoList)) {
            if (deductionInfoList.stream().anyMatch(t -> !t.getType().equals(DeductionInfoEnum.CITY.getCode()))) {
                throw new ServiceException("只能结算城市仓加扣款单");
            }
            deductionInfoList.forEach(t -> {
                t.setStatus(DeductionInfoStatusEnum.DIVIDE.getCode());
                t.setDivideTime(new Date());
            });
            baseMapper.updateBatchById(deductionInfoList);
            return deductionInfoList.size();
        }
        return 0;
    }

    /**
     * 组装结算单数据
     *
     * @param infoVo
     * @param createBo
     */
    private void convertRemoteTransferCreateBo(DeductionInfoVo infoVo, RemoteTransferCreateBo createBo) {
        createBo.setTransId(infoVo.getId());
        createBo.setTransNo(infoVo.getCode());
        if (DeductionInfoEnum.SUPPLIER.getCode().equals(infoVo.getType())
                && AmountTypeEnum.LOSS.getCode().equals(infoVo.getAmountType())) {
            createBo.setBusiType(TransferBusiTypeEnum.TRANSFER_SUPPLIER.getCode());
        } else if (DeductionInfoEnum.SUPPLIER.getCode().equals(infoVo.getType())
                && AmountTypeEnum.ADD.getCode().equals(infoVo.getAmountType())) {
            createBo.setBusiType(TransferBusiTypeEnum.TRANSFER_SUPPLIER_ADD.getCode());
        } else if (DeductionInfoEnum.CITY.getCode().equals(infoVo.getType())
                && AmountTypeEnum.LOSS.getCode().equals(infoVo.getAmountType())) {
            createBo.setBusiType(TransferBusiTypeEnum.TRANSFER_CITY.getCode());
        } else if (DeductionInfoEnum.CITY.getCode().equals(infoVo.getType())
                && AmountTypeEnum.ADD.getCode().equals(infoVo.getAmountType())) {
            createBo.setBusiType(TransferBusiTypeEnum.TRANSFER_CITY_ADD.getCode());
        } else if (DeductionInfoEnum.REGION.getCode().equals(infoVo.getType())) {
            createBo.setBusiType(TransferBusiTypeEnum.TRANSFER_REGION.getCode());
        }
        if (ObjectUtil.isNotEmpty(infoVo.getBillTime())) {
            createBo.setTransDate(infoVo.getBillTime());
        }
        createBo.setAcctOrgId(infoVo.getRegionWhId());

        if (DeductionInfoEnum.SUPPLIER.getCode().equals(infoVo.getType())) {
            createBo.setOrgType(AccountOrgTypeEnum.SUPPLIER.getCode());
        } else if (DeductionInfoEnum.CITY.getCode().equals(infoVo.getType())) {
            createBo.setOrgType(AccountOrgTypeEnum.CITY_WH.getCode());
        } else if (DeductionInfoEnum.REGION.getCode().equals(infoVo.getType())) {
            createBo.setOrgType(AccountOrgTypeEnum.REGION_WH.getCode());
        }
        createBo.setOrgId(infoVo.getCommonId());
        createBo.setDeptId(infoVo.getSupplierDeptId());

        //默认总仓先
        createBo.setSourceOrgType(AccountOrgTypeEnum.REGION_WH.getCode());
        createBo.setSourceOrgId(infoVo.getRegionWhId());


        createBo.setRelateNo(infoVo.getBillCode());
        if (BillTypeEnum.STOCKOUT_RECORD.getCode().equals(infoVo.getBillType())) {
            StockoutDetailVO detailVO = iStockoutRecordService.getStockoutOrderCodeByCode(infoVo.getBillCode());
            if (ObjectUtil.isNotEmpty(detailVO)) {
                createBo.setOrderNo(detailVO.getOrderCode());
                createBo.setTransOrgId(detailVO.getCityWhId());
                createBo.setRelateId(detailVO.getId());
            }
        } else if (BillTypeEnum.MORE_GOODS_RECORD.getCode().equals(infoVo.getBillType())) {
            MoreGoodsDetailVO moreGoodsByCode = iMoreGoodsService.getMoreGoodsByCode(infoVo.getBillCode());
            if (ObjectUtil.isNotEmpty(moreGoodsByCode)) {
                createBo.setRelateId(moreGoodsByCode.getId());
                createBo.setTransOrgId(moreGoodsByCode.getCityWhId());
            }
        } else if (BillTypeEnum.ORDER.getCode().equals(infoVo.getBillType())) {
            GetInfoVo getInfoVo = iOrderService.queryByCode(infoVo.getBillCode());
            if (ObjectUtil.isNotEmpty(getInfoVo)) {
                createBo.setRelateId(getInfoVo.getId());
            }
        }

        if (AmountTypeEnum.LOSS.getCode().equals(infoVo.getAmountType())) {
            createBo.setTransAmt(infoVo.getAmount().negate());
        } else {
            createBo.setTransAmt(infoVo.getAmount());
        }
        if (ObjectUtil.isEmpty(infoVo.getDeductionReason())) {
            createBo.setRemark(ObjectUtil.isEmpty(DeductionTypeEnum.loadByCode(infoVo.getDeductionType())) ? null
                    : DeductionTypeEnum.loadByCode(infoVo.getDeductionType()).getDesc());
        } else {
            createBo.setRemark(infoVo.getDeductionReason());
        }

        //扣款单不需要传明细
    }

    private List<DeductionInfoVo> getDeductionInfoByParam(Date startDate, Date endDate) {
        DeductionInfoBo bo = new DeductionInfoBo();
        bo.setBillTimeStart(startDate);
        bo.setBillTimeEnd(endDate);
        bo.setStatus(DeductionInfoStatusEnum.COMMITTED.getCode());
        LambdaQueryWrapper<DeductionInfo> wrapper = this.buildQueryWrapper(bo);
        List<DeductionInfoVo> deductionInfoVos = baseMapper.selectVoList(wrapper);
        if (ObjectUtil.isEmpty(deductionInfoVos)) {
            return Collections.emptyList();
        }
        return deductionInfoVos;
    }

    private void convertSupplierSkuInfo(String supplierSkuId, DeductionInfo newData, Map<Long, RemoteSupplierSkuInfoVo> map) {
        List<Long> tmpSupplierSkuIds = Arrays.stream(supplierSkuId.split(StringUtils.SEPARATOR)).map(Long::parseLong).toList();
        List<Long> spuIds = new ArrayList<>();
        List<String> spuNameList = new ArrayList<>();
        for (Long tmpId : tmpSupplierSkuIds) {
            if (ObjectUtil.isNotEmpty(map.get(tmpId))) {
                RemoteSupplierSkuInfoVo skuInfoVo = map.get(tmpId);
                newData.setSaleTime(skuInfoVo.getSaleDate());
                spuIds.add(skuInfoVo.getSpuId());
                spuNameList.add(skuInfoVo.getSpuName());
            }
        }
        newData.setSpuId(StringUtils.join(spuIds, StringUtils.SEPARATOR));
        newData.setSpuName(StringUtils.join(spuNameList, StringUtils.SEPARATOR));
    }

    @Override
    public DeductionStatusCountVO getDeductionStatusCount(DeductionInfoBo bo) {
        if (ObjectUtil.isEmpty(bo)) {
            return null;
        }
        LambdaQueryWrapper<DeductionInfo> lqw = buildQueryWrapper(bo);
        Long count = baseMapper.selectCount(lqw);
        DeductionStatusCountVO vo = new DeductionStatusCountVO();
        vo.setStatus(bo.getStatus());
        vo.setStatusName(DeductionInfoStatusEnum.loadByCode(bo.getStatus()).getDesc());
        vo.setStatusCount(count.intValue());
        return vo;
    }

    @Override
    public void exportDeductionRecord(DeductionInfoBo bo) {
        LambdaQueryWrapper<DeductionInfo> lqw = buildQueryWrapper(bo);
        Page<DeductionInfoVo> result = baseMapper.selectVoPage(new PageQuery().build(), lqw);
        exportData(bo, ExportModuleEnum.DEDUCTION_RECORD, DeductionInfoVo.class, result);
    }

    private <T> void exportData(DeductionInfoBo bo, ExportModuleEnum moduleEnum, Class<T> clazz, Page<T> page) {
        if (CollUtil.isEmpty(page.getRecords())) {
            throw new ServiceException("没有数据可导出");
        }
        remoteExportJobService.addJob(moduleEnum, JSONUtil.toJsonStr(bo));
    }
}
