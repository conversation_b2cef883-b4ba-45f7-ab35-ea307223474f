package cn.xianlink.order.mapper;

import cn.xianlink.common.mybatis.core.mapper.BaseMapperPlus;
import cn.xianlink.order.domain.InvDutyFreeGoods;
import cn.xianlink.order.domain.vo.InvDutyFreeGoodsVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * 免税商品Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-30
 */
@Mapper
public interface InvDutyFreeGoodsMapper extends BaseMapperPlus<InvDutyFreeGoods, InvDutyFreeGoodsVo> {

    /**
     * 根据采购人员和分类查询有效的免税商品
     *
     * @param purchUserCode 采购人员代码
     * @param categoryId 商品分类ID
     * @param currentDate 当前日期
     * @return 免税商品列表
     */
    List<InvDutyFreeGoodsVo> selectValidByPurchUserAndCategory(
            @Param("purchUserCode") String purchUserCode,
            @Param("categoryId") Long categoryId,
            @Param("currentDate") LocalDate currentDate
    );

    /**
     * 根据分类ID查询有效的免税商品
     *
     * @param categoryId 商品分类ID
     * @param currentDate 当前日期
     * @return 免税商品列表
     */
    List<InvDutyFreeGoodsVo> selectValidByCategory(
            @Param("categoryId") Long categoryId,
            @Param("currentDate") LocalDate currentDate
    );

    /**
     * 查询即将过期的免税商品（7天内过期）
     *
     * @param currentDate 当前日期
     * @param expireDays 过期天数
     * @return 免税商品列表
     */
    List<InvDutyFreeGoodsVo> selectExpiringSoon(
            @Param("currentDate") LocalDate currentDate,
            @Param("expireDays") int expireDays
    );

}
