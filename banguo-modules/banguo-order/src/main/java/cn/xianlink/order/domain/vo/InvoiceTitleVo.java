package cn.xianlink.order.domain.vo;

import cn.xianlink.order.domain.InvoiceTitle;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import cn.xianlink.common.excel.annotation.ExcelDictFormat;
import cn.xianlink.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 发票抬头视图对象 inv_invoice_title
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Schema(description = "发票抬头视图对象")
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = InvoiceTitle.class)
public class InvoiceTitleVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 发票抬头ID (主键)
     */
    @Schema(description = "发票抬头ID", example = "1")
    @ExcelProperty(value = "发票抬头ID (主键)")
    private Long id;

    /**
     * 客户Id
     */
    @Schema(description = "客户ID", example = "1001")
    @ExcelProperty(value = "客户Id")
    private Long customerId;

    /**
     * 抬头名称 (如公司名)
     */
    @Schema(description = "发票抬头名称", example = "深圳XXYY科技有限公司")
    @ExcelProperty(value = "抬头名称 (如公司名)")
    private String titleName;

    /**
     * 税号 (公司类型必填)
     */
    @Schema(description = "税号", example = "91440011223344XXCC")
    @ExcelProperty(value = "税号 (公司类型必填)")
    private String taxNumber;

    /**
     * 地址
     */
    @Schema(description = "注册地址", example = "深圳市宝安区西乡街道XXYY路YXXX号XXYY大厦")
    @ExcelProperty(value = "地址")
    private String address;

    /**
     * 银行账号
     */
    @Schema(description = "银行账号", example = "*****************")
    @ExcelProperty(value = "银行账号")
    private String bankAccount;

    /**
     * 银行名称
     */
    @Schema(description = "银行名称", example = "中国工商银行")
    @ExcelProperty(value = "银行名称")
    private String bankName;

    /**
     * 电话
     */
    @Schema(description = "注册电话", example = "0755-xxxxxxxx")
    @ExcelProperty(value = "电话")
    private String phone;

    /**
     * 邮箱地址
     */
    @Schema(description = "邮箱地址", example = "<EMAIL>")
    @ExcelProperty(value = "邮箱地址")
    private String emailAddress;

    /**
     * 抬头类型 (company公司, personal个人)
     */
    @Schema(description = "抬头类型", example = "company", allowableValues = {"company", "personal"})
    @ExcelProperty(value = "抬头类型 (company公司, personal个人)")
    private String titleType;

    /**
     * 发票类型 (normal普票, special专票)
     */
    @Schema(description = "发票类型", example = "normal", allowableValues = {"normal", "special"})
    @ExcelProperty(value = "发票类型 (normal普票, special专票)")
    private String invoiceType;

    /**
     * 联系电话
     */
    @Schema(description = "联系电话", example = "13800138000")
    @ExcelProperty(value = "联系电话")
    private String contactPhone;

    /**
     * 所在地区
     */
    @ExcelProperty(value = "所在地区")
    private String region;

    /**
     * 详细地址
     */
    @ExcelProperty(value = "详细地址")
    private String detailedAddress;

    /**
     * 收件人姓名
     */
    @ExcelProperty(value = "收件人姓名")
    private String recipientName;

    /**
     * 纬度
     */
    @ExcelProperty(value = "纬度")
    private String latitude;

    /**
     * 经度
     */
    @ExcelProperty(value = "经度")
    private String longitude;

    /**
     * 创建用户代码
     */
    @ExcelProperty(value = "创建用户代码")
    private String createCode;

    /**
     * 创建用户名称
     */
    @ExcelProperty(value = "创建用户名称")
    private String createName;

    /**
     * 修改用户代码
     */
    @ExcelProperty(value = "修改用户代码")
    private String updateCode;

    /**
     * 修改用户名称
     */
    @ExcelProperty(value = "修改用户名称")
    private String updateName;


}
