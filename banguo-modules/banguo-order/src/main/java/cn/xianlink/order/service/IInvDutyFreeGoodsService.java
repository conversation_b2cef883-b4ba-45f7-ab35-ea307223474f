package cn.xianlink.order.service;

import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.order.domain.bo.InvDutyFreeGoodsBo;
import cn.xianlink.order.domain.vo.InvDutyFreeGoodsVo;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;

/**
 * 免税商品Service接口
 *
 * <AUTHOR>
 * @date 2025-01-30
 */
public interface IInvDutyFreeGoodsService {

    /**
     * 查询免税商品
     *
     * @param id 免税商品主键
     * @return 免税商品
     */
    InvDutyFreeGoodsVo queryById(Long id);

    /**
     * 查询免税商品列表
     *
     * @param bo 免税商品
     * @return 免税商品集合
     */
    TableDataInfo<InvDutyFreeGoodsVo> queryPageList(InvDutyFreeGoodsBo bo);

    /**
     * 查询免税商品列表
     *
     * @param bo 免税商品
     * @return 免税商品集合
     */
    List<InvDutyFreeGoodsVo> queryList(InvDutyFreeGoodsBo bo);

    /**
     * 根据采购人员和分类查询有效的免税商品
     *
     * @param purchUserCode 采购人员代码
     * @param categoryId 商品分类ID
     * @return 免税商品列表
     */
    List<InvDutyFreeGoodsVo> queryValidByPurchUserAndCategory(String purchUserCode, Long categoryId);

    /**
     * 根据分类ID查询有效的免税商品
     *
     * @param categoryId 商品分类ID
     * @return 免税商品列表
     */
    List<InvDutyFreeGoodsVo> queryValidByCategory(Long categoryId);

    /**
     * 查询即将过期的免税商品
     *
     * @param expireDays 过期天数，默认7天
     * @return 免税商品列表
     */
    List<InvDutyFreeGoodsVo> queryExpiringSoon(int expireDays);

    /**
     * 计算免税重量
     *
     * @param purchUserCode 采购人员代码
     * @param categoryId 商品分类ID
     * @param targetDate 目标日期
     * @return 免税重量
     */
    BigDecimal calculateDutyFreeWeight(String purchUserCode, Long categoryId, LocalDate targetDate);

    /**
     * 检查是否在免税期内
     *
     * @param purchUserCode 采购人员代码
     * @param categoryId 商品分类ID
     * @param targetDate 目标日期
     * @return 是否在免税期内
     */
    boolean isInDutyFreePeriod(String purchUserCode, Long categoryId, LocalDate targetDate);

    /**
     * 新增免税商品
     *
     * @param bo 免税商品
     * @return 结果
     */
    Boolean insertByBo(InvDutyFreeGoodsBo bo);

    /**
     * 修改免税商品
     *
     * @param bo 免税商品
     * @return 结果
     */
    Boolean updateByBo(InvDutyFreeGoodsBo bo);

    /**
     * 审核免税商品
     *
     * @param id 免税商品ID
     * @param status 审核状态：1审核通过；2审核不通过
     * @param remark 审核备注
     * @return 结果
     */
    Boolean auditById(Long id, Integer status, String remark);

    /**
     * 批量审核免税商品
     *
     * @param ids 免税商品ID集合
     * @param status 审核状态：1审核通过；2审核不通过
     * @param remark 审核备注
     * @return 结果
     */
    Boolean batchAudit(Collection<Long> ids, Integer status, String remark);

    /**
     * 校验并批量删除免税商品信息
     *
     * @param ids 需要删除的免税商品主键集合
     * @param isValid 是否校验,true-删除前校验,false-不校验
     * @return 结果
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

}
