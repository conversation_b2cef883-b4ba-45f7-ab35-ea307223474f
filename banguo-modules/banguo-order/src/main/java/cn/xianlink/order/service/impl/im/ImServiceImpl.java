package cn.xianlink.order.service.impl.im;


import cn.hutool.core.collection.CollectionUtil;
import cn.xianlink.common.api.enums.system.SysImGroupStatusEnum;
import cn.xianlink.order.config.TencentImProperties;
import cn.xianlink.order.domain.im.bo.ImBo;
import cn.xianlink.order.domain.im.vo.ImVo;
import cn.xianlink.order.enums.BizTypeEnum;
import cn.xianlink.order.service.im.IImService;
import cn.xianlink.order.service.im.IImStrategy;
import cn.xianlink.system.api.RemoteImGroupService;
import cn.xianlink.system.api.RemoteTencentImService;
import cn.xianlink.system.api.domain.vo.RemoteImGroupVo;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@CustomLog
@RequiredArgsConstructor
@Service
public class ImServiceImpl implements IImService {

    private final ImFactory strategyFactory;

    @DubboReference
    private transient RemoteImGroupService remoteImGroupService;

    @DubboReference
    private transient RemoteTencentImService tencentImService;

    private final TencentImProperties tencentImProperties;

    /**
     * 创建IM聊天
     *
     * @param req
     * @return
     */
    @Override
    public ImVo create(ImBo req) {
        BizTypeEnum bizTypeEnum = BizTypeEnum.loadByType(req.getBizType());
        IImStrategy strategy = strategyFactory.getStrategy(bizTypeEnum);
        return strategy.handle(req);
    }


    /**
     * 删除过期的IM群组
     * @return
     */
    @Override
    public boolean cleanupExpiredGroups() {
        // 1. 查询7天前创建的群组
        LocalDateTime minusDays = LocalDateTime.now().minusDays(tencentImProperties.getCleanupDay());
        List<RemoteImGroupVo> groupList = remoteImGroupService.getByLastMsgTime(minusDays, tencentImProperties.getCleanupSize());
        if (CollectionUtil.isEmpty(groupList)) {
            return false;
        }

        Map<String, RemoteImGroupVo> imGroupMap = groupList.stream()
                .collect(Collectors.toMap(RemoteImGroupVo::getGroupId, Function.identity(), (e1, e2) -> e1));

        // 3. 分批查询IM群组最后发送的时间
        // IM-默认接口请求频率限制：200次/秒,一次请求最多支持50个群组
        Map<String, Integer> lastMsgTimeMap = new HashMap<>();
        List<List<RemoteImGroupVo>> partitionedGroups = Lists.partition(groupList, 50);
        for (List<RemoteImGroupVo> batch : partitionedGroups) {
            List<String> groupIdList = batch.stream().map(RemoteImGroupVo::getGroupId).toList();
            lastMsgTimeMap.putAll(tencentImService.getGroupInfo(groupIdList));
            try {
                Thread.sleep(100L);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }

        // 4. 处理过期和未过期群组
        List<RemoteImGroupVo> toUpdateList = new ArrayList<>();
        List<RemoteImGroupVo> toDisbandList = new ArrayList<>();
        for (Map.Entry<String, Integer> entry : lastMsgTimeMap.entrySet()) {
            String groupId = entry.getKey();
            Integer lastMsgTimestamp = entry.getValue();
            RemoteImGroupVo group = imGroupMap.get(groupId);
            if (group == null) continue;
            // 群组不存在
            if(lastMsgTimestamp == 0){
                group.setStatus(SysImGroupStatusEnum.DISBAND.getCode());
                toUpdateList.add(group);
                continue;
            }

            LocalDateTime lastMsgTime = safeConvert(lastMsgTimestamp);
            if (minusDays.isAfter(lastMsgTime)) {
                toDisbandList.add(group);
            } else {
                LocalDateTime afterDays = lastMsgTime.plusDays(tencentImProperties.getCleanupDay()); //更新群组最后消息时间
                group.setLastMsgTime(Date.from(afterDays.atZone(ZoneId.systemDefault()).toInstant()));
                toUpdateList.add(group);
            }
        }

        if (CollectionUtil.isNotEmpty(toUpdateList)) {
            remoteImGroupService.updateBatchById(toUpdateList);
        }

        // 5. 解散过期群组
        for (RemoteImGroupVo group : toDisbandList) {
            try {
                boolean success = tencentImService.disbandGroup(group.getBizType(), group.getBizCode());
                if (success) {
                    log.info("群组 {} 解散成功", group.getGroupId());
                } else {
                    log.error("群组 {} 解散失败", group.getGroupId());
                }
            } catch (Exception e) {
                log.error("解散群组 {} 时出错", group.getGroupId(), e);
            }
        }
        return true;
    }

    public static LocalDateTime safeConvert(Integer timestampSecond) {
        if (timestampSecond == null || timestampSecond == 0) {
            return null;
        }
        return Instant.ofEpochMilli(timestampSecond.longValue() * 1000L)
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
    }
}