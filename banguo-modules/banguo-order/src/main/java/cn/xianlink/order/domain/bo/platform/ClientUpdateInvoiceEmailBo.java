package cn.xianlink.order.domain.bo.platform;

import cn.xianlink.common.core.validate.EditGroup;
import cn.xianlink.common.mybatis.core.domain.BaseEntity;
import cn.xianlink.order.domain.Invoice;
import cn.xianlink.order.domain.InvoiceTitle;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * 客户端更新发票邮箱业务对象
 *
 * 注意：系统字段(createCode,createName,updateCode,updateName)由系统自动维护，
 * 不需要前端传参，也不需要手动设置
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@AutoMapper(target = Invoice.class, reverseConvertGenerate = false)
public class ClientUpdateInvoiceEmailBo implements Serializable {

    /**
     * 发票ID
     */
    @NotNull(message = "发票ID不能为空", groups = {EditGroup.class})
    private Long invoiceId;

    /**
     * 客户ID（从登录用户获取，系统自动设置）
     */
    @JsonIgnore
    private Long customerId;

    /**
     * 新的邮箱地址
     */
    @NotBlank(message = "邮箱地址不能为空", groups = {EditGroup.class})
    @Email(message = "邮箱格式不正确", groups = {EditGroup.class})
    private String emailAddress;

}
