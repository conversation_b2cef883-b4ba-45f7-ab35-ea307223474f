package cn.xianlink.order.domain;

import cn.xianlink.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 免税商品对象 inv_duty_free_goods
 *
 * <AUTHOR>
 * @date 2025-01-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("inv_duty_free_goods")
public class InvDutyFreeGoods extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 免税重量(剩余)
     */
    private BigDecimal weight;

    /**
     * 免税重量(原始)
     */
    private BigDecimal origWeight;

    /**
     * 免税时间-开始
     */
    private LocalDate freeDateStart;

    /**
     * 免税时间-结束
     */
    private LocalDate freeDateEnd;

    /**
     * 采购人员代码
     */
    private String purchUserCode;

    /**
     * 采购人员名称
     */
    private String purchUserName;

    /**
     * 商品分类id
     */
    private Long categoryId;

    /**
     * 商品分类名称
     */
    private String categoryName;

    /**
     * 审核状态，0待审核；1审核通过；2审核不通过
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0代表存在 存入这条数据的主键代表删除）
     */
    @TableLogic(delval = "id")
    private Long delFlag;

}
