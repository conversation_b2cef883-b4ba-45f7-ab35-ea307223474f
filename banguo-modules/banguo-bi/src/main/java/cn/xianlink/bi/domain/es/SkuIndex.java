package cn.xianlink.bi.domain.es;

import cn.xianlink.common.api.enums.product.CategoryAfterTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;
import org.dromara.easyes.annotation.InnerIndexField;
import org.dromara.easyes.annotation.MultiIndexField;
import org.dromara.easyes.annotation.rely.Analyzer;
import org.dromara.easyes.annotation.rely.FieldType;
import org.dromara.easyes.annotation.rely.IdType;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 搜索商品的信息
 *
 * <AUTHOR>
 * @date 2025/7/23
 */
@Data
@EqualsAndHashCode
@IndexName(value = "banguo_sku", aliasName = "bg_sku")
public class SkuIndex implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @IndexId(type = IdType.CUSTOMIZE)
    private Long id;

    /**
     *
     */
    @IndexField(fieldType = FieldType.LONG)
    private Long skuId;

    /**
     * sku 编码
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String skuCode;


    /**
     * 绑定的批次id
     */
    @IndexField(fieldType = FieldType.LONG)
    private Long supplierSkuId;

    /**
     * 供应商批次商品唯一编码
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String code;

    /**
     * 商品品种id
     */
    @IndexField(fieldType = FieldType.LONG)
    private Long categoryId;

    /**
     * 商品品种
     */
    @IndexField(fieldType = FieldType.TEXT, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
    private String categoryPathName;

    /**
     * 平台商品id
     */
    @IndexField(fieldType = FieldType.LONG)
    private Long spuId;

    /**
     * 平台商品唯一编码
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String spuCode;

    /**
     * 平台商品名称
     */
    @MultiIndexField(mainIndexField = @IndexField(fieldType = FieldType.KEYWORD), otherIndexFields = {@InnerIndexField(suffix = "zh", fieldType = FieldType.TEXT, analyzer = "ik_max_word", searchAnalyzer = "ik_smart"), @InnerIndexField(suffix = "pinyin", fieldType = FieldType.TEXT, analyzer = Analyzer.PINYIN)})
    private String spuName;

    /**
     * 平台商品等级，数据字典配置
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String spuGrade;

    /**
     * 等级说明
     */
    @IndexField(fieldType = FieldType.TEXT)
    private String spuGradeDesc;

    /**
     * 平台商品规格
     */
    @IndexField(fieldType = FieldType.TEXT)
    private String spuStandards;


    /**
     * 进出口：0国产 1进口
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer domestic;


    /**
     * 平台商品毛重(斤)
     */
    @IndexField(fieldType = FieldType.DOUBLE)
    private BigDecimal spuGrossWeight;

    /**
     * 平台商品净重(斤)
     */
    @IndexField(fieldType = FieldType.DOUBLE)
    private BigDecimal spuNetWeight;

    /**
     * 供应商商品id
     */
    @IndexField(fieldType = FieldType.LONG)
    private Long supplierSpuId;

    /**
     * 供应商子单位id
     */
    @IndexField(fieldType = FieldType.LONG)
    private Long supplierDeptId;

    /**
     * 供应商商品唯一编码
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String supplierSpuCode;

    /**
     * 装卸队id
     */
    @IndexField(fieldType = FieldType.LONG)
    private Long basPortageTeamId;

    /**
     * 供货总仓id
     */
    @IndexField(fieldType = FieldType.LONG)
    private Long provideRegionWhId;

    /**
     * 总仓id
     */
    @IndexField(fieldType = FieldType.LONG)
    private Long regionWhId;

    /**
     * 总仓唯一编码
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String regionWhCode;

    /**
     * 总仓名称
     */
    @IndexField(fieldType = FieldType.TEXT)
    private String regionWhName;

    /**
     * 供应商id
     */
    @IndexField(fieldType = FieldType.LONG)
    private Long supplierId;

    /**
     * 供应商唯一编码
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String supplierCode;

    /**
     * 供应商名称
     */
    @IndexField(fieldType = FieldType.TEXT)
    private String supplierName;

    /**
     * 产地
     */
    @MultiIndexField(mainIndexField = @IndexField(fieldType = FieldType.KEYWORD), otherIndexFields = {@InnerIndexField(suffix = "zh", fieldType = FieldType.TEXT, analyzer = "ik_max_word", searchAnalyzer = "ik_smart"), @InnerIndexField(suffix = "pinyin", fieldType = FieldType.TEXT, analyzer = Analyzer.PINYIN)})
    private String producer;

    /**
     * 是否战略合作品，0为否，1为是
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer cooperation;

    /**
     * 是否特价商品，0为否，1为是
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer bargain;

    /**
     * 特价商品-降价率
     */
    @IndexField(fieldType = FieldType.DOUBLE)
    private BigDecimal bargainRate;

    /**
     * 市场价，行情价
     */
    @IndexField(fieldType = FieldType.DOUBLE)
    private BigDecimal marketPrice;

    /**
     * 礼盒
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer giftBox;

    /**
     * 小件，0为否，1为是
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer little;

    /**
     * 好吃
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer goodToEat;

    /**
     * 寿光蔬菜
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer shouguangVegetables;

    /**
     * 简介
     */
    @IndexField(fieldType = FieldType.TEXT)
    private String snapshot;

    /**
     * 单件价格
     */
    @IndexField(fieldType = FieldType.DOUBLE)
    private BigDecimal price;

    /**
     * 上架价格
     */
    @IndexField(fieldType = FieldType.DOUBLE)
    private BigDecimal upPrice;

    /**
     * 销售日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd")
    private Date saleDate;

    /**
     * 明日预测
     */
    @IndexField(fieldType = FieldType.TEXT)
    private String predictionTomorrow;

    /**
     * 未来预测
     */
    @IndexField(fieldType = FieldType.TEXT)
    private String predictionFuture;

    /**
     * 包装上的字
     */
    @IndexField(fieldType = FieldType.TEXT)
    private String packageWord;

    /**
     * 包装容器-框，泡沫箱，袋等
     */
    @IndexField(fieldType = FieldType.TEXT)
    private String packageType;

    /**
     * 最小甜度
     */
    @IndexField(fieldType = FieldType.DOUBLE)
    private BigDecimal sweetMin;

    /**
     * 最大甜度
     */
    @IndexField(fieldType = FieldType.DOUBLE)
    private BigDecimal sweetMax;

    /**
     * 下单倍数
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer placeOrderMultiple;

    /**
     * 最小购买数量，为-1不限制
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer buyMin;

    /**
     * 最大购买数量，为-1不限制
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer buyMax;

    /**
     * 采购员code
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String buyerCode;

    /**
     * 采购员name
     */
    @IndexField(fieldType = FieldType.TEXT)
    private String buyerName;

    /**
     * 售后规则：0无售后、1正常售后、2库管验货
     *
     * @see CategoryAfterTypeEnum
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer afterSaleType;

    /**
     * 售后天数：0无售后，其它暂时写死 1
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer afterSaleDay;

    /**
     * 是否免检，0表示不是免检,1表示免检
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer isCheck;

    /**
     * 免赔情况
     */
    @IndexField(fieldType = FieldType.TEXT)
    private String deductibleSituation;

    /**
     * 可申请售后说明
     */
    @IndexField(fieldType = FieldType.TEXT)
    private String afterSaleExplain;

    /**
     * 入口隐藏，1隐藏，2不隐藏
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer entranceHide;

    /**
     * 瀑布流标签，来源数据字典
     */
    @IndexField(fieldType = FieldType.TEXT)
    private String waterfall;

    /**
     * 批次条形码
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String skuLabel;

    /**
     * 上架日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date upTime;

    /**
     * 下架日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date downTime;

    /**
     * 过季日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date outTime;

    /**
     * 状态(状态,1提交/待审核,2驳回,3仅审核通过,4上架,5下架,6过季,7隐藏)
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer status;

    /**
     * 批次类型，1正常，2尾货，3后台
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer batchType;

    /**
     * 业务类型，1市采, 10地采, 20基采, 30产地
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer businessType;

    /**
     * 换供应商的原始供应商id
     */
    @IndexField(fieldType = FieldType.LONG)
    private Long originalSupplierId;

    /**
     * 换供应商的原始供应商批次id
     */
    @IndexField(fieldType = FieldType.LONG)
    private Long originalSupplierSkuId;

    /**
     * 删除标志（0代表存在 存入这条数据的主键代表删除）
     */
    @IndexField(fieldType = FieldType.LONG)
    private Long delFlag;


    /**
     * 区域编码
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String areaCode;

    /**
     * 品牌
     */
    @MultiIndexField(mainIndexField = @IndexField(fieldType = FieldType.KEYWORD), otherIndexFields = {@InnerIndexField(suffix = "zh", fieldType = FieldType.TEXT, analyzer = "ik_max_word", searchAnalyzer = "ik_smart"), @InnerIndexField(suffix = "pinyin", fieldType = FieldType.TEXT, analyzer = Analyzer.PINYIN)})
    private String brand;

    /**
     * 已售-库存表冗余作排序
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer copySold;

    /**
     * 产地简称
     */
    @MultiIndexField(mainIndexField = @IndexField(fieldType = FieldType.KEYWORD), otherIndexFields = {@InnerIndexField(suffix = "zh", fieldType = FieldType.TEXT, analyzer = "ik_max_word", searchAnalyzer = "ik_smart"), @InnerIndexField(suffix = "pinyin", fieldType = FieldType.TEXT, analyzer = Analyzer.PINYIN)})
    private String shortProducer;


    /**
     * 是否送货审核 - 0否df 1是
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer hasDeliveryAudit;

    /**
     * 通过送货审核 - 0否df 1是
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer passDeliveryAudit;

    /**
     * 商品名称
     */
    @MultiIndexField(mainIndexField = @IndexField(fieldType = FieldType.KEYWORD), otherIndexFields = {@InnerIndexField(suffix = "zh", fieldType = FieldType.TEXT, analyzer = "ik_max_word", searchAnalyzer = "ik_smart"), @InnerIndexField(suffix = "pinyin", fieldType = FieldType.TEXT, analyzer = Analyzer.PINYIN)})
    private String categoryName;

    /**
     * 检索全称
     */
    @IndexField(fieldType = FieldType.TEXT, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
    private String fullName;

    /**
     * 集采是否隐藏 1-是
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer isSaleHide;

    /**
     * 商品二级分类
     */
    @IndexField(fieldType = FieldType.LONG)
    private Long categoryIdLevel2;

    /**
     * 关联skuId
     */
    @IndexField(fieldType = FieldType.LONG)
    private Long relationSkuId;

    /**
     * 补贴金额
     */
    @IndexField(fieldType = FieldType.DOUBLE)
    private BigDecimal subsidyAmount;

    /**
     * 销售类型：1为般果代采，2为商家自营
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer saleType;

    /**
     * tenantId
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String tenantId;

    /**
     * createCode
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String createCode;

    /**
     * createName
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String createName;

    /**
     * createTime
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * updateCode
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String updateCode;

    /**
     * updateName
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String updateName;

    /**
     * updateTime
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 件规格
     */
    @IndexField(fieldType = FieldType.TEXT)
    private String packageStandards;

    /**
     * 客户门店类型
     */
    @IndexField(fieldType = FieldType.TEXT, fieldData = true)
    private String customerStoreTypes;

    /**
     * 行情预测类型
     */
    @IndexField(fieldType = FieldType.TEXT)
    private String marketForecastType;

    /**
     * discountPrice
     */
    @IndexField(fieldType = FieldType.DOUBLE)
    private BigDecimal discountPrice;

    /**
     * 自动补齐
     * suggestSearch
     */
    @IndexField(fieldType = FieldType.COMPLETION)
    private CompletionObject suggestSearch;

    /**
     * 是否售罄
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer isOut;

    /**
     * 排序分值
     */
    @IndexField(fieldType = FieldType.FLOAT)
    private Float sortingScore;

    /**
     * 开票类型
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String invoiceType;

    /**
     * 促销价格
     */
    @IndexField(fieldType = FieldType.DOUBLE)
    private BigDecimal promoPrice;


    /**
     * sku 券模板标签
     */
    @IndexField(fieldType = FieldType.NESTED, nestedOrObjectClass = SkuCouponAttrValue.class)
    private List<SkuCouponAttrValue> coupon;

    /**
     * sku 营销标签
     */
    @IndexField(fieldType = FieldType.NESTED, nestedOrObjectClass = SkuMarketingAttrValue.class)
    private List<SkuMarketingAttrValue> marketing;

}