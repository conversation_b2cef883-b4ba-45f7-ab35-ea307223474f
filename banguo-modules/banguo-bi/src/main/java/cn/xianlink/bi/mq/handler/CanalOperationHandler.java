package cn.xianlink.bi.mq.handler;

import cn.xianlink.bi.domain.vo.CanalMessageVo;

/**
 * Canal操作处理策略接口
 * 
 * <AUTHOR>
 */
public interface CanalOperationHandler {

    /**
     * 处理Canal消息
     * 
     * @param canalMessageVo Canal消息对象
     */
    void handle(CanalMessageVo canalMessageVo);

    /**
     * 获取支持的操作类型
     * 
     * @return 操作类型：INSERT/UPDATE/DELETE
     */
    String getSupportedOperationType();
} 