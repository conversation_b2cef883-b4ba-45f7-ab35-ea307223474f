package cn.xianlink.bi.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.basic.api.RemoteCityWhPlaceService;
import cn.xianlink.basic.api.RemoteRegionLogisticsService;
import cn.xianlink.basic.api.RemoteSupplierService;
import cn.xianlink.basic.api.domain.bo.RemoteRegionLogisticsQueryBo;
import cn.xianlink.basic.api.domain.vo.RemoteCityWhPlaceVo;
import cn.xianlink.basic.api.domain.vo.RemoteRegionLogisticsVo;
import cn.xianlink.basic.api.domain.vo.RemoteSupplierVo;
import cn.xianlink.bi.domain.*;
import cn.xianlink.bi.domain.bo.RwWaitEntruckQueryBo;
import cn.xianlink.bi.domain.bo.SupWaitDeliveryQueryBo;
import cn.xianlink.bi.domain.bo.TodoBo;
import cn.xianlink.bi.domain.vo.*;
import cn.xianlink.bi.mapper.*;
import cn.xianlink.bi.mapper.product.IBiSupplierSkuMapper;
import cn.xianlink.common.api.enums.order.*;
import cn.xianlink.common.api.enums.product.SupplierSkuSubsidyAuditStatusEnum;
import cn.xianlink.common.api.util.SaleDateUtil;
import cn.xianlink.common.core.enums.YNStatusEnum;
import cn.xianlink.order.api.RemoteInvoiceService;
import cn.xianlink.order.api.RemoteOrderService;
import cn.xianlink.order.api.bo.RemoteInvoiceBo;
import cn.xianlink.order.api.vo.RemoteInvoiceVo;
import cn.xianlink.product.api.RemoteCwStockService;
import cn.xianlink.product.api.RemoteSupplierSkuService;
import cn.xianlink.product.api.domain.bo.RemoteDeliveryAuditBo;
import cn.xianlink.product.api.domain.bo.RemoteQueryInfoListBo;
import cn.xianlink.product.api.domain.vo.RemoteCwStockVo;
import cn.xianlink.product.api.domain.vo.RemoteSupplierSkuInfoVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
@RefreshScope
public class TodoService {
    private final transient RegionWhMapper regionWhMapper;
    private final transient SupDeliveryGoodsMapper supDeliveryGoodsMapper;
    private final transient RwEntruckGoodsMapper rwEntruckGoodsMapper;
    private final transient RwEntruckRecordMapper rwEntruckRecordMapper;
    private final transient RwEntruckMapper entruckMapper;
    private final transient ReportLossOrderMapper reportLossOrderMapper;
    private final transient StockoutRecordMapper stockoutRecordMapper;
    private final transient SortGoodsMapper sortGoodsMapper;
    private final transient SupplierSkuDisableCityWhMapper supplierSkuDisableCityWhMapper;
    private final transient IBiSupplierSkuMapper supplierSkuMapper;

    @DubboReference
    private final transient RemoteOrderService orderService;
    @DubboReference
    private final transient RemoteSupplierService remoteSupplierService;
    @DubboReference
    private final transient RemoteSupplierSkuService remoteSupplierSkuService;
    @DubboReference
    private final transient RemoteCityWhPlaceService remoteCityWhPlaceService;
    @DubboReference
    private final transient RemoteRegionLogisticsService remoteRegionLogisticsService;
    @DubboReference
    private final transient RemoteCwStockService remoteCwStockService;
    @DubboReference
    private final transient RemoteInvoiceService remoteInvoiceService;

    /**
     * 是否检验供应商待送货需要总仓审核通过
     */
    @Value("${banguo-bi.todo_days.wait_delivery:7}")
    private Integer waitDeliveryDays;

    public Integer getWaitDeliveryDays() {
        return waitDeliveryDays > 0 ? waitDeliveryDays - 1 : 1;
    }

    public void loadSupplierInfo(List<TodoGoodsVo> list) {
        if (list.size() == 0) {
            return;
        }
        // 供应商名称
        List<Long> supplierIds = list.stream().map(TodoGoodsVo::getSupplierId).distinct().toList();
        Map<Long, RemoteSupplierVo> supplierMap = remoteSupplierService.getSupplierByIds(supplierIds)
                .stream().collect(Collectors.toMap(RemoteSupplierVo::getId, Function.identity()));
        list.forEach(goodsVo -> {
            if (supplierMap.containsKey(goodsVo.getSupplierId())) {
                goodsVo.setSupplierName(supplierMap.get(goodsVo.getSupplierId()).getName());
                goodsVo.setSupplierAlias(supplierMap.get(goodsVo.getSupplierId()).getAlias());
            }
        });
    }

    public void loadLogisticsInfo(List<TodoGoodsVo> list) {
        if (list.size() == 0) {
            return;
        }
        // 物流
        List<Long> logisticsIds = list.stream().map(TodoGoodsVo::getLogisticsId).distinct().toList();
        RemoteRegionLogisticsQueryBo queryBo = new RemoteRegionLogisticsQueryBo();
        queryBo.setLogisticsIds(logisticsIds);
        queryBo.setAllStatus(true);
        Map<Long, RemoteRegionLogisticsVo> logisticsMap = remoteRegionLogisticsService.queryList(queryBo)
                .stream().collect(Collectors.toMap(RemoteRegionLogisticsVo::getId, Function.identity()));
        // 提货点、城市仓
        List<Long> placeIds = logisticsMap.values().stream().map(RemoteRegionLogisticsVo::getPlaceId).distinct().toList();
        Map<Long, RemoteCityWhPlaceVo> placeMap = remoteCityWhPlaceService.queryList(placeIds).stream().collect(Collectors.toMap(RemoteCityWhPlaceVo::getId, Function.identity()));
        list.forEach(goodsVo -> {
            RemoteRegionLogisticsVo logisticsVo = logisticsMap.get(goodsVo.getLogisticsId());
            if (logisticsVo != null) {
                goodsVo.setLogisticsName(logisticsVo.getLogisticsName());
                goodsVo.setCityWhName(placeMap.getOrDefault(logisticsVo.getPlaceId(), new RemoteCityWhPlaceVo()).getCityWhName());
                goodsVo.setPlaceName(placeMap.getOrDefault(logisticsVo.getPlaceId(), new RemoteCityWhPlaceVo()).getPlaceName());
            }
        });
    }

    public void loadSkuInfo(List<TodoGoodsVo> list) {
        this.loadSkuInfo(list, null);
    }

    public void loadSkuInfo(List<TodoGoodsVo> list, Integer passAudit) {
        if (list.size() == 0) {
            return;
        }
        RemoteQueryInfoListBo skuBo = new RemoteQueryInfoListBo();
        skuBo.setSupplierSkuIdList(list.stream().map(TodoGoodsVo::getSupplierSkuId).distinct().toList());
        // 获取不需要审核或审核通过的数据: 0-否，1-是
        skuBo.setPassAudit(passAudit);
        Map<Long, RemoteSupplierSkuInfoVo> skuInfoMap = remoteSupplierSkuService.queryInfoList(skuBo)
                .stream().collect(Collectors.toMap(RemoteSupplierSkuInfoVo::getId, Function.identity()));
        list.forEach(e -> {
            RemoteSupplierSkuInfoVo skuInfoVo = skuInfoMap.get(e.getSupplierSkuId());
            if (ObjectUtil.isNotNull(skuInfoVo)) {
                e.setQuerySkuIsNotNull(true);
                e.setSpuGrade(skuInfoVo.getSpuGrade());
                e.setSpuStandards(skuInfoVo.getSpuStandards());
                e.setProducer(skuInfoVo.getProducer());
                e.setPackageWord(skuInfoVo.getPackageWord());
                // 产地简称
                e.setBrand(skuInfoVo.getBrand());
                e.setShortProducer(skuInfoVo.getShortProducer());
                if (e.getSpuNetWeight() == null) {
                    e.setSpuNetWeight(skuInfoVo.getSpuNetWeight());
                    e.setSpuGrossWeight(skuInfoVo.getSpuGrossWeight());
                }
                if (e.getBusinessType() == null) {
                    e.setBusinessType(skuInfoVo.getBusinessType());
                }
                if (e.getSupplierId() == null) {
                    e.setSupplierId(skuInfoVo.getSupplierId());
                }
                if (e.getBuyerName() == null) {
                    e.setBuyerName(skuInfoVo.getBuyerName());
                }
            }
        });
    }

    /**
     * 待送货、待分拣
     */
    // @Cacheable(value = "statistics:bi:todo:new:waitdeliverylist:cache#5m", key = "#bo.cacheUserId + '_' + #bo.regionWhId + '_' + #bo.source")
    public TodoInfo waitDeliveryList(TodoBo bo) {
        LocalDate saleDate = this.getSaleDate(bo.getRegionWhId());
        SupWaitDeliveryQueryBo queryBo = new SupWaitDeliveryQueryBo();
        BeanUtils.copyProperties(bo, queryBo);
        queryBo.setSaleDateStart(saleDate.plusDays(-getWaitDeliveryDays()));
        queryBo.setSaleDateEnd(saleDate);
        queryBo.setPayTime(orderService.getDelayQueryTime());
        if (bo.getSource().equals(OrderDeliverSourceEnum.SUPPLIER.getCode())) {
            queryBo.setBusinessTypes(ListUtil.toList(OrderBusinessTypeEnum.BUSINESS_TYPE1.getCode()));
            queryBo.setPassAudit(1);
        } else if (bo.getSource().equals(OrderDeliverSourceEnum.REGION_PICKING.getCode())) {
            queryBo.setBusinessTypes(ListUtil.toList(OrderBusinessTypeEnum.BUSINESS_TYPE20.getCode(), OrderBusinessTypeEnum.BUSINESS_TYPE30.getCode()));
            queryBo.setPassAudit(1);
        }
        int waitDeliveryDays = this.waitDeliveryDays;
        // 提前发车，只查当前销售日
        if (YNStatusEnum.ENABLE.getCode().equals(bo.getIsEarlyEnd())) {
            queryBo.setSaleDateStart(saleDate);
            waitDeliveryDays = 1;
        }
        List<TodoGoodsVo> list = rwEntruckGoodsMapper.waitDeliveryList(queryBo);
        if (CollUtil.isNotEmpty(list)) {
            this.loadSkuInfo(list, queryBo.getPassAudit());
            this.loadSupplierInfo(list);
            list = list.stream().filter(f -> f.getQuerySkuIsNotNull()).collect(Collectors.toList());
        }
        return new TodoInfo(list, waitDeliveryDays);
    }


    /**
     * 送货待审核
     */
    // @Cacheable(value = "statistics:bi:todo:new:regiondeliveryauditlist:cache#5m", key = "#bo.cacheUserId + '_' + #bo.regionWhId")
    public TodoInfo regionDeliveryAuditList(TodoBo bo) {
        LocalDate saleDate = this.getSaleDate(bo.getRegionWhId());
        RemoteDeliveryAuditBo queryBo = new RemoteDeliveryAuditBo();
        queryBo.setRegionWhId(bo.getRegionWhId());
        queryBo.setSaleDateStart(saleDate);
        queryBo.setSaleDateEnd(saleDate);
        List<RemoteSupplierSkuInfoVo> skuInfoVos = remoteSupplierSkuService.queryDeliveryAuditList(queryBo);
        List<TodoGoodsVo> list = new ArrayList<>();
        skuInfoVos.forEach(skuVo -> {
            TodoGoodsVo goodsVo = new TodoGoodsVo();
            BeanUtils.copyProperties(skuVo, goodsVo);
            list.add(goodsVo);
        });
        this.loadSupplierInfo(list);
        return new TodoInfo(list, 1);
    }

    /**
     * 待质检
     */
    // @Cacheable(value = "statistics:bi:todo:new:waitinspectgoodslist:cache#5m", key = "#bo.cacheUserId + '_' + #bo.regionWhId + '_' + #bo.source")
    public TodoInfo waitInspectGoodsList(TodoBo bo) {
        LocalDate saleDate = this.getSaleDate(bo.getRegionWhId());
        SupWaitDeliveryQueryBo queryBo = new SupWaitDeliveryQueryBo();
        BeanUtils.copyProperties(bo, queryBo);
        queryBo.setSaleDateStart(saleDate.plusDays(-getWaitDeliveryDays()));
        queryBo.setSaleDateEnd(saleDate);
        List<TodoGoodsVo> list = supDeliveryGoodsMapper.waitInspectGoodsList(queryBo);
        this.loadSkuInfo(list);
        this.loadSupplierInfo(list);
        return new TodoInfo(list, waitDeliveryDays);
    }

    /**
     * 待装车
     * entruckType类型； 1嘉兴分货(市采)， 2天津分货(基采、产地),  3天津分拣(基采、产地)
     */
    // @Cacheable(value = "statistics:bi:todo:new:waitentruckgoodslist:cache#5m", key = "#bo.cacheUserId + '_' + #bo.regionWhId + '_' + #bo.source + '_' + #entruckType")
    public TodoInfo waitEntruckGoodsList(TodoBo bo, Integer entruckType) {
        LocalDate saleDate = this.getSaleDate(bo.getRegionWhId());
        RwWaitEntruckQueryBo queryBo = new RwWaitEntruckQueryBo();
        BeanUtils.copyProperties(bo, queryBo);
        queryBo.setSaleDateStart(saleDate.plusDays(-getWaitDeliveryDays()));
        queryBo.setSaleDateEnd(saleDate);
        queryBo.setEntruckType(entruckType);
        List<TodoGoodsVo> list = rwEntruckGoodsMapper.waitEntruckGoodsList(queryBo);
        this.loadSkuInfo(list);
        this.loadSupplierInfo(list);
        return new TodoInfo(list, waitDeliveryDays);
    }

    // @Cacheable(value = "statistics:bi:todo:new:waitcreateentrucknocount:cache#5m", key = "#bo.cacheUserId + '_' + #bo.regionWhId")
    public TodoInfo waitCreateEntruckNoCount(TodoBo bo) {
        LocalDate saleDate = this.getSaleDate(bo.getRegionWhId());
        RwWaitEntruckQueryBo queryBo = new RwWaitEntruckQueryBo();
        queryBo.setRegionWhId(bo.getRegionWhId());
        queryBo.setSaleDateStart(saleDate.plusDays(-getWaitDeliveryDays()));
        queryBo.setSaleDateEnd(saleDate);
        List<RwEntruckRecordSumVo> recordList = rwEntruckRecordMapper.waitCreateEntruckNoList(queryBo);
        List<TodoGoodsVo> list = new ArrayList<>();
        for (RwEntruckRecordSumVo record : recordList) {
            TodoGoodsVo goodsVo = new TodoGoodsVo();
            BeanUtils.copyProperties(record, goodsVo);
            list.add(goodsVo);
        }
        return new TodoInfo(list, waitDeliveryDays);
    }

    // @Cacheable(value = "statistics:bi:todo:new:waitcreateentrucknocount:cache#5m", key = "#bo.cacheUserId + '_' + #bo.regionWhId + '_' + #bo.cityWhId + '_' + #status")
    public TodoInfo waitDepartCount(TodoBo bo, Integer status) {
        LambdaQueryWrapper<RwEntruck> lqw = Wrappers.lambdaQuery();
        lqw.eq(RwEntruck::getStatus, status);
        if (status.intValue() == DeliveryStatusEnum.WAIT_DEPART.getCode()) {
            lqw.eq(RwEntruck::getRegionWhId, bo.getRegionWhId());
        } else if (status.intValue() == DeliveryStatusEnum.COMPLETE_DEPART.getCode()) {
            lqw.eq(RwEntruck::getCityWhId, bo.getCityWhId());
        }
        List<RwEntruck> rwEntruckList = entruckMapper.selectList(lqw);
        List<TodoGoodsVo> list = new ArrayList<>();
        for (RwEntruck entruck : rwEntruckList) {
            TodoGoodsVo goodsVo = new TodoGoodsVo();
            BeanUtils.copyProperties(entruck, goodsVo);
            list.add(goodsVo);
        }
        return new TodoInfo(list, null);
    }

    // @Cacheable(value = "statistics:bi:todo:new:supreportlossauditcount:cache#5m", key = "#bo.cacheUserId + '_' + #bo.regionWhId")
    public TodoInfo supReportLossAuditCount(TodoBo bo) {
        LambdaQueryWrapper<ReportLossOrder> lqw = Wrappers.lambdaQuery();
        lqw.eq(ReportLossOrder::getLossStatus, ReportLossStatusEnum.WAIT_AUDIT.getCode());
        lqw.eq(ReportLossOrder::getRegionWhId, bo.getRegionWhId());
        lqw.eq(ReportLossOrder::getSupplierId, bo.getSupplierId());
        lqw.eq(ObjectUtil.isNotNull(bo.getSupplierDeptId()), ReportLossOrder::getSupplierDeptId, bo.getSupplierDeptId());
        List<ReportLossOrder> reportLossList = reportLossOrderMapper.selectList(lqw);
        List<TodoGoodsVo> list = new ArrayList<>();
        for (ReportLossOrder loss : reportLossList) {
            TodoGoodsVo goodsVo = new TodoGoodsVo();
            BeanUtils.copyProperties(loss, goodsVo);
            list.add(goodsVo);
        }
        return new TodoInfo(list, null);
    }

    /**
     * 获取未确认缺货少货单列表
     *
     * @param type 类型 1缺货 2少货 3未判责少货
     */
    // @Cacheable(value = "statistics:bi:todo:new:waitconfirmstockoutlist:cache#5m", key = "#bo.cacheUserId + '_' + #bo.regionWhId + '_' + #bo.cashKey + '_' + #bo.source + '_' + #type")
    public TodoInfo waitConfirmStockOutList(TodoBo bo, Integer type) {
        LambdaQueryWrapper<StockoutRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(StockoutRecord::getDelFlag, 0);
        if (type == 3) {
            lqw.eq(StockoutRecord::getBlameStatus, BlameStatusEnum.STATE_BLAME.getCode());
            lqw.eq(StockoutRecord::getBusinessType, OrderBusinessTypeEnum.BUSINESS_TYPE1.getCode());
            lqw.eq(StockoutRecord::getType, 2);
        } else {
            lqw.eq(StockoutRecord::getAuditStatus, StockoutAuditStatusEnum.STATE_CONFIRM.getCode());
            lqw.eq(StockoutRecord::getType, type);
        }
        // 总仓查询
        lqw.eq(ObjectUtil.isNotNull(bo.getRegionWhId()), StockoutRecord::getRegionWhId, bo.getRegionWhId());
        lqw.eq(ObjectUtil.isNotNull(bo.getBuyerId()), StockoutRecord::getBuyerId, bo.getBuyerId());
        // 城市仓查询
        lqw.eq(ObjectUtil.isNotNull(bo.getCityWhId()), StockoutRecord::getCityWhId, bo.getCityWhId());
        // 供应商查询
        lqw.eq(ObjectUtil.isNotNull(bo.getSupplierId()), StockoutRecord::getSupplierId, bo.getSupplierId());

        lqw.select(StockoutRecord::getSupplierId, StockoutRecord::getSaleDate, StockoutRecord::getLogisticsId,
                StockoutRecord::getSupplierSkuId, StockoutRecord::getSpuName, StockoutRecord::getBuyerName,
                StockoutRecord::getStockoutCount);
        if (CollUtil.isNotEmpty(bo.getLogisticsIds()) && CollUtil.isNotEmpty(bo.getPlaceIdLevel2List())) {
            lqw.in(StockoutRecord::getLogisticsId, bo.getLogisticsIds());
            bo.getPlaceIdLevel2List().add(0L);
            lqw.in(StockoutRecord::getPlaceIdLevel2, bo.getPlaceIdLevel2List());
        } else if (CollUtil.isNotEmpty(bo.getLogisticsIds()) && CollUtil.isEmpty(bo.getPlaceIdLevel2List())) {
            lqw.in(StockoutRecord::getLogisticsId, bo.getLogisticsIds());
        } else if (CollUtil.isEmpty(bo.getLogisticsIds()) && CollUtil.isNotEmpty(bo.getPlaceIdLevel2List())) {
            lqw.in(StockoutRecord::getPlaceIdLevel2, bo.getPlaceIdLevel2List());
        }
        lqw.orderByDesc(StockoutRecord::getId);
        List<StockoutRecord> stockOutList = stockoutRecordMapper.selectList(lqw);
        List<TodoGoodsVo> list = new ArrayList<>();
        for (StockoutRecord record : stockOutList) {
            TodoGoodsVo goodsVo = new TodoGoodsVo();
            BeanUtils.copyProperties(record, goodsVo);
            goodsVo.setSkuQuantity(record.getStockoutCount());
            list.add(goodsVo);
        }
        this.loadSkuInfo(list);
        this.loadLogisticsInfo(list);
        this.loadSupplierInfo(list);
        return new TodoInfo(list, null);
    }


    /**
     * 查询待确定的禁用sku数
     * @param cityWhId
     * @return
     */
    public TodoVo getWaitDeliveryTodo(Long cityWhId) {

        LambdaQueryWrapper<SupplierSkuDisableCityWh> eq = new LambdaQueryWrapper<SupplierSkuDisableCityWh>()
                .eq(SupplierSkuDisableCityWh::getCityWhId, cityWhId)
                .eq(SupplierSkuDisableCityWh::getStatus, 0)
                // 下面条件删掉历史数据后可删
                .ne(SupplierSkuDisableCityWh::getSkuId, 0)
                ;
        Long l = supplierSkuDisableCityWhMapper.selectCount(eq);

        TodoVo todoVo = new TodoVo();
        todoVo.setTodoCode(ToDosEnum.AUDIT_GOODS_DISABLE_CITY_WH.getCode());
        todoVo.setSkuCount(l.intValue());
        return todoVo;
    }

    /**
     * 获取未完成分货列表
     *
     * @param status 1待分货 2分货中 3已分货
     */
    // @Cacheable(value = "statistics:bi:todo:new:waitconfirmsortlist:cache#5m", key = "#bo.cashKey + '_' + #status")
    public TodoInfo waitConfirmSortList(TodoBo bo, Integer status) {
        List<SortGoods> sortGoodsList = sortGoodsMapper.waitConfirmSortList(bo, status);
        List<TodoGoodsVo> list = new ArrayList<>();
        for (SortGoods goods : sortGoodsList) {
            TodoGoodsVo goodsVo = new TodoGoodsVo();
            BeanUtils.copyProperties(goods, goodsVo);
            //可操作的数量 未收 > 已收-已分 ? 已收-已分 : 未收
            int count = goods.getReceived() - goods.getActualReceived() > goods.getUnreceived() ? goods.getUnreceived() : goods.getReceived() - goods.getActualReceived();
            if (count <= 0) {
                continue;
            }
            goodsVo.setSkuQuantity(count);
            goodsVo.setSortStatus(goods.getStatus());
            list.add(goodsVo);
        }
        this.loadSkuInfo(list);
        this.loadSupplierInfo(list);
        return new TodoInfo(list, null);
    }

    /**
     * 根据总仓销售结束时间获取当前时间的销售日
     *
     * @return
     */
    public LocalDate getSaleDate(Long regionWhId) {
        RegionWh regionWh = regionWhMapper.selectById(regionWhId);
        return SaleDateUtil.getSaleDate(regionWh.getDeliveryTimeEnd());
    }

    /**
     * 获取待审核的补贴审核单
     */
    public TodoInfo getTodoSubsidyAuditList(Long regionWhId) {
        //查询当天的补贴待审核
        List<BiSupplierSkuSubsidyAuditVo> subsidyAuditVos = supplierSkuMapper.selectSubsidyAudit(getSaleDate(regionWhId), regionWhId, SupplierSkuSubsidyAuditStatusEnum.PENDING.getCode());
        return new TodoInfo(BeanUtil.copyToList(subsidyAuditVos, TodoGoodsVo.class), null);
    }


    /**
     * 根据总仓Id获取当前云仓库存不足的商品
     *
     * @param bo
     * @return
     */
    public TodoInfo waitStockInList(TodoBo bo) {
        ArrayList<TodoGoodsVo> todoGoodsVos = new ArrayList<>();
        if (bo != null && bo.getRegionWhId() != null) {
            List<RemoteCwStockVo> remoteCwStockVos = remoteCwStockService.waitStockInList(bo.getRegionWhId());
            // 封装返回参数
            for (RemoteCwStockVo remoteCwStockVo : remoteCwStockVos) {
                TodoGoodsVo todoGoodsVo = new TodoGoodsVo();
                todoGoodsVo.setSpuName(remoteCwStockVo.getSpuName());
                todoGoodsVo.setLackQty(remoteCwStockVo.getLackQty());
                todoGoodsVo.setSupplierName(remoteCwStockVo.getSupplierName());
                todoGoodsVo.setBuyerName(remoteCwStockVo.getBuyerName());
                todoGoodsVo.setSpuStandards(remoteCwStockVo.getSpuStandards());
                todoGoodsVos.add(todoGoodsVo);
            }
            return new TodoInfo(todoGoodsVos, null);
        }
        return new TodoInfo(todoGoodsVos, null);
    }

    /**
     * 获取待开票单数
     *
     * @param bo
     * @return
     */
    public TodoInfo waitCreateInvoiceCount(TodoBo bo, String status) {
        RemoteInvoiceBo invoiceBo = new RemoteInvoiceBo();
        invoiceBo.setStatus(status);
        List<RemoteInvoiceVo> remoteInvoiceVos = remoteInvoiceService.queryList(invoiceBo);
        return new TodoInfo(BeanUtil.copyToList(remoteInvoiceVos, TodoGoodsVo.class), null);
    }
}
