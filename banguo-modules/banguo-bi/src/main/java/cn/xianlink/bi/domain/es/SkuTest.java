package cn.xianlink.bi.domain.es;

import lombok.Data;
import lombok.experimental.Accessors;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;
import org.dromara.easyes.annotation.rely.Analyzer;
import org.dromara.easyes.annotation.rely.FieldType;
import org.dromara.easyes.annotation.rely.IdType;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * SKU测试 ES映射实体类
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@Data
@Accessors(chain = true)
@IndexName(value = "sku_test", keepGlobalPrefix = false)
public class SkuTest {

    /**
     * ES中的唯一ID
     */
    @IndexId(type = IdType.CUSTOMIZE)
    private String id;
    
    /**
     * 商品名称 - 支持中文分词和精确匹配
     */
    @IndexField(fieldType = FieldType.TEXT, analyzer = Analyzer.IK_SMART)
    private String spuName;
    
    /**
     * 商品等级
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String spuGrade;
    
    /**
     * 商品规格
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String spuStandards;
    
    /**
     * 创建时间
     */
    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String createTime;
    
    /**
     * 更新时间
     */
    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String updateTime;
    
    /**
     * 是否删除
     */
    @IndexField(fieldType = FieldType.BOOLEAN)
    private Boolean isDeleted;
    
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    public SkuTest() {
        this.createTime = LocalDateTime.now().format(DATE_TIME_FORMATTER);
        this.updateTime = LocalDateTime.now().format(DATE_TIME_FORMATTER);
        this.isDeleted = false;
    }
    
    public SkuTest(String id, String spuName, String spuGrade, String spuStandards) {
        this();
        this.id = id;
        this.spuName = spuName;
        this.spuGrade = spuGrade;
        this.spuStandards = spuStandards;
    }
}