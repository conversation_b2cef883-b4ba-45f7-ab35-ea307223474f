package cn.xianlink.bi.domain.es;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
@EqualsAndHashCode
public class CompletionObject implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private List<String> input;

    private Integer weight;

    public CompletionObject() {
    }

    public CompletionObject(List<String> input, Integer weight) {
        this.input = input;
        this.weight = weight;
    }
}