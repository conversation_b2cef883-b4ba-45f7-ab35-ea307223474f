package cn.xianlink.bi.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Canal操作类型枚举
 * 
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum CanalOperationTypeEnum {

    /**
     * 插入操作
     */
    INSERT("INSERT", "插入操作"),

    /**
     * 更新操作
     */
    UPDATE("UPDATE", "更新操作"),

    /**
     * 删除操作
     */
    DELETE("DELETE", "删除操作");

    /**
     * 操作类型代码
     */
    private final String code;

    /**
     * 操作类型描述
     */
    private final String description;

    /**
     * 根据代码获取枚举
     * 
     * @param code 操作类型代码
     * @return 对应的枚举值
     */
    public static CanalOperationTypeEnum getByCode(String code) {
        for (CanalOperationTypeEnum operationType : CanalOperationTypeEnum.values()) {
            if (operationType.getCode().equals(code)) {
                return operationType;
            }
        }
        return null;
    }

    /**
     * 检查是否为有效的操作类型
     * 
     * @param code 操作类型代码
     * @return 是否有效
     */
    public static boolean isValidOperation(String code) {
        return getByCode(code) != null;
    }
} 