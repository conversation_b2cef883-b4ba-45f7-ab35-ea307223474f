package cn.xianlink.bi.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.ContentType;
import cn.hutool.json.JSONUtil;
import cn.hutool.log.Log;
import cn.xianlink.basic.api.*;
import cn.xianlink.basic.api.domain.bo.RemoteRegionLogisticsQueryBo;
import cn.xianlink.basic.api.domain.vo.*;
import cn.xianlink.bi.api.domain.vo.RemoteCustomerLossRateVo;
import cn.xianlink.bi.api.domain.vo.RemoteOrderLossRateVo;
import cn.xianlink.bi.domain.bo.BiOrderBo;
import cn.xianlink.bi.domain.bo.order.BiCustomerOrderBo;
import cn.xianlink.bi.domain.bo.order.BiOrderSaleDataBo;
import cn.xianlink.bi.domain.bo.order.BiSaleDateProductInfoBo;
import cn.xianlink.bi.domain.bo.FinanceOrderItemReportBo;
import cn.xianlink.bi.domain.bo.OrderItemExportSelectBo;
import cn.xianlink.bi.domain.bo.product.SupplierSkuQueryBo;
import cn.xianlink.bi.domain.vo.*;
import cn.xianlink.bi.domain.vo.order.*;
import cn.xianlink.bi.mapper.IBiOrderItemMapper;
import cn.xianlink.bi.mapper.IBiOrderMapper;
import cn.xianlink.bi.mapper.IBiOrderRefundMapper;
import cn.xianlink.bi.mapper.product.IBiSupplierSkuMapper;
import cn.xianlink.bi.service.IBiOrderService;
import cn.xianlink.common.api.enums.SSDSEnum;
import cn.xianlink.common.api.enums.basic.CustomerAfterSaleStatusEnum;
import cn.xianlink.common.api.enums.basic.ExportModuleEnum;
import cn.xianlink.common.api.enums.basic.RegionWhTypeEnum;
import cn.xianlink.common.api.enums.order.*;
import cn.xianlink.common.api.enums.system.SysUserTypeEnum;
import cn.xianlink.common.api.util.SSDSUtil;
import cn.xianlink.common.api.util.SaleDateUtil;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.excel.convert.ExcelBigNumberConvert;
import cn.xianlink.common.excel.core.CellMergeStrategy;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.oss.core.OssClient;
import cn.xianlink.common.oss.factory.OssFactory;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.order.api.RemoteReceiveGoodsService;
import cn.xianlink.order.api.vo.RemoteReceiveGoodsDetailVO;
import cn.xianlink.order.api.vo.RemoteReceiveGoodsVO;
import cn.xianlink.product.api.RemoteBuyerService;
import cn.xianlink.product.api.RemoteCategoryService;
import cn.xianlink.product.api.RemoteSupplierSkuService;
import cn.xianlink.product.api.domain.bo.RemoteQueryInfoListBo;
import cn.xianlink.product.api.domain.vo.RemoteCategoryVO;
import cn.xianlink.product.api.domain.vo.RemoteSupplierSkuFundsInfoVo;
import cn.xianlink.product.api.domain.vo.RemoteSupplierSkuInfoVo;
import cn.xianlink.resource.api.RemoteFileService;
import cn.xianlink.system.api.RemoteDeptService;
import cn.xianlink.system.api.RemoteUserService;
import cn.xianlink.system.api.domain.bo.RemoteUserBo;
import cn.xianlink.trade.api.domain.vo.RemoteTradeAccTransVo;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import jakarta.validation.constraints.NotNull;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @program: banguo-parent-ms
 * @description:
 * @author: chenbin
 * @create: 2025-02-28 11:44
 **/
@RequiredArgsConstructor
@Service
@CustomLog
public class BiOrderServiceImpl implements IBiOrderService {

    private final IBiOrderMapper orderMapper;

    private final IBiOrderItemMapper orderItemMapper;

    private final IBiOrderRefundMapper orderRefundMapper;

    private final IBiSupplierSkuMapper supplierSkuMapper;

    @DubboReference
    private final RemoteBasCustomerService remoteBasCustomerService;

    @DubboReference
    private final RemoteCityWhService remoteCityWhService;

    @DubboReference
    private final RemoteRegionWhService remoteRegionWhService;

    @DubboReference
    private final RemoteRegionLogisticsService regionLogisticsService;

    @DubboReference
    private final RemoteReceiveGoodsService remoteReceiveGoodsService;

    @DubboReference
    private final RemoteSupplierService remoteSupplierService;

    @DubboReference
    private final RemoteDeptService remoteDeptService;

    @DubboReference
    private final RemoteSupplierSkuService remoteSupplierSkuService;

    @DubboReference
    private final RemoteExportJobService remoteExportJobService;

    @DubboReference
    private final RemoteFileService remoteFileService;
    @DubboReference
    private final RemoteUserService remoteUserService;
    @DubboReference
    private final RemoteCategoryService remoteCategoryService;
    @DubboReference
    private final RemoteWhProfitRuleService remoteWhProfitRuleService;

    @DubboReference
    private final RemoteBuyerService remoteBuyerService;

    @Override
    public Page<BiOrderVo> list(BiOrderBo bo) {
        if(StringUtils.isNotBlank(bo.getPhone())) {
            RemoteUserBo remoteUserBo = remoteUserService.getUserByPhoneNo(bo.getPhone(), SysUserTypeEnum.CUSTOMER_USER.getType());
            if(remoteUserBo == null) {
                bo.setCustomerId(-1L);
            }else {
                bo.setCustomerId(remoteUserService.getUserRelation(remoteUserBo.getUserId()));
            }
        }
        Page<BiOrderVo> page = orderMapper.list(bo, bo.build());
//        List<BiOrderVo> records = page.getRecords();
//        List<Long> customerIdList = records.stream().map(BiOrderVo::getCustomerId).distinct().toList();
//        List<Long> cityWhIdList = records.stream().map(BiOrderVo::getCityWhId).distinct().toList();
//        List<Long> regionWhIdList = records.stream().map(BiOrderVo::getRegionWhId).distinct().toList();
//        List<Long> logisticsIds = records.stream().flatMap(item -> CollectionUtil.newArrayList(item.getLogisticsId()
//                , item.getLogisticsIdLevel2()).stream()).distinct().collect(Collectors.toList());
//        List<RemoteCustomerVo> customerVoList = remoteBasCustomerService.getByIds(customerIdList);
//        List<RemoteCityWhVo> cityWhVoList = remoteCityWhService.queryList(cityWhIdList);
//        List<RemoteRegionWhVo> regionWhVoList = remoteRegionWhService.selectRegionWhInfoByIds(regionWhIdList);
//        RemoteRegionLogisticsQueryBo logisticsQueryBo = new RemoteRegionLogisticsQueryBo();
//        logisticsQueryBo.setLogisticsIds(logisticsIds);
//        List<RemoteRegionLogisticsVo> logisticsVoList = regionLogisticsService.queryList(logisticsQueryBo);
//
//        Map<Long, RemoteCustomerVo> customerVoMap = customerVoList.stream().collect(Collectors.toMap(RemoteCustomerVo::getId, Function.identity()));
//        Map<Long, RemoteCityWhVo> cityWhVoMap = cityWhVoList.stream().collect(Collectors.toMap(RemoteCityWhVo::getId, Function.identity()));
//        Map<Long, RemoteRegionWhVo> regionWhVoMap = regionWhVoList.stream().collect(Collectors.toMap(RemoteRegionWhVo::getId, Function.identity()));
//        Map<Long, RemoteRegionLogisticsVo> regionLogisticsVoMap = logisticsVoList.stream().collect(Collectors.toMap(RemoteRegionLogisticsVo::getId, Function.identity()));
//        for(BiOrderVo vo : records) {
//            RemoteCustomerVo customerVo = customerVoMap.get(vo.getCustomerId());
//            if(Objects.nonNull(customerVo)) {
//                vo.setCustomerName(customerVo.getName());
//                vo.setCustomerAlias(customerVo.getAlias());
//            }
//            RemoteCityWhVo cityWhVo = cityWhVoMap.get(vo.getCityWhId());
//            if(Objects.nonNull(cityWhVo)) {
//                vo.setCityWhName(cityWhVo.getName());
//            }
//            RemoteRegionWhVo regionWhVo = regionWhVoMap.get(vo.getRegionWhId());
//            if(Objects.nonNull(regionWhVo)) {
//                vo.setRegionWhName(regionWhVo.getRegionWhName());
//            }
//            RemoteRegionLogisticsVo remoteRegionLogisticsVo = regionLogisticsVoMap.get(vo.getLogisticsId());
//            if(Objects.nonNull(remoteRegionLogisticsVo)) {
//                vo.setLogisticsName(remoteRegionLogisticsVo.getLogisticsName());
//            }
//            RemoteRegionLogisticsVo logisticsVoLevel2 = regionLogisticsVoMap.get(vo.getLogisticsIdLevel2());
//            if(Objects.nonNull(logisticsVoLevel2)) {
//                vo.setLogisticsNameLevel2(logisticsVoLevel2.getLogisticsName());
//            }
//        }
        return page;
    }

    @Override
    public BiOrderVo info(Long orderId) {
        BiOrderVo orderVo = orderMapper.getById(orderId);
        if(orderVo == null) {
            throw new ServiceException("订单不存在");
        }

        List<BiOrderInfoVo> itemListVoList = orderItemMapper.queryItemList(orderId);
        if(itemListVoList == null || itemListVoList.size() == 0) {
            throw new ServiceException("订单项不存在");
        }

        List<Long> orderItemIds = itemListVoList.stream().map(BiOrderInfoVo::getId).toList();
        //获取用户信息
        RemoteCustomerVo remoteCustomerVo = remoteBasCustomerService.getById(orderVo.getCustomerId());
        Map<Long, Integer> afterSaleStatusMap = new HashMap<>();
        if(remoteCustomerVo != null) {
            orderVo.setCustomerName(remoteCustomerVo.getName());
            orderVo.setCustomerAlias(remoteCustomerVo.getAlias());
            if(CustomerAfterSaleStatusEnum.OPEN.getCode().equals(remoteCustomerVo.getAfterSaleStatus())) {
                RemoteReceiveGoodsVO afterSaleStatus = remoteReceiveGoodsService.getAfterSaleStatus(orderItemIds);
                if (ObjectUtil.isNotNull(afterSaleStatus)) {
                    orderVo.setAfterSaleTimeSeconds(afterSaleStatus.getAfterSaleTimeSeconds());
                    if (afterSaleStatus.getGoodsDetailList().size() > 0) {
                        afterSaleStatusMap = afterSaleStatus.getGoodsDetailList().stream().collect(Collectors
                                .toMap(RemoteReceiveGoodsDetailVO::getOrderItemId, RemoteReceiveGoodsDetailVO::getAfterSaleStatus, (V1, V2) -> V1));
                    }
                }
            }
        }
        RemoteCityWhVo cityWhVoList = remoteCityWhService.queryById(orderVo.getCityWhId());
        orderVo.setCityWhName(cityWhVoList.getName());
        RemoteRegionWhVo remoteRegionWhVo = remoteRegionWhService.queryById(orderVo.getRegionWhId());
        orderVo.setRegionWhName(remoteRegionWhVo.getRegionWhName());
        RemoteRegionLogisticsQueryBo logisticsQueryBo = new RemoteRegionLogisticsQueryBo();
        logisticsQueryBo.setLogisticsIds(CollectionUtil.newArrayList(orderVo.getLogisticsId(),orderVo.getLogisticsIdLevel2()));
        List<RemoteRegionLogisticsVo> logisticsVoList = regionLogisticsService.queryList(logisticsQueryBo);
        Map<Long, String> logisticsVoMap = logisticsVoList.stream().collect(Collectors.toMap(RemoteRegionLogisticsVo::getId, RemoteRegionLogisticsVo::getLogisticsName));
        orderVo.setLogisticsName(logisticsVoMap.get(orderVo.getLogisticsId()));
        orderVo.setLogisticsNameLevel2(logisticsVoMap.get(orderVo.getLogisticsIdLevel2()));
        List<BiOrderRefundDetailVo> refundProductDetailList = orderRefundMapper.getRefundByItemIdList(orderItemIds,null);
        Map<Long, BigDecimal> refundProductPriceMap = new HashMap<>();
        if(CollectionUtil.isNotEmpty(refundProductDetailList)) {
            refundProductPriceMap = refundProductDetailList.stream().collect(Collectors
                    .groupingBy(BiOrderRefundDetailVo::getOrderItemId
                            , Collectors.reducing(BigDecimal.ZERO, BiOrderRefundDetailVo::getRefundProductAmount, BigDecimal::add)));
        }

        RemoteQueryInfoListBo skuBo = new RemoteQueryInfoListBo();
        List<Long> skuIdList = itemListVoList.stream().map(BiOrderInfoVo::getSupplierSkuId).toList();
        skuBo.setSupplierSkuIdList(skuIdList);
        List<RemoteSupplierSkuInfoVo> skuList = remoteSupplierSkuService.queryInfoList(skuBo);
        Map<Long, RemoteSupplierSkuInfoVo> skuVoMap = new HashMap<>();
        if(skuList != null && skuList.size() > 0) {
            skuVoMap = skuList.stream().collect(Collectors.toMap(RemoteSupplierSkuInfoVo::getId, Function.identity()));
        }
        for(BiOrderInfoVo itemListVo : itemListVoList) {
            RemoteSupplierSkuInfoVo skuInfoVo = skuVoMap.get(itemListVo.getSupplierSkuId());
            if(skuInfoVo != null) {
                itemListVo.setSpuGrade(skuInfoVo.getSpuGrade());
                itemListVo.setSpuStandards(skuInfoVo.getSpuStandards());
                itemListVo.setProducer(skuInfoVo.getProducer());
                itemListVo.setBrand(skuInfoVo.getBrand());
                itemListVo.setAreaCode(skuInfoVo.getAreaCode());
                itemListVo.setShortProducer(skuInfoVo.getShortProducer());
                itemListVo.setAfterSaleType(skuInfoVo.getAfterSaleType());
            }
//            RemoteSupplierVo supplierVo = supplierVoMap.get(itemListVo.getSupplierId());
//            if(supplierVo != null) {
//                itemListVo.setSupplierSimpleCode(supplierVo.getSimpleCode());
//                itemListVo.setSupplierName(supplierVo.getName());
//            }
//            // 档口名称
//            itemListVo.setSupplierDeptName(deptNameMap.get(itemListVo.getSupplierDeptId()));

            //计算售后状态
            if (afterSaleStatusMap.containsKey(itemListVo.getId())) {
                itemListVo.setAfterSaleStatus(afterSaleStatusMap.get(itemListVo.getId()));
            }
            BigDecimal refundProductAmountTotal = refundProductPriceMap.getOrDefault(itemListVo.getId(),BigDecimal.ZERO);
            BigDecimal lossAmount = itemListVo.getProductAmount().subtract(itemListVo.getSubsidyFreeAmount()).subtract(refundProductAmountTotal);

            if(lossAmount.compareTo(BigDecimal.ZERO) <= 0 && AfterSaleStatusEnum.CAN_AFTER_SALE.getCode().equals(itemListVo.getAfterSaleStatus())) {
                itemListVo.setAfterSaleStatus(AfterSaleStatusEnum.NOT_AFTER_SALE.getCode());
            }

            //计算毛重单价
            BigDecimal grossWeightPrice = itemListVo.getFinalPrice().divide(itemListVo.getSpuGrossWeight(), 2 , RoundingMode.HALF_UP);
            if(grossWeightPrice.compareTo(BigDecimal.ZERO) == 0) {
                grossWeightPrice = new BigDecimal("0.01");
            }
            itemListVo.setGrossWeightPrice(grossWeightPrice);
            //计算净重单价
            BigDecimal netWeightPrice = itemListVo.getFinalPrice().divide(itemListVo.getSpuNetWeight(), 2 , RoundingMode.HALF_UP);
            if(netWeightPrice.compareTo(BigDecimal.ZERO) == 0) {
                netWeightPrice = new BigDecimal("0.01");
            }
            // todo Liyong：临时调整
            itemListVo.setWeightPrice(netWeightPrice);
            itemListVo.setNetWeightPrice(netWeightPrice);
            if(OrderStatusEnum.CANCEL.getCode().equals(itemListVo.getStatus())
                    || OrderStatusEnum.WAIT.getCode().equals(itemListVo.getStatus())) {
                itemListVo.setShowStatusName(OrderStatusEnum.getDescByCode(itemListVo.getStatus()));
            }else {
                itemListVo.setShowStatusName(OrderWorkStatusEnum.getDescByCode(itemListVo.getWorkStatus()));
            }
            if(itemListVo.getPrice().compareTo(itemListVo.getFinalPrice()) == 0) {
                itemListVo.setFinalPrice(null);
            }
            //商品实付金额
            itemListVo.setTotalAmount(itemListVo.getProductAmount().subtract(itemListVo.getProductFreeAmount()));
        }
        orderVo.setOrderInfoVoList(itemListVoList);
        //检测总仓
        RemoteRegionWhVo regionWhVo = remoteRegionWhService.queryById(orderVo.getRegionWhId());
        if(regionWhVo == null) {
            throw new ServiceException("总仓不存在");
        }
        orderVo.setDeliverName(regionWhVo.getRegionWhName());

        //获取退款信息
        if (ObjectUtil.isNotNull(refundProductDetailList)) {
            BigDecimal differenceRefundAmount = refundProductDetailList.stream().filter(item -> Objects.equals(item.getRefundType(), RefundTypeEnum.DIFFERENCE.getCode()))
                    .map(BiOrderRefundDetailVo::getRefundProductAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal lossAmount = refundProductDetailList.stream().filter(item ->
                            Objects.equals(item.getRefundType(), RefundTypeEnum.REPORT_LOSS.getCode()))
                    .map(BiOrderRefundDetailVo::getRefundProductAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal refundAmount = refundProductDetailList.stream().filter(item ->
                            Objects.equals(item.getRefundType(), RefundTypeEnum.LESS.getCode()) ||  Objects.equals(item.getRefundType(), RefundTypeEnum.LACK.getCode()) )
                    .map(BiOrderRefundDetailVo::getRefundProductAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            orderVo.setDifferenceRefundAmount(differenceRefundAmount);
            orderVo.setLossAmount(lossAmount);
            orderVo.setRefundAmount(refundAmount);
        }
        return orderVo;
    }
    private @NotNull Map<Long, String> getSupplierMap(List<Long> supplierDeptIdList) {
        if(CollUtil.isNotEmpty(supplierDeptIdList)){
            Map<Long, String> deptNameMap = remoteDeptService.getDeptNameMap(supplierDeptIdList);
            return deptNameMap;
        }
        return new HashMap<>();
    }
    @Override
    public BiOrderVo total(BiOrderBo bo) {
        if(StringUtils.isNotBlank(bo.getPhone())) {
            RemoteUserBo remoteUserBo = remoteUserService.getUserByPhoneNo(bo.getPhone(), SysUserTypeEnum.CUSTOMER_USER.getType());
            if(remoteUserBo == null) {
                bo.setCustomerId(-1L);
            }else {
                bo.setCustomerId(remoteUserService.getUserRelation(remoteUserBo.getUserId()));
            }
        }
        BiOrderVo biOrderVo = orderMapper.total(bo);
        return biOrderVo;
    }

    @Override
    public void exportOrder(BiOrderBo bo) {
        bo.setPageNum(1);
        bo.setPageSize(1);
        Page<BiOrderVo> page = orderMapper.list(bo, bo.build());
        if (CollUtil.isEmpty(page.getRecords())) {
            throw new ServiceException("没有数据可导出");
        }
        if (page.getTotal() > 1000000) {
            throw new ServiceException("导出数据量过大，请缩小查询条件");
        }
        List<Long> cityWhIds = SSDSUtil.getAuthorizeDate(SSDSEnum.CITY_WH_ID.getCode());
        List<Long> regionWhIds = SSDSUtil.getAuthorizeDate(SSDSEnum.REGION_WH_ID.getCode());
        log.keyword("userInfo").info(String.format("loginUserId:%s,phone:%s,cityWhIds:%s,regionWhIds:%s"
                , LoginHelper.getUserId(), LoginHelper.getLoginUser().getPhoneNo(), cityWhIds, regionWhIds));
        bo.setCityWhIds(cityWhIds);
        bo.setRegionWhIds(regionWhIds);
        remoteExportJobService.addJob(ExportModuleEnum.BI_ORDER, JSONUtil.toJsonStr(bo));
    }

    @Override
    public String exportOrderInfo(Long jobId, BiOrderBo bo) {
        String fileUrl;

        bo.setPageNum(1);
        bo.setPageSize(10000);

        String fileName = "订单明细导出" + jobId + ".xlsx";
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
             ExcelWriter excelWriter = EasyExcel.write(outputStream, BiOrderExcelVo.class).build()) {
            WriteSheet writeSheet = EasyExcel.writerSheet("Sheet1").build();

            boolean hasNext;
            do {
                Page<BiOrderVo> page = this.list(bo);

                if (page.getTotal() > 1000000) {
                    throw new ServiceException("导出数据量过大，请缩小查询条件");
                }
                if (CollUtil.isEmpty(page.getRecords())) {
                    break;
                }
                bo.setPageNum(bo.getPageNum() + 1);
                hasNext = page.hasNext();

                List<BiOrderVo> records = page.getRecords();

                List<BiOrderExcelVo> biOrderExcelVos = BeanUtil.copyToList(records, BiOrderExcelVo.class);
                excelWriter.write(biOrderExcelVos, writeSheet);
            } while (hasNext);
            BiOrderVo total = this.total(bo);
            BiOrderExcelVo biOrderExcel = BeanUtil.copyProperties(total, BiOrderExcelVo.class);
            biOrderExcel.setCode("合计");
            excelWriter.write(CollectionUtil.newArrayList(biOrderExcel), writeSheet);
            //完成写入
            excelWriter.finish();
            byte[] excelBytes = outputStream.toByteArray();
            fileUrl = remoteFileService.uploadTempFile(fileName, fileName, ContentType.OCTET_STREAM.getValue(), excelBytes, 1).getUrl();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return fileUrl;
    }

    @Override
    public void orderItemExport(BiOrderBo bo) {
        bo.setPageNum(1);
        bo.setPageSize(1);
        Page<BiOrderVo> page = orderMapper.list(bo, bo.build());
        if (CollUtil.isEmpty(page.getRecords())) {
            throw new ServiceException("没有数据可导出");
        }
        if (page.getTotal() > 1000000) {
            throw new ServiceException("导出数据量过大，请缩小查询条件");
        }
        List<Long> cityWhIds = SSDSUtil.getAuthorizeDate(SSDSEnum.CITY_WH_ID.getCode());
        List<Long> regionWhIds = SSDSUtil.getAuthorizeDate(SSDSEnum.REGION_WH_ID.getCode());
        log.keyword("userInfo").info(String.format("loginUserId:%s,phone:%s,cityWhIds:%s,regionWhIds:%s"
                , LoginHelper.getUserId(), LoginHelper.getLoginUser().getPhoneNo(), cityWhIds, regionWhIds));
        bo.setCityWhIds(cityWhIds);
        bo.setRegionWhIds(regionWhIds);
        remoteExportJobService.addJob(ExportModuleEnum.BI_ORDER_ITEM, JSONUtil.toJsonStr(bo));
    }

    /**
     * 导出订单明细
     * @param jobId
     * @param
     * @return
     */
    @Override
    public String exportOrderItemInfo(Long jobId, BiOrderBo bo) {
        String fileUrl;
        if(StringUtils.isNotBlank(bo.getPhone())) {
            RemoteUserBo remoteUserBo = remoteUserService.getUserByPhoneNo(bo.getPhone(), SysUserTypeEnum.CUSTOMER_USER.getType());
            if(remoteUserBo == null) {
                bo.setCustomerId(-1L);
            }else {
                bo.setCustomerId(remoteUserService.getUserRelation(remoteUserBo.getUserId()));
            }
        }
        bo.setPageNum(1);
        bo.setPageSize(10000);

        String fileName = "订单明细导出" + jobId + ".xlsx";

        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
             ExcelWriter excelWriter = EasyExcel.write(outputStream, BiOrderInfoExcelVo.class).build()) {
            WriteSheet writeSheet = EasyExcel.writerSheet("Sheet1").build();

            boolean hasNext;
            do {
                Page<OrderItemExportSelectBo> page = orderItemMapper.selectOrderInfoExportPage(bo, bo.build());
                if (page.getTotal() > 1000000) {
                    throw new ServiceException("导出数据量过大，请缩小查询条件");
                }
                if (CollUtil.isEmpty(page.getRecords())) {
                    break;
                }
                bo.setPageNum(bo.getPageNum() + 1);
                hasNext = page.hasNext();

                List<BiOrderInfoExcelVo> data = new ArrayList<>();
                List<OrderItemExportSelectBo> records = page.getRecords();

                // 按订单ID分组
                Map<Long, List<OrderItemExportSelectBo>> orderMap = records.stream()
                        .collect(Collectors.groupingBy(OrderItemExportSelectBo::getOrderId));

                for (Map.Entry<Long, List<OrderItemExportSelectBo>> entry : orderMap.entrySet()) {
                    List<OrderItemExportSelectBo> orderItems = entry.getValue();

                    // 计算订单总金额
                    BigDecimal totalAmount = orderItems.stream()
                            .map(OrderItemExportSelectBo::getProductAmount)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    // 计算订单总服务费
                    BigDecimal totalServiceFee = orderItems.get(0).getFinancialServiceAmount();

                    // 分配服务费
                    BigDecimal allocatedServiceFee = BigDecimal.ZERO;
                    for (OrderItemExportSelectBo record : orderItems) {
                        // 规格转换
                        record.buildSpuStandardsName();

                        // 计算每个订单项的服务费
                        BigDecimal itemServiceFee = BigDecimal.ZERO;
                        if (totalAmount.compareTo(BigDecimal.ZERO) != 0) {
                            itemServiceFee = record.getProductAmount()
                                    .divide(totalAmount, 10, RoundingMode.HALF_UP)
                                    .multiply(totalServiceFee)
                                    .setScale(2, RoundingMode.HALF_UP);
                        }
                        record.setFinancialServiceAmount(itemServiceFee);
                        allocatedServiceFee = allocatedServiceFee.add(itemServiceFee);

                        BiOrderInfoExcelVo rowVo = BeanUtil.copyProperties(record, BiOrderInfoExcelVo.class);
                        rowVo.setTotalAmount(this.zeroConvert(record.getProductAmount())
                                .add(this.zeroConvert(record.getPlatformServiceAmount()))
                                .add(this.zeroConvert(record.getPlatformFreightAmount()))
                                .add(this.zeroConvert(record.getPlatformFreightAmountLevel2()))
                                .add(this.zeroConvert(record.getBaseFreightAmount()))
                                .subtract(this.zeroConvert(record.getPlatformServiceFreeAmount()))
                                .subtract(this.zeroConvert(record.getFreightTotalFreeAmount()))
                        );
                        data.add(rowVo);
                    }

                    // 处理由于四舍五入导致的总和不等于服务费的问题
                    BigDecimal difference = totalServiceFee.subtract(allocatedServiceFee).setScale(2, RoundingMode.HALF_UP);
                    if (difference.compareTo(BigDecimal.ZERO) != 0) {
                        // 将差异分配到第一个订单项
                        if (!orderItems.isEmpty()) {
                            OrderItemExportSelectBo firstRecord = orderItems.get(0);
                            firstRecord.setFinancialServiceAmount(firstRecord.getFinancialServiceAmount().add(difference));
                        }
                    }
                }

                excelWriter.write(data, writeSheet);
            } while (hasNext);
            //完成写入
            excelWriter.finish();
            byte[] excelBytes = outputStream.toByteArray();
            fileUrl = remoteFileService.uploadTempFile(fileName, fileName, ContentType.OCTET_STREAM.getValue(), excelBytes, 1).getUrl();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return fileUrl;
    }

    /**
     * 预订单列表
     * @param bo
     * @return
     */
    @Override
    public Page<BiOrderInfoVo> advanceList(BiOrderBo bo) {

        Page<BiOrderInfoVo> page = orderItemMapper.advanceList(bo,bo.build());
        List<BiOrderInfoVo> records = page.getRecords();
        if (CollUtil.isEmpty(records)){
            return page;
        }
        List<String> customerAdminCodes = records.stream().filter(f -> StringUtils.isNotBlank(f.getCustomerAdminCode())).map(BiOrderInfoVo::getCustomerAdminCode).distinct().toList();
        if (customerAdminCodes.size() > 0) {
            Map<String, RemoteUserBo> usersMap = remoteUserService.getUsersByUserCodes(customerAdminCodes);
            records.forEach(e -> e.setPhone(usersMap.getOrDefault(e.getCustomerAdminCode(), new RemoteUserBo()).getPhoneNo()));
        }
        return page;

//        List<Long> customerIdList = records.stream().map(BiOrderInfoVo::getCustomerId).distinct().toList();
//        List<Long> cityWhIdList = records.stream().map(BiOrderInfoVo::getCityWhId).distinct().toList();
//        List<Long> regionWhIdList = records.stream().map(BiOrderInfoVo::getRegionWhId).distinct().toList();
//        List<Long> supplierSkuIds = records.stream().map(BiOrderInfoVo::getSupplierSkuId).distinct().toList();
//        List<Long> logisticsIds = records.stream().flatMap(item -> CollectionUtil.newArrayList(item.getLogisticsId()
//                , item.getLogisticsIdLevel2()).stream()).distinct().collect(Collectors.toList());
//
//        List<Long> orderItemIds = records.stream().map(BiOrderInfoVo::getId).collect(Collectors.toList());
//        List<BiOrderRefundDetailVo> refundProductDetailList = orderRefundMapper.getRefundByItemIdList(orderItemIds, RefundTypeEnum.REPORT_LOSS.getCode());
//        Map<Long, BigDecimal> refundProductPriceMap = new HashMap<>();
//        if(CollectionUtil.isNotEmpty(refundProductDetailList)) {
//            refundProductPriceMap = refundProductDetailList.stream().collect(Collectors
//                    .groupingBy(BiOrderRefundDetailVo::getOrderItemId
//                            , Collectors.reducing(BigDecimal.ZERO, BiOrderRefundDetailVo::getRefundProductAmount, BigDecimal::add)));
//        }
//
//
//        List<RemoteCustomerVo> customerVoList = remoteBasCustomerService.getByIds(customerIdList);
//        List<RemoteCityWhVo> cityWhVoList = remoteCityWhService.queryList(cityWhIdList);
//        List<RemoteRegionWhVo> regionWhVoList = remoteRegionWhService.selectRegionWhInfoByIds(regionWhIdList);
//        RemoteRegionLogisticsQueryBo logisticsQueryBo = new RemoteRegionLogisticsQueryBo();
//        logisticsQueryBo.setLogisticsIds(logisticsIds);
//        List<RemoteRegionLogisticsVo> logisticsVoList = regionLogisticsService.queryList(logisticsQueryBo);
//
//        Map<String, RemoteUserBo> usersByUserCodes = remoteUserService.getUsersByUserCodes(customerVoList.stream()
//                .map(RemoteCustomerVo::getUserCode).collect(Collectors.toList()));
//
//        Map<Long, RemoteSupplierSkuFundsInfoVo> skuFundsInfoVoMap = remoteSupplierSkuService.queryInfoListByFunds(supplierSkuIds)
//                .stream().collect(Collectors.toMap(RemoteSupplierSkuFundsInfoVo::getSkuId, Function.identity()));
//
//        Map<Long, RemoteCustomerVo> customerVoMap = customerVoList.stream().collect(Collectors.toMap(RemoteCustomerVo::getId, Function.identity()));
//        Map<Long, RemoteCityWhVo> cityWhVoMap = cityWhVoList.stream().collect(Collectors.toMap(RemoteCityWhVo::getId, Function.identity()));
//        Map<Long, RemoteRegionWhVo> regionWhVoMap = regionWhVoList.stream().collect(Collectors.toMap(RemoteRegionWhVo::getId, Function.identity()));
//        Map<Long, RemoteRegionLogisticsVo> regionLogisticsVoMap = logisticsVoList.stream().collect(Collectors.toMap(RemoteRegionLogisticsVo::getId, Function.identity()));
//
//        for (BiOrderInfoVo record : records) {
//            RemoteCustomerVo customerVo = customerVoMap.get(record.getCustomerId());
//            if(Objects.nonNull(customerVo)) {
//                record.setCustomerName(customerVo.getName());
//                record.setCustomerAlias(customerVo.getAlias());
//                record.setPhone(usersByUserCodes.getOrDefault(customerVo.getUserCode(), new RemoteUserBo()).getPhoneNo());
//            }
//            RemoteCityWhVo cityWhVo = cityWhVoMap.get(record.getCityWhId());
//            if(Objects.nonNull(cityWhVo)) {
//                record.setCityWhName(cityWhVo.getName());
//            }
//            RemoteRegionWhVo regionWhVo = regionWhVoMap.get(record.getRegionWhId());
//            if(Objects.nonNull(regionWhVo)) {
//                record.setRegionWhName(regionWhVo.getRegionWhName());
//            }
//            RemoteRegionLogisticsVo remoteRegionLogisticsVo = regionLogisticsVoMap.get(record.getLogisticsId());
//            if(Objects.nonNull(remoteRegionLogisticsVo)) {
//                record.setLogisticsName(remoteRegionLogisticsVo.getLogisticsName());
//            }
//            RemoteRegionLogisticsVo logisticsVoLevel2 = regionLogisticsVoMap.get(record.getLogisticsIdLevel2());
//            if(Objects.nonNull(logisticsVoLevel2)) {
//                record.setLogisticsNameLevel2(logisticsVoLevel2.getLogisticsName());
//            }
//            var sku = skuFundsInfoVoMap.get(record.getSupplierSkuId());
//            if (sku != null) {
//                record.setSpuName(sku.getSkuName());
//                record.setSpuStandards(sku.getSpuStandardsName());
//                record.setBrand(sku.getBrand());
//                record.setSpuGrade(sku.getSpuGrade());
//                record.setProducer(sku.getProducer());
//            }
//            record.setLossAmount(refundProductPriceMap.get(record.getId()));
//        }
//        return page;
    }

    /**
     * 预订单合计
     * @param bo
     * @return
     */
    @Override
    public BiOrderInfoVo advanceTotal(BiOrderBo bo) {
        return orderItemMapper.advanceTotal(bo);
    }

    /**
     * 预订单导出
     * @param bo
     */
    @Override
    public void advanceOrderExport(BiOrderBo bo) {
        bo.setPageNum(1);
        bo.setPageSize(1);
        Page<BiOrderInfoVo> page = orderItemMapper.advanceList(bo,bo.build());
        if (CollUtil.isEmpty(page.getRecords())) {
            throw new ServiceException("没有数据可导出");
        }
        if (page.getTotal() > 1000000) {
            throw new ServiceException("导出数据量过大，请缩小查询条件");
        }
        List<Long> cityWhIds = SSDSUtil.getAuthorizeDate(SSDSEnum.CITY_WH_ID.getCode());
        List<Long> regionWhIds = SSDSUtil.getAuthorizeDate(SSDSEnum.REGION_WH_ID.getCode());
        log.keyword("userInfo").info(String.format("loginUserId:%s,phone:%s,cityWhIds:%s,regionWhIds:%s"
                , LoginHelper.getUserId(), LoginHelper.getLoginUser().getPhoneNo(), cityWhIds, regionWhIds));
        bo.setCityWhIds(cityWhIds);
        bo.setRegionWhIds(regionWhIds);
        remoteExportJobService.addJob(ExportModuleEnum.BI_ADVANCE_ORDER_ITEM, JSONUtil.toJsonStr(bo));
    }

    @Override
    public String exportAdvanceOrder(Long jobId, BiOrderBo bo) {
        String fileUrl;
        bo.setPageNum(1);
        bo.setPageSize(10000);
        String fileName = "订单明细导出" + jobId + ".xlsx";
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
             ExcelWriter excelWriter = EasyExcel.write(outputStream, BiOrderItemExcelVo.class).build()) {
            WriteSheet writeSheet = EasyExcel.writerSheet("Sheet1").build();
            boolean hasNext;
            do {
                Page<BiOrderInfoVo> page = this.advanceList(bo);
                if (page.getTotal() > 1000000) {
                    throw new ServiceException("导出数据量过大，请缩小查询条件");
                }
                if (CollUtil.isEmpty(page.getRecords())) {
                    break;
                }
                bo.setPageNum(bo.getPageNum() + 1);
                hasNext = page.hasNext();

                List<BiOrderInfoVo> orderInfoVoList = page.getRecords();
                List<BiOrderItemExcelVo> biOrderExcelVos = BeanUtil.copyToList(orderInfoVoList, BiOrderItemExcelVo.class);
                excelWriter.write(biOrderExcelVos, writeSheet);
            } while (hasNext);
            BiOrderInfoVo total = this.advanceTotal(bo);
            BiOrderItemExcelVo biOrderExcel = BeanUtil.copyProperties(total, BiOrderItemExcelVo.class);
            biOrderExcel.setLogisticsName("合计");
            excelWriter.write(CollectionUtil.newArrayList(biOrderExcel), writeSheet);
            //完成写入
            excelWriter.finish();
            byte[] excelBytes = outputStream.toByteArray();
            fileUrl = remoteFileService.uploadTempFile(fileName, fileName, ContentType.OCTET_STREAM.getValue(), excelBytes, 1).getUrl();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return fileUrl;
    }

    @Override
    public RemoteOrderLossRateVo getLossRate(List<Long> customerIds) {
        //获取平均报损率
        RemoteOrderLossRateVo lossRate = orderItemMapper.getLossRate();
        //获取客户报损率
        List<RemoteCustomerLossRateVo> customerLossRateVos= orderItemMapper.getCustomerLossRate(customerIds);
        if (CollectionUtil.isNotEmpty(customerLossRateVos)){
            lossRate.setCustomerLossRateVos(customerLossRateVos);
        }
        return lossRate;
    }

    @Override
    public Page<BiSaleDateProductInfoVo> getSaleDateProductInfo(BiSaleDateProductInfoBo bo) {
        return orderItemMapper.getSaleDateProductInfo(bo, bo.build());
    }

    @Override
    public BiSaleDateProductTotalVo getSaleDateProductInfoTotal(BiSaleDateProductInfoBo bo) {
        return orderItemMapper.getSaleDateProductInfoTotal(bo);
    }

    @Override
    public Page<BiSaleDateCustomerInfoVo> getCustomerList(BiCustomerOrderBo bo) {
        return orderItemMapper.getCustomerList(bo,  bo.build());
    }

    @Override
    public Page<BiSaleDateCustomerOrderVo> getCustomerOrderList(BiSaleDateProductInfoBo bo) {
        return orderItemMapper.getCustomerOrderList(bo, bo.build());
    }

    @Override
    public BiSaleDateProductTotalVo getCustomerOrderTotal(BiSaleDateProductInfoBo bo) {
        return orderItemMapper.getCustomerOrderTotal(bo);
    }

    @Override
    public BiSaleDateCustomerOrderInfoVo getSaleDateCustomerOrderInfo(BiSaleDateProductInfoBo bo) {
        return orderItemMapper.getSaleDateCustomerOrderInfo(bo);
    }


    private List<BiOrderInfoExcelVo> buildOrderItemExportVo(List<OrderItemExportSelectBo> dtos,
                                                            Map<Long, RemoteCityWhVo> cityMap,
                                                            Map<Long, RemoteRegionWhVo> regionMap,
                                                            Map<Long, RemoteRegionLogisticsVo> logisticsMap,
                                                            Map<Long, RemoteCustomerVo> customerMap,
                                                            Map<Long, RemoteSupplierSkuFundsInfoVo> skuMap,
                                                            Map<Long, RemoteSupplierVo> supplierMap,
                                                            Map<String, RemoteTradeAccTransVo> itemTransMap) {
        List<BiOrderInfoExcelVo> result = new LinkedList<>();
        for (OrderItemExportSelectBo d : dtos) {
            BiOrderInfoExcelVo resultVo = BeanUtil.copyProperties(d, BiOrderInfoExcelVo.class);
            result.add(resultVo);
            var city = cityMap.get(d.getCityWhId());
            if (city != null) {
                resultVo.setCityWhName(city.getName());
            }
            var region = regionMap.get(d.getRegionWhId());
            if (region != null) {
                resultVo.setRegionWhName(region.getRegionWhName());
            }
            var logisticsVo = logisticsMap.get(d.getLogisticsId());
            if (logisticsVo != null) {
                resultVo.setLogisticsName(logisticsVo.getLogisticsName());
            }
            var logisticsLevelVo = logisticsMap.get(d.getLogisticsIdLevel2());
            if (logisticsLevelVo != null) {
                resultVo.setLogisticsNameLevel2(logisticsLevelVo.getLogisticsName());
            }
            var customerVo = customerMap.get(d.getCustomerId());
            if (customerVo != null) {
                resultVo.setCustomerName(customerVo.getName());
            }
            var sku = skuMap.get(d.getSupplierSkuId());
            if (sku != null) {
                resultVo.setSpuName(sku.getSkuName());
                resultVo.setSpuStandardsName(sku.getSpuStandardsName());
                resultVo.setBrand(sku.getBrand());
                resultVo.setSpuGrade(sku.getSpuGrade());
                resultVo.setProducer(sku.getProducer());
            }

            resultVo.setTotalAmount(this.zeroConvert(d.getProductAmount())
                    .add(this.zeroConvert(d.getPlatformServiceAmount()))
                    .add(this.zeroConvert(d.getPlatformFreightAmount()))
                    .add(this.zeroConvert(d.getPlatformFreightAmountLevel2()))
                    .add(this.zeroConvert(d.getBaseFreightAmount()))
                    .subtract(this.zeroConvert(d.getPlatformServiceFreeAmount()))
                    .subtract(this.zeroConvert(d.getFreightTotalFreeAmount()))
            );
        }
        return result;
    }

    private void fillCityMap(Map<Long, RemoteCityWhVo> cityMap, Set<Long> cityIds) {
        //删除已有的id, 剩下的就是不存在的id
        cityIds.removeAll(cityMap.keySet());
        if (!cityIds.isEmpty()) {
            //分批次查询org信息
            List<List<Long>> l = Lists.partition(Lists.newArrayList(cityIds), 200);
            for (List<Long> city : l) {
                var t = remoteCityWhService.queryList(city).stream().collect(Collectors.toMap(RemoteCityWhVo::getId, Function.identity()));
                cityMap.putAll(t);
            }
        }
    }

    private void fillRegionMap(Map<Long, RemoteRegionWhVo> regionMap, Set<Long> regionIds) {
        //删除已有的id, 剩下的就是不存在的id
        regionIds.removeAll(regionMap.keySet());
        if (!regionIds.isEmpty()) {
            //分批次查询org信息
            List<List<Long>> regions = Lists.partition(Lists.newArrayList(regionIds), 200);
            for (List<Long> region : regions) {
                var t = remoteRegionWhService.selectRegionWhInfoByIds(region).stream().collect(Collectors.toMap(RemoteRegionWhVo::getId, Function.identity()));
                regionMap.putAll(t);
            }
        }
    }

    private void fillLogisticsMap(Map<Long, RemoteRegionLogisticsVo> logisticsMap, Set<Long> allLogistics) {
        //删除已有的id, 剩下的就是不存在的id
        allLogistics.removeAll(logisticsMap.keySet());
        if (!allLogistics.isEmpty()) {
            //分批次查询org信息
            List<List<Long>> t = Lists.partition(Lists.newArrayList(allLogistics), 200);
            for (List<Long> logistics : t) {
                RemoteRegionLogisticsQueryBo logisticsQueryBo = new RemoteRegionLogisticsQueryBo();
                logisticsQueryBo.setLogisticsIds(logistics);
                var tt = regionLogisticsService.queryList(logisticsQueryBo).stream().collect(Collectors.toMap(RemoteRegionLogisticsVo::getId, Function.identity()));
                logisticsMap.putAll(tt);
            }
        }
    }

    private void fillCustomerMap(Map<Long, RemoteCustomerVo> customerMap, Set<Long> allCustomerIds) {
        //删除已有的id, 剩下的就是不存在的id
        allCustomerIds.removeAll(customerMap.keySet());
        if (!allCustomerIds.isEmpty()) {
            //分批次查询org信息
            List<List<Long>> customerIds = Lists.partition(Lists.newArrayList(allCustomerIds), 200);
            for (List<Long> customerId : customerIds) {
                var t = remoteBasCustomerService.getByIds(customerId).stream().collect(Collectors.toMap(RemoteCustomerVo::getId, Function.identity()));
                customerMap.putAll(t);
            }
        }
    }

    private void fillSkuMap(Map<Long, RemoteSupplierSkuFundsInfoVo> skuMap, Set<Long> allSkuIds) {
        //删除已有的id, 剩下的就是不存在的id
        allSkuIds.removeAll(skuMap.keySet());
        if (!allSkuIds.isEmpty()) {
            //分批次查询org信息
            List<List<Long>> skuIds = Lists.partition(Lists.newArrayList(allSkuIds), 200);
            for (List<Long> skuId : skuIds) {
                var t = remoteSupplierSkuService.queryInfoListByFunds(skuId).stream().collect(Collectors.toMap(RemoteSupplierSkuFundsInfoVo::getSkuId, Function.identity()));
                skuMap.putAll(t);
            }
        }
    }



    private void fillSupplierMap(Map<Long, RemoteSupplierVo> supplierMap, Set<Long> allSupplierIds) {
        //删除已有的id, 剩下的就是不存在的id
        allSupplierIds.removeAll(supplierMap.keySet());
        if (!allSupplierIds.isEmpty()) {
            //分批次查询org信息
            List<List<Long>> supplierIds = Lists.partition(Lists.newArrayList(allSupplierIds), 200);
            for (List<Long> supplierId : supplierIds) {
                var t = remoteSupplierService.getSupplierByIds(supplierId).stream().collect(Collectors.toMap(RemoteSupplierVo::getId, Function.identity()));
                supplierMap.putAll(t);
            }
        }
    }

    private BigDecimal zeroConvert(BigDecimal amount) {
        if (amount == null) {
            return BigDecimal.ZERO;
        }
        return amount;
    }


    @Override
    public Page<FinanceOrderItemReportVo> financeOrderItemReportPage(FinanceOrderItemReportBo bo) {
        //商品名称
        if(ObjectUtil.isNotEmpty(bo.getProductName())){
            SupplierSkuQueryBo supplierSkuQueryBo = new SupplierSkuQueryBo();
            supplierSkuQueryBo.setProductName(bo.getProductName());
            supplierSkuQueryBo.setSupplierId(bo.getSupplierId());
            supplierSkuQueryBo.setSaleDateStart(bo.getSaleDateStart());
            supplierSkuQueryBo.setSaleDateEnd(bo.getSaleDateEnd());
            List<Long> supplierSkuIdList = supplierSkuMapper.selectIdsListByBo(supplierSkuQueryBo);
            if(ObjectUtil.isNotEmpty(supplierSkuIdList)){
                bo.setSupplierSkuIdList(supplierSkuIdList);
            }else{
                return bo.build();
            }
        }
        //平安单号（般果）
        if(ObjectUtil.isNotEmpty(bo.getRegionWhSplitNo())){
            List<String> orderCodeList = orderItemMapper.getOrderCodeByRegionWhSplitNo(bo.getRegionWhSplitNo());
            if(ObjectUtil.isNotEmpty(orderCodeList)){
                bo.setOrderCodeList(orderCodeList);
            }else{
                return bo.build();
            }
        }
        //分页查询
        Page<FinanceOrderItemReportVo> res = orderItemMapper.financeOrderItemReportList(bo, bo.build());
        List<FinanceOrderItemReportVo> reportVoList = res.getRecords();
        if(ObjectUtil.isEmpty(res.getRecords())){
            return res;
        }
        //填充关联信息
        this.fillFinanceOrderItemReport(reportVoList);
        //填充判责信息
        this.fillFinanceOrderItemBlame(bo, reportVoList);
        return res;
    }

    /**
     * 判责信息填充
     * @param bo
     * @param reportVoList
     */
    private void fillFinanceOrderItemBlame(FinanceOrderItemReportBo bo, List<FinanceOrderItemReportVo> reportVoList) {
        List<Long> orderItemIdList = reportVoList.stream().map(FinanceOrderItemReportVo::getId).distinct().toList();
        bo.setOrderItemIdList(orderItemIdList);
        //少货查询退款表、判责详情表
        List<FinanceOrderItemBlameVo> blameVoList = new ArrayList<>();
        if(ObjectUtil.isEmpty(bo.getSourceType()) || BlameSourceTypeEnum.LESS_GOODS.getCode().equals(bo.getSourceType())){
            List<FinanceOrderItemBlameVo> lessBlameList = orderItemMapper.getLessBlame(bo);
            if(ObjectUtil.isNotEmpty(lessBlameList)){
                lessBlameList.forEach(item -> {
                    item.setBlameAmount(ObjectUtil.isEmpty(item.getBlameAmount()) ? BigDecimal.ZERO : item.getBlameAmount());
                });
            }
            blameVoList.addAll(lessBlameList);
        }
        //报损
        if(ObjectUtil.isEmpty(bo.getSourceType()) || BlameSourceTypeEnum.LOSS_ORDER.getCode().equals(bo.getSourceType())){
            List<FinanceOrderItemBlameVo> lossBlame = orderItemMapper.getLossBlame(bo);
            blameVoList.addAll(lossBlame);
        }
        //缺货、差额退，查询退款表
        if(ObjectUtil.isEmpty(bo.getSourceType()) || (!BlameSourceTypeEnum.LESS_GOODS.getCode().equals(bo.getSourceType())
            && !BlameSourceTypeEnum.LOSS_ORDER.getCode().equals(bo.getSourceType()))){
            List<FinanceOrderItemBlameVo> otherBlame = orderItemMapper.getOtherBlame(bo);
            blameVoList.addAll(otherBlame);
        }
        reportVoList.forEach(reportVo -> {
            //这里初始化商品实补=商品补贴，下面再减掉合计的退补贴金额
            reportVo.setActualRegionSubsidyAmount(reportVo.getRegionSubsidyAmount());
        });
        if(!blameVoList.isEmpty()){
            //分组
            Map<Long, List<FinanceOrderItemBlameVo>> blameVoMap = blameVoList.stream().collect(Collectors.groupingBy(FinanceOrderItemBlameVo::getOrderItemId));
            reportVoList.forEach(reportVo -> {
                reportVo.setBlameVoList(blameVoMap.get(reportVo.getId()));
                if(ObjectUtil.isNotEmpty(reportVo.getBlameVoList())){
                    reportVo.getBlameVoList().sort(Comparator.comparing(FinanceOrderItemBlameVo::getSourceType));
                    reportVo.getBlameVoList().forEach(blameVo -> {
                        blameVo.setBlameGrossWeight(ObjectUtil.isNotEmpty(blameVo.getBlameGrossWeight()) ? blameVo.getBlameGrossWeight().stripTrailingZeros() : blameVo.getBlameGrossWeight());
                        blameVo.setBlameCount(ObjectUtil.isNotEmpty(blameVo.getBlameCount()) ? blameVo.getBlameCount().stripTrailingZeros() : blameVo.getBlameCount());
                        blameVo.setBlameAmount(ObjectUtil.isNotEmpty(blameVo.getBlameAmount()) ? blameVo.getBlameAmount().stripTrailingZeros() : blameVo.getBlameAmount());
                        blameVo.setRefundAmount(ObjectUtil.isNotEmpty(blameVo.getRefundAmount()) ? blameVo.getRefundAmount().stripTrailingZeros() : blameVo.getRefundAmount());
                        //商品实补=商品补贴-合计的退补贴金额
                        reportVo.setActualRegionSubsidyAmount(ObjectUtil.isNotEmpty(blameVo.getRefundSubsidyFreeAmount()) ? reportVo.getActualRegionSubsidyAmount().subtract(blameVo.getRefundSubsidyFreeAmount()) : reportVo.getActualRegionSubsidyAmount());
                    });
                }
            });
        }
    }

    /**
     * 字段填充
     * @param reportVoList
     */
    private void fillFinanceOrderItemReport(List<FinanceOrderItemReportVo> reportVoList) {
        //获取查询入参
        List<Long> customerIdList = reportVoList.stream().map(FinanceOrderItemReportVo::getCustomerId).distinct().toList();
        List<Long> regionWhIdList = reportVoList.stream().map(FinanceOrderItemReportVo::getRegionWhId).distinct().toList();
        List<Long> cityWhIdList = reportVoList.stream().map(FinanceOrderItemReportVo::getCityWhId).distinct().toList();
        List<Long> logisticsIdList = reportVoList.stream().map(FinanceOrderItemReportVo::getLogisticsId).distinct().toList();
        List<Long> categoryIdLevel2List = reportVoList.stream().map(FinanceOrderItemReportVo::getCategoryIdLevel2).distinct().toList();
        List<Long> supplierSkuIdList = reportVoList.stream().map(FinanceOrderItemReportVo::getSupplierSkuId).distinct().toList();
        List<Long> ruleRecordIdList = reportVoList.stream().map(FinanceOrderItemReportVo::getServiceRuleRecordId).filter(ObjectUtil::isNotEmpty).collect(Collectors.toList());
        ruleRecordIdList.addAll(reportVoList.stream().map(FinanceOrderItemReportVo::getFreightRuleRecordId).filter(ObjectUtil::isNotEmpty).toList());
        ruleRecordIdList = ruleRecordIdList.stream().distinct().toList();
        List<Long> itemIdList = reportVoList.stream().map(FinanceOrderItemReportVo::getId).distinct().toList();
        List<String> orderCodeList = reportVoList.stream().map(FinanceOrderItemReportVo::getOrderCode).distinct().toList();
        List<Long> supplierIdList = reportVoList.stream().map(FinanceOrderItemReportVo::getSupplierId).distinct().toList();
        //查询客户
        List<RemoteCustomerVo> customerVoList = remoteBasCustomerService.getByIds(customerIdList);
        //查询用户
        List<String> userCodeList = customerVoList.stream().map(RemoteCustomerVo::getUserCode).distinct().toList();
        Map<String, RemoteUserBo> userMap = remoteUserService.getUsersByUserCodes(userCodeList);
        //查询总仓
        List<RemoteRegionWhVo> regionWhVoList = remoteRegionWhService.selectRegionWhInfoByIds(regionWhIdList);
        //查询城市仓
        List<RemoteCityWhVo> cityWhVoList = remoteCityWhService.queryList(cityWhIdList);
        //查询物流
        List<RemoteRegionLogisticsVo> regionLogisticsVoList = new ArrayList<>();
        if(ObjectUtil.isNotEmpty(logisticsIdList)){
            RemoteRegionLogisticsQueryBo logisticsQueryBo = new RemoteRegionLogisticsQueryBo();
            logisticsQueryBo.setLogisticsIds(logisticsIdList);
            regionLogisticsVoList = regionLogisticsService.queryList(logisticsQueryBo);
        }
        //查询二级分类
        List<RemoteCategoryVO> categoryVoList = remoteCategoryService.queryListByIds(categoryIdLevel2List);
        //查询供应商商品组合名称
        Map<Long, String> skuCombinationNameMap = remoteSupplierSkuService.getSkuProductName(supplierSkuIdList);
        //查询分润规则记录
        List<RemoteWhProfitRuleRecordVo> whProfitRuleRecordVos = remoteWhProfitRuleService.selectListByIds(ruleRecordIdList);
        //查询退佣金总和
        List<RefundDistributionAmountVo> refundDistributionAmountTotal = orderItemMapper.getRefundDistributionAmountTotal(itemIdList);
        //查分账流水表（总仓维度）
        List<FinanceOrderItemReportVo> tradeAccTransList = orderItemMapper.getTradeAccTransByOrderCode(orderCodeList);
        //查支付表
        List<FinanceOrderItemReportVo> tradePayList = orderItemMapper.getTradePayByOrderCode(orderCodeList);
        //查分账流水表（商品维度）
        List<FinanceOrderItemReportVo> transCashList = orderItemMapper.getTransCashByOrderCode(orderCodeList);
        //供应商
        List<RemoteSupplierVo> supplierVoList = remoteSupplierService.getSupplierByIds(supplierIdList);
        //转换成map
        Map<Long, RemoteCustomerVo> customerVoMap = customerVoList.stream().collect(Collectors.toMap(RemoteCustomerVo::getId, Function.identity(), (e1, e2) -> e1));
        Map<Long, RemoteRegionWhVo> regionWhVoMap = regionWhVoList.stream().collect(Collectors.toMap(RemoteRegionWhVo::getId, Function.identity(), (e1, e2) -> e1));
        Map<Long, RemoteCityWhVo> cityWhVoMap = cityWhVoList.stream().collect(Collectors.toMap(RemoteCityWhVo::getId, Function.identity(), (e1, e2) -> e1));
        Map<Long, RemoteRegionLogisticsVo> regionLogisticsVoMap = regionLogisticsVoList.stream().collect(Collectors.toMap(RemoteRegionLogisticsVo::getId, Function.identity(), (e1, e2) -> e1));
        Map<Long, RemoteCategoryVO> categoryVoMap = categoryVoList.stream().collect(Collectors.toMap(RemoteCategoryVO::getId, Function.identity(), (e1, e2) -> e1));
        Map<Long, RemoteWhProfitRuleRecordVo> whProfitRuleRecordVoMap = whProfitRuleRecordVos.stream().collect(Collectors.toMap(RemoteWhProfitRuleRecordVo::getId, Function.identity(), (e1, e2) -> e1));
        Map<Long, RefundDistributionAmountVo> refundDistributionAmountVoMap = refundDistributionAmountTotal.stream().collect(Collectors.toMap(RefundDistributionAmountVo::getItemId, Function.identity(), (e1, e2) -> e1));
        Map<String, FinanceOrderItemReportVo> tradeAccTransMap = tradeAccTransList.stream().collect(Collectors.toMap(FinanceOrderItemReportVo::getOrderCode, Function.identity(), (e1, e2) -> e1));
        Map<String, FinanceOrderItemReportVo> tradePayMap = tradePayList.stream().collect(Collectors.toMap(FinanceOrderItemReportVo::getOrderCode, Function.identity(), (e1, e2) -> e1));
        Map<String, FinanceOrderItemReportVo> transCashMap = transCashList.stream().collect(Collectors.toMap(e -> e.getOrderCode() + "_" + e.getSupplierSkuId(), Function.identity(), (e1, e2) -> e1));
        Map<Long, RemoteSupplierVo> supplierVoMap = supplierVoList.stream().collect(Collectors.toMap(RemoteSupplierVo::getId, Function.identity(), (e1, e2) -> e1));

        //循环处理
        reportVoList.forEach(reportVo -> {
            //从map中获取数据
            RemoteCustomerVo remoteCustomerVo = customerVoMap.get(reportVo.getCustomerId());
            RemoteRegionWhVo remoteRegionWhVo = regionWhVoMap.get(reportVo.getRegionWhId());
            RemoteCityWhVo remoteCityWhVo = cityWhVoMap.get(reportVo.getCityWhId());
            RemoteRegionLogisticsVo remoteRegionLogisticsVo = regionLogisticsVoMap.get(reportVo.getLogisticsId());
            RemoteCategoryVO remoteCategoryVO = categoryVoMap.get(reportVo.getCategoryIdLevel2());
            RemoteWhProfitRuleRecordVo serviceProfitRuleRecordVo = whProfitRuleRecordVoMap.get(reportVo.getServiceRuleRecordId());
            RemoteWhProfitRuleRecordVo freightProfitRuleRecordVo = whProfitRuleRecordVoMap.get(reportVo.getFreightRuleRecordId());
            RefundDistributionAmountVo refundDistributionAmountVo = refundDistributionAmountVoMap.get(reportVo.getId());
            FinanceOrderItemReportVo tradeAccTrans = tradeAccTransMap.get(reportVo.getOrderCode());
            FinanceOrderItemReportVo tradePay = tradePayMap.get(reportVo.getOrderCode());
            FinanceOrderItemReportVo transCash = transCashMap.get(reportVo.getOrderCode() + "_" + reportVo.getSupplierSkuId());
            RemoteSupplierVo remoteSupplierVo = supplierVoMap.get(reportVo.getSupplierId());
            //设置字段
            if(ObjectUtil.isNotEmpty(remoteCustomerVo)){
                RemoteUserBo remoteUserBo = userMap.get(remoteCustomerVo.getUserCode());
                reportVo.setCustomerName(ObjectUtil.isNotEmpty(remoteUserBo) ? remoteUserBo.getRealName() : "");
                reportVo.setPhoneNo(ObjectUtil.isNotEmpty(remoteUserBo) ? remoteUserBo.getPhoneNo() : "");
            }
            reportVo.setRegionWhName(ObjectUtil.isNotEmpty(remoteRegionWhVo) ? remoteRegionWhVo.getRegionWhName() : "");
            reportVo.setCityWhName(ObjectUtil.isNotEmpty(remoteCityWhVo) ? remoteCityWhVo.getName() : "");
            reportVo.setLogisticsName(ObjectUtil.isNotEmpty(remoteRegionLogisticsVo) ? remoteRegionLogisticsVo.getLogisticsName() : "");
            reportVo.setCategoryLevel2PathName(ObjectUtil.isNotEmpty(remoteCategoryVO) ? remoteCategoryVO.getPathName() : "");
            reportVo.setProductName(skuCombinationNameMap.get(reportVo.getSupplierSkuId()));
            reportVo.setSupplierName(ObjectUtil.isNotEmpty(remoteSupplierVo) ? remoteSupplierVo.getName() : "");
            //设置商品金额
            if(RegionWhTypeEnum.NORMAL.getCode().equals(remoteRegionWhVo.getType())){
                reportVo.setProxyProductAmountAfter(reportVo.getProductAmount().subtract(reportVo.getProductFreeAmount()));
            }else if(RegionWhTypeEnum.GROUND.getCode().equals(remoteRegionWhVo.getType())){
                reportVo.setGroundProductAmountAfter(reportVo.getProductAmount().subtract(reportVo.getProductFreeAmount()));
            }
            //设置分润规则
            reportVo.setPurchaseFeeRule(this.formatProfitRule(serviceProfitRuleRecordVo));
            reportVo.setLogisticsProfitRule(this.formatProfitRule(freightProfitRuleRecordVo));
            //佣金减掉退佣金
            if(ObjectUtil.isNotEmpty(refundDistributionAmountVo) && ObjectUtil.isNotEmpty(refundDistributionAmountVo.getRefundDistributionAmountTotal()) && ObjectUtil.isNotEmpty(reportVo.getCommissionAmount())){
                reportVo.setCommissionAmount(reportVo.getCommissionAmount().subtract(refundDistributionAmountVo.getRefundDistributionAmountTotal()));
            }
            //平安单号（般果）
            reportVo.setRegionWhSplitNo(ObjectUtil.isNotEmpty(tradeAccTrans) ? tradeAccTrans.getRegionWhSplitNo() : "");
            //支付单号
            reportVo.setTradeNo(ObjectUtil.isNotEmpty(tradePay) ? tradePay.getTradeNo() : "");
            //提现、结算字段
            if(ObjectUtil.isNotEmpty(transCash)){
                reportVo.setSupplierSplitNo(transCash.getSupplierSplitNo());
                reportVo.setAvailDay(transCash.getAvailDay());
                reportVo.setAvailTime(transCash.getAvailTime());
                reportVo.setAvailNo(transCash.getAvailNo());
                reportVo.setCashNo(transCash.getCashNo());
                reportVo.setApplyDay(transCash.getApplyDay());
                reportVo.setApplyTime(transCash.getApplyTime());
                reportVo.setCashDay(transCash.getCashDay());
                reportVo.setCashTime(transCash.getCashTime());
                reportVo.setCashStatus(transCash.getCashStatus());
                reportVo.setCashStatusName(transCash.getCashStatusName());
            }
            //切割时间字段的年月日、时分秒
            this.splitTimeDay(reportVo);
        });
    }

    /**
     * 拼接格式化分润规则字段
     * @param profitRuleRecordVo 分润规则
     */
    private String formatProfitRule(RemoteWhProfitRuleRecordVo profitRuleRecordVo) {
        StringBuilder str = new StringBuilder();
        if(ObjectUtil.isNotEmpty(profitRuleRecordVo) && ObjectUtil.isNotEmpty(profitRuleRecordVo.getCityWhRatio()) && ObjectUtil.isNotEmpty(profitRuleRecordVo.getRegionWhRatio())){
            String format = String.format("%s - %.2f%%，%s - %.2f%%", profitRuleRecordVo.getCityWhName(), profitRuleRecordVo.getCityWhRatio() * 100, profitRuleRecordVo.getRegionWhName(), profitRuleRecordVo.getRegionWhRatio() * 100);
            str.append(format);
            if(ObjectUtil.isNotEmpty(profitRuleRecordVo.getSuperiorCityWhName()) && ObjectUtil.isNotEmpty(profitRuleRecordVo.getSuperiorCityWhRatio())){
                String format1 = String.format("，%s - %.2f%%", profitRuleRecordVo.getSuperiorCityWhName(), profitRuleRecordVo.getSuperiorCityWhRatio() * 100);
                str.append(format1);
            }
        }
        return str.toString();
    }

    /**
     * 切割时间字段的年月日、时分秒
     * @param reportVo
     */
    private void splitTimeDay(FinanceOrderItemReportVo reportVo) {
        //时间字段分割成年月日、时分秒
        if(StringUtils.isNotBlank(reportVo.getPayDate())){
            String[] s = reportVo.getPayDate().split(" ");
            reportVo.setPayDate(s.length > 0 ? s[0] : "");
            reportVo.setPayTime(s.length > 1 ? s[1] : "");
        }
        if(StringUtils.isNotBlank(reportVo.getAvailDay())){
            String[] s = reportVo.getAvailDay().split(" ");
            reportVo.setAvailDay(s.length > 0 ? s[0] : "");
            reportVo.setAvailTime(s.length > 1 ? s[1] : "");
        }
        if(StringUtils.isNotBlank(reportVo.getApplyDay())){
            String[] s = reportVo.getApplyDay().split(" ");
            reportVo.setApplyDay(s.length > 0 ? s[0] : "");
            reportVo.setApplyTime(s.length > 1 ? s[1] : "");
        }
        if(StringUtils.isNotBlank(reportVo.getCashDay())){
            String[] s = reportVo.getCashDay().split(" ");
            reportVo.setCashDay(s.length > 0 ? s[0] : "");
            reportVo.setCashTime(s.length > 1 ? s[1] : "");
        }
    }

    /**
     * 财务报表-订单详情/供应商对账数据导出请求
     */
    @Override
    public void exportFinanceOrderItemReport(FinanceOrderItemReportBo bo) {
        Page<FinanceOrderItemReportVo> page = this.financeOrderItemReportPage(bo);
        if (CollUtil.isEmpty(page.getRecords())) {
            throw new ServiceException("没有数据可导出");
        }
        //1-订单详情，2-供应商对账
        ExportModuleEnum exportModuleEnum = Objects.equals(1, bo.getPageType()) ? ExportModuleEnum.BI_FINANCE_ORDER_DETAIL_RECORD : ExportModuleEnum.BI_FINANCE_SUPPLIER_RECORD;
        remoteExportJobService.addJob(exportModuleEnum, JSONUtil.toJsonStr(bo));
    }

    /**
     * 财务报表-订单详情数据导出，回调
     */
    @Override
    public String exportFinanceOrderDetailsReportJob(Long jobId, FinanceOrderItemReportBo bo) {
        String fileUrl = "";
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
             ExcelWriter excelWriter = EasyExcel.write(outputStream, FinanceOrderItemExportVo.class)
                .autoCloseStream(false)
                // 自动适配
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                // 大数值自动转换 防止失真
                .registerConverter(new ExcelBigNumberConvert()).build()
        ) {
            boolean hasNext;
            Long totalCount = 0L;
            Integer pageNum = 1;
            bo.setPageSize(1000);
            String sheetNmae = "Sheet";
            Long seqNo = 1L;
            WriteSheet writeSheet = null;
            do {
                bo.setPageNum(pageNum);
                Page<FinanceOrderItemReportVo> page = this.financeOrderItemReportPage(bo);
                if (CollUtil.isEmpty(page.getRecords())) {
                    break;
                }
                List<FinanceOrderItemExportVo> exportVos = new ArrayList<>();
                for (FinanceOrderItemReportVo vo : page.getRecords()) {
                    if(ObjectUtil.isEmpty(vo.getBlameVoList())){
                        FinanceOrderItemExportVo exportVo = new FinanceOrderItemExportVo();
                        BeanUtils.copyProperties(vo, exportVo);
                        exportVo.setSeqNo(String.valueOf(seqNo));
                        exportVos.add(exportVo);
                    }else{
                        for (FinanceOrderItemBlameVo blameVo : vo.getBlameVoList()) {
                            FinanceOrderItemExportVo exportVo = new FinanceOrderItemExportVo();
                            BeanUtils.copyProperties(vo, exportVo);
                            BeanUtils.copyProperties(blameVo, exportVo);
                            exportVo.setSeqNo(String.valueOf(seqNo));
                            exportVos.add(exportVo);
                        }
                    }
                    seqNo = seqNo + 1;
                }
                totalCount += exportVos.size();
                Long sheetNum = totalCount / 1000000;
                writeSheet = EasyExcel.writerSheet(sheetNmae + sheetNum)
                        .registerWriteHandler(new CellMergeStrategy(exportVos, true)).build();
                hasNext = page.hasNext();
                excelWriter.write(exportVos, writeSheet);
                pageNum ++;
            } while (hasNext);
            if(ObjectUtil.isNotEmpty(writeSheet)){
                FinanceOrderItemReportVo total = this.financeOrderItemReportTotal(bo);
                FinanceOrderItemExportVo exportVo = BeanUtil.copyProperties(total, FinanceOrderItemExportVo.class);
                exportVo.setSeqNo("合计");
                exportVo.setBlameGrossWeight(total.getBlameGrossWeightTotal());
                exportVo.setBlameCount(total.getBlameCountTotal());
                exportVo.setBlameAmount(total.getBlameAmountTotal());
                exportVo.setRefundAmount(total.getRefundAmountTotal());
                exportVo.setRefundSubsidyFreeAmount(total.getRefundSubsidyFreeAmountTotal());
                excelWriter.write(CollectionUtil.newArrayList(exportVo), writeSheet);
            }
            excelWriter.finish();
            byte[] excelBytes = outputStream.toByteArray();
            OssClient storage = OssFactory.instance();
            fileUrl = storage.uploadTempFile("财务报表-订单详情数据导出", ContentType.OCTET_STREAM.getValue(), excelBytes, 1).getUrl();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return fileUrl;
    }

    /**
     * 财务报表-供应商对账数据导出，回调
     */
    @Override
    public String exportFinanceSupplierReportJob(Long jobId, FinanceOrderItemReportBo bo) {
        String fileUrl = "";
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
             ExcelWriter excelWriter = EasyExcel.write(outputStream, FinanceOrderSupplierExportVo.class)
                .autoCloseStream(false)
                // 自动适配
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                // 大数值自动转换 防止失真
                .registerConverter(new ExcelBigNumberConvert()).build()
        ) {
            boolean hasNext;
            Long totalCount = 0L;
            Integer pageNum = 1;
            bo.setPageSize(1000);
            String sheetNmae = "Sheet";
            Long seqNo = 1L;
            WriteSheet writeSheet = null;
            do {
                bo.setPageNum(pageNum);
                Page<FinanceOrderItemReportVo> page = this.financeOrderItemReportPage(bo);
                if (CollUtil.isEmpty(page.getRecords())) {
                    break;
                }
                List<FinanceOrderSupplierExportVo> exportVos = new ArrayList<>();
                for (FinanceOrderItemReportVo vo : page.getRecords()) {
                    if(ObjectUtil.isEmpty(vo.getBlameVoList())){
                        FinanceOrderSupplierExportVo exportVo = new FinanceOrderSupplierExportVo();
                        BeanUtils.copyProperties(vo, exportVo);
                        exportVo.setSeqNo(String.valueOf(seqNo));
                        exportVos.add(exportVo);
                    }else{
                        for (FinanceOrderItemBlameVo blameVo : vo.getBlameVoList()) {
                            FinanceOrderSupplierExportVo exportVo = new FinanceOrderSupplierExportVo();
                            BeanUtils.copyProperties(vo, exportVo);
                            BeanUtils.copyProperties(blameVo, exportVo);
                            exportVo.setSeqNo(String.valueOf(seqNo));
                            exportVos.add(exportVo);
                        }
                    }
                    seqNo = seqNo + 1;
                }
                totalCount += exportVos.size();
                Long sheetNum = totalCount / 1000000;
                writeSheet = EasyExcel.writerSheet(sheetNmae + sheetNum)
                        .registerWriteHandler(new CellMergeStrategy(exportVos, true)).build();
                hasNext = page.hasNext();
                excelWriter.write(exportVos, writeSheet);
                pageNum ++;
            } while (hasNext);
            if(ObjectUtil.isNotEmpty(writeSheet)){
                FinanceOrderItemReportVo total = this.financeOrderItemReportTotal(bo);
                FinanceOrderSupplierExportVo exportVo = BeanUtil.copyProperties(total, FinanceOrderSupplierExportVo.class);
                exportVo.setSeqNo("合计");
                exportVo.setBlameGrossWeight(total.getBlameGrossWeightTotal());
                exportVo.setBlameCount(total.getBlameCountTotal());
                exportVo.setBlameAmount(total.getBlameAmountTotal());
                exportVo.setRefundAmount(total.getRefundAmountTotal());
                exportVo.setRefundSubsidyFreeAmount(total.getRefundSubsidyFreeAmountTotal());
                excelWriter.write(CollectionUtil.newArrayList(exportVo), writeSheet);
            }
            excelWriter.finish();
            byte[] excelBytes = outputStream.toByteArray();
            OssClient storage = OssFactory.instance();
            fileUrl = storage.uploadTempFile( "财务报表-供应商对账数据导出", ContentType.OCTET_STREAM.getValue(), excelBytes, 1).getUrl();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return fileUrl;
    }

    /**
     * 合计财务报表-订单详情/供应商对账
     */
    @Override
    public FinanceOrderItemReportVo financeOrderItemReportTotal(FinanceOrderItemReportBo bo) {
        //订单项合计
        FinanceOrderItemReportVo reportVoTotal = orderItemMapper.financeOrderItemReportTotal(bo);
        if(ObjectUtil.isEmpty(bo.getSourceType()) || BlameSourceTypeEnum.LESS_GOODS.getCode().equals(bo.getSourceType())){
            //少货判责合计
            FinanceOrderItemBlameVo lessBlameTotal = orderItemMapper.getLessBlameTotal(bo);
            if(ObjectUtil.isNotEmpty(lessBlameTotal)){
                //累加少货判责
                reportVoTotal.setBlameGrossWeightTotal(ObjectUtil.isNotEmpty(lessBlameTotal.getBlameGrossWeight()) ? lessBlameTotal.getBlameGrossWeight().add(reportVoTotal.getBlameGrossWeightTotal()) : reportVoTotal.getBlameGrossWeightTotal());
                reportVoTotal.setBlameCountTotal(ObjectUtil.isNotEmpty(lessBlameTotal.getBlameCount()) ? lessBlameTotal.getBlameCount().add(reportVoTotal.getBlameCountTotal()) : reportVoTotal.getBlameCountTotal());
                reportVoTotal.setBlameAmountTotal(ObjectUtil.isNotEmpty(lessBlameTotal.getBlameAmount()) ? lessBlameTotal.getBlameAmount().add(reportVoTotal.getBlameAmountTotal()) : reportVoTotal.getBlameAmountTotal());
                reportVoTotal.setRefundAmountTotal(ObjectUtil.isNotEmpty(lessBlameTotal.getRefundAmount()) ? lessBlameTotal.getRefundAmount().add(reportVoTotal.getRefundAmountTotal()) : reportVoTotal.getRefundAmountTotal());
                reportVoTotal.setRefundSubsidyFreeAmountTotal(ObjectUtil.isNotEmpty(lessBlameTotal.getRefundSubsidyFreeAmount()) ? lessBlameTotal.getRefundSubsidyFreeAmount().add(reportVoTotal.getRefundSubsidyFreeAmountTotal()) : reportVoTotal.getRefundSubsidyFreeAmountTotal());
            }
        }
        if(ObjectUtil.isEmpty(bo.getSourceType()) || BlameSourceTypeEnum.LOSS_ORDER.getCode().equals(bo.getSourceType())){
            //报损合计
            FinanceOrderItemBlameVo lossBlameTotal = orderItemMapper.getLossBlameTotal(bo);
            if(ObjectUtil.isNotEmpty(lossBlameTotal)){
                //累加其他判责
                reportVoTotal.setBlameGrossWeightTotal(ObjectUtil.isNotEmpty(lossBlameTotal.getBlameGrossWeight()) ? lossBlameTotal.getBlameGrossWeight().add(reportVoTotal.getBlameGrossWeightTotal()) : reportVoTotal.getBlameGrossWeightTotal());
                reportVoTotal.setBlameCountTotal(ObjectUtil.isNotEmpty(lossBlameTotal.getBlameCount()) ? lossBlameTotal.getBlameCount().add(reportVoTotal.getBlameCountTotal()) : reportVoTotal.getBlameCountTotal());
                reportVoTotal.setBlameAmountTotal(ObjectUtil.isNotEmpty(lossBlameTotal.getBlameAmount()) ? lossBlameTotal.getBlameAmount().add(reportVoTotal.getBlameAmountTotal()) : reportVoTotal.getBlameAmountTotal());
                reportVoTotal.setRefundAmountTotal(ObjectUtil.isNotEmpty(lossBlameTotal.getRefundAmount()) ? lossBlameTotal.getRefundAmount().add(reportVoTotal.getRefundAmountTotal()) : reportVoTotal.getRefundAmountTotal());
                reportVoTotal.setRefundSubsidyFreeAmountTotal(ObjectUtil.isNotEmpty(lossBlameTotal.getRefundSubsidyFreeAmount()) ? lossBlameTotal.getRefundSubsidyFreeAmount().add(reportVoTotal.getRefundSubsidyFreeAmountTotal()) : reportVoTotal.getRefundSubsidyFreeAmountTotal());
            }
        }
        if(ObjectUtil.isEmpty(bo.getSourceType()) || (!BlameSourceTypeEnum.LESS_GOODS.getCode().equals(bo.getSourceType())
                && !BlameSourceTypeEnum.LOSS_ORDER.getCode().equals(bo.getSourceType()))){
            //缺货、差额退判责合计
            FinanceOrderItemBlameVo otherBlameTotal = orderItemMapper.getOtherBlameTotal(bo);
            if(ObjectUtil.isNotEmpty(otherBlameTotal)){
                //累加其他判责
                reportVoTotal.setBlameGrossWeightTotal(ObjectUtil.isNotEmpty(otherBlameTotal.getBlameGrossWeight()) ? otherBlameTotal.getBlameGrossWeight().add(reportVoTotal.getBlameGrossWeightTotal()) : reportVoTotal.getBlameGrossWeightTotal());
                reportVoTotal.setBlameCountTotal(ObjectUtil.isNotEmpty(otherBlameTotal.getBlameCount()) ? otherBlameTotal.getBlameCount().add(reportVoTotal.getBlameCountTotal()) : reportVoTotal.getBlameCountTotal());
                reportVoTotal.setBlameAmountTotal(ObjectUtil.isNotEmpty(otherBlameTotal.getBlameAmount()) ? otherBlameTotal.getBlameAmount().add(reportVoTotal.getBlameAmountTotal()) : reportVoTotal.getBlameAmountTotal());
                reportVoTotal.setRefundAmountTotal(ObjectUtil.isNotEmpty(otherBlameTotal.getRefundAmount()) ? otherBlameTotal.getRefundAmount().add(reportVoTotal.getRefundAmountTotal()) : reportVoTotal.getRefundAmountTotal());
                reportVoTotal.setRefundSubsidyFreeAmountTotal(ObjectUtil.isNotEmpty(otherBlameTotal.getRefundSubsidyFreeAmount()) ? otherBlameTotal.getRefundSubsidyFreeAmount().add(reportVoTotal.getRefundSubsidyFreeAmountTotal()) : reportVoTotal.getRefundSubsidyFreeAmountTotal());
            }
        }
        //商品实补=商品补贴-合计的退补贴金额
        if(ObjectUtil.isNotEmpty(reportVoTotal.getRegionSubsidyAmount()) && ObjectUtil.isNotEmpty(reportVoTotal.getRefundSubsidyFreeAmountTotal())){
            reportVoTotal.setActualRegionSubsidyAmount(reportVoTotal.getRegionSubsidyAmount().subtract(reportVoTotal.getRefundSubsidyFreeAmountTotal()));
        }
        return reportVoTotal;
    }

    /**
     * 小计-财务报表-订单详情/供应商对账
     */
    @Override
    public FinanceOrderItemReportVo financeOrderItemReportSubtotal(Page<FinanceOrderItemReportVo> page) {
        FinanceOrderItemReportVo total = new FinanceOrderItemReportVo();
        total.setBlameGrossWeightTotal(BigDecimal.ZERO);
        total.setBlameCountTotal(BigDecimal.ZERO);
        total.setBlameAmountTotal(BigDecimal.ZERO);
        total.setRefundAmountTotal(BigDecimal.ZERO);
        total.setTotalGrossWeight(BigDecimal.ZERO);
        total.setTotalNetWeight(BigDecimal.ZERO);
        total.setCount(0);
        total.setGroundProductAmountAfter(BigDecimal.ZERO);
        total.setProxyProductAmountAfter(BigDecimal.ZERO);
        total.setProductFreeAmount(BigDecimal.ZERO);
        total.setPlatformServiceAmount(BigDecimal.ZERO);
        total.setPlatformFreightAmount(BigDecimal.ZERO);
        total.setBaseFreightAmount(BigDecimal.ZERO);
        total.setPlatformFreightAmountLevel2(BigDecimal.ZERO);
        total.setRegionFreightAmount(BigDecimal.ZERO);
        total.setPlatformServiceAmountAfter(BigDecimal.ZERO);
        total.setFreightTotalFreeAmount(BigDecimal.ZERO);
        total.setFinancialServiceAmount(BigDecimal.ZERO);
        total.setTotalGrossWeight(BigDecimal.ZERO);
        total.setPlatformServiceFreeAmount(BigDecimal.ZERO);
        total.setCommissionAmount(BigDecimal.ZERO);
        total.setRefundSubsidyFreeAmountTotal(BigDecimal.ZERO);
        total.setRegionSubsidyAmount(BigDecimal.ZERO);
        total.setActualRegionSubsidyAmount(BigDecimal.ZERO);
        total.setProductAmount(BigDecimal.ZERO);

        page.getRecords().forEach(record -> {
            total.setTotalGrossWeight(ObjectUtil.isNotEmpty(record.getTotalGrossWeight()) ? record.getTotalGrossWeight().add(total.getTotalGrossWeight()) : total.getTotalGrossWeight());
            total.setTotalNetWeight(ObjectUtil.isNotEmpty(record.getTotalNetWeight()) ? record.getTotalNetWeight().add(total.getTotalNetWeight()) : total.getTotalNetWeight());
            total.setCount(ObjectUtil.isNotEmpty(record.getCount()) ? record.getCount() + total.getCount() : total.getCount());
            total.setGroundProductAmountAfter(ObjectUtil.isNotEmpty(record.getGroundProductAmountAfter()) ? record.getGroundProductAmountAfter().add(total.getGroundProductAmountAfter()) : total.getGroundProductAmountAfter());
            total.setProxyProductAmountAfter(ObjectUtil.isNotEmpty(record.getProxyProductAmountAfter()) ? record.getProxyProductAmountAfter().add(total.getProxyProductAmountAfter()) : total.getProxyProductAmountAfter());
            total.setProductFreeAmount(ObjectUtil.isNotEmpty(record.getProductFreeAmount()) ? record.getProductFreeAmount().add(total.getProductFreeAmount()) : total.getProductFreeAmount());
            total.setPlatformServiceAmount(ObjectUtil.isNotEmpty(record.getPlatformServiceAmount()) ? record.getPlatformServiceAmount().add(total.getPlatformServiceAmount()) : total.getPlatformServiceAmount());
            total.setPlatformFreightAmount(ObjectUtil.isNotEmpty(record.getPlatformFreightAmount()) ? record.getPlatformFreightAmount().add(total.getPlatformFreightAmount()) : total.getPlatformFreightAmount());
            total.setBaseFreightAmount(ObjectUtil.isNotEmpty(record.getBaseFreightAmount()) ? record.getBaseFreightAmount().add(total.getBaseFreightAmount()) : total.getBaseFreightAmount());
            total.setPlatformFreightAmountLevel2(ObjectUtil.isNotEmpty(record.getPlatformFreightAmountLevel2()) ? record.getPlatformFreightAmountLevel2().add(total.getPlatformFreightAmountLevel2()) : total.getPlatformFreightAmountLevel2());
            total.setRegionFreightAmount(ObjectUtil.isNotEmpty(record.getRegionFreightAmount()) ? record.getRegionFreightAmount().add(total.getRegionFreightAmount()) : total.getRegionFreightAmount());
            total.setPlatformServiceAmountAfter(ObjectUtil.isNotEmpty(record.getPlatformServiceAmountAfter()) ? record.getPlatformServiceAmountAfter().add(total.getPlatformServiceAmountAfter()) : total.getPlatformServiceAmountAfter());
            total.setFreightTotalFreeAmount(ObjectUtil.isNotEmpty(record.getFreightTotalFreeAmount()) ? record.getFreightTotalFreeAmount().add(total.getFreightTotalFreeAmount()) : total.getFreightTotalFreeAmount());
            total.setFinancialServiceAmount(ObjectUtil.isNotEmpty(record.getFinancialServiceAmount()) ? record.getFinancialServiceAmount().add(total.getFinancialServiceAmount()) : total.getFinancialServiceAmount());
            total.setPlatformServiceFreeAmount(ObjectUtil.isNotEmpty(record.getPlatformServiceFreeAmount()) ? record.getPlatformServiceFreeAmount().add(total.getPlatformServiceFreeAmount()) : total.getPlatformServiceFreeAmount());
            total.setCommissionAmount(ObjectUtil.isNotEmpty(record.getCommissionAmount()) ? record.getCommissionAmount().add(total.getCommissionAmount()) : total.getCommissionAmount());
            total.setRegionSubsidyAmount(ObjectUtil.isNotEmpty(record.getRegionSubsidyAmount()) ? record.getRegionSubsidyAmount().add(total.getRegionSubsidyAmount()) : total.getRegionSubsidyAmount());
            total.setActualRegionSubsidyAmount(ObjectUtil.isNotEmpty(record.getActualRegionSubsidyAmount()) ? record.getActualRegionSubsidyAmount().add(total.getActualRegionSubsidyAmount()) : total.getActualRegionSubsidyAmount());
            total.setProductAmount(ObjectUtil.isNotEmpty(record.getProductAmount()) ? record.getProductAmount().add(total.getProductAmount()) : total.getProductAmount());

            if(ObjectUtil.isNotEmpty(record.getBlameVoList())){
                record.getBlameVoList().forEach(blameVo -> {
                    //累加判责
                    total.setBlameGrossWeightTotal(ObjectUtil.isNotEmpty(blameVo.getBlameGrossWeight()) ? blameVo.getBlameGrossWeight().add(total.getBlameGrossWeightTotal()) : total.getBlameGrossWeightTotal());
                    total.setBlameCountTotal(ObjectUtil.isNotEmpty(blameVo.getBlameCount()) ? blameVo.getBlameCount().add(total.getBlameCountTotal()) : total.getBlameCountTotal());
                    total.setBlameAmountTotal(ObjectUtil.isNotEmpty(blameVo.getBlameAmount()) ? blameVo.getBlameAmount().add(total.getBlameAmountTotal()) : total.getBlameAmountTotal());
                    total.setRefundAmountTotal(ObjectUtil.isNotEmpty(blameVo.getRefundAmount()) ? blameVo.getRefundAmount().add(total.getRefundAmountTotal()) : total.getRefundAmountTotal());
                    total.setRefundSubsidyFreeAmountTotal(ObjectUtil.isNotEmpty(blameVo.getRefundSubsidyFreeAmount()) ? blameVo.getRefundSubsidyFreeAmount().add(total.getRefundSubsidyFreeAmountTotal()) : total.getRefundSubsidyFreeAmountTotal());
                });
            }
        });
        return total;
    }

    /**
     * 按销售日获取销售数据
     */
    @Override
    public BiOrderSaleDataVo getSaleDataByDay(BiOrderSaleDataBo bo) {
        // 查询总仓的销售时间
        RemoteRegionWhVo remoteRegionWhVo = remoteRegionWhService.queryById(bo.getRegionWhId());
        List<String> buyerList;
        if (!(remoteBuyerService.isLeader(LoginHelper.getUserCode()) && CollectionUtil.isEmpty(bo.getBuyerList()))){
            // 根据权限过滤采购员
            buyerList = checkBuyers(bo.getBuyerList(), remoteRegionWhVo.getRegionWhCode());
            bo.setBuyerList(buyerList);
        }
        if (bo.getSaleDate() == null){
            //获取当前供应商销售日
            LocalDate saleDate = SaleDateUtil.getSaleDate(remoteRegionWhVo.getSalesTimeEnd());
            bo.setSaleDate(saleDate);
        }
        return getByBo(bo);
    }

    /**
     * 获取销售数据详情
     */
    @Override
    public List<BiOrderSaleDataVo> getSaleDataDetail(BiOrderSaleDataBo bo) {
        List<BiOrderSaleDataVo> result = new ArrayList<>();
        RemoteRegionWhVo remoteRegionWhVo = remoteRegionWhService.queryById(bo.getRegionWhId());
        List<String> buyerList = null;
        if (!(remoteBuyerService.isLeader(LoginHelper.getUserCode()) && CollectionUtil.isEmpty(bo.getBuyerList()))){
            // 根据权限过滤采购员
            buyerList = checkBuyers(bo.getBuyerList(), remoteRegionWhVo.getRegionWhCode());
            bo.setBuyerList(buyerList);
        }
        LocalDate saleDate = SaleDateUtil.getSaleDate(remoteRegionWhVo.getSalesTimeEnd());
        if (bo.getSaleDate() == null) {
            if (saleDate.equals(LocalDate.now().withDayOfMonth(1))){
                return result;
            }
            bo.setSaleDateStart(LocalDate.now().withDayOfMonth(1));
            bo.setSaleDateEnd(saleDate.minusDays(1));
        }else {
            //销售日是本月1号，没数据
            if (bo.getSaleDate().equals(saleDate.withDayOfMonth(1)) && saleDate.equals(saleDate.withDayOfMonth(1))){
                return result;
            }
            //同月份的，少一天
            else if (bo.getSaleDate().getMonth().equals(saleDate.getMonth())){
                bo.setSaleDateStart(bo.getSaleDate().withDayOfMonth(1));
                bo.setSaleDateEnd(saleDate.minusDays(1));
            }else {
                bo.setSaleDateStart(bo.getSaleDate().withDayOfMonth(1));
                bo.setSaleDateEnd(bo.getSaleDate().with(TemporalAdjusters.lastDayOfMonth()));
            }
            bo.setSaleDate(null);
        }
        List<BiOrderSaleDataVo> saleDataDetail = orderMapper.getSaleDataDetail(bo);
        if (CollectionUtil.isNotEmpty(saleDataDetail)){
            //获取平均数
            BiOrderSaleDataVo vo = new BiOrderSaleDataVo();
            vo.setOrderCount(saleDataDetail.stream().mapToInt(BiOrderSaleDataVo::getOrderCount).sum()/saleDataDetail.size());
            vo.setSettleCount(saleDataDetail.stream().map(BiOrderSaleDataVo::getSettleCount).reduce(BigDecimal.ZERO,BigDecimal::add).divide(BigDecimal.valueOf(saleDataDetail.size()), 2, RoundingMode.HALF_UP));
            vo.setOrderProductCount(saleDataDetail.stream().mapToInt(BiOrderSaleDataVo::getOrderProductCount).sum()/saleDataDetail.size());
            vo.setOrderCustomerCount(saleDataDetail.stream().mapToInt(BiOrderSaleDataVo::getOrderCustomerCount).sum()/saleDataDetail.size());
            vo.setOrderCityCount(saleDataDetail.stream().mapToInt(BiOrderSaleDataVo::getOrderCityCount).sum()/saleDataDetail.size());
            vo.setRefundFewCount(saleDataDetail.stream().map(BiOrderSaleDataVo::getRefundFewCount).reduce(BigDecimal.ZERO,BigDecimal::add).divide(BigDecimal.valueOf(saleDataDetail.size()), 1, RoundingMode.HALF_UP));
            vo.setRefundOutCount(saleDataDetail.stream().map(BiOrderSaleDataVo::getRefundOutCount).reduce(BigDecimal.ZERO,BigDecimal::add).divide(BigDecimal.valueOf(saleDataDetail.size()), 1, RoundingMode.HALF_UP));
            vo.setType(1);
            result.add(vo);
            //获取合计
            BiOrderSaleDataVo total = orderMapper.getSaleDataByMon(bo);
            total.setType(2);
            result.add(total);
        }
        result.addAll(saleDataDetail);
        return result;
    }

    /**
     * 获取销售数据排名
     */
    @Override
    public List<BiOrderSaleDataVo> getSaleDataRanking(BiOrderSaleDataBo bo) {
        List<BiOrderSaleDataVo> result = orderMapper.getSaleDataRanking(bo);
        if (CollUtil.isNotEmpty(result)){
            switch (bo.getOrderByGroup()) {
                case 1 -> {
                    List<Long> cityWhIds = result.stream().map(BiOrderSaleDataVo::getCityWhId).distinct().toList();
                    List<RemoteCityWhVo> remoteCityWhVos = remoteCityWhService.queryList(cityWhIds);
                    Map<Long, String> cityMap = remoteCityWhVos.stream().collect(Collectors.toMap(RemoteCityWhVo::getId, RemoteCityWhVo::getName));
                    result.forEach(vo -> vo.setCityWhName(cityMap.get(vo.getCityWhId())));
                }
                case 3 -> {
                    List<Long> skuIds = result.stream().map(BiOrderSaleDataVo::getSkuId).distinct().toList();
                    Map<Long, String> skuProductName = remoteSupplierSkuService.getSkuProductName(skuIds);
                    result.forEach(vo -> vo.setSkuName(skuProductName.get(vo.getSkuId())));
                }
                case 4 -> {
                    List<Long> supplierIds = result.stream().map(BiOrderSaleDataVo::getSupplierId).distinct().toList();
                    List<RemoteSupplierVo> supplierByIds = remoteSupplierService.getSupplierByIds(supplierIds);
                    Map<Long, String> supplierMap = supplierByIds.stream().collect(Collectors.toMap(RemoteSupplierVo::getId, RemoteSupplierVo::getName));
                    result.forEach(vo -> vo.setSupplierName(supplierMap.get(vo.getSupplierId())));
                }
                case 5 -> {
                    List<Long> customerIds = result.stream().map(BiOrderSaleDataVo::getCustomerId).distinct().toList();
                    List<RemoteCustomerVo> customerVos = remoteBasCustomerService.getByIds(customerIds);
                    Map<Long, RemoteCustomerVo> userCodeMap = customerVos.stream().collect(Collectors.toMap(RemoteCustomerVo::getId, Function.identity()));
                    result.forEach(vo -> vo.setCustomerName(userCodeMap.getOrDefault(vo.getCustomerId(), new RemoteCustomerVo()).getName()));
                }
                default -> {}
            }
        }
        return result;
    }

    /**
     * 获取销售数据排名详情
     */
    @Override
    public TableDataInfo<BiOrderSaleDataRankingVo> getCitySaleDataRankingDetail(BiOrderSaleDataBo bo) {
        Page<BiOrderSaleDataRankingVo> page = orderMapper.getCitySaleDataRankingDetail(bo,bo.build());
        return null;
    }

    /**
     * 获取本月销售数据
     */
    @Override
    public BiOrderSaleDataVo getMonSaleData(BiOrderSaleDataBo bo) {
        // 查询总仓的销售时间
        RemoteRegionWhVo remoteRegionWhVo = remoteRegionWhService.queryById(bo.getRegionWhId());
        LocalDate saleDate = SaleDateUtil.getSaleDate(remoteRegionWhVo.getSalesTimeEnd());
        if (bo.getSaleDate() == null) {
            if (saleDate.equals(LocalDate.now().withDayOfMonth(1))){
                return new BiOrderSaleDataVo();
            }
            bo.setSaleDateStart(LocalDate.now().withDayOfMonth(1));
            bo.setSaleDateEnd(saleDate.minusDays(1));
        }else {
            //销售日是本月1号，没数据
            if (bo.getSaleDate().equals(saleDate.withDayOfMonth(1)) && saleDate.equals(saleDate.withDayOfMonth(1))){
                return new BiOrderSaleDataVo();
            }
            //同月份的，少一天
            else if (bo.getSaleDate().getMonth().equals(saleDate.getMonth())){
                bo.setSaleDateStart(bo.getSaleDate().withDayOfMonth(1));
                bo.setSaleDateEnd(saleDate.minusDays(1));
            }else {
                bo.setSaleDateStart(bo.getSaleDate().withDayOfMonth(1));
                bo.setSaleDateEnd(bo.getSaleDate().with(TemporalAdjusters.lastDayOfMonth()));
            }
            bo.setSaleDate(null);
        }

        List<String> buyerList = null;
        if (!(remoteBuyerService.isLeader(LoginHelper.getUserCode()) && CollectionUtil.isEmpty(bo.getBuyerList()))){
            // 根据权限过滤采购员
            buyerList = checkBuyers(bo.getBuyerList(), remoteRegionWhVo.getRegionWhCode());
            bo.setBuyerList(buyerList);
        }

        return getByBo(bo);
    }

    /**
     * 获取销售数据分析-环比
     */
    @Override
    public BiOrderSaleDataAnalysisVo getAnalysisByDay(BiOrderSaleDataBo bo) {
        BiOrderSaleDataAnalysisVo result = new BiOrderSaleDataAnalysisVo();
        RemoteRegionWhVo remoteRegionWhVo = remoteRegionWhService.queryById(bo.getRegionWhId());
        bo.setSalesTimeStart(remoteRegionWhVo.getSalesTimeStart());
        bo.setSalesTimeEnd(remoteRegionWhVo.getSalesTimeEnd());
        List<String> buyerList = null;
        if (!(remoteBuyerService.isLeader(LoginHelper.getUserCode()) && CollectionUtil.isEmpty(bo.getBuyerList()))){
            // 根据权限过滤采购员
            buyerList = checkBuyers(bo.getBuyerList(), remoteRegionWhVo.getRegionWhCode());
            bo.setBuyerList(buyerList);
        }
        if (bo.getSaleDate() == null) {
            LocalDate saleDate = SaleDateUtil.getSaleDate(remoteRegionWhVo.getSalesTimeEnd());
            bo.setSaleDate(saleDate);
        }
        // 当天各小时数据
        Map<String, BigDecimal> todayData = getHourlyDataForDate(bo);

        // 上个月整体数据（按小时合并）
        Map<String, BigDecimal> lastMonthHourlyData = getHourlyDataForLastMonth(bo);

        bo.setSaleDate(bo.getSaleDate().minusDays(1));
        // 昨天各小时数据
        Map<String, BigDecimal> yesterdayData = getHourlyDataForDate(bo);

        result.setNowSaleDate(todayData);
        result.setLastSaleDate(yesterdayData);
        result.setLastMonthSaleDate(lastMonthHourlyData);

        return result;
    }
    private Map<String, BigDecimal> getHourlyDataForDate(BiOrderSaleDataBo bo) {
        String s = Arrays.stream(bo.getSalesTimeStart().split(":")).toList().get(0);
        String e = Arrays.stream(bo.getSalesTimeEnd().split(":")).toList().get(0);
        // 查询数据库中的小时数据（如：{"14": 100}）
        List<BiOrderSaleMapVo> dataFromDb = orderMapper.selectHourlyOrderCount(bo);
        Map<String, BigDecimal> map = new HashMap<>();
        if (CollectionUtil.isNotEmpty(dataFromDb)) {
            map = dataFromDb.stream().collect(Collectors.toMap(BiOrderSaleMapVo::getHourKey, BiOrderSaleMapVo::getTotalCount));
        }
        // 构造完整 24 小时格式为 "HH:00"
        Map<String, BigDecimal> fullData = new LinkedHashMap<>();
        if (Integer.parseInt(s) >= Integer.parseInt(e)) {
            for (int hour = Integer.parseInt(e); hour < 24; hour++) {
                // 格式化为 "00:00", "01:00", ..., "23:00"
                String key = String.format("%02d:00", hour);
                fullData.put(key, map.getOrDefault(String.valueOf(hour), BigDecimal.ZERO));
            }
            for (int hour = 0; hour < Integer.parseInt(s); hour++) {
                // 格式化为 "00:00", "01:00", ..., "23:00"
                String key = String.format("%02d:00", hour);
                fullData.put(key, map.getOrDefault(String.valueOf(hour), BigDecimal.ZERO));
            }
        } else {
            for (int hour = Integer.parseInt(s); hour < Integer.parseInt(e); hour++) {
                // 格式化为 "00:00", "01:00", ..., "23:00"
                String key = String.format("%02d:00", hour);
                fullData.put(key, map.getOrDefault(String.valueOf(hour), BigDecimal.ZERO));
            }
        }

        return fullData;
    }
    private Map<String, BigDecimal> getHourlyDataForLastMonth(BiOrderSaleDataBo bo) {
        String s = Arrays.stream(bo.getSalesTimeStart().split(":")).toList().get(0);
        String e = Arrays.stream(bo.getSalesTimeEnd().split(":")).toList().get(0);
        LocalDate firstDayOfLastMonth = bo.getSaleDate().minusMonths(1);
//        LocalDate lastDayOfLastMonth = firstDayOfLastMonth.withDayOfMonth(firstDayOfLastMonth.lengthOfMonth());
        bo.setSaleDateStart(firstDayOfLastMonth);
        bo.setSaleDateEnd(firstDayOfLastMonth);
        // 查询上个月每天每小时的数据（格式为 "HH"）
        List<BiOrderSaleMapVo> hourlyData = orderMapper.selectHourlyOrderCountForDateRange(bo);
        Map<String, BigDecimal> map = new HashMap<>();
        if (CollectionUtil.isNotEmpty(hourlyData)) {
            map = hourlyData.stream().collect(Collectors.toMap(BiOrderSaleMapVo::getHourKey, BiOrderSaleMapVo::getTotalCount));
        }
        // 按小时聚合（格式转为 "HH:00"）
        Map<String, BigDecimal> aggregated = new LinkedHashMap<>();
        if (Integer.parseInt(s) >= Integer.parseInt(e)) {
            for (int hour = Integer.parseInt(e); hour < 24; hour++) {
                String key = String.format("%02d:00", hour);
                aggregated.put(key, BigDecimal.ZERO);
            }
            for (int hour = 0; hour < Integer.parseInt(s); hour++) {
                String key = String.format("%02d:00", hour);
                aggregated.put(key, BigDecimal.ZERO);
            }
        }else {
            for (int hour = Integer.parseInt(s); hour < Integer.parseInt(e); hour++) {
                String key = String.format("%02d:00", hour);
                aggregated.put(key, BigDecimal.ZERO);
            }
        }

        // 按照 orderedKeys 的顺序处理 map 数据
        Map<String, BigDecimal> finalMap = map;
        aggregated.forEach((k,v) -> {
            String key = k.split(":")[0];
            BigDecimal value = finalMap.get(key);
            if (value != null) {
                aggregated.put(k, value);
            }else {
                aggregated.put(k, BigDecimal.ZERO);
            }
        });

        return aggregated;
    }
    /**
     * 获取月度销售数据分析
     */
    @Override
    public BiOrderSaleDataAnalysisVo getAnalysisByMon(BiOrderSaleDataBo bo) {
        BiOrderSaleDataAnalysisVo result = new BiOrderSaleDataAnalysisVo();
        // 查询总仓的销售时间
        RemoteRegionWhVo remoteRegionWhVo = remoteRegionWhService.queryById(bo.getRegionWhId());
        if (bo.getSaleDate() == null) {
            LocalDate saleDate = SaleDateUtil.getSaleDate(remoteRegionWhVo.getSalesTimeEnd());
            bo.setSaleDate(saleDate);
        }
        List<String> buyerList = null;
        if (!(remoteBuyerService.isLeader(LoginHelper.getUserCode()) && CollectionUtil.isEmpty(bo.getBuyerList()))){
            // 根据权限过滤采购员
            buyerList = checkBuyers(bo.getBuyerList(), remoteRegionWhVo.getRegionWhCode());
            bo.setBuyerList(buyerList);
        }
        // 上个月时间范围
        LocalDate lastMonthStart = bo.getSaleDate().minusMonths(1).withDayOfMonth(1);
        LocalDate lastMonthEnd = lastMonthStart.withDayOfMonth(lastMonthStart.lengthOfMonth());

        bo.setSaleDateStart(lastMonthStart);
        bo.setSaleDateEnd(lastMonthEnd);
        // 查询上个月数据
        List<BiOrderSaleMapVo> lastMonthData = orderMapper.selectDailyOrderCount(bo);
        Map<String, BigDecimal> fullLastMonthData = fillMissingDaysAsDayOfMonth(lastMonthStart, lastMonthEnd, lastMonthData);

        LocalDate saleDate = SaleDateUtil.getSaleDate(remoteRegionWhVo.getSalesTimeEnd());
        if (bo.getSaleDate().getMonth().equals(saleDate.getMonth())){
            bo.setSaleDateStart(bo.getSaleDate().withDayOfMonth(1));
            bo.setSaleDateEnd(saleDate.minusDays(1));
        }else {
            bo.setSaleDateStart(bo.getSaleDate().withDayOfMonth(1));
            bo.setSaleDateEnd(bo.getSaleDate().with(TemporalAdjusters.lastDayOfMonth()));
        }
        // 查询本月数据（截止到今天）
        List<BiOrderSaleMapVo> thisMonthData = orderMapper.selectDailyOrderCount(bo);
        Map<String, BigDecimal> fullThisMonthData = fillMissingDaysAsDayOfMonth(bo.getSaleDateStart(), bo.getSaleDateEnd(), thisMonthData);

        result.setLastMonthSaleDate(fullLastMonthData);
        result.setThisMonthSaleDate(fullThisMonthData);

        return result;
    }

    private Map<String, BigDecimal> fillMissingDaysAsDayOfMonth(LocalDate startDate, LocalDate endDate, List<BiOrderSaleMapVo> dataFromDb) {
        Map<String, BigDecimal> result = new LinkedHashMap<>();
        Map<String, BigDecimal> map = new HashMap<>();
        if (CollectionUtil.isNotEmpty(dataFromDb)) {
            map = dataFromDb.stream().collect(Collectors.toMap(BiOrderSaleMapVo::getHourKey, BiOrderSaleMapVo::getTotalCount));
        }
        long daysBetween = ChronoUnit.DAYS.between(startDate, endDate);

        for (int i = 0; i <= daysBetween; i++) {
            LocalDate date = startDate.plus(i, ChronoUnit.DAYS);
            // 转为 "1", "2", ..., "31"
            String dayKey = String.valueOf(date.getDayOfMonth());
            result.put(dayKey, map.getOrDefault(date.toString(), BigDecimal.ZERO));
        }

        return result;
    }

    @Cacheable(value = "saleDataCache", keyGenerator = "customKeyGenerator")
    public BiOrderSaleDataVo getByBo(BiOrderSaleDataBo bo){
        return orderMapper.getSaleDataByDay(bo);
    }

    private List<String> checkBuyers(List<String> buyerList, String regionWhCode) {
        Set<String> buyerCodes = remoteBuyerService.getBuyerCodes(regionWhCode, true);
        if (CollUtil.isEmpty(buyerCodes)) {
            // 全部采购员
            return buyerList;
        }
        if (CollUtil.isNotEmpty(buyerList) && new HashSet<>(buyerCodes).containsAll(buyerList)){
            return buyerList;
        }
        if (buyerCodes.contains("admin")){
            return new ArrayList<>();
        }
        if (CollUtil.isEmpty(buyerList)) {
            return new ArrayList<>(buyerCodes);
        }
        return buyerList.stream().filter(buyerCodes::contains).collect(Collectors.toList());
    }
}
