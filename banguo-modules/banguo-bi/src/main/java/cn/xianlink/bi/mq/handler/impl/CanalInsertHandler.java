package cn.xianlink.bi.mq.handler.impl;

import cn.xianlink.bi.domain.es.SkuTest;
import cn.xianlink.bi.domain.vo.CanalMessageVo;
import cn.xianlink.bi.enums.CanalOperationTypeEnum;
import cn.xianlink.bi.esmapper.SkuTestMapper;
import cn.xianlink.bi.mq.handler.CanalOperationHandler;
import com.alibaba.fastjson.JSON;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * Canal INSERT操作处理器
 * 
 * <AUTHOR>
 */
@CustomLog
@RequiredArgsConstructor
@Component
public class CanalInsertHandler implements CanalOperationHandler {

    private final SkuTestMapper skuTestMapper;

    @Override
    public void handle(CanalMessageVo canalMessageVo) {
        log.keyword("canalInsertHandler").info("处理INSERT操作: {}", JSON.toJSONString(canalMessageVo));
        
        // 参数校验
        if (canalMessageVo == null || canalMessageVo.getData() == null || canalMessageVo.getData().isEmpty()) {
            log.keyword("canalInsertHandler").warn("Canal消息为空或数据为空，跳过处理");
            return;
        }
        
        try {
            List<SkuTest> skuList = canalMessageVo.getData().stream().map(item -> {
                SkuTest sku = new SkuTest();
                
                // 安全获取字段值，处理null情况
                sku.setId(safeGetString(item, "id", "UNKNOWN_" + System.currentTimeMillis()));
                sku.setSpuName(safeGetString(item, "spu_name", "未知商品"));
                sku.setSpuGrade(safeGetString(item, "spu_grade", "未知等级"));
                sku.setSpuStandards(safeGetString(item, "spu_standards", "标准规格"));
                sku.setCreateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                sku.setUpdateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                sku.setIsDeleted(safeGetBoolean(item, "is_deleted", false));
                
                return sku;
            }).toList();
            
            log.keyword("canalInsertHandler").info("转换完成，准备批量插入SKU数据，数量: {}", skuList.size());
            log.keyword("canalInsertHandler").debug("批量插入SKU数据详情: {}", JSON.toJSONString(skuList));
            
            insertBatch(skuList);
            
        } catch (Exception e) {
            log.keyword("canalInsertHandler").error("处理Canal INSERT消息异常: {}", JSON.toJSONString(canalMessageVo), e);
            throw e; // 重新抛出异常，让上层处理
        }
    }

    @Override
    public String getSupportedOperationType() {
        return "INSERT";
    }

    /**
     * 批量插入SKU数据
     * 
     * @param skus SKU列表
     */
    private void insertBatch(List<SkuTest> skus) {
        if (skus == null || skus.isEmpty()) {
            log.keyword("canalInsertHandler").warn("SKU列表为空，跳过批量插入");
            return;
        }
        
        // 数据验证和清洗
        validateAndCleanData(skus);
        
        try {
            log.keyword("canalInsertHandler").info("开始执行批量插入，数量: {}", skus.size());
            
            // 执行批量插入
            Integer result = skuTestMapper.insertBatch(skus);
            
            log.keyword("canalInsertHandler").info("批量插入SKU数据成功，插入数量: {}, 返回结果: {}", skus.size(), result);
            
        } catch (org.dromara.easyes.common.exception.EasyEsException e) {
            log.keyword("canalInsertHandler").error("EasyEs批量插入异常，数据: {}", JSON.toJSONString(skus), e);
            
            // 尝试单个插入作为降级处理
            handleBatchInsertFallback(skus);
            
        } catch (java.lang.reflect.UndeclaredThrowableException e) {
            log.keyword("canalInsertHandler").error("代理调用异常，可能是EasyEs内部异常，数据: {}", JSON.toJSONString(skus), e);
            
            // 检查是否是EasyEs异常
            Throwable cause = e.getCause();
            if (cause instanceof org.dromara.easyes.common.exception.EasyEsException) {
                log.keyword("canalInsertHandler").error("确认是EasyEs异常，启用降级处理");
                handleBatchInsertFallback(skus);
            } else {
                log.keyword("canalInsertHandler").error("未知代理异常，尝试降级处理");
                handleBatchInsertFallback(skus);
            }
            
        } catch (Exception e) {
            log.keyword("canalInsertHandler").error("批量插入SKU数据失败，数据: {}", JSON.toJSONString(skus), e);
            
            // 所有异常都尝试降级处理
            log.keyword("canalInsertHandler").warn("尝试降级处理所有异常");
            handleBatchInsertFallback(skus);
        }
    }
    
    /**
     * 批量插入失败时的降级处理：尝试单个插入
     */
    private void handleBatchInsertFallback(List<SkuTest> skus) {
        log.keyword("canalInsertHandler").info("批量插入失败，尝试单个插入降级处理，数量: {}", skus.size());
        
        int successCount = 0;
        int failCount = 0;
        StringBuilder errorDetails = new StringBuilder();
        
        for (SkuTest sku : skus) {
            try {
                log.keyword("canalInsertHandler").debug("尝试单个插入SKU: {}", JSON.toJSONString(sku));
                skuTestMapper.insert(sku);
                successCount++;
                log.keyword("canalInsertHandler").debug("单个插入成功: {}", sku.getId());
                          } catch (Exception e) {
                  failCount++;
                  String errorMsg = String.format("SKU ID: %s, 错误类型: %s, 错误信息: %s", 
                      sku.getId(), e.getClass().getSimpleName(), e.getMessage());
                  errorDetails.append(errorMsg).append("; ");
                  log.keyword("canalInsertHandler").error("单个插入失败，SKU: {}", JSON.toJSONString(sku), e);
                  
                  // 打印完整的异常堆栈
                  log.keyword("canalInsertHandler").error("单个插入异常堆栈:", e);
              }
        }
        
        log.keyword("canalInsertHandler").info("降级处理完成，成功: {}, 失败: {}", successCount, failCount);
        
        if (failCount > 0) {
            String errorMessage = String.format("批量插入降级处理部分失败，成功: %d, 失败: %d. 详细错误: %s", 
                successCount, failCount, errorDetails.toString());
            throw new RuntimeException(errorMessage);
        }
    }
    
    /**
     * 数据验证和清洗
     */
    private void validateAndCleanData(List<SkuTest> skus) {
        for (SkuTest sku : skus) {
            // 确保必要字段不为null
            if (sku.getId() == null || sku.getId().trim().isEmpty()) {
                sku.setId("UNKNOWN_" + System.currentTimeMillis());
                log.keyword("canalInsertHandler").warn("SKU ID为空，自动生成: {}", sku.getId());
            }
            
            if (sku.getSpuName() == null || sku.getSpuName().trim().isEmpty()) {
                sku.setSpuName("未知商品");
                log.keyword("canalInsertHandler").warn("SKU名称为空，设置默认值: {}", sku.getId());
            }
            
            if (sku.getSpuGrade() == null || sku.getSpuGrade().trim().isEmpty()) {
                sku.setSpuGrade("未知等级");
                log.keyword("canalInsertHandler").warn("SKU等级为空，设置默认值: {}", sku.getId());
            }
            
            if (sku.getSpuStandards() == null || sku.getSpuStandards().trim().isEmpty()) {
                sku.setSpuStandards("标准规格");
                log.keyword("canalInsertHandler").warn("SKU规格为空，设置默认值: {}", sku.getId());
            }
            
            if (sku.getIsDeleted() == null) {
                sku.setIsDeleted(false);
            }
            
            if (sku.getCreateTime() == null) {
                sku.setCreateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            }
            
            if (sku.getUpdateTime() == null) {
                sku.setUpdateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            }
        }
    }
    
    /**
     * 安全获取字符串值
     */
    private String safeGetString(Map<String, Object> data, String key, String defaultValue) {
        try {
            Object value = data.get(key);
            if (value == null) {
                return defaultValue;
            }
            String strValue = value.toString().trim();
            return StringUtils.hasText(strValue) ? strValue : defaultValue;
        } catch (Exception e) {
            log.keyword("canalInsertHandler").warn("获取字段 {} 异常，使用默认值: {}", key, defaultValue, e);
            return defaultValue;
        }
    }
    
    /**
     * 安全获取布尔值
     */
    private Boolean safeGetBoolean(Map<String, Object> data, String key, Boolean defaultValue) {
        try {
            Object value = data.get(key);
            if (value == null) {
                return defaultValue;
            }
            
            if (value instanceof Boolean) {
                return (Boolean) value;
            }
            
            if (value instanceof Number) {
                return ((Number) value).intValue() != 0;
            }
            
            String strValue = value.toString().toLowerCase();
            return "true".equals(strValue) || "1".equals(strValue) || "yes".equals(strValue);
            
        } catch (Exception e) {
            log.keyword("canalInsertHandler").warn("获取布尔字段 {} 异常，使用默认值: {}", key, defaultValue, e);
            return defaultValue;
        }
    }
} 