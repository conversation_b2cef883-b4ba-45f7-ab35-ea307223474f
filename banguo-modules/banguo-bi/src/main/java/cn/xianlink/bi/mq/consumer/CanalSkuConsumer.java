package cn.xianlink.bi.mq.consumer;

import cn.xianlink.bi.domain.es.SkuIndex;
import cn.xianlink.bi.domain.vo.CanalMessageVo;
import cn.xianlink.bi.enums.CanalOperationTypeEnum;
import cn.xianlink.bi.esmapper.SkuIndexMapper;
import cn.xianlink.bi.service.ISkuEsService;
import cn.xianlink.common.redis.utils.RedisUtils;
import com.alibaba.fastjson.JSON;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.StringUtils;
import org.dromara.easyes.core.conditions.update.LambdaEsUpdateWrapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
import java.util.function.Consumer;

@CustomLog
@RequiredArgsConstructor
@Component
@RefreshScope
public class CanalSkuConsumer implements Consumer<CanalMessageVo> {

    private final static String DB_INSTANCE = "banguo_product";

    private final static String SKU_TABLE = "sku";

    private final static String KEYWORD = "canalSkuConsumer";

    private final static String CANNAL_SKU_DELETE_KEY = "cannal:sku:del:";

    private final SkuIndexMapper skuIndexMapper;

    private final ISkuEsService skuEsService;

    @Value("${cannal.sku.consumeSwitch:false}")
    private boolean skuConsumeSwitch;

    @Value("${cannal.sku.consumeCacheExpireTime:24}")
    private long consumeCacheExpireTime;

    private static final DateTimeFormatter STANDARD_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").withZone(ZoneId.of("Asia/Shanghai"));

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd").withZone(ZoneId.of("Asia/Shanghai"));

    @Override
    public void accept(CanalMessageVo canalMessageVo) {
        try {
            log.keyword(KEYWORD).info("接收到Canal消息: {}", JSON.toJSONString(canalMessageVo));

            // 参数校验
            if (null == canalMessageVo || StringUtils.isEmpty(canalMessageVo.getType())) {
                log.keyword(KEYWORD).warn("Canal消息为空或操作类型为空");
                return;
            }

            // 检查是否为sku表的数据变更
            if (!DB_INSTANCE.equalsIgnoreCase(canalMessageVo.getDatabase()) && !SKU_TABLE.equalsIgnoreCase(canalMessageVo.getTable())) {
                log.keyword(KEYWORD).debug("忽略非sku表的数据变更");
                return;
            }
            if (null == CanalOperationTypeEnum.getByCode(canalMessageVo.getType().toUpperCase())) {
                log.keyword(KEYWORD).info("暂不处理该操作：{}", canalMessageVo.getType());
                return;
            }
            if (!skuConsumeSwitch) {
                log.keyword(KEYWORD).debug("sku消费开关未开启");
                return;
            }
            int ret = 0;
            switch (CanalOperationTypeEnum.getByCode(canalMessageVo.getType().toUpperCase())) {
                case INSERT:
                    ret = handleInsert(canalMessageVo);
                    break;
                case UPDATE:
                    ret = handleUpdate(canalMessageVo);
                    break;
                case DELETE:
                    ret = handleDelete(canalMessageVo);
                    break;
                default:
                    log.keyword(KEYWORD).warn("未知的操作类型: {}", canalMessageVo.getType());
                    break;
            }
            log.keyword(KEYWORD).info("Canal消息处理完成，操作类型: {},操作结果：{}", canalMessageVo.getType(), ret > 0 ? "成功" : "失败");
        } catch (Exception e) {
            log.keyword(KEYWORD).error("Canal消息处理异常: {}", ExceptionUtils.getStackTrace(e));
            // 触发重试
            throw new RuntimeException(e);
        }
    }

    /**
     * 新增
     *
     * @param message
     * @return
     */
    private int handleInsert(CanalMessageVo message) {
        if (CollectionUtils.isEmpty(message.getData())) {
            log.keyword(KEYWORD).warn("INSERT操作数据为空");
            return 0;
        }
        Integer ret = 0;
        SkuIndex sku = null;
        for (Map<String, Object> data : message.getData()) {
            try {
                // 将Map转换为Sku对象
                sku = convertToSku(data);
                if (null == sku || sku.getId() < 0) {
                    continue;
                }
                // 查询redis，判断是否存在删除操作
                Object cacheObject = RedisUtils.getCacheObject(CANNAL_SKU_DELETE_KEY + sku.getSkuId());
                if (null != cacheObject) {
                    Long upTime = (Long) cacheObject;
                    if (null != sku.getUpdateTime() && upTime >= sku.getUpdateTime().getTime()) {
                        log.keyword(KEYWORD).info("skuId: {}, 存在删除操作，忽略新增操作,delTime:{},insertTime:{}", sku.getSkuId(), upTime, sku.getUpdateTime().getTime());
                        continue;
                    }
                }
                ret = skuIndexMapper.insert(sku);
                if (ret <= 0) {
                    SkuIndex skuIndex = skuIndexMapper.selectById(sku.getSkuId());
                    if (null != skuIndex) {
                        log.keyword(KEYWORD).info("新增SKU成功, skuId: {}", sku.getSkuId());
                    } else {
                        log.keyword(KEYWORD).warn("新增SKU失败, skuId: {}", sku.getSkuId());
                        throw new RuntimeException("cannal处理新增SKU异常,skuId: " + sku.getSkuId());
                    }
                } else {
                    log.keyword(KEYWORD).info("新增SKU成功,skuId: {}", sku.getSkuId());
                }
            } catch (Exception e) {
                log.keyword(KEYWORD).error("处理新增SKU异常, skuId: {}, 异常: {}", sku.getSkuId(), e);
                throw new RuntimeException("cannal处理新增SKU异常");
            }
        }
        return ret;
    }


    /**
     * 更新
     *
     * @param message
     * @return
     */
    private int handleUpdate(CanalMessageVo message) {
        if (CollectionUtils.isEmpty(message.getData())) {
            log.keyword(KEYWORD).warn("UPDATE操作数据或旧数据为空");
            return 0;
        }
        List<SkuIndex> skuIndexList = new LinkedList<>();
        int ret = 0;
        for (int i = 0; i < message.getData().size(); i++) {
            try {
                Map<String, Object> newData = message.getData().get(i);
                // 将Map转换为Sku对象
                SkuIndex sku = convertToSku(newData);
                if (null == sku || null == sku.getId() || sku.getId() < 0) {
                    continue;
                }
                SkuIndex skuIndex = skuIndexMapper.selectById(sku.getId());
                if (null == skuIndex) {
                    ret = skuIndexMapper.insert(sku);
                    log.keyword(KEYWORD).info("处理skuId:{} 更新操作，es查无数据，进行新增操作，结果：{}", sku.getId(), ret);
                } else {
                    if (null == sku.getUpdateTime() || null == skuIndex.getUpdateTime()) {
                        if (null == sku.getUpdateTime()) {
                            sku.setUpdateTime(new Date());
                        }
                        Integer update = skuIndexMapper.updateById(sku);
                        log.keyword(KEYWORD).warn("skuId：{} updateTime为空，直接更新，结果：{}", sku.getId(), update);
                        if (update <= 0) {
                            log.keyword(KEYWORD).error("skuId：{} updateTime为空，更新失败", sku.getId());
                            throw new RuntimeException("cannal处理SKU更新异常,skuId：" + sku.getId());
                        } else {
                            log.keyword(KEYWORD).info("skuId：{} updateTime为空，更新成功", sku.getId());
                        }
                        return update;
                    }
                    // 判断更新时间是否可以更新
                    if (sku.getUpdateTime().getTime() >= skuIndex.getUpdateTime().getTime()) {
                        ret = skuIndexMapper.updateById(sku);
                        if (ret <= 0) {
                            log.keyword(KEYWORD).error("更新SKU失败,skuId: {}", sku.getId());
                            throw new RuntimeException("cannal处理更新SKU异常,skuId：" + sku.getId());
                        } else {
                            log.keyword(KEYWORD).info("更新SKU成功,skuId: {}", sku.getId());
                        }
                    } else {
                        log.keyword(KEYWORD).info("当前消息sku 更新时间落后于es最新记录[mqSku:{},skuIndex:{}]，不执行更新操作", formatDateTime(sku.getUpdateTime()), formatDateTime(skuIndex.getUpdateTime()));
                    }
                }
            } catch (Exception e) {
                log.keyword(KEYWORD).error("处理更新SKU异常, 新数据: {}, 旧数据: {}, 异常: {}", JSON.toJSONString(message.getData().get(i)), JSON.toJSONString(message.getOld().get(i)), ExceptionUtils.getStackTrace(e));
                throw new RuntimeException("处理更新SKU异常");
            }
        }
        return ret;
    }

    /**
     * 删除
     *
     * @param message
     * @return
     */
    private int handleDelete(CanalMessageVo message) {
        if (CollectionUtils.isEmpty(message.getData())) {
            log.keyword(KEYWORD).warn("DELETE操作数据为空");
            return 0;
        }
        Integer ret = 0;
        for (Map<String, Object> data : message.getData()) {
            try {
                SkuIndex sku = convertToSku(data);
                if (null == sku || sku.getId() < 0) {
                    continue;
                }
                SkuIndex skuIndex = skuIndexMapper.selectById(sku.getId());
                if (null == skuIndex) {
                    log.keyword(KEYWORD).warn("当前es查无sku记录，先记录缓存，skuId:{}", sku.getId());
                    RedisUtils.setCacheObject(CANNAL_SKU_DELETE_KEY + sku.getId(), sku.getUpdateTime().getTime(), Duration.ofHours(consumeCacheExpireTime));
                    continue;
                }
                // 判断del操作的更新时间是否落后当前es记录的更新时间，若是则忽略，反之执行删除操作
                if (sku.getUpdateTime().getTime() < skuIndex.getUpdateTime().getTime()) {
                    log.keyword(KEYWORD).warn("skuId：{} del操作的更新时间:{}落后当前es记录的更新时间:{},忽略操作", sku.getId(), formatDateTime(sku.getUpdateTime()), formatDateTime(skuIndex.getUpdateTime()));
                    continue;
                }
                ret = skuIndexMapper.deleteById(sku.getId());
                if (ret <= 0) {
                    log.keyword(KEYWORD).warn("当前删除操作无受影响记录，先记录缓存，skuId:{}", sku.getId());
                    RedisUtils.setCacheObject(CANNAL_SKU_DELETE_KEY + sku.getId(), sku.getUpdateTime().getTime(), Duration.ofHours(consumeCacheExpireTime));
                } else {
                    log.keyword(KEYWORD).info("处理删除SKU成功,条数：{}， skuId: {}", ret, sku.getId());
                }
            } catch (Exception e) {
                log.keyword(KEYWORD).error("处理删除SKU异常, 数据: {}, 异常: {}", JSON.toJSONString(data), ExceptionUtils.getStackTrace(e));
                throw new RuntimeException("cannal处理删除SKU异常");
            }
        }
        return ret;
    }

    /**
     * 将Map转换为Sku对象
     *
     * @param data
     * @return
     */
    private SkuIndex convertToSku(Map<String, Object> data) {
        // 这里实现从Map到Sku对象的转换逻辑
        if (null == data) {
            return null;
        }
        Object idObj = data.get("id");
        if (null == idObj || StringUtils.isEmpty(idObj.toString())) {
            return null;
        }
        SkuIndex sku = new SkuIndex();
        // 提取公共变量，避免重复调用 get()
        sku.setId(Long.parseLong(idObj.toString()));
        sku.setSkuId(sku.getId());
        // 基础信息
        sku.setSkuCode(getString(data, "sku_code"));
        sku.setSupplierSkuId(getLong(data, "supplier_sku_id"));
        sku.setCode(getString(data, "code"));
        sku.setCategoryPathName(getString(data, "category_path_name"));
        // 价格相关属性
        sku.setPrice(getBigDecimal(data, "price"));
        sku.setMarketPrice(getBigDecimal(data, "market_price"));
        sku.setDiscountPrice(getBigDecimal(data, "discount_price"));
        sku.setUpPrice(getBigDecimal(data, "up_price"));
        sku.setSubsidyAmount(getBigDecimal(data, "subsidy_amount"));
        // 时间相关属性
        sku.setSaleDate(parseDate(data.get("sale_date")));
        sku.setUpTime(parseDateTime(data.get("up_time")));
        sku.setDownTime(parseDateTime(data.get("down_time")));
        sku.setCreateTime(parseDateTime(data.get("create_time")));
        sku.setUpdateTime(parseDateTime(data.get("update_time")));
        sku.setOutTime(parseDateTime(data.get("out_time")));
        // SPU 相关
        sku.setSpuId(getLong(data, "spu_id"));
        sku.setSpuCode(getString(data, "spu_code"));
        sku.setSpuName(getString(data, "spu_name"));
        sku.setSpuGrade(getString(data, "spu_grade"));
        sku.setSpuGradeDesc(getString(data, "spu_grade_desc"));
        sku.setSpuStandards(getString(data, "spu_standards"));
        sku.setSpuGrossWeight(getBigDecimal(data, "spu_gross_weight"));
        sku.setSpuNetWeight(getBigDecimal(data, "spu_net_weight"));
        sku.setSupplierSpuId(getLong(data, "supplier_spu_id"));
        sku.setSupplierSpuCode(getString(data, "supplier_spu_code"));
        // 供应商相关
        sku.setSupplierId(getLong(data, "supplier_id"));
        sku.setSupplierName(getString(data, "supplier_name"));
        sku.setSupplierCode(getString(data, "supplier_code"));
        sku.setSupplierDeptId(getLong(data, "supplier_dept_id"));
        sku.setOriginalSupplierId(getLong(data, "original_supplier_id"));
        sku.setOriginalSupplierSkuId(getLong(data, "original_supplier_sku_id"));
        // 仓库相关
        sku.setBasPortageTeamId(getLong(data, "bas_portage_team_id"));
        sku.setProvideRegionWhId(getLong(data, "provide_region_wh_id"));
        sku.setRegionWhId(getLong(data, "region_wh_id"));
        sku.setRegionWhCode(getString(data, "region_wh_code"));
        sku.setRegionWhName(getString(data, "region_wh_name"));
        // 分类相关
        sku.setCategoryId(getLong(data, "category_id"));
        sku.setCategoryName(getString(data, "category_name"));
        sku.setCategoryIdLevel2(getLong(data, "category_id_level2"));
        // 商品信息
        sku.setFullName(getString(data, "full_name"));
        sku.setBrand(getString(data, "brand"));
        sku.setProducer(getString(data, "producer"));
        sku.setShortProducer(getString(data, "short_producer"));
        sku.setPackageType(getString(data, "package_type"));
        sku.setPackageStandards(getString(data, "package_standards"));
        sku.setPackageWord(getString(data, "package_word"));
        sku.setSnapshot(getString(data, "snapshot"));
        sku.setSkuLabel(getString(data, "sku_label"));
        // 业务属性
        sku.setStatus(getInt(data, "status"));
        sku.setCooperation(getInt(data, "cooperation"));
        sku.setGiftBox(getInt(data, "gift_box"));
        sku.setGoodToEat(getInt(data, "good_to_eat"));
        sku.setLittle(getInt(data, "little"));
        sku.setShouguangVegetables(getInt(data, "shouguang_vegetables"));
        sku.setBargain(getInt(data, "bargain"));
        sku.setBargainRate(getBigDecimal(data, "bargain_rate"));
        sku.setDomestic(getInt(data, "domestic"));
        sku.setEntranceHide(getInt(data, "entrance_hide"));
        sku.setIsSaleHide(getInt(data, "is_sale_hide"));
        sku.setBatchType(getInt(data, "batch_type"));
        sku.setBusinessType(getInt(data, "business_type"));
        sku.setCopySold(getInt(data, "copy_sold"));
        sku.setIsCheck(getInt(data, "is_check"));
        sku.setSaleType(getInt(data, "sale_type"));
        sku.setAfterSaleType(getInt(data, "after_sale_type"));
        // 采购相关
        sku.setPlaceOrderMultiple(getInt(data, "place_order_multiple"));
        sku.setBuyMin(getInt(data, "buy_min"));
        sku.setBuyMax(getInt(data, "buy_max"));
        sku.setBuyerCode(getString(data, "buyer_code"));
        sku.setBuyerName(getString(data, "buyer_name"));
        sku.setAfterSaleDay(getInt(data, "after_sale_day"));
        sku.setDeductibleSituation(getString(data, "deductible_situation"));
        sku.setAfterSaleExplain(getString(data, "after_sale_explain"));
        sku.setHasDeliveryAudit(getInt(data, "has_delivery_audit"));
        sku.setPassDeliveryAudit(getInt(data, "pass_delivery_audit"));
        // 其他
        sku.setWaterfall(getString(data, "waterfall"));
        sku.setPredictionTomorrow(getString(data, "prediction_tomorrow"));
        sku.setPredictionFuture(getString(data, "prediction_future"));
        sku.setSweetMin(getBigDecimal(data, "sweet_min"));
        sku.setSweetMax(getBigDecimal(data, "sweet_max"));
        sku.setAreaCode(getString(data, "area_code"));
        sku.setCustomerStoreTypes(getString(data, "customer_store_types"));
        sku.setMarketForecastType(getString(data, "market_forecast_type"));
        sku.setRelationSkuId(getLong(data, "relation_sku_id"));
        // 系统字段
        sku.setTenantId(getString(data, "tenant_id"));
        sku.setDelFlag(getLong(data, "del_flag"));
        sku.setCreateCode(getString(data, "create_code"));
        sku.setCreateName(getString(data, "create_name"));
        sku.setUpdateCode(getString(data, "update_code"));
        sku.setUpdateName(getString(data, "update_name"));
        // 新增是否售罄
        sku.setIsOut(getInt(data, "is_out"));
        return sku;
    }


    // 工具方法封装
    private String getString(Map<String, Object> data, String key) {
        Object obj = data.get(key);
        return obj != null ? obj.toString() : null;
    }

    private Long getLong(Map<String, Object> data, String key) {
        String str = getString(data, key);
        if (str == null) {
            return null;
        }
        try {
            return Long.parseLong(str);
        } catch (NumberFormatException e) {
            return null;
        }
    }

    private Integer getInt(Map<String, Object> data, String key) {
        String str = getString(data, key);
        if (str == null) {
            return null;
        }
        try {
            return Integer.parseInt(str);
        } catch (NumberFormatException e) {
            return null;
        }
    }

    private BigDecimal getBigDecimal(Map<String, Object> data, String key) {
        String str = getString(data, key);
        if (str == null) {
            return null;
        }
        try {
            return new BigDecimal(str);
        } catch (NumberFormatException e) {
            return null;
        }
    }


    private String formatDateTime(Date date) {
        if (date == null) {
            return null;
        }
        try {
            String format = STANDARD_FORMATTER.format(date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
            return format;
        } catch (Exception e) {
            return null;
        }
    }

    private Date parseDate(Object dateObj) {
        if (dateObj == null) {
            return null;
        }
        LocalDate date = LocalDate.parse(dateObj.toString(), DATE_FORMATTER);
        return Date.from(date.atStartOfDay(ZoneId.of("Asia/Shanghai")).toInstant());
    }

    private Date parseDateTime(Object dateTimeObj) {
        if (dateTimeObj == null) {
            return null;
        }
        LocalDateTime dateTime = LocalDateTime.parse(dateTimeObj.toString(), STANDARD_FORMATTER);
        return Date.from(dateTime.atZone(ZoneId.of("Asia/Shanghai")).toInstant());
    }
}