package cn.xianlink.bi.domain.bo;

import cn.xianlink.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;

/**
 * 活动匹配出参
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class BiActivitySkuBo extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 供应商商品id
     */
    private Long supplierSkuId;

    /**
     * 平台商品ID
     */
    private Long spuId;

    /**
     * 二级商品分类
     */
    private Long categoryIdLevel2;
    /**
     * 一级分类
     */
    private Long categoryIdLevel1;

    /**
     * 商品分类id
     */
    private Long categoryId;

    /**
     * 供应商skuId
     */
    private Long skuId;

    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * 销量
     */
    private Integer copySold;
}
