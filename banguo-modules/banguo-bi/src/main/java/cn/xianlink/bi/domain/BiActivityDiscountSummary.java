package cn.xianlink.bi.domain;

import cn.xianlink.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;

/**
 * 营销活动检索宽表实体类
 * <AUTHOR>
 */
@Data
@TableName("banguo_marketing.mk_activity_discount_summary")
public class BiActivityDiscountSummary extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("活动ID")
    private Long activityId;

    @ApiModelProperty("活动类型：10：减运费，20：减代采费，30：条件折扣，40：满件减钱，50：满钱减钱，60：满件则扣，70：满钱折扣，80：特价活动，90：限时打折")
    private Integer activityType;

    @ApiModelProperty("活动名称")
    private String name;

    @ApiModelProperty("归属类型(1：供应商，2：城市仓，3：总仓，4：平台)")
    private Integer ownershipType;

    @ApiModelProperty("所属id (供应商id、总仓id、城市仓id、平台id)")
    private String ownerId;

    @ApiModelProperty("适用范围 1：全部，2：客户，3：供应商，4：城市仓，5：总仓，6：物流线，支持多选")
    private String scopeType;

    @ApiModelProperty("适用范围具体值，该字段存储具体的业务id，如总仓、城市仓、供应商，客户、物流线 ，逗号分隔")
    private String suitThrongValue;

    @ApiModelProperty("商品值(根据scope_type不同存储不同值)，* 表示全部参与 ，逗号分隔")
    private String scopeValue;

    @ApiModelProperty("动作类型(计算方式) FIXED 一口价/REDUCE 直降/DISCOUNT 折扣/GIFT 赠品/POINT 增积分/COUPON 赠券")
    private String actionType;

    @ApiModelProperty("标识指定or剔除，0：剔除；1：指定")
    private Integer exclude;

    @ApiModelProperty("门槛金额;门槛金额:折赠金额")
    private BigDecimal limitAmount;

    @ApiModelProperty("商品促销价格，一般用于单品一口价")
    private BigDecimal promoPrice;

    @ApiModelProperty("折扣率（如0.95表示95折，1.00表示不打折），单品折扣、类别折扣、会员折扣、指定日折扣等")
    private BigDecimal discountRate;

    @ApiModelProperty("优惠上限（如10.00元，超过部分不优惠）")
    private BigDecimal maxDiscountAmount;

    @ApiModelProperty("销售目标件数（限时打折最大可售数量）")
    private Long targetQuantity;

    @ApiModelProperty("活动状态(依赖审核状态) 1：待生效，2：进行中，3：暂停，4：结束")
    private Integer status;

    @ApiModelProperty("开始日期")
    private LocalDate startDate;

    @ApiModelProperty("结束日期")
    private LocalDate endDate;

    @ApiModelProperty("生效时段开始")
    private LocalTime effectiveStartTime;

    @ApiModelProperty("生效时段结束")
    private LocalTime effectiveEndTime;

    @ApiModelProperty("时间生效类型 1：永久，2：指定时间范围，3：每周周几生效，4：每月几号生效")
    private Integer effectType;

    @ApiModelProperty("时间值")
    private String effectValue;

    @ApiModelProperty("参与渠道 1：线上，2：线下，3：外部平台 支持多选，逗号隔开")
    private String channel;

    @ApiModelProperty("预留字段，用于信息冗余")
    private String extValue;

    @ApiModelProperty("版本号")
    private Integer version;

    @ApiModelProperty("租户编号")
    private String tenantId;

    @ApiModelProperty("删除标志（0代表存在）")
    private Long delFlag;

}
