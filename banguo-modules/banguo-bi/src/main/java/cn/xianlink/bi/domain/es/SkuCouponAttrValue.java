package cn.xianlink.bi.domain.es;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.rely.FieldType;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 搜索商品的信息
 *
 * <AUTHOR>
 * @date 2025/7/23
 */
@Data
@EqualsAndHashCode
public class SkuCouponAttrValue implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


    /**
     * 券模板id
     */
    @IndexField(fieldType = FieldType.LONG)
    private Long couponId;

    /**
     * 券模板name
     */
    @IndexField(fieldType = FieldType.KEYWORD)
    private String couponName;

    /**
     * 券模板类型
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer couponType;

    @IndexField(fieldType = FieldType.INTEGER)
    private Integer ownershipType;


    @IndexField(fieldType = FieldType.LONG)
    private Long ownershipId;

    /**
     * 时间生效类型
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer effectType;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @IndexField(fieldType = FieldType.KEYWORD)
    private List<Integer> effectDays;

    /**
     * 门槛
     */
    @IndexField(fieldType = FieldType.DOUBLE)
    private BigDecimal threshold;

    /**
     * 优惠金额
     */
    @IndexField(fieldType = FieldType.DOUBLE)
    private BigDecimal discount;

    /**
     * 限领数量
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer limitCount;

}