package cn.xianlink.bi.dubbo;

import cn.xianlink.bi.api.RemoteBiOrderService;
import cn.xianlink.bi.api.domain.vo.RemoteOrderLossRateVo;
import cn.xianlink.bi.service.IBiOrderService;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @description: 订单bi实现类
 * @author: weijian
 **/
@RequiredArgsConstructor
@Service
@DubboService
@CustomLog
public class RemoteBiOrderServiceImpl implements RemoteBiOrderService {

    private final IBiOrderService orderService;

    /**
     * 获取客户报损率 && 对应平均报损率
     */
    @Override
    public RemoteOrderLossRateVo getLossRate(List<Long> customerIds) {
        return orderService.getLossRate(customerIds);
    }
}
