package cn.xianlink.bi.mq.handler.impl;

import cn.xianlink.bi.domain.es.SkuTest;
import cn.xianlink.bi.domain.vo.CanalMessageVo;
import cn.xianlink.bi.esmapper.SkuTestMapper;
import cn.xianlink.bi.mq.handler.CanalOperationHandler;
import com.alibaba.fastjson.JSON;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * Canal DELETE操作处理器
 * 
 * <AUTHOR>
 */
@CustomLog
@RequiredArgsConstructor
@Component
public class CanalDeleteHandler implements CanalOperationHandler {

    private final SkuTestMapper skuTestMapper;

    @Override
    public void handle(CanalMessageVo canalMessageVo) {
        log.keyword("canalDeleteHandler").info("处理DELETE操作: {}", JSON.toJSONString(canalMessageVo));
        
        List<Map<String, Object>> dataList = canalMessageVo.getData();
        
        for (Map<String, Object> data : dataList) {
            deleteSku(data);
        }
    }

    @Override
    public String getSupportedOperationType() {
        return "DELETE";
    }

    /**
     * 删除单个SKU数据
     * 
     * @param data 被删除的数据
     */
    private void deleteSku(Map<String, Object> data) {
        String id = data.get("id").toString();
        
        // 构建删除条件
        LambdaEsQueryWrapper<SkuTest> deleteWrapper = new LambdaEsQueryWrapper<>();
        deleteWrapper.eq(SkuTest::getId, id);
        
        log.keyword("canalDeleteHandler").info("删除SKU [{}] 数据", id);
        skuTestMapper.delete(deleteWrapper);
    }
} 