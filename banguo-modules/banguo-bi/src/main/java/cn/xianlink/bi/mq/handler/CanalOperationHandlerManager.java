package cn.xianlink.bi.mq.handler;

import cn.xianlink.bi.domain.vo.CanalMessageVo;
import lombok.CustomLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Canal操作处理器管理器
 * 负责根据操作类型选择对应的处理策略
 * 
 * <AUTHOR>
 */
@CustomLog
@Component
public class CanalOperationHandlerManager {

    @Autowired
    private List<CanalOperationHandler> handlers;

    private final Map<String, CanalOperationHandler> handlerMap = new HashMap<>();

    /**
     * 初始化处理器映射
     */
    @PostConstruct
    public void initHandlers() {
        for (CanalOperationHandler handler : handlers) {
            String operationType = handler.getSupportedOperationType();
            handlerMap.put(operationType, handler);
            log.keyword("canalHandlerManager").info("注册Canal操作处理器: {} -> {}", 
                operationType, handler.getClass().getSimpleName());
        }
    }

    /**
     * 处理Canal消息
     * 
     * @param canalMessageVo Canal消息对象
     */
    public void handleMessage(CanalMessageVo canalMessageVo) {
        String operationType = canalMessageVo.getType();
        
        CanalOperationHandler handler = handlerMap.get(operationType);
        if (handler == null) {
            log.keyword("canalHandlerManager").warn("未找到操作类型 [{}] 对应的处理器", operationType);
            return;
        }
        
        log.keyword("canalHandlerManager").info("使用处理器 {} 处理操作类型 {}", 
            handler.getClass().getSimpleName(), operationType);
        
        handler.handle(canalMessageVo);
    }

    /**
     * 获取支持的操作类型
     * 
     * @return 支持的操作类型集合
     */
    public Map<String, CanalOperationHandler> getSupportedOperations() {
        return new HashMap<>(handlerMap);
    }
} 