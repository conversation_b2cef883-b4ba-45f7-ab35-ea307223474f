package cn.xianlink.bi.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.xianlink.basic.api.RemoteCityWhService;
import cn.xianlink.basic.api.RemoteRegionWhService;
import cn.xianlink.basic.api.RemoteSupplierService;
import cn.xianlink.basic.api.domain.vo.RemoteCityWhVo;
import cn.xianlink.basic.api.domain.vo.RemoteRegionWhVo;
import cn.xianlink.basic.api.domain.vo.RemoteSupplierVo;
import cn.xianlink.bi.domain.BiActivityDiscountSummary;
import cn.xianlink.bi.domain.BiCategory;
import cn.xianlink.bi.domain.bo.BiActivitySkuBo;
import cn.xianlink.bi.domain.bo.BiActivitySkuDiscountBo;
import cn.xianlink.bi.domain.bo.BiCustomerSearchBO;
import cn.xianlink.bi.domain.bo.BiDistributionActivityQueryBo;
import cn.xianlink.bi.domain.vo.BiCustomerSearchVO;
import cn.xianlink.bi.domain.vo.BiDistributionActivitySkuVo;
import cn.xianlink.bi.mapper.BiBasCustomerMapper;
import cn.xianlink.bi.mapper.BiDistributionActivityMapper;
import cn.xianlink.bi.mapper.marketing.BiActivityDiscountSummaryMapper;
import cn.xianlink.bi.mapper.product.BICategoryMapper;
import cn.xianlink.bi.service.IBiCustomerService;
import cn.xianlink.bi.service.IBiDistributionActivityService;
import cn.xianlink.common.api.util.SaleDateUtil;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.product.api.RemoteCategoryService;
import cn.xianlink.product.api.RemoteSkuService;
import cn.xianlink.product.api.domain.vo.RemoteCategoryVO;
import cn.xianlink.product.api.domain.vo.RemoteSkuVo;
import cn.xianlink.system.api.RemoteUserService;
import cn.xianlink.system.api.domain.bo.RemoteUserBo;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @program: banguo-parent-ms
 * @description:
 * @author: chenbin
 * @create: 2025-02-28 15:15
 **/
@RequiredArgsConstructor
@Service
public class BiDistributionActivityServiceImpl implements IBiDistributionActivityService {

    private final BiDistributionActivityMapper distributionActivityMapper;

    private final BiActivityDiscountSummaryMapper summaryMapper;

    private final BICategoryMapper categoryMapper;

    @DubboReference
    private final RemoteRegionWhService regionWhService;

    @Override
    public TableDataInfo<BiDistributionActivitySkuVo> querySkuList(BiDistributionActivityQueryBo bo) {
        Page<BiDistributionActivitySkuVo> page = distributionActivityMapper.getPage(bo, bo.build());
        List<BiDistributionActivitySkuVo> records = page.getRecords();
        if (CollUtil.isEmpty(records)){
            return TableDataInfo.build();
        }
        List<Long> categoryIds = records.stream().map(BiDistributionActivitySkuVo::getCategoryIdLevel2).distinct().toList();
        List<BiCategory> biCategories = categoryMapper.selectBatchIds(categoryIds);
        Map<Long, Long> categoryMap = biCategories.stream().collect(Collectors.toMap(BiCategory::getId, BiCategory::getParentId));
        BiActivitySkuDiscountBo discountBo = new BiActivitySkuDiscountBo();
        List<BiActivitySkuBo> supplierSkuList = new ArrayList<>();
        for (BiDistributionActivitySkuVo vo : records){
            BiActivitySkuBo skuBo = new BiActivitySkuBo();
            skuBo.setSupplierSkuId(vo.getId()).setSpuId(vo.getSpuId()).setCategoryId(vo.getCategoryId())
                    .setCategoryIdLevel2(vo.getCategoryIdLevel2()).setSkuId(vo.getSkuId())
                    .setSupplierId(vo.getSupplierId()).setCopySold(vo.getSold());
            if (categoryMap.containsKey(vo.getCategoryIdLevel2())){
                skuBo.setCategoryIdLevel1(categoryMap.get(vo.getCategoryIdLevel2()));
            }
            supplierSkuList.add(skuBo);
        }
        discountBo.setSupplierSkuList(supplierSkuList);
        discountBo.setRegionId(bo.getRegionWhIdList().get(0));
        RemoteRegionWhVo remoteRegionWhVo = regionWhService.queryById(bo.getRegionWhIdList().get(0));
        LocalDate saleDate = SaleDateUtil.getSaleDate(remoteRegionWhVo.getSalesTimeEnd());
        discountBo.setSaleDate(saleDate);
        List<BiActivityDiscountSummary> skuDiscount = getSkuDiscountIdList(discountBo);
        Map<Long, BiActivityDiscountSummary> discountVoMap = skuDiscount.stream().collect(Collectors.toMap(BiActivityDiscountSummary::getId, Function.identity()));
        for (BiDistributionActivitySkuVo record : records) {
            // 处理单价 - 列表要显示
            BigDecimal netWeightPrice = record.getPrice().divide(record.getSpuNetWeight(), 2, RoundingMode.HALF_UP);
            record.setPrice(record.getPrice().subtract(record.getSubsidyAmount()));
            if (!record.getStatus().equals(4)){
                record.setStock(0);
            }
            if (discountVoMap.containsKey(record.getId()) && record.getStock() >0){
                BiActivityDiscountSummary discountVo = discountVoMap.get(record.getId());
                int count = discountVo.getTargetQuantity().intValue() - record.getSold();
                record.setHasLimitDiscount(1);
                record.setDiscountCount(count > record.getStock() ? record.getStock() : count);
                BigDecimal scale = record.getPrice().multiply(discountVo.getDiscountRate()).setScale(2, RoundingMode.HALF_UP);
                record.setPriceFree(record.getPrice().subtract(scale).compareTo(discountVo.getMaxDiscountAmount()) > 0
                        ? record.getPrice().subtract(discountVo.getMaxDiscountAmount()) : scale);
                netWeightPrice = record.getPriceFree().divide(record.getSpuNetWeight(), 2, RoundingMode.HALF_UP);
            }
            if (netWeightPrice.compareTo(BigDecimal.ZERO) == 0) {
                netWeightPrice = new BigDecimal("0.01");
            }
            record.setNetWeightPrice(netWeightPrice);
        }
        return TableDataInfo.build(page);
    }

    private List<BiActivityDiscountSummary> getSkuDiscountIdList(BiActivitySkuDiscountBo bo){
        List<BiActivityDiscountSummary> vos = new ArrayList<>();
        if (ObjectUtils.isNull(bo) || CollectionUtils.isEmpty(bo.getSupplierSkuList())){
            return vos;
        }
        //组装商品信息
        List<String> skuInfo = new ArrayList<>();
        Map<Long, List<Integer>> skuActivityMap = new HashMap<>();
        Map<Long, List<String>> skuInfoMap = new HashMap<>();
        skuInfo.add("*");
        for (BiActivitySkuBo skuBo : bo.getSupplierSkuList()){
            List<String> s = new ArrayList<>();
            s.add("SS:"+skuBo.getSupplierSkuId());
            s.add("K:"+skuBo.getSkuId());
            s.add("P:"+skuBo.getSpuId());
            s.add("C:"+skuBo.getCategoryId());
            s.add("C:"+skuBo.getCategoryIdLevel1());
            s.add("C:"+skuBo.getCategoryIdLevel2());
            skuInfo.addAll(s);
            skuActivityMap.put(skuBo.getSkuId(),new ArrayList<>());
            skuInfoMap.put(skuBo.getSupplierSkuId(),s);
        }
        bo.setSkuInfo(skuInfo);
        bo.setTime(LocalTime.parse(LocalTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"))));
        List<BiActivityDiscountSummary> list = summaryMapper.getSkuDiscount(bo);
        if (CollectionUtils.isNotEmpty(list)){
            for (BiActivitySkuBo skuBo :bo.getSupplierSkuList()){
                BiActivityDiscountSummary vo = new BiActivityDiscountSummary();
                vo.setId(skuBo.getSupplierSkuId());
                for (BiActivityDiscountSummary summary : list){
                    List<Integer> typeList = skuActivityMap.get(skuBo.getSkuId());
                    if (typeList.contains(summary.getActivityType())){
                        //补贴取最小折扣
                        if (summary.getActivityType().equals(90)
                                && summary.getDiscountRate().compareTo(vo.getDiscountRate()) > 0) {
                            continue;
                        }
                    }
                    //校验
                    if (check(bo, skuInfoMap, skuBo, summary)){ continue;}
                    switch (summary.getActivityType()){
                        //补贴
                        case 90 -> {
                            vo.setDiscountRate(summary.getDiscountRate());
                            vo.setTargetQuantity(summary.getTargetQuantity());
                            vo.setMaxDiscountAmount(summary.getMaxDiscountAmount());
                        }
                        case 10,20,30,40,50,60,70,80 -> {
                        }
                        default ->{}
                    }
                    skuActivityMap.get(skuBo.getSkuId()).add(summary.getActivityType());
                }
                if (vo.getDiscountRate()!=null) {
                    vos.add(vo);
                }
            }
        }
        return vos;
    }
    private static boolean check(BiActivitySkuDiscountBo bo, Map<Long, List<String>> skuInfoMap, BiActivitySkuBo skuBo,
                                 BiActivityDiscountSummary summary) {
        //过滤时间 固定周几
        if (summary.getEffectType().equals(3) && summary.getEffectValue() != null){
            int week = bo.getSaleDate().getDayOfWeek().getValue();
            if (!Arrays.stream(summary.getEffectValue().split(",")).toList().contains(String.valueOf(week))){
                return true;
            }
        }
        //过滤时间 几号
        if (summary.getEffectType().equals(4) && summary.getEffectValue() != null){
            int day = bo.getSaleDate().getDayOfMonth();
            if (!Arrays.stream(summary.getEffectValue().split(",")).toList().contains(String.valueOf(day))){
                return true;
            }
        }
        //限时折扣有限购门槛
        if (skuBo != null && summary.getActivityType().equals(90) && summary.getTargetQuantity() != null && summary.getTargetQuantity()< skuBo.getCopySold()){
            return true;
        }
        //适用范围  目前只有物流线
        if (bo.getLogisticsId() != null && "6".equals(summary.getScopeType()) && org.apache.commons.lang3.StringUtils.isNotEmpty(summary.getSuitThrongValue())){
            List<Long> logisticsIds = Arrays.stream(summary.getSuitThrongValue().split(",")).map(Long::parseLong).toList();
            if (!logisticsIds.contains(bo.getLogisticsId())){
                return true;
            }
        }
        //过滤商品
        if (skuInfoMap != null && StringUtils.isNotEmpty(summary.getScopeValue()) && !"*".equals(summary.getScopeValue())){
            List<String> skuInfos = skuInfoMap.get(skuBo.getSupplierSkuId());
            if (ObjectUtils.isNotNull(skuInfos) && CollectionUtils.isNotEmpty(skuInfos)
                    &&((!skuInfos.contains(summary.getScopeValue()) && summary.getExclude().equals(1))
                    || (skuInfos.contains(summary.getScopeValue()) && summary.getExclude().equals(0)))){
                return true;
            }
        }
        return false;
    }
}
