package cn.xianlink.bi.controller.sup;


import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.bi.controller.TodoService;
import cn.xianlink.bi.domain.bo.TodoBo;
import cn.xianlink.bi.domain.vo.TodoGoodsVo;
import cn.xianlink.bi.domain.vo.TodoVo;
import cn.xianlink.common.api.enums.order.OrderDeliverSourceEnum;
import cn.xianlink.common.api.enums.order.ToDosEnum;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.enums.YNStatusEnum;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.system.api.model.LoginUser;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 供应商小程序-待办
 *
 * <AUTHOR>
 * @date 2025-01-05
 * @folder 供应商端(小程序)/待办
 */
@Validated
@RequiredArgsConstructor
@RestController()
@RequestMapping("/bi/sup/todo/")
public class SupTodoController extends BaseController {
    private transient final TodoService todoService;

    /**
     * 待办列表
     */
    @PostMapping("/list")
    public R<List<TodoVo>> list(@RequestBody TodoBo bo) {
        if (ObjectUtil.isNull(bo.getRegionWhId())) {
            return R.warn("总仓id不能为空");
        }
        LoginUser user = LoginHelper.getLoginUser();
        bo.setCacheUserId(user.getUserId());
        bo.setSource(OrderDeliverSourceEnum.SUPPLIER.getCode());
        bo.setSupplierId(user.getRelationId());
        if (user.getDeptId() != 0L) {
            bo.setSupplierDeptId(user.getDeptId());
        }
        List<TodoVo> list = new ArrayList<>();
        // 待送货
        list.add(new TodoVo(ToDosEnum.WAIT_DELIVERY.getCode(), todoService.waitDeliveryList(bo)));
        // 待送货 - 提前发车
        TodoBo bo3 = new TodoBo();
        BeanUtils.copyProperties(bo, bo3);
        bo3.setIsEarlyEnd(YNStatusEnum.ENABLE.getCode());
        list.add(new TodoVo(ToDosEnum.EARLY_WAIT_DELIVERY.getCode(), todoService.waitDeliveryList(bo3)));
        // 待质检
        list.add(new TodoVo(ToDosEnum.WAIT_INSPECT.getCode(), todoService.waitInspectGoodsList(bo)));
        // 待装车 - 市采
        list.add(new TodoVo(ToDosEnum.WAIT_ENTRUCK.getCode(), todoService.waitEntruckGoodsList(bo, 1)));
        // 报损待审核
        list.add(new TodoVo(ToDosEnum.AUDIT_REPORT_LOSS.getCode(), todoService.supReportLossAuditCount(bo)));
        // 少货待判责
        list.add(new TodoVo(ToDosEnum.LESS_GOODS_BLAME.getCode(), todoService.waitConfirmStockOutList(bo, 3)));
        // 待开发票
        list.add(new TodoVo(ToDosEnum.WAIT_CREATE_INVOICE.getCode(), todoService.waitCreateInvoiceCount(bo,"applied")));
        return R.ok(list.stream().filter(f -> f.getSkuCount() > 0).collect(Collectors.toList()));
    }


    /**
     * 待办明细
     */
    @PostMapping("/details")
    public R<List<TodoGoodsVo>> details(@RequestBody TodoBo bo) {
        if (ObjectUtil.isNull(bo.getRegionWhId())) {
            return R.warn("总仓id不能为空");
        }
        if (ObjectUtil.isNull(bo.getTodoCode())) {
            return R.warn("待办类型不能为空");
        }
        LoginUser user = LoginHelper.getLoginUser();
        bo.setCacheUserId(user.getUserId());
        bo.setSource(OrderDeliverSourceEnum.SUPPLIER.getCode());
        bo.setSupplierId(user.getRelationId());
        List<TodoGoodsVo> list = new ArrayList<>();
        // 待送货
        if (bo.getTodoCode().intValue() == ToDosEnum.WAIT_DELIVERY.getCode()) {
            list = todoService.waitDeliveryList(bo).getDataList();
        }
        // 待送货 - 提前发车
        if (bo.getTodoCode().intValue() == ToDosEnum.EARLY_WAIT_DELIVERY.getCode()) {
            TodoBo bo3 = new TodoBo();
            BeanUtils.copyProperties(bo, bo3);
            bo3.setIsEarlyEnd(YNStatusEnum.ENABLE.getCode());
            list = todoService.waitDeliveryList(bo3).getDataList();
        }
        // 待质检
        else if (bo.getTodoCode().intValue() == ToDosEnum.WAIT_INSPECT.getCode()) {
            list = todoService.waitInspectGoodsList(bo).getDataList();
        }
        // 待装车 - 市采
        else if (bo.getTodoCode().intValue() == ToDosEnum.WAIT_ENTRUCK.getCode()) {
            list = todoService.waitEntruckGoodsList(bo, 1).getDataList();
        } else if (bo.getTodoCode().intValue() == ToDosEnum.LESS_GOODS_BLAME.getCode()) {
            list = todoService.waitConfirmStockOutList(bo, 3).getDataList();
        }
        // 待开发票
        if(bo.getTodoCode().intValue() == ToDosEnum.WAIT_CREATE_INVOICE.getCode()){
            todoService.waitCreateInvoiceCount(bo,"applied");
        }
        return R.ok(list);
    }
}