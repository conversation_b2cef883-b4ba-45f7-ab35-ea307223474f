package cn.xianlink.bi.mapper.marketing;


import cn.xianlink.bi.domain.BiActivityDiscountSummary;
import cn.xianlink.bi.domain.bo.BiActivitySkuDiscountBo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 分销活动Mapper接口
 * <AUTHOR>
 */
public interface BiActivityDiscountSummaryMapper extends BaseMapper {

    List<BiActivityDiscountSummary> getSkuDiscount(@Param("bo") BiActivitySkuDiscountBo bo);
}