package cn.xianlink.bi.aop;

import cn.dev33.satoken.context.SaHolder;
import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.bi.service.IBiBasicBaseDataService;
import cn.xianlink.common.api.enums.trade.BaseTypeEnum;
import cn.xianlink.common.api.util.BaseDataIdColumn;
import cn.xianlink.common.api.vo.RemoteBaseDataVo;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.Collection;

@CustomLog
@Aspect
@RequiredArgsConstructor
@Component
public class BaseEntityAutoFillAspect {

    public final static String CACHE_BASE_DATA = "bi:cache:base_data#300s";
    private final static String THREAD_BASE_DATA = "bi_base_data:%s:%s";
    private final transient IBiBasicBaseDataService biBasicBaseDataService;

    @AfterReturning(value = "@annotation(cn.xianlink.common.api.util.BaseEntityAutoFill)", returning = "result")
    public void afterReturning(JoinPoint joinPoint, Object result) throws IllegalAccessException {
        if (result == null) {
            return;
        }
        Class<?> clazz = null;
        Collection<?> collection = null;
        if (result instanceof TableDataInfo) {
            collection = ((TableDataInfo<?>) result).getRows();
        } else if (result instanceof Collection) {
            collection = (Collection<?>) result;
        } else {
            clazz = result.getClass();
        }
        if (clazz == null) {
            if (collection == null || collection.isEmpty()) {
                return;
            }
            clazz = collection.iterator().next().getClass();
        }
        Field[] fields = clazz.getDeclaredFields();
        try {
            for (Field field : fields) {
                BaseDataIdColumn dataIdColumn = field.getAnnotation(BaseDataIdColumn.class);
                if (dataIdColumn == null) {
                    continue;
                }
                Field dynamic = getField(clazz, dataIdColumn.dynamic());
                Field code = getField(clazz, dataIdColumn.code());
                Field name = getField(clazz, dataIdColumn.name());
                Field alias = getField(clazz, dataIdColumn.alias());
                if (code == null && name == null && alias == null) {
                    continue;
                }
                field.setAccessible(true);
                if (collection == null) {
                    setValue(result, field, code, name, alias, dynamic, dataIdColumn.type());
                } else {
                    for (Object o : collection) {
                        setValue(o, field, code, name, alias, dynamic, dataIdColumn.type());
                    }
                }
            }
        } catch (NoSuchFieldException nsfe) {
            log.keyword("BaseEntityAutoFillAspect").error("", nsfe);
        }
    }

    private Field getField(Class<?> clazz, String fieldName) throws NoSuchFieldException {
        if (StringUtils.isBlank(fieldName)) {
            return null;
        }
        Field field = clazz.getDeclaredField(fieldName);
        field.setAccessible(true);
        return field;
    }

    private void setValue(Object obj, Field idFiled, Field code, Field name,
                          Field alias, Field dynamic, BaseTypeEnum typeEnum) throws IllegalAccessException {
        Long id = (Long) idFiled.get(obj);
        Integer type;
        if (dynamic != null) {
            type = (Integer) dynamic.get(obj);
        } else {
            type = typeEnum.getCode();
        }
        if (id == null || type == null) {
            return;
        }
        String cacheKey = String.format(THREAD_BASE_DATA, id, type);
        RemoteBaseDataVo vo = (RemoteBaseDataVo) SaHolder.getStorage().get(cacheKey);
        if (ObjectUtil.isNull(vo)) {
            vo = biBasicBaseDataService.queryBaseDataById(id, type);
            SaHolder.getStorage().set(cacheKey, vo);
        }
        if (!ObjectUtil.isNull(vo)) {
            if (code != null) {
                code.set(obj, vo.getCode());
            }
            if (name != null) {
                name.set(obj, vo.getName());
            }
            if (alias != null) {
                alias.set(obj, vo.getAlias());
            }
        }
    }
}
