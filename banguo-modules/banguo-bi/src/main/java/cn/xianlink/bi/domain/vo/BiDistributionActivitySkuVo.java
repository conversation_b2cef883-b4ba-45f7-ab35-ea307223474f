package cn.xianlink.bi.domain.vo;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.xianlink.bi.domain.BiDistributionActivitySku;
import cn.xianlink.common.dict.annotation.DictConvertFiled;
import cn.xianlink.common.dict.enums.DictConvertTypeEnum;
import cn.xianlink.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 分销活动商品业务对象 distribution_activity_sku
 * <AUTHOR>
 */
@Data
@AutoMapper(target = BiDistributionActivitySku.class)
public class BiDistributionActivitySkuVo extends BaseEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 活动开始时间
     */
    private Date activityStartTime;

    /**
     * 活动结束时间
     */
    private Date activityEndTime;

    /**
     * SKU ID
     */
    private Long skuId;

    /**
     * SPU 名称
     */
    private String spuName;

    /**
     * 供应商ID
     */
    private Long supplierId;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 分销件数金额
     */
    private BigDecimal distributionItemAmount;

    /**
     * 分类路径
     */
    private String categoryPathName;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 产地简称
     */
    private String shortProducer;

    /**
     * 规格
     */
    private String spuStandards;

    /**
     * 平台商品净重(斤)
     */
    private BigDecimal spuNetWeight;

    @DictConvertFiled(dictCode = "orderOrderBusinessType", filedName = "businessTypeName", type = DictConvertTypeEnum.ENUM)
    private Integer businessType;


    @ApiModelProperty("商品图片地址")
    private String imgUrl;

    @ApiModelProperty("净单价")
    private BigDecimal netWeightPrice;

    @ApiModelProperty("库存")
    private Integer stock;

    @ApiModelProperty("上架库存")
    private Integer upStock;

    @DictConvertFiled(dictCode = "productSupplierSkuBatchType", filedName = "batchTypeName", type = DictConvertTypeEnum.ENUM)
    private Integer batchType;

    /**
     * 是否战略合作品，0为否，1为是
     */
    private Integer cooperation;

    @ApiModelProperty("寿光蔬菜，0为否，1为是")
    private Integer shouguangVegetables;

    private Integer status;

    /**
     * 补贴金额
     */
    private BigDecimal subsidyAmount;
    /**
     * 销售类型：1为般果代采，2为商家自营
     */
    private Integer saleType;

    private Long spuId;

    private Long categoryId;

    private Long categoryIdLevel1;

    private Long categoryIdLevel2;

    private Integer sold;

    /**
     * 限时折扣标识
     */
    private Integer hasLimitDiscount;
    /**
     * 活动数量  剩余数据
     */
    private Integer discountCount;

    @ApiModelProperty("售价，加入购物车时的商品价格")
    private BigDecimal priceFree;

    public boolean isValidTime() {
        return isValidTime(activityStartTime, activityEndTime);
    }

    public static boolean isValidTime(Date activityStartTime, Date activityEndTime) {
        LocalDateTime startTime =  LocalDateTimeUtil.beginOfDay(LocalDateTimeUtil.of(activityStartTime));
        LocalDateTime endTime = LocalDateTimeUtil.endOfDay(LocalDateTimeUtil.of(activityEndTime));
        LocalDateTime now = LocalDateTime.now();
        return startTime.compareTo(now) <= 0 && now.compareTo(endTime) <= 0;
    }
}