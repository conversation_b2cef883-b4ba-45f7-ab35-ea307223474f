package cn.xianlink.bi.domain;


import cn.xianlink.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;


/**
 * 商品品类表
 *
 * <AUTHOR>
 * @date 2024-03-21 10:05:56
 */
@Data
@TableName("banguo_product.category")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class BiCategory extends TenantEntity {

    @Id
    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("品类唯一编码")
    private String code;

    @ApiModelProperty("品类名称")
    private String name;

    @ApiModelProperty("运营主体id")
    private Long orgId;

    @ApiModelProperty("父ID")
    private Long parentId;

//    @ApiModelProperty("售后天数")
//    private Integer afterTime;
    @ApiModelProperty("售后规则：0无售后、1正常售后、2库管验货")
    private Integer afterType;
    @ApiModelProperty("报损处理时长(小时)")
    private Integer reportHandleTime;

    @ApiModelProperty("申诉窗口时长(小时)")
    private Integer appealWindowTime;

    @ApiModelProperty("申诉处理时长(小时)")
    private Integer appealHandleTime;

    @ApiModelProperty("品类层级")
    private Integer level;

    @ApiModelProperty("0后端类目，1前端类目【停用】")
    private Integer type;

    @ApiModelProperty("品类地址{parent_code}-{child_code},...")
    private String path;

    @ApiModelProperty("品类地址{parent_name}/{child_name},...")
    private String pathName;

    @ApiModelProperty("状态，0为不可用；1为可用")
    private Integer status;

    @ApiModelProperty("品类图片地址")
    private String imgUrl;

    @ApiModelProperty("排序")
    private Integer sort;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("删除标志（0代表存在 存入这条数据的主键代表删除）")
    private Long delFlag;

}
