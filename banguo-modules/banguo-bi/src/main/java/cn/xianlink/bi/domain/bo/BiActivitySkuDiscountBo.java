package cn.xianlink.bi.domain.bo;

import cn.xianlink.common.mybatis.core.domain.BaseEntity;
import lombok.Data;

import java.io.Serial;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

/**
 * 活动匹配出参
 * <AUTHOR>
 */
@Data
public class BiActivitySkuDiscountBo extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 供应商商品
     */
    private List<BiActivitySkuBo> supplierSkuList;

    /**
     * 总仓id
     */
    private Long regionId;

    /**
     * 城市仓id
     */
    private Long cityWhId;

    /**
     * 物流id
     */
    private Long logisticsId;

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 销售日期
     */
    private LocalDate saleDate;

    /**
     * sql查询用
     */
    private List<String> skuInfo;

    private LocalTime time;

    /**
     * 活动类型：10：减运费，20：减代采费，30：条件折扣，40：满件减钱，50：满钱减钱，60：满件则扣，70：满钱折扣，80：特价活动，90：限时打折
     */
    private Integer activityType;
}
