package cn.xianlink.bi.mq.handler.impl;

import cn.xianlink.bi.domain.es.SkuTest;
import cn.xianlink.bi.domain.vo.CanalMessageVo;
import cn.xianlink.bi.esmapper.SkuTestMapper;
import cn.xianlink.bi.mq.handler.CanalOperationHandler;
import com.alibaba.fastjson.JSON;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.dromara.easyes.core.conditions.update.LambdaEsUpdateWrapper;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Canal UPDATE操作处理器
 * 
 * <AUTHOR>
 */
@CustomLog
@RequiredArgsConstructor
@Component
public class CanalUpdateHandler implements CanalOperationHandler {

    private final SkuTestMapper skuTestMapper;

    @Override
    public void handle(CanalMessageVo canalMessageVo) {
        log.keyword("canalUpdateHandler").info("处理UPDATE操作: {}", JSON.toJSONString(canalMessageVo));
        
        List<Map<String, Object>> dataList = canalMessageVo.getData();
        List<Map<String, Object>> oldList = canalMessageVo.getOld();
        
        for (int i = 0; i < dataList.size(); i++) {
            Map<String, Object> newData = dataList.get(i);
            Map<String, Object> oldData = i < oldList.size() ? oldList.get(i) : null;
            
            updateSku(newData, oldData);
        }
    }

    @Override
    public String getSupportedOperationType() {
        return "UPDATE";
    }

    /**
     * 更新单个SKU数据
     * 
     * @param newData 更新后的数据
     * @param oldData 更新前的数据
     */
    private void updateSku(Map<String, Object> newData, Map<String, Object> oldData) {
        String id = newData.get("id").toString();
        
        // 构建更新条件
        LambdaEsUpdateWrapper<SkuTest> updateWrapper = new LambdaEsUpdateWrapper<>();
        updateWrapper.eq(SkuTest::getId, id);
        
        // 检查哪些字段发生了变化并更新
        if (hasFieldChanged("spu_name", newData, oldData)) {
            updateWrapper.set(SkuTest::getSpuName, newData.get("spu_name").toString());
        }
        
        if (hasFieldChanged("spu_grade", newData, oldData)) {
            updateWrapper.set(SkuTest::getSpuGrade, newData.get("spu_grade").toString());
        }
        
        if (hasFieldChanged("spu_standards", newData, oldData)) {
            updateWrapper.set(SkuTest::getSpuStandards, newData.get("spu_standards").toString());
        }
        
        // 始终更新修改时间
        updateWrapper.set(SkuTest::getUpdateTime, LocalDateTime.now());
        
        log.keyword("canalUpdateHandler").info("更新SKU [{}] 数据", id);
        skuTestMapper.update(null, updateWrapper);
    }

    /**
     * 检查字段是否发生变化
     * 
     * @param fieldName 字段名
     * @param newData 新数据
     * @param oldData 旧数据
     * @return 是否发生变化
     */
    private boolean hasFieldChanged(String fieldName, Map<String, Object> newData, Map<String, Object> oldData) {
        if (oldData == null) {
            return true; // 如果没有旧数据，认为字段发生了变化
        }
        
        Object newValue = newData.get(fieldName);
        Object oldValue = oldData.get(fieldName);
        
        if (newValue == null && oldValue == null) {
            return false;
        }
        
        if (newValue == null || oldValue == null) {
            return true;
        }
        
        return !newValue.toString().equals(oldValue.toString());
    }
} 