package cn.xianlink.bi.domain.bo;

import cn.xianlink.common.mybatis.core.page.PageQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * @program: banguo-parent-ms
 * @description: 订单查询参数
 * @author: chenbin
 * @create: 2025-02-28 11:46
 **/

@Data
public class BiOrderRefundBo extends PageQuery {


    @ApiModelProperty("退货退款单号")
    private String code;

    @ApiModelProperty("客户id")
    private Long customerId;

    @ApiModelProperty("总仓id")
    private Long regionWhId;

    @ApiModelProperty("城市仓id")
    private Long cityWhId;

    @ApiModelProperty("订单号")
    private String orderCode;

    @ApiModelProperty("销售日期-始")
    private LocalDate saleDateStart;

    @ApiModelProperty("销售日期-末")
    private LocalDate saleDateEnd;

    @ApiModelProperty("创建时间-始")
    private Date createTimeStart;

    @ApiModelProperty("创建时间-末")
    private Date createTimeEnd;

    @ApiModelProperty("状态变更时间-始")
    private Date updateTimeStart;

    @ApiModelProperty("状态变更时间-末")
    private Date updateTimeEnd;

    @ApiModelProperty("支付时间-始")
    private Date payTimeStart;

    @ApiModelProperty("支付时间-末")
    private Date payTimeEnd;

    @ApiModelProperty("退货时间-始")
    private Date refundTimeStart;

    @ApiModelProperty("退货时间-末")
    private Date refundTimeEnd;

    @ApiModelProperty("退款类型 2缺货 3少货 4差额退款")
    private Integer refundType;

    @ApiModelProperty("退款状态(0退款中 1已退款 2退款失败)")
    private Integer refundStatus;

    @ApiModelProperty("审核状态(0审核中 1审核通过)")
    private Integer auditStatus;

    /**
     * 退款金额区间最低
     */
    private BigDecimal refundAmountMin;

    /**
     * 退款金额区间最高
     */
    private BigDecimal refundAmountMax;

    @ApiModelProperty("城市仓权限")
    private List<Long> cityWhIds;

    @ApiModelProperty("总仓权限")
    private List<Long> regionWhIds;

    @ApiModelProperty("物流id")
    private Long logisticsId;

    @ApiModelProperty("子仓物流")
    private Long logisticsIdLevel2;

    @ApiModelProperty("商品名称")
    private String spuName;
}
