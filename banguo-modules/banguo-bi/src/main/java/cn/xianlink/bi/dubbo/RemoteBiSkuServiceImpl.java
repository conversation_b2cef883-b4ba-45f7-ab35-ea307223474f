package cn.xianlink.bi.dubbo;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.bi.api.IRemoteBiSkuService;
import cn.xianlink.bi.api.domain.bo.RemoteSkuLossBo;
import cn.xianlink.bi.api.domain.vo.RemoteSkuLossVo;
import cn.xianlink.bi.service.ISkuLossService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: Pei
 * Date: 2025/3/6 下午12:02
 */

@RequiredArgsConstructor
@Service
@DubboService
public class RemoteBiSkuServiceImpl implements IRemoteBiSkuService {

    private final ISkuLossService skuLossService;

    @Override
    public List<RemoteSkuLossVo> getSkuLossByRemoteBI(RemoteSkuLossBo bo)  {
        if(ObjectUtil.isEmpty(bo) || CollectionUtil.isEmpty(bo.getSkuIdList())){
            return List.of();
        }
        return skuLossService.getSkuLossAndOrderInfo(bo);
    }

    @Override
    public List<RemoteSkuLossVo> getSkuInspectByRemoteBI(RemoteSkuLossBo bo) {
        if(ObjectUtil.isEmpty(bo) || CollectionUtil.isEmpty(bo.getSkuIdList())){
            return List.of();
        }
        return skuLossService.getInspectCount(bo);
    }
}
