package cn.xianlink.bi.domain.es;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.rely.FieldType;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 搜索商品的信息
 *
 * <AUTHOR>
 * @date 2025/7/23
 */
@Data
@EqualsAndHashCode
public class SkuMarketingAttrValue implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 活动id
     */
    @IndexField(fieldType = FieldType.LONG)
    private Long activityId;

    /**
     * 活动名称
     */
    @IndexField(fieldType = FieldType.TEXT)
    private String activityName;

    /**
     * 活动类型
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer activityType;

    @IndexField(fieldType = FieldType.INTEGER)
    private Integer ownershipType;


    @IndexField(fieldType = FieldType.LONG)
    private Long ownershipId;

    /**
     * 时间生效类型
     */
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer effectType;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @IndexField(fieldType = FieldType.KEYWORD)
    private List<Integer> effectDays;

    @IndexField(fieldType = FieldType.INTEGER)
    private Integer priority;

    @IndexField(fieldType = FieldType.DOUBLE)
    private BigDecimal dicountAmount;

    @IndexField(fieldType = FieldType.DOUBLE)
    private BigDecimal dicountRate;

    @IndexField(fieldType = FieldType.KEYWORD)
    private String comboSkus;

    @IndexField(fieldType = FieldType.DOUBLE)
    private BigDecimal comboPrice;

    @IndexField(fieldType = FieldType.DOUBLE)
    private BigDecimal fullAmount;

    @IndexField(fieldType = FieldType.DOUBLE)
    private BigDecimal reduceAmount;

    @IndexField(fieldType = FieldType.KEYWORD)
    private String reduceType;

}