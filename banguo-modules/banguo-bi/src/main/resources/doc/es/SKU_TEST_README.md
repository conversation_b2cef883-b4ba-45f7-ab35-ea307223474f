# SKU Test Easy-ES 项目文档

## 项目概述

基于 Easy-ES 框架和 `sku_test` 索引生成的完整测试项目，包含了15种不同的 Elasticsearch 查询场景示例。

## 项目结构

```
banguo-modules/banguo-bi/
├── src/main/java/cn/xianlink/bi/
│   ├── BanguoBiApplication.java           # 主应用类(含@EsMapperScan)
│   ├── controller/
│   │   └── SkuTestController.java         # SKU测试控制器
│   ├── domain/
│   │   ├── bo/
│   │   │   ├── SkuTestBo.java            # 业务对象
│   │   │   └── SkuTestQueryBo.java       # 查询业务对象
│   │   ├── es/
│   │   │   └── SkuTest.java              # ES映射实体类
│   │   └── vo/
│   │       └── SkuTestVo.java            # 视图对象
│   ├── esmapper/
│   │   └── SkuTestMapper.java            # ES映射接口
│   └── service/
│       ├── ISkuTestService.java          # 服务接口
│       └── impl/
│           └── SkuTestServiceImpl.java   # 服务实现类
└── src/main/resources/
    ├── application-easy-es.yml           # Easy-ES配置文件
    └── doc/es/
        └── SKU_TEST_README.md           # 本文档
```

## 核心功能

### 1. ES映射实体类 (SkuTest.java)

```java
@IndexName(value = "sku_test", keepGlobalPrefix = false)
public class SkuTest {
    @IndexId(type = IdType.CUSTOMIZE)
    private String id;
    
    @IndexField(fieldType = FieldType.TEXT, analyzer = Analyzer.IK_SMART)
    private String spuName;
    
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer spuGrade;
    
    // ... 其他字段
}
```

### 2. Easy-ES 配置

#### 主应用类注解 (BanguoBiApplication.java)
```java
@EsMapperScan("cn.xianlink.bi.esmapper")
@SpringBootApplication
public class BanguoBiApplication {
    // ...
}
```

#### YAML配置文件 (application-easy-es.yml)
```yaml
easy-es:
  enable: true
  address: **********:9200
  api-key: VFNOdEhaZ0JYTTdVZFJhR1plQTE6UldLeGtkYmpRYkdsT0c5dFlIZi1mdw==
  print-dsl: true
```

## 15种查询场景

### 基础查询
1. **基本查询** - 按ID查询
   - 接口：`GET /bi/admin/skuTest/test/basicQuery/{id}`
   - 示例：`/bi/admin/skuTest/test/basicQuery/SKU001`

2. **中文分词全文搜索** - 搜索商品名称
   - 接口：`GET /bi/admin/skuTest/test/searchBySpuName?spuName=有机`
   - 使用 `match` 查询支持中文分词

3. **精确匹配** - 使用keyword字段
   - 接口：`GET /bi/admin/skuTest/test/exactMatch?spuName=有机西红柿`
   - 使用 `term` 查询精确匹配

### 范围查询
4. **等级范围查询**
   - 接口：`GET /bi/admin/skuTest/test/gradeRange?minGrade=2&maxGrade=3`
   - 使用 `between` 查询

5. **日期范围查询**
   - 接口：`GET /bi/admin/skuTest/test/dateRange?startTime=2023-05-01 00:00:00&endTime=2023-05-05 23:59:59`
   - 支持日期格式：`yyyy-MM-dd HH:mm:ss`

### 复杂查询
6. **复合查询** - 组合多个条件
   - 接口：`POST /bi/admin/skuTest/test/complexQuery`
   - 支持 `must`, `must_not`, `should` 条件

7. **聚合查询** - 按商品等级统计
   - 接口：`GET /bi/admin/skuTest/test/aggregateByGrade`
   - 返回各等级商品数量统计

8. **高级聚合** - 按规格分组计算平均等级
   - 接口：`GET /bi/admin/skuTest/test/aggregateByStandards`
   - 分组聚合并计算平均值

### 排序和分页
9. **排序查询**
   - 接口：`GET /bi/admin/skuTest/test/sortByGrade?sortOrder=desc`
   - 支持 `asc`/`desc` 排序

10. **分页查询**
    - 接口：`GET /bi/admin/skuTest/test/pagination?pageNum=1&pageSize=10&sortField=createTime&sortOrder=desc`
    - 支持分页、排序

### 模式匹配
11. **前缀查询**
    - 接口：`GET /bi/admin/skuTest/test/prefixQuery?prefix=SKU00`
    - 使用 `prefix` 查询

12. **通配符查询**
    - 接口：`GET /bi/admin/skuTest/test/wildcardQuery?pattern=*kg*&field=spuStandards`
    - 支持 `*` 和 `?` 通配符

13. **模糊查询**
    - 接口：`GET /bi/admin/skuTest/test/fuzzyQuery?value=西红柿&field=spuName`
    - 允许拼写错误的模糊匹配

### 高级功能
14. **高亮显示**
    - 接口：`GET /bi/admin/skuTest/test/highlightQuery?keyword=有机&field=spuName`
    - 搜索结果高亮显示

15. **布尔查询**
    - 接口：`POST /bi/admin/skuTest/test/booleanQuery`
    - 复杂布尔逻辑查询

## 批量操作

### 批量查询
```bash
POST /bi/admin/skuTest/test/queryByIds
{
  "ids": ["SKU001", "SKU002", "SKU003"]
}
```

### 批量插入
```bash
POST /bi/admin/skuTest/test/batchInsert
[
  {
    "id": "SKU011",
    "spuName": "有机南瓜",
    "spuGrade": 1,
    "spuStandards": "1kg/个"
  }
]
```

## 索引管理

### 创建索引
```bash
POST /bi/admin/skuTest/index/create
```

### 删除索引
```bash
DELETE /bi/admin/skuTest/index/delete
```

### 检查索引
```bash
GET /bi/admin/skuTest/index/exists
```

### 健康检查
```bash
GET /bi/admin/skuTest/health
```

## 性能测试

### 性能测试接口
```bash
POST /bi/admin/skuTest/test/performance
{
  "spuName": "有机",
  "pageNum": 1,
  "pageSize": 100
}
```

返回查询时间和性能指标。

## 使用示例

### 1. 基础CRUD操作

#### 新增数据
```bash
POST /bi/admin/skuTest/add
{
  "id": "SKU013",
  "spuName": "有机苹果",
  "spuGrade": 2,
  "spuStandards": "500g/袋",
  "isDeleted": false
}
```

#### 查询数据
```bash
GET /bi/admin/skuTest/SKU013
```

#### 更新数据
```bash
POST /bi/admin/skuTest/edit
{
  "id": "SKU013",
  "spuName": "有机苹果（优质）",
  "spuGrade": 3
}
```

#### 删除数据
```bash
DELETE /bi/admin/skuTest/SKU013
```

### 2. 复杂查询示例

#### 复合查询
```bash
POST /bi/admin/skuTest/test/complexQuery
{
  "spuName": "进口",
  "spuGradeMin": 2,
  "isDeleted": false,
  "createTimeStart": "2023-05-01T00:00:00",
  "createTimeEnd": "2023-05-10T23:59:59",
  "sortField": "spuGrade",
  "sortOrder": "desc"
}
```

#### 分页查询
```bash
POST /bi/admin/skuTest/list
{
  "pageNum": 1,
  "pageSize": 10,
  "spuName": "有机",
  "spuGradeMin": 1,
  "spuGradeMax": 3,
  "sortField": "createTime",
  "sortOrder": "desc"
}
```

## 数据模型

### SkuTest 字段说明

| 字段名 | 类型 | 说明 | ES字段类型 |
|--------|------|------|------------|
| id | String | 唯一标识 | keyword |
| spuName | String | 商品名称 | text (ik_smart分词) |
| spuGrade | Integer | 商品等级 | integer |
| spuStandards | String | 商品规格 | keyword |
| createTime | LocalDateTime | 创建时间 | date |
| updateTime | LocalDateTime | 更新时间 | date |
| isDeleted | Boolean | 是否删除 | boolean |

### 查询参数说明

#### SkuTestQueryBo 主要字段

| 字段名 | 类型 | 说明 | 用途 |
|--------|------|------|------|
| spuName | String | 商品名称 | 模糊查询 |
| spuGrade | Integer | 商品等级 | 精确查询 |
| spuGradeMin/Max | Integer | 等级范围 | 范围查询 |
| createTimeStart/End | LocalDateTime | 时间范围 | 日期范围查询 |
| sortField | String | 排序字段 | 排序控制 |
| sortOrder | String | 排序方向 | asc/desc |
| keyword | String | 关键词 | 全文搜索 |
| prefixValue | String | 前缀值 | 前缀查询 |
| wildcardValue | String | 通配符值 | 通配符查询 |

## 配置说明

### API Key 认证

在 `application-easy-es.yml` 中配置：
```yaml
easy-es:
  api-key: VFNOdEhaZ0JYTTdVZFJhR1plQTE6UldLeGtkYmpRYkdsT0c5dFlIZi1mdw==
```

### 连接配置
```yaml
easy-es:
  address: **********:9200
  connect-timeout: 5000
  socket-timeout: 60000
  max-conn-total: 100
```

### 调试配置
```yaml
easy-es:
  print-dsl: true  # 打印DSL语句
  
logging:
  level:
    org.dromara.easyes: DEBUG
    cn.xianlink.bi: DEBUG
```

## 错误处理

### 常见错误及解决方案

1. **连接超时**
   - 检查ES服务器地址和端口
   - 检查网络连接
   - 调整超时时间配置

2. **认证失败**
   - 检查API Key是否正确
   - 确认ES服务器支持API Key认证

3. **索引不存在**
   - 使用 `/index/create` 接口创建索引
   - 检查索引名称是否正确

4. **查询语法错误**
   - 开启 `print-dsl: true` 查看生成的DSL语句
   - 检查查询参数格式

## 扩展功能

### 自定义查询

可以在 `SkuTestServiceImpl` 中添加自定义查询方法：

```java
public List<SkuTestVo> customQuery() {
    LambdaEsQueryWrapper<SkuTest> wrapper = new LambdaEsQueryWrapper<>();
    // 自定义查询逻辑
    wrapper.match(SkuTest::getSpuName, "自定义条件");
    List<SkuTest> results = skuTestMapper.selectList(wrapper);
    return MapstructUtils.convert(results, SkuTestVo.class);
}
```

### 自定义聚合

```java
public Map<String, Object> customAggregation() {
    // 实现自定义聚合逻辑
    return new HashMap<>();
}
```

## 测试建议

1. **单元测试**：为每个查询方法编写单元测试
2. **集成测试**：测试ES连接和索引操作
3. **性能测试**：使用性能测试接口评估查询性能
4. **数据一致性测试**：验证CRUD操作的数据一致性

## 监控和日志

### 开启详细日志
```yaml
logging:
  level:
    org.dromara.easyes: DEBUG
    org.elasticsearch.client: DEBUG
```

### 性能监控
使用性能测试接口监控查询时间：
```bash
POST /bi/admin/skuTest/test/performance
```

### 健康检查
定期调用健康检查接口：
```bash
GET /bi/admin/skuTest/health
```

## 总结

本项目提供了基于 Easy-ES 的完整 Elasticsearch 解决方案，包含：

- ✅ 完整的 CRUD 操作
- ✅ 15种不同的查询场景
- ✅ 批量操作支持
- ✅ 索引管理功能
- ✅ 性能测试工具
- ✅ 健康检查机制
- ✅ 详细的配置示例
- ✅ Swagger API 文档

通过这个项目，您可以快速上手 Easy-ES 框架，并了解各种 Elasticsearch 查询技巧。 