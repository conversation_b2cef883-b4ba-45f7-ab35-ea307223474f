# Elasticsearch 订单索引设计与示例查询

本文档提供了将MySQL订单表转换为Elasticsearch索引的创建语句，以及相关的示例查询操作。

## 索引创建

以下是基于MySQL订单表结构创建的Elasticsearch索引定义，使用了IK中文分词器：

```json
PUT /howard_test_order
{
  "settings": {
    "number_of_shards": 3,
    "number_of_replicas": 1,
    "analysis": {
      "analyzer": {
        "ik_smart_pinyin": {
          "type": "custom",
          "tokenizer": "ik_smart",
          "filter": ["my_pinyin", "word_delimiter"]
        },
        "ik_max_word_pinyin": {
          "type": "custom",
          "tokenizer": "ik_max_word",
          "filter": ["my_pinyin", "word_delimiter"]
        }
      },
      "filter": {
        "my_pinyin": {
          "type": "pinyin",
          "keep_first_letter": true,
          "keep_separate_first_letter": false,
          "keep_full_pinyin": true,
          "keep_original": true,
          "limit_first_letter_length": 16,
          "lowercase": true
        }
      }
    }
  },
  "mappings": {
    "properties": {
      "id": {
        "type": "long"
      },
      "code": {
        "type": "keyword"
      },
      "customer_id": {
        "type": "long"
      },
      "region_wh_id": {
        "type": "long"
      },
      "city_wh_id": {
        "type": "long"
      },
      "logistics_id": {
        "type": "long"
      },
      "business_type": {
        "type": "byte"
      },
      "place_id": {
        "type": "long"
      },
      "place_name": {
        "type": "text",
        "analyzer": "ik_max_word",
        "search_analyzer": "ik_smart",
        "fields": {
          "keyword": {
            "type": "keyword",
            "ignore_above": 100
          },
          "pinyin": {
            "type": "text",
            "analyzer": "ik_max_word_pinyin",
            "search_analyzer": "ik_smart_pinyin"
          }
        }
      },
      "address": {
        "type": "text",
        "analyzer": "ik_max_word",
        "search_analyzer": "ik_smart",
        "fields": {
          "keyword": {
            "type": "keyword",
            "ignore_above": 128
          },
          "pinyin": {
            "type": "text",
            "analyzer": "ik_max_word_pinyin",
            "search_analyzer": "ik_smart_pinyin"
          }
        }
      },
      "sale_date": {
        "type": "date",
        "format": "yyyy-MM-dd"
      },
      "count": {
        "type": "integer"
      },
      "total_gross_weight": {
        "type": "scaled_float",
        "scaling_factor": 100
      },
      "total_net_weight": {
        "type": "scaled_float",
        "scaling_factor": 100
      },
      "product_amount": {
        "type": "scaled_float",
        "scaling_factor": 100
      },
      "platform_service_amount": {
        "type": "scaled_float",
        "scaling_factor": 100
      },
      "platform_freight_amount": {
        "type": "scaled_float",
        "scaling_factor": 100
      },
      "base_freight_amount": {
        "type": "scaled_float",
        "scaling_factor": 100
      },
      "region_freight_amount": {
        "type": "scaled_float",
        "scaling_factor": 100
      },
      "freight_total_amount": {
        "type": "scaled_float",
        "scaling_factor": 100
      },
      "other_total_amount": {
        "type": "scaled_float",
        "scaling_factor": 100
      },
      "platform_service_free_amount": {
        "type": "scaled_float",
        "scaling_factor": 100
      },
      "platform_freight_free_amount": {
        "type": "scaled_float",
        "scaling_factor": 100
      },
      "base_freight_free_amount": {
        "type": "scaled_float",
        "scaling_factor": 100
      },
      "region_freight_free_amount": {
        "type": "scaled_float",
        "scaling_factor": 100
      },
      "freight_total_free_amount": {
        "type": "scaled_float",
        "scaling_factor": 100
      },
      "coin_count": {
        "type": "scaled_float",
        "scaling_factor": 100
      },
      "coin_amount": {
        "type": "scaled_float",
        "scaling_factor": 100
      },
      "free_total_amount": {
        "type": "scaled_float",
        "scaling_factor": 100
      },
      "financial_service_amount": {
        "type": "scaled_float",
        "scaling_factor": 100
      },
      "total_amount": {
        "type": "scaled_float",
        "scaling_factor": 100
      },
      "pay_amount": {
        "type": "scaled_float",
        "scaling_factor": 100
      },
      "status": {
        "type": "keyword"
      },
      "pay_channel": {
        "type": "keyword"
      },
      "pay_time": {
        "type": "date"
      },
      "cancel_type": {
        "type": "keyword"
      },
      "cancel_time": {
        "type": "date"
      },
      "del_status": {
        "type": "byte"
      },
      "version": {
        "type": "integer"
      },
      "tenant_id": {
        "type": "keyword"
      },
      "del_flag": {
        "type": "long"
      },
      "create_code": {
        "type": "keyword"
      },
      "create_name": {
        "type": "text",
        "analyzer": "ik_max_word",
        "search_analyzer": "ik_smart",
        "fields": {
          "keyword": {
            "type": "keyword",
            "ignore_above": 64
          },
          "pinyin": {
            "type": "text",
            "analyzer": "ik_max_word_pinyin",
            "search_analyzer": "ik_smart_pinyin"
          }
        }
      },
      "create_time": {
        "type": "date"
      },
      "update_code": {
        "type": "keyword"
      },
      "update_name": {
        "type": "text",
        "analyzer": "ik_max_word",
        "search_analyzer": "ik_smart",
        "fields": {
          "keyword": {
            "type": "keyword",
            "ignore_above": 64
          },
          "pinyin": {
            "type": "text",
            "analyzer": "ik_max_word_pinyin",
            "search_analyzer": "ik_smart_pinyin"
          }
        }
      },
      "update_time": {
        "type": "date"
      },
      "subsidy_free_amount": {
        "type": "scaled_float",
        "scaling_factor": 100
      },
      "remark": {
        "type": "text",
        "analyzer": "ik_max_word",
        "search_analyzer": "ik_smart",
        "fields": {
          "keyword": {
            "type": "keyword",
            "ignore_above": 256
          }
        }
      },
      "financial_service_rate": {
        "type": "scaled_float",
        "scaling_factor": 1000000
      },
      "logistics_id_level2": {
        "type": "long"
      },
      "place_id_level2": {
        "type": "long"
      },
      "place_name_level2": {
        "type": "text",
        "analyzer": "ik_max_word",
        "search_analyzer": "ik_smart",
        "fields": {
          "keyword": {
            "type": "keyword",
            "ignore_above": 100
          },
          "pinyin": {
            "type": "text",
            "analyzer": "ik_max_word_pinyin",
            "search_analyzer": "ik_smart_pinyin"
          }
        }
      },
      "address_level2": {
        "type": "text",
        "analyzer": "ik_max_word",
        "search_analyzer": "ik_smart",
        "fields": {
          "keyword": {
            "type": "keyword",
            "ignore_above": 128
          },
          "pinyin": {
            "type": "text",
            "analyzer": "ik_max_word_pinyin",
            "search_analyzer": "ik_smart_pinyin"
          }
        }
      },
      "platform_freight_amount_level2": {
        "type": "scaled_float",
        "scaling_factor": 100
      },
      "place_path": {
        "type": "keyword"
      },
      "product_free_amount": {
        "type": "scaled_float",
        "scaling_factor": 100
      },
      "platform_freight_free_amount_level2": {
        "type": "scaled_float",
        "scaling_factor": 100
      },
      "fix_price_free_amount": {
        "type": "scaled_float",
        "scaling_factor": 100
      },
      "order_channel": {
        "type": "byte"
      }
    }
  }
}
```

## 插入示例数据

以下是向索引中插入示例数据的语句：

```json
POST /howard_test_order/_doc/1
{
  "id": 1001,
  "code": "ORD20230501001",
  "customer_id": 10086,
  "region_wh_id": 1,
  "city_wh_id": 101,
  "logistics_id": 501,
  "business_type": 1,
  "place_id": 201,
  "place_name": "北京朝阳区配送点",
  "address": "北京市朝阳区建国路88号",
  "sale_date": "2023-05-01",
  "count": 5,
  "total_gross_weight": 10.5,
  "total_net_weight": 9.8,
  "product_amount": 199.99,
  "platform_service_amount": 10.00,
  "platform_freight_amount": 15.00,
  "freight_total_amount": 15.00,
  "total_amount": 224.99,
  "pay_amount": 224.99,
  "status": "ALREADY",
  "pay_channel": "1",
  "pay_time": "2023-05-01T10:30:00",
  "create_name": "张三",
  "create_time": "2023-05-01T10:15:00",
  "remark": "生鲜蔬菜配送订单",
  "order_channel": 1
}

POST /howard_test_order/_doc/2
{
  "id": 1002,
  "code": "ORD20230502001",
  "customer_id": 10087,
  "region_wh_id": 1,
  "city_wh_id": 102,
  "logistics_id": 502,
  "business_type": 1,
  "place_id": 202,
  "place_name": "上海浦东新区配送点",
  "address": "上海市浦东新区张江高科技园区",
  "sale_date": "2023-05-02",
  "count": 8,
  "total_gross_weight": 15.2,
  "total_net_weight": 14.5,
  "product_amount": 299.50,
  "platform_service_amount": 15.00,
  "platform_freight_amount": 0.00,
  "freight_total_amount": 0.00,
  "total_amount": 314.50,
  "pay_amount": 314.50,
  "status": "FINISH",
  "pay_channel": "1",
  "pay_time": "2023-05-02T09:20:00",
  "create_name": "李四",
  "create_time": "2023-05-02T09:10:00",
  "remark": "水果礼盒配送订单",
  "order_channel": 1
}

POST /howard_test_order/_doc/3
{
  "id": 1003,
  "code": "ORD20230503001",
  "customer_id": 10088,
  "region_wh_id": 2,
  "city_wh_id": 103,
  "logistics_id": 503,
  "business_type": 1,
  "place_id": 203,
  "place_name": "广州天河区配送点",
  "address": "广州市天河区天河路385号",
  "sale_date": "2023-05-03",
  "count": 3,
  "total_gross_weight": 5.8,
  "total_net_weight": 5.2,
  "product_amount": 99.80,
  "platform_service_amount": 5.00,
  "platform_freight_amount": 10.00,
  "freight_total_amount": 10.00,
  "total_amount": 114.80,
  "pay_amount": 114.80,
  "status": "WAIT",
  "create_name": "王五",
  "create_time": "2023-05-03T14:25:00",
  "remark": "海鲜生鲜配送订单",
  "order_channel": 1
}

POST /howard_test_order/_doc/4
{
  "id": 1004,
  "code": "ORD20230504001",
  "customer_id": 10089,
  "region_wh_id": 2,
  "city_wh_id": 104,
  "logistics_id": 504,
  "business_type": 0,
  "place_id": 204,
  "place_name": "深圳南山区配送点",
  "address": "深圳市南山区科技园路33号",
  "sale_date": "2023-05-04",
  "count": 10,
  "total_gross_weight": 20.5,
  "total_net_weight": 19.8,
  "product_amount": 399.99,
  "platform_service_amount": 20.00,
  "platform_freight_amount": 0.00,
  "freight_total_amount": 0.00,
  "platform_service_free_amount": 20.00,
  "free_total_amount": 20.00,
  "total_amount": 419.99,
  "pay_amount": 399.99,
  "status": "ALREADY",
  "pay_channel": "1",
  "pay_time": "2023-05-04T16:40:00",
  "create_name": "赵六",
  "create_time": "2023-05-04T16:30:00",
  "remark": "有机蔬菜配送订单",
  "order_channel": 1
}

POST /howard_test_order/_doc/5
{
  "id": 1005,
  "code": "ORD20230505001",
  "customer_id": 10090,
  "region_wh_id": 3,
  "city_wh_id": 105,
  "logistics_id": 505,
  "business_type": 1,
  "place_id": 205,
  "place_name": "成都锦江区配送点",
  "address": "成都市锦江区红星路三段99号",
  "sale_date": "2023-05-05",
  "count": 6,
  "total_gross_weight": 12.3,
  "total_net_weight": 11.5,
  "product_amount": 259.90,
  "platform_service_amount": 12.00,
  "platform_freight_amount": 18.00,
  "freight_total_amount": 18.00,
  "total_amount": 289.90,
  "pay_amount": 289.90,
  "status": "CANCEL",
  "cancel_type": "BUYER",
  "cancel_time": "2023-05-05T11:20:00",
  "create_name": "孙七",
  "create_time": "2023-05-05T10:50:00",
  "remark": "肉类生鲜配送订单",
  "order_channel": 1
}
```

## 示例查询

### 1. 基本查询 - 按订单状态查询

```json
GET /howard_test_order/_search
{
  "query": {
    "term": {
      "status": "ALREADY"
    }
  }
}
```

### 2. 中文分词全文搜索 - 搜索地址或配送点名称

```json
GET /howard_test_order/_search
{
  "query": {
    "multi_match": {
      "query": "北京朝阳",
      "fields": ["place_name", "address"]
    }
  }
}
```

### 3. 拼音搜索 - 通过拼音搜索地址或配送点

```json
GET /howard_test_order/_search
{
  "query": {
    "multi_match": {
      "query": "beijing",
      "fields": ["place_name.pinyin", "address.pinyin"]
    }
  }
}
```

### 4. 范围查询 - 按订单金额范围查询

```json
GET /howard_test_order/_search
{
  "query": {
    "range": {
      "total_amount": {
        "gte": 200,
        "lte": 300
      }
    }
  }
}
```

### 5. 复合查询 - 按日期范围和订单状态查询

```json
GET /howard_test_order/_search
{
  "query": {
    "bool": {
      "must": [
        {
          "range": {
            "sale_date": {
              "gte": "2023-05-01",
              "lte": "2023-05-03"
            }
          }
        },
        {
          "term": {
            "status": "ALREADY"
          }
        }
      ]
    }
  }
}
```

### 6. 聚合查询 - 按订单状态统计订单数量和总金额

```json
GET /howard_test_order/_search
{
  "size": 0,
  "aggs": {
    "order_status": {
      "terms": {
        "field": "status"
      },
      "aggs": {
        "total_orders": {
          "value_count": {
            "field": "id"
          }
        },
        "total_amount_sum": {
          "sum": {
            "field": "total_amount"
          }
        }
      }
    }
  }
}
```

### 7. 模糊查询 - 搜索备注中包含特定关键词的订单

```json
GET /howard_test_order/_search
{
  "query": {
    "match": {
      "remark": "蔬菜"
    }
  }
}
```

### 8. 高亮显示 - 搜索结果中高亮显示匹配的文本

```json
GET /howard_test_order/_search
{
  "query": {
    "match": {
      "remark": "生鲜"
    }
  },
  "highlight": {
    "fields": {
      "remark": {}
    }
  }
}
```

### 9. 排序 - 按订单金额降序排列

```json
GET /howard_test_order/_search
{
  "query": {
    "match_all": {}
  },
  "sort": [
    {
      "total_amount": {
        "order": "desc"
      }
    }
  ]
}
```

### 10. 分页查询 - 实现分页效果

```json
GET /howard_test_order/_search
{
  "from": 0,
  "size": 2,
  "query": {
    "match_all": {}
  },
  "sort": [
    {
      "create_time": {
        "order": "desc"
      }
    }
  ]
}
```

## 同义词配置示例

如果需要配置同义词，可以在索引设置中添加同义词过滤器：

```json
PUT /howard_test_order/_settings
{
  "analysis": {
    "filter": {
      "my_synonym_filter": {
        "type": "synonym",
        "synonyms": [
          "订单,订购,下单",
          "配送,送货,快递",
          "生鲜,新鲜,食材"
        ]
      }
    },
    "analyzer": {
      "ik_synonym": {
        "type": "custom",
        "tokenizer": "ik_smart",
        "filter": ["my_synonym_filter"]
      }
    }
  }
}
```

然后可以使用这个分析器进行查询：

```json
GET /howard_test_order/_search
{
  "query": {
    "match": {
      "remark": {
        "query": "食材",
        "analyzer": "ik_synonym"
      }
    }
  }
}
```

## 注意事项

1. 使用IK分词器需要先安装插件：`./bin/elasticsearch-plugin install https://github.com/medcl/elasticsearch-analysis-ik/releases/download/v7.x.0/elasticsearch-analysis-ik-7.x.0.zip`（请根据你的ES版本选择对应版本的插件）

2. 拼音搜索需要安装拼音分词插件：`./bin/elasticsearch-plugin install https://github.com/medcl/elasticsearch-analysis-pinyin/releases/download/v7.x.0/elasticsearch-analysis-pinyin-7.x.0.zip`

3. 对于大量数据的索引，建议使用Bulk API进行批量导入

4. 在生产环境中，应根据实际需求调整分片数和副本数

5. 对于金额类型的字段，使用scaled_float类型可以提高存储和查询效率

6. 对于中文字段，建议同时保留keyword类型以支持精确匹配和排序