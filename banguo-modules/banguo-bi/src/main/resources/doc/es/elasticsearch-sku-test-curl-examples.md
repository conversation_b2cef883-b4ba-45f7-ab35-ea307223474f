# Elasticsearch SKU测试索引 Curl 查询示例

本文档提供了针对SKU测试索引的curl命令行查询示例，可直接在命令行中执行。

## 连接验证

首先验证Elasticsearch服务是否可连接：

```bash
curl -s -I -H "Authorization: ApiKey VFNOdEhaZ0JYTTdVZFJhR1plQTE6UldLeGtkYmpRYkdsT0c5dFlIZi1mdw==" http://10.1.16.13:9200
```

## 索引创建

创建SKU测试索引：

```bash
curl -X PUT "http://10.1.16.13:9200/sku_test" \
-H "Authorization: ApiKey VFNOdEhaZ0JYTTdVZFJhR1plQTE6UldLeGtkYmpRYkdsT0c5dFlIZi1mdw==" \
-H "Content-Type: application/json" \
-d '
{
   "mappings": {
     "dynamic_templates": [
       {
         "message_full": {
           "match": "message_full",
           "mapping": {
             "fields": {
               "keyword": {
                 "ignore_above": 2048,
                 "type": "keyword"
               }
             },
             "type": "match_only_text"
           }
         }
       },
       {
         "message": {
           "match": "message",
           "mapping": {
             "type": "match_only_text"
           }
         }
       },
       {
         "strings": {
           "match_mapping_type": "string",
           "mapping": {
             "type": "keyword"
           }
         }
       }
     ],
     "properties": {
       "createTime": {
         "type": "date",
         "format": "yyyy-MM-dd HH:mm:ss"
       },
       "id": {
         "type": "keyword"
       },
       "isDeleted": {
         "type": "boolean"
       },
       "spuGrade": {
         "type": "integer"
       },
       "spuName": {
         "type": "text",
         "fields": {
           "keyword": {
             "type": "keyword"
           }
         },
         "analyzer": "ik_smart"
       },
       "spuStandards": {
         "type": "keyword"
       },
       "updateTime": {
         "type": "date",
         "format": "yyyy-MM-dd HH:mm:ss"
       }
     }
   }
}'
```

## 插入示例数据

以下是向索引中插入10条示例数据的curl命令：

```bash
# 插入第1条数据
curl -X POST "http://10.1.16.13:9200/sku_test/_doc/1" \
-H "Authorization: ApiKey VFNOdEhaZ0JYTTdVZFJhR1plQTE6UldLeGtkYmpRYkdsT0c5dFlIZi1mdw==" \
-H "Content-Type: application/json" \
-d '
{
  "id": "SKU001",
  "spuName": "有机西红柿",
  "spuGrade": 1,
  "spuStandards": "500g/盒",
  "isDeleted": false,
  "createTime": "2023-05-01 10:15:00",
  "updateTime": "2023-05-01 10:15:00"
}'

# 插入第2条数据
curl -X POST "http://10.1.16.13:9200/sku_test/_doc/2" \
-H "Authorization: ApiKey VFNOdEhaZ0JYTTdVZFJhR1plQTE6UldLeGtkYmpRYkdsT0c5dFlIZi1mdw==" \
-H "Content-Type: application/json" \
-d '
{
  "id": "SKU002",
  "spuName": "进口牛肉",
  "spuGrade": 3,
  "spuStandards": "1kg/包",
  "isDeleted": false,
  "createTime": "2023-05-02 09:30:00",
  "updateTime": "2023-05-02 09:30:00"
}'

# 插入第3条数据
curl -X POST "http://10.1.16.13:9200/sku_test/_doc/3" \
-H "Authorization: ApiKey VFNOdEhaZ0JYTTdVZFJhR1plQTE6UldLeGtkYmpRYkdsT0c5dFlIZi1mdw==" \
-H "Content-Type: application/json" \
-d '
{
  "id": "SKU003",
  "spuName": "新鲜三文鱼",
  "spuGrade": 4,
  "spuStandards": "300g/份",
  "isDeleted": false,
  "createTime": "2023-05-03 14:20:00",
  "updateTime": "2023-05-03 14:20:00"
}'

# 插入第4条数据
curl -X POST "http://10.1.16.13:9200/sku_test/_doc/4" \
-H "Authorization: ApiKey VFNOdEhaZ0JYTTdVZFJhR1plQTE6UldLeGtkYmpRYkdsT0c5dFlIZi1mdw==" \
-H "Content-Type: application/json" \
-d '
{
  "id": "SKU004",
  "spuName": "有机菠菜",
  "spuGrade": 1,
  "spuStandards": "250g/袋",
  "isDeleted": false,
  "createTime": "2023-05-04 11:45:00",
  "updateTime": "2023-05-04 11:45:00"
}'

# 插入第5条数据
curl -X POST "http://10.1.16.13:9200/sku_test/_doc/5" \
-H "Authorization: ApiKey VFNOdEhaZ0JYTTdVZFJhR1plQTE6UldLeGtkYmpRYkdsT0c5dFlIZi1mdw==" \
-H "Content-Type: application/json" \
-d '
{
  "id": "SKU005",
  "spuName": "特级大米",
  "spuGrade": 2,
  "spuStandards": "5kg/袋",
  "isDeleted": false,
  "createTime": "2023-05-05 16:30:00",
  "updateTime": "2023-05-05 16:30:00"
}'

# 插入第6条数据
curl -X POST "http://10.1.16.13:9200/sku_test/_doc/6" \
-H "Authorization: ApiKey VFNOdEhaZ0JYTTdVZFJhR1plQTE6UldLeGtkYmpRYkdsT0c5dFlIZi1mdw==" \
-H "Content-Type: application/json" \
-d '
{
  "id": "SKU006",
  "spuName": "进口橙子",
  "spuGrade": 2,
  "spuStandards": "1kg/网袋",
  "isDeleted": false,
  "createTime": "2023-05-06 08:15:00",
  "updateTime": "2023-05-06 08:15:00"
}'

# 插入第7条数据
curl -X POST "http://10.1.16.13:9200/sku_test/_doc/7" \
-H "Authorization: ApiKey VFNOdEhaZ0JYTTdVZFJhR1plQTE6UldLeGtkYmpRYkdsT0c5dFlIZi1mdw==" \
-H "Content-Type: application/json" \
-d '
{
  "id": "SKU007",
  "spuName": "黑猪五花肉",
  "spuGrade": 3,
  "spuStandards": "500g/盒",
  "isDeleted": false,
  "createTime": "2023-05-07 13:40:00",
  "updateTime": "2023-05-07 13:40:00"
}'

# 插入第8条数据
curl -X POST "http://10.1.16.13:9200/sku_test/_doc/8" \
-H "Authorization: ApiKey VFNOdEhaZ0JYTTdVZFJhR1plQTE6UldLeGtkYmpRYkdsT0c5dFlIZi1mdw==" \
-H "Content-Type: application/json" \
-d '
{
  "id": "SKU008",
  "spuName": "有机鸡蛋",
  "spuGrade": 2,
  "spuStandards": "10枚/盒",
  "isDeleted": false,
  "createTime": "2023-05-08 10:25:00",
  "updateTime": "2023-05-08 10:25:00"
}'

# 插入第9条数据
curl -X POST "http://10.1.16.13:9200/sku_test/_doc/9" \
-H "Authorization: ApiKey VFNOdEhaZ0JYTTdVZFJhR1plQTE6UldLeGtkYmpRYkdsT0c5dFlIZi1mdw==" \
-H "Content-Type: application/json" \
-d '
{
  "id": "SKU009",
  "spuName": "进口牛奶",
  "spuGrade": 3,
  "spuStandards": "1L/盒",
  "isDeleted": false,
  "createTime": "2023-05-09 15:50:00",
  "updateTime": "2023-05-09 15:50:00"
}'

# 插入第10条数据
curl -X POST "http://10.1.16.13:9200/sku_test/_doc/10" \
-H "Authorization: ApiKey VFNOdEhaZ0JYTTdVZFJhR1plQTE6UldLeGtkYmpRYkdsT0c5dFlIZi1mdw==" \
-H "Content-Type: application/json" \
-d '
{
  "id": "SKU010",
  "spuName": "有机胡萝卜",
  "spuGrade": 1,
  "spuStandards": "400g/袋",
  "isDeleted": false,
  "createTime": "2023-05-10 12:10:00",
  "updateTime": "2023-05-10 12:10:00"
}'
```

## 示例查询

### 1. 基本查询 - 按ID查询

```bash
curl -X GET "http://10.1.16.13:9200/sku_test/_search" \
-H "Authorization: ApiKey VFNOdEhaZ0JYTTdVZFJhR1plQTE6UldLeGtkYmpRYkdsT0c5dFlIZi1mdw==" \
-H "Content-Type: application/json" \
-d '
{
  "query": {
    "term": {
      "id": "SKU001"
    }
  }
}'
```

### 2. 中文分词全文搜索 - 搜索商品名称

```bash
curl -X GET "http://10.1.16.13:9200/sku_test/_search" \
-H "Authorization: ApiKey VFNOdEhaZ0JYTTdVZFJhR1plQTE6UldLeGtkYmpRYkdsT0c5dFlIZi1mdw==" \
-H "Content-Type: application/json" \
-d '
{
  "query": {
    "match": {
      "spuName": "有机"
    }
  }
}'
```

### 3. 精确匹配关键词 - 使用keyword字段

```bash
curl -X GET "http://10.1.16.13:9200/sku_test/_search" \
-H "Authorization: ApiKey VFNOdEhaZ0JYTTdVZFJhR1plQTE6UldLeGtkYmpRYkdsT0c5dFlIZi1mdw==" \
-H "Content-Type: application/json" \
-d '
{
  "query": {
    "term": {
      "spuName.keyword": "有机西红柿"
    }
  }
}'
```

### 4. 范围查询 - 按商品等级范围查询

```bash
curl -X GET "http://10.1.16.13:9200/sku_test/_search" \
-H "Authorization: ApiKey VFNOdEhaZ0JYTTdVZFJhR1plQTE6UldLeGtkYmpRYkdsT0c5dFlIZi1mdw==" \
-H "Content-Type: application/json" \
-d '
{
  "query": {
    "range": {
      "spuGrade": {
        "gte": 2,
        "lte": 3
      }
    }
  }
}'
```

### 5. 日期范围查询 - 按创建时间范围查询

```bash
curl -X GET "http://10.1.16.13:9200/sku_test/_search" \
-H "Authorization: ApiKey VFNOdEhaZ0JYTTdVZFJhR1plQTE6UldLeGtkYmpRYkdsT0c5dFlIZi1mdw==" \
-H "Content-Type: application/json" \
-d '
{
  "query": {
    "range": {
      "createTime": {
        "gte": "2023-05-01 00:00:00",
        "lte": "2023-05-05 23:59:59"
      }
    }
  }
}'
```

### 6. 复合查询 - 组合多个条件

```bash
curl -X GET "http://10.1.16.13:9200/sku_test/_search" \
-H "Authorization: ApiKey VFNOdEhaZ0JYTTdVZFJhR1plQTE6UldLeGtkYmpRYkdsT0c5dFlIZi1mdw==" \
-H "Content-Type: application/json" \
-d '
{
  "query": {
    "bool": {
      "must": [
        {
          "match": {
            "spuName": "进口"
          }
        },
        {
          "range": {
            "spuGrade": {
              "gte": 2
            }
          }
        }
      ],
      "must_not": [
        {
          "term": {
            "isDeleted": true
          }
        }
      ]
    }
  }
}'
```

### 7. 聚合查询 - 按商品等级统计数量

```bash
curl -X GET "http://10.1.16.13:9200/sku_test/_search" \
-H "Authorization: ApiKey VFNOdEhaZ0JYTTdVZFJhR1plQTE6UldLeGtkYmpRYkdsT0c5dFlIZi1mdw==" \
-H "Content-Type: application/json" \
-d '
{
  "size": 0,
  "aggs": {
    "grade_count": {
      "terms": {
        "field": "spuGrade"
      }
    }
  }
}'
```

### 8. 高级聚合 - 按商品规格分组并计算每组的平均等级

```bash
curl -X GET "http://10.1.16.13:9200/sku_test/_search" \
-H "Authorization: ApiKey VFNOdEhaZ0JYTTdVZFJhR1plQTE6UldLeGtkYmpRYkdsT0c5dFlIZi1mdw==" \
-H "Content-Type: application/json" \
-d '
{
  "size": 0,
  "aggs": {
    "standards_group": {
      "terms": {
        "field": "spuStandards"
      },
      "aggs": {
        "avg_grade": {
          "avg": {
            "field": "spuGrade"
          }
        }
      }
    }
  }
}'
```

### 9. 排序 - 按商品等级降序排列

```bash
curl -X GET "http://10.1.16.13:9200/sku_test/_search" \
-H "Authorization: ApiKey VFNOdEhaZ0JYTTdVZFJhR1plQTE6UldLeGtkYmpRYkdsT0c5dFlIZi1mdw==" \
-H "Content-Type: application/json" \
-d '
{
  "query": {
    "match_all": {}
  },
  "sort": [
    {
      "spuGrade": {
        "order": "desc"
      }
    }
  ]
}'
```

### 10. 分页查询 - 实现分页效果

```bash
curl -X GET "http://10.1.16.13:9200/sku_test/_search" \
-H "Authorization: ApiKey VFNOdEhaZ0JYTTdVZFJhR1plQTE6UldLeGtkYmpRYkdsT0c5dFlIZi1mdw==" \
-H "Content-Type: application/json" \
-d '
{
  "from": 0,
  "size": 3,
  "query": {
    "match_all": {}
  },
  "sort": [
    {
      "createTime": {
        "order": "desc"
      }
    }
  ]
}'
```

### 11. 前缀查询 - 查询ID前缀

```bash
curl -X GET "http://10.1.16.13:9200/sku_test/_search" \
-H "Authorization: ApiKey VFNOdEhaZ0JYTTdVZFJhR1plQTE6UldLeGtkYmpRYkdsT0c5dFlIZi1mdw==" \
-H "Content-Type: application/json" \
-d '
{
  "query": {
    "prefix": {
      "id": "SKU00"
    }
  }
}'
```

### 12. 通配符查询 - 使用通配符匹配

```bash
curl -X GET "http://10.1.16.13:9200/sku_test/_search" \
-H "Authorization: ApiKey VFNOdEhaZ0JYTTdVZFJhR1plQTE6UldLeGtkYmpRYkdsT0c5dFlIZi1mdw==" \
-H "Content-Type: application/json" \
-d '
{
  "query": {
    "wildcard": {
      "spuStandards": "*kg*"
    }
  }
}'
```

### 13. 模糊查询 - 允许拼写错误

```bash
curl -X GET "http://10.1.16.13:9200/sku_test/_search" \
-H "Authorization: ApiKey VFNOdEhaZ0JYTTdVZFJhR1plQTE6UldLeGtkYmpRYkdsT0c5dFlIZi1mdw==" \
-H "Content-Type: application/json" \
-d '
{
  "query": {
    "fuzzy": {
      "spuName": {
        "value": "西红柿",
        "fuzziness": "AUTO"
      }
    }
  }
}'
```

### 14. 高亮显示 - 搜索结果中高亮显示匹配的文本

```bash
curl -X GET "http://10.1.16.13:9200/sku_test/_search" \
-H "Authorization: ApiKey VFNOdEhaZ0JYTTdVZFJhR1plQTE6UldLeGtkYmpRYkdsT0c5dFlIZi1mdw==" \
-H "Content-Type: application/json" \
-d '
{
  "query": {
    "match": {
      "spuName": "有机"
    }
  },
  "highlight": {
    "fields": {
      "spuName": {}
    }
  }
}'
```

### 15. 布尔查询 - 查询已删除或等级为1的商品

```bash
curl -X GET "http://10.1.16.13:9200/sku_test/_search" \
-H "Authorization: ApiKey VFNOdEhaZ0JYTTdVZFJhR1plQTE6UldLeGtkYmpRYkdsT0c5dFlIZi1mdw==" \
-H "Content-Type: application/json" \
-d '
{
  "query": {
    "bool": {
      "should": [
        {
          "term": {
            "isDeleted": true
          }
        },
        {
          "term": {
            "spuGrade": 1
          }
        }
      ],
      "minimum_should_match": 1
    }
  }
}'
```

## 批量操作示例

### 批量查询 - 使用mget查询多个文档

```bash
curl -X GET "http://10.1.16.13:9200/sku_test/_mget" \
-H "Authorization: ApiKey VFNOdEhaZ0JYTTdVZFJhR1plQTE6UldLeGtkYmpRYkdsT0c5dFlIZi1mdw==" \
-H "Content-Type: application/json" \
-d '
{
  "ids": ["1", "3", "5"]
}'
```

### 批量操作 - 使用bulk API进行批量操作

```bash
curl -X POST "http://10.1.16.13:9200/_bulk" \
-H "Authorization: ApiKey VFNOdEhaZ0JYTTdVZFJhR1plQTE6UldLeGtkYmpRYkdsT0c5dFlIZi1mdw==" \
-H "Content-Type: application/json" \
-d '
{"index":{"_index":"sku_test","_id":"11"}}
{"id":"SKU011","spuName":"有机南瓜","spuGrade":1,"spuStandards":"1kg/个","isDeleted":false,"createTime":"2023-05-11 09:20:00","updateTime":"2023-05-11 09:20:00"}
{"index":{"_index":"sku_test","_id":"12"}}
{"id":"SKU012","spuName":"进口苹果","spuGrade":2,"spuStandards":"500g/袋","isDeleted":false,"createTime":"2023-05-12 14:30:00","updateTime":"2023-05-12 14:30:00"}
'
```

## 索引管理操作

### 获取索引信息

```bash
curl -X GET "http://10.1.16.13:9200/sku_test" \
-H "Authorization: ApiKey VFNOdEhaZ0JYTTdVZFJhR1plQTE6UldLeGtkYmpRYkdsT0c5dFlIZi1mdw=="
```

### 获取索引统计信息

```bash
curl -X GET "http://10.1.16.13:9200/sku_test/_stats" \
-H "Authorization: ApiKey VFNOdEhaZ0JYTTdVZFJhR1plQTE6UldLeGtkYmpRYkdsT0c5dFlIZi1mdw=="
```

### 刷新索引

```bash
curl -X POST "http://10.1.16.13:9200/sku_test/_refresh" \
-H "Authorization: ApiKey VFNOdEhaZ0JYTTdVZFJhR1plQTE6UldLeGtkYmpRYkdsT0c5dFlIZi1mdw=="
```

### 删除索引

```bash
curl -X DELETE "http://10.1.16.13:9200/sku_test" \
-H "Authorization: ApiKey VFNOdEhaZ0JYTTdVZFJhR1plQTE6UldLeGtkYmpRYkdsT0c5dFlIZi1mdw=="
```

## 文档管理操作

### 获取单个文档

```bash
curl -X GET "http://10.1.16.13:9200/sku_test/_doc/1" \
-H "Authorization: ApiKey VFNOdEhaZ0JYTTdVZFJhR1plQTE6UldLeGtkYmpRYkdsT0c5dFlIZi1mdw=="
```

### 更新文档

```bash
curl -X POST "http://10.1.16.13:9200/sku_test/_update/1" \
-H "Authorization: ApiKey VFNOdEhaZ0JYTTdVZFJhR1plQTE6UldLeGtkYmpRYkdsT0c5dFlIZi1mdw==" \
-H "Content-Type: application/json" \
-d '
{
  "doc": {
    "spuGrade": 2,
    "updateTime": "2023-05-15 10:00:00"
  }
}'
```

### 删除文档

```bash
curl -X DELETE "http://10.1.16.13:9200/sku_test/_doc/1" \
-H "Authorization: ApiKey VFNOdEhaZ0JYTTdVZFJhR1plQTE6UldLeGtkYmpRYkdsT0c5dFlIZi1mdw=="
```