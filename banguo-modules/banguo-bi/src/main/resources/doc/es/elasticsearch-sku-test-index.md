# Elasticsearch SKU测试索引示例数据

本文档提供了SKU测试索引的创建语句以及10条示例数据。

## 索引创建

以下是SKU测试索引的创建语句：

```json
PUT /sku_test
{
   "mappings": {
     "dynamic_templates": [
       {
         "message_full": {
           "match": "message_full",
           "mapping": {
             "fields": {
               "keyword": {
                 "ignore_above": 2048,
                 "type": "keyword"
               }
             },
             "type": "match_only_text"
           }
         }
       },
       {
         "message": {
           "match": "message",
           "mapping": {
             "type": "match_only_text"
           }
         }
       },
       {
         "strings": {
           "match_mapping_type": "string",
           "mapping": {
             "type": "keyword"
           }
         }
       }
     ],
     "properties": {
       "createTime": {
         "type": "date",
         "format": "yyyy-MM-dd HH:mm:ss"
       },
       "id": {
         "type": "keyword"
       },
       "isDeleted": {
         "type": "boolean"
       },
       "spuGrade": {
         "type": "keyword",
         "null_value": "未知等级"
       },
       "spuName": {
         "type": "text",
         "fields": {
           "keyword": {
             "type": "keyword"
           }
         },
         "analyzer": "ik_smart"
       },
       "spuStandards": {
         "type": "keyword",
         "null_value": "标准规格"
       },
       "updateTime": {
         "type": "date",
         "format": "yyyy-MM-dd HH:mm:ss"
       }
     }
   }
}
```

## 插入示例数据

以下是向索引中插入10条示例数据的语句：

```json
POST /sku_test/_doc/1
{
  "id": "SKU001",
  "spuName": "有机西红柿",
  "spuGrade": "七级",
  "spuStandards": "500g/盒",
  "isDeleted": false,
  "createTime": "2023-05-01 10:15:00",
  "updateTime": "2023-05-01 10:15:00"
}

POST /sku_test/_doc/2
{
  "id": "SKU002",
  "spuName": "进口牛肉",
  "spuGrade": "三级",
  "spuStandards": "1kg/包",
  "isDeleted": false,
  "createTime": "2023-05-02 09:30:00",
  "updateTime": "2023-05-02 09:30:00"
}

POST /sku_test/_doc/3
{
  "id": "SKU003",
  "spuName": "新鲜三文鱼",
  "spuGrade": "九级",
  "spuStandards": "300g/份",
  "isDeleted": false,
  "createTime": "2023-05-03 14:20:00",
  "updateTime": "2023-05-03 14:20:00"
}

POST /sku_test/_doc/4
{
  "id": "SKU004",
  "spuName": "有机菠菜",
  "spuGrade": "一级",
  "spuStandards": "250g/袋",
  "isDeleted": false,
  "createTime": "2023-05-04 11:45:00",
  "updateTime": "2023-05-04 11:45:00"
}

POST /sku_test/_doc/5
{
  "id": "SKU005",
  "spuName": "特级大米",
  "spuGrade": "五级",
  "spuStandards": "5kg/袋",
  "isDeleted": false,
  "createTime": "2023-05-05 16:30:00",
  "updateTime": "2023-05-05 16:30:00"
}

POST /sku_test/_doc/6
{
  "id": "SKU006",
  "spuName": "进口橙子",
  "spuGrade": "二级",
  "spuStandards": "1kg/网袋",
  "isDeleted": false,
  "createTime": "2023-05-06 08:15:00",
  "updateTime": "2023-05-06 08:15:00"
}

POST /sku_test/_doc/7
{
  "id": "SKU007",
  "spuName": "黑猪五花肉",
  "spuGrade": "十级",
  "spuStandards": "500g/盒",
  "isDeleted": false,
  "createTime": "2023-05-07 13:40:00",
  "updateTime": "2023-05-07 13:40:00"
}

POST /sku_test/_doc/8
{
  "id": "SKU008",
  "spuName": "有机鸡蛋",
  "spuGrade": "四级",
  "spuStandards": "10枚/盒",
  "isDeleted": false,
  "createTime": "2023-05-08 10:25:00",
  "updateTime": "2023-05-08 10:25:00"
}

POST /sku_test/_doc/9
{
  "id": "SKU009",
  "spuName": "进口牛奶",
  "spuGrade": "八级",
  "spuStandards": "1L/盒",
  "isDeleted": false,
  "createTime": "2023-05-09 15:50:00",
  "updateTime": "2023-05-09 15:50:00"
}

POST /sku_test/_doc/10
{
  "id": "SKU010",
  "spuName": "有机胡萝卜",
  "spuGrade": "六级",
  "spuStandards": "400g/袋",
  "isDeleted": false,
  "createTime": "2023-05-10 12:10:00",
  "updateTime": "2023-05-10 12:10:00"
}
```

## 示例查询

### 1. 基本查询 - 按ID查询

```json
GET /sku_test/_search
{
  "query": {
    "term": {
      "id": "SKU001"
    }
  }
}
```

### 2. 中文分词全文搜索 - 搜索商品名称

```json
GET /sku_test/_search
{
  "query": {
    "match": {
      "spuName": "有机"
    }
  }
}
```

### 3. 精确匹配关键词 - 使用keyword字段

```json
GET /sku_test/_search
{
  "query": {
    "term": {
      "spuName.keyword": "有机西红柿"
    }
  }
}
```

### 4. 精确匹配查询 - 按商品等级精确匹配

```json
GET /sku_test/_search
{
  "query": {
    "term": {
      "spuGrade": "三级"
    }
  }
}
```

### 5. 多值匹配查询 - 查询多个等级的商品

```json
GET /sku_test/_search
{
  "query": {
    "terms": {
      "spuGrade": ["一级", "二级", "三级"]
    }
  }
}
```

### 6. 日期范围查询 - 按创建时间范围查询

```json
GET /sku_test/_search
{
  "query": {
    "range": {
      "createTime": {
        "gte": "2023-05-01 00:00:00",
        "lte": "2023-05-05 23:59:59"
      }
    }
  }
}
```

### 7. 复合查询 - 组合多个条件

```json
GET /sku_test/_search
{
  "query": {
    "bool": {
      "must": [
        {
          "match": {
            "spuName": "进口"
          }
        },
        {
          "terms": {
            "spuGrade": ["三级", "四级", "五级", "六级", "七级", "八级", "九级", "十级"]
          }
        }
      ],
      "must_not": [
        {
          "term": {
            "isDeleted": true
          }
        }
      ]
    }
  }
}
```

### 8. 聚合查询 - 按商品等级统计数量

```json
GET /sku_test/_search
{
  "size": 0,
  "aggs": {
    "grade_count": {
      "terms": {
        "field": "spuGrade"
      }
    }
  }
}
```

### 9. 高级聚合 - 按商品规格分组并计算每组的平均等级

```json
GET /sku_test/_search
{
  "size": 0,
  "aggs": {
    "standards_group": {
      "terms": {
        "field": "spuStandards"
      },
      "aggs": {
        "avg_grade": {
          "avg": {
            "field": "spuGrade"
          }
        }
      }
    }
  }
}
```

### 10. 排序 - 按商品等级降序排列

```json
GET /sku_test/_search
{
  "query": {
    "match_all": {}
  },
  "sort": [
    {
      "spuGrade": {
        "order": "desc"
      }
    }
  ]
}
```

### 11. 分页查询 - 实现分页效果

```json
GET /sku_test/_search
{
  "from": 0,
  "size": 3,
  "query": {
    "match_all": {}
  },
  "sort": [
    {
      "createTime": {
        "order": "desc"
      }
    }
  ]
}
```

### 12. 前缀查询 - 查询ID前缀

```json
GET /sku_test/_search
{
  "query": {
    "prefix": {
      "id": "SKU00"
    }
  }
}
```

### 13. 通配符查询 - 使用通配符匹配

```json
GET /sku_test/_search
{
  "query": {
    "wildcard": {
      "spuStandards": "*kg*"
    }
  }
}
```

### 14. 模糊查询 - 允许拼写错误

```json
GET /sku_test/_search
{
  "query": {
    "fuzzy": {
      "spuName": {
        "value": "西红柿",
        "fuzziness": "AUTO"
      }
    }
  }
}
```

### 15. 高亮显示 - 搜索结果中高亮显示匹配的文本

```json
GET /sku_test/_search
{
  "query": {
    "match": {
      "spuName": "有机"
    }
  },
  "highlight": {
    "fields": {
      "spuName": {}
    }
  }
}
```

### 16. 布尔查询 - 查询已删除或等级为一级的商品

```json
GET /sku_test/_search
{
  "query": {
    "bool": {
      "should": [
        {
          "term": {
            "isDeleted": true
          }
        },
        {
          "term": {
            "spuGrade": "一级"
          }
        }
      ],
      "minimum_should_match": 1
    }
  }
}
```

## 故障排除

### Canal批量插入异常解决方案

如果遇到以下异常：
```
EasyEsException: bulkRequest exception
Caused by: java.lang.NullPointerException
at org.elasticsearch.action.DocWriteResponse.<init>
```

#### 原因分析：
1. **索引映射缺少null值处理**：某些字段可能为null导致ES响应解析失败
2. **EasyEs版本兼容性**：EasyEs与Elasticsearch版本不匹配
3. **索引不存在或损坏**：需要重建索引

#### 解决步骤：

**步骤1：删除现有索引**
```bash
curl -X DELETE "http://10.1.16.13:9200/sku_test" \
-H "Authorization: ApiKey VFNOdEhaZ0JYTTdVZFJhR1plQTE6UldLeGtkYmpRYkdsT0c5dFlIZi1mdw=="
```

**步骤2：使用修复后的映射重建索引**
索引映射已在上方更新，包含null_value处理。

**步骤3：验证索引创建**
```bash
curl -X GET "http://10.1.16.13:9200/sku_test/_mapping" \
-H "Authorization: ApiKey VFNOdEhaZ0JYTTdVZFJhR1plQTE6UldLeGtkYmpRYkdsT0c5dFlIZi1mdw=="
```

**步骤4：测试批量插入**
```bash
curl -X POST "http://10.1.16.13:9200/sku_test/_doc/test" \
-H "Authorization: ApiKey VFNOdEhaZ0JYTTdVZFJhR1plQTE6UldLeGtkYmpRYkdsT0c5dFlIZi1mdw==" \
-H "Content-Type: application/json" \
-d '{
  "id": "TEST001",
  "spuName": "测试商品",
  "spuGrade": null,
  "spuStandards": null,
  "isDeleted": false,
  "createTime": "2025-07-19 21:30:00",
  "updateTime": "2025-07-19 21:30:00"
}'
```

#### 代码修复建议：

**在CanalInsertHandler中添加空值检查：**
```java
private void insertBatch(List<SkuTest> skus) {
    // 添加数据验证
    skus.forEach(sku -> {
        if (sku.getSpuGrade() == null) {
            sku.setSpuGrade("未知等级");
        }
        if (sku.getSpuStandards() == null) {
            sku.setSpuStandards("标准规格");
        }
        if (sku.getSpuName() == null) {
            sku.setSpuName("未知商品");
        }
    });
    
    try {
        skuTestMapper.insertBatch(skus);
        log.info("批量插入成功，数量: {}", skus.size());
    } catch (Exception e) {
        log.error("批量插入失败，数据: {}", JSON.toJSONString(skus), e);
        throw e;
    }
}
```

#### 监控建议：

**添加健康检查：**
```bash
# 检查索引状态
curl -X GET "http://10.1.16.13:9200/_cluster/health/sku_test" \
-H "Authorization: ApiKey VFNOdEhaZ0JYTTdVZFJhR1plQTE6UldLeGtkYmpRYkdsT0c5dFlIZi1mdw=="

# 检查索引统计
curl -X GET "http://10.1.16.13:9200/sku_test/_stats" \
-H "Authorization: ApiKey VFNOdEhaZ0JYTTdVZFJhR1plQTE6UldLeGtkYmpRYkdsT0c5dFlIZi1mdw=="
```

## 重建索引步骤

由于发现了字段命名不一致的问题（同时存在驼峰命名和蛇形命名），需要重建索引以确保数据一致性。

### 步骤1：删除现有索引

```bash
curl -X DELETE "http://127.0.0.1:9200/sku_test" \
-H "Authorization: ApiKey VFNOdEhaZ0JYTTdVZFJhR1plQTE6UldLeGtkYmpRYkdsT0c5dFlIZi1mdw=="
```

### 步骤2：使用正确的映射创建索引

使用上面提供的索引创建语句重新创建索引。

### 步骤3：验证索引映射

```bash
curl -X GET "http://127.0.0.1:9200/sku_test/_mapping" \
-H "Authorization: ApiKey VFNOdEhaZ0JYTTdVZFJhR1plQTE6UldLeGtkYmpRYkdsT0c5dFlIZi1mdw=="
```

### 步骤4：重新插入数据

使用上面提供的示例数据重新插入。

## 字段命名规范说明

### 当前规范：统一使用驼峰命名

- ✅ `spuName` - 商品名称
- ✅ `spuGrade` - 商品等级  
- ✅ `spuStandards` - 商品规格
- ✅ `createTime` - 创建时间
- ✅ `updateTime` - 更新时间
- ✅ `isDeleted` - 是否删除

### 避免使用蛇形命名

- ❌ `spu_name` - 会导致字段不一致
- ❌ `spu_grade` - 会导致字段不一致
- ❌ `spu_standards` - 会导致字段不一致
- ❌ `create_time` - 会导致字段不一致
- ❌ `update_time` - 会导致字段不一致
- ❌ `is_deleted` - 会导致字段不一致

## 配置说明

在 `application.yml` 中，EasyEs配置已设置为：

```yaml
easy-es:
  global-config:
    db-config:
      # 关闭下划线转驼峰，保持字段名一致性
      map-underscore-to-camel-case: false
```

这样可以确保：
1. Java实体类的字段名与ES索引字段名完全一致
2. 避免字段名转换导致的查询问题
3. 提高代码的可维护性和可读性