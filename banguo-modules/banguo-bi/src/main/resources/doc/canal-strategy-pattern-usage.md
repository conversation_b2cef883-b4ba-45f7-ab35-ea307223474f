# Canal消息处理策略模式使用说明

## 设计概述

本项目使用策略模式（Strategy Pattern）来处理Canal消息的不同操作类型（INSERT/UPDATE/DELETE），实现了代码的解耦和扩展性。

## 架构设计

```
CanalSkuConsumer (消息消费者)
    ↓
CanalOperationHandlerManager (策略管理器)
    ↓
CanalOperationHandler (策略接口)
    ↓
具体处理器实现:
├── CanalInsertHandler (INSERT操作处理器)
├── CanalUpdateHandler (UPDATE操作处理器)
└── CanalDeleteHandler (DELETE操作处理器)
```

## 核心组件

### 1. 策略接口 (CanalOperationHandler)
定义了所有Canal操作处理器必须实现的方法：
- `handle(CanalMessageVo)`: 处理具体的Canal消息
- `getSupportedOperationType()`: 返回支持的操作类型

### 2. 策略管理器 (CanalOperationHandlerManager)
- 自动注册所有实现了 `CanalOperationHandler` 接口的处理器
- 根据消息中的操作类型路由到对应的处理器
- 提供统一的消息处理入口

### 3. 具体策略实现
- **CanalInsertHandler**: 处理INSERT操作，将新数据插入到Elasticsearch
- **CanalUpdateHandler**: 处理UPDATE操作，比较新旧数据并更新变化的字段
- **CanalDeleteHandler**: 处理DELETE操作，从Elasticsearch中删除对应数据

## 使用示例

### 1. INSERT操作示例
```json
{
  "type": "INSERT",
  "database": "banguo_product",
  "table": "sku",
  "data": [{
    "id": "123",
    "spu_name": "苹果",
    "spu_grade": "一级",
    "spu_standards": "5kg/箱"
  }]
}
```

处理流程：
1. CanalSkuConsumer 接收消息
2. CanalOperationHandlerManager 识别 type="INSERT"
3. 路由到 CanalInsertHandler
4. 转换数据格式并批量插入到ES

### 2. UPDATE操作示例
```json
{
  "type": "UPDATE",
  "database": "banguo_product",
  "table": "sku",
  "data": [{
    "id": "123",
    "spu_name": "红富士苹果",
    "spu_grade": "特级",
    "spu_standards": "5kg/箱"
  }],
  "old": [{
    "id": "123",
    "spu_name": "苹果",
    "spu_grade": "一级",
    "spu_standards": "5kg/箱"
  }]
}
```

处理流程：
1. CanalUpdateHandler 比较新旧数据
2. 识别变化字段：spu_name, spu_grade
3. 只更新变化的字段，提高性能

### 3. DELETE操作示例
```json
{
  "type": "DELETE",
  "database": "banguo_product",
  "table": "sku",
  "data": [{
    "id": "123",
    "spu_name": "红富士苹果",
    "spu_grade": "特级",
    "spu_standards": "5kg/箱"
  }]
}
```

处理流程：
1. CanalDeleteHandler 提取被删除记录的ID
2. 从ES中删除对应的文档

## 扩展新的操作类型

如需支持新的操作类型（如TRUNCATE），只需：

1. 在 `CanalOperationTypeEnum` 中添加新枚举值
2. 创建新的处理器实现 `CanalOperationHandler` 接口
3. 使用 `@Component` 注解让Spring自动注册

示例：
```java
@Component
public class CanalTruncateHandler implements CanalOperationHandler {
    
    @Override
    public void handle(CanalMessageVo canalMessageVo) {
        // 处理TRUNCATE操作
    }
    
    @Override
    public String getSupportedOperationType() {
        return CanalOperationTypeEnum.TRUNCATE.getCode();
    }
}
```

## 最佳实践

### 1. 异常处理
- 每个处理器都应该有适当的异常处理
- 使用统一的日志格式便于排查问题
- 考虑失败重试机制

### 2. 性能优化
- INSERT操作使用批量插入
- UPDATE操作只更新变化的字段
- 考虑使用异步处理提高吞吐量

### 3. 数据一致性
- 确保ES操作的幂等性
- 处理并发更新场景
- 监控数据同步状态

### 4. 监控和告警
- 记录处理耗时
- 监控处理成功率
- 设置异常告警

## 配置说明

在 `application.yml` 中可以配置相关参数：
```yaml
canal:
  sku:
    batch-size: 100  # 批量处理大小
    retry-times: 3   # 重试次数
    timeout: 30s     # 处理超时时间
```

## 总结

通过策略模式的设计，我们实现了：
- **解耦**: 不同操作类型的处理逻辑分离
- **扩展性**: 易于添加新的操作类型支持
- **可维护性**: 各个处理器职责单一，易于维护
- **可测试性**: 每个策略可以独立测试 