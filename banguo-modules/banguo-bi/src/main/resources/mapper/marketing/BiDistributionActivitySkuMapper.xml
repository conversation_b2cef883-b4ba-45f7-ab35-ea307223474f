<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.bi.mapper.BiDistributionActivityMapper">


    <select id="getPage" resultType="cn.xianlink.bi.domain.vo.BiDistributionActivitySkuVo">
        select
        s.id,
        s.activity_id,
        s.activity_start_time,
        s.activity_end_time,
        s.sku_id,
        s.category_path_name,
        s.supplier_id,
        s.distribution_item_amount,
        sku.brand,
        sku.short_producer,
        sku.spu_standards,
        sku.spu_net_weight,
        sku.price,
        sku.subsidy_amount,
        sku.business_type,
        sku.spu_name,
        sku.batch_type,
        sku.cooperation,
        sku.shouguang_vegetables,
        sf.file_url as imgUrl,
        sst.stock,
        sst.up_stock,
        sku.status,
        sku.sale_type,
        sku.spu_id,
        sku.category_id,
        sku.category_id_level2,
        sst.sold + sst.lock_stock as sold
        from ${marketingDB}.distribution_activity_sku s
        left join ${marketingDB}.distribution_activity da on s.activity_id = da.id and da.del_flag = 0
        left join ${productDB}.sku sku on s.sku_id = sku.id
        left join ${productDB}.supplier_sku ss on ss.id = sku.supplier_sku_id
        left join ${productDB}.supplier_sku_stock sst on ss.id = sst.supplier_sku_id
        left join (
        select
        supplier_sku_id,
        file_url,
        create_time,
        row_number() over (partition by supplier_sku_id order by create_time) as rn
        from ${productDB}.supplier_sku_file
        where type = 1 and del_flag = 0
        ) sf on sf.supplier_sku_id = sku.supplier_sku_id and sf.rn = 1
        <where>
            s.del_flag  = 0
            <if test="bo.saleType != null">
                and sku.sale_type = #{bo.saleType}
            </if>
            <if test="bo.activityStatus != null">
                and da.activity_status = #{bo.activityStatus}
            </if>
            <if test="bo.categoryPathName != null and bo.categoryPathName != ''">
                and s.category_path_name like concat('%', #{bo.categoryPathName}, '%')
            </if>
            <if test="bo.supplierId != null">
                and da.supplier_id = #{bo.supplierId}
            </if>
            <if test="bo.spuName != null and bo.spuName != ''">
                and s.spu_name like concat('%', #{bo.spuName}, '%')
            </if>
            <if test="bo.regionWhIdList != null and bo.regionWhIdList.size()>0">
                and sku.region_wh_id in
                <foreach collection="bo.regionWhIdList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        order by
        case
        when sku.status = 4 then 1
        when sku.status = 6 then 2
        when sku.status = 5 then 3
        when sku.status = 3 then 4
        else 5
        end,
        ss.is_out, ss.copy_sold desc, ss.id desc
    </select>

</mapper>
