<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.bi.mapper.marketing.BiActivityDiscountSummaryMapper">



    <select id="getSkuDiscount" resultType="cn.xianlink.bi.domain.BiActivityDiscountSummary">
        select s.*
        from  ${marketingDB}.mk_activity_discount_summary s
        where del_flag = 0
        and status = 2
        and exclude = 1
        <if test="bo.regionId != null">
            and owner_id = #{bo.regionId} and ownership_type = 3
        </if>
        <if test="bo.activityType != null">
            and activity_type = #{bo.activityType}
        </if>

        <!-- skuInfo 匹配 -->
        <if test="bo.skuInfo != null and bo.skuInfo.size() > 0">
            and scope_value in
            <foreach item="item" collection="bo.skuInfo" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <!-- 时间范围匹配 -->
        <if test="bo.saleDate != null">
            and (s.start_date &lt;= #{bo.saleDate} or s.start_date is null)
            and (s.end_date &gt;= #{bo.saleDate} or s.end_date is null)
        </if>
        <if test="bo.time != null">
            and (
            (s.effective_start_time &lt;= s.effective_end_time AND CURTIME() BETWEEN s.effective_start_time AND s.effective_end_time)
            OR
            (s.effective_start_time > s.effective_end_time AND (CURTIME()  &gt;= s.effective_start_time OR CURTIME() &lt;= s.effective_end_time)))
        </if>
    </select>
</mapper>
