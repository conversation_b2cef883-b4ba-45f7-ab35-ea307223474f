<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.bi.mapper.IBiOrderItemMapper">

    <select id="queryItemList" resultType="cn.xianlink.bi.domain.vo.BiOrderInfoVo">
        select  oi.*, (oi.spu_gross_weight * oi.`count` ) as totalGrossWeight , (oi.spu_net_weight * oi.`count` ) totalNetWeight from ${orderDB}.order_item oi where oi.order_id = #{orderId}
    </select>

    <select id="selectOrderInfoExportPage" resultType="cn.xianlink.bi.domain.bo.OrderItemExportSelectBo">
        select o.id as orderId,
        oi.id as orderItemId,
        oi.sale_date,
        oi.city_wh_id,
        oi.region_wh_id,
        oi.logistics_id,
        oi.logistics_id_level2,
        oi.buyer_name,
        oi.customer_id,
        oi.spu_name,
        oi.supplier_sku_id,
        oi.supplier_id,
        oi.price,
        oi.spu_gross_weight,
        oi.spu_net_weight,
        oi.count,
        oi.product_amount,
        oi.platform_service_amount,
        oi.platform_freight_amount,
        oi.platform_freight_amount_level2,
        oi.base_freight_amount,
        oi.region_freight_amount,
        oi.platform_service_free_amount,
        oi.freight_total_free_amount,
        o.financial_service_amount,
        o.product_amount as totalProductAmount,
        oi.pay_time,
        o.code as orderCode,
        oi.settle_time,
        oi.settle_number as settleItemCount,
        bc.name as customer_name, bc.alias as customer_alias,
        bcw.city_wh_name, brw.region_wh_name, brl.logistics_name, brl2.logistics_name as logistics_name_level2,
        ss.spu_standards, ss.brand, ss.spu_grade, ss.producer,
        oi.product_free_amount  productFreeAmount,
        oi.count * oi.spu_gross_weight as totalSpuGrossWeight,
        oi.count * oi.spu_net_weight as totalSpuNetWeight
        from ${orderDB}.order_item oi
        inner join ${orderDB}.`order` o on oi.order_id = o.id
        left join ${basicDB}.bas_city_wh bcw on oi.city_wh_id = bcw.id
        left join ${basicDB}.bas_region_wh brw on oi.region_wh_id = brw.id
        left join ${basicDB}.bas_region_logistics brl on oi.logistics_id = brl.id
        left join ${basicDB}.bas_region_logistics brl2 on oi.logistics_id_level2 = brl2.id
        left join ${basicDB}.bas_customer bc on oi.customer_id = bc.id
        left join ${productDB}.supplier_sku ss on oi.supplier_sku_id = ss.id
        <where>
            o.del_flag = 0
            <if test="bo.spuName != null and bo.spuName != ''">
                and oi.spu_name like concat('%', #{bo.spuName}, '%')
            </if>
            <if test="bo.orderCode != null and bo.orderCode != ''">
                and o.code like concat('%', #{bo.orderCode}, '%')
            </if>
            <if test="bo.status != null and bo.status != ''">
                and o.status = #{bo.status}
            </if>
            <if test="bo.customerId != null">
                and o.customer_id = #{bo.customerId}
            </if>
            <if test="bo.cityWhId != null">
                and o.city_wh_id = #{bo.cityWhId}
            </if>
            <if test="bo.regionWhId != null">
                and o.region_wh_id = #{bo.regionWhId}
            </if>
            <if test="bo.createName != null and bo.createName != ''">
                and o.create_name = #{bo.createName}
            </if>
            <if test="bo.saleDateStart != null">
                and o.sale_date &gt;= #{bo.saleDateStart}
            </if>
            <if test="bo.saleDateEnd != null">
                and o.sale_date &lt;= #{bo.saleDateEnd}
            </if>
            <if test="bo.createTimeStart != null">
                and o.create_time &gt;= #{bo.createTimeStart}
            </if>
            <if test="bo.createTimeEnd != null">
                and o.create_time &lt;= #{bo.createTimeEnd}
            </if>
            <if test="bo.payTimeStart != null">
                and o.pay_time &gt;= #{bo.payTimeStart}
            </if>
            <if test="bo.payTimeEnd != null">
                and o.pay_time &lt;= #{bo.payTimeEnd}
            </if>
            <if test="bo.logisticsId != null">
                and o.logistics_id = #{bo.logisticsId}
            </if>
            <if test="bo.logisticsIdLevel2 != null">
                and o.logistics_id_level2 = #{bo.logisticsIdLevel2}
            </if>
            <if test="bo.cityWhIds != null and bo.cityWhIds.size() != 0">
                and o.city_wh_id in
                <foreach collection="bo.cityWhIds" item="cityWhId" open="(" close=")" separator="," >
                    #{cityWhId}
                </foreach>
            </if>
            <if test="bo.regionWhIds != null and bo.regionWhIds.size() != 0">
                and o.region_wh_id in
                <foreach collection="bo.regionWhIds" item="regionWhId" open="(" close=")" separator="," >
                    #{regionWhId}
                </foreach>
            </if>
            <if test="bo.cityWhIdStr != null and bo.cityWhIdStr != ''">
                ${bo.cityWhIdStr}
            </if>
        </where>
        order by o.create_time desc
    </select>



    <select id="advanceList" resultType="cn.xianlink.bi.domain.vo.BiOrderInfoVo">
        select o.id as orderId,
        oi.id as orderItemId,
        oi.sale_date,
        oi.city_wh_id,
        oi.region_wh_id,
        oi.logistics_id,
        oi.logistics_id_level2,
        oi.buyer_name,
        oi.customer_id,
        oi.spu_name,
        oi.supplier_sku_id,
        oi.supplier_id,
        oi.price,
        oi.spu_gross_weight,
        (oi.spu_gross_weight * oi.count) as totalGrossWeight ,
        oi.spu_net_weight,
        oi.count,
        oi.product_amount,
        oi.platform_service_amount,
        oi.platform_freight_amount,
        oi.platform_freight_amount_level2,
        oi.base_freight_amount,
        oi.region_freight_amount,
        oi.platform_service_free_amount,
        oi.freight_total_free_amount,
        o.financial_service_amount,
        o.product_amount as totalProductAmount,
        oi.pay_time,
        oi.work_status,
        o.code as orderCode,
        oi.settle_time,
        su.simple_code as supplierSimpleCode,
        oi.settle_number as settleItemCount,
        oi.create_time as createTime,
        bc.name as customer_name, bc.alias as customer_alias, bc.admin_code as customer_admin_code,
        bcw.city_wh_name, brw.region_wh_name, brl.logistics_name, brl2.logistics_name as logistics_name_level2,
        ss.spu_standards, ss.brand, ss.spu_grade, ss.producer,
        sum(ifnull(rpd.refund_product_amount, 0)) as loss_amount,
        oi.product_free_amount as productFreeAmount
        from ${orderDB}.order_item oi
        inner join ${orderDB}.`order` o on oi.order_id = o.id
        left join ${basicDB}.bas_supplier su on su.id = oi.supplier_id
        left join ${productDB}.sku sku on sku.id = oi.sku_id
        left join ${basicDB}.bas_city_wh bcw on oi.city_wh_id = bcw.id
        left join ${basicDB}.bas_region_wh brw on oi.region_wh_id = brw.id
        left join ${basicDB}.bas_region_logistics brl on oi.logistics_id = brl.id
        left join ${basicDB}.bas_region_logistics brl2 on oi.logistics_id_level2 = brl2.id
        left join ${basicDB}.bas_customer bc on oi.customer_id = bc.id
        left join ${productDB}.supplier_sku ss on oi.supplier_sku_id = ss.id
        left join ${orderDB}.refund_product_detail rpd on oi.id = rpd.order_item_id and rpd.refund_type = 5 and rpd.refund_status != 2
        <where>
            o.del_flag = 0
            and ( oi.status = 'ALREADY' or oi.status = 'FINISH' or ( oi.status = 'CANCEL' and (oi.cancel_type = 'OUT' or oi.cancel_type = 'FEW') ) )
            <if test="bo.spuName != null and bo.spuName != ''">
                and sku.full_name like concat('%', #{bo.spuName}, '%')
            </if>
            <if test="bo.buyerName != null and bo.buyerName != ''">
                and oi.buyer_name like concat('%', #{bo.buyerName}, '%')
            </if>
            <if test="bo.orderCode != null and bo.orderCode != ''">
                and o.code like concat('%', #{bo.orderCode}, '%')
            </if>
            <if test="bo.supplierSimpleCode != null and bo.supplierSimpleCode != ''">
                and su.simple_code like concat('%',#{bo.supplierSimpleCode} , '%')
            </if>
            <if test="bo.status != null and bo.status != ''">
                and o.status = #{bo.status}
            </if>
            <if test="bo.customerId != null">
                and o.customer_id = #{bo.customerId}
            </if>
            <if test="bo.cityWhId != null">
                and o.city_wh_id = #{bo.cityWhId}
            </if>
            <if test="bo.regionWhId != null">
                and o.region_wh_id = #{bo.regionWhId}
            </if>
            <if test="bo.createName != null and bo.createName != ''">
                and o.create_name = #{bo.createName}
            </if>
            <if test="bo.saleDateStart != null">
                and o.sale_date &gt;= #{bo.saleDateStart}
            </if>
            <if test="bo.saleDateEnd != null">
                and o.sale_date &lt;= #{bo.saleDateEnd}
            </if>
            <if test="bo.createTimeStart != null">
                and o.create_time &gt;= #{bo.createTimeStart}
            </if>
            <if test="bo.createTimeEnd != null">
                and o.create_time &lt;= #{bo.createTimeEnd}
            </if>
            <if test="bo.payTimeStart != null">
                and o.pay_time &gt;= #{bo.payTimeStart}
            </if>
            <if test="bo.payTimeEnd != null">
                and o.pay_time &lt;= #{bo.payTimeEnd}
            </if>
            <if test="bo.logisticsId != null">
                and o.logistics_id = #{bo.logisticsId}
            </if>
            <if test="bo.logisticsIdLevel2 != null">
                and o.logistics_id_level2 = #{bo.logisticsIdLevel2}
            </if>
            <if test="bo.cityWhIds != null and bo.cityWhIds.size() != 0">
                and o.city_wh_id in
                <foreach collection="bo.cityWhIds" item="cityWhId" open="(" close=")" separator="," >
                    #{cityWhId}
                </foreach>
            </if>
            <if test="bo.regionWhIds != null and bo.regionWhIds.size() != 0">
                and o.region_wh_id in
                <foreach collection="bo.regionWhIds" item="regionWhId" open="(" close=")" separator="," >
                    #{regionWhId}
                </foreach>
            </if>

            <if test="bo.workStatus != null and bo.workStatus != ''">
                and oi.work_status = #{bo.workStatus}
            </if>
        </where>
        group by oi.id
        order by o.create_time desc
    </select>


    <select id="advanceTotal" resultType="cn.xianlink.bi.domain.vo.BiOrderInfoVo">
        select
        sum(oi.price) as price,
        sum(oi.spu_gross_weight) as spuGrossWeight,
        sum(oi.spu_gross_weight * oi.count) as totalGrossWeight,
        sum(oi.spu_net_weight) as spuNetWeight,
        sum(oi.count) as count,
        sum(oi.product_amount) as productAmount,
        sum(oi.platform_service_amount) as platformServiceAmount,
        sum(oi.platform_freight_amount) as platformFreightAmount,
        sum(oi.platform_freight_amount_level2) as platformFreightAmountLevel2,
        sum(oi.base_freight_amount) as baseFreightAmount,
        sum(oi.region_freight_amount) as  regionFreightAmount,
        sum(oi.platform_service_free_amount) as platformServiceFreeAmount,
        sum(oi.freight_total_free_amount) as freightTotalFreeAmount,
        sum(oi.product_free_amount) as productFreeAmount

        from ${orderDB}.order_item oi
        inner join ${orderDB}.`order` o on oi.order_id = o.id
        left join ${basicDB}.bas_supplier su on su.id = oi.supplier_id
        left join ${productDB}.sku sku on sku.id = oi.sku_id
        <where>
            o.del_flag = 0
            and ( oi.status = 'ALREADY' or oi.status = 'FINISH' or ( oi.status = 'CANCEL' and (oi.cancel_type = 'OUT' or oi.cancel_type = 'FEW') ) )
            <if test="bo.spuName != null and bo.spuName != ''">
                and sku.full_name like concat('%', #{bo.spuName}, '%')
            </if>
            <if test="bo.buyerName != null and bo.buyerName != ''">
                and oi.buyer_name like concat('%', #{bo.buyerName}, '%')
            </if>
            <if test="bo.orderCode != null and bo.orderCode != ''">
                and o.code like concat('%', #{bo.orderCode}, '%')
            </if>
            <if test="bo.supplierSimpleCode != null and bo.supplierSimpleCode != ''">
                and su.simple_code like concat('%',#{bo.supplierSimpleCode} , '%')
            </if>
            <if test="bo.status != null and bo.status != ''">
                and o.status = #{bo.status}
            </if>
            <if test="bo.customerId != null">
                and o.customer_id = #{bo.customerId}
            </if>
            <if test="bo.cityWhId != null">
                and o.city_wh_id = #{bo.cityWhId}
            </if>
            <if test="bo.regionWhId != null">
                and o.region_wh_id = #{bo.regionWhId}
            </if>
            <if test="bo.createName != null and bo.createName != ''">
                and o.create_name = #{bo.createName}
            </if>
            <if test="bo.saleDateStart != null">
                and o.sale_date &gt;= #{bo.saleDateStart}
            </if>
            <if test="bo.saleDateEnd != null">
                and o.sale_date &lt;= #{bo.saleDateEnd}
            </if>
            <if test="bo.createTimeStart != null">
                and o.create_time &gt;= #{bo.createTimeStart}
            </if>
            <if test="bo.createTimeEnd != null">
                and o.create_time &lt;= #{bo.createTimeEnd}
            </if>
            <if test="bo.payTimeStart != null">
                and o.pay_time &gt;= #{bo.payTimeStart}
            </if>
            <if test="bo.payTimeEnd != null">
                and o.pay_time &lt;= #{bo.payTimeEnd}
            </if>
            <if test="bo.logisticsId != null">
                and o.logistics_id = #{bo.logisticsId}
            </if>
            <if test="bo.logisticsIdLevel2 != null">
                and o.logistics_id_level2 = #{bo.logisticsIdLevel2}
            </if>
            <if test="bo.cityWhIds != null and bo.cityWhIds.size() != 0">
                and o.city_wh_id in
                <foreach collection="bo.cityWhIds" item="cityWhId" open="(" close=")" separator="," >
                    #{cityWhId}
                </foreach>
            </if>
            <if test="bo.regionWhIds != null and bo.regionWhIds.size() != 0">
                and o.region_wh_id in
                <foreach collection="bo.regionWhIds" item="regionWhId" open="(" close=")" separator="," >
                    #{regionWhId}
                </foreach>
            </if>

            <if test="bo.workStatus != null">
                and oi.work_status = #{bo.workStatus}
            </if>
        </where>

    </select>
    <select id="getLossRate" resultType="cn.xianlink.bi.api.domain.vo.RemoteOrderLossRateVo">
        SELECT
        count(lo.id)/count(i.id)*100 as lossCountRate,
        sum(ifnull(lo.loss_amount,0))/sum(i.product_amount)*100 as lossRate
        FROM ${orderDB}.order_item i
        left join ${orderDB}.report_loss_order lo on lo.order_item_id = i.id and lo.del_flag =0
        and lo.loss_status > 0
        where i.`sale_date` BETWEEN ADDDATE(CURRENT_DATE,-7) and ADDDATE(CURRENT_DATE,-1)
        and i.status in ('ALREADY','FINISH')
        and i.del_flag = 0
    </select>

    <select id="getCustomerLossRate" resultType="cn.xianlink.bi.api.domain.vo.RemoteCustomerLossRateVo">
        SELECT
            lo.customer_id,
            COUNT(lo.losId) / NULLIF(COUNT(lo.order_item_id), 0) * 100 AS lossCountRate,
            SUM(IFNULL(lo.loss_amount, 0)) / NULLIF(SUM(lo.product_amount), 0) * 100 AS lossRate
        FROM (
            SELECT
                i.id AS order_item_id,
                i.customer_id,
                i.product_amount,
                lo.loss_amount,
                lo.id as losId,
                ROW_NUMBER() OVER (PARTITION BY i.customer_id ORDER BY i.sale_date DESC) AS rn
            FROM
                ${orderDB}.order_item i
            LEFT JOIN ${orderDB}.report_loss_order lo
                ON lo.order_item_id = i.id AND lo.del_flag <![CDATA[<=]]> 1 AND lo.loss_status > 0
            WHERE
                i.del_flag = 0
                AND i.status IN ('ALREADY', 'FINISH')
                AND i.customer_id IN
                <foreach collection="customerIds" item="customerId" open="(" close=")" separator=",">
                    #{customerId}
                </foreach>
        ) lo
        WHERE lo.rn <![CDATA[<=]]> 10
        GROUP BY lo.customer_id;
    </select>

    <select id="getSaleDateProductInfo" resultType="cn.xianlink.bi.domain.vo.order.BiSaleDateProductInfoVo">
        SELECT
        s.id,
        s.spu_name AS spuName,
        s.spu_standards AS spuStandards,
        s.spu_grade AS spuGrade,
        s.producer AS producer,
        s.brand AS brand,
        s.price AS price,
        s.spu_gross_weight AS spuGrossWeight,
        s.spu_net_weight AS spuNetWeight,
        oi.buyer_name AS buyerName,
        COUNT(DISTINCT oi.order_id) AS orderCount,
        SUM(oi.count) AS orderItemCount,
        SUM(oi.count - oi.refund_out_count - oi.refund_few_count) AS receivedItemCount,
        CASE
        WHEN SUM(oi.product_amount) = 0 THEN 0
        ELSE ROUND(sum(case when r.loss_status=0 then 0 else r.loss_amount end) / sum(case when r.loss_status=0 then 0 else oi.product_amount end) * 100, 2)
        END AS lossRate
        FROM
        ${productDB}.supplier_sku s
        LEFT JOIN
        ${orderDB}.order_item oi ON s.id = oi.supplier_sku_id AND s.del_flag = 0
        LEFT JOIN
        ${orderDB}.report_loss_order r ON oi.id = r.order_item_id AND r.del_flag = 0
        WHERE
        oi.del_flag = 0
        AND (r.loss_status >= 0 or r.loss_status is null)
        AND oi.status IN ('ALREADY', 'FINISH')
        <if test="bo.saleDate != null">
            AND oi.sale_date = #{bo.saleDate}
        </if>
        AND oi.status IN ('ALREADY', 'FINISH')
        <if test="bo.cityWhId != null">
            AND oi.city_wh_id = #{bo.cityWhId}
        </if>
        <if test="bo.logisticsId != null">
            AND oi.logistics_id = #{bo.logisticsId}
        </if>
        <if test="bo.logisticsIdLevel2 != null">
            AND oi.logistics_id_level2 = #{bo.logisticsIdLevel2}
        </if>
        <if test="bo.spuName != null and bo.spuName != ''">
            AND s.full_name like concat ('%',#{bo.spuName},'%')
        </if>
        <if test="bo.cityWhIds != null and bo.cityWhIds.size() > 0">
            AND oi.city_wh_id in
            <foreach collection="bo.cityWhIds" item="cityWhId" open="(" close=")" separator="," >
                #{cityWhId}
            </foreach>
        </if>
        <if test="bo.regionWhIds != null and bo.regionWhIds.size() > 0">
            AND oi.region_wh_id in
            <foreach collection="bo.regionWhIds" item="regionWhId" open="(" close=")" separator="," >
                #{regionWhId}
            </foreach>
        </if>
        <if test="bo.placeId != null">
            AND oi.place_id = #{bo.placeId}
        </if>
        <if test="bo.placeIdLevel2 != null">
            AND oi.place_id_level2 = #{bo.placeIdLevel2}
        </if>
        group by s.id
        ORDER BY s.id
    </select>

    <select id="getSaleDateProductInfoTotal"
            resultType="cn.xianlink.bi.domain.vo.order.BiSaleDateProductTotalVo">
        SELECT
        COUNT(DISTINCT s.id) AS skuCount,
        COUNT(DISTINCT oi.order_id) AS orderCountTotal,
        SUM(oi.count) AS orderItemCountTotal,
        SUM(oi.count - oi.refund_out_count - oi.refund_few_count) AS receivedItemCountTotal,
        CASE
        WHEN SUM(oi.product_amount) = 0 THEN 0
        ELSE ROUND(sum(case when r.loss_status=0 then 0 else r.loss_amount end) / sum(case when r.loss_status=0 then 0 else oi.product_amount end) * 100, 2)
        END AS lossRate
        FROM
        ${productDB}.supplier_sku s
        LEFT JOIN
        ${orderDB}.order_item oi ON s.id = oi.supplier_sku_id AND s.del_flag = 0
        LEFT JOIN
        ${orderDB}.report_loss_order r ON oi.id = r.order_item_id AND r.del_flag = 0
        WHERE
        oi.del_flag = 0
        AND (r.loss_status >= 0 or r.loss_status is null)
        <if test="bo.saleDate != null">
            AND oi.sale_date = #{bo.saleDate}
        </if>
        AND oi.status IN ('ALREADY', 'FINISH')
        <if test="bo.cityWhId != null">
            AND oi.city_wh_id = #{bo.cityWhId}
        </if>
        <if test="bo.logisticsId != null">
            AND oi.logistics_id = #{bo.logisticsId}
        </if>
        <if test="bo.logisticsIdLevel2 != null">
            AND oi.logistics_id_level2 = #{bo.logisticsIdLevel2}
        </if>
        <if test="bo.spuName != null and bo.spuName != ''">
            AND s.full_name like concat ('%',#{bo.spuName},'%')
        </if>
        <if test="bo.cityWhIds != null and bo.cityWhIds.size() > 0">
            AND oi.city_wh_id in
            <foreach collection="bo.cityWhIds" item="cityWhId" open="(" close=")" separator="," >
                #{cityWhId}
            </foreach>
        </if>
        <if test="bo.regionWhIds != null and bo.regionWhIds.size() > 0">
            AND oi.region_wh_id in
            <foreach collection="bo.regionWhIds" item="regionWhId" open="(" close=")" separator="," >
                #{regionWhId}
            </foreach>
        </if>
        <if test="bo.placeId != null">
            AND oi.place_id = #{bo.placeId}
        </if>
        <if test="bo.placeIdLevel2 != null">
            AND oi.place_id_level2 = #{bo.placeIdLevel2}
        </if>
    </select>

    <select id="getCustomerList" resultType="cn.xianlink.bi.domain.vo.order.BiSaleDateCustomerInfoVo">
        SELECT
        concat((case when gd.place_level2_code is not null then gd.place_level2_code
        when oi.place_name_level2 is not null then oi.place_name_level2 else '' end),
        (case when oi.delivery_number != 0 then oi.delivery_number
        else gd.delivery_number end))  as deliveryNumber,
        oi.customer_id AS customerId,
        c.name AS customerName,
        COUNT(DISTINCT oi.order_id) AS orderCount,
        SUM(oi.count) AS orderItemCount,
        SUM(oi.count - oi.refund_out_count - oi.refund_few_count) AS receivedItemCount,
        CASE
        WHEN SUM(oi.product_amount) = 0 THEN 0
        ELSE ROUND(sum(case when r.loss_status=0 then 0 else r.loss_amount end) / sum(case when r.loss_status=0 then 0 else oi.product_amount end) * 100, 2)
        END AS lossRate,
        oi.logistics_id,
        l.logistics_name
        FROM
        ${orderDB}.order_item oi
        LEFT JOIN
        ${orderDB}.sort_goods_detail gd ON oi.id = gd.order_item_id AND gd.del_flag = 0
        LEFT JOIN
        ${orderDB}.report_loss_order r ON oi.id = r.order_item_id AND r.del_flag = 0
        LEFT JOIN
        ${basicDB}.bas_customer c ON oi.customer_id = c.id
        LEFT JOIN
        ${basicDB}.bas_region_logistics l ON oi.logistics_id = l.id
        WHERE
        oi.del_flag = 0
        AND (r.loss_status >= 0 or r.loss_status is null)
        AND oi.status IN ('ALREADY', 'FINISH')
        <if test="bo.id != null">
            AND oi.supplier_sku_id = #{bo.id}
        </if>
        <if test="bo.saleDate != null">
            AND oi.sale_date = #{bo.saleDate}
        </if>
        <if test="bo.cityWhId != null">
            AND oi.city_wh_id = #{bo.cityWhId}
        </if>
        <if test="bo.logisticsId != null">
            AND oi.logistics_id = #{bo.logisticsId}
        </if>
        <if test="bo.logisticsIdLevel2 != null">
            AND oi.logistics_id_level2 = #{bo.logisticsIdLevel2}
        </if>
        <if test="bo.cityWhIds != null and bo.cityWhIds.size() > 0">
            AND oi.city_wh_id in
            <foreach collection="bo.cityWhIds" item="cityWhId" open="(" close=")" separator="," >
                #{cityWhId}
            </foreach>
        </if>
        <if test="bo.regionWhIds != null and bo.regionWhIds.size() > 0">
            AND oi.region_wh_id in
            <foreach collection="bo.regionWhIds" item="regionWhId" open="(" close=")" separator="," >
                #{regionWhId}
            </foreach>
        </if>
        <if test="bo.placeId != null">
            AND oi.place_id = #{bo.placeId}
        </if>
        <if test="bo.placeIdLevel2 != null">
            AND oi.place_id_level2 = #{bo.placeIdLevel2}
        </if>
        GROUP BY  oi.customer_id, oi.place_name_level2
    </select>

    <select id="getCustomerOrderList" resultType="cn.xianlink.bi.domain.vo.order.BiSaleDateCustomerOrderVo">
        select
        oi.customer_id AS customerId,
        c.name AS customerName,
        su.username as salesmanName,
        count(distinct oi.supplier_sku_id) AS skuCount,
        sum(oi.count) AS orderItemCount,
        sum(oi.count - oi.refund_out_count - oi.refund_few_count) AS receivedItemCount,
        CASE
        WHEN SUM(oi.product_amount) = 0 THEN 0
        ELSE ROUND(sum(case when r.loss_status=0 then 0 else r.loss_amount end) / sum(case when r.loss_status=0 then 0 else oi.product_amount end) * 100, 2)
        END AS lossRate
        from
        ${orderDB}.order_item oi
        LEFT join
        ${orderDB}.report_loss_order r ON oi.id = r.order_item_id
        left join
        ${basicDB}.bas_customer c ON oi.customer_id = c.id
        join
        `${systemDB}`.sys_user su on c.salesman_code = su.user_code
        <if test="bo.customerName != null and bo.customerName != ''">
            join `${systemDB}`.sys_user su2 on c.admin_code = su2.user_code and su2.user_type = 'customer_user'
        </if>
        where
        oi.del_flag = 0
        AND (r.loss_status >= 0 or r.loss_status is null)
        AND oi.status IN ('ALREADY', 'FINISH')
        <if test="bo.saleDate != null">
            AND oi.sale_date = #{bo.saleDate}
        </if>
        <if test="bo.cityWhId != null">
            AND oi.city_wh_id = #{bo.cityWhId}
        </if>
        <if test="bo.logisticsId != null">
            AND oi.logistics_id= #{bo.logisticsId}
        </if>
        <if test="bo.logisticsIdLevel2 != null">
            AND oi.logistics_id_level2 = #{bo.logisticsIdLevel2}
        </if>
        <if test="bo.customerName != null and bo.customerName != ''">
            AND (c.name like concat ('%',#{bo.customerName},'%')
            or su2.phone_no like concat ('%',#{bo.customerName},'%'))
        </if>
        <if test="bo.cityWhIds != null and bo.cityWhIds.size() > 0">
            AND oi.city_wh_id in
            <foreach collection="bo.cityWhIds" item="cityWhId" open="(" close=")" separator="," >
                #{cityWhId}
            </foreach>
        </if>
        <if test="bo.regionWhIds != null and bo.regionWhIds.size() > 0">
            AND oi.region_wh_id in
            <foreach collection="bo.regionWhIds" item="regionWhId" open="(" close=")" separator="," >
                #{regionWhId}
            </foreach>
        </if>
        group by oi.customer_id
        order by oi.customer_id
    </select>


    <select id="getCustomerOrderTotal" resultType="cn.xianlink.bi.domain.vo.order.BiSaleDateProductTotalVo">
        select
        count(distinct oi.order_id) AS orderCountTotal,
        count(distinct oi.supplier_sku_id,oi.customer_id) AS skuCount,
        sum(oi.count) AS orderItemCountTotal,
        sum(oi.count - oi.refund_out_count - oi.refund_few_count) AS receivedItemCountTotal,
        CASE
        WHEN SUM(oi.product_amount) = 0 THEN 0
        ELSE ROUND(sum(case when r.loss_status=0 then 0 else r.loss_amount end) / sum(case when r.loss_status=0 then 0 else oi.product_amount end) * 100, 2)
        END AS lossRate
        from
        ${orderDB}.order_item oi
        LEFT JOIN
        ${orderDB}.report_loss_order r ON oi.id = r.order_item_id AND r.del_flag = 0
        left join
        ${basicDB}.bas_customer c ON oi.customer_id = c.id
        <if test="bo.customerName != null and bo.customerName != ''">
            join `${systemDB}`.sys_user su2 on c.admin_code = su2.user_code and su2.user_type = 'customer_user'
        </if>
        where
        oi.del_flag = 0
        AND (r.loss_status >= 0 or r.loss_status is null)
        AND oi.status IN ('ALREADY', 'FINISH')
        <if test="bo.saleDate != null">
            AND oi.sale_date = #{bo.saleDate}
        </if>
        <if test="bo.cityWhId != null">
            AND oi.city_wh_id = #{bo.cityWhId}
        </if>
        <if test="bo.logisticsId != null">
            AND oi.logistics_id= #{bo.logisticsId}
        </if>
        <if test="bo.logisticsIdLevel2 != null">
            AND oi.logistics_id_level2 = #{bo.logisticsIdLevel2}
        </if>
        <if test="bo.customerName != null and bo.customerName != ''">
            AND (c.name like concat ('%',#{bo.customerName},'%')
            or su2.phone_no like concat ('%',#{bo.customerName},'%'))
        </if>
        <if test="bo.cityWhIds != null and bo.cityWhIds.size() > 0">
            AND oi.city_wh_id in
            <foreach collection="bo.cityWhIds" item="cityWhId" open="(" close=")" separator="," >
                #{cityWhId}
            </foreach>
        </if>
        <if test="bo.regionWhIds != null and bo.regionWhIds.size() > 0">
            AND oi.region_wh_id in
            <foreach collection="bo.regionWhIds" item="regionWhId" open="(" close=")" separator="," >
                #{regionWhId}
            </foreach>
        </if>
    </select>

    <resultMap id="saleDateCustomerOrderInfo" type="cn.xianlink.bi.domain.vo.order.BiSaleDateCustomerOrderInfoVo">
        <id property="customerId" column="customer_id"/>
        <result property="customerName" column="customerName"/>
        <result property="phoneNo" column="phone_no"/>
        <collection property="biSaleDateProductInfoVos" ofType="cn.xianlink.bi.domain.vo.order.BiSaleDateProductInfoVo">
            <id property="id" column="supplier_sku_id"/>
            <result property="spuName" column="spuName"/>
            <result property="spuStandards" column="spuStandards"/>
            <result property="spuGrade" column="spuGrade"/>
            <result property="producer" column="producer"/>
            <result property="brand" column="brand"/>
            <result property="orderItemCount" column="orderItemCount"/>
            <result property="receivedItemCount" column="receivedItemCount"/>
            <result property="lossRate" column="lossRate"/>
        </collection>
    </resultMap>
    <select id="getSaleDateCustomerOrderInfo" resultMap="saleDateCustomerOrderInfo">
        select
        oi.customer_id,
        c.name AS customerName,
        su.phone_no,
        oi.supplier_sku_id,
        sum(oi.count) AS orderItemCount,
        sum(oi.count - oi.refund_out_count - oi.refund_few_count) AS receivedItemCount,
        CASE
        WHEN SUM(oi.product_amount) = 0 THEN 0
        ELSE ROUND(sum(case when r.loss_status=0 then 0 else r.loss_amount end) / sum(case when r.loss_status=0 then 0 else oi.product_amount end) * 100, 2)
        END AS lossRate,
        s.spu_name AS spuName,
        s.spu_standards AS spuStandards,
        s.spu_grade AS spuGrade,
        s.producer AS producer,
        s.brand AS brand
        from
        ${orderDB}.order_item oi
        left join
        ${productDB}.supplier_sku s ON oi.supplier_sku_id = s.id
        LEFT JOIN
        ${orderDB}.report_loss_order r ON oi.id = r.order_item_id AND r.del_flag = 0
        left join
        ${basicDB}.bas_customer c ON oi.customer_id = c.id
        join
        `${systemDB}`.sys_user su on c.admin_code = su.user_code and su.user_type = 'customer_user'
        <where>
            oi.del_flag = 0
            AND (r.loss_status >= 0 or r.loss_status is null)
            AND oi.status IN ('ALREADY', 'FINISH')
            <if test="bo.customerId != null">
                AND oi.customer_id = #{bo.customerId}
            </if>
            <if test="bo.logisticsId != null">
                AND oi.logistics_id = #{bo.logisticsId}
            </if>
            <if test="bo.logisticsIdLevel2 != null">
                AND oi.logistics_id_level2 = #{bo.logisticsIdLevel2}
            </if>
            <if test="bo.saleDate != null">
                AND oi.sale_date = #{bo.saleDate}
            </if>
            <if test="bo.cityWhIds != null and bo.cityWhIds.size() > 0">
                AND oi.city_wh_id in
                <foreach collection="bo.cityWhIds" item="cityWhId" open="(" close=")" separator="," >
                    #{cityWhId}
                </foreach>
            </if>
            <if test="bo.regionWhIds != null and bo.regionWhIds.size() > 0">
                AND oi.region_wh_id in
                <foreach collection="bo.regionWhIds" item="regionWhId" open="(" close=")" separator="," >
                    #{regionWhId}
                </foreach>
            </if>
        </where>
        group by oi.supplier_sku_id
    </select>

    <sql id="financeOrderItemReportSql">
        select
        it.id as id
        ,it.pay_time as pay_date   <!-- 订单支付时间 -->
        ,it.pay_time   <!-- 订单支付时间 -->
        ,it.customer_id            <!-- 客户ID -->
        ,it.sale_date               <!-- 销售日 -->
        ,it.pay_channel              <!-- 支付渠道 -->
        ,CASE
        WHEN it.`pay_channel` = 'weixinB2b' THEN '微信支付'
        WHEN it.`pay_channel` = 'pinganCloudWX' THEN '平安支付'
        WHEN it.`pay_channel` = 'pinganCloudWXProgramQL' THEN '平安支付'
        WHEN it.`pay_channel` = 'pinganCloudLarge' THEN '转账支付'
        WHEN it.`pay_channel` = 'pinganCloudBal' THEN '余额支付'
        WHEN it.`pay_channel` = 'innerZero' THEN '零支付'
        WHEN it.`pay_channel` IS NULL THEN ''
        ELSE it.`pay_channel`
        END AS pay_channel_name      <!-- 支付渠道名称 -->
        ,it.order_code             <!-- 客户订单号 -->
        ,'支付订单' AS order_status_name      <!-- 订单状态名称 -->
        ,it.work_status             <!-- 商品履约状态 -->
        ,CASE
        WHEN it.work_status = 'WAIT_SEND' THEN '待送货'
        WHEN it.work_status = 'WAIT_SHARE' THEN '待分货'
        WHEN it.work_status = 'SHARE_ING' THEN '分货中'
        WHEN it.work_status = 'WAIT_EXTRACT' THEN '待提货'
        WHEN it.work_status = 'EXTRACT_FINISH' THEN '已提货'
        WHEN it.work_status = 'STOCKOUT_ING' THEN '待确认'
        WHEN it.work_status IS NULL THEN ''
        ELSE it.work_status
        END AS work_status_name            <!-- 商品履约状态名称 -->
        ,it.supplier_id             <!-- 供应商ID -->
        ,it.region_wh_id             <!-- 总仓ID -->
        ,it.city_wh_id             <!--  城市仓ID -->
        ,case when (it.place_id_level2 is not null and it.place_id_level2 != 0) then '子仓'
        else '一级城市仓' end as city_wh_type      <!--  城市仓类别 -->
        ,it.place_name_level2           <!--  子仓名称 -->
        ,it.logistics_id             <!-- 物流线ID -->
        ,it.buyer_name                  <!-- 采购员 -->
        ,it.supplier_sku_id      <!-- 供应商商品批次ID -->
        ,it.category_id_level2        <!-- 商品分类 -->
        ,it.service_rule_record_id      <!-- 城市仓(服务费)分润规则记录id -->
        ,it.freight_rule_record_id      <!-- 城市仓(运费)分润规则记录id -->
        ,it.final_price as price                              <!-- 单价，最终单价 -->
        ,it.`count` * it.spu_gross_weight as total_gross_weight       <!-- 订单项的总毛重 -->
        ,it.`count` * it.spu_net_weight as total_net_weight           <!-- 订单项的总净重 -->
        ,it.`count`                        <!-- 订单项的件数 -->
        ,it.product_amount    <!--商品金额-->
        ,it.product_free_amount    <!--优惠商品金额-->
        ,it.platform_service_amount                        <!-- 服务费(优惠前) -->
        ,it.platform_freight_amount                        <!-- 普通运费(优惠前) -->
        ,it.base_freight_amount                        <!-- 基采运费(优惠前) -->
        ,it.platform_freight_amount_level2                        <!-- 子仓运费(优惠前) -->
        ,it.region_freight_amount                        <!-- 地采运费(优惠前) -->
        ,it.platform_service_amount - it.platform_service_free_amount as platform_service_amount_after   <!-- 实付服务费(服务费优惠后) -->
        ,it.platform_service_free_amount                   <!-- 服务费优惠金额 -->
        ,it.freight_total_free_amount                        <!-- 运费优惠金额 -->
        ,it.financial_service_amount                     <!-- 商品金融手续费 -->
        ,IF(it.distribution_amount + it.distribution_tax_amount > 0, 1, 0) as is_distribution <!-- 是否分销 -->
        ,IF(it.distribution_amount + it.distribution_tax_amount > 0, '是', '否') as is_distribution_name <!-- 是否分销名称 -->
        ,(it.region_subsidy_amount + it.sku_subsidy_amount)  as region_subsidy_amount                     <!-- 补贴金额 -->
        ,0.5 as supplier_withdraw_fee         <!-- 供应商提现手续费 -->
        ,it.distribution_amount + it.distribution_tax_amount as commission_amount         <!-- 佣金 -->
        ,it.cancel_type    <!--订单取消原因-->
        ,CASE
            WHEN it.cancel_type = 'TIMEOUT' THEN '超时未支付'
            WHEN it.cancel_type = 'SKU_DOWN' THEN '商品下架'
            WHEN it.cancel_type = 'LOGISTICS_OUT' THEN '吨位不足'
            WHEN it.cancel_type = 'STOCK_OUT' THEN '库存不足'
            WHEN it.cancel_type = 'BUYER' THEN '买家取消'
            WHEN it.cancel_type = 'REGION' THEN '总仓取消'
            WHEN it.cancel_type = 'REPLACE_SUPPLIER' THEN '换供应商'
            WHEN it.cancel_type = 'OUT' THEN '缺货'
            WHEN it.cancel_type = 'FEW' THEN '少货'
            ELSE it.cancel_type
        END as cancel_type_name    <!--订单取消原因名称-->
        from ${orderDB}.order_item it
        <if test="(bo.supplierSplitNo != null and bo.supplierSplitNo != '') or bo.cashStatus != null">
            <!-- 结算相关表 -->
            left join ${tradeDB}.trade_acc_trans tct on tct.trans_no = it.order_code and it.del_flag = 0<!-- 订单号 -->
            and tct.trans_type = 'OP' <!-- OP-订单 -->
            and tct.org_type = 2    <!--  2-供应商，0-总仓  -->
            and tct.sku_id = it.supplier_sku_id <!-- supplier_sku_id -->
            and tct.del_flag = 0
        </if>
        <if test="bo.cashStatus != null">
            <!-- 提现表 -->
            left join ${tradeDB}.trade_acc_cash tac on tct.cash_no = tac.cash_no and tac.del_flag = 0
        </if>
        <where>
            <include refid="financeOrderItemReportWhere"/>
        </where>
        order by it.order_id desc
    </sql>

    <sql id="financeOrderItemReportWhere">
            and it.del_flag = 0
            and it.pay_time is not null
            <if test="bo.payTimeStart != null and bo.payTimeStart != ''">
                and it.pay_time >= #{bo.payTimeStart}
            </if>
            <if test="bo.payTimeEnd != null and bo.payTimeEnd != ''">
                and it.pay_time &lt;= #{bo.payTimeEnd}
            </if>
            <if test="bo.saleDateStart != null and bo.saleDateStart != ''">
                and it.sale_date >= #{bo.saleDateStart}
            </if>
            <if test="bo.saleDateEnd != null and bo.saleDateEnd != ''">
                and it.sale_date &lt;= #{bo.saleDateEnd}
            </if>
            <if test="bo.payChannel != null and bo.payChannel != ''">
                and it.pay_channel = #{bo.payChannel}
            </if>
            <if test="bo.tradeNo != null and bo.tradeNo != ''">
                and it.order_code in (
                    select order_no from ${tradeDB}.trade_pay where trade_no = #{bo.tradeNo} and del_flag = 0
                )
            </if>
            <if test="bo.orderCode != null and bo.orderCode != ''">
                and it.order_code like concat(#{bo.orderCode},'%')
            </if>
            <if test="bo.supplierSplitNo != null and bo.supplierSplitNo != ''">
                and tct.split_no like concat(#{bo.supplierSplitNo},'%')
            </if>
            <if test="bo.supplierId != null">
                and it.supplier_id = #{bo.supplierId}
            </if>
            <if test="bo.regionWhIdList != null and bo.regionWhIdList.size() > 0">
                and it.region_wh_id in
                <foreach collection="bo.regionWhIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="bo.cityWhId != null">
                and it.city_wh_id = #{bo.cityWhId}
            </if>
            <if test="bo.placeIdLevel2 != null">
                and it.place_id_level2 = #{bo.placeIdLevel2}
            </if>
            <if test="bo.logisticsId != null">
                and it.logistics_id  = #{bo.logisticsId}
            </if>
            <if test="bo.isDistribution != null and bo.isDistribution == 1">
                AND it.distribution_amount + it.distribution_tax_amount > 0
            </if>
            <if test="bo.isDistribution != null and bo.isDistribution == 0">
                AND it.distribution_amount + it.distribution_tax_amount &lt;= 0
            </if>
            <if test="bo.supplierSkuIdList != null and bo.supplierSkuIdList.size > 0">
                and it.supplier_sku_id in
                <foreach collection="bo.supplierSkuIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="bo.cashStatus != null">
                AND tac.status = #{bo.cashStatus}
            </if>
            <if test="bo.orderCodeList != null and bo.orderCodeList.size() > 0">
                AND it.order_code in
                <foreach collection="bo.orderCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
    </sql>

    <select id="financeOrderItemReportList" resultType="cn.xianlink.bi.domain.vo.FinanceOrderItemReportVo">
        <include refid="financeOrderItemReportSql"/>
    </select>

    <select id="getRefundDistributionAmountTotal" resultType="cn.xianlink.bi.domain.vo.RefundDistributionAmountVo">
        select rd.order_item_id,sum(refund_distribution_amount + refund_distribution_tax_amount) as refund_distribution_amount_total
        from ${orderDB}.refund_product_detail rd
        where rd.del_flag = 0
        and rd.refund_status in (0,1)
        and rd.order_item_id in
        <foreach collection="orderItemIdList" item="item" open="(" separator="," close=")">
                #{item}
        </foreach>
        GROUP BY rd.order_item_id
    </select>

    <select id="getOrderCodeByRegionWhSplitNo" resultType="java.lang.String">
        select DISTINCT tct1.trans_no
        from ${tradeDB}.trade_acc_trans tct1
        where tct1.trans_type = 'OP' <!-- OP-订单 -->
        and tct1.org_type = 0    <!--  2-供应商，0-总仓 -->
        and tct1.del_flag = 0
        and tct1.split_no = #{regionWhSplitNo}
    </select>

    <select id="getTradeAccTransByOrderCode" resultType="cn.xianlink.bi.domain.vo.FinanceOrderItemReportVo">
        select tct1.trans_no as orderCode, GROUP_CONCAT(IF(reg.id is null, concat(tct1.split_no, '(分销)'), tct1.split_no) SEPARATOR ', ') as region_wh_split_no
        from ${tradeDB}.trade_acc_trans tct1
        left join ${basicDB}.bas_region_wh reg on reg.region_wh_code = tct1.org_code
        where tct1.trans_type = 'OP' <!-- OP-订单 -->
        and tct1.org_type = 0    <!--  2-供应商，0-总仓 -->
        and tct1.del_flag = 0
        and tct1.trans_no in
        <foreach collection="orderCodeList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by tct1.trans_no
    </select>

    <select id="getTradePayByOrderCode" resultType="cn.xianlink.bi.domain.vo.FinanceOrderItemReportVo">
        select
        order_no as order_code
        ,trade_no                 <!-- 支付单号 -->
        from ${tradeDB}.trade_pay
        where del_flag = 0
        and order_no in
        <foreach collection="orderCodeList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getTransCashByOrderCode" resultType="cn.xianlink.bi.domain.vo.FinanceOrderItemReportVo">
        select
        tct.sku_id as supplier_sku_id  <!-- 供应商批次ID -->
        ,tct.trans_no as order_code    <!-- 订单号 -->
        ,tct.split_no  as supplier_split_no        <!-- 平安单号（商品），供应商-平安分账唯一编号 -->
        ,tct.avail_time as avail_day                        <!-- 供应商结算日，年月日 -->
        ,tct.avail_time as avail_time                        <!-- 供应商结算时间，时分秒 -->
        ,tct.avail_no                                   <!-- 供应商结算单号 -->
        ,tct.cash_no                                     <!-- 供应商提现单号 -->
        ,tac.create_time as apply_day                        <!-- 供应商提现申请日，年月日 -->
        ,tac.create_time as apply_time                        <!-- 供应商提现申请时间，时分秒 -->
        ,tac.cash_check_time as cash_day                        <!-- 供应商提现日，年月日 -->
        ,tac.cash_check_time as cash_time                      <!-- 供应商提现时间，时分秒 -->
        ,tac.status as cash_status                       <!-- 供应商提现状态 -->
        ,CASE
        WHEN tac.status = 2 THEN '可提现'
        WHEN tac.status = 3 THEN '提现完成'
        WHEN tac.status = 4 THEN '提现待审核'
        WHEN tac.status = 5 THEN '已过期'
        WHEN tac.status = 6 THEN '已取消'
        WHEN tac.status IS NULL THEN ''
        ELSE tac.status
        END AS cash_status_name                       <!-- 供应商提现状态 -->
        <!-- 结算相关表 -->
        from ${tradeDB}.trade_acc_trans tct
        <!-- 提现表 -->
        left join ${tradeDB}.trade_acc_cash tac on tct.cash_no = tac.cash_no and tac.del_flag = 0
        where tct.del_flag = 0
        and tct.trans_type = 'OP' <!-- OP-订单 -->
        and tct.org_type = 2    <!--  2-供应商，0-总仓  -->
        and tct.trans_no in
        <foreach collection="orderCodeList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getLessBlame" resultType="cn.xianlink.bi.domain.vo.FinanceOrderItemBlameVo">
        select
        it.id as order_item_id     <!-- 订单项id -->
        ,brd.create_time as blame_time   <!-- 判责时间 -->
        ,2 as source_type              <!-- 判责类型 -->
        ,'少货' as source_type_name       <!-- 判责类型名称 -->
        ,br.blame_status <!-- 判责状态 -->
        ,CASE
            WHEN br.blame_status = 0 THEN '待确认'
            WHEN br.blame_status = 1 THEN '待判责'
            WHEN br.blame_status = 2 THEN '已判责'
            WHEN br.blame_status = 3 THEN '部分确认'
            WHEN br.blame_status = 4 THEN '全部确认'
            WHEN br.blame_status = 5 THEN '已完成'
            WHEN br.blame_status = 6 THEN '申诉中'
            WHEN br.blame_status = 7 THEN '已作废'
            WHEN br.blame_status IS NULL THEN ''
            ELSE br.blame_status
        END AS blame_status_name   <!-- 判责状态名称 -->
        ,br.responsibility_type      <!-- 承担方 -->
        ,CASE
            WHEN br.responsibility_type = 0 THEN '无承担方'
            WHEN br.responsibility_type = 1 THEN '供应商'
            WHEN br.responsibility_type = 2 THEN '城市仓'
            WHEN br.responsibility_type = 3 THEN '总仓'
            WHEN br.responsibility_type IS NULL THEN ''
            ELSE br.responsibility_type
        END AS responsibility_type_name        <!-- 承担方名称 -->
        ,rd.stockout_count * it.spu_gross_weight as blame_gross_weight <!-- 判责毛重 -->
        ,br.total_count as blame_count  <!-- 判责件数 -->
        ,(br.product_amount/br.total_count) * rd.stockout_count as blame_amount         <!-- 判责金额 -->
        ,rd.refund_product_amount as refund_amount   <!-- 退款金额 -->
        ,rd.refund_time           <!-- 退款时间 -->
        ,rd.refund_status   <!-- 退款状态 -->
        ,CASE
            WHEN rd.refund_status = 0 THEN '退款中'
            WHEN rd.refund_status = 1 THEN '已退款'
            WHEN rd.refund_status = 2 THEN '退款失败'
            WHEN rd.refund_status IS NULL THEN ''
            ELSE rd.refund_status
        END AS refund_status_name   <!-- 退款状态名称 -->
        ,rd.refund_subsidy_free_amount as refund_subsidy_free_amount  <!-- 退补贴金额 -->
        from ${orderDB}.order_item it
        inner join ${orderDB}.refund_product_detail rd on rd.order_item_id = it.id and it.del_flag = 0 and rd.del_flag = 0
        left join ${orderDB}.refund_record rr on rr.id = rd.refund_record_id and rr.del_flag = 0
        left join ${orderDB}.blame_record br on rr.source_code = br.source_code and br.del_flag = 0
        left join ${orderDB}.blame_record_detail brd on brd.blame_record_id = br.id and brd.del_flag = 0
        <where>
            and br.source_type = 2
            and rd.refund_type = 3
            and rd.refund_status in (0,1)
            <if test="bo.orderItemIdList != null and bo.orderItemIdList.size > 0">
                and it.id in
                <foreach collection="bo.orderItemIdList" item="orderItemId" open="(" separator="," close=")">
                    #{orderItemId}
                </foreach>
            </if>
            <if test="bo.pageType != null and bo.pageType == 2">
                <!-- 承担方是供应商 -->
                and br.responsibility_type = 1
            </if>
        </where>
        group by it.id,rr.source_code
    </select>

    <select id="getOtherBlame" resultType="cn.xianlink.bi.domain.vo.FinanceOrderItemBlameVo">
        select
        it.id as order_item_id     <!-- 订单项id -->
        ,rd.create_time as blame_time   <!-- 判责时间 -->
        ,rd.refund_type as  source_type         <!-- 判责类型，退款类型(1取消订单 2缺货 3少货 4差额退款 5报损) -->
        ,CASE
            WHEN rd.refund_type = 2 THEN '缺货'
            WHEN rd.refund_type = 3 THEN '少货'
            WHEN rd.refund_type = 4 THEN '差额退款'
            WHEN rd.refund_type = 5 THEN '报损'
            WHEN rd.refund_type = 6 THEN '改价'
            WHEN rd.refund_type IS NULL THEN ''
            ELSE rd.refund_type
        END AS source_type_name          <!-- 判责类型名称-->
        ,1 as responsibility_type       <!-- 承担方(0无承担方 1供应商 2城市仓 3总仓) -->
        ,'供应商' AS responsibility_type_name        <!-- 承担方名称 -->
        ,rd.refund_amount   <!-- 退款金额   -->
        ,rd.refund_time           <!-- 退款时间 -->
        ,rd.refund_status   <!-- 退款状态 -->
        ,CASE
            WHEN rd.refund_status = 0 THEN '退款中'
            WHEN rd.refund_status = 1 THEN '已退款'
            WHEN rd.refund_status = 2 THEN '退款失败'
            WHEN rd.refund_status IS NULL THEN ''
        ELSE rd.refund_status
        END AS refund_status_name   <!-- 退款状态名称 -->
        ,rd.refund_subsidy_free_amount  <!-- 退补贴金额 -->

        from ${orderDB}.order_item it
        inner join ${orderDB}.refund_product_detail rd on rd.order_item_id = it.id and it.del_flag = 0 and rd.del_flag = 0
        left join ${orderDB}.refund_record rr on rr.id = rd.refund_record_id and rr.del_flag = 0
        <where>
            AND rd.refund_type in (2,4)
            AND rd.refund_status in (0,1)
            <if test="bo.orderItemIdList != null and bo.orderItemIdList.size > 0">
                and it.id in
                <foreach collection="bo.orderItemIdList" item="orderItemId" open="(" separator="," close=")">
                    #{orderItemId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getLossBlame" resultType="cn.xianlink.bi.domain.vo.FinanceOrderItemBlameVo">
        select
        it.id as order_item_id     <!-- 订单项id -->
        ,max(rd.create_time) as blame_time   <!-- 判责时间 -->
        ,5 as  source_type         <!-- 判责类型，退款类型(1取消订单 2缺货 3少货 4差额退款 5报损) -->
        ,'报损' AS source_type_name          <!-- 判责类型名称-->
        ,1 as responsibility_type       <!-- 承担方(0无承担方 1供应商 2城市仓 3总仓) -->
        ,'供应商' AS responsibility_type_name        <!-- 承担方名称 -->
        ,ROUND(sum(rd.refund_amount/(it.product_amount - it.product_free_amount) * (it.`count` * it.spu_gross_weight)), 2) as blame_gross_weight    <!-- 判责毛重 -->
        ,CEIL(sum(rd.refund_amount/(it.product_amount - it.product_free_amount) * it.`count`)) as blame_count     <!-- 判责件数 -->
        ,sum(rd.refund_amount) as refund_amount   <!-- 退款金额   -->
        ,max(rd.refund_time) as refund_time          <!-- 退款时间 -->
        ,sum(rd.refund_subsidy_free_amount) as refund_subsidy_free_amount <!-- 退补贴金额 -->
        ,MIN(rd.refund_status) as refund_status  <!-- 退款状态 -->
        ,CASE
            WHEN MIN(rd.refund_status) = 0 THEN '退款中'
            WHEN MIN(rd.refund_status) = 1 THEN '已退款'
            WHEN MIN(rd.refund_status) = 2 THEN '退款失败'
            WHEN MIN(rd.refund_status) IS NULL THEN ''
            ELSE MIN(rd.refund_status)
        END AS refund_status_name   <!-- 退款状态名称 -->
        ,ls.loss_status as blame_status     <!-- 判责状态 -->
        ,CASE
            WHEN ls.loss_status = -3 THEN '创建订单'
            WHEN ls.loss_status = -2 THEN '完成分货'
            WHEN ls.loss_status = -1 THEN '取消'
            WHEN ls.loss_status = 0 THEN '待审核'
            WHEN ls.loss_status = 1 THEN '全额通过'
            WHEN ls.loss_status = 2 THEN '减额通过'
            WHEN ls.loss_status = 3 THEN '驳回'
            WHEN ls.loss_status = 4 THEN '申诉中'
            WHEN ls.loss_status = 5 THEN '已介入'
            WHEN ls.loss_status = 6 THEN '已完成'
            ELSE ls.loss_status
        END AS blame_status_name    <!-- 判责状态名称 -->

        from ${orderDB}.order_item it
        INNER join ${orderDB}.report_loss_order ls on ls.order_item_id = it.id and it.del_flag = 0 and ls.del_flag = 0
        left join ${orderDB}.refund_product_detail rd on rd.order_item_id = it.id and rd.del_flag = 0
            AND rd.refund_type = 5
            AND rd.refund_status in (0,1)
        <where>
            <if test="bo.orderItemIdList != null and bo.orderItemIdList.size > 0">
                and it.id in
                <foreach collection="bo.orderItemIdList" item="orderItemId" open="(" separator="," close=")">
                    #{orderItemId}
                </foreach>
            </if>
        </where>
        group by it.id
    </select>

    <select id="financeOrderItemReportTotal" resultType="cn.xianlink.bi.domain.vo.FinanceOrderItemReportVo">
        select
        sum(it.`count` * it.spu_gross_weight) as total_gross_weight  <!-- 订单项的总毛重合计 -->
        ,sum(it.`count` * it.spu_net_weight) as total_net_weight   <!-- 订单项的总净重合计 -->
        ,sum(it.`count`) as count  <!-- 订单项的件数合计 -->
        ,sum(IF(reg.type = 0,it.product_amount - it.product_free_amount,0)) as ground_product_amount_after  <!--地采商品金额合计-->
        ,sum(IF(reg.type = 1,it.product_amount - it.product_free_amount,0)) as proxy_product_amount_after  <!--代采商品金额合计-->
        ,sum(it.product_amount) as product_amount  <!--商品金额合计-->
        ,sum(it.product_free_amount) as product_free_amount   <!--优惠商品金额合计-->
        ,sum(it.platform_service_amount) as platform_service_amount   <!-- 服务费(优惠前)合计 -->
        ,sum(it.platform_freight_amount) as platform_freight_amount  <!-- 普通运费(优惠前)合计 -->
        ,sum(it.base_freight_amount) as base_freight_amount     <!-- 基采运费(优惠前)合计 -->
        ,sum(it.platform_freight_amount_level2) as platform_freight_amount_level2   <!-- 子仓运费(优惠前)合计 -->
        ,sum(it.region_freight_amount) as region_freight_amount      <!-- 地采运费(优惠前)合计 -->
        ,sum(it.platform_service_free_amount) as platform_service_free_amount      <!-- 服务费优惠金额合计 -->
        ,sum(it.platform_service_amount - it.platform_service_free_amount) as platform_service_amount_after    <!-- 实付服务费(服务费优惠后)合计 -->
        ,sum(it.freight_total_free_amount) as freight_total_free_amount  <!-- 运费优惠金额合计 -->
        ,sum(it.financial_service_amount) as financial_service_amount     <!-- 商品金融手续费合计 -->
        ,sum(it.region_subsidy_amount + it.sku_subsidy_amount) as  region_subsidy_amount               <!-- 补贴金额合计 -->
        ,sum(it.distribution_amount + it.distribution_tax_amount - IF(rd_sum.refund_commission_amount is null,0,rd_sum.refund_commission_amount)) as commission_amount        <!-- 佣金合计 -->
        ,0 as blame_gross_weight_total <!-- 判责毛重合计 -->
        ,0 as blame_count_total <!-- 判责件数合计 -->
        ,0 as blame_amount_total   <!-- 判责金额合计 -->
        ,0 as refund_amount_total <!-- 退款金额合计 -->
        ,0 as refund_subsidy_free_amount_total <!-- 退补贴金额合计 -->

        from ${orderDB}.order_item it
        left join ${basicDB}.bas_region_wh reg on reg.id = it.region_wh_id and it.del_flag = 0
        <if test="(bo.supplierSplitNo != null and bo.supplierSplitNo != '') or bo.cashStatus != null">
            <!-- 结算相关表 -->
            left join ${tradeDB}.trade_acc_trans tct on tct.trans_no = it.order_code and it.del_flag = 0<!-- 订单号 -->
            and tct.trans_type = 'OP' <!-- OP-订单 -->
            and tct.org_type = 2    <!--  2-供应商，0-总仓  -->
            and tct.sku_id = it.supplier_sku_id <!-- supplier_sku_id -->
            and tct.del_flag = 0
        </if>
        <if test="bo.cashStatus != null">
            <!-- 提现表 -->
            left join ${tradeDB}.trade_acc_cash tac on tct.cash_no = tac.cash_no and tac.del_flag = 0
        </if>
        LEFT JOIN (
            select rd.order_item_id,sum(refund_distribution_amount + refund_distribution_tax_amount) as refund_commission_amount
            from ${orderDB}.refund_product_detail rd
            where rd.del_flag = 0
            and rd.refund_status in (0,1)
            <if test="bo.saleDateStart != null and bo.saleDateStart != ''">
                and rd.sale_date >= #{bo.saleDateStart}
            </if>
            <if test="bo.saleDateEnd != null and bo.saleDateEnd != ''">
                and rd.sale_date &lt;= #{bo.saleDateEnd}
            </if>
        GROUP BY rd.order_item_id
        ) rd_sum on rd_sum.order_item_id = it.id
        <where>
            <include refid="financeOrderItemReportWhere"/>
        </where>
    </select>

    <select id="getLessBlameTotal" resultType="cn.xianlink.bi.domain.vo.FinanceOrderItemBlameVo">
        select
            sum(rd.stockout_count * it.spu_gross_weight) as blame_gross_weight <!-- 判责毛重合计 -->
            ,sum(br.total_count) as blame_count <!-- 判责件数合计 -->
            ,sum((br.product_amount/br.total_count) * rd.stockout_count)  as  blame_amount   <!-- 判责金额合计 -->
            ,sum(rd.refund_product_amount) as refund_amount <!-- 退款金额合计 -->
            ,sum(rd.refund_subsidy_free_amount) as  refund_subsidy_free_amount <!-- 退补贴金额合计 -->
        from ${orderDB}.refund_product_detail rd
        <!--##########主要做过滤的表 start########-->
        inner join ${orderDB}.order_item it on rd.order_item_id = it.id and rd.del_flag = 0 and it.del_flag = 0
            and rd.refund_type = 3
            and rd.refund_status in (0,1)
        <if test="(bo.supplierSplitNo != null and bo.supplierSplitNo != '') or bo.cashStatus != null">
            <!-- 结算相关表 -->
            left join ${tradeDB}.trade_acc_trans tct on tct.trans_no = it.order_code and it.del_flag = 0<!-- 订单号 -->
            and tct.trans_type = 'OP' <!-- OP-订单 -->
            and tct.org_type = 2    <!--  2-供应商，0-总仓  -->
            and tct.sku_id = it.supplier_sku_id <!-- supplier_sku_id -->
            and tct.del_flag = 0
        </if>
        <if test="bo.cashStatus != null">
            <!-- 提现表 -->
            left join ${tradeDB}.trade_acc_cash tac on tct.cash_no = tac.cash_no and tac.del_flag = 0
        </if>
        <!--##########主要做过滤的表 end########-->
        left join ${orderDB}.refund_record rr on rr.id = rd.refund_record_id and rr.del_flag = 0
        left join ${orderDB}.blame_record br on rr.source_code = br.source_code and br.del_flag = 0
            and br.source_type = 2
        left join ${orderDB}.blame_record_detail brd on brd.blame_record_id = br.id and brd.del_flag = 0
        <where>
            <include refid="financeOrderItemReportWhere"/>
            <if test="bo.pageType != null and bo.pageType == 2">
                <!-- 承担方是供应商 -->
                and br.responsibility_type = 1
            </if>
        </where>
    </select>

    <select id="getOtherBlameTotal" resultType="cn.xianlink.bi.domain.vo.FinanceOrderItemBlameVo">
        select
        sum(rd.refund_amount) as refund_amount <!-- 退款金额合计 -->
        ,sum(rd.refund_subsidy_free_amount) as refund_subsidy_free_amount <!-- 退补贴金额合计 -->
        from ${orderDB}.refund_product_detail rd
        <!--##########主要做过滤的表 start########-->
        inner join ${orderDB}.order_item it on rd.order_item_id = it.id and rd.del_flag = 0
            AND rd.refund_type in (2,4)
            AND rd.refund_status in (0,1)
        <if test="(bo.supplierSplitNo != null and bo.supplierSplitNo != '') or bo.cashStatus != null">
            <!-- 结算相关表 -->
            left join ${tradeDB}.trade_acc_trans tct on tct.trans_no = it.order_code and it.del_flag = 0<!-- 订单号 -->
            and tct.trans_type = 'OP' <!-- OP-订单 -->
            and tct.org_type = 2    <!--  2-供应商，0-总仓  -->
            and tct.sku_id = it.supplier_sku_id <!-- supplier_sku_id -->
            and tct.del_flag = 0
        </if>
        <if test="bo.cashStatus != null">
            <!-- 提现表 -->
            left join ${tradeDB}.trade_acc_cash tac on tct.cash_no = tac.cash_no and tac.del_flag = 0
        </if>
        <!--##########主要做过滤的表 end########-->
        left join ${orderDB}.refund_record rr on rr.id = rd.refund_record_id and rr.del_flag = 0
        <where>
            <include refid="financeOrderItemReportWhere"/>
        </where>
    </select>

    <select id="getLossBlameTotal" resultType="cn.xianlink.bi.domain.vo.FinanceOrderItemBlameVo">
        select
        sum(t1.blame_gross_weight) as blame_gross_weight <!-- 判责毛重合计 -->
        ,sum(t1.blame_count) as blame_count <!-- 判责件数合计 -->
        ,sum(t1.refund_amount) as refund_amount <!-- 退款金额合计 -->
        ,sum(t1.refund_subsidy_free_amount) as refund_subsidy_free_amount <!-- 退补贴金额合计 -->
        from (
            select
            ROUND(sum(rd.refund_amount/(it.product_amount - it.product_free_amount) * (it.`count` * it.spu_gross_weight)), 2) as blame_gross_weight    <!-- 判责毛重 -->
            ,CEIL(sum(rd.refund_amount/(it.product_amount - it.product_free_amount) * it.`count`)) as blame_count     <!-- 判责件数 -->
            ,sum(rd.refund_amount) as refund_amount  <!-- 退款金额   -->
            ,sum(rd.refund_subsidy_free_amount) as refund_subsidy_free_amount <!-- 退补贴金额 -->
            from ${orderDB}.report_loss_order ls
            <!--##########主要做过滤的表 start########-->
            INNER join ${orderDB}.order_item it on ls.order_item_id = it.id and ls.del_flag = 0
            <if test="(bo.supplierSplitNo != null and bo.supplierSplitNo != '') or bo.cashStatus != null">
                <!-- 结算相关表 -->
                left join ${tradeDB}.trade_acc_trans tct on tct.trans_no = it.order_code and it.del_flag = 0<!-- 订单号 -->
                and tct.trans_type = 'OP' <!-- OP-订单 -->
                and tct.org_type = 2    <!--  2-供应商，0-总仓  -->
                and tct.sku_id = it.supplier_sku_id <!-- supplier_sku_id -->
                and tct.del_flag = 0
            </if>
            <if test="bo.cashStatus != null">
                <!-- 提现表 -->
                left join ${tradeDB}.trade_acc_cash tac on tct.cash_no = tac.cash_no and tac.del_flag = 0
            </if>
            <!--##########主要做过滤的表 end########-->
            left join ${orderDB}.refund_product_detail rd on rd.order_item_id = it.id and rd.del_flag = 0
                AND rd.refund_type = 5
                AND rd.refund_status in (0,1)
            <where>
                <include refid="financeOrderItemReportWhere"/>
            </where>
             group by it.id
        ) t1
    </select>

</mapper>