package cn.xianlink.marketing.dubbo;


import cn.xianlink.basic.api.RemoteRegionWhService;
import cn.xianlink.basic.api.domain.vo.RemoteRegionWhVo;
import cn.xianlink.common.api.util.SaleDateUtil;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.marketing.api.RemoteMkActivityDiscountSummaryService;
import cn.xianlink.marketing.api.bo.RemoteMkActivitySkuBo;
import cn.xianlink.marketing.api.bo.RemoteMkActivitySkuDiscountBo;
import cn.xianlink.marketing.api.dto.RemoteMkActivityRecordDTO;
import cn.xianlink.marketing.api.vo.RemoteMkActivityDiscountInfoVo;
import cn.xianlink.marketing.api.vo.RemoteMkActivitySkuDiscountVo;
import cn.xianlink.marketing.api.vo.RemoteMkActivitySkuSearchVo;
import cn.xianlink.marketing.domain.MkActivityDiscountSummary;
import cn.xianlink.marketing.domain.bo.MkActivityRecordBo;
import cn.xianlink.marketing.mapper.MkActivityDiscountSummaryMapper;
import cn.xianlink.marketing.service.IMkActivityRecordService;
import cn.xianlink.product.api.RemoteSupplierSkuService;
import cn.xianlink.product.api.domain.vo.RemoteSkuInfoForActivityVo;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: banguo-parent-ms
 * @description: 活动匹配远程调用
 * @author: weijian
 **/

@RequiredArgsConstructor
@Service
@DubboService
@CustomLog
public class RemoteMkActivityDiscountSummaryServiceImpl implements RemoteMkActivityDiscountSummaryService {

    private final MkActivityDiscountSummaryMapper activityDiscountSummaryMapper;

    private final IMkActivityRecordService mkActivityRecordService;

    @DubboReference
    private final RemoteRegionWhService remoteRegionWhService;

    @DubboReference
    private final RemoteSupplierSkuService remoteSupplierSkuService;

    /**
     * 匹配商品活动id集合
     */
    @Override
    public List<RemoteMkActivitySkuDiscountVo> getSkuDiscountIdList(RemoteMkActivitySkuDiscountBo bo){
        List<RemoteMkActivitySkuDiscountVo> vos = new ArrayList<>();
        if (ObjectUtils.isNull(bo) || com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(bo.getSupplierSkuList())){
            return vos;
        }
        List<Long> skuIds = bo.getSupplierSkuList().stream().map(RemoteMkActivitySkuBo::getSupplierSkuId).toList();
        List<RemoteSkuInfoForActivityVo> supplierSkuList = remoteSupplierSkuService.queryActivityInfoList(skuIds);
        if (CollectionUtils.isEmpty(supplierSkuList)){
            return vos;
        }
        //组装商品信息
        List<String> skuInfo = new ArrayList<>();
        Map<Long, List<Integer>> skuActivityMap = new HashMap<>();
        Map<Long, List<String>> skuInfoMap = new HashMap<>();
        skuInfo.add("*");
        for (RemoteSkuInfoForActivityVo skuBo : supplierSkuList){
            List<String> s = new ArrayList<>();
            s.add("SS:"+skuBo.getSupplierSkuId());
            s.add("K:"+skuBo.getSkuId());
            s.add("P:"+skuBo.getSpuId());
            s.add("C:"+skuBo.getCategoryId());
            s.add("C:"+skuBo.getCategoryIdLevel1());
            s.add("C:"+skuBo.getCategoryIdLevel2());
            skuInfo.addAll(s);
            skuActivityMap.put(skuBo.getSkuId(),new ArrayList<>());
            skuInfoMap.put(skuBo.getSupplierSkuId(),s);
        }
        bo.setSkuInfo(skuInfo);
        bo.setTime(LocalTime.parse(LocalTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"))));
        List<MkActivityDiscountSummary> list = activityDiscountSummaryMapper.getSkuDiscount(bo);
        if (CollectionUtils.isNotEmpty(list)){
            log.keyword("getSkuDiscountIdList").info("活动:{}", list);
            for (RemoteSkuInfoForActivityVo skuBo :supplierSkuList){
                RemoteMkActivitySkuDiscountVo vo = new RemoteMkActivitySkuDiscountVo();
                vo.setSupplierSkuId(skuBo.getSupplierSkuId());
                for (MkActivityDiscountSummary summary : list){
                    log.keyword("getSkuDiscountIdList").info("开始遍历活动");
                    List<Integer> typeList = skuActivityMap.get(skuBo.getSkuId());
                    if (typeList.contains(summary.getActivityType())){
                        //补贴取最小折扣
                        if (summary.getActivityType().equals(90)
                                && summary.getDiscountRate().compareTo(vo.getDiscountRate()) > 0) {
                            log.keyword("getSkuDiscountIdList").info("类型过滤");
                            continue;
                        }
                    }
                    //校验
                    if (check(bo, skuInfoMap, skuBo, summary)){ continue;}
                    log.keyword("getSkuDiscountIdList").info("通过校验{}",summary);
                    switch (summary.getActivityType()){
                        //补贴
                        case 90 -> {
                            log.keyword("getSkuDiscountIdList").info("补贴");
                            vo.setHasLimitDiscount(1);
                            vo.setDiscountRate(summary.getDiscountRate());
                            vo.setTargetQuantity(summary.getTargetQuantity());
                            vo.setMaxDiscountAmount(summary.getMaxDiscountAmount());
                        }
                        case 10,20,30,40,50,60,70,80 -> {
                        }
                        default ->{}
                    }
                    skuActivityMap.get(skuBo.getSkuId()).add(summary.getActivityType());
                }
                if (vo.getHasLimitDiscount()!=null) {
                    vos.add(vo);
                }
            }
        }
        log.keyword("getSkuDiscountIdList").info("匹配商品活动id集合:{}", vos);
        return vos;
    }

    private static boolean check(RemoteMkActivitySkuDiscountBo bo, Map<Long, List<String>> skuInfoMap, RemoteSkuInfoForActivityVo skuBo,
                                 MkActivityDiscountSummary summary) {
        //过滤时间 固定周几
        if (summary.getEffectType().equals(3) && summary.getEffectValue() != null){
            int week = bo.getSaleDate().getDayOfWeek().getValue();
            if (!Arrays.stream(summary.getEffectValue().split(",")).toList().contains(String.valueOf(week))){
                log.keyword("getSkuDiscountIdList").info("周几被过滤");
                return true;
            }
        }
        //过滤时间 几号
        if (summary.getEffectType().equals(4) && summary.getEffectValue() != null){
            int day = bo.getSaleDate().getDayOfMonth();
            if (!Arrays.stream(summary.getEffectValue().split(",")).toList().contains(String.valueOf(day))){
                log.keyword("getSkuDiscountIdList").info("月号被过滤");
                return true;
            }
        }
        //限时折扣有限购门槛
        if (skuBo != null && summary.getActivityType().equals(90) && summary.getTargetQuantity() != null && summary.getTargetQuantity()< skuBo.getCopySold()){
            log.keyword("getSkuDiscountIdList").info("销量门槛被过滤");
            return true;
        }
        //适用范围  目前只有物流线
        if (bo.getLogisticsId() != null && "6".equals(summary.getScopeType()) && StringUtils.isNotEmpty(summary.getSuitThrongValue())){
            List<Long> logisticsIds = Arrays.stream(summary.getSuitThrongValue().split(",")).map(Long::parseLong).toList();
            if (!logisticsIds.contains(bo.getLogisticsId())){
                log.keyword("getSkuDiscountIdList").info("物流线限制被过滤");
                return true;
            }
        }
        //过滤商品
        if (skuInfoMap != null && StringUtils.isNotEmpty(summary.getScopeValue()) && !"*".equals(summary.getScopeValue())){
            List<String> skuInfos = skuInfoMap.get(skuBo.getSupplierSkuId());
            if (ObjectUtils.isNotNull(skuInfos) && CollectionUtils.isNotEmpty(skuInfos)
                    &&((!skuInfos.contains(summary.getScopeValue()) && summary.getExclude().equals(1))
                    || (skuInfos.contains(summary.getScopeValue()) && summary.getExclude().equals(0)))){
                log.keyword("getSkuDiscountIdList").info("商品限制被过滤");
                return true;
            }
        }
        return false;
    }

    /**
     * 根据下单商品筛选活动
     */
    @Override
    public List<RemoteMkActivitySkuDiscountVo> getSkuDiscountIdForOrder(RemoteMkActivitySkuDiscountBo bo) {
        List<RemoteMkActivitySkuDiscountVo> vos = new ArrayList<>();
        if (ObjectUtils.isNull(bo) || CollectionUtils.isEmpty(bo.getSupplierSkuList())){
            return vos;
        }
        List<Long> skuIds = bo.getSupplierSkuList().stream().map(RemoteMkActivitySkuBo::getSupplierSkuId).toList();
        List<RemoteSkuInfoForActivityVo> supplierSkuList = remoteSupplierSkuService.queryActivityInfoList(skuIds);
        if (CollectionUtils.isEmpty(supplierSkuList)){
            return vos;
        }
        //组装商品信息
        List<String> skuInfo = new ArrayList<>();
        Map<Long, List<String>> skuInfoMap = new HashMap<>();
        skuInfo.add("*");
        for (RemoteSkuInfoForActivityVo skuBo : supplierSkuList){
            List<String> s = new ArrayList<>();
            s.add("SS:"+skuBo.getSupplierSkuId());
            s.add("K:"+skuBo.getSkuId());
            s.add("P:"+skuBo.getSpuId());
            s.add("C:"+skuBo.getCategoryId());
            s.add("C:"+skuBo.getCategoryIdLevel1());
            s.add("C:"+skuBo.getCategoryIdLevel2());
            skuInfo.addAll(s);
            skuInfoMap.put(skuBo.getSupplierSkuId(),s);
        }
        bo.setSkuInfo(skuInfo.stream().distinct().toList());
        bo.setTime(LocalTime.parse(LocalTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"))));
        List<MkActivityDiscountSummary> list = activityDiscountSummaryMapper.getSkuDiscount(bo);
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(list)){
            for (RemoteSkuInfoForActivityVo skuBo :supplierSkuList){
                RemoteMkActivitySkuDiscountVo vo = new RemoteMkActivitySkuDiscountVo();
                List<RemoteMkActivityDiscountInfoVo> activityInfoList = new ArrayList<>();
                vo.setSupplierSkuId(skuBo.getSupplierSkuId());
                vo.setSkuId(skuBo.getSkuId());
                for (MkActivityDiscountSummary summary : list){
                    //校验
                    if (check(bo, skuInfoMap, skuBo, summary)){ continue;}
                    RemoteMkActivityDiscountInfoVo infoVo = new RemoteMkActivityDiscountInfoVo();
                    switch (summary.getActivityType()){
                        //补贴
                        case 90 -> {
                            vo.setHasLimitDiscount(1);
                            infoVo.setActivityId(summary.getActivityId());
                            infoVo.setActionType(summary.getActionType());
                            infoVo.setActivityType(summary.getActivityType());
                            infoVo.setDiscountRate(summary.getDiscountRate());
                            infoVo.setTargetQuantity(summary.getTargetQuantity());
                            infoVo.setMaxDiscountAmount(summary.getMaxDiscountAmount());
                        }
                        case 10,20,30,40,50,60,70,80 -> {
                        }
                        default ->{}
                    }
                    activityInfoList.add(infoVo);
                }
                vo.setActivityInfoList(activityInfoList);
                vos.add(vo);
            }
        }
        return vos;
    }

    @Override
    public RemoteMkActivitySkuSearchVo getSkuDiscountQueryInfo(RemoteMkActivitySkuDiscountBo bo) {
        RemoteMkActivitySkuSearchVo vo = new RemoteMkActivitySkuSearchVo();
        bo.setTime(LocalTime.parse(LocalTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"))));
        List<MkActivityDiscountSummary> list = activityDiscountSummaryMapper.getSkuDiscount(bo);
        int maxCopySold = 0;
        if (CollectionUtils.isNotEmpty(list)) {
            for (MkActivityDiscountSummary summary : list) {
                //校验
                if (check(bo, null, null, summary)) {
                    continue;
                }
                //限制物流线
                if ("6".equals(summary.getScopeType()) && StringUtils.isNotEmpty(summary.getSuitThrongValue())) {
                    List<Long> logisticsIds = Arrays.stream(summary.getSuitThrongValue().split(",")).map(Long::parseLong).toList();
                    vo.getCategoryIds().addAll(logisticsIds);
                }
                //处理商品限制信息
                if (StringUtils.isNotEmpty(summary.getScopeValue()) && !"*".equals(summary.getScopeValue())) {
                    if (summary.getExclude().equals(1)) {
                        Arrays.stream(summary.getScopeValue().split(":")).forEach(s -> {
                            if (s.startsWith("K:")) {
                                vo.getSkuIds().add(Long.parseLong(s.substring(1)));
                            }
                            if (s.startsWith("P:")) {
                                vo.getSpuIds().add(Long.parseLong(s.substring(1)));
                            }
                            if (s.startsWith("C:")) {
                                vo.getCategoryIds().add(Long.parseLong(s.substring(1)));
                            }
                        });
                    }else {
                        Arrays.stream(summary.getScopeValue().split(":")).forEach(s -> {
                            if (s.startsWith("K:")) {
                                vo.getRollSkuIds().add(Long.parseLong(s.substring(1)));
                            }
                            if (s.startsWith("P:")) {
                                vo.getRollSpuIds().add(Long.parseLong(s.substring(1)));
                            }
                            if (s.startsWith("C:")) {
                                vo.getRollCategoryIds().add(Long.parseLong(s.substring(1)));
                            }
                        });
                    }
                }
                if (summary.getTargetQuantity() > maxCopySold) {
                    maxCopySold = summary.getTargetQuantity().intValue();
                }
            }
            //取反需去掉正向的
            vo.getRollSkuIds().removeAll(vo.getSkuIds());
            vo.getRollSpuIds().removeAll(vo.getSpuIds());
            vo.getRollCategoryIds().removeAll(vo.getCategoryIds());
            vo.setCopySold(maxCopySold);
        }
        return vo;
    }

    @Override
    public List<RemoteMkActivitySkuSearchVo> getRegionQueryInfo(RemoteMkActivitySkuDiscountBo bo) {
        List<RemoteMkActivitySkuSearchVo> vos = new ArrayList<>();
        bo.setTime(LocalTime.parse(LocalTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"))));
        bo.setOwnershipType(3);
        List<MkActivityDiscountSummary> list = activityDiscountSummaryMapper.getSkuDiscount(bo);
        if (CollectionUtils.isNotEmpty(list)) {
            List<Long> regionIds = list.stream().map(MkActivityDiscountSummary::getOwnerId).distinct().map(Long::valueOf).toList();
            List<RemoteRegionWhVo> regionWhVos = remoteRegionWhService.selectRegionWhInfoByIds(regionIds);
            Map<Long, String> map = regionWhVos.stream().collect(Collectors.toMap(RemoteRegionWhVo::getId, RemoteRegionWhVo::getSalesTimeEnd));
            Map<String, List<MkActivityDiscountSummary>> listMap = list.stream().collect(Collectors.groupingBy(MkActivityDiscountSummary::getOwnerId));
            for (String ownerId : listMap.keySet()) {
                for (MkActivityDiscountSummary summary : listMap.get(ownerId)) {
                    RemoteMkActivitySkuSearchVo vo = new RemoteMkActivitySkuSearchVo();
                    vo.setRegionId(Long.valueOf(ownerId));
                    vo.setSaleDate(SaleDateUtil.getSaleDate(map.get(Long.valueOf(ownerId))));
                    bo.setSaleDate(vo.getSaleDate());
                    //校验
                    if (check(bo, null, null, summary)) {
                        continue;
                    }
                    if (summary.getStartDate() != null
                            && (summary.getStartDate().isBefore(vo.getSaleDate())
                            || summary.getStartDate().isAfter(vo.getSaleDate()))){
                        continue;
                    }
                    //限制物流线
                    if ("6".equals(summary.getScopeType()) && StringUtils.isNotEmpty(summary.getSuitThrongValue())) {
                        List<Long> logisticsIds = Arrays.stream(summary.getSuitThrongValue().split(",")).map(Long::parseLong).toList();
                        vo.getLogisticsIds().addAll(logisticsIds);
                    }
                    //处理商品限制信息
                    if (StringUtils.isNotEmpty(summary.getScopeValue()) && !"*".equals(summary.getScopeValue())) {
                        if (summary.getExclude().equals(1)) {
                            String[] s = summary.getScopeValue().split(":");
                            if ("K".equals(s[0]) && !vo.getSkuIds().contains(Long.parseLong(s[1]))) {
                                vo.getSkuIds().add(Long.parseLong(s[1]));
                            }
                            if ("P".equals(s[0]) && !vo.getSpuIds().contains(Long.parseLong(s[1]))) {
                                vo.getSpuIds().add(Long.parseLong(s[1]));
                            }
                            if ("C".equals(s[0]) && !vo.getCategoryIds().contains(Long.parseLong(s[1]))) {
                                vo.getCategoryIds().add(Long.parseLong(s[1]));
                            }
                        } else {
                            String[] s = summary.getScopeValue().split(":");
                            if ("K".equals(s[0]) && !vo.getRollSkuIds().contains(Long.parseLong(s[1]))) {
                                vo.getRollSkuIds().add(Long.parseLong(s[1]));
                            }
                            if ("P".equals(s[0]) && !vo.getRollSpuIds().contains(Long.parseLong(s[1]))) {
                                vo.getRollSpuIds().add(Long.parseLong(s[1]));
                            }
                            if ("C".equals(s[0]) && !vo.getRollCategoryIds().contains(Long.parseLong(s[1]))) {
                                vo.getRollCategoryIds().add(Long.parseLong(s[1]));
                            }
                        }
                    }
                    vo.setCopySold(Math.toIntExact(summary.getTargetQuantity()));
                    vo.setRate(summary.getDiscountRate());
                    vo.setMaxDiscountAmount(summary.getMaxDiscountAmount());
                    vos.add(vo);
                }
            }
        }
        return vos;
    }

    @Override
    public void joinMkActivityRecordByOrder(List<RemoteMkActivityRecordDTO> mkActivityRecordDTOList) {
        if (CollectionUtils.isEmpty(mkActivityRecordDTOList)) {
            return;
        }
        List<MkActivityRecordBo> mkActivityRecordBoList = mkActivityRecordDTOList.stream().map(item -> {
            MkActivityRecordBo bo = new MkActivityRecordBo();
            bo.setActivityId(item.getActivityId());
            bo.setActivityType(item.getActivityType());
            bo.setOrderNo(item.getOrderNo());
            bo.setOrderAmt(item.getOrderAmt());
            bo.setJoinTime(item.getJoinTime());
            bo.setCustomerType(item.getCustomerType());
            bo.setCustomerId(item.getCustomerId());
            bo.setOrderTime(item.getOrderTime());
            bo.setDiscountAmount(item.getDiscountAmount());
            bo.setRegionWhId(item.getRegionWhId());
            bo.setCityWhId(item.getCityWhId());
            bo.setPlaceId(item.getPlaceId());
            bo.setLogisticsId(item.getLogisticsId());
            bo.setSupplierId(item.getSupplierId());
            bo.setAppliedRules(item.getAppliedRules());
            bo.setCreateTime(item.getJoinTime());
            bo.setCreateCode(item.getCustomerId());
            return bo;
        }).toList();
        mkActivityRecordService.batchInsertByBo(mkActivityRecordBoList);
    }


}
