package cn.xianlink.marketing.controller.activity;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.log.annotation.Log;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.validate.AddGroup;
import cn.xianlink.common.core.validate.EditGroup;
import cn.xianlink.common.log.enums.BusinessType;
import cn.xianlink.common.excel.utils.ExcelUtil;
import cn.xianlink.marketing.domain.vo.MkActivitySkuVo;
import cn.xianlink.marketing.domain.bo.MkActivitySkuBo;
import cn.xianlink.marketing.service.IMkActivitySkuService;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;

/**
 * 营销活动商品适用范围
 * 前端访问路由地址为:/marketing/activitySku
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/activitySku")
public class MkActivitySkuController extends BaseController {

    private final IMkActivitySkuService mkActivitySkuService;

    /**
     * 查询营销活动商品适用范围列表
     */
    @SaCheckPermission("marketing:activitySku:list")
    @GetMapping("/list")
    public TableDataInfo<MkActivitySkuVo> list(MkActivitySkuBo bo, PageQuery pageQuery) {
        return mkActivitySkuService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出营销活动商品适用范围列表
     */
    @SaCheckPermission("marketing:activitySku:export")
    @Log(title = "营销活动商品适用范围", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MkActivitySkuBo bo, HttpServletResponse response) {
        List<MkActivitySkuVo> list = mkActivitySkuService.queryList(bo);
        ExcelUtil.exportExcel(list, "营销活动商品适用范围", MkActivitySkuVo.class, response);
    }

    /**
     * 获取营销活动商品适用范围详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("marketing:activitySku:query")
    @GetMapping("/{id}")
    public R<MkActivitySkuVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(mkActivitySkuService.queryById(id));
    }

    /**
     * 新增营销活动商品适用范围
     */
    @SaCheckPermission("marketing:activitySku:add")
    @Log(title = "营销活动商品适用范围", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MkActivitySkuBo bo) {
        return toAjax(mkActivitySkuService.insertByBo(bo));
    }

    /**
     * 修改营销活动商品适用范围
     */
    @SaCheckPermission("marketing:activitySku:edit")
    @Log(title = "营销活动商品适用范围", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MkActivitySkuBo bo) {
        return toAjax(mkActivitySkuService.updateByBo(bo));
    }

    /**
     * 删除营销活动商品适用范围
     *
     * @param ids 主键串
     */
    @SaCheckPermission("marketing:activitySku:remove")
    @Log(title = "营销活动商品适用范围", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(mkActivitySkuService.deleteWithValidByIds(List.of(ids), true));
    }
}
