package cn.xianlink.marketing.service.impl;

import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import cn.xianlink.marketing.domain.bo.MkActivitySkuBo;
import cn.xianlink.marketing.domain.vo.MkActivitySkuVo;
import cn.xianlink.marketing.domain.MkActivitySku;
import cn.xianlink.marketing.mapper.MkActivitySkuMapper;
import cn.xianlink.marketing.service.IMkActivitySkuService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 营销活动商品适用范围Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@RequiredArgsConstructor
@Service
public class MkActivitySkuServiceImpl implements IMkActivitySkuService {

    private final MkActivitySkuMapper baseMapper;

    /**
     * 查询营销活动商品适用范围
     */
    @Override
    public MkActivitySkuVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询营销活动商品适用范围列表
     */
    @Override
    public TableDataInfo<MkActivitySkuVo> queryPageList(MkActivitySkuBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MkActivitySku> lqw = buildQueryWrapper(bo);
        Page<MkActivitySkuVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询营销活动商品适用范围列表
     */
    @Override
    public List<MkActivitySkuVo> queryList(MkActivitySkuBo bo) {
        LambdaQueryWrapper<MkActivitySku> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MkActivitySku> buildQueryWrapper(MkActivitySkuBo bo) {
        LambdaQueryWrapper<MkActivitySku> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getActivityId() != null, MkActivitySku::getActivityId, bo.getActivityId());
        lqw.eq(bo.getActivityType() != null, MkActivitySku::getActivityType, bo.getActivityType());
        lqw.eq(bo.getOwnershipType() != null, MkActivitySku::getOwnershipType, bo.getOwnershipType());
        lqw.eq(bo.getOwnerId() != null, MkActivitySku::getOwnerId, bo.getOwnerId());
        lqw.eq(bo.getScopeType() != null, MkActivitySku::getScopeType, bo.getScopeType());
        lqw.eq(StringUtils.isNotBlank(bo.getScopeValue()), MkActivitySku::getScopeValue, bo.getScopeValue());
        lqw.eq(StringUtils.isNotBlank(bo.getActionType()), MkActivitySku::getActionType, bo.getActionType());
        lqw.eq(bo.getExclude() != null, MkActivitySku::getExclude, bo.getExclude());
        lqw.eq(StringUtils.isNotBlank(bo.getExtValue()), MkActivitySku::getExtValue, bo.getExtValue());
       return lqw;
    }

    /**
     * 新增营销活动商品适用范围
     */
    @Override
    public Boolean insertByBo(MkActivitySkuBo bo) {
        MkActivitySku add = MapstructUtils.convert(bo, MkActivitySku.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改营销活动商品适用范围
     */
    @Override
    public Boolean updateByBo(MkActivitySkuBo bo) {
        MkActivitySku update = MapstructUtils.convert(bo, MkActivitySku.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MkActivitySku entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除营销活动商品适用范围
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
