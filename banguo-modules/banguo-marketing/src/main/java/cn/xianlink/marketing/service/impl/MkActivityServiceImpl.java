package cn.xianlink.marketing.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.basic.api.RemoteRegionLogisticsService;
import cn.xianlink.basic.api.RemoteRegionWhService;
import cn.xianlink.basic.api.domain.bo.RemoteRegionLogisticsQueryBo;
import cn.xianlink.basic.api.domain.vo.RemoteRegionLogisticsVo;
import cn.xianlink.basic.api.domain.vo.RemoteRegionWhVo;
import cn.xianlink.common.api.enums.marketing.*;
import cn.xianlink.common.api.util.SaleDateUtil;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.DateUtils;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.redis.utils.RedisUtils;
import cn.xianlink.marketing.domain.MkActivity;
import cn.xianlink.marketing.domain.MkActivityDiscountSummary;
import cn.xianlink.marketing.domain.MkActivitySku;
import cn.xianlink.marketing.domain.bo.MkActivityBo;
import cn.xianlink.marketing.domain.bo.MkActivityLaunchTimeBo;
import cn.xianlink.marketing.domain.bo.MkActivityQueryBo;
import cn.xianlink.marketing.domain.bo.MkActivitySkuQueryBo;
import cn.xianlink.marketing.domain.vo.*;
import cn.xianlink.marketing.mapper.MkActivityDiscountSummaryMapper;
import cn.xianlink.marketing.mapper.MkActivityMapper;
import cn.xianlink.marketing.mapper.MkActivitySkuMapper;
import cn.xianlink.marketing.service.IMkActivityDiscountSummaryService;
import cn.xianlink.marketing.service.IMkActivityService;
import cn.xianlink.order.api.RemoteActivityService;
import cn.xianlink.order.api.bo.RemoteMkOrderDataStatBo;
import cn.xianlink.order.api.vo.RemoteMkDataStatVo;
import cn.xianlink.order.api.vo.RemoteMkOrderDataStatVo;
import cn.xianlink.product.api.RemoteCategoryService;
import cn.xianlink.product.api.RemoteSkuService;
import cn.xianlink.product.api.RemoteSpuService;
import cn.xianlink.product.api.RemoteSupplierSkuService;
import cn.xianlink.product.api.domain.vo.RemoteCategorySpuVo;
import cn.xianlink.product.api.domain.vo.RemoteCategoryVO;
import cn.xianlink.product.api.domain.vo.RemotePubVo;
import cn.xianlink.product.api.domain.vo.RemoteSkuVo;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.redisson.api.RAtomicLong;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 营销活动Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@RequiredArgsConstructor
@Service
@CustomLog
public class MkActivityServiceImpl implements IMkActivityService {

    private final MkActivityMapper baseMapper;

    @DubboReference
    private final RemoteActivityService remoteActivityService;

    private final MkActivitySkuMapper activitySkuMapper;

    @DubboReference
    private final RemoteSupplierSkuService remoteSupplierSkuService;

    @DubboReference
    private final RemoteSkuService remoteSkuService;

    @DubboReference
    private final RemoteRegionWhService remoteRegionWhService;

    private final IMkActivityDiscountSummaryService mkActivityDiscountSummaryService;

    @DubboReference
    private final RemoteSpuService remoteSpuService;

    @DubboReference
    private final RemoteCategoryService remoteCategoryService;

    @DubboReference
    private final RemoteRegionLogisticsService regionLogisticsService;

    private final MkActivityDiscountSummaryMapper summaryMapper;

    private final TransactionTemplate transactionTemplate;

    /**
     * 查询营销活动
     */
    @Override
    public MkActivityVo queryById(Long id){
        if(ObjectUtil.isEmpty(id)){
            throw new ServiceException("活动id不能为空");
        }
        MkActivity mkActivity = baseMapper.selectById(id);
        if(ObjectUtil.isEmpty(mkActivity)){
            throw new ServiceException("活动不存在");
        }
        MkActivityVo vo = convertMkActivityVo(mkActivity);
        return vo;
    }

    /**
     * 转换实体到VO
     * @return
     */
    private MkActivityVo convertMkActivityVo(MkActivity mkActivity) {
        MkActivityVo vo = MapstructUtils.convert(mkActivity, MkActivityVo.class);
        //所属id
        vo.setOwnerId(Long.valueOf(mkActivity.getOwnerId()));
        RemoteRegionWhVo remoteRegionWhVo = remoteRegionWhService.queryById(vo.getOwnerId());
        vo.setOwnerName(ObjectUtil.isNotEmpty(remoteRegionWhVo) ? remoteRegionWhVo.getRegionWhName() : "");
        //活动日期范围,当effect_type值为3或4时
        if(ObjectUtil.isNotEmpty(mkActivity.getEffectValue()) && (MkActivityEffectTypeEnum.WEEK.getCode().equals(mkActivity.getEffectType()) || MkActivityEffectTypeEnum.MONTH.getCode().equals(mkActivity.getEffectType()))){
            vo.setEffectValueList( Stream.of(mkActivity.getEffectValue().split(",")).map(Integer::valueOf).collect(Collectors.toList()));
        }
        //活动规则设置 json格式
        vo.setRuleConfigVo(ObjectUtil.isNotEmpty(mkActivity.getRuleConfig()) ? JSON.parseObject(mkActivity.getRuleConfig(), MkActivityRuleConfigVo.class) : null);
        //适用范围
        vo.setSubScopeType(MkActivityScopeTypeEnum.ALL.getCode().equals(mkActivity.getScopeType()) ? "1" : "2");
        //适用范围具体值
        if(ObjectUtil.isNotEmpty(mkActivity.getSuitThrongValue()) && !MkActivityScopeTypeEnum.ALL.getCode().equals(mkActivity.getScopeType())){
            vo.setSuitThrongValueList(Stream.of(mkActivity.getSuitThrongValue().split(",")).map(Long::valueOf).collect(Collectors.toList()));
        }
        //sku范围类型转换
        MkActivitySkuScopeTypeEnum skuScopeTypeEnum = MkActivitySkuScopeTypeEnum.loadByCode(mkActivity.getSkuScopeType());
        if(ObjectUtil.isEmpty(skuScopeTypeEnum)){
            throw new ServiceException("sku范围类型错误");
        }
        vo.setProductRange(ObjectUtil.isNotEmpty(skuScopeTypeEnum.getProductRangeEnum()) ? skuScopeTypeEnum.getProductRangeEnum().getCode() : null);
        vo.setProductDimension(ObjectUtil.isNotEmpty(skuScopeTypeEnum.getProductDimensionEnum()) ? skuScopeTypeEnum.getProductDimensionEnum().getCode() : null);
        //物流线
        if(ObjectUtil.isNotEmpty(vo.getSuitThrongValueList())){
            RemoteRegionLogisticsQueryBo logisticsQueryBo = new RemoteRegionLogisticsQueryBo();
            logisticsQueryBo.setLogisticsIds(vo.getSuitThrongValueList());
            List<RemoteRegionLogisticsVo> regionLogisticsVoList = regionLogisticsService.queryList(logisticsQueryBo);
            vo.setRegionLogisticsVoList(regionLogisticsVoList);
        }
        //适用商品
        LambdaQueryWrapper<MkActivitySku> qw = Wrappers.lambdaQuery();
        qw.eq(MkActivitySku::getActivityId, mkActivity.getId());
        List<MkActivitySkuVo> mkActivitySkuVos = activitySkuMapper.selectVoList(qw);
        if(ObjectUtil.isNotEmpty(mkActivitySkuVos)){
            List<MkActivitySkuSimpleVo> activitySkuSimpleVoList = mkActivitySkuVos.stream().map(mkActivitySkuVo -> {
                MkActivitySkuSimpleVo mkActivitySkuSimpleVo = new MkActivitySkuSimpleVo();
                mkActivitySkuSimpleVo.setScopeValue(Long.valueOf(mkActivitySkuVo.getScopeValue()));
                mkActivitySkuSimpleVo.setScopeDesc(mkActivitySkuVo.getScopeDesc());
                return mkActivitySkuSimpleVo;
            }).toList();
            vo.setActivitySkuVoList(activitySkuSimpleVoList);
        }

        return vo;
    }

    /**
     * 查询营销活动列表
     */
    @Override
    public TableDataInfo<MkActivityQueryVo> queryPageList(MkActivityQueryBo bo) {
        //转换产品范围
        MkActivityProductRangeEnum productRangeEnum = MkActivityProductRangeEnum.loadByCode(bo.getProductRange());
        List<String> skuScopeTypeCodes = MkActivitySkuScopeTypeEnum.loadCodeList(productRangeEnum);
        if(ObjectUtil.isNotEmpty(skuScopeTypeCodes)){
            bo.setSkuScopeTypeList(skuScopeTypeCodes);
        }
        //组装活动商品的查询参数
        this.buildActivitySkuQueryBo(bo);
        //分页查询
        Page<MkActivityQueryVo> page = baseMapper.selectActivityList(bo, bo.build());
        if(ObjectUtil.isEmpty(page.getRecords())){
            return TableDataInfo.build();
        }
        List<MkActivityQueryVo> list = page.getRecords();
        buildResult(list);
        return TableDataInfo.build(page);
    }

    /**
     * 组装活动商品的查询参数
     * @param bo
     */
    private void buildActivitySkuQueryBo(MkActivityQueryBo bo) {
        /**
         * 1、不传商品范围，只传 分类、商品、平台商品：根据对应id，自动附上，不带排除标识
         * 2、传了商品范围，也传 分类、商品、平台商品：根据商品范围，取对应数据即可
         * 3、传了商品范围，商品范围为全部 或 不传分类、商品、平台商品 ：直接根据商品范围筛选主表，下面不组装
         */
        bo.setCategoryIdList(ObjectUtil.isEmpty(bo.getCategoryIdList()) ? new ArrayList<>() : bo.getCategoryIdList());
        List<MkActivitySkuQueryBo> skuQueryBoList = new ArrayList<>();
        bo.setActivitySkuQueryBoList(skuQueryBoList);
        if(ObjectUtil.isEmpty(bo.getProductRange())){
            //1、不传商品范围，只传 分类、商品、平台商品：根据对应id，自动附上，不带排除标识
            this.handleWithoutProductRange(bo, skuQueryBoList);
        }else if(ObjectUtil.isNotEmpty(bo.getProductRange()) && !MkActivityProductRangeEnum.ALL.getCode().equals(bo.getProductRange())){
            //2、传了商品范围，也传 分类、商品、平台商品：根据商品范围，取对应数据即可
            this.handleWithProductRange(bo, skuQueryBoList);
        }
    }

    /**
     * 组装活动商品的查询参数
     * 2、传了商品范围，也传 分类、商品、平台商品：根据商品范围，取对应数据即可
     * @param bo
     * @param skuQueryBoList
     */
    private void handleWithProductRange(MkActivityQueryBo bo, List<MkActivitySkuQueryBo> skuQueryBoList) {
        if(MkActivityProductRangeEnum.CATEGORY.getCode().equals(bo.getProductRange()) || MkActivityProductRangeEnum.CATEGORY_NOT.getCode().equals(bo.getProductRange())){
            //分类
            if(ObjectUtil.isNotEmpty(bo.getCategoryIdList())){
                MkActivitySkuQueryBo activitySkuQueryBo = new MkActivitySkuQueryBo(
                        MkActivitySkuSubScopeTypeEnum.CATEGORY.getCode(),
                        MkActivityProductRangeEnum.CATEGORY.getCode().equals(bo.getProductRange()) ? MkActivitySkuExcludeEnum.SPECIFY.getCode() : MkActivitySkuExcludeEnum.EXCLUDE.getCode(),
                        bo.getCategoryIdList().stream().map(String::valueOf).toList()
                );
                skuQueryBoList.add(activitySkuQueryBo);
            }
        }else if (MkActivityProductRangeEnum.PRODUCT.getCode().equals(bo.getProductRange()) || MkActivityProductRangeEnum.PRODUCT_NOT.getCode().equals(bo.getProductRange())){
            //spu
            if(ObjectUtil.isNotEmpty(bo.getSpuIdList())){
                Integer exclude = MkActivityProductRangeEnum.PRODUCT.getCode().equals(bo.getProductRange()) ? MkActivitySkuExcludeEnum.SPECIFY.getCode() : MkActivitySkuExcludeEnum.EXCLUDE.getCode();
                MkActivitySkuQueryBo activitySkuQueryBo = new MkActivitySkuQueryBo(
                    MkActivitySkuSubScopeTypeEnum.SPU.getCode(),
                    exclude,
                    bo.getSpuIdList().stream().map(String::valueOf).toList()
                );
                skuQueryBoList.add(activitySkuQueryBo);
                //除了spu，还要spu的分类
                List<RemoteCategorySpuVo> categorySpuList = remoteSpuService.getCategorySpuByIds(bo.getSpuIdList());
                if(ObjectUtil.isNotEmpty(categorySpuList)){
                    List<Long> categoryIds = categorySpuList.stream().map(RemoteCategorySpuVo::getCategoryId).collect(Collectors.toList());
                    List<RemoteCategoryVO> remoteCategoryVoList = remoteCategoryService.getParentCategoryByIds(categoryIds);
                    if(ObjectUtil.isNotEmpty(remoteCategoryVoList)){
                        categoryIds = remoteCategoryVoList.stream().map(RemoteCategoryVO::getId).collect(Collectors.toList());
                    }
                    MkActivitySkuQueryBo activitySkuQueryBo1 = new MkActivitySkuQueryBo(
                            MkActivitySkuSubScopeTypeEnum.CATEGORY.getCode(),
                            exclude,
                            categoryIds.stream().map(String::valueOf).toList()
                    );
                    skuQueryBoList.add(activitySkuQueryBo1);
                }
            }
            //sku
            if(ObjectUtil.isNotEmpty(bo.getSkuIdList())){
                Integer exclude = MkActivityProductRangeEnum.PRODUCT.getCode().equals(bo.getProductRange()) ? MkActivitySkuExcludeEnum.SPECIFY.getCode() : MkActivitySkuExcludeEnum.EXCLUDE.getCode();
                MkActivitySkuQueryBo activitySkuQueryBo = new MkActivitySkuQueryBo(
                        MkActivitySkuSubScopeTypeEnum.SKU.getCode(),
                        exclude,
                        bo.getSkuIdList().stream().map(String::valueOf).toList()
                );
                skuQueryBoList.add(activitySkuQueryBo);
                //除了sku，还要sku的分类、平台商品
                List<RemoteSkuVo> remoteSkuVoList = remoteSkuService.getByIds(bo.getSkuIdList());
                if(ObjectUtil.isNotEmpty(remoteSkuVoList)){
                    List<Long> categoryIds = remoteSkuVoList.stream().map(RemoteSkuVo::getCategoryId).collect(Collectors.toList());
                    List<RemoteCategoryVO> remoteCategoryVoList = remoteCategoryService.getParentCategoryByIds(categoryIds);
                    if(ObjectUtil.isNotEmpty(remoteCategoryVoList)){
                        categoryIds = remoteCategoryVoList.stream().map(RemoteCategoryVO::getId).collect(Collectors.toList());
                    }
                    MkActivitySkuQueryBo activitySkuQueryBo1 = new MkActivitySkuQueryBo(
                            MkActivitySkuSubScopeTypeEnum.CATEGORY.getCode(),
                            exclude,
                            categoryIds.stream().map(String::valueOf).toList()
                    );
                    skuQueryBoList.add(activitySkuQueryBo1);
                    List<Long> spuIdList = remoteSkuVoList.stream().map(RemoteSkuVo::getSpuId).toList();
                    MkActivitySkuQueryBo activitySkuQueryBo2 = new MkActivitySkuQueryBo(
                            MkActivitySkuSubScopeTypeEnum.SPU.getCode(),
                            exclude,
                            spuIdList.stream().map(String::valueOf).toList()
                    );
                    skuQueryBoList.add(activitySkuQueryBo2);
                }
            }
        }
    }

    /**
     * 组装活动商品的查询参数
     * 1、不传商品范围，只传 分类、商品、平台商品：根据对应id，自动附上，不带排除标识
     * @param bo
     * @param skuQueryBoList
     */
    private void handleWithoutProductRange(MkActivityQueryBo bo, List<MkActivitySkuQueryBo> skuQueryBoList) {
        //spu
        if(ObjectUtil.isNotEmpty(bo.getSpuIdList())){
            MkActivitySkuQueryBo activitySkuQueryBo = new MkActivitySkuQueryBo(
                    MkActivitySkuSubScopeTypeEnum.SPU.getCode(),
                    null,
                    bo.getSpuIdList().stream().map(String::valueOf).toList()
            );
            skuQueryBoList.add(activitySkuQueryBo);
            //除了spu，还要spu的分类
            List<RemoteCategorySpuVo> categorySpuList = remoteSpuService.getCategorySpuByIds(bo.getSpuIdList());
            if(ObjectUtil.isNotEmpty(categorySpuList)){
                List<Long> categoryIds = categorySpuList.stream().map(RemoteCategorySpuVo::getCategoryId).collect(Collectors.toList());
                List<RemoteCategoryVO> remoteCategoryVoList = remoteCategoryService.getParentCategoryByIds(categoryIds);
                if(ObjectUtil.isNotEmpty(remoteCategoryVoList)){
                    categoryIds = remoteCategoryVoList.stream().map(RemoteCategoryVO::getId).collect(Collectors.toList());
                }
                MkActivitySkuQueryBo activitySkuQueryBo1 = new MkActivitySkuQueryBo(
                        MkActivitySkuSubScopeTypeEnum.CATEGORY.getCode(),
                        null,
                        categoryIds.stream().map(String::valueOf).toList()
                );
                skuQueryBoList.add(activitySkuQueryBo1);
            }
        }
        //sku
        if(ObjectUtil.isNotEmpty(bo.getSkuIdList())){
            MkActivitySkuQueryBo activitySkuQueryBo = new MkActivitySkuQueryBo(
                    MkActivitySkuSubScopeTypeEnum.SKU.getCode(),
                    null,
                    bo.getSkuIdList().stream().map(String::valueOf).toList()
            );
            skuQueryBoList.add(activitySkuQueryBo);
            //除了sku，还要sku的分类、平台商品
            List<RemoteSkuVo> remoteSkuVoList = remoteSkuService.getByIds(bo.getSkuIdList());
            if(ObjectUtil.isNotEmpty(remoteSkuVoList)){
                List<Long> categoryIds = remoteSkuVoList.stream().map(RemoteSkuVo::getCategoryId).collect(Collectors.toList());
                List<RemoteCategoryVO> remoteCategoryVoList = remoteCategoryService.getParentCategoryByIds(categoryIds);
                if(ObjectUtil.isNotEmpty(remoteCategoryVoList)){
                    categoryIds = remoteCategoryVoList.stream().map(RemoteCategoryVO::getId).collect(Collectors.toList());
                }
                MkActivitySkuQueryBo activitySkuQueryBo1 = new MkActivitySkuQueryBo(
                        MkActivitySkuSubScopeTypeEnum.CATEGORY.getCode(),
                        null,
                        categoryIds.stream().map(String::valueOf).toList()
                );
                skuQueryBoList.add(activitySkuQueryBo1);
                List<Long> spuIdList = remoteSkuVoList.stream().map(RemoteSkuVo::getSpuId).toList();
                MkActivitySkuQueryBo activitySkuQueryBo2 = new MkActivitySkuQueryBo(
                        MkActivitySkuSubScopeTypeEnum.SPU.getCode(),
                        null,
                        spuIdList.stream().map(String::valueOf).toList()
                );
                skuQueryBoList.add(activitySkuQueryBo2);
            }
        }
        //分类
        if(ObjectUtil.isNotEmpty(bo.getCategoryIdList())){
            bo.setCategoryIdList(bo.getCategoryIdList().stream().distinct().collect(Collectors.toList()));
            MkActivitySkuQueryBo activitySkuQueryBo = new MkActivitySkuQueryBo(
                    MkActivitySkuSubScopeTypeEnum.CATEGORY.getCode(),
                    null,
                    bo.getCategoryIdList().stream().map(String::valueOf).toList()
            );
            skuQueryBoList.add(activitySkuQueryBo);
        }
    }


    /**
     * 组装查询结果
     * @param list
     */
    private void buildResult(List<MkActivityQueryVo> list) {
        //总仓
        List<Long> ownerIdList = list.stream().map(e -> Long.valueOf(e.getOwnerId())).collect(Collectors.toList());
        Map<Long, RemoteRegionWhVo> regionWhVoMap = new HashMap<>(1);
        if(ObjectUtil.isNotEmpty(ownerIdList)){
            List<RemoteRegionWhVo> remoteRegionWhVos = remoteRegionWhService.selectRegionWhInfoByIds(ownerIdList);
            regionWhVoMap = remoteRegionWhVos.stream().collect(Collectors.toMap(RemoteRegionWhVo::getId, e -> e, (e1, e2) -> e1));
        }
        for (MkActivityQueryVo vo : list) {
            RemoteRegionWhVo remoteRegionWhVo = regionWhVoMap.get(Long.valueOf(vo.getOwnerId()));
            vo.setOwnerName(ObjectUtil.isNotEmpty(remoteRegionWhVo) ? remoteRegionWhVo.getRegionWhName() : "");
            MkActivitySkuScopeTypeEnum skuScopeTypeEnum = MkActivitySkuScopeTypeEnum.loadByCode(vo.getSkuScopeType());
            if(ObjectUtil.isNotEmpty(skuScopeTypeEnum) && ObjectUtil.isNotEmpty(skuScopeTypeEnum.getProductRangeEnum())){
                vo.setProductRange(skuScopeTypeEnum.getProductRangeEnum().getCode());
            }
            vo.setRuleConfigVo(JSON.parseObject(vo.getRuleConfig(), MkActivityRuleConfigVo.class));
            vo.setMcnt(ObjectUtil.isNotEmpty(vo.getRuleConfigVo()) ? vo.getRuleConfigVo().getMcnt() : null);
        }
        //组装生效时间名称
        this.buildMkActivityEffectName(list);
        //组装适用商品相关的数据
        this.buildResultActivitySku(list);
    }

    /**
     * 组装生效时间名称
     */
    private void buildMkActivityEffectName(List<MkActivityQueryVo> list) {
        // 创建一个映射关系
        Map<Integer, String> numberToChinese = new HashMap<>();
        numberToChinese.put(1, "一");
        numberToChinese.put(2, "二");
        numberToChinese.put(3, "三");
        numberToChinese.put(4, "四");
        numberToChinese.put(5, "五");
        numberToChinese.put(6, "六");
        numberToChinese.put(7, "日");
        for (MkActivityQueryVo vo : list) {
            //组装生效时间
            if(MkActivityEffectTypeEnum.TIME_RANGE.getCode().equals(vo.getEffectType())){
                StringBuilder str = new StringBuilder();
                // 格式化模式
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                // 使用格式化器将 LocalDate 格式化为字符串
                String formattedStartDate = vo.getStartDate().format(formatter);
                String formattedEndDate = vo.getEndDate().format(formatter);
                str.append(ObjectUtil.isNotEmpty(formattedStartDate) ? formattedStartDate : "").append("~")
                        .append(ObjectUtil.isNotEmpty(formattedEndDate) ? formattedEndDate : "");
                vo.setEffectName(str.toString());
            }else{
                //活动日期范围,当effect_type值为3或4时
                if(ObjectUtil.isNotEmpty(vo.getEffectValue()) && (MkActivityEffectTypeEnum.WEEK.getCode().equals(vo.getEffectType()) || MkActivityEffectTypeEnum.MONTH.getCode().equals(vo.getEffectType()))){
                    vo.setEffectValueList( Stream.of(vo.getEffectValue().split(",")).map(Integer::valueOf).collect(Collectors.toList()));
                    StringBuilder str = new StringBuilder();
                    if(MkActivityEffectTypeEnum.WEEK.getCode().equals(vo.getEffectType()) && ObjectUtil.isNotEmpty(vo.getEffectValueList())){
                        for (Integer effectValue : vo.getEffectValueList()) {
                            str.append("星期").append(numberToChinese.get(effectValue)).append("、");
                        }
                        vo.setEffectName(str.substring(0, str.length() - 1));
                    }else if(MkActivityEffectTypeEnum.MONTH.getCode().equals(vo.getEffectType()) && ObjectUtil.isNotEmpty(vo.getEffectValueList())){
                        for (Integer effectValue : vo.getEffectValueList()) {
                            str.append(effectValue).append("号").append("、");
                        }
                        vo.setEffectName(str.substring(0, str.length() - 1));
                    }
                }
            }
        }
    }

    /**
     * 组装适用商品相关的数据
     * @param list
     */
    private void buildResultActivitySku(List<MkActivityQueryVo> list) {
        if(ObjectUtil.isEmpty(list)){
            return;
        }
        List<Long> idList = list.stream().map(MkActivityQueryVo::getId).collect(Collectors.toList());
        //查询适用商品
        LambdaQueryWrapper<MkActivitySku> qw = Wrappers.lambdaQuery();
        qw.in(MkActivitySku::getActivityId, idList);
        List<MkActivitySku> activitySkuList = activitySkuMapper.selectList(qw);
        //适用商品map
        Map<Long, List<MkActivitySku>> activitySkuMap = activitySkuList.stream().collect(Collectors.groupingBy(MkActivitySku::getActivityId));
        //分类
        List<Long> categoryIdList = new ArrayList<>();
        Map<Long, RemoteCategoryVO> remoteCategoryMap = new HashMap<>();
        //平台商品
        List<Long> spuIdList = new ArrayList<>();
        Map<Long, RemotePubVo> remoteSpuMap = new HashMap<>();
        //查询参数
        activitySkuList.forEach(activitySku -> {
            if(MkActivitySkuSubScopeTypeEnum.SPU.getCode().equals(activitySku.getScopeType())){
                spuIdList.add(Long.valueOf(activitySku.getScopeValue()));
            }else if(MkActivitySkuSubScopeTypeEnum.CATEGORY.getCode().equals(activitySku.getScopeType())){
                categoryIdList.add(Long.valueOf(activitySku.getScopeValue()));
            }
        });
        //远程调用查数据
        if(ObjectUtil.isNotEmpty(spuIdList)){
            List<RemotePubVo> remoteSpuList = remoteSpuService.getByIds(spuIdList);
            if(ObjectUtil.isNotEmpty(remoteSpuList)){
                remoteSpuMap = remoteSpuList.stream().collect(Collectors.toMap(RemotePubVo::getId, Function.identity(), (v1, v2) -> v1));
            }
        }
        if(ObjectUtil.isNotEmpty(categoryIdList)){
            List<RemoteCategoryVO> remoteCategoryVoList = remoteCategoryService.queryListByIds(categoryIdList);
            if(ObjectUtil.isNotEmpty(remoteCategoryVoList)){
                remoteCategoryMap = remoteCategoryVoList.stream().collect(Collectors.toMap(RemoteCategoryVO::getId, Function.identity(), (v1, v2) -> v1));
            }
        }
        //组装数据
        for (MkActivityQueryVo vo : list) {
            StringBuilder strBuilder = new StringBuilder();
            List<MkActivitySku> mkActivitySkus = activitySkuMap.get(vo.getId());
            if(MkActivitySkuScopeTypeEnum.ALL.getCode().equals(vo.getSkuScopeType()) || ObjectUtil.isEmpty(mkActivitySkus)){
                continue;
            }
            for (MkActivitySku activitySku : mkActivitySkus) {
                if(MkActivitySkuSubScopeTypeEnum.SPU.getCode().equals(activitySku.getScopeType())){
                    RemotePubVo remoteSpuVo = remoteSpuMap.get(Long.valueOf(activitySku.getScopeValue()));
                    if(ObjectUtil.isNotEmpty(remoteSpuVo)){
                        strBuilder.append(remoteSpuVo.getName());
                    }
                }else if(MkActivitySkuSubScopeTypeEnum.CATEGORY.getCode().equals(activitySku.getScopeType())){
                    RemoteCategoryVO remoteCategoryVO = remoteCategoryMap.get(Long.valueOf(activitySku.getScopeValue()));
                    if(ObjectUtil.isNotEmpty(remoteCategoryVO)){
                        strBuilder.append(remoteCategoryVO.getName());
                    }
                }else if(MkActivitySkuSubScopeTypeEnum.SKU.getCode().equals(activitySku.getScopeType())){
                    String skuName = activitySku.getScopeDesc();
                    if(ObjectUtil.isNotEmpty(skuName)){
                        strBuilder.append(skuName);
                    }
                }
                strBuilder.append("、");
            }
            if(!strBuilder.isEmpty()){
                vo.setProductName(strBuilder.substring(0, strBuilder.length() - 1));
            }
        }
    }

    /**
     * 查询营销活动列表
     */
    @Override
    public List<MkActivityQueryVo> queryList(MkActivityQueryBo bo) {
        return baseMapper.selectActivityList(bo);
    }

    /**
     * 新增营销活动
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(MkActivityBo bo) {
        this.validEntityBeforeSave(bo);
        //转换入参
        MkActivity add = convertMkActivity(bo, true);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }else{
            return false;
        }
        //活动商品适用范围表
        this.addMkActivitySku(bo, add);
        //营销活动检索宽表
        mkActivityDiscountSummaryService.activityCreate(add.getId());

        return flag;
    }

    /**
     * 活动商品适用范围表
     * @param bo
     * @param add
     */
    private void addMkActivitySku(MkActivityBo bo, MkActivity add) {
        //组装活动商品表
        if(MkActivitySkuScopeTypeEnum.ALL.getCode().equals(add.getSkuScopeType()) || ObjectUtil.isEmpty(bo.getActivitySkuBoList())){
            return;
        }
        List<MkActivitySku> activitySkuList = bo.getActivitySkuBoList().stream().map(activitySkuBo -> {
            MkActivitySku mkActivitySku = new MkActivitySku();
            mkActivitySku.setActivityId(add.getId());
            mkActivitySku.setOwnershipType(add.getOwnershipType());
            mkActivitySku.setOwnerId(Long.valueOf(add.getOwnerId()));
            //商品范围类型、标识指定or剔除
            if (MkActivitySkuScopeTypeEnum.SPU.getCode().equals(add.getSkuScopeType())) {
                mkActivitySku.setScopeType(MkActivitySkuSubScopeTypeEnum.SPU.getCode());
                mkActivitySku.setExclude(MkActivitySkuExcludeEnum.SPECIFY.getCode());
            }else if (MkActivitySkuScopeTypeEnum.SPU_NOT.getCode().equals(add.getSkuScopeType())) {
                mkActivitySku.setScopeType(MkActivitySkuSubScopeTypeEnum.SPU.getCode());
                mkActivitySku.setExclude(MkActivitySkuExcludeEnum.EXCLUDE.getCode());
            }else if (MkActivitySkuScopeTypeEnum.CATEGORY.getCode().equals(add.getSkuScopeType())) {
                mkActivitySku.setScopeType(MkActivitySkuSubScopeTypeEnum.CATEGORY.getCode());
                mkActivitySku.setExclude(MkActivitySkuExcludeEnum.SPECIFY.getCode());
            }else if (MkActivitySkuScopeTypeEnum.CATEGORY_NOT.getCode().equals(add.getSkuScopeType())) {
                mkActivitySku.setScopeType(MkActivitySkuSubScopeTypeEnum.CATEGORY.getCode());
                mkActivitySku.setExclude(MkActivitySkuExcludeEnum.EXCLUDE.getCode());
            }else if (MkActivitySkuScopeTypeEnum.SKU.getCode().equals(add.getSkuScopeType())) {
                mkActivitySku.setScopeType(MkActivitySkuSubScopeTypeEnum.SKU.getCode());
                mkActivitySku.setExclude(MkActivitySkuExcludeEnum.SPECIFY.getCode());
            }else if (MkActivitySkuScopeTypeEnum.SKU_NOT.getCode().equals(add.getSkuScopeType())) {
                mkActivitySku.setScopeType(MkActivitySkuSubScopeTypeEnum.SKU.getCode());
                mkActivitySku.setExclude(MkActivitySkuExcludeEnum.EXCLUDE.getCode());
            }
            mkActivitySku.setScopeValue(String.valueOf(activitySkuBo.getScopeValue()));
            mkActivitySku.setScopeDesc(activitySkuBo.getScopeDesc());
            return mkActivitySku;
        }).collect(Collectors.toList());
        activitySkuMapper.insertBatch(activitySkuList);
    }

    /**
     * 转换bo到实体
     * @param bo
     * @return
     */
    private MkActivity convertMkActivity(MkActivityBo bo, boolean isAdd) {
        MkActivity add = MapstructUtils.convert(bo, MkActivity.class);
        add.setOwnerId(String.valueOf(bo.getOwnerId()));
        add.setOwnershipType(MkActivityOwnershipTypeEnum.REGION_WH.getCode());
        //活动日期范围,当effect_type值为3或4时
        if(ObjectUtil.isNotEmpty(bo.getEffectValueList()) && (MkActivityEffectTypeEnum.WEEK.getCode().equals(bo.getEffectType()) || MkActivityEffectTypeEnum.MONTH.getCode().equals(bo.getEffectType()))){
            add.setEffectValue(String.join(",", bo.getEffectValueList().stream().map(String::valueOf).toList()));
        }
        //活动规则设置 json格式
        add.setRuleConfig(ObjectUtil.isNotEmpty(bo.getRuleConfigBo()) ? JSON.toJSONString(bo.getRuleConfigBo()) : null);
        //适用范围
        add.setScopeType(Objects.equals(bo.getSubScopeType(), "1") ? MkActivityScopeTypeEnum.ALL.getCode() : MkActivityScopeTypeEnum.LOGISTICS_LINE.getCode());
        //适用范围具体值
        if(ObjectUtil.isNotEmpty(bo.getSuitThrongValueList()) && !MkActivityScopeTypeEnum.ALL.getCode().equals(add.getScopeType())){
            add.setSuitThrongValue(String.join(",", bo.getSuitThrongValueList().stream().map(String::valueOf).toList()));
        }
        //sku范围类型转换
        MkActivityProductRangeEnum productRangeEnum = MkActivityProductRangeEnum.loadByCode(bo.getProductRange());
        if(ObjectUtil.isEmpty(productRangeEnum)){
            throw new ServiceException("商品范围类型错误");
        }
        MkActivityProductDimensionEnum productDimensionEnum = MkActivityProductDimensionEnum.loadByCode(bo.getProductDimension());
        MkActivitySkuScopeTypeEnum skuScopeTypeEnum = MkActivitySkuScopeTypeEnum.load(productRangeEnum, productDimensionEnum);
        if(ObjectUtil.isEmpty(skuScopeTypeEnum)){
            throw new ServiceException("商品范围、商品维度错误");
        }
        add.setSkuScopeType(skuScopeTypeEnum.getCode());
        //处理活动状态
        //获取当前销售日
        RemoteRegionWhVo remoteRegionWhVo = remoteRegionWhService.queryById(bo.getOwnerId());
        LocalDate saleDate = SaleDateUtil.getSaleDate(remoteRegionWhVo.getSalesTimeEnd());
        DayOfWeek dayOfWeek = saleDate.getDayOfWeek();
        int dayOfMonth = saleDate.getDayOfMonth();
        log.keyword("convertMkActivity").info("effectType：{},saleDate={},startDate={},endDate={},effectValueList:{},",bo.getEffectType(),saleDate,bo.getStartDate(),bo.getEndDate(),JSON.toJSONString(bo.getEffectValueList()));
        //活动状态
        if(MkActivityEffectTypeEnum.TIME_RANGE.getCode().equals(bo.getEffectType()) && !saleDate.isBefore(bo.getStartDate()) && !saleDate.isAfter(bo.getEndDate())){
            //日期范围
            log.keyword("convertMkActivity").info("新增活动符合时间范围saleDate={},startDate={},endDate={}",saleDate,bo.getStartDate(),bo.getEndDate());
            add.setStatus(MkActivityStatusEnum.START.getCode());
            add.setAuditStatus(MkActivityAuditStatusEnum.PASS.getCode());
        }else if(MkActivityEffectTypeEnum.WEEK.getCode().equals(bo.getEffectType()) && bo.getEffectValueList().contains(dayOfWeek.getValue())){
            //每周周几
            add.setStatus(MkActivityStatusEnum.START.getCode());
            add.setAuditStatus(MkActivityAuditStatusEnum.PASS.getCode());
            bo.getEffectValueList().sort(Integer::compareTo);
        }else if(MkActivityEffectTypeEnum.MONTH.getCode().equals(bo.getEffectType()) && bo.getEffectValueList().contains(dayOfMonth)){
            //每月几号
            add.setStatus(MkActivityStatusEnum.START.getCode());
            add.setAuditStatus(MkActivityAuditStatusEnum.PASS.getCode());
            bo.getEffectValueList().sort(Integer::compareTo);
        }else if(MkActivityEffectTypeEnum.PERMANENT.getCode().equals(bo.getEffectType())){
            add.setStatus(MkActivityStatusEnum.START.getCode());
            add.setAuditStatus(MkActivityAuditStatusEnum.PASS.getCode());
        }else{
            add.setStatus(MkActivityStatusEnum.WAIT.getCode());
            add.setAuditStatus(MkActivityAuditStatusEnum.DRAFT.getCode());
        }
        if(isAdd){
            //生成名称
            add.setName(this.generateActivityName(bo.getOwnerId(), bo.getOwnerName()));
        }else{
            add.setName(null);
        }
        return add;
    }

    /**
     * 修改营销活动
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(MkActivityBo bo) {
        MkActivity mkActivity = baseMapper.selectById(bo);
        if(ObjectUtil.isEmpty(mkActivity)){
            throw new ServiceException("活动不存在");
        }
        this.validEntityBeforeSave(bo);
        //转换入参
        bo.setActivityType(null);
        MkActivity update = convertMkActivity(bo, false);
        if(baseMapper.updateById(update) <= 0){
            return false;
        }
        //清除活动商品适用范围表
        activitySkuMapper.deleteByActivityId(bo.getId());
        //插入活动商品适用范围表
        this.addMkActivitySku(bo, update);
        //营销活动检索宽表
        mkActivityDiscountSummaryService.activityUpdate(update.getId());
        return true;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MkActivityBo bo){
        //校验投放时间不能重叠
        List<MkActivityLaunchTimeBo> ltime = bo.getRuleConfigBo().getLtime();
        List<MkActivityLaunchTimeBo> sortedRanges = ltime.stream()
                .sorted(Comparator.comparing(MkActivityLaunchTimeBo::getSt))
                .toList();
        for (int i = 1; i < sortedRanges.size(); i++) {
            MkActivityLaunchTimeBo previous = sortedRanges.get(i - 1);
            MkActivityLaunchTimeBo current = sortedRanges.get(i);
            if (current.getSt().isBefore(previous.getEt())) {
                throw new ServiceException("投放时间不能重叠");
            }
        }
        //时间生效类型
        if(MkActivityEffectTypeEnum.WEEK.getCode().equals(bo.getEffectType()) || MkActivityEffectTypeEnum.MONTH.getCode().equals(bo.getEffectType())){
            if(ObjectUtil.isEmpty(bo.getEffectValueList())){
                throw new ServiceException("时间生效具体内容不能为空");
            }
        }else if(MkActivityEffectTypeEnum.TIME_RANGE.getCode().equals(bo.getEffectType())){
            //销售日
            RemoteRegionWhVo remoteRegionWhVo = remoteRegionWhService.queryById(bo.getOwnerId());
            LocalDate saleDate = SaleDateUtil.getSaleDate(remoteRegionWhVo.getSalesTimeEnd());
            if(ObjectUtil.isEmpty(bo.getStartDate()) || ObjectUtil.isEmpty(bo.getEndDate())){
                throw new ServiceException("生效开始日期和生效结束日期不能为空");
            }else if(bo.getStartDate().isAfter(bo.getEndDate())){
                throw new ServiceException("生效开始日期不能大于生效结束日期");
            }else if(bo.getStartDate().isBefore(saleDate) || bo.getEndDate().isBefore(saleDate)){
                throw new ServiceException("生效开始结束日期需大于等于当前销售日" + saleDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            }
        }
        //适用范围 1：全部物流线，2：指定物流线
        if("2".equals(bo.getSubScopeType()) && ObjectUtil.isEmpty(bo.getSuitThrongValueList())){
            throw new ServiceException("物流线不能为空");
        }
        //商品范围
        if(!MkActivityProductRangeEnum.ALL.getCode().equals(bo.getProductRange()) && ObjectUtil.isEmpty(bo.getActivitySkuBoList())){
            throw new ServiceException("商品列表不能为空");
        }
        //校验折扣范围
        ltime.forEach(ltimeBo -> {
            if(ltimeBo.getDr().compareTo(BigDecimal.ONE) < 0 || ltimeBo.getDr().compareTo(new BigDecimal("99")) > 0){
                throw new ServiceException("折扣范围为1% ~ 99%");
            }
        });
    }

    /**
     * 批量删除营销活动
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(ObjectUtil.isEmpty(ids)){
            return true;
        }
        if(isValid){
            LambdaQueryWrapper<MkActivity> qw = Wrappers.lambdaQuery();
            qw.ne(MkActivity::getStatus, MkActivityStatusEnum.WAIT.getCode());
            qw.in(MkActivity::getId, ids);
            if(baseMapper.exists(qw)){
                throw new ServiceException("活动状态不为待开始，不能删除");
            }
        }
        int i = baseMapper.deleteBatchIds(ids);
        LambdaUpdateWrapper<MkActivitySku> qw = Wrappers.lambdaUpdate();
        qw.in(MkActivitySku::getActivityId, ids);
        activitySkuMapper.delete(qw);
        summaryMapper.delete((Wrappers.lambdaUpdate(MkActivityDiscountSummary.class)
                .in(MkActivityDiscountSummary::getActivityId, ids)));
        return i > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void stopActivity(Long id) {
        if(ObjectUtil.isEmpty(id)){
            throw new ServiceException("活动id不能为空");
        }
        LambdaUpdateWrapper<MkActivity> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(MkActivity::getStatus, MkActivityStatusEnum.STOP.getCode())
                .eq(MkActivity::getId, id);
        baseMapper.update(updateWrapper);
        mkActivityDiscountSummaryService.updateActivityStatus(Collections.singletonList(id), MkActivityStatusEnum.STOP.getCode());
    }


    /**
     * 营销数据统计
     */
    @Override
    public MkDataStatVo mkDataStat(Long activityId, Integer type, Integer ownerId , LocalDateTime startDate, LocalDateTime endDate) {
        if (startDate == null || endDate == null) {
            throw new ServiceException("统计区间不能为空");
        }
        RemoteMkDataStatVo mkDataStat = remoteActivityService.getMkDataStat(activityId, type,ownerId, startDate, endDate);
        return BeanUtil.copyProperties(mkDataStat, MkDataStatVo.class);
    }

    /**
     * 营销订单数据统计
     */
    @Override
    public TableDataInfo<MkOrderDataStatVo> mkOrderDataStat(RemoteMkOrderDataStatBo bo) {
        TableDataInfo<RemoteMkOrderDataStatVo> infoList = remoteActivityService.getMkOrderDataStat(bo);
        if (infoList == null || CollectionUtils.isEmpty(infoList.getRows())) {
            return TableDataInfo.build();
        }
        List<MkOrderDataStatVo> list = BeanUtil.copyToList(infoList.getRows(), MkOrderDataStatVo.class);
        return TableDataInfo.build(list, infoList.getTotal());
    }

    /**
     * 总仓名称+创建年月日+0001
     * @param regionId 总仓主键
     * @param regionName 总仓名称
     */
    @Override
    public String generateActivityName(Long regionId, String regionName) {
        // 生成四位序列号（每个总仓每天独立计数）
        String dateStr = DateUtils.dateTimeNow(DateUtils.YYYYMMDD);
        String counterKey = String.format("mk_activity_name:%d:%s", regionId, dateStr);
        try {
            RAtomicLong atomic = RedisUtils.getClient().getAtomicLong(counterKey);
            long current = atomic.incrementAndGet();
            // 格式化为4位数字
            String seq = String.format("%04d", current);
            String name = String.format("%s%s%s", regionName, dateStr, seq);
            return name;
        } catch (Exception e) {
            throw new ServiceException("生成活动名称失败").setDetailMessage(e.getMessage());
        }
    }


    /**
     * 更新营销活动的生效 -开启活动
     */
    @Override
    //@Transactional(rollbackFor = Exception.class)
    public void updateActivityStatusStart() {
        LambdaQueryWrapper<MkActivity> lqw = Wrappers.lambdaQuery();
        lqw.eq(MkActivity::getStatus, MkActivityStatusEnum.WAIT.getCode()).orderByDesc(MkActivity::getId);

        int pageNum = 1;
        int pageSize = 50;
        boolean hasNext = true;

        while (hasNext) {
            PageQuery pageQuery = new PageQuery();
            pageQuery.setPageNum(pageNum);
            pageQuery.setPageSize(pageSize);
            IPage<MkActivityVo> page = baseMapper.selectVoPage(pageQuery.build(), lqw);
            List<MkActivityVo> records = page.getRecords();
            if (records == null || records.isEmpty()) {
                break;
            }
            //获取当前销售日
            Map<Long, String> regionWhMap = new HashMap<>();
            List<Long> regionWhIds = records.stream().map(MkActivityVo::getOwnerId).filter(Objects::nonNull).distinct().toList();
            List<RemoteRegionWhVo> regionWhList = remoteRegionWhService.selectRegionWhInfoByIds(regionWhIds);
            if(CollectionUtils.isNotEmpty(regionWhList)){
                regionWhMap = regionWhList.stream().collect(Collectors.toMap(RemoteRegionWhVo::getId, RemoteRegionWhVo::getSalesTimeEnd, (n1, n2) -> n1));
            }

            //过滤符合条件的
            Map<Long, String> finalRegionWhMap = regionWhMap;
            List<MkActivityVo> list = records.stream().filter(x -> isValidMkActivityVo(x,finalRegionWhMap,Boolean.TRUE)).toList();
            // 遍历当前页数据
            List<Long> activityIds = list.stream().map(MkActivityVo::getId).filter(Objects::nonNull).toList();
            if(CollectionUtils.isEmpty(activityIds)){
                pageNum++;
                continue;
            }
            transactionTemplate.execute(x->{
                //更新
                baseMapper.updateActivityStatus(activityIds , MkActivityStatusEnum.WAIT.getCode(), MkActivityStatusEnum.START.getCode());
                mkActivityDiscountSummaryService.updateActivityStatus(activityIds, MkActivityStatusEnum.START.getCode());
                return null;
            });
            hasNext = page.getCurrent() < page.getPages();
            pageNum++;
        }
    }


    /**
     * 更新营销活动的生效状态 - 结束活动
     */
    @Override
    //@Transactional(rollbackFor = Exception.class)
    public void updateActivityStatusEnd() {
        LambdaQueryWrapper<MkActivity> lqw = Wrappers.lambdaQuery();
        lqw.eq(MkActivity::getEffectType, MkActivityEffectTypeEnum.TIME_RANGE.getCode())
                .eq(MkActivity::getStatus, MkActivityStatusEnum.START.getCode())
                .lt(MkActivity::getEndDate,  LocalDate.now().plusDays(1))
                .orderByDesc(MkActivity::getId);

        int pageNum = 1;
        int pageSize = 50;
        boolean hasNext = true;

        while (hasNext) {
            PageQuery pageQuery = new PageQuery();
            pageQuery.setPageNum(pageNum);
            pageQuery.setPageSize(pageSize);
            IPage<MkActivityVo> page = baseMapper.selectVoPage(pageQuery.build(), lqw);
            List<MkActivityVo> records = page.getRecords();
            if (records == null || records.isEmpty()) {
                break;
            }
            //获取当前销售日
            Map<Long, String> regionWhMap = new HashMap<>();
            List<Long> regionWhIds = records.stream().map(MkActivityVo::getOwnerId).filter(Objects::nonNull).distinct().toList();
            List<RemoteRegionWhVo> regionWhList = remoteRegionWhService.selectRegionWhInfoByIds(regionWhIds);
            if(CollectionUtils.isNotEmpty(regionWhList)){
                regionWhMap = regionWhList.stream().collect(Collectors.toMap(RemoteRegionWhVo::getId, RemoteRegionWhVo::getSalesTimeEnd, (n1, n2) -> n1));
            }

            //过滤符合条件的
            Map<Long, String> finalRegionWhMap = regionWhMap;
            List<MkActivityVo> list = records.stream().filter(x -> isValidMkActivityVo(x,finalRegionWhMap,Boolean.FALSE)).toList();

            // 遍历当前页数据
            List<Long> activityIds = list.stream().map(MkActivityVo::getId).filter(Objects::nonNull).toList();
            if(CollectionUtils.isEmpty(activityIds)){
                pageNum++;
                continue;
            }
            transactionTemplate.execute(x -> {
                //更新
                baseMapper.updateActivityStatus(activityIds, MkActivityStatusEnum.START.getCode(), MkActivityStatusEnum.END.getCode());
                mkActivityDiscountSummaryService.updateActivityStatus(activityIds, MkActivityStatusEnum.END.getCode());
                return null;
            });
            // 判断是否还有下一页
            hasNext = page.getCurrent() < page.getPages();
            pageNum++;
        }
    }

    /**
     * 营销活动生效条件
     * @param x
     * @param finalRegionWhMap
     * @return
     */
    private boolean isValidMkActivityVo(MkActivityVo x,Map<Long, String> finalRegionWhMap,Boolean isStart) {
        if (x == null || x.getOwnerId() == null) {
            return false;
        }

        String regionWhPlanDate = finalRegionWhMap.get(x.getOwnerId());
        if (regionWhPlanDate == null) {
            return false;
        }

        LocalDate saleDate = SaleDateUtil.getSaleDate(regionWhPlanDate);
        if (saleDate == null) {
            return false;
        }

        DayOfWeek dayOfWeek = saleDate.getDayOfWeek();
        int dayOfMonth = saleDate.getDayOfMonth();

        if (StringUtils.isEmpty(x.getEffectValue())
                && !MkActivityEffectTypeEnum.TIME_RANGE.getCode().equals(x.getEffectType())) {
            return false;
        }

        List<Integer> effectValueList;
        try {
            effectValueList = Stream.of(x.getEffectValue().split(","))
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .map(Integer::valueOf)
                    .toList();
        } catch (NumberFormatException e) {
            // 可选：记录日志
            return false;
        }
        if (isStart) {
            if (MkActivityEffectTypeEnum.WEEK.getCode().equals(x.getEffectType())) {
                return effectValueList.stream().anyMatch(e -> e <= dayOfWeek.getValue());
            } else if (MkActivityEffectTypeEnum.MONTH.getCode().equals(x.getEffectType())) {
                return effectValueList.stream().anyMatch(e -> e <= dayOfMonth);
            } else if (MkActivityEffectTypeEnum.TIME_RANGE.getCode().equals(x.getEffectType())) {
                // saleDate 在区间内（包含等于）
                return ((x.getStartDate() == null || !saleDate.isBefore(x.getStartDate()))
                        && (x.getEndDate() == null || !saleDate.isAfter(x.getEndDate())));
            } else {
                return false;
            }
        }else {
            if (MkActivityEffectTypeEnum.WEEK.getCode().equals(x.getEffectType())) {
                return effectValueList.stream().allMatch(e -> e < dayOfWeek.getValue());
            } else if (MkActivityEffectTypeEnum.MONTH.getCode().equals(x.getEffectType())) {
                return effectValueList.stream().allMatch(e -> e < dayOfMonth);
            } else if (MkActivityEffectTypeEnum.TIME_RANGE.getCode().equals(x.getEffectType())) {
                // saleDate 在区间外
                return (x.getStartDate() != null && saleDate.isBefore(x.getStartDate()))
                        || (x.getEndDate() != null && saleDate.isAfter(x.getEndDate()));
            } else {
                return false;
            }
        }
    }


}
