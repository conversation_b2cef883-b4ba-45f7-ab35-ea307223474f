package cn.xianlink.marketing.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.xianlink.basic.api.RemoteRegionWhService;
import cn.xianlink.basic.api.domain.vo.RemoteRegionWhVo;
import cn.xianlink.common.api.util.SaleDateUtil;
import cn.xianlink.common.core.enums.YNStatusEnum;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.marketing.domain.MkActivity;
import cn.xianlink.marketing.domain.MkActivityDiscountSummary;
import cn.xianlink.marketing.domain.MkActivitySku;
import cn.xianlink.marketing.domain.bo.MkActivityLaunchTimeBo;
import cn.xianlink.marketing.domain.bo.MkActivityRuleConfigBo;
import cn.xianlink.marketing.domain.bo.MkActivityStatusBo;
import cn.xianlink.marketing.domain.vo.MkActivityStatusVo;
import cn.xianlink.marketing.mapper.MkActivityDiscountSummaryMapper;
import cn.xianlink.marketing.mapper.MkActivityMapper;
import cn.xianlink.marketing.mapper.MkActivitySkuMapper;
import cn.xianlink.marketing.service.IMkActivityDiscountSummaryService;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.*;

/**
 * 分销活动Service业务层处理
 * <AUTHOR>
 */
@CustomLog
@RequiredArgsConstructor
@Service
public class MkActivityDiscountSummaryServiceImpl extends ServiceImpl<MkActivityDiscountSummaryMapper, MkActivityDiscountSummary> implements IMkActivityDiscountSummaryService {

    private final MkActivityMapper activityMapper;

    private final MkActivitySkuMapper activitySkuMapper;

    @DubboReference
    private final RemoteRegionWhService remoteRegionWhService;

    /**
     * 创建活动
     * @param id 活动id
     */
    @Override
    public void activityCreate(Long id) {
        List<MkActivityDiscountSummary> list = new ArrayList<>();
        //获取活动信息
        MkActivity activity = activityMapper.selectById(id);
        if (activity == null) {
            throw new ServiceException("活动不存在");
        }
        //组装主表信息对象
        MkActivityDiscountSummary mainSummary = new MkActivityDiscountSummary();
        mainSummary.setActivityId(activity.getId());
        mainSummary.setActivityType(activity.getActivityType());
        mainSummary.setName(activity.getName());
        mainSummary.setOwnershipType(activity.getOwnershipType());
        mainSummary.setOwnerId(activity.getOwnerId());
        mainSummary.setScopeType(activity.getScopeType());
        mainSummary.setSuitThrongValue(activity.getSuitThrongValue());
        mainSummary.setActionType(activity.getActionType());
        mainSummary.setStatus(activity.getStatus());
        mainSummary.setStartDate(activity.getStartDate());
        mainSummary.setEndDate(activity.getEndDate());
        mainSummary.setEffectiveStartTime(activity.getEffectiveStartTime());
        mainSummary.setEffectiveEndTime(activity.getEffectiveEndTime());
        mainSummary.setEffectType(activity.getEffectType());
        mainSummary.setEffectValue(activity.getEffectValue());
        mainSummary.setChannel(activity.getChannel());
        mainSummary.setExtValue(activity.getExtValue());
        mainSummary.setScopeValue("*");
        mainSummary.setExclude(1);
        //阶梯配置
        List<MkActivityDiscountSummary> stepList = new ArrayList<>();
        if (StringUtils.isNotEmpty(activity.getRuleConfig())) {
            MkActivityRuleConfigBo stepBos = JSON.parseObject(activity.getRuleConfig(), MkActivityRuleConfigBo.class);
            mainSummary.setTargetQuantity(stepBos.getMcnt().longValue());
            for (MkActivityLaunchTimeBo stepBo : stepBos.getLtime()) {
                MkActivityDiscountSummary step = new MkActivityDiscountSummary();
                BeanUtils.copyProperties(mainSummary, step);
                step.setEffectiveStartTime(stepBo.getSt());
                step.setEffectiveEndTime(stepBo.getEt());
                step.setDiscountRate(stepBo.getDr().divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
                step.setMaxDiscountAmount(stepBo.getDc());
                stepList.add(step);
            }
        } else {
            stepList.add(mainSummary);
        }
        Map<Integer,String> scopeTypeMap = new HashMap<>();
        scopeTypeMap.put(1,"C:");
        scopeTypeMap.put(2,"P:");
        scopeTypeMap.put(3,"SS:");
        scopeTypeMap.put(4,"K:");
        LambdaQueryWrapper<MkActivitySku> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MkActivitySku::getActivityId, id);
        List<MkActivitySku> activitySkuList = activitySkuMapper.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(activitySkuList)) {
            for (MkActivityDiscountSummary s : stepList) {
                for (MkActivitySku activitySku : activitySkuList) {
                    MkActivityDiscountSummary s1 = new MkActivityDiscountSummary();
                    BeanUtils.copyProperties(s, s1);
                    s1.setExclude(activitySku.getExclude());
                    if (ObjectUtils.isNotNull(activitySku.getScopeValue()) && !Objects.equals(activitySku.getScopeValue(), "*")) {
                        s1.setScopeValue(scopeTypeMap.get(activitySku.getScopeType()) + activitySku.getScopeValue());
                    }
                    list.add(s1);
                }
            }
        } else {
            list.addAll(stepList);
        }
        //分500条新增
        List<List<MkActivityDiscountSummary>> l = ListUtil.split(list, 500);
        l.forEach(e -> baseMapper.batchInsert(e));
    }

    /**
     * 更新活动状态
     *
     * @param ids    活动id集合
     * @param status 活动状态
     */
    @Override
    public void updateActivityStatus(List<Long> ids, Integer status) {
        baseMapper.update(Wrappers.lambdaUpdate(MkActivityDiscountSummary.class)
                .set(MkActivityDiscountSummary::getStatus, status)
                .in(MkActivityDiscountSummary::getActivityId, ids)
                .eq(MkActivityDiscountSummary::getDelFlag, YNStatusEnum.DISABLE.getCode()));
    }

    /**
     * 更新活动
     */
    @Override
    public void activityUpdate(Long id) {
        //删掉原来的，再新增
        baseMapper.delete((Wrappers.lambdaUpdate(MkActivityDiscountSummary.class)
                .eq(MkActivityDiscountSummary::getActivityId, id)));
        this.activityCreate(id);
    }

    @Override
    public MkActivityStatusVo getActivityStatus(MkActivityStatusBo bo) {
        MkActivityStatusVo vo = new MkActivityStatusVo();
        //获取销售日
        RemoteRegionWhVo remoteRegionWhVo = remoteRegionWhService.queryById(bo.getRegionWhId());
        LocalDate saleDate = SaleDateUtil.getSaleDate(remoteRegionWhVo.getSalesTimeEnd());
        bo.setSaleDate(saleDate);
        List<MkActivityDiscountSummary> summaryList = baseMapper.getActivityStatus(bo);
        vo.setActivityStatus(0);
        if (CollectionUtil.isNotEmpty(summaryList)){
            LocalTime nowTime = LocalTime.now();
            int day = bo.getSaleDate().getDayOfMonth();
            int week = bo.getSaleDate().getDayOfWeek().getValue();
            for (MkActivityDiscountSummary summary : summaryList) {
                //过滤时间 固定周几
                if (summary.getEffectType().equals(3) && summary.getEffectValue() != null){
                    if (!Arrays.stream(summary.getEffectValue().split(",")).toList().contains(String.valueOf(week))){
                        continue;
                    }
                }
                //过滤时间 几号
                if (summary.getEffectType().equals(4) && summary.getEffectValue() != null) {
                    if (!Arrays.stream(summary.getEffectValue().split(",")).toList().contains(String.valueOf(day))) {
                        continue;
                    }
                }
                //命中就返回
                if (summary.getEffectiveStartTime().isBefore(nowTime)) {
                    vo.setActivityStatus(1);
                    return vo;
                }
                vo.setActivityStatus(1);
                if (vo.getActivityStartTime() != null && summary.getEffectiveStartTime().isBefore(vo.getActivityStartTime())) {
                    vo.setActivityStartTime(summary.getEffectiveStartTime());
                }else if (vo.getActivityStartTime() == null){
                    vo.setActivityStartTime(summary.getEffectiveStartTime());
                }

            }
        }
        return vo;
    }
}