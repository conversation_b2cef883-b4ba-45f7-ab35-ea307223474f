package cn.xianlink.marketing.service;

import cn.xianlink.marketing.domain.MkActivitySku;
import cn.xianlink.marketing.domain.vo.MkActivitySkuVo;
import cn.xianlink.marketing.domain.bo.MkActivitySkuBo;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 营销活动商品适用范围Service接口
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
public interface IMkActivitySkuService {

    /**
     * 查询营销活动商品适用范围
     */
    MkActivitySkuVo queryById(Long id);

    /**
     * 查询营销活动商品适用范围列表
     */
    TableDataInfo<MkActivitySkuVo> queryPageList(MkActivitySkuBo bo, PageQuery pageQuery);

    /**
     * 查询营销活动商品适用范围列表
     */
    List<MkActivitySkuVo> queryList(MkActivitySkuBo bo);

    /**
     * 新增营销活动商品适用范围
     */
    Boolean insertByBo(MkActivitySkuBo bo);

    /**
     * 修改营销活动商品适用范围
     */
    Boolean updateByBo(MkActivitySkuBo bo);

    /**
     * 校验并批量删除营销活动商品适用范围信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
