<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.marketing.mapper.MkActivityDiscountSummaryMapper">


    <insert id="batchInsert">
        INSERT INTO mk_activity_discount_summary (
        activity_id, activity_type, name, ownership_type, owner_id, scope_type,
        suit_throng_value, scope_value, action_type, exclude, limit_amount, promo_price,
        discount_rate, max_discount_amount, target_quantity, status, start_date, end_date,
        effect_type, effect_value, channel, ext_value,effective_start_time,effective_end_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.activityId}, #{item.activityType}, #{item.name}, #{item.ownershipType},
            #{item.ownerId}, #{item.scopeType}, #{item.suitThrongValue}, #{item.scopeValue},
            #{item.actionType}, #{item.exclude}, #{item.limitAmount}, #{item.promoPrice},
            #{item.discountRate}, #{item.maxDiscountAmount}, #{item.targetQuantity},
            #{item.status}, #{item.startDate}, #{item.endDate}, #{item.effectType},
            #{item.effectValue}, #{item.channel}, #{item.extValue},#{item.effectiveStartTime},
            #{item.effectiveEndTime}
            )
        </foreach>
    </insert>

    <select id="getSkuDiscount" resultType="cn.xianlink.marketing.domain.MkActivityDiscountSummary">
        select s.*
        from mk_activity_discount_summary s
        where del_flag = 0
        and status = 2
        and exclude = 1
        <if test="bo.regionId != null">
            and owner_id = #{bo.regionId} and ownership_type = 3
        </if>
        <if test="bo.ownershipType != null">
            and ownership_type = #{bo.ownershipType}
        </if>
        <if test="bo.activityType != null">
            and activity_type = #{bo.activityType}
        </if>

        <!-- skuInfo 匹配 -->
        <if test="bo.skuInfo != null and bo.skuInfo.size() > 0">
            and scope_value in
            <foreach item="item" collection="bo.skuInfo" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <!-- 时间范围匹配 -->
        <if test="bo.saleDate != null">
            and (s.start_date &lt;= #{bo.saleDate} or s.start_date is null)
            and (s.end_date &gt;= #{bo.saleDate} or s.end_date is null)
        </if>
        <if test="bo.time != null">
            and (
            (s.effective_start_time &lt;= s.effective_end_time AND #{bo.time}  BETWEEN s.effective_start_time AND s.effective_end_time)
            OR
            (s.effective_start_time > s.effective_end_time AND (#{bo.time}  &gt;= s.effective_start_time OR #{bo.time} &lt;= s.effective_end_time)))
        </if>
    </select>

    <select id="getActivityStatus" resultType="cn.xianlink.marketing.domain.MkActivityDiscountSummary">
        select s.*
        from mk_activity_discount_summary s
        where del_flag = 0
        and status = 2
        <if test="bo.regionWhId != null">
            and owner_id = #{bo.regionWhId} and ownership_type = 3
        </if>
        <if test="bo.type != null">
            and activity_type = #{bo.type}
        </if>
        <!-- 时间范围匹配 -->
        <if test="bo.saleDate != null">
            and (s.start_date &lt;= #{bo.saleDate} or s.start_date is null)
            and (s.end_date &gt;= #{bo.saleDate} or s.end_date is null)
        </if>
        and ((s.effective_start_time &lt;= s.effective_end_time
        and CURTIME() &lt;= s.effective_end_time or CURTIME() between s.effective_start_time and s.effective_end_time)
        or (s.effective_start_time > s.effective_end_time
        and CURTIME() between s.effective_end_time and s.effective_start_time))
        order by s.effective_start_time, s.effective_end_time, s.id
    </select>
</mapper>
