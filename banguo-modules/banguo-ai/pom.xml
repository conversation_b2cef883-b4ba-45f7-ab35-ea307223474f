<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.xianlink</groupId>
        <artifactId>banguo-modules</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>banguo-ai</artifactId>

    <description>
        banguo-ai AI模块
    </description>


    <properties>
        <!-- Spring AI -->
        <spring-ai.version>1.0.0</spring-ai.version>
        <!-- Spring AI Alibaba -->
        <spring-ai-alibaba.version>*******</spring-ai-alibaba.version>
    </properties>


    <dependencies>

        <!-- 测试依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- Spring Boot Undertow 替代 Tomcat -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
            <version>3.5.3</version>
        </dependency>

        <!--         排除 spring-boot-starter-web 中的 Tomcat -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>com.alibaba.cloud.ai</groupId>-->
        <!--            <artifactId>spring-ai-alibaba-starter-nacos2-mcp-server</artifactId>-->
        <!--            <version>${spring-ai-alibaba.version}</version>-->
        <!--        </dependency>-->


        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-starter-mcp-server-webmvc</artifactId>
            <version>${spring-ai.version}</version>
        </dependency>

        <!-- JSON Schema 生成器 - Spring AI MCP 需要 没有这个启动会报错？-->
        <dependency>
            <groupId>com.github.victools</groupId>
            <artifactId>jsonschema-generator</artifactId>
            <version>4.36.0</version>
        </dependency>

        <!-- Jackson 模块支持 - MCP 工具参数序列化需要 没有这个启动会报错？-->
        <dependency>
            <groupId>com.github.victools</groupId>
            <artifactId>jsonschema-module-jackson</artifactId>
            <version>4.36.0</version>
        </dependency>

        <!-- 阿里云百练 DashScope SDK -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>dashscope-sdk-java</artifactId>
            <version>2.20.6</version>
        </dependency>

        <!-- SpringCloud Alibaba Nacos -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Nacos Config -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>


        <!--        <dependency>-->
        <!--            <groupId>cn.xianlink</groupId>-->
        <!--            <artifactId>xianjian-common-elasticsearch</artifactId>-->
        <!--            <version>${xianjian.vision}</version>-->
        <!--        </dependency>-->

        <!--        <dependency>-->
        <!--            <groupId>cn.xianlink</groupId>-->
        <!--            <artifactId>xianjian-common-sentinel</artifactId>-->
        <!--            <version>${xianjian.vision}</version>-->
        <!--        </dependency>-->

        <!-- xianlink Common Log -->
        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-common-log</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-common-dict</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-common-doc</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <!--       <dependency>-->
        <!--           <groupId>org.springframework</groupId>-->
        <!--           <artifactId>spring-web</artifactId>-->
        <!--       </dependency>-->


        <!--        <dependency>-->
        <!--            <groupId>cn.xianlink</groupId>-->
        <!--            <artifactId>xianjian-common-web</artifactId>-->
        <!--            <version>${xianjian.vision}</version>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-common-wechat</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-common-redis</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-common-mybatis</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-common-dubbo</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-common-seata</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-common-idempotent</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-common-tenant</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-common-security</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-common-translation</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-common-rocketmq</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-common-sensitive</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-common-encrypt</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <!-- xianlink Api System -->
        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-api-system</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-api-resource</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>banguo-api-order</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>banguo-api-product</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>banguo-api-ai</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>banguo-api-basic</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>banguo-api-marketing</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>banguo-api-trade</artifactId>
        </dependency>

        <!-- 微信小程序客服消息 -->
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-miniapp</artifactId>
            <version>4.6.0</version>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>

        <!--other-->

        <!--        <dependency>-->
        <!--            <groupId>io.swagger</groupId>-->
        <!--            <artifactId>swagger-annotations</artifactId>-->
        <!--            <version>1.6.2</version>-->
        <!--        </dependency>-->

        <!--excel-->
        <!--        <dependency>-->
        <!--            <groupId>org.jxls</groupId>-->
        <!--            <artifactId>jxls-poi</artifactId>-->
        <!--            <version>2.12.0</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>org.jxls</groupId>-->
        <!--            <artifactId>jxls-jexcel</artifactId>-->
        <!--            <version>1.0.9</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>org.jxls</groupId>-->
        <!--            <artifactId>jxls-reader</artifactId>-->
        <!--            <version>2.0.6</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>org.jxls</groupId>-->
        <!--            <artifactId>jxls</artifactId>-->
        <!--            <version>2.12.0</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>org.ostermiller</groupId>-->
        <!--            <artifactId>utils</artifactId>-->
        <!--            <version>1.07.00</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>cn.xianlink</groupId>-->
        <!--            <artifactId>banguo-api-bi</artifactId>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>org.junit.jupiter</groupId>-->
        <!--            <artifactId>junit-jupiter</artifactId>-->
        <!--            <scope>test</scope>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>org.junit.jupiter</groupId>-->
        <!--            <artifactId>junit-jupiter</artifactId>-->
        <!--            <scope>test</scope>-->
        <!--        </dependency>-->

        <!-- JUnit 5 测试框架 -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- Mockito 测试框架 -->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- AssertJ 断言库 -->
        <dependency>
            <groupId>org.assertj</groupId>
            <artifactId>assertj-core</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- Spring Test 支持 -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <!-- Maven Profiles 配置 -->
    <profiles>
        <!-- 测试环境 Profile - 排除 Nacos 依赖 -->
        <profile>
            <id>test</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <dependencies>
                <!-- 排除 Nacos Discovery -->
                <dependency>
                    <groupId>com.alibaba.cloud</groupId>
                    <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
                    <scope>provided</scope>
                </dependency>
                <!-- 排除 Nacos Config -->
                <dependency>
                    <groupId>com.alibaba.cloud</groupId>
                    <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
                    <scope>provided</scope>
                </dependency>
            </dependencies>
        </profile>

        <!-- 生产环境 Profile - 包含 Nacos 依赖 -->
        <profile>
            <id>prod</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <!-- 生产环境使用默认的 Nacos 依赖配置 -->
        </profile>
    </profiles>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- 测试插件配置 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${maven-surefire-plugin.version}</version>
                <configuration>
                    <argLine>-Dfile.encoding=UTF-8</argLine>
                    <!-- 支持local和test标签的测试 -->
                    <groups>local,test</groups>
                    <!-- 排除标签 -->
                    <excludedGroups>exclude</excludedGroups>
                    <!-- 启用并行测试执行 -->
                    <parallel>methods</parallel>
                    <threadCount>2</threadCount>
                    <!-- 测试失败时继续执行其他测试 -->
                    <testFailureIgnore>false</testFailureIgnore>
                    <!-- 跳过测试的条件 -->
                    <skipTests>false</skipTests>
                    <!-- 包含的测试类模式 -->
                    <includes>
                        <include>**/*Test.java</include>
                        <include>**/*Tests.java</include>
                    </includes>
                    <!-- 系统属性 -->
                    <systemPropertyVariables>
                        <spring.profiles.active>test</spring.profiles.active>
                    </systemPropertyVariables>
                </configuration>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <excludes>
                    <exclude>**/*.xlsx</exclude>
                </excludes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
                <includes>
                    <include>**/*.xlsx</include>
                </includes>
            </resource>
        </resources>
    </build>

</project>