# 测试环境配置文件
# 配合 Maven test profile 使用，彻底排除 Nacos 依赖
# 使用方式: mvn test -Ptest 或设置 spring.profiles.active=test

# 测试环境配置
spring:
  # 禁用特定自动装配
  autoconfigure:
    exclude:
      - org.apache.dubbo.spring.boot.autoconfigure.DubboAutoConfiguration
      - org.apache.dubbo.spring.boot.autoconfigure.DubboServiceAutoConfiguration
      - org.apache.dubbo.spring.boot.autoconfigure.DubboReferenceAutoConfiguration
      - org.apache.dubbo.spring.boot.autoconfigure.DubboMetadataAutoConfiguration
      - org.apache.dubbo.spring.boot.autoconfigure.DubboRelaxedBindingAutoConfiguration
      - org.redisson.spring.starter.RedissonAutoConfigurationV2
      - com.baomidou.lock.spring.boot.autoconfigure.LockAutoConfiguration
      - com.alibaba.cloud.nacos.NacosConfigAutoConfiguration
      - com.alibaba.cloud.nacos.NacosDiscoveryAutoConfiguration
      - com.alibaba.cloud.nacos.NacosServiceAutoConfiguration

  # 禁用actuator中的Dubbo端点
  main:
    allow-bean-definition-overriding: true

  # 数据源配置（使用H2内存数据库进行测试）
  # datasource:
  #   driver-class-name: org.h2.Driver
  #   url: jdbc:h2:mem:testdb;MODE=MySQL;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE;INIT=CREATE SCHEMA IF NOT EXISTS PUBLIC\\;SET SCHEMA PUBLIC
  #   username: sa
  #   password: 
  redis:
    host: 127.0.0.1
    port: 6379
    enabled: false
    # 禁用redis自动装配
    # 这样Redisson相关Bean不会被创建
    # 如果有其它starter依赖redis，也会被禁用

  # H2控制台配置（可选，用于调试）
  h2:
    console:
      enabled: true
      path: /h2-console

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true

  # SQL初始化配置
  sql:
    init:
      mode: always
      schema-locations: classpath:schema-h2.sql
      continue-on-error: true

  # MyBatis-Plus配置
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

  # 动态数据源配置，显式声明H2为主数据源
  # 这样dynamic-datasource就能识别到主库
  datasource:
    dynamic:
      primary: master
      strict: true
      datasource:
        master:
          driver-class-name: org.h2.Driver
          url: jdbc:h2:mem:testdb;MODE=MySQL;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE;INIT=CREATE SCHEMA IF NOT EXISTS PUBLIC\;SET SCHEMA PUBLIC
          username: sa
          password:

# 禁用分布式锁和Redisson自动装配，避免依赖Redis
lock:
  enabled: false
redisson:
  enabled: false
  config: ""

# 禁用Nacos配置中心
nacos:
  config:
    enabled: false
    server-addr: N/A
    namespace: N/A
    group: N/A
    file-extension: N/A
    shared-configs: [ ]
    extension-configs: [ ]
  discovery:
    enabled: false
    server-addr: N/A
    namespace: N/A
    group: N/A
  client:
    enabled: false
    server-addr: N/A
    namespace: N/A
    group: N/A

# 禁用Dubbo所有外部功能
# 彻底关闭注册中心、配置中心、元数据中心
# 只保留本地Bean，不暴露服务
# 这样不会有任何外部连接

dubbo:
  application:
    name: banguo-ai-test
  protocol:
    name: dubbo
    port: -1
  registry:
    address: N/A
    register: false
    subscribe: false
    check: false
    dynamic: false
    simplified: true
    group: DUBBO_GROUP
    parameters:
      namespace: test
  config-center:
    address: N/A
    enabled: false
  metadata-report:
    enabled: false
  provider:
    register: false
  consumer:
    check: false
  enabled: false
  # 禁用所有Dubbo相关的自动装配
  autoconfigure:
    enabled: false
  # 禁用Dubbo的actuator端点
  endpoint:
    enabled: false

seata:
  enabled: false

# 日志配置
logging:
  level:
    cn.xianlink.ai: DEBUG
    org.springframework: WARN
    com.baomidou: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
    com.alibaba.nacos: ERROR
    com.alibaba.cloud.nacos: ERROR
    org.apache.dubbo: WARN
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# MyBatis-Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0 