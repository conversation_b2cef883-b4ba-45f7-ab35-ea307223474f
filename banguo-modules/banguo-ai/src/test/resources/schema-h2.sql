-- H2数据库初始化脚本
-- 客服工单表
CREATE TABLE IF NOT EXISTS service_ticket
(
    id
    BIGINT
    AUTO_INCREMENT
    PRIMARY
    KEY,
    ticket_no
    VARCHAR
(
    32
) NOT NULL,
    customer_name VARCHAR
(
    100
),
    customer_phone VARCHAR
(
    20
),
    wechat_openid VARCHAR
(
    64
),
    title VARCHAR
(
    50
),
    problem_type VARCHAR
(
    50
),
    problem_description TEXT,
    conversation_history TEXT,
    priority_level INT DEFAULT 1,
    status INT DEFAULT 1,
    ai_assessment TEXT,
    follow_up_time TIMESTAMP,
    resolved_time TIMESTAMP,
    remark TEXT,
    tenant_id VARCHAR
(
    32
) DEFAULT 'test-tenant',
    deleted INT DEFAULT 0,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    creator_id INT DEFAULT 0,
    updater_id INT DEFAULT 0,
    create_code VARCHAR
(
    64
),
    create_name <PERSON><PERSON><PERSON><PERSON>
(
    64
),
    update_code VARCHAR
(
    64
),
    update_name VA<PERSON>HAR
(
    64
)
    );

-- 创建唯一索引
CREATE UNIQUE INDEX IF NOT EXISTS uk_ticket_no ON service_ticket (ticket_no);
CREATE INDEX IF NOT EXISTS idx_customer_phone ON service_ticket (customer_phone);
CREATE INDEX IF NOT EXISTS idx_wechat_openid ON service_ticket (wechat_openid);

-- 工单处理记录表
CREATE TABLE IF NOT EXISTS ticket_process_log
(
    id
    BIGINT
    AUTO_INCREMENT
    PRIMARY
    KEY,
    ticket_id
    BIGINT
    NOT
    NULL,
    ticket_no
    VARCHAR
(
    32
) NOT NULL,
    action_type VARCHAR
(
    20
) NOT NULL,
    action_description TEXT,
    operator_id INT DEFAULT 0,
    deleted INT DEFAULT 0,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    creator_id INT DEFAULT 0,
    updater_id INT DEFAULT 0
    );

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_ticket_id ON ticket_process_log (ticket_id);
CREATE INDEX IF NOT EXISTS idx_ticket_no ON ticket_process_log (ticket_no);
CREATE INDEX IF NOT EXISTS idx_action_type ON ticket_process_log (action_type); 