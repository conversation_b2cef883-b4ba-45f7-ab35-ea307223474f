# JSON性能测试指南

## 测试概述

JsonPerformanceTest 是一个全面的JSON性能测试工具，用于对比不同JSON处理方式的性能差异。

## 测试内容

### 1. 性能对比测试 (performanceComparison)

- **JsonUtil方式**: 使用统一配置的JsonUtil工具类
- **直接ObjectMapper**: 每次创建新的ObjectMapper实例
- **静态方法**: 使用JsonUtil的静态方法

### 2. 内存使用测试 (memoryUsageTest)

- 监控JSON操作过程中的内存使用情况
- 测试内存泄漏和垃圾回收效果

### 3. JSON验证测试 (jsonValidationTest)

- 测试各种JSON格式的验证能力
- 包含有效和无效的JSON字符串

## 运行方法

### 方法1: Maven命令运行

```bash
# 运行特定测试类
mvn test -Dtest=JsonPerformanceTest

# 运行特定测试方法
mvn test -Dtest=JsonPerformanceTest#performanceComparison

# 运行所有性能测试
mvn test -Dtest=JsonPerformanceTest#*
```

### 方法2: IDE运行

1. 在IDEA中打开 `JsonPerformanceTest.java`
2. 右键点击类名或方法名
3. 选择 "Run" 或 "Debug"

### 方法3: 命令行直接运行

```bash
# 编译项目
mvn compile test-compile

# 运行测试运行器（需要Spring Boot环境）
java -cp target/classes:target/test-classes cn.xianlink.ai.utils.JsonPerformanceTestRunner
```

## 测试参数

可以在测试类中调整以下参数：

```java
private static final int TEST_ITERATIONS = 10000;  // 测试迭代次数
private static final int WARMUP_ITERATIONS = 1000; // JVM预热次数
```

## 期望输出

测试完成后会输出类似以下的性能对比结果：

```
==== JSON性能测试开始 ====
测试次数: 10000, 预热次数: 1000
开始JVM预热...
JVM预热完成
==== 开始正式性能测试 ====
---- 序列化性能测试 ----
=== 序列化性能对比结果 ===
JsonUtil方式: 234ms
直接ObjectMapper: 456ms
静态方法: 567ms
JsonUtil vs 直接ObjectMapper: 48.68% 更快
JsonUtil vs 静态方法: 58.73% 更快

---- 反序列化性能测试 ----
=== 反序列化性能对比结果 ===
JsonUtil方式: 187ms
直接ObjectMapper: 298ms
静态方法: 345ms
JsonUtil vs 直接ObjectMapper: 37.25% 更快
JsonUtil vs 静态方法: 45.80% 更快

==== JSON性能测试完成 ====
```

## 性能分析

### JsonUtil的优势

1. **配置复用**: 使用预配置的ObjectMapper，避免重复创建
2. **性能优化**: 统一的配置降低了序列化/反序列化开销
3. **内存效率**: 减少对象创建，降低GC压力

### 直接ObjectMapper的劣势

1. **重复创建**: 每次new ObjectMapper()造成额外开销
2. **配置缺失**: 缺少优化配置可能影响性能
3. **内存浪费**: 大量临时对象增加GC负担

## 注意事项

1. **JVM预热**: 测试包含JVM预热过程，确保结果准确性
2. **GC影响**: 测试之间会执行System.gc()减少垃圾回收影响
3. **测试环境**: 建议在相对稳定的环境中运行，避免其他进程干扰
4. **多次运行**: 建议多次运行取平均值，减少偶然因素影响

## 故障排除

### 编译错误

- 确保所有依赖项已正确引入
- 检查Spring Boot测试环境配置

### 运行时错误

- 确保数据库连接配置正确（如果需要）
- 检查日志配置是否正确

### 性能异常

- 检查JVM内存设置: `-Xmx2g -Xms1g`
- 关闭不必要的后台程序
- 确保测试数据大小合适 