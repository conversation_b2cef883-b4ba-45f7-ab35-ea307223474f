# H2数据库测试配置指南

## 概述

本指南说明如何在AI模块中使用H2内存数据库进行测试，确保测试数据能够正确存储和查询。

## 配置说明

### 1. 依赖配置

在 `pom.xml` 中已经添加了H2数据库依赖：

```xml
<dependency>
    <groupId>com.h2database</groupId>
    <artifactId>h2</artifactId>
    <scope>test</scope>
</dependency>
```

### 2. 测试配置文件

测试配置文件位于：`src/test/resources/application-test.yml`

主要配置项：

- **数据源配置**: 使用H2内存数据库
- **H2控制台**: 启用H2控制台用于调试
- **SQL初始化**: 自动执行schema-h2.sql创建表结构
- **日志配置**: 显示SQL执行日志

### 3. 数据库表结构

H2数据库初始化脚本：`src/test/resources/schema-h2.sql`

包含以下表：

- `service_ticket`: 客服工单表
- `ticket_process_log`: 工单处理记录表

## 运行测试

### 方法1: 运行集成测试

```bash
# 进入AI模块目录
cd banguo-modules/banguo-ai

# 运行集成测试
mvn test -Dtest=ServiceTicketServiceImplIntegrationTest

# 运行所有测试
mvn test
```

### 方法2: 运行特定测试方法

```bash
# 运行特定的测试方法
mvn test -Dtest=ServiceTicketServiceImplIntegrationTest#testCreateTicket_Integration

# 运行Mock测试
mvn test -Dtest=ServiceTicketServiceImplTest
```

### 方法3: IDE中运行

1. 在IDEA中打开测试类
2. 右键点击类名或方法名
3. 选择 "Run" 或 "Debug"

## 测试类型说明

### 1. Mock测试 (ServiceTicketServiceImplTest)

- 使用Mockito框架
- 模拟数据库操作
- 测试业务逻辑
- 不依赖真实数据库

### 2. 集成测试 (ServiceTicketServiceImplIntegrationTest)

- 使用真实的H2数据库
- 测试完整的数据库操作
- 验证数据持久化
- 使用@Transactional确保测试数据隔离

## 调试技巧

### 1. 查看H2控制台

测试运行时，可以访问H2控制台查看数据库内容：

- URL: http://localhost:8080/h2-console
- JDBC URL: jdbc:h2:mem:testdb
- 用户名: sa
- 密码: (空)

### 2. 查看SQL日志

测试配置中启用了SQL日志，可以在控制台看到：

- 执行的SQL语句
- 参数绑定信息
- 执行时间

### 3. 数据库内容验证

在集成测试中，可以通过以下方式验证数据：

```java
// 验证数据是否保存到数据库
ServiceTicket savedTicket = serviceTicketService.getByTicketNo(result.getTicketNo());
assertNotNull(savedTicket);
assertEquals("期望的值", savedTicket.getFieldName());
```

## 常见问题

### 1. 数据库连接失败

检查配置：

- 确保H2依赖已添加
- 检查application-test.yml配置
- 确认schema-h2.sql文件存在

### 2. 表不存在

检查：

- schema-h2.sql是否正确
- SQL初始化配置是否正确
- 表名和字段名是否匹配

### 3. 测试数据不持久

确保：

- 使用@Transactional注解
- 测试方法中调用了实际的数据库操作
- 没有使用Mock对象

## 最佳实践

1. **测试隔离**: 每个测试方法使用@Transactional确保数据隔离
2. **数据验证**: 在集成测试中验证数据是否真正保存到数据库
3. **异常测试**: 测试各种异常情况
4. **性能考虑**: 集成测试比Mock测试慢，合理使用
5. **日志调试**: 启用SQL日志帮助调试数据库操作

## 示例测试流程

```java
@Test
@DisplayName("集成测试 - 创建工单并保存到数据库")
void testCreateTicket_Integration() {
    // 1. 准备测试数据
    CustomerInfoVo customerInfo = new CustomerInfoVo();
    customerInfo.setCustomerName("测试用户");
    
    // 2. 执行被测试的方法
    TicketCreateResultVo result = serviceTicketService.createTicket(
        customerInfo, "问题描述", "问题类型", null, "AI评估");
    
    // 3. 验证返回结果
    assertTrue(result.getSuccess());
    assertNotNull(result.getTicketNo());
    
    // 4. 验证数据是否真正保存到数据库
    ServiceTicket savedTicket = serviceTicketService.getByTicketNo(result.getTicketNo());
    assertNotNull(savedTicket);
    assertEquals("测试用户", savedTicket.getCustomerName());
}
``` 