### 百练AI测试接口
### 用于测试BailianController中的各种AI接口

### 变量定义
@host = localhost:9211
@baseUrl = http://{{host}}/ai/test/bailian

### 1. 健康检查接口
GET {{baseUrl}}/health
Accept: application/json

###

### 2. 获取测试用例列表
GET {{baseUrl}}/test-cases
Accept: application/json

###

### 3. 测试百练智能体API调用 - 基础测试
POST {{baseUrl}}/app/test
Content-Type: application/json

{
  "prompt": "你好，请简单介绍一下自己"
}

###

### 4. 测试百练智能体API调用 - 带会话ID
POST {{baseUrl}}/app/test
Content-Type: application/json

{
  "prompt": "请继续我们刚才的对话",
  "sessionId": "test-session-001"
}

###

### 5. 测试百练智能体API调用 - 复杂问题
POST {{baseUrl}}/app/test
Content-Type: application/json

{
  "prompt": "请详细介绍一下Spring Boot的核心特性和优势，以及它在微服务架构中的作用"
}

###

### 6. 测试百练模型API简单调用 - 默认模型
POST {{baseUrl}}/model/simple
Content-Type: application/json

{
  "prompt": "用一句话介绍什么是人工智能"
}

###

### 7. 测试百练模型API简单调用 - 指定qwen-plus模型
POST {{baseUrl}}/model/simple
Content-Type: application/json

{
  "prompt": "请解释一下什么是大语言模型",
  "modelName": "qwen-plus"
}

###

### 8. 测试百练模型API简单调用 - 指定qwen-max模型
POST {{baseUrl}}/model/simple
Content-Type: application/json

{
  "prompt": "请写一首关于人工智能的现代诗",
  "modelName": "qwen-max"
}

###

### 9. 测试百练模型API简单调用 - 指定qwen-turbo模型
POST {{baseUrl}}/model/simple
Content-Type: application/json

{
  "prompt": "简单介绍一下Java的发展历程",
  "modelName": "qwen-turbo"
}

###

### 10. 测试百练模型API多轮对话 - Java场景
POST {{baseUrl}}/model/multi-turn
Content-Type: application/json

{
  "scenario": "java"
}

###

### 11. 测试百练模型API多轮对话 - Java场景 + 指定模型
POST {{baseUrl}}/model/multi-turn
Content-Type: application/json

{
  "scenario": "java",
  "modelName": "qwen-plus"
}

###

### 12. 测试百练模型API多轮对话 - 创意写作场景
POST {{baseUrl}}/model/multi-turn
Content-Type: application/json

{
  "scenario": "creative"
}

###

### 13. 测试百练模型API多轮对话 - 创意写作场景 + 指定模型
POST {{baseUrl}}/model/multi-turn
Content-Type: application/json

{
  "scenario": "creative",
  "modelName": "qwen-max"
}

###

### 14. 测试百练模型API多轮对话 - 默认场景
POST {{baseUrl}}/model/multi-turn
Content-Type: application/json

{
  "scenario": "default"
}

###

### 15. 测试不同模型的性能对比 - 诗词创作
POST {{baseUrl}}/model/compare
Content-Type: application/json

{
  "prompt": "请写一首关于春天的五言绝句"
}

###

### 16. 测试不同模型的性能对比 - 技术问答
POST {{baseUrl}}/model/compare
Content-Type: application/json

{
  "prompt": "解释什么是微服务架构以及它的优缺点"
}

###

### 17. 测试不同模型的性能对比 - 代码生成
POST {{baseUrl}}/model/compare
Content-Type: application/json

{
  "prompt": "用Java写一个简单的单例模式实现"
}

###

### 18. 测试不同模型的性能对比 - 创意写作
POST {{baseUrl}}/model/compare
Content-Type: application/json

{
  "prompt": "写一个关于未来科技城市的简短故事开头"
}

###

### 19. 测试不同模型的性能对比 - 逻辑推理
POST {{baseUrl}}/model/compare
Content-Type: application/json

{
  "prompt": "如果所有玫瑰都是红色的，而有些花是玫瑰，那么能得出什么结论？"
}

###

### 20. 压力测试 - 长文本处理
POST {{baseUrl}}/model/simple
Content-Type: application/json

{
  "prompt": "请详细分析微服务架构相比单体架构的优势和挑战，包括技术实现、团队组织、运维部署、性能监控、数据一致性、服务治理等多个维度，并给出具体的实施建议和最佳实践",
  "modelName": "qwen-plus"
}

###

### 21. 边界测试 - 空字符串
POST {{baseUrl}}/model/simple
Content-Type: application/json

{
  "prompt": ""
}

###

### 22. 边界测试 - 非常短的问题
POST {{baseUrl}}/model/simple
Content-Type: application/json

{
  "prompt": "你好"
}

###

### 23. 错误测试 - 不支持的模型名
POST {{baseUrl}}/model/simple
Content-Type: application/json

{
  "prompt": "测试错误处理",
  "modelName": "invalid-model-name"
}

### 