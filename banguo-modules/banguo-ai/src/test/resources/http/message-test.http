### 消息管理接口测试文件
### 基础URL配置
@baseUrl = http://localhost:9209
@contentType = application/json

### ==========================================
### 消息会话管理接口测试
### ==========================================

### 1. 查询消息会话列表
POST {{baseUrl}}/ai/admin/support/message-session/list
Content-Type: {{contentType}}

{
  "pageNum": 1,
  "pageSize": 10,
  "sessionId": "",
  "openId": "",
  "userId": "",
  "nickname": "",
  "phone": "",
  "sessionStatus": null,
  "createTimeStart": null,
  "createTimeEnd": null
}

###

### 2. 查询消息会话详情
GET {{baseUrl}}/ai/admin/support/message-session/1
Content-Type: {{contentType}}

###

### 3. 新增消息会话
POST {{baseUrl}}/ai/admin/support/message-session
Content-Type: {{contentType}}

{
  "sessionId": "session_001",
  "openId": "openid_001",
  "userId": "user_001",
  "nickname": "测试用户",
  "phone": "13800138000",
  "title": "测试会话",
  "questionType": "订单查询",
  "description": "这是一个测试会话",
  "sessionStatus": 1,
  "lastMessageTime": 1640995200000,
  "messageCount": 0
}

###

### 4. 修改消息会话
PUT {{baseUrl}}/ai/admin/support/message-session
Content-Type: {{contentType}}

{
  "id": 1,
  "sessionId": "session_001",
  "openId": "openid_001",
  "userId": "user_001",
  "nickname": "测试用户-修改",
  "phone": "13800138000",
  "title": "测试会话-修改",
  "questionType": "商品咨询",
  "description": "这是一个修改后的测试会话",
  "sessionStatus": 2,
  "lastMessageTime": 1640995200000,
  "messageCount": 5
}

###

### 5. 删除消息会话
DELETE {{baseUrl}}/ai/admin/support/message-session/1
Content-Type: {{contentType}}

###

### 6. 批量删除消息会话
DELETE {{baseUrl}}/ai/admin/support/message-session/batch/1,2,3
Content-Type: {{contentType}}

###

### 7. 根据open_id获取消息会话
GET {{baseUrl}}/ai/admin/support/message-session/by-open-id/openid_001
Content-Type: {{contentType}}

###

### 8. 根据user_id获取消息会话
GET {{baseUrl}}/ai/admin/support/message-session/by-user-id/user_001
Content-Type: {{contentType}}

###

### 9. 更新会话状态
PUT {{baseUrl}}/ai/admin/support/message-session/update-status?sessionId=session_001&sessionStatus=3
Content-Type: {{contentType}}

###

### ==========================================
### 消息记录管理接口测试
### ==========================================

### 10. 查询消息记录列表
POST {{baseUrl}}/ai/admin/support/message-history/list
Content-Type: {{contentType}}

{
  "pageNum": 1,
  "pageSize": 10,
  "sessionId": "",
  "msgId": "",
  "toUserName": "",
  "fromUserName": "",
  "msgType": "",
  "isReply": null,
  "isSend": null,
  "createTimeStart": null,
  "createTimeEnd": null
}

###

### 11. 查询消息记录详情
GET {{baseUrl}}/ai/admin/support/message-history/1
Content-Type: {{contentType}}

###

### 12. 新增消息记录
POST {{baseUrl}}/ai/admin/support/message-history
Content-Type: {{contentType}}

{
  "sessionId": "session_001",
  "msgId": "msg_001",
  "toUserName": "to_user_001",
  "fromUserName": "from_user_001",
  "createTimestamp": 1640995200000,
  "msgType": "text",
  "content": "这是一条测试消息",
  "picUrl": "",
  "mediaId": "",
  "isReply": 0,
  "isSend": 1,
  "linkInfo": "",
  "cardInfo": ""
}

###

### 13. 修改消息记录
PUT {{baseUrl}}/ai/admin/support/message-history
Content-Type: {{contentType}}

{
  "id": 1,
  "sessionId": "session_001",
  "msgId": "msg_001",
  "toUserName": "to_user_001",
  "fromUserName": "from_user_001",
  "createTimestamp": 1640995200000,
  "msgType": "text",
  "content": "这是一条修改后的测试消息",
  "picUrl": "",
  "mediaId": "",
  "isReply": 1,
  "isSend": 1,
  "linkInfo": "",
  "cardInfo": ""
}

###

### 14. 删除消息记录
DELETE {{baseUrl}}/ai/admin/support/message-history/1
Content-Type: {{contentType}}

###

### 15. 批量删除消息记录
DELETE {{baseUrl}}/ai/admin/support/message-history/batch/1,2,3
Content-Type: {{contentType}}

###

### 16. 根据会话ID获取消息记录列表
GET {{baseUrl}}/ai/admin/support/message-history/by-session-id/session_001
Content-Type: {{contentType}}

###

### 17. 根据消息ID获取消息记录
GET {{baseUrl}}/ai/admin/support/message-history/by-msg-id/msg_001
Content-Type: {{contentType}}

###

### 18. 根据发送方微信号获取消息记录列表
GET {{baseUrl}}/ai/admin/support/message-history/by-from-user-name/from_user_001
Content-Type: {{contentType}}

###

### 19. 更新消息发送状态
PUT {{baseUrl}}/ai/admin/support/message-history/update-send-status?msgId=msg_001&isSend=1
Content-Type: {{contentType}}

###

### ==========================================
### 前端接口测试
### ==========================================

### 20. 创建消息会话（前端接口）
POST {{baseUrl}}/ai/support/message-session/create
Content-Type: {{contentType}}

{
  "sessionId": "session_002",
  "openId": "openid_002",
  "customerId": "customer_002",
  "nickname": "前端测试用户",
  "phone": "13800138001",
  "title": "前端测试会话",
  "description": "这是一个前端测试会话",
  "sessionStatus": 1,
  "lastMessageTime": 1640995200000,
  "messageCount": 0
}

###

### 21. 根据open_id获取消息会话（前端接口）
GET {{baseUrl}}/ai/support/message-session/by-open-id/openid_002
Content-Type: {{contentType}}

###

### 22. 根据customer_id获取消息会话（前端接口）
GET {{baseUrl}}/ai/support/message-session/by-customer-id/customer_002
Content-Type: {{contentType}}

###

### 23. 更新会话状态（前端接口）
PUT {{baseUrl}}/ai/support/message-session/update-status?sessionId=session_002&sessionStatus=2
Content-Type: {{contentType}}

###

### 24. 更新最后消息时间（前端接口）
PUT {{baseUrl}}/ai/support/message-session/update-last-message-time?sessionId=session_002&lastMessageTime=1640995300000
Content-Type: {{contentType}}

###

### 25. 创建消息记录（前端接口）
POST {{baseUrl}}/ai/support/message-history/create
Content-Type: {{contentType}}

{
  "sessionId": "session_002",
  "msgId": "msg_002",
  "toUserName": "to_user_002",
  "fromUserName": "from_user_002",
  "createTimestamp": 1640995300000,
  "msgType": "text",
  "content": "这是一条前端测试消息",
  "picUrl": "",
  "mediaId": "",
  "isReply": 0,
  "isSend": 1,
  "linkInfo": "",
  "cardInfo": ""
}

###

### 26. 根据会话ID获取消息记录列表（前端接口）
GET {{baseUrl}}/ai/support/message-history/by-session-id/session_002
Content-Type: {{contentType}}

###

### 27. 根据消息ID获取消息记录（前端接口）
GET {{baseUrl}}/ai/support/message-history/by-msg-id/msg_002
Content-Type: {{contentType}}

###

### 28. 根据发送方微信号获取消息记录列表（前端接口）
GET {{baseUrl}}/ai/support/message-history/by-from-user-name/from_user_002
Content-Type: {{contentType}}

###

### 29. 更新消息发送状态（前端接口）
PUT {{baseUrl}}/ai/support/message-history/update-send-status?msgId=msg_002&isSend=0
Content-Type: {{contentType}}

###

### ==========================================
### 测试用例 - 正常情况
### ==========================================

### 测试1: 正常创建消息会话
POST {{baseUrl}}/ai/admin/support/message-session
Content-Type: {{contentType}}

{
  "sessionId": "test_session_001",
  "openId": "test_openid_001",
  "customerId": "test_customer_001",
  "nickname": "测试用户1",
  "phone": "13800138000",
  "title": "测试会话1",
  "description": "这是一个测试会话",
  "sessionStatus": 1,
  "lastMessageTime": 1640995200000,
  "messageCount": 0
}

###

### 测试2: 正常创建消息记录
POST {{baseUrl}}/ai/admin/support/message-history
Content-Type: {{contentType}}

{
  "sessionId": "test_session_001",
  "msgId": "test_msg_001",
  "toUserName": "test_to_user_001",
  "fromUserName": "test_from_user_001",
  "createTimestamp": 1640995200000,
  "msgType": "text",
  "content": "这是一条测试消息",
  "picUrl": "",
  "mediaId": "",
  "isReply": 0,
  "isSend": 1,
  "linkInfo": "",
  "cardInfo": ""
}

###

### 测试3: 正常查询消息会话列表
POST {{baseUrl}}/ai/admin/support/message-session/list
Content-Type: {{contentType}}

{
  "pageNum": 1,
  "pageSize": 10
}

###

### 测试4: 正常查询消息记录列表
POST {{baseUrl}}/ai/admin/support/message-history/list
Content-Type: {{contentType}}

{
  "pageNum": 1,
  "pageSize": 10
}

###

### ==========================================
### 测试用例 - 边界情况
### ==========================================

### 测试5: 空会话ID
POST {{baseUrl}}/ai/admin/support/message-session
Content-Type: {{contentType}}

{
  "sessionId": "",
  "openId": "test_openid_002",
  "customerId": "test_customer_002",
  "nickname": "测试用户2",
  "phone": "13800138001",
  "title": "测试会话2",
  "description": "这是一个测试会话",
  "sessionStatus": 1,
  "lastMessageTime": 1640995200000,
  "messageCount": 0
}

###

### 测试6: 空消息ID
POST {{baseUrl}}/ai/admin/support/message-history
Content-Type: {{contentType}}

{
  "sessionId": "test_session_002",
  "msgId": "",
  "toUserName": "test_to_user_002",
  "fromUserName": "test_from_user_002",
  "createTimestamp": 1640995200000,
  "msgType": "text",
  "content": "这是一条测试消息",
  "picUrl": "",
  "mediaId": "",
  "isReply": 0,
  "isSend": 1,
  "linkInfo": "",
  "cardInfo": ""
}

###

### 测试7: 不存在的ID查询
GET {{baseUrl}}/ai/admin/support/message-session/999999
Content-Type: {{contentType}}

###

### 测试8: 不存在的消息ID查询
GET {{baseUrl}}/ai/admin/support/message-history/999999
Content-Type: {{contentType}}

### 