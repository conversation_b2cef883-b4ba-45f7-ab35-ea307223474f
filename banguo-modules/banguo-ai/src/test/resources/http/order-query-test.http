### 订单查询接口测试文件
### 基础URL配置
@baseUrl = http://localhost:9209
@contentType = application/json

### 1. 根据订单号查询订单信息
GET {{baseUrl}}/ai/order/support/query-by-no?orderNo=186A025060847922457
Content-Type: {{contentType}}

###

### 2. 根据订单号和收货手机号查询订单信息
GET {{baseUrl}}/ai/order/support/query-by-no-and-phone?orderNo=186A025060847922457&phone=13800138000
Content-Type: {{contentType}}

###

### 3. 根据订单ID查询订单信息
GET {{baseUrl}}/ai/order/support/query-by-id?orderId=2783
Content-Type: {{contentType}}

###

### 4. 根据订单ID查询售后信息
GET {{baseUrl}}/ai/order/support/after-sale-info?orderId=2783
Content-Type: {{contentType}}

###

### 5. 根据订单ID和收货手机号查询售后信息
GET {{baseUrl}}/ai/order/support/after-sale-info-by-order-id-and-phone?orderId=2783&phone=13800138000
Content-Type: {{contentType}}

###

### 测试用例 - 正常情况

### 测试1: 正常订单号查询
GET {{baseUrl}}/ai/order/support/query-by-no?orderNo=186A025060847922457

### 测试2: 正常订单号和手机号查询
GET {{baseUrl}}/ai/order/support/query-by-no-and-phone?orderNo=186A025060847922457&phone=13800138000

### 测试3: 正常订单ID查询
GET {{baseUrl}}/ai/order/support/query-by-id?orderId=2783

### 测试4: 正常售后信息查询
GET {{baseUrl}}/ai/order/support/after-sale-info?orderId=2783

### 测试5: 正常售后信息查询（带手机号验证）
GET {{baseUrl}}/ai/order/support/after-sale-info-by-order-id-and-phone?orderId=2783&phone=13800138000

### 测试用例 - 边界情况

### 测试6: 空订单号
GET {{baseUrl}}/ai/order/support/query-by-no?orderNo=



### 测试7: 不存在的订单号
GET {{baseUrl}}/ai/order/support/query-by-no?orderNo=186A025060847999999

### 测试8: 格式错误的手机号
GET {{baseUrl}}/ai/order/support/query-by-no-and-phone?orderNo=186A025060847922457&phone=123456

### 测试9: 空手机号
GET {{baseUrl}}/ai/order/support/query-by-no-and-phone?orderNo=186A025060847922457&phone=



### 测试10: 不存在的订单ID
GET {{baseUrl}}/ai/order/support/query-by-id?orderId=99999

### 测试11: 负数订单ID
GET {{baseUrl}}/ai/order/support/query-by-id?orderId=-1

### 测试用例 - 真实数据测试

### 测试12: 真实订单号1
GET {{baseUrl}}/ai/order/support/query-by-no?orderNo=186A025060847922457

### 测试13: 真实订单号2
GET {{baseUrl}}/ai/order/support/query-by-no?orderNo=186A025060847922471

### 测试14: 真实订单号3
GET {{baseUrl}}/ai/order/support/query-by-no?orderNo=186A025060847922466

### 测试15: 真实订单号4
GET {{baseUrl}}/ai/order/support/query-by-no?orderNo=186A025060847922417

### 测试16: 真实订单号5
GET {{baseUrl}}/ai/order/support/query-by-no?orderNo=186A025060616514810

### 测试17: 真实手机号1
GET {{baseUrl}}/ai/order/support/query-by-no-and-phone?orderNo=186A025060847922457&phone=13800138000

### 测试18: 真实手机号2
GET {{baseUrl}}/ai/order/support/query-by-no-and-phone?orderNo=186A025060847922471&phone=13900139000

### 测试19: 真实手机号3
GET {{baseUrl}}/ai/order/support/query-by-no-and-phone?orderNo=186A025060847922466&phone=13700137000

### 测试20: 真实订单ID1
GET {{baseUrl}}/ai/order/support/query-by-id?orderId=2783

### 测试21: 真实订单ID2
GET {{baseUrl}}/ai/order/support/query-by-id?orderId=2781

### 测试22: 真实订单ID3
GET {{baseUrl}}/ai/order/support/query-by-id?orderId=2780

### 测试23: 真实订单ID4
GET {{baseUrl}}/ai/order/support/query-by-id?orderId=2779

### 测试24: 真实订单ID5
GET {{baseUrl}}/ai/order/support/query-by-id?orderId=2777

### 测试25: 真实售后查询1
GET {{baseUrl}}/ai/order/support/after-sale-info?orderId=2783

### 测试26: 真实售后查询2
GET {{baseUrl}}/ai/order/support/after-sale-info?orderId=2781

### 测试27: 真实售后查询3
GET {{baseUrl}}/ai/order/support/after-sale-info?orderId=2780

### 测试28: 真实售后查询4
GET {{baseUrl}}/ai/order/support/after-sale-info?orderId=2779

### 测试29: 真实售后查询5
GET {{baseUrl}}/ai/order/support/after-sale-info?orderId=2777

### 测试30: 真实售后查询（带手机号）1
GET {{baseUrl}}/ai/order/support/after-sale-info-by-order-id-and-phone?orderId=2783&phone=13800138000

### 测试31: 真实售后查询（带手机号）2
GET {{baseUrl}}/ai/order/support/after-sale-info-by-order-id-and-phone?orderId=2781&phone=13900139000

### 测试32: 真实售后查询（带手机号）3
GET {{baseUrl}}/ai/order/support/after-sale-info-by-order-id-and-phone?orderId=2780&phone=13700137000

### 测试33: 真实售后查询（带手机号）4
GET {{baseUrl}}/ai/order/support/after-sale-info-by-order-id-and-phone?orderId=2779&phone=13600136000

### 测试34: 真实售后查询（带手机号）5
GET {{baseUrl}}/ai/order/support/after-sale-info-by-order-id-and-phone?orderId=2777&phone=13500135000

### 批量测试 - 所有接口

### 批量测试1: 所有查询接口
# 1. 订单号查询
GET {{baseUrl}}/ai/order/support/query-by-no?orderNo=186A025060847922457

###

# 2. 订单号和手机号查询
GET {{baseUrl}}/ai/order/support/query-by-no-and-phone?orderNo=186A025060847922457&phone=13800138000

###

# 3. 订单ID查询
GET {{baseUrl}}/ai/order/support/query-by-id?orderId=2783

###

# 4. 售后信息查询
GET {{baseUrl}}/ai/order/support/after-sale-info?orderId=2783

###

# 5. 售后信息查询（带手机号）
GET {{baseUrl}}/ai/order/support/after-sale-info-by-order-id-and-phone?orderId=2783&phone=13800138000

###

### 性能测试 - 并发请求

### 性能测试1: 快速连续请求
GET {{baseUrl}}/ai/order/support/query-by-no?orderNo=186A025060847922457

###

GET {{baseUrl}}/ai/order/support/query-by-no?orderNo=186A025060847922471

###

GET {{baseUrl}}/ai/order/support/query-by-no?orderNo=186A025060847922466

###

### 性能测试2: 订单ID连续查询
GET {{baseUrl}}/ai/order/support/query-by-id?orderId=2783

###

GET {{baseUrl}}/ai/order/support/query-by-id?orderId=2781

###

GET {{baseUrl}}/ai/order/support/query-by-id?orderId=2780

###

### 错误处理测试

### 错误测试1: 缺少必需参数
GET {{baseUrl}}/ai/order/support/query-by-no

###

### 错误测试2: 参数类型错误
GET {{baseUrl}}/ai/order/support/query-by-id?orderId=abc

###

### 错误测试3: 特殊字符测试
GET {{baseUrl}}/ai/order/support/query-by-no?orderNo=186A025060847922457<script>alert('xss')</script>

###

### 错误测试4: 超长参数测试
GET {{baseUrl}}/ai/order/support/query-by-no?
    orderNo=186A025060847922457186A025060847922457186A025060847922457186A025060847922457186A025060847922457

###

### 真实数据完整测试 - 所有订单

### 完整测试1: 订单2783
GET {{baseUrl}}/ai/order/support/query-by-no?orderNo=186A025060847922457

###

GET {{baseUrl}}/ai/order/support/query-by-id?orderId=2783

###

### 完整测试2: 订单2781
GET {{baseUrl}}/ai/order/support/query-by-no?orderNo=186A025060847922471

###

GET {{baseUrl}}/ai/order/support/query-by-id?orderId=2781

###

### 完整测试3: 订单2780
GET {{baseUrl}}/ai/order/support/query-by-no?orderNo=186A025060847922466

###

GET {{baseUrl}}/ai/order/support/query-by-id?orderId=2780

###

### 完整测试4: 订单2779
GET {{baseUrl}}/ai/order/support/query-by-no?orderNo=186A025060847922417

###

GET {{baseUrl}}/ai/order/support/query-by-id?orderId=2779

###

### 完整测试5: 订单2777
GET {{baseUrl}}/ai/order/support/query-by-no?orderNo=186A025060616514810

###

GET {{baseUrl}}/ai/order/support/query-by-id?orderId=2777

###

### 完整测试6: 订单2772
GET {{baseUrl}}/ai/order/support/query-by-no?orderNo=186A025060616512533

###

GET {{baseUrl}}/ai/order/support/query-by-id?orderId=2772

###

### 完整测试7: 订单2770
GET {{baseUrl}}/ai/order/support/query-by-no?orderNo=186A025050973732456

###

GET {{baseUrl}}/ai/order/support/query-by-id?orderId=2770

###

### 完整测试8: 订单2769
GET {{baseUrl}}/ai/order/support/query-by-no?orderNo=186A025050973732401

###

GET {{baseUrl}}/ai/order/support/query-by-id?orderId=2769

###

### 完整测试9: 订单2768
GET {{baseUrl}}/ai/order/support/query-by-no?orderNo=186A025050973732481

###

GET {{baseUrl}}/ai/order/support/query-by-id?orderId=2768

###

### 完整测试10: 订单2767
GET {{baseUrl}}/ai/order/support/query-by-no?orderNo=186A025050973732436

###

GET {{baseUrl}}/ai/order/support/query-by-id?orderId=2767

### 