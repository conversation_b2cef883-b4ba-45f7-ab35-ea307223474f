#!/bin/bash

# 订单查询接口测试脚本
# 使用方法: ./test-orders.sh

BASE_URL="http://localhost:9209"

echo "=== 订单查询接口测试 ==="
echo "基础URL: $BASE_URL"
echo ""

# 测试1: 根据订单号查询订单信息
echo "测试1: 根据订单号查询订单信息"
curl -X GET "$BASE_URL/ai/order/support/query-by-no?orderNo=186A025060847922457" \
  -H "Content-Type: application/json"
echo ""
echo ""

# 测试2: 根据订单号和收货手机号查询订单信息
echo "测试2: 根据订单号和收货手机号查询订单信息"
curl -X GET "$BASE_URL/ai/order/support/query-by-no-and-phone?orderNo=186A025060847922457&phone=13800138000" \
  -H "Content-Type: application/json"
echo ""
echo ""

# 测试3: 根据订单ID查询订单信息
echo "测试3: 根据订单ID查询订单信息"
curl -X GET "$BASE_URL/ai/order/support/query-by-id?orderId=2783" \
  -H "Content-Type: application/json"
echo ""
echo ""

# 测试4: 根据订单ID查询售后信息
echo "测试4: 根据订单ID查询售后信息"
curl -X GET "$BASE_URL/ai/order/support/after-sale-info?orderId=2783" \
  -H "Content-Type: application/json"
echo ""
echo ""

# 测试5: 根据订单ID和收货手机号查询售后信息
echo "测试5: 根据订单ID和收货手机号查询售后信息"
curl -X GET "$BASE_URL/ai/order/support/after-sale-info-by-order-id-and-phone?orderId=2783&phone=13800138000" \
  -H "Content-Type: application/json"
echo ""
echo ""

# 边界情况测试
echo "=== 边界情况测试 ==="

# 测试6: 空订单号
echo "测试6: 空订单号"
curl -X GET "$BASE_URL/ai/order/support/query-by-no?orderNo=" \
  -H "Content-Type: application/json"
echo ""
echo ""

# 测试7: 不存在的订单号
echo "测试7: 不存在的订单号"
curl -X GET "$BASE_URL/ai/order/support/query-by-no?orderNo=186A025060847999999" \
  -H "Content-Type: application/json"
echo ""
echo ""

# 测试8: 格式错误的手机号
echo "测试8: 格式错误的手机号"
curl -X GET "$BASE_URL/ai/order/support/query-by-no-and-phone?orderNo=186A025060847922457&phone=123456" \
  -H "Content-Type: application/json"
echo ""
echo ""

# 测试9: 不存在的订单ID
echo "测试9: 不存在的订单ID"
curl -X GET "$BASE_URL/ai/order/support/query-by-id?orderId=99999" \
  -H "Content-Type: application/json"
echo ""
echo ""

# 测试10: 负数订单ID
echo "测试10: 负数订单ID"
curl -X GET "$BASE_URL/ai/order/support/query-by-id?orderId=-1" \
  -H "Content-Type: application/json"
echo ""
echo ""

# 真实数据测试
echo "=== 真实数据测试 ==="

# 测试11: 真实订单号1
echo "测试11: 真实订单号1 - 186A025060847922457"
curl -X GET "$BASE_URL/ai/order/support/query-by-no?orderNo=186A025060847922457" \
  -H "Content-Type: application/json"
echo ""
echo ""

# 测试12: 真实订单号2
echo "测试12: 真实订单号2 - 186A025060847922471"
curl -X GET "$BASE_URL/ai/order/support/query-by-no?orderNo=186A025060847922471" \
  -H "Content-Type: application/json"
echo ""
echo ""

# 测试13: 真实订单号3
echo "测试13: 真实订单号3 - 186A025060847922466"
curl -X GET "$BASE_URL/ai/order/support/query-by-no?orderNo=186A025060847922466" \
  -H "Content-Type: application/json"
echo ""
echo ""

# 测试14: 真实订单ID1
echo "测试14: 真实订单ID1 - 2783"
curl -X GET "$BASE_URL/ai/order/support/query-by-id?orderId=2783" \
  -H "Content-Type: application/json"
echo ""
echo ""

# 测试15: 真实订单ID2
echo "测试15: 真实订单ID2 - 2781"
curl -X GET "$BASE_URL/ai/order/support/query-by-id?orderId=2781" \
  -H "Content-Type: application/json"
echo ""
echo ""

# 测试16: 真实订单ID3
echo "测试16: 真实订单ID3 - 2780"
curl -X GET "$BASE_URL/ai/order/support/query-by-id?orderId=2780" \
  -H "Content-Type: application/json"
echo ""
echo ""

# 测试17: 真实售后查询1
echo "测试17: 真实售后查询1 - 订单ID 2783"
curl -X GET "$BASE_URL/ai/order/support/after-sale-info?orderId=2783" \
  -H "Content-Type: application/json"
echo ""
echo ""

# 测试18: 真实售后查询2
echo "测试18: 真实售后查询2 - 订单ID 2781"
curl -X GET "$BASE_URL/ai/order/support/after-sale-info?orderId=2781" \
  -H "Content-Type: application/json"
echo ""
echo ""

# 测试19: 真实售后查询3
echo "测试19: 真实售后查询3 - 订单ID 2780"
curl -X GET "$BASE_URL/ai/order/support/after-sale-info?orderId=2780" \
  -H "Content-Type: application/json"
echo ""
echo ""

# 批量测试 - 所有真实订单
echo "=== 批量测试 - 所有真实订单 ==="

# 订单2783
echo "批量测试1: 订单2783 (186A025060847922457)"
curl -X GET "$BASE_URL/ai/order/support/query-by-no?orderNo=186A025060847922457" \
  -H "Content-Type: application/json"
echo ""
curl -X GET "$BASE_URL/ai/order/support/query-by-id?orderId=2783" \
  -H "Content-Type: application/json"
echo ""
echo ""

# 订单2781
echo "批量测试2: 订单2781 (186A025060847922471)"
curl -X GET "$BASE_URL/ai/order/support/query-by-no?orderNo=186A025060847922471" \
  -H "Content-Type: application/json"
echo ""
curl -X GET "$BASE_URL/ai/order/support/query-by-id?orderId=2781" \
  -H "Content-Type: application/json"
echo ""
echo ""

# 订单2780
echo "批量测试3: 订单2780 (186A025060847922466)"
curl -X GET "$BASE_URL/ai/order/support/query-by-no?orderNo=186A025060847922466" \
  -H "Content-Type: application/json"
echo ""
curl -X GET "$BASE_URL/ai/order/support/query-by-id?orderId=2780" \
  -H "Content-Type: application/json"
echo ""
echo ""

# 订单2779
echo "批量测试4: 订单2779 (186A025060847922417)"
curl -X GET "$BASE_URL/ai/order/support/query-by-no?orderNo=186A025060847922417" \
  -H "Content-Type: application/json"
echo ""
curl -X GET "$BASE_URL/ai/order/support/query-by-id?orderId=2779" \
  -H "Content-Type: application/json"
echo ""
echo ""

# 订单2777
echo "批量测试5: 订单2777 (186A025060616514810)"
curl -X GET "$BASE_URL/ai/order/support/query-by-no?orderNo=186A025060616514810" \
  -H "Content-Type: application/json"
echo ""
curl -X GET "$BASE_URL/ai/order/support/query-by-id?orderId=2777" \
  -H "Content-Type: application/json"
echo ""
echo ""

echo "=== 测试完成 ===" 