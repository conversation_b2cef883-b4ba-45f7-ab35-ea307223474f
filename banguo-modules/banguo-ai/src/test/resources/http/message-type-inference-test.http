### 消息类型推断功能测试

### 1. 单个内容测试 - 文本消息
GET {{baseUrl}}/ai/test/inference/test/你好，这是一条普通文本消息

### 2. 单个内容测试 - 命令消息
GET {{baseUrl}}/ai/test/inference/test/%2Fhelp%20查看帮助

### 3. 单个内容测试 - 图片URL
GET {{baseUrl}}/ai/test/inference/test/https://example.com/image.jpg

### 4. 单个内容测试 - 视频URL
GET {{baseUrl}}/ai/test/inference/test/https://example.com/video.mp4

### 5. 单个内容测试 - 音频URL
GET {{baseUrl}}/ai/test/inference/test/https://example.com/audio.mp3

### 6. 单个内容测试 - 普通链接
GET {{baseUrl}}/ai/test/inference/test/https://example.com

### 7. 单个内容测试 - 图片文件路径
GET {{baseUrl}}/ai/test/inference/test/%2Fpath%2Fto%2Fimage.png

### 8. 单个内容测试 - 视频文件路径
GET {{baseUrl}}/ai/test/inference/test/C%3A%5Cvideos%5Cmovie.avi

### 9. 单个内容测试 - 音频文件路径
GET {{baseUrl}}/ai/test/inference/test/%2Fmusic%2Fsong.wav

### 10. 单个内容测试 - 文档文件
GET {{baseUrl}}/ai/test/inference/test/%2Fdocs%2Fdocument.pdf

### 11. 批量测试
POST {{baseUrl}}/ai/test/inference/batch-test
Content-Type: application/json

{
  "文本消息": "你好，这是一条普通文本消息",
  "命令消息": "/help 查看帮助信息",
  "图片URL": "https://example.com/image.jpg",
  "视频URL": "https://example.com/video.mp4",
  "音频URL": "https://example.com/audio.mp3",
  "普通链接": "https://example.com",
  "图片文件路径": "/path/to/image.png",
  "视频文件路径": "C:\\videos\\movie.avi",
  "音频文件路径": "/music/song.wav",
  "文档文件": "/docs/document.pdf"
}

### 12. 完整请求推断 - 文本数据但内容是图片URL
POST {{baseUrl}}/ai/test/inference/infer
Content-Type: application/json

{
  "messageType": "text",
  "data": {
    "content": "https://example.com/image.jpg",
    "type": "text"
  }
}

### 13. 完整请求推断 - 图片数据
POST {{baseUrl}}/ai/test/inference/infer
Content-Type: application/json

{
  "messageType": "image",
  "data": {
    "url": "https://example.com/image.jpg",
    "width": 800,
    "height": 600,
    "format": "jpg",
    "type": "image"
  }
}

### 14. 完整请求推断 - 命令数据
POST {{baseUrl}}/ai/test/inference/infer
Content-Type: application/json

{
  "messageType": "command",
  "data": {
    "command": "help",
    "arguments": "查看帮助信息",
    "type": "command"
  }
}

### 15. 预设场景测试
GET {{baseUrl}}/ai/test/inference/preset-test

### 16. 边界情况测试
GET {{baseUrl}}/ai/test/inference/edge-test

### 17. 测试空内容
GET {{baseUrl}}/ai/test/inference/test/

### 18. 测试特殊字符
GET {{baseUrl}}/ai/test/inference/test/%21%40%23%24%25%5E%26%2A%28%29

### 19. 测试数字
GET {{baseUrl}}/ai/test/inference/test/123456

### 20. 测试中文
GET {{baseUrl}}/ai/test/inference/test/你好世界

### 21. 测试混合内容
GET {{baseUrl}}/ai/test/inference/test/Hello%20世界%20123%20%21%40%23

### 22. 测试长文本
GET {{baseUrl}}/ai/test/inference/test/这是一段很长的文本内容，用来测试长文本的推断结果，包含各种字符和标点符号，以及一些数字123和特殊字符!@#等。

### 23. 测试无效URL
GET {{baseUrl}}/ai/test/inference/test/not-a-url

### 24. 测试部分URL
GET {{baseUrl}}/ai/test/inference/test/example.com 