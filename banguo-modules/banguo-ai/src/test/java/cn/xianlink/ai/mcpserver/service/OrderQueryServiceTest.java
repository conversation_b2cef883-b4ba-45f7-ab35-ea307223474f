package cn.xianlink.ai.mcpserver.service;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * OrderQueryService 测试类
 *
 * <AUTHOR>
 */
@SpringBootTest
@ActiveProfiles("test")
class OrderQueryServiceTest {

    @Test
    void testPhoneNumberValidation() {
        // 创建一个OrderQueryService实例来测试手机号验证方法
        // 由于isValidPhoneNumber方法是public的，我们可以直接调用

        // 测试有效的手机号
        assertTrue(isValidPhoneNumber("13800138000"));
        assertTrue(isValidPhoneNumber("+8613800138000"));
        assertTrue(isValidPhoneNumber("138-0013-8000"));
        assertTrue(isValidPhoneNumber("138 0013 8000"));

        // 测试无效的手机号
        assertFalse(isValidPhoneNumber("1234567890"));
        assertFalse(isValidPhoneNumber("1380013800"));
        assertFalse(isValidPhoneNumber(""));
        assertFalse(isValidPhoneNumber(null));
    }

    /**
     * 验证电话号码格式
     * 复制OrderQueryService中的方法用于测试
     */
    private boolean isValidPhoneNumber(String phone) {
        if (phone == null) return false;

        // 移除空格和常见分隔符
        String cleanPhone = phone.replaceAll("[\\s\\-()]", "");

        // 简单的电话号码验证：11位数字或带+86前缀的13位数字
        return cleanPhone.matches("^1[3-9]\\d{9}$") ||
                cleanPhone.matches("^(\\+86)?1[3-9]\\d{9}$") ||
                cleanPhone.matches("^\\d{10,11}$");
    }

    @Test
    void testQueryOrderByNo() {
        // 这里可以添加实际的订单查询测试
        // 由于需要真实的订单数据，暂时跳过
        assertTrue(true); // 占位测试
    }

    @Test
    void testQueryAfterSaleInfo() {
        // 这里可以添加实际的售后查询测试
        // 由于需要真实的订单数据，暂时跳过
        assertTrue(true); // 占位测试
    }
} 