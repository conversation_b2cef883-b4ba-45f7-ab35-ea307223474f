package cn.xianlink.ai;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 简单测试类，用于验证测试环境
 */
@DisplayName("简单测试")
@Tag("local")
class SimpleTest {

    @Test
    @DisplayName("基本断言测试")
    @Tag("local")
    void testBasicAssertion() {
        assertTrue(true);
        assertEquals(2, 1 + 1);
        assertNotNull("测试字符串");
    }

    @Test
    @DisplayName("字符串测试")
    @Tag("local")
    void testString() {
        String expected = "Hello World";
        String actual = "Hello World";
        assertEquals(expected, actual);
    }
} 