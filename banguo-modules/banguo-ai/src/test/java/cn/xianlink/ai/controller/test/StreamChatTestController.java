package cn.xianlink.ai.controller.test;

import cn.xianlink.ai.domain.dto.stream.StreamChatRequestDTO;
import cn.xianlink.ai.domain.dto.stream.StreamOptionsDTO;
import cn.xianlink.ai.domain.enums.MessageType;
import cn.xianlink.ai.service.support.IStreamChatService;
import cn.xianlink.common.core.domain.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.HashMap;
import java.util.Map;

/**
 * 流式聊天测试控制器
 * 仅在测试环境和开发环境下启用，用于调试和测试流式响应功能
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/ai/test/stream")
@Profile({"test", "dev", "local"})
public class StreamChatTestController {

    private final IStreamChatService streamChatService;

    /**
     * 简单流式测试接口
     */
    @GetMapping(value = "/simple", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter simpleStreamTest(
            @RequestParam(defaultValue = "test-session-001") String sessionId,
            @RequestParam(defaultValue = "test-openid-001") String openId,
            @RequestParam(defaultValue = "你好，我是测试用户") String message) {

        log.info("简单流式测试: sessionId={}, openId={}, message={}", sessionId, openId, message);

        StreamChatRequestDTO request = new StreamChatRequestDTO()
                .setSessionId(sessionId)
                .setOpenId(openId)
                .setContent(message)
                .setMessageType(MessageType.fromCode("text"))
                .setTimestamp(System.currentTimeMillis())
                .setOptions(new StreamOptionsDTO()
                        .setEnableTyping(true)
                        .setTypingDelay(100L)
                        .setChunkSize(5));

        SseEmitter emitter = new SseEmitter(60000L); // 1分钟超时
        streamChatService.processStreamRequest(request, emitter);

        return emitter;
    }

    /**
     * 高级流式测试接口
     */
    @PostMapping(value = "/advanced", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter advancedStreamTest(@RequestBody StreamChatRequestDTO request) {
        log.info("高级流式测试: {}", request);

        // 设置默认测试配置
        if (request.getOptions() == null) {
            request.setOptions(new StreamOptionsDTO());
        }
        if (request.getTimestamp() == null) {
            request.setTimestamp(System.currentTimeMillis());
        }

        SseEmitter emitter = new SseEmitter(120000L); // 2分钟超时
        streamChatService.processStreamRequest(request, emitter);

        return emitter;
    }

    /**
     * 测试不同消息类型
     */
    @GetMapping(value = "/message-types/{messageType}", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter testMessageTypes(
            @PathVariable String messageType,
            @RequestParam(defaultValue = "test-session-002") String sessionId,
            @RequestParam(defaultValue = "test-openid-002") String openId,
            @RequestParam String content) {

        log.info("消息类型测试: messageType={}, content={}", messageType, content);

        StreamChatRequestDTO request = new StreamChatRequestDTO()
                .setSessionId(sessionId)
                .setOpenId(openId)
                .setContent(content)
                .setMessageType(MessageType.fromCode(messageType))
                .setTimestamp(System.currentTimeMillis())
                .setOptions(new StreamOptionsDTO()
                        .setEnableTyping(true)
                        .setTypingDelay(50L)
                        .setChunkSize(8));

        SseEmitter emitter = new SseEmitter(60000L);
        streamChatService.processStreamRequest(request, emitter);

        return emitter;
    }

    /**
     * 测试快速模式（无打字效果）
     */
    @GetMapping(value = "/fast", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter fastModeTest(
            @RequestParam(defaultValue = "test-session-003") String sessionId,
            @RequestParam(defaultValue = "test-openid-003") String openId,
            @RequestParam(defaultValue = "快速响应测试") String message) {

        log.info("快速模式测试: sessionId={}, message={}", sessionId, message);

        StreamChatRequestDTO request = new StreamChatRequestDTO()
                .setSessionId(sessionId)
                .setOpenId(openId)
                .setContent(message)
                .setMessageType(MessageType.fromCode("text"))
                .setTimestamp(System.currentTimeMillis())
                .setOptions(new StreamOptionsDTO()
                        .setEnableTyping(false)  // 关闭打字效果
                        .setEnableProgress(false));

        SseEmitter emitter = new SseEmitter(30000L);
        streamChatService.processStreamRequest(request, emitter);

        return emitter;
    }

    /**
     * 测试连接状态
     */
    @GetMapping("/status/{sessionId}")
    public R<Map<String, Object>> getTestStatus(@PathVariable String sessionId) {
        String status = streamChatService.getStreamStatus(sessionId);

        Map<String, Object> result = new HashMap<>();
        result.put("sessionId", sessionId);
        result.put("status", status);
        result.put("timestamp", System.currentTimeMillis());

        return R.ok(result);
    }

    /**
     * 测试中断连接
     */
    @PostMapping("/interrupt/{sessionId}")
    public R<String> testInterrupt(@PathVariable String sessionId) {
        log.info("测试中断连接: sessionId={}", sessionId);
        streamChatService.interruptStream(sessionId);
        return R.ok("已中断测试连接");
    }

    /**
     * 清理测试连接
     */
    @PostMapping("/cleanup")
    public R<String> cleanupTest() {
        log.info("清理测试连接");
        streamChatService.cleanupExpiredStreams();
        return R.ok("已清理过期连接");
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public R<Map<String, Object>> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("timestamp", System.currentTimeMillis());
        health.put("service", "stream-chat-test");
        health.put("version", "1.0.0");

        return R.ok(health);
    }

    /**
     * 获取测试配置信息
     */
    @GetMapping("/config")
    public R<Map<String, Object>> getTestConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("defaultTimeout", 60000L);
        config.put("supportedMessageTypes", new String[]{"text", "image", "command", "link", "card"});
        config.put("defaultChunkSize", 10);
        config.put("defaultTypingDelay", 50L);

        return R.ok(config);
    }
} 