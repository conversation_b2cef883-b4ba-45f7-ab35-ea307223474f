package cn.xianlink.ai.config;

import cn.xianlink.basic.api.RemoteBasCustomerService;
import cn.xianlink.product.api.RemoteSupplierSkuService;
import cn.xianlink.resource.api.RemoteFileService;
import cn.xianlink.system.api.RemoteClientService;
import cn.xianlink.system.api.RemoteDeptService;
import cn.xianlink.system.api.RemoteSysOrgService;
import cn.xianlink.system.api.RemoteUserService;
import com.alibaba.cloud.nacos.NacosConfigManager;
import com.alibaba.cloud.nacos.NacosServiceManager;
import com.alibaba.cloud.nacos.refresh.NacosRefreshHistory;
import org.apache.dubbo.config.context.ConfigManager;
import org.apache.dubbo.rpc.model.ApplicationModel;
import org.mockito.Mockito;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

@TestConfiguration
public class TestDubboMockConfig {

    @Bean
    @Primary
    public ApplicationModel applicationModel() {
        return Mockito.mock(ApplicationModel.class);
    }

    @Bean
    @Primary
    public ConfigManager configManager() {
        return Mockito.mock(ConfigManager.class);
    }

    @Bean
    @Primary
    public RemoteDeptService remoteDeptService() {
        return Mockito.mock(RemoteDeptService.class);
    }

    @Bean
    @Primary
    public RemoteUserService remoteUserService() {
        return Mockito.mock(RemoteUserService.class);
    }

    @Bean
    @Primary
    public RemoteSysOrgService remoteSysOrgService() {
        return Mockito.mock(RemoteSysOrgService.class);
    }

    @Bean
    @Primary
    public RemoteSupplierSkuService remoteSupplierSkuService() {
        return Mockito.mock(RemoteSupplierSkuService.class);
    }

    @Bean
    @Primary
    public RemoteBasCustomerService remoteBasCustomerService() {
        return Mockito.mock(RemoteBasCustomerService.class);
    }

    @Bean
    @Primary
    public RemoteClientService remoteClientService() {
        return Mockito.mock(RemoteClientService.class);
    }

    @Bean
    @Primary
    public RemoteFileService remoteFileService() {
        return Mockito.mock(RemoteFileService.class);
    }

    @Bean
    @Primary
    public NacosServiceManager nacosServiceManager() {
        return Mockito.mock(NacosServiceManager.class);
    }

    @Bean
    @Primary
    public NacosConfigManager nacosConfigManager() {
        return Mockito.mock(NacosConfigManager.class);
    }

    @Bean
    @Primary
    public NacosRefreshHistory nacosRefreshHistory() {
        return Mockito.mock(NacosRefreshHistory.class);
    }
} 