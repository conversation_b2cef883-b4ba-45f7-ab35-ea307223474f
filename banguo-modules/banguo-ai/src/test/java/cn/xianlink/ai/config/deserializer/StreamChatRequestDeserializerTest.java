package cn.xianlink.ai.config.deserializer;

import cn.xianlink.ai.domain.dto.data.CustomMessageData;
import cn.xianlink.ai.domain.dto.data.TextMessageData;
import cn.xianlink.ai.domain.dto.stream.StreamChatRequestDTO;
import cn.xianlink.ai.domain.enums.MessageType;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * StreamChatRequestDeserializer 测试类
 * 验证自动填充 type 字段的功能
 *
 * <AUTHOR>
 */
class StreamChatRequestDeserializerTest {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Test
    void testTextMessageWithoutType() throws Exception {
        String json = """
                {
                    "messageType": "text",
                    "data": {
                        "content": "这是一条文本消息"
                    }
                }
                """;

        StreamChatRequestDTO request = objectMapper.readValue(json, StreamChatRequestDTO.class);

        assertEquals(MessageType.TEXT, request.getMessageType());
        assertNotNull(request.getData());
        assertTrue(request.getData() instanceof TextMessageData);
        assertEquals("text", request.getData().getType());
        assertEquals("这是一条文本消息", ((TextMessageData) request.getData()).getContent());
    }

    @Test
    void testCustomMessageWithoutType() throws Exception {
        String json = """
                {
                    "messageType": "custom",
                    "data": {
                        "businessType": "order",
                        "businessId": "ORD123456789",
                        "context": "用户询问订单状态"
                    }
                }
                """;

        StreamChatRequestDTO request = objectMapper.readValue(json, StreamChatRequestDTO.class);

        assertEquals(MessageType.CUSTOM, request.getMessageType());
        assertNotNull(request.getData());
        assertTrue(request.getData() instanceof CustomMessageData);
        assertEquals("custom", request.getData().getType());
        
        CustomMessageData customData = (CustomMessageData) request.getData();
        assertEquals("order", customData.getBusinessType());
        assertEquals("ORD123456789", customData.getBusinessId());
        assertEquals("用户询问订单状态", customData.getContext());
    }

    @Test
    void testMessageWithWrongType() throws Exception {
        // 前端传了错误的 type，应该被自动修正
        String json = """
                {
                    "messageType": "custom",
                    "data": {
                        "businessType": "order",
                        "businessId": "ORD123456789",
                        "context": "用户询问订单状态",
                        "type": "text"
                    }
                }
                """;

        StreamChatRequestDTO request = objectMapper.readValue(json, StreamChatRequestDTO.class);

        assertEquals(MessageType.CUSTOM, request.getMessageType());
        assertNotNull(request.getData());
        assertTrue(request.getData() instanceof CustomMessageData);
        // type 应该被自动修正为 "custom"
        assertEquals("custom", request.getData().getType());
    }

    @Test
    void testMessageWithoutData() throws Exception {
        String json = """
                {
                    "messageType": "custom"
                }
                """;

        StreamChatRequestDTO request = objectMapper.readValue(json, StreamChatRequestDTO.class);

        assertEquals(MessageType.CUSTOM, request.getMessageType());
        assertNotNull(request.getData());
        assertTrue(request.getData() instanceof CustomMessageData);
        assertEquals("custom", request.getData().getType());
    }

    @Test
    void testCompleteRequest() throws Exception {
        String json = """
                {
                    "messageType": "custom",
                    "data": {
                        "businessType": "product",
                        "businessId": "PROD987654321",
                        "context": "用户询问产品详情"
                    },
                    "context": {
                        "source": "web",
                        "userAgent": "Mozilla/5.0"
                    },
                    "options": {
                        "enableTyping": true,
                        "typingDelay": 50,
                        "chunkSize": 10
                    }
                }
                """;

        StreamChatRequestDTO request = objectMapper.readValue(json, StreamChatRequestDTO.class);

        assertEquals(MessageType.CUSTOM, request.getMessageType());
        assertNotNull(request.getData());
        assertTrue(request.getData() instanceof CustomMessageData);
        assertEquals("custom", request.getData().getType());

        CustomMessageData customData = (CustomMessageData) request.getData();
        assertEquals("product", customData.getBusinessType());
        assertEquals("PROD987654321", customData.getBusinessId());
        assertEquals("用户询问产品详情", customData.getContext());

        assertNotNull(request.getContext());
        assertEquals("web", request.getContext().get("source"));

        assertNotNull(request.getOptions());
        assertTrue(request.getOptions().getEnableTyping());
        assertEquals(50L, request.getOptions().getTypingDelay());
        assertEquals(10, request.getOptions().getChunkSize());
    }

    @Test
    void testSimpleUsage() throws Exception {
        // 测试最简单的用法：前端只传 messageType 和 data，不传 type
        String json = """
                {
                    "messageType": "custom",
                    "data": {
                        "businessType": "order",
                        "businessId": "ORD123",
                        "context": "查询订单"
                    }
                }
                """;

        StreamChatRequestDTO request = objectMapper.readValue(json, StreamChatRequestDTO.class);

        // 验证 type 字段被自动填充
        assertEquals("custom", request.getData().getType());
        assertEquals(MessageType.CUSTOM, request.getMessageType());

        System.out.println("✅ 测试通过：前端不需要传递 type 字段，后端自动填充！");
    }
}
