package cn.xianlink.ai.processor;

import cn.xianlink.ai.domain.dto.data.CustomMessageData;
import cn.xianlink.ai.domain.enums.MessageType;
import cn.xianlink.ai.service.impl.processor.CustomMessageProcessor;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 自定义消息处理器测试
 *
 * <AUTHOR>
 */
@SpringBootTest
class CustomMessageProcessorTest {

    @Test
    void testCustomMessageDataCreation() {
        // 测试自定义消息数据创建
        CustomMessageData customData = CustomMessageData.of("order", "ORD123456789", "用户询问订单状态");
        
        assertNotNull(customData);
        assertEquals("custom", customData.getType());
        assertEquals("order", customData.getBusinessType());
        assertEquals("ORD123456789", customData.getBusinessId());
        assertEquals("用户询问订单状态", customData.getContext());
        assertTrue(customData.isValid());
    }

    @Test
    void testCustomMessageDataValidation() {
        // 测试数据验证
        CustomMessageData validData = CustomMessageData.of("product", "PROD987654321", "产品信息查询");
        assertTrue(validData.isValid());

        // 测试无效数据
        CustomMessageData invalidData = new CustomMessageData();
        assertFalse(invalidData.isValid());

        // 测试部分数据
        CustomMessageData partialData = new CustomMessageData()
                .setBusinessType("customer")
                .setBusinessId("CUST555666777");
        assertFalse(partialData.isValid()); // 缺少 context
    }

    @Test
    void testMessageTypeEnum() {
        // 测试消息类型枚举
        assertEquals("custom", MessageType.CUSTOM.getCode());
        assertEquals("自定义消息", MessageType.CUSTOM.getDescription());
        assertEquals(MessageType.CUSTOM, MessageType.fromCode("custom"));
    }

    @Test
    void testCustomMessageProcessorType() {
        // 测试处理器支持的类型
        CustomMessageProcessor processor = new CustomMessageProcessor(null, null, null, null, null);
        assertEquals(MessageType.CUSTOM, processor.getSupportedType());
    }
}
