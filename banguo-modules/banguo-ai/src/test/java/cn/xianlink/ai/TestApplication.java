package cn.xianlink.ai;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;

/**
 * 测试用启动类
 * 用于集成测试，简化配置
 *
 * <AUTHOR>
 */
@SpringBootApplication(
        exclude = {
                DataSourceAutoConfiguration.class,
                com.baomidou.lock.spring.boot.autoconfigure.LockAutoConfiguration.class,
                org.redisson.spring.starter.RedissonAutoConfigurationV2.class,
                // 排除Dubbo自动装配
                org.apache.dubbo.spring.boot.autoconfigure.DubboAutoConfiguration.class,
                // 排除Translation自动装配
                cn.xianlink.common.translation.config.TranslationConfig.class
        }
)

@ComponentScan(basePackages = {"cn.xianlink.ai"})
public class TestApplication {

    public static void main(String[] args) {
        SpringApplication.run(TestApplication.class, args);
    }
} 