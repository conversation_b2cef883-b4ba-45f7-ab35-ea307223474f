package cn.xianlink.ai.controller.support;

import cn.hutool.core.util.IdUtil;
import cn.xianlink.ai.domain.dto.stream.StreamChatRequestDTO;
import cn.xianlink.ai.domain.dto.stream.StreamOptionsDTO;
import cn.xianlink.ai.domain.enums.MessageType;
import cn.xianlink.ai.domain.vo.MessageHistory4FrontendVo;
import cn.xianlink.ai.service.support.IMessageHistoryService;
import cn.xianlink.ai.service.support.IStreamChatService;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.system.api.model.LoginUser;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.List;

/**
 * 流式聊天控制器
 * 提供SSE流式响应接口
 *
 * <AUTHOR>
 * @folder 采集平台(小程序)/客服管理/消息
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/ai/support/message")
public class MessageController {

    private final IStreamChatService streamChatService;
    private final IMessageHistoryService messageHistoryService;

    /**
     * 流式聊天接口 - GET方式
     * 支持URL参数方式调用，适合简单场景
     */
    @GetMapping(value = "/chat", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter streamChat(
            @RequestParam String messageId,
            @RequestParam String content,
            @RequestParam(defaultValue = "text") String messageType,
            @RequestParam(required = false) String apiKey,
            @RequestParam(defaultValue = "true") Boolean enableTyping,
            @RequestParam(defaultValue = "50") Long typingDelay,
            @RequestParam(defaultValue = "10") Integer chunkSize) {

        log.info("收到流式聊天请求: messageId={}, messageType={}", messageId, messageType);

        messageId = messageId == null ? IdUtil.simpleUUID() : messageId;

        // 构建请求对象
        StreamChatRequestDTO request = new StreamChatRequestDTO()
                .setMessageId(messageId)
                .setTimestamp(System.currentTimeMillis())
                .setOptions(new StreamOptionsDTO()
                        .setEnableTyping(enableTyping)
                        .setTypingDelay(typingDelay)
                        .setChunkSize(chunkSize));

        // 根据消息类型设置对应的数据
        MessageType msgType = MessageType.fromCode(messageType);
        switch (msgType) {
            case TEXT:
                request.setTextMessageData(content);
                break;
            case IMAGE:
                request.setImageMessageData(content);
                break;
            case LINK:
                request.setLinkMessageData(content);
                break;
            case COMMAND:
                if (content.startsWith("/")) {
                    String[] parts = content.substring(1).split("\\s+", 2);
                    String command = parts[0];
                    String arguments = parts.length > 1 ? parts[1] : null;
                    request.setCommandMessageData(command, arguments);
                } else {
                    request.setCommandMessageData(content, null);
                }
                break;
            default:
                request.setTextMessageData(content);
                break;
        }

        // 创建SSE连接
        SseEmitter emitter = new SseEmitter(300000L); // 5分钟超时

        // 异步处理请求
        streamChatService.processStreamRequest(request, emitter);

        return emitter;
    }

    /**
     * 流式聊天接口 - POST方式
     * 支持复杂参数和配置，适合高级场景
     */
    @PostMapping(value = "/chat", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter streamChatPost(@RequestBody StreamChatRequestDTO chatRequest, HttpServletRequest httpRequest) {
        log.info("收到POST流式聊天请求: messageType={}, messageId={}",
                chatRequest.getMessageType(), chatRequest.getMessageId());

        // 设置messageId
        chatRequest.setMessageId(IdUtil.getSnowflakeNextIdStr());

        // 通过token获取userId
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser != null) {
            chatRequest.setUserId(loginUser.getUserId());
        } else {
            chatRequest.setUserId(chatRequest.getUserId() == null ? 0 : chatRequest.getUserId());
        }

        if (chatRequest.getUserId() == null || chatRequest.getUserId() == 0) {
            chatRequest.setUserId(886L);
        }

        // 获取clientId
        String clientId = httpRequest.getHeader("ClientId");
        chatRequest.setClientId(clientId);

        // 设置默认值
        chatRequest.setTimestamp(System.currentTimeMillis());
        if (chatRequest.getOptions() == null) {
            chatRequest.setOptions(new StreamOptionsDTO());
        }

        // 创建SSE连接
        SseEmitter emitter = new SseEmitter(
                chatRequest.getOptions().getConnectionTimeout() != null
                        ? chatRequest.getOptions().getConnectionTimeout()
                        : 1800000L
        );

        // 异步处理请求
        streamChatService.processStreamRequest(chatRequest, emitter);

        return emitter;
    }

    /**
     * 获取流式响应状态
     */
    @GetMapping("/chat/status/{sessionId}")
    public R<String> getChatStatus(@PathVariable String sessionId) {
        // 返回会话状态信息
        return R.ok("Active"); // 简单实现，后续可以扩展
    }

    /**
     * 中断流式响应
     */
    @PostMapping("/chat/interrupt/{sessionId}")
    public R<String> interruptChat(@PathVariable String sessionId) {
        // 中断指定会话的流式响应
        log.info("中断流式聊天: sessionId={}", sessionId);
        streamChatService.interruptStream(sessionId);
        return R.ok("已中断");
    }

    /**
     * 获取用户消息历史
     * 支持分页加载，用于信息流展示
     *
     * @param messageId 消息ID，为空时获取最新12条，有值时获取小于该ID的12条
     * @return 消息历史列表
     */
    @GetMapping("/history")
    public R<List<MessageHistory4FrontendVo>> getMessageHistory(
            @RequestParam(required = false) String messageId,
            @RequestParam(required = false) Integer limit
    ) {

        log.info("获取用户消息历史:  messageId={}", messageId);

        if (limit == null) {
            limit = 12;
        }

        Long userId = 0L;
//        Long userId = 34836L;
        // 如果没有传userId，则从token中获取当前登录用户
        try {
            LoginUser loginUser = LoginHelper.getLoginUser();
            if (loginUser != null) {
                userId = loginUser.getUserId();
            } else {
                return R.fail("用户未登录或token无效");
            }
        } catch (Exception e) {
            return R.fail("获取用户信息失败");
        }

        try {
            // 调用service方法获取消息历史
            // 如果messageId为空，获取最新12条
            // 如果messageId有值，获取小于该messageId的12条
            List<MessageHistory4FrontendVo> messageHistory = messageHistoryService.getMessageHistory(userId, messageId, limit);

            return R.ok(messageHistory);
        } catch (Exception e) {
            log.error("获取用户消息历史失败: userId={}, messageId={}, error={}", userId, messageId, e.getMessage(), e);
            return R.fail("获取消息历史失败: " + e.getMessage());
        }
    }

    /**
     * 简单的流式聊天Demo - 用于快速测试微信小程序流式输出功能
     * 纯模拟数据，无任何业务逻辑
     */
    @PostMapping(value = "/chat-demo", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter chatDemo(@RequestBody String content) throws IOException {
        log.info("收到Demo流式聊天请求: content={}", content);

        // 创建SSE连接，30秒超时
        SseEmitter emitter = new SseEmitter(30000L);

        // 发送结束标识
        emitter.send(SseEmitter.event()
                .name("started")
                .data("{'gag':1,'hehe':2}", MediaType.TEXT_PLAIN));

        // 异步处理模拟流式响应
        new Thread(() -> {
            try {
                // 模拟AI思考延迟
                Thread.sleep(800);

                emitter.send(SseEmitter.event()
                        .name("typing")
                        .data("{'gag':1,'hehe':2}", MediaType.TEXT_PLAIN));

                // 模拟分块响应
                String[] responseChunks = {
                        "您好！",
                        "我收",
                        "到了",
                        "您的",
                        "问题：",
                        content,
                        "\n\n这是一个",
                        "模拟的",
                        "流式响",
                        "应Demo。",
                        "每个文",
                        "字块之间",
                        "会有短暂",
                        "延迟，",
                        "以模拟",
                        "真实的A",
                        "I流式输",
                        "出效果。",
                        "\n\n当前",
                        "时间：",
                        new java.util.Date().toString(),
                        "\n\n感谢您",
                        "的测试！"
                };

                for (int i = 0; i < responseChunks.length; i++) {
                    // 发送数据块
                    emitter.send(SseEmitter.event()
                            .name("message")
                            .data(responseChunks[i], MediaType.TEXT_PLAIN));

                    // 模拟打字延迟
                    Thread.sleep(300);
                }

                // 发送结束标识
                emitter.send(SseEmitter.event()
                        .name("end")
                        .data("流式响应完成", MediaType.TEXT_PLAIN));

                emitter.complete();
                log.info("Demo流式响应完成");

            } catch (Exception e) {
                log.error("Demo流式响应出错: ", e);
                emitter.completeWithError(e);
            }
        }).start();

        return emitter;
    }


} 