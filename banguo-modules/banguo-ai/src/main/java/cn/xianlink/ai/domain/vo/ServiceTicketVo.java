package cn.xianlink.ai.domain.vo;

import cn.xianlink.common.dict.annotation.DictConvertFiled;
import cn.xianlink.common.dict.enums.DictConvertTypeEnum;
import cn.xianlink.common.mybatis.core.domain.BaseEntity;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 客服工单记录对象 service_ticket
 *
 * <AUTHOR>
 */
@Data
public class ServiceTicketVo extends BaseEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 用户open_id
     */
    private String openId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 工单号
     */
    private String ticketNo;

    /**
     * 客户姓名
     */
    private String customerName;

    /**
     * 客户电话
     */
    private String customerPhone;


    /**
     * 标题
     */
    private String title;

    /**
     * 问题类型:1-产品相关，2-物流配送，3-订单与支付，4-退换货，5-客户投诉与纠纷，6-系统与流程，7-其他
     */
    @DictConvertFiled(dictCode = "aiSupportProblemType", filedName = "problemTypeName", type = DictConvertTypeEnum.ENUM)
    private Integer problemType;

    /**
     * 问题描述
     */
    private String problemDescription;

    /**
     * 状态：1-待处理，2-处理中，3-已完成，4-已关闭
     */
    @DictConvertFiled(dictCode = "aiSupportTicketStatus", filedName = "statusName", type = DictConvertTypeEnum.ENUM)
    private Integer status;

    /**
     * AI评估结果
     */
    private String aiAssessment;

    /**
     * 预计回访时间
     */
    private LocalDateTime followUpTime;

    /**
     * 解决时间
     */
    private LocalDateTime resolvedTime;

    /**
     * 备注
     */
    private String remark;

} 