package cn.xianlink.ai.domain.dto.stream;

import cn.xianlink.ai.domain.dto.data.*;
import cn.xianlink.ai.domain.enums.MessageType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.util.Map;

/**
 * 流式聊天请求DTO
 * 包含流式响应所需的所有参数
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class StreamChatRequestDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */

    @Schema(hidden = true)
    @JsonIgnore
    private Long userId;

    /**
     * 客户端
     */

    @Schema(hidden = true)
    @JsonIgnore
    private String clientId;

    /**
     * 消息ID
     */

    @Schema(hidden = true)
    @JsonIgnore
    private String messageId;

    /**
     * 消息数据（根据messageType的不同有不同的结构）
     */
    private MessageData data;

    /**
     * 消息类型：text, image, command, link, card 等
     */
    private MessageType messageType = MessageType.TEXT;

    /**
     * 扩展上下文信息
     */
    private Map<String, Object> context;

    /**
     * 流式响应配置选项
     */
    private StreamOptionsDTO options;

    /**
     * 请求时间戳
     */

    @Schema(hidden = true)
    @JsonIgnore
    private Long timestamp;


    // ==================== 类型安全的便捷方法 ====================

    /**
     * 设置文本数据
     */
    public StreamChatRequestDTO setTextMessageData(String content) {
        this.messageType = MessageType.TEXT;
        this.data = TextMessageData.of(content);
        return this;
    }

    /**
     * 设置图片数据
     */
    public StreamChatRequestDTO setImageMessageData(String url) {
        this.messageType = MessageType.IMAGE;
        this.data = ImageMessageData.of(url);
        return this;
    }

    /**
     * 设置链接数据
     */
    public StreamChatRequestDTO setLinkMessageData(String url) {
        this.messageType = MessageType.LINK;
        this.data = LinkMessageData.of(url);
        return this;
    }

    /**
     * 设置命令数据
     */
    public StreamChatRequestDTO setCommandMessageData(String command, String arguments) {
        this.messageType = MessageType.COMMAND;
        this.data = CommandMessageData.of(command, arguments);
        return this;
    }

    /**
     * 验证数据是否有效
     */
    @JsonIgnore
    public boolean isDataValid() {
        return data != null && data.isValid();
    }

    /**
     * 自动推断并设置消息类型
     * 根据data字段的内容自动推断合适的MessageType
     */
    @JsonIgnore
    public StreamChatRequestDTO autoInferMessageType() {
        if (data != null) {
            String dataType = data.getType();
            this.messageType = MessageType.fromCode(dataType);
        }
        return this;
    }

    /**
     * 设置内容并自动推断类型
     * 根据内容智能推断消息类型
     */
    public StreamChatRequestDTO setContentAndInferType(String content) {
        if (content == null || content.trim().isEmpty()) {
            this.messageType = MessageType.TEXT;
            this.data = TextMessageData.of("");
            return this;
        }

        content = content.trim();

        // 检查是否为命令
        if (content.startsWith("/")) {
            String[] parts = content.substring(1).split("\\s+", 2);
            String command = parts[0];
            String arguments = parts.length > 1 ? parts[1] : null;
            this.setCommandMessageData(command, arguments);
            return this;
        }

        // 检查是否为URL
        if (isUrl(content)) {
            if (isImageUrl(content)) {
                this.setImageMessageData(content);
            } else {
                this.setLinkMessageData(content);
            }
            return this;
        }

        // 默认为文本
        this.setTextMessageData(content);
        return this;
    }

    /**
     * 检查是否为URL
     */
    private boolean isUrl(String content) {
        return content.startsWith("http://") || content.startsWith("https://") ||
                content.startsWith("www.") || content.contains("://");
    }

    /**
     * 检查是否为图片URL
     */
    private boolean isImageUrl(String url) {
        String lowerUrl = url.toLowerCase();
        return lowerUrl.contains(".jpg") || lowerUrl.contains(".jpeg") ||
                lowerUrl.contains(".png") || lowerUrl.contains(".gif") ||
                lowerUrl.contains(".bmp") || lowerUrl.contains(".webp");
    }
} 