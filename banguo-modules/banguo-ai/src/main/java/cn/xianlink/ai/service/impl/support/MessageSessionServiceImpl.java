package cn.xianlink.ai.service.impl.support;

import cn.hutool.core.bean.BeanUtil;
import cn.xianlink.ai.domain.MessageSession;
import cn.xianlink.ai.domain.dto.MessageHistoryDTO;
import cn.xianlink.ai.domain.dto.MessageSessionDTO;
import cn.xianlink.ai.domain.dto.MessageSessionListDTO;
import cn.xianlink.ai.domain.dto.message.AiCallResultDTO;
import cn.xianlink.ai.domain.vo.MessageSessionVo;
import cn.xianlink.ai.mapper.MessageSessionMapper;
import cn.xianlink.ai.service.support.IMessageSessionService;
import cn.xianlink.ai.utils.TimeUtil;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 消息会话Service业务层处理
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MessageSessionServiceImpl extends ServiceImpl<MessageSessionMapper, MessageSession>
        implements IMessageSessionService {

    /**
     * 查询消息会话
     *
     * @param id 消息会话主键
     * @return 消息会话
     */
    @Override
    public MessageSessionDTO info(Long id) {
        MessageSession messageSession = this.getById(id);
        if (messageSession == null) {
            log.error("消息会话不存在，id: {}", id);
            return null;
        }
        return BeanUtil.copyProperties(messageSession, MessageSessionDTO.class);
    }

    /**
     * 查询消息会话列表
     *
     * @param listDTO 查询条件
     * @return 消息会话集合
     */
    @Override
    public TableDataInfo<MessageSessionDTO> list(MessageSessionListDTO listDTO) {
        LambdaQueryWrapper<MessageSession> wrapper = new LambdaQueryWrapper<>();

        if (StringUtils.hasText(listDTO.getNickname())) {
            wrapper.like(MessageSession::getNickname, listDTO.getNickname());
        }
        if (StringUtils.hasText(listDTO.getPhone())) {
            wrapper.like(MessageSession::getPhone, listDTO.getPhone());
        }
        if (StringUtils.hasText(listDTO.getTitle())) {
            wrapper.like(MessageSession::getTitle, listDTO.getTitle());
        }
        if (listDTO.getSessionStatus() != null) {
            wrapper.eq(MessageSession::getSessionStatus, listDTO.getSessionStatus());
        }
        if (listDTO.getCreateTimeStart() != null) {
            wrapper.ge(MessageSession::getCreateTime, listDTO.getCreateTimeStart());
        }
        if (listDTO.getCreateTimeEnd() != null) {
            wrapper.le(MessageSession::getCreateTime, listDTO.getCreateTimeEnd());
        }
        wrapper.orderByDesc(MessageSession::getCreateTime);

        Page<MessageSession> pageInfo = this.page(listDTO.build(), wrapper);

        // 使用BeanCopyUtil进行高性能对象拷贝
        List<MessageSessionDTO> records = BeanUtil.copyToList(pageInfo.getRecords(), MessageSessionDTO.class);

        return TableDataInfo.build(records, pageInfo.getTotal());
    }

    /**
     * 查询消息会话列表（返回VO）
     *
     * @param listDTO 查询条件
     * @return 消息会话集合
     */
    public TableDataInfo<MessageSessionVo> listVo(MessageSessionListDTO listDTO) {
        // 调用原有的list方法获取DTO数据
        TableDataInfo<MessageSessionDTO> dtoResult = this.list(listDTO);

        // 逐行转换DTO为VO
        List<MessageSessionVo> voRecords = dtoResult.getRows().stream()
                .map(this::formatDtoToVo)
                .collect(java.util.stream.Collectors.toList());

        return TableDataInfo.build(voRecords, dtoResult.getTotal());
    }

    /**
     * 格式化MessageSessionDTO为MessageSessionVo
     * 在这里可以添加自定义的业务逻辑
     *
     * @param dto MessageSessionDTO对象
     * @return MessageSessionVo对象
     */
    private MessageSessionVo formatDtoToVo(MessageSessionDTO dto) {
        // 基础属性拷贝
        MessageSessionVo vo = BeanUtil.copyProperties(dto, MessageSessionVo.class);

        // 自定义逻辑处理
        if (vo != null) {
            // 格式化显示时间
            if (vo.getLastMessageTime() != null) {
                vo.setShowTime(TimeUtil.formatTimeDisplay(vo.getLastMessageTime()));
            }

            // 可以在这里添加更多自定义逻辑
            // 例如：处理用户昵称显示、格式化手机号等
        }

        return vo;
    }


    /**
     * 新增消息会话
     *
     * @param messageSession 消息会话信息
     * @return 结果
     */
    @Override
    public MessageSessionDTO create(MessageSessionDTO messageSession) {
        log.info("创建消息会话，会话ID: {}, 用户open_id: {}", messageSession.getSessionId(), messageSession.getOpenId());

        // 使用BeanCopyUtil将MessageSessionDTO转换为MessageSession
        MessageSession messageSessionEntity = BeanUtil.copyProperties(messageSession, MessageSession.class);
        boolean saveFlag = save(messageSessionEntity);

        if (saveFlag) {
            // 创建成功，返回创建的会话信息
            messageSession.setId(messageSessionEntity.getId());
            return messageSession;
        }

        return null;
    }

    /**
     * 修改消息会话
     *
     * @param messageSession 消息会话信息
     * @return 结果
     */
    @Override
    public Boolean update(MessageSessionDTO messageSession) {
        log.info("更新消息会话，记录ID: {}", messageSession.getId());

        // 使用BeanCopyUtil将MessageSessionDTO转换为MessageSession
        MessageSession messageSessionEntity = BeanUtil.copyProperties(messageSession, MessageSession.class);
        return updateById(messageSessionEntity);
    }

    /**
     * 批量删除消息会话
     *
     * @param ids 需要删除的消息会话主键集合
     * @return 结果
     */
    @Override
    public Boolean deleteByIds(List<Long> ids) {
        return removeByIds(ids);
    }

    /**
     * 删除消息会话信息
     *
     * @param id 消息会话主键
     * @return 结果
     */
    @Override
    public Boolean delete(Long id) {
        return removeById(id);
    }

    /**
     * 根据open_id获取消息会话
     *
     * @param openId 用户open_id
     * @return 消息会话
     */
    @Override
    public MessageSessionDTO getByOpenId(String openId) {
        LambdaQueryWrapper<MessageSession> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MessageSession::getOpenId, openId);
        MessageSession messageSession = this.getOne(wrapper);
        return BeanUtil.copyProperties(messageSession, MessageSessionDTO.class);
    }

    /**
     * 根据user_id获取消息会话
     *
     * @param userId 用户id
     * @return 消息会话
     */
    @Override
    public MessageSessionDTO getByUserId(String userId) {
        LambdaQueryWrapper<MessageSession> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MessageSession::getUserId, userId);
        MessageSession messageSession = this.getOne(wrapper);
        return BeanUtil.copyProperties(messageSession, MessageSessionDTO.class);
    }

    /**
     * 根据session_id获取消息会话
     *
     * @param sessionId 会话ID
     * @return 消息会话
     */
    @Override
    public MessageSessionDTO getBySessionId(String sessionId) {
        LambdaQueryWrapper<MessageSession> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MessageSession::getSessionId, sessionId);
        MessageSession messageSession = this.getOne(wrapper);
        return BeanUtil.copyProperties(messageSession, MessageSessionDTO.class);
    }

    /**
     * 更新会话状态
     *
     * @param sessionId     会话ID
     * @param sessionStatus 会话状态
     * @return 结果
     */
    @Override
    public Boolean updateSessionStatus(String sessionId, Integer sessionStatus) {
        log.info("更新会话状态，会话ID: {}, 新状态: {}", sessionId, sessionStatus);

        try {
            LambdaQueryWrapper<MessageSession> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(MessageSession::getSessionId, sessionId);
            MessageSession messageSession = this.getOne(wrapper);

            if (messageSession == null) {
                log.error("消息会话不存在，sessionId: {}", sessionId);
                return false;
            }

            messageSession.setSessionStatus(sessionStatus);
            return this.updateById(messageSession);
        } catch (Exception e) {
            log.error("更新会话状态异常", e);
            return false;
        }
    }

    /**
     * 更新最后消息时间和消息数量
     *
     * @param sessionId       会话ID
     * @param lastMessageTime 最后消息时间戳
     * @return 结果
     */
    @Override
    public Boolean updateLastMessageTime(String sessionId, Long lastMessageTime) {
        log.info("更新最后消息时间，会话ID: {}, 时间戳: {}", sessionId, lastMessageTime);

        try {
            LambdaQueryWrapper<MessageSession> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(MessageSession::getSessionId, sessionId);
            MessageSession messageSession = this.getOne(wrapper);

            if (messageSession == null) {
                log.error("消息会话不存在，sessionId: {}", sessionId);
                return false;
            }

            messageSession.setLastMessageTime(lastMessageTime);
            // 消息数量+1
            messageSession.setMessageCount(messageSession.getMessageCount() == null ? 1 : messageSession.getMessageCount() + 1);
            return this.updateById(messageSession);
        } catch (Exception e) {
            log.error("更新最后消息时间异常", e);
            return false;
        }
    }

    /**
     * 创建新的会话记录
     * 构建会话基本信息，待后续完善客户详细信息
     */
    public MessageSessionDTO buildNewMessageSession(AiCallResultDTO aiCallResult, MessageHistoryDTO messageHistoryDTO) {
        MessageSessionDTO sessionDTO = new MessageSessionDTO();

        sessionDTO.setSessionId(aiCallResult.getSessionId());
        sessionDTO.setOpenId(messageHistoryDTO.getOpenId());
        sessionDTO.setUserId(messageHistoryDTO.getUserId());
        sessionDTO.setSessionStatus(1); // 活跃状态
        sessionDTO.setLastMessageTime(messageHistoryDTO.getCreateTimestamp());
        sessionDTO.setMessageCount(1);

        sessionDTO.setNickname(""); // 需要通过openId查询用户信息
        sessionDTO.setPhone(""); // 需要通过openId查询用户信息
        sessionDTO.setTitle("AI智能客服会话"); // 默认标题
        sessionDTO.setDescription("用户发起的AI智能客服对话"); // 默认描述

        return sessionDTO;
    }


}