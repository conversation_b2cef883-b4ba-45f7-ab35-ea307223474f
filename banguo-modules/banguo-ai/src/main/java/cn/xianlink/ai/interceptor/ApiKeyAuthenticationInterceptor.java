package cn.xianlink.ai.interceptor;

import cn.xianlink.ai.service.ApiKeyService;
import cn.xianlink.common.core.exception.ServiceException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * API Key认证拦截器
 * 用于验证SSE请求中的API Key
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ApiKeyAuthenticationInterceptor implements HandlerInterceptor {

    private final ApiKeyService apiKeyService;

    // API Key的Header名称
    private static final String API_KEY_HEADER = "X-API-Key";
    // API Key的URL参数名称
    private static final String API_KEY_PARAM = "apiKey";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String requestURI = request.getRequestURI();
        log.debug("API Key认证拦截器处理请求: {}", requestURI);

        // 获取API Key（优先从Header获取，其次从URL参数获取）
        String apiKey = getApiKey(request);

        if (!StringUtils.hasText(apiKey)) {
            log.warn("请求缺少API Key: {}", requestURI);
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().write("{\"error\":\"缺少API Key\",\"code\":401}");
            return false;
        }

        try {
            // 验证API Key
            boolean isValid = apiKeyService.validateApiKey(apiKey);
            if (!isValid) {
                log.warn("无效的API Key: {}", apiKey);
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"error\":\"无效的API Key\",\"code\":401}");
                return false;
            }

            // 记录API Key使用日志
            apiKeyService.recordApiKeyUsage(apiKey, requestURI);

            log.debug("API Key验证成功: {}", apiKey);
            return true;

        } catch (ServiceException e) {
            log.error("API Key验证异常: {}", e.getMessage());
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().write("{\"error\":\"" + e.getMessage() + "\",\"code\":401}");
            return false;
        }
    }

    /**
     * 获取API Key
     * 优先从Header获取，其次从URL参数获取
     */
    private String getApiKey(HttpServletRequest request) {
        // 从Header获取
        String apiKey = request.getHeader(API_KEY_HEADER);
        if (StringUtils.hasText(apiKey)) {
            return apiKey;
        }

        // 从URL参数获取
        apiKey = request.getParameter(API_KEY_PARAM);
        if (StringUtils.hasText(apiKey)) {
            return apiKey;
        }

        return null;
    }
} 