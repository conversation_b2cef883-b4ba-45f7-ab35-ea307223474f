package cn.xianlink.ai.service.impl.support;

import cn.hutool.core.bean.BeanUtil;
import cn.xianlink.ai.domain.ServiceTicket;
import cn.xianlink.ai.domain.dto.MessageHistoryDTO;
import cn.xianlink.ai.domain.dto.ServiceTicketDTO;
import cn.xianlink.ai.domain.dto.data.TextMessageData;
import cn.xianlink.ai.domain.enums.MessageType;
import cn.xianlink.ai.domain.vo.MessageHistoryVo;
import cn.xianlink.ai.domain.vo.ServiceTicketVo;
import cn.xianlink.ai.domain.vo.TicketCreateResultVo;
import cn.xianlink.ai.mapper.ServiceTicketMapper;
import cn.xianlink.ai.service.support.IMessageHistoryService;
import cn.xianlink.ai.service.support.IServiceTicketService;
import cn.xianlink.ai.utils.MessageSessionRedisUtil;
import cn.xianlink.common.api.enums.ai.SupportTicketStatusEnum;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.system.api.RemoteUserService;
import cn.xianlink.system.api.domain.bo.RemoteUserBo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 客服工单记录Service业务层处理
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ServiceTicketServiceImpl extends ServiceImpl<ServiceTicketMapper, ServiceTicket>
        implements IServiceTicketService {

    private final RemoteUserService remoteUserService;
    private final MessageSessionRedisUtil messageSessionRedisUtil;
    private final IMessageHistoryService messageHistoryService;

    // ==========================================
    // 公用方法
    // ==========================================

    @Override
    public ServiceTicketVo info(Long id) {
        log.info("查询工单详情，工单ID: {}", id);

        ServiceTicket serviceTicket = this.getById(id);
        if (serviceTicket == null) {
            log.error("工单不存在，id: {}", id);
            return null;
        }
        return BeanUtil.copyProperties(serviceTicket, ServiceTicketVo.class);
    }

    @Override
    public ServiceTicketVo getInfoByTicketNo(String ticketNo) {
        LambdaQueryWrapper<ServiceTicket> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ServiceTicket::getTicketNo, ticketNo);

        ServiceTicket serviceTicket = this.getOne(wrapper);
        if (serviceTicket == null) {
            log.error("工单不存在，ticketNo: {}", ticketNo);
            return null;
        }
        return BeanUtil.copyProperties(serviceTicket, ServiceTicketVo.class);
    }

    // ==========================================
    // 公用方法
    // ==========================================


    // ==========================================
    // 管理后台专用方法
    // ==========================================

    @Override
    public TableDataInfo<ServiceTicketVo> list(PageQuery pageQuery, String ticketNo, String customerName,
                                               String customerPhone, Integer problemType, Integer status,
                                               LocalDateTime createTimeStart, LocalDateTime createTimeEnd) {
        log.info("管理员查询工单列表，查询条件: ticketNo={}, customerName={}, problemType={}, status={}",
                ticketNo, customerName, problemType, status);

        LambdaQueryWrapper<ServiceTicket> wrapper = new LambdaQueryWrapper<>();

        // 添加查询条件
        if (StringUtils.hasText(ticketNo)) {
            wrapper.like(ServiceTicket::getTicketNo, ticketNo);
        }
        if (StringUtils.hasText(customerName)) {
            wrapper.like(ServiceTicket::getCustomerName, customerName);
        }
        if (StringUtils.hasText(customerPhone)) {
            wrapper.like(ServiceTicket::getCustomerPhone, customerPhone);
        }
        if (problemType != null && problemType > 0) {
            wrapper.eq(ServiceTicket::getProblemType, problemType);
        }
        if (status != null && status > 0) {
            wrapper.eq(ServiceTicket::getStatus, status);
        }
        if (createTimeStart != null) {
            wrapper.ge(ServiceTicket::getCreateTime, createTimeStart);
        }
        if (createTimeEnd != null) {
            wrapper.le(ServiceTicket::getCreateTime, createTimeEnd);
        }

        // 按创建时间倒序排列
        wrapper.orderByDesc(ServiceTicket::getCreateTime);

        Page<ServiceTicket> pageInfo = this.page(pageQuery.build(), wrapper);
        List<ServiceTicketVo> records = BeanUtil.copyToList(pageInfo.getRecords(), ServiceTicketVo.class);
        return TableDataInfo.build(records, pageInfo.getTotal());
    }

    @Override
    public List<MessageHistoryVo> getConversationHistory(Long ticketId) {
        ServiceTicket serviceTicket = this.getById(ticketId);
        if (serviceTicket == null) {
            log.error("工单不存在，id: {}", ticketId);
            return Collections.emptyList();
        }

        // 如果有 sessionId，从 message_history 表获取对话记录
        if (StringUtils.hasText(serviceTicket.getSessionId())) {
            log.info("工单ID: {} 有 sessionId: {}，从 message_history 表获取对话记录", ticketId, serviceTicket.getSessionId());

            // 根据 sessionId 获取消息记录列表
            List<MessageHistoryDTO> messageHistoryList = messageHistoryService.getListBySessionId(serviceTicket.getSessionId());

            if (CollectionUtils.isEmpty(messageHistoryList)) {
                log.warn("sessionId: {} 在 message_history 表中没有找到记录", serviceTicket.getSessionId());
                return Collections.emptyList();
            }

            return BeanUtil.copyToList(messageHistoryList, MessageHistoryVo.class);
        } else {
            log.info("工单ID: {} 没有 sessionId，使用原有的 conversationHistory 字段逻辑", ticketId);

            // 如果没有 sessionId，使用原有的 conversationHistory 字段逻辑
            if (!StringUtils.hasText(serviceTicket.getConversationHistory())) {
                log.error("工单ID: {} 的会话记录为空", ticketId);
                return Collections.emptyList();
            }

            List<MessageHistoryVo> messageList = parseConversationHistory(serviceTicket.getConversationHistory());

            if (CollectionUtils.isEmpty(messageList)) {
                log.error("工单ID: {} 的会话记录为空", ticketId);
                MessageHistoryVo oneMessage = new MessageHistoryVo();
                oneMessage.setMessageType(MessageType.TEXT.getCode());
                oneMessage.setRole("user");
                oneMessage.setSource(serviceTicket.getConversationHistory());
                TextMessageData messageData = TextMessageData.of(serviceTicket.getConversationHistory());
                oneMessage.setData(messageData);
                return Collections.singletonList(oneMessage);
            }

        }

        return Collections.emptyList();
    }

    /**
     * 解析对话历史字符串，将XML格式转换为VO对象列表
     *
     * @param conversationHistory 对话历史字符串，格式如：<q>用户问题</q><a>客服回答</a>
     * @return 对话消息VO列表
     */
    private List<MessageHistoryVo> parseConversationHistory(String conversationHistory) {
        List<MessageHistoryVo> messageHistoryList = new ArrayList<>();

        if (!StringUtils.hasText(conversationHistory)) {
            return messageHistoryList;
        }

        try {
            // 使用正则表达式匹配<q>和<a>标签
            String questionPattern = "<q>(.*?)</q>";
            String answerPattern = "<a>(.*?)</a>";

            // 匹配所有问题
            java.util.regex.Pattern questionRegex = java.util.regex.Pattern.compile(questionPattern, java.util.regex.Pattern.DOTALL);
            java.util.regex.Matcher questionMatcher = questionRegex.matcher(conversationHistory);

            while (questionMatcher.find()) {
                MessageHistoryVo message = new MessageHistoryVo();
                message.setMessageType(MessageType.TEXT.getCode());
                message.setRole("user");
                message.setSource(questionMatcher.group(1).trim());
                TextMessageData messageData = TextMessageData.of(questionMatcher.group(1).trim());
                message.setData(messageData);
                messageHistoryList.add(message);
            }

            // 匹配所有回答
            java.util.regex.Pattern answerRegex = java.util.regex.Pattern.compile(answerPattern, java.util.regex.Pattern.DOTALL);
            java.util.regex.Matcher answerMatcher = answerRegex.matcher(conversationHistory);

            while (answerMatcher.find()) {
                MessageHistoryVo message = new MessageHistoryVo();
                message.setMessageType(MessageType.TEXT.getCode());
                message.setRole("assistant");
                message.setSource(answerMatcher.group(1).trim());
                TextMessageData messageData = TextMessageData.of(answerMatcher.group(1).trim());
                message.setData(messageData);
                messageHistoryList.add(message);
            }

            // 按在原文中的顺序排序
            messageHistoryList.sort((m1, m2) -> {
                // 借用 source 字段
                int index1 = conversationHistory.indexOf(m1.getSource());
                int index2 = conversationHistory.indexOf(m2.getSource());
                return Integer.compare(index1, index2);
            });

        } catch (Exception e) {
            log.error("解析对话历史异常", e);
        }

        return messageHistoryList;
    }

    @Override
    public ServiceTicketDTO create(ServiceTicketDTO serviceTicket) {
        log.info("创建工单，客户: {}, 问题类型: {}", serviceTicket.getCustomerName(), serviceTicket.getProblemType());

        try {
            // 生成工单号
            if (!StringUtils.hasText(serviceTicket.getTicketNo())) {
                serviceTicket.setTicketNo(generateTicketNo());
            }

            // 设置默认值
            if (serviceTicket.getStatus() == null) {
                serviceTicket.setStatus(1); // 待处理
            }

            // 设置预计回访时间（1个工作日后）
            if (serviceTicket.getFollowUpTime() == null) {
                LocalDateTime followUpTime = calculateFollowUpTime();
                serviceTicket.setFollowUpTime(followUpTime);
            }

            // 使用BeanUtil将ServiceTicketDTO转换为ServiceTicket
            ServiceTicket serviceTicketEntity = BeanUtil.copyProperties(serviceTicket, ServiceTicket.class);
            boolean saveFlag = this.save(serviceTicketEntity);

            if (saveFlag) {
                // 创建成功，返回创建的工单信息
                serviceTicket.setId(serviceTicketEntity.getId());
                return serviceTicket;
            }

            return null;
        } catch (Exception e) {
            log.error("创建工单异常", e);
            return null;
        }
    }

    @Override
    public Boolean update(ServiceTicketDTO serviceTicket) {
        log.info("更新工单，工单ID: {}", serviceTicket.getId());

        try {
            // 如果状态变为已完成，设置解决时间
            if (serviceTicket.getStatus() != null && serviceTicket.getStatus() == 3) {
                serviceTicket.setResolvedTime(LocalDateTime.now());
            }

            ServiceTicket serviceTicketInfo = BeanUtil.copyProperties(serviceTicket, ServiceTicket.class);

            return this.updateById(serviceTicketInfo);
        } catch (Exception e) {
            log.error("更新工单异常", e);
            return false;
        }
    }

    @Override
    public Boolean delete(Long id) {
        log.info("删除工单，工单ID: {}", id);
        return this.removeById(id);
    }

    @Override
    public Boolean deleteByIds(List<Long> ids) {
        log.info("批量删除工单，工单IDs: {}", ids);
        return this.removeByIds(ids);
    }

    @Override
    public Boolean updateTicketStatus(Long ticketId, Integer status) {
        log.info("更新工单状态，工单ID: {}, 新状态: {}", ticketId, status);

        try {
            // 检查工单是否存在
            ServiceTicket serviceTicket = this.getById(ticketId);
            if (serviceTicket == null) {
                log.error("工单不存在，id: {}", ticketId);
                return false;
            }

            // 更新状态
            serviceTicket.setStatus(status);

            // 如果状态变为已完成，设置解决时间
            if (status != null && status.equals(SupportTicketStatusEnum.COMPLETED.getValue())) {
                serviceTicket.setResolvedTime(LocalDateTime.now());
            }

            return this.updateById(serviceTicket);
        } catch (Exception e) {
            log.error("更新工单状态异常", e);
            return false;
        }
    }

    // ==========================================
    // 管理后台专用方法
    // ==========================================

    // ==========================================
    // 前端专用方法
    // ==========================================

    @Override
    public TicketCreateResultVo createTicket(ServiceTicketDTO serviceTicketDTO) {
        log.info("开始创建工单，用户ID: {}, 会话ID: {}", serviceTicketDTO.getUserId(), serviceTicketDTO.getSessionId());

        TicketCreateResultVo result = new TicketCreateResultVo();

        try {
            // 生成工单号
            String ticketNo = this.generateTicketNo();

            if (serviceTicketDTO.getSessionId() == null || serviceTicketDTO.getSessionId().trim().isEmpty()) {
                String sessionId = messageSessionRedisUtil.getSessionIdByKeyId(String.valueOf(serviceTicketDTO.getUserId()));
                serviceTicketDTO.setSessionId(sessionId);
            }

            RemoteUserBo userInfo = remoteUserService.getUserInfo(serviceTicketDTO.getUserId());
            if (userInfo != null) {
                log.info("成功获取用户信息，userId: {}, 姓名: {}, 手机号: {}",
                        serviceTicketDTO.getUserId(), userInfo.getRealName(), userInfo.getPhoneNo());
                serviceTicketDTO.setCustomerName(userInfo.getRealName());
                serviceTicketDTO.setCustomerPhone(userInfo.getPhoneNo());
            } else {
                log.warn("未找到用户详细信息，userId: {}", serviceTicketDTO.getUserId());
            }

            // 创建工单对象
            ServiceTicket ticketInfo = new ServiceTicket();
            ticketInfo.setTicketNo(ticketNo)
                    .setUserId(serviceTicketDTO.getUserId())
                    .setSessionId(serviceTicketDTO.getSessionId())
                    .setCustomerName(serviceTicketDTO.getCustomerName())
                    .setCustomerPhone(serviceTicketDTO.getCustomerPhone())
                    .setProblemType(serviceTicketDTO.getProblemType());

            // 设置预计回访时间（1个工作日后）
            LocalDateTime followUpTime = calculateFollowUpTime();
            ticketInfo.setFollowUpTime(followUpTime);

            // 保存工单
            boolean saved = this.save(ticketInfo);

            if (saved) {
                result.setSuccess(true);
                result.setTicketNo(ticketNo);
                result.setCreateTime(LocalDateTime.now());
                result.setMessage("工单创建成功");
                result.setUserReplyTemplate(generateUserReplyTemplate(ticketNo));

                log.info("工单创建成功，工单号: {}", ticketNo);
            } else {
                result.setSuccess(false);
                result.setMessage("工单创建失败");
                log.error("工单保存失败");
            }

        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage("工单创建异常,请稍后重试");
            log.error("创建工单异常", e);
        }

        return result;
    }

    @Override
    public String generateTicketNo() {
        // 格式: CS + YYYYMMDD + 4位随机数
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        int randomNum = ThreadLocalRandom.current().nextInt(1000, 9999);
        return "CS" + dateStr + randomNum;
    }

    /**
     * 计算预计回访时间（1个工作日后）
     */
    private LocalDateTime calculateFollowUpTime() {
        LocalDateTime followUpTime = LocalDateTime.now().plusDays(5);
        // 周末顺延到周一
        if (followUpTime.getDayOfWeek().getValue() >= 6) {
            followUpTime = followUpTime.plusDays(2);
        }
        return followUpTime;
    }

    /**
     * 生成给用户的回复模板
     */
    private String generateUserReplyTemplate(String ticketNo) {
        return String.format(
                "您的问题已记录，工单号：%s。我们的工作人员会尽快与您电话联系，请保持电话畅通。感谢您的耐心等待！",
                ticketNo
        );
    }

    // ==========================================
    // 前端专用方法
    // ==========================================


}