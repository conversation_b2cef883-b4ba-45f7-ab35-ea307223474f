package cn.xianlink.ai.service.impl.support;

import cn.xianlink.ai.domain.dto.data.*;
import cn.xianlink.ai.domain.dto.stream.StreamChatRequestDTO;
import cn.xianlink.ai.domain.enums.MessageType;
import cn.xianlink.ai.service.support.IMessageTypeInferenceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.net.URL;
import java.util.regex.Pattern;

/**
 * 消息类型推断服务实现类
 * 根据消息内容智能推断合适的MessageType
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class MessageTypeInferenceServiceImpl implements IMessageTypeInferenceService {

    // URL正则表达式
    private static final Pattern URL_PATTERN = Pattern.compile(
            "^(https?://)?([\\w-]+\\.)+[\\w-]+(/[\\w-./?%&=]*)?$",
            Pattern.CASE_INSENSITIVE
    );

    // 图片文件扩展名
    private static final Pattern IMAGE_PATTERN = Pattern.compile(
            "\\.(jpg|jpeg|png|gif|bmp|webp|svg)$",
            Pattern.CASE_INSENSITIVE
    );

    // 视频文件扩展名
    private static final Pattern VIDEO_PATTERN = Pattern.compile(
            "\\.(mp4|avi|mov|wmv|flv|webm|mkv)$",
            Pattern.CASE_INSENSITIVE
    );

    // 音频文件扩展名
    private static final Pattern AUDIO_PATTERN = Pattern.compile(
            "\\.(mp3|wav|aac|ogg|flac|m4a)$",
            Pattern.CASE_INSENSITIVE
    );

    // 命令模式
    private static final Pattern COMMAND_PATTERN = Pattern.compile(
            "^/([a-zA-Z0-9_]+)(\\s+(.+))?$"
    );

    @Override
    public MessageType inferMessageType(StreamChatRequestDTO request) {
        if (request == null) {
            return MessageType.TEXT;
        }

        // 如果已经设置了messageType，优先使用
        if (request.getMessageType() != null && request.getMessageType() != MessageType.TEXT) {
            return request.getMessageType();
        }

        // 如果有data字段，根据data类型推断
        if (request.getData() != null) {
            return inferFromData(request.getData());
        }

        // 尝试从其他字段推断
        return MessageType.TEXT; // 默认返回文本类型
    }

    @Override
    public MessageType inferMessageType(String content, Object data) {
        if (data != null) {
            return inferFromData(data);
        }

        if (StringUtils.hasText(content)) {
            return inferFromContent(content);
        }

        return MessageType.TEXT;
    }

    @Override
    public boolean validateInference(StreamChatRequestDTO request, MessageType inferredType) {
        if (request == null || inferredType == null) {
            return false;
        }

        // 检查data字段是否与推断的类型匹配
        if (request.getData() != null) {
            String dataType = request.getData().getType();
            String inferredCode = inferredType.getCode();

            if (!dataType.equals(inferredCode)) {
                log.warn("推断类型与数据类型不匹配: inferredType={}, dataType={}",
                        inferredType, dataType);
                return false;
            }
        }

        return true;
    }

    /**
     * 从数据对象推断消息类型
     */
    private MessageType inferFromData(Object data) {
        if (data instanceof TextMessageData) {
            return MessageType.TEXT;
        } else if (data instanceof ImageMessageData) {
            return MessageType.IMAGE;
        } else if (data instanceof VoiceMessageData) {
            return MessageType.VOICE;
        } else if (data instanceof VideoMessageData) {
            return MessageType.VIDEO;
        } else if (data instanceof FileMessageData) {
            return MessageType.FILE;
        } else if (data instanceof LocationMessageData) {
            return MessageType.LOCATION;
        } else if (data instanceof LinkMessageData) {
            return MessageType.LINK;
        } else if (data instanceof CardMessageData) {
            return MessageType.CARD;
        } else if (data instanceof CommandMessageData) {
            return MessageType.COMMAND;
        } else if (data instanceof RichTextMessageData) {
            return MessageType.RICH_TEXT;
        } else if (data instanceof SystemMessageData) {
            return MessageType.SYSTEM;
        }

        return MessageType.TEXT;
    }

    /**
     * 从内容推断消息类型
     */
    public MessageType inferFromContent(String content) {
        if (!StringUtils.hasText(content)) {
            return MessageType.TEXT;
        }

        content = content.trim();

        // 检查是否为命令
        if (COMMAND_PATTERN.matcher(content).matches()) {
            return MessageType.COMMAND;
        }

        // 检查是否为URL
        if (isValidUrl(content)) {
            // 进一步判断URL类型
            if (isImageUrl(content)) {
                return MessageType.IMAGE;
            } else if (isVideoUrl(content)) {
                return MessageType.VIDEO;
            } else if (isAudioUrl(content)) {
                return MessageType.VOICE;
            } else {
                return MessageType.LINK;
            }
        }

        // 检查是否为文件路径
        if (content.contains("/") || content.contains("\\")) {
            if (isImageFile(content)) {
                return MessageType.IMAGE;
            } else if (isVideoFile(content)) {
                return MessageType.VIDEO;
            } else if (isAudioFile(content)) {
                return MessageType.VOICE;
            } else {
                return MessageType.FILE;
            }
        }

        // 默认返回文本类型
        return MessageType.TEXT;
    }

    /**
     * 检查是否为有效的URL
     */
    private boolean isValidUrl(String url) {
        try {
            new URL(url);
            return true;
        } catch (Exception e) {
            // 尝试添加协议前缀
            try {
                new URL("https://" + url);
                return true;
            } catch (Exception ex) {
                return URL_PATTERN.matcher(url).matches();
            }
        }
    }

    /**
     * 检查是否为图片URL
     */
    private boolean isImageUrl(String url) {
        return IMAGE_PATTERN.matcher(url).find();
    }

    /**
     * 检查是否为视频URL
     */
    private boolean isVideoUrl(String url) {
        return VIDEO_PATTERN.matcher(url).find();
    }

    /**
     * 检查是否为音频URL
     */
    private boolean isAudioUrl(String url) {
        return AUDIO_PATTERN.matcher(url).find();
    }

    /**
     * 检查是否为图片文件
     */
    private boolean isImageFile(String path) {
        return IMAGE_PATTERN.matcher(path).find();
    }

    /**
     * 检查是否为视频文件
     */
    private boolean isVideoFile(String path) {
        return VIDEO_PATTERN.matcher(path).find();
    }

    /**
     * 检查是否为音频文件
     */
    private boolean isAudioFile(String path) {
        return AUDIO_PATTERN.matcher(path).find();
    }
} 