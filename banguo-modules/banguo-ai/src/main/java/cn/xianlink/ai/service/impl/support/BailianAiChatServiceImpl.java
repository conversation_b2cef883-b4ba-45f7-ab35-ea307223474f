package cn.xianlink.ai.service.impl.support;

import cn.hutool.core.util.StrUtil;
import cn.xianlink.ai.config.BailianConfig;
import cn.xianlink.ai.domain.dto.message.AiCallResultDTO;
import cn.xianlink.ai.domain.dto.stream.StreamChatRequestDTO;
import cn.xianlink.ai.domain.dto.stream.StreamChatResponseDTO;
import cn.xianlink.ai.service.support.IAiChatService;
import cn.xianlink.ai.utils.StreamEventUtils;
import com.alibaba.dashscope.aigc.generation.Generation;
import com.alibaba.dashscope.aigc.generation.GenerationParam;
import com.alibaba.dashscope.aigc.generation.GenerationResult;
import com.alibaba.dashscope.app.Application;
import com.alibaba.dashscope.app.ApplicationParam;
import com.alibaba.dashscope.app.ApplicationResult;
import com.alibaba.dashscope.common.Message;
import com.alibaba.dashscope.common.Role;
import com.alibaba.dashscope.exception.ApiException;
import com.alibaba.dashscope.exception.InputRequiredException;
import com.alibaba.dashscope.exception.NoApiKeyException;
import com.alibaba.dashscope.protocol.Protocol;
import com.alibaba.fastjson.JSONObject;
import io.reactivex.Flowable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

/**
 * 百练AI聊天服务实现类
 * 基于阿里云百练平台的智能体服务和模型服务
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BailianAiChatServiceImpl implements IAiChatService {

    private final BailianConfig bailianConfig;

    @PostConstruct
    public void init() {
        if (StrUtil.isBlank(bailianConfig.getApp().getApiKey())) {
            log.warn("百练API Key未配置，AI功能将不可用");
        } else {
            log.info("百练AI服务初始化成功，appId: {}",
                    bailianConfig.getApp().getAppId());
        }
    }

    /**
     * 调用百练智能体API获取AI回复
     *
     * @param prompt    用户输入的内容
     * @param sessionId 会话ID，新会话时为null
     * @return AI调用结果，包含回复内容和sessionId
     */
    @Override
    public AiCallResultDTO callBailianApp(String prompt, String sessionId) throws ApiException, NoApiKeyException, InputRequiredException {
        if (StrUtil.isBlank(bailianConfig.getApp().getApiKey())) {
            throw new RuntimeException("百练API Key未配置");
        }

        // 第一次尝试
        try {
            return doCallBailianApp(prompt, sessionId);
        } catch (ApiException e) {
            // 检查是否为超时错误，如果是则重试一次
            if (isTimeoutError(e)) {
                log.warn("检测到请求超时错误，进行重试。原错误: {}", e.getMessage());
                try {
                    return doCallBailianApp(prompt, sessionId);
                } catch (Exception retryException) {
                    log.error("重试后仍然失败: {}", retryException.getMessage(), retryException);
                    throw retryException;
                }
            } else {
                throw e;
            }
        }
    }

    /**
     * 流式调用百练智能体API
     *
     * @param chatRequest 用户请求体
     * @param sessionId   会话ID，新会话时为null
     * @param messageId   消息ID
     * @param prompt      组合后的提示词
     * @param emitter     SSE发射器，用于流式发送数据
     * @return AI调用结果
     * @throws ApiException           API异常
     * @throws NoApiKeyException      API Key异常
     * @throws InputRequiredException 输入参数异常
     */
    @Override
    public AiCallResultDTO callBailianAppStream(StreamChatRequestDTO chatRequest, String sessionId, String messageId, String prompt, SseEmitter emitter)
            throws ApiException, NoApiKeyException, InputRequiredException {
        if (StrUtil.isBlank(bailianConfig.getApp().getApiKey())) {
            throw new RuntimeException("百练API Key未配置");
        }

        StringBuilder fullContent = new StringBuilder();
        // 使用数组来在lambda表达式中存储可变的sessionId和requestId
        final String[] currentSessionId = {sessionId};
        final String[] currentRequestId = {null};

        try {
            log.info("开始使用DashScope SDK流式调用百练智能体API, appId: {}, sessionId: {}",
                    bailianConfig.getApp().getAppId(), sessionId);

            // 发送输入事件
            StreamEventUtils.sendStreamEventSilently(emitter, StreamChatResponseDTO.createTypingEvent(messageId, "text", "开始AI处理"));

            // 构建智能体调用参数，启用增量输出
            ApplicationParam paramBuilder = ApplicationParam.builder()
                    .appId(bailianConfig.getApp().getAppId())
                    .apiKey(bailianConfig.getApp().getApiKey())
                    .prompt(prompt)
                    .incrementalOutput(true) // 启用增量输出，这是流式调用的关键参数
                    .build();

            // 如果是继续会话，设置sessionId
            if (StrUtil.isNotBlank(sessionId)) {
                paramBuilder.setSessionId(sessionId);
            }

            // 使用DashScope SDK的官方流式调用方法
            Application application = new Application();

            // 调用百练智能体API - 获取流式结果
            Flowable<ApplicationResult> resultFlowable = application.streamCall(paramBuilder);

            // 处理流式响应
            resultFlowable.blockingForEach(result -> {
                try {
                    if (result != null && result.getOutput() != null) {
                        // 获取requestId（通常第一次响应会包含）
                        if (currentRequestId[0] == null && StrUtil.isNotBlank(result.getRequestId())) {
                            currentRequestId[0] = result.getRequestId();
                        }

                        // 如果当前没有sessionId，从响应中获取
                        if (StrUtil.isBlank(currentSessionId[0]) && StrUtil.isNotBlank(result.getOutput().getSessionId())) {
                            currentSessionId[0] = result.getOutput().getSessionId();
                        }

                        // 获取增量文本内容
                        String incrementalText = result.getOutput().getText();
                        if (StrUtil.isNotBlank(incrementalText)) {
                            fullContent.append(incrementalText);

                            // 发送增量内容到前端（使用静默模式）
                            boolean sent = StreamEventUtils.sendStreamEventSilently(emitter,
                                    StreamChatResponseDTO.createChunkEvent(messageId, "text", incrementalText));

                            if (!sent) {
                                log.debug("SSE连接已断开，停止发送增量内容: sessionId={}", currentSessionId[0]);
                                // 连接断开，跳出循环
                                return;
                            }

                            log.debug("发送流式增量内容: sessionId={}, content={}",
                                    currentSessionId[0], incrementalText);
                        }
                    }
                } catch (Exception e) {
                    log.error("处理流式响应时发生异常: {}", e.getMessage(), e);
                    // 不抛出异常，避免中断整个流程
                    return;
                }
            });

            // 构建返回结果
            AiCallResultDTO aiResult = new AiCallResultDTO();
            aiResult.setRequestId(currentRequestId[0]);
            aiResult.setText(fullContent.toString());
            aiResult.setSessionId(currentSessionId[0]);
            aiResult.setNewSession(StrUtil.isBlank(sessionId));

            log.info("百练智能体API流式调用成功, sessionId: {}, totalLength: {}",
                    currentSessionId[0], fullContent.length());

            return aiResult;

        } catch (ApiException e) {
            log.error("百练智能体API流式调用异常 - API异常: {}", e.getMessage(), e);
            throw e;
        } catch (NoApiKeyException e) {
            log.error("百练智能体API流式调用异常 - API Key异常: {}", e.getMessage(), e);
            throw e;
        } catch (InputRequiredException e) {
            log.error("百练智能体API流式调用异常 - 输入参数异常: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("百练智能体API流式调用异常 - 未知异常: {}", e.getMessage(), e);
            throw new RuntimeException("百练智能体API流式调用失败: " + e.getMessage(), e);
        }
    }

    /**
     * 调用百练模型API获取AI回复
     *
     * @param prompt    用户输入的内容
     * @param modelName 模型名称，为null时使用默认模型
     * @return AI调用结果，包含回复内容
     */
    @Override
    public AiCallResultDTO callBailianModel(String prompt, String modelName) throws ApiException, NoApiKeyException, InputRequiredException {
        if (StrUtil.isBlank(bailianConfig.getApp().getApiKey())) {
            throw new RuntimeException("百练API Key未配置");
        }

        // 构建消息列表
        List<Message> messages = new ArrayList<>();
        messages.add(Message.builder()
                .role(Role.USER.getValue())
                .content(prompt)
                .build());

        return callBailianModel(messages, modelName);
    }

    /**
     * 调用百练模型API获取AI回复（支持多轮对话）
     *
     * @param messages  对话消息列表
     * @param modelName 模型名称，为null时使用默认模型
     * @return AI调用结果，包含回复内容
     */
    @Override
    public AiCallResultDTO callBailianModel(List<Message> messages, String modelName) throws ApiException, NoApiKeyException, InputRequiredException {
        if (StrUtil.isBlank(bailianConfig.getApp().getApiKey())) {
            throw new RuntimeException("百练API Key未配置");
        }

        return doCallBailianModel(messages, modelName);
    }

    /**
     * 实际执行百练智能体API调用
     *
     * @param prompt    用户输入的内容
     * @param sessionId 会话ID，新会话时为null
     * @return AI调用结果，包含回复内容和sessionId
     */
    private AiCallResultDTO doCallBailianApp(String prompt, String sessionId) throws ApiException, NoApiKeyException, InputRequiredException {
        try {
            log.info("开始调用百练智能体API, appId: {}, sessionId: {}", bailianConfig.getApp().getAppId(), sessionId);

            // 构建智能体调用参数
            ApplicationParam paramBuilder = ApplicationParam.builder()
                    .appId(bailianConfig.getApp().getAppId())
                    .apiKey(bailianConfig.getApp().getApiKey())
                    .prompt(prompt)
                    .build();

            // 如果是继续会话，设置sessionId
            if (StrUtil.isNotBlank(sessionId)) {
                paramBuilder.setSessionId(sessionId);
            }

            // 调用百练智能体API
            Application application = new Application();
            ApplicationResult result = application.call(paramBuilder);

            // 检查返回结果的完整性
            if (result == null) {
                log.error("百练智能体API返回结果为null");
                throw new RuntimeException("百练智能体API返回结果为null");
            }

            if (result.getOutput() == null) {
                log.error("百练智能体API返回结果output为null, requestId: {}", result.getRequestId());
                throw new RuntimeException("百练智能体API返回结果output为null");
            }

            if (StrUtil.isBlank(result.getOutput().getText())) {
                log.error("百练智能体API返回文本为空, requestId: {}", result.getRequestId());
                throw new RuntimeException("百练智能体API返回文本为空");
            }

            // 构建返回结果
            String responseText = result.getOutput().getText();
            String responseSessionId = result.getOutput().getSessionId();
            String requestId = result.getRequestId();

            AiCallResultDTO aiResult = new AiCallResultDTO();
            aiResult.setRequestId(requestId);
            aiResult.setText(responseText);
            aiResult.setSessionId(responseSessionId);
            aiResult.setNewSession(StrUtil.isBlank(sessionId));

            log.info("百练智能体API调用成功, requestId: {}, responseSessionId: {}", requestId, responseSessionId);
            return aiResult;

        } catch (ApiException e) {
            log.error("百练智能体API调用异常 - API异常: {}", e.getMessage(), e);
            throw e;
        } catch (NoApiKeyException e) {
            log.error("百练智能体API调用异常 - API Key异常: {}", e.getMessage(), e);
            throw e;
        } catch (InputRequiredException e) {
            log.error("百练智能体API调用异常 - 输入参数异常: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("百练智能体API调用异常 - 未知异常: {}", e.getMessage(), e);
            throw new RuntimeException("百练智能体API调用失败: " + e.getMessage(), e);
        }
    }

    /**
     * 实际执行百练模型API调用
     *
     * @param messages  对话消息列表
     * @param modelName 模型名称
     * @return AI调用结果，包含回复内容
     */
    private AiCallResultDTO doCallBailianModel(List<Message> messages, String modelName) throws ApiException, NoApiKeyException, InputRequiredException {
        try {
            String actualModelName = StrUtil.isNotBlank(modelName) ? modelName : bailianConfig.getModel().getDefaultModel();
            log.info("开始调用百练模型API, model: {}, messageCount: {}", actualModelName, messages.size());

            // 构建模型调用参数
            GenerationParam param = GenerationParam.builder()
                    .apiKey(bailianConfig.getApp().getApiKey())
                    .model(actualModelName)
                    .messages(messages)
                    .resultFormat(GenerationParam.ResultFormat.MESSAGE)
                    .build();

            // 调用百练模型API
            // 根据官方文档，Generation需要指定协议和端点
            Generation generation = new Generation(Protocol.HTTP.getValue(), "https://dashscope.aliyuncs.com/api/v1");
            GenerationResult result = generation.call(param);

            // 检查返回结果的完整性
            if (result == null) {
                log.error("百练模型API返回结果为null");
                throw new RuntimeException("百练模型API返回结果为null");
            }

            if (result.getOutput() == null) {
                log.error("百练模型API返回结果output为null, requestId: {}", result.getRequestId());
                throw new RuntimeException("百练模型API返回结果output为null");
            }

            if (result.getOutput().getChoices() == null || result.getOutput().getChoices().isEmpty()) {
                log.error("百练模型API返回结果choices为空, requestId: {}", result.getRequestId());
                throw new RuntimeException("百练模型API返回结果choices为空");
            }

            // 获取AI回复内容
            String responseText = result.getOutput().getChoices().get(0).getMessage().getContent();
            String requestId = result.getRequestId();

            if (StrUtil.isBlank(responseText)) {
                log.error("百练模型API返回文本为空, requestId: {}", requestId);
                throw new RuntimeException("百练模型API返回文本为空");
            }

            // 构建返回结果
            AiCallResultDTO aiResult = new AiCallResultDTO();
            aiResult.setRequestId(requestId);
            aiResult.setText(responseText);
            aiResult.setSessionId(null); // 模型API不支持会话管理
            aiResult.setNewSession(true); // 每次都是新的对话

            log.info("百练模型API调用成功, model: {}, requestId: {}", actualModelName, requestId);
            return aiResult;

        } catch (ApiException e) {
            log.error("百练模型API调用异常 - API异常: {}", e.getMessage(), e);
            throw e;
        } catch (NoApiKeyException e) {
            log.error("百练模型API调用异常 - API Key异常: {}", e.getMessage(), e);
            throw e;
        } catch (InputRequiredException e) {
            log.error("百练模型API调用异常 - 输入参数异常: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("百练模型API调用异常 - 未知异常: {}", e.getMessage(), e);
            throw new RuntimeException("百练模型API调用失败: " + e.getMessage(), e);
        }
    }

    /**
     * 检查是否为超时错误
     *
     * @param e 异常对象
     * @return 是否为超时错误
     */
    private boolean isTimeoutError(ApiException e) {
        if (e == null || e.getMessage() == null) {
            return false;
        }
        String errorMessage = e.getMessage();
        try {
            JSONObject json = JSONObject.parseObject(errorMessage);
            String code = json.getString("code");
            if ("RequestTimeOut".equals(code)) {
                return true;
            }
        } catch (Exception ex) {
            // 不是标准JSON，忽略
        }
        // 兼容原有字符串判断
        return errorMessage.contains("RequestTimeOut")
                || errorMessage.contains("Request timed out")
                || errorMessage.contains("timeout");
    }

} 