package cn.xianlink.ai.domain;

import cn.xianlink.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 客服工单记录对象 service_ticket
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("support_service_ticket")
public class ServiceTicket extends TenantEntity {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 工单号
     */
    private String ticketNo;

    /**
     * 客户姓名
     */
    private String customerName;

    /**
     * 客户电话
     */
    private String customerPhone;

    /**
     * 标题
     */
    private String title;

    /**
     * 问题类型
     */
    private Integer problemType;

    /**
     * 问题描述
     */
    private String problemDescription;

    /**
     * 对话历史记录
     */
    private String conversationHistory;

    /**
     * 状态：1-待处理，2-处理中，3-已完成，4-已关闭
     */
    private Integer status;

    /**
     * AI评估结果
     */
    private String aiAssessment;

    /**
     * 预计回访时间
     */
    private LocalDateTime followUpTime;

    /**
     * 解决时间
     */
    private LocalDateTime resolvedTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0代表存在 存入这条数据的主键代表删除）
     */
    @TableLogic(delval = "id")
    private Long delFlag;

} 