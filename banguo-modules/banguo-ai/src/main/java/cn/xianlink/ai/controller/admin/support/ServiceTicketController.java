package cn.xianlink.ai.controller.admin.support;

import cn.xianlink.ai.domain.dto.ServiceTicketDTO;
import cn.xianlink.ai.domain.dto.ServiceTicketListDTO;
import cn.xianlink.ai.domain.vo.MessageHistoryVo;
import cn.xianlink.ai.domain.vo.ServiceTicketVo;
import cn.xianlink.ai.service.support.IServiceTicketService;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 工单管理
 *
 * <AUTHOR>
 * @date 2025-06-27
 * @folder 般果管理中心/AI服务/客服管理
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController()
@RequestMapping("/ai/admin/support/service-ticket")
@Tag(name = "客服工单管理", description = "客服工单管理相关接口")
public class ServiceTicketController {

    private final IServiceTicketService serviceTicketService;

    // ==========================================
    // 专用方法
    // ==========================================

    /**
     * 查询工单列表（分页）
     */
    @PostMapping("/list")
    @Operation(summary = "查询工单列表", description = "分页查询所有工单信息，支持多条件查询")
    public R<TableDataInfo<ServiceTicketVo>> list(@RequestBody ServiceTicketListDTO listDTO) {
        log.info("查询工单列表，页码: {}, 每页大小: {}, 查询条件: ticketNo={}, customerName={}, problemType={}, status={}",
                listDTO.getPageNum(), listDTO.getPageSize(), listDTO.getTicketNo(), listDTO.getCustomerName(),
                listDTO.getProblemType(), listDTO.getStatus());

        // 调用Service层 - 专用方法
        TableDataInfo<ServiceTicketVo> result = serviceTicketService.list(listDTO, listDTO.getTicketNo(),
                listDTO.getCustomerName(), listDTO.getCustomerPhone(),
                listDTO.getProblemType(), listDTO.getStatus(),
                listDTO.getCreateTimeStart(), listDTO.getCreateTimeEnd());

        return R.ok(result);
    }

    /**
     * 根据工单ID获取对话历史记录 conversation_history
     */
    @GetMapping("/conversation-history/{ticketId}")
    @Operation(summary = "根据工单ID获取对话历史记录", description = "根据工单ID获取对话历史记录")
    public R<List<MessageHistoryVo>> conversationHistory(@PathVariable Long ticketId) {
        log.info("根据工单ID获取对话历史记录，工单ID: {}", ticketId);
        return R.ok(serviceTicketService.getConversationHistory(ticketId));
    }

    /**
     * 查询工单详情
     */
    @GetMapping("/info/{id}")
    @Operation(summary = "查询工单详情", description = "根据工单ID查询详细信息")
    public R<ServiceTicketVo> info(@PathVariable Long id) {
        log.info("查询工单详情，工单ID: {}", id);

        // 调用Service层 - 专用方法
        ServiceTicketVo ticketInfo = serviceTicketService.info(id);
        if (ticketInfo == null) {
            return R.fail("工单不存在");
        }

        return R.ok(ticketInfo);
    }

    /**
     * 创建工单
     */
    @PostMapping("/create")
    @Operation(summary = "创建工单", description = "手动创建工单")
    public R<Boolean> create(@RequestBody ServiceTicketDTO serviceTicket) {
        log.info("创建工单，客户: {}, 问题类型: {}", serviceTicket.getCustomerName(), serviceTicket.getProblemType());

        // 调用Service层 - 专用方法
        ServiceTicketDTO result = serviceTicketService.create(serviceTicket);
        return R.ok(result != null);
    }

    /**
     * 更新工单
     */
    @PutMapping("/update")
    @Operation(summary = "更新工单", description = "更新工单信息")
    public R<Boolean> update(@RequestBody ServiceTicketDTO serviceTicket) {
        log.info("更新工单，工单ID: {}", serviceTicket.getId());

        // 调用Service层 - 专用方法
        Boolean result = serviceTicketService.update(serviceTicket);
        return R.ok(result);
    }

    /**
     * 删除工单
     */
    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除工单", description = "删除指定工单")
    public R<Boolean> delete(@PathVariable Long id) {
        log.info("删除工单，工单ID: {}", id);

        // 调用Service层 - 专用方法
        Boolean result = serviceTicketService.delete(id);
        return R.ok(result);
    }

    /**
     * 批量删除工单
     */
    @DeleteMapping("/deleteByIds")
    @Operation(summary = "批量删除工单", description = "批量删除多个工单")
    public R<Boolean> deleteByIds(@RequestParam List<Long> ids) {
        log.info("批量删除工单，工单IDs: {}", ids);

        // 调用Service层 - 专用方法
        Boolean result = serviceTicketService.deleteByIds(ids);
        return R.ok(result);
    }

} 