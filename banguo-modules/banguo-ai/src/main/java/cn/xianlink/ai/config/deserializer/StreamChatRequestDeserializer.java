package cn.xianlink.ai.config.deserializer;

import cn.xianlink.ai.domain.dto.data.*;
import cn.xianlink.ai.domain.dto.stream.StreamChatRequestDTO;
import cn.xianlink.ai.domain.dto.stream.StreamOptionsDTO;
import cn.xianlink.ai.domain.enums.MessageType;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.Map;

/**
 * StreamChatRequestDTO 自定义反序列化器
 * 自动根据 messageType 填充 data 中的 type 字段
 * 这样前端就不需要在 data 中传递 type 字段了
 *
 * <AUTHOR>
 */
@Slf4j
public class StreamChatRequestDeserializer extends JsonDeserializer<StreamChatRequestDTO> {

    @Override
    public StreamChatRequestDTO deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        ObjectMapper mapper = (ObjectMapper) p.getCodec();
        JsonNode node = mapper.readTree(p);

        StreamChatRequestDTO request = new StreamChatRequestDTO();

        // 解析基本字段
        if (node.has("userId")) {
            request.setUserId(node.get("userId").asLong());
        }
        if (node.has("clientId")) {
            request.setClientId(node.get("clientId").asText());
        }
        if (node.has("messageId")) {
            request.setMessageId(node.get("messageId").asText());
        }
        if (node.has("timestamp")) {
            request.setTimestamp(node.get("timestamp").asLong());
        }

        // 解析 messageType
        MessageType messageType = MessageType.TEXT; // 默认值
        if (node.has("messageType")) {
            String messageTypeStr = node.get("messageType").asText();
            messageType = MessageType.fromCode(messageTypeStr);
        }
        request.setMessageType(messageType);

        // 解析 context
        if (node.has("context")) {
            JsonNode contextNode = node.get("context");
            Map<String, Object> context = mapper.convertValue(contextNode, Map.class);
            request.setContext(context);
        }

        // 解析 options
        if (node.has("options")) {
            JsonNode optionsNode = node.get("options");
            StreamOptionsDTO options = mapper.treeToValue(optionsNode, StreamOptionsDTO.class);
            request.setOptions(options);
        }

        // 解析 data 字段，并自动填充 type
        if (node.has("data")) {
            JsonNode dataNode = node.get("data");
            MessageData data = parseMessageData(mapper, dataNode, messageType);
            request.setData(data);
        } else {
            // 如果没有 data 字段，创建一个空的对应类型的 data
            MessageData data = createEmptyMessageData(messageType);
            request.setData(data);
        }

        return request;
    }

    /**
     * 根据 messageType 解析 MessageData
     */
    private MessageData parseMessageData(ObjectMapper mapper, JsonNode dataNode, MessageType messageType) throws IOException {
        // 如果 dataNode 中没有 type 字段，或者 type 字段与 messageType 不匹配，
        // 则自动添加/修改 type 字段
        String expectedType = messageType.getCode();
        
        // 创建一个可修改的 JsonNode 副本
        if (dataNode.isObject()) {
            ((com.fasterxml.jackson.databind.node.ObjectNode) dataNode).put("type", expectedType);
        }

        // 根据 messageType 反序列化为对应的具体类型
        switch (messageType) {
            case TEXT:
                return mapper.treeToValue(dataNode, TextMessageData.class);
            case IMAGE:
                return mapper.treeToValue(dataNode, ImageMessageData.class);
            case LINK:
                return mapper.treeToValue(dataNode, LinkMessageData.class);
            case COMMAND:
                return mapper.treeToValue(dataNode, CommandMessageData.class);
            case VOICE:
                return mapper.treeToValue(dataNode, VoiceMessageData.class);
            case VIDEO:
                return mapper.treeToValue(dataNode, VideoMessageData.class);
            case FILE:
                return mapper.treeToValue(dataNode, FileMessageData.class);
            case LOCATION:
                return mapper.treeToValue(dataNode, LocationMessageData.class);
            case CARD:
                return mapper.treeToValue(dataNode, CardMessageData.class);
            case RICH_TEXT:
                return mapper.treeToValue(dataNode, RichTextMessageData.class);
            case SYSTEM:
                return mapper.treeToValue(dataNode, SystemMessageData.class);
            case CUSTOM:
                return mapper.treeToValue(dataNode, CustomMessageData.class);
            default:
                log.warn("未知的消息类型: {}, 使用默认的文本消息类型", messageType);
                return mapper.treeToValue(dataNode, TextMessageData.class);
        }
    }

    /**
     * 创建空的 MessageData 对象
     */
    private MessageData createEmptyMessageData(MessageType messageType) {
        switch (messageType) {
            case TEXT:
                return new TextMessageData();
            case IMAGE:
                return new ImageMessageData();
            case LINK:
                return new LinkMessageData();
            case COMMAND:
                return new CommandMessageData();
            case VOICE:
                return new VoiceMessageData();
            case VIDEO:
                return new VideoMessageData();
            case FILE:
                return new FileMessageData();
            case LOCATION:
                return new LocationMessageData();
            case CARD:
                return new CardMessageData();
            case RICH_TEXT:
                return new RichTextMessageData();
            case SYSTEM:
                return new SystemMessageData();
            case CUSTOM:
                return new CustomMessageData();
            default:
                return new TextMessageData();
        }
    }
}
