package cn.xianlink.ai.domain.dto;

import cn.xianlink.common.dict.annotation.DictConvertFiled;
import cn.xianlink.common.dict.enums.DictConvertTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 客服工单记录对象 service_ticket
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ServiceTicketDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 用户open_id
     */
    private String openId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 工单号
     */
    private String ticketNo;

    /**
     * 客户姓名
     */
    private String customerName;

    /**
     * 客户电话
     */
    private String customerPhone;

    /**
     * 标题
     */
    private String title;

    /**
     * 问题类型
     */
    @DictConvertFiled(dictCode = "aiSupportProblemType", filedName = "problemTypeName", type = DictConvertTypeEnum.ENUM)
    private Integer problemType;

    /**
     * 问题描述
     */
    private String problemDescription;

    /**
     * 对话历史记录
     */
    private String conversationHistory;

    /**
     * 状态：1-待处理，2-处理中，3-已完成，4-已关闭
     */
    @DictConvertFiled(dictCode = "aiSupportProblemStatus", filedName = "statusName", type = DictConvertTypeEnum.ENUM)
    private Integer status;

    /**
     * AI评估结果
     */
    private String aiAssessment;

    /**
     * 预计回访时间
     */
    private LocalDateTime followUpTime;

    /**
     * 解决时间
     */
    private LocalDateTime resolvedTime;

    /**
     * 备注
     */
    private String remark;

} 