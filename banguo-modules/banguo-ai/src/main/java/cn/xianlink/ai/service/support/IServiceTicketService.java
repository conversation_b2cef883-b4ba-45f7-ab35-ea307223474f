package cn.xianlink.ai.service.support;

import cn.xianlink.ai.domain.ServiceTicket;
import cn.xianlink.ai.domain.dto.ServiceTicketDTO;
import cn.xianlink.ai.domain.vo.MessageHistoryVo;
import cn.xianlink.ai.domain.vo.ServiceTicketVo;
import cn.xianlink.ai.domain.vo.TicketCreateResultVo;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 客服工单记录Service接口
 *
 * <AUTHOR>
 */
public interface IServiceTicketService extends IService<ServiceTicket> {

    // ==========================================
    // 公用方法
    // ==========================================

    /**
     * 管理员查询工单详情
     *
     * @param id 工单ID
     * @return 工单信息
     */
    ServiceTicketVo info(Long id);

    // ==========================================
    // 公用方法
    // ==========================================


    // ==========================================
    // 管理后台专用方法
    // ==========================================

    /**
     * 管理员分页查询工单列表（带查询条件）
     *
     * @param pageQuery       分页查询参数
     * @param ticketNo        工单号（模糊查询）
     * @param customerName    客户姓名（模糊查询）
     * @param customerPhone   客户电话（模糊查询）
     * @param problemType     问题类型
     * @param status          状态
     * @param createTimeStart 开始时间
     * @param createTimeEnd   结束时间
     * @return 分页结果
     */
    TableDataInfo<ServiceTicketVo> list(PageQuery pageQuery, String ticketNo, String customerName,
                                        String customerPhone, Integer problemType, Integer status,
                                        LocalDateTime createTimeStart, LocalDateTime createTimeEnd);

    /**
     * 根据工单ID获取对话历史记录
     *
     * @param ticketId 工单ID
     * @return 对话历史记录
     */
    List<MessageHistoryVo> getConversationHistory(Long ticketId);

    /**
     * 管理员创建工单
     *
     * @param serviceTicket 工单信息
     * @return 工单信息（包含生成的ID）
     */
    ServiceTicketDTO create(ServiceTicketDTO serviceTicket);

    /**
     * 管理员更新工单
     *
     * @param serviceTicket 工单信息
     * @return 是否成功
     */
    Boolean update(ServiceTicketDTO serviceTicket);

    /**
     * 管理员删除工单
     *
     * @param id 工单ID
     * @return 是否成功
     */
    Boolean delete(Long id);

    /**
     * 管理员批量删除工单
     *
     * @param ids 工单ID列表
     * @return 是否成功
     */
    Boolean deleteByIds(List<Long> ids);

    /**
     * 更新工单状态
     *
     * @param ticketId 工单ID
     * @param status   新状态
     * @return 是否成功
     */
    Boolean updateTicketStatus(Long ticketId, Integer status);

    // ==========================================
    // 管理后台专用方法
    // ==========================================


    // ==========================================
    // 前端专用方法
    // ==========================================


    /**
     * 创建客服工单
     *
     * @param serviceTicketDTO 工单信息
     * @return 工单创建结果
     */
    TicketCreateResultVo createTicket(ServiceTicketDTO serviceTicketDTO);

    /**
     * 生成工单号
     *
     * @return 工单号
     */
    String generateTicketNo();

    /**
     * 根据工单号查询工单
     *
     * @param ticketNo 工单号
     * @return 工单信息
     */
    ServiceTicketVo getInfoByTicketNo(String ticketNo);

    // ==========================================
    // 前端专用方法
    // ==========================================

} 