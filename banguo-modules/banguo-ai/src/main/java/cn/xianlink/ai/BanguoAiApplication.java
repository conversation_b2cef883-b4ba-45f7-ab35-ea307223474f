package cn.xianlink.ai;

// import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
//import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;

import cn.xianlink.ai.utils.CustomBeanNameGenerator;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * AI模块
 *
 * <AUTHOR>
 */
@EnableAsync
@EnableCaching
// @EnableDubbo
@SpringBootApplication
@ComponentScan(basePackages = "cn.xianlink.ai", nameGenerator = CustomBeanNameGenerator.class)
public class BanguoAiApplication {
    public static void main(String[] args) {
        System.setProperty("nacos.logging.default.config.enabled", "false");
        System.setProperty("druid.filters", "xLogSql");
        SpringApplication application = new SpringApplication(BanguoAiApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
        System.out.println("(♥◠‿◠)ﾉﾞ  ai模块启动成功   ლ(´ڡ`ლ)ﾞ  ");
    }
} 