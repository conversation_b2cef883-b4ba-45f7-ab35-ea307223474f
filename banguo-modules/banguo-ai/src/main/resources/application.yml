--- # 默认配置

server:
  port: 9211
  undertow:
    # 基础配置
    no-request-timeout: 300000  # 连接空闲超时5分钟（适配SSE长连接）
    max-http-post-size: 10485760  # 10MB，支持大数据量POST
    buffer-size: 65536 # 64KB缓冲区，优化SSE数据传输
    direct-buffers: true # 启用直接缓冲区，提升性能
    
    # 线程配置（针对SSE优化）
    io-threads: 8  # IO线程数（CPU核心数*2）
    worker-threads: 128  # 工作线程数（支持更多并发SSE连接）
    buffer-pool-size: 2048  # 缓冲池大小，支持更多SSE连接
    
    # 连接配置（支持流式响应）
    max-connections: 2000  # 最大并发连接数
    max-parameters: 2000   # URL参数数量
    max-headers: 500       # HTTP头数量
    max-header-size: 2097152  # 2MB，支持大请求头
    
    # 请求解析配置
    decode-url: true
    url-charset: UTF-8
    
    # SSE特定优化
    eager-filter-init: true  # 提前初始化过滤器
    preserve-path-on-forward: true  # 保持转发路径

nacos:
  server: @nacos.server@
  group: @nacos.discovery.group@
  seata:
    group: @nacos.seata.group@

local-active: @profiles.active@
# Spring
spring:
  application:
    # 应用名称
    name: banguo-ai
  profiles:
    # 环境配置
    active: ${SPRING_ACTIVE:${local-active}}

# MyBatis-Plus 配置
mybatis-plus:
  configuration:
    # 开启驼峰命名自动映射
    map-underscore-to-camel-case: true
    # 开启缓存
    cache-enabled: true
    # 日志实现
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    # 数据库配置
    db-config:
      # 主键类型
      id-type: auto
      # 逻辑删除配置
      logic-delete-field: delFlag
      logic-delete-value: 1
      logic-not-delete-value: 0
  # 类型别名包路径
  type-aliases-package: cn.xianlink.ai.domain
  # Mapper XML 文件路径
  mapper-locations: classpath*:mapper/**/*.xml

--- # nacos 配置
spring:
  cloud:
    nacos:
      # nacos 服务地址
      server-addr: ${NACOS_SERVER:${nacos.server}}
      discovery:
        # 注册组
        group: ${NACOS_GROUP:${nacos.group}}
        namespace: ${spring.profiles.active}
        username: ${NACOS_NAME:nacos}
        password: ${NACOS_PWD:nacos}
      config:
        # 配置组
        group: ${NACOS_GROUP:${nacos.group}}
        namespace: ${spring.profiles.active}
        username: ${NACOS_NAME:nacos}
        password: ${NACOS_PWD:nacos}
  config:
    import:
      - optional:nacos:application-common.yml
      - optional:nacos:datasource.yml
      - optional:nacos:${spring.application.name}.yml

--- # ai

# NOTE: You must disable the banner and the console logging
# to allow the STDIO transport to work !!!
# Config reference: https://docs.spring.io/spring-ai/reference/api/mcp/mcp-server-boot-starter-docs.html#_webflux_server_configuration

spring:
  codec:
    max-in-memory-size: 10MB
  main:
    banner-mode: off
  ai:
    mcp:
      server:
        name: banguo-mcp-server
        version: 0.0.1
        type: ASYNC  # Recommended for reactive applications
        # 配置 sse 的根路径，默认值为 /sse
        # 下面的最终路径为 ip:port/sse/mcp
        sse-endpoint: /ai/mcp/sse
        sse-message-endpoint: /ai/mcp/message
        capabilities:
          tool: true
          resource: true
          prompt: true
          completion: true
        stdio: false
    alibaba:
      mcp:
        nacos:
          namespace: ${spring.profiles.active}
          enabled: true
          server-addr: ${NACOS_SERVER:${nacos.server}}
          username: ${NACOS_NAME:nacos}
          password: ${NACOS_PWD:nacos}
          registry:
            enabled: true
            service-group: ${NACOS_GROUP:${nacos.group}}    
