# 已完成 ✅

# 表结构

```
support_message.sql
```

# 变更表名

```
RENAME TABLE `banguo_ai`.`feedback` TO `banguo_ai`.`support_feedback`;
RENAME TABLE `banguo_ai`.`service_ticket` TO `banguo_ai`.`support_service_ticket`;
```

# 新增字段

```

ALTER TABLE `banguo_ai`.`support_service_ticket` 
ADD COLUMN `open_id` VARCHAR(100) COLLATE utf8mb4_bin NOT NULL COMMENT '用户open_id' AFTER `id`,
ADD COLUMN `session_id` VARCHAR(32) COLLATE utf8mb4_bin COMMENT '会话ID' AFTER `open_id`;
```

# 相关的子模块

```
banguo-basic
banguo-order
banguo-trade
xianjian-common-wechat
```

# nacos 配置

```

 cloud:
    function:
      definition: messageProcessConsumer
    stream:
      rocketmq:
        binder:
          name-server: 10.222.128.195:9876;10.222.129.4:9876
          #name-server: rocketmq-namesrv-1.common:9876;rocketmq-namesrv-2.common:9876
      bindings:
        messageProcessConsumer-in-0:
          content-type: application/json
          destination: ${mq.profiles}ai-support-message-process-topic
          group: ai-support-message-process-group
        messageProcessProducer-out-0:
          content-type: application/json
          destination: ${mq.profiles}ai-support-message-process-topic
```

```
mq:
  profiles: banguo_${spring.profiles.active}_

```

```


ai:
  # API Key配置
  mcp:
    api-key:
      # 默认API Keys（逗号分隔，用于测试和开发环境）
      default-keys: ${AI_MCP_API_KEY_DEFAULT:sk-5dcce97078013c26f624b12cd6980828}
      # 每日使用限制
      usage-limit: ${AI_MCP_API_KEY_USAGE_LIMIT:100000}
      # 缓存TTL（秒）
      cache-ttl: ${AI_MCP_API_KEY_CACHE_TTL:3600}
  # 阿里云百练AI配置
  bailian:
  # 智能体配置
    app:
      # APP ID配置（从环境变量或配置中心获取）
      app-id: ${AI_BAILIAN_APP_APP_ID:4d0f0f84c7b54c178830646a5064cfd5}
      # API Key配置（从环境变量或配置中心获取）
      api-key: ${AI_BAILIAN_APP_API_KEY:sk-dc793babf25a4f13b77265e1143fa3c1}
    # 模型配置
    model:
      # 模型API Key配置（从环境变量或配置中心获取）
      api-key: ${AI_BAILIAN_MODEL_API_KEY:sk-eb99c022f27e4bd1b94afeafc4a45b2c}
      # 模型名称配置（从环境变量或配置中心获取） 免费模型
      default-model: ${AI_BAILIAN_MODEL_DEFAULT_MODEL:deepseek-r1-distill-llama-70b}

```

```

  
  # 新增：originalId 到小程序 appId 的映射关系
  original-mapping:
    configs:
      gh_ec67c6255831: wx863edfff90bb02c0    # 领鲜集采小程序
      gh_28873a0e022f: wx5da7004a4491e2c1    # 般果集采平台+
      gh_c6cebacb87c4: wx9cc94925bfca5c9d    # 总仓小程序
      gh_4a5d73fc9aea: wxdfad8c82da70105d    # 供应商小程序
      gh_558bb7eda9cf: wx7ae0dc5396e60821    # 城市仓小程序
      
```

