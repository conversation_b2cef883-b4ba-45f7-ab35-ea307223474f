# 待上线 🔧

# 表结构变更

```

-- 字段重命名
ALTER TABLE `banguo_ai`.`support_message_history` 
CHANGE COLUMN `msg_id` `message_id` VARCHAR(32) NOT NULL COMMENT '消息ID',
CHANGE COLUMN `to_user_name` `source` VARCHAR(32) COLLATE utf8mb4_bin NOT NULL COMMENT '消息来源',
CHANGE COLUMN `from_user_name` `open_id` VARCHAR(32) COLLATE utf8mb4_bin NOT NULL COMMENT 'Open ID',
CHANGE COLUMN `msg_type` `message_type` VARCHAR(16) COLLATE utf8mb4_bin NOT NULL COMMENT '消息类型：text-文本，image-图片，voice-语音，video-视频，location-地理位置，link-链接',
CHANGE COLUMN `content` `data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '消息内容';
  

-- 删除字段
ALTER TABLE `banguo_ai`.`support_message_history` 
DROP COLUMN `pic_url`,
DROP COLUMN `media_id`,
DROP COLUMN `link_info`,
DROP COLUMN `card_info`;

-- 新增字段

ALTER TABLE `banguo_ai`.`support_message_history` 
ADD COLUMN  `user_id` bigint NOT NULL DEFAULT 0 COMMENT '用户ID' after `open_id`,
ADD COLUMN  `ms_id` bigint NOT NULL DEFAULT 0 COMMENT 'message session ID' after `id`;


-- 索引更新
ALTER TABLE `banguo_ai`.`support_message_history` DROP INDEX `idx_from_user_name`;
ALTER TABLE `banguo_ai`.`support_message_history` DROP INDEX `idx_msg_id`;
ALTER TABLE `banguo_ai`.`support_message_history` ADD INDEX `idx_user_id` (`user_id`);
ALTER TABLE `banguo_ai`.`support_message_history` ADD INDEX `idx_message_id` (`message_id`);

-- 字段重命名
ALTER TABLE `banguo_ai`.`support_message_session` 
CHANGE COLUMN `customer_id` `user_id` VARCHAR(100) COLLATE utf8mb4_bin NOT NULL COMMENT '用户id';

-- 新增字段
ALTER TABLE `banguo_ai`.`support_message_session` 
ADD COLUMN `user_id` bigint NULL DEFAULT 0 COMMENT '用户ID' AFTER `customer_id`,
ADD COLUMN `question_type` VARCHAR(255) COLLATE utf8mb4_bin DEFAULT '' COMMENT '问题类型' AFTER `title`;

-- 索引更新
ALTER TABLE `banguo_ai`.`support_message_session` DROP INDEX `idx_customer_id`;
ALTER TABLE `banguo_ai`.`support_message_session` ADD INDEX `idx_user_id` (`user_id`);

UPDATE `banguo_ai`.`support_message_session` SET `user_id` = `customer_id`;

-- 删除字段
ALTER TABLE `banguo_ai`.`support_message_session` 
DROP COLUMN `customer_id`;

```

# 相关的子模块

```

```

# nacos 配置

```


      
```

