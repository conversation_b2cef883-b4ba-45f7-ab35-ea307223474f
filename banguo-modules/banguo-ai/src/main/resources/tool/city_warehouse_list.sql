-- auto-generated definition
create table banguo_basic.bas_city_wh
(
    id                     bigint auto_increment comment '城市仓id，主键'
        primary key,
    city_wh_code           varchar(64)                            not null comment '城市仓编码',
    city_wh_name           varchar(100)                           not null comment '城市仓名称',
    city_wh_initial        char                                   not null comment '城市仓首字母',
    city_wh_type_code      tinyint null comment '城市仓类型代码',
    admin_code             varchar(64)  default '' null comment '超管代码',
    status                 tinyint      default 1                 not null comment '状态，0为不可用；1为可用',
    status_time            datetime null comment '是否可用状态变化时间',
    area_code              varchar(32) null comment '区域编码',
    area_full_name         varchar(100) null comment '区域全称[冗余]',
    address                varchar(128)                           not null comment '详细地址',
    lng                    varchar(64)  default ''                not null comment '经度',
    lat                    varchar(64)  default ''                not null comment '纬度',
    is_show                tinyint      default 1                 not null comment '是否集采平台展示；0:否，1:是',
    sort                   smallint                               not null comment '排序号',
    delivery_days          smallint     default 0                 not null comment '提货天数',
    delivery_complete_rule tinyint null comment '提货完成策略；1:分货即提货，2:确认才提货【暂时不用】',
    remark                 varchar(255) default '' null comment '备注',
    tenant_id              varchar(128) default ''                not null comment 'tenant_id',
    del_flag               bigint       default 0                 not null comment '删除标志（0代表存在，存入这条数据的id代表删除）',
    create_code            varchar(64)  default ''                not null comment '创建用户代码',
    create_name            varchar(64)  default ''                not null comment '创建用户名称',
    create_time            datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
    update_code            varchar(64)  default ''                not null comment '修改用户代码',
    update_name            varchar(64)  default ''                not null comment '修改用户名称',
    update_time            datetime     default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '修改时间',
    constraint idx_bas_city_wh_city_wh_code
        unique (city_wh_code)
) comment '城市仓定义表';

create index idx_city_wh_name
    on bas_city_wh (city_wh_name);

-- auto-generated definition
create table banguo_basic.bas_city_wh_place
(
    id                bigint auto_increment comment '城市仓提货点id，主键'
        primary key,
    city_wh_id        bigint                                 not null comment '城市仓id[外键]',
    city_wh_code      varchar(64)                            not null comment '城市仓编码[冗余]',
    city_wh_name      varchar(100)                           not null comment '城市仓名称[冗余]',
    place_code        varchar(68)                            not null comment '提货点编码',
    place_name        varchar(100)                           not null comment '提货点名称',
    child_count       smallint     default 0                 not null comment '子提货点个数',
    parent_place_id   bigint null comment '父级提货点id',
    parent_place_code varchar(68)  default '' null comment '父级提货点编码',
    address           varchar(128)                           not null comment '详细地址',
    lng               varchar(64)  default ''                not null comment '经度',
    lat               varchar(64)  default ''                not null comment '纬度',
    position_title    varchar(128) default ''                not null comment '位置标题',
    contact_name      varchar(32)  default ''                not null comment '联系人名称',
    contact_phone     varchar(32)  default ''                not null comment '联系人电话',
    remark            varchar(255) default '' null comment '备注',
    tenant_id         varchar(128) default ''                not null comment 'tenant_id',
    del_flag          bigint       default 0                 not null comment '删除标志（0代表存在，存入这条数据的id代表删除）',
    create_code       varchar(64)  default ''                not null comment '创建用户代码',
    create_name       varchar(64)  default ''                not null comment '创建用户名称',
    create_time       datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
    update_code       varchar(64)  default ''                not null comment '修改用户代码',
    update_name       varchar(64)  default ''                not null comment '修改用户名称',
    update_time       datetime     default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '修改时间',
    path              varchar(255) default ''                not null comment 'path地址',
    constraint ux_bas_city_wh_place_place_code
        unique (place_code, del_flag)
) comment '城市仓提货点表';

create index idx_bas_city_wh_place_city_wh_code
    on bas_city_wh_place (city_wh_code);

create index idx_bas_city_wh_place_city_wh_id
    on bas_city_wh_place (city_wh_id);

create index idx_place_name
    on bas_city_wh_place (place_name);

create index idx_update_name
    on bas_city_wh_place (update_name);


-- auto-generated definition
create table banguo_system.sys_user
(
    user_id      bigint auto_increment comment '用户ID'
        primary key,
    username     varchar(30)                            not null comment '用户名',
    user_code    varchar(32)                            not null comment '用户编号',
    phone_no     varchar(11)                            not null comment '手机号码',
    email        varchar(50)  default ''                not null comment '用户邮箱',
    user_type    varchar(32)  default 'sys_user'        not null comment '用户类型（sys_user系统用户）',
    status       tinyint(1)   default 1                 not null comment '状态，0为禁用；1为可用',
    audit_status tinyint      default 1                 not null comment '审核状态，0待审核；1审核通过；2审核不通过',
    password     varchar(100) default '' null comment '密码',
    nick_name    varchar(32)  default ''                not null comment '用户昵称',
    real_name    varchar(32)  default ''                not null comment '真实姓名',
    sex          char         default '0' null comment '用户性别（0男 1女 2未知）',
    org_id       bigint       default 0                 not null comment '所属机构ID',
    dept_id      bigint       default 0                 not null comment '部门ID',
    post_id      bigint       default 0                 not null comment '岗位ID',
    avatar       varchar(255) default '' null comment '头像地址',
    remark       varchar(500) default '' null comment '备注',
    login_ip     varchar(128) default '' null comment '最后登录IP',
    login_date   datetime null comment '最后登录时间',
    tenant_id    varchar(20)                            not null comment '租户编号',
    del_flag     bigint       default 0                 not null comment '删除标志(0未删，非0已删）',
    create_code  varchar(64)  default ''                not null comment '创建人编号',
    create_name  varchar(64)  default ''                not null comment '创建人姓名',
    create_time  datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
    update_code  varchar(64)  default ''                not null comment '最后更新人编号',
    update_name  varchar(64)  default ''                not null comment '最后更新人姓名',
    update_time  datetime     default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '最后更新时间',
    import_time  datetime null comment '数据导入时间',
    constraint idx_user_code
        unique (user_code, del_flag)
) comment '系统用户表';

create index idx_dept
    on sys_user (dept_id, del_flag);

create index idx_org
    on sys_user (org_id, del_flag);

create index idx_phone_no_user_type
    on sys_user (phone_no, user_type, del_flag, tenant_id);

create index idx_user_type
    on sys_user (user_type);

-- 查询提货点地址的SQL语句
SELECT bcw.id  as warehouse_id,
       bcp.id  as place_id,
       bcp.position_title,

       CASE
           WHEN LOCATE('-', bcp.city_wh_name) > 0 THEN SUBSTRING(bcp.city_wh_name, 3)
           ELSE bcp.city_wh_name
           END AS city_wh_name,
       bcp.place_name,
       -- 如果提货点地址为空，则使用城市仓的地址
       CASE
           WHEN bcp.address IS NULL OR bcp.address = '' THEN CONCAT(bcw.address)
           ELSE CONCAT(bcp.address)
           END as address,
       -- 如果提货点联系人电话为空，则使用城市仓的联系人信息
       CASE
           WHEN bcp.contact_phone IS NULL OR bcp.contact_phone = '' THEN su.real_name
           ELSE bcp.contact_name
           END AS contact_name,
       CASE
           WHEN bcp.contact_phone IS NULL OR bcp.contact_phone = '' THEN su.phone_no
           ELSE bcp.contact_phone
           END AS contact_phone
FROM banguo_basic.bas_city_wh_place bcp
         LEFT JOIN banguo_basic.bas_city_wh bcw
                   ON bcp.city_wh_id = bcw.id
                       AND bcw.del_flag = 0 -- 确保城市仓未删除
         LEFT JOIN banguo_system.sys_user su
                   ON bcw.admin_code = su.user_code
                       AND su.del_flag = 0 -- 确保超管用户未删除
WHERE bcp.del_flag = 0 -- 确保提货点未删除
  AND bcw.status = 1   -- 确保城市仓状态为可用
ORDER BY address limit 10;


