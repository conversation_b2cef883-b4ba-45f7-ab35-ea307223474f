-- ================================================================
-- 消息会话表(support_message_session)字段迁移SQL
-- 执行时间：建议在业务低峰期执行
-- 影响：重命名customerId为userId，新增question_type字段
-- ================================================================

-- 1. 将customer_id字段重命名为user_id
-- 注意：确保在执行前已停止相关服务，避免数据不一致
ALTER TABLE `banguo_ai`.`support_message_session` 
CHANGE COLUMN `customer_id` `user_id` VARCHAR(100) COLLATE utf8mb4_bin NOT NULL COMMENT '用户id';

-- 2. 更新索引名称，删除原有的customer_id索引
ALTER TABLE `banguo_ai`.`support_message_session` 
DROP INDEX `idx_customer_id`;

-- 3. 为新的user_id字段添加索引
ALTER TABLE `banguo_ai`.`support_message_session` 
ADD INDEX `idx_user_id` (`user_id`);

-- 4. 在title字段后新增question_type字段
ALTER TABLE `banguo_ai`.`support_message_session` 
ADD COLUMN `question_type` VARCHAR(20) COLLATE utf8mb4_bin COMMENT '问题类型' AFTER `title`;

-- ================================================================
-- 验证SQL（可选执行，用于验证迁移结果）
-- ================================================================

-- 查看表结构，确认字段修改成功
DESCRIBE `banguo_ai`.`support_message_session`;

-- 查看索引情况，确认索引修改成功
SHOW INDEX FROM `banguo_ai`.`support_message_session`;

-- 查看前几条数据，确认数据完整性
SELECT 
    id, 
    session_id, 
    open_id, 
    user_id,  -- 新字段名
    nickname, 
    phone, 
    title, 
    question_type, -- 新字段
    description,
    session_status,
    create_time
FROM `banguo_ai`.`support_message_session` 
ORDER BY id DESC 
LIMIT 5;

-- ================================================================
-- 回滚SQL（紧急情况下使用，仅在迁移完成后短时间内可用）
-- ================================================================

-- 注意：回滚前确保新字段question_type的数据可以丢失
-- ROLLBACK STEP 1: 删除question_type字段
-- ALTER TABLE `banguo_ai`.`support_message_session` DROP COLUMN `question_type`;

-- ROLLBACK STEP 2: 将user_id重命名回customer_id
-- ALTER TABLE `banguo_ai`.`support_message_session` 
-- CHANGE COLUMN `user_id` `customer_id` VARCHAR(100) COLLATE utf8mb4_bin NOT NULL COMMENT '客户id';

-- ROLLBACK STEP 3: 更新索引
-- ALTER TABLE `banguo_ai`.`support_message_session` DROP INDEX `idx_user_id`;
-- ALTER TABLE `banguo_ai`.`support_message_session` ADD INDEX `idx_customer_id` (`customer_id`);

-- ================================================================
-- 数据迁移完成确认清单
-- ================================================================
-- □ 1. 服务已停止
-- □ 2. 数据已备份
-- □ 3. customerId -> userId 重命名完成
-- □ 4. question_type字段添加完成
-- □ 5. 索引更新完成
-- □ 6. 验证SQL执行成功
-- □ 7. 应用代码已更新部署
-- □ 8. 功能测试通过
-- □ 9. 性能测试通过
-- □ 10. 回滚方案已准备（可选）
-- ================================================================ 