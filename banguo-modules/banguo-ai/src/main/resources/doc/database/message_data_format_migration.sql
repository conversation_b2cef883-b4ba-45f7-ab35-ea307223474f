-- 消息数据格式迁移脚本
-- 用途：将support_message_history表中的data字段从旧格式转换为新格式
-- 作者：howard
-- 日期：2025-01-16
-- 说明：将直接字符串格式转换为标准JSON格式 {"type":"text","content":"原内容"}

USE `banguo_ai`;

-- 备份表（可选，建议在生产环境执行）
-- CREATE TABLE `support_message_history_backup_20250116` AS SELECT * FROM `support_message_history`;

-- 更新直接字符串格式的文本消息为标准JSON格式
UPDATE `support_message_history` 
SET `data` = JSON_OBJECT('type', 'text', 'content', `data`)
WHERE `message_type` = 'text' 
  AND `data` IS NOT NULL 
  AND `data` != ''
  AND `data` NOT LIKE '{%'  -- 排除已经是JSON格式的数据
  AND LENGTH(`data`) > 0;

-- 验证更新结果
SELECT 
    COUNT(*) as total_records,
    SUM(CASE WHEN `data` LIKE '{"type":"text"%' THEN 1 ELSE 0 END) as standard_format_count,
    SUM(CASE WHEN `data` NOT LIKE '{%' AND `data` IS NOT NULL AND `data` != '' THEN 1 ELSE 0 END) as old_format_count
FROM `support_message_history` 
WHERE `message_type` = 'text';

-- 显示更新前后的对比（示例数据，前10条）
SELECT 
    id,
    message_type,
    LEFT(`data`, 100) as data_sample,
    create_time
FROM `support_message_history` 
WHERE `message_type` = 'text' 
  AND `data` IS NOT NULL 
  AND `data` != ''
ORDER BY id DESC 
LIMIT 10;

-- 查找可能的异常数据
SELECT 
    id,
    message_type,
    `data`,
    create_time
FROM `support_message_history` 
WHERE `message_type` = 'text' 
  AND (`data` IS NULL OR `data` = '' OR (`data` NOT LIKE '{"type":"text"%' AND `data` LIKE '{%'))
ORDER BY id DESC 
LIMIT 10; 