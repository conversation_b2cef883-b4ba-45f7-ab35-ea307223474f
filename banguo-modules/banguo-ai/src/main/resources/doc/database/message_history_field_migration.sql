-- MessageHistory表字段变更SQL
-- 作者: howard
-- 日期: 2025年1月28日
-- 描述: 对support_message_history表进行字段重命名和删除操作

USE
`banguo_ai`;

-- 字段重命名
ALTER TABLE `support_message_history`
    CHANGE COLUMN `msg_id` `message_id` VARCHAR (32) NOT NULL COMMENT '消息ID',
    CHANGE COLUMN `to_user_name` `source` VARCHAR (32) COLLATE utf8mb4_bin NOT NULL COMMENT '消息来源',
    <PERSON>AN<PERSON> COLUMN `from_user_name` `user_id` VARCHAR (32) COLLATE utf8mb4_bin NOT NULL COMMENT '用户ID',
    <PERSON>AN<PERSON> COLUMN `msg_type` `message_type` VARCHAR (16) COLLATE utf8mb4_bin NOT NULL COMMENT '消息类型：text-文本，image-图片，voice-语音，video-视频，location-地理位置，link-链接';

-- 删除字段
ALTER TABLE `support_message_history`
DROP
COLUMN `pic_url`,
DROP
COLUMN `media_id`,
DROP
COLUMN `link_info`,
DROP
COLUMN `card_info`;

-- 验证表结构
DESCRIBE `support_message_history`; 