-- 智能客服工单记录系统数据库表

-- 客服工单表
CREATE TABLE `service_ticket`
(
    `id`                   BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `open_id`              VARCHAR(100) COLLATE utf8mb4_bin NOT NULL COMMENT '用户open_id',
    `session_id`           VARCHAR(32) COLLATE utf8mb4_bin COMMENT '会话ID',
    `ticket_no`            VARCHAR(32) COLLATE utf8mb4_bin  NOT NULL COMMENT '工单号',
    `customer_name`        VARCHAR(100) COLLATE utf8mb4_bin COMMENT '客户姓名',
    `customer_phone`       VARCHAR(20) COLLATE utf8mb4_bin COMMENT '客户电话',
    `title`                VARCHAR(50) COLLATE utf8mb4_bin COMMENT '工单标题',
    `problem_type`         VARCHAR(50) COLLATE utf8mb4_bin COMMENT '问题类型',
    `problem_description`  TEXT COLLATE utf8mb4_bin COMMENT '问题描述',
    `conversation_history` TEXT COLLATE utf8mb4_bin COMMENT '对话历史记录',
    `status`               TINYINT(1) DEFAULT 1 COMMENT '状态：1-待处理，2-处理中，3-已完成，4-已关闭',
    `ai_assessment`        TEXT COLLATE utf8mb4_bin COMMENT 'AI评估结果',
    `follow_up_time`       DATETIME COMMENT '预计回访时间',
    `resolved_time`        DATETIME COMMENT '解决时间',
    `remark`               TEXT COLLATE utf8mb4_bin COMMENT '备注',
    `tenant_id`            varchar(32) COLLATE utf8mb4_bin  NOT NULL COMMENT '机构ID',
    `del_flag`             bigint(20) NOT NULL DEFAULT '0' COMMENT '删除标志（0代表存在 存入这条数据的主键代表删除）',
    `create_time`          datetime                         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`          datetime                         NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `create_code`          varchar(32) COLLATE utf8mb4_bin  NOT NULL COMMENT '创建用户代码',
    `create_name`          varchar(32) COLLATE utf8mb4_bin  NOT NULL COMMENT '创建用户名称',
    `update_code`          varchar(32) COLLATE utf8mb4_bin  NOT NULL COMMENT '修改用户代码',
    `update_name`          varchar(32) COLLATE utf8mb4_bin  NOT NULL COMMENT '修改用户名称',

    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_ticket_no` (`ticket_no`),
    KEY                    `idx_customer_phone` (`customer_phone`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='客服工单记录表';

-- 工单处理记录表
CREATE TABLE `feedback`
(
    `id`            BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `ticket_id`     BIGINT(20) NOT NULL COMMENT '工单ID',
    `ticket_status` TINYINT(1) DEFAULT 1 COMMENT '状态：1-待处理，2-处理中，3-已完成，4-已关闭',
    `description`   TEXT COLLATE utf8mb4_bin COMMENT '操作描述',
    `tenant_id`     varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '机构ID',
    `del_flag`      bigint(20) NOT NULL DEFAULT '0' COMMENT '删除标志（0代表存在 存入这条数据的主键代表删除）',
    `create_time`   datetime                        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`   datetime                        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `create_code`   varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '创建用户代码',
    `create_name`   varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '创建用户名称',
    `update_code`   varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '修改用户代码',
    `update_name`   varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '修改用户名称',

    PRIMARY KEY (`id`),
    KEY             `idx_ticket_id` (`ticket_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='客服工单处理记录表';

-- 新增字段的ALTER TABLE语句（如果表已存在，可以直接执行以下语句）
ALTER TABLE `service_ticket`
    ADD COLUMN `open_id` VARCHAR(100) COLLATE utf8mb4_bin NOT NULL COMMENT '用户open_id' AFTER `id`,
ADD COLUMN `session_id` VARCHAR(32) COLLATE utf8mb4_bin COMMENT '会话ID' AFTER `open_id`; 

-- 更新service_ticket表结构
-- 1. 新增userId字段在open_id之后
ALTER TABLE `support_service_ticket` 
    ADD COLUMN `user_id` BIGINT(20) NOT NULL DEFAULT 0 COMMENT '用户ID' AFTER `open_id`;

-- 2. 删除open_id字段
ALTER TABLE `support_service_ticket` 
    DROP COLUMN `open_id`;

-- 3. 为session_id添加索引
ALTER TABLE `support_service_ticket` 
    ADD INDEX `idx_session_id` (`session_id`); 
