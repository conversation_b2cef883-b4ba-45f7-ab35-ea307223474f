-- 消息会话表
CREATE TABLE `banguo_ai`.`support_message_session`
(
    `id`                BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `session_id`        VARCHAR(100) COLLATE utf8mb4_bin NOT NULL COMMENT '会话ID',
    `open_id`           VARCHAR(100) COLLATE utf8mb4_bin NOT NULL COMMENT '用户open_id',
    `user_id`           VARCHAR(100) COLLATE utf8mb4_bin NOT NULL COMMENT '用户id',
    `nickname`          VARCHAR(100) COLLATE utf8mb4_bin NOT NULL COMMENT '用户昵称',
    `phone`             VARCHAR(100) COLLATE utf8mb4_bin NOT NULL COMMENT '用户手机号',
    `title`             VARCHAR(100) COLLATE utf8mb4_bin NOT NULL COMMENT '标题',
    `question_type`     VARCHAR(20) COLLATE utf8mb4_bin COMMENT '问题类型',
    `description`       TEXT COLLATE utf8mb4_bin COMMENT '操作描述',
    `session_status`    TINYINT(1) DEFAULT 1 COMMENT '会话状态：1-活跃，2-暂停，3-结束',
    `last_message_time` BIGINT(20) COMMENT '最后消息时间戳',
    `message_count`     INT(11) DEFAULT 0 COMMENT '消息数量',

    `tenant_id`         varchar(32) COLLATE utf8mb4_bin  NOT NULL COMMENT '机构ID',
    `del_flag`          bigint(20) NOT NULL DEFAULT '0' COMMENT '删除标志（0代表存在 存入这条数据的主键代表删除）',
    `create_time`       datetime                         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`       datetime                         NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `create_code`       varchar(32) COLLATE utf8mb4_bin  NOT NULL COMMENT '创建用户代码',
    `create_name`       varchar(32) COLLATE utf8mb4_bin  NOT NULL COMMENT '创建用户名称',
    `update_code`       varchar(32) COLLATE utf8mb4_bin  NOT NULL COMMENT '修改用户代码',
    `update_name`       varchar(32) COLLATE utf8mb4_bin  NOT NULL COMMENT '修改用户名称',

    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_session_id` (`session_id`),
    KEY                 `idx_open_id` (`open_id`),
    KEY                 `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='消息会话表';

-- 消息记录表
CREATE TABLE `banguo_ai`.`support_message_history`
(
    `id`               BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `session_id`       VARCHAR(32) COLLATE utf8mb4_bin COMMENT '会话ID',
    `msg_id`           VARCHAR(32)                     NOT NULL COMMENT '消息ID',
    `to_user_name`     VARCHAR(32) COLLATE utf8mb4_bin NOT NULL COMMENT '接收方微信号',
    `from_user_name`   VARCHAR(32) COLLATE utf8mb4_bin NOT NULL COMMENT '发送方微信号',
    `create_timestamp` BIGINT(11) NOT NULL COMMENT '消息创建时间戳',
    `msg_type`         VARCHAR(16) COLLATE utf8mb4_bin NOT NULL COMMENT '消息类型：text-文本，image-图片，voice-语音，video-视频，location-地理位置，link-链接',
    `content`          TEXT COLLATE utf8mb4_bin COMMENT '消息内容',
    `pic_url`          VARCHAR(255) COLLATE utf8mb4_bin COMMENT '图片链接',
    `media_id`         VARCHAR(255) COLLATE utf8mb4_bin COMMENT '媒体文件ID',
    `role`             VARCHAR(16) COLLATE utf8mb4_bin          DEFAULT 'user' COMMENT '角色类型：user-用户，assistant-助手，bot-机器人，system-系统，attachment-附件，tool-工具，manual-人工',

    `is_reply`         TINYINT(1) DEFAULT 0 COMMENT '是否回复消息：0-否，1-是',
    `is_send`          TINYINT(1) DEFAULT 0 COMMENT '是否发送成功：0-否，1-是',
    `link_info`        JSON COMMENT '图文链接对象',
    `card_info`        JSON COMMENT '小程序卡片对象',

    `tenant_id`        varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '机构ID',
    `del_flag`         bigint(20) NOT NULL DEFAULT '0' COMMENT '删除标志（0代表存在 存入这条数据的主键代表删除）',
    `create_time`      datetime                        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据库创建时间',
    `update_time`      datetime                        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `create_code`      varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '创建用户代码',
    `create_name`      varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '创建用户名称',
    `update_code`      varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '修改用户代码',
    `update_name`      varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '修改用户名称',

    PRIMARY KEY (`id`),
    KEY                `idx_from_user_name` (`from_user_name`),
    KEY                `idx_msg_id` (`msg_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='消息记录表';
