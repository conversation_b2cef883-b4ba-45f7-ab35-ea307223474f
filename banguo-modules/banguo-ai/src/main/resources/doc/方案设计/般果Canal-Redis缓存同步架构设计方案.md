# 般果Canal-Redis缓存同步架构设计方案

## 1. 整体架构设计

### 1.1 技术架构图

```mermaid
graph TB
    subgraph "数据源层"
        DB[(MySQL 8.0)]
    end
    
    subgraph "数据同步层"
        Canal[Canal 1.1.7<br/>Binlog解析]
        MQ[RocketMQ 4.6.1<br/>消息队列]
    end
    
    subgraph "缓存层"
        Redis[(Redis 7.x<br/>缓存存储)]
        ES[(Elasticsearch 8.12.0<br/>搜索引擎)]
    end
    
    subgraph "应用服务层"
        BasicApp[banguo-basic<br/>基础服务]
        OrderApp[banguo-order<br/>订单服务]
        ProductApp[banguo-product<br/>商品服务]
        MarketingApp[banguo-marketing<br/>营销服务]
        TradeApp[banguo-trade<br/>交易服务]
        AIApp[banguo-ai<br/>AI服务]
        BIApp[banguo-bi<br/>BI服务]
    end
    
    subgraph "框架组件层"
        SyncFramework[Cache-Sync-Framework<br/>缓存同步框架]
        EasyES[Easy-ES 1.1.0<br/>ES操作框架]
    end
    
    DB --> Canal
    Canal --> MQ
    MQ --> SyncFramework
    SyncFramework --> Redis
    SyncFramework --> ES
    
    BasicApp --> SyncFramework
    OrderApp --> SyncFramework
    ProductApp --> SyncFramework
    MarketingApp --> SyncFramework
    TradeApp --> SyncFramework
    AIApp --> SyncFramework
    BIApp --> SyncFramework
    
    style Canal fill:#e1f5fe
    style MQ fill:#f3e5f5
    style Redis fill:#ffebee
    style SyncFramework fill:#e8f5e8
```

### 1.2 数据流向图

```mermaid
sequenceDiagram
    participant DB as MySQL数据库
    participant Canal as Canal Server
    participant MQ as RocketMQ
    participant App as 业务应用
    participant Cache as Redis缓存
    participant ES as Elasticsearch
    
    DB->>Canal: Binlog事件
    Canal->>MQ: 发送数据变更消息
    
    Note over MQ: 消息分发策略<br/>按表名路由
    
    MQ->>App: 消费数据变更消息
    
    par 缓存更新
        App->>Cache: 更新Redis缓存
    and 搜索更新  
        App->>ES: 更新ES索引
    end
    
    Note over App: 事务消息确保<br/>数据一致性
```

## 2. 核心组件设计

### 2.1 缓存同步框架结构

```mermaid
graph TB
    subgraph "Cache-Sync-Framework"
        subgraph "核心层"
            CacheConfig[缓存配置管理]
            EntityMapper[实体映射器]
            KeyGenerator[缓存键生成器]
            SyncProcessor[同步处理器]
        end
        
        subgraph "注解层"
            EnableCache[@EnableCacheSync]
            CacheEntity[@CacheEntity]
            CacheKey[@CacheKey]
            CacheField[@CacheField]
        end
        
        subgraph "监听层"
            MQListener[MQ消息监听器]
            TableHandler[表变更处理器]
            EventProcessor[事件处理器]
        end
        
        subgraph "存储层"
            RedisTemplate[Redis操作模板]
            ESTemplate[ES操作模板]
            CacheSerializer[缓存序列化器]
        end
    end
    
    EnableCache --> CacheConfig
    CacheEntity --> EntityMapper
    CacheKey --> KeyGenerator
    MQListener --> SyncProcessor
    SyncProcessor --> RedisTemplate
    SyncProcessor --> ESTemplate
```

### 2.2 组件职责说明

| 组件                 | 职责     | 核心功能               |
|--------------------|--------|--------------------|
| `@EnableCacheSync` | 启用缓存同步 | 自动配置、规则加载          |
| `@CacheEntity`     | 标记缓存实体 | 表名映射、缓存策略          |
| `@CacheKey`        | 缓存键标记  | 主键识别、分片键设定         |
| `@CacheField`      | 字段映射配置 | 字段转换、序列化规则         |
| `EntityMapper`     | 实体映射器  | MySQL↔Java↔Redis转换 |
| `KeyGenerator`     | 键生成器   | 缓存键规则生成            |
| `SyncProcessor`    | 同步处理器  | 业务逻辑处理             |

## 3. 实体映射设计

### 3.1 注解设计规范

```java
/**
 * 缓存实体标记
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface CacheEntity {
    
    /**
     * 数据库表名
     */
    String tableName();
    
    /**
     * 缓存键前缀
     */
    String keyPrefix();
    
    /**
     * 缓存过期时间（秒）
     */
    long expireTime() default 3600;
    
    /**
     * 缓存策略
     */
    CacheStrategy strategy() default CacheStrategy.HASH;
    
    /**
     * 是否同步到ES
     */
    boolean syncToES() default false;
    
    /**
     * ES索引名称
     */
    String esIndex() default "";
}

/**
 * 缓存键标记
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface CacheKey {
    
    /**
     * 是否为分片键
     */
    boolean isShardKey() default false;
    
    /**
     * 键类型
     */
    KeyType keyType() default KeyType.PRIMARY;
}

/**
 * 缓存字段配置
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface CacheField {
    
    /**
     * Redis字段名（不设置则使用Java字段名）
     */
    String redisField() default "";
    
    /**
     * 是否索引字段（用于二级索引）
     */
    boolean isIndex() default false;
    
    /**
     * 字段转换器
     */
    Class<? extends FieldConverter> converter() default DefaultConverter.class;
    
    /**
     * 是否忽略该字段
     */
    boolean ignore() default false;
}
```

### 3.2 实体映射示例

```java
/**
 * 客户实体映射示例
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("bas_customer")
@CacheEntity(
    tableName = "bas_customer",
    keyPrefix = "customer",
    expireTime = 7200,
    strategy = CacheStrategy.HASH,
    syncToES = true,
    esIndex = "customer_index"
)
public class BasCustomer extends TenantEntity {

    @TableId(value = "id")
    @CacheKey(keyType = KeyType.PRIMARY)
    private Long id;

    @CacheField(isIndex = true)
    private String code;

    @CacheField(redisField = "customer_name")
    private String name;

    @CacheField(isIndex = true)
    private String phone;

    @CacheKey(isShardKey = true)
    private Long cityWhId;

    @CacheField(converter = StatusConverter.class)
    private Integer status;

    @CacheField(ignore = true)
    private String remark; // 不缓存备注字段
}

/**
 * 订单实体映射示例
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("`order`")
@CacheEntity(
    tableName = "order",
    keyPrefix = "order",
    expireTime = 1800,
    strategy = CacheStrategy.HASH,
    syncToES = true,
    esIndex = "order_index"
)
public class Order extends TenantEntity {

    @TableId(value = "id")
    @CacheKey(keyType = KeyType.PRIMARY)
    private Long id;

    @CacheField(isIndex = true)
    private String code;

    @CacheKey(isShardKey = true)
    private Long customerId;

    @CacheField(converter = BigDecimalConverter.class)
    private BigDecimal totalAmount;

    @CacheField(converter = DateTimeConverter.class)
    private LocalDateTime orderTime;

    @CacheField(converter = OrderStatusConverter.class)
    private Integer status;
}
```

## 4. 缓存键设计规范

### 4.1 键命名规范

```mermaid
graph LR
    subgraph "缓存键结构"
        A[业务前缀] --> B[分片键] --> C[主键ID]
    end
    
    subgraph "示例"
        D[customer:1001:12345]
        E[order:2001:98765]
        F[product:3001:56789]
    end
    
    A -.-> D
    A -.-> E  
    A -.-> F
```

### 4.2 键生成策略

```java
/**
 * 缓存键生成器
 */
@Component
public class CacheKeyGenerator {
    
    /**
     * 生成主键
     * 格式: {prefix}:{shardKey}:{id}
     */
    public String generatePrimaryKey(Object entity, CacheEntity cacheEntity) {
        String prefix = cacheEntity.keyPrefix();
        String shardKey = extractShardKey(entity);
        String primaryKey = extractPrimaryKey(entity);
        
        return String.format("%s:%s:%s", prefix, shardKey, primaryKey);
    }
    
    /**
     * 生成索引键
     * 格式: {prefix}:idx:{fieldName}:{fieldValue}
     */
    public String generateIndexKey(String prefix, String fieldName, Object fieldValue) {
        return String.format("%s:idx:%s:%s", prefix, fieldName, fieldValue);
    }
    
    /**
     * 生成列表键
     * 格式: {prefix}:list:{condition}
     */
    public String generateListKey(String prefix, String condition) {
        return String.format("%s:list:%s", prefix, condition);
    }
}
```

### 4.3 缓存策略枚举

```java
/**
 * 缓存策略
 */
public enum CacheStrategy {
    
    /**
     * Hash结构 - 适用于实体对象
     */
    HASH("hash"),
    
    /**
     * String结构 - 适用于简单值
     */
    STRING("string"),
    
    /**
     * List结构 - 适用于列表数据
     */
    LIST("list"),
    
    /**
     * Set结构 - 适用于去重集合
     */
    SET("set"),
    
    /**
     * ZSet结构 - 适用于排序集合
     */
    ZSET("zset");
}
```

## 5. Canal配置设计

### 5.1 Canal Server配置

```yaml
# canal.properties
canal.serverMode = rocketMQ
canal.mq.servers = 127.0.0.1:9876
canal.mq.retries = 3
canal.mq.batchSize = 1000
canal.mq.maxRequestSize = 1048576

# 实例配置
canal.destinations = banguo-sync
canal.conf.dir = ../conf
canal.zkServers = 127.0.0.1:2181

# MQ配置
canal.mq.topic = canal-banguo
canal.mq.partition = 0
canal.mq.partitionsNum = 1
canal.mq.partitionHash = ${database}.${table}
```

### 5.2 实例配置文件

```yaml
# instance.properties
canal.instance.master.address = 127.0.0.1:3306
canal.instance.dbUsername = canal
canal.instance.dbPassword = canal
canal.instance.defaultDatabaseName = banguo

# 过滤配置
canal.instance.filter.regex = banguo\\..*
canal.instance.filter.black.regex = .*\\.tmp_.*

# 监听表配置
canal.instance.filter.regex = banguo\\.bas_customer,banguo\\.order,banguo\\.spu,banguo\\.account_info,banguo\\.service_ticket

# MQ发送配置
canal.mq.topic = canal-banguo
canal.mq.partition = 0
canal.mq.dynamicTopic = canal-${database}-${table}
```

## 6. SpringBoot集成配置

### 6.1 自动配置类

```java
/**
 * 缓存同步自动配置
 */
@Configuration
@EnableConfigurationProperties(CacheSyncProperties.class)
@ConditionalOnProperty(prefix = "banguo.cache.sync", name = "enabled", havingValue = "true")
public class CacheSyncAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public CacheEntityRegistry cacheEntityRegistry() {
        return new CacheEntityRegistry();
    }

    @Bean
    @ConditionalOnMissingBean
    public CacheKeyGenerator cacheKeyGenerator() {
        return new CacheKeyGenerator();
    }

    @Bean
    @ConditionalOnMissingBean
    public CacheSyncProcessor cacheSyncProcessor(
            RedisTemplate<String, Object> redisTemplate,
            CacheEntityRegistry registry,
            CacheKeyGenerator keyGenerator) {
        return new CacheSyncProcessor(redisTemplate, registry, keyGenerator);
    }

    @Bean
    public CanalMessageListener canalMessageListener(CacheSyncProcessor processor) {
        return new CanalMessageListener(processor);
    }
}
```

### 6.2 配置属性类

```yaml
# application.yml
banguo:
  cache:
    sync:
      enabled: true
      # Canal MQ配置
      canal:
        topic: canal-banguo
        consumerGroup: banguo-cache-sync
        nameserver: 127.0.0.1:9876
      # Redis配置
      redis:
        database: 1
        key-prefix: banguo
        default-expire: 3600
      # ES配置
      elasticsearch:
        enabled: true
        cluster-name: banguo-es
        cluster-nodes: 127.0.0.1:9200

spring:
  redis:
    host: 127.0.0.1
    port: 6379
    database: 1
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 200
        max-idle: 20
        min-idle: 5
```

## 7. 消息处理流程

### 7.1 消息处理流程图

```mermaid
flowchart TD
    A[Canal Binlog Event] --> B{消息类型判断}
    
    B -->|INSERT| C[新增处理]
    B -->|UPDATE| D[更新处理]
    B -->|DELETE| E[删除处理]
    
    C --> F[构建缓存对象]
    D --> G[差异比较]
    E --> H[清理缓存]
    
    F --> I[生成缓存键]
    G --> I
    
    I --> J{缓存策略}
    
    J -->|HASH| K[HSET操作]
    J -->|STRING| L[SET操作]
    J -->|LIST| M[LPUSH操作]
    
    K --> N[设置过期时间]
    L --> N
    M --> N
    
    N --> O{是否同步ES}
    O -->|是| P[更新ES索引]
    O -->|否| Q[处理完成]
    P --> Q
    
    H --> R[删除主键缓存]
    R --> S[删除索引缓存]
    S --> T[删除ES文档]
    T --> Q
```

### 7.2 消息处理器实现

```java
/**
 * Canal消息监听器
 */
@Component
@RocketMQMessageListener(
    topic = "${banguo.cache.sync.canal.topic}",
    consumerGroup = "${banguo.cache.sync.canal.consumerGroup}",
    messageModel = MessageModel.CLUSTERING
)
public class CanalMessageListener implements RocketMQListener<String> {

    private final CacheSyncProcessor cacheSyncProcessor;
    private final ObjectMapper objectMapper;

    @Override
    public void onMessage(String message) {
        try {
            CanalMessage canalMessage = objectMapper.readValue(message, CanalMessage.class);
            cacheSyncProcessor.process(canalMessage);
        } catch (Exception e) {
            log.error("处理Canal消息失败: {}", message, e);
            throw new CacheSyncException("消息处理失败", e);
        }
    }
}

/**
 * 缓存同步处理器
 */
@Component
@Transactional
public class CacheSyncProcessor {

    public void process(CanalMessage message) {
        String tableName = message.getTable();
        EventType eventType = message.getType();
        
        // 获取实体注册信息
        CacheEntityInfo entityInfo = cacheEntityRegistry.getEntityInfo(tableName);
        if (entityInfo == null) {
            return; // 不需要缓存的表
        }
        
        switch (eventType) {
            case INSERT:
                handleInsert(entityInfo, message.getData());
                break;
            case UPDATE:
                handleUpdate(entityInfo, message.getData(), message.getOld());
                break;
            case DELETE:
                handleDelete(entityInfo, message.getData());
                break;
        }
    }
    
    private void handleInsert(CacheEntityInfo entityInfo, List<Map<String, Object>> dataList) {
        for (Map<String, Object> data : dataList) {
            Object entity = mapToEntity(entityInfo, data);
            cacheEntity(entityInfo, entity);
            syncToElasticsearch(entityInfo, entity);
        }
    }
    
    private void handleUpdate(CacheEntityInfo entityInfo, 
                             List<Map<String, Object>> dataList,
                             List<Map<String, Object>> oldDataList) {
        for (int i = 0; i < dataList.size(); i++) {
            Map<String, Object> data = dataList.get(i);
            Map<String, Object> oldData = oldDataList.get(i);
            
            Object entity = mapToEntity(entityInfo, data);
            Object oldEntity = mapToEntity(entityInfo, oldData);
            
            updateCache(entityInfo, entity, oldEntity);
            syncToElasticsearch(entityInfo, entity);
        }
    }
    
    private void handleDelete(CacheEntityInfo entityInfo, List<Map<String, Object>> dataList) {
        for (Map<String, Object> data : dataList) {
            Object entity = mapToEntity(entityInfo, data);
            deleteCache(entityInfo, entity);
            deleteFromElasticsearch(entityInfo, entity);
        }
    }
}
```

## 8. 业务服务接入方式

### 8.1 启用注解

```java
/**
 * 主应用类添加注解
 */
@SpringBootApplication
@EnableCacheSync // 启用缓存同步
public class BanguoBasicApplication {
    public static void main(String[] args) {
        SpringApplication.run(BanguoBasicApplication.class, args);
    }
}
```

### 8.2 实体类配置

```java
/**
 * 只需在现有实体类上添加注解
 */
@CacheEntity(
    tableName = "bas_customer",
    keyPrefix = "customer",
    expireTime = 7200
)
public class BasCustomer extends TenantEntity {
    // 现有代码不变，只需添加注解
}
```

### 8.3 服务层使用

```java
/**
 * 业务服务层
 */
@Service
public class CustomerService {
    
    @Autowired
    private CacheTemplate cacheTemplate;
    
    /**
     * 查询客户 - 自动缓存
     */
    public BasCustomer getCustomer(Long id) {
        return cacheTemplate.get("customer", id, BasCustomer.class);
    }
    
    /**
     * 按索引查询 - 自动缓存
     */
    public BasCustomer getCustomerByCode(String code) {
        return cacheTemplate.getByIndex("customer", "code", code, BasCustomer.class);
    }
    
    /**
     * 查询列表 - 自动缓存
     */
    public List<BasCustomer> getCustomersByCityWh(Long cityWhId) {
        return cacheTemplate.getList("customer", "cityWhId", cityWhId, BasCustomer.class);
    }
}
```

## 9. 缓存一致性保障

### 9.1 事务消息机制

```mermaid
sequenceDiagram
    participant App as 业务应用
    participant MQ as RocketMQ
    participant Cache as Redis缓存
    participant DB as MySQL
    
    App->>MQ: 1. 发送Half消息
    MQ-->>App: 2. Half消息发送成功
    
    App->>DB: 3. 执行数据库事务
    
    alt 事务成功
        App->>MQ: 4. 提交消息
        MQ->>Cache: 5. 消费消息更新缓存
    else 事务失败
        App->>MQ: 4. 回滚消息
        Note over MQ: 消息被丢弃
    end
```

### 9.2 最终一致性策略

```java
/**
 * 缓存一致性保障器
 */
@Component
public class CacheConsistencyGuard {
    
    /**
     * 延迟双删策略
     */
    @Async
    public void delayedDoubleDelete(String cacheKey) {
        // 立即删除
        redisTemplate.delete(cacheKey);
        
        // 延迟删除（处理主从延迟）
        try {
            Thread.sleep(1000);
            redisTemplate.delete(cacheKey);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * 缓存预热
     */
    @EventListener(ApplicationReadyEvent.class)
    public void warmUpCache() {
        // 预热热点数据
        warmUpCustomers();
        warmUpProducts();
        warmUpOrders();
    }
}
```

## 10. 监控与运维

### 10.1 监控指标设计

```java
/**
 * 缓存同步监控
 */
@Component
public class CacheSyncMetrics {
    
    private final MeterRegistry meterRegistry;
    
    // 同步成功计数
    private final Counter syncSuccessCounter;
    
    // 同步失败计数
    private final Counter syncFailureCounter;
    
    // 同步耗时
    private final Timer syncTimer;
    
    // 缓存命中率
    private final Gauge cacheHitRate;
    
    public void recordSyncSuccess(String tableName) {
        syncSuccessCounter.increment(
            Tags.of("table", tableName, "status", "success")
        );
    }
    
    public void recordSyncFailure(String tableName, String errorType) {
        syncFailureCounter.increment(
            Tags.of("table", tableName, "status", "failure", "error", errorType)
        );
    }
}
```

### 10.2 健康检查端点

```java
/**
 * 缓存同步健康检查
 */
@Component
public class CacheSyncHealthIndicator implements HealthIndicator {
    
    @Override
    public Health health() {
        try {
            // 检查Redis连接
            redisTemplate.opsForValue().get("health:check");
            
            // 检查Canal连接状态
            boolean canalConnected = checkCanalConnection();
            
            // 检查MQ连接状态  
            boolean mqConnected = checkMQConnection();
            
            if (canalConnected && mqConnected) {
                return Health.up()
                    .withDetail("canal", "connected")
                    .withDetail("mq", "connected")
                    .withDetail("redis", "connected")
                    .build();
            } else {
                return Health.down()
                    .withDetail("canal", canalConnected ? "connected" : "disconnected")
                    .withDetail("mq", mqConnected ? "connected" : "disconnected")
                    .build();
            }
        } catch (Exception e) {
            return Health.down(e).build();
        }
    }
}
```

## 11. 性能优化策略

### 11.1 批量操作优化

```java
/**
 * 批量缓存操作
 */
@Component
public class BatchCacheOperator {
    
    /**
     * 批量设置缓存
     */
    public void batchSet(Map<String, Object> cacheData) {
        redisTemplate.executePipelined((RedisCallback<Object>) connection -> {
            cacheData.forEach((key, value) -> {
                connection.set(key.getBytes(), serialize(value));
            });
            return null;
        });
    }
    
    /**
     * 批量获取缓存
     */
    public Map<String, Object> batchGet(List<String> keys) {
        List<Object> values = redisTemplate.opsForValue().multiGet(keys);
        Map<String, Object> result = new HashMap<>();
        
        for (int i = 0; i < keys.size(); i++) {
            if (values.get(i) != null) {
                result.put(keys.get(i), values.get(i));
            }
        }
        return result;
    }
}
```

### 11.2 连接池优化

```yaml
spring:
  redis:
    lettuce:
      pool:
        max-active: 200    # 最大连接数
        max-idle: 20       # 最大空闲连接
        min-idle: 5        # 最小空闲连接
        max-wait: 2000ms   # 最大等待时间
      cluster:
        refresh:
          adaptive: true   # 自适应刷新
          period: 60s      # 刷新周期
```

## 12. 部署与配置

### 12.1 Docker Compose部署

```yaml
version: '3.8'
services:
  # Canal Server
  canal-server:
    image: canal/canal-server:v1.1.7
    container_name: canal-server
    ports:
      - "11111:11111"
    environment:
      - canal.destinations=banguo-sync
      - canal.serverMode=rocketMQ
      - canal.mq.servers=rocketmq:9876
    volumes:
      - ./canal/conf:/home/<USER>/canal-server/conf
    depends_on:
      - mysql
      - rocketmq

  # RocketMQ NameServer
  rocketmq-nameserver:
    image: apache/rocketmq:4.6.1
    container_name: rocketmq-nameserver
    ports:
      - "9876:9876"
    command: sh mqnamesrv

  # RocketMQ Broker
  rocketmq-broker:
    image: apache/rocketmq:4.6.1
    container_name: rocketmq-broker
    ports:
      - "10909:10909"
      - "10911:10911"
    environment:
      - NAMESRV_ADDR=rocketmq-nameserver:9876
    command: sh mqbroker -n rocketmq-nameserver:9876 -c /opt/rocketmq-4.6.1/conf/broker.conf
    depends_on:
      - rocketmq-nameserver

  # Redis
  redis:
    image: redis:7-alpine
    container_name: redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes

  # Elasticsearch
  elasticsearch:
    image: elasticsearch:8.12.0
    container_name: elasticsearch
    ports:
      - "9200:9200"
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
```

### 12.2 生产环境配置

```yaml
# application-prod.yml
banguo:
  cache:
    sync:
      enabled: true
      canal:
        topic: canal-banguo-prod
        consumerGroup: banguo-cache-sync-prod
        nameserver: 10.0.1.100:9876,10.0.1.101:9876
      redis:
        cluster-nodes:
          - 10.0.1.200:6379
          - 10.0.1.201:6379
          - 10.0.1.202:6379
        default-expire: 7200
      elasticsearch:
        enabled: true
        cluster-nodes: 10.0.1.300:9200,10.0.1.301:9200

logging:
  level:
    cn.xianlink.cache.sync: DEBUG
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId}] %logger{50} - %msg%n"
```

## 13. 最佳实践建议

### 13.1 缓存设计原则

1. **热点数据优先**：优先缓存访问频繁的数据
2. **合理过期时间**：根据业务特性设置不同的过期时间
3. **避免大Key**：单个缓存对象大小不超过10KB
4. **批量操作**：使用Pipeline和批量操作提升性能
5. **监控告警**：建立完善的监控和告警机制

### 13.2 开发规范

1. **实体注解**：所有需要缓存的实体必须添加`@CacheEntity`注解
2. **字段映射**：使用`@CacheField`明确字段映射关系
3. **键命名**：遵循统一的缓存键命名规范
4. **异常处理**：妥善处理缓存异常，不影响主业务流程
5. **版本兼容**：考虑实体结构变更的向后兼容性

### 13.3 运维建议

1. **容量规划**：根据数据量合理规划Redis集群容量
2. **备份策略**：建立Redis数据备份和恢复策略
3. **性能调优**：定期监控性能指标并进行调优
4. **故障处理**：建立缓存故障时的降级处理机制
5. **数据一致性**：定期对比数据库和缓存数据一致性

## 14. 总结

本方案基于Canal + RocketMQ + Redis + SpringBoot构建了一套完整的缓存同步架构，具有以下特点：

✅ **技术先进**：采用业界成熟的技术栈组合  
✅ **架构清晰**：组件职责明确，层次分明  
✅ **接入简单**：业务服务只需添加注解即可接入  
✅ **性能优异**：支持批量操作和连接池优化  
✅ **监控完善**：提供全面的监控和健康检查  
✅ **扩展性强**：支持多种缓存策略和存储结构

该方案能够很好地满足般果微服务架构的缓存同步需求，为业务发展提供强有力的技术支撑。

## 15. 设计方案核心亮点

### 🎯 核心特性

**1. 优雅的实体映射**

- 通过 `@CacheEntity`、`@CacheKey`、`@CacheField` 注解自动完成MySQL↔Java↔Redis转换
- 支持字段级别的转换器和序列化策略
- 无需硬编码字段映射关系

**2. 业务接入极简**

- 主应用类只需添加 `@EnableCacheSync` 注解
- 实体类添加注解即可自动缓存同步
- 服务层通过 `CacheTemplate` 透明使用缓存

**3. 缓存键设计规范**

- 采用 `业务前缀:{shard_key}:{id}` 结构
- 支持分库分表场景的分片键设计
- 自动生成索引键和列表键

**4. 数据一致性保障**

- RocketMQ事务消息确保最终一致性
- 延迟双删策略处理主从延迟
- 完善的异常处理和重试机制

### 🏗️ 技术架构优势

**分层清晰**：数据源层 → 同步层 → 缓存层 → 应用层 → 框架层  
**组件解耦**：各组件职责单一，便于维护和扩展  
**性能优化**：支持批量操作、连接池优化、Pipeline操作  
**监控完善**：提供健康检查、性能指标、告警机制

### 🚀 完整接入示例

```java
// 1. 启用缓存同步
@SpringBootApplication
@EnableCacheSync
public class BanguoBasicApplication {
    public static void main(String[] args) {
        SpringApplication.run(BanguoBasicApplication.class, args);
    }
}

// 2. 实体类添加注解
@CacheEntity(
    tableName = "bas_customer",
    keyPrefix = "customer", 
    expireTime = 7200,
    syncToES = true,
    esIndex = "customer_index"
)
public class BasCustomer extends TenantEntity {
    @CacheKey(keyType = KeyType.PRIMARY)
    private Long id;
    
    @CacheField(isIndex = true)
    private String code;
    
    @CacheKey(isShardKey = true)
    private Long cityWhId;
    
    @CacheField(redisField = "customer_name")
    private String name;
    // ...
}

// 3. 服务层透明使用
@Service
public class CustomerService {
    @Autowired
    private CacheTemplate cacheTemplate;
    
    // 主键查询
    public BasCustomer getCustomer(Long id) {
        return cacheTemplate.get("customer", id, BasCustomer.class);
    }
    
    // 索引查询
    public BasCustomer getCustomerByCode(String code) {
        return cacheTemplate.getByIndex("customer", "code", code, BasCustomer.class);
    }
    
    // 列表查询
    public List<BasCustomer> getCustomersByCityWh(Long cityWhId) {
        return cacheTemplate.getList("customer", "cityWhId", cityWhId, BasCustomer.class);
    }
}
```

### 🎉 方案价值

这套方案完全符合您的需求：

- ✅ **技术栈先进**：MySQL 8.0 + RocketMQ 4.6.1 + Elasticsearch 8.12.0 + Canal 1.1.7 + Easy-ES 1.1.0 + SpringBoot 3.x +
  Redis 7.x
- ✅ **架构清晰**：分层明确，组件解耦，一目了然
- ✅ **接入简单**：注解驱动，自动配置，开发友好
- ✅ **性能优异**：批量处理，连接池优化，监控完善
- ✅ **生产就绪**：容错处理，监控告警，运维友好

现在可以基于这个设计开始具体的代码实现了！框架的每个组件都有明确的职责和接口定义，便于团队并行开发。 