# 般果搜索引擎技术方案设计

> **版本**: v1.0  
> **技术栈**: MySQL 8.0 + RocketMQ 4.6.1 + Elasticsearch 8.12.0 + Canal 1.1.7 + Easy-ES 1.1.0 + SpringBoot  
> **设计时间**: 2024年7月11日

## 1. 项目概述

### 1.1 项目背景

般果B2B平台需要构建高性能的搜索引擎，提供商品搜索、数据分析、实时监控等核心功能。通过Elasticsearch构建分析服务，实现近实时的搜索和聚合分析能力。

### 1.2 技术目标

- **高性能**: 支持万级并发搜索请求，毫秒级响应
- **高可用**: 99.9%服务可用性，故障自动恢复
- **易扩展**: 支持水平扩展，数据量和流量平滑增长
- **实时性**: 数据变更1秒内同步到搜索引擎

### 1.3 核心功能

- 商品全文搜索与筛选
- 实时数据同步
- 用户行为分析
- 订单管理查询
- 业务指标监控

## 2. 整体架构设计

### 2.1 系统架构图

```mermaid
graph TB
    subgraph "应用层"
        A1[般果前端] --> B1[搜索网关]
        A2[管理后台] --> B1
        A3[移动端APP] --> B1
    end
    
    subgraph "服务层"
        B1 --> C1[搜索服务]
        B1 --> C2[同步服务]
        B1 --> C3[监控服务]
    end
    
    subgraph "数据同步链路"
        D1[MySQL主库] --> D2[Canal Server]
        D2 --> D3[RocketMQ]
        D3 --> C2
        C2 --> E1[Elasticsearch集群]
    end
    
    subgraph "存储层"
        D1
        E1
        E2[Redis缓存]
        E3[监控数据库]
    end
    
    subgraph "基础设施"
        F1[Nacos注册中心]
        F2[Sentinel熔断器]
        F3[SkyWalking链路追踪]
        F4[Prometheus监控]
    end
    
    C1 --> E1
    C1 --> E2
    C3 --> E3
    
    C1 --> F1
    C2 --> F1
    C3 --> F1
    
    C1 --> F2
    
    style E1 fill:#e1f5fe
    style D1 fill:#f3e5f5
    style D3 fill:#fff3e0
```

### 2.2 技术架构分层

```mermaid
graph LR
    subgraph "接入层"
        A[Nginx] --> B[SpringCloud Gateway]
    end
    
    subgraph "微服务层"
        C[搜索服务<br/>banguo-search]
        D[同步服务<br/>banguo-sync] 
        E[分析服务<br/>banguo-analytics]
    end
    
    subgraph "中间件层"
        F[RocketMQ<br/>消息队列]
        G[Canal<br/>数据捕获]
        H[Redis<br/>缓存]
        I[Nacos<br/>配置中心]
    end
    
    subgraph "数据层"
        J[MySQL<br/>业务数据]
        K[Elasticsearch<br/>搜索引擎]
        L[InfluxDB<br/>监控数据]
    end
    
    B --> C
    B --> D
    B --> E
    
    C --> F
    C --> H
    C --> K
    
    D --> F
    D --> G
    D --> K
    
    E --> K
    E --> L
    
    G --> J
    
    C --> I
    D --> I
    E --> I
```

## 3. 核心模块设计

### 3.1 搜索服务模块 (banguo-search)

#### 3.1.1 模块结构

```mermaid
graph TD
    A[SearchController] --> B[SearchService]
    B --> C[ProductSearchService]
    B --> D[OrderSearchService]
    B --> E[UserSearchService]
    
    C --> F[ProductRepository]
    D --> G[OrderRepository]
    E --> H[UserRepository]
    
    F --> I[Easy-ES]
    G --> I
    H --> I
    
    I --> J[Elasticsearch]
    
    B --> K[SearchCacheService]
    K --> L[Redis]
    
    B --> M[SearchMetricsService]
    M --> N[Prometheus]
```

#### 3.1.2 核心接口设计

```java
// 搜索服务主接口
public interface SearchService {
    /**
     * 商品搜索
     */
    SearchResult<ProductDocument> searchProducts(ProductSearchRequest request);
    
    /**
     * 订单搜索
     */
    SearchResult<OrderDocument> searchOrders(OrderSearchRequest request);
    
    /**
     * 搜索建议
     */
    List<String> getSuggestions(String keyword);
    
    /**
     * 热门搜索词
     */
    List<String> getHotKeywords(int limit);
}

// 商品搜索请求
@Data
public class ProductSearchRequest {
    private String keyword;           // 搜索关键词
    private List<String> categories;  // 类目筛选
    private List<String> brands;      // 品牌筛选
    private BigDecimal minPrice;      // 最低价格
    private BigDecimal maxPrice;      // 最高价格
    private Integer status;           // 商品状态
    private String sortField;         // 排序字段
    private String sortOrder;         // 排序方向
    private Integer pageNum;          // 页码
    private Integer pageSize;         // 页大小
}
```

### 3.2 数据同步模块 (banguo-sync)

#### 3.2.1 同步架构流程

```mermaid
sequenceDiagram
    participant MySQL
    participant Canal
    participant RocketMQ
    participant SyncService
    participant Elasticsearch
    
    MySQL->>Canal: Binlog变更事件
    Canal->>Canal: 解析Binlog
    Canal->>RocketMQ: 发送变更消息
    RocketMQ->>SyncService: 消费消息
    SyncService->>SyncService: 数据转换
    SyncService->>Elasticsearch: 同步数据
    SyncService->>SyncService: 记录同步状态
```

#### 3.2.2 同步服务设计

```java
// Canal消息处理器
@Component
@RocketMQMessageListener(
    topic = "${canal.topic}",
    consumerGroup = "${canal.consumer.group}",
    consumeThreadMax = 16,
    consumeTimeout = 5000L
)
public class CanalMessageProcessor implements RocketMQListener<MessageExt> {
    
    @Autowired
    private DataSyncService dataSyncService;
    
    @Override
    public void onMessage(MessageExt message) {
        try {
            Entry entry = Entry.parseFrom(message.getBody());
            dataSyncService.processEntry(entry);
        } catch (Exception e) {
            log.error("处理Canal消息失败", e);
            throw new RuntimeException("消息处理异常", e);
        }
    }
}

// 数据同步服务
@Service
public class DataSyncService {
    
    private final Map<String, SyncHandler> syncHandlers;
    
    public void processEntry(Entry entry) {
        if (entry.getEntryType() != EntryType.ROWDATA) {
            return;
        }
        
        String tableName = entry.getHeader().getTableName();
        SyncHandler handler = syncHandlers.get(tableName);
        
        if (handler != null) {
            handler.handle(entry);
        }
    }
}
```

### 3.3 监控分析模块 (banguo-analytics)

#### 3.3.1 监控指标体系

```mermaid
graph LR
    subgraph "业务指标"
        A1[搜索QPS]
        A2[搜索延迟]
        A3[搜索成功率]
        A4[热门关键词]
    end
    
    subgraph "技术指标"
        B1[ES集群状态]
        B2[索引大小]
        B3[同步延迟]
        B4[消息积压]
    end
    
    subgraph "系统指标"
        C1[CPU使用率]
        C2[内存使用率]
        C3[磁盘IO]
        C4[网络流量]
    end
    
    A1 --> D[Grafana仪表板]
    A2 --> D
    A3 --> D
    A4 --> D
    
    B1 --> D
    B2 --> D
    B3 --> D
    B4 --> D
    
    C1 --> D
    C2 --> D
    C3 --> D
    C4 --> D
```

## 4. 数据模型设计

### 4.1 Elasticsearch索引设计

#### 4.1.1 商品索引 (product_index)

```json
{
  "mappings": {
    "properties": {
      "id": {
        "type": "keyword"
      },
      "name": {
        "type": "text",
        "analyzer": "ik_max_word",
        "search_analyzer": "ik_smart",
        "fields": {
          "keyword": {
            "type": "keyword"
          }
        }
      },
      "description": {
        "type": "text",
        "analyzer": "ik_max_word"
      },
      "categoryId": {
        "type": "keyword"
      },
      "categoryName": {
        "type": "text",
        "analyzer": "ik_smart"
      },
      "brandId": {
        "type": "keyword"
      },
      "brandName": {
        "type": "text",
        "analyzer": "ik_smart"
      },
      "price": {
        "type": "double"
      },
      "originalPrice": {
        "type": "double"
      },
      "stock": {
        "type": "integer"
      },
      "sales": {
        "type": "integer"
      },
      "status": {
        "type": "integer"
      },
      "tags": {
        "type": "keyword"
      },
      "attributes": {
        "type": "nested",
        "properties": {
          "name": {
            "type": "keyword"
          },
          "value": {
            "type": "keyword"
          }
        }
      },
      "images": {
        "type": "keyword"
      },
      "createTime": {
        "type": "date",
        "format": "yyyy-MM-dd HH:mm:ss"
      },
      "updateTime": {
        "type": "date",
        "format": "yyyy-MM-dd HH:mm:ss"
      },
      "location": {
        "type": "geo_point"
      }
    }
  },
  "settings": {
    "number_of_shards": 3,
    "number_of_replicas": 1,
    "analysis": {
      "analyzer": {
        "ik_max_word": {
          "type": "ik_max_word"
        },
        "ik_smart": {
          "type": "ik_smart"
        }
      }
    }
  }
}
```

#### 4.1.2 订单索引 (order_index)

```json
{
  "mappings": {
    "properties": {
      "orderId": {
        "type": "keyword"
      },
      "userId": {
        "type": "keyword"
      },
      "userPhone": {
        "type": "keyword"
      },
      "totalAmount": {
        "type": "double"
      },
      "payAmount": {
        "type": "double"
      },
      "status": {
        "type": "integer"
      },
      "payStatus": {
        "type": "integer"
      },
      "deliveryStatus": {
        "type": "integer"
      },
      "orderTime": {
        "type": "date",
        "format": "yyyy-MM-dd HH:mm:ss"
      },
      "payTime": {
        "type": "date",
        "format": "yyyy-MM-dd HH:mm:ss"
      },
      "items": {
        "type": "nested",
        "properties": {
          "productId": {
            "type": "keyword"
          },
          "productName": {
            "type": "text",
            "analyzer": "ik_smart"
          },
          "quantity": {
            "type": "integer"
          },
          "price": {
            "type": "double"
          }
        }
      },
      "deliveryAddress": {
        "type": "text"
      },
      "receiverName": {
        "type": "keyword"
      },
      "receiverPhone": {
        "type": "keyword"
      }
    }
  },
  "settings": {
    "number_of_shards": 3,
    "number_of_replicas": 1
  }
}
```

### 4.2 Easy-ES实体类设计

```java
// 商品文档实体
@Data
@IndexName("product_index")
public class ProductDocument {
    
    @IndexId
    private String id;
    
    @HighLight
    @IndexField(fieldType = FieldType.TEXT, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
    private String name;
    
    @IndexField(fieldType = FieldType.TEXT, analyzer = "ik_max_word")
    private String description;
    
    @IndexField(fieldType = FieldType.KEYWORD)
    private String categoryId;
    
    @IndexField(fieldType = FieldType.TEXT, analyzer = "ik_smart")
    private String categoryName;
    
    @IndexField(fieldType = FieldType.KEYWORD)
    private String brandId;
    
    @IndexField(fieldType = FieldType.TEXT, analyzer = "ik_smart")
    private String brandName;
    
    @IndexField(fieldType = FieldType.DOUBLE)
    private BigDecimal price;
    
    @IndexField(fieldType = FieldType.DOUBLE)
    private BigDecimal originalPrice;
    
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer stock;
    
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer sales;
    
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer status;
    
    @IndexField(fieldType = FieldType.KEYWORD)
    private List<String> tags;
    
    @IndexField(fieldType = FieldType.NESTED)
    private List<ProductAttribute> attributes;
    
    @IndexField(fieldType = FieldType.KEYWORD)
    private List<String> images;
    
    @IndexField(fieldType = FieldType.DATE, format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    @IndexField(fieldType = FieldType.DATE, format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    
    @IndexField(fieldType = FieldType.GEO_POINT)
    private String location;
}

// 商品属性嵌套对象
@Data
public class ProductAttribute {
    private String name;
    private String value;
}

// 订单文档实体
@Data
@IndexName("order_index")
public class OrderDocument {
    
    @IndexId
    private String orderId;
    
    @IndexField(fieldType = FieldType.KEYWORD)
    private String userId;
    
    @IndexField(fieldType = FieldType.KEYWORD)
    private String userPhone;
    
    @IndexField(fieldType = FieldType.DOUBLE)
    private BigDecimal totalAmount;
    
    @IndexField(fieldType = FieldType.DOUBLE)
    private BigDecimal payAmount;
    
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer status;
    
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer payStatus;
    
    @IndexField(fieldType = FieldType.INTEGER)
    private Integer deliveryStatus;
    
    @IndexField(fieldType = FieldType.DATE, format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime orderTime;
    
    @IndexField(fieldType = FieldType.DATE, format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime payTime;
    
    @IndexField(fieldType = FieldType.NESTED)
    private List<OrderItem> items;
    
    @IndexField(fieldType = FieldType.TEXT)
    private String deliveryAddress;
    
    @IndexField(fieldType = FieldType.KEYWORD)
    private String receiverName;
    
    @IndexField(fieldType = FieldType.KEYWORD)
    private String receiverPhone;
}

// 订单项嵌套对象
@Data
public class OrderItem {
    private String productId;
    
    @IndexField(fieldType = FieldType.TEXT, analyzer = "ik_smart")
    private String productName;
    
    private Integer quantity;
    private BigDecimal price;
}
```

## 5. 服务实现方案

### 5.1 搜索服务实现

#### 5.1.1 商品搜索服务

```java
@Service
@Slf4j
public class ProductSearchServiceImpl implements ProductSearchService {
    
    @Autowired
    private ProductMapper productMapper;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Override
    public SearchResult<ProductDocument> searchProducts(ProductSearchRequest request) {
        // 构建查询条件
        EsWrapperQuery<ProductDocument> wrapper = EsWrapperQuery.builder();
        
        // 关键词搜索
        if (StringUtils.hasText(request.getKeyword())) {
            wrapper.multiMatchQuery(request.getKeyword(), "name^2", "description", "brandName", "categoryName");
        }
        
        // 类目筛选
        if (CollectionUtils.isNotEmpty(request.getCategories())) {
            wrapper.in("categoryId", request.getCategories());
        }
        
        // 品牌筛选
        if (CollectionUtils.isNotEmpty(request.getBrands())) {
            wrapper.in("brandId", request.getBrands());
        }
        
        // 价格区间
        if (request.getMinPrice() != null) {
            wrapper.ge("price", request.getMinPrice());
        }
        if (request.getMaxPrice() != null) {
            wrapper.le("price", request.getMaxPrice());
        }
        
        // 商品状态
        if (request.getStatus() != null) {
            wrapper.eq("status", request.getStatus());
        }
        
        // 库存过滤
        wrapper.gt("stock", 0);
        
        // 排序
        if (StringUtils.hasText(request.getSortField())) {
            SortOrder sortOrder = "desc".equalsIgnoreCase(request.getSortOrder()) 
                ? SortOrder.DESC : SortOrder.ASC;
            wrapper.orderBy(sortOrder, request.getSortField());
        } else {
            // 默认按相关性和销量排序
            wrapper.orderByDesc("_score", "sales");
        }
        
        // 分页
        wrapper.from((request.getPageNum() - 1) * request.getPageSize())
               .size(request.getPageSize());
        
        // 执行查询
        EsPageInfo<ProductDocument> pageInfo = productMapper.pageQuery(wrapper, request.getPageNum(), request.getPageSize());
        
        // 构建返回结果
        SearchResult<ProductDocument> result = new SearchResult<>();
        result.setData(pageInfo.getList());
        result.setTotal(pageInfo.getTotal());
        result.setPageNum(request.getPageNum());
        result.setPageSize(request.getPageSize());
        
        // 聚合统计
        Map<String, Object> aggregations = buildAggregations(request);
        result.setAggregations(aggregations);
        
        return result;
    }
    
    private Map<String, Object> buildAggregations(ProductSearchRequest request) {
        Map<String, Object> aggregations = new HashMap<>();
        
        // 品牌聚合
        EsWrapperQuery<ProductDocument> brandWrapper = buildBaseWrapper(request);
        brandWrapper.aggregation("brands", AggregationType.TERMS, "brandId");
        List<EsBucket> brandBuckets = productMapper.aggregation(brandWrapper);
        aggregations.put("brands", brandBuckets);
        
        // 类目聚合
        EsWrapperQuery<ProductDocument> categoryWrapper = buildBaseWrapper(request);
        categoryWrapper.aggregation("categories", AggregationType.TERMS, "categoryId");
        List<EsBucket> categoryBuckets = productMapper.aggregation(categoryWrapper);
        aggregations.put("categories", categoryBuckets);
        
        // 价格区间聚合
        EsWrapperQuery<ProductDocument> priceWrapper = buildBaseWrapper(request);
        priceWrapper.aggregation("price_ranges", AggregationType.RANGE, "price")
                   .addRange(0, 100)
                   .addRange(100, 500)
                   .addRange(500, 1000)
                   .addRange(1000, null);
        List<EsBucket> priceBuckets = productMapper.aggregation(priceWrapper);
        aggregations.put("priceRanges", priceBuckets);
        
        return aggregations;
    }
    
    @Override
    public List<String> getSuggestions(String keyword) {
        String cacheKey = "search:suggestions:" + keyword;
        
        // 从缓存获取
        List<String> cached = (List<String>) redisTemplate.opsForValue().get(cacheKey);
        if (cached != null) {
            return cached;
        }
        
        // ES建议查询
        SuggestionBuilder suggestionBuilder = SuggestBuilders
            .completionSuggestion("name.suggest")
            .prefix(keyword)
            .size(10);
            
        SearchRequest searchRequest = new SearchRequest("product_index");
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.suggest(new SuggestBuilder().addSuggestion("product_suggest", suggestionBuilder));
        searchRequest.source(sourceBuilder);
        
        try {
            SearchResponse response = elasticsearchClient.search(searchRequest, RequestOptions.DEFAULT);
            List<String> suggestions = extractSuggestions(response);
            
            // 缓存结果
            redisTemplate.opsForValue().set(cacheKey, suggestions, Duration.ofMinutes(30));
            
            return suggestions;
        } catch (Exception e) {
            log.error("获取搜索建议失败", e);
            return Collections.emptyList();
        }
    }
}
```

#### 5.1.2 缓存策略

```java
@Component
@Slf4j
public class SearchCacheService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    private static final String CACHE_PREFIX = "search:";
    private static final Duration DEFAULT_TTL = Duration.ofMinutes(30);
    
    /**
     * 缓存搜索结果
     */
    public void cacheSearchResult(String cacheKey, SearchResult<?> result) {
        try {
            redisTemplate.opsForValue().set(
                CACHE_PREFIX + cacheKey, 
                result, 
                DEFAULT_TTL
            );
        } catch (Exception e) {
            log.warn("缓存搜索结果失败: {}", e.getMessage());
        }
    }
    
    /**
     * 获取缓存的搜索结果
     */
    @SuppressWarnings("unchecked")
    public <T> SearchResult<T> getCachedSearchResult(String cacheKey, Class<T> type) {
        try {
            return (SearchResult<T>) redisTemplate.opsForValue().get(CACHE_PREFIX + cacheKey);
        } catch (Exception e) {
            log.warn("获取缓存搜索结果失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 生成缓存键
     */
    public String generateCacheKey(Object request) {
        return DigestUtils.md5DigestAsHex(JsonUtils.toJson(request).getBytes());
    }
    
    /**
     * 清除相关缓存
     */
    public void evictCache(String pattern) {
        try {
            Set<String> keys = redisTemplate.keys(CACHE_PREFIX + pattern + "*");
            if (!keys.isEmpty()) {
                redisTemplate.delete(keys);
            }
        } catch (Exception e) {
            log.warn("清除缓存失败: {}", e.getMessage());
        }
    }
}
```

### 5.2 数据同步服务实现

#### 5.2.1 Canal配置

```yaml
# canal配置文件 conf/canal.properties
canal.port = 11111
canal.metrics.pull.port = 11112

# RocketMQ配置
canal.mq.servers = 127.0.0.1:9876
canal.mq.retries = 3
canal.mq.batchSize = 1000
canal.mq.maxRequestSize = 1048576
canal.mq.lingerMs = 100
canal.mq.bufferMemory = 134217728

# 动态Topic配置
canal.mq.dynamicTopic = banguo_db\\.product.*:product_topic,banguo_db\\.order.*:order_topic,banguo_db\\.user.*:user_topic,.*\\..*:default_topic

# 分区Hash配置
canal.mq.partitionHash = banguo_db\\.product:id,banguo_db\\.order:order_id,banguo_db\\.user:user_id,.*\\..*:id
```

```yaml
# instance配置文件 conf/banguo/instance.properties
canal.instance.master.address = 127.0.0.1:3306
canal.instance.dbUsername = canal
canal.instance.dbPassword = canal123
canal.instance.connectionCharset = UTF-8MB4

# binlog过滤配置
canal.instance.filter.regex = banguo_db\\.product,banguo_db\\.order,banguo_db\\.user
canal.instance.filter.black.regex = banguo_db\\.test.*

# binlog格式和事务支持
canal.instance.rds.accesskey = 
canal.instance.rds.secretkey = 
canal.instance.rds.instanceId = 

# MySQL8.0特殊配置
canal.instance.master.gtid = false
canal.instance.master.heartbeatPeriod = 15000
canal.instance.detect.heartbeat.enable = true
```

#### 5.2.2 同步处理器实现

```java
// 商品同步处理器
@Component
@Slf4j
public class ProductSyncHandler implements SyncHandler {
    
    @Autowired
    private ProductMapper productMapper;
    
    @Autowired
    private SearchCacheService cacheService;
    
    @Override
    public void handle(Entry entry) {
        try {
            RowChange rowChange = RowChange.parseFrom(entry.getStoreValue());
            EventType eventType = rowChange.getEventType();
            
            for (RowData rowData : rowChange.getRowDatasList()) {
                switch (eventType) {
                    case INSERT:
                        handleInsert(rowData.getAfterColumnsList());
                        break;
                    case UPDATE:
                        handleUpdate(rowData.getBeforeColumnsList(), rowData.getAfterColumnsList());
                        break;
                    case DELETE:
                        handleDelete(rowData.getBeforeColumnsList());
                        break;
                    default:
                        break;
                }
            }
        } catch (Exception e) {
            log.error("处理商品同步消息失败", e);
            throw new RuntimeException("商品同步失败", e);
        }
    }
    
    private void handleInsert(List<Column> columns) {
        ProductDocument product = convertToDocument(columns);
        productMapper.insert(product);
        
        // 清除相关缓存
        cacheService.evictCache("product");
        
        log.info("同步新增商品: {}", product.getId());
    }
    
    private void handleUpdate(List<Column> beforeColumns, List<Column> afterColumns) {
        ProductDocument product = convertToDocument(afterColumns);
        productMapper.updateById(product);
        
        // 清除相关缓存
        cacheService.evictCache("product");
        
        log.info("同步更新商品: {}", product.getId());
    }
    
    private void handleDelete(List<Column> columns) {
        String productId = getColumnValue(columns, "id");
        productMapper.deleteById(productId);
        
        // 清除相关缓存
        cacheService.evictCache("product");
        
        log.info("同步删除商品: {}", productId);
    }
    
    private ProductDocument convertToDocument(List<Column> columns) {
        ProductDocument product = new ProductDocument();
        
        product.setId(getColumnValue(columns, "id"));
        product.setName(getColumnValue(columns, "name"));
        product.setDescription(getColumnValue(columns, "description"));
        product.setCategoryId(getColumnValue(columns, "category_id"));
        product.setCategoryName(getColumnValue(columns, "category_name"));
        product.setBrandId(getColumnValue(columns, "brand_id"));
        product.setBrandName(getColumnValue(columns, "brand_name"));
        
        String priceStr = getColumnValue(columns, "price");
        if (StringUtils.hasText(priceStr)) {
            product.setPrice(new BigDecimal(priceStr));
        }
        
        String originalPriceStr = getColumnValue(columns, "original_price");
        if (StringUtils.hasText(originalPriceStr)) {
            product.setOriginalPrice(new BigDecimal(originalPriceStr));
        }
        
        String stockStr = getColumnValue(columns, "stock");
        if (StringUtils.hasText(stockStr)) {
            product.setStock(Integer.parseInt(stockStr));
        }
        
        String salesStr = getColumnValue(columns, "sales");
        if (StringUtils.hasText(salesStr)) {
            product.setSales(Integer.parseInt(salesStr));
        }
        
        String statusStr = getColumnValue(columns, "status");
        if (StringUtils.hasText(statusStr)) {
            product.setStatus(Integer.parseInt(statusStr));
        }
        
        String tagsStr = getColumnValue(columns, "tags");
        if (StringUtils.hasText(tagsStr)) {
            product.setTags(Arrays.asList(tagsStr.split(",")));
        }
        
        String imagesStr = getColumnValue(columns, "images");
        if (StringUtils.hasText(imagesStr)) {
            product.setImages(Arrays.asList(imagesStr.split(",")));
        }
        
        String createTimeStr = getColumnValue(columns, "create_time");
        if (StringUtils.hasText(createTimeStr)) {
            product.setCreateTime(LocalDateTime.parse(createTimeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        
        String updateTimeStr = getColumnValue(columns, "update_time");
        if (StringUtils.hasText(updateTimeStr)) {
            product.setUpdateTime(LocalDateTime.parse(updateTimeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        
        return product;
    }
    
    private String getColumnValue(List<Column> columns, String columnName) {
        return columns.stream()
            .filter(column -> columnName.equals(column.getName()))
            .findFirst()
            .map(column -> column.getIsNull() ? null : column.getValue())
            .orElse(null);
    }
}

// 订单同步处理器
@Component
@Slf4j
public class OrderSyncHandler implements SyncHandler {
    
    @Autowired
    private OrderMapper orderMapper;
    
    @Override
    public void handle(Entry entry) {
        // 类似的实现逻辑
        // ...
    }
}
```

#### 5.2.3 同步监控与告警

```java
@Component
@Slf4j
public class SyncMonitorService {
    
    @Autowired
    private MeterRegistry meterRegistry;
    
    private final Counter syncSuccessCounter;
    private final Counter syncFailureCounter;
    private final Timer syncLatencyTimer;
    
    public SyncMonitorService(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.syncSuccessCounter = Counter.builder("sync.success")
            .description("同步成功次数")
            .register(meterRegistry);
        this.syncFailureCounter = Counter.builder("sync.failure")
            .description("同步失败次数")
            .register(meterRegistry);
        this.syncLatencyTimer = Timer.builder("sync.latency")
            .description("同步延迟时间")
            .register(meterRegistry);
    }
    
    public void recordSyncSuccess(String table) {
        syncSuccessCounter.increment(Tags.of("table", table));
    }
    
    public void recordSyncFailure(String table, Exception e) {
        syncFailureCounter.increment(Tags.of("table", table, "error", e.getClass().getSimpleName()));
    }
    
    public Timer.Sample startSyncTimer() {
        return Timer.start(meterRegistry);
    }
    
    @Scheduled(fixedRate = 60000) // 每分钟检查一次
    public void checkSyncHealth() {
        // 检查同步延迟
        long currentTime = System.currentTimeMillis();
        // 从ES查询最新的数据更新时间
        // 如果延迟超过阈值，发送告警
        
        // 检查消息队列积压
        // 从RocketMQ获取积压消息数量
        // 如果积压过多，发送告警
    }
}
```

## 6. 部署方案

### 6.1 环境准备

#### 6.1.1 服务器规划

```mermaid
graph TB
    subgraph "生产环境"
        subgraph "应用服务器"
            A1[App-01<br/>搜索服务]
            A2[App-02<br/>搜索服务]
            A3[App-03<br/>同步服务]
        end
        
        subgraph "Elasticsearch集群"
            E1[ES-Master-01]
            E2[ES-Master-02] 
            E3[ES-Master-03]
            E4[ES-Data-01]
            E5[ES-Data-02]
            E6[ES-Data-03]
        end
        
        subgraph "中间件集群"
            M1[RocketMQ-01<br/>NameServer+Broker]
            M2[RocketMQ-02<br/>NameServer+Broker]
            R1[Redis-Master]
            R2[Redis-Slave]
        end
        
        subgraph "数据库"
            D1[MySQL-Master]
            D2[MySQL-Slave]
        end
        
        subgraph "监控服务"
            Mon1[Prometheus]
            Mon2[Grafana]
            Mon3[AlertManager]
        end
    end
    
    A1 --> E1
    A2 --> E2
    A3 --> E3
    
    A1 --> R1
    A2 --> R1
    A3 --> R1
    
    A3 --> M1
    A3 --> M2
    
    D1 --> A3
```

#### 6.1.2 服务器配置要求

| 服务类型      | CPU | 内存   | 磁盘        | 网络     | 数量 |
|-----------|-----|------|-----------|--------|----|
| 搜索服务      | 8核  | 16GB | 100GB SSD | 1Gbps  | 2台 |
| 同步服务      | 4核  | 8GB  | 50GB SSD  | 1Gbps  | 1台 |
| ES Master | 4核  | 8GB  | 100GB SSD | 1Gbps  | 3台 |
| ES Data   | 16核 | 32GB | 1TB SSD   | 10Gbps | 3台 |
| RocketMQ  | 8核  | 16GB | 200GB SSD | 1Gbps  | 2台 |
| Redis     | 4核  | 16GB | 100GB SSD | 1Gbps  | 2台 |

### 6.2 Docker部署

#### 6.2.1 Elasticsearch集群部署

```yaml
# docker-compose-es.yml
version: '3.8'

services:
  es-master-01:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.12.0
    container_name: es-master-01
    environment:
      - node.name=es-master-01
      - cluster.name=banguo-es-cluster
      - node.roles=master
      - discovery.seed_hosts=es-master-02,es-master-03
      - cluster.initial_master_nodes=es-master-01,es-master-02,es-master-03
      - xpack.security.enabled=false
      - xpack.security.transport.ssl.enabled=false
      - "ES_JAVA_OPTS=-Xms4g -Xmx4g"
    volumes:
      - es-master-01-data:/usr/share/elasticsearch/data
      - ./config/elasticsearch.yml:/usr/share/elasticsearch/config/elasticsearch.yml
    ports:
      - "9200:9200"
      - "9300:9300"
    networks:
      - es-network
    restart: unless-stopped

  es-master-02:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.12.0
    container_name: es-master-02
    environment:
      - node.name=es-master-02
      - cluster.name=banguo-es-cluster
      - node.roles=master
      - discovery.seed_hosts=es-master-01,es-master-03
      - cluster.initial_master_nodes=es-master-01,es-master-02,es-master-03
      - xpack.security.enabled=false
      - xpack.security.transport.ssl.enabled=false
      - "ES_JAVA_OPTS=-Xms4g -Xmx4g"
    volumes:
      - es-master-02-data:/usr/share/elasticsearch/data
      - ./config/elasticsearch.yml:/usr/share/elasticsearch/config/elasticsearch.yml
    ports:
      - "9201:9200"
      - "9301:9300"
    networks:
      - es-network
    restart: unless-stopped

  es-data-01:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.12.0
    container_name: es-data-01
    environment:
      - node.name=es-data-01
      - cluster.name=banguo-es-cluster
      - node.roles=data,ingest
      - discovery.seed_hosts=es-master-01,es-master-02,es-master-03
      - cluster.initial_master_nodes=es-master-01,es-master-02,es-master-03
      - xpack.security.enabled=false
      - xpack.security.transport.ssl.enabled=false
      - "ES_JAVA_OPTS=-Xms16g -Xmx16g"
    volumes:
      - es-data-01-data:/usr/share/elasticsearch/data
      - ./config/elasticsearch.yml:/usr/share/elasticsearch/config/elasticsearch.yml
    ports:
      - "9202:9200"
    networks:
      - es-network
    restart: unless-stopped

volumes:
  es-master-01-data:
  es-master-02-data:
  es-data-01-data:

networks:
  es-network:
    driver: bridge
```

#### 6.2.2 RocketMQ集群部署

```yaml
# docker-compose-rocketmq.yml
version: '3.8'

services:
  rocketmq-nameserver-01:
    image: apache/rocketmq:4.6.1
    container_name: rocketmq-nameserver-01
    command: sh mqnamesrv
    environment:
      - JAVA_OPT_EXT=-server -Xms1g -Xmx1g
    ports:
      - "9876:9876"
    volumes:
      - rocketmq-nameserver-01-logs:/home/<USER>/logs
    networks:
      - rocketmq-network
    restart: unless-stopped

  rocketmq-broker-01:
    image: apache/rocketmq:4.6.1
    container_name: rocketmq-broker-01
    command: sh mqbroker -n rocketmq-nameserver-01:9876 -c /opt/rocketmq-4.6.1/conf/broker.conf
    environment:
      - JAVA_OPT_EXT=-server -Xms2g -Xmx2g
    ports:
      - "10909:10909"
      - "10911:10911"
    volumes:
      - rocketmq-broker-01-logs:/home/<USER>/logs
      - rocketmq-broker-01-store:/home/<USER>/store
      - ./config/broker.conf:/opt/rocketmq-4.6.1/conf/broker.conf
    depends_on:
      - rocketmq-nameserver-01
    networks:
      - rocketmq-network
    restart: unless-stopped

  rocketmq-console:
    image: styletang/rocketmq-console-ng:latest
    container_name: rocketmq-console
    environment:
      - JAVA_OPTS=-Drocketmq.namesrv.addr=rocketmq-nameserver-01:9876
    ports:
      - "8080:8080"
    depends_on:
      - rocketmq-nameserver-01
    networks:
      - rocketmq-network
    restart: unless-stopped

volumes:
  rocketmq-nameserver-01-logs:
  rocketmq-broker-01-logs:
  rocketmq-broker-01-store:

networks:
  rocketmq-network:
    driver: bridge
```

#### 6.2.3 Canal部署

```yaml
# docker-compose-canal.yml
version: '3.8'

services:
  canal-server:
    image: canal/canal-server:v1.1.7
    container_name: canal-server
    environment:
      - canal.destinations=banguo
      - canal.instance.mysql.slaveId=1234
      - canal.instance.master.address=mysql-master:3306
      - canal.instance.dbUsername=canal
      - canal.instance.dbPassword=canal123
      - canal.instance.connectionCharset=UTF-8MB4
      - canal.instance.filter.regex=banguo_db\\..*
      - canal.mq.servers=rocketmq-nameserver-01:9876
      - canal.serverMode=rocketMQ
    ports:
      - "11111:11111"
      - "11112:11112"
    volumes:
      - ./config/canal:/opt/canal-server/conf
      - canal-logs:/opt/canal-server/logs
    depends_on:
      - mysql-master
      - rocketmq-nameserver-01
    networks:
      - banguo-network
    restart: unless-stopped

volumes:
  canal-logs:

networks:
  banguo-network:
    external: true
```

### 6.3 应用服务部署

#### 6.3.1 Dockerfile

```dockerfile
# 搜索服务Dockerfile
FROM openjdk:8-jre-alpine

LABEL maintainer="banguo-team"

RUN apk add --no-cache tzdata && \
    ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone

WORKDIR /app

COPY target/banguo-search-*.jar app.jar

EXPOSE 8080

ENV JAVA_OPTS="-server -Xms2g -Xmx2g -XX:+UseG1GC -XX:G1HeapRegionSize=16m -XX:+UseGCOverheadLimit -XX:+ExplicitGCInvokesConcurrent -XX:+HeapDumpOnOutOfMemoryError -XX:+UseStringDeduplication"

ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]
```

#### 6.3.2 Kubernetes部署

```yaml
# k8s-deployment.yml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: banguo-search
  labels:
    app: banguo-search
spec:
  replicas: 2
  selector:
    matchLabels:
      app: banguo-search
  template:
    metadata:
      labels:
        app: banguo-search
    spec:
      containers:
      - name: banguo-search
        image: banguo/banguo-search:latest
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "prod"
        - name: ELASTICSEARCH_HOSTS
          value: "es-master-01:9200,es-master-02:9201,es-master-03:9202"
        - name: ROCKETMQ_NAMESERVER
          value: "rocketmq-nameserver-01:9876"
        - name: REDIS_HOST
          value: "redis-master"
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: banguo-search-service
spec:
  selector:
    app: banguo-search
  ports:
    - protocol: TCP
      port: 80
      targetPort: 8080
  type: ClusterIP
```

## 7. 监控与运维

### 7.1 监控体系设计

#### 7.1.1 监控架构

```mermaid
graph TB
    subgraph "数据采集层"
        A1[应用指标<br/>Micrometer]
        A2[JVM指标<br/>JMX]
        A3[系统指标<br/>Node Exporter]
        A4[ES指标<br/>ES Exporter]
        A5[中间件指标<br/>RocketMQ Exporter]
    end
    
    subgraph "数据存储层"
        B1[Prometheus<br/>时序数据库]
        B2[Elasticsearch<br/>日志存储]
    end
    
    subgraph "可视化层"
        C1[Grafana<br/>监控大屏]
        C2[Kibana<br/>日志分析]
    end
    
    subgraph "告警层"
        D1[AlertManager<br/>告警管理]
        D2[钉钉/企微<br/>消息通知]
    end
    
    A1 --> B1
    A2 --> B1
    A3 --> B1
    A4 --> B1
    A5 --> B1
    
    B1 --> C1
    B2 --> C2
    
    B1 --> D1
    D1 --> D2
```

#### 7.1.2 核心监控指标

```yaml
# prometheus告警规则
groups:
- name: banguo-search-alerts
  rules:
  # 搜索服务可用性
  - alert: SearchServiceDown
    expr: up{job="banguo-search"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "搜索服务不可用"
      description: "搜索服务 {{ $labels.instance }} 已下线超过1分钟"

  # 搜索响应时间
  - alert: SearchHighLatency
    expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job="banguo-search"}[5m])) > 1
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "搜索响应时间过高"
      description: "搜索服务95分位响应时间超过1秒，当前值: {{ $value }}s"

  # 搜索错误率
  - alert: SearchHighErrorRate
    expr: rate(http_requests_total{job="banguo-search",status=~"5.."}[5m]) / rate(http_requests_total{job="banguo-search"}[5m]) > 0.05
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "搜索错误率过高"
      description: "搜索服务错误率超过5%，当前值: {{ $value | humanizePercentage }}"

  # ES集群状态
  - alert: ElasticsearchClusterRed
    expr: elasticsearch_cluster_health_status{color="red"} == 1
    for: 0m
    labels:
      severity: critical
    annotations:
      summary: "Elasticsearch集群状态异常"
      description: "Elasticsearch集群状态为红色，存在严重问题"

  # ES磁盘使用率
  - alert: ElasticsearchHighDiskUsage
    expr: elasticsearch_filesystem_data_used_percent > 85
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "Elasticsearch磁盘使用率过高"
      description: "节点 {{ $labels.node }} 磁盘使用率超过85%，当前值: {{ $value }}%"

  # 同步延迟
  - alert: SyncHighLatency
    expr: sync_latency_seconds > 30
    for: 1m
    labels:
      severity: warning
    annotations:
      summary: "数据同步延迟过高"
      description: "数据同步延迟超过30秒，当前值: {{ $value }}s"

  # MQ消息积压
  - alert: RocketMQMessageBacklog
    expr: rocketmq_brokerruntime_consumer_tps{topic="product_topic"} < 10 and rocketmq_brokerruntime_put_tps{topic="product_topic"} > 100
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "RocketMQ消息积压"
      description: "主题 {{ $labels.topic }} 消息积压严重，消费速度: {{ $labels.consumeTps }}，生产速度: {{ $labels.putTps }}"
```

#### 7.1.3 Grafana监控大屏

```json
{
  "dashboard": {
    "id": null,
    "title": "般果搜索引擎监控大屏",
    "panels": [
      {
        "title": "搜索QPS",
        "type": "stat",
        "targets": [
          {
            "expr": "rate(http_requests_total{job=\"banguo-search\",uri=~\"/api/search.*\"}[1m])",
            "legendFormat": "QPS"
          }
        ]
      },
      {
        "title": "搜索响应时间",
        "type": "timeseries",
        "targets": [
          {
            "expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket{job=\"banguo-search\"}[5m]))",
            "legendFormat": "P50"
          },
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job=\"banguo-search\"}[5m]))",
            "legendFormat": "P95"
          },
          {
            "expr": "histogram_quantile(0.99, rate(http_request_duration_seconds_bucket{job=\"banguo-search\"}[5m]))",
            "legendFormat": "P99"
          }
        ]
      },
      {
        "title": "ES集群状态",
        "type": "stat",
        "targets": [
          {
            "expr": "elasticsearch_cluster_health_status",
            "legendFormat": "集群状态"
          }
        ]
      },
      {
        "title": "同步成功率",
        "type": "stat",
        "targets": [
          {
            "expr": "rate(sync_success_total[5m]) / (rate(sync_success_total[5m]) + rate(sync_failure_total[5m])) * 100",
            "legendFormat": "成功率"
          }
        ]
      }
    ]
  }
}
```

### 7.2 日志管理

#### 7.2.1 日志配置

```xml
<!-- logback-spring.xml -->
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <springProfile name="prod">
        <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
            <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
                <providers>
                    <timestamp/>
                    <logLevel/>
                    <loggerName/>
                    <message/>
                    <mdc/>
                    <stackTrace/>
                </providers>
            </encoder>
        </appender>
        
        <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>logs/banguo-search.log</file>
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <fileNamePattern>logs/banguo-search-%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
                <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                    <maxFileSize>100MB</maxFileSize>
                </timeBasedFileNamingAndTriggeringPolicy>
                <maxHistory>30</maxHistory>
            </rollingPolicy>
            <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
                <providers>
                    <timestamp/>
                    <logLevel/>
                    <loggerName/>
                    <message/>
                    <mdc/>
                    <stackTrace/>
                </providers>
            </encoder>
        </appender>
        
        <logger name="cn.xianlink" level="INFO"/>
        <logger name="org.springframework" level="WARN"/>
        <logger name="com.alibaba.druid" level="WARN"/>
        
        <root level="INFO">
            <appender-ref ref="STDOUT"/>
            <appender-ref ref="FILE"/>
        </root>
    </springProfile>
</configuration>
```

#### 7.2.2 链路追踪

```java
// 自定义追踪配置
@Configuration
public class TracingConfiguration {
    
    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }
    
    @Bean
    public Sampler alwaysSampler() {
        return Sampler.create(0.1f); // 10%采样率
    }
    
    @NewSpan("search-operation")
    @GetMapping("/api/search/products")
    public SearchResult<ProductDocument> searchProducts(
            @SpanTag("keyword") String keyword,
            ProductSearchRequest request) {
        
        return searchService.searchProducts(request);
    }
}
```

### 7.3 性能优化方案

#### 7.3.1 ES性能调优

```yaml
# elasticsearch.yml优化配置
# 堆内存设置（不超过32GB）
-Xms16g
-Xmx16g

# GC优化
-XX:+UseG1GC
-XX:MaxGCPauseMillis=50

# 索引设置优化
index:
  number_of_shards: 3
  number_of_replicas: 1
  refresh_interval: 5s
  max_result_window: 50000
  
  # 索引模板
  template:
    settings:
      index:
        translog:
          durability: async
          sync_interval: 30s
        merge:
          policy:
            max_merged_segment: 2gb
            segments_per_tier: 24

# 查询缓存
indices:
  requests:
    cache:
      size: 5%
  queries:
    cache:
      size: 15%

# 线程池优化
thread_pool:
  search:
    size: 16
    queue_size: 1000
  write:
    size: 8
    queue_size: 200
```

#### 7.3.2 应用性能优化

```java
// 连接池优化
@Configuration
public class EsClientConfig {
    
    @Bean
    public RestHighLevelClient elasticsearchClient() {
        HttpHost[] hosts = {
            new HttpHost("es-node-1", 9200, "http"),
            new HttpHost("es-node-2", 9200, "http"),
            new HttpHost("es-node-3", 9200, "http")
        };
        
        RestClientBuilder builder = RestClient.builder(hosts);
        
        // 连接池配置
        builder.setHttpClientConfigCallback(httpClientBuilder -> {
            httpClientBuilder.setMaxConnTotal(200);
            httpClientBuilder.setMaxConnPerRoute(100);
            httpClientBuilder.setConnectionTimeToLive(5, TimeUnit.MINUTES);
            return httpClientBuilder;
        });
        
        // 请求配置
        builder.setRequestConfigCallback(requestConfigBuilder -> {
            requestConfigBuilder.setConnectTimeout(5000);
            requestConfigBuilder.setSocketTimeout(60000);
            return requestConfigBuilder;
        });
        
        return new RestHighLevelClient(builder);
    }
}

// 查询优化
@Component
public class SearchOptimizer {
    
    /**
     * 构建优化的查询条件
     */
    public BoolQueryBuilder buildOptimizedQuery(ProductSearchRequest request) {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        
        // 使用filter而不是must来避免评分计算
        if (CollectionUtils.isNotEmpty(request.getCategories())) {
            boolQuery.filter(QueryBuilders.termsQuery("categoryId", request.getCategories()));
        }
        
        if (CollectionUtils.isNotEmpty(request.getBrands())) {
            boolQuery.filter(QueryBuilders.termsQuery("brandId", request.getBrands()));
        }
        
        if (request.getMinPrice() != null || request.getMaxPrice() != null) {
            RangeQueryBuilder priceRange = QueryBuilders.rangeQuery("price");
            if (request.getMinPrice() != null) {
                priceRange.gte(request.getMinPrice());
            }
            if (request.getMaxPrice() != null) {
                priceRange.lte(request.getMaxPrice());
            }
            boolQuery.filter(priceRange);
        }
        
        // 关键词搜索放在must中，参与评分
        if (StringUtils.hasText(request.getKeyword())) {
            MultiMatchQueryBuilder multiMatch = QueryBuilders.multiMatchQuery(request.getKeyword())
                .field("name", 3.0f)  // 商品名权重最高
                .field("description", 1.0f)
                .field("brandName", 2.0f)
                .field("categoryName", 1.5f)
                .type(MultiMatchQueryBuilder.Type.BEST_FIELDS)
                .fuzziness(Fuzziness.AUTO);
                
            boolQuery.must(multiMatch);
        }
        
        return boolQuery;
    }
}
```

## 8. 测试方案

### 8.1 功能测试

#### 8.1.1 单元测试

```java
@ExtendWith(SpringExtension.class)
@SpringBootTest
class ProductSearchServiceTest {
    
    @Autowired
    private ProductSearchService searchService;
    
    @MockBean
    private ProductMapper productMapper;
    
    @Test
    @DisplayName("测试商品关键词搜索")
    void testSearchProductsByKeyword() {
        // Given
        ProductSearchRequest request = new ProductSearchRequest();
        request.setKeyword("苹果");
        request.setPageNum(1);
        request.setPageSize(10);
        
        EsPageInfo<ProductDocument> mockPageInfo = new EsPageInfo<>();
        mockPageInfo.setTotal(100L);
        mockPageInfo.setList(createMockProducts());
        
        when(productMapper.pageQuery(any(), eq(1), eq(10))).thenReturn(mockPageInfo);
        
        // When
        SearchResult<ProductDocument> result = searchService.searchProducts(request);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getTotal()).isEqualTo(100L);
        assertThat(result.getData()).hasSize(10);
        
        verify(productMapper).pageQuery(any(), eq(1), eq(10));
    }
    
    @Test
    @DisplayName("测试商品多维度筛选")
    void testSearchProductsWithFilters() {
        // Given
        ProductSearchRequest request = new ProductSearchRequest();
        request.setCategories(Arrays.asList("cat_001", "cat_002"));
        request.setBrands(Arrays.asList("brand_001"));
        request.setMinPrice(new BigDecimal("100"));
        request.setMaxPrice(new BigDecimal("1000"));
        
        // When & Then
        SearchResult<ProductDocument> result = searchService.searchProducts(request);
        
        assertThat(result).isNotNull();
        // 验证筛选逻辑
    }
    
    private List<ProductDocument> createMockProducts() {
        List<ProductDocument> products = new ArrayList<>();
        for (int i = 1; i <= 10; i++) {
            ProductDocument product = new ProductDocument();
            product.setId("product_" + i);
            product.setName("苹果商品" + i);
            product.setPrice(new BigDecimal("99.99"));
            products.add(product);
        }
        return products;
    }
}
```

#### 8.1.2 集成测试

```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Testcontainers
@TestMethodOrder(OrderAnnotation.class)
class SearchIntegrationTest {
    
    @Container
    static ElasticsearchContainer elasticsearch = new ElasticsearchContainer("docker.elastic.co/elasticsearch/elasticsearch:8.12.0")
            .withExposedPorts(9200, 9300)
            .withEnv("discovery.type", "single-node")
            .withEnv("xpack.security.enabled", "false");
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Autowired
    private ProductMapper productMapper;
    
    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        registry.add("spring.elasticsearch.uris", 
            () -> "http://localhost:" + elasticsearch.getMappedPort(9200));
    }
    
    @Test
    @Order(1)
    @DisplayName("初始化测试数据")
    void setupTestData() {
        // 创建测试索引和数据
        List<ProductDocument> testProducts = createTestProducts();
        testProducts.forEach(product -> productMapper.insert(product));
        
        // 等待索引刷新
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    @Test
    @Order(2)
    @DisplayName("测试搜索API")
    void testSearchApi() {
        // Given
        String url = "/api/search/products?keyword=苹果&pageNum=1&pageSize=5";
        
        // When
        ResponseEntity<SearchResult> response = restTemplate.getForEntity(url, SearchResult.class);
        
        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().getData()).isNotEmpty();
    }
    
    @Test
    @Order(3)
    @DisplayName("测试搜索建议API")
    void testSuggestApi() {
        // Given
        String url = "/api/search/suggest?keyword=苹";
        
        // When
        ResponseEntity<List> response = restTemplate.getForEntity(url, List.class);
        
        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
    }
    
    private List<ProductDocument> createTestProducts() {
        // 创建测试商品数据
        return Arrays.asList(
            createProduct("1", "苹果手机", "电子产品", "苹果", new BigDecimal("5999")),
            createProduct("2", "苹果电脑", "电子产品", "苹果", new BigDecimal("9999")),
            createProduct("3", "新鲜苹果", "水果", "绿色农场", new BigDecimal("19.9"))
        );
    }
    
    private ProductDocument createProduct(String id, String name, String category, String brand, BigDecimal price) {
        ProductDocument product = new ProductDocument();
        product.setId(id);
        product.setName(name);
        product.setCategoryName(category);
        product.setBrandName(brand);
        product.setPrice(price);
        product.setStock(100);
        product.setStatus(1);
        product.setCreateTime(LocalDateTime.now());
        return product;
    }
}
```

### 8.2 性能测试

#### 8.2.1 JMeter测试脚本

```xml
<?xml version="1.0" encoding="UTF-8"?>
<jmeterTestPlan version="1.2">
  <hashTree>
    <TestPlan guiclass="TestPlanGui" testclass="TestPlan" testname="般果搜索引擎性能测试">
      <elementProp name="TestPlan.arguments" elementType="Arguments" guiclass="ArgumentsPanel">
        <collectionProp name="Arguments.arguments">
          <elementProp name="host" elementType="Argument">
            <stringProp name="Argument.name">host</stringProp>
            <stringProp name="Argument.value">localhost</stringProp>
          </elementProp>
          <elementProp name="port" elementType="Argument">
            <stringProp name="Argument.name">port</stringProp>
            <stringProp name="Argument.value">8080</stringProp>
          </elementProp>
        </collectionProp>
      </elementProp>
    </TestPlan>
    
    <hashTree>
      <ThreadGroup guiclass="ThreadGroupGui" testclass="ThreadGroup" testname="搜索压力测试">
        <stringProp name="ThreadGroup.on_sample_error">continue</stringProp>
        <elementProp name="ThreadGroup.main_controller" elementType="LoopController">
          <boolProp name="LoopController.continue_forever">false</boolProp>
          <stringProp name="LoopController.loops">100</stringProp>
        </elementProp>
        <stringProp name="ThreadGroup.num_threads">50</stringProp>
        <stringProp name="ThreadGroup.ramp_time">30</stringProp>
      </ThreadGroup>
      
      <hashTree>
        <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="商品搜索">
          <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
            <collectionProp name="Arguments.arguments">
              <elementProp name="" elementType="HTTPArgument">
                <boolProp name="HTTPArgument.always_encode">false</boolProp>
                <stringProp name="Argument.value">keyword=苹果&pageNum=1&pageSize=20</stringProp>
                <stringProp name="Argument.metadata">=</stringProp>
              </elementProp>
            </collectionProp>
          </elementProp>
          <stringProp name="HTTPSampler.domain">${host}</stringProp>
          <stringProp name="HTTPSampler.port">${port}</stringProp>
          <stringProp name="HTTPSampler.path">/api/search/products</stringProp>
          <stringProp name="HTTPSampler.method">GET</stringProp>
        </HTTPSamplerProxy>
        
        <hashTree>
          <ResponseAssertion guiclass="AssertionGui" testclass="ResponseAssertion" testname="响应断言">
            <collectionProp name="Asserion.test_strings">
              <stringProp>200</stringProp>
            </collectionProp>
            <stringProp name="Assertion.custom_message">响应状态码应该是200</stringProp>
            <stringProp name="Assertion.test_field">Assertion.response_code</stringProp>
          </ResponseAssertion>
          
          <DurationAssertion guiclass="DurationAssertionGui" testclass="DurationAssertion" testname="响应时间断言">
            <stringProp name="DurationAssertion.duration">1000</stringProp>
          </DurationAssertion>
        </hashTree>
      </hashTree>
    </hashTree>
  </hashTree>
</jmeterTestPlan>
```

#### 8.2.2 性能基准

| 测试场景 | 并发用户 | 持续时间 | 目标QPS | 目标响应时间       | 成功率    |
|------|------|------|-------|--------------|--------|
| 商品搜索 | 100  | 10分钟 | 1000+ | <500ms (P95) | >99.9% |
| 复杂筛选 | 50   | 5分钟  | 500+  | <800ms (P95) | >99.5% |
| 聚合查询 | 20   | 5分钟  | 100+  | <2s (P95)    | >99%   |
| 搜索建议 | 200  | 5分钟  | 2000+ | <200ms (P95) | >99.9% |

## 9. 实施计划

### 9.1 项目里程碑

```mermaid
gantt
    title 般果搜索引擎实施计划
    dateFormat  YYYY-MM-DD
    section 基础设施
    环境准备         :done,    infra1, 2024-07-11, 3d
    中间件部署       :done,    infra2, after infra1, 5d
    监控系统搭建     :active,  infra3, after infra2, 3d
    
    section 核心开发
    数据模型设计     :done,    dev1, 2024-07-11, 2d
    搜索服务开发     :active,  dev2, after dev1, 7d
    同步服务开发     :dev3, after dev2, 5d
    监控服务开发     :dev4, after dev3, 3d
    
    section 测试验证
    单元测试         :test1, after dev2, 3d
    集成测试         :test2, after dev4, 3d
    性能测试         :test3, after test2, 5d
    
    section 上线部署
    预发布环境       :deploy1, after test3, 2d
    生产环境部署     :deploy2, after deploy1, 3d
    线上验证         :deploy3, after deploy2, 2d
```

### 9.2 详细实施步骤

#### 第一阶段：基础设施准备 (3天)

**Day 1: 环境规划与准备**

- 服务器资源申请和配置
- 网络环境配置
- 基础软件安装（Docker、JDK等）

**Day 2-3: 中间件部署**

- Elasticsearch集群部署和配置
- RocketMQ集群部署和配置
- Redis主从部署
- Canal服务部署

#### 第二阶段：核心功能开发 (15天)

**Day 4-5: 数据模型设计**

- Elasticsearch索引结构设计
- Easy-ES实体类开发
- 数据库表结构优化

**Day 6-12: 搜索服务开发**

- 搜索API接口开发
- 查询构建器实现
- 缓存机制实现
- 聚合统计功能

**Day 13-17: 同步服务开发**

- Canal消息处理器
- 数据转换逻辑
- 异常处理机制
- 同步状态监控

**Day 18-20: 监控服务开发**

- 监控指标收集
- 告警规则配置
- Grafana大屏开发

#### 第三阶段：测试验证 (11天)

**Day 21-23: 单元测试**

- 核心业务逻辑测试
- 边界条件测试
- 异常场景测试

**Day 24-26: 集成测试**

- API接口测试
- 数据同步测试
- 端到端流程测试

**Day 27-31: 性能测试**

- 基准性能测试
- 压力测试
- 稳定性测试
- 性能优化

#### 第四阶段：部署上线 (7天)

**Day 32-33: 预发布环境**

- 预发布环境部署
- 功能验证测试
- 问题修复

**Day 34-36: 生产环境部署**

- 灰度发布策略
- 生产环境部署
- 监控配置

**Day 37-38: 线上验证**

- 功能验证
- 性能验证
- 用户反馈收集

### 9.3 风险控制

#### 9.3.1 技术风险

| 风险项     | 风险等级 | 影响      | 应对措施                                   |
|---------|------|---------|----------------------------------------|
| ES集群稳定性 | 高    | 搜索服务不可用 | 1. 充分的压测验证<br/>2. 主备集群方案<br/>3. 快速回滚机制 |
| 数据同步延迟  | 中    | 搜索结果不准确 | 1. 多重同步验证<br/>2. 同步状态监控<br/>3. 手动补偿机制  |
| 性能不达标   | 中    | 用户体验差   | 1. 缓存优化<br/>2. 查询优化<br/>3. 硬件资源扩容      |

#### 9.3.2 项目风险

| 风险项  | 风险等级 | 影响     | 应对措施                               |
|------|------|--------|------------------------------------|
| 进度延期 | 中    | 上线时间推迟 | 1. 任务优先级排序<br/>2. 人员调配<br/>3. 并行开发 |
| 人员变动 | 低    | 开发进度影响 | 1. 文档完善<br/>2. 知识转移<br/>3. 备用人员    |
| 需求变更 | 中    | 开发返工   | 1. 需求评审<br/>2. 变更控制<br/>3. 增量开发    |

## 10. 总结

本技术方案基于业界最佳实践，结合般果B2B平台的实际需求，设计了一套完整的搜索引擎解决方案。方案具有以下特点：

### 10.1 技术优势

1. **高性能架构**: 采用Elasticsearch + Redis双层缓存，支持万级并发
2. **实时数据同步**: Canal + RocketMQ确保数据1秒内同步
3. **高可用设计**: 集群部署 + 故障自动转移
4. **易于扩展**: 微服务架构 + 水平扩展支持

### 10.2 业务价值

1. **提升用户体验**: 毫秒级搜索响应，智能搜索建议
2. **增强运营能力**: 实时数据分析，精准用户画像
3. **降低运维成本**: 自动化部署，完善监控告警

### 10.3 后续规划

1. **AI能力增强**: 接入大模型，提供智能推荐
2. **多语言支持**: 扩展国际化搜索能力
3. **图像搜索**: 基于商品图片的相似度搜索

通过本方案的实施，般果平台将建立起强大的搜索能力，为业务发展提供有力支撑。

---

*文档版本: v1.0*  
*最后更新: 2024年7月11日*  
*负责团队: 般果技术团队* 