# 提货点查询MCP服务使用说明

## 概述

提货点查询MCP服务是`banguo-ai`模块中的一个重要组件，提供了通过MCP（Model Context
Protocol）协议查询提货点信息的功能。该服务采用分层架构设计，通过Dubbo调用`banguo-basic`
模块的接口，支持根据城市名称查询提货点和根据提货点名称查询详细信息，并返回标准JSON格式数据。

## 架构设计

### 分层架构

- **MCP工具层** (`PickupPointService`): 负责MCP工具注册和参数处理
- **业务逻辑层** (`PickupPointBusinessService`): 处理复杂的业务逻辑
- **数据传输层** (`PickupPointResponseDto`, `PickupPointSummaryDto`, `PickupPointDetailDto`): 标准化数据传输

### 核心特性

- **JSON格式返回**: 符合业界最佳实践，便于AI解析和处理
- **回退逻辑处理**: 智能处理联系人和地址信息的回退逻辑
- **分步式查询**: 先获取提货点列表，再查询详细信息
- **批量优化**: 采用批量查询优化性能

## 功能特性

### 1. 根据城市名称查询提货点摘要

- **功能描述**: 根据城市名称获取该城市的所有提货点基本信息
- **使用场景**: 当用户需要了解某个城市有哪些提货点时使用
- **返回信息**: 提货点名称、编码、所属城市仓等基本信息（不包含详细联系信息）

### 2. 根据提货点名称查询详细信息

- **功能描述**: 根据提货点名称获取提货点的完整详细信息，包含智能回退逻辑
- **使用场景**: 当用户需要了解某个提货点的具体联系信息时使用
- **返回信息**: 详细地址、联系人、联系电话、位置标题、经纬度、备注等
- **智能回退**: 当提货点联系人或地址为空时，自动使用城市仓超管信息

## API接口

### 1. getPickupPointsByCityName

根据城市名称获取提货点摘要列表

**参数：**

- `cityName` (String): 城市名称，例如：北京、上海、广州等

**返回格式（JSON）：**

```json
{
  "status": "success",
  "message": "查询成功",
  "keyword": "北京",
  "queryType": "city_summary",
  "totalCount": 3,
  "summaries": [
    {
      "placeId": 1001,
      "placeName": "朝阳提货点",
      "placeCode": "BJ001",
      "cityWhName": "北京仓",
      "cityWhId": 100
    },
    {
      "placeId": 1002,
      "placeName": "海淀提货点",
      "placeCode": "BJ002",
      "cityWhName": "北京仓",
      "cityWhId": 100
    }
  ]
}
```

### 2. getPickupPointDetailsByName

根据提货点名称获取详细信息

**参数：**

- `pickupPointName` (String): 提货点名称，例如：某某提货点、某某仓库等

**返回格式（JSON）：**

```json
{
  "status": "success",
  "message": "查询成功",
  "keyword": "朝阳提货点",
  "queryType": "place_detail",
  "totalCount": 1,
  "details": [
    {
      "placeId": 1001,
      "placeName": "朝阳提货点",
      "placeCode": "BJ001",
      "cityWhName": "北京仓",
      "cityWhId": 100,
      "address": "北京市朝阳区xxx街道xxx号",
      "contactName": "张三",
      "contactPhone": "13800138000",
      "positionTitle": "朝阳区提货点",
      "longitude": "116.123456",
      "latitude": "39.123456",
      "remark": "营业时间：9:00-18:00",
      "isFirstLevel": true,
      "parentPlaceId": null,
      "parentPlaceName": null
    }
  ]
}
```

## 技术实现

### 依赖服务

- `RemoteCityWhService`: 城市仓服务接口
- `RemoteCityWhPlaceService`: 提货点服务接口

### 核心方法

#### 城市仓查询

```java
// 根据城市名称查询城市仓
List<RemoteOriginCityWhVo> cityWhList = remoteCityWhService.searchByName(cityName);
```

#### 提货点查询

```java
// 根据城市仓ID查询提货点
List<RemoteOriginCityWhPlaceVo> pickupPoints = remoteCityWhPlaceService.listByCityWhId(cityWhId);

// 根据提货点名称查询
List<RemoteOriginCityWhPlaceVo> pickupPoints = remoteCityWhPlaceService.searchByName(pickupPointName);
```

## 配置说明

### MCP工具配置

在`McpToolConfiguration`中注册了提货点查询工具：

```java
@Bean
public ToolCallbackProvider pickupPointTools(PickupPointService pickupPointService) {
    return MethodToolCallbackProvider.builder()
            .toolObjects(pickupPointService)
            .build();
}
```

### 依赖配置

在`pom.xml`中包含了必要的依赖：

```xml
<dependency>
    <groupId>cn.xianlink</groupId>
    <artifactId>banguo-api-basic</artifactId>
</dependency>
```

## 使用示例

### 1. 查询城市提货点

```
输入：北京
输出：
城市【北京】的提货点信息：

城市仓：北京仓
城市仓地址：北京市朝阳区xxx
提货点列表：
- 提货点名称：朝阳提货点
  提货点编码：BJ001
  提货点地址：北京市朝阳区xxx
  联系人：张三
  联系电话：13800138000

- 提货点名称：海淀提货点
  提货点编码：BJ002
  提货点地址：北京市海淀区xxx
  联系人：李四
  联系电话：13900139000

---
```

### 2. 查询提货点详细信息

```
输入：朝阳提货点
输出：
提货点【朝阳提货点】的详细信息：

提货点名称：朝阳提货点
提货点编码：BJ001
所属城市仓：北京仓
详细地址：北京市朝阳区xxx
联系人：张三
联系电话：13800138000
位置标题：朝阳区配送中心
经纬度：116.397128, 39.916527
备注：工作时间：9:00-18:00

---
```

## 错误处理

### 常见错误情况

1. **城市名称为空或无效**
    - 错误信息：请提供有效的城市名称

2. **未找到城市信息**
    - 错误信息：未找到该城市的相关信息，请检查城市名称是否正确

3. **提货点名称为空或无效**
    - 错误信息：请提供有效的提货点名称

4. **未找到提货点信息**
    - 错误信息：未找到该提货点的相关信息，请检查提货点名称是否正确

5. **系统异常**
    - 错误信息：查询信息时发生错误，请稍后重试

## 注意事项

1. **城市名称支持模糊查询**：输入部分城市名称也可以查询到结果
2. **提货点名称支持模糊查询**：输入部分提货点名称也可以查询到结果
3. **联系信息可能为空**：某些提货点可能没有配置联系人或联系电话
4. **地址信息完整性**：系统会显示所有可用的地址信息
5. **异常处理**：所有MCP工具都包含异常处理，确保系统稳定性

## 日志记录

系统会记录以下关键操作的日志：

- 查询请求的开始和结束
- 查询参数和结果数量
- 异常情况的详细信息

日志级别设置为INFO，可以在应用配置中调整：

```yaml
logging:
  level:
    cn.xianlink.ai.mcpserver.service.PickupPointService: DEBUG
``` 