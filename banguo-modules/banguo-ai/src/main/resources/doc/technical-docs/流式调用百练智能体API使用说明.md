# 流式调用百练智能体API使用说明

## 概述

新增的 `callBailianAppStream` 方法实现了真正的百练智能体API流式调用，支持实时流式响应。

## 功能特性

- ✅ **真正的流式调用**：使用DashScope SDK的official streamCall方法
- ✅ **实时响应**：通过SSE实时推送AI回复的增量内容
- ✅ **会话管理**：支持会话ID的传入和回传
- ✅ **增量输出**：使用incrementalOutput(true)启用增量模式
- ✅ **异常处理**：完善的错误处理机制
- ✅ **Flowable支持**：基于RxJava的流式数据处理

## 方法签名

```java
public String callBailianAppStream(String prompt, String sessionId, 
                                  SseEmitter emitter, String[] responseSessionId)
    throws ApiException, NoApiKeyException, InputRequiredException
```

### 参数说明

- `prompt`: 用户输入的提示内容
- `sessionId`: 会话ID，新会话时为null
- `emitter`: SSE发射器，用于流式发送数据到前端
- `responseSessionId`: 响应会话ID容器（数组长度至少为1），用于回传新的会话ID

### 返回值

- `String`: 完整的AI回复内容

## 使用示例

### 1. 在消息处理器中使用

```java
@Component
public class StreamMessageProcessor {
    
    @Autowired
    private IAiChatService aiChatService;
    
    public void processStreamMessage(String prompt, String sessionId, SseEmitter emitter) {
        try {
            // 准备会话ID容器
            String[] responseSessionId = new String[1];
            
            // 发送开始事件
            StreamResponseEventDTO startEvent = StreamResponseEventDTO
                .createStartedEvent(sessionId, "开始处理消息");
            sendEvent(emitter, startEvent);
            
            // 调用流式API
            String fullResponse = aiChatService.callBailianAppStream(
                prompt, sessionId, emitter, responseSessionId);
            
            // 发送完成事件
            StreamResponseEventDTO completeEvent = StreamResponseEventDTO
                .createCompletedEvent(responseSessionId[0], "处理完成");
            sendEvent(emitter, completeEvent);
            
            // 保存消息历史
            saveMessageHistory(prompt, fullResponse, responseSessionId[0]);
            
        } catch (Exception e) {
            // 发送错误事件
            StreamResponseEventDTO errorEvent = StreamResponseEventDTO
                .createErrorEvent(sessionId, e.getMessage(), "STREAM_ERROR");
            sendEvent(emitter, errorEvent);
        }
    }
    
    private void sendEvent(SseEmitter emitter, StreamResponseEventDTO event) {
        try {
            emitter.send(SseEmitter.event()
                .name(event.getType())
                .data(event));
        } catch (IOException e) {
            log.error("发送SSE事件失败: {}", e.getMessage(), e);
        }
    }
}
```

### 2. 在现有的TextMessageProcessor中集成

修改 `TextMessageProcessor.processMessage` 方法：

```java
@Override
public MessageProcessResult processMessage(StreamChatRequestDTO request, SseEmitter emitter) {
    String sessionId = request.getSessionId();
    
    try {
        // ... 现有的验证和准备逻辑
        
        // 使用流式调用替代原来的同步调用
        String[] responseSessionId = new String[1];
        String aiResponse = aiChatService.callBailianAppStream(
            request.getContent(), sessionId, emitter, responseSessionId);
        
        // 构建AI回复消息
        MessageHistoryDTO aiMessage = buildAiMessage(aiResponse, responseSessionId[0], request);
        
        // 保存到数据库
        messageHistoryService.saveMessage(aiMessage);
        
        return MessageProcessResult.success("流式处理完成");
        
    } catch (Exception e) {
        log.error("流式处理失败: sessionId={}", sessionId, e);
        return MessageProcessResult.error("流式处理失败: " + e.getMessage(), "STREAM_PROCESS_ERROR");
    }
}
```

### 3. Controller层使用

```java
@GetMapping("/stream-chat")
public SseEmitter streamChat(@RequestParam String prompt, 
                            @RequestParam(required = false) String sessionId) {
    SseEmitter emitter = new SseEmitter(300000L); // 5分钟超时
    
    // 异步处理
    CompletableFuture.runAsync(() -> {
        try {
            String[] responseSessionId = new String[1];
            aiChatService.callBailianAppStream(prompt, sessionId, emitter, responseSessionId);
            emitter.complete();
        } catch (Exception e) {
            emitter.completeWithError(e);
        }
    });
    
    return emitter;
}
```

## 技术实现原理

### 1. DashScope SDK流式调用

方法使用阿里云DashScope SDK的官方流式接口：

```java
// 构建调用参数
ApplicationParam param = ApplicationParam.builder()
    .appId(appId)
    .apiKey(apiKey)
    .prompt(prompt)
    .incrementalOutput(true)  // 🔑 启用增量输出
    .sessionId(sessionId)     // 可选
    .build();

// 使用官方streamCall方法
Application application = new Application();
Flowable<ApplicationResult> resultFlowable = application.streamCall(param);
```

### 2. 流式数据处理

使用RxJava的Flowable处理流式响应：

```java
resultFlowable.blockingForEach(result -> {
    // 获取增量文本
    String incrementalText = result.getOutput().getText();
    String sessionId = result.getOutput().getSessionId();
    
    // 实时推送到前端
    StreamResponseEventDTO event = StreamResponseEventDTO
        .createChunkEvent(sessionId, incrementalText);
    emitter.send(SseEmitter.event().data(event));
});
```

### 3. 依赖要求

- **DashScope SDK**: >= 2.15.0
- **RxJava**: 用于Flowable流式处理
- **Spring Boot**: 用于SSE支持

## 配置要求

确保 `application.yml` 中配置了正确的百练API密钥：

```yaml
ai:
  bailian:
    app:
      api-key: sk-your-api-key
      app-id: your-app-id
```

## 异常处理

方法包含以下异常处理：

1. **API Key验证**：检查配置是否正确
2. **网络异常**：HTTP连接和读取异常
3. **响应解析**：JSON解析异常
4. **SSE发送异常**：前端连接断开等

## 性能优化建议

1. **连接超时设置**：
    - 连接超时：30秒
    - 读取超时：5分钟

2. **缓冲优化**：
    - 使用BufferedReader读取流式响应
    - 及时释放连接资源

3. **错误恢复**：
    - 网络异常时记录详细日志
    - 向前端发送错误事件

## 注意事项

1. **会话ID管理**：响应的会话ID需要回传并保存，用于后续对话
2. **前端处理**：前端需要正确处理不同类型的SSE事件
3. **资源释放**：确保在异常情况下正确关闭SSE连接
4. **并发控制**：考虑限制同时进行的流式连接数量 