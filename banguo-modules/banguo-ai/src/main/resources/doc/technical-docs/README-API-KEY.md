# API Key 鉴权使用说明

## 概述

本项目为SSE模式的MCP服务器提供了基于API Key的鉴权功能，确保只有授权的客户端才能访问AI服务。

## 功能特性

- ✅ **双重认证方式**：支持HTTP Header和URL参数两种方式传递API Key
- ✅ **使用限制**：支持每日使用次数限制
- ✅ **动态管理**：支持API Key的生成、撤销和管理
- ✅ **缓存优化**：使用Redis和内存缓存提高验证性能
- ✅ **安全防护**：支持API Key撤销和黑名单机制

## 配置说明

### 环境变量配置

```bash
# 默认API Keys（逗号分隔）
AI_API_KEY_DEFAULT=test-api-key-123,dev-api-key-456

# 每日使用限制
AI_API_KEY_USAGE_LIMIT=1000

# 缓存TTL（秒）
AI_API_KEY_CACHE_TTL=3600
```

### 配置文件配置

在 `application.yml` 中：

```yaml
ai:
  api-key:
    default-keys: test-api-key-123,dev-api-key-456
    usage-limit: 1000
    cache-ttl: 3600
```

## 使用方法

### 1. HTTP Header方式

```bash
# 使用curl
curl -H "X-API-Key: your-api-key" http://localhost:9209/ai/mcp/sse

# 使用JavaScript
fetch('http://localhost:9209/ai/mcp/sse', {
  headers: {
    'X-API-Key': 'your-api-key'
  }
})
```

### 2. URL参数方式

```bash
# 使用curl
curl "http://localhost:9209/ai/mcp/sse?apiKey=your-api-key"

# 使用JavaScript
fetch('http://localhost:9209/ai/mcp/sse?apiKey=your-api-key')
```

### 3. 客户端代码示例

#### JavaScript/TypeScript

```javascript
// 创建EventSource连接
const eventSource = new EventSource('http://localhost:9209/ai/mcp/sse?apiKey=your-api-key');

eventSource.onmessage = function(event) {
    console.log('收到消息:', event.data);
};

eventSource.onerror = function(error) {
    console.error('连接错误:', error);
};
```

#### Python

```python
import requests

# 使用Header方式
headers = {'X-API-Key': 'your-api-key'}
response = requests.get('http://localhost:9209/ai/mcp/sse', headers=headers)

# 使用参数方式
response = requests.get('http://localhost:9209/ai/mcp/sse?apiKey=your-api-key')
```

## API管理接口

### 1. 生成API Key

```bash
curl -X POST "http://localhost:9209/ai/api-key/generate?description=测试API Key"
```

响应：

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "apiKey": "banguo_1234567890abcdef",
    "description": "测试API Key",
    "message": "API Key生成成功"
  }
}
```

### 2. 撤销API Key

```bash
curl -X POST "http://localhost:9209/ai/api-key/revoke?apiKey=banguo_1234567890abcdef"
```

### 3. 验证API Key

```bash
curl -X POST "http://localhost:9209/ai/api-key/validate?apiKey=banguo_1234567890abcdef"
```

### 4. 获取使用说明

```bash
curl "http://localhost:9209/ai/api-key/usage"
```

## 错误处理

### 常见错误码

- **401 Unauthorized**: API Key缺失或无效
- **429 Too Many Requests**: 使用次数超限

### 错误响应格式

```json
{
  "error": "缺少API Key",
  "code": 401
}
```

## 安全建议

1. **保护API Key**：不要将API Key暴露在客户端代码中
2. **定期轮换**：定期更换API Key以提高安全性
3. **监控使用**：监控API Key的使用情况，发现异常及时处理
4. **最小权限**：为不同的客户端分配不同的API Key

## 测试

### 测试API Key认证

```bash
# 测试认证功能
curl -H "X-API-Key: test-api-key-123" http://localhost:9209/ai/test/auth-test

# 测试无API Key访问（应该返回401）
curl http://localhost:9209/ai/test/auth-test

# 测试无效API Key（应该返回401）
curl -H "X-API-Key: invalid-key" http://localhost:9209/ai/test/auth-test
```

## 注意事项

1. API Key验证在拦截器层面进行，所有 `/ai/mcp/**` 路径的请求都会被验证
2. 测试路径 `/ai/test/**` 被排除在验证之外
3. API Key使用次数限制按天计算，每天零点重置
4. 撤销的API Key会在30天后自动清理
5. 默认API Key主要用于开发和测试环境，生产环境建议使用动态生成的API Key 