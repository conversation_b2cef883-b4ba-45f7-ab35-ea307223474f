# xianjian-common-wechat 微信公众号模块使用文档

## 模块概述

`xianjian-common-wechat` 是一个基于 Spring Boot 的微信公众号集成模块，支持多公众号配置，使用 Redis 作为配置存储，基于
`weixin-java-mp` 开发。

## 模块结构

```
xianjian-common-wechat/
├── src/main/java/cn/xianlink/common/wechat/
│   └── config/
│       ├── WxMpAutoConfiguration.java          # 自动配置类
│       └── properties/
│           ├── WxMpProperties.java             # 微信公众号配置属性
│           └── UserWechatBinding.java          # 用户类型与微信账号绑定配置
└── src/main/resources/
    └── META-INF/spring/
        └── org.springframework.boot.autoconfigure.AutoConfiguration.imports
```

## 在其他项目中使用

### 1. Maven 依赖配置

在你的项目（如 `banguo-ai`）的 `pom.xml` 中添加依赖：

```xml
<dependencies>
    <!-- 引入微信公众号模块 -->
    <dependency>
        <groupId>cn.xianlink</groupId>
        <artifactId>xianjian-common-wechat</artifactId>
        <version>${xianjian.version}</version>
    </dependency>
    
    <!-- 必须的 Redis 依赖（如果项目中没有） -->
    <dependency>
        <groupId>cn.xianlink</groupId>
        <artifactId>xianjian-common-redis</artifactId>
        <version>${xianjian.version}</version>
    </dependency>
</dependencies>
```

### 2. 应用配置文件

在 `application.yml` 或 `application.properties` 中配置微信公众号信息：

#### YAML 格式配置示例：

```yaml
# 微信公众号配置
wx:
  mp:
    configs:
      
  
  # 用户类型与微信账号绑定关系配置
  bind:
    configs:
  # 新增：originalId 到小程序 appId 的映射关系
  original-mapping:
    configs:
      gh_ec67c6255831: wx863edfff90bb02c0    # 领鲜集采小程序
      gh_28873a0e022f: wx5da7004a4491e2c1    # 般果集采平台+
      gh_c6cebacb87c4: wx9cc94925bfca5c9d    # 总仓小程序
      gh_4a5d73fc9aea: wxdfad8c82da70105d    # 供应商小程序
      gh_558bb7eda9cf: wx7ae0dc5396e60821    # 城市仓小程序
        
```

#### Properties 格式配置示例：

```properties
# 微信公众号配置
wx.mp.configs.account1.app-id=wx1234567890abcdef
wx.mp.configs.account1.app-secret=your_app_secret_here
wx.mp.configs.account1.token=your_token_here
wx.mp.configs.account1.aes-key=your_aes_key_here

wx.mp.configs.account2.app-id=wx0987654321fedcba
wx.mp.configs.account2.app-secret=another_app_secret
wx.mp.configs.account2.token=another_token
wx.mp.configs.account2.aes-key=another_aes_key

# 用户绑定配置
wx.bind.configs.normal.mp-id=account1
wx.bind.configs.normal.mini-app-id=miniapp1234567890
wx.bind.configs.vip.mp-id=account2
wx.bind.configs.vip.mini-app-id=miniapp0987654321
```

### 3. 在代码中使用

#### 3.1 注入 WxMpService 使用

```java
@Service
public class WechatService {
    
    @Autowired
    private WxMpService wxMpService;
    
    public void sendTemplateMessage(String accountKey, String openId, String templateId) {
        try {
            // 切换到指定的公众号配置
            wxMpService.switchover(accountKey);
            
            // 发送模板消息
            WxMpTemplateMessage message = WxMpTemplateMessage.builder()
                .toUser(openId)
                .templateId(templateId)
                .build();
                
            WxMpTemplateMessage.WxMpTemplateData data = new WxMpTemplateMessage.WxMpTemplateData();
            data.setName("content");
            data.setValue("您好，这是一条测试消息");
            message.addData(data);
            
            wxMpService.getTemplateMsgService().sendTemplateMsg(message);
        } catch (WxErrorException e) {
            log.error("发送模板消息失败", e);
        }
    }
}
```

#### 3.2 注入配置属性使用

```java
@Service
public class WechatConfigService {
    
    @Autowired
    private WxMpProperties wxMpProperties;
    
    @Autowired
    private UserWechatBinding userWechatBinding;
    
    /**
     * 根据用户类型获取对应的公众号配置
     */
    public WxMpProperties.Config getMpConfigByUserType(String userType) {
        UserWechatBinding.Config bindConfig = userWechatBinding.getConfigs().get(userType);
        if (bindConfig != null) {
            String mpId = bindConfig.getMpId();
            return wxMpProperties.getConfigs().get(mpId);
        }
        return null;
    }
    
    /**
     * 获取所有公众号配置
     */
    public Map<String, WxMpProperties.Config> getAllMpConfigs() {
        return wxMpProperties.getConfigs();
    }
}
```

### 4. 自动配置原理

模块通过 Spring Boot 的自动配置机制工作：

1. **自动配置类注册**：`META-INF/spring/org.springframework.boot.autoconfigure.AutoConfiguration.imports` 文件自动注册了
   `WxMpAutoConfiguration`
2. **属性绑定**：`@EnableConfigurationProperties` 启用了 `WxMpProperties` 和 `UserWechatBinding` 的属性绑定
3. **Bean 创建**：自动创建 `WxMpService` Bean，并配置多公众号支持
4. **Redis 集成**：使用 `WxMpRedissonConfigImpl` 将配置存储在 Redis 中

### 5. 依赖要求

使用此模块需要确保项目中包含以下依赖：

- Spring Boot Starter
- Redisson（Redis 客户端）
- weixin-java-mp 4.6.0

### 6. 注意事项

1. **Redis 配置**：确保项目中已正确配置 Redis 连接
2. **配置前缀**：微信公众号配置使用 `wx.mp` 前缀，绑定配置使用 `wx.bind` 前缀
3. **多公众号支持**：通过 `configs` Map 结构支持配置多个公众号
4. **安全性**：AppSecret 等敏感信息建议使用环境变量或加密配置

### 7. 完整示例项目结构

```
banguo-ai/
├── pom.xml
├── src/main/java/
│   └── com/banguo/ai/
│       ├── BanguoAiApplication.java
│       └── service/
│           └── WechatMessageService.java
└── src/main/resources/
    └── application.yml
```

通过以上配置，`banguo-ai` 项目就可以完全使用 `xianjian-common-wechat` 模块提供的微信公众号功能了。 