# 百练模型API调用说明

## 概述

本文档介绍如何使用 `BailianAiChatServiceImpl` 类中新增的百练模型API调用功能。该功能支持直接调用阿里云百练平台的大语言模型，区别于现有的智能体API调用。

## 功能特性

- **多种调用方式**：支持简单文本输入和复杂多轮对话
- **模型选择**：可以指定特定模型或使用默认模型
- **高效调用**：模型API直接调用，无重试机制，响应更快
- **完整的错误处理**：提供详细的错误日志和异常处理
- **统一返回格式**：返回标准的 `AiCallResultDTO` 对象

## API调用方式对比

| 特性       | 智能体API (`callBailianApp`) | 模型API (`callBailianModel`) |
|----------|---------------------------|----------------------------|
| **会话管理** | ✅ 支持会话ID管理                | ❌ 不支持会话管理                  |
| **重试机制** | ✅ 超时自动重试一次                | ❌ 无重试机制                    |
| **扩展功能** | ✅ 支持插件和扩展                 | ❌ 仅基础对话                    |
| **响应速度** | 相对较慢                      | ✅ 更快                       |
| **适用场景** | 复杂对话、会话管理                 | 简单问答、快速响应                  |

## 配置说明

### 1. 基础配置

在 `application.yml` 或 Nacos 配置中心添加以下配置：

```yaml
ai:
  bailian:
    app:
      api-key: ${AI_BAILIAN_API_KEY:your-api-key-here}      # 智能体和模型API共用的API Key
      app-id: ${AI_BAILIAN_APP_ID:your-app-id-here}         # 用于智能体API
    model:
      default-model: ${AI_BAILIAN_DEFAULT_MODEL:qwen-plus}  # 默认模型名称
```

**注意事项：**

- 智能体API和模型API使用相同的API Key
- 模型API不需要单独的app-id，但需要指定具体的模型名称
- API Key必须配置，否则两种API都无法正常工作

### 2. 环境变量

推荐使用环境变量配置敏感信息：

```bash
export AI_BAILIAN_API_KEY="your-actual-api-key"
export AI_BAILIAN_APP_ID="your-app-id"
export AI_BAILIAN_DEFAULT_MODEL="qwen-plus"
```

### 3. 支持的模型

常用模型列表：

- `qwen-plus`：通义千问增强版
- `qwen-turbo`：通义千问标准版
- `qwen-max`：通义千问旗舰版
- `qwen-vl-plus`：通义千问视觉理解模型
- `qwen-long`：通义千问长文本模型

## 使用方法

### 1. 简单文本对话

```java
@Autowired
private IAiChatService aiChatService;

// 使用默认模型（传null作为modelName）
AiCallResultDTO result = aiChatService.callBailianModel("你好，请介绍一下自己", null);
System.out.println("AI回复: " + result.getText());

// 指定特定模型
AiCallResultDTO result2 = aiChatService.callBailianModel("写一首诗", "qwen-max");
System.out.println("AI回复: " + result2.getText());
```

### 2. 多轮对话

```java
import com.alibaba.dashscope.common.Message;
import com.alibaba.dashscope.common.Role;

// 构建对话历史
List<Message> messages = new ArrayList<>();

// 系统提示词
messages.add(Message.builder()
    .role(Role.SYSTEM.getValue())
    .content("你是一个专业的Java开发助手")
    .build());

// 用户消息
messages.add(Message.builder()
    .role(Role.USER.getValue())
    .content("什么是Spring Boot？")
    .build());

// AI回复
messages.add(Message.builder()
    .role(Role.ASSISTANT.getValue())
    .content("Spring Boot是一个基于Spring Framework的开源框架...")
    .build());

// 继续对话
messages.add(Message.builder()
    .role(Role.USER.getValue())
    .content("请给出一个简单的示例")
    .build());

// 调用API
AiCallResultDTO result = aiChatService.callBailianModel(messages, "qwen-plus");
System.out.println("AI回复: " + result.getText());
```

### 3. 在Controller中使用

```java
@RestController
@RequestMapping("/api/ai")
public class AiController {
    
    @Autowired
    private IAiChatService aiChatService;
    
    @PostMapping("/chat")
    public ResponseEntity<String> chat(@RequestBody String message) {
        try {
            // 使用默认模型
            AiCallResultDTO result = aiChatService.callBailianModel(message, null);
            return ResponseEntity.ok(result.getText());
        } catch (Exception e) {
            log.error("AI调用失败", e);
            return ResponseEntity.status(500).body("AI服务暂时不可用");
        }
    }
    
    @PostMapping("/chat/advanced")
    public ResponseEntity<AiCallResultDTO> advancedChat(
            @RequestBody List<Message> messages,
            @RequestParam(required = false) String model) {
        try {
            AiCallResultDTO result = aiChatService.callBailianModel(messages, model);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("AI调用失败", e);
            return ResponseEntity.status(500).build();
        }
    }
}
```

## API 方法说明

### 1. `callBailianModel(String prompt, String modelName)`

指定模型的单问题调用。

**参数：**

- `prompt`：用户输入的文本
- `modelName`：模型名称，为null时使用默认模型

**返回：** `AiCallResultDTO` 对象

### 2. `callBailianModel(List<Message> messages, String modelName)`

完整功能的多轮对话调用。

**参数：**

- `messages`：对话消息列表
- `modelName`：模型名称，为null时使用默认模型

**返回：** `AiCallResultDTO` 对象

## 返回对象说明

`AiCallResultDTO` 包含以下字段：

```java
public class AiCallResultDTO {
    private String requestId;     // 请求ID
    private String text;          // AI回复文本
    private String sessionId;     // 会话ID（模型API为null）
    private boolean newSession;   // 是否新会话（模型API始终为true）
}
```

## 错误处理

### 常见异常

1. **`RuntimeException("百练API Key未配置")`**
    - 原因：未正确配置API Key
    - 解决：检查配置文件和环境变量

2. **`ApiException`**
    - 原因：API调用失败
    - 解决：检查网络连接和API配额

3. **`NoApiKeyException`**
    - 原因：API Key无效
    - 解决：检查API Key是否正确

4. **`InputRequiredException`**
    - 原因：输入参数不完整
    - 解决：检查输入消息是否为空

### 超时重试机制

系统会自动检测超时错误并进行一次重试：

- 检测错误码：`RequestTimeOut`
- 检测错误消息包含：`timeout`、`Request timed out`
- 重试间隔：立即重试
- 重试次数：1次

## 性能建议

1. **连接池管理**：底层SDK会自动管理连接池
2. **并发控制**：建议控制并发请求数量，避免超过API限制
3. **缓存策略**：对于相同问题可以考虑缓存结果
4. **异步处理**：对于耗时操作建议使用异步调用

## 与智能体API的区别

| 特性    | 模型API (`callBailianModel`) | 智能体API (`callBailianApp`) |
|-------|----------------------------|---------------------------|
| 会话管理  | 不支持，需要手动维护消息历史             | 支持，自动维护会话状态               |
| 功能扩展  | 基础对话功能                     | 支持插件、工具调用等扩展功能            |
| 灵活性   | 高，可完全控制对话流程                | 中，受智能体配置限制                |
| 配置复杂度 | 低，只需要API Key               | 高，需要创建和配置智能体              |
| 适用场景  | 简单对话、文本生成                  | 复杂业务流程、多功能集成              |

## 示例项目

参考 `banguo-ai` 模块中的测试类了解更多使用示例。

## 注意事项

1. **API配额**：注意阿里云百练的API调用配额限制
2. **成本控制**：不同模型的计费标准不同，选择合适的模型
3. **数据安全**：敏感信息不要通过API传递
4. **错误日志**：生产环境注意控制错误日志的输出级别 