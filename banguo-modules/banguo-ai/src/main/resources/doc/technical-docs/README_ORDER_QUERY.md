# 订单查询MCP服务

## 概述

订单查询MCP服务是`banguo-ai`模块中的一个重要组件，提供了通过MCP（Model Context Protocol）协议查询订单信息和售后信息的功能。该服务通过Dubbo调用
`banguo-order`模块的接口，支持订单号查询和售后查询。

## 功能特性

### 1. 订单查询功能

- **根据订单号查询订单信息**：提供订单的基本信息、状态、金额等详细信息
- **根据订单号和手机号查询订单信息**：支持订单归属验证
- **根据订单ID查询订单信息**：直接通过订单ID查询

### 2. 售后查询功能

- **根据订单ID查询售后信息**：包括退款记录、售后状态、退款金额等
- **根据订单ID和手机号查询售后信息**：支持订单归属验证

### 3. 数据验证功能

- **手机号格式验证**：支持多种手机号格式的验证
- **订单归属验证**：通过手机号验证订单归属

## API接口

### 订单查询接口

#### 1. queryOrderByNo

根据订单号查询订单信息

**参数：**

- `orderNo` (String): 订单号，格式如：DD20240315xxxx

**返回：**

- 订单详细信息，包括订单ID、业务类型、实付金额、商品金额、代采费、运费等

#### 2. queryOrderByNoAndPhone

根据订单号和收货手机号查询订单信息

**参数：**

- `orderNo` (String): 订单号，格式如：DD20240315xxxx
- `phone` (String): 收货手机号，用于验证订单归属

**返回：**

- 订单详细信息（需要验证手机号匹配）

#### 3. queryOrderById

根据订单ID查询订单信息

**参数：**

- `orderId` (Long): 订单ID，数字格式

**返回：**

- 订单详细信息

### 售后查询接口

#### 1. queryAfterSaleInfo

根据订单ID查询售后信息

**参数：**

- `orderId` (Long): 订单ID，数字格式

**返回：**

- 售后信息，包括取消订单退款、差额退款、少货退款等

#### 2. queryAfterSaleInfoByOrderIdAndPhone

根据订单ID和收货手机号查询售后信息

**参数：**

- `orderId` (Long): 订单ID，数字格式
- `phone` (String): 收货手机号，用于验证订单归属

**返回：**

- 售后信息（需要验证手机号匹配）

## 技术实现

### 依赖服务

- `RemoteOrderService`: 订单服务接口
- `RemoteRefundRecordService`: 退款记录服务接口
- `RemoteBasCustomerService`: 客户基础服务接口

### 核心方法

#### 订单信息查询

```java
// 通过订单号查询
RemoteOrderInfoVo orderInfo = remoteOrderService.getOrderInfoByCode(orderNo);

// 通过订单ID查询
RemoteOrderInfoVo orderInfo = remoteOrderService.getOrderInfo(orderId);
```

#### 售后信息查询

```java
// 查询各种退款信息
RemoteOrderBusiAmtVo cancelRefund = remoteRefundRecordService.getCancelRefund(orderId);
RemoteOrderBusiAmtVo diffRefund = remoteRefundRecordService.getDiffRefund(orderId);
RemoteOrderBusiAmtVo cityRefund = remoteRefundRecordService.getCityRefund(orderId);
```

#### 手机号验证

```java
// 验证手机号格式
boolean isValid = isValidPhoneNumber(phone);
```

## 配置说明

### MCP工具配置

在`McpToolConfiguration`中注册了订单查询工具：

```java
@Bean
public ToolCallbackProvider orderQueryTools(OrderQueryService orderQueryService) {
    return MethodToolCallbackProvider.builder()
            .toolObjects(orderQueryService)
            .build();
}
```

### 依赖配置

在`pom.xml`中包含了必要的依赖：

```xml
<dependency>
    <groupId>cn.xianlink</groupId>
    <artifactId>banguo-api-order</artifactId>
</dependency>
<dependency>
    <groupId>cn.xianlink</groupId>
    <artifactId>banguo-api-basic</artifactId>
</dependency>
```

## 使用示例

### 1. 查询订单信息

```
输入：订单号 DD20240315xxxx
输出：
订单查询结果：
订单ID：12345
业务类型：1
实付金额：1000.00元
商品金额：800.00元
代采费：50.00元
运费：150.00元
优惠合计：0.00元
金融服务费：0.00元
```

### 2. 查询售后信息

```
输入：订单ID 12345
输出：
售后查询结果：
订单ID：12345
退款信息：
- 取消订单退款：100.00元
- 差额退款：50.00元
```

## 错误处理

### 常见错误

1. **订单号不存在**：返回"未找到该订单号对应的记录，请确认订单号是否正确"
2. **手机号格式错误**：返回"请提供正确格式的手机号码"
3. **系统异常**：返回"查询订单信息时发生错误：[错误详情]"

### 异常处理

所有方法都包含了try-catch异常处理，确保服务的稳定性。

## 测试

### 单元测试

运行订单查询服务的单元测试：

```bash
mvn test -Dtest=OrderQueryServiceTest
```

### 测试覆盖

- 手机号格式验证测试
- 订单查询功能测试（需要真实数据）
- 售后查询功能测试（需要真实数据）

## 注意事项

1. **权限验证**：某些查询可能需要验证用户权限
2. **数据安全**：敏感信息（如手机号）需要进行脱敏处理
3. **性能优化**：大量查询时需要考虑缓存机制
4. **日志记录**：所有查询操作都会记录详细的日志信息

## 版本历史

- **v1.0.0**: 初始版本，支持基本的订单查询和售后查询功能
- 支持订单号查询和订单ID查询
- 支持售后信息查询
- 支持手机号格式验证

## 联系方式

如有问题或建议，请联系开发团队。 