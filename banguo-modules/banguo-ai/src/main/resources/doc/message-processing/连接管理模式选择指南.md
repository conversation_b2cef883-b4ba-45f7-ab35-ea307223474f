# 般果AI连接管理模式选择指南

## 🎯 概述

般果AI系统提供两种连接管理模式，您可以根据部署环境和需求选择：

1. **本地连接管理模式（Local）**：依赖会话粘滞，适合稳定环境
2. **分布式连接管理模式（Distributed）**：基于Redis，适合高可用环境

## 📊 模式对比

| 特性         | 本地模式    | 分布式模式 |
|------------|---------|-------|
| **实现复杂度**  | 简单      | 复杂    |
| **性能**     | 高       | 中等    |
| **内存使用**   | 低       | 中等    |
| **可用性**    | 依赖实例稳定性 | 高     |
| **容错性**    | 低       | 高     |
| **扩展性**    | 有限制     | 好     |
| **实例重启影响** | 连接全部丢失  | 连接可恢复 |
| **负载均衡要求** | 需要会话粘滞  | 无特殊要求 |

## 🏗️ 架构对比

### 本地连接管理架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Nginx/LB       │    │  Instance-1     │    │  Instance-2     │
│  (Session       │    │  ┌─────────────┐│    │  ┌─────────────┐│
│   Sticky)   ────┼────┼─▶│Local Memory ││    │  │Local Memory ││
│                 │    │  │Connection   ││    │  │Connection   ││
└─────────────────┘    │  │Manager      ││    │  │Manager      ││
                       │  └─────────────┘│    │  └─────────────┘│
                       └─────────────────┘    └─────────────────┘
```

### 分布式连接管理架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Nginx/LB       │    │  Instance-1     │    │  Instance-2     │
│  (Round Robin)  │    │  ┌─────────────┐│    │  ┌─────────────┐│
│             ────┼────┼─▶│Distributed  ││    │  │Distributed  ││
│                 │    │  │Connection   ││    │  │Connection   ││
└─────────────────┘    │  │Manager      ││    │  │Manager      ││
                       │  └─────────────┘│    │  └─────────────┘│
                       └─────────────────┘    └─────────────────┘
                                │                        │
                                └───────┬────────────────┘
                                        │
                                ┌─────────────────┐
                                │  Redis Cluster  │
                                │  ┌─────────────┐│
                                │  │Connection   ││
                                │  │State Store  ││
                                │  └─────────────┘│
                                └─────────────────┘
```

## ⚙️ 配置方式

### 1. 本地连接管理模式

**配置文件：** `application-local.yml`

```yaml
spring:
  profiles:
    active: local

banguo:
  ai:
    stream:
      # 连接管理模式：local（本地）
      connection-mode: local
      
      # SSE连接配置
      sse:
        heartbeat-interval: 30        # 心跳间隔（秒）
        connection-timeout: 300       # 连接超时时间（秒）
        max-connections: 10000        # 最大连接数
        buffer-size: 8192            # 缓冲区大小

# Nacos配置 - 启用会话粘滞
spring:
  cloud:
    nacos:
      discovery:
        metadata:
          sticky-session: true       # 启用会话粘滞
          load-balance: consistent   # 一致性负载均衡
```

**启动命令：**

```bash
java -jar banguo-ai.jar --spring.profiles.active=local
```

### 2. 分布式连接管理模式

**配置文件：** `application-distributed.yml`

```yaml
spring:
  profiles:
    active: distributed

banguo:
  ai:
    stream:
      # 连接管理模式：distributed（分布式）
      connection-mode: distributed
      
      # SSE连接配置
      sse:
        heartbeat-interval: 30        # 心跳间隔（秒）
        connection-timeout: 300       # 连接超时时间（秒）
        max-connections: 5000         # 最大连接数（分布式单实例）
        buffer-size: 8192            # 缓冲区大小
      
      # 分布式配置
      distributed:
        redis-key-prefix: "stream:connection:"
        cleanup-interval: 60         # 连接清理间隔（秒）
        instance-id: ${HOSTNAME:${spring.application.name}-${server.port}}
        enable-cross-instance: true  # 启用跨实例通信

# Redis配置（必需）
spring:
  data:
    redis:
      host: ${REDIS_HOST:redis-server}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      database: ${REDIS_DB:0}
```

**启动命令：**

```bash
java -jar banguo-ai.jar --spring.profiles.active=distributed
```

## 🚀 部署建议

### 开发环境

- **推荐模式**：本地连接管理
- **原因**：简单易用，调试方便
- **配置**：`application-local.yml`

### 测试环境

- **推荐模式**：分布式连接管理
- **原因**：验证分布式特性
- **配置**：`application-distributed.yml`

### 生产环境

#### 小型项目（< 1000 并发）

- **推荐模式**：本地连接管理
- **前提条件**：
    - 环境稳定，服务重启频率低
    - 负载均衡器支持会话粘滞
    - 可以接受重启时连接丢失
- **优势**：性能好，资源消耗低

#### 中大型项目（> 1000 并发）

- **推荐模式**：分布式连接管理
- **前提条件**：
    - 高可用性要求
    - 需要支持动态扩缩容
    - 有Redis集群支持
- **优势**：高可用，容错性强

## 🔧 负载均衡器配置

### 本地模式 - Nginx配置

```nginx
upstream banguo-ai {
    ip_hash;  # 启用会话粘滞
    server ************:9211;
    server ************:9211;
    server ************:9211;
}

server {
    listen 80;
    server_name ai.banguo.com;
    
    location /ai/stream/ {
        proxy_pass http://banguo-ai;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        
        # SSE特定配置
        proxy_set_header Connection '';
        proxy_http_version 1.1;
        proxy_buffering off;
        proxy_cache off;
        
        # 会话粘滞配置
        proxy_set_header Cookie $http_cookie;
    }
}
```

### 分布式模式 - Nginx配置

```nginx
upstream banguo-ai {
    # 不需要ip_hash，使用普通轮询
    server ************:9211;
    server ************:9211;
    server ************:9211;
}

server {
    listen 80;
    server_name ai.banguo.com;
    
    location /ai/stream/ {
        proxy_pass http://banguo-ai;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        
        # SSE特定配置
        proxy_set_header Connection '';
        proxy_http_version 1.1;
        proxy_buffering off;
        proxy_cache off;
    }
}
```

## 📈 性能对比

### 本地模式性能

- **单实例连接数**：10,000+
- **响应时间**：< 100ms
- **内存使用**：低（本地Map存储）
- **CPU使用**：低
- **网络开销**：无

### 分布式模式性能

- **单实例连接数**：5,000+
- **响应时间**：< 200ms
- **内存使用**：中等
- **CPU使用**：中等
- **网络开销**：有（Redis交互）

## 🛠️ 运维监控

### 本地模式监控指标

```yaml
# 关键指标
- 连接数：current_connections
- 内存使用：local_memory_usage
- 服务状态：service_health
- 会话粘滞率：sticky_session_rate
```

### 分布式模式监控指标

```yaml
# 关键指标
- 连接数：current_connections
- Redis响应时间：redis_response_time
- 跨实例通信：cross_instance_messages
- 连接迁移：connection_migration_count
```

## 🔄 模式切换

### 从本地模式切换到分布式模式

1. **停止所有服务实例**
2. **部署Redis集群**
3. **修改配置文件**
   ```yaml
   banguo:
     ai:
       stream:
         connection-mode: distributed
   ```
4. **启动服务实例**
5. **验证功能**

### 从分布式模式切换到本地模式

1. **停止所有服务实例**
2. **配置负载均衡器会话粘滞**
3. **修改配置文件**
   ```yaml
   banguo:
     ai:
       stream:
         connection-mode: local
   ```
4. **启动服务实例**
5. **验证功能**

## 🚨 故障排查

### 本地模式常见问题

1. **连接频繁断开**
    - 检查负载均衡器会话粘滞配置
    - 检查Nacos服务注册状态

2. **重启后连接丢失**
    - 正常现象，客户端需要重新连接
    - 可以考虑客户端自动重连机制

### 分布式模式常见问题

1. **连接状态不一致**
    - 检查Redis集群状态
    - 检查实例ID配置

2. **跨实例通信失败**
    - 检查Redis发布/订阅功能
    - 检查网络连接

## 📝 最佳实践

### 本地模式最佳实践

1. **确保负载均衡器会话粘滞**
2. **定期检查服务健康状态**
3. **实现客户端自动重连**
4. **监控连接数和内存使用**

### 分布式模式最佳实践

1. **使用Redis集群提高可用性**
2. **设置合理的连接超时时间**
3. **定期清理过期连接**
4. **监控Redis性能指标**

## 🎯 总结

**选择建议：**

- **优先考虑分布式模式**：如果您需要高可用性和容错性
- **考虑本地模式**：如果您的环境非常稳定且对性能要求极高
- **混合方案**：在负载均衡器层面实现会话粘滞，同时保留分布式连接管理作为容错机制

记住：您随时可以通过修改配置文件在两种模式之间切换！ 