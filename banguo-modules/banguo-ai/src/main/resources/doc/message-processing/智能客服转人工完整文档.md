# 智能客服转人工完整文档

## 概述

本文档详细描述了智能客服转人工的完整业务流程，包括流程图、时序图、业务规则和异常处理机制。用户通过小程序或公众号客服入口咨询问题，后端使用百炼Agent作为智能客服，基于知识库内容进行回答。当遇到无法回答的问题或知识库中没有的内容时，会触发转人工流程。

## 核心流程图

```mermaid
flowchart TD
    A[用户进入小程序/公众号客服] --> B[用户发送咨询问题]
    B --> C[百炼Agent接收问题]
    C --> D{Agent分析问题}
    D --> E{知识库中是否有答案?}
    
    E -->|是| F[Agent基于知识库回答]
    F --> G[用户满意?]
    G -->|是| H[结束对话]
    G -->|否| I[用户继续提问]
    I --> C
    
    E -->|否| J[触发转人工流程]
    J --> K[Agent提示转人工]
    K --> L[收集用户姓名]
    L --> M[收集用户电话]
    M --> N{信息收集完成?}
    
    N -->|否| O[继续收集信息]
    O --> L
    
    N -->|是| P[Agent总结会话内容]
    P --> Q[生成工单标题]
    Q --> R[生成问题描述]
    R --> S[调用MCP服务创建工单]
    S --> T{工单创建成功?}
    
    T -->|否| U[返回错误信息]
    U --> V[提示用户稍后重试]
    V --> W[结束流程]
    
    T -->|是| X[返回工单号]
    X --> Y[Agent告知用户工单号]
    Y --> Z[告知用户人工客服将回访]
    Z --> AA[结束当前对话]
    
    AA --> BB[工单进入人工处理队列]
    BB --> CC[人工客服查看工单]
    CC --> DD[人工客服联系用户]
    DD --> EE[人工客服解决问题]
    EE --> FF[更新工单状态]
    FF --> GG[流程结束]

    style A fill:#e1f5fe
    style J fill:#fff3e0
    style S fill:#f3e5f5
    style BB fill:#e8f5e8
    style GG fill:#fce4ec
```

## 系统交互时序图

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端(小程序/公众号)
    participant A as 百炼Agent
    participant K as 知识库系统
    participant M as MCP工单服务
    participant H as 人工客服系统
    participant C as 人工客服

    Note over U,C: 智能客服阶段
    U->>F: 进入客服入口
    F->>A: 建立会话连接
    U->>F: 发送咨询问题
    F->>A: 转发用户问题
    A->>K: 查询知识库
    K-->>A: 返回匹配结果
    
    alt 知识库有答案
        A->>F: 返回智能回答
        F->>U: 显示回答内容
        U->>F: 继续提问或结束
    else 知识库无答案
        A->>F: 触发转人工流程
        F->>U: 提示转人工
        
        Note over U,C: 信息收集阶段
        A->>F: 请求收集用户姓名
        F->>U: 提示输入姓名
        U->>F: 输入姓名
        F->>A: 返回用户姓名
        
        A->>F: 请求收集用户电话
        F->>U: 提示输入电话
        U->>F: 输入电话
        F->>A: 返回用户电话
        A->>M: 调用MCP服务验证手机号
        
        Note over U,C: 工单创建阶段
        A->>A: 总结会话内容
        A->>A: 生成工单标题
        A->>A: 生成问题描述
        A->>M: 调用创建工单接口
        Note right of A: 参数包含:<br/>- 用户姓名<br/>- 用户电话<br/>- 工单标题<br/>- 问题描述<br/>- 会话记录
        M-->>A: 返回工单号
        A->>F: 返回工单信息
        F->>U: 显示工单号和回访提示
        
        Note over U,C: 人工处理阶段
        M->>H: 工单进入处理队列
        H->>C: 分配工单给客服
        C->>C: 查看工单详情
        C->>U: 主动联系用户(电话)
        U->>C: 接听电话
        C->>U: 解决问题
        C->>H: 更新工单状态
        
    end
```

## 详细业务规则

### 1. 智能客服阶段

- **用户入口**: 小程序或公众号客服入口
- **Agent处理**: 百炼Agent接收并分析用户问题
- **知识库匹配**: 基于知识库内容进行智能回答
- **转人工触发条件**:
    - 知识库中没有相关答案
    - Agent无法理解或处理的问题
    - 用户明确要求转人工
    - 置信度低于阈值

### 2. 信息收集阶段

- **用户姓名**: 必填信息，验证格式
- **用户电话**: 必填信息，用于人工回访，验证格式
- **信息验证**: 格式不正确时重新收集

### 3. 工单创建阶段

- **会话总结**: Agent自动总结对话内容
- **标题生成**: 基于问题内容生成工单标题
- **描述生成**: 详细描述用户问题和需求
- **MCP服务**: 调用工单系统创建工单
- **重试机制**: 创建失败时重试3次

### 4. 人工处理阶段

- **工单分配**: 进入人工处理队列
- **客服回访**: 人工客服主动联系用户
- **问题解决**: 人工客服处理并解决问题
- **状态更新**: 更新工单处理状态

## 关键接口说明

### 1. Agent与知识库交互

```json
// 知识库查询请求
{
  "query": "用户问题内容",
  "session_id": "会话ID",
  "user_id": "用户ID"
}

// 知识库查询响应
{
  "has_answer": true/false,
  "answer": "回答内容",
  "confidence": 0.95,
  "source": "知识库来源"
}
```

### 2. Agent与MCP工单服务交互

```json
// 创建工单请求
{
  "title": "工单标题",
  "description": "问题详细描述",
  "user_name": "用户姓名",
  "user_phone": "用户电话",
  "session_id": "会话ID",
  "priority": "优先级",
  "category": "问题分类"
}

// 创建工单响应
{
  "success": true,
  "ticket_id": "工单号",
  "message": "创建成功"
}
```

## 关键业务指标

### 1. 智能客服指标

- **智能回答率**: 知识库能够回答的问题占比
- **用户满意度**: 用户对智能回答的满意程度
- **转人工率**: 需要转人工的问题占比
- **平均响应时间**: Agent处理问题的平均时间

### 2. 转人工指标

- **信息收集成功率**: 成功收集用户信息的比例
- **工单创建成功率**: 成功创建工单的比例
- **平均处理时间**: 从转人工到工单创建的时间

### 3. 人工处理指标

- **首次联系成功率**: 人工客服首次联系成功的比例
- **问题解决率**: 人工客服解决问题的比例
- **平均解决时间**: 从分配到解决的平均时间

## 异常处理机制

### 1. 网络异常

- Agent无法连接知识库时，直接转人工
- MCP服务不可用时，提示用户稍后重试
- 前端网络异常时，保存本地数据，网络恢复后同步

### 2. 信息收集异常

- 用户拒绝提供信息时，记录并转人工
- 信息格式错误时，重新收集
- 收集超时时，使用默认值或转人工

### 3. 工单创建异常

- 创建失败时，重试3次
- 重试失败后，提示用户联系客服
- 部分信息缺失时，创建工单并标记为待补充

### 4. 人工处理异常

- 客服无法联系用户时，发送短信通知
- 用户拒绝接听时，记录并标记
- 问题无法解决时，升级处理

## 技术架构要点

1. **前端**: 小程序/公众号客服入口
2. **后端**: 百炼Agent + 知识库系统
3. **工单系统**: MCP服务接口
4. **人工客服**: 工单管理系统

## 业务价值

### 1. 提升用户体验

- 快速响应：智能客服24/7在线
- 无缝转接：智能到人工的平滑过渡
- 个性化服务：基于用户历史提供定制化服务

### 2. 降低运营成本

- 减少人工客服工作量
- 提高问题解决效率
- 优化资源配置

### 3. 提升服务质量

- 标准化处理流程
- 减少人为错误
- 持续优化知识库

## 总结

智能客服转人工流程是一个完整的客户服务体系，通过AI技术和人工服务的有机结合，为用户提供高效、便捷的客户服务体验。该流程不仅提升了服务质量，还降低了运营成本，是现代客户服务的重要发展方向。 