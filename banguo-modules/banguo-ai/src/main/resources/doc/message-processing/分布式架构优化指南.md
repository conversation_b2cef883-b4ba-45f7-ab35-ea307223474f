# 般果AI分布式架构优化指南

## 会话粘滞 vs 分布式连接管理选择

### 🎯 两种方案对比

| 特性    | 会话粘滞    | 分布式连接管理 |
|-------|---------|---------|
| 实现复杂度 | 简单      | 复杂      |
| 性能    | 高       | 中等      |
| 可用性   | 依赖实例稳定性 | 高       |
| 容错性   | 低       | 高       |
| 扩展性   | 有限制     | 好       |

### 🔄 会话粘滞的局限性

1. **实例故障**：服务实例重启/崩溃时，所有SSE连接丢失
2. **网络问题**：负载均衡器故障可能导致路由失效
3. **扩缩容限制**：添加新实例时，现有连接无法迁移
4. **配置复杂性**：需要在多个层面配置粘滞策略

### 💡 推荐策略

**生产环境建议：**

- 高可用性要求：使用分布式连接管理
- 性能优先且环境稳定：使用会话粘滞
- 混合方案：会话粘滞 + 分布式连接管理作为容错

**开发/测试环境：**

- 使用简单的本地连接管理即可

## 🎯 概述

在分布式环境下部署流式响应功能时，会遇到诸多挑战。本指南详细分析了这些问题并提供了完整的解决方案。

## ⚠️ 分布式环境下的核心问题

### 1. SSE连接会话粘性问题

**问题描述**：

- SSE是长连接，用户连接到某个实例后，后续请求可能被负载均衡器路由到其他实例
- 导致连接丢失、状态不一致

**影响**：

```
用户 → LB → 实例A (建立SSE连接)
用户 → LB → 实例B (连接丢失❌)
```

**解决方案**：

1. **会话粘性负载均衡**：使用IP Hash或Session ID路由
2. **分布式连接管理**：使用Redis记录连接状态
3. **连接迁移机制**：支持跨实例连接迁移

### 2. 消息处理器状态共享

**问题描述**：

- `MessageProcessorFactory`在每个实例独立初始化
- 处理器状态不共享，可能导致不一致

**解决方案**：

- 使用Redis共享处理器状态
- 实现分布式配置管理

### 3. Nacos配置优化

**问题描述**：

- 缺少负载均衡配置
- 健康检查不完善
- 服务元数据不足

## 🛠️ 完整解决方案

### 1. 分布式SSE连接管理

#### 架构设计

```
┌─────────────────────────────────────────────────────────────┐
│                    负载均衡器 (Nginx/ALB)                    │
├─────────────────────────────────────────────────────────────┤
│  实例A(IP1:9211)  │  实例B(IP2:9211)  │  实例C(IP3:9211)   │
├─────────────────────────────────────────────────────────────┤
│                      Redis 连接注册表                       │
│  sessionId → instanceId 映射                                │
│  stream:connection:session-123 → "***********:9211"       │
└─────────────────────────────────────────────────────────────┘
```

#### 实现方案

```java
// 分布式连接管理
@Service("distributedStreamChatService")
public class DistributedStreamChatServiceImpl implements IStreamChatService {
    
    // 本地连接 + Redis注册
    private final Map<String, SseEmitter> localActiveStreams = new ConcurrentHashMap<>();
    private final RedisTemplate<String, Object> redisTemplate;
    
    @Override
    public void processStreamRequest(StreamChatRequestDTO request, SseEmitter emitter) {
        String sessionId = request.getSessionId();
        String instanceId = getLocalInstanceId();
        
        // 1. 注册到Redis
        registerConnection(sessionId, instanceId, emitter);
        
        // 2. 处理业务逻辑
        // ...
    }
    
    private void registerConnection(String sessionId, String instanceId, SseEmitter emitter) {
        // 本地注册
        localActiveStreams.put(sessionId, emitter);
        
        // Redis注册
        redisTemplate.opsForValue().set(
            "stream:connection:" + sessionId, 
            instanceId, 
            Duration.ofMinutes(6)
        );
    }
}
```

### 2. 负载均衡配置

#### Nginx配置（推荐）

```nginx
upstream banguo-ai-backend {
    # 使用IP Hash确保同一用户连接到同一实例
    ip_hash;
    
    server ***********0:9211 weight=3 max_fails=3 fail_timeout=30s;
    server ***********1:9211 weight=3 max_fails=3 fail_timeout=30s;
    server ***********2:9211 weight=3 max_fails=3 fail_timeout=30s;
}

server {
    listen 80;
    server_name api.banguo.com;
    
    location /ai/support/message/chat {
        proxy_pass http://banguo-ai-backend;
        
        # SSE特殊配置
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        
        # SSE必须禁用缓冲
        proxy_buffering off;
        proxy_cache off;
        
        # 超时配置
        proxy_connect_timeout 3s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
    }
}
```

#### Spring Cloud Gateway配置

```yaml
spring:
  cloud:
    gateway:
      routes:
        - id: banguo-ai-stream
          uri: lb://banguo-ai
          predicates:
            - Path=/ai/support/message/chat
          filters:
            - name: StickyCookie
              args:
                name: BANGUO_SESSION
```

### 3. Nacos优化配置

#### 服务注册优化

```yaml
spring:
  cloud:
    nacos:
      discovery:
        # 核心配置
        group: BANGUO_GROUP
        namespace: banguo-distributed
        cluster-name: DEFAULT
        
        # 健康检查
        heart-beat-interval: 5000  # 5秒心跳
        heart-beat-timeout: 15000  # 15秒超时
        ip-delete-timeout: 30000   # 30秒删除
        
        # 服务元数据（重要！）
        metadata:
          version: ${project.version}
          zone: ${DEPLOY_ZONE:default}
          feature.stream-chat: true
          feature.message-types: "TEXT,COMMAND,IMAGE"
          max-connections: 5000
          health-check-url: /actuator/health
          
        # 实例配置
        weight: 100
        ephemeral: true  # 临时实例
        
      config:
        # 配置中心优化
        refresh-enabled: true
        shared-configs:
          - data-id: application-common.yml
            refresh: true
          - data-id: redis.yml
            refresh: true
          - data-id: rocketmq.yml
            refresh: true
```

#### 配置管理最佳实践

```
Nacos配置层次：
├── application-common.yml        # 公共配置
├── datasource.yml               # 数据源配置
├── redis.yml                    # Redis配置
├── rocketmq.yml                 # 消息队列配置
├── stream-chat-config.yml       # 流式聊天配置
└── banguo-ai.yml               # 应用特定配置
```

### 4. Redis优化

#### 连接池配置

```yaml
spring:
  data:
    redis:
      host: ${REDIS_HOST:redis-cluster}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      timeout: 3000ms
      lettuce:
        pool:
          max-active: 50    # 最大连接数
          max-idle: 20      # 最大空闲连接数
          min-idle: 5       # 最小空闲连接数
          max-wait: 3000ms  # 最大等待时间
        shutdown-timeout: 100ms
        cluster:
          refresh:
            adaptive: true  # 自适应刷新
            period: 30s     # 刷新周期
```

#### 分布式锁实现

```java
@Component
public class DistributedLockService {
    
    private final RedisTemplate<String, Object> redisTemplate;
    
    public boolean tryLock(String key, String value, Duration expiration) {
        Boolean result = redisTemplate.opsForValue()
            .setIfAbsent(key, value, expiration);
        return Boolean.TRUE.equals(result);
    }
    
    public void unlock(String key, String value) {
        String script = "if redis.call('get', KEYS[1]) == ARGV[1] then " +
                       "return redis.call('del', KEYS[1]) else return 0 end";
        redisTemplate.execute(new DefaultRedisScript<>(script, Long.class), 
                            Collections.singletonList(key), value);
    }
}
```

### 5. 消息队列优化

#### RocketMQ配置

```yaml
rocketmq:
  name-server: ${ROCKETMQ_NAMESERVER:rocketmq-cluster:9876}
  producer:
    group: banguo-ai-producer
    send-message-timeout: 5000
    retry-times-when-send-failed: 3
    # 分布式事务
    enable-msg-trace: true
    customized-trace-topic: RMQ_SYS_TRACE_TOPIC
  consumer:
    group: banguo-ai-consumer
    consume-message-batch-max-size: 10
    max-reconsume-times: 5
    # 消费模式
    message-model: CLUSTERING  # 集群消费
```

#### 消息幂等性处理

```java
@Component
@RocketMQMessageListener(
    topic = "ai-message-topic",
    consumerGroup = "ai-message-consumer-group"
)
public class IdempotentMessageConsumer implements RocketMQListener<MessageHistoryDTO> {
    
    private final RedisTemplate<String, Object> redisTemplate;
    
    @Override
    public void onMessage(MessageHistoryDTO message) {
        String messageId = message.getMessageId();
        String lockKey = "msg:lock:" + messageId;
        
        // 分布式锁防重复消费
        if (!tryLock(lockKey, "processing", Duration.ofMinutes(5))) {
            log.warn("消息正在处理中，跳过: {}", messageId);
            return;
        }
        
        try {
            // 幂等性检查
            if (isMessageProcessed(messageId)) {
                log.info("消息已处理，跳过: {}", messageId);
                return;
            }
            
            // 处理消息
            processMessage(message);
            
            // 标记已处理
            markMessageProcessed(messageId);
            
        } finally {
            unlock(lockKey, "processing");
        }
    }
}
```

### 6. 监控和告警

#### Prometheus监控指标

```java
@Component
public class DistributedMetrics {
    
    private final MeterRegistry meterRegistry;
    
    // SSE连接指标
    private final Gauge activeConnectionsGauge;
    private final Counter connectionCreatedCounter;
    private final Counter connectionClosedCounter;
    
    // 分布式指标
    private final Gauge clusterSizeGauge;
    private final Timer messageProcessingTimer;
    
    public DistributedMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        
        this.activeConnectionsGauge = Gauge.builder("sse.connections.active")
            .description("活跃SSE连接数")
            .register(meterRegistry);
            
        this.clusterSizeGauge = Gauge.builder("cluster.size")
            .description("集群实例数量")
            .register(meterRegistry);
    }
    
    @Scheduled(fixedRate = 30000)
    public void updateMetrics() {
        // 更新指标
        activeConnectionsGauge.set(getActiveConnectionCount());
        clusterSizeGauge.set(getClusterSize());
    }
}
```

#### 健康检查端点

```java
@Component
public class DistributedHealthIndicator implements HealthIndicator {
    
    private final RedisTemplate<String, Object> redisTemplate;
    private final DiscoveryClient discoveryClient;
    
    @Override
    public Health health() {
        Health.Builder builder = new Health.Builder();
        
        try {
            // 检查Redis连接
            redisTemplate.opsForValue().get("health:check");
            builder.withDetail("redis", "UP");
            
            // 检查Nacos连接
            List<ServiceInstance> instances = discoveryClient.getInstances("banguo-ai");
            builder.withDetail("nacos", "UP")
                   .withDetail("cluster-size", instances.size());
            
            // 检查本地SSE连接
            int activeConnections = getActiveConnectionCount();
            builder.withDetail("sse-connections", activeConnections);
            
            builder.up();
            
        } catch (Exception e) {
            builder.down().withException(e);
        }
        
        return builder.build();
    }
}
```

## 🚀 部署最佳实践

### 1. 容器化部署

#### Docker配置

```dockerfile
FROM openjdk:17-jdk-alpine

# 环境变量
ENV JAVA_OPTS="-Xms2g -Xmx2g -XX:+UseG1GC"
ENV SPRING_PROFILES_ACTIVE=distributed

# 应用
COPY banguo-ai-*.jar app.jar

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:9211/actuator/health || exit 1

# 启动
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]
```

#### Kubernetes部署

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: banguo-ai
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app: banguo-ai
  template:
    metadata:
      labels:
        app: banguo-ai
    spec:
      containers:
      - name: banguo-ai
        image: banguo-ai:latest
        ports:
        - containerPort: 9211
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "distributed"
        - name: NACOS_SERVER
          value: "nacos-server:8848"
        - name: REDIS_HOST
          value: "redis-cluster"
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 9211
          initialDelaySeconds: 30
          periodSeconds: 10
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 9211
          initialDelaySeconds: 60
          periodSeconds: 30
```

### 2. 服务网格集成

#### Istio配置

```yaml
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: banguo-ai-vs
spec:
  hosts:
  - banguo-ai
  http:
  - match:
    - uri:
        prefix: /ai/support/message/chat
    route:
    - destination:
        host: banguo-ai
        subset: v1
      headers:
        request:
          add:
            x-sticky-session: "true"
    timeout: 300s
```

### 3. 性能调优

#### JVM参数优化

```bash
# 分布式环境JVM参数
JAVA_OPTS="-server \
-Xms4g -Xmx4g \
-XX:NewRatio=1 \
-XX:SurvivorRatio=8 \
-XX:+UseG1GC \
-XX:MaxGCPauseMillis=200 \
-XX:G1HeapRegionSize=16m \
-XX:+UseStringDeduplication \
-XX:+PrintGCDetails \
-XX:+PrintGCTimeStamps \
-Xloggc:gc.log \
-XX:+UseGCLogFileRotation \
-XX:NumberOfGCLogFiles=5 \
-XX:GCLogFileSize=10M \
-XX:+HeapDumpOnOutOfMemoryError \
-XX:HeapDumpPath=/tmp/heapdump.hprof \
-Dspring.profiles.active=distributed"
```

## 📊 性能指标

### 目标性能

| 指标      | 单实例     | 3实例集群   |
|---------|---------|---------|
| 并发SSE连接 | 5,000   | 15,000  |
| 响应时间    | <500ms  | <800ms  |
| 消息吞吐量   | 1,000/s | 3,000/s |
| 可用性     | 99.9%   | 99.99%  |

### 关键监控指标

1. **连接指标**：活跃连接数、连接创建/关闭速率
2. **性能指标**：响应时间、吞吐量、错误率
3. **资源指标**：CPU、内存、网络使用率
4. **分布式指标**：集群大小、实例健康状态

## 🔧 故障排查

### 常见问题

1. **SSE连接断开**
    - 检查负载均衡配置
    - 查看Redis连接状态
    - 验证实例健康状态

2. **消息丢失**
    - 检查RocketMQ连接
    - 验证消费者组配置
    - 查看重试机制

3. **性能下降**
    - 监控JVM GC
    - 检查数据库连接池
    - 分析慢查询

### 调试工具

```bash
# 检查Nacos服务状态
curl http://nacos-server:8848/nacos/v1/ns/instance/list?serviceName=banguo-ai

# 检查Redis连接
redis-cli -h redis-server -p 6379 ping

# 检查应用健康状态
curl http://instance:9211/actuator/health

# 查看SSE连接
curl -H "Accept: text/event-stream" \
  "http://instance:9211/ai/support/message/chat?sessionId=debug&openId=test&message=hello&messageType=TEXT"
```

## 📝 总结

通过以上优化方案，可以实现：

1. **高可用性**：多实例部署、故障自动恢复
2. **高性能**：负载均衡、连接池优化、缓存策略
3. **高可靠性**：分布式锁、消息幂等、事务一致性
4. **易监控**：完整的监控指标、健康检查、告警机制

分布式环境下的流式响应系统需要考虑的方面很多，但通过合理的架构设计和配置优化，可以构建出稳定、高效的分布式系统。

### 一下为优化配置

```yaml
# 分布式环境配置
# 使用: spring.profiles.active=distributed

server:
  port: 9211
  undertow:
    # 基础配置（针对分布式SSE优化）
    no-request-timeout: 300000  # 连接空闲超时5分钟
    max-http-post-size: 10485760  # 10MB
    buffer-size: 65536 # 64KB缓冲区
    direct-buffers: true
    
    # 分布式环境线程配置
    io-threads: 32  # 增加IO线程数以支持更多SSE连接
    worker-threads: 256  # 增加工作线程数
    buffer-pool-size: 4096  # 增加缓冲池大小
    
    # 分布式连接配置
    max-connections: 5000  # 支持更多并发连接
    max-parameters: 2000
    max-headers: 500
    max-header-size: 2097152
    
    # 请求解析配置
    decode-url: true
    url-charset: UTF-8
    
    # SSE特定优化
    eager-filter-init: true
    preserve-path-on-forward: true

spring:
  application:
    name: banguo-ai
  profiles:
    active: distributed
  
  # 分布式服务发现配置
  cloud:
    nacos:
      server-addr: ${NACOS_SERVER:nacos-server:8848}
      discovery:
        # 服务注册配置
        group: ${NACOS_GROUP:BANGUO_GROUP}
        namespace: ${NACOS_NAMESPACE:banguo-distributed}
        username: ${NACOS_NAME:nacos}
        password: ${NACOS_PWD:nacos}
        
        # 分布式特定配置
        cluster-name: ${NACOS_CLUSTER:DEFAULT}
        service: ${spring.application.name}
        weight: 100  # 服务权重
        healthy: true
        ephemeral: true  # 临时实例，断线即删除
        
        # 心跳配置
        heart-beat-interval: 5000  # 5秒心跳
        heart-beat-timeout: 15000  # 15秒心跳超时
        ip-delete-timeout: 30000   # 30秒IP删除超时
        
        # 元数据配置
        metadata:
          version: ${project.version:1.0.0}
          zone: ${DEPLOY_ZONE:default}
          feature.stream-chat: true  # 标识支持流式聊天
          feature.message-types: "TEXT,COMMAND,IMAGE"  # 支持的消息类型
          max-connections: 5000  # 最大连接数

      config:
        group: ${NACOS_GROUP:BANGUO_GROUP}
        namespace: ${NACOS_NAMESPACE:banguo-distributed}
        username: ${NACOS_NAME:nacos}
        password: ${NACOS_PWD:nacos}
        file-extension: yml
        refresh-enabled: true  # 启用配置刷新
        
        # 共享配置
        shared-configs:
          - data-id: application-common.yml
            group: ${NACOS_GROUP:BANGUO_GROUP}
            refresh: true
          - data-id: datasource.yml
            group: ${NACOS_GROUP:BANGUO_GROUP}
            refresh: true
          - data-id: redis.yml
            group: ${NACOS_GROUP:BANGUO_GROUP}
            refresh: true
          - data-id: rocketmq.yml
            group: ${NACOS_GROUP:BANGUO_GROUP}
            refresh: true

        # 扩展配置
        extension-configs:
          - data-id: ${spring.application.name}.yml
            group: ${NACOS_GROUP:BANGUO_GROUP}
            refresh: true
          - data-id: stream-chat-config.yml
            group: ${NACOS_GROUP:BANGUO_GROUP}
            refresh: true
    
    # 负载均衡配置
    loadbalancer:
      # 使用Nacos负载均衡器
      nacos:
        enabled: true
      # 健康检查
      health-check:
        initial-delay: 10s
        interval: 30s

    # 服务调用配置
    openfeign:
      client:
        config:
          default:
            connect-timeout: 3000
            read-timeout: 10000
      compression:
        request:
          enabled: true
        response:
          enabled: true

# Redis配置（分布式缓存）
spring:
  data:
    redis:
      host: ${REDIS_HOST:redis-server}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      database: ${REDIS_DB:0}
      timeout: 3000ms
      lettuce:
        pool:
          max-active: 50    # 连接池最大连接数
          max-idle: 20      # 连接池最大空闲连接数
          min-idle: 5       # 连接池最小空闲连接数
          max-wait: 3000ms  # 连接池最大阻塞等待时间
        shutdown-timeout: 100ms
        cluster:
          refresh:
            adaptive: true
            period: 30s

# 分布式消息队列配置
rocketmq:
  name-server: ${ROCKETMQ_NAMESERVER:rocketmq-nameserver:9876}
  producer:
    group: ${spring.application.name}-producer
    send-message-timeout: 5000
    compress-message-body-threshold: 4096
    max-message-size: 4194304
    retry-times-when-send-failed: 3
    retry-times-when-send-async-failed: 3
    # 分布式事务配置
    enable-msg-trace: true
    customized-trace-topic: RMQ_SYS_TRACE_TOPIC
  consumer:
    group: ${spring.application.name}-consumer
    consume-message-batch-max-size: 10
    consume-timeout: 30
    max-reconsume-times: 5

# AI服务配置
spring:
  ai:
    mcp:
      server:
        name: banguo-mcp-server
        version: ${project.version:1.0.0}
        type: ASYNC
        sse-endpoint: /ai/mcp/sse
        sse-message-endpoint: /ai/mcp/message
        capabilities:
          tool: true
          resource: true
          prompt: true
          completion: true
        stdio: false
    alibaba:
      mcp:
        nacos:
          namespace: ${NACOS_NAMESPACE:banguo-distributed}
          enabled: true
          server-addr: ${NACOS_SERVER:nacos-server:8848}
          username: ${NACOS_NAME:nacos}
          password: ${NACOS_PWD:nacos}
          registry:
            enabled: true
            service-group: ${NACOS_GROUP:BANGUO_GROUP}

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,prometheus,nacos
  endpoint:
    health:
      show-details: always
      show-components: always
  health:
    nacos:
      enabled: true
    redis:
      enabled: true
  metrics:
    tags:
      application: ${spring.application.name}
      instance: ${spring.cloud.client.hostname:localhost}:${server.port}
    export:
      prometheus:
        enabled: true

# 日志配置
logging:
  level:
    cn.xianlink.ai: DEBUG
    com.alibaba.nacos: INFO
    org.springframework.cloud: INFO
    org.springframework.web: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"

# 分布式链路追踪配置
spring:
  sleuth:
    sampler:
      probability: 1.0  # 采样率100%（生产环境建议0.1）
    zipkin:
      base-url: ${ZIPKIN_URL:http://zipkin-server:9411}

# 业务配置
banguo:
  ai:
    stream:
      # 连接管理模式：distributed（分布式）
      connection-mode: distributed
      
      # SSE连接配置
      sse:
        heartbeat-interval: 30        # 心跳间隔（秒）
        connection-timeout: 300       # 连接超时时间（秒）
        max-connections: 5000         # 最大连接数（分布式单实例）
        buffer-size: 8192            # 缓冲区大小
      
      # 分布式配置
      distributed:
        redis-key-prefix: "stream:connection:"
        cleanup-interval: 60         # 连接清理间隔（秒）
        instance-id: ${HOSTNAME:${spring.application.name}-${server.port}}
        enable-cross-instance: true  # 启用跨实例通信
      
      # 分布式SSE配置（保持兼容性）
      max-connections-per-instance: 5000
      connection-timeout: 300000  # 5分钟
      heartbeat-interval: 30000   # 30秒心跳
      
      # Redis配置
      redis:
        key-prefix: "banguo:ai:stream:"
        connection-ttl: 360000  # 6分钟TTL

      # 负载均衡配置
      load-balancer:
        strategy: weighted-round-robin  # 加权轮询
        health-check: true

    # 消息处理配置
    message:
      processors:
        text:
          enabled: true
          max-concurrent: 100
        command:
          enabled: true
          max-concurrent: 50
        image:
          enabled: true
          max-concurrent: 20

    # 监控配置
    monitoring:
      metrics:
        enabled: true
        interval: 30s
      alerts:
        enabled: true
        thresholds:
          error-rate: 0.05  # 5%错误率告警
          response-time: 5000  # 5秒响应时间告警 
```