# 消息会话总结功能使用说明

## 功能概述

消息会话总结功能通过AI技术自动分析客服对话内容，提取核心问题和处理结果，为每个会话生成简洁的标题和描述。该功能主要通过定时任务自动执行，无需人工干预。

## 功能特性

- ✅ **自动化处理**：定时任务自动扫描需要总结的会话
- ✅ **AI智能分析**：使用百练大语言模型分析对话内容
- ✅ **结构化输出**：生成标准化的标题和描述
- ✅ **批量处理**：支持一次处理多个会话，提高效率
- ✅ **失败重试**：内置错误处理和日志记录
- ✅ **长度控制**：自动控制标题（≤15字）和描述（≤80字）长度

## 系统架构

```
job定时任务 → Dubbo调用 → AI服务 → 百练API → 数据库更新
     ↓              ↓           ↓          ↓
  参数解析      远程服务    AI模型处理   会话表更新
  错误处理      接口调用    结果解析     日志记录
```

## 核心组件

### 1. 定时任务模块 (banguo-job)

**文件位置**: `banguo-modules/banguo-job/src/main/java/cn/xianlink/job/processors/ai/MessageSessionSummaryJob.java`

**任务名称**:

- `messageSessionSummaryJob` - 消息会话总结任务

**主要改进**:

- 使用Dubbo远程服务调用，替代HTTP调用
- 统一的日志记录和参数处理
- 更好的异常处理和错误信息记录
- 符合现有项目的代码风格

### 2. AI服务模块 (banguo-ai)

**核心服务**: `MessageSessionSummaryServiceImpl`

- 查询待总结会话
- 构建对话历史文本
- 调用AI API生成总结
- 解析AI返回结果
- 更新数据库记录

**远程服务接口**: `RemoteMessageSessionSummaryService`

- `autoSummarizePendingSessions(Integer limit)` - 自动处理未总结的会话
- `summarizeSession(String sessionId)` - 总结单个会话
- `batchSummarizeSessions(List<String> sessionIds)` - 批量总结多个会话

### 3. 提示词模版

**文件位置**: `banguo-modules/banguo-ai/src/main/resources/prompt/总结客服会话历史-提示词模版3.md`

**模版特点**:

- 明确的任务目标和输出要求
- 标准化的JSON格式输出
- 严格的长度和格式约束

## 使用方式

### 1. 定时任务配置

在XXL-JOB管理后台配置定时任务：

#### 消息会话总结任务

```
任务名称：messageSessionSummaryJob
执行器：banguo-job
任务描述：自动总结客服会话历史
Cron表达式：0 0 2 * * ?  # 每天凌晨2点执行
任务参数：
{
  "limit": 50,
  "desc": "自动总结客服会话历史，提取核心问题和处理结果"
}
```

### 2. 依赖配置

确保job模块包含AI API依赖：

```xml
<dependency>
    <groupId>cn.xianlink</groupId>
    <artifactId>banguo-api-ai</artifactId>
</dependency>
```

### 3. 手动触发

可以在XXL-JOB管理后台手动执行任务：

1. 找到对应的任务
2. 点击"执行一次"
3. 输入自定义参数（可选）

## 处理逻辑

### 1. 会话筛选条件

系统会查询满足以下条件的会话进行总结：

```sql
-- 标题或描述为空的会话
(title IS NULL OR title = '' OR description IS NULL OR description = '')
AND
-- 已结束的会话 或 消息数量>=3的会话
(session_status = 3 OR message_count >= 3)
ORDER BY last_message_time DESC
```

### 2. 对话文本格式化

将消息历史转换为易于AI理解的文本格式：

```
用户：你好，我的订单什么时候能发货？
AI助手：您好！请提供您的订单号，我来帮您查询发货状态。
用户：订单号是BG202501150001
AI助手：经查询，您的订单已在今天上午发货，预计明天下午送达。
```

### 3. AI总结示例

**输入对话**：用户咨询订单发货时间，提供订单号后确认已发货

**AI输出**：

```json
{
  "title": "订单发货时间查询",
  "desc": "用户咨询订单BG202501150001发货状态，确认已于当日发货，预计次日送达"
}
```

## 监控和日志

### 1. 任务执行日志

在XXL-JOB管理后台可以查看：

- 任务执行状态
- 处理数量统计
- 详细的执行日志
- 错误信息和堆栈

### 2. 应用日志

在banguo-ai和banguo-job的应用日志中会记录：

- Dubbo远程服务调用详情
- AI API调用详情
- 数据库操作结果
- 异常处理过程
- 性能统计信息

### 3. 关键日志标识

使用以下关键字搜索相关日志：

- `messageSessionSummaryJob` - 主任务日志
- `MessageSessionSummaryServiceImpl` - 服务层日志
- `RemoteMessageSessionSummaryService` - 远程服务调用日志

## 故障排查

### 1. 常见问题

#### Dubbo服务调用失败

- 检查服务提供者状态
- 确认服务注册与发现
- 查看Dubbo连接状态

#### AI API调用失败

- 检查百练API Key配置
- 确认API配额和限制
- 查看AI服务日志

#### 数据库更新失败

- 检查数据库连接
- 确认表结构和权限
- 查看具体错误信息

### 2. 调试步骤

1. **验证远程服务可用性**
    - 检查Dubbo服务注册状态
    - 确认AI服务模块正常运行

2. **手动触发总结**
    - 在XXL-JOB管理后台手动执行
    - 调整limit参数进行小批量测试

3. **查看数据库状态**
   ```sql
   -- 查询待总结的会话
   SELECT session_id, title, description, message_count, session_status 
   FROM support_message_session 
   WHERE (title IS NULL OR title = '' OR description IS NULL OR description = '')
   LIMIT 10;
   ```

## 性能优化

### 1. 批量处理建议

- 建议每次处理50-100个会话
- 避免一次处理过多导致超时
- 可以根据服务器性能调整limit参数

### 2. 执行时间建议

- 选择业务低峰期执行（如凌晨2-6点）
- 避免与其他重要任务冲突
- 根据数据量调整执行频率

### 3. 资源监控

- 监控AI API调用配额使用情况
- 关注数据库连接池状态
- 监控任务执行时间和成功率
- 监控Dubbo服务调用情况

## 技术架构优势

### 1. 使用Dubbo远程调用的优势

- **高性能**：基于NIO的高性能通信
- **负载均衡**：自动负载均衡和容错
- **服务治理**：完善的服务注册与发现
- **统一接口**：标准化的服务接口定义

### 2. 代码风格统一

- 遵循现有项目的代码规范
- 统一的异常处理和日志记录
- 标准化的参数解析和验证
- 一致的服务调用模式

## 扩展功能

### 1. 可配置的总结策略

未来可以支持：

- 不同类型会话的不同总结策略
- 可配置的筛选条件
- 自定义提示词模版

### 2. 实时总结

可以扩展支持：

- 会话结束时自动总结
- 基于消息数量触发总结
- 支持手动触发总结

### 3. 多语言支持

可以扩展支持：

- 不同语言的提示词模版
- 多语言对话内容处理
- 国际化的总结结果

## 注意事项

1. **数据安全**：确保客服对话数据的安全性和隐私保护
2. **成本控制**：合理控制AI API的调用频率和数量
3. **质量监控**：定期检查总结结果的质量和准确性
4. **备份机制**：重要数据需要有备份和恢复机制
5. **版本兼容**：升级时注意接口和数据结构的兼容性
6. **服务依赖**：确保AI服务模块正常运行，避免服务依赖问题

## 更新日志

- **v1.1.0** (2025-01-15)
    - 重构代码架构，使用Dubbo远程服务调用
    - 统一代码风格，符合现有项目规范
    - 改进异常处理和日志记录
    - 简化任务配置，移除健康检查任务
    - 优化性能和可维护性

- **v1.0.0** (2025-01-15)
    - 初始版本发布
    - 支持基本的会话总结功能
    - 集成百练AI API
    - 实现定时任务处理机制 