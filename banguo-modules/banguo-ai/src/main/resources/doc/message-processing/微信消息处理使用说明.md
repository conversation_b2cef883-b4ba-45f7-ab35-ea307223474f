# 客服消息处理使用说明

## 概述

本模块提供了完整的客服消息处理功能，支持将微信发送的JSON消息转换为数据库实体并保存。

## 架构设计

### 数据流转过程

```
微信JSON消息 → TextMessageDTO/ImageMessageDTO → MessageHistoryDTO → MessageHistory(数据库实体)
```

### 核心组件

1. **TextMessageDTO** - 微信文本消息DTO
2. **ImageMessageDTO** - 微信图片消息DTO
3. **SupportMessageConverter** - 消息转换器
4. **ISupportMessageService** - 客服消息服务接口
5. **SupportMessageServiceImpl** - 客服消息服务实现
6. **SupportMessageController** - 客服消息控制器

## 使用方法

### 1. 直接调用服务

```java
@Autowired
private ISupportMessageService supportMessageService;

// 处理客服消息
String jsonMessage = "{\"ToUserName\":\"gh_ec67c6255831\",\"FromUserName\":\"oP8WG6yS93lWss_VPGVkSnIz1V9Y\",\"CreateTime\":1751683444,\"MsgType\":\"text\",\"Content\":\"你好\",\"MsgId\":25078074412367540}";
String sessionId = "session_123456";
Boolean result = supportMessageService.processMessage(jsonMessage, sessionId);
```

### 2. 使用转换器

```java
@Autowired
private SupportMessageConverter supportMessageConverter;

// 转换为MessageHistoryDTO
MessageHistoryDTO dto = supportMessageConverter.convertToMessageHistoryDTO(jsonMessage, sessionId);

// 保存到数据库
@Autowired
private IMessageHistoryService messageHistoryService;
Boolean result = messageHistoryService.create(dto);
```

### 3. HTTP接口调用

#### 处理客服消息

```http
POST /ai/support/wechat-message/process?sessionId=session_123456
Content-Type: application/json

{
    "ToUserName": "gh_ec67c6255831",
    "FromUserName": "oP8WG6yS93lWss_VPGVkSnIz1V9Y",
    "CreateTime": 1751683444,
    "MsgType": "text",
    "Content": "你好",
    "MsgId": 25078074412367540
}
```

#### 测试文本消息

```http
POST /ai/support/wechat-message/test-text?sessionId=session_123456
```

#### 测试图片消息

```http
POST /ai/support/wechat-message/test-image?sessionId=session_123456
```

## 支持的消息类型

### 文本消息 (text)

```json
{
    "ToUserName": "gh_ec67c6255831",
    "FromUserName": "oP8WG6yS93lWss_VPGVkSnIz1V9Y",
    "CreateTime": 1751683444,
    "MsgType": "text",
    "Content": "你好",
    "MsgId": 25078074412367540
}
```

### 图片消息 (image)

```json
{
    "ToUserName": "gh_ec67c6255831",
    "FromUserName": "oP8WG6yS93lWss_VPGVkSnIz1V9Y",
    "CreateTime": 1751683559,
    "MsgType": "image",
    "PicUrl": "http://mmbiz.qpic.cn/mmbiz_jpg/xxx/0",
    "MsgId": 25078078363203658,
    "MediaId": "AGsUxkUROHvmxKQegyxK8FEcgFLk9O2qiQefUJm6BxCCNxlamO9xAb6bCiI5yF3O"
}
```

## 字段映射关系

| 微信字段         | DTO字段        | 数据库字段            | 说明     |
|--------------|--------------|------------------|--------|
| ToUserName   | ToUserName   | to_user_name     | 开发者微信号 |
| FromUserName | FromUserName | from_user_name   | 发送方微信号 |
| CreateTime   | CreateTime   | create_timestamp | 消息创建时间 |
| MsgType      | MsgType      | msg_type         | 消息类型   |
| Content      | Content      | content          | 文本消息内容 |
| PicUrl       | PicUrl       | pic_url          | 图片链接   |
| MediaId      | MediaId      | media_id         | 媒体文件ID |
| MsgId        | MsgId        | msg_id           | 消息ID   |

## 扩展说明

### 添加新的消息类型

1. 创建新的DTO类（如VoiceMessageDTO）
2. 在SupportMessageConverter中添加解析方法
3. 添加转换方法
4. 更新convertToMessageHistoryDTO方法

### 示例：添加语音消息支持

```java
// 1. 创建VoiceMessageDTO
@Data
public class VoiceMessageDTO implements Serializable {
    private String ToUserName;
    private String FromUserName;
    private Long CreateTime;
    private String MsgType;
    private String MediaId;
    private String Format;
    private String Recognition;
    private Long MsgId;
}

// 2. 在SupportMessageConverter中添加解析方法
private VoiceMessageDTO parseVoiceMessage(String jsonMessage) {
    try {
        VoiceMessageDTO voiceMessage = objectMapper.readValue(jsonMessage, VoiceMessageDTO.class);
        if ("voice".equals(voiceMessage.getMsgType())) {
            return voiceMessage;
        }
        return null;
    } catch (Exception e) {
        return null;
    }
}

// 3. 添加转换方法
private MessageHistoryDTO convertVoiceMessageToDTO(VoiceMessageDTO voiceMessage, String sessionId) {
    MessageHistoryDTO dto = new MessageHistoryDTO();
    // 设置字段...
    dto.setMsgType("voice");
    return dto;
}
```

## 错误处理

- 无法解析的消息类型会记录错误日志并返回null
- 数据库保存失败会记录错误日志并返回false
- 所有异常都会被捕获并记录详细日志

## 性能优化

- 使用ObjectMapper进行JSON解析，性能较好
- 使用BeanCopyUtil进行对象转换，避免手动设置字段
- 批量处理时可以考虑使用批量插入接口 