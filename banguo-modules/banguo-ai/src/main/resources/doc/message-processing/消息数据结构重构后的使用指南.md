# 消息数据结构使用指南

## 概述

系统采用类型化的 `data` 字段，根据不同的 `messageType` 提供了不同的数据结构。这使得消息数据更加类型安全，并且支持更丰富的消息内容。

## 支持的消息类型

### 1. 文本消息 (TEXT)
```json
{
  "messageType": "text",
  "data": {
    "content": "这是一条文本消息",
    "type": "text"
  }
}
```

### 2. 图片消息 (IMAGE)
```json
{
  "messageType": "image",
  "data": {
    "url": "https://example.com/image.jpg",
    "width": 800,
    "height": 600,
    "format": "jpg",
    "type": "image"
  }
}
```

### 3. 链接消息 (LINK)
```json
{
  "messageType": "link",
  "data": {
    "url": "https://example.com",
    "title": "链接标题",
    "description": "链接描述",
    "thumbnail": "https://example.com/thumb.jpg",
    "type": "link"
  }
}
```

### 4. 命令消息 (COMMAND)
```json
{
  "messageType": "command",
  "data": {
    "command": "help",
    "arguments": "参数",
    "params": {"key": "value"},
    "type": "command"
  }
}
```

## 使用方法

### 1. 创建不同类型的消息

#### 文本消息
```java
// 使用便捷方法
StreamChatRequestDTO request = new StreamChatRequestDTO()
    .setTextData("这是一条文本消息");

// 或者直接设置data
StreamChatRequestDTO request = new StreamChatRequestDTO()
    .setMessageType(MessageType.TEXT)
    .setData(TextData.of("这是一条文本消息"));
```

#### 图片消息
```java
StreamChatRequestDTO request = new StreamChatRequestDTO()
    .setImageData("https://example.com/image.jpg");

// 带详细信息
StreamChatRequestDTO request = new StreamChatRequestDTO()
    .setMessageType(MessageType.IMAGE)
    .setData(ImageData.of("https://example.com/image.jpg", 800, 600, "jpg"));
```

#### 链接消息
```java
StreamChatRequestDTO request = new StreamChatRequestDTO()
    .setLinkData("https://example.com");

// 带详细信息
StreamChatRequestDTO request = new StreamChatRequestDTO()
    .setMessageType(MessageType.LINK)
    .setData(LinkData.of("https://example.com", "标题", "描述"));
```

#### 命令消息
```java
StreamChatRequestDTO request = new StreamChatRequestDTO()
    .setCommandData("help", "参数");
```

### 2. 处理消息历史记录

#### 使用新的类型化DTO
```java
@Autowired
private IMessageHistoryEnhancedService enhancedService;

// 创建文本消息历史记录
TextMessageHistoryDTO textMessage = TextMessageHistoryDTO.of("消息内容")
    .setUserId(userId)
    .setSessionId(sessionId)
    .setRole("user");

BaseMessageHistoryDTO saved = enhancedService.createTyped(textMessage);

// 查询类型化消息
BaseMessageHistoryDTO message = enhancedService.getTypedByMessageId(messageId);
if (message instanceof TextMessageHistoryDTO) {
    String content = ((TextMessageHistoryDTO) message).getTextContent();
}
```

### 3. 处理不同消息类型

```java
MessageData data = chatRequest.getData();
if (data instanceof TextData) {
    String content = ((TextData) data).getContent();
    // 处理文本消息
} else if (data instanceof ImageData) {
    String url = ((ImageData) data).getUrl();
    // 处理图片消息
} else if (data instanceof LinkData) {
    String url = ((LinkData) data).getUrl();
    String title = ((LinkData) data).getTitle();
    // 处理链接消息
} else if (data instanceof CommandData) {
    String command = ((CommandData) data).getCommand();
    String arguments = ((CommandData) data).getArguments();
    // 处理命令消息
}
```

## 最佳实践

### 1. 使用类型安全的方法
```java
// 推荐
request.setTextData("消息内容");
request.setImageData("https://example.com/image.jpg");
request.setLinkData("https://example.com");
request.setCommandData("help", "参数");
```

### 2. 验证数据
```java
@Autowired
private IMessageHistoryEnhancedService enhancedService;

if (enhancedService.validateMessageData(messageHistory)) {
    // 数据有效
}

// 或者直接验证data
if (request.isDataValid()) {
    // 数据有效
}
```

### 3. 使用工厂方法
```java
// 推荐使用静态工厂方法
TextData textData = TextData.of("内容");
ImageData imageData = ImageData.of("url");
LinkData linkData = LinkData.of("url", "标题", "描述");
CommandData commandData = CommandData.of("command", "arguments");
```

### 4. 便捷访问方法
```java
// 对于消息历史记录DTO，使用便捷方法
TextMessageHistoryDTO textMessage = ...;
String content = textMessage.getTextContent();
textMessage.setTextContent("新内容");

ImageMessageHistoryDTO imageMessage = ...;
String url = imageMessage.getImageUrl();
imageMessage.setImageUrl("新URL");
```

## 扩展新的消息类型

### 1. 创建新的数据类
```java
@JsonTypeName("newtype")
public class NewTypeData implements MessageData {
    private String newField;
    
    @Override
    public boolean isValid() {
        return newField != null && !newField.isEmpty();
    }
    
    public static NewTypeData of(String newField) {
        NewTypeData data = new NewTypeData();
        data.setNewField(newField);
        return data;
    }
}
```

### 2. 创建对应的历史记录DTO
```java
public class NewTypeMessageHistoryDTO extends BaseMessageHistoryDTO {
    @JsonProperty("data")
    private NewTypeData data;

    @Override
    public MessageData getData() {
        return data;
    }

    @Override
    public void setData(MessageData data) {
        if (data instanceof NewTypeData) {
            this.data = (NewTypeData) data;
        }
    }
}
```

### 3. 创建转换器
```java
@Component
public class NewTypeMessageDataConverter implements MessageDataConverter {
    @Override
    public MessageType getSupportedType() {
        return MessageType.NEW_TYPE;
    }

    @Override
    public boolean validate(MessageData data) {
        return data instanceof NewTypeData && data.isValid();
    }

    @Override
    public BaseMessageHistoryDTO createEmpty() {
        NewTypeMessageHistoryDTO dto = new NewTypeMessageHistoryDTO();
        dto.setMessageType("newtype");
        return dto;
    }
}
```

## 注意事项

1. **类型检查**：使用 `instanceof` 检查数据类型
2. **空值检查**：始终检查 `data` 是否为 `null`
3. **验证**：使用 `isValid()` 方法验证数据完整性
4. **JSON序列化**：数据类支持多态JSON序列化

## 错误处理

```java
try {
    BaseMessageHistoryDTO message = enhancedService.createTyped(messageHistory);
    if (message == null) {
        log.error("创建消息失败");
        return;
    }
} catch (Exception e) {
    log.error("处理消息异常", e);
}
```

## 总结

新的数据结构提供了：
- 更好的类型安全性
- 更丰富的消息内容支持
- 清晰的数据模型
- 优雅的设计模式应用
- 易于扩展的架构

通过使用策略模式、工厂模式和模板方法模式，整个架构保持了代码的优雅性和可维护性。 