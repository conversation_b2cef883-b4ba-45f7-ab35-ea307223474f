# 微信客服消息发送功能使用说明

## 概述

本功能实现了向微信小程序用户发送客服消息的能力，集成在AI智能客服系统中，当AI生成回复后自动向用户发送消息。

客服消息

```
https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/customer-message/customer-message.html
```

发送客服消息

```
https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/kf-mgnt/kf-message/sendCustomMessage.html
```

开源库

```
https://github.com/binarywang/WxJava
```

## 🎯 功能特性

- ✅ 支持发送文本类型的客服消息
- ✅ 集成在AI消息处理流程中
- ✅ 提供独立的测试接口
- ✅ **支持多小程序配置管理**
- ✅ **多appId单例模式服务管理**
- ✅ **配置抽离到专门的配置类**
- ✅ 完整的错误处理和日志记录
- ✅ 服务缓存管理API

## 🏗️ 架构说明

### 核心组件

1. **WxMaConfiguration** - 微信小程序配置类，支持多小程序配置
2. **WxMaServiceManager** - 微信小程序服务管理器，单例模式管理多个服务实例
3. **IWxMessageService** - 微信消息服务接口
4. **WxMessageServiceImpl** - 微信消息服务实现，使用配置管理器
5. **WxMessageTestController** - 测试控制器，包含服务管理API
6. **SupportMessageServiceImpl** - 集成AI消息处理流程

### 消息流程

```
用户发送消息 → AI处理生成回复 → 保存消息记录 → 发送微信客服消息 → 用户收到回复
```

### 服务管理架构

```
配置文件 → WxMaConfiguration → WxMaServiceManager → WxMaService实例缓存 → 消息发送
```

## 配置说明

### 环境变量配置

在环境变量或配置中心设置以下参数：

```bash
# 微信小程序配置
WX_MINIAPP_APPID=你的小程序APPID
WX_MINIAPP_SECRET=你的小程序SECRET
WX_MINIAPP_TOKEN=你的消息推送TOKEN
WX_MINIAPP_AESKEY=你的消息推送AESKEY
```

### application.yml配置

```yaml
# 微信小程序配置
wx:
  miniapp:
    # 默认小程序配置
    appid: ${WX_MINIAPP_APPID:}
    secret: ${WX_MINIAPP_SECRET:}
    token: ${WX_MINIAPP_TOKEN:}
    aesKey: ${WX_MINIAPP_AESKEY:}
    
    # 多个小程序配置（如果需要支持多个小程序）
    configs:
      - appid: ${WX_MINIAPP_APPID:}
        secret: ${WX_MINIAPP_SECRET:}
        token: ${WX_MINIAPP_TOKEN:}
        aesKey: ${WX_MINIAPP_AESKEY:}
        name: "默认小程序"
      # 示例：添加第二个小程序配置
      # - appid: ${WX_MINIAPP_APPID_2:}
      #   secret: ${WX_MINIAPP_SECRET_2:}
      #   token: ${WX_MINIAPP_TOKEN_2:}
      #   aesKey: ${WX_MINIAPP_AESKEY_2:}
      #   name: "第二个小程序"
```

## 依赖配置

### Maven依赖

已在 `banguo-ai/pom.xml` 中添加了以下依赖：

```xml
<!-- 微信小程序客服消息 -->
<dependency>
    <groupId>com.github.binarywang</groupId>
    <artifactId>weixin-java-miniapp</artifactId>
    <version>4.6.0</version>
</dependency>
```

## 使用方法

### 1. 自动消息发送（AI集成）

当用户向智能客服发送消息时，系统会自动：

1. 接收用户消息
2. 调用AI生成回复
3. 保存消息记录
4. **自动发送回复消息给用户**

无需手动干预，AI回复会自动推送给用户。

### 2. 手动测试接口

#### 发送文本消息

```http
POST /ai/support/wx/send-text
Content-Type: application/x-www-form-urlencoded

openId=用户的OpenId&content=测试消息内容
```

#### 发送客服消息（支持不同类型）

```http
POST /ai/support/wx/send-kefu
Content-Type: application/x-www-form-urlencoded

openId=用户的OpenId&content=消息内容&msgType=text
```

#### 发送测试回复

```http
POST /ai/support/wx/send-test-reply
Content-Type: application/x-www-form-urlencoded

openId=用户的OpenId
```

### 3. 程序化调用

```java
@Autowired
private IWxMessageService wxMessageService;

// 发送文本消息
Boolean result = wxMessageService.sendTextMessage(appId, openId, "消息内容");

// 发送指定类型消息
Boolean result = wxMessageService.sendKefuMessage(appId, openId, "消息内容", "text");
```

## API接口说明

### 发送文本消息

- **URL**: `POST /ai/support/wx/send-text`
- **参数**:
    - `appId` (可选): 小程序AppId，不传则使用默认配置
    - `openId` (必需): 用户OpenId
    - `content` (必需): 消息内容
- **返回**: 发送结果

### 发送客服消息

- **URL**: `POST /ai/support/wx/send-kefu`
- **参数**:
    - `appId` (可选): 小程序AppId
    - `openId` (必需): 用户OpenId
    - `content` (必需): 消息内容
    - `msgType` (默认text): 消息类型
- **返回**: 发送结果

### 发送测试回复

- **URL**: `POST /ai/support/wx/send-test-reply`
- **参数**:
    - `openId` (必需): 用户OpenId
- **返回**: 发送结果

## 错误处理

### 常见错误及解决方案

1. **配置错误**
    - 检查appId、secret等配置是否正确
    - 确认环境变量是否设置

2. **权限错误**
    - 确认小程序已开启客服消息功能
    - 检查API权限设置

3. **网络错误**
    - 检查网络连接
    - 确认微信API是否可访问

### 错误日志

系统会记录详细的错误日志：

```
发送客服消息失败，微信API异常，appId: xxx, openId: xxx, 错误码: 40001, 错误信息: invalid credential
```

## 监控和日志

### 日志级别配置

```yaml
logging:
  level:
    cn.xianlink.ai.service.impl.support.WechatMessageServiceImpl: DEBUG
```

### 关键日志

- 发送成功：`发送客服消息成功，appId: {}, openId: {}, content: {}`
- 发送失败：`发送客服消息失败，微信API异常...`
- 配置错误：`获取微信小程序服务失败，appId: {}`

## 性能考虑

1. **异步处理**: 消息发送失败不会影响AI处理流程
2. **重试机制**: 可以根据需要添加重试逻辑
3. **限流控制**: 微信API有调用频率限制，需要注意控制

## 安全考虑

1. **配置安全**: 敏感配置通过环境变量传入
2. **参数验证**: 对输入参数进行基本验证
3. **错误处理**: 避免在日志中暴露敏感信息

## 扩展功能

### 支持更多消息类型

可以扩展支持图片、视频等类型的消息：

```java
// 发送图片消息
public Boolean sendImageMessage(String appId, String openId, String mediaId) {
    return sendKefuMessage(appId, openId, mediaId, "image");
}
```

### 多小程序支持

可以根据业务需求支持多个小程序：

```java
// 根据业务场景选择不同的小程序配置
private String getAppIdByScene(String scene) {
    // 实现业务逻辑
    return appId;
}
```

## 相关文档

- [微信小程序客服消息API](https://developers.weixin.qq.com/miniprogram/dev/api-backend/open-api/customer-service/customerServiceMessage.send.html)
- [WxJava开发文档](https://github.com/binarywang/WxJava)

## 更新日志

### v1.0.0

- 实现基本的文本消息发送功能
- 集成到AI消息处理流程
- 提供测试接口
- 完善错误处理和日志记录

### v2.0.0 - 架构重构版本 🎉

- **🏗️ 架构优化**：
    - ✅ 配置抽离到专门的配置类（`WxMaConfiguration`）
    - ✅ 实现多appId单例模式服务管理（`WxMaServiceManager`）
    - ✅ 支持多小程序配置管理
    - ✅ 使用双重检查锁定模式确保线程安全

- **🔧 功能增强**：
    - ✅ 新增服务缓存管理API
    - ✅ 支持动态配置多个小程序
    - ✅ 优化服务实例创建和管理
    - ✅ 完善错误处理和日志记录

- **📝 文档完善**：
    - ✅ 更新配置说明，支持多小程序配置
    - ✅ 添加最佳实践指导
    - ✅ 完善API接口文档
    - ✅ 增加服务管理相关说明

- **🚀 性能提升**：
    - ✅ 单例模式避免重复创建服务实例
    - ✅ 服务缓存提升响应速度
    - ✅ 优化内存使用和并发处理

```mermaid
graph TD
    A["receiveMessage<br/>消息接收入口"] --> B["保存消息到数据库"]
    B --> C["发送到MQ队列"]
    C --> D["processMessage<br/>消息分发器"]
    D --> E{"判断消息类型"}
    E -->|"text"| F["handleTextMessage<br/>文本消息处理器"]
    E -->|"image"| G["handleImageMessage<br/>图片消息处理器"]
    E -->|"其他类型"| H["handleUnsupportedMessage<br/>不支持消息处理器"]
    
    F --> F1["发送处理中提示"]
    F1 --> F2["获取/创建会话"]
    F2 --> F3["调用AI服务"]
    F3 --> F4["保存AI回复"]
    F4 --> F5["发送回复给用户"]
    
    G --> G1["生成sessionId"]
    G1 --> G2["返回不支持图片提示"]
    
    H --> H1["生成sessionId"]
    H1 --> H2["返回格式不支持提示"]
    
    F5 --> I["标记处理完成"]
    G2 --> I
    H2 --> I
    
    style A fill:#e1f5fe
    style D fill:#f3e5f5
    style F fill:#e8f5e8
    style G fill:#fff3e0
    style H fill:#ffebee
```

### 推荐升级原因

1. **更好的架构设计**：配置与业务逻辑分离，代码更规整
2. **支持多小程序**：可以同时管理多个小程序的消息发送
3. **性能优化**：单例模式和缓存机制提升性能
4. **运维友好**：提供服务管理API，便于运维监控 