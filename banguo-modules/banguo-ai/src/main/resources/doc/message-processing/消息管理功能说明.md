# 消息管理功能说明

## 概述

消息管理功能是基于客服消息系统的消息会话和消息记录管理模块，提供了完整的 CRUD 操作和业务功能。

## 功能特性

### 消息会话管理 (MessageSession)

- ✅ **会话创建**: 支持创建新的消息会话
- ✅ **会话查询**: 支持多种条件查询会话信息
- ✅ **会话更新**: 支持更新会话状态、最后消息时间等
- ✅ **会话删除**: 支持单个和批量删除会话
- ✅ **状态管理**: 支持会话状态管理（活跃、暂停、结束）
- ✅ **消息计数**: 自动维护会话中的消息数量

### 消息记录管理 (MessageHistory)

- ✅ **消息记录**: 支持记录各种类型的消息（文本、图片、语音、视频等）
- ✅ **消息查询**: 支持按会话ID、消息ID、发送方等条件查询
- ✅ **消息状态**: 支持消息发送状态和回复状态管理
- ✅ **消息类型**: 支持多种消息类型（text、image、voice、video、location、link）
- ✅ **媒体文件**: 支持图片链接和媒体文件ID管理

## 数据库设计

### 消息会话表 (message_session)

| 字段名               | 类型           | 说明                  |
|-------------------|--------------|---------------------|
| id                | BIGINT       | 主键ID                |
| session_id        | VARCHAR(100) | 会话ID                |
| open_id           | VARCHAR(100) | 用户open_id           |
| customer_id       | VARCHAR(100) | 客户id                |
| nickname          | VARCHAR(100) | 用户昵称                |
| phone             | VARCHAR(100) | 用户手机号               |
| title             | VARCHAR(100) | 标题                  |
| description       | TEXT         | 操作描述                |
| session_status    | TINYINT      | 会话状态：1-活跃，2-暂停，3-结束 |
| last_message_time | BIGINT       | 最后消息时间戳             |
| message_count     | INT          | 消息数量                |

### 消息记录表 (message_history)

| 字段名              | 类型           | 说明                                                                          |
|------------------|--------------|-----------------------------------------------------------------------------|
| id               | BIGINT       | 主键ID                                                                        |
| session_id       | VARCHAR(32)  | 会话ID                                                                        |
| msg_id           | VARCHAR(32)  | 消息ID                                                                        |
| to_user_name     | VARCHAR(32)  | 接收方微信号                                                                      |
| from_user_name   | VARCHAR(32)  | 发送方微信号                                                                      |
| create_timestamp | BIGINT       | 消息创建时间戳                                                                     |
| msg_type         | VARCHAR(16)  | 消息类型                                                                        |
| content          | TEXT         | 消息内容                                                                        |
| pic_url          | VARCHAR(255) | 图片链接                                                                        |
| media_id         | VARCHAR(255) | 媒体文件ID                                                                      |
| role             | VARCHAR(16)  | 角色类型：user-用户，assistant-助手，bot-机器人，system-系统，attachment-附件，tool-工具，manual-人工 |
| is_reply         | TINYINT      | 是否回复消息：0-否，1-是                                                              |
| is_send          | TINYINT      | 是否发送成功：0-否，1-是                                                              |
| link_info        | JSON         | 图文链接对象                                                                      |
| card_info        | JSON         | 小程序卡片对象                                                                     |

## API 接口

### 管理后台接口

#### 消息会话管理

- `POST /ai/admin/support/message-session/list` - 查询消息会话列表
- `GET /ai/admin/support/message-session/{id}` - 查询消息会话详情
- `POST /ai/admin/support/message-session` - 新增消息会话
- `PUT /ai/admin/support/message-session` - 修改消息会话
- `DELETE /ai/admin/support/message-session/{id}` - 删除消息会话
- `DELETE /ai/admin/support/message-session/batch/{ids}` - 批量删除消息会话
- `GET /ai/admin/support/message-session/by-open-id/{openId}` - 根据open_id获取消息会话
- `GET /ai/admin/support/message-session/by-customer-id/{customerId}` - 根据customer_id获取消息会话
- `PUT /ai/admin/support/message-session/update-status` - 更新会话状态

#### 消息记录管理

- `POST /ai/admin/support/message-history/list` - 查询消息记录列表
- `GET /ai/admin/support/message-history/{id}` - 查询消息记录详情
- `POST /ai/admin/support/message-history` - 新增消息记录
- `PUT /ai/admin/support/message-history` - 修改消息记录
- `DELETE /ai/admin/support/message-history/{id}` - 删除消息记录
- `DELETE /ai/admin/support/message-history/batch/{ids}` - 批量删除消息记录
- `GET /ai/admin/support/message-history/by-session-id/{sessionId}` - 根据会话ID获取消息记录列表
- `GET /ai/admin/support/message-history/by-msg-id/{msgId}` - 根据消息ID获取消息记录
- `GET /ai/admin/support/message-history/by-from-user-name/{fromUserName}` - 根据发送方微信号获取消息记录列表
- `PUT /ai/admin/support/message-history/update-send-status` - 更新消息发送状态

### 前端接口

#### 消息会话服务

- `POST /ai/support/message-session/create` - 创建消息会话
- `GET /ai/support/message-session/by-open-id/{openId}` - 根据open_id获取消息会话
- `GET /ai/support/message-session/by-customer-id/{customerId}` - 根据customer_id获取消息会话
- `PUT /ai/support/message-session/update-status` - 更新会话状态
- `PUT /ai/support/message-session/update-last-message-time` - 更新最后消息时间

#### 消息记录服务

- `POST /ai/support/message-history/create` - 创建消息记录
- `GET /ai/support/message-history/by-session-id/{sessionId}` - 根据会话ID获取消息记录列表
- `GET /ai/support/message-history/by-msg-id/{msgId}` - 根据消息ID获取消息记录
- `GET /ai/support/message-history/by-from-user-name/{fromUserName}` - 根据发送方微信号获取消息记录列表
- `PUT /ai/support/message-history/update-send-status` - 更新消息发送状态

## 使用示例

### 创建消息会话

```json
POST /ai/admin/support/message-session
{
  "sessionId": "session_001",
  "openId": "openid_001",
  "customerId": "customer_001",
  "nickname": "测试用户",
  "phone": "13800138000",
  "title": "测试会话",
  "description": "这是一个测试会话",
  "sessionStatus": 1,
  "lastMessageTime": 1640995200000,
  "messageCount": 0
}
```

### 创建消息记录

```json
POST /ai/admin/support/message-history
{
  "sessionId": "session_001",
  "msgId": "msg_001",
  "toUserName": "to_user_001",
  "fromUserName": "from_user_001",
  "createTimestamp": 1640995200000,
  "msgType": "text",
  "content": "这是一条测试消息",
  "picUrl": "",
  "mediaId": "",
  "role": "user",
  "isReply": 0,
  "isSend": 1,
  "linkInfo": "",
  "cardInfo": ""
}
```

### 查询消息会话列表

```json
POST /ai/admin/support/message-session/list
{
  "pageNum": 1,
  "pageSize": 10,
  "sessionId": "",
  "openId": "",
  "customerId": "",
  "nickname": "",
  "phone": "",
  "sessionStatus": null,
  "createTimeStart": null,
  "createTimeEnd": null
}
```

## 技术特性

### 高性能

- 使用 MyBatis-Plus 提供高效的数据库操作
- 使用 BeanCopyUtil 进行高性能对象拷贝
- 支持分页查询，避免大数据量查询性能问题

### 类型安全

- 完整的 DTO、VO、Entity 分层设计
- 使用泛型确保类型安全
- 完善的参数验证和异常处理

### 扩展性

- 支持多种消息类型扩展
- 支持自定义消息状态管理
- 支持灵活的查询条件组合

### 可维护性

- 清晰的代码结构和注释
- 统一的日志记录和异常处理
- 完整的单元测试覆盖

## 注意事项

1. **会话ID唯一性**: 确保会话ID在系统中唯一
2. **消息ID唯一性**: 确保消息ID在系统中唯一
3. **状态管理**: 合理管理会话和消息的状态变化
4. **数据一致性**: 注意会话消息数量与实际消息记录的一致性
5. **性能优化**: 对于大量消息的场景，考虑分批处理

## 未来扩展

1. **消息推送**: 支持实时消息推送功能
2. **消息模板**: 支持消息模板管理
3. **消息统计**: 支持消息统计分析功能
4. **消息搜索**: 支持全文搜索功能
5. **消息归档**: 支持历史消息归档功能 