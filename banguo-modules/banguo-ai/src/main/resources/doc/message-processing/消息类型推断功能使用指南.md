# 消息类型推断功能使用指南

## 概述

消息类型推断功能可以根据 `StreamChatRequestDTO` 的内容智能地推断出合适的 `MessageType`，实现优雅的前置判断。这个功能可以：

1. **自动推断消息类型**：根据内容自动判断是文本、图片、链接、命令等
2. **验证类型一致性**：确保推断的类型与数据内容匹配
3. **提供便捷方法**：简化消息类型的设置过程

## 核心组件

### 1. IMessageTypeInferenceService 接口

```java
public interface IMessageTypeInferenceService {
    // 推断消息类型
    MessageType inferMessageType(StreamChatRequestDTO request);
    
    // 验证推断结果
    boolean validateInference(StreamChatRequestDTO request, MessageType inferredType);
}
```

### 2. MessageTypeInferenceServiceImpl 实现类

实现了智能推断逻辑，支持以下推断规则：

- **命令消息**：以 `/` 开头的内容
- **图片URL**：包含图片文件扩展名的URL
- **视频URL**：包含视频文件扩展名的URL
- **音频URL**：包含音频文件扩展名的URL
- **普通链接**：其他有效的URL
- **文本消息**：默认类型

## 使用方法

### 1. 在 StreamChatServiceImpl 中集成

```java
@Override
@Async("aiTaskExecutor")
public void processStreamRequest(StreamChatRequestDTO chatRequest, SseEmitter emitter) {
    // ... 其他代码 ...
    
    // 智能推断消息类型
    MessageType inferredType = messageTypeInferenceService.inferMessageType(chatRequest);
    
    // 如果推断的类型与当前设置的不同，更新请求对象
    if (chatRequest.getMessageType() != inferredType) {
        log.info("消息类型推断: 原类型={}, 推断类型={}", 
                chatRequest.getMessageType(), inferredType);
        chatRequest.setMessageType(inferredType);
    }

    // 验证推断结果
    if (!messageTypeInferenceService.validateInference(chatRequest, inferredType)) {
        sendErrorEvent(emitter, messageId, "消息类型与数据内容不匹配", "INVALID_MESSAGE_TYPE");
        return;
    }
    
    // ... 继续处理 ...
}
```

### 2. 使用便捷方法

```java
// 方法1：使用 setContentAndInferType 方法
StreamChatRequestDTO request = new StreamChatRequestDTO()
    .setContentAndInferType("https://example.com/image.jpg");
// 自动推断为 IMAGE 类型

// 方法2：使用 setContentAndInferType 方法处理命令
StreamChatRequestDTO request = new StreamChatRequestDTO()
    .setContentAndInferType("/help 查看帮助");
// 自动推断为 COMMAND 类型

// 方法3：使用 setContentAndInferType 方法处理普通文本
StreamChatRequestDTO request = new StreamChatRequestDTO()
    .setContentAndInferType("你好，这是一条文本消息");
// 自动推断为 TEXT 类型
```

### 3. 手动推断和验证

```java
@Autowired
private IMessageTypeInferenceService messageTypeInferenceService;

// 推断消息类型
MessageType inferredType = messageTypeInferenceService.inferMessageType(request);

// 验证推断结果
boolean isValid = messageTypeInferenceService.validateInference(request, inferredType);

if (isValid) {
    request.setMessageType(inferredType);
} else {
    // 处理验证失败的情况
    log.warn("消息类型推断验证失败");
}
```

## 推断规则详解

### 1. 命令消息推断

**规则**：以 `/` 开头的内容
**示例**：
- `/help` → COMMAND
- `/status` → COMMAND
- `/restart 会话` → COMMAND

### 2. URL类型推断

**图片URL**：
- `https://example.com/image.jpg` → IMAGE
- `http://example.com/photo.png` → IMAGE
- `www.example.com/pic.gif` → IMAGE

**视频URL**：
- `https://example.com/video.mp4` → VIDEO
- `http://example.com/movie.avi` → VIDEO

**音频URL**：
- `https://example.com/audio.mp3` → VOICE
- `http://example.com/sound.wav` → VOICE

**普通链接**：
- `https://example.com` → LINK
- `http://example.com/page` → LINK

### 3. 文件路径推断

**图片文件**：
- `/path/to/image.jpg` → IMAGE
- `C:\photos\pic.png` → IMAGE

**视频文件**：
- `/path/to/video.mp4` → VIDEO
- `C:\videos\movie.avi` → VIDEO

**音频文件**：
- `/path/to/audio.mp3` → VOICE
- `C:\music\song.wav` → VOICE

**其他文件**：
- `/path/to/document.pdf` → FILE
- `C:\docs\file.txt` → FILE

## 测试接口

### 1. 单个内容测试

```bash
GET /ai/test/inference/test/{content}
```

**示例**：
```bash
# 测试图片URL
GET /ai/test/inference/test/https://example.com/image.jpg

# 测试命令
GET /ai/test/inference/test/%2Fhelp

# 测试普通文本
GET /ai/test/inference/test/你好世界
```

### 2. 批量测试

```bash
POST /ai/test/inference/batch-test
Content-Type: application/json

{
  "图片测试": "https://example.com/image.jpg",
  "命令测试": "/help 查看帮助",
  "链接测试": "https://example.com",
  "文本测试": "这是一条普通文本消息",
  "视频测试": "https://example.com/video.mp4"
}
```

### 3. 完整请求推断

```bash
POST /ai/test/inference/infer
Content-Type: application/json

{
  "messageType": "text",
  "data": {
    "content": "https://example.com/image.jpg",
    "type": "text"
  }
}
```

### 4. 预设场景测试

```bash
GET /ai/test/inference/preset-test
```

这个接口会测试常见的消息类型场景，包括：
- 文本消息
- 命令消息
- 图片URL
- 视频URL
- 音频URL
- 普通链接
- 各种文件路径

### 5. 边界情况测试

```bash
GET /ai/test/inference/edge-test
```

这个接口会测试边界情况，包括：
- 空内容
- null内容
- 纯空格
- 特殊字符
- 数字
- 中文
- 混合内容
- 长文本
- 无效URL
- 部分URL

## 最佳实践

### 1. 在控制器中使用

```java
@PostMapping("/chat")
public SseEmitter streamChatPost(@RequestBody StreamChatRequestDTO chatRequest) {
    // 自动推断消息类型
    chatRequest.autoInferMessageType();
    
    // 或者使用内容推断
    if (chatRequest.getData() == null && chatRequest.getContent() != null) {
        chatRequest.setContentAndInferType(chatRequest.getContent());
    }
    
    // 继续处理...
}
```

### 2. 错误处理

```java
// 验证推断结果
if (!messageTypeInferenceService.validateInference(chatRequest, inferredType)) {
    // 记录警告日志
    log.warn("消息类型推断验证失败: originalType={}, inferredType={}", 
            originalType, inferredType);
    
    // 可以选择使用原始类型或默认类型
    chatRequest.setMessageType(MessageType.TEXT);
}
```

### 3. 性能优化

```java
// 如果已经明确设置了类型，可以跳过推断
if (chatRequest.getMessageType() != null && 
    chatRequest.getMessageType() != MessageType.TEXT) {
    // 使用已设置的类型
    return;
}

// 只在需要时进行推断
MessageType inferredType = messageTypeInferenceService.inferMessageType(chatRequest);
```

## 扩展自定义推断规则

如果需要添加新的推断规则，可以扩展 `MessageTypeInferenceServiceImpl`：

```java
@Override
public MessageType inferMessageType(StreamChatRequestDTO request) {
    // 自定义推断逻辑
    if (isCustomType(request)) {
        return MessageType.CUSTOM;
    }
    
    // 调用原有逻辑
    return super.inferMessageType(request);
}

private boolean isCustomType(StreamChatRequestDTO request) {
    // 实现自定义判断逻辑
    return false;
}
```

## 注意事项

1. **性能考虑**：推断过程涉及正则表达式匹配，对于高频调用场景要注意性能
2. **准确性**：推断结果可能不是100%准确，建议在关键场景下进行人工验证
3. **扩展性**：新增消息类型时需要同步更新推断规则
4. **兼容性**：推断功能应该向后兼容，不影响现有功能
5. **测试接口**：所有测试接口都位于 `/ai/test/inference` 路径下，仅在测试、开发、本地环境下可用
6. **环境限制**：测试控制器使用了 `@Profile({"test", "dev", "local"})` 注解，确保只在非生产环境中启用 