# 工单总结功能开发说明

## 1. 需求解读

本需求旨在为客服工单（service_ticket）提供类似“消息会话总结”的AI自动总结能力。即：
- 支持对单个或批量工单进行AI总结，自动生成工单标题、问题类型、描述等关键信息。
- 支持定时任务自动处理未总结的工单。
- 提供Dubbo远程服务接口，供job等模块调用。
- 结构、日志、调用方式与现有消息会话总结功能保持一致。

## 2. 功能目标

- 新增工单总结相关的远程服务接口、实现类、VO、Service接口。
- 支持单个工单总结、批量工单总结、自动处理未总结工单三种调用方式。
- 便于后续扩展AI总结逻辑。

## 3. 设计方案

### 3.1 主要接口与类

#### 远程服务接口
- `RemoteServiceTicketSummaryService`（API层，banguo-api-ai）
  - `Boolean summarizeTicket(Long ticketId)`
  - `Integer batchSummarizeTickets(List<Long> ticketIdList)`
  - `Integer autoSummarizePendingTickets(Integer limit)`

#### 远程服务实现
- `RemoteServiceTicketSummaryServiceImpl`（banguo-ai/dubbo）
  - 注入 `IServiceTicketSummaryService`，实现远程接口，负责日志、异常处理、调用本地服务。

#### 本地服务接口
- `IServiceTicketSummaryService`（banguo-ai/service/support）
  - `ServiceTicketSummaryVo summarizeTicket(Long ticketId)`
  - `List<ServiceTicketSummaryVo> batchSummarizeTickets(List<Long> ticketIdList)`
  - `Integer autoSummarizePendingTickets(Integer limit)`

#### VO对象
- `ServiceTicketSummaryVo`（banguo-ai/domain/vo）
  - `Long ticketId`
  - `String title`
  - `String problemType`
  - `String description`
  - `Boolean success`
  - `String errorMessage`

### 3.2 业务流程

1. 远程服务收到请求（单个/批量/自动），记录日志。
2. 调用本地Service进行AI总结处理。
3. 本地Service实现AI总结逻辑（可参考MessageSessionSummaryServiceImpl）。
4. 返回处理结果，记录成功/失败日志。

## 4. 开发步骤

### 4.1 新增接口与VO
- [ ] 在 `banguo-api-ai` 新增 `RemoteServiceTicketSummaryService` 接口。
- [ ] 在 `banguo-ai/domain/vo` 新增 `ServiceTicketSummaryVo`。
- [ ] 在 `banguo-ai/service/support` 新增 `IServiceTicketSummaryService`。

### 4.2 远程服务实现
- [ ] 在 `banguo-ai/dubbo` 新增 `RemoteServiceTicketSummaryServiceImpl`，实现远程接口，注入本地Service，日志与异常处理仿照 `RemoteMessageSessionSummaryServiceImpl`。

### 4.3 本地Service实现
- [ ] 在 `banguo-ai/service/impl/support` 新增 `ServiceTicketSummaryServiceImpl`，实现AI总结工单的核心逻辑（可参考 `MessageSessionSummaryServiceImpl`）。
- [ ] 实现单个、批量、自动总结方法。
- [ ] 处理AI调用、数据库更新、异常捕获等。

### 4.4 Dubbo暴露与依赖
- [ ] 确认Dubbo注解、Service暴露无误。
- [ ] job等模块可通过Dubbo远程调用。

### 4.5 测试与联调
- [ ] 编写单元测试/集成测试，验证各接口功能。
- [ ] job模块联调，确保定时任务可正常调用。

## 5. 注意事项

- 命名、包路径、注解风格与现有消息会话总结功能保持一致。
- 日志关键字、异常处理、返回结构参照 `RemoteMessageSessionSummaryServiceImpl`。
- AI总结逻辑可先复用/仿照会话总结，后续可根据工单业务特点优化。
- VO、DTO等字段如需扩展，建议与前端/产品沟通确认。

## 6. 参考资料
- `RemoteMessageSessionSummaryServiceImpl`、`MessageSessionSummaryServiceImpl` 相关代码
- `banguo-modules/banguo-ai/src/main/resources/doc/message-processing/消息会话总结功能使用说明.md`
- `banguo-modules/banguo-ai/src/main/resources/doc/message-processing/智能客服MCP服务使用说明.md`

---

如有疑问，建议先实现接口与远程服务，后续AI总结逻辑可逐步完善。 