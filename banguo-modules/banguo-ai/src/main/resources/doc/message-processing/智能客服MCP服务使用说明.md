# 智能客服MCP服务使用说明

## 概述

智能客服MCP服务是基于Model Context Protocol (MCP)
实现的智能客服工单管理系统。当阿里百炼智能体无法解决用户问题时，可以通过MCP工具自动创建人工客服工单，记录用户问题和对话历史，安排后续人工回访处理。

## 系统架构

```
微信小程序 → 阿里百炼智能体 → MCP智能客服服务 → 工单数据库
                    ↓
              智能体无法处理时自动创建工单
                    ↓
              工作人员电话回访处理
```

## 核心功能

### 1. 工单创建工具 (createCustomerServiceTicket)

**功能描述**: 创建人工客服工单，记录客户信息和问题详情

**使用场景**: 当智能客服无法解决客户问题时调用

**参数说明**:

- `customerInfoJson`: 客户信息JSON字符串
- `problemDescription`: 客户问题的详细描述
- `problemType`: 问题类型分类
- `conversationHistoryJson`: 完整的对话历史记录JSON
- `aiAssessmentDetails`: AI评估结果的详细说明

**返回结果**:

- `success`: 是否创建成功
- `ticketNo`: 工单号
- `createTime`: 创建时间
- `followUpTime`: 预计回访时间
- `userReplyTemplate`: 给用户的回复模板

### 2. 客户信息收集工具 (collectCustomerInfo)

**功能描述**: 收集和验证客户的基本信息

**使用场景**: 在创建工单前确保收集到必要的联系信息

**参数说明**:

- `customerName`: 客户姓名
- `customerPhone`: 客户联系电话
- `wechatOpenid`: 微信openid（可选）

**返回结果**: 信息收集反馈和验证结果

### 3. 工单状态查询工具 (queryTicketStatus)

**功能描述**: 根据工单号查询处理状态

**使用场景**: 客户跟踪问题处理进度时调用

**参数说明**:

- `ticketNo`: 客服工单号（格式：CS20240315xxxx）

**返回结果**: 工单详细状态信息

## 数据库表结构

### 主工单表 (service_ticket)

| 字段名                  | 类型           | 说明                         |
|----------------------|--------------|----------------------------|
| id                   | BIGINT       | 主键ID                       |
| ticket_no            | VARCHAR(32)  | 工单号                        |
| customer_name        | VARCHAR(100) | 客户姓名                       |
| customer_phone       | VARCHAR(20)  | 客户电话                       |
| wechat_openid        | VARCHAR(64)  | 微信openid                   |
| problem_type         | VARCHAR(50)  | 问题类型                       |
| problem_description  | TEXT         | 问题描述                       |
| conversation_history | JSON         | 对话历史记录                     |
| priority_level       | TINYINT      | 优先级：1-低，2-中，3-高            |
| status               | TINYINT      | 状态：1-待处理，2-处理中，3-已完成，4-已关闭 |
| ai_assessment        | TEXT         | AI评估结果                     |
| follow_up_time       | DATETIME     | 预计回访时间                     |
| resolved_time        | DATETIME     | 解决时间                       |

### 处理记录表 (ticket_process_log)

| 字段名                | 类型          | 说明   |
|--------------------|-------------|------|
| id                 | BIGINT      | 主键ID |
| ticket_id          | BIGINT      | 工单ID |
| ticket_no          | VARCHAR(32) | 工单号  |
| action_type        | VARCHAR(20) | 操作类型 |
| action_description | TEXT        | 操作描述 |
| operator           | VARCHAR(64) | 操作人  |

## API接口

### 创建工单

```http
POST /ai/customer-service/create-ticket
Content-Type: application/json

{
    "customerInfo": {
        "customerName": "张三",
        "customerPhone": "13800138000",
        "wechatOpenid": "openid123"
    },
    "problemDescription": "商品质量问题要求退款",
    "problemType": "退款",
    "conversationHistory": [...],
    "aiAssessment": "客户明确要求退款，建议人工处理"
}
```

### 查询工单

```http
GET /ai/customer-service/ticket/{ticketNo}
```

### 更新工单状态

```http
PUT /ai/customer-service/ticket/{ticketNo}/status?status=2&remark=正在处理中
```

## 使用流程

1. **智能对话阶段**: 微信小程序用户与阿里百炼智能体进行对话
2. **信息收集**: 当智能体发现无法解决问题时，调用`collectCustomerInfo`收集客户联系信息
3. **创建工单**: 调用`createCustomerServiceTicket`创建工单，系统返回工单号和回复模板
4. **用户通知**: 智能体向用户发送工单创建成功的消息，包含工单号和回访时间
5. **人工处理**: 工作人员根据工单信息电话联系客户处理问题
6. **状态跟踪**: 用户可以通过工单号查询处理状态

## 配置说明

### 应用配置 (application.yml)

MCP服务相关配置已在现有配置中包含:

```yaml
spring:
  ai:
    mcp:
      server:
        capabilities:
          tool: true
          resource: true
          prompt: true
        sse-endpoint: /ai/mcp/sse
        sse-message-endpoint: /ai/mcp/message
```

### Jackson配置

系统已配置优化的Jackson ObjectMapper：

```java
@Bean
@Primary
public ObjectMapper objectMapper() {
    ObjectMapper mapper = new ObjectMapper();
    
    // 时间处理
    mapper.registerModule(new JavaTimeModule());
    mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
    mapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
    
    // 属性处理
    mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    mapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
    
    // 空值处理
    mapper.setDefaultPropertyInclusion(JsonInclude.Include.NON_NULL);
    
    return mapper;
}
```

### 工具注册

智能客服工具已在`McpToolConfiguration`中自动注册，无需额外配置。

## 注意事项

1. **数据库初始化**: 部署前需要执行SQL脚本创建相关表
2. **电话号码验证**: 系统会验证客户电话号码格式
3. **工单号格式**: 自动生成，格式为CS+日期+4位随机数
4. **回访时间**: 默认为创建后1个工作日，周末自动顺延
5. **对话历史**: 以JSON格式存储，支持完整的对话上下文
6. **错误处理**: 所有MCP工具都包含异常处理，确保系统稳定性
7. **JSON处理**: 统一使用Jackson进行JSON序列化/反序列化

## 扩展说明

本系统提供了基础的工单管理功能框架，后续可以扩展：

- 工单优先级和自动分配规则
- 客服工作台界面
- 短信/微信通知功能
- 工单统计和报表功能 