# 智能客服流式响应功能设计文档

## 1. 项目概述

### 1.1 功能目标

基于现有`banguo-ai`模块，新增流式响应功能，提供实时的AI对话体验。该功能完全兼容现有消息处理流程，支持多种消息类型，并针对小程序场景进行优化。

### 1.2 核心特性

- ✅ **实时流式响应**：基于SSE技术，提供实时的AI回复体验
- ✅ **完全向下兼容**：不影响现有消息处理流程
- ✅ **多消息类型支持**：文本、图片、命令、链接、卡片等
- ✅ **打字效果**：可配置的逐字符显示效果
- ✅ **MCP工具集成**：支持现有的MCP工具调用
- ✅ **小程序优化**：针对微信小程序场景优化
- ✅ **高性能设计**：Undertow服务器优化，支持大量并发连接

## 2. 技术架构

### 2.1 核心技术栈

- **服务器**: Spring Boot 3.5.x + Undertow（优化配置）
- **流式协议**: Server-Sent Events (SSE)
- **AI服务**: 阿里云百练 DashScope SDK
- **缓存**: Redis (会话管理)
- **消息队列**: RocketMQ (兼容现有异步处理)
- **认证**: API Key + 现有认证体系

### 2.2 系统架构图

```
小程序客户端 
    ↓ SSE连接 (/ai/support/message/chat)
流式响应控制器 (StreamChatController)
    ↓ 异步处理
流式聊天服务 (StreamChatService)
    ↓ AI调用 + 消息保存
阿里云百练API + 数据库 + Redis
    ↓ 流式输出
客户端实时展示
```

## 3. 接口设计

### 3.1 核心接口

#### GET方式（简单场景）

```http
GET /ai/support/message/chat?sessionId={sessionId}&openId={openId}&message={message}&messageType=text&enableTyping=true&typingDelay=50&chunkSize=10
Accept: text/event-stream
```

#### POST方式（高级场景）

```http
POST /ai/support/message/chat
Content-Type: application/json
Accept: text/event-stream

{
  "sessionId": "session_123",
  "openId": "openid_456",
  "content": "你好，请介绍一下产品",
  "messageType": "text",
  "options": {
    "enableTyping": true,
    "typingDelay": 50,
    "chunkSize": 10,
    "enableProgress": true,
    "connectionTimeout": 300000
  },
  "enableMcpTools": true,
  "context": {}
}
```

### 3.2 响应格式

#### SSE事件流格式

```
event: started
id: evt_1703123456789_0.123
data: {"eventId":"evt_1703123456789_0.123","sessionId":"session_123","type":"started","data":"开始处理消息","timestamp":1703123456789}

event: typing
id: evt_1703123456790_0.456
data: {"eventId":"evt_1703123456790_0.456","sessionId":"session_123","type":"typing","data":"AI正在思考中...","timestamp":1703123456790}

event: chunk
id: evt_1703123456791_0.789
data: {"eventId":"evt_1703123456791_0.789","sessionId":"session_123","type":"chunk","data":"您好！","timestamp":1703123456791}

event: chunk
id: evt_1703123456792_0.012
data: {"eventId":"evt_1703123456792_0.012","sessionId":"session_123","type":"chunk","data":"我是小般","timestamp":1703123456792}

event: completed
id: evt_1703123456793_0.345
data: {"eventId":"evt_1703123456793_0.345","sessionId":"session_123","type":"completed","data":"处理完成","timestamp":1703123456793,"isLast":true}
```

### 3.3 事件类型说明

| 事件类型          | 说明   | 数据格式                                             |
|---------------|------|--------------------------------------------------|
| `started`     | 开始处理 | `{"message": "开始处理消息"}`                          |
| `typing`      | 正在输入 | `{"message": "AI正在思考中..."}`                      |
| `progress`    | 处理进度 | `{"progress": 50, "message": "处理中..."}`          |
| `chunk`       | 内容块  | `{"content": "部分回复内容"}`                          |
| `tool_call`   | 工具调用 | `{"tool": "createTicket", "params": {...}}`      |
| `tool_result` | 工具结果 | `{"tool": "createTicket", "result": {...}}`      |
| `completed`   | 处理完成 | `{"message": "处理完成"}`                            |
| `error`       | 错误   | `{"message": "错误信息", "errorCode": "ERROR_CODE"}` |
| `heartbeat`   | 心跳   | `{"message": "ping"}`                            |

## 4. 数据模型设计

### 4.1 请求DTO

```java
// StreamChatRequestDTO - 流式聊天请求
public class StreamChatRequestDTO {
    private String sessionId;           // 会话ID
    private String openId;              // 用户OpenID
    private String content;             // 消息内容
    private String messageType;         // 消息类型：text, image, command, link, card
    private Map<String, Object> context; // 扩展上下文
    private StreamOptionsDTO options;   // 流式配置选项
    private String apiKey;              // API Key（可选）
    private Boolean enableMcpTools;     // 是否启用MCP工具
    private Long timestamp;             // 请求时间戳
}

// StreamOptionsDTO - 流式配置选项
public class StreamOptionsDTO {
    private Boolean enableTyping = true;        // 是否启用打字效果
    private Long typingDelay = 50L;             // 打字延迟（毫秒）
    private Boolean enableProgress = true;      // 是否显示进度
    private Integer chunkSize = 10;             // 每次发送字符数
    private Long heartbeatInterval = 30000L;    // 心跳间隔
    private Boolean enableToolCallHints = true; // 是否启用工具调用提示
    private Long connectionTimeout = 300000L;   // 连接超时时间
    private Boolean enableRetry = true;         // 是否启用错误重试
    private Integer maxRetries = 3;             // 最大重试次数
}

// StreamResponseEventDTO - 流式响应事件
public class StreamResponseEventDTO {
    private String eventId;                     // 事件ID
    private String sessionId;                   // 会话ID
    private String type;                        // 事件类型
    private Object data;                        // 事件数据
    private Map<String, Object> metadata;       // 元数据
    private Long timestamp;                     // 时间戳
    private Integer progress;                   // 进度百分比
    private String errorCode;                   // 错误码
    private Boolean isLast = false;             // 是否最后一个事件
}
```

### 4.2 消息类型扩展

```java
// 支持的消息类型
public enum MessageType {
    TEXT("text", "文本消息"),
    IMAGE("image", "图片消息"),
    COMMAND("command", "命令消息"),
    LINK("link", "链接消息"),
    CARD("card", "卡片消息"),
    VOICE("voice", "语音消息"),
    VIDEO("video", "视频消息"),
    FILE("file", "文件消息"),
    LOCATION("location", "位置消息"),
    RICH_TEXT("rich_text", "富文本消息");
}
```

## 5. 核心服务设计

### 5.1 流式聊天服务接口

```java
public interface IStreamChatService {
    // 处理流式请求
    void processStreamRequest(StreamChatRequestDTO request, SseEmitter emitter);

    // 中断流式响应
    void interruptStream(String sessionId);

    // 获取流式处理状态
    String getStreamStatus(String sessionId);

    // 清理过期连接
    void cleanupExpiredStreams();
}
```

### 5.2 核心处理流程

```java
@Async("aiTaskExecutor")
public void processStreamRequest(StreamChatRequestDTO request, SseEmitter emitter) {
    try {
        // 1. 注册活跃连接
        activeStreams.put(sessionId, emitter);
        
        // 2. 发送开始事件
        sendEvent(emitter, createStartedEvent(sessionId, "开始处理消息"));
        
        // 3. 保存用户消息
        saveUserMessage(request);
        
        // 4. 发送思考状态
        sendTypingEvent(emitter, sessionId);
        
        // 5. 调用AI服务
        AiCallResultDTO aiResult = aiChatService.callBailianApp(prompt, aiSessionId);
        
        // 6. 流式发送回复
        streamAiResponse(emitter, sessionId, aiResult.getText(), options);
        
        // 7. 保存AI回复
        saveAiMessage(aiResult, sessionId);
        
        // 8. 发送完成事件
        sendEvent(emitter, createCompletedEvent(sessionId, "处理完成"));
        
    } catch (Exception e) {
        sendErrorEvent(emitter, sessionId, e.getMessage());
    } finally {
        activeStreams.remove(sessionId);
        emitter.complete();
    }
}
```

## 6. Undertow性能优化

### 6.1 服务器配置优化

```yaml
server:
  port: 9211
  undertow:
    # 基础配置
    no-request-timeout: 300000  # 连接空闲超时5分钟（适配SSE长连接）
    max-http-post-size: 10485760  # 10MB，支持大数据量POST
    buffer-size: 65536 # 64KB缓冲区，优化SSE数据传输
    direct-buffers: true # 启用直接缓冲区，提升性能
    
    # 线程配置（针对SSE优化）
    io-threads: 16  # IO线程数（CPU核心数*2）
    worker-threads: 128  # 工作线程数（支持更多并发SSE连接）
    buffer-pool-size: 2048  # 缓冲池大小，支持更多SSE连接
    
    # 连接配置（支持流式响应）
    max-connections: 2000  # 最大并发连接数
    max-parameters: 2000   # URL参数数量
    max-headers: 500       # HTTP头数量
    max-header-size: 2097152  # 2MB，支持大请求头
    
    # SSE特定优化
    eager-filter-init: true  # 提前初始化过滤器
    preserve-path-on-forward: true  # 保持转发路径
```

### 6.2 JVM参数优化

```bash
# 生产环境JVM参数（针对SSE优化）
JAVA_OPTS="-server \
-Xms4g -Xmx4g \
-XX:NewRatio=1 \
-XX:SurvivorRatio=8 \
-XX:+UseG1GC \
-XX:MaxGCPauseMillis=100 \
-XX:G1HeapRegionSize=16m \
-XX:+UseStringDeduplication \
-XX:+UnlockExperimentalVMOptions \
-XX:+UseJVMCICompiler \
-Djava.awt.headless=true \
-Dfile.encoding=UTF-8 \
-Duser.timezone=GMT+08"
```

## 7. 小程序集成方案

### 7.1 微信小程序实现（官方方式）

```javascript
// 小程序流式聊天实现
class StreamChatClient {
  constructor(options) {
    this.baseUrl = options.baseUrl || 'https://your-domain.com';
    this.apiKey = options.apiKey;
    this.sessionId = options.sessionId || this.generateSessionId();
    this.openId = options.openId;
    this.onMessage = options.onMessage || (() => {});
    this.onError = options.onError || (() => {});
    this.onComplete = options.onComplete || (() => {});
  }

  // 发送消息并建立流式连接
  sendMessage(content, messageType = 'text', options = {}) {
    const url = this.buildUrl(content, messageType, options);
    
    // 使用微信小程序的请求API模拟SSE
    return this.connectStream(url);
  }

  // 构建请求URL
  buildUrl(content, messageType, options) {
    const params = new URLSearchParams({
      sessionId: this.sessionId,
      openId: this.openId,
      message: content,
      messageType: messageType,
      apiKey: this.apiKey,
      enableTyping: options.enableTyping !== false,
      typingDelay: options.typingDelay || 50,
      chunkSize: options.chunkSize || 10
    });

    return `${this.baseUrl}/ai/support/message/chat?${params.toString()}`;
  }

  // 建立流式连接（小程序兼容方式）
  connectStream(url) {
    let buffer = '';
    let eventBuffer = '';

    const requestTask = wx.request({
      url: url,
      method: 'GET',
      header: {
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache'
      },
      enableChunked: true,  // 启用分块传输
      
      success: (res) => {
        console.log('流式连接建立成功');
      },
      
      fail: (error) => {
        console.error('流式连接失败:', error);
        this.onError(error);
      }
    });

    // 监听数据接收
    requestTask.onHeadersReceived((res) => {
      console.log('接收到响应头:', res.header);
    });

    // 处理流式数据（模拟SSE解析）
    requestTask.onChunkReceived((res) => {
      const chunk = wx.arrayBufferToBase64(res.data);
      const text = this.base64ToText(chunk);
      buffer += text;

      // 解析SSE格式数据
      this.parseSSEData(buffer);
    });

    return {
      // 中断连接
      abort: () => {
        requestTask.abort();
      },
      
      // 获取连接状态
      getStatus: () => {
        return 'connected'; // 简化实现
      }
    };
  }

  // 解析SSE数据格式
  parseSSEData(buffer) {
    const lines = buffer.split('\n');
    let eventType = '';
    let eventId = '';
    let eventData = '';

    for (let line of lines) {
      line = line.trim();
      
      if (line.startsWith('event:')) {
        eventType = line.substring(6).trim();
      } else if (line.startsWith('id:')) {
        eventId = line.substring(3).trim();
      } else if (line.startsWith('data:')) {
        eventData = line.substring(5).trim();
      } else if (line === '') {
        // 事件结束，处理数据
        if (eventType && eventData) {
          this.handleSSEEvent({
            type: eventType,
            id: eventId,
            data: eventData
          });
        }
        
        // 重置
        eventType = '';
        eventId = '';
        eventData = '';
      }
    }
  }

  // 处理SSE事件
  handleSSEEvent(event) {
    try {
      const data = JSON.parse(event.data);
      
      switch (event.type) {
        case 'started':
          this.onMessage({
            type: 'status',
            content: data.data,
            timestamp: data.timestamp
          });
          break;
          
        case 'typing':
          this.onMessage({
            type: 'typing',
            content: data.data,
            timestamp: data.timestamp
          });
          break;
          
        case 'chunk':
          this.onMessage({
            type: 'chunk',
            content: data.data,
            timestamp: data.timestamp
          });
          break;
          
        case 'completed':
          this.onMessage({
            type: 'complete',
            content: data.data,
            timestamp: data.timestamp
          });
          this.onComplete();
          break;
          
        case 'error':
          this.onError({
            message: data.data,
            errorCode: data.errorCode,
            timestamp: data.timestamp
          });
          break;
      }
    } catch (e) {
      console.error('解析SSE事件失败:', e);
    }
  }

  // 工具方法
  base64ToText(base64) {
    return decodeURIComponent(escape(atob(base64)));
  }

  generateSessionId() {
    return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }
}

// 使用示例
const chatClient = new StreamChatClient({
  baseUrl: 'https://your-api-domain.com',
  apiKey: 'your-api-key',
  openId: 'user-openid',
  sessionId: 'session-123',
  
  onMessage: (message) => {
    console.log('收到消息:', message);
    
    switch (message.type) {
      case 'status':
        // 显示状态信息
        showStatus(message.content);
        break;
        
      case 'typing':
        // 显示打字指示器
        showTypingIndicator(message.content);
        break;
        
      case 'chunk':
        // 逐步显示内容
        appendMessageContent(message.content);
        break;
        
      case 'complete':
        // 隐藏打字指示器，完成显示
        hideTypingIndicator();
        markMessageComplete();
        break;
    }
  },
  
  onError: (error) => {
    console.error('聊天错误:', error);
    showErrorMessage(error.message);
  },
  
  onComplete: () => {
    console.log('对话完成');
  }
});

// 发送消息
chatClient.sendMessage('你好，请介绍一下产品功能', 'text', {
  enableTyping: true,
  typingDelay: 50,
  chunkSize: 10
});
```

### 7.2 小程序页面实现示例

```javascript
// pages/chat/chat.js
Page({
  data: {
    messages: [],
    inputValue: '',
    isTyping: false,
    currentResponse: ''
  },

  onLoad: function(options) {
    this.initChatClient();
  },

  // 初始化聊天客户端
  initChatClient() {
    this.chatClient = new StreamChatClient({
      baseUrl: 'https://your-api-domain.com',
      apiKey: 'your-api-key',
      openId: wx.getStorageSync('openId'),
      
      onMessage: (message) => {
        this.handleChatMessage(message);
      },
      
      onError: (error) => {
        this.showError(error.message);
      }
    });
  },

  // 处理聊天消息
  handleChatMessage(message) {
    switch (message.type) {
      case 'status':
        console.log('状态:', message.content);
        break;
        
      case 'typing':
        this.setData({
          isTyping: true
        });
        break;
        
      case 'chunk':
        // 逐步显示AI回复
        this.setData({
          currentResponse: this.data.currentResponse + message.content
        });
        this.scrollToBottom();
        break;
        
      case 'complete':
        // 完成回复，添加到消息列表
        this.setData({
          isTyping: false,
          messages: [...this.data.messages, {
            id: Date.now(),
            type: 'ai',
            content: this.data.currentResponse,
            timestamp: new Date()
          }],
          currentResponse: ''
        });
        this.scrollToBottom();
        break;
    }
  },

  // 发送消息
  sendMessage() {
    const content = this.data.inputValue.trim();
    if (!content) return;

    // 添加用户消息到列表
    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: content,
      timestamp: new Date()
    };

    this.setData({
      messages: [...this.data.messages, userMessage],
      inputValue: '',
      currentResponse: ''
    });

    // 发送到服务器
    this.chatClient.sendMessage(content, 'text');
    
    this.scrollToBottom();
  },

  // 滚动到底部
  scrollToBottom() {
    this.setData({
      scrollToView: 'msg-' + (this.data.messages.length - 1)
    });
  },

  // 显示错误
  showError(message) {
    wx.showToast({
      title: message,
      icon: 'none'
    });
  },

  // 输入框事件
  onInputChange(e) {
    this.setData({
      inputValue: e.detail.value
    });
  }
});
```

## 8. 测试方案

### 8.1 测试接口

- `GET /ai/test/stream/simple` - 简单流式测试
- `POST /ai/test/stream/advanced` - 高级流式测试
- `GET /ai/test/stream/message-types/{type}` - 消息类型测试
- `GET /ai/test/stream/fast` - 快速模式测试
- `GET /ai/test/stream/status/{sessionId}` - 状态查询
- `POST /ai/test/stream/interrupt/{sessionId}` - 中断测试

### 8.2 测试用例

```bash
# 1. 基础功能测试
curl -N "http://localhost:9211/ai/test/stream/simple?message=你好"

# 2. 打字效果测试
curl -N "http://localhost:9211/ai/test/stream/simple?message=请详细介绍产品功能&enableTyping=true&typingDelay=100&chunkSize=5"

# 3. 快速模式测试
curl -N "http://localhost:9211/ai/test/stream/fast?message=快速回复测试"

# 4. POST方式测试
curl -X POST -H "Content-Type: application/json" -N \
  "http://localhost:9211/ai/test/stream/advanced" \
  -d '{
    "sessionId": "test-001",
    "openId": "test-openid",
    "content": "测试POST请求",
    "messageType": "text",
    "options": {
      "enableTyping": true,
      "typingDelay": 50,
      "chunkSize": 8
    }
  }'
```

## 9. 扩展性设计

### 9.1 消息类型扩展

支持未来扩展的消息类型：

- **富文本消息**：支持Markdown格式
- **卡片消息**：结构化信息展示
- **命令消息**：特殊指令处理
- **语音消息**：语音转文本处理
- **文件消息**：文件上传和处理

### 9.2 工具集成扩展

- **现有MCP工具**：完全兼容现有工具
- **自定义工具**：支持注册新工具
- **工具链调用**：支持多工具协作
- **工具结果流式返回**：实时显示工具执行过程

### 9.3 多端适配

- **Web端**：标准EventSource实现
- **移动端App**：WebView + SSE
- **小程序**：兼容性实现方案
- **API集成**：RESTful接口支持

## 10. 性能指标

### 10.1 性能目标

- **并发连接数**：支持2000+并发SSE连接
- **响应延迟**：首字符响应<500ms
- **吞吐量**：每秒处理100+消息
- **可用性**：99.9%服务可用性

### 10.2 监控指标

- **连接数监控**：实时SSE连接数
- **响应时间监控**：各阶段耗时统计
- **错误率监控**：连接失败/处理失败率
- **资源使用监控**：CPU/内存/网络使用率

## 11. 安全设计

### 11.1 认证授权

- **API Key认证**：复用现有API Key机制
- **会话验证**：验证会话合法性
- **频率限制**：防止恶意请求
- **IP白名单**：限制访问来源

### 11.2 数据安全

- **敏感信息过滤**：过滤敏感内容
- **日志脱敏**：日志中敏感信息脱敏
- **传输加密**：HTTPS强制加密
- **数据留存**：符合数据保护法规

## 12. 运维监控

### 12.1 健康检查

- **服务健康**：/ai/test/stream/health
- **连接池监控**：活跃连接数统计
- **资源使用**：实时监控资源使用情况
- **自动恢复**：异常情况自动恢复

### 12.2 日志管理

- **结构化日志**：JSON格式日志输出
- **链路追踪**：完整请求链路跟踪
- **错误聚合**：错误信息聚合分析
- **性能分析**：性能瓶颈分析

## 13. 详细实施计划

### 13.1 第一阶段：基础功能实现（2-3天）

- [x] **Stream事件类型枚举**：定义所有事件类型
- [x] **请求响应DTO设计**：完整的数据模型
- [x] **流式聊天服务接口**：核心服务接口定义
- [x] **流式聊天服务实现**：核心业务逻辑
- [x] **流式聊天控制器**：HTTP接口层
- [x] **Undertow配置优化**：服务器性能优化
- [ ] **单元测试编写**：核心功能测试用例
- [ ] **集成测试验证**：端到端功能验证

### 13.2 第二阶段：测试和优化（1-2天）

- [x] **测试控制器实现**：完整的测试接口
- [ ] **性能压力测试**：并发连接测试
- [ ] **错误处理完善**：异常情况处理
- [ ] **连接管理优化**：连接池和清理机制
- [ ] **日志和监控完善**：运维监控指标
- [ ] **文档补充完善**：API文档和使用指南

### 13.3 第三阶段：小程序集成（1-2天）

- [ ] **小程序SDK实现**：流式聊天客户端
- [ ] **小程序页面示例**：完整的聊天页面
- [ ] **兼容性测试**：多端兼容性验证
- [ ] **用户体验优化**：交互体验优化
- [ ] **错误处理机制**：客户端错误处理

### 13.4 第四阶段：生产部署（1天）

- [ ] **生产环境配置**：生产参数调优
- [ ] **安全配置检查**：安全策略验证
- [ ] **监控告警配置**：运维监控设置
- [ ] **灰度发布验证**：小范围功能验证
- [ ] **全量发布**：正式上线

## 14. 风险评估和预案

### 14.1 技术风险

- **风险**：SSE连接数过多导致服务器压力
- **预案**：连接数限制 + 自动清理 + 负载均衡

- **风险**：AI服务响应慢影响用户体验
- **预案**：超时处理 + 降级方案 + 缓存机制

- **风险**：小程序兼容性问题
- **预案**：降级到轮询模式 + 多种实现方案

### 14.2 业务风险

- **风险**：现有功能受影响
- **预案**：完全兼容现有接口 + 开关控制

- **风险**：用户体验不佳
- **预案**：A/B测试 + 用户反馈收集 + 快速迭代

## 15. 总结

本设计方案提供了一个完整的流式响应解决方案，具有以下优势：

1. **技术成熟**：基于成熟的SSE技术和Spring Boot生态
2. **完全兼容**：不影响现有功能，可平滑升级
3. **扩展性强**：支持多种消息类型和未来功能扩展
4. **性能优异**：针对大并发场景进行了深度优化
5. **小程序友好**：提供了完整的小程序集成方案
6. **运维友好**：完善的监控、日志和错误处理机制

该方案可以显著提升用户的AI对话体验，为业务发展提供强有力的技术支撑。 