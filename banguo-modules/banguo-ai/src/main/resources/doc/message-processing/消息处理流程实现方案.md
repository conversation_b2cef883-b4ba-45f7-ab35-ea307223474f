# 消息处理流程实现方案

## 概述

本文档描述了 `processMessage` 方法的完整实现方案，包括Redis会话管理、数据库操作、MQ消息处理等各个环节。

## 处理流程

### 1. 整体流程图

```
客服消息 → JSON解析 → Redis会话管理 → 数据库保存 → MQ发送 → 异步处理
```

### 2. 详细步骤

#### 步骤1: JSON消息解析

- 使用 `MessageConverter` 将微信JSON消息转换为 `MessageHistoryDTO`
- 支持文本消息和图片消息的解析
- 验证消息格式和必要字段

#### 步骤2: Redis会话管理

- 根据 `open_id` 从Redis中查找现有会话
- 如果存在且未过期（1小时内），使用现有 `sessionId`
- 如果不存在或已过期，生成新的 `sessionId` 并保存到Redis
- 设置1小时过期时间，每次访问时自动延长

#### 步骤3: 消息记录保存

- 将 `MessageHistoryDTO` 保存到 `message_history` 表
- 设置会话ID、消息ID、发送方、接收方等信息
- 记录消息类型、内容、时间戳等

#### 步骤4: 会话记录管理

- 检查 `message_session` 表中是否存在该 `sessionId` 的记录
- 如果存在：更新最后消息时间和消息数量
- 如果不存在：创建新的会话记录，包含用户信息、会话状态等

#### 步骤5: MQ消息发送

- 创建 `MessageProcessMqDTO` 对象
- 根据是否为新会话设置不同的处理类型
- 通过 `MessageProcessProducer` 发送到MQ

#### 步骤6: 异步处理

- `MessageProcessConsumer` 接收MQ消息
- 根据处理类型执行不同的业务逻辑
- 调用AI服务生成回复、保存回复消息等

## 技术实现

### 1. Redis会话管理

**工具类**: `SessionRedisUtils`

- Key格式: `ai:session:{open_id}`
- 过期时间: 1小时
- 支持会话ID的增删改查和过期时间管理

**核心方法**:

```java
// 获取会话ID
String getSessionIdByOpenId(String openId)

// 保存会话ID
void saveSessionId(String openId, String sessionId)

// 延长过期时间
void extendSessionExpire(String openId)
```

### 2. 数据库操作

**消息记录表** (`message_history`):

- 存储所有客服消息记录
- 包含会话ID、消息ID、发送方、接收方、消息类型、内容等

**会话记录表** (`message_session`):

- 存储用户会话信息
- 包含会话ID、用户open_id、会话状态、最后消息时间、消息数量等

**事务管理**:

- 使用 `@Transactional` 注解确保数据一致性
- 消息记录和会话记录的保存在同一事务中

### 3. MQ消息处理

**生产者**: `MessageProcessProducer`

- 使用 `StreamBridge` 发送消息
- 支持延迟消息和普通消息
- 包含消息ID、处理类型等信息

**消费者**: `MessageProcessConsumer`

- 实现 `Consumer<MessageProcessDo>` 接口
- 根据处理类型执行不同的业务逻辑
- 支持新会话和继续会话两种处理模式

**消息DTO**: `MessageProcessDo`

- 包含会话ID、消息ID、消息记录ID、处理类型等
- 支持新会话和继续会话的静态工厂方法
- 精简设计，其他数据通过数据库查询获取

### 4. 配置管理

**MQ配置** (`application.yml`):

```yaml
spring:
  cloud:
    stream:
      function:
        definition: messageProcessConsumer
      bindings:
        messageProcessConsumer-in-0:
          destination: message-process-topic
          group: ai-message-process-group
        messageProcessProducer-out-0:
          destination: message-process-topic
      rocketmq:
        binder:
          name-server: ${ROCKETMQ_NAMESRV_ADDR:127.0.0.1:9876}
          group: ai-group
```

## 关键特性

### 1. 会话连续性

- 同一用户1小时内的消息使用相同的会话ID
- 自动延长会话过期时间
- 支持会话状态的更新和管理

### 2. 数据一致性

- 使用数据库事务确保消息记录和会话记录的一致性
- Redis操作失败不影响主流程
- MQ发送失败不影响数据保存

### 3. 异步处理

- 消息保存和MQ发送分离
- 支持AI服务的异步调用
- 提高系统响应性能

### 4. 错误处理

- 完善的异常处理和日志记录
- 消息解析失败时的优雅降级
- 数据库操作失败时的回滚机制

### 5. 扩展性

- 支持多种消息类型的扩展
- MQ消息格式的统一设计
- 消费者处理逻辑的模块化

## 测试验证

### 1. 单元测试

- `SupportMessageServiceTest` 测试完整的消息处理流程
- 支持文本消息、图片消息、无效消息的测试
- 测试同一用户连续发送消息的会话连续性

### 2. 测试场景

- 新用户首次发送消息
- 现有用户继续发送消息
- 会话过期后重新建立会话
- 无效消息格式的处理
- 数据库操作失败的处理

## 部署说明

### 1. 依赖要求

- Redis服务（用于会话管理）
- RocketMQ服务（用于消息队列）
- MySQL数据库（用于数据存储）

### 2. 配置要求

- 添加 `xianjian-common-rocketmq` 依赖
- 配置Redis连接信息
- 配置RocketMQ连接信息
- 配置数据库连接信息

### 3. 监控建议

- 监控Redis会话缓存的使用情况
- 监控MQ消息的发送和消费情况
- 监控数据库操作的性能指标
- 监控消息处理的成功率

## 注意事项

1. **会话过期时间**: 当前设置为1小时，可根据业务需求调整
2. **MQ重试机制**: 建议配置MQ的重试策略和死信队列
3. **数据备份**: 定期备份消息记录和会话记录数据
4. **性能优化**: 考虑Redis集群和数据库读写分离
5. **安全考虑**: 对用户open_id等敏感信息进行加密存储

## 后续优化

1. **AI集成**: 在消费者中集成AI服务生成智能回复
2. **消息推送**: 实现客服消息的自动回复功能
3. **统计分析**: 添加消息处理的统计和分析功能
4. **监控告警**: 完善系统监控和异常告警机制
5. **性能调优**: 根据实际使用情况优化系统性能 