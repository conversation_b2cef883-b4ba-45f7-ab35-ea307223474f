# 消息类型处理指南

## 🎯 概述

系统现在支持多种消息类型的差异化处理，每种消息类型都有专门的处理器来处理不同的业务逻辑。本指南将详细介绍如何使用和扩展这些消息处理器。

## 📊 消息类型架构

### 架构设计

```
┌─────────────────────────────────────────────────────────────┐
│                    StreamChatController                      │
├─────────────────────────────────────────────────────────────┤
│                 MessageProcessorFactory                     │
├─────────────────────────────────────────────────────────────┤
│  TextProcessor  │ CommandProcessor │ ImageProcessor │ ...   │
├─────────────────────────────────────────────────────────────┤
│            IMessageProcessor 接口                           │
└─────────────────────────────────────────────────────────────┘
```

### 已实现的处理器

| 消息类型    | 处理器                     | 功能描述    |
|---------|-------------------------|---------|
| TEXT    | TextMessageProcessor    | AI对话处理  |
| COMMAND | CommandMessageProcessor | 命令解析和执行 |
| IMAGE   | ImageMessageProcessor   | 图像识别和分析 |

## 🔧 使用指南

### 1. 文本消息 (TEXT)

文本消息是最常见的类型，直接与AI进行对话。

#### API调用示例

```bash
# GET方式
curl -H "Accept: text/event-stream" \
  "http://localhost:9211/ai/support/message/chat?sessionId=test-session&openId=user-1&message=你好，我想了解产品信息&messageType=TEXT"

# POST方式
curl -X POST \
  -H "Content-Type: application/json" \
  -H "Accept: text/event-stream" \
  -d '{
    "sessionId": "test-session",
    "openId": "user-1",
    "content": "你好，我想了解产品信息",
    "messageType": "TEXT",
    "options": {
      "enableTyping": true,
      "typingDelay": 50,
      "chunkSize": 10
    }
  }' \
  http://localhost:9211/ai/support/message/chat
```

#### 响应事件流程

```
event: started → typing → chunk → chunk → ... → completed
```

#### 小程序调用示例

```javascript
// utils/ChatClient.js
const streamClient = new StreamChatClient('https://api.example.com');

// 发送文本消息
await streamClient.connect('session-123', 'user-456', '你好', {
  messageType: 'TEXT',
  enableTyping: true,
  typingDelay: 50
});
```

### 2. 命令消息 (COMMAND)

命令消息用于执行特定的系统操作，支持多种内置命令。

#### 支持的命令

| 命令         | 参数                      | 功能     |
|------------|-------------------------|--------|
| `/help`    | 无                       | 显示帮助信息 |
| `/status`  | 无                       | 查看会话状态 |
| `/history` | [数量]                    | 查看消息历史 |
| `/clear`   | 无                       | 清空会话历史 |
| `/info`    | [session\|user\|system] | 查看信息   |
| `/restart` | 无                       | 重启会话   |

#### API调用示例

```bash
# 查看帮助
curl -H "Accept: text/event-stream" \
  "http://localhost:9211/ai/support/message/chat?sessionId=cmd-session&openId=user-1&message=/help&messageType=COMMAND"

# 查看会话状态
curl -H "Accept: text/event-stream" \
  "http://localhost:9211/ai/support/message/chat?sessionId=cmd-session&openId=user-1&message=/status&messageType=COMMAND"

# 查看历史消息
curl -H "Accept: text/event-stream" \
  "http://localhost:9211/ai/support/message/chat?sessionId=cmd-session&openId=user-1&message=/history 20&messageType=COMMAND"
```

#### 小程序调用示例

```javascript
// 执行命令
await streamClient.connect('session-123', 'user-456', '/help', {
  messageType: 'COMMAND'
});

// 处理命令响应
streamClient.on('chunk', (event) => {
  // 命令响应通常包含格式化的文本
  console.log('命令输出:', event.data);
});
```

### 3. 图片消息 (IMAGE)

图片消息用于图像识别和分析，支持多种图片格式。

#### 支持的图片格式

- JPG/JPEG
- PNG
- GIF
- WebP
- BMP

#### API调用示例

```bash
# 图片分析
curl -X POST \
  -H "Content-Type: application/json" \
  -H "Accept: text/event-stream" \
  -d '{
    "sessionId": "img-session",
    "openId": "user-1",
    "content": "https://example.com/image.jpg",
    "messageType": "IMAGE"
  }' \
  http://localhost:9211/ai/support/message/chat
```

#### 响应事件流程

```
started → progress(30%) → progress(60%) → progress(80%) → chunk → chunk → ... → progress(100%) → completed
```

#### 小程序调用示例

```javascript
// 上传并分析图片
await streamClient.connect('session-123', 'user-456', 'https://example.com/photo.jpg', {
  messageType: 'IMAGE'
});

// 处理分析进度
streamClient.on('progress', (event) => {
  console.log('分析进度:', event.progress + '%');
  console.log('当前状态:', event.data);
});

// 处理分析结果
streamClient.on('chunk', (event) => {
  console.log('分析结果片段:', event.data);
});
```

## 🛠️ 扩展指南

### 添加新的消息类型

#### 1. 定义消息类型

首先在 `MessageType` 枚举中添加新类型：

```java
// MessageType.java
/**
 * 语音消息
 */
VOICE("voice", "语音消息"),
```

#### 2. 创建处理器

创建新的处理器实现 `IMessageProcessor` 接口：

```java
@Slf4j
@Component
@RequiredArgsConstructor
public class VoiceMessageProcessor implements IMessageProcessor {
    
    @Override
    public MessageType getSupportedType() {
        return MessageType.VOICE;
    }
    
    @Override
    public MessageProcessResult processMessage(StreamChatRequestDTO request, SseEmitter emitter) {
        // 实现语音处理逻辑
        // 1. 语音转文字
        // 2. 调用AI处理
        // 3. 返回结果
        return MessageProcessResult.success("语音识别完成");
    }
}
```

#### 3. 测试新处理器

```bash
curl -H "Accept: text/event-stream" \
  "http://localhost:9211/ai/support/message/chat?sessionId=voice-session&openId=user-1&message=https://example.com/audio.mp3&messageType=VOICE"
```

### 自定义命令扩展

在 `CommandMessageProcessor` 中添加新命令：

```java
// 在 executeCommand 方法中添加新的 case
case "weather":
    return executeWeatherCommand(commandInfo.getArguments(), emitter, sessionId);

// 实现命令处理方法
private String executeWeatherCommand(String location, SseEmitter emitter, String sessionId) {
    // 查询天气信息
    // 发送实时天气数据
    return "天气查询完成";
}
```

## 📈 性能优化

### 1. 处理器缓存

```java
@Component
public class ProcessorCache {
    
    private final Map<MessageType, IMessageProcessor> cache = new ConcurrentHashMap<>();
    
    @EventListener
    public void onApplicationReady(ApplicationReadyEvent event) {
        // 预热处理器缓存
        messageProcessorFactory.getAllProcessors().forEach(cache::put);
    }
}
```

### 2. 异步处理优化

```java
@Configuration
public class MessageProcessorConfig {
    
    @Bean("messageProcessorExecutor")
    public Executor messageProcessorExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(20);
        executor.setMaxPoolSize(50);
        executor.setQueueCapacity(200);
        executor.setThreadNamePrefix("MessageProcessor-");
        return executor;
    }
}
```

### 3. 内存优化

```java
@Component
public class MessageProcessorMonitor {
    
    @Scheduled(fixedRate = 60000)
    public void monitorProcessors() {
        // 监控处理器性能
        messageProcessorFactory.getAllProcessors().forEach((type, processor) -> {
            // 记录处理器使用情况
            log.info("处理器 {} 状态正常", type);
        });
    }
}
```

## 🧪 测试用例

### 1. 单元测试

```java
@SpringBootTest
class MessageProcessorFactoryTest {
    
    @Autowired
    private MessageProcessorFactory factory;
    
    @Test
    void testTextProcessor() {
        IMessageProcessor processor = factory.getProcessor(MessageType.TEXT);
        assertThat(processor).isInstanceOf(TextMessageProcessor.class);
    }
    
    @Test
    void testCommandProcessor() {
        IMessageProcessor processor = factory.getProcessor(MessageType.COMMAND);
        assertThat(processor).isInstanceOf(CommandMessageProcessor.class);
    }
}
```

### 2. 集成测试

```bash
#!/bin/bash
# 测试脚本

# 测试文本消息
echo "测试文本消息..."
curl -s -H "Accept: text/event-stream" \
  "http://localhost:9211/ai/support/message/chat?sessionId=test-1&openId=user-1&message=你好&messageType=TEXT" \
  | head -10

# 测试命令消息
echo "测试命令消息..."
curl -s -H "Accept: text/event-stream" \
  "http://localhost:9211/ai/support/message/chat?sessionId=test-2&openId=user-1&message=/help&messageType=COMMAND" \
  | head -10

# 测试图片消息
echo "测试图片消息..."
curl -s -X POST \
  -H "Content-Type: application/json" \
  -H "Accept: text/event-stream" \
  -d '{"sessionId":"test-3","openId":"user-1","content":"https://example.com/test.jpg","messageType":"IMAGE"}' \
  http://localhost:9211/ai/support/message/chat \
  | head -10
```

## 🚀 最佳实践

### 1. 处理器设计原则

- **单一职责**：每个处理器只负责一种消息类型
- **无状态**：处理器应该是无状态的，便于并发
- **异常安全**：妥善处理异常，避免影响其他处理器
- **性能优化**：使用异步处理，避免阻塞

### 2. 错误处理

```java
@Override
public MessageProcessResult processMessage(StreamChatRequestDTO request, SseEmitter emitter) {
    try {
        // 处理逻辑
        return MessageProcessResult.success(result);
    } catch (ValidationException e) {
        // 业务验证异常
        return MessageProcessResult.error(e.getMessage(), "VALIDATION_ERROR");
    } catch (Exception e) {
        // 其他异常
        log.error("处理器异常", e);
        return MessageProcessResult.error("处理失败", "PROCESS_ERROR");
    }
}
```

### 3. 监控和日志

```java
@Component
public class MessageProcessorAspect {
    
    @Around("@annotation(ProcessorMonitor)")
    public Object monitor(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        try {
            Object result = joinPoint.proceed();
            long duration = System.currentTimeMillis() - startTime;
            log.info("处理器执行时间: {}ms", duration);
            return result;
        } catch (Exception e) {
            log.error("处理器执行异常", e);
            throw e;
        }
    }
}
```

## 📝 总结

通过消息类型处理器架构，系统实现了：

1. **可扩展性**：轻松添加新的消息类型处理器
2. **可维护性**：每种消息类型的逻辑独立维护
3. **高性能**：异步处理和工厂模式优化
4. **灵活性**：支持复杂的业务逻辑定制

这种设计让系统能够灵活应对不同的业务场景，同时保持代码的清晰和可维护性。 