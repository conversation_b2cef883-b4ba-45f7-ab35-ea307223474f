```mermaid
flowchart TD
    A[开始: insertByBo方法] --> B[获取分布式锁]
    B --> C{前置校验}
    
    C --> C1[检查配货商品限制]
    C1 --> C2[检查特价商品市场价]
    C2 --> C3[验证毛重净重]
    C3 --> C4[检查品类是否禁用]
    C4 --> C5[检查补贴价格限制]
    C5 --> C6[转换商品规格]
    C6 --> C7[获取平台商品信息]
    C7 --> C8[检查平台商品扩展属性]
    
    C8 --> D[数据准备阶段]
    D --> D1[设置售后类型]
    D1 --> D2[设置免检标识]
    D2 --> D3[处理产地信息]
    D3 --> D4[计算特价率]
    D4 --> D5[转换BO为实体对象]
    
    D5 --> E[总仓信息处理]
    E --> E1[获取总仓信息]
    E1 --> E2{总仓是否存在且启用?}
    E2 -->|否| E3[抛出异常]
    E2 -->|是| E4[设置总仓相关属性]
    
    E4 --> F[供应商信息处理]
    F --> F1[获取供应商信息]
    F1 --> F2{供应商是否存在?}
    F2 -->|否| F3[抛出异常]
    F2 -->|是| F4[检查子账户管控状态]
    F4 --> F5[检查坑位限制]
    F5 --> F6[设置供应商属性]
    
    F6 --> G[销售日期处理]
    G --> G1[计算销售日期]
    G1 --> G2[设置上架/下架时间]
    
    G2 --> H[重复商品检查]
    H --> H1[检查是否存在相同规格商品]
    H1 --> H2{存在重复商品?}
    H2 -->|是| H3[根据规则决定是否抛出异常]
    H2 -->|否| H4[设置平台商品属性]
    
    H4 --> I[供应商商品管理]
    I --> I1[查询现有供应商商品]
    I1 --> I2{供应商商品是否存在?}
    I2 -->|否| I3[创建新供应商商品]
    I2 -->|是| I4[更新现有供应商商品]
    I3 --> I5[设置供应商商品编码]
    I4 --> I5
    I5 --> I6[设置批次商品关联]
    
    I6 --> J[批次商品编码生成]
    J --> J1[生成批次商品编码]
    J1 --> J2[设置扫描识别码]
    J2 --> J3[拼接商品全称]
    
    J3 --> K[专区商品处理]
    K --> K1{是否为专区商品?}
    K1 -->|是| K2[复制专区属性]
    K1 -->|否| K3[跳过]
    K2 --> K3
    
    K3 --> L[保存批次商品]
    L --> L1[插入批次商品记录]
    L1 --> L2[同步创建SKU记录]
    L2 --> L3[处理历史批次隐藏]
    
    L3 --> M[库存管理]
    M --> M1[创建库存记录]
    M1 --> M2[设置初始库存]
    
    M2 --> N[操作日志记录]
    N --> N1[插入审核日志]
    
    N1 --> O[文件处理]
    O --> O1{是否有文件?}
    O1 -->|是| O2[处理文件URL]
    O2 --> O3[插入文件记录]
    O3 --> O4[文件校验]
    O1 -->|否| O4
    O4 --> O5[设置禁用城市仓]
    
    O5 --> P[后续处理]
    P --> P1{商品状态是否为上架?}
    P1 -->|是| P2[处理二级目录数据]
    P1 -->|否| P3[跳过]
    P2 --> P4{商品状态是否为待审核?}
    P4 -->|是| P5[发送延迟审核消息]
    P4 -->|否| P6[跳过]
    P3 --> P4
    P5 --> P6
    
    P6 --> Q[返回批次商品ID]
    Q --> R[结束]
    
    E3 --> R
    F3 --> R
    H3 --> R
```
