# 般果微服务项目功能清单

## 项目概述

般果微服务项目是一个基于Spring Boot + Dubbo的分布式微服务架构系统，主要服务于农产品集采平台业务。项目采用模块化设计，包含API层、业务模块层、部署配置层等多个层次。

## 技术架构

- **框架**: Spring Boot + Dubbo + MyBatis Plus
- **数据库**: MySQL
- **缓存**: Redis
- **消息队列**: RocketMQ
- **支付渠道**: 平安云支付、微信B2B支付
- **文件存储**: OSS
- **监控**: LogTube
- **部署**: Docker + Kubernetes

## 子项目功能清单

### 1. API层 (banguo-api)

#### 1.1 基础服务API (banguo-api-basic)

- **消息通知服务** (RemoteMessageNotifyService)
    - 统一消息发送接口
    - 批量消息发送
    - 消息通知V2版本支持

- **区域仓库服务** (RemoteRegionWhService)
    - 总仓信息查询
    - 销售日期管理
    - 库存清理时间配置

- **物流服务** (RemoteRegionLogisticsService)
    - 基础物流运费查询
    - 城市仓用户物流线绑定
    - 提货点权限管理
    - 物流线配置验证

- **免运免代服务** (RemoteRuleFreightServiceService)
    - 服务费计算
    - 运费计算
    - 商品运费规则查询

- **客户报损统计** (RemoteRcsService)
    - 客户报损统计查询
    - 报损记录副本管理
    - 统计数据持久化

- **导出任务服务** (RemoteExportJobService)
    - 导出任务提交
    - 任务执行状态管理
    - 定时任务触发

- **异地物流服务** (RemoteOffsiteLogisticsService)
    - 基采物流运费查询
    - 异地货物流计划管理

- **装卸队服务** (RemotePortageTeamService)
    - 装卸队信息查询
    - 装卸队员管理

- **供应商服务** (RemoteSupplierService)
    - 供应商信息查询
    - 供应商坑位数量管理
    - 供应商状态管理
    - 新注册供应商查询

- **城市仓服务** (RemoteCityWhService)
    - 城市仓信息查询
    - 城市仓与总仓关系管理

#### 1.2 订单服务API (banguo-api-order)

- **订单服务** (RemoteOrderService)
    - 优惠补贴金额计算
    - 供应商更换处理
    - 订单支付状态更新
    - 订单取消处理
    - 订单信息查询
    - 城市仓销量统计

- **活动服务** (RemoteActivityService)
    - 活动记录查询

- **供应商订单结算** (RemoteSupOrderSettleService)
    - 结算日期刷新通知
    - 订单结算任务通知

#### 1.3 商品服务API (banguo-api-product)

- **分类服务** (RemoteCategoryService)
    - 分类信息查询
    - 商品名称模糊查询
    - 分类层级管理

- **商品服务** (RemoteSpuService)
    - 商品信息查询
    - 商品分类关联查询

#### 1.4 营销服务API (banguo-api-marketing)

- **分销服务** (RemoteDistributionService)
    - 订单分销信息获取
    - 分销活动匹配
    - 分销结算处理
    - 分销订单取消

#### 1.5 交易服务API (banguo-api-trade)

- **支付服务** (RemotePaymentService)
    - 订单支付处理
    - 支付查询
    - 支付关闭
    - 支付退款
    - 退款查询
    - 微信B2B验证

- **机构关系服务** (RemoteOrgRelationService)
    - 账户开通
    - 银行绑定
    - 转账授权
    - 机构管控校验

- **转账服务** (RemoteTransferService)
    - 划转单生成
    - 供应商更换转账
    - 划转单审核
    - 划转单查询

- **流水服务** (RemoteTransAvailService)
    - 流水到可用余额转换

#### 1.6 通用服务API (banguo-api-common)

- **基础服务接口** (IService)
    - 通用异常处理
    - 服务基础功能

#### 1.7 BI服务API (banguo-api-bi)

- 商业智能数据接口
- 报表数据查询
- 统计分析接口

### 2. 业务模块层 (banguo-modules)

#### 2.1 基础服务模块 (banguo-basic)

##### 2.1.1 客户管理

- **客户信息管理**
    - 客户注册、编辑、查询
    - 客户状态管理
    - 客户城市仓切换
    - 客户提货点管理

- **客户统计服务**
    - 客户购买行为统计
    - 客户报损统计
    - 客户活跃度分析

##### 2.1.2 供应商管理

- **供应商信息管理**
    - 供应商注册、审核、编辑
    - 供应商状态管理
    - 供应商坑位数量限制
    - 供应商别名管理

- **供应商服务**
    - 供应商送货单管理
    - 供应商结算管理
    - 供应商权限控制

##### 2.1.3 仓库管理

- **总仓管理**
    - 总仓信息配置
    - 销售时间设置
    - 库存清理规则
    - 总仓状态管理

- **城市仓管理**
    - 城市仓信息管理
    - 城市仓与总仓关系
    - 城市仓管理员设置
    - 城市仓提货点管理

##### 2.1.4 物流管理

- **基础物流**
    - 物流线配置
    - 运费计算规则
    - 物流计划管理
    - 装卸队管理

- **异地物流**
    - 基采物流配置
    - 异地货物流计划
    - 市场定义管理

##### 2.1.5 银行管理

- **银行信息管理**
    - 银行列表维护
    - 银行节点管理
    - 银行卡绑定
    - 转账授权管理

##### 2.1.6 数据运维

- **数据操作**
    - 客户城市仓切换
    - 城市仓管理员更换
    - 子用户城市仓切换

#### 2.2 订单模块 (banguo-order)

##### 2.2.1 订单管理

- **订单创建与处理**
    - 订单创建验证
    - 订单状态管理
    - 订单支付处理
    - 订单取消处理

- **订单查询**
    - 订单列表查询
    - 订单详情查询
    - 订单统计报表
    - 订单导出功能

##### 2.2.2 订单项管理

- **订单项处理**
    - 订单项创建
    - 订单项状态管理
    - 订单项价格调整
    - 订单项供应商更换

##### 2.2.3 提货管理

- **提货单管理**
    - 提货单生成
    - 提货单状态跟踪
    - 提货单详情查询
    - 提货单导出

- **提货记录**
    - 提货记录创建
    - 提货记录详情
    - 提货记录统计

##### 2.2.4 分货管理

- **分货记录**
    - 分货记录创建
    - 分货详情管理
    - 分货状态跟踪
    - 分货统计报表

##### 2.2.5 缺货管理

- **缺货记录**
    - 缺货单创建
    - 缺货原因分析
    - 缺货处理流程
    - 缺货统计报表

##### 2.2.6 多货管理

- **多货记录**
    - 多货单创建
    - 多货处理流程
    - 多货统计报表

##### 2.2.7 报损管理

- **报损记录**
    - 报损单创建
    - 报损审核流程
    - 报损统计报表
    - 报损原因分析

##### 2.2.8 判责管理

- **判责记录**
    - 判责单创建
    - 判责流程管理
    - 判责结果处理
    - 判责统计报表

##### 2.2.9 供应商送货

- **送货单管理**
    - 送货单创建
    - 送货单状态跟踪
    - 送货单详情查询
    - 送货单统计

##### 2.2.10 车型管理

- **车型信息**
    - 车型添加、编辑、删除
    - 车型查询
    - 车型统计

##### 2.2.11 活动记录

- **活动管理**
    - 活动记录创建
    - 活动查询
    - 活动统计

#### 2.3 商品模块 (banguo-product)

##### 2.3.1 商品管理

- **平台商品**
    - 商品创建、编辑、删除
    - 商品状态管理
    - 商品分类管理
    - 商品查询统计

- **供应商商品**
    - 供应商商品管理
    - 商品价格管理
    - 商品库存管理
    - 商品上下架

##### 2.3.2 分类管理

- **分类体系**
    - 分类创建、编辑、删除
    - 分类层级管理
    - 分类状态管理
    - 分类统计查询

##### 2.3.3 采购管理

- **采购小组**
    - 采购小组创建、编辑
    - 采购员管理
    - 采购目标设置
    - 采购统计报表

- **采购员分组**
    - 采购员分组管理
    - 分组权限控制
    - 分组统计报表

##### 2.3.4 商品规格

- **规格管理**
    - 商品规格定义
    - 规格模板管理
    - 规格关联商品

#### 2.4 营销模块 (banguo-marketing)

##### 2.4.1 优惠券管理

- **优惠券配置**
    - 优惠券创建、编辑、删除
    - 优惠券类型管理（代金券、赠送券、折扣券、商品补贴）
    - 优惠券使用规则配置
    - 优惠券投放策略

- **优惠券发放**
    - 批量发放优惠券
    - 定向发放优惠券
    - 自动发放优惠券
    - 发放记录管理

- **优惠券使用**
    - 优惠券使用验证
    - 优惠券计算规则
    - 优惠券结算处理
    - 优惠券退款处理

##### 2.4.2 分销管理

- **分销活动**
    - 分销活动创建
    - 分销规则配置
    - 分销佣金计算
    - 分销统计报表

##### 2.4.3 营销策略

- **营销规则**
    - 满减规则
    - 折扣规则
    - 赠品规则
    - 组合优惠

#### 2.5 交易模块 (banguo-trade)

##### 2.5.1 支付管理

- **支付处理**
    - 多种支付渠道支持（平安云支付、微信B2B、余额支付）
    - 支付订单创建
    - 支付状态跟踪
    - 支付回调处理

- **支付查询**
    - 支付订单查询
    - 支付状态查询
    - 支付统计报表

##### 2.5.2 退款管理

- **退款处理**
    - 退款申请处理
    - 退款审核流程
    - 退款状态跟踪
    - 退款统计报表

##### 2.5.3 账户管理

- **客户账户**
    - 客户账户创建
    - 账户余额管理
    - 账户流水记录
    - 账户冻结解冻

- **机构账户**
    - 机构账户管理
    - 银行绑定管理
    - 转账授权管理
    - 账户状态管理

##### 2.5.4 转账管理

- **转账处理**
    - 转账申请处理
    - 转账审核流程
    - 转账状态跟踪
    - 转账统计报表

##### 2.5.5 对账管理

- **对账处理**
    - 日对账数据导出
    - 对账文件处理
    - 对账异常处理
    - 对账统计报表

#### 2.6 AI智能客服模块 (banguo-ai)

##### 2.6.1 智能客服

- **客服机器人**
    - 智能问答系统
    - 多轮对话管理
    - 上下文继承
    - 意图识别

- **知识库管理**
    - 知识库维护
    - 问题分类管理
    - 答案模板管理
    - 知识库更新

##### 2.6.2 人机协作

- **人工转接**
    - 智能转人工
    - 人工客服分配
    - 会话记录管理
    - 服务质量评估

##### 2.6.3 业务集成

- **订单查询**
    - 订单状态查询
    - 订单详情展示
    - 订单操作指导

- **售后服务**
    - 报损处理指导
    - 退款流程指导
    - 问题解决方案

#### 2.7 BI商业智能模块 (banguo-bi)

##### 2.7.1 数据统计

- **订单统计**
    - 订单量统计
    - 订单金额统计
    - 订单趋势分析
    - 订单地域分布

- **客户统计**
    - 客户数量统计
    - 客户活跃度分析
    - 客户购买行为分析
    - 客户价值分析

- **商品统计**
    - 商品销量统计
    - 商品热度分析
    - 商品分类统计
    - 商品趋势分析

##### 2.7.2 报表管理

- **报表生成**
    - 自动报表生成
    - 报表模板管理
    - 报表导出功能
    - 报表订阅功能

##### 2.7.3 数据分析

- **趋势分析**
    - 销售趋势分析
    - 客户增长分析
    - 商品热度分析
    - 地域分布分析

#### 2.8 任务调度模块 (banguo-job)

##### 2.8.1 定时任务

- **系统任务**
    - 数据清理任务
    - 状态更新任务
    - 报表生成任务
    - 通知发送任务

##### 2.8.2 任务管理

- **任务配置**
    - 任务调度配置
    - 任务执行监控
    - 任务日志管理
    - 任务异常处理

#### 2.9 开放API模块 (banguo-openapi)

- **API网关**
    - 接口统一管理
    - 接口权限控制
    - 接口限流控制
    - 接口监控统计

## 核心业务流程

### 1. 订单流程

1. 客户选择商品加入购物车
2. 提交订单，系统验证库存和价格
3. 客户支付订单
4. 系统生成提货单
5. 供应商送货到总仓
6. 总仓分货到城市仓
7. 客户到城市仓提货
8. 完成订单

### 2. 支付流程

1. 客户选择支付方式
2. 系统创建支付订单
3. 调用第三方支付接口
4. 支付成功后更新订单状态
5. 系统进行分账处理
6. 完成资金结算

### 3. 营销流程

1. 创建营销活动
2. 配置优惠券规则
3. 发放优惠券给目标客户
4. 客户下单时使用优惠券
5. 系统计算优惠金额
6. 完成优惠券结算

### 4. 智能客服流程

1. 客户发起咨询
2. 智能客服识别意图
3. 根据知识库回答问题
4. 如需人工服务，转接人工客服
5. 记录会话内容
6. 评估服务质量

## 技术特性

### 1. 微服务架构

- 服务拆分合理，职责清晰
- 服务间通过Dubbo进行RPC调用
- 支持服务注册与发现
- 支持服务熔断与降级

### 2. 数据一致性

- 分布式事务处理
- 最终一致性保证
- 数据补偿机制
- 幂等性设计

### 3. 高可用设计

- 服务集群部署
- 负载均衡策略
- 故障自动切换
- 监控告警机制

### 4. 安全性

- 接口权限控制
- 数据脱敏处理
- 操作日志记录
- 敏感信息加密

### 5. 性能优化

- 数据库连接池
- Redis缓存策略
- 异步处理机制
- 分页查询优化

## 部署架构

### 1. 容器化部署

- Docker容器化
- Kubernetes编排
- 服务网格管理
- 自动扩缩容

### 2. 监控体系

- 应用性能监控
- 业务指标监控
- 系统资源监控
- 日志收集分析

### 3. 配置管理

- 配置中心管理
- 环境隔离
- 配置热更新
- 配置版本控制

## 业务价值

### 1. 提升效率

- 自动化订单处理
- 智能客服减少人工成本
- 数据驱动决策
- 流程优化改进

### 2. 增强体验

- 便捷的购物流程
- 智能的客服服务
- 个性化的营销推荐
- 透明的订单跟踪

### 3. 降低成本

- 减少人工干预
- 优化库存管理
- 提高资金周转
- 降低运营成本

### 4. 数据价值

- 客户行为分析
- 商品销售趋势
- 市场机会识别
- 业务决策支持

## 总结

般果微服务项目是一个功能完整、架构合理的农产品集采平台系统。通过模块化设计和微服务架构，实现了高可用、高性能、易扩展的技术目标。系统涵盖了从商品管理、订单处理、支付结算到客户服务的完整业务流程，为农产品供应链的数字化转型提供了强有力的技术支撑。 