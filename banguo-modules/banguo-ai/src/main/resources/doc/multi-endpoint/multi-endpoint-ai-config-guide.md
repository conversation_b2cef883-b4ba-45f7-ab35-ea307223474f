# 多端AI配置使用指南

## 概述

本系统支持四个不同端的AI智能客服配置：
- **customer**: 客户端（般果集采平台）
- **city**: 城市仓（般果城市仓）
- **supplier**: 供应商（般果供应商）
- **warehouse**: 总仓（般果总仓）

每个端都可以配置独立的百练AI智能体，通过前端传递的32位`clientId`来自动选择对应的AI配置。

## 实际配置信息

### App ID 配置
- **般果集采平台**: `a431ec41de4d4baa933346bb4eb592e7`
- **般果城市仓**: `4c3062c2dfb046b296d5f22db21feca9`
- **般果供应商**: `121ca1ff775b43b9ac08b4733081c072`
- **般果总仓**: `5d2650d38d714ec1a6235070c6db820b`

### API Key
所有端统一使用: `sk-xxx`

### Client ID 映射关系

#### 测试环境
- `428a8310cd442757ae699df5d894f051` → 般果集采平台（customer）
- `********************************` → 般果总仓（warehouse）
- `cx8aklfwcd442757xd6fgd312894f051` → 般果供应商（supplier）
- `o86tjw3owfqh7z7ev10m7wwcgzfzy2b5` → 般果城市仓（city）
- `o76tj89sdfsiegzfz98sl322j9d23k32` → 般果集采测试平台（customer）

#### 正式环境
- `ws9vibft2203pgxuj7foav0eo9r6eq7f` → 般果集采平台（customer）
- `cht5u4hdtmi6tj2740f3o4o2ww3qh2ie` → 般果城市仓（city）
- `u1zazpc5jartv40ufucgteoquttqziu3` → 般果供应商（supplier）
- `********************************` → 般果总仓（warehouse）

## 配置结构

### 1. 基础配置

```yaml
ai:
  bailian:
    # 智能体配置 - 支持四个端
    support-app:
      # 客户端配置（般果集采平台）
      customer:
        app-id: ${AI_BAILIAN_CUSTOMER_APP_ID:a431ec41de4d4baa933346bb4eb592e7}
        api-key: ${AI_BAILIAN_CUSTOMER_API_KEY:sk-xxx}
        name: ${AI_BAILIAN_CUSTOMER_NAME:般果集采平台智能客服}
      # 城市仓配置
      city:
        app-id: ${AI_BAILIAN_CITY_APP_ID:4c3062c2dfb046b296d5f22db21feca9}
        api-key: ${AI_BAILIAN_CITY_API_KEY:sk-xxx}
        name: ${AI_BAILIAN_CITY_NAME:般果城市仓智能客服}
      # 供应商配置
      supplier:
        app-id: ${AI_BAILIAN_SUPPLIER_APP_ID:121ca1ff775b43b9ac08b4733081c072}
        api-key: ${AI_BAILIAN_SUPPLIER_API_KEY:sk-xxx}
        name: ${AI_BAILIAN_SUPPLIER_NAME:般果供应商智能客服}
      # 总仓配置
      warehouse:
        app-id: ${AI_BAILIAN_WAREHOUSE_APP_ID:5d2650d38d714ec1a6235070c6db820b}
        api-key: ${AI_BAILIAN_WAREHOUSE_API_KEY:sk-xxx}
        name: ${AI_BAILIAN_WAREHOUSE_NAME:般果总仓智能客服}
```

### 2. 客户端映射配置

```yaml
ai:
  bailian:
    # 客户端ID映射配置
    client-mapping:
      # 默认端类型（当clientId未找到映射时使用）
      default-endpoint: customer
      # 客户端ID到端类型的映射
      client-to-endpoint:
        # 测试环境
        "428a8310cd442757ae699df5d894f051": "customer"  # 般果集采平台
        "********************************": "warehouse" # 般果总仓
        "cx8aklfwcd442757xd6fgd312894f051": "supplier"  # 般果供应商
        "o86tjw3owfqh7z7ev10m7wwcgzfzy2b5": "city"      # 般果城市仓
        "o76tj89sdfsiegzfz98sl322j9d23k32": "customer"  # 般果集采测试平台
        # 正式环境
        "ws9vibft2203pgxuj7foav0eo9r6eq7f": "customer"  # 般果集采平台
        "cht5u4hdtmi6tj2740f3o4o2ww3qh2ie": "city"      # 般果城市仓
        "u1zazpc5jartv40ufucgteoquttqziu3": "supplier"  # 般果供应商
```

## 使用方法

### 1. 前端调用

前端在调用AI接口时，需要在HTTP Header中传递`ClientId`：

```javascript
// 般果集采平台调用示例（测试环境）
fetch('/ai/support/message/chat', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'ClientId': '428a8310cd442757ae699df5d894f051'  // 般果集采平台测试环境
  },
  body: JSON.stringify({
    data: { content: '你好，我想咨询产品信息' },
    messageType: 'TEXT'
  })
})

// 般果城市仓调用示例（正式环境）
fetch('/ai/support/message/chat', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'ClientId': 'cht5u4hdtmi6tj2740f3o4o2ww3qh2ie'  // 般果城市仓正式环境
  },
  body: JSON.stringify({
    data: { content: '库存查询' },
    messageType: 'TEXT'
  })
})

// 般果供应商调用示例（正式环境）
fetch('/ai/support/message/chat', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'ClientId': 'u1zazpc5jartv40ufucgteoquttqziu3'  // 般果供应商正式环境
  },
  body: JSON.stringify({
    data: { content: '商品上架咨询' },
    messageType: 'TEXT'
  })
})

// 般果总仓调用示例（通用）
fetch('/ai/support/message/chat', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'ClientId': '********************************'  // 般果总仓（测试和正式环境通用）
  },
  body: JSON.stringify({
    data: { content: '仓储管理咨询' },
    messageType: 'TEXT'
  })
})
```

### 2. 系统处理流程

1. **接收请求**: `MessageController.streamChatPost()` 接收请求
2. **提取ClientId**: 从HTTP Header中获取`ClientId`
3. **端类型识别**: `ClientMappingService` 根据`ClientId`识别端类型
4. **配置选择**: `BailianConfig` 根据端类型选择对应的AI配置
5. **AI调用**: `BailianAiChatService` 使用选定的配置调用百练AI
6. **流式响应**: 返回对应端的AI响应

### 3. 日志输出

系统会输出详细的日志信息，便于调试和监控：

```
收到POST流式聊天请求: messageType=TEXT, messageId=xxx, clientId=428a8310cd442757ae699df5d894f051, endpointType=customer, endpointName=客户端
使用客户端(customer)处理用户886的消息请求
开始使用customer端DashScope SDK流式调用百练智能体API, appId=a431ec41de4d4baa933346bb4eb592e7, sessionId=xxx, name=般果集采平台智能客服
```

## 管理接口

### 1. 查询接口

```bash
# 获取所有映射关系
GET /ai/support/client-mapping/all

# 根据clientId获取端类型
GET /ai/support/client-mapping/endpoint-type/{clientId}

# 根据clientId获取端名称
GET /ai/support/client-mapping/endpoint-name/{clientId}

# 验证clientId格式
GET /ai/support/client-mapping/validate/{clientId}

# 获取默认端类型
GET /ai/support/client-mapping/default-endpoint

# 获取支持的端类型列表
GET /ai/support/client-mapping/supported-endpoints
```

### 2. 管理接口

```bash
# 添加或更新映射关系
POST /ai/support/client-mapping/mapping?clientId=xxx&endpointType=customer

# 移除映射关系
DELETE /ai/support/client-mapping/mapping/{clientId}

# 刷新映射缓存
POST /ai/support/client-mapping/refresh
```

## 环境变量配置

推荐使用环境变量来配置不同端的API Key和App ID：

```bash
# 客户端配置（般果集采平台）
export AI_BAILIAN_CUSTOMER_APP_ID="a431ec41de4d4baa933346bb4eb592e7"
export AI_BAILIAN_CUSTOMER_API_KEY="sk-xxx"
export AI_BAILIAN_CUSTOMER_NAME="般果集采平台智能客服"

# 城市仓配置
export AI_BAILIAN_CITY_APP_ID="4c3062c2dfb046b296d5f22db21feca9"
export AI_BAILIAN_CITY_API_KEY="sk-xxx"
export AI_BAILIAN_CITY_NAME="般果城市仓智能客服"

# 供应商配置
export AI_BAILIAN_SUPPLIER_APP_ID="121ca1ff775b43b9ac08b4733081c072"
export AI_BAILIAN_SUPPLIER_API_KEY="sk-xxx"
export AI_BAILIAN_SUPPLIER_NAME="般果供应商智能客服"

# 总仓配置
export AI_BAILIAN_WAREHOUSE_APP_ID="5d2650d38d714ec1a6235070c6db820b"
export AI_BAILIAN_WAREHOUSE_API_KEY="sk-xxx"
export AI_BAILIAN_WAREHOUSE_NAME="般果总仓智能客服"
```

## 注意事项

1. **ClientId格式**: 必须是32位的字母数字字符串
2. **默认配置**: 当ClientId无效或未找到映射时，使用默认端配置
3. **缓存机制**: 映射关系会被缓存，修改后需要调用刷新接口
4. **配置验证**: 系统启动时会验证各端的配置是否正确
5. **兼容性**: 保留了旧版本的API接口，确保向后兼容

## 故障排查

### 1. 常见问题

**问题**: ClientId无效
- **现象**: 日志显示"收到无效的clientId格式"
- **解决**: 检查ClientId是否为32位字母数字字符串

**问题**: 端配置未找到
- **现象**: 日志显示"端[xxx]百练API Key未配置"
- **解决**: 检查对应端的环境变量配置

**问题**: 映射关系未生效
- **现象**: 使用了错误的端配置
- **解决**: 调用刷新缓存接口或重启服务

### 2. 调试方法

1. 查看启动日志，确认各端配置状态
2. 使用管理接口验证映射关系
3. 检查请求日志中的端类型识别结果
4. 验证环境变量配置是否正确

## 扩展说明

如需添加新的端类型，需要：
1. 在配置文件中添加新端的配置结构
2. 更新`BailianConfig`类中的相关方法
3. 在`ClientMappingServiceImpl`中添加端类型名称映射
4. 更新相关文档和示例
