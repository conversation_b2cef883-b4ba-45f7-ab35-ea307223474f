# 客户端映射配置 - 实际生产配置
# 根据实际业务需求配置clientId到端类型的映射关系

ai:
  bailian:
    # 智能体配置 - 支持四个端
    support-app:
      # 客户端配置（般果集采平台）
      customer:
        app-id: ${AI_BAILIAN_CUSTOMER_APP_ID:a431ec41de4d4baa933346bb4eb592e7}
        api-key: ${AI_BAILIAN_CUSTOMER_API_KEY:sk-xxx}
        name: ${AI_BAILIAN_CUSTOMER_NAME:般果集采平台智能客服}
      # 城市仓配置
      city:
        app-id: ${AI_BAILIAN_CITY_APP_ID:4c3062c2dfb046b296d5f22db21feca9}
        api-key: ${AI_BAILIAN_CITY_API_KEY:sk-xxx}
        name: ${AI_BAILIAN_CITY_NAME:般果城市仓智能客服}
      # 供应商配置
      supplier:
        app-id: ${AI_BAILIAN_SUPPLIER_APP_ID:121ca1ff775b43b9ac08b4733081c072}
        api-key: ${AI_BAILIAN_SUPPLIER_API_KEY:sk-xxx}
        name: ${AI_BAILIAN_SUPPLIER_NAME:般果供应商智能客服}
      # 总仓配置
      warehouse:
        app-id: ${AI_BAILIAN_WAREHOUSE_APP_ID:5d2650d38d714ec1a6235070c6db820b}
        api-key: ${AI_BAILIAN_WAREHOUSE_API_KEY:sk-xxx}
        name: ${AI_BAILIAN_WAREHOUSE_NAME:般果总仓智能客服}

    # 客户端ID映射配置
    client-mapping:
      # 默认端类型（当clientId未找到映射时使用）
      default-endpoint: customer
      # 客户端ID到端类型的映射
      client-to-endpoint:
        # === 测试环境映射 ===
        # 般果集采平台（客户端）
        "428a8310cd442757ae699df5d894f051": "customer"
        # 般果总仓
        "********************************": "warehouse"
        # 般果供应商
        "cx8aklfwcd442757xd6fgd312894f051": "supplier"
        # 般果城市仓
        "o86tjw3owfqh7z7ev10m7wwcgzfzy2b5": "city"
        # 般果集采测试平台
        "o76tj89sdfsiegzfz98sl322j9d23k32": "customer"

        # === 正式环境映射 ===
        # 般果总仓
        "********************************": "warehouse"
        # 般果供应商
        "u1zazpc5jartv40ufucgteoquttqziu3": "supplier"
        # 般果集采平台（客户端）
        "ws9vibft2203pgxuj7foav0eo9r6eq7f": "customer"
        # 般果城市仓
        "cht5u4hdtmi6tj2740f3o4o2ww3qh2ie": "city"

# 环境变量配置 - 实际生产配置
# 可以通过环境变量来配置不同端的API Key和App ID

# 客户端环境变量（般果集采平台）
# AI_BAILIAN_CUSTOMER_APP_ID=a431ec41de4d4baa933346bb4eb592e7
# AI_BAILIAN_CUSTOMER_API_KEY=sk-xxx
# AI_BAILIAN_CUSTOMER_NAME=般果集采平台智能客服

# 城市仓环境变量
# AI_BAILIAN_CITY_APP_ID=4c3062c2dfb046b296d5f22db21feca9
# AI_BAILIAN_CITY_API_KEY=sk-xxx
# AI_BAILIAN_CITY_NAME=般果城市仓智能客服

# 供应商环境变量
# AI_BAILIAN_SUPPLIER_APP_ID=121ca1ff775b43b9ac08b4733081c072
# AI_BAILIAN_SUPPLIER_API_KEY=sk-xxx
# AI_BAILIAN_SUPPLIER_NAME=般果供应商智能客服

# 总仓环境变量
# AI_BAILIAN_WAREHOUSE_APP_ID=5d2650d38d714ec1a6235070c6db820b
# AI_BAILIAN_WAREHOUSE_API_KEY=sk-xxx
# AI_BAILIAN_WAREHOUSE_NAME=般果总仓智能客服

# 使用说明：
# 1. 每个端都有独立的app-id、api-key和name配置
# 2. client-to-endpoint映射定义了32位clientId到端类型的对应关系
# 3. default-endpoint定义了当clientId未找到映射时使用的默认端类型
# 4. 前端在调用API时需要在Header中传递ClientId
# 5. 系统会根据ClientId自动选择对应端的AI配置进行处理

# 前端调用示例：

# 般果集采平台调用示例（测试环境）
# fetch('/ai/support/message/chat', {
#   method: 'POST',
#   headers: {
#     'Content-Type': 'application/json',
#     'ClientId': '428a8310cd442757ae699df5d894f051'  // 般果集采平台测试环境
#   },
#   body: JSON.stringify({
#     data: { content: '你好，我想咨询产品信息' },
#     messageType: 'TEXT'
#   })
# })

# 般果城市仓调用示例（正式环境）
# fetch('/ai/support/message/chat', {
#   method: 'POST',
#   headers: {
#     'Content-Type': 'application/json',
#     'ClientId': 'cht5u4hdtmi6tj2740f3o4o2ww3qh2ie'  // 般果城市仓正式环境
#   },
#   body: JSON.stringify({
#     data: { content: '库存查询' },
#     messageType: 'TEXT'
#   })
# })

# 般果供应商调用示例（正式环境）
# fetch('/ai/support/message/chat', {
#   method: 'POST',
#   headers: {
#     'Content-Type': 'application/json',
#     'ClientId': 'u1zazpc5jartv40ufucgteoquttqziu3'  // 般果供应商正式环境
#   },
#   body: JSON.stringify({
#     data: { content: '商品上架咨询' },
#     messageType: 'TEXT'
#   })
# })

# 般果总仓调用示例（通用）
# fetch('/ai/support/message/chat', {
#   method: 'POST',
#   headers: {
#     'Content-Type': 'application/json',
#     'ClientId': '********************************'  // 般果总仓（测试和正式环境通用）
#   },
#   body: JSON.stringify({
#     data: { content: '仓储管理咨询' },
#     messageType: 'TEXT'
#   })
# })

# 管理API示例：
# GET /ai/support/client-mapping/all - 获取所有映射关系
# GET /ai/support/client-mapping/endpoint-type/{clientId} - 获取端类型
# POST /ai/support/client-mapping/mapping?clientId=xxx&endpointType=customer - 添加映射
# DELETE /ai/support/client-mapping/mapping/{clientId} - 删除映射
# POST /ai/support/client-mapping/refresh - 刷新缓存
