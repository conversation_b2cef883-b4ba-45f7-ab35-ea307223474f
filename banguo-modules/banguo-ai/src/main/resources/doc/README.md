# 般果AI模块文档中心

本目录包含般果AI模块的所有技术文档，按照功能和用途进行了重新分类整理。

## 📁 目录结构

### 🎯 [ai-strategy/](./ai-strategy/) - AI战略规划

- **般果B2B平台AI赋能战略规划方案.md** - 整体AI战略规划
- **AI赋能业务场景分析.md** - 详细的AI业务场景分析
- **商品AI赋能场景建议.md** - 商品管理AI应用建议

### 💬 [message-processing/](./message-processing/) - 消息处理与智能客服

- **消息处理流程实现方案.md** - 消息处理技术实现
- **消息处理流程重构完成说明.md** - 重构说明
- **智能客服转人工完整文档.md** - 客服转人工流程
- **微信客服消息发送功能使用说明.md** - 微信客服功能
- **智能客服MCP服务使用说明.md** - 客服MCP服务
- **消息会话总结功能使用说明.md** - 会话总结功能
- **客服问题分类指南.md** - 问题分类指南
- 其他消息处理相关文档

### 🔧 [technical-docs/](./technical-docs/) - 技术文档与API说明

- **百练模型API调用说明.md** - 百练模型集成说明
- **xianjian-common-wechat使用文档.md** - 微信SDK使用指南
- **提货点查询MCP服务使用说明.md** - 提货点查询MCP服务
- **README-API-KEY.md** - API密钥配置说明
- **README_ORDER_QUERY.md** - 订单查询功能说明

### 🛠 [troubleshooting/](./troubleshooting/) - 问题解决方案

- **MQ常见问题分析与解决方案.md** - MQ问题排查和解决
- **BEAN_NAME_CONFLICT_SOLUTION.md** - Bean冲突解决方案

### 🏢 [business-process/](./business-process/) - 业务流程与功能说明

- **般果微服务项目功能清单.md** - 完整功能列表
- **供应商新增商品流程图.md** - 供应商业务流程

### 🗄 [database/](./database/) - 数据库相关

- **support_message.sql** - 客服消息相关表结构
- **ai_customer_service.sql** - AI客服相关表结构

### 📋 [project-management/](./project-management/) - 项目管理

- **version_memo.md** - 版本更新记录

### 🏙 [城市仓/](./城市仓/) - 城市仓库相关（原有分类）

- **GeoCalculatorUtil工具类分析文档.md** - 地理计算工具分析
- **queryCityWhList方法实现逻辑文档.md** - 城市仓查询实现

## 🔍 快速查找指南

### 按用途查找：

- **想了解AI战略和规划** → `ai-strategy/`
- **开发消息处理功能** → `message-processing/`
- **集成外部API或SDK** → `technical-docs/`
- **排查系统问题** → `troubleshooting/`
- **了解业务流程** → `business-process/`
- **设计数据库** → `database/`
- **查看版本历史** → `project-management/`

### 按角色查找：

- **产品经理** → `ai-strategy/`, `business-process/`
- **开发工程师** → `message-processing/`, `technical-docs/`, `troubleshooting/`
- **运维工程师** → `troubleshooting/`, `database/`, `project-management/`
- **架构师** → `ai-strategy/`, `message-processing/`, `technical-docs/`

## 📝 文档编写规范

1. **新增文档时请放入合适的分类目录**
2. **每个目录都有README.md说明分类标准**
3. **文档命名使用中文，便于理解**
4. **重要的技术实现文档应包含代码示例**
5. **API文档应包含参数说明和返回格式**

## 🔄 更新说明

文档已于 2024年重新整理分类，如有疑问请联系开发团队。

---
*最后更新时间：2024年7月* 