# queryCityWhList 方法实现逻辑文档

## 方法概述

`queryCityWhList` 方法用于根据用户坐标查询附近的城市仓库列表，已标记为【停用】状态。

**文件位置**:
`@banguo-modules/banguo-basic/src/main/java/cn/xianlink/basic/controller/platform/WhiteCityWhPlaceController.java:67`

## 输入参数

- `lng`: 经度 (Double, 必填)
- `lat`: 纬度 (Double, 必填)

## 核心实现流程

```mermaid
flowchart TD
    A[开始 - 接收经纬度参数] --> B[查询所有状态为1的城市仓]
    B --> C[计算每个城市仓与用户位置的距离]
    C --> D{城市仓列表是否为空?}
    D -->|是| E[返回空结果]
    D -->|否| F[查询所有总仓信息]
    F --> G[按距离升序排序城市仓列表]
    G --> H[截取前10个最近的城市仓]
    H --> I[查询城市仓关联的总仓关系]
    I --> J[为每个城市仓设置途径的总仓列表]
    J --> K[返回最终结果]
```

## 详细步骤说明

### 1. 数据查询与距离计算

```mermaid
sequenceDiagram
    participant Controller
    participant CityWhService
    participant GeoCalculatorUtil
    
    Controller->>CityWhService: selectListByStatus1()
    CityWhService-->>Controller: 返回活跃城市仓列表
    
    loop 遍历每个城市仓
        Controller->>GeoCalculatorUtil: calculateDistance(用户坐标, 城市仓坐标)
        GeoCalculatorUtil-->>Controller: 返回距离(米)
        Controller->>Controller: 创建CityWhVoApplet并设置距离
    end
```

### 2. 数据处理与关联

```mermaid
graph LR
    A[城市仓列表] --> B[按距离排序]
    B --> C[截取前10个]
    C --> D[查询总仓映射表]
    D --> E[查询城市仓-总仓关系]
    E --> F[设置途径总仓信息]
    F --> G[返回最终结果]
```

## 关键业务逻辑

1. **距离计算**: 使用 `GeoCalculatorUtil.calculateDistance()` 计算用户位置到各城市仓的直线距离
2. **数据过滤**: 只查询状态为1（启用）的城市仓
3. **性能优化**: 按距离排序后只取前10个最近的城市仓
4. **关联数据**: 为每个城市仓关联其途径的总仓信息

## 数据流转图

```mermaid
graph TB
    subgraph "输入层"
        A[用户坐标 lng, lat]
    end
    
    subgraph "数据查询层"
        B[城市仓服务]
        C[总仓服务]
        D[仓库关系服务]
    end
    
    subgraph "处理层"
        E[距离计算]
        F[排序筛选]
        G[数据组装]
    end
    
    subgraph "输出层"
        H[CityWhVoApplet列表]
    end
    
    A --> B
    A --> E
    B --> E
    E --> F
    F --> D
    C --> G
    D --> G
    G --> H
```

## 核心代码实现

### 主要步骤代码解析

1. **查询城市仓并计算距离** (第71-75行)

```java
List<CityWhVoApplet> list = cityWhService.selectListByStatus1().stream().map(e -> {
    CityWhVoApplet voApplet = MapstructUtils.convert(e, CityWhVoApplet.class);
    voApplet.setDistance((int) GeoCalculatorUtil.calculateDistance(lat, lng, 
        Double.parseDouble(e.getLat()), Double.parseDouble(e.getLng())));
    return voApplet;
}).collect(Collectors.toList());
```

2. **查询总仓映射** (第80行)

```java
Map<Long, RegionWhVo> regionWhMap = regionWhService.selectListByStatus1(0L).stream()
    .collect(Collectors.toMap(RegionWhVo::getId, Function.identity(), (key1, key2) -> key2));
```

3. **排序和截取** (第82-84行)

```java
list.sort(Comparator.comparingInt(CityWhVoApplet::getDistance));
list = list.subList(0, Math.min(list.size(), 10));
```

4. **关联总仓信息** (第86-94行)

```java
Map<Long, List<WhNexusVo>> regionWhListMap = whNexusService
    .selectListByCityWhId(list.stream().map(CityWhVoApplet::getId).collect(Collectors.toList()))
    .stream().collect(Collectors.groupingBy(WhNexusVo::getCityWhId));

list.forEach(e-> {
    if (CollectionUtil.isNotEmpty(regionWhListMap.get(e.getId()))) {
        List<RegionWhVo> routeRegionWhList = regionWhListMap.get(e.getId()).stream()
            .map(re -> regionWhMap.get(re.getRegionWhId())).collect(Collectors.toList());
        e.setRouteRegionWhList(MapstructUtils.convert(routeRegionWhList, RegionWhVoApplet.class));
    }
});
```

## 核心方法调用链

1. `cityWhService.selectListByStatus1()` - 查询活跃城市仓
2. `GeoCalculatorUtil.calculateDistance()` - 计算距离
3. `regionWhService.selectListByStatus1(0L)` - 查询总仓
4. `whNexusService.selectListByCityWhId()` - 查询城市仓关联关系
5. `MapstructUtils.convert()` - 数据转换

## 返回结果结构

返回包含以下信息的城市仓列表：

- 城市仓基本信息
- 与用户位置的距离（整数，单位：米）
- 途径的总仓列表信息
- 最多返回10个最近的城市仓

## 注意事项

1. **方法状态**: 该方法已标记为【停用】
2. **异常处理**: 参数验证通过注解 `@NotNull` 进行
3. **性能考虑**: 限制返回结果为前10个以提升性能
4. **坐标系统**: 使用标准的经纬度坐标系统

## 相关类和服务

- `ICityWhService` - 城市仓服务
- `IRegionWhService` - 总仓服务
- `IWhNexusService` - 仓库关系服务
- `GeoCalculatorUtil` - 地理位置计算工具
- `MapstructUtils` - 对象转换工具