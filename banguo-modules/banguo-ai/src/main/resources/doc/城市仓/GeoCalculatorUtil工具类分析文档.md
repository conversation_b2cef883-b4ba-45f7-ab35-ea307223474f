# GeoCalculatorUtil 工具类分析文档

## 类概述

`GeoCalculatorUtil` 是一个地理位置距离计算工具类，主要用于计算两个经纬度坐标点之间的球面距离。

**文件位置**: `@banguo-modules/banguo-basic/src/main/java/cn/xianlink/basic/util/GeoCalculatorUtil.java`

## 核心原理

### 1. Haversine公式

该工具类使用 **Haversine公式** 计算地球表面两点间的最短距离（大圆距离）。

```mermaid
graph TB
    A[输入: 两个坐标点] --> B[转换为弧度]
    B --> C[计算纬度差和经度差]
    C --> D[应用Haversine公式]
    D --> E[计算角度距离]
    E --> F[乘以地球半径得到实际距离]
```

### 2. 数学原理

**Haversine公式**:

```
a = sin²(Δφ/2) + cos φ1 ⋅ cos φ2 ⋅ sin²(Δλ/2)
c = 2 ⋅ atan2(√a, √(1−a))
d = R ⋅ c
```

其中:

- φ = 纬度 (弧度)
- λ = 经度 (弧度)
- R = 地球半径 (6,371,000 米)
- d = 距离 (米)

### 3. 地球模型假设

- **地球半径**: `EARTH_RADIUS = 6371000` 米
- **模型**: 假设地球为标准球体
- **精度**: 适用于地球表面短距离计算，误差小于0.5%

## 代码实现分析

### 1. 基础距离计算方法

#### calculateDistance 方法

```java
public static double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
    // 转换为弧度
    double phi1 = Math.toRadians(lat1);
    double phi2 = Math.toRadians(lat2);
    double deltaPhi = Math.toRadians(lat2 - lat1);
    double deltaLambda = Math.toRadians(lon2 - lon1);

    // Haversine公式核心计算
    double a = Math.sin(deltaPhi / 2) * Math.sin(deltaPhi / 2) +
            Math.cos(phi1) * Math.cos(phi2) *
                    Math.sin(deltaLambda / 2) * Math.sin(deltaLambda / 2);
    double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return EARTH_RADIUS * c;
}
```

**实现步骤**:

1. **角度转弧度**: 使用 `Math.toRadians()` 将角度转换为弧度
2. **计算差值**: 分别计算纬度差 `deltaPhi` 和经度差 `deltaLambda`
3. **Haversine计算**:
    - 计算 `a` 值（半弦值的平方）
    - 计算 `c` 值（角距离）
    - 乘以地球半径得到实际距离

### 2. 批量计算与排序方法

```mermaid
sequenceDiagram
    participant Client
    participant GeoCalculatorUtil
    participant JSONUtil
    
    Client->>GeoCalculatorUtil: calculator(objList, lng, lat, resultType, isSort)
    GeoCalculatorUtil->>JSONUtil: toList(toJsonStr(objList), JSONObject.class)
    JSONUtil-->>GeoCalculatorUtil: JSON对象列表
    
    loop 遍历每个对象
        GeoCalculatorUtil->>GeoCalculatorUtil: calculateDistance(坐标)
        GeoCalculatorUtil->>GeoCalculatorUtil: 设置distance字段
    end
    
    alt 需要排序
        GeoCalculatorUtil->>GeoCalculatorUtil: Collections.sort(按distance排序)
    end
    
    GeoCalculatorUtil->>JSONUtil: toList(json, resultType)
    JSONUtil-->>GeoCalculatorUtil: 目标类型列表
    GeoCalculatorUtil-->>Client: 返回结果
```

#### calculator 方法详解

```java
public static <T, A> List<T> calculator(List<A> objList, Double lng, Double lat, Class<T> resultType, Boolean isSort) {
    // 1. 对象转JSON列表
    List<JSONObject> list = JSONUtil.toList(JSONUtil.toJsonStr(objList), JSONObject.class);
    
    // 2. 计算距离并添加distance字段
    list.forEach(e -> e.set("distance", (int) calculateDistance(lat, lng, e.getDouble("lat"), e.getDouble("lng"))));
    
    // 3. 可选排序
    if (isSort) {
        Collections.sort(list, Comparator.comparingInt(o -> o.getInt("distance")));
    }
    
    // 4. 转换回目标类型
    return JSONUtil.toList(list.toString(), resultType);
}
```

## 方法详解

### 1. calculateDistance

- **功能**: 计算两个坐标点间的直线距离
- **参数**:
    - `lat1, lon1`: 第一个点的纬度和经度
    - `lat2, lon2`: 第二个点的纬度和经度
- **返回**: 距离（米，double类型）
- **精度**: 适用于地球表面短距离计算，误差小于0.5%

### 2. calculatorAndSort

- **功能**: 批量计算距离并按距离排序
- **参数**:
    - `objList`: 包含lat和lng字段的对象列表
    - `lng, lat`: 参考点坐标
    - `resultType`: 返回对象类型
- **特点**: 自动添加distance字段到结果对象
- **返回**: 按距离升序排列的对象列表

### 3. calculatorNotSort

- **功能**: 批量计算距离但不排序
- **适用**: 不需要排序的场景，性能更好
- **参数**: 与 `calculatorAndSort` 相同

### 4. calculator (核心方法)

- **功能**: 批量计算的核心实现
- **参数**:
    - `isSort`: 是否需要排序
    - 其他参数同上
- **实现流程**:
    1. 对象序列化为JSON
    2. 循环计算每个点的距离
    3. 可选排序操作
    4. 反序列化为目标类型

## 数据流处理图

```mermaid
graph TB
    subgraph "输入层"
        A[对象列表 with lat/lng]
        B[参考点坐标]
        C[目标类型]
    end
    
    subgraph "处理层"
        D[JSON序列化]
        E[距离计算循环]
        F[添加distance字段]
        G[可选排序]
        H[JSON反序列化]
    end
    
    subgraph "输出层"
        I[包含distance的对象列表]
    end
    
    A --> D
    B --> E
    C --> H
    D --> E
    E --> F
    F --> G
    G --> H
    H --> I
```

## 使用场景

### 1. 在queryCityWhList中的应用

```java
voApplet.setDistance((int) GeoCalculatorUtil.calculateDistance(
    lat, lng, 
    Double.parseDouble(e.getLat()), 
    Double.parseDouble(e.getLng())
));
```

### 2. 批量处理地理位置数据

- **查找附近的服务点**: 计算用户到各服务点的距离
- **按距离排序商家列表**: 为用户展示最近的商家
- **计算配送路径**: 评估配送距离和成本
- **地理围栏**: 判断是否在特定范围内

### 3. 实际业务应用

- 城市仓库距离计算
- 提货点距离排序
- 附近门店推荐
- 配送范围判断

## 技术特点

### 优点

1. **精确性**: 使用标准Haversine公式，适合地球表面距离计算
2. **通用性**: 支持批量处理和泛型返回类型
3. **灵活性**: 可选择是否排序，适应不同业务需求
4. **易用性**: 方法签名简洁，易于理解和使用

### 限制

1. **地球模型假设**: 假设地球为球体，实际地球是椭球体
2. **精度限制**: 在极地和长距离计算时有误差
3. **直线距离**: 计算的是两点间的最短球面距离，不考虑：
    - 地形障碍
    - 道路路径
    - 交通状况
4. **JSON转换开销**: 批量方法使用JSON序列化，有性能开销

## 性能考虑

```mermaid
graph LR
    A[单次计算] --> B[数学运算]
    B --> C[毫秒级响应]
    
    D[批量计算] --> E[JSON序列化]
    E --> F[循环计算]
    F --> G[排序操作 O(n log n)]
    G --> H[JSON反序列化]
    H --> I[响应时间取决于数据量]
```

### 性能特征

- **单次计算**: 纯数学运算，性能极佳
- **批量计算**: 受JSON序列化和排序算法影响
- **时间复杂度**: O(n log n) 当需要排序时
- **空间复杂度**: O(n) 需要额外存储JSON对象

## 精度与适用范围

### 距离范围适用性

- **短距离 (< 1km)**: 精度极高，误差 < 0.1%
- **中距离 (1-100km)**: 精度良好，误差 < 0.5%
- **长距离 (100-1000km)**: 可接受，误差 < 1%
- **超长距离 (> 1000km)**: 误差增大，需考虑地球椭球体模型

### 业务场景适用性

- ✅ **适合**: 附近搜索、配送距离、门店推荐
- ✅ **适合**: 城市内距离计算、区域服务范围
- ❌ **不适合**: 高精度GPS导航、跨大洋距离计算
- ❌ **不适合**: 需要考虑道路路径的场景

## 常数定义

```java
private static final double EARTH_RADIUS = 6371000; // 地球半径，单位米
```

- **值**: 6,371,000 米
- **含义**: 地球平均半径
- **精度**: 对于大多数业务场景足够精确
- **替代值**: 可根据具体地理区域调整（如使用WGS84椭球体参数）

## 代码质量评估

### 优点

1. **算法标准**: 使用经典Haversine公式
2. **代码清晰**: 方法职责单一，易于理解
3. **类型安全**: 使用泛型确保类型安全
4. **工具性强**: 静态方法，无状态，易于测试

### 改进建议

1. **参数验证**: 可添加经纬度范围验证
2. **异常处理**: 处理无效坐标输入
3. **性能优化**: 批量方法可避免JSON序列化
4. **精度选择**: 提供不同精度的计算选项

## 测试建议

### 单元测试覆盖

1. **基本功能测试**: 已知坐标点的距离计算
2. **边界条件测试**:
    - 同一点距离（应为0）
    - 跨越180°经线的情况
    - 极地附近的计算
3. **批量操作测试**: 验证排序和数据转换正确性
4. **性能测试**: 大数据量的处理时间

### 参考测试用例

```java
// 北京到上海的距离约为1067km
double distance = GeoCalculatorUtil.calculateDistance(
    39.9042, 116.4074,  // 北京
    31.2304, 121.4737   // 上海
);
// 预期结果: 约1067000米
```