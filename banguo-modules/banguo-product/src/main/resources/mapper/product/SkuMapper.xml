<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.product.mapper.SkuMapper">


    <select id="queryLastList" resultType="cn.xianlink.product.domain.supplierSku.vo.SupplierSkuVo">
        select
        ss.*, ss.id as skuId
        from sku ss
        where
            ss.del_flag = 0
            <!-- 控制是否查看隐藏的尾货 -->
            <if test=" bo.lookHideTail == 0">
                and (ss.status != 7 or ss.batch_type != 2)
            </if>
            <if test=" bo.isAll == 0">
                and ss.status != 7
            </if>
            <if test="bo.fullName != null and bo.fullName != ''">
                and ss.full_name like concat('%', #{bo.fullName}, '%')
            </if>
            <if test="bo.categoryIdList != null and bo.categoryIdList.size() > 0">
                and ss.category_id in
                <foreach collection="bo.categoryIdList" item="categoryId" open="(" close=")" separator=",">
                    #{categoryId}
                </foreach>
            </if>
            <if test="bo.businessTypeList != null and bo.businessTypeList.size() > 0">
                and ss.business_type in
                <foreach collection="bo.businessTypeList" item="businessType" open="(" close=")" separator=",">
                    #{businessType}
                </foreach>
            </if>
            <if test="bo.exBusinessTypeList != null and bo.exBusinessTypeList.size() > 0">
                and ss.business_type not in
                <foreach collection="bo.exBusinessTypeList" item="exBusinessTypeList" open="(" close=")" separator=",">
                    #{exBusinessTypeList}
                </foreach>
            </if>
            <if test="bo.businessType != null">
                and ss.business_type = #{bo.businessType}
            </if>
            <if test="bo.supplierIdList != null and bo.supplierIdList.size() > 0">
                and ss.supplier_id in
                <foreach collection="bo.supplierIdList" item="supplierId" open="(" close=")" separator=",">
                    #{supplierId}
                </foreach>
            </if>
            <if test="bo.supplierDeptIdList != null and bo.supplierDeptIdList.size() > 0">
                and ss.supplier_dept_id in
                <foreach collection="bo.supplierDeptIdList" item="supplierDeptId" open="(" close=")" separator=",">
                    #{supplierDeptId}
                </foreach>
            </if>
            <if test="bo.spuIdList != null and bo.spuIdList.size() > 0">
                and ss.spu_id in
                <foreach collection="bo.spuIdList" item="spuId" open="(" close=")" separator=",">
                    #{spuId}
                </foreach>
            </if>
            <if test="bo.supplierSpuIdList != null and bo.supplierSpuIdList.size() > 0">
                and ss.supplier_spu_id in
                <foreach collection="bo.supplierSpuIdList" item="supplierSpuId" open="(" close=")" separator=",">
                    #{supplierSpuId}
                </foreach>
            </if>
            <if test="bo.regionWhIdList != null and bo.regionWhIdList.size() > 0">
                and ss.region_wh_id in
                <foreach collection="bo.regionWhIdList" item="regionWhId" open="(" close=")" separator=",">
                    #{regionWhId}
                </foreach>
            </if>
            <if test="bo.provideRegionWhId != null">
                and ss.provide_region_wh_id = #{bo.provideRegionWhId}
            </if>
            <if test="bo.codeOrName != null and bo.codeOrName != ''">
                and (ss.code = #{bo.codeOrName} or ss.spu_name like concat('%', #{bo.codeOrName}, '%'))
            </if>
            <if test="bo.spuStandardsList != null and bo.spuStandardsList.size() > 0">
                and
                <foreach collection="bo.spuStandardsList" item="spuStandards" open="(" close=")" separator="or">
                    ( ss.spu_standards like concat('%|', #{spuStandards}, '|%') )
                </foreach>
            </if>
            <if test="bo.spuName != null and bo.spuName != ''">
                and ss.spu_name like concat('%', #{bo.spuName}, '%')
            </if>

            <if test="bo.saleDateLt != null">
                and ss.sale_date &lt; #{bo.saleDateLt}
            </if>
            <if test="bo.getOutSeason != null">
                and ss.status != 6
            </if>
            <if test="bo.batchType != null">
                and ss.batch_type = #{bo.batchType}
            </if>
            <if test="bo.statusList != null and bo.statusList.size() > 0">
                and ss.status in
                <foreach collection="bo.statusList" item="status" open="(" close=")" separator=",">
                    #{status}
                </foreach>
            </if>
            <if test="bo.waterfallList != null and bo.waterfallList.size() > 0">
                and ss.waterfall in
                <foreach collection="bo.waterfallList" item="waterfall" open="(" close=")" separator=",">
                    #{waterfall}
                </foreach>
            </if>
            <if test="bo.entranceHide != null">
                and ss.entranceHide = #{bo.entranceHide}
            </if>
            <if test="bo.buyerCodeList != null and bo.buyerCodeList.size() > 0">
                and ss.buyer_code in
                <foreach collection="bo.buyerCodeList" item="buyerCode" open="(" close=")" separator=",">
                    #{buyerCode}
                </foreach>
            </if>
        <if test="bo.isSaleHide != null">
            and ss.is_sale_hide = #{bo.isSaleHide}
        </if>
        <if test="bo.spuStandardsList != null and bo.spuStandardsList.size() > 0">
            and
            <foreach collection="bo.spuStandardsList" item="spuStandards" open="(" close=")" separator="or">
                ( ss.spu_standards like concat('%|', #{spuStandards}, '|%') )
            </foreach>
        </if>
        <if test="bo.spuStandard != null">
            and ss.spu_standards like concat('%', #{bo.spuStandard}, '%')
        </if>
        order by
        <if test="bo.orderByStatusDesc != null">
            ss.status desc,
        </if>
        ss.update_time desc
    </select>



    <select id="queryById" resultType="cn.xianlink.product.domain.supplierSku.vo.SupplierSkuVo">
        select
            ss.*
        from sku ss where ss.del_flag = 0 and ss.id = #{id}
    </select>


    <select id="getBySupplierSkuCode" resultType="cn.xianlink.product.domain.supplierSku.vo.SupplierSkuVo">
        select
            ss.*
        from
            sku ss
        <where>
            ss.del_flag = 0
            and ss.code = #{code}
        </where>
    </select>

    <select id="listBySkuLabel" resultType="cn.xianlink.product.api.domain.vo.RemoteSupplierSkuInfoVo">
        select
        ss.*
        from
        sku ss
        <where>
            ss.del_flag = 0
            and ss.sku_label = #{bo.skuLabel}
            <if test="bo.saleDateStart != null and bo.saleDateEnd != null">
                and ss.sale_date between #{bo.saleDateStart} and #{bo.saleDateEnd}
            </if>
            and ss.batch_type = #{bo.batchType}
        </where>
        order by ss.id desc
    </select>

    <select id="getBySkuLabel" resultType="cn.xianlink.product.domain.supplierSku.vo.SupplierSkuVo">
        select
            ss.*
        from
            sku ss
        <where>
            ss.del_flag = 0
            and ss.sku_label = #{bo.skuLabel}
            <if test="bo.saleDate != null">
                and ss.sale_date = #{bo.saleDate}
            </if>
            <if test="bo.batchType != null">
                and ss.batch_type = #{bo.batchType}
            </if>
        </where>
        order by ss.id desc limit 1
    </select>





    <select id="checkSupplierSku" resultType="cn.xianlink.product.domain.supplierSku.vo.SupplierSkuVo">
        select
        ss.*
        from
        sku ss
        where
        del_flag = 0
        <if test="bo.id != null">
            and supplier_sku_id != #{bo.id}
        </if>
        <if test="bo.batchType != null">
            and batch_type = #{bo.batchType}
        </if>
        <if test="bo.saleDate != null">
            and sale_date = #{bo.saleDate}
        </if>
        and spu_id = #{bo.spuId}
        and region_wh_id = #{bo.regionWhId}
        <if test="bo.supplierId != null">
            and supplier_id = #{bo.supplierId}
        </if>
        <if test="bo.supplierDeptId != null">
            and supplier_dept_id = #{bo.supplierDeptId}
        </if>
        and spu_grade = #{bo.spuGrade}
        and spu_standards = #{bo.spuStandards}
        and domestic = #{bo.domestic}
        and business_type = #{bo.businessType}
        and provide_region_wh_id = #{bo.provideRegionWhId}
        and spu_name = #{bo.spuName}
        and category_id = #{bo.categoryId}
        order by sale_date desc limit 1
    </select>



    <select id="getFirstIdByDeptAndStatus" resultType="java.lang.Long">
        select id from sku where supplier_dept_id = #{supplierDeptId} and status in
        <foreach collection="statusList" item="status"   open="(" close=")" separator=",">
            #{status}
        </foreach>
        limit 1
    </select>

    <update id="updateStatusHideByOnly">
        update sku set status = 7
        where
        del_flag = 0
        and id = #{bo.skuId}
        and sale_date != #{bo.saleDate}
    </update>

    <update id="updateLabelByOnly">
        update sku set sku_label = #{bo.skuLabel}
        where
        del_flag = 0
        and spu_id = #{bo.spuId}
        and region_wh_id = #{bo.regionWhId}
        and supplier_id = #{bo.supplierId}
        and supplier_dept_id = #{bo.supplierDeptId}
        and spu_grade = #{bo.spuGrade}
        and spu_standards = #{bo.spuStandards}
        and domestic = #{bo.domestic}
        and business_type = #{bo.businessType}
        and provide_region_wh_id = #{bo.provideRegionWhId}
    </update>


    <select id="checkSupplierSkuNum" resultType="int">
        select count(1) from sku where del_flag = 0
        <if test="bo.skuLabel">
            and sku_label = #{bo.skuLabel}
        </if>
        <if test="bo.code">
            and code = #{bo.code}
        </if>
        <if test="bo.id != null">
            and id != #{bo.id}
        </if>
    </select>

    <select id="getSameSupplierSkuIdList" resultType="java.lang.Long">
        select
        ss.id
        from
        sku ss
        where
        del_flag = 0
        and spu_id = #{bo.spuId}
        and region_wh_id = #{bo.regionWhId}
        and supplier_id = #{bo.supplierId}
        and supplier_dept_id = #{bo.supplierDeptId}
        and spu_grade = #{bo.spuGrade}
        and spu_standards = #{bo.spuStandards}
        and domestic = #{bo.domestic}
        and business_type = #{bo.businessType}
        and provide_region_wh_id = #{bo.provideRegionWhId}
        and spu_name = #{bo.spuName}
        and category_id = #{bo.categoryId}
        <if test="bo.saleDateStart != null and bo.saleDateEnd != null">
            and sale_date between #{bo.saleDateStart} and #{bo.saleDateEnd}
        </if>
        order by sale_date
    </select>


    <update id="syncSold">
        update sku ss join supplier_sku_stock sss on ss.supplier_sku_id = sss.supplier_sku_id
        set ss.copy_sold = sss.sold, ss.update_time = ss.update_time
        where ss.supplier_sku_id in
        <foreach collection="supplierSkuIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="querySupplierSkuById" resultType="cn.xianlink.product.domain.supplierSku.vo.SupplierSkuVo">
        select
        ss.supplier_sku_id as id,
        ss.id as sku_id,
        ss.*,
        sss.stock,
        sss.up_stock,
        sss.sold,
        sss.lock_stock,
        sss.stock_status
        from
        sku ss
        left join supplier_sku_stock sss on ss.supplier_sku_id = sss.supplier_sku_id
        where ss.del_flag = 0 and ss.id = #{id}
    </select>

    <select id="querySupplierSkuSaleNum" resultType="cn.xianlink.product.api.domain.bo.SupplierSkuSaleNumBo">
        SELECT
            supplier_id,
            region_wh_id,
            COUNT(1) AS sale_num
        FROM sku
        WHERE `status` = 4
        AND business_type != 40
        <if test="saleType != null">
            AND sale_type = #{saleType}
        </if>
        <if test="bo != null and !bo.isEmpty()">
            and (supplier_id,region_wh_id) in
            <foreach item="b" collection="bo" open="(" separator="," close=")">
                (#{b.supplierId},#{b.regionWhId})
            </foreach>
        </if>
        GROUP BY supplier_id, region_wh_id
    </select>
    <select id="getBySku" resultType="cn.xianlink.product.domain.Sku">
        select id, region_wh_id, status, supplier_sku_id
        from sku
        <where>
            del_flag = 0
            <if test="sku.supplierId != null">
                and supplier_id = #{sku.supplierId}
            </if>
            <if test="sku.spuId != null">
                and spu_id = #{sku.spuId}
            </if>
            <if test="sku.spuStandards != null">
                and spu_standards = #{sku.spuStandards}
            </if>
            <if test="sku.spuGrade != null">
                and spu_grade = #{sku.spuGrade}
            </if>
            <if test="sku.domestic != null">
                and domestic = #{sku.domestic}
            </if>
            <if test="sku.producer != null">
                and producer = #{sku.producer}
            </if>
            <if test="sku.brand != null">
                and brand = #{sku.brand}
            </if>
            <if test="sku.businessType != null">
                and business_type = #{sku.businessType}
            </if>
            and batch_type = 1
        </where>
    </select>
    <select id="getSkuIdBySupplier" resultType="cn.xianlink.product.domain.Sku">
        select
        MIN(id) AS id,
        spu_id,
        spu_standards,
        spu_grade,
        domestic,
        producer,
        brand,
        business_type,
        batch_type,
        spu_name,
        supplier_id,
        supplier_code,
        supplier_name,
        category_path_name,
        status,
        price,
        package_standards,
        category_name,
        full_name
        from sku
        where
        del_flag = 0
        and supplier_id = #{supplierId}
        and batch_type = 1
        and status in (3,4,5,6)
        and price >= #{price}
        <if test="spuName != null and spuName != '' ">
            and full_name like concat('%',#{spuName},'%')
        </if>
        group by spu_id, spu_standards, spu_grade, domestic, producer, brand, business_type, batch_type
    </select>

    <select id="getPriceCount" resultType="java.lang.Integer">
        select count(1) from sku
        <where>
            del_flag = 0
            <if test="bo.regionWhId != null">
                and region_wh_id = #{bo.regionWhId}
            </if>
            and batch_type = 1
            and status in (3,4,5,6)
            <if test="bo.minPrice != null">
                and price >= #{bo.minPrice}
            </if>
            <if test="bo.maxPrice != null">
                and #{bo.maxPrice} >= price
            </if>
        </where>
    </select>

    <select id="getCategoryMinPriceByNames" resultType="cn.xianlink.product.api.domain.vo.RemoteSkuVo">
        select
        MIN(price) AS price,
        category_path_name as categoryPathName
        from sku
        where
        del_flag = 0
        and status in (3,4,5,6)
        and batch_type = 1
        and
        <foreach collection="categoryNames" item="categoryName" open="(" separator="or" close=")">
            category_path_name like concat(#{categoryName},'%')
        </foreach>
        group by category_path_name
    </select>

</mapper>