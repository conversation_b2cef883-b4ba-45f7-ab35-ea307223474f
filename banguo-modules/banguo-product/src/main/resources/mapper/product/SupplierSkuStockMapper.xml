<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.product.mapper.SupplierSkuStockMapper">

    <update id="updateStock">
        update supplier_sku_stock
        <set>
            <if test="bo.addStock != null">
                stock = stock + #{bo.addStock},
            </if>
            <if test="bo.stock != null">
                stock = #{bo.stock},
            </if>
            <if test="bo.sold != null">
                sold = sold + #{bo.sold},
            </if>
            <if test="bo.backStock != null">
                stock = stock + #{bo.backStock},
            </if>
            <if test="bo.lockStock != null">
                lock_stock = lock_stock + #{bo.lockStock},
            </if>
        </set>
        where supplier_sku_id = #{bo.supplierSkuId}
    </update>

    <update id="updateStockBatch">
        UPDATE supplier_sku_stock
        <set>
            <!-- 合并stock字段的多种操作逻辑 -->
            stock = CASE supplier_sku_id
            <foreach collection="list" item="item">
                <choose>
                    <when test="item.stock != null">
                        WHEN #{item.supplierSkuId} THEN #{item.stock}
                    </when>
                    <otherwise >
                        WHEN #{item.supplierSkuId} THEN stock
                    </otherwise>
                </choose>
            </foreach>
            END,
            <!-- 合并stock字段的多种操作逻辑 -->
            stock = CASE supplier_sku_id
            <foreach collection="list" item="item">
                <choose>

                    <when test="item.addStock != null">
                        WHEN #{item.supplierSkuId} THEN stock + #{item.addStock}
                    </when>
                    <otherwise>
                        WHEN #{item.supplierSkuId} THEN stock
                    </otherwise>
                </choose>
            </foreach>
            END,
            <!-- 合并stock字段的多种操作逻辑 -->
            stock = CASE supplier_sku_id
            <foreach collection="list" item="item">
                <choose>
                    <when test="item.backStock != null">
                        WHEN #{item.supplierSkuId} THEN stock + #{item.backStock}
                    </when>
                    <otherwise>
                        WHEN #{item.supplierSkuId} THEN stock
                    </otherwise>
                </choose>
            </foreach>
            END,
        <!-- sold字段增量 -->
            sold = CASE supplier_sku_id
            <foreach collection="list" item="item">
                <choose>
                    <when test="item.sold != null">
                        WHEN #{item.supplierSkuId} THEN sold + #{item.sold}
                    </when>
                    <otherwise>
                        WHEN #{item.supplierSkuId} THEN sold
                    </otherwise>
                </choose>
            </foreach>
            END,
        <!-- lock_stock字段增量 -->
        lock_stock = CASE supplier_sku_id
        <foreach collection="list" item="item">
            <choose>
                <when test="item.lockStock != null">
                    WHEN #{item.supplierSkuId} THEN lock_stock + #{item.lockStock}
                </when>
                <otherwise>
                    WHEN #{item.supplierSkuId} THEN lock_stock
                </otherwise>
            </choose>
        </foreach>
        END,

        </set>
        WHERE supplier_sku_id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.supplierSkuId}
        </foreach>
    </update>

    <update id="clearStock" >
        update supplier_sku ss
            join supplier_sku_stock sss on ss.id = sss.supplier_sku_id
            set sss.stock = 0, ss.is_out = 1
        where ss.del_flag = 0
          and ss.sale_date = #{bo.saleDate}
          and ss.region_wh_id = #{bo.id}
          and ss.business_type != 40
        <if test="bo.categoryIds != null and bo.categoryIds.size() > 0">
            and ss.category_id in
            <foreach collection="bo.categoryIds" item="categoryId" open="(" separator="," close=")">
                #{categoryId}
            </foreach>
        </if>
    </update>

    <update id="updateStockUsedByMkActivity">
        UPDATE supplier_sku_stock
        SET stock = stock + #{bo.backStock},
            lock_stock=lock_stock + #{bo.lockStock}
        WHERE
        supplier_sku_id = #{bo.supplierSkuId}
        AND sold + lock_stock + #{bo.actDiscountNum} <![CDATA[<=]]> #{bo.targetQty}
        AND stock <![CDATA[>=]]> #{bo.lockStock}
    </update>


    <update id="batchUpdateStockUsedByMkActivity">
        <foreach collection="list" item="bo" separator=";">
        UPDATE supplier_sku_stock
        SET stock = stock + #{bo.backStock},
            lock_stock=lock_stock + #{bo.lockStock}
        WHERE
        supplier_sku_id = #{bo.supplierSkuId}
        AND sold + lock_stock + #{bo.actDiscountNum} <![CDATA[<=]]> #{bo.targetQty}
        AND stock <![CDATA[>=]]> #{bo.lockStock}
        </foreach>
    </update>

</mapper>