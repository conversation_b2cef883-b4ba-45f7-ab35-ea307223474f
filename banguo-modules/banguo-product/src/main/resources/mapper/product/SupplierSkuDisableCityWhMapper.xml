<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.product.mapper.SupplierSkuDisableCityWhMapper">

    <update id="delByBo">
        update supplier_sku_disable_city_wh set del_flag = id
        <where>
            del_flag = 0 and status = 0
            <if test="bo.skuId != null">
                and sku_id = #{bo.skuId}
            </if>
            <if test="bo.idList != null and bo.idList.size() > 0">
                and id in
                <foreach collection="bo.idList" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
    </update>

    <select id="getSkuIdsByCityWhId" resultType="java.lang.Long">
        select
        ssdcw.sku_id
        from
        supplier_sku_disable_city_wh ssdcw
        where
        ssdcw.del_flag = 0
        and ssdcw.city_wh_id = #{cityWhId}
        and sku_id != 0
        <if test="skuIds != null and skuIds.size() >0">
            and sku_id in
            <foreach collection="skuIds" item="skuId" open="(" close=")" separator=",">
                #{skuId}
            </foreach>
        </if>
        and status = 1
    </select>

    <select id="listWithSku" resultType="cn.xianlink.product.domain.vo.SkuDisableCityListWhVo">
        SELECT
            d.id,
            d.city_wh_id AS cityWhId,
            d.city_wh_code AS cityWhCode,
            d.status,
            s.id AS skuId,
            s.spu_name AS spuName,
            s.spu_grade AS spuGrade,
            s.producer,
            s.brand,
            s.short_producer AS shortProducer,
            s.spu_standards AS spuStandards,
            s.status AS skuStatus,
            s.full_name AS fullName,
            s.buyer_code AS buyerCode,
            s.buyer_name AS buyerName,
            s.category_id_level2 AS categoryIdLevel2,
            s.category_id AS categoryId,
            s.category_path_name AS categoryPathName,
            s.category_name AS categoryName,
            s.region_wh_id,
            s.region_wh_name
        <choose>
            <!-- 只查记录，补充sku信息 -->
            <when test="dto.status != null and dto.status  &lt; 3">
                FROM supplier_sku_disable_city_wh d
                INNER JOIN sku s
                ON d.sku_id = s.id
                AND d.del_flag = 0
            </when>
            <!-- 以sku为主，记录补充 -->
            <otherwise>
                FROM sku s
                LEFT JOIN supplier_sku_disable_city_wh d
                ON d.sku_id  = s.id AND d.del_flag  = 0
            </otherwise>
        </choose>
        <!--   返回右表状态为2 和 sku中没联表过结果为null的数据     -->
        <if test="dto.status  != null and dto.status  == 3">AND  d.status = 2</if>
        <!--   城市仓不存在时，也能查询出商品     -->
        <if test="dto.cityWhId  != null">
            AND d.city_wh_id  = #{dto.cityWhId}
        </if>
        <if test="dto.cityWhCode  != null">
            AND d.city_wh_code  = #{dto.cityWhCode}
        </if>
        <where>
            s.status not in (6,7)
            <if test="dto.status  != null and dto.status &lt; 3">
                AND d.status  = #{dto.status}
            </if>
            <!-- 关联表条件 -->
            <if test="dto.skuId != null">
                AND s.id = #{dto.skuId}
            </if>
            <if test="dto.regionWhId != null">
                AND s.region_wh_id = #{dto.regionWhId}
            </if>
            <if test="dto.spuName != null and dto.spuName != ''">
                AND s.spu_name LIKE CONCAT('%', #{dto.spuName}, '%')
            </if>
            <if test="dto.categoryIdLevel2 != null">
                AND s.category_id_level2 = #{dto.categoryIdLevel2}
            </if>
            <if test="dto.categoryId != null">
                AND s.category_id = #{dto.categoryId}
            </if>
            <if test="dto.fullName != null and dto.fullName != ''">
                AND s.full_name LIKE CONCAT('%', #{dto.fullName}, '%')
            </if>
            <if test="dto.spuGrade != null and dto.spuGrade != ''">
                AND s.spu_grade = #{dto.spuGrade}
            </if>
            <if test="dto.spuStandards != null and dto.spuStandards != ''">
                AND s.spu_standards = #{dto.spuStandards}
            </if>
            <if test="dto.buyerCode != null and dto.buyerCode != ''">
                AND s.buyer_code = #{dto.buyerCode}
            </if>
            <if test="dto.statusList != null and dto.statusList.size()  > 0">
                s.status IN
                <foreach collection="dto.statusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
        </where>
        ORDER BY
        <choose>
            <!--     3 意味着 2 或者 null       -->
            <when test="dto.status  == 3">
                CASE
                WHEN d.status = 2 THEN 1
                WHEN d.status IS NULL THEN 2
                ELSE 3
                END,
            </when>
            <when test="dto.status  == null or dto.status  == 4">
                CASE
                WHEN d.status = 0 THEN 1
                WHEN d.status = 1 THEN 2
                WHEN d.status = 2 THEN 3
                WHEN d.status IS NULL THEN 4
                ELSE 5
                END,
            </when>
            <otherwise/>
        </choose>
        s.up_time DESC
    </select>
</mapper>
