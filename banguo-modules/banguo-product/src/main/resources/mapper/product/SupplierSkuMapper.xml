<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.product.mapper.SupplierSkuMapper">

    <select id="queryList" resultType="cn.xianlink.product.domain.supplierSku.vo.SupplierSkuVo">
        select
        *
        from
        supplier_sku
        where id in (
        select
        max(id) as id
        from
        supplier_sku
        where
        del_flag = 0 and supplier_spu_id = #{supplierSpuId}
        group by supplier_spu_id, region_wh_id)
    </select>

    <select id="queryLastList" resultType="cn.xianlink.product.domain.supplierSku.vo.SupplierSkuVo">
        select
        ss.supplier_sku_id as id,
        ss.id as sku_id,
        ss.*,
        sss.stock,
        sss.up_stock,
        sss.sold,
        sss.stock_status
        from sku ss
        left join supplier_sku_stock sss on ss.supplier_sku_id = sss.supplier_sku_id
        where
        ss.del_flag = 0
        and ss.status != 7
        <if test="bo.fullName != null and bo.fullName != ''">
            and ss.full_name like concat('%', #{bo.fullName}, '%')
        </if>
        <if test="bo.categoryIdList != null and bo.categoryIdList.size() > 0">
            and ss.category_id in
            <foreach collection="bo.categoryIdList" item="categoryId" open="(" close=")" separator=",">
                #{categoryId}
            </foreach>
        </if>
        <if test="bo.businessTypeList != null and bo.businessTypeList.size() > 0">
            and ss.business_type in
            <foreach collection="bo.businessTypeList" item="businessType" open="(" close=")" separator=",">
                #{businessType}
            </foreach>
        </if>
        <if test="bo.exBusinessTypeList != null and bo.exBusinessTypeList.size() > 0">
            and ss.business_type not in
            <foreach collection="bo.exBusinessTypeList" item="exBusinessTypeList" open="(" close=")" separator=",">
                #{exBusinessTypeList}
            </foreach>
        </if>
        <if test="bo.businessType != null">
            and ss.business_type = #{bo.businessType}
        </if>
        <if test="bo.supplierIdList != null and bo.supplierIdList.size() > 0">
            and ss.supplier_id in
            <foreach collection="bo.supplierIdList" item="supplierId" open="(" close=")" separator=",">
                #{supplierId}
            </foreach>
        </if>
        <if test="bo.supplierDeptIdList != null and bo.supplierDeptIdList.size() > 0">
            and ss.supplier_dept_id in
            <foreach collection="bo.supplierDeptIdList" item="supplierDeptId" open="(" close=")" separator=",">
                #{supplierDeptId}
            </foreach>
        </if>
        <if test="bo.spuIdList != null and bo.spuIdList.size() > 0">
            and ss.spu_id in
            <foreach collection="bo.spuIdList" item="spuId" open="(" close=")" separator=",">
                #{spuId}
            </foreach>
        </if>
        <if test="bo.supplierSpuIdList != null and bo.supplierSpuIdList.size() > 0">
            and ss.supplier_spu_id in
            <foreach collection="bo.supplierSpuIdList" item="supplierSpuId" open="(" close=")" separator=",">
                #{supplierSpuId}
            </foreach>
        </if>
        <if test="bo.regionWhIdList != null and bo.regionWhIdList.size() > 0">
            and ss.region_wh_id in
            <foreach collection="bo.regionWhIdList" item="regionWhId" open="(" close=")" separator=",">
                #{regionWhId}
            </foreach>
        </if>

        <if test="bo.codeOrName != null and bo.codeOrName != ''">
            and (ss.code = #{bo.codeOrName} or ss.spu_name like concat('%', #{bo.codeOrName}, '%'))
        </if>
        <if test="bo.spuStandardsList != null and bo.spuStandardsList.size() > 0">
            and
            <foreach collection="bo.spuStandardsList" item="spuStandards" open="(" close=")" separator="or">
                ( ss.spu_standards like concat('%|', #{spuStandards}, '|%') )
            </foreach>
        </if>
        <if test="bo.spuName != null and bo.spuName != ''">
            and ss.spu_name like concat('%', #{bo.spuName}, '%')
        </if>

        <if test="bo.saleDateLt != null">
            and ss.sale_date &lt; #{bo.saleDateLt}
        </if>
        <if test="bo.getOutSeason != null">
            and ss.status != 6
        </if>
        <if test="bo.batchType != null">
            and ss.batch_type = #{bo.batchType}
        </if>
        <if test="bo.statusList != null and bo.statusList.size() > 0">
            and ss.status in
            <foreach collection="bo.statusList" item="status" open="(" close=")" separator=",">
                #{status}
            </foreach>
        </if>
        <if test="bo.waterfallList != null and bo.waterfallList.size() > 0">
            and ss.waterfall in
            <foreach collection="bo.waterfallList" item="waterfall" open="(" close=")" separator=",">
                #{waterfall}
            </foreach>
        </if>
        <if test="bo.entranceHide != null">
            and ss.entranceHide = #{bo.entranceHide}
        </if>
        <if test="bo.isSaleHide != null">
            and ss.is_sale_hide = #{bo.isSaleHide}
        </if>
        <if test="bo.saleType != null">
            and ss.sale_type = #{bo.saleType}
        </if>
        <if test="(bo.buyerCodeList  != null and !bo.buyerCodeList.isEmpty())
          || (bo.category2IdList  != null and !bo.category2IdList.isEmpty())">
            AND (
            <trim prefixOverrides="OR">
                <if test="bo.buyerCodeList  != null and !bo.buyerCodeList.isEmpty()">
                    ss.buyer_code IN
                    <foreach collection="bo.buyerCodeList" item="buyerCode" open="(" close=")" separator=",">
                        #{buyerCode}
                    </foreach>
                </if>
                <if test="bo.category2IdList  != null and !bo.category2IdList.isEmpty()">
                    OR ss.category_id_level2 IN
                    <foreach collection="bo.category2IdList" item="category2Id" open="(" close=")" separator=",">
                        #{category2Id}
                    </foreach>
                </if>
            </trim>
            )
        </if>
        order by
        <if test="bo.orderByStatusDesc != null">
            CASE ss.status
                WHEN 8 THEN 1
            ELSE ss.status
            END desc,
        </if>
        ss.update_time desc
    </select>

    <select id="querySubsidyAuditPage" resultType="cn.xianlink.product.domain.supplierSku.vo.SupplierSkuVo">
        select
        sa.id as subsidy_audit_id,
        sa.`status` as subsidy_audit_status,
        sa.submit_time as subsidy_audit_submit_time,
        sa.subsidy_amount as audit_subsidy_amount,
        IF(sa.status = 0, ss.subsidy_amount, sa.subsidy_amount) as subsidy_amount,
        IF(sa.status = 0, ss.price, sa.price) as price,
        IF(sa.status = 0, ss.price - ss.subsidy_amount, sa.price - sa.subsidy_amount) as subsidy_price,
        ss.*,
        sss.stock,
        sss.up_stock,
        sss.sold,
        sss.stock_status
        from supplier_sku_subsidy_audit sa
        inner join supplier_sku ss on ss.id = sa.supplier_sku_id
        left join supplier_sku_stock sss on ss.id = sss.supplier_sku_id
        where
        sa.del_flag = 0
        and ss.del_flag = 0
        <if test="bo.fullName != null and bo.fullName != ''">
            and ss.full_name like concat('%', #{bo.fullName}, '%')
        </if>
        <if test="bo.regionWhIdList != null and bo.regionWhIdList.size() > 0">
            and sa.region_wh_id in
            <foreach collection="bo.regionWhIdList" item="regionWhId" open="(" close=")" separator=",">
                #{regionWhId}
            </foreach>
        </if>
        <if test="bo.codeOrName != null and bo.codeOrName != ''">
            and (ss.code = #{bo.codeOrName} or ss.spu_name like concat('%', #{bo.codeOrName}, '%'))
        </if>
        <if test="bo.spuName != null and bo.spuName != ''">
            and ss.spu_name like concat('%', #{bo.spuName}, '%')
        </if>
        <if test="bo.subsidyAuditStatus != null">
            and sa.status = #{bo.subsidyAuditStatus}
        </if>
        <if test="bo.saleDate != null">
            and sa.sale_date = #{bo.saleDate}
        </if>
        order by sa.status asc, sa.submit_time desc
    </select>

    <select id="queryById" resultType="cn.xianlink.product.domain.supplierSku.vo.SupplierSkuVo">
        select
        ss.*,
        sss.stock,
        sss.up_stock,
        sss.sold,
        sss.stock_status
        from
        supplier_sku ss
        left join supplier_sku_stock sss on ss.id = sss.supplier_sku_id
        where ss.del_flag = 0 and ss.id = #{id}
    </select>

    <select id="queryPage" resultType="cn.xianlink.product.domain.supplierSku.vo.SupplierSkuVo">
        select
        ss.*,
        sss.stock,
        sss.up_stock,
        sss.sold,
        sss.stock_status
        from
        supplier_sku ss
        left join supplier_sku_stock sss on ss.id = sss.supplier_sku_id
        <where>
            ss.del_flag = 0
            <if test="bo.supplierIdList != null and bo.supplierIdList.size() > 0">
                and ss.supplier_id in
                <foreach collection="bo.supplierIdList" item="supId" open="(" close=")" separator=",">
                    #{supId}
                </foreach>
            </if>
            <if test="bo.spuName != null and bo.spuName != ''">
                and ss.full_name like concat('%', #{bo.spuName}, '%')
            </if>
            <if test="bo.codeOrName != null and bo.codeOrName != ''">
                and (ss.code = #{bo.codeOrName} or ss.spu_name like concat('%', #{bo.codeOrName}, '%'))
            </if>
            <if test="bo.supplierSpuId != null">
                and ss.supplier_spu_id = #{bo.supplierSpuId}
            </if>
            <if test="bo.supplierSpuCode != null and bo.supplierSpuCode != ''">
                and ss.supplier_spu_code = #{bo.supplierSpuCode}
            </if>
            <if test="bo.spuKeyword != null and bo.spuKeyword != ''">
                and (ss.spu_name like concat('%', #{bo.spuKeyword}, '%') or ss.spu_standards like concat('%',
                #{bo.spuKeyword}, '%')
                or ss.package_word like concat('%', #{bo.spuKeyword}, '%') or ss.producer like concat('%',
                #{bo.spuKeyword}, '%'))
            </if>
            <if test="bo.batchType != null">
                and ss.batch_type = #{bo.batchType}
            </if>
            <if test="bo.entranceHide != null">
                and ss.entranceHide = #{bo.entranceHide}
            </if>
            <if test="bo.exBusinessType != null">
                and ss.business_type != #{bo.exBusinessType}
            </if>
            <if test="bo.saleDateStart != null and bo.saleDateEnd != null">
                and ss.sale_date between #{bo.saleDateStart} and #{bo.saleDateEnd}
            </if>
            <if test="bo.upTimeStart != null and bo.upTimeEnd != null">
                and ss.up_time between #{bo.upTimeStart} and #{bo.upTimeEnd}
            </if>
            <if test="bo.downTimeStart != null and bo.downTimeEnd != null">
                and ss.down_time between #{bo.downTimeStart} and #{bo.downTimeEnd}
            </if>
            <if test="bo.submitTimeStart != null and bo.submitTimeEnd != null">
                and ss.create_time between #{bo.submitTimeStart} and #{bo.submitTimeEnd}
            </if>
            <if test="bo.statusList != null and bo.statusList.size() > 0">
                and ss.status in
                <foreach collection="bo.statusList" item="status" open="(" close=")" separator=",">
                    #{status}
                </foreach>
            </if>
            <if test="bo.categoryIdList != null and bo.categoryIdList.size() > 0">
                and ss.category_id in
                <foreach collection="bo.categoryIdList" item="categoryId" open="(" close=")" separator=",">
                    #{categoryId}
                </foreach>
            </if>
            <if test="bo.spuStandardsList != null and bo.spuStandardsList.size() > 0">
                and
                <foreach collection="bo.spuStandardsList" item="spuStandards" open="(" close=")" separator="or">
                    ( spu_standards like concat('%|', #{spuStandards}, '|%') )
                </foreach>
            </if>
            <if test="bo.supplierDeptIdList != null and bo.supplierDeptIdList.size() > 0">
                and ss.supplier_dept_id in
                <foreach collection="bo.supplierDeptIdList" item="supplierDeptId" open="(" close=")" separator=",">
                    #{supplierDeptId}
                </foreach>
            </if>
            <if test="bo.waterfallList != null and bo.waterfallList.size() > 0">
                and ss.waterfall in
                <foreach collection="bo.waterfallList" item="waterfall" open="(" close=")" separator=",">
                    #{waterfall}
                </foreach>
            </if>
            <if test="bo.buyerCodeList != null and bo.buyerCodeList.size() > 0">
                and ss.buyer_code in
                <foreach collection="bo.buyerCodeList" item="buyerCode" open="(" close=")" separator=",">
                    #{buyerCode}
                </foreach>
            </if>
            <if test="bo.businessTypeList != null and bo.businessTypeList.size() > 0">
                and ss.business_type in
                <foreach collection="bo.businessTypeList" item="businessType" open="(" close=")" separator=",">
                    #{businessType}
                </foreach>
            </if>
            <if test="bo.regionWhIdList != null and bo.regionWhIdList.size() > 0">
                and ss.region_wh_id in
                <foreach collection="bo.regionWhIdList" item="regionWhId" open="(" close=")" separator=",">
                    #{regionWhId}
                </foreach>
            </if>
        </where>
        <if test="bo.moreGoodsFlag == 1">
            group by ss.sku_id, ss.price, ss.spu_gross_weight, ss.spu_net_weight
        </if>
        order by ss.id desc
    </select>

    <select id="querySalePage" resultType="cn.xianlink.product.domain.supplierSku.vo.QuerySalePageVo">
        select
        ss.id, ss.code, ss.category_path_name, ss.spu_name, ss.spu_gross_weight, ss.spu_net_weight,
        ss.spu_grade, ss.spu_standards, ss.region_wh_id, ss.supplier_id, ss.supplier_code, ss.supplier_dept_id,
        ss.buy_min, ss.buy_max, ss.domestic, ss.producer, ss.price, ss.sale_date, ss.status, ss.business_type,
        ss.batch_type, ss.area_code, ss.brand, ss.short_producer, ss.place_order_multiple, ss.region_wh_name,
        ss.cooperation, ss.little, ss.good_to_eat, ss.provide_region_wh_id,ss.spu_id, ss.bargain, ss.bargain_rate,ss.market_price,
        ss.gift_box, ss.shouguang_vegetables,ss.after_sale_type, ss.sku_id,ss.spu_code,ss.supplier_spu_code, ss.subsidy_amount,
        ss.sale_type, ss.copy_sold, ss.category_id_level2, ss.category_id, ss.discount_price as bargainDownPrice
        <if test="bo.shouguangVegetables != null">
            ,( CASE
            <choose>
                <when test="bo.newMerchantIds != null and !bo.newMerchantIds.isEmpty()">
                    WHEN ss.supplier_id IN
                    <foreach item="id" collection="bo.newMerchantIds" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </when>
                <otherwise>
                    WHEN 1=0  <!-- 所有商品标记为非新商户 -->
                </otherwise>
            </choose>
            THEN 1 ELSE 0 END ) AS is_new_merchant
        </if>
        from
        supplier_sku ss
        <where>
            ss.del_flag = 0  and ss.is_sale_hide = 0
            <if test="bo.saleType != null">
                and ss.sale_type = #{bo.saleType}
            </if>
            <if test="bo.status != null">
                and ss.status = #{bo.status}
            </if>
            <if test="bo.supplierId != null">
                and ss.supplier_id = #{bo.supplierId}
            </if>
            <if test="bo.supplierIdList != null and bo.supplierIdList.size() > 0">
                and ss.supplier_id in
                <foreach collection="bo.supplierIdList" item="supplierId" open="(" close=")" separator=",">
                    #{supplierId}
                </foreach>
            </if>
            <if test="bo.supplierSpuCodeList != null and bo.supplierSpuCodeList.size() > 0">
                and ss.supplier_spu_code in
                <foreach collection="bo.supplierSpuCodeList" item="spuCode" open="(" close=")" separator=",">
                    #{spuCode}
                </foreach>
            </if>
            <if test="bo.notInSkuIdList != null and bo.notInSkuIdList.size() > 0">
                and ss.sku_id not in
                <foreach collection="bo.notInSkuIdList" item="skuId" open="(" close=")" separator=",">
                    #{skuId}
                </foreach>
            </if>

            <if test="bo.supplierDeptId != null">
                and ss.supplier_dept_id = #{bo.supplierDeptId}
            </if>
            <if test="bo.regionWhIdList != null and bo.regionWhIdList.size() > 0">
                and ss.region_wh_id in
                <foreach collection="bo.regionWhIdList" item="regionWhId" open="(" close=")" separator=",">
                    #{regionWhId}
                </foreach>
            </if>
            <if test="bo.categoryThreeIdList != null and bo.categoryThreeIdList.size() > 0">
                and ss.category_id in
                <foreach collection="bo.categoryThreeIdList" item="categoryId" open="(" close=")" separator=",">
                    #{categoryId}
                </foreach>
            </if>
            <if test="bo.spuName != null and bo.spuName != ''">
                and ss.full_name like concat('%', #{bo.spuName}, '%')
            </if>
            <if test="bo.businessTypeList != null and bo.businessTypeList.size() > 0">
                and ss.business_type in
                <foreach collection="bo.businessTypeList" item="businessType" open="(" close=")" separator=",">
                    #{businessType}
                </foreach>
            </if>
            <if test="bo.entranceHide != null">
                and ss.entrance_hide = #{bo.entranceHide}
            </if>
            <if test="bo.notInSupplierSkuIdList != null and bo.notInSupplierSkuIdList.size() > 0">
                and ss.id not in
                <foreach collection="bo.notInSupplierSkuIdList" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="bo.categoryThreeIdList == null and bo.spuName == null">
                and ss.business_type != 40
            </if>
            <if test="bo.cooperation != null">
                and ss.cooperation = #{bo.cooperation}
            </if>
            <if test="bo.bargain != null">
                and ss.bargain = #{bo.bargain}
            </if>
            <if test="bo.giftBox != null">
                and ss.gift_box = #{bo.giftBox}
            </if>
            <if test="bo.little != null">
                and ss.little = #{bo.little}
            </if>
            <if test="bo.goodToEat != null">
                and ss.good_to_eat = #{bo.goodToEat}
            </if>
            <if test="bo.shouguangVegetables != null">
                and ss.shouguang_vegetables = #{bo.shouguangVegetables}
            </if>
            <if test="bo.saleType != null">
                and ss.sale_type = #{bo.saleType}
            </if>
            <if test="bo.priceDownLimitTime != null and bo.priceDownLimitTime == 1">
                and ss.discount_price > 0
            </if>
            <if test="bo.customerStoreType != null">
                and ss.customer_store_types like concat('%', #{bo.customerStoreType}, '%')
            </if>
        </where>
        order by
        <if test="bo.regionWhIdList != null and bo.regionWhIdList.size() > 1">
            FIELD(ss.region_wh_id,
            <foreach collection="bo.regionWhIdList" item="regionWhId" separator=",">
                #{regionWhId}
            </foreach>
            ),
        </if>
        <if test="bo.categoryThreeIdList != null and bo.categoryThreeIdList.size() > 0">
            ss.category_name,
        </if>
        <choose>
            <when test="bo.bargain != null and bo.bargain == 1">
                ss.is_out, ss.bargain_rate desc,ss.id desc
            </when>
            <when test="bo.shouguangVegetables != null and bo.shouguangVegetables == 1">
                ss.is_out, is_new_merchant DESC, ss.copy_sold desc, ss.id desc
            </when>
            <otherwise>
                ss.is_out, ss.copy_sold desc,ss.id desc
            </otherwise>
        </choose>
    </select>

    <select id="queryInfoList" resultType="cn.xianlink.product.api.domain.vo.RemoteSupplierSkuInfoVo">

        select
        ss.*,
        sss.stock,
        sss.lock_stock,
        sss.sold
        from
        supplier_sku ss
        left join supplier_sku_stock sss on ss.id = sss.supplier_sku_id
        <where>
            ss.del_flag = 0
            <if test="bo.supplierSkuIdList != null and bo.supplierSkuIdList.size() > 0">
                and ss.id in
                <foreach collection="bo.supplierSkuIdList" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="bo.spuName != null and bo.spuName != ''">
                and ss.spu_name like concat('%', #{bo.spuName}, '%')
            </if>
            <if test="bo.passAudit != null">
                <if test="bo.passAudit = 1">
                    and ( ss.has_delivery_audit = 0 or ss.pass_delivery_audit = 1 )
                </if>
                <if test="bo.passAudit = 0">
                    and ss.has_delivery_audit = 1 and ss.pass_delivery_audit = 0
                </if>
            </if>
        </where>
    </select>
    <select id="getByCode" resultType="cn.xianlink.product.api.domain.vo.RemoteSupplierSkuInfoVo">
        select
        ss.*,
        sss.stock
        from
        supplier_sku ss
        left join supplier_sku_stock sss on ss.id = sss.supplier_sku_id
        <where>
            ss.del_flag = 0
            and ss.code = #{code}
        </where>
    </select>

    <select id="listBySkuLabel" resultType="cn.xianlink.product.api.domain.vo.RemoteSupplierSkuInfoVo">
        select
        ss.*,
        sss.stock
        from
        supplier_sku ss
        left join supplier_sku_stock sss on ss.id = sss.supplier_sku_id
        <where>
            ss.del_flag = 0
            and ss.sku_label = #{bo.skuLabel}
            <if test="bo.saleDateStart != null and bo.saleDateEnd != null">
                and ss.sale_date between #{bo.saleDateStart} and #{bo.saleDateEnd}
            </if>
            and ss.batch_type = #{bo.batchType}
        </where>
        order by ss.id desc
    </select>

    <select id="getBySkuLabel" resultType="cn.xianlink.product.api.domain.vo.RemoteSupplierSkuInfoVo">
        <include refid="selectSkuLabel"/>
    </select>

    <select id="getBySkuLabelList" resultType="cn.xianlink.product.api.domain.vo.RemoteSupplierSkuInfoVo">
        <include refid="selectSkuLabel"/>
    </select>

    <sql id="selectSkuLabel">
        select
        ss.*,
        sss.stock
        from
        supplier_sku ss
        left join supplier_sku_stock sss on ss.id = sss.supplier_sku_id
        <where>
            ss.del_flag = 0
            and ss.sku_label = #{bo.skuLabel}
            <if test="bo.saleDate != null">
                and ss.sale_date = #{bo.saleDate}
            </if>
            <if test="bo.batchType != null">
                and ss.batch_type = #{bo.batchType}
            </if>
            <if test="bo.regionWhId != null">
                and ss.region_wh_id = #{bo.regionWhId}
            </if>
        </where>
        order by ss.id desc
        <choose>
            <when test="bo.size != null">
                limit #{bo.size}
            </when>
            <otherwise>
                limit 1
            </otherwise>
        </choose>
    </sql>

    <select id="queryUpSupplierSku" resultType="long">
        select id from supplier_sku where del_flag = 0 and status in (3,4)
    </select>
    <select id="queryRefundDifference" resultType="cn.xianlink.product.domain.supplierSku.vo.QueryRefundDifferenceSkuVo">
        select
        id,
        price,
        spu_net_weight
        from
        supplier_sku
        where del_flag = 0 and status >= #{bo.status}
        <if test="bo.saleDate != null">
            and sale_date = #{bo.saleDate}
        </if>
        <if test="bo.supplierSkuIdList != null and bo.supplierSkuIdList.size() > 0">
            and id in
            <foreach collection="bo.supplierSkuIdList" item="supplierSkuId" open="(" close=")" separator=",">
                #{supplierSkuId}
            </foreach>
        </if>
        limit #{bo.offset}, #{bo.pageSize}
    </select>
    <select id="getBySpuCode" resultType="cn.xianlink.product.api.domain.vo.RemoteSupplierSkuInfoVo">
        select
        ss.*
        from
        supplier_spu sp left join supplier_sku ss on sp.id = ss.supplier_spu_id and sp.del_flag = 0
        <where>
            ss.del_flag = 0
            and (sp.label = #{code} or sp.spu_code = #{code})
            and ss.sale_date >= #{saleDate}
        </where>
    </select>

    <select id="checkSupplierSku" resultType="cn.xianlink.product.domain.supplierSku.vo.SupplierSkuVo">
        select
        ss.*
        from
        supplier_sku ss
        where
        del_flag = 0
        <if test="bo.id != null">
            and id != #{bo.id}
        </if>
        <if test="bo.batchType != null">
            and batch_type = #{bo.batchType}
        </if>
        <if test="bo.saleDate != null">
            and sale_date = #{bo.saleDate}
        </if>
        <if test="bo.supplierId != null">
            and supplier_id = #{bo.supplierId}
        </if>
        <if test="bo.supplierDeptId != null">
            and supplier_dept_id = #{bo.supplierDeptId}
        </if>
        <if test="bo.lastUpTime != null">
            and up_time is not null
        </if>
        <if test="bo.skuId != null">
            and sku_id = #{bo.skuId}
        </if>
        <if test="bo.categoryId != null">
            and category_id = #{bo.categoryId}
        </if>
        <if test="bo.spuId != null">
            and spu_id = #{bo.spuId}
        </if>
        <if test="bo.regionWhId != null">
            and region_wh_id = #{bo.regionWhId}
        </if>
        <if test="bo.spuGrade != null">
            and spu_grade = #{bo.spuGrade}
        </if>
        <if test="bo.spuStandards != null">
            and spu_standards = #{bo.spuStandards}
        </if>
        <if test="bo.domestic != null">
            and domestic = #{bo.domestic}
        </if>
        <if test="bo.businessType != null">
            and business_type = #{bo.businessType}
        </if>
        <if test="bo.provideRegionWhId != null">
            and provide_region_wh_id = #{bo.provideRegionWhId}
        </if>
        <if test="bo.spuName != null">
            and spu_name = #{bo.spuName}
        </if>
        order by sale_date desc limit 1
    </select>

    <select id="querySupplierDeliver" resultType="cn.xianlink.product.api.domain.vo.RemoteQuerySupplierDeliverVo">
        select
        supplier_id,
        count(id) as count
        from
        supplier_sku
        where
        del_flag = 0
        and sale_date = #{bo.saleDate}
        and supplier_id in
        <foreach collection="bo.supplierIdList" item="supplierId" open="(" close=")" separator=",">
            #{supplierId}
        </foreach>
        and region_wh_id = #{bo.regionWhId}
        group by supplier_id
    </select>

    <select id="getFirstIdByDeptAndStatus" resultType="java.lang.Long">
        select id from supplier_sku where supplier_dept_id = #{supplierDeptId} and status in
        <foreach collection="statusList" item="status" open="(" close=")" separator=",">
            #{status}
        </foreach>
        limit 1
    </select>

    <update id="updateStatusHideByOnly">
        update supplier_sku set status = 7
        where
        del_flag = 0
        and sku_id = #{bo.skuId}
        and sale_date != #{bo.saleDate}
    </update>

    <update id="updateLabelByOnly">
        update supplier_sku set sku_label = #{bo.skuLabel}
        where
        del_flag = 0
        and spu_id = #{bo.spuId}
        and supplier_id = #{bo.supplierId}
        and region_wh_id = #{bo.regionWhId}
        and supplier_dept_id = #{bo.supplierDeptId}
        and spu_grade = #{bo.spuGrade}
        and spu_standards = #{bo.spuStandards}
        and domestic = #{bo.domestic}
        and business_type = #{bo.businessType}
        and provide_region_wh_id = #{bo.provideRegionWhId}
    </update>

    <select id="queryOnSalePage" resultType="cn.xianlink.product.domain.supplierSku.vo.SupplierSkuSalePageVo">
        select ss.* from supplier_sku ss
        where
        ss.del_flag = 0 and ss.status = 4 and ss.is_sale_hide = 0
        <if test="bo.spuName != null and bo.spuName != ''">
            and ss.spu_name like concat('%', #{bo.spuName}, '%')
        </if>
        <if test="bo.categoryIdList != null and bo.categoryIdList.size() > 0">
            and category_id in
            <foreach collection="bo.categoryIdList" item="categoryId" open="(" close=")" separator=",">
                #{categoryId}
            </foreach>
        </if>
        <if test="bo.supplierIdList != null and bo.supplierIdList.size() > 0">
            and supplier_id in
            <foreach collection="bo.supplierIdList" item="supplierId" open="(" close=")" separator=",">
                #{supplierId}
            </foreach>
        </if>
        <if test="bo.supplierDeptIdList != null and bo.supplierDeptIdList.size() > 0">
            and supplier_dept_id in
            <foreach collection="bo.supplierDeptIdList" item="supplierDeptId" open="(" close=")" separator=",">
                #{supplierDeptId}
            </foreach>
        </if>
        <if test="bo.regionWhIdList != null and bo.regionWhIdList.size() > 0">
            and region_wh_id in
            <foreach collection="bo.regionWhIdList" item="regionWhId" open="(" close=")" separator=",">
                #{regionWhId}
            </foreach>
        </if>
        order by update_time desc
    </select>

    <select id="checkSupplierSkuNum" resultType="int">
        select count(1) from supplier_sku where del_flag = 0
        <if test="bo.skuLabel">
            and sku_label = #{bo.skuLabel}
        </if>
        <if test="bo.code">
            and code = #{bo.code}
        </if>
        <if test="bo.id != null">
            and id != #{bo.id}
        </if>
    </select>

    <select id="getSameSupplierSkuIdList" resultType="java.lang.Long">
        select
        ss.id
        from
        supplier_sku ss
        where
        del_flag = 0
        and spu_id = #{bo.spuId}
        and supplier_id = #{bo.supplierId}
        and region_wh_id = #{bo.regionWhId}
        and supplier_dept_id = #{bo.supplierDeptId}
        and spu_grade = #{bo.spuGrade}
        and spu_standards = #{bo.spuStandards}
        and domestic = #{bo.domestic}
        and business_type = #{bo.businessType}
        and provide_region_wh_id = #{bo.provideRegionWhId}
        and spu_name = #{bo.spuName}
        and category_id = #{bo.categoryId}
        <if test="bo.saleDateStart != null and bo.saleDateEnd != null">
            and sale_date between #{bo.saleDateStart} and #{bo.saleDateEnd}
        </if>
        order by sale_date
    </select>

    <select id="queryDeliveryAuditPage" resultType="cn.xianlink.product.domain.supplierSku.vo.SupplierSkuVo">
        select
        ss.*,
        sss.stock,
        sss.up_stock,
        sss.sold,
        sss.stock_status
        from
        supplier_sku ss
        left join supplier_sku_stock sss on ss.id = sss.supplier_sku_id
        where ss.del_flag = 0
        and ss.has_delivery_audit = 1
        and ss.batch_type = 1
        <if test="bo.saleDate != null">
            and ss.sale_date = #{bo.saleDate}
        </if>
        <if test="bo.saleDateStart != null and bo.saleDateEnd != null">
            and ss.pass_delivery_audit = 0
            and ss.sale_date between #{bo.saleDateStart} and #{bo.saleDateEnd}
        </if>
        <if test="bo.supplierIdList != null and bo.supplierIdList.size() > 0">
            and ss.supplier_id in
            <foreach collection="bo.supplierIdList" item="supplierId" open="(" close=")" separator=",">
                #{supplierId}
            </foreach>
        </if>
        <if test="bo.regionWhIdList != null and bo.regionWhIdList.size() > 0">
            and ss.region_wh_id in
            <foreach collection="bo.regionWhIdList" item="regionWhId" open="(" close=")" separator=",">
                #{regionWhId}
            </foreach>
        </if>
        <if test="bo.regionWhId != null">
            and ss.region_wh_id = #{bo.regionWhId}
        </if>
        <if test="bo.supplierDeptIdList != null and bo.supplierDeptIdList.size() > 0">
            and ss.supplier_dept_id in
            <foreach collection="bo.supplierDeptIdList" item="supplierDeptId" open="(" close=")" separator=",">
                #{supplierDeptId}
            </foreach>
        </if>
        <if test="bo.spuName != null and bo.spuName != ''">
            and ss.spu_name like concat('%', #{bo.spuName}, '%')
        </if>
        <if test="bo.categoryIdList != null and bo.categoryIdList.size() > 0">
            and ss.category_id in
            <foreach collection="bo.categoryIdList" item="categoryId" open="(" close=")" separator=",">
                #{categoryId}
            </foreach>
        </if>
        <if test="bo.buyerCodeList != null and bo.buyerCodeList.size() > 0">
            and ss.buyer_code in
            <foreach collection="bo.buyerCodeList" item="buyerCode" open="(" close=")" separator=",">
                #{buyerCode}
            </foreach>
        </if>
        order by pass_delivery_audit
    </select>

    <select id="getBacthList" resultType="cn.xianlink.product.domain.supplierSku.vo.SupplierSkuVo">
        select
        ss.*
        from
        supplier_sku ss
        where
        del_flag = 0
        <if test="bo.id != null">
            and id != #{bo.id}
        </if>
        <if test="bo.batchType != null">
            and batch_type = #{bo.batchType}
        </if>
        <if test="bo.saleDate != null">
            and sale_date = #{bo.saleDate}
        </if>
        and spu_id = #{bo.spuId}
        and supplier_id = #{bo.supplierId}
        and region_wh_id = #{bo.regionWhId}
        and supplier_dept_id = #{bo.supplierDeptId}
        and spu_grade = #{bo.spuGrade}
        and spu_standards = #{bo.spuStandards}
        and domestic = #{bo.domestic}
        and business_type = #{bo.businessType}
        and provide_region_wh_id = #{bo.provideRegionWhId}
        and spu_name = #{bo.spuName}
        and category_id = #{bo.categoryId}
        order by sale_date desc
    </select>
    <select id="querySimpleInfoListHasStock" resultType="cn.xianlink.product.domain.supplierSku.vo.SupplierSkuVo">
        select
        ss.* , sss.stock
        from
        supplier_sku ss
        left join supplier_sku_stock sss on ss.id = sss.supplier_sku_id
        where
        ss.del_flag = 0
        <if test="ids != null and ids.size() > 0">
            and ss.id in
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </select>
    <select id="queryTodayPrice" resultType="cn.xianlink.product.domain.excel.RegionSkuPriceExportVO">
        select
        cpp.name,
        s.category_name ,
        concat( case when s.short_producer =''then '' else concat(s.short_producer,'-') end , s.spu_name, '【',
        s.spu_standards,'】') as skuFullName,
        s.price,
        FORMAT( s.price / s.spu_net_weight , 2) as weightPrice,
        s.spu_gross_weight,
        s.spu_net_weight ,
        sss.up_stock as stock
        from
        supplier_sku s
        join supplier_sku_stock sss on
        s.id = sss.supplier_sku_id
        join category c on s.category_id = c.id
        join category cp on c.parent_id = cp.id
        join category cpp on cp.parent_id = cpp.id
        where
        s.region_wh_id = #{regionWhId}
        and s.sale_date = #{saleDate}
        and s.spu_name != '配货商品'
        order by cp.parent_id
    </select>

    <update id="syncSold">
        update supplier_sku ss join supplier_sku_stock sss on ss.id = sss.supplier_sku_id
        set ss.copy_sold = sss.sold, ss.update_time = ss.update_time
        where ss.id in
        <foreach collection="supplierSkuIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="queryYunStock" resultType="cn.xianlink.product.api.domain.vo.RemoteSupplierSkuStockVo">
        select
            ss.id as supplierSkuId,
            ss.sku_id,
            csd.onhand_qty,
            sss.stock,
            sss.sold
            from
            supplier_sku ss
        join cw_stock_detail csd on ss.sku_id = csd.sku_id and ss.sku_id != 0
        join supplier_sku_stock sss on ss.id = sss.supplier_sku_id
        where
        ss.id in
        <foreach collection="supplierSkuIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="nearUpSkuIdMap" resultType="java.lang.Long">
        select
            ss.sku_id
            from
            supplier_sku ss
        where
        ss.sku_id in
        <foreach collection="skuIdList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and ss.sale_date >= #{nearDay}
        and ss.up_time is not null
        group by ss.sku_id
    </select>
    <select id="queryDiscountPage" resultType="cn.xianlink.product.domain.supplierSku.vo.QuerySalePageVo">
        select
        ss.id, ss.code, ss.category_path_name, ss.spu_name, ss.spu_gross_weight, ss.spu_net_weight,
        ss.spu_grade, ss.spu_standards, ss.region_wh_id, ss.supplier_id, ss.supplier_code, ss.supplier_dept_id,
        ss.buy_min, ss.buy_max, ss.domestic, ss.producer, ss.price, ss.sale_date, ss.status, ss.business_type,
        ss.batch_type, ss.area_code, ss.brand, ss.short_producer, ss.place_order_multiple, ss.region_wh_name,
        ss.cooperation, ss.little, ss.good_to_eat, ss.provide_region_wh_id,ss.spu_id, ss.bargain, ss.bargain_rate,ss.market_price,
        ss.gift_box, ss.shouguang_vegetables,ss.after_sale_type, ss.sku_id,ss.spu_code,ss.supplier_spu_code, ss.subsidy_amount, ss.sale_type,
        ss.copy_sold, ss.category_id_level2
        <if test="bo.shouguangVegetables != null">
            ,( CASE
            <choose>
                <when test="bo.newMerchantIds != null and !bo.newMerchantIds.isEmpty()">
                    WHEN ss.supplier_id IN
                    <foreach item="id" collection="bo.newMerchantIds" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </when>
                <otherwise>
                    WHEN 1=0  <!-- 所有商品标记为非新商户 -->
                </otherwise>
            </choose>
            THEN 1 ELSE 0 END ) AS is_new_merchant
        </if>
        from
        supplier_sku ss
        <where>
            ss.del_flag = 0  and ss.is_sale_hide = 0
            <if test="bo.saleType != null">
                and ss.sale_type = #{bo.saleType}
            </if>
            <if test="bo.status != null">
                and ss.status = #{bo.status}
            </if>
            <if test="bo.supplierId != null">
                and ss.supplier_id = #{bo.supplierId}
            </if>
            <if test="bo.supplierIdList != null and bo.supplierIdList.size() > 0">
                and ss.supplier_id in
                <foreach collection="bo.supplierIdList" item="supplierId" open="(" close=")" separator=",">
                    #{supplierId}
                </foreach>
            </if>
            <if test="bo.skuIds != null and bo.skuIds.size() > 0">
                and ss.sku_id in
                <foreach collection="bo.skuIds" item="skuId" open="(" close=")" separator=",">
                    #{skuId}
                </foreach>
            </if>
            <if test="bo.notInSkuIdList != null and bo.notInSkuIdList.size() > 0">
                and ss.sku_id not in
                <foreach collection="bo.notInSkuIdList" item="skuId" open="(" close=")" separator=",">
                    #{skuId}
                </foreach>
            </if>
            <if test="bo.regionWhIdList != null and bo.regionWhIdList.size() > 0">
                and ss.region_wh_id in
                <foreach collection="bo.regionWhIdList" item="regionWhId" open="(" close=")" separator=",">
                    #{regionWhId}
                </foreach>
            </if>
            <if test="bo.categoryThreeIdList != null and bo.categoryThreeIdList.size() > 0">
                and ss.category_id in
                <foreach collection="bo.categoryThreeIdList" item="categoryId" open="(" close=")" separator=",">
                    #{categoryId}
                </foreach>
            </if>
            <if test="bo.spuIds != null and bo.spuIds.size() > 0">
                and ss.spu_id in
                <foreach collection="bo.spuIds" item="spuId" open="(" close=")" separator=",">
                    #{spuId}
                </foreach>
            </if>
            <if test="bo.rollSpuIds != null and bo.rollSpuIds.size() > 0">
                and ss.spu_id not in
                <foreach collection="bo.rollSpuIds" item="spuId" open="(" close=")" separator=",">
                    #{spuId}
                </foreach>
            </if>
            <if test="bo.spuName != null and bo.spuName != ''">
                and ss.full_name like concat('%', #{bo.spuName}, '%')
            </if>
            <if test="bo.businessTypeList != null and bo.businessTypeList.size() > 0">
                and ss.business_type in
                <foreach collection="bo.businessTypeList" item="businessType" open="(" close=")" separator=",">
                    #{businessType}
                </foreach>
            </if>
            <if test="bo.entranceHide != null">
                and ss.entrance_hide = #{bo.entranceHide}
            </if>
            <if test="bo.notInSupplierSkuIdList != null and bo.notInSupplierSkuIdList.size() > 0">
                and ss.id not in
                <foreach collection="bo.notInSupplierSkuIdList" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="bo.categoryThreeIdList == null and bo.spuName == null">
                and ss.business_type != 40
            </if>
            <if test="bo.saleType != null">
                and ss.sale_type = #{bo.saleType}
            </if>
        </where>
        order by ss.is_out, ss.copy_sold,ss.id desc
    </select>

    <select id="querySaleLowSku" resultType="cn.xianlink.product.api.domain.vo.RemoteSupplierSkuInfoVo">
        select
        ss.id, ss.spu_id, ss.sku_id, ss.category_id_level2, ss.category_id, c.parent_id as categoryIdLevel1,
        ss.price, ss.discount_price, sto.sold, sto.lock_stock, ss.subsidy_amount
        from supplier_sku ss
        left join supplier_sku_stock sto on ss.id = sto.supplier_sku_id
        left join category c on ss.category_id_level2 = c.id
        <where>
            ss.del_flag = 0
            and ss.is_sale_hide = 0
            and ss.status = 4
            and ss.is_out = 0
            <if test="bo.saleDate != null">
                and ss.sale_date = #{bo.saleDate}
            </if>
            <if test="bo.regionId != null">
                and ss.region_wh_id = #{bo.regionId}
            </if>
            <if test="bo.saleType != null and bo.saleType != 0">
                and ss.sale_type = #{bo.saleType}
            </if>
            and #{bo.copySold} > sto.sold + sto.lock_stock
        </where>
        order by ss.id
    </select>

    <select id="queryActivityInfoList" resultType="cn.xianlink.product.api.domain.vo.RemoteSkuInfoForActivityVo">
        select
        ss.id as supplierSkuId, ss.spu_id, ss.sku_id, ss.category_id_level2, ss.category_id,
        c.parent_id as categoryIdLevel1, sto.sold + sto.lock_stock as copySold, ss.supplier_id,
        ss.sale_type
        from supplier_sku ss
        left join supplier_sku_stock sto on ss.id = sto.supplier_sku_id
        left join category c on ss.category_id_level2 = c.id
        <where>
            ss.id in (
            <foreach collection="skuIds" item="id" separator=",">
                #{id}
            </foreach>
            )
            <if test="saleType != null and saleType != 0">
                and ss.sale_type = #{saleType}
            </if>
            and ss.spu_name != '配货商品'
        </where>
    </select>
</mapper>
