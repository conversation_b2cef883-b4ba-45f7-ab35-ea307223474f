package cn.xianlink.product.domain.supplierSku.vo;

import cn.xianlink.bi.api.domain.vo.RemoteSkuCardVo;
import cn.xianlink.bi.api.domain.vo.RemoteSkuLossVo;
import cn.xianlink.common.dict.annotation.DictConvertFiled;
import cn.xianlink.common.dict.enums.DictConvertTypeEnum;
import cn.xianlink.product.domain.Sku;
import cn.xianlink.product.domain.SupplierSku;
import cn.xianlink.product.domain.supplierSku.bo.KeyValueBO;
import cn.xianlink.product.domain.supplierSkuFile.vo.SupplierSkuFileVo;
import com.alibaba.cloud.commons.lang.StringUtils;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 供应商销售批次商品视图对象 supplier_sku
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Data
@AutoMappers({
        @AutoMapper(target = SupplierSku.class),
        @AutoMapper(target = Sku.class),
        @AutoMapper(target = QuerySupSubscribePageVo.class),
        @AutoMapper(target = QueryBatchPageVo.class),
        @AutoMapper(target = SupplierSkuInfoVo.class),
        @AutoMapper(target = GetBackInfoVo.class),
        @AutoMapper(target = QueryRegSubscribePageVo.class),
        @AutoMapper(target = QuerySalePageVo.class),
        @AutoMapper(target = QueryPassPageVo.class),
        @AutoMapper(target = QueryHistoryPageVo.class),
})
public class SupplierSkuVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("sku唯一编码")
    private String skuCode;

    @ApiModelProperty("绑定的批次id")
    private Long supplierSkuId;
    private Long skuId;;

    @ApiModelProperty("供应商批次商品唯一编码")
    private String code;

    @ApiModelProperty("商品品种id")
    private Long categoryId;

    @ApiModelProperty("商品品种名称")
    private String categoryName;

    @ApiModelProperty("商品品种")
    private String categoryPathName;

    @ApiModelProperty("平台商品id")
    private Long spuId;

    @ApiModelProperty("平台商品唯一编码")
    private String spuCode;

    @ApiModelProperty("平台商品名称")
    private String spuName;

    @ApiModelProperty("平台商品等级，数据字典配置")
    private String spuGrade;

    @ApiModelProperty("商品等级描述")
    private String spuGradeDesc;

    @ApiModelProperty("平台规格列表")
    private List<KeyValueBO> skuStandardsBoList;

    @ApiModelProperty("产地来源：0国产 1进口")
    private Integer domestic ;

    @ApiModelProperty("产地")
    private String producer;

    @ApiModelProperty("平台商品规格")
    private String spuStandards;

    @ApiModelProperty("规格储存内容")
    private String spuStandardsName;

    @ApiModelProperty("平台商品毛重(斤)")
    private BigDecimal spuGrossWeight;

    @ApiModelProperty("平台商品净重(斤)")
    private BigDecimal spuNetWeight;

    @ApiModelProperty("供应商商品id")
    private Long supplierSpuId;

    @ApiModelProperty("供应商商品唯一编码")
    private String supplierSpuCode;

    @ApiModelProperty("装卸队id")
    private Long basPortageTeamId;

    @ApiModelProperty("供货总仓id")
    private Long provideRegionWhId;

    @ApiModelProperty("供货总仓名称")
    private String provideRegionWhName;

    @ApiModelProperty("总仓id")
    private Long regionWhId;

    @ApiModelProperty("总仓唯一编码")
    private String regionWhCode;

    @ApiModelProperty("总仓名称")
    private String regionWhName;

    @ApiModelProperty("供应商id")
    private Long supplierId;

    @ApiModelProperty("供应商子单位ID")
    private Long supplierDeptId;

    @ApiModelProperty("供应商唯一编码")
    private String supplierCode;

    @ApiModelProperty("档口唯一编码")
    private String supplierDeptCode;

    @ApiModelProperty("供应商名称")
    private String supplierName;
    /**
     * 供应商别名
     */
    private String supplierAlias;

    @ApiModelProperty("子供应商名称")
    private String supplierDeptName;


    @ApiModelProperty("是否战略合作品，0为否，1为是")
    private Integer cooperation;

    @ApiModelProperty("是否特价商品，0为否，1为是")
    private Integer bargain;
    private BigDecimal bargainRate;
    @ApiModelProperty("商品特价门槛，小于等于则前端自动勾选特价商品属性")
    private BigDecimal bargainPrice;

    @ApiModelProperty("市场价")
    private BigDecimal marketPrice;


    @ApiModelProperty("礼盒，0为否，1为是")
    private Integer giftBox;

    @ApiModelProperty("小件，0为否，1为是")
    private Integer little;

    @ApiModelProperty("好吃，0为否，1为是")
    private Integer goodToEat;

    @ApiModelProperty("寿光蔬菜，0为否，1为是")
    private Integer shouguangVegetables;

    @ApiModelProperty("简介")
    private String snapshot;

    @ApiModelProperty("售卖价格")
    private BigDecimal price;

//    @ApiModelProperty("上架价格")
//    private BigDecimal upPrice;

    @ApiModelProperty("可卖库存")
    private Integer stock;

    @ApiModelProperty("上架库存")
    private Integer upStock;

    @ApiModelProperty("库存状态，1预售，2在途，3库房")
    private Integer stockStatus;

    /**
     * 云仓可用库存
     */
    private Integer cloudStock;
    /**
     * 云仓锁定库存
     */
    private Integer cloudLockStock;
    /**
     * 云仓实际库存
     */
    private Integer cloudActualStock;
    /**
     * 云仓在途库存
     */
    private Integer cloudOnWayStock;

    @ApiModelProperty("批次库存")
    private Integer batchStock;

    @ApiModelProperty("已售")
    private Integer sold;
    private Integer copySold;
    private Integer lockStock;

    @ApiModelProperty("销售日期")
    private LocalDate saleDate;

    @ApiModelProperty("明日预测")
    private String predictionTomorrow;

    @ApiModelProperty("未来预测")
    private String predictionFuture;

    @ApiModelProperty("包装上的字")
    private String packageWord;

    @ApiModelProperty("包装容器-框，泡沫箱，袋等")
    private String packageType;

    @ApiModelProperty("最小甜度")
    private BigDecimal sweetMin;

    @ApiModelProperty("最大甜度")
    private BigDecimal sweetMax;

    @ApiModelProperty("下单倍数")
    private Integer placeOrderMultiple;

    @ApiModelProperty("最小购买数量，为-1不限制")
    private Integer buyMin;

    @ApiModelProperty("最大购买数量，为-1不限制")
    private Integer buyMax;

    @ApiModelProperty("采购员code")
    private String buyerCode;

    @ApiModelProperty("采购员name")
    private String buyerName;

    @ApiModelProperty("售后服务，0表示没售后,其他数字代表多少天有售后")
    private Integer afterSaleDay;

    @ApiModelProperty("售后规则：0无售后、1正常售后、2库管验货")
    private Integer afterSaleType;
    private String afterSaleName;
    private String afterSaleDesc;

    @ApiModelProperty("是否免检，0表示不是免检,1表示免检")
    private Integer isCheck;

    @ApiModelProperty("免赔情况")
    private String deductibleSituation;

    @ApiModelProperty("可申请售后说明")
    private String afterSaleExplain;

    @ApiModelProperty("入口隐藏，1隐藏，2不隐藏")
    private Integer entranceHide;

    @ApiModelProperty("同一批次批次识别码")
    private String waterfall;

    @ApiModelProperty("批次条形码")
    private String skuLabel;

    @ApiModelProperty("上架日期")
    private Date upTime;

    @ApiModelProperty("下架日期")
    private Date downTime;

    @ApiModelProperty("过季日期")
    private Date outTime;

    @ApiModelProperty("状态(1待审核,2仅审核通过,3驳回,4上架,5下架,6过季,7隐藏)")
    private Integer status;

    @ApiModelProperty("批次类型，1正常，2尾货，3后台")
    private Integer batchType;

    @ApiModelProperty("业务类型，1市采, 10地采, 20基采, 30产地，40配货")
    @DictConvertFiled(dictCode = "orderOrderBusinessType", filedName = "businessTypeName", type = DictConvertTypeEnum.ENUM)
    private Integer businessType;

    @ApiModelProperty("换供应商的原始供应商id")
    private Long originalSupplierId;

    @ApiModelProperty("换供应商的原始供应商批次id")
    private Long originalSupplierSkuId;

    @ApiModelProperty("供应商商品条形码，默认供应商商品编码，可以修改")
    private String label;

    @ApiModelProperty("删除标识")
    private Long delFlag;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("修改时间")
    private Date updateTime;

    @ApiModelProperty("商品图片地址")
    private String imgUrl;

    @ApiModelProperty("文件集合")
    private List<SupplierSkuFileVo> fileList;

    @ApiModelProperty("是否在当前销售日 1-在 0-不在")
    private int onSalesDay;

    @ApiModelProperty("区域编码")
    private String areaCode;

    @ApiModelProperty("净单价")
    private BigDecimal netWeightPrice;

    @ApiModelProperty("品牌")
    private String brand;

    @ApiModelProperty("产地简称")
    private String shortProducer;

    @ApiModelProperty("是否送货审核 - 0否df 1是")
    private Integer hasDeliveryAudit;

    @ApiModelProperty("通过送货审核 - 0否df 1是")
    private Integer passDeliveryAudit;

    @ApiModelProperty("检索字符")
    private String fullName;

    @ApiModelProperty("是否可打印条形码 - 1可以")
    private Integer allowPrinting;

    @ApiModelProperty("集采是否隐藏 1-是")
    private Integer isSaleHide;

    @ApiModelProperty("sku售后信息")
    private RemoteSkuLossVo remoteSkuLossVo;

    @ApiModelProperty("sku卡片信息")
    private RemoteSkuCardVo remoteSkuCard;

    @ApiModelProperty("补贴金额")
    private BigDecimal subsidyAmount;

    @ApiModelProperty("补贴后单价")
    private BigDecimal subsidyPrice;

    /**
     * 商品二级分类
     */
    private Long categoryIdLevel2;

    @ApiModelProperty("销售类型：1为般果代采，2为商家自营")
    private Integer saleType;

    @ApiModelProperty("补贴审核id")
    private Long subsidyAuditId;

    @ApiModelProperty("补贴审核状态（0-补贴待审核、1-已审核）")
    private Integer subsidyAuditStatus;

    @ApiModelProperty("补贴审核提交时间")
    private Date subsidyAuditSubmitTime;

    @ApiModelProperty("补贴审核金额")
    private BigDecimal auditSubsidyAmount;

    /**
     * 降价活动优惠价格
     */
    private BigDecimal discountPrice;

    @ApiModelProperty("客户门店类型")
    private String customerStoreTypes;

    @ApiModelProperty("行情预测类型")
    private String marketForecastType;


    public SupplierSkuVo buildSkuStandardsList() {
        if(StringUtils.isNotBlank(spuStandards) && spuStandards.contains("|")) {
            String[] split = spuStandards.split("\\|");
            List<KeyValueBO> list = new ArrayList<>();
            for (String s : split) {
                if(StringUtils.isNotBlank(s) && s.contains(":")){
                    list.add(new KeyValueBO(s.split(":")[0], s.split(":")[1]));
                }
            }
            this.skuStandardsBoList = list;
        }
        return this;
    }

    public SupplierSkuVo tranSpuStandardsName(){
        spuStandardsName = spuStandards;
        // 支持单规格和多规格
        spuStandards = KeyValueBO.parseSkuStandardsValuesName(spuStandards);
        return this;
    }
    public SupplierSkuVo recoverDbSpuStandards(){
        spuStandards = this.getDbSpuStandards();
        return this;
    }

    /**
     * 获取数据库存储规格 - 原字段可能会被转义成前端显示规格了
     * @return
     */
    public String getDbSpuStandards() {
        if (StringUtils.isBlank(spuStandardsName)) {
            return spuStandards;
        }
        if (StringUtils.isBlank(spuStandards)) {
            return spuStandardsName;
        }
        if (spuStandardsName.length() > spuStandards.length()) {
            return spuStandardsName;
        }
        return spuStandards;
    }

}
