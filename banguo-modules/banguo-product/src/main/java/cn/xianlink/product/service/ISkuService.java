package cn.xianlink.product.service;

import cn.xianlink.common.api.bo.RemoteRegionWhMsgBo;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.product.api.domain.bo.*;
import cn.xianlink.product.api.domain.vo.RemoteSkuVo;
import cn.xianlink.product.api.domain.vo.RemoteSupplierSkuInfoVo;
import cn.xianlink.product.domain.Sku;
import cn.xianlink.product.api.domain.bo.SupplierSkuSaleNumBo;
import cn.xianlink.product.domain.bo.UpdateSkuPriceBo;
import cn.xianlink.product.domain.supplierSku.bo.*;
import cn.xianlink.product.domain.supplierSku.vo.*;
import cn.xianlink.product.domain.supplierSkuStock.vo.SupplierSkuStockVo;
import cn.xianlink.product.domain.vo.CwStockDetailVo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 供应商销售批次商品Service接口
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
public interface ISkuService extends IService<Sku> {

    /**
     * 根据批次id查询批次详情
     * 【注意】规格不会转换成 value 的形式!
     * @param id
     * @return
     */
    SupplierSkuVo selectById(Long id);

    /**
     * 根据供应商商品销售批次ID获取供应商商品销售批次详细信息
     */
    SupplierSkuInfoVo queryById(Long id);

    /**
     * 查询供应商销售批次商品列表
     * @param bo
     * @return
     */
    TableDataInfo<SupplierSkuVo> queryPageList(SupplierSkuPageBo bo);


    /**
     * 供应商商品查询
     * @param bo
     * @return
     */
    TableDataInfo<SupplierSkuVo> queryPage(SupplierSkuSimplePageBo bo);


    /**
     * new - 新增供应商销售批次商品后同步sku
     *
     * @return
     */
    Long insertBatchSyncSku(Long supplierSkuId);

    /**
     * new - 修改供应商销售批次商品后同步sku
     *
     * @return
     */
    Long updateBatchSyncSku(Long supplierSkuId);



    /**
     * 根据code查询详情
     */
    SupplierSkuVo getBySupplierSkuCode(String code);

    /**
     * 根据商品条码获取批次
     *
     * @param bo@return
     */
    SupplierSkuVo getByLabel(RemoteQuerySkuIdBo bo);


    /**
     * 批量获取库存信息
     *
     * @param supplierSkuIds
     * @return
     */
    List<CwStockDetailVo> listSupplierSkuStockBySupplierIds(List<Long> supplierSkuIds);

    /**
     * 获取供应商商品库存
     * @param supplierSkuId
     * @return
     */
    SupplierSkuStockVo getSupplierSkuStock(Long supplierSkuId);

    /**
     * 查询最新的批次id
     * @param skuIds
     * @return
     */
    List<Long> queryNewSupplierSkuIds(List<Long> skuIds);

    void batchDown(RemoteRegionWhMsgBo bo);


    /***
     * 获取在售商品数量
     *
     * @param bo@return
     */
    List<SupplierSkuSaleNumBo> querySupplierSkuSaleNum(List<SupplierOnSaleNumBo> bo);

    SupplierSkuSaleNumBo querySupplierSkuSaleNum(Long supplierId, Long regionWhId,Integer saleType);

    List<RemoteSupplierSkuInfoVo> querySkuIds(List<Long> ids);

    /**
     * 根据skuId查询自定义字段
     * @param skuIds
     * @return
     */
    List<RemoteSkuVo> getSkuCustomFieldByIds(List<Long> skuIds);

    /**
     * skuId关联
     * @param map <skuId, relationSkuId>
     */
    void batchRelationSkuId(Map<Long, Long> map);
    /**
     * 根据供应商spuCode查询商品
     *
     * @param supplierSpuCodes
     * @return
     */
    List<Sku> listBySupplierSpuCodes(List<String> supplierSpuCodes);
    List<Sku> querySkuIdByDistribution(Long skuId);

    TableDataInfo<RemoteSkuVo> getSkuIdBySupplier(Long supplierId, String spuName, BigDecimal price, PageQuery pageQuery);

    Integer getPriceCount(SupplierSkuSimplePageBo bo);

    List<RemoteSkuVo> getCategoryMinPriceByNames(List<String> categoryName);

    Long updatePriceById(UpdateSkuPriceBo updateSkuPriceBo);

    /**
     * 查询商品
     * @param bo
     * @return
     */
    TableDataInfo<SupplierSkuVo> findSkuPage(RemoteSkuQueryBo bo);
}
