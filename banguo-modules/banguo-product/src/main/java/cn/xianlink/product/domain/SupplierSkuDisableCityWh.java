package cn.xianlink.product.domain;

import cn.xianlink.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 供应商销售批次商品禁下单城市仓对象 supplier_sku_disable_city_wh
 *
 * <AUTHOR>
 * @date 2024-03-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("supplier_sku_disable_city_wh")
public class SupplierSkuDisableCityWh extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 供应商销售批次商品（sku）id
     */
    private Long supplierSkuId;

    private Long skuId;

    /**
     * 状态（0-待确定  1-已确定 2-已驳回 3-已驳回和未禁用））
     */
    private Integer status;

    /**
     * 城市仓id
     */
    private Long cityWhId;

    /**
     * 城市仓唯一编码
     */
    private String cityWhCode;

    /**
     * 删除标志（0代表存在 存入这条数据的主键代表删除）
     */
    @TableLogic(delval = "id")
    private Long delFlag;
}
