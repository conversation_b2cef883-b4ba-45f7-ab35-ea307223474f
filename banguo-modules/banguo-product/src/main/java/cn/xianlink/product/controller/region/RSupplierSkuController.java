package cn.xianlink.product.controller.region;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.xianlink.common.api.enums.product.SupplierSkuBatchTypeEnum;
import cn.xianlink.common.api.enums.product.SupplierSkuStatusEnum;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.validate.AddGroup;
import cn.xianlink.common.core.validate.EditGroup;
import cn.xianlink.common.core.validate.QueryGroup;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.log.annotation.Log;
import cn.xianlink.common.log.enums.BusinessType;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.product.domain.PublicId;
import cn.xianlink.product.domain.PublicIdList;
import cn.xianlink.product.domain.supplierSku.bo.*;
import cn.xianlink.product.domain.supplierSku.vo.*;
import cn.xianlink.product.domain.supplierSkuStock.vo.SupplierSkuStockVo;
import cn.xianlink.product.service.ISupplierSkuService;
import cn.xianlink.common.api.enums.product.SupplierSkuApproveLogOperateEnum;
import cn.xianlink.system.api.model.LoginUser;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Objects;
import java.util.Optional;

/**
 * 供应商销售批次商品
 * 前端访问路由地址为:/product/region/supplierSku
 * <AUTHOR>
 * @date 2024-03-22
 * @folder 总仓助手(小程序)/商品/总仓助手-供应商销售批次商品
 */
@Tag(name = "总仓助手-供应商销售批次商品")
@Validated
@RequiredArgsConstructor
@RestController("regionRSupplierSkuController")
@RequestMapping("/product/region/supplierSku")
public class RSupplierSkuController extends BaseController {

    private final ISupplierSkuService skuService;

//    @SaCheckPermission("region:supplierSpu:querySupSubscribePage")
    @PostMapping("/querySupSubscribePage")
    @Operation(summary = "上架申请列表")
    @Deprecated
    public R<TableDataInfo<QueryRegSubscribePageVo>> querySupSubscribePage(@RequestBody QueryRegSubscribePageBo bo) {
        return R.ok(skuService.querySupSubscribePage(bo));
    }

//    @SaCheckPermission("region:supplierSpu:queryPassPage")
    @PostMapping("/queryPassPage")
    @Operation(summary = "上下架列表")
    @Deprecated
    public R<TableDataInfo<QueryPassPageVo>> queryPassPage(@Validated(QueryGroup.class) @RequestBody QueryPassPageBo bo) {
        return R.ok(skuService.queryPassPage(bo));
    }

    @PostMapping("/queryPage")
    @Operation(summary = "供应商商品查询")
    public R<TableDataInfo<SupplierSkuVo>>  queryPage(@Validated @RequestBody SupplierSkuSimplePageBo bo) {
        bo.setIsManage(1);
        return R.ok(skuService.queryPage(bo));
    }

    @PostMapping("/querySubsidyAuditPage")
    @Operation(summary = "供应商商品-补贴审核列表")
    public R<TableDataInfo<SupplierSkuVo>> querySubsidyAuditPage(@Validated @RequestBody SupplierSkuSimplePageBo bo) {
        bo.setIsManage(1);
        return R.ok(skuService.querySubsidyAuditPage(bo));
    }

    @GetMapping("/querySkuInfoSingle")
    @Operation(summary = "城市仓多货选择供应商商品销售批次列表接口(单个商品)")
    public R<QueryHistoryPageVo> querySkuInfoSingle(@RequestParam("supplierSpuCode") String supplierSpuCode, @RequestParam("regionWhId") Long regionWhId) {
        QueryHistoryPageBo bo = new QueryHistoryPageBo();
        bo.setRegionWhId(regionWhId);
        bo.setSupplierSpuCode(supplierSpuCode);
        return R.ok(skuService.querySkuInfoSingle(bo));
    }

    @PostMapping("/queryPage/quickUp")
    @Operation(summary = "供应商商品查询-快捷供货")
    public R<TableDataInfo<SupplierSkuVo>>  queryPageByQuickUp(@Validated @RequestBody SupplierSkuSimplePageBo bo) {
        bo.setIsManage(1);
        return R.ok(skuService.queryPageByQuickUp(bo));
    }

    @PostMapping("/queryPage/auditDelivery")
    @Operation(summary = "供应商商品查询-送货审核")
    public R<TableDataInfo<SupplierSkuVo>>  queryDeliveryAuditPage(@Validated @RequestBody SupplierSkuSimplePageBo bo) {
        bo.setIsManage(1);
        return R.ok(skuService.queryDeliveryAuditPage(bo));
    }

//    @SaCheckPermission("region:supplierSpu:getInfo")
    @PostMapping("/getInfo")
    @Operation(summary = "根据供应商商品销售批次ID获取供应商商品销售批次详细信息")
    public R<SupplierSkuInfoVo> getInfo(@RequestBody PublicId publicId) {
        return R.ok(skuService.queryById(publicId.getId(), true));
    }

//    @SaCheckPermission("region:supplierSpu:edit")
    @Log(title = "总仓助手-供应商销售批次商品", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/edit")
    @Operation(summary = "修改供应商销售批次商品")
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody UpdateSupplierSkuBo bo) {
        bo.setOperate(SupplierSkuApproveLogOperateEnum.BUYER.getDesc() + LoginHelper.getLoginUser().getRealName());
        SupplierSkuVo skuVo = skuService.selectById(bo.getId());

        Integer businessType = bo.getBusinessType();
        skuService.checkProvide(businessType, bo.getProvideRegionWhId());
        skuService.checkPortageTeam(businessType, bo.getBasPortageTeamId());

        return toAjax(skuService.updateByBo(bo, skuVo));
    }

    //    @SaCheckPermission("region:supplierSpu:changeStatus")
    @Log(title = "总仓助手-供应商销售批次商品", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/changeStatus")
    @Operation(summary = "修改状态")
    public R<Void> changeStatus(@Validated(EditGroup.class) @RequestBody ChangeStatusBo bo) {

        return toAjax(skuService.changeStatus(bo));
    }

//    @SaCheckPermission("region:supplierSpu:clearStock")
    @Log(title = "总仓助手-供应商销售批次商品", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/clearStock")
    @Operation(summary = "清空库存")
    public R<Void> clearStock(@Validated(EditGroup.class) @RequestBody PublicIdList bo) {
        return toAjax(skuService.clearStock(bo.getIdList()));
    }

//    @SaCheckPermission("sup:supplierSku:addTail")
    @Log(title = "总仓助手-供应商销售批次商品", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/addTail")
    @Operation(summary = "尾货上架")
    public R<Void> addTail(@Validated(AddGroup.class) @RequestBody AddTailSupplierSkuBo bo) {
        return toAjax(skuService.addTail(bo));
    }

    @Log(title = "总仓助手-供应商销售批次商品", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/replaceSupplier")
    @Operation(summary = "换供应商")
    public R<Void> replaceSupplier(@Validated(AddGroup.class) @RequestBody ReplaceSupplierBo bo) {
        return toAjax(skuService.replaceSupplier(bo));
    }

    @Log(title = "总仓助手-供应商销售批次商品", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/quickUp")
    @Operation(summary = "快捷上架")
    public R<Void> quickUp(@Validated(EditGroup.class) @RequestBody QuickUpBo bo) {
        LoginUser user = LoginHelper.getLoginUser();
        if(user == null) {
            throw new ServiceException("用户不存在");
        }
        if(Objects.equals(1, bo.getUpType()) && (ObjectUtil.isEmpty(bo.getSubsidyAmount()) || ObjectUtil.isEmpty(bo.getMarketPrice()))) {
            throw new ServiceException("补贴上架的市场价和补贴金额不能为空");
        }
        String operate = SupplierSkuApproveLogOperateEnum.BUYER.getDesc() + user.getRealName()+ "," + SupplierSkuBatchTypeEnum.BATCH_TYPE111.getDesc();
        SupplierSkuVo skuVo = skuService.selectById(bo.getId());
        skuVo.setMarketForecastType(bo.getMarketForecastType());
        if(skuVo == null) {
            throw new ServiceException("供应商商品销售批次不存在");
        }
        if(bo.getStock() != null) {
            skuVo.setStock(bo.getStock());
        }else {
            skuVo.setStock(null);
        }
        if(bo.getPrice() != null) {
            skuVo.setPrice(bo.getPrice());
        }
        //是否补贴上架
        if(Objects.equals(1, bo.getUpType())){
            skuVo.setSubsidyAmount(bo.getSubsidyAmount());
            skuVo.setMarketPrice(bo.getMarketPrice());
            skuVo.setBargain(1);
        }
        skuService.checkPortageTeam(skuVo.getBusinessType(), skuVo.getBasPortageTeamId());

        return toAjax(skuService.quickUp(skuVo, false, operate, bo.getUpType()));
    }

    @PostMapping("/getBackInfoBySkuId")
    @Operation(summary = "根据供应商商品销售批次ID查询供应商供货的回填信息")
    public R<GetBackInfoVo> getBackInfoBySkuId(@Validated(QueryGroup.class) @RequestBody PublicId id) {
        return R.ok(skuService.getBackInfoBySkuId(id.getId()));
    }

    @PostMapping("/getBackInfo")
    @Operation(summary = "根据平台商品ID查询供应商供货的回填信息")
    public R<GetBackInfoVo> getBackInfo(@Validated @RequestBody GetBackInfoBo bo) {
        bo.setManage(true);
        return R.ok(skuService.getBackInfo(bo));
    }

    @Log(title = "总仓助手-供应商销售批次商品", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/add")
    @Operation(summary = "修改并快捷上架")
    public R<Long> add(@Validated(AddGroup.class) @RequestBody AddSupplierSkuBo bo) {
        LoginUser user = LoginHelper.getLoginUser();
        if(user == null) {
            throw new ServiceException("用户不存在");
        }
        // 前端传入 供应商 + 部门
        bo.setStatus(SupplierSkuStatusEnum.STATUS4.getCode());
        //补贴金额大于0，要走补贴待审核
        if(ObjectUtil.isNotEmpty(bo.getSubsidyAmount()) && bo.getSubsidyAmount().compareTo(BigDecimal.ZERO) > 0) {
            bo.setStatus(SupplierSkuStatusEnum.STATUS8.getCode());
        }
        bo.setOperate(SupplierSkuApproveLogOperateEnum.BUYER.getDesc() + user.getRealName() + "," + SupplierSkuBatchTypeEnum.BATCH_TYPE111.getDesc());
        // 档口不传默认为供应商
        bo.setSupplierDeptId(Optional.ofNullable(bo.getSupplierDeptId()).orElseGet(()->0L));

        Integer businessType = bo.getBusinessType();
        skuService.checkProvide(businessType, bo.getProvideRegionWhId());
        skuService.checkPortageTeam(businessType, bo.getBasPortageTeamId());

        if (StrUtil.isBlank(bo.getMarketForecastType())){
            throw new ServiceException("行情预测类型为空，请选择行情预测类型");
        }
        return R.ok(skuService.insertByBo(bo));
    }

    @PostMapping("/getStock")
    @Operation(summary = "获取库存信息")
    public R<SupplierSkuStockVo> getStock(@Validated(QueryGroup.class) @RequestBody PublicId id) {
        return R.ok(skuService.getSupplierSkuStock(id.getId()));
    }

    @Log(title = "总仓助手-修改批次条码", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/changeLabel")
    @Operation(summary = "修改批次条码")
    public R<Void> changeLabel(@Validated(EditGroup.class) @RequestBody UpdateSupplierSkuBo bo) {
        if(bo.getId() == null){
            throw new ServiceException("批次号不能为空");
        }
        if(StringUtils.isBlank(bo.getSkuLabel())){
            throw new ServiceException("条形码不能为空");
        }
        skuService.changeLabel(bo);
        return R.ok();
    }

    @Log(title = "总仓助手-批次送货审核", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/audit/delivery")
    @Operation(summary = "批次送货审核")
    public R<Void> auditDelivery(@Validated(EditGroup.class) @RequestBody UpdateSupplierSkuBo bo) {
        if(bo.getId() == null){
            throw new ServiceException("批次号不能为空");
        }
        if(Objects.isNull(bo.getPassDeliveryAudit())){
            throw new ServiceException("送货审核结果不能为空");
        }
        skuService.auditDelivery(bo);
        return R.ok();
    }

    @Log(title = "总仓助手-手动上架配货商品", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/auto/insert/ph")
    @Operation(summary = "配货商品手动上架")
    public R<Void> phSku(@RequestBody PublicId bo) {
        skuService.autoInsertList(bo.getRegionWhId(), null);
        return R.ok();
    }



    /**
     * 总仓多货选择供应商商品销售批次列表接口
     * @param bo
     * @return
     */
    @PostMapping("/queryHistoryPage")
    @Operation(summary = "总仓多货选择供应商商品销售批次列表接口")
    public R<TableDataInfo<QueryHistoryPageVo>> queryHistoryPage(@RequestBody QueryHistoryPageBo bo) {
        return R.ok(skuService.queryHistoryPage(bo));
    }

    @GetMapping("/exportTodayPrice/{regionWhId}/{saleDate}")
    @ApiOperation(value = "导出今日报价")
    @RepeatSubmit()
    public R<String> exportTodayPrice(@PathVariable("regionWhId") Long regionWhId, @PathVariable("saleDate") LocalDate saleDate) {
        return R.ok(null,skuService.exportTodayPrice(regionWhId,saleDate));
    }

    /**
     * 坑位校验
     */
    @GetMapping("/checkSkuMaxSaleNum")
    @ApiOperation(value = "商品供货时校验坑位")
    public R<CheckSkuMaxSaleNumResVo> checkSkuMaxSaleNum(@RequestParam("regionWhId") Long regionWhId, @RequestParam("supplierId") Long supplierId){
        return R.ok(skuService.checkSkuMaxSaleNum(regionWhId,supplierId));
    }

    /**
     * 获取最大坑位和剩余坑位数
     */
    @GetMapping("/getSaleNum")
    @ApiOperation(value = "获取最大坑位和剩余坑位数")
    public R<SaleNumVo> getSaleNum(@RequestParam("regionWhId") Long regionWhId, @RequestParam("supplierId") Long supplierId){
        return R.ok(skuService.getSaleNum(regionWhId,supplierId));
    }
}
