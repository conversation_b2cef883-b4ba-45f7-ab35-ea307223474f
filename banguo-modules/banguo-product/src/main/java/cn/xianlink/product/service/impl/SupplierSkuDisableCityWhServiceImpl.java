package cn.xianlink.product.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.basic.api.RemoteCityWhService;
import cn.xianlink.basic.api.RemoteRegionWhService;
import cn.xianlink.basic.api.domain.bo.WhNexusBo;
import cn.xianlink.basic.api.domain.vo.CityWhVoAppletVo;
import cn.xianlink.basic.api.domain.vo.RemoteCityWhVo;
import cn.xianlink.basic.api.domain.vo.RemoteRegionWhVo;
import cn.xianlink.bi.api.IRemoteBiSkuService;
import cn.xianlink.bi.api.domain.bo.RemoteSkuLossBo;
import cn.xianlink.bi.api.domain.vo.RemoteSkuLossVo;
import cn.xianlink.common.api.util.SaleDateUtil;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.domain.page.RemotePageQuery;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.product.api.domain.bo.RemoteSkuCityDisableMsgBo;
import cn.xianlink.product.api.domain.vo.RemoteSupplierDisableCityWhVo;
import cn.xianlink.product.config.SkuGlobalProperties;
import cn.xianlink.product.domain.SupplierSkuDisableCityWh;
import cn.xianlink.product.domain.dto.DisableCityDto;
import cn.xianlink.product.domain.dto.SkuDisableCityListDto;
import cn.xianlink.product.domain.supplierSku.vo.SupplierSkuVo;
import cn.xianlink.product.domain.supplierSkuDisableCityWh.bo.AddSupplierSkuDisableCityWhBo;
import cn.xianlink.product.domain.supplierSkuDisableCityWh.vo.SupplierSkuDisableCityWhVo;
import cn.xianlink.product.domain.vo.SkuDisableCityListWhVo;
import cn.xianlink.product.mapper.SupplierSkuDisableCityWhMapper;
import cn.xianlink.product.mq.producer.SkuDisableCitySendMsgProducer;
import cn.xianlink.product.service.ISupplierSkuDisableCityWhService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 供应商销售批次商品禁下单城市仓Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
@RequiredArgsConstructor
@Service
@CustomLog
public class SupplierSkuDisableCityWhServiceImpl implements ISupplierSkuDisableCityWhService {

    private final SupplierSkuDisableCityWhMapper supplierSkuDisableCityWhMapper;

    private final SkuDisableCitySendMsgProducer sendMsgProducer;

    @DubboReference
    private final RemoteRegionWhService remoteRegionWhService;

    @DubboReference
    private RemoteCityWhService remoteCityWhService;


    @Autowired
    private SkuGlobalProperties skuGlobalProperties;

    @DubboReference
    private final IRemoteBiSkuService remoteBiSkuService;

    @Override
    public List<RemoteSupplierDisableCityWhVo> listBySupplierSkuIds(List<Long> skuIds) {
        if (ObjectUtil.isEmpty(skuIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<SupplierSkuDisableCityWh> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(SupplierSkuDisableCityWh::getSkuId, skuIds).eq(SupplierSkuDisableCityWh::getStatus, 1);

        List<SupplierSkuDisableCityWhVo> supplierSkuDisableCityWhVos = supplierSkuDisableCityWhMapper.selectVoList(wrapper);
        if (ObjectUtil.isEmpty(supplierSkuDisableCityWhVos)) {
            return Collections.emptyList();
        }

        return BeanUtil.copyToList(supplierSkuDisableCityWhVos, RemoteSupplierDisableCityWhVo.class);
    }

    @Override
    public List<Long> getSkuIdsByCityWhId(Long cityWhId) {
        return this.getSkuIdsByCityWhId(cityWhId, null);
    }

    @Override
    public List<Long> getSkuIdsByCityWhId(Long cityWhId, List<Long> skuIds) {
        return supplierSkuDisableCityWhMapper.getSkuIdsByCityWhId(cityWhId, skuIds);
    }

    @Override
    public Page<SkuDisableCityListWhVo> listWithSku(SkuDisableCityListDto dto) {

        Page<SkuDisableCityListWhVo> skuDisableCityListWhVoPage = supplierSkuDisableCityWhMapper.listWithSku(dto, dto.build());

        List<SkuDisableCityListWhVo> records = skuDisableCityListWhVoPage.getRecords();
        if (CollUtil.isEmpty(records)) {
            return skuDisableCityListWhVoPage;
        }

        // 报损信息
        List<Long> skuIdList = records.stream().map(SkuDisableCityListWhVo::getSkuId).toList();
        Long regionId = ObjectUtil.defaultIfNull(dto.getRegionWhId(), records.get(0).getRegionWhId());
        Map<Long, RemoteSkuLossVo> remoteSkuLossVoMap = this.getRemoteSkuLossVoMap(skuIdList, regionId, dto.getCityWhId());
        for (SkuDisableCityListWhVo record : records) {
            record.setRemoteSkuLossVo(remoteSkuLossVoMap.getOrDefault(record.getSkuId(), new RemoteSkuLossVo().init()));
        }


        return skuDisableCityListWhVoPage;
    }

    @Override
    public void updateStatus(List<Long> skuId, Integer status, Long cityWhId) {
        if (CollUtil.isEmpty(skuId)) {
            return;
        }
        skuId.forEach(e -> insertSkuDisableCityRecord(e, cityWhId, status));
    }

    @Override
    public void updateStatus(Long skuId, Integer status, Long cityWhId) {
        if (skuId == null || skuId <= 0) {
            return;
        }
        insertSkuDisableCityRecord(skuId, cityWhId, status);
    }

    @Override
    public void insertSkuDisableCityRecord(Long skuId, Long cityWhId, Integer status) {
        LambdaQueryWrapper<SupplierSkuDisableCityWh> in = new LambdaQueryWrapper<SupplierSkuDisableCityWh>()
                .select(SupplierSkuDisableCityWh::getId, SupplierSkuDisableCityWh::getStatus)
                .eq(SupplierSkuDisableCityWh::getCityWhId, cityWhId)
                .eq(SupplierSkuDisableCityWh::getSkuId, skuId).orderByDesc(SupplierSkuDisableCityWh::getId);
        SupplierSkuDisableCityWh db = supplierSkuDisableCityWhMapper.selectOne(in);
        if (db != null) {
            if (db.getStatus().equals(status)) {
                return;
            }
            db.setStatus(status);
            supplierSkuDisableCityWhMapper.updateById(db);
            return;
        }
        RemoteCityWhVo remoteCityWhVo = remoteCityWhService.queryById(cityWhId);

        SupplierSkuDisableCityWh supplierSkuDisableCityWh = new SupplierSkuDisableCityWh();
        supplierSkuDisableCityWh.setSkuId(skuId);
        supplierSkuDisableCityWh.setCityWhId(cityWhId);
        supplierSkuDisableCityWh.setCityWhCode(remoteCityWhVo.getCode());
        supplierSkuDisableCityWh.setStatus(status);
        supplierSkuDisableCityWhMapper.insert(supplierSkuDisableCityWh);
    }

    @Override
    public List<CityWhVoAppletVo> selectListByRegionWhCode(String regionWhCode, Long skuId) {

        // 根据总仓查询城市仓
        List<WhNexusBo> whNexusBos = remoteRegionWhService.selectListByRegionWhCode(regionWhCode);
        if (CollUtil.isEmpty(whNexusBos)) {
            return List.of();
        }

        // 获取所有城市仓信息
        List<Long> cityIdList = whNexusBos.stream().map(WhNexusBo::getCityWhId).collect(Collectors.toList());
        List<RemoteCityWhVo> remoteCityWhVos = remoteCityWhService.queryList(cityIdList);
        if (CollUtil.isEmpty(remoteCityWhVos)) {
            return List.of();
        }
        List<CityWhVoAppletVo> cityWhVoAppletVos = remoteCityWhVos.stream().map(e -> {
            CityWhVoAppletVo cityWhVoAppletVo = new CityWhVoAppletVo();
            cityWhVoAppletVo.setId(e.getId());
            cityWhVoAppletVo.setCityWhCode(e.getCode());
            cityWhVoAppletVo.setCityWhName(e.getName());
            cityWhVoAppletVo.setAddress(e.getAddress());
            cityWhVoAppletVo.setAreaFullName(e.getAreaFullName());
            cityWhVoAppletVo.setStatus(0);
            return cityWhVoAppletVo;
        }).toList();

        if (skuId == null) {
            return cityWhVoAppletVos;
        }

        // 城市仓禁用过滤
        Set<String> set = cityWhVoAppletVos.stream().map(CityWhVoAppletVo::getCityWhCode).collect(Collectors.toSet());

        LambdaQueryWrapper<SupplierSkuDisableCityWh> in = new LambdaQueryWrapper<SupplierSkuDisableCityWh>()
                .select(SupplierSkuDisableCityWh::getCityWhCode, SupplierSkuDisableCityWh::getStatus)
                .in(SupplierSkuDisableCityWh::getCityWhCode, set)
                .eq(SupplierSkuDisableCityWh::getSkuId, skuId)
                // 审核的不算禁用，任意修改
                .ne(SupplierSkuDisableCityWh::getStatus, 0);
        List<SupplierSkuDisableCityWh> supplierSkuDisableCityWhs = supplierSkuDisableCityWhMapper.selectList(in);

        if (CollUtil.isNotEmpty(supplierSkuDisableCityWhs)) {
            Map<String, Integer> map = supplierSkuDisableCityWhs.stream().collect(Collectors.toMap(e -> e.getCityWhCode(), e -> e.getStatus()));
            for (CityWhVoAppletVo vo : cityWhVoAppletVos) {
                vo.setStatus(map.get(vo.getCityWhCode()));
            }
        }

        return cityWhVoAppletVos;
    }

    /**
     * 获取总仓能操作的城市仓列表（分页）
     *
     * @param dto
     * @return
     */
    @Override
    public TableDataInfo<CityWhVoAppletVo> selectListByRegionWhCodeByPage(DisableCityDto dto) {
        String regionWhCode = dto.getRegionWhCode();
        String cityWhName = dto.getCityWhName();
        Long skuId = dto.getSkuId();
        RemotePageQuery remotePageQuery = new RemotePageQuery();
        remotePageQuery.setPageNum(dto.getPageNum());
        remotePageQuery.setPageSize(dto.getPageSize());
        // 根据总仓查询城市仓
        TableDataInfo<CityWhVoAppletVo> cityWhVoAppletVosPage = remoteRegionWhService.selectListByRegionWhCodeByPage(regionWhCode,cityWhName, remotePageQuery);
        List<CityWhVoAppletVo> cityWhVoAppletVos = cityWhVoAppletVosPage.getRows();
        if (CollUtil.isEmpty(cityWhVoAppletVos)) {
            return TableDataInfo.build();
        }

        // 获取所有城市仓信息
//        List<Long> cityIdList = whNexusBos.stream().map(WhNexusBo::getCityWhId).collect(Collectors.toList());
//        List<RemoteCityWhVo> remoteCityWhVos = remoteCityWhService.queryList(cityIdList);
//        if (CollUtil.isEmpty(remoteCityWhVos)) {
//            return TableDataInfo.build();
//        }
//        List<CityWhVoAppletVo> cityWhVoAppletVos = remoteCityWhVos.stream().map(e -> {
//            CityWhVoAppletVo cityWhVoAppletVo = new CityWhVoAppletVo();
//            cityWhVoAppletVo.setId(e.getId());
//            cityWhVoAppletVo.setCityWhCode(e.getCode());
//            cityWhVoAppletVo.setCityWhName(e.getName());
//            cityWhVoAppletVo.setAddress(e.getAddress());
//            cityWhVoAppletVo.setAreaFullName(e.getAreaFullName());
//            cityWhVoAppletVo.setStatus(0);
//            return cityWhVoAppletVo;
//        }).toList();
        cityWhVoAppletVos.stream().map(e -> {
            e.setStatus(0);
            return e;
        }).toList();

        if (skuId == null) {
            return TableDataInfo.build(cityWhVoAppletVos,cityWhVoAppletVosPage.getTotal());
        }

        // 城市仓禁用过滤
        Set<String> set = cityWhVoAppletVos.stream().map(CityWhVoAppletVo::getCityWhCode).collect(Collectors.toSet());

        LambdaQueryWrapper<SupplierSkuDisableCityWh> in = new LambdaQueryWrapper<SupplierSkuDisableCityWh>()
                .select(SupplierSkuDisableCityWh::getCityWhCode, SupplierSkuDisableCityWh::getStatus)
                .in(SupplierSkuDisableCityWh::getCityWhCode, set)
                .eq(SupplierSkuDisableCityWh::getSkuId, skuId)
                // 审核的不算禁用，任意修改
                .ne(SupplierSkuDisableCityWh::getStatus, 0);
        List<SupplierSkuDisableCityWh> supplierSkuDisableCityWhs = supplierSkuDisableCityWhMapper.selectList(in);

        if (CollUtil.isNotEmpty(supplierSkuDisableCityWhs)) {
            Map<String, Integer> map = supplierSkuDisableCityWhs.stream().collect(Collectors.toMap(e -> e.getCityWhCode(), e -> e.getStatus()));
            for (CityWhVoAppletVo vo : cityWhVoAppletVos) {
                vo.setStatus(map.get(vo.getCityWhCode()));
            }
        }

        return TableDataInfo.build(cityWhVoAppletVos, cityWhVoAppletVosPage.getTotal());
    }


    /**
     * 批量新增供应商销售批次商品禁下单城市仓
     *
     * @param skuId
     * @param boList
     */
    @Override
    public void insertBySkuIfNotExist(Long skuId, List<AddSupplierSkuDisableCityWhBo> boList) {
        if (skuId == 0L || CollUtil.isEmpty(boList)) {
            return;
        }
        List<SupplierSkuDisableCityWhVo> supplierSkuDisableCityWhVos = supplierSkuDisableCityWhMapper.queryList(skuId, null);
        // 不要重复写入禁用
        Set<String> exitSet = supplierSkuDisableCityWhVos.stream().map(SupplierSkuDisableCityWhVo::getCityWhCode).collect(Collectors.toSet());

        List<SupplierSkuDisableCityWh> insertList = boList.stream().filter(e -> !exitSet.contains(e.getCityWhCode())).map(e -> {
            SupplierSkuDisableCityWh vo = new SupplierSkuDisableCityWh();
            vo.setSkuId(skuId);
            vo.setCityWhId(e.getId());
            vo.setCityWhCode(e.getCityWhCode());
            return vo;
        }).collect(Collectors.toList());

        insertAndSendMsg(insertList);
    }

    private void insertAndSendMsg(List<SupplierSkuDisableCityWh> insertList) {
        if (CollUtil.isNotEmpty(insertList)) {
            // 新增
            supplierSkuDisableCityWhMapper.insertBatch(insertList);

            // 通知
            List<RemoteSkuCityDisableMsgBo> collect = insertList.stream()
                    .map(e -> new RemoteSkuCityDisableMsgBo(e.getSkuId(), e.getCityWhId(), 0))
                    .toList();
            for (RemoteSkuCityDisableMsgBo remoteSkuCityDisableMsgBo : collect) {
                sendMsgProducer.send(remoteSkuCityDisableMsgBo);
            }
        }
    }

    @Override
    public void insertListIfNotExist(List<SupplierSkuDisableCityWh> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }

        // 查询已存在的记录
        LambdaQueryWrapper<SupplierSkuDisableCityWh> qw = Wrappers.lambdaQuery();
        qw.in(SupplierSkuDisableCityWh::getCityWhCode, list.stream().map(SupplierSkuDisableCityWh::getCityWhCode).collect(Collectors.toSet()))
                .in(SupplierSkuDisableCityWh::getSkuId, list.stream().map(SupplierSkuDisableCityWh::getSkuId).collect(Collectors.toSet()));
        List<SupplierSkuDisableCityWh> existingList = supplierSkuDisableCityWhMapper.selectList(qw);

        // 提取已存在的skuId和cityWhCode组合
        Set<String> existingSkuCityWhCodes = existingList.stream()
                .map(cityWh -> cityWh.getSkuId() + "_" + cityWh.getCityWhCode())
                .collect(Collectors.toSet());

        // 过滤掉已存在的记录
        List<SupplierSkuDisableCityWh> insertList = list.stream()
                .filter(cityWh -> !existingSkuCityWhCodes.contains(cityWh.getSkuId() + "_" + cityWh.getCityWhCode()))
                .collect(Collectors.toList());

        insertAndSendMsg(insertList);
    }

    @Override
    public void statusAutoPass(Long skuId, Long cityWhId) {
        LambdaQueryWrapper<SupplierSkuDisableCityWh> last = new LambdaQueryWrapper<SupplierSkuDisableCityWh>().eq(SupplierSkuDisableCityWh::getSkuId, skuId)
                .eq(SupplierSkuDisableCityWh::getCityWhId, cityWhId).orderByDesc(SupplierSkuDisableCityWh::getId).last("limit 1");
        SupplierSkuDisableCityWh wh = supplierSkuDisableCityWhMapper.selectOne(last);
        if (wh != null && wh.getStatus() == 0) {
            SupplierSkuDisableCityWh up = new SupplierSkuDisableCityWh();
            up.setId(wh.getId());
            up.setStatus(1);
            log.keyword("skuDisableCitySendMsgConsumer", skuId + "-" + cityWhId).info("sku城市仓禁用审核通过");
            supplierSkuDisableCityWhMapper.updateById(up);
        }
    }


    /**
     * 获取报损信息
     *
     * @param skuIdList
     * @param regionWhId
     * @return
     */
    public Map<Long, RemoteSkuLossVo> getRemoteSkuLossVoMap(List<Long> skuIdList, Long regionWhId, Long cityWhId) {

        RemoteRegionWhVo whVo = remoteRegionWhService.queryById(regionWhId);
        if (whVo == null) {
            throw new ServiceException("总仓不存在");
        }
        if (whVo.getStatus() == 0) {
            throw new ServiceException("总仓已被禁用");
        }
        LocalDate saleDate = SaleDateUtil.getSaleDate(whVo.getSalesTimeEnd());

        if (!skuGlobalProperties.getOpenBI()) {
            // bi 服务降级开关
            log.keyword("BI-Sku-Loss").info("bi 服务降级-不查询商品报损信息");
            return new HashMap<>();
        }
        RemoteSkuLossBo remoteSkuLossBo = new RemoteSkuLossBo();
        remoteSkuLossBo.setSkuIdList(skuIdList);
        remoteSkuLossBo.setSaleDateEnd(saleDate.plusDays(-ObjectUtil.defaultIfNull(skuGlobalProperties.getEndDay(), 1)));
        remoteSkuLossBo.setSaleDateStart(saleDate.plusDays(-ObjectUtil.defaultIfNull(skuGlobalProperties.getLossSkuDay(), 7)));
        remoteSkuLossBo.setCityWhId(cityWhId);
        List<RemoteSkuLossVo> skuLossByRemoteBI = null;
        List<RemoteSkuLossVo> skuInspectByRemoteBI = null;
        try {
            skuLossByRemoteBI = remoteBiSkuService.getSkuLossByRemoteBI(remoteSkuLossBo);
            remoteSkuLossBo.setSaleDateStart(saleDate.plusDays(-ObjectUtil.defaultIfNull(skuGlobalProperties.getInspectDay(), 180)));
            skuInspectByRemoteBI = remoteBiSkuService.getSkuInspectByRemoteBI(remoteSkuLossBo);
        } catch (Exception e) {
            log.keyword("BI-Sku-Loss").info("bi 服务请求出错, msg={}", e.getMessage(), e);
        }
        return RemoteSkuLossVo.mergeToMap(skuLossByRemoteBI, skuInspectByRemoteBI);
    }

}
