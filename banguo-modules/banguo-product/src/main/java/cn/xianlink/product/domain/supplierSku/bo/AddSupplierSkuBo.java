package cn.xianlink.product.domain.supplierSku.bo;

import cn.hutool.core.collection.CollUtil;
import cn.xianlink.common.api.enums.product.CategoryAfterTypeEnum;
import cn.xianlink.common.api.enums.product.SupplierSkuBusinessTypeEnum;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.core.validate.AddGroup;
import cn.xianlink.common.core.validate.EditGroup;
import cn.xianlink.product.domain.SupplierSku;
import cn.xianlink.product.domain.SupplierSpu;
import cn.xianlink.product.domain.supplierSku.vo.SupplierSkuVo;
import cn.xianlink.product.domain.supplierSkuDisableCityWh.bo.AddSupplierSkuDisableCityWhBo;
import cn.xianlink.product.domain.supplierSkuFile.bo.AddSupplierSkuFileBo;
import com.google.common.collect.Lists;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 供应商销售批次商品业务对象 sku
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
@Data
@AutoMappers({@AutoMapper(target = SupplierSpu.class), @AutoMapper(target = SupplierSku.class), @AutoMapper(target = SupplierSkuVo.class)})
public class AddSupplierSkuBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("作废批次id")
    private Long supplierSkuId;

    @ApiModelProperty("商品品种id")
//    @NotNull(message = "商品品种id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long categoryId;

    @ApiModelProperty("商品品种")
//    @NotBlank(message = "商品品种不能为空", groups = {AddGroup.class, EditGroup.class})
    private String categoryPathName;

    @ApiModelProperty("平台商品id")
    @NotNull(message = "平台商品id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long spuId;

    @ApiModelProperty("业务类型，1市采, 10地采, 20基采, 30产地，40配货")
    @NotNull(message = "商品类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer businessType;

    @ApiModelProperty("装卸队id")
    private Long basPortageTeamId;

    @ApiModelProperty("供货总仓id")
    private Long provideRegionWhId;

    @ApiModelProperty("总仓id")
    @NotNull(message = "总仓id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long regionWhId;

    @ApiModelProperty("商品等级")
    @NotBlank(message = "商品等级不能为空", groups = {AddGroup.class, EditGroup.class})
    private String spuGrade;

    @ApiModelProperty("商品等级描述")
//    @NotBlank(message = "商品等级描述不能为空", groups = {AddGroup.class, EditGroup.class})
    private String spuGradeDesc;


    @ApiModelProperty("商品规格")
//    @NotBlank(message = "商品规格不能为空", groups = {AddGroup.class, EditGroup.class})
    private String spuStandards;

    @ApiModelProperty("平台规格列表")
//    @NotEmpty(message = "平台规格列表不能为空", groups = {AddGroup.class, EditGroup.class})
    private List<KeyValueBO> skuStandardsBoList;

    @ApiModelProperty("产地来源：0国产 1进口")
    @NotNull(message = "产地来源不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer domestic ;

    @ApiModelProperty("产地")
//    @NotBlank(message = "产地不能为空", groups = {AddGroup.class, EditGroup.class})
    private String producer;

    @ApiModelProperty("商品毛重(斤)")
    @NotNull(message = "商品毛重不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal spuGrossWeight;

    @ApiModelProperty("商品净重(斤)")
    @NotNull(message = "商品净重不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal spuNetWeight;


    @ApiModelProperty("简介")
    @NotBlank(message = "简介不能为空", groups = {AddGroup.class, EditGroup.class})
    private String snapshot;

    @ApiModelProperty("单件价格")
    @NotNull(message = "单件价格不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal price;

    @ApiModelProperty("库存")
    @NotNull(message = "库存", groups = {AddGroup.class, EditGroup.class})
    @Min(value = 0, message = "库存不能小于0", groups = {AddGroup.class, EditGroup.class})
    private Integer stock;

    @ApiModelProperty("明日预测")
//    @NotNull(message = "明日预测不能为空", groups = {AddGroup.class, EditGroup.class})
    private String predictionTomorrow;

    @ApiModelProperty("未来预测")
//    @NotNull(message = "未来预测不能为空", groups = {AddGroup.class, EditGroup.class})
    private String predictionFuture;

    @ApiModelProperty("包装上的字")
    @NotBlank(message = "包装上的字不能为空", groups = {AddGroup.class, EditGroup.class})
    private String packageWord;

    @ApiModelProperty("包装容器-框，泡沫箱，袋等")
//    @NotNull(message = "包装不能为空", groups = {AddGroup.class, EditGroup.class})
    private String packageType;

    @ApiModelProperty("最小甜度")
    private BigDecimal sweetMin;

    @ApiModelProperty("最大甜度")
    private BigDecimal sweetMax;

    @ApiModelProperty("下单倍数")
    @Min(value = 1, message = "下单倍数必须是大于等于1的正整数", groups = {AddGroup.class, EditGroup.class})
    private Integer placeOrderMultiple;

    @ApiModelProperty("最小购买数量，为-1不限制")
    private Integer buyMin;

    @ApiModelProperty("最大购买数量，为-1不限制")
    private Integer buyMax;

    @ApiModelProperty("采购员code")
    private String buyerCode;

    @ApiModelProperty("采购员name")
    private String buyerName;


    @Schema(description = "售后天数：0无售后，其它暂时写死 1")
    private Integer afterSaleDay;

    /**
     * @see CategoryAfterTypeEnum
     */
    @ApiModelProperty("售后规则：0无售后、1正常售后、2库管验货")
    @NotNull(message = "售后规则不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer afterSaleType;


    @ApiModelProperty("免赔情况")
    private String deductibleSituation;

    @ApiModelProperty("可申请售后说明")
    private String afterSaleExplain;

    @ApiModelProperty("禁止下单城市仓ID集合")
    private List<AddSupplierSkuDisableCityWhBo> cityWhList;

    @ApiModelProperty("文件集合")
    private List<AddSupplierSkuFileBo> fileList;

    @ApiModelProperty(value = "供应商id")
    private Long supplierId;

    @ApiModelProperty("供应商子单位ID")
    private Long supplierDeptId;

    @ApiModelProperty("是否免检，0表示不是免检,1表示免检")
    private Integer isCheck;

    @ApiModelProperty(value = "操作步骤", hidden = true)
    private String operate;

    @ApiModelProperty(value = "状态,1提交/待审核,2驳回,3仅审核通过,4上架,5下架,6过季,7隐藏", hidden = true)
    private Integer status;

    @ApiModelProperty("区域编码")
    private String areaCode;

    @ApiModelProperty("同一批次批次识别码")
    @Length(min = 8, max = 32, message = "识别码不能超过32位字符")
    private String skuLabel;

    @ApiModelProperty("是否战略合作品，0为否，1为是")
    private Integer cooperation;

    @ApiModelProperty("礼盒，0为否，1为是")
    private Integer giftBox;

    @ApiModelProperty("小件，0为否，1为是")
    private Integer little;
    @ApiModelProperty("好吃，0为否，1为是")
    private Integer goodToEat;

    @ApiModelProperty("寿光蔬菜，0为否，1为是")
    private Integer shouguangVegetables;

    @ApiModelProperty("品牌")
    private String brand;

    @ApiModelProperty("产地简称")
    private String shortProducer;

    @ApiModelProperty("是否送货审核 - 0否df 1是")
    private Integer hasDeliveryAudit;

    @ApiModelProperty("1：校验批次存在")
    private Integer checkSkuExist;

    private Integer isSupplier;

    @ApiModelProperty("集采是否隐藏 1-是")
    private Integer isSaleHide;

    @ApiModelProperty("客户门店类型")
    private String customerStoreTypes;

    @ApiModelProperty("行情预测类型")
    private String marketForecastType;

    /**
     * 商品二级分类
     */
    private Long categoryIdLevel2;

    @ApiModelProperty("是否特价商品，0为否，1为是")
    private Integer bargain;
    private BigDecimal bargainRate;
    private BigDecimal bargainPrice;

    @ApiModelProperty("市场价")
    private BigDecimal marketPrice;

    /**
     * 为true时，跳过包装视频、质检报告等必填校验
     */
    private Boolean skipFileCheck = false;

    /**
     * 上架时间
     */
    private Date upTime;

    /**
     * 销售类型：1为般果代采，2为商家自营
     */
//    @NotNull(message = "销售类型不能为空")
    private Integer saleType;

    @ApiModelProperty("补贴金额")
    private BigDecimal subsidyAmount;

    public QueryLastListBo getQueryLastListBo() {
        QueryLastListBo bo = new QueryLastListBo();
        bo.setSupplierIdList(Lists.newArrayList(supplierId));
        bo.setSupplierDeptIdList(Lists.newArrayList(supplierDeptId));
        bo.setRegionWhIdList(Lists.newArrayList(regionWhId));
        bo.setSpuIdList(Lists.newArrayList(spuId));
        bo.setBusinessTypeList(Lists.newArrayList(SupplierSkuBusinessTypeEnum.BUSINESS_TYPE1.getCode()));
        return bo;
    }

    public void buildSkuStandardsSortJoin() {
        String listSortAndJoin = KeyValueBO.getListSortAndJoin(skuStandardsBoList);
        if(StringUtils.isNotBlank(listSortAndJoin)) {
            spuStandards = listSortAndJoin;
        }
    }

    @NotNull(message = "商品规格不能为空", groups = {AddGroup.class, EditGroup.class})
    public Integer getSpuStandardsCheck(){
        Integer result = null;
        if(StringUtils.isNotBlank(spuStandards)){
            result = 1;
        }
        if(CollUtil.isNotEmpty(skuStandardsBoList)){
            result = 2;
        }
        return result;
    }
}
