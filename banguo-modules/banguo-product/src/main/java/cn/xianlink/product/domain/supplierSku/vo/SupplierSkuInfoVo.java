package cn.xianlink.product.domain.supplierSku.vo;

import cn.xianlink.bi.api.domain.vo.RemoteSkuCardVo;
import cn.xianlink.bi.api.domain.vo.RemoteSkuLossVo;
import cn.xianlink.common.api.enums.product.CategoryAfterTypeEnum;
import cn.xianlink.common.dict.annotation.DictConvertFiled;
import cn.xianlink.common.dict.enums.DictConvertTypeEnum;
import cn.xianlink.product.domain.supplierSku.bo.KeyValueBO;
import cn.xianlink.product.domain.supplierSkuApproveLog.SupplierSkuApproveLogVo;
import cn.xianlink.product.domain.supplierSkuDisableCityWh.vo.SupplierSkuDisableCityWhVo;
import cn.xianlink.product.domain.supplierSkuFile.vo.SupplierSkuFileVo;
import cn.xianlink.product.domain.vo.CategoryExpVO;
import cn.xianlink.product.domain.vo.SpuCreateControlVO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

@Data
@AutoMappers({
        @AutoMapper(target = GetBackInfoVo.class),
})
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SupplierSkuInfoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("供应商批次商品id")
    private Long id;

    @ApiModelProperty("绑定的批次id")
    private Long supplierSkuId;

    @ApiModelProperty("商品skuId")
    private Long skuId;;

    @ApiModelProperty("供应商批次商品唯一编码")
    private String code;

    @ApiModelProperty("商品品种id")
    private Long categoryId;

    @ApiModelProperty("商品品种")
    private String categoryPathName;

    @ApiModelProperty("平台商品id")
    private Long spuId;

    @ApiModelProperty("平台商品唯一编码")
    private String spuCode;

    @ApiModelProperty("平台商品spu名称")
    private String spuName;

    @ApiModelProperty("商品等级描述")
    private String spuGradeDesc;

    @ApiModelProperty("平台规格列表")
    private List<KeyValueBO> skuStandardsBoList;

    @ApiModelProperty("产地来源：0国产 1进口")
    private Integer domestic ;

    @ApiModelProperty("产地")
    private String producer;

    @ApiModelProperty("商品毛重(斤)")
    private BigDecimal spuGrossWeight;

    @ApiModelProperty("商品净重(斤)")
    private BigDecimal spuNetWeight;

    @ApiModelProperty("平台商品等级，数据字典配置")
//    @DictConvertFiled(dictCode = "BasePro_rank", filedName = "spuGradeName", dictRemark = true)
    private String spuGrade;

    @ApiModelProperty("平台商品规格")
    private String spuStandards;

    @ApiModelProperty("储存商品规格")
    private String spuStandardsName;

    @ApiModelProperty("供应商商品id")
    private Long supplierSpuId;

    @ApiModelProperty("供应商商品唯一编码")
    private String supplierSpuCode;

    @ApiModelProperty("装卸队id")
    private Long basPortageTeamId;

    @ApiModelProperty("装卸队名称")
    private String basPortageTeamName;

    @ApiModelProperty("供货总仓id")
    private Long provideRegionWhId;

    @ApiModelProperty("供货总仓名称")
    private String provideRegionWhName;

    @ApiModelProperty("总仓id")
    private Long regionWhId;

    @ApiModelProperty("总仓唯一编码")
    private String regionWhCode;

    @ApiModelProperty("总仓名称")
    private String regionWhName;

    @ApiModelProperty("供应商id")
    private Long supplierId;

    @ApiModelProperty("供应商唯一编码")
    private String supplierCode;

    @ApiModelProperty("供应商短码")
    private String supplierSimpleCode;

    @ApiModelProperty("供应商名称")
    private String supplierName;

    /**
     * 供应商别名
     */
    private String supplierAlias;

    @ApiModelProperty("供应商子单位ID")
    private Long supplierDeptId;

    @ApiModelProperty("子供应商名称")
    private String supplierDeptName;

    @ApiModelProperty("是否战略合作品，0为否，1为是")
    private Integer cooperation;

    @ApiModelProperty("是否特价商品，0为否，1为是")
    private Integer bargain;
    private BigDecimal bargainRate;
    private BigDecimal bargainPrice;

    @ApiModelProperty("市场价")
    private BigDecimal marketPrice;

    @ApiModelProperty("礼盒，0为否，1为是")
    private Integer giftBox;

    @ApiModelProperty("小件，0为否，1为是")
    private Integer little;
    @ApiModelProperty("好吃，0为否，1为是")
    private Integer goodToEat;

    @ApiModelProperty("寿光蔬菜，0为否，1为是")
    private Integer shouguangVegetables;


    @ApiModelProperty("简介")
    private String snapshot;

    @ApiModelProperty("单件价格")
    private BigDecimal price;

    @ApiModelProperty("上架价格")
    private BigDecimal upPrice;

    @ApiModelProperty("净单价")
    private BigDecimal netWeightPrice;

    @ApiModelProperty("加入购物车数量")
    private Integer cartCount;

    @ApiModelProperty("库存")
    private Integer stock;

    @ApiModelProperty("锁定库存")
    private Integer lockStock;

    /**
     * 云仓可用库存
     */
    private Integer cloudStock;

    /**
     * 云仓锁定库存
     */
    private Integer cloudLockStock;

    /**
     * 云仓实际库存
     */
    private Integer cloudActualStock;

    /**
     * 云仓在途库存
     */
    private Integer cloudOnWayStock;

    @ApiModelProperty("上架库存")
    private Integer upStock;

    @ApiModelProperty("上架日期")
    private Date upTime;
    private Date lastUpTime;

    @ApiModelProperty("下架日期")
    private Date downTime;

    @ApiModelProperty("已售")
    private Integer sold;
    private Integer copySold;

    @ApiModelProperty("库存状态，1预售，2在途，3库房")
    private Integer stockStatus;

    @ApiModelProperty(value = "销售时间")
    private LocalDate saleDate;

    @ApiModelProperty("明日预测")
    private String predictionTomorrow;

    @ApiModelProperty("未来预测")
    private String predictionFuture;

    @ApiModelProperty("包装上的字")
    private String packageWord;

    @ApiModelProperty("包装容器-框，泡沫箱，袋等")
    private String packageType;

    @ApiModelProperty("过季日期")
    private Date outTime;

    @ApiModelProperty("最小甜度")
    private BigDecimal sweetMin;

    @ApiModelProperty("最大甜度")
    private BigDecimal sweetMax;

    @ApiModelProperty("下单倍数")
    private Long placeOrderMultiple;

    @ApiModelProperty("最小购买数量，为-1不限制")
    private Long buyMin;

    @ApiModelProperty("最大购买数量，为-1不限制")
    private Long buyMax;

    @ApiModelProperty("采购员code")
    private String buyerCode;

    @ApiModelProperty("采购员name")
    private String buyerName;

    /**
     * @see CategoryAfterTypeEnum
     */
    @ApiModelProperty("售后规则：0无售后、1正常售后、2库管验货")
    private Integer afterSaleType;
    private String afterSaleName;
    private String afterSaleDesc;

    @ApiModelProperty("售后天数：0无售后，其它暂时写死 1")
    private Integer afterSaleDay;

    @ApiModelProperty("是否免检，0表示不是免检,1表示免检")
    private Integer isCheck;

    @ApiModelProperty("免赔情况")
    private String deductibleSituation;

    @ApiModelProperty("可申请售后说明")
    private String afterSaleExplain;

    @ApiModelProperty("入口隐藏，0隐藏，1不隐藏")
    private Integer entranceHide;

    @ApiModelProperty("瀑布流标签，0爆品，1其他")
    private String waterfall;

    @ApiModelProperty("状态(1待审核,2驳回,3仅审核通过,4上架,5下架,6过季,7隐藏)")
    private Integer status;

    @ApiModelProperty("业务类型，1市采, 10地采, 20基采, 30产地，40配货")
    @DictConvertFiled(dictCode = "orderOrderBusinessType", filedName = "businessTypeName", type = DictConvertTypeEnum.ENUM)
    private Integer businessType;

    @ApiModelProperty("批次类型，1正常，2尾货，3后台")
    @DictConvertFiled(dictCode = "productSupplierSkuBatchType", filedName = "batchTypeName", type = DictConvertTypeEnum.ENUM)
    private Integer batchType;

    @ApiModelProperty("最后驳回原因")
    private String approveLogReason;

    @ApiModelProperty("主图链接地址")
    private String imgUrl;

    @ApiModelProperty("文件集合")
    private List<SupplierSkuFileVo> fileList;

    @ApiModelProperty("文件控制参数")
    private List<SpuCreateControlVO> controlList;

    @ApiModelProperty("禁止下单城市仓集合")
    private List<SupplierSkuDisableCityWhVo> cityWhList;

    @ApiModelProperty("审批日志集合")
    private List<SupplierSkuApproveLogVo> approveLogList;

    @ApiModelProperty("供应商商品条形码，默认供应商商品编码，可以修改")
    private String label;

    @ApiModelProperty("是否不出售")
    private Boolean isNotSale = false;

    @ApiModelProperty("可选等级、规格、包装、产地列表")
    private List<CategoryExpVO> expList;

    @ApiModelProperty("区域编码")
    private String areaCode;

    @ApiModelProperty("0 无 1 榴莲商品")
    private Integer hasDurian;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("批次条形码")
    private String skuLabel;

    @ApiModelProperty("品牌")
    private String brand;

    @ApiModelProperty("产地简称")
    private String shortProducer;

    @ApiModelProperty("是否送货审核 - 0否df 1是")
    private Integer hasDeliveryAudit;

    @ApiModelProperty("通过送货审核 - 0否df 1是")
    private Integer passDeliveryAudit;

    @ApiModelProperty("是否在当前销售日 1-在 0-不在")
    private int onSalesDay;
    @ApiModelProperty("是否参与运费优惠 0否 1是")
    private Integer isFreightDiscount;
    @ApiModelProperty("运费优惠最小件数")
    private Integer freightDiscountMinNum;

    @ApiModelProperty("前端隐藏商户编码和店铺入口")
    private Integer hideMerchant;

    @ApiModelProperty("集采是否隐藏 1-是")
    private Integer isSaleHide;

    @ApiModelProperty("销售类型：1为般果代采，2为商家自营")
    private Integer saleType;

    @ApiModelProperty("客户门店类型ids")
    private String customerStoreTypes;

    @ApiModelProperty("客户门店类型values")
    private String customerStoreTypeValues;

    @ApiModelProperty("行情预测类型")
    private String marketForecastType;

    @ApiModelProperty("行情预测values")
    private String marketForecastTypeValues;
    public void buildSpuStandardsName() {
        spuStandardsName = spuStandards;
        // 支持单规格和多规格
        spuStandards = KeyValueBO.parseSkuStandardsValuesName(spuStandards);
    }

    @ApiModelProperty("sku售后信息")
    private RemoteSkuLossVo remoteSkuLossVo;

    @ApiModelProperty("sku卡片信息")
    private RemoteSkuCardVo remoteSkuCard;

    /**
     * 商品二级分类
     */
    private Long categoryIdLevel2;

    @ApiModelProperty("商品详情显示近7天价格曲线")
    private List<DailyPriceVo> dailyPriceVoList;

    /**
     * 是否需要坑位，0否1是
     */
    @ApiModelProperty("是否需要坑位，0否1是")
    private Integer provideRegionWhIsSaleNum;

    @ApiModelProperty("补贴金额")
    private BigDecimal subsidyAmount;

    @ApiModelProperty("补贴后单价")
    private BigDecimal subsidyPrice;

    /**
     * 限时折扣标识
     */
    private Integer hasLimitDiscount;
    /**
     * 活动数量  剩余数据
     */
    private Integer discountCount;

    @ApiModelProperty("售价，加入购物车时的商品价格")
    private BigDecimal priceFree;

    @ApiModelProperty("供应商可提供发票类型（1-不提供，2-提供普通发票，3-提供普通发票+专用发票）")
    private Integer invoiceType;
}
