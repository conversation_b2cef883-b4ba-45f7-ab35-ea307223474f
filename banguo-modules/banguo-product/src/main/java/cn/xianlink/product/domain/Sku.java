package cn.xianlink.product.domain;

import cn.xianlink.common.api.enums.product.CategoryAfterTypeEnum;
import cn.xianlink.common.tenant.core.TenantEntity;
import cn.xianlink.product.api.domain.vo.RemoteSkuVo;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * 供应商销售批次商品对象 supplier_sku
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sku")
@AutoMapper(target = RemoteSkuVo.class, reverseConvertGenerate = false)
public class Sku extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * sku 编码
     */
    private String skuCode;


    /**
     * 绑定的批次id
     */
    private Long supplierSkuId;

    /**
     * 供应商批次商品唯一编码
     */
    private String code;

    /**
     * 商品品种id
     */
    private Long categoryId;

    /**
     * 商品品种
     */
    private String categoryPathName;

    /**
     * 平台商品id
     */
    private Long spuId;

    /**
     * 平台商品唯一编码
     */
    private String spuCode;

    /**
     * 平台商品名称
     */
    private String spuName;

    /**
     * 平台商品等级，数据字典配置
     */
    private String spuGrade;

    /**
     * 等级说明
     */
    private String spuGradeDesc;

    /**
     * 平台商品规格
     */
    private String spuStandards;


    /**
     * 进出口：0国产 1进口
     */
    private Integer domestic ;


    /**
     * 平台商品毛重(斤)
     */
    private BigDecimal spuGrossWeight;

    /**
     * 平台商品净重(斤)
     */
    private BigDecimal spuNetWeight;

    /**
     * 供应商商品id
     */
    private Long supplierSpuId;

    /**
     * 供应商子单位id
     */
    private Long supplierDeptId;

    /**
     * 供应商商品唯一编码
     */
    private String supplierSpuCode;

    /**
     * 装卸队id
     */
    private Long basPortageTeamId;

    /**
     * 供货总仓id
     */
    private Long provideRegionWhId;

    /**
     * 总仓id
     */
    private Long regionWhId;

    /**
     * 总仓唯一编码
     */
    private String regionWhCode;

    /**
     * 总仓名称
     */
    private String regionWhName;

    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * 供应商唯一编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 产地
     */
    private String producer;

    /**
     * 是否战略合作品，0为否，1为是
     */
    private Integer cooperation;

    /**
     * 是否特价商品，0为否，1为是
     */
    private Integer bargain;
    private BigDecimal bargainRate;

    /**
     * 市场价，行情价
     */
    private BigDecimal marketPrice;

    /**
     * 礼盒
     */
    private Integer giftBox;

    /**
     * 小件，0为否，1为是
     */
    private Integer little;
    /**
     * 好吃
     */
    private Integer goodToEat;

    /**
     * 寿光蔬菜
     */
    private Integer shouguangVegetables;

    /**
     * 简介
     */
    private String snapshot;

    /**
     * 单件价格
     */
    private BigDecimal price;

    /**
     * 上架价格
     */
//    private BigDecimal upPrice;

    /**
     * 销售日期
     */
    private LocalDate saleDate;

    /**
     * 明日预测
     */
    private String predictionTomorrow;

    /**
     * 未来预测
     */
    private String predictionFuture;

    /**
     * 包装上的字
     */
    private String packageWord;

    /**
     * 包装容器-框，泡沫箱，袋等
     */
    private String packageType;

    /**
     * 最小甜度
     */
    private BigDecimal sweetMin;

    /**
     * 最大甜度
     */
    private BigDecimal sweetMax;

    /**
     * 下单倍数
     */
    private Integer placeOrderMultiple;

    /**
     * 最小购买数量，为-1不限制
     */
    private Integer buyMin;

    /**
     * 最大购买数量，为-1不限制
     */
    private Integer buyMax;

    /**
     * 采购员code
     */
    private String buyerCode;

    /**
     * 采购员name
     */
    private String buyerName;

    /**
     * 售后规则：0无售后、1正常售后、2库管验货
     * @see CategoryAfterTypeEnum
     */
    private Integer afterSaleType;

    /**
     * 售后天数：0无售后，其它暂时写死 1
     */
    private Integer afterSaleDay;

    /**
     * 是否免检，0表示不是免检,1表示免检
     */
    private Integer isCheck;

    /**
     * 免赔情况
     */
    private String deductibleSituation;

    /**
     * 可申请售后说明
     */
    private String afterSaleExplain;

    /**
     * 入口隐藏，1隐藏，2不隐藏
     */
    private Integer entranceHide;

    /**
     * 瀑布流标签，来源数据字典
     */
    private String waterfall;

    /**
     * 批次条形码
     */
    private String skuLabel;

    /**
     * 上架日期
     */
    private Date upTime;

    /**
     * 下架日期
     */
    private Date downTime;

    /**
     * 过季日期
     */
    private Date outTime;

    /**
     * 状态(状态,1提交/待审核,2驳回,3仅审核通过,4上架,5下架,6过季,7隐藏)
     */
    private Integer status;

    /**
     * 批次类型，1正常，2尾货，3后台
     */
    private Integer batchType;

    /**
     * 业务类型，1市采, 10地采, 20基采, 30产地
     */
    private Integer businessType;

    /**
     * 换供应商的原始供应商id
     */
    private Long originalSupplierId;

    /**
     * 换供应商的原始供应商批次id
     */
    private Long originalSupplierSkuId;

    /**
     * 删除标志（0代表存在 存入这条数据的主键代表删除）
     */
    @TableLogic(delval = "id")
    private Long delFlag;


    /**
     * 区域编码
     */
    private String areaCode;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 已售-库存表冗余作排序
     */
    private Integer copySold;

    /**
     * 产地简称
     */
    private String shortProducer;


    /**
     * 是否送货审核 - 0否df 1是
     */
    private Integer hasDeliveryAudit;

    /**
     * 通过送货审核 - 0否df 1是
     */
    private Integer passDeliveryAudit;

    /**
     * 商品名称
     */
    private String categoryName;

    /**
     * 检索全称
     */
    private String fullName;

    /**
     * 集采是否隐藏 1-是
     */
    private Integer isSaleHide;

    /**
     * 商品二级分类
     */
    private Long categoryIdLevel2;

    /**
     * 关联skuId
     */
    private Long relationSkuId;

    /**
     * 补贴金额
     */
    private BigDecimal subsidyAmount;

    /**
     * 销售类型：1为般果代采，2为商家自营
     */
    private Integer saleType;

    /**
     * 降价活动优惠价格
     */
    private BigDecimal discountPrice;

    /**
     * 客户门店类型
     */
    private String customerStoreTypes;

    /**
     * 门店预测类型
     */
    private String marketForecastType;
}
