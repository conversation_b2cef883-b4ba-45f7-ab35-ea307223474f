package cn.xianlink.product.mapper;

import cn.hutool.core.collection.CollUtil;
import cn.xianlink.common.mybatis.core.mapper.BaseMapperPlus;
import cn.xianlink.product.domain.SupplierSkuDisableCityWh;
import cn.xianlink.product.domain.dto.SkuDisableCityListDto;
import cn.xianlink.product.domain.supplierSkuDisableCityWh.bo.DelSupplierSkuDisableCityWhBo;
import cn.xianlink.product.domain.supplierSkuDisableCityWh.vo.SupplierSkuDisableCityWhVo;
import cn.xianlink.product.domain.vo.SkuDisableCityListWhVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 供应商销售批次商品禁下单城市仓Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
public interface SupplierSkuDisableCityWhMapper extends BaseMapperPlus<SupplierSkuDisableCityWh, SupplierSkuDisableCityWhVo> {

    /**
     * 变更状态
     * @param Ids
     * @param status
     * @return
     */
    default boolean updateStatusById(List<Long> Ids, Integer status) {
        if(CollUtil.isEmpty(Ids)){
            return false;
        }
        List<SupplierSkuDisableCityWh> updateList = Ids.stream()
                .filter(e -> e != null && e != 0L).map(e -> {
            SupplierSkuDisableCityWh wh = new SupplierSkuDisableCityWh();
            wh.setId(e);
            wh.setStatus(status);
            return wh;
        }).collect(Collectors.toList());

        if(CollUtil.isEmpty(updateList)){
            return false;
        }

        return this.updateBatchById(updateList);
    }

    /**
     * 查询供应商销售批次商品禁下单城市仓列表
     *
     * @param skuId
     * @param statusList
     * @return
     */
    default List<SupplierSkuDisableCityWhVo> queryList(Long skuId, List<Integer> statusList) {
        LambdaQueryWrapper<SupplierSkuDisableCityWh> lqw = Wrappers.lambdaQuery();
        lqw.eq(SupplierSkuDisableCityWh::getSkuId, skuId)
                .in(CollUtil.isNotEmpty(statusList) , SupplierSkuDisableCityWh::getStatus, statusList);
        return this.selectVoList(lqw);
    }


    /**
     * 总仓删除禁用城市仓，仅能删除待审核的
     * @param bo
     */
    void delByBo(@Param("bo") DelSupplierSkuDisableCityWhBo bo);

    List<Long> getSkuIdsByCityWhId(@Param("cityWhId")Long cityWhId, @Param("skuIds")List<Long> skuIds);

    /**
     * 查询禁用sku信息
     * @param dto
     * @return
     */
    List<SkuDisableCityListWhVo> listWithSku(@Param("dto") SkuDisableCityListDto dto);
    Page<SkuDisableCityListWhVo> listWithSku(@Param("dto") SkuDisableCityListDto dto, @Param("page") Page<SkuDisableCityListWhVo> page);
}
