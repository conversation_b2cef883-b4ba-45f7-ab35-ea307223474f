package cn.xianlink.product.controller.platform;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.CharPool;
import cn.hutool.core.util.StrUtil;
import cn.xianlink.basic.api.RemoteCityWhPlaceService;
import cn.xianlink.basic.api.RemoteRegionLogisticsService;
import cn.xianlink.basic.api.RemoteRegionWhService;
import cn.xianlink.basic.api.RemoteSupplierService;
import cn.xianlink.basic.api.domain.vo.RemoteCityWhPlaceVo;
import cn.xianlink.basic.api.domain.vo.RemoteSupplierVo;
import cn.xianlink.common.api.enums.product.SupplierSkuFileTypeEnum;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.validate.QueryGroup;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.marketing.api.RemoteCouponService;
import cn.xianlink.marketing.api.constant.CouponRestrictionTypeEnum;
import cn.xianlink.marketing.api.vo.RemoteCouponRestrictionVO;
import cn.xianlink.marketing.api.vo.RemoteCouponVo;
import cn.xianlink.product.domain.PublicId;
import cn.xianlink.product.domain.supplierSku.bo.QueryCouponSalePageBo;
import cn.xianlink.product.domain.supplierSku.bo.QuerySalePageBo;
import cn.xianlink.product.domain.supplierSku.bo.QuerySkuInfoBo;
import cn.xianlink.product.domain.supplierSku.bo.SupplierSkuSalePageBo;
import cn.xianlink.product.domain.supplierSku.vo.QuerySalePageVo;
import cn.xianlink.product.domain.supplierSku.vo.SupplierSkuInfoVo;
import cn.xianlink.product.domain.supplierSku.vo.SupplierSkuSalePageVo;
import cn.xianlink.product.service.ISupplierSkuService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.ListUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 供应商销售批次商品
 * 前端访问路由地址为:/product/platform/supplierSku
 * <AUTHOR>
 * @date 2024-03-22
 * @folder 采集平台(小程序)/商品/集采平台-供应商销售批次商品
 */
@Tag(name = "集采平台-供应商销售批次商品")
@Validated
@RequiredArgsConstructor
@RestController("platformPSupplierSkuController")
@RequestMapping("/product/platform/supplierSku")
public class PSupplierSkuController extends BaseController {

    private final ISupplierSkuService skuService;
    @DubboReference
    private final RemoteRegionWhService remoteRegionWhService;
    @DubboReference
    private final RemoteRegionLogisticsService remoteRegionLogisticsService;

    @DubboReference
    private final RemoteCityWhPlaceService remoteCityWhPlaceService;

    @DubboReference
    private final RemoteCouponService remoteCouponService;
    private final RemoteSupplierService remoteSupplierService;

    @PostMapping("/queryCouponSalePage")
    @Operation(summary = "优惠券商品列表查询")
    public R<TableDataInfo<QuerySalePageVo>> queryCouponSalePage(@RequestBody QueryCouponSalePageBo queryCouponBo) {
        if (queryCouponBo.getCouponId() == null){
            return R.ok(TableDataInfo.build());
        }
        RemoteCouponVo coupon = remoteCouponService.getCouponById(queryCouponBo.getCouponId());
        if (coupon == null) {
            return R.ok(TableDataInfo.build());
        }
        if (!RemoteCouponVo.matchId(coupon.getApplicableRegionWhIds(), queryCouponBo.getRegionWhId()) ||
                !RemoteCouponVo.matchId(coupon.getApplicableCityWhIds(), queryCouponBo.getCityWhId()) ||
                !RemoteCouponVo.matchId(coupon.getApplicablePlaceIds(), queryCouponBo.getPlaceId())) {
            return R.ok(TableDataInfo.build());
        }
        QuerySalePageBo querySaleBo = BeanUtil.copyProperties(queryCouponBo, QuerySalePageBo.class);
        List<Long> supplierIdList = StrUtil.isEmpty(coupon.getApplicableSupplierIds()) ? Collections.emptyList() :
                StrUtil.split(coupon.getApplicableSupplierIds().trim(), CharPool.COMMA).stream().map(Long::valueOf).collect(Collectors.toList());
        querySaleBo.setSupplierIdList(supplierIdList);
        querySaleBo.setRegionWhIdList(Optional.ofNullable(queryCouponBo.getRegionWhId()).map(List::of).orElseGet(Collections::emptyList));
        List<String> supplierSpuCodeList = coupon.getCouponRestrictions().stream()
                .filter(r -> CouponRestrictionTypeEnum.SUPPLIER_SPU.getCode().equals(r.getRestrictionType()))
                .map(RemoteCouponRestrictionVO::getRestrictionBusinessCode).toList();
        querySaleBo.setSupplierSpuCodeList(supplierSpuCodeList);
        return querySalePage(querySaleBo);
    }

//    @SaCheckPermission("platform:supplierSku:querySalePage")
    @PostMapping("/querySalePage")
    @Operation(summary = "销售批次商品列表查询")
    public R<TableDataInfo<QuerySalePageVo>> querySalePage(@Validated(QueryGroup.class) @RequestBody QuerySalePageBo bo) {
        //没提货点直接不显示
        if (bo.getPlaceId() == null){
            return R.ok(TableDataInfo.build());
        }
        if(bo.getRegionWhIdList() == null || bo.getRegionWhIdList().size() == 0) {
            List<Long> regionWhIdList = remoteRegionWhService.getByCityWhId(bo.getCityWhId());
            if(regionWhIdList == null || regionWhIdList.size() == 0) {
                return R.ok(TableDataInfo.build());
            }
            bo.setRegionWhIdList(regionWhIdList);
        }
        RemoteCityWhPlaceVo remoteCityWhPlaceVo = remoteCityWhPlaceService.queryById(bo.getPlaceId());
        Long placeId = bo.getPlaceId();
        if (Objects.nonNull(remoteCityWhPlaceVo)){
            Long parentPlaceId = remoteCityWhPlaceVo.getParentPlaceId();
            placeId = Objects.nonNull(parentPlaceId) ? parentPlaceId :bo.getPlaceId();
        }
        //根据总仓&&提货点查询物流线及基础吨位是否配置
        List<Long> isConfig = remoteRegionLogisticsService.checkForSalePage(placeId, bo.getRegionWhIdList());
        //有物流线的优先
//        if (bo.getRegionWhIdList() == null && CollectionUtil.isNotEmpty(isConfig)){
//            bo.setRegionWhIdList(isConfig);
//        }

        TableDataInfo<QuerySalePageVo> page = skuService.querySalePage(bo);
        if (CollectionUtil.isNotEmpty(page.getRows())){
            if (CollectionUtil.isEmpty(isConfig)) {
                page.getRows().forEach(e -> e.setHasLogistics(0));
            }else{
                List<Long> regionWhIdList = ListUtils.removeAll(bo.getRegionWhIdList(), isConfig);
                if (CollectionUtil.isNotEmpty(regionWhIdList)){
                    page.getRows().forEach(e -> {
                        if (regionWhIdList.contains(e.getRegionWhId())){
                            e.setHasLogistics(0);
                        }
                    });
                }
            }
        }
        return R.ok(page);
    }


    @PostMapping("/queryOnSaleSkuPage")
    @Operation(summary = "在售商品批次分页查询")
    public R<TableDataInfo<SupplierSkuSalePageVo>> queryOnSaleSkuPage(@RequestBody SupplierSkuSalePageBo bo) {
        return R.ok(skuService.queryOnSaleSkuPage(bo));
    }

//    @SaCheckPermission("platform:supplierSpu:getInfo")
    @PostMapping("/getInfo")
    @Operation(summary = "根据供应商商品销售批次ID获取供应商商品销售批次详细信息")
    public R<SupplierSkuInfoVo> getInfo(@Validated(QueryGroup.class) @RequestBody QuerySkuInfoBo publicId) {
        SupplierSkuInfoVo data = skuService.getInfoV2(publicId);
        if(data != null){
            // 集采平台只显示商品详情图片&视频，包装等视频隐藏
            HashSet<Integer> showFileTypeSet = CollectionUtil.newHashSet(SupplierSkuFileTypeEnum.TYPE1.getCode(), SupplierSkuFileTypeEnum.TYPE2.getCode());
            if(CollectionUtil.isNotEmpty(data.getFileList())) {
                data.setFileList(data.getFileList().stream().filter(e -> showFileTypeSet.contains(e.getType())).toList());
            }
        }
        return R.ok(data);
    }

    @PostMapping("/getSkuInfo/byId")
    @Operation(summary = "获取最新sku详情")
    public R<SupplierSkuInfoVo> getSkuInfo(@RequestBody PublicId publicId) {
        SupplierSkuInfoVo data = skuService.getSkuInfo(publicId);
        if(data != null){
            RemoteSupplierVo supplierVo = remoteSupplierService.getSupplierById(data.getSupplierId());
            data.setInvoiceType(supplierVo.getProvideInvoice());
            // 集采平台只显示商品详情图片&视频，包装等视频隐藏
            HashSet<Integer> showFileTypeSet = CollectionUtil.newHashSet(SupplierSkuFileTypeEnum.TYPE1.getCode(), SupplierSkuFileTypeEnum.TYPE2.getCode());
            if(CollectionUtil.isNotEmpty(data.getFileList())) {
                data.setFileList(data.getFileList().stream().filter(e -> showFileTypeSet.contains(e.getType())).toList());
            }
        }
        return R.ok(data);
    }

    @PostMapping("/queryDiscountPage")
    @Operation(summary = "限时折扣商品列表查询")
    public R<TableDataInfo<QuerySalePageVo>> queryDiscountPage(@RequestBody QuerySalePageBo bo) {
        //没提货点直接不显示
        if (bo.getPlaceId() == null){
            return R.ok(TableDataInfo.build());
        }
        if(bo.getRegionWhIdList() == null || bo.getRegionWhIdList().size() == 0) {
            List<Long> regionWhIdList = remoteRegionWhService.getByCityWhId(bo.getCityWhId());
            if(regionWhIdList == null || regionWhIdList.size() == 0) {
                return R.ok(TableDataInfo.build());
            }
            bo.setRegionWhIdList(regionWhIdList);
        }
        RemoteCityWhPlaceVo remoteCityWhPlaceVo = remoteCityWhPlaceService.queryById(bo.getPlaceId());
        Long placeId = bo.getPlaceId();
        if (Objects.nonNull(remoteCityWhPlaceVo)){
            Long parentPlaceId = remoteCityWhPlaceVo.getParentPlaceId();
            placeId = Objects.nonNull(parentPlaceId) ? parentPlaceId :bo.getPlaceId();
        }
        //根据总仓&&提货点查询物流线及基础吨位是否配置
        List<Long> isConfig = remoteRegionLogisticsService.checkForSalePage(placeId, bo.getRegionWhIdList());

        TableDataInfo<QuerySalePageVo> page = skuService.queryDiscountPage(bo);
        if (CollectionUtil.isNotEmpty(page.getRows())){
            if (CollectionUtil.isEmpty(isConfig)) {
                page.getRows().forEach(e -> e.setHasLogistics(0));
            }else{
                List<Long> regionWhIdList = ListUtils.removeAll(bo.getRegionWhIdList(), isConfig);
                if (CollectionUtil.isNotEmpty(regionWhIdList)){
                    page.getRows().forEach(e -> {
                        if (regionWhIdList.contains(e.getRegionWhId())){
                            e.setHasLogistics(0);
                        }
                    });
                }
            }
        }
        return R.ok(page);
    }

    /**
     * 测试mq延迟消息发送
     * @param id
     * @return
     */
    @GetMapping("/test/sendMsg/{id}")
    public R<String> test(@PathVariable("id") Long id) {
        skuService.sendDelayWxAuditMsg(id);
        return R.ok("ok");
    }
}