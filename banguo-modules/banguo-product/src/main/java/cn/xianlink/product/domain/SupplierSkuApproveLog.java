package cn.xianlink.product.domain;

import cn.xianlink.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 供应商商品销售批次审批日志对象 supplier_sku_approve_log
 *
 * <AUTHOR>
 * @date 2024-04-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("supplier_sku_approve_log")
public class SupplierSkuApproveLog extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 供应商商品销售批次id
     */
    private Long supplierSkuId;

    /**
     * 操作步骤
     */
    private String operate;

    /**
     * 原因（只有驳回才有原因）
     */
    private String reason;

    /**
     * 状态(1提交,2驳回,3审核通过)
     */
    private Integer status;

    /**
     * 删除标志（0代表存在 存入这条数据的主键代表删除）
     */
    @TableLogic(delval = "id")
    private Long delFlag;
}
