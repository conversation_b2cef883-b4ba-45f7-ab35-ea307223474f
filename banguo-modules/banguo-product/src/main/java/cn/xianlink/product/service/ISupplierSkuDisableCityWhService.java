package cn.xianlink.product.service;

import cn.xianlink.basic.api.domain.vo.CityWhVoAppletVo;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.product.api.domain.vo.RemoteSupplierDisableCityWhVo;
import cn.xianlink.product.domain.SupplierSkuDisableCityWh;
import cn.xianlink.product.domain.dto.DisableCityDto;
import cn.xianlink.product.domain.dto.SkuDisableCityListDto;
import cn.xianlink.product.domain.supplierSkuDisableCityWh.bo.AddSupplierSkuDisableCityWhBo;
import cn.xianlink.product.domain.vo.SkuDisableCityListWhVo;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * 供应商销售批次商品禁下单城市仓Service接口
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
public interface ISupplierSkuDisableCityWhService {

    /**
     * 根据商品批次id查询禁用的城市仓id
     *
     * @param skuIds
     * @return
     */
    List<RemoteSupplierDisableCityWhVo> listBySupplierSkuIds(List<Long> skuIds);

    /**
     * 获取城市仓禁售的批次id列表
     *
     * @param cityWhId
     * @return
     */
    List<Long> getSkuIdsByCityWhId(Long cityWhId);

    /**
     * 获取城市仓禁售的批次id列表
     *
     * @param cityWhId
     * @param skuIds
     * @return
     */
    List<Long> getSkuIdsByCityWhId(Long cityWhId, List<Long> skuIds);

    /**
     * sku 禁用城市仓列表
     *
     * @param dto
     * @return
     */
    Page<SkuDisableCityListWhVo> listWithSku(SkuDisableCityListDto dto);


    /**
     * 城市仓禁用sku
     *
     * @param skuId
     * @param status
     * @param cityWhId
     */
    void updateStatus(List<Long> skuId, Integer status, Long cityWhId);


    void updateStatus(Long skuId, Integer status, Long cityWhId);

    /**
     * 操作sku城市仓屏蔽状态 - 无则写入有则变更
     *
     * @param skuId
     * @param cityWhId
     * @param status
     */
    void insertSkuDisableCityRecord(Long skuId, Long cityWhId, Integer status);


    /**
     * 获取总仓能操作的城市仓列表
     *
     * @param regionWhCode
     * @param skuId
     * @return
     */
    List<CityWhVoAppletVo> selectListByRegionWhCode(String regionWhCode, Long skuId);

    /**
     * 获取总仓能操作的城市仓列表（分页）
     *
     * @return
     */
    TableDataInfo<CityWhVoAppletVo> selectListByRegionWhCodeByPage(DisableCityDto dto);

    void insertBySkuIfNotExist(Long skuId, List<AddSupplierSkuDisableCityWhBo> boList);

    void insertListIfNotExist(List<SupplierSkuDisableCityWh> list);

    void statusAutoPass(Long skuId, Long cityWhId);
}
