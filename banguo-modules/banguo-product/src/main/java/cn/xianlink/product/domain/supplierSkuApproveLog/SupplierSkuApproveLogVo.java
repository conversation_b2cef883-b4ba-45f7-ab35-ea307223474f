package cn.xianlink.product.domain.supplierSkuApproveLog;

import cn.xianlink.product.domain.SupplierSkuApproveLog;
import io.github.linpeilie.annotations.AutoMapper;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 供应商商品销售批次审批日志视图对象 supplier_sku_approve_log
 *
 * <AUTHOR>
 * @date 2024-04-07
 */
@Data
@AutoMapper(target = SupplierSkuApproveLog.class)
public class SupplierSkuApproveLogVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("供应商商品销售批次id")
    private Long supplierSkuId;

    @ApiModelProperty("操作步骤")
    private String operate;

    @ApiModelProperty("原因（只有驳回才有原因）")
    private String reason;

    @ApiModelProperty("状态(1提交,2驳回,3审核通过)")
    private Integer status;

    @ApiModelProperty("创建人,当前数据操作人")
    private String createName;

    @ApiModelProperty("创建创建时间")
    private Date createTime;
}
