package cn.xianlink.product.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.common.api.bo.RemoteRegionWhMsgBo;
import cn.xianlink.common.api.bo.RemoteRegionWhTimeCategory;
import cn.xianlink.common.api.enums.product.CategoryLevelEnum;
import cn.xianlink.common.api.enums.product.TransTypeCodeEnum;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.json.utils.JsonUtils;
import cn.xianlink.common.redis.utils.RedisUtils;
import cn.xianlink.order.api.constant.StockTypeEnum;
import cn.xianlink.product.api.constant.SkuStockErrorEnum;
import cn.xianlink.product.api.domain.bo.RemoteUpdateStockBo;
import cn.xianlink.product.api.domain.vo.RemoteSupplierSkuStockVo;
import cn.xianlink.product.constant.ProductRedisNames;
import cn.xianlink.product.domain.SupplierSkuStockLog;
import cn.xianlink.product.domain.supplierSkuStock.bo.UpdateSupplierSkuStockBo;
import cn.xianlink.product.domain.vo.CategoryVO;
import cn.xianlink.product.mapper.SupplierSkuStockLogMapper;
import cn.xianlink.product.mapper.SupplierSkuStockMapper;
import cn.xianlink.product.service.ICategoryService;
import cn.xianlink.product.service.ICwBatchStockInfoService;
import cn.xianlink.product.service.ICwStockDetailService;
import cn.xianlink.product.service.ISupplierSkuService;
import cn.xianlink.product.service.ISupplierSkuStockService;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
@CustomLog
public class SupplierSkuStockServiceImpl implements ISupplierSkuStockService {

    private final SupplierSkuStockMapper supplierSkuStockMapper;

    private final SupplierSkuStockLogMapper supplierSkuStockLogMapper;

    private final ISupplierSkuService supplierSkuService;

    private final ICwStockDetailService cwStockDetailService;

    private final ICwBatchStockInfoService cwBatchStockInfoService;
    private final ICategoryService categoryService;


    /**
     * 通用方法，在事务提交后执行指定的操作
     */
    private void executeAfterTransaction(Runnable task) {
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                task.run();
            }
        });
    }

    /**
     * 批量更新库存
     * @param stockList
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<RemoteUpdateStockBo>  batchUpdateStock(List<RemoteUpdateStockBo> stockList,String type) {
        log.keyword("下单时间11").info(""+System.currentTimeMillis());
        if(stockList == null) {
            return stockList;
        }

        List<UpdateSupplierSkuStockBo> updateSupplierSkuStockBos = BeanUtil.copyToList(stockList, UpdateSupplierSkuStockBo.class);
        // 活动商品跟普通商品分开处理商品库存扣减
        List<UpdateSupplierSkuStockBo> mkSkuList = updateSupplierSkuStockBos.stream().filter(item -> null != item && null != item.getOpType() && BigDecimal.ONE.intValue() == item.getOpType()).toList();
        if (CollectionUtils.isNotEmpty(mkSkuList)) {
            for (UpdateSupplierSkuStockBo skuStockBo : mkSkuList) {
                int ret = supplierSkuStockMapper.updateStockUsedByMkActivity(skuStockBo);
                if (ret <= 0) {
                    log.keyword("SupplierSkuStockServiceImpl.timeDiscountStockUpdate").error("限时折扣活动-先到先得,当前商品活动库存不足，商品已售数量已达活动目标销量");
                    throw new ServiceException(SkuStockErrorEnum.TIME_DISCOUNT_STOCK_NOT_ENOUGH.getMsg(), SkuStockErrorEnum.TIME_DISCOUNT_STOCK_NOT_ENOUGH.getCode());
                }
            }
        }
        List<UpdateSupplierSkuStockBo> normalSkuList = updateSupplierSkuStockBos.stream().filter(item -> null != item && (null == item.getOpType() || BigDecimal.ONE.intValue() != item.getOpType())).toList();
        if (CollectionUtils.isNotEmpty(normalSkuList)) {
            supplierSkuStockMapper.updateStockBatch(normalSkuList);
        }
        Map<Long, UpdateSupplierSkuStockBo> supplierSkuStockBoMap = updateSupplierSkuStockBos.stream().collect(Collectors.toMap(UpdateSupplierSkuStockBo::getSupplierSkuId, UpdateSupplierSkuStockBo -> UpdateSupplierSkuStockBo, (o1, o2) -> o2));
        log.keyword("下单时间12").info(""+System.currentTimeMillis());
        //批量获取商品库存
        List<Long> supplierSkuIds = stockList.stream().map(RemoteUpdateStockBo::getSupplierSkuId).toList();
        List<RemoteSupplierSkuStockVo> supplierSkuStockVos = supplierSkuService.listSupplierSkuStockBySupplierIds(supplierSkuIds);
        supplierSkuStockVos.forEach(stockVo -> {
            if (stockVo.getStock() < 0) {
                log.keyword("SupplierSkuStockServiceImpl.batchUpdateStock").error("库存不足:" + JsonUtils.toJsonString(stockVo));
                throw new ServiceException("库存不足");
            }
        });

        log.keyword("下单时间13").info(""+System.currentTimeMillis());
        //修改商品可售状态
        if(CollectionUtil.isNotEmpty(supplierSkuStockVos) && StockTypeEnum.ORDER.getCode().equals(type)){
            List<Long> supplierSkus = supplierSkuStockVos.stream().filter(item -> item.getStock() <= 0 ).map(RemoteSupplierSkuStockVo::getSupplierSkuId).collect(Collectors.toList());
            supplierSkuService.updateIsOut(supplierSkus, 1);
            //创建订单时，售罄，发送公众号消息给供应商和采购
            supplierSkuService.asyncSendSellOutMessage(supplierSkus);

        }
        log.keyword("下单时间14").info(""+System.currentTimeMillis());
        // 同步销量
        supplierSkuService.asyncSold(supplierSkuIds);
        log.keyword("下单时间15").info(""+System.currentTimeMillis());
        //记录库存操作
        List<SupplierSkuStockLog> skuStockLogList = BeanUtil.copyToList(stockList, SupplierSkuStockLog.class);
        skuStockLogList.forEach(item -> {
            item.setType(type);
        });
        supplierSkuStockLogMapper.insertBatch(skuStockLogList);
        List<String> operateCode = StockTypeEnum.getOperateCode();
        if(operateCode.contains(type)){
             stockList.forEach(item -> {
                if (Objects.equals(type, StockTypeEnum.CANCEL.getCode())) {
                    item.setLockStock(Optional.ofNullable(item.getSold()).orElse(item.getLockStock()) );
                }
            });

            executeAfterTransaction(() -> {
                //云仓增加库存锁定
                cwStockDetailService.syncStockAllocation(stockList, TransTypeCodeEnum.ORDER.getCode());
                log.keyword("下单时间16").info(""+System.currentTimeMillis());
            });
        }
        log.keyword("下单时间17").info(""+System.currentTimeMillis());
        return stockList;
    }

    /**
     * 定时清理库存
     * @param bo
     * @return
     */
    @Override
    @Lock4j(name = ProductRedisNames.CLEAR_STOCK_LOCK, keys = "#bo.id + '_' + #bo.saleDate", expire = 30000, acquireTimeout = 1000)
    public int clearStock(RemoteRegionWhMsgBo bo) {
        if(!bo.isHigh()) {
            String key = ProductRedisNames.CLEAR_STOCK + bo.getId() + bo.getSaleDate();
            String value = RedisUtils.getCacheObject(key);
            if(StringUtils.isNotBlank(value)) {
                return 0;
            }
            RedisUtils.setCacheObject(key, bo.getSaleDate().toString());
            RedisUtils.expire(key, Duration.ofHours(25));
        }
        if (CollUtil.isNotEmpty(bo.getCategoryList())) {
            List<Long> categoryIds = categoryService.getCategoryByIds(bo.getCategoryList().stream().map(RemoteRegionWhTimeCategory::getCategoryId).collect(Collectors.toList()))
                    .stream().filter(f -> f.getLevel().equals(CategoryLevelEnum.LEVELTHREE.getCode())).map(CategoryVO::getId).collect(Collectors.toList());
            bo.setCategoryIds(categoryIds);
        }
        return supplierSkuStockMapper.clearStock(bo);
    }

    /**
     * 订单回滚补偿-回退库存
     * @param orderCode 订单号
     * @param cancelStock 是否回退上架库存
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void compensateStock(String orderCode, boolean cancelStock) {
        LambdaQueryWrapper<SupplierSkuStockLog> qw = new LambdaQueryWrapper<>();
        qw.eq(SupplierSkuStockLog::getOrderCode, orderCode);
        qw.eq(SupplierSkuStockLog::getType, StockTypeEnum.ORDER.getCode());
        List<SupplierSkuStockLog> supplierSkuStockLogList = supplierSkuStockLogMapper.selectList(qw);
        if(ObjectUtil.isEmpty(supplierSkuStockLogList)){
            return;
        }

        //处理库存
        List<RemoteUpdateStockBo> updateStockList = new ArrayList<>();
        for(SupplierSkuStockLog stockLog : supplierSkuStockLogList) {
            RemoteUpdateStockBo updateStockBo = new RemoteUpdateStockBo();
            updateStockBo.setSupplierSkuId(stockLog.getSupplierSkuId());
            updateStockBo.setOrderId(stockLog.getOrderId());
            updateStockBo.setOrderCode(stockLog.getOrderCode());
            updateStockBo.setOrderItemId(stockLog.getOrderItemId());
            updateStockBo.setLockStock(-stockLog.getLockStock());
            if(cancelStock){
                updateStockBo.setBackStock(-stockLog.getBackStock());
            }
            updateStockList.add(updateStockBo);
        }
        this.batchUpdateStock(updateStockList, StockTypeEnum.CANCEL.getCode());
    }
}
