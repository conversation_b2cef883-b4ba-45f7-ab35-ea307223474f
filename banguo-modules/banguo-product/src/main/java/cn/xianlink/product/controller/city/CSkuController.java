package cn.xianlink.product.controller.city;

import cn.xianlink.basic.api.RemoteRegionWhService;
import cn.xianlink.basic.api.domain.vo.RemoteRegionWhVo;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.product.api.domain.bo.RemoteQuerySkuIdBo;
import cn.xianlink.product.domain.PublicId;
import cn.xianlink.product.domain.bo.BasCustomerBo;
import cn.xianlink.product.domain.dto.SkuDisableCityListDto;
import cn.xianlink.product.domain.dto.SkuDisableCityStatusDto;
import cn.xianlink.product.domain.supplierSku.bo.SupplierSkuSimplePageBo;
import cn.xianlink.product.domain.supplierSku.vo.SupplierSkuInfoVo;
import cn.xianlink.product.domain.supplierSku.vo.SupplierSkuVo;
import cn.xianlink.product.domain.vo.SkuDisableCityListWhVo;
import cn.xianlink.product.service.IBuyerGroupService;
import cn.xianlink.product.service.ISkuService;
import cn.xianlink.product.service.ISupplierSkuDisableCityWhService;
import cn.xianlink.system.api.model.LoginUser;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 库存最小管理单位 sku
 * 前端访问路由地址为:/product/region/sku
 *
 * <AUTHOR>
 * @date 2025-01-10
 * @folder 城市仓端(小程序)/商品/总仓助手-sku
 */
@Tag(name = "城市仓端(小程序)--sku 库存最新单位商品")
@Validated
@RequiredArgsConstructor
@RestController("cRSkuController")
@RequestMapping("/product/city/sku")
@CustomLog
public class CSkuController extends BaseController {

    private final ISkuService skuService;

    private final ISupplierSkuDisableCityWhService supplierSkuDisableCityWhService;

    private final IBuyerGroupService buyerGroupService;

    @DubboReference
    private RemoteRegionWhService remoteRegionWhService;


    @PostMapping("queryPage")
    @Operation(summary = "sku分页查询")
    public R<TableDataInfo<SupplierSkuVo>> queryPage(@RequestBody SupplierSkuSimplePageBo bo) {
        return R.ok(skuService.queryPage(bo));
    }


    //    @SaCheckPermission("region:supplierSpu:getInfo")
    @PostMapping("/getInfo")
    @Operation(summary = "sku详情")
    public R<SupplierSkuInfoVo> getInfo(@RequestBody PublicId publicId) {
        return R.ok(skuService.queryById(publicId.getId()));
    }


    @GetMapping("/getByLabel")
    @Operation(summary = "条形码获取sku详情")
    public R<SupplierSkuVo> getInfoByLabel(@RequestParam(value = "label") String label) {
        return R.ok(skuService.getByLabel(new RemoteQuerySkuIdBo().setSkuLabel(label)));
    }

    @PostMapping("/pageSkuDisableCityList")
    @Operation(summary = "城市仓分页查询sku列表 - 进行禁用")
    public R<TableDataInfo<SkuDisableCityListWhVo>> pageSkuDisableCityList(@RequestBody SkuDisableCityListDto dto) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        dto.setCityWhId(loginUser.getRelationId());
        log.keyword("pageSkuDisableCityList").info("城市仓用户id={}, loginUser={}", loginUser.getTenantId(), JSON.toJSONString(loginUser));
        return R.ok(TableDataInfo.build(supplierSkuDisableCityWhService.listWithSku(dto)));
    }


    @PostMapping("/updateCityBanStatusSku")
    @Operation(summary = "城市仓禁用sku")
    public R<Void> updateCityBanStatusSku(@RequestBody @Validated SkuDisableCityStatusDto dto) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        supplierSkuDisableCityWhService.updateStatus(dto.getSkuId(), dto.getStatus(), loginUser.getRelationId());
        supplierSkuDisableCityWhService.updateStatus(dto.getSkuIds(), dto.getStatus(), loginUser.getRelationId());
        return R.ok();
    }

    @PostMapping("/queryRegionListByCityWhId")
    @Operation(summary = "城市仓查询关联总仓列表")
    public R<List<RemoteRegionWhVo>> queryRegionListByCityWhId() {
        LoginUser loginUser = LoginHelper.getLoginUser();
        return R.ok(remoteRegionWhService.queryRegionListByCityWhId(loginUser.getRelationId()));
    }

    /**
     *
     * @param regionWhCode 城市仓编码
     * @param regionWhName 城市仓名称 - 模糊搜索
     * @return
     */
    @GetMapping("/listAllByRegionWhCode")
    @Operation(summary = "查询总仓所有采购列表")
    public R<List<BasCustomerBo>> listAllByRegionWhCode(@RequestParam("regionWhCode") String regionWhCode, @RequestParam("regionWhName") String regionWhName){
        List<BasCustomerBo> data = buyerGroupService.listAllByRegionWhCode(regionWhCode);
        // data过滤掉名称不匹配 %regionWhName%的
        List<BasCustomerBo> filteredData = data.stream()
                .filter(customer -> customer.getName().contains(regionWhName))
                .toList();
        return R.ok(filteredData);
    }

}
