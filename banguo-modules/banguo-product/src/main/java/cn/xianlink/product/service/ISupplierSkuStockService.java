package cn.xianlink.product.service;

import cn.xianlink.common.api.bo.RemoteRegionWhMsgBo;
import cn.xianlink.product.api.domain.bo.RemoteUpdateStockBo;

import java.util.List;

public interface ISupplierSkuStockService {

    /**
     * 批量更新库存
     * @param stockList
     * @return
     */
    List<RemoteUpdateStockBo> batchUpdateStock(List<RemoteUpdateStockBo> stockList,String type);

    /**
     * 定时清理库存
     * @param bo
     * @return
     */
    int clearStock(RemoteRegionWhMsgBo bo);

    /**
     * 订单回滚补偿-回退库存
     * @param orderCode 订单号
     * @param cancelStock 是否回退上架库存
     */
    void compensateStock(String orderCode, boolean cancelStock);
}
