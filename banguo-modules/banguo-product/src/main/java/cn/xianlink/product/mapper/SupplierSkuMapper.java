package cn.xianlink.product.mapper;

import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.common.mybatis.core.mapper.BaseMapperPlus;
import cn.xianlink.product.api.domain.bo.RemoteDiscountSkuSearchBo;
import cn.xianlink.product.api.domain.bo.RemoteQueryInfoListBo;
import cn.xianlink.product.api.domain.bo.RemoteQuerySkuIdBo;
import cn.xianlink.product.api.domain.bo.RemoteQuerySupplierDeliverBo;
import cn.xianlink.product.api.domain.vo.RemoteQuerySupplierDeliverVo;
import cn.xianlink.product.api.domain.vo.RemoteSkuInfoForActivityVo;
import cn.xianlink.product.api.domain.vo.RemoteSupplierSkuInfoVo;
import cn.xianlink.product.api.domain.vo.RemoteSupplierSkuStockVo;
import cn.xianlink.product.domain.SupplierSku;
import cn.xianlink.product.domain.excel.RegionSkuPriceExportVO;
import cn.xianlink.product.domain.supplierSku.bo.*;
import cn.xianlink.product.domain.supplierSku.vo.QueryRefundDifferenceSkuVo;
import cn.xianlink.product.domain.supplierSku.vo.QuerySalePageVo;
import cn.xianlink.product.domain.supplierSku.vo.SupplierSkuSalePageVo;
import cn.xianlink.product.domain.supplierSku.vo.SupplierSkuVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;

/**
 * 供应商销售批次商品Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
public interface SupplierSkuMapper extends BaseMapperPlus<SupplierSku, SupplierSkuVo> {

    /**
     * 根据供应商商品id，以供应商商品id，总仓id分组，查询出这个供应商商品每个总仓最后的供货批次
     * @param supplierSpuId
     * @return
     */
    List<SupplierSkuVo> queryList(@Param("supplierSpuId") Long supplierSpuId);

    /**
     * 查询供应商商品销售批次最后供货批次
     * @param bo
     * @return
     */
    Page<SupplierSkuVo> queryLastList(@Param("bo") QueryLastListBo bo, @Param("page") Page<SupplierSkuVo> page);

    /**
     * 查询供应商商品销售批次的补贴审核页
     * @param bo
     * @return
     */
    Page<SupplierSkuVo> querySubsidyAuditPage(@Param("bo") QueryLastListBo bo, @Param("page") Page<SupplierSkuVo> page);

    /**
     * 可打印的sku
     */
    Set<Long> nearUpSkuIdMap(@Param("skuIdList") List<Long> skuIdList, @Param("nearDay")LocalDate nearDay);

        /**
     * 查询供应商商品销售批次最后供货批次
     * @param bo
     * @return
     */
    List<SupplierSkuVo> queryLastList(@Param("bo") QueryLastListBo bo);

    /**
     * 送货审核批次列表
     * @param bo
     * @param page
     * @return
     */
    Page<SupplierSkuVo> queryDeliveryAuditPage(@Param("bo") SupplierSkuSimplePageBo bo, @Param("page") Page<SupplierSkuVo> page);
    List<SupplierSkuVo> queryDeliveryAuditPage(@Param("bo") SupplierSkuSimplePageBo bo);


    /**
     * 根据供应商商品销售批次ID获取供应商商品销售批次详细信息
     * @param id
     * @return
     */
    SupplierSkuVo queryById(@Param("id") Long id);

    /**
     * 查询供应商商品销售批次列表
     * @param bo
     * @param page
     * @return
     */
    Page<SupplierSkuVo> queryPage(@Param("bo") SupplierSkuPageBo bo, @Param("page") Page<QuerySalePageVo> page);

    /**
     * 集采小程序销售批次列表接口
     * @param bo
     * @return
     */
    Page<QuerySalePageVo> querySalePage(@Param("bo") QuerySalePageBo bo, @Param("page")Page<QuerySalePageBo> page);

    Page<SupplierSkuSalePageVo> queryOnSalePage(@Param("bo") SupplierSkuSalePageBo bo, @Param("page") Page<SupplierSkuSalePageBo> page);

    /**
     * 根据条件查询销售批次详情
     * @param bo
     * @return
     */
    List<RemoteSupplierSkuInfoVo> queryInfoList(@Param("bo") RemoteQueryInfoListBo bo);

    /**
     * 根据code查询销售批次详情
     */
    RemoteSupplierSkuInfoVo getByCode(@Param("code") String code);


    RemoteSupplierSkuInfoVo getBySkuLabel(@Param("bo") RemoteQuerySkuIdBo bo);

    List<RemoteSupplierSkuInfoVo> getBySkuLabelList(@Param("bo") RemoteQuerySkuIdBo bo);

    /**
     * 查询需要下架的供应商商品销售批次
     * @param page
     * @return
     */
    Page<Long> queryUpSupplierSku(@Param("page") Page<Long> page);



    /**
     * 查询需要差额退款的供应商商品销售批次
     * @param bo
     * @return
     */
    List<QueryRefundDifferenceSkuVo> queryRefundDifference(@Param("bo") RefundDifferenceBo bo);

    /**
     * 根据供应商商品code查询供应商批次商品(最近15天)
     */
    List<RemoteSupplierSkuInfoVo> getBySpuCode(@Param("code") String code, @Param("saleDate") LocalDate saleDate);

    /**
     * 根据总仓id查询供应商今日供货商品数量
     * @param bo
     * @return
     */
    List<RemoteQuerySupplierDeliverVo> querySupplierDeliver(@Param("bo") RemoteQuerySupplierDeliverBo bo);

    /**
     * 检验这个供应商今天能不能给总仓供这个货
     * @param bo
     * @return
     */
    SupplierSkuVo checkSupplierSku(@Param("bo") CheckSupplierSkuBo bo);


    List<SupplierSkuVo> getBacthList(@Param("bo") CheckSupplierSkuBo bo);

    int checkSupplierSkuNum(@Param("bo") CheckSupplierSkuBo bo);

    List<Long> getSameSupplierSkuIdList(@Param("bo") CheckSupplierSkuBo bo);

    /**
     * 根据批次唯一性统一隐藏商品
     * @param bo
     * @return
     */
    int updateStatusHideByOnly(@Param("bo") CheckSupplierSkuBo bo);

    /**
     * 根据商品唯一性更新商品标签
     * @param bo
     * @return
     */
    int updateLabelByOnly(@Param("bo") CheckSupplierSkuBo bo);

    /**
     * 获取一条指定状态和子供应商（档口）的商品id
     * @param supplierDeptId
     * @param statusList
     * @return
     */
    Long getFirstIdByDeptAndStatus(@Param("supplierDeptId")Long supplierDeptId, @Param("statusList") List<Integer> statusList);

    /**
     * 根据销售日期和label搜索
     * @param bo
     * @return
     */
    List<RemoteSupplierSkuInfoVo> listBySkuLabel(@Param("bo") RemoteQuerySkuIdBo bo);

    int syncSold(@Param("supplierSkuIds") List<Long> supplierSkuIds);

    /**
     * 根据id查询商品详情 包含库存 给少量用
     */
    List<SupplierSkuVo> querySimpleInfoListHasStock(@Param("ids") List<Long> ids);

    /**
     * 查询当日报价
     * @param regionWhId
     * @param saleDate
     * @param page
     * @return
     */
    Page<RegionSkuPriceExportVO> queryTodayPrice(@Param("regionWhId")Long regionWhId,
                                                 @Param("saleDate")LocalDate saleDate,
                                                 @Param("page")Page<RegionSkuPriceExportVO> page);

    /**
     * 实时获取批次的库存信息（含云仓）
     * @param supplierSkuIds
     * @return
     */
    List<RemoteSupplierSkuStockVo> queryYunStock(@Param("supplierSkuIds") List<Long> supplierSkuIds);


    /**
     * 根据参数skuId,timeStart,timeEnd查询供应商商品
     *
     * @param skuId
     * @param timeStart
     * @param timeEnd
     * @return
     */
    default List<SupplierSkuVo> getSupplierSkuByParam(Long skuId, LocalDate timeStart, LocalDate timeEnd) {
        LambdaQueryWrapper<SupplierSku> lqw = Wrappers.lambdaQuery();
        lqw.eq(SupplierSku::getSkuId, skuId);
        lqw.ge(ObjectUtil.isNotNull(timeStart), SupplierSku::getSaleDate, timeStart);
        lqw.le(ObjectUtil.isNotNull(timeEnd), SupplierSku::getSaleDate, timeEnd);
        return this.selectVoList(lqw);
    }

    Page<QuerySalePageVo> queryDiscountPage(@Param("bo") QuerySalePageBo bo, @Param("page")Page<QuerySalePageBo> page);

    List<RemoteSupplierSkuInfoVo> querySaleLowSku(@Param("bo")RemoteDiscountSkuSearchBo bo,
                                                  @Param("page")Page<RemoteDiscountSkuSearchBo> page);

    List<RemoteSkuInfoForActivityVo> queryActivityInfoList(@Param("skuIds") List<Long> skuIds,
                                                           @Param("saleType") Integer saleType);
}
