package cn.xianlink.product.domain.supplierSku.vo;

import cn.xianlink.common.api.enums.product.CategoryAfterTypeEnum;
import cn.xianlink.common.dict.annotation.DictConvertFiled;
import cn.xianlink.common.dict.enums.DictConvertTypeEnum;
import cn.xianlink.product.domain.supplierSku.bo.KeyValueBO;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class QuerySalePageVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("供应商批次商品唯一编码")
    private String code;
    private String skuCode;

    /**
     * 二级商品分类
     */
    private Long categoryIdLevel2;
    /**
     * 一级分类
     */
    private Long categoryIdLevel1;

    @ApiModelProperty("销量")
    private Integer copySold;

    @ApiModelProperty("三级分类id")
    private Long categoryId;
    @ApiModelProperty(value = "三级code", hidden = true)
    private String categoryCode;

    @ApiModelProperty(value = "三级分类路径", hidden = true)
    private String categoryPathName;

    @ApiModelProperty("三级分类名称")
    private String categoryName;

    @ApiModelProperty("平台商品id")
    private Long spuId;

    @ApiModelProperty("skuid")
    private Long skuId;

    @ApiModelProperty("平台商品唯一编码")
    private String spuCode;

    @ApiModelProperty("平台商品名称")
    private String spuName;

    @ApiModelProperty("商品毛重(斤)")
    private BigDecimal spuGrossWeight;

    @ApiModelProperty("商品净重(斤)")
    private BigDecimal spuNetWeight;

    @ApiModelProperty("平台商品等级，数据字典配置")
    @DictConvertFiled(dictCode = "BasePro_rank", filedName = "spuGradeName", dictRemark = true)
    private String spuGrade;

    @ApiModelProperty("商品规格")
    private String spuStandards;

    @ApiModelProperty("供应商商品ID")
    private Long supplierSpuId;

    @ApiModelProperty("供应商商品唯一编码")
    private String supplierSpuCode;

    @ApiModelProperty("装卸队id")
    private Long basPortageTeamId;

    @ApiModelProperty("供货总仓id")
    private Long provideRegionWhId;

    @ApiModelProperty("供货总仓名称")
    private String provideRegionWhName;

    @ApiModelProperty("总仓id")
    private Long regionWhId;

    @ApiModelProperty("总仓唯一编码")
    private String regionWhCode;

    /**
     * 总仓名称
     */
    private String regionWhName;

    @ApiModelProperty("供应商id")
    private Long supplierId;

    @ApiModelProperty("供应商唯一编码")
    private String supplierCode;

    @ApiModelProperty("供应商名称")
    private String supplierName;
    /**
     * 供应商别名
     */
    private String supplierAlias;

    @ApiModelProperty("供应商短码")
    private String supplierSimpleCode;

    @ApiModelProperty("供应商子单位ID")
    private Long supplierDeptId;

    @ApiModelProperty("子供应商名称")
    private String supplierDeptName;

    @ApiModelProperty("最小购买数量，为-1不限制")
    private Integer buyMin;

    @ApiModelProperty("最大购买数量，为-1不限制")
    private Integer buyMax;

    @ApiModelProperty("商品等级描述")
    private String spuGradeDesc;

    @ApiModelProperty("平台规格列表")
    private List<KeyValueBO> skuStandardsBoList;

    @ApiModelProperty("产地来源：0国产 1进口")
    private Integer domestic ;

    @ApiModelProperty("产地")
    private String producer;

    @ApiModelProperty("开市价")
    private BigDecimal price;

    @ApiModelProperty("净单价")
    private BigDecimal netWeightPrice;

    @ApiModelProperty("库存")
    private Integer stock;

    @ApiModelProperty("加入购物车数量")
    private Integer cartCount = 0;

    @ApiModelProperty("库存状态，1预售，2在途，3库房")
    private Integer stockStatus;

    @ApiModelProperty("销售日")
    private LocalDate saleDate;

    @ApiModelProperty("状态(1待审核,2驳回,3仅审核通过,4上架,5下架,6过季,7隐藏)")
    private Integer status;

    @ApiModelProperty("下单倍数")
    private Integer placeOrderMultiple;

    @ApiModelProperty("业务类型，1市采, 10地采, 20基采, 30产地，40配货")
    @DictConvertFiled(dictCode = "orderOrderBusinessType", filedName = "businessTypeName", type = DictConvertTypeEnum.ENUM)
    private Integer businessType;

    @ApiModelProperty("批次类型，1正常，2尾货，3后台")
    @DictConvertFiled(dictCode = "productSupplierSkuBatchType", filedName = "batchTypeName", type = DictConvertTypeEnum.ENUM)
    private Integer batchType;

    @ApiModelProperty("是否战略合作品，0为否，1为是")
    private Integer cooperation;

    @ApiModelProperty("是否特价商品，0为否，1为是")
    private Integer bargain;
    private BigDecimal bargainRate;
    private BigDecimal bargainDownPrice;

    @ApiModelProperty("市场价")
    private BigDecimal marketPrice;

    @ApiModelProperty("礼盒，0为否，1为是")
    private Integer giftBox;

    @ApiModelProperty("小件，0为否，1为是")
    private Integer little;
    @ApiModelProperty("好吃，0为否，1为是")
    private Integer goodToEat;

    @ApiModelProperty("寿光蔬菜，0为否，1为是")
    private Integer shouguangVegetables;

    /**
     * @see CategoryAfterTypeEnum
     */
    @ApiModelProperty("售后规则：0无售后、1正常售后、2库管验货")
    private Integer afterSaleType;
    private String afterSaleName;
    private String afterSaleDesc;

    @ApiModelProperty("售后天数：0无售后，其它暂时写死 1")
    private Integer afterSaleDay;

    @ApiModelProperty("商品图片地址")
    private String imgUrl;

    @ApiModelProperty("是否不出售")
    private Boolean isNotSale = false;

    @ApiModelProperty("规格储存内容")
    private String spuStandardsName;

    @ApiModelProperty("批次条形码")
    private String skuLabel;


    @ApiModelProperty("区域编码")
    private String areaCode;

    @ApiModelProperty("品牌")
    private String brand;

    @ApiModelProperty("产地简称")
    private String shortProducer;

    @ApiModelProperty("是否送货审核 - 0否df 1是")
    private Integer hasDeliveryAudit;

    @ApiModelProperty("通过送货审核 - 0否df 1是")
    private Integer passDeliveryAudit;

    @ApiModelProperty("是否支持配送 0否 1是")
    private Integer hasLogistics = 1;

    @ApiModelProperty("是否参与运费优惠 0否 1是")
    private Integer isFreightDiscount = 0;

    @ApiModelProperty("运费优惠最小件数")
    private Integer freightDiscountMinNum;

    @ApiModelProperty("前端隐藏商户编码和店铺入口")
    private Integer hideMerchant;

    @ApiModelProperty("补贴金额")
    private BigDecimal subsidyAmount;

    @ApiModelProperty("销售类型：1为般果代采，2为商家自营")
    private Integer saleType;

    /*-----营销--------*/

    /**
     * 限时折扣标识
     */
    private Integer hasLimitDiscount;
    /**
     * 活动数量  剩余数据
     */
    private Integer discountCount;

    @ApiModelProperty("售价，加入购物车时的商品价格")
    private BigDecimal priceFree;

    private Integer sold;

    private Integer lockStock;

    @ApiModelProperty("客户门店类型")
    private String customerStoreTypes;

    public QuerySalePageVo buildSkuStandardsList() {
        if(StringUtils.isNotBlank(spuStandards) && spuStandards.contains("|")) {
            String[] split = spuStandards.split("\\|");
            List<KeyValueBO> list = new ArrayList<>();
            for (String s : split) {
                if(StringUtils.isNotBlank(s) && s.contains(":")){
                    list.add(new KeyValueBO(s.split(":")[0], s.split(":")[1]));
                }
            }
            this.skuStandardsBoList = list;
        }
        return this;
    }

    public void buildSpuStandardsName() {
        spuStandardsName = spuStandards;
        // 支持单规格和多规格
        spuStandards = KeyValueBO.parseSkuStandardsValuesName(spuStandards);
    }
}
