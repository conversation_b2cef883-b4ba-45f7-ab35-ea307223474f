package cn.xianlink.product.domain.supplierSkuDisableCityWh.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class AddSupplierSkuDisableCityWhBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 旧名称 cityWhId 与前端入参的id不符，导致禁用城市仓一直没有城市仓id的值
     */
    @ApiModelProperty("城市仓id")
    private Long id;

    @ApiModelProperty("城市仓唯一编码")
    private String cityWhCode;
}
