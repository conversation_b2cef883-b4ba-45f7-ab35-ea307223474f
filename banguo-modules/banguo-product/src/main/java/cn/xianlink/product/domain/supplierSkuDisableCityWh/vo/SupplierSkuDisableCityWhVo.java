package cn.xianlink.product.domain.supplierSkuDisableCityWh.vo;

import cn.xianlink.product.domain.SupplierSkuDisableCityWh;
import io.github.linpeilie.annotations.AutoMapper;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 供应商销售批次商品禁下单城市仓视图对象 sku_disable_city_wh
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
@Data
@AutoMapper(target = SupplierSkuDisableCityWh.class)
public class SupplierSkuDisableCityWhVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("供应商销售批次商品id")
    private Long supplierSkuId;

    @ApiModelProperty("城市仓id")
    private Long cityWhId;

    @ApiModelProperty("城市仓唯一编码")
    private String cityWhCode;

    private Long skuId;

    @ApiModelProperty("城市仓名称")
    private String cityWhName;

    /**
     * 状态（0-待确定  1-已确定 2-已驳回 3-已驳回和未禁用））
     */
    private Integer status;
}
