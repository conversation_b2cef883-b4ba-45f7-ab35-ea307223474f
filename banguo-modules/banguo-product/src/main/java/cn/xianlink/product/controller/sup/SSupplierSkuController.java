package cn.xianlink.product.controller.sup;

import cn.xianlink.basic.api.RemoteRegionWhService;
import cn.xianlink.basic.api.domain.vo.RemoteRegionWhVo;
import cn.xianlink.common.api.enums.product.SupplierSkuApproveLogOperateEnum;
import cn.xianlink.common.api.enums.product.SupplierSkuBatchTypeEnum;
import cn.xianlink.common.api.enums.product.SupplierSkuSaleTypeEnum;
import cn.xianlink.common.api.enums.product.SupplierSkuStatusEnum;
import cn.xianlink.common.api.util.SaleDateUtil;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.core.validate.AddGroup;
import cn.xianlink.common.core.validate.EditGroup;
import cn.xianlink.common.core.validate.QueryGroup;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.log.annotation.Log;
import cn.xianlink.common.log.enums.BusinessType;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.product.api.domain.bo.RemoteQuerySkuIdsBo;
import cn.xianlink.product.domain.PublicId;
import cn.xianlink.product.domain.supplierSku.bo.*;
import cn.xianlink.product.domain.supplierSku.vo.*;
import cn.xianlink.product.domain.supplierSkuStock.vo.SupplierSkuStockVo;
import cn.xianlink.product.service.ISupplierSkuService;
import cn.xianlink.system.api.model.LoginUser;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

import java.util.Optional;

/**
 * 供应商销售批次商品
 * 前端访问路由地址为:/product/sup/supplierSku
 * <AUTHOR>
 * @date 2024-03-22
 * @folder 供应商端(小程序)/商品/供应商小程序-供应商销售批次商品
 */
@Tag(name = "供应商小程序-供应商销售批次商品")
@Validated
@RequiredArgsConstructor
@RestController("supSSupplierSkuController")
@RequestMapping("/product/sup/supplierSku")
@RefreshScope
public class SSupplierSkuController extends BaseController {

    private static final Logger log = LoggerFactory.getLogger(SSupplierSkuController.class);
    private final ISupplierSkuService skuService;

    private final RemoteRegionWhService remoteRegionWhService;

    @Value("${business-constant.supplierSkuReview:0}")
    private Integer supplierSkuReview;

/*    @GetMapping("/test")
    @Operation(summary = "【临时】测试配置")
    public R<Integer> test(){
        return R.ok(skuService.isNoCheck(3L, "/干果/枣/蜜枣"));
    }
    @GetMapping("/test2")
    @Operation(summary = "【临时】测试配置2")
    public R<Void> test2(){
        List<SaleCategoryVo> list = new ArrayList<>();
        list.add(new SaleCategoryVo().setCategoryName("3干果"));
        list.add(new SaleCategoryVo().setCategoryName("蔬菜"));
        list.add(new SaleCategoryVo().setCategoryName("1水果"));
        list.add(new SaleCategoryVo().setCategoryName("甘蔗"));
        list.add(new SaleCategoryVo().setCategoryName("松茸"));

        System.out.println(JSON.toJSONString(list));
        System.out.println(JSON.toJSONString(skuService.sortName(list, 0)));
        System.out.println(JSON.toJSONString(skuService.sortName(list, 1)));
        System.out.println(JSON.toJSONString(skuService.sortName(list, 2)));
        return R.ok();
    }*/

//    @SaCheckPermission("sup:supplierSku:querySupSubscribePage")
    @PostMapping("/querySupSubscribePage")
    @Operation(summary = "供货申请列表")
    @Deprecated
    public R<TableDataInfo<QuerySupSubscribePageVo>> querySupSubscribePage(@Validated @RequestBody QuerySupSubscribePageBo bo) {
        return R.ok(skuService.querySupSubscribePage(bo));
    }

//    @SaCheckPermission("sup:supplierSku:queryBatchPage")
    @PostMapping("/queryBatchPage")
    @Operation(summary = "供货批次列表")
    @Deprecated
    public R<TableDataInfo<QueryBatchPageVo>>  queryBatchPage(@Validated @RequestBody QueryBatchPageBo bo) {
        return R.ok(skuService.queryBatchPage(bo));
    }

    // 直接把上面接口二合一，快捷供货进行拆分
    @PostMapping("/queryPage")
    @Operation(summary = "供应商商品查询")
    public R<TableDataInfo<SupplierSkuVo>>  queryPage(@Validated @RequestBody SupplierSkuSimplePageBo bo) {
        bo.setByLoginInfo(true);
        return R.ok(skuService.queryPage(bo));
    }

    @PostMapping("/queryPage/quickUp")
    @Operation(summary = "供应商商品查询-快捷供货")
    public R<TableDataInfo<SupplierSkuVo>>  queryPageByQuickUp(@Validated @RequestBody SupplierSkuSimplePageBo bo) {
        bo.setByLoginInfo(true);
        return R.ok(skuService.queryPageByQuickUp(bo));
    }

//    @SaCheckPermission("sup:supplierSku:getInfo")
    @PostMapping("/getInfo")
    @Operation(summary = "获取供应商销售批次商品详细信息")
    public R<SupplierSkuInfoVo> getInfo(@RequestBody PublicId publicId) {
        return R.ok(skuService.queryById(publicId.getId()));
    }

//    @SaCheckPermission("sup:supplierSku:add")
    @Log(title = "供应商小程序-供应商销售批次商品", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/add")
    @Operation(summary = "市采上架/我要供货")
    public R<Long> add(@Validated(AddGroup.class) @RequestBody AddSupplierSkuBo bo) {
        LoginUser user = LoginHelper.getLoginUser();
        if(user == null) {
            throw new ServiceException("用户不存在");
        }
        Long supplierId = user.getRelationId();
        if(supplierId == null) {
            throw new ServiceException("登录用户异常");
        }
        bo.setStatus(SupplierSkuStatusEnum.STATUS1.getCode());
        // 供应商无需审核直接上架
        if(Objects.equals(supplierSkuReview, 1) || SupplierSkuSaleTypeEnum.SALE_TYPE2.getCode().equals(bo.getSaleType())){
            bo.setStatus(SupplierSkuStatusEnum.STATUS4.getCode());
        }
        bo.setSupplierId(supplierId);
        bo.setSupplierDeptId(Optional.ofNullable(bo.getSupplierDeptId()).orElseGet(()->0L));
        bo.setOperate(SupplierSkuApproveLogOperateEnum.SUPPLIER.getDesc() + user.getRealName());
        bo.setSubsidyAmount(null);
        skuService.checkProvide(bo.getBusinessType(), bo.getProvideRegionWhId());

        if(Objects.nonNull(bo.getHasDeliveryAudit())){
            throw new ServiceException("供应商不支持编辑送货审核");
        }
        bo.setIsSupplier(1);
        return R.ok(skuService.insertByBo(bo));
    }

    @Log(title = "供应商小程序-供应商销售批次商品", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/quickUp")
    @Operation(summary = "快捷供货")
    public R<Void> quickUp(@Validated(EditGroup.class) @RequestBody QuickUpBo bo) {
        LoginUser user = LoginHelper.getLoginUser();
        if(user == null) {
            throw new ServiceException("用户不存在");
        }
        String operate = SupplierSkuApproveLogOperateEnum.SUPPLIER.getDesc() + user.getRealName()+ "," + SupplierSkuBatchTypeEnum.BATCH_TYPE111.getDesc();
        SupplierSkuVo skuVo = skuService.selectById(bo.getId());
        if(skuVo == null) {
            throw new ServiceException("供应商商品销售批次不存在");
        }
        if(bo.getStock() != null) {
            skuVo.setStock(bo.getStock());
        }else {
            skuVo.setStock(null);
        }
        if(bo.getPrice() != null) {
            skuVo.setPrice(bo.getPrice());
        }
        return toAjax(skuService.quickUp(skuVo, true, operate, 0));
    }

//    @SaCheckPermission("sup:supplierSku:edit")
    @Log(title = "供应商小程序-供应商销售批次商品", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/edit")
    @Operation(summary = "修改供应商销售批次商品")
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody UpdateSupplierSkuBo bo) {
        bo.setOperate(SupplierSkuApproveLogOperateEnum.SUPPLIER.getDesc() + LoginHelper.getLoginUser().getRealName());
        SupplierSkuVo skuVo = skuService.selectById(bo.getId());
        // 驳回状态重新上架判断
        if(Objects.equals(bo.getOldStatus(), SupplierSkuStatusEnum.STATUS2.getCode())
        && Objects.equals(skuVo.getStatus(), SupplierSkuStatusEnum.STATUS4.getCode())){
            throw new ServiceException("商品已审核通过，不允许再次编辑");
        }
        if(bo.getOldStatus() != null && !Objects.equals(bo.getOldStatus(), skuVo.getStatus())){
            throw new ServiceException("商品已更新，请刷新后再编辑");
        }
        bo.setSup(true);
        if(Objects.nonNull(bo.getHasDeliveryAudit())){
            throw new ServiceException("供应商不支持编辑送货审核");
        }
        // 供应商只能增加库存，不能直接改
        if(Objects.equals(skuVo.getStatus(), SupplierSkuStatusEnum.STATUS4.getCode())) {
            bo.setStock(null);
        }
        //供应商不能修改补贴
        bo.setSubsidyAmount(null);
        return toAjax(skuService.updateByBo(bo, skuVo));
    }

    private LocalDate getSaleDate(Long regionWhId) {
        RemoteRegionWhVo whVo = remoteRegionWhService.queryById(regionWhId);
        if(whVo == null) {
            throw new ServiceException("总仓不存在");
        }
        if(whVo.getStatus() == 0) {
            throw new ServiceException("总仓已被禁用");
        }
        return SaleDateUtil.getSaleDate(whVo.getSalesTimeEnd());
    }
    @Log(title = "供应商小程序-修改批次条码", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/changeLabel")
    @Operation(summary = "修改批次条码")
    public R<Void> changeLabel(@Validated(EditGroup.class) @RequestBody UpdateSupplierSkuBo bo) {
        if(bo.getId() == null){
            throw new ServiceException("批次号不能为空");
        }
        if(StringUtils.isBlank(bo.getSkuLabel())){
            throw new ServiceException("条形码不能为空");
        }
        skuService.changeLabel(bo);
        return R.ok();
    }

//    @SaCheckPermission("region:supplierSpu:getBackInfo")
    @PostMapping("/getBackInfo")
    @Operation(summary = "根据平台商品ID查询供应商供货的回填信息")
    public R<GetBackInfoVo> getBackInfo(@Validated @RequestBody GetBackInfoBo bo) {
        return R.ok(skuService.getBackInfo(bo));
    }

    @PostMapping("/getBackInfoBySkuId")
    @Operation(summary = "根据供应商商品销售批次ID查询供应商供货的回填信息")
    public R<GetBackInfoVo> getBackInfoBySkuId(@Validated(QueryGroup.class) @RequestBody PublicId id) {
        return R.ok(skuService.getBackInfoBySkuId(id.getId()));
    }

    //    @SaCheckPermission("region:supplierSpu:getBackInfo")
    @PostMapping("/getCount")
    @Operation(summary = "查询供应商供货申请数量")
    public R<Long> getCount(@RequestBody GetCountBo bo) {
        return R.ok(skuService.getCount(bo));
    }

    @PostMapping("/createTestSku")
    @Operation(summary = "根据条件批量新增批次")
    public void createTestSku(@RequestBody CreateTestSkuBo bo) {
        skuService.createTestSku(bo);
    }

    @RepeatSubmit()
    @PostMapping("/changeStatus")
    @Operation(summary = "修改状态")
    public R<Void> changeStatus(@Validated(EditGroup.class) @RequestBody ChangeStatusBo bo) {
        bo.setSupplier(true);
        bo.setSubsidyAmount(null);
        return toAjax(skuService.changeStatus(bo));
    }

    @PostMapping("/getStock")
    @Operation(summary = "获取库存信息")
    public R<SupplierSkuStockVo> getStock(@Validated(QueryGroup.class) @RequestBody PublicId id) {
        return R.ok(skuService.getSupplierSkuStock(id.getId()));
    }



    @PostMapping("/querySupplierSkuIdGroup")
    @Operation(summary = "获取skuId唯一性相同的所有批次")
    public R<List<Long>> querySupplierSkuIdGroup(@Validated(QueryGroup.class) @RequestBody RemoteQuerySkuIdsBo bo) {
        return R.ok(skuService.querySupplierSkuIdGroup(bo));
    }

    @PostMapping("/queryHistoryPage")
    @Operation(summary = "供应商多货选择供应商商品销售批次列表接口")
    public R<TableDataInfo<QueryHistoryPageVo>> queryHistoryPage(@RequestBody QueryHistoryPageBo bo) {
        bo.setSupplierId(LoginHelper.getLoginUser().getRelationId());
        return R.ok(skuService.queryHistoryPage(bo));
    }

    @GetMapping("/querySkuInfoSingle")
    @Operation(summary = "供应商多货选择供应商商品销售批次列表接口(单个商品)")
    public R<QueryHistoryPageVo> querySkuInfoSingle(@RequestParam("supplierSpuCode") String supplierSpuCode, @RequestParam("regionWhId") Long regionWhId) {
        QueryHistoryPageBo bo = new QueryHistoryPageBo();
        bo.setSupplierId(LoginHelper.getLoginUser().getRelationId());
        bo.setSupplierSpuCode(supplierSpuCode);
        bo.setRegionWhId(regionWhId);
        return R.ok(skuService.querySkuInfoSingle(bo));
    }

    /**
     * 坑位校验
     */
    @GetMapping("/checkSkuMaxSaleNum")
    @ApiOperation(value = "商品供货时校验坑位")
    public R<CheckSkuMaxSaleNumResVo> checkSkuMaxSaleNum(@RequestParam("regionWhId") Long regionWhId, @RequestParam("supplierId") Long supplierId){
        return R.ok(skuService.checkSkuMaxSaleNum(regionWhId,supplierId));
    }

    /**
     * 获取最大坑位和剩余坑位数
     */
    @GetMapping("/getSaleNum")
    @ApiOperation(value = "获取最大坑位和剩余坑位数")
    public R<SaleNumVo> getSaleNum(@RequestParam("regionWhId") Long regionWhId, @RequestParam("supplierId") Long supplierId){
        return R.ok(skuService.getSaleNum(regionWhId,supplierId));
    }

}
