package cn.xianlink.product.service.impl;

import cn.dev33.satoken.context.SaHolder;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.xianlink.basic.api.*;
import cn.xianlink.basic.api.domain.bo.RemotePortageTeamQueryBo;
import cn.xianlink.basic.api.domain.vo.*;
import cn.xianlink.common.api.bo.RemoteRegionWhMsgBo;
import cn.xianlink.common.api.enums.product.*;
import cn.xianlink.common.api.util.SaleDateUtil;
import cn.xianlink.common.core.domain.vo.CodeNameVo;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.redis.utils.RedisUtils;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.order.api.RemoteCartItemService;
import cn.xianlink.order.api.RemoteOrderService;
import cn.xianlink.order.api.vo.RemoteCartItemCountVO;
import cn.xianlink.product.api.domain.bo.*;
import cn.xianlink.product.api.domain.vo.RemoteSkuVo;
import cn.xianlink.product.api.domain.vo.RemoteSupplierSkuInfoVo;
import cn.xianlink.product.api.util.TemCode;
import cn.xianlink.product.config.CosProperties;
import cn.xianlink.product.constant.ProductRedisNames;
import cn.xianlink.product.domain.*;
import cn.xianlink.product.api.domain.bo.SupplierSkuSaleNumBo;
import cn.xianlink.product.domain.bo.UpdateSkuPriceBo;
import cn.xianlink.product.domain.supplierSku.bo.*;
import cn.xianlink.product.domain.supplierSku.vo.*;
import cn.xianlink.product.domain.supplierSkuApproveLog.SupplierSkuApproveLogVo;
import cn.xianlink.product.domain.supplierSkuDisableCityWh.vo.SupplierSkuDisableCityWhVo;
import cn.xianlink.product.domain.supplierSkuFile.bo.QuerySupplierSkuFileBo;
import cn.xianlink.product.domain.supplierSkuFile.vo.SupplierSkuFileVo;
import cn.xianlink.product.domain.supplierSkuStock.vo.SupplierSkuStockVo;
import cn.xianlink.product.domain.supplierSpu.vo.SupplierSpuVo;
import cn.xianlink.product.domain.vo.CategoryVO;
import cn.xianlink.product.domain.vo.CwStockDetailVo;
import cn.xianlink.product.domain.vo.SpuCreateControlVO;
import cn.xianlink.product.domain.vo.SpuVO;
import cn.xianlink.product.mapper.*;
import cn.xianlink.product.service.ICwStockDetailService;
import cn.xianlink.product.service.ISkuSalesStatisticsService;
import cn.xianlink.product.service.ISkuService;
import cn.xianlink.product.service.ISpuService;
import cn.xianlink.system.api.RemoteDeptService;
import cn.xianlink.system.api.domain.vo.RemoteDeptDetailVo;
import cn.xianlink.system.api.model.LoginUser;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.redisson.api.RAtomicLong;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 供应商销售批次商品Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
@RequiredArgsConstructor
@Service
@CustomLog
@RefreshScope
public class SkuServiceImpl extends ServiceImpl<SkuMapper, Sku> implements ISkuService {

    private final SkuMapper skuMapper;

    private final SupplierSkuMapper supplierSkuMapper;

    private final SpuMapper spuMapper;

    private final SupplierSpuMapper supplierSpuMapper;

    private final SupplierSkuFileMapper supplierSkuFileMapper;

    private final SupplierSkuDisableCityWhMapper supplierSkuDisableCityWhMapper;

    private final SupplierSkuApproveLogMapper supplierSkuApproveLogMapper;

    private final SpuCreateControlMapper spuCreateControlMapper;

    private final SupplierSkuStockMapper supplierSkuStockMapper;

    private final ISpuService spuService;

    private final ICwStockDetailService cwStockDetailService;

    private final CategoryMapper categoryMapper;

    private final ISkuSalesStatisticsService skuSalesStatisticsService;

    @DubboReference
    private final RemoteRegionWhService remoteRegionWhService;

    @DubboReference
    private final RemoteSupplierService remoteSupplierService;

    @DubboReference
    private final RemoteCartItemService remoteCartItemService;

    @DubboReference
    private final RemoteOrderService remoteOrderService;

    @DubboReference
    private final RemotePortageTeamService remotePortageTeamService;
    @DubboReference
    private RemoteDeptService remoteDeptService;

    @DubboReference
    private final RemoteBasPubAreaService remoteBasPubAreaService;

    @DubboReference
    private final RemoteCityWhService remoteCityWhService;


    @Autowired
    private CosProperties cosProperties;

    /**
     * 天津总仓，不返回采购员
     */
    @Value("#{'${business-constant.regionWhIdList:}'.split(',')}")
    private List<Long> regionWhIdList;

    /**
     * 嘉兴总仓免检
     */
    @Value("#{'${business-constant.noCheckRegionWhIdList:}'.split(',')}")
    private List<Long> noCheckRegionWhIdList;

    /**
     * 耗材免检
     */
    @Value("#{'${business-constant.productTypeNameList:}'.split(',')}")
    private List<String> productTypeNameList;


    /**
     * 根据批次id查询批次详情
     *
     * @param id
     * @return
     */
    @Override
    public SupplierSkuVo selectById(Long id) {
        SupplierSkuVo supplierSkuVo = supplierSkuMapper.selectVoById(id);
        if (Objects.nonNull(supplierSkuVo)) {
            // 规格
            supplierSkuVo.buildSkuStandardsList();
            // 库存
            SupplierSkuStockVo supplierSkuStock = this.getSupplierSkuStock(id);
            if (Objects.nonNull(supplierSkuStock)) {
                supplierSkuVo.setStock(supplierSkuStock.getStock());
            }
        }
        return supplierSkuVo;
    }

    /**
     * 根据供应商商品销售批次ID获取供应商商品销售批次详细信息
     */
    @Override
    public SupplierSkuInfoVo queryById(Long id) {
        Integer isManage = SaHolder.getStorage().get("isManage", 0);

        SupplierSkuVo skuVo = skuMapper.queryById(id);
        if (skuVo == null) {
            throw new ServiceException("供应商商品销售批次不存在");
        }
        // 查询其它关联批次的信息
        id = skuVo.getSupplierSkuId();
        // 写入档口信息 - 名称，简码
        fillDeptSupplierInfo(skuVo);
        // 规格转换
        skuVo.tranSpuStandardsName().buildSkuStandardsList();
        SupplierSkuInfoVo infoVo = MapstructUtils.convert(skuVo, SupplierSkuInfoVo.class);
        RemoteSupplierVo supplierVo = remoteSupplierService.getSupplierById(infoVo.getSupplierId());
        if (supplierVo == null) {
            throw new ServiceException("供应商不存在");
        }
        if (ObjectUtil.isNotEmpty(infoVo.getProvideRegionWhId()) && ObjectUtil.notEqual(infoVo.getProvideRegionWhId(), 0L)) {
            RemoteRegionWhVo regionWhVo = remoteRegionWhService.queryById(infoVo.getProvideRegionWhId());
            infoVo.setProvideRegionWhName(ObjectUtil.isNotEmpty(regionWhVo) ? regionWhVo.getRegionWhName() : null);
        }
        if (ObjectUtil.isNotEmpty(infoVo.getBasPortageTeamId())) {
            RemotePortageTeamQueryBo queryBo = new RemotePortageTeamQueryBo();
            queryBo.setTeamId(infoVo.getBasPortageTeamId());
            List<RemotePortageTeamVo> remotePortageTeamVos = remotePortageTeamService.queryList(queryBo);
            if (ObjectUtil.isNotEmpty(remotePortageTeamVos)) {
                RemotePortageTeamVo vo = remotePortageTeamVos.get(0);
                infoVo.setBasPortageTeamName(vo.getTeamName());
            }
        }

        infoVo.setSpuStandardsName(infoVo.getSpuStandards());
        infoVo.buildSpuStandardsName();
        infoVo.setSupplierSimpleCode(supplierVo.getSimpleCode());
        infoVo.setSupplierAlias(supplierVo.getAlias());
        // 档口编码覆盖供应商简码
        if (StringUtils.isNotBlank(skuVo.getSupplierDeptCode())) {
            infoVo.setSupplierSimpleCode(skuVo.getSupplierDeptCode());
        }
        // 返回前端可选信息（等级、包装、规格等）
        SpuVO spuVO = spuService.getById(skuVo.getSpuId());
        if (spuVO == null) {
            throw new ServiceException("平台商品不存在");
        }
        infoVo.setExpList(spuVO.getExpList());
        BigDecimal netWeightPrice = infoVo.getPrice().divide(infoVo.getSpuNetWeight(), 2, RoundingMode.HALF_UP);
        if (netWeightPrice.compareTo(BigDecimal.ZERO) == 0) {
            netWeightPrice = new BigDecimal("0.01");
        }
        infoVo.setNetWeightPrice(netWeightPrice);
        //获取库存信息
        SupplierSkuStockVo supplierSkuStock = this.getSupplierSkuStock(id);
        if (Objects.nonNull(supplierSkuStock)) {
            infoVo.setStock(supplierSkuStock.getStock());
        }
        //根据最后供货的供应商商品批次id查询禁止下单城市仓信息
        List<Integer> statusList = isManage == 1 ? List.of(0, 1) : List.of(1);
        // 禁用城市仓列表
        List<SupplierSkuDisableCityWhVo> cityWhVos = supplierSkuDisableCityWhMapper.queryList(skuVo.getSkuId(), statusList);
        // 获取城市仓id集合
        List<Long> ids = cityWhVos.stream().map(SupplierSkuDisableCityWhVo::getCityWhId).toList();
        // 获取城市仓信息
        List<RemoteCityWhVo> remoteCityWhVos = remoteCityWhService.queryList(ids);
        // 填充城市仓名字
        cityWhVos.forEach(cityWhVo -> {
            cityWhVo.setCityWhName(remoteCityWhVos.stream().filter(city -> city.getId().equals(cityWhVo.getCityWhId())).findFirst().get().getName());
        });
        infoVo.setCityWhList(cityWhVos);

        //根据供应商商品id查询文件
        QuerySupplierSkuFileBo queryFileBo = new QuerySupplierSkuFileBo();
        queryFileBo.setSupplierSkuIdList(Lists.newArrayList(id));
        List<SupplierSkuFileVo> fileList = supplierSkuFileMapper.queryList(queryFileBo);
        if (fileList != null && fileList.size() > 0) {
            for (SupplierSkuFileVo file : fileList) {
                if (SupplierSkuFileTypeEnum.TYPE1.getCode().equals(file.getType())) {
                    infoVo.setImgUrl(file.getFileUrl());
                    break;
                }
            }
        }
        infoVo.setFileList(fileList);
        List<SpuCreateControlVO> spuCreateControlVOList = spuCreateControlMapper.queryList(skuVo.getSpuId());
        if (CollectionUtil.isNotEmpty(spuCreateControlVOList)) {
            RemoteRegionWhVo regionWhVo = remoteRegionWhService.queryById(skuVo.getRegionWhId());
            if (regionWhVo == null) {
                throw new ServiceException("总仓不存在");
            }
            SpuCreateControlVO spuCreateControlVO = spuCreateControlVOList.get(0);
            if (regionWhVo.getQualityReport() > 0 && spuCreateControlVO.getQualityReport() > 0) {
                spuCreateControlVO.setQualityReport(1);
            } else {
                spuCreateControlVO.setQualityReport(0);
            }
            infoVo.setControlList(spuCreateControlVOList);
        }

        //查询供应商商品销售批次审批列表
        List<SupplierSkuApproveLogVo> logVoList = supplierSkuApproveLogMapper.queryList(id);
        if (logVoList != null && logVoList.size() > 0) {
            String rerson = null;
            for (SupplierSkuApproveLogVo logVo : logVoList) {
                if (logVo.getStatus().equals(SupplierSkuStatusEnum.STATUS2.getCode()) && logVo.getReason() != null) {
                    rerson = logVo.getReason();
                }
            }
            infoVo.setApproveLogReason(rerson);
        }

        LoginUser user = LoginHelper.getLoginUser();
        if (user != null) {
            List<RemoteCartItemCountVO> cartList = remoteCartItemService.getCartItemListInfo(user.getUserId(), Lists.newArrayList(infoVo.getId()));
            if (cartList != null && cartList.size() > 0) {
                infoVo.setCartCount(cartList.get(0).getCount());
            }
        }
        infoVo.setApproveLogList(logVoList);
        infoVo.setLabel(TemCode.getTemCode(infoVo.getCode()));
        if (StringUtils.isBlank(infoVo.getSkuLabel())) {
            infoVo.setSkuLabel(infoVo.getLabel());
        }

        // 是否为榴莲商品
        infoVo.setHasDurian(0);
        if (infoVo.getCategoryId() != null) {
            Category category = categoryMapper.selectById(infoVo.getCategoryId());
            if (category != null && StringUtils.isNotBlank(category.getPathName()) && category.getPathName().contains("榴莲")) {
                String[] split = category.getPathName().split("/");
                if ((split.length > 1 && split[1].contains("榴莲")
                        || (split.length > 2 && split[2].contains("榴莲")))) {
                    infoVo.setHasDurian(1);
                }
            }
        }

        // 处理售后规则
        CategoryAfterTypeEnum afterTypeEnum = CategoryAfterTypeEnum.loadByCode(infoVo.getAfterSaleType());
        if (afterTypeEnum != null) {
            infoVo.setAfterSaleName(afterTypeEnum.getName());
            infoVo.setAfterSaleDesc(afterTypeEnum.getDesc());
        }

        // 区域简称
        if (StringUtils.isNotBlank(infoVo.getAreaCode()) && StringUtils.isBlank(infoVo.getShortProducer())) {
            RemotePubAreaVO remotePubAreaVO = remoteBasPubAreaService.queryByCode(infoVo.getAreaCode());
            if (remotePubAreaVO != null) {
                infoVo.setShortProducer(remotePubAreaVO.getAreaAlias());
            }
        }

        // 云仓库存
        CwStockDetailVo stockDetailVo = cwStockDetailService.getBySkuId(infoVo.getId());
        Integer stock = Optional.ofNullable(stockDetailVo).map(CwStockDetailVo::getOnhandQty).map(BigDecimal::intValue).orElseGet(() -> null);
        Integer lockStock = Optional.ofNullable(stockDetailVo).map(CwStockDetailVo::getAllocationQty).map(BigDecimal::intValue).orElseGet(() -> 0);
        infoVo.setCloudActualStock(stock);
        infoVo.setCloudLockStock(lockStock);
        if (stock != null) {
            infoVo.setCloudStock(stock - lockStock);
        }

        return infoVo;
    }

    /**
     * 查询供应商销售批次商品列表
     */
    @Override
    public TableDataInfo<SupplierSkuVo> queryPageList(SupplierSkuPageBo bo) {
        Page<SupplierSkuVo> result = supplierSkuMapper.queryPage(bo, bo.build());
        if (result.getRecords() != null && result.getRecords().size() > 0) {
            //根据供应商商品销售批次id集合和类型查询供应商商品销售批次文件信息(查询商品展示图片)
            List<Long> supplierSkuIdList = result.getRecords().stream().map(SupplierSkuVo::getId).toList();
            QuerySupplierSkuFileBo fileBo = new QuerySupplierSkuFileBo();
            fileBo.setSupplierSkuIdList(supplierSkuIdList);
            List<SupplierSkuFileVo> fileVoList = supplierSkuFileMapper.queryList(fileBo);
            //处理数据
            Map<Long, List<SupplierSkuFileVo>> fileMap = fileVoList.stream().collect(Collectors.groupingBy(SupplierSkuFileVo::getSupplierSkuId));
            //处理出参
            for (SupplierSkuVo skuVo : result.getRecords()) {
                skuVo.tranSpuStandardsName().buildSkuStandardsList();
                List<SupplierSkuFileVo> fileList = fileMap.get(skuVo.getId());
                skuVo.setFileList(fileList);
                if (fileList != null && fileList.size() > 0) {
                    for (SupplierSkuFileVo file : fileList) {
                        if (SupplierSkuFileTypeEnum.TYPE1.getCode().equals(file.getType())) {
                            skuVo.setImgUrl(file.getFileUrl());
                            break;
                        }
                    }
                }
                // 规格转换 - db text jsonString to List
                skuVo.tranSpuStandardsName().buildSkuStandardsList();
                // 区域简称
                if (StringUtils.isNotBlank(skuVo.getAreaCode()) && StringUtils.isBlank(skuVo.getShortProducer())) {
                    RemotePubAreaVO remotePubAreaVO = remoteBasPubAreaService.queryByCode(skuVo.getAreaCode());
                    if (remotePubAreaVO != null) {
                        skuVo.setShortProducer(remotePubAreaVO.getAreaAlias());
                    }
                }
            }
            // 填充档口信息
            fillDeptSupplierInfo(result.getRecords());
        }
        return TableDataInfo.build(result);
    }

    /**
     * 填充档口信息
     *
     * @param result
     */
    private void fillDeptSupplierInfo(List<SupplierSkuVo> result) {
        if (CollUtil.isEmpty(result)) {
            return;
        }
        List<Long> supplierIds = result.stream().map(SupplierSkuVo::getSupplierId).distinct().toList();
        Map<Long, RemoteSupplierVo> supplierMap = remoteSupplierService.getSupplierByIds(supplierIds).stream().collect(Collectors.toMap(RemoteSupplierVo::getId, Function.identity()));
        result.forEach(e -> e.setSupplierAlias(supplierMap.getOrDefault(e.getSupplierId(), new RemoteSupplierVo()).getAlias()));

        List<Long> supplierDeptIdList = result.stream().map(SupplierSkuVo::getSupplierDeptId).filter(Objects::nonNull).filter(v -> v > 0L).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(supplierDeptIdList)) {
            return;
        }
        //填充档口信息
        Map<Long, CodeNameVo> deptNameMap = remoteDeptService.getDeptCodeNameMap(supplierDeptIdList);
        if (CollUtil.isEmpty(deptNameMap)) {
            return;
        }
        for (SupplierSkuVo record : result) {
            CodeNameVo vo = deptNameMap.get(record.getSupplierDeptId());
            if (Objects.nonNull(vo)) {
                record.setSupplierDeptName(vo.getName());
                record.setSupplierDeptCode(vo.getCode());
            }
        }
    }


    private void fillDeptSupplierInfo(SupplierSkuVo result) {
        if (Objects.isNull(result) || Objects.isNull(result.getSupplierDeptId()) || result.getSupplierDeptId() == 0L) {
            return;
        }
        RemoteDeptDetailVo byDeptId = remoteDeptService.getByDeptId(result.getSupplierDeptId());
        if (Objects.isNull(byDeptId)) {
            return;
        }
        result.setSupplierDeptName(byDeptId.getDeptName());
        result.setSupplierDeptCode(byDeptId.getDeptCode());
    }

    @Override
    public TableDataInfo<SupplierSkuVo> queryPage(SupplierSkuSimplePageBo bo) {
        LoginUser user = LoginHelper.getLoginUser();
        if (user == null) {
            throw new ServiceException("非法请求");
        }

        // 名称检索升级
        if (StringUtils.isNotBlank(bo.getSpuName())) {
            bo.setFullName(bo.getSpuName());
            bo.setSpuName(null);
        }

        //        SupplierSkuPageBo queryBo = MapstructUtils.convert(bo, SupplierSkuPageBo.class);
        QueryLastListBo queryBo = MapstructUtils.convert(bo, QueryLastListBo.class);

        // 供应商&档口数据过滤
        if (bo.isByLoginInfo()) {
            queryBo.setSupplierIdList(Lists.newArrayList(user.getRelationId()));
            if (Objects.nonNull(user.getDeptId()) && !Objects.equals(user.getDeptId(), 0L)) {
                queryBo.setSupplierDeptIdList(Lists.newArrayList(user.getDeptId()));
            }
        }

        // 总仓数据过滤 - 仅自己
        if (bo.isOwn()) {
            queryBo.setBuyerCodeList(Lists.newArrayList(user.getUserCode()));
        }

        // 供应商要传总仓id
        if (CollUtil.isEmpty(bo.getRegionWhIdList()) && bo.getRegionWhId() != null) {
            queryBo.setRegionWhIdList(Lists.newArrayList(bo.getRegionWhId()));
        }

        // 总仓过滤掉非当期销售日，审核驳回的数据
        if (Objects.equals(bo.getIsManage(), 1)) {
            queryBo.setHistoryRejected(getSaleDate(bo.getRegionWhId()));
        } else {
            // 供应商如果查的是 已驳回+待审核，则逆序排列 - 已驳回置顶
            if (CollUtil.isNotEmpty(bo.getStatusList())
                    && bo.getStatusList().contains(SupplierSkuStatusEnum.STATUS2.getCode())
                    && bo.getStatusList().contains(SupplierSkuStatusEnum.STATUS1.getCode())) {
                queryBo.setOrderByStatusDesc(1);
            }
        }

        LocalDate saleDate = null;
        if (bo.getRegionWhId() != null && bo.getRegionWhId() > 0L) {
            saleDate = getSaleDate(bo.getRegionWhId());
            queryBo.setSaleDate(saleDate);
        }


        Page<SupplierSkuVo> vo = skuMapper.queryLastList(queryBo, bo.build());
        // 参数转换
        fillSupplierSkuVOPage(vo, saleDate);

        return TableDataInfo.build(vo);
    }


    public void fillSupplierSkuVOPage(Page<SupplierSkuVo> vo, @Nullable LocalDate saleDate) {
        if (CollUtil.isNotEmpty(vo.getRecords())) {
            // 填充档口信息
            fillDeptSupplierInfo(vo.getRecords());
            //根据供应商商品销售批次id集合和类型查询供应商商品销售批次文件信息(查询商品展示图片)
            List<Long> supplierSkuIdList = vo.getRecords().stream().map(SupplierSkuVo::getSupplierSkuId).toList();
            List<Long> skuIdList = vo.getRecords().stream().map(SupplierSkuVo::getId).toList();
            QuerySupplierSkuFileBo fileBo = new QuerySupplierSkuFileBo();
            fileBo.setSupplierSkuIdList(supplierSkuIdList);
            List<SupplierSkuFileVo> fileVoList = supplierSkuFileMapper.queryList(fileBo);
            // 云仓库存
            List<CwStockDetailVo> remoteSupplierSkuStockVos = this.listSupplierSkuStockBySupplierIds(skuIdList);
            Map<Long, CwStockDetailVo> stockMap = remoteSupplierSkuStockVos.stream().collect(Collectors.toMap(CwStockDetailVo::getSkuId, e -> e, (key1, key2) -> key2));

            //处理数据
            Map<Long, List<SupplierSkuFileVo>> fileMap = fileVoList.stream().collect(Collectors.groupingBy(SupplierSkuFileVo::getSupplierSkuId));


            //处理出参
            for (SupplierSkuVo skuVo : vo.getRecords()) {
//                    skuVo.setPrice(skuVo.getUpPrice());
                // 是否在当前销售日
                if (saleDate != null && saleDate.isEqual(skuVo.getSaleDate())) {
                    skuVo.setOnSalesDay(1);
                }
                // 处理单价 - 列表要显示
                BigDecimal netWeightPrice = skuVo.getPrice().divide(skuVo.getSpuNetWeight(), 2, RoundingMode.HALF_UP);
                if (netWeightPrice.compareTo(BigDecimal.ZERO) == 0) {
                    netWeightPrice = new BigDecimal("0.01");
                }
                // 处理售后规则
                CategoryAfterTypeEnum afterTypeEnum = CategoryAfterTypeEnum.loadByCode(skuVo.getAfterSaleType());
                if (afterTypeEnum != null) {
                    skuVo.setAfterSaleName(afterTypeEnum.getName());
                    skuVo.setAfterSaleDesc(afterTypeEnum.getDesc());
                }
                skuVo.setNetWeightPrice(netWeightPrice);
                // 商品库存
                CwStockDetailVo stockVo = stockMap.get(skuVo.getId());
                if (stockVo != null) {
                    Integer stock = Optional.ofNullable(stockVo).map(CwStockDetailVo::getOnhandQty).map(BigDecimal::intValue).orElseGet(() -> null);
                    Integer lockStock = Optional.ofNullable(stockVo).map(CwStockDetailVo::getAllocationQty).map(BigDecimal::intValue).orElseGet(() -> 0);
                    skuVo.setCloudActualStock(stock);
                    skuVo.setCloudLockStock(lockStock);
                    if (stock != null) {
                        skuVo.setCloudStock(stock - lockStock);
                    }
                } else {
                    skuVo.setStock(0);
                }
                // 规格名称
                skuVo.tranSpuStandardsName().buildSkuStandardsList();
                List<SupplierSkuFileVo> fileList = fileMap.get(skuVo.getSupplierSkuId());
                skuVo.setFileList(fileList);
                if (CollUtil.isNotEmpty(fileList)) {
                    for (SupplierSkuFileVo file : fileList) {
                        if (SupplierSkuFileTypeEnum.TYPE1.getCode().equals(file.getType())) {
                            skuVo.setImgUrl(file.getFileUrl());
                            break;
                        }
                    }
                }
                // 区域简称
                if (StringUtils.isNotBlank(skuVo.getAreaCode()) && StringUtils.isBlank(skuVo.getShortProducer())) {
                    RemotePubAreaVO remotePubAreaVO = remoteBasPubAreaService.queryByCode(skuVo.getAreaCode());
                    if (remotePubAreaVO != null) {
                        skuVo.setShortProducer(remotePubAreaVO.getAreaAlias());
                    }
                }
            }
        }
    }


    /**
     * 查询最新的批次id
     *
     * @param skuIds
     * @return
     */
    @Override
    public List<Long> queryNewSupplierSkuIds(List<Long> skuIds) {
        return skuMapper.selectList(new LambdaQueryWrapper<Sku>().in(Sku::getId, skuIds).select(Sku::getSupplierSkuId))
                .stream().map(Sku::getSupplierSkuId).collect(Collectors.toList());
    }

    private void convertHandleSkuInfo(List<SupplierSkuVo> pageVoList) {

        List<Long> supplierSkuIdList = pageVoList.stream().map(SupplierSkuVo::getId).toList();
        QuerySupplierSkuFileBo fileBo = new QuerySupplierSkuFileBo();
        fileBo.setSupplierSkuIdList(supplierSkuIdList);
        List<SupplierSkuFileVo> fileVoList = supplierSkuFileMapper.queryList(fileBo);
        //处理数据
        Map<Long, List<SupplierSkuFileVo>> fileMap = fileVoList.stream().collect(Collectors.groupingBy(SupplierSkuFileVo::getSupplierSkuId));
        for (SupplierSkuVo infoVo : pageVoList) {
            List<SupplierSkuFileVo> fileList = fileMap.get(infoVo.getId());
            infoVo.setFileList(fileList);
            if (fileList != null && fileList.size() > 0) {
                for (SupplierSkuFileVo file : fileList) {
                    if (SupplierSkuFileTypeEnum.TYPE1.getCode().equals(file.getType())) {
                        infoVo.setImgUrl(file.getFileUrl());
                        break;
                    }
                }
            }
        }
        // 填充档口信息
        fillDeptSupplierInfo(pageVoList);
    }

    private List<RemoteSupplierSkuInfoVo> listByLabel(RemoteQuerySkuIdBo bo) {
        if (StrUtil.isBlank(bo.getSkuLabel())) {
            return Collections.emptyList();
        }
        // 只获取正常批次，暂不考虑尾货
        bo.setBatchType(SupplierSkuBatchTypeEnum.BATCH_TYPE1.getCode());
        List<RemoteSupplierSkuInfoVo> list = supplierSkuMapper.listBySkuLabel(bo);
        log.keyword("城市仓查询多货商品").info("城市仓查询多货商品, bo:{},list:{}", bo, list);

        if (ObjectUtil.isNotEmpty(list)) {
            for (RemoteSupplierSkuInfoVo infoVo : list) {
                // 规格转换
                infoVo.buildSpuStandardsName();
            }
            return list;
        }

        // 兼容无label的老数据 + 批次码识别功能
        SupplierSkuVo skuInfoVo = this.getBySupplierSkuCode(TemCode.getOriginalCode(bo.getSkuLabel()));
        log.keyword("城市仓查询多货商品").info("城市仓查询多货商品, bo:{},skuInfoVo:{}", bo, skuInfoVo);
        if (ObjectUtil.isEmpty(skuInfoVo)) {
            return Collections.emptyList();
        }
        return Lists.newArrayList(MapstructUtils.convert(skuInfoVo, RemoteSupplierSkuInfoVo.class));

    }


    private LocalDate getSaleDate(Long regionWhId) {
        RemoteRegionWhVo whVo = remoteRegionWhService.queryById(regionWhId);
        if (whVo == null) {
            throw new ServiceException("总仓不存在");
        }
        if (whVo.getStatus() == 0) {
            throw new ServiceException("总仓已被禁用");
        }
        return SaleDateUtil.getSaleDate(whVo.getSalesTimeEnd());
    }

    private void verifyWeight(BigDecimal spuGrossWeight, BigDecimal spuNetWeight) {
        if (spuGrossWeight != null) {
            if (spuGrossWeight.compareTo(BigDecimal.ZERO) <= 0) {
                throw new ServiceException("毛重必须大于0");
            }
        }
        if (spuNetWeight != null) {
            if (spuNetWeight.compareTo(BigDecimal.ZERO) <= 0) {
                throw new ServiceException("净重必须大于0");
            }
        }
    }

    /**
     * 新增供应商销售批次商品
     *
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @Lock4j(name = ProductRedisNames.SKU_ADD, keys = "#supplierSkuId", expire = 30000, acquireTimeout = 1000)
    public Long insertBatchSyncSku(Long supplierSkuId) {

        SupplierSkuVo supplierSku = supplierSkuMapper.queryById(supplierSkuId);
        if (supplierSku == null) {
            throw new ServiceException("销售批次不存在");
        }
        Sku sku = MapstructUtils.convert(supplierSku, Sku.class);
        sku.setId(null);
        sku.setSupplierSkuId(supplierSku.getId());

        // 批次匹配skuId
        CheckSupplierSkuBo checkSupplierSkuBo = new CheckSupplierSkuBo();
//        checkSupplierSkuBo.setId(supplierSkuId);
        checkSupplierSkuBo.setSpuId(supplierSku.getSpuId());
        checkSupplierSkuBo.setRegionWhId(supplierSku.getRegionWhId());
        checkSupplierSkuBo.setSupplierId(supplierSku.getSupplierId());
        checkSupplierSkuBo.setSupplierDeptId(supplierSku.getSupplierDeptId() == null ? 0L : supplierSku.getSupplierDeptId());
        checkSupplierSkuBo.setSpuGrade(Optional.ofNullable(supplierSku.getSpuGrade()).orElseGet(() -> ""));
        checkSupplierSkuBo.setSpuStandards(Optional.ofNullable(supplierSku.getSpuStandards()).orElseGet(() -> ""));
        checkSupplierSkuBo.setDomestic(Optional.ofNullable(supplierSku.getDomestic()).orElseGet(() -> 0));
        checkSupplierSkuBo.setBusinessType(supplierSku.getBusinessType());
        checkSupplierSkuBo.setProvideRegionWhId(supplierSku.getProvideRegionWhId() == null ? 0L : supplierSku.getProvideRegionWhId());
        checkSupplierSkuBo.setSpuName(supplierSku.getSpuName());
        checkSupplierSkuBo.setCategoryId(supplierSku.getCategoryId());
        checkSupplierSkuBo.setBatchType(supplierSku.getBatchType());

        // END: 匹配不到，新增sku并反填批次
        SupplierSkuVo lastSku = skuMapper.checkSupplierSku(checkSupplierSkuBo);
        if (lastSku == null) {
            sku.setSkuCode(this.createSkuCode(supplierSku.getSpuCode(), supplierSku.getSaleDate()));
            skuMapper.insert(sku);
            supplierSkuMapper.update(new LambdaUpdateWrapper<SupplierSku>().set(SupplierSku::getSkuId, sku.getId()).eq(SupplierSku::getId, supplierSkuId));
            // sku状态变更更新统计表
            skuSalesStatisticsService.updateSkuStatus(sku.getId(), sku.getStatus());
            return sku.getId();
        }

        // 反填skuId
        if (!Objects.equals(supplierSku.getSkuId(), lastSku.getId())) {
            supplierSkuMapper.update(new LambdaUpdateWrapper<SupplierSku>().set(SupplierSku::getSkuId, lastSku.getId()).eq(SupplierSku::getId, supplierSkuId));
        }
        // 更新 sku 为最新批次
        if (supplierSku.getId() >= lastSku.getSupplierSkuId()) {
            sku.setId(lastSku.getId());
            skuMapper.updateById(sku);
            // sku状态变更更新统计表
            if (!Objects.equals(sku.getStatus(), lastSku.getStatus())) {
                skuSalesStatisticsService.updateSkuStatus(lastSku.getId(), sku.getStatus());
            }
        }
        return lastSku.getId();
    }

    /**
     * 检验品类是否禁用
     *
     * @param categoryId
     */
    private void checkCategory(Long categoryId) {
        Category category = categoryMapper.selectById(categoryId);
        if (category == null) {
            throw new ServiceException("品类不存在");
        }
        if (category.getLevel() == null || category.getLevel() != 2) {
            throw new ServiceException("商品绑定品类有误，如需上架请联系采购");
        }
        if (category.getStatus().equals(0)) {
            throw new ServiceException("品类已被禁用，禁止上架。如需上架请联系采购");
        }
    }

    public List<Long> querySupplierSkuIdGroup(RemoteQuerySkuIdsBo bo) {
        SupplierSkuVo skuVo = supplierSkuMapper.queryById(bo.getSupplierSkuId());
        if (skuVo == null) {
            throw new ServiceException("批次不存在");
        }

        CheckSupplierSkuBo checkSupplierSkuBo = new CheckSupplierSkuBo();
        // 相同商品条件key
        checkSupplierSkuBo.setSpuId(Optional.ofNullable(skuVo.getSpuId()).orElseGet(() -> 0L));
        checkSupplierSkuBo.setRegionWhId(Optional.ofNullable(skuVo.getRegionWhId()).orElseGet(() -> 0L));
        checkSupplierSkuBo.setSupplierId(Optional.ofNullable(skuVo.getSupplierId()).orElseGet(() -> 0L));
        checkSupplierSkuBo.setSupplierDeptId(Optional.ofNullable(skuVo.getSupplierDeptId()).orElseGet(() -> 0L));
        checkSupplierSkuBo.setSpuGrade(Optional.ofNullable(skuVo.getSpuGrade()).orElseGet(() -> ""));
        checkSupplierSkuBo.setSpuStandards(Optional.ofNullable(skuVo.getSpuStandards()).orElseGet(() -> ""));
        checkSupplierSkuBo.setDomestic(Optional.ofNullable(skuVo.getDomestic()).orElseGet(() -> 0));
        checkSupplierSkuBo.setBusinessType(Optional.ofNullable(skuVo.getBusinessType()).orElseGet(() -> 0));
        checkSupplierSkuBo.setProvideRegionWhId(Optional.ofNullable(skuVo.getProvideRegionWhId()).orElseGet(() -> 0L));
        checkSupplierSkuBo.setSpuName(Optional.ofNullable(skuVo.getSpuName()).orElseGet(() -> ""));
        checkSupplierSkuBo.setCategoryId(Optional.ofNullable(skuVo.getCategoryId()).orElseGet(() -> 0L));
        // 销售日期范围
        checkSupplierSkuBo.setSaleDateStart(bo.getSaleDateStart());
        checkSupplierSkuBo.setSaleDateEnd(bo.getSaleDateEnd());
        return supplierSkuMapper.getSameSupplierSkuIdList(checkSupplierSkuBo);
    }

    /**
     * 获取供应商商品销售批次编码
     *
     * @param spuId
     * @param supplierId
     * @param supplierSpuCode
     * @return
     */
    private String getSkuCode(Long spuId, Long supplierId, String supplierSpuCode, LocalDate saleDate) {
        String dateStr = DateUtil.format(saleDate.atStartOfDay(), "yyMMdd");
        String redisKey = ProductRedisNames.SUPPLIER_SKU_CODE + ":" + supplierId + ":" + spuId + ":" + dateStr;
        RAtomicLong atomicLong = RedisUtils.getClient().getAtomicLong(redisKey);
        long no = atomicLong.incrementAndGet();
        if (no == 1) {
            RedisUtils.getClient().getBucket(redisKey).expire(Duration.ofHours(25));
        }
        if (no >= 10000) {
            throw new ServiceException("此商品超过每天供货数量上限");
        }
        return String.format("%s%s%0" + 4 + "d", supplierSpuCode, dateStr, no);
    }

    /**
     * 创建 sku 编码
     *
     * @return
     */
    private String createSkuCode(String spuCode, LocalDate saleDate) {
        String dateStr = DateUtil.format(saleDate.atStartOfDay(), "yyMMdd");
        String no = IdUtil.getSnowflakeNextIdStr().substring(10, 19);
        return spuCode + "K" + dateStr + no;
    }

    public boolean isUnique(SupplierSkuVo supplierSku, Sku historySku) {
        if (supplierSku == null || historySku == null) {
            return false; // 如果任意一个对象为空，则认为不唯一
        }

        return Objects.equals(supplierSku.getSpuId(), historySku.getSpuId()) &&
                Objects.equals(supplierSku.getRegionWhId(), historySku.getRegionWhId()) &&
                Objects.equals(supplierSku.getSupplierId(), historySku.getSupplierId()) &&
                Objects.equals(ObjectUtil.defaultIfNull(supplierSku.getSupplierDeptId(), 0L), ObjectUtil.defaultIfNull(historySku.getSupplierDeptId(), 0L)) &&
                Objects.equals(ObjectUtil.defaultIfNull(supplierSku.getSpuGrade(), ""), ObjectUtil.defaultIfNull(historySku.getSpuGrade(), "")) &&
                Objects.equals(ObjectUtil.defaultIfNull(supplierSku.getSpuStandards(), ""), ObjectUtil.defaultIfNull(historySku.getSpuStandards(), "")) &&
                Objects.equals(ObjectUtil.defaultIfNull(supplierSku.getDomestic(), 0), ObjectUtil.defaultIfNull(historySku.getDomestic(), 0)) &&
                Objects.equals(supplierSku.getBusinessType(), historySku.getBusinessType()) &&
                Objects.equals(ObjectUtil.defaultIfNull(supplierSku.getProvideRegionWhId(), 0L), ObjectUtil.defaultIfNull(historySku.getProvideRegionWhId(), 0L)) &&
                Objects.equals(supplierSku.getSpuName(), historySku.getSpuName()) &&
                Objects.equals(supplierSku.getCategoryId(), historySku.getCategoryId()) &&
                Objects.equals(supplierSku.getBatchType(), historySku.getBatchType());
    }

    /**
     * 修改供应商销售批次商品
     * <p>一些空的字段无法覆盖，可能会有问题，但可以使用其上架时间作为sku的最后上架时间</p>
     *
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @Lock4j(name = ProductRedisNames.SKU_ADD, keys = "#supplierSkuId", expire = 30000, acquireTimeout = 1000)
    public Long updateBatchSyncSku(Long supplierSkuId) {
        // 修改后的批次
        SupplierSkuVo supplierSku = supplierSkuMapper.queryById(supplierSkuId);
        if (supplierSku == null) {
            return 0L;
        }

        // 更新sku的批次数据
        Sku updateSku = MapstructUtils.convert(supplierSku, Sku.class);
        updateSku.setId(null);
        updateSku.setSupplierSkuId(supplierSku.getId());

        // 更新前sku
        Sku historySku = this.getOne(new LambdaQueryWrapper<Sku>().eq(Sku::getSupplierSkuId, supplierSkuId).last("limit 1"));
        // 健壮处理，修改了列表不可见的历史数据
        if (historySku == null) {
            return 0L;
        }

        // 唯一性字段不变，直接修改
        if (isUnique(supplierSku, historySku)) {
            updateSku.setId(historySku.getId());
            skuMapper.updateById(updateSku);
            if (!Objects.equals(updateSku.getStatus(), historySku.getStatus())) {
                // sku状态变更更新统计表
                skuSalesStatisticsService.updateSkuStatus(historySku.getId(), updateSku.getStatus());
            }
            return historySku.getId();
        }

        // 批次新唯一性重新匹配的sku
        CheckSupplierSkuBo checkSku = new CheckSupplierSkuBo();
//        checkSku.setId(supplierSkuId);
        checkSku.setSpuId(supplierSku.getSpuId());
        checkSku.setRegionWhId(supplierSku.getRegionWhId());
        checkSku.setSupplierId(supplierSku.getSupplierId());
        checkSku.setSupplierDeptId(supplierSku.getSupplierDeptId() == null ? 0L : supplierSku.getSupplierDeptId());
        checkSku.setSpuGrade(Optional.ofNullable(supplierSku.getSpuGrade()).orElseGet(() -> ""));
        checkSku.setSpuStandards(Optional.ofNullable(supplierSku.getSpuStandards()).orElseGet(() -> ""));
        checkSku.setDomestic(Optional.ofNullable(supplierSku.getDomestic()).orElseGet(() -> 0));
        checkSku.setBusinessType(supplierSku.getBusinessType());
        checkSku.setProvideRegionWhId(supplierSku.getProvideRegionWhId() == null ? 0L : supplierSku.getProvideRegionWhId());
        checkSku.setSpuName(supplierSku.getSpuName());
        checkSku.setCategoryId(supplierSku.getCategoryId());
        checkSku.setBatchType(supplierSku.getBatchType());
        SupplierSkuVo lastSku = skuMapper.checkSupplierSku(checkSku);


        // 旧sku唯一性重新匹配的批次
        CheckSupplierSkuBo checkSupplierSku = new CheckSupplierSkuBo();
//            checkSupplierSku.setId(supplierSkuId);
        checkSupplierSku.setSpuId(historySku.getSpuId());
        checkSupplierSku.setRegionWhId(historySku.getRegionWhId());
        checkSupplierSku.setSupplierId(historySku.getSupplierId());
        checkSupplierSku.setSupplierDeptId(historySku.getSupplierDeptId() == null ? 0L : historySku.getSupplierDeptId());
        checkSupplierSku.setSpuGrade(Optional.ofNullable(historySku.getSpuGrade()).orElseGet(() -> ""));
        checkSupplierSku.setSpuStandards(Optional.ofNullable(historySku.getSpuStandards()).orElseGet(() -> ""));
        checkSupplierSku.setDomestic(Optional.ofNullable(historySku.getDomestic()).orElseGet(() -> 0));
        checkSupplierSku.setBusinessType(historySku.getBusinessType());
        checkSupplierSku.setProvideRegionWhId(historySku.getProvideRegionWhId() == null ? 0L : historySku.getProvideRegionWhId());
        checkSupplierSku.setSpuName(historySku.getSpuName());
        checkSupplierSku.setCategoryId(historySku.getCategoryId());
        checkSupplierSku.setBatchType(historySku.getBatchType());
        SupplierSkuVo lastSupplierSku = supplierSkuMapper.checkSupplierSku(checkSupplierSku);

        // 修改后的sku匹配不到批次，可直接变更
        if (lastSupplierSku == null) {
            updateSku.setId(historySku.getId());
            skuMapper.updateById(updateSku);
            // sku状态变更更新统计表
            if (!Objects.equals(updateSku.getStatus(), historySku.getStatus())) {
                skuSalesStatisticsService.updateSkuStatus(historySku.getId(), updateSku.getStatus());
            }
            return historySku.getId();
        }

        // 健壮处理，原先的数据匹配sku有误
        if (!Objects.equals(lastSupplierSku.getSkuId(), historySku.getId())) {
            supplierSkuMapper.update(new LambdaUpdateWrapper<SupplierSku>().set(SupplierSku::getSkuId, historySku.getId()).eq(SupplierSku::getId, lastSupplierSku.getId()));
        }
        // 回退之前匹配的sku到上一批次
        Sku updateOldSku = MapstructUtils.convert(lastSupplierSku, Sku.class);
        updateOldSku.setSupplierSkuId(lastSupplierSku.getId());
        updateOldSku.setId(historySku.getId());
        skuMapper.updateById(updateOldSku);
        if (!Objects.equals(updateOldSku.getStatus(), historySku.getStatus())) {
            skuSalesStatisticsService.updateSkuStatus(historySku.getId(), updateOldSku.getStatus());
        }

        // 批次匹配不到sku，走新增反填
        if (lastSku == null) {
            updateSku.setSkuCode(this.createSkuCode(supplierSku.getSpuCode(), supplierSku.getSaleDate()));
            skuMapper.insert(updateSku);
            supplierSkuMapper.update(new LambdaUpdateWrapper<SupplierSku>().set(SupplierSku::getSkuId, updateSku.getId()).eq(SupplierSku::getId, supplierSkuId));
            // sku状态变更更新统计表
            skuSalesStatisticsService.updateSkuStatus(updateSku.getId(), updateSku.getStatus());
            return updateSku.getId();
        }

        // 批次匹配到老sku，刷新sku的批次数据和批次skuId
        supplierSkuMapper.update(new LambdaUpdateWrapper<SupplierSku>().set(SupplierSku::getSkuId, lastSku.getId()).eq(SupplierSku::getId, supplierSkuId));
        if (lastSku.getSupplierSkuId() <= supplierSkuId) {
            updateSku.setId(lastSku.getId());
            skuMapper.updateById(updateSku);
            // sku状态变更更新统计表
            if (!Objects.equals(updateSku.getStatus(), lastSku.getStatus())) {
                skuSalesStatisticsService.updateSkuStatus(lastSku.getId(), updateSku.getStatus());
            }
        }

        return lastSku.getId();
    }


    /**
     * 当批次过了销售日期之后禁止修改
     *
     * @param old
     */
    private void updateVerify(SupplierSkuVo old) {
        if (old.getSaleDate().isBefore(getSaleDate(old.getRegionWhId()))) {
            throw new ServiceException("该批次销售日已过，不支持修改");
        }
    }


    @Value("#{'${banguo-product.fruitSet:}'.split(',')}")
    private Set<String> fruitSet = new TreeSet<>() {{
        add("国产水果");
        add("进口水果");
    }};

    @Value("${banguo-product.mergeFruitName}")
    private String mergeFruitName = "水果";

    @Value("#{'${banguo-product.vegetableSet:}'.split(',')}")
    private Set<String> vegetableSet = new TreeSet<>() {{
        add("国产蔬菜");
        add("进口蔬菜");
    }};

    @Value("${banguo-product.mergeVegetableName}")
    private String mergeVegetableName = "蔬菜";

    @Value("#{'${banguo-product.sortNameList:}'.split(',')}")
    private List<String> sortNameList = new ArrayList<>() {{
        add("水果");
        add("蔬菜");
        add("干果");
        add("耗材");
        add("其他");
    }};

    /**
     * 按照标准排序
     *
     * @param oldList
     * @return
     */
    private List<SaleCategoryVo> sortName(List<SaleCategoryVo> oldList) {
        if (oldList != null && oldList.size() > 0) {
            Map<String, SaleCategoryVo> map = oldList.stream().collect(Collectors.toMap(SaleCategoryVo::getCategoryName, Function.identity()));
            List<SaleCategoryVo> newList = new ArrayList<>();
            for (String name : sortNameList) {
                if (map.get(name) != null) {
                    newList.add(map.get(name));
                    map.remove(name);
                }
            }
            newList.addAll(map.values().stream().toList());
            return newList;
        }
        return new ArrayList<>();
    }

    private String getMergeName(String categoryName) {
        if (fruitSet.contains(categoryName)) {
            return mergeFruitName;
        }
        if (vegetableSet.contains(categoryName)) {
            return mergeVegetableName;
        }
        return null;
    }

    /**
     * 上架操作的时候，处理所属二级目录数据
     *
     * @param supplierSkuId
     * @param categoryId
     * @param regionWhId
     */
    private void handleUp(Long supplierSkuId, Long categoryId, Long regionWhId) {
        //根据三级分类查询二级分类
        CategoryVO categoryVO = categoryMapper.getParParent(categoryId);
        if (categoryVO == null || categoryVO.getLevel() == null || categoryVO.getLevel() == 0) {
            log.keyword("商品绑定品类有误").info(supplierSkuId.toString());
            throw new ServiceException("商品绑定品类有误，如需上架请联系采购");
        }
        //处理总仓下二级分类现在有多少个上架批次
        String updateKey = ProductRedisNames.SALE_CATEGORY_UPDATE + regionWhId;
        String updateHkey = categoryVO.getName();
        //获取总仓下二级分类现在有多少个上架批次
        HashSet<String> updateValue = RedisUtils.getCacheMapValue(updateKey, updateHkey);
        if (updateValue == null) {
            updateValue = new HashSet<>();
        }
        //把新上架的批次加入到所属的二级分类下
        updateValue.add(supplierSkuId.toString());
        RedisUtils.setCacheMapValue(updateKey, updateHkey, updateValue);
        //处理一级分类下当前总仓所有的二级分类
        CategoryVO topCategoryVO = categoryMapper.selectVoById(categoryVO.getParentId());
        //需求是把国产水果和进口水果合成水果
        String mergeTopName = getMergeName(topCategoryVO.getName());
        if (mergeTopName == null) {
            mergeTopName = topCategoryVO.getName();
        }
        String selectKey = ProductRedisNames.SALE_CATEGORY_SELECT + mergeTopName;
        String selectHkey = regionWhId.toString();
        //获取一级分类下当前总仓所有的二级分类
        HashMap<String, SaleCategoryVo> map = RedisUtils.getCacheMapValue(selectKey, selectHkey);
        if (map == null || map.size() == 0) {
            map = new HashMap<>();

            //如果一级分类下当前总仓所有的二级分类为空，那么需要把一级分类加入到所属总仓下面
            String topSelectKey = ProductRedisNames.SALE_TOP_CATEGORY_SELECT;
            String topSelectHkey = regionWhId.toString();
            //获取当前总仓下的一级分类
            HashMap<String, SaleCategoryVo> topMap = RedisUtils.getCacheMapValue(topSelectKey, topSelectHkey);
            if (topMap == null) {
                topMap = new HashMap<>();
            }
            SaleCategoryVo vo = topMap.get(mergeTopName);
            //组装一级分类信息
            if (vo == null) {
                vo = new SaleCategoryVo();
                HashSet<Long> topSet = new HashSet<>();
                vo.setCategoryIdList(topSet);
                vo.setCategoryName(mergeTopName);
                vo.setImgUrl(topCategoryVO.getImgUrl());
            }
            vo.getCategoryIdList().add(topCategoryVO.getId());
            //把一级分类加入到所属总仓下面
            topMap.put(mergeTopName, vo);
            RedisUtils.setCacheMapValue(topSelectKey, topSelectHkey, topMap);
        }
        //获取当前批次所属二级分类
        SaleCategoryVo vo = map.get(categoryVO.getName());
        if (vo == null) {
            //如果为空，则把当前批次所属的二级分类加入到所属总仓下
            vo = new SaleCategoryVo();
            HashSet<Long> set = new HashSet<>();
            vo.setCategoryIdList(set);
            vo.setCategoryName(categoryVO.getName());
            vo.setImgUrl(categoryVO.getImgUrl());
        }
        vo.getCategoryIdList().add(categoryVO.getId());
        //把当前批次所属的二级分类加入到所属总仓下
        map.put(categoryVO.getName(), vo);
        RedisUtils.setCacheMapValue(selectKey, selectHkey, map);
    }

    /**
     * 下架操作的时候，处理所属二级目录数据
     *
     * @param supplierSkuId
     * @param categoryId
     * @param regionWhId
     */
    private void handleDown(Long supplierSkuId, Long categoryId, Long regionWhId) {
        //根据三级分类查询二级分类
        CategoryVO categoryVO = categoryMapper.getParParent(categoryId);
        if (categoryVO == null || categoryVO.getLevel() == null || categoryVO.getLevel() == 0) {
            log.keyword("商品绑定品类有误").info(supplierSkuId.toString());
            throw new ServiceException("商品绑定品类有误，如需上架请联系采购");
        }
        //处理总仓下二级分类现在有多少个上架批次
        String updateKey = ProductRedisNames.SALE_CATEGORY_UPDATE + regionWhId;
        String updateHkey = categoryVO.getName();
        //获取总仓下二级分类现在有多少个上架批次
        HashSet<String> updateValue = RedisUtils.getCacheMapValue(updateKey, updateHkey);
        if (updateValue != null) {
            //如果不为空，则删除二级分类下的这个批次id
            updateValue.remove(supplierSkuId.toString());
            if (updateValue.size() == 0) {
                //二级分类下没有批次了，那就把二级分类也删除
                RedisUtils.delCacheMapValue(updateKey, updateHkey);
            } else {
                //二级分类下还有批次，则把现有的批次集合存到所属二级分类下
                RedisUtils.setCacheMapValue(updateKey, updateHkey, updateValue);
            }
        }
        //如果下架批次，二级目录下还有批次，那么不用管二级分类了
        if (updateValue == null || updateValue.size() == 0) {
            //如果下架批次，二级目录下没有批次了，那么当前批次所属二级分类也需要删掉
            CategoryVO topCategoryVO = categoryMapper.selectVoById(categoryVO.getParentId());
            //需求是把国产水果和进口水果合成水果
            String mergeTopName = getMergeName(topCategoryVO.getName());
            if (mergeTopName == null) {
                mergeTopName = topCategoryVO.getName();
            }
            String selectKey = ProductRedisNames.SALE_CATEGORY_SELECT + mergeTopName;
            String selectHkey = regionWhId.toString();
            //获取一级分类下当前总仓下的所有二级分类
            HashMap<String, SaleCategoryVo> map = RedisUtils.getCacheMapValue(selectKey, selectHkey);
            if (map != null) {
                //获取所属二级分类信息
                SaleCategoryVo vo = map.get(categoryVO.getName());
                if (vo != null) {
                    vo.getCategoryIdList().remove(categoryVO.getId());
                    if (vo.getCategoryIdList().size() == 0) {
                        //如果所属二级分类下面的id被删完了，那么删除这个二级分类的信息
                        map.remove(categoryVO.getName());
                    }
                }
                if (map.size() == 0) {
                    //如果一级分类下当前总仓下没有二级分类了，则删除一级分类下的总仓
                    RedisUtils.delCacheMapValue(selectKey, selectHkey);

                    String topSelectKey = ProductRedisNames.SALE_TOP_CATEGORY_SELECT;
                    String topSelectHkey = regionWhId.toString();
                    //获取总仓的所有一级分类
                    HashMap<String, SaleCategoryVo> topMap = RedisUtils.getCacheMapValue(topSelectKey, topSelectHkey);
                    if (topMap != null) {
                        //删除总仓下的一级分类
                        SaleCategoryVo topVo = topMap.get(mergeTopName);
                        if (topVo != null) {
                            topVo.getCategoryIdList().remove(topCategoryVO.getId());
                            if (topVo.getCategoryIdList().size() == 0) {
                                topMap.remove(mergeTopName);
                            }
                        }
                        if (topMap.size() == 0) {
                            //如果总仓下没有一级分类了，就把整个总仓都删掉
                            RedisUtils.delCacheMapValue(topSelectKey, topSelectHkey);
                        } else {
                            //如果总仓下还有一级分类，则保存最新数据
                            RedisUtils.setCacheMapValue(topSelectKey, topSelectHkey, topMap);
                        }
                    }
                } else {
                    //如果一级分类下当前总仓下还有二级分类了，则保存最新数据
                    RedisUtils.setCacheMapValue(selectKey, selectHkey, map);
                }
            }
        }
    }

    /**
     * 每天定时任务批量下架总仓所有商品，处理总仓所属二级目录
     *
     * @param regionWhId
     */
    public void handleAll(Long regionWhId) {
        //删除所属总仓下的所有数据
        String updateKey = ProductRedisNames.SALE_CATEGORY_UPDATE + regionWhId;
        RedisUtils.deleteObject(updateKey);
        RedisUtils.delCacheMapValue(ProductRedisNames.SALE_TOP_CATEGORY_SELECT, regionWhId.toString());
        List<CategoryVO> categoryList = categoryMapper.getTopCategory();
        if (categoryList != null && categoryList.size() > 0) {
            String selectHkey = regionWhId.toString();
            for (CategoryVO vo : categoryList) {
                String mergeTopName = getMergeName(vo.getName());
                if (mergeTopName == null) {
                    mergeTopName = vo.getName();
                }
                String selectKey = ProductRedisNames.SALE_CATEGORY_SELECT + mergeTopName;
                RedisUtils.delCacheMapValue(selectKey, selectHkey);
            }
        }
    }


    /**
     * 根据平台商品控制参数，判断需要回填哪些文件
     *
     * @param infoVo
     * @param spuCreateControlVOList
     * @param supplierSpuVo
     * @param supplierSkuVo
     */
    public void getBackFile(GetBackInfoVo infoVo, List<SpuCreateControlVO> spuCreateControlVOList, SupplierSpuVo supplierSpuVo, SupplierSkuVo supplierSkuVo) {
        //根据平台商品控制参数，判断需要回填哪些文件
        if (spuCreateControlVOList != null && spuCreateControlVOList.size() > 0) {
            //计算当前时间和上次清除时间直接的天数差
            long interval = supplierSpuVo == null ? 0 : ChronoUnit.DAYS.between(supplierSpuVo.getLastClearTime(), LocalDate.now());
            SpuCreateControlVO createControlVO = spuCreateControlVOList.get(0);
            //查询批次文件
            QuerySupplierSkuFileBo queryFileBo = new QuerySupplierSkuFileBo();
            queryFileBo.setSupplierSkuIdList(Lists.newArrayList(supplierSkuVo.getId()));
            List<SupplierSkuFileVo> fileVoList = supplierSkuFileMapper.queryList(queryFileBo);
            //如果超过自动清除时间，且文件设置了自动清理，就不回填
            if (createControlVO.getAutoCle() > 0 && createControlVO.getAutoCle() <= interval) {
                //判断是否回填商品简介
                if (createControlVO.getSkuIntroductionCle() == 1) {
                    infoVo.setSnapshot(null);
                }
                if (fileVoList != null && fileVoList.size() > 0) {
                    List<SupplierSkuFileVo> newFileVoList = new ArrayList<>();
                    for (SupplierSkuFileVo fileVo : fileVoList) {
                        if (SupplierSkuFileTypeEnum.TYPE1.getCode().equals(fileVo.getType())) {
                            //判断是否回填商品图片
                            if (createControlVO.getSkuDetailImgCle() == 0) {
                                newFileVoList.add(fileVo);
                            }
                        } else if (SupplierSkuFileTypeEnum.TYPE2.getCode().equals(fileVo.getType())) {
                            //判断是否回填商品视频
                            if (createControlVO.getSkuVideoCle() == 0) {
                                newFileVoList.add(fileVo);
                            }
                        } else if (SupplierSkuFileTypeEnum.TYPE3.getCode().equals(fileVo.getType())
                                || SupplierSkuFileTypeEnum.TYPE31.getCode().equals(fileVo.getType())
                                || SupplierSkuFileTypeEnum.TYPE32.getCode().equals(fileVo.getType())
                                || SupplierSkuFileTypeEnum.TYPE33.getCode().equals(fileVo.getType())) {
                            //判断是否回填包装图片
                            if (createControlVO.getSkuPackImgCle() == 0) {
                                newFileVoList.add(fileVo);
                            }
                        } else if (SupplierSkuFileTypeEnum.TYPE4.getCode().equals(fileVo.getType())) {
                            //判断是否回填包装视频
                            if (createControlVO.getSkuPackVideoCle() == 0) {
                                newFileVoList.add(fileVo);
                            }
                        } else if (SupplierSkuFileTypeEnum.TYPE5.getCode().equals(fileVo.getType())) {
                            //判断是否回填质检报告
                            if (createControlVO.getQualityReportCle() == 0) {
                                newFileVoList.add(fileVo);
                            }
                        }
                    }
                    fileVoList = newFileVoList;
                }
            }
            infoVo.setFileList(fileVoList);
        }
    }


    /**
     * 根据旧批次id给新批次复制创建库存，禁用城市仓，文件
     *
     * @param newSku
     * @param oldId
     * @param stock
     * @return
     */
    private String addSupplierSkuOther(SupplierSku newSku, Long oldId, Integer stock) {
        //新增库存
        SupplierSkuStock skuStock = new SupplierSkuStock();
        skuStock.setSupplierSkuId(newSku.getId());
        if (stock == null) {
            SupplierSkuStockVo oldStock = supplierSkuStockMapper.selectBySupplierSkuId(oldId);
            skuStock.setStock(oldStock.getUpStock());
        } else {
            skuStock.setStock(stock);
        }
        skuStock.setUpStock(skuStock.getStock());
        supplierSkuStockMapper.insert(skuStock);

        //添加供应商商品批次的禁下单城市仓
        List<SupplierSkuDisableCityWhVo> cityWhVoList = supplierSkuDisableCityWhMapper.queryList(newSku.getSkuId(), List.of(1));
        if (cityWhVoList != null && cityWhVoList.size() > 0) {
            List<SupplierSkuDisableCityWh> cityWhList = new ArrayList<>();
            for (SupplierSkuDisableCityWhVo CityWhVo : cityWhVoList) {
                SupplierSkuDisableCityWh cityWh = new SupplierSkuDisableCityWh();
                cityWh.setSupplierSkuId(newSku.getId());
                cityWh.setCityWhId(CityWhVo.getId());
                cityWh.setCityWhCode(CityWhVo.getCityWhCode());
                cityWhList.add(cityWh);
            }
            supplierSkuDisableCityWhMapper.insertBatch(cityWhList);
        }
        //添加供应商商品批次的文件
        QuerySupplierSkuFileBo fileBo = new QuerySupplierSkuFileBo();
        fileBo.setSupplierSkuIdList(Lists.newArrayList(oldId));
        List<SupplierSkuFileVo> fileVoList = supplierSkuFileMapper.queryList(fileBo);
        //检验图片文件简介是否重新上传
        checkControl(newSku, fileVoList);
        String imgUrl = "";
        if (fileVoList != null && fileVoList.size() > 0) {
            List<SupplierSkuFile> fileList = new ArrayList<>();
            for (SupplierSkuFileVo fileVo : fileVoList) {
                SupplierSkuFile file = new SupplierSkuFile();
                file.setSupplierSpuId(newSku.getSupplierSpuId());
                file.setSupplierSkuId(newSku.getId());
                file.setType(fileVo.getType());
                file.setFileUrl(fileVo.getFileUrl());
                fileList.add(file);
                if (imgUrl.length() == 0 && SupplierSkuFileTypeEnum.TYPE1.getCode().equals(fileVo.getType())) {
                    imgUrl = fileVo.getFileUrl();
                }
            }
            supplierSkuFileMapper.insertBatch(fileList);
        }
        return imgUrl;
    }

    /**
     * 检验文件
     *
     * @param sku
     * @param fileVoList
     */
    private void checkControl(SupplierSku sku, List<SupplierSkuFileVo> fileVoList) {
        List<SpuCreateControlVO> spuCreateControlVOList = spuCreateControlMapper.queryList(sku.getSpuId());
        if (spuCreateControlVOList != null && spuCreateControlVOList.size() > 0) {
            //查询供应商商品
            SupplierSpuVo supplierSpuVo = supplierSpuMapper.selectVoById(sku.getSupplierSpuId());
            if (supplierSpuVo == null) {
                throw new ServiceException("供应商商品不存在，请重新供货");
            }
            if (supplierSpuVo.getLastClearTime() != null) {
                //计算当前时间和上次清除时间直接的天数差
                long interval = ChronoUnit.DAYS.between(supplierSpuVo.getLastClearTime(), getSaleDate(sku.getRegionWhId()));
                SpuCreateControlVO createControlVO = spuCreateControlVOList.get(0);
                //如果超过自动清除时间，且文件设置了自动清理，就不回填
                if (createControlVO.getAutoCle() > 0 && createControlVO.getAutoCle() <= interval) {
                    //判断是否回填商品简介
                    if (createControlVO.getSkuIntroduction() == 1) {
                        if (createControlVO.getSkuIntroductionCle() == 1 || StringUtils.isBlank(sku.getSnapshot())) {
                            throw new ServiceException("商品简介已清除，需要重新填写");
                        }
                    }
                    Map<Integer, List<SupplierSkuFileVo>> fileMap = new HashMap<>();
                    if (fileVoList != null && fileVoList.size() > 0) {
                        fileMap = fileVoList.stream().collect(Collectors.groupingBy(SupplierSkuFileVo::getType));
                    }
                    if (createControlVO.getSkuDetailImg() == 1) {
                        if (createControlVO.getSkuDetailImgCle() == 1 || CollectionUtil.isEmpty(fileMap.get(SupplierSkuFileTypeEnum.TYPE1.getCode()))) {
                            throw new ServiceException("商品图片已清除，需要重新上传");
                        }
                    } else if (createControlVO.getSkuVideo() == 1) {
                        if (createControlVO.getSkuVideoCle() == 1 || CollectionUtil.isEmpty(fileMap.get(SupplierSkuFileTypeEnum.TYPE2.getCode()))) {
                            throw new ServiceException("商品视频已清除，需要重新上传");
                        }
                    } else if (createControlVO.getSkuPackImg() == 1) {
                        if (createControlVO.getSkuPackImgCle() == 1 || (CollectionUtil.isEmpty(fileMap.get(SupplierSkuFileTypeEnum.TYPE3.getCode()))
                                && CollectionUtil.isEmpty(fileMap.get(SupplierSkuFileTypeEnum.TYPE31.getCode()))
                                && CollectionUtil.isEmpty(fileMap.get(SupplierSkuFileTypeEnum.TYPE32.getCode()))
                                && CollectionUtil.isEmpty(fileMap.get(SupplierSkuFileTypeEnum.TYPE33.getCode())))) {
                            throw new ServiceException("包装图片已清除，需要重新上传");
                        }
                    } else if (createControlVO.getSkuPackVideo() == 1) {
                        if (createControlVO.getSkuPackVideoCle() == 1 || CollectionUtil.isEmpty(fileMap.get(SupplierSkuFileTypeEnum.TYPE4.getCode()))) {
                            throw new ServiceException("包装视频已清除，需要重新上传");
                        }
                    } else if (createControlVO.getQualityReport() == 1) {
                        if (createControlVO.getQualityReportCle() == 1 || CollectionUtil.isEmpty(fileMap.get(SupplierSkuFileTypeEnum.TYPE5.getCode()))) {
                            throw new ServiceException("质检报告已清除，需要重新上传");
                        }
                    }
                }
            }
        }
    }

    /**
     * 根据code查询详情
     */
    @Override
    public SupplierSkuVo getBySupplierSkuCode(String code) {
        SupplierSkuVo byCode = skuMapper.getBySupplierSkuCode(code);
        // 规格转换
        if (ObjectUtil.isNotEmpty(byCode)) {
            byCode.tranSpuStandardsName();
        }
        return byCode;
    }

    @Override
    public SupplierSkuVo getByLabel(RemoteQuerySkuIdBo bo) {
        if (StrUtil.isBlank(bo.getSkuLabel())) {
            throw new ServiceException("商品码不能为空");
        }

        // 只获取正常批次，暂不考虑尾货
        bo.setBatchType(SupplierSkuBatchTypeEnum.BATCH_TYPE1.getCode());
        SupplierSkuVo byCode = skuMapper.getBySkuLabel(bo);

        // 规格转换
        if (ObjectUtil.isNotEmpty(byCode)) {
            byCode.tranSpuStandardsName();
        }

        // 兼容无label的老数据 + 批次码识别功能
        if (ObjectUtil.isEmpty(byCode)) {
            byCode = this.getBySupplierSkuCode(TemCode.getOriginalCode(bo.getSkuLabel()));
        }

        if (byCode == null) {
            throw new ServiceException("商品不存在");
        }

        // 云仓库存
        CwStockDetailVo stockDetailVo = cwStockDetailService.getBySkuId(byCode.getId());
        Integer stock = Optional.ofNullable(stockDetailVo).map(CwStockDetailVo::getOnhandQty).map(BigDecimal::intValue).orElseGet(() -> null);
        Integer lockStock = Optional.ofNullable(stockDetailVo).map(CwStockDetailVo::getAllocationQty).map(BigDecimal::intValue).orElseGet(() -> 0);
        byCode.setCloudActualStock(stock);
        byCode.setCloudLockStock(lockStock);
        if (stock != null) {
            byCode.setCloudStock(stock - lockStock);
        }

        return byCode;
    }

    private void convertSupplierSku(SupplierSku addSku) {
        addSku.setCreateCode(null);
        addSku.setCreateName(null);
        addSku.setCreateTime(null);
        addSku.setUpdateCode(null);
        addSku.setUpdateName(null);
        addSku.setUpdateTime(null);
    }

    @Override
    public List<CwStockDetailVo> listSupplierSkuStockBySupplierIds(List<Long> skuId) {
        if (CollectionUtil.isEmpty(skuId)) {
            return Collections.emptyList();
        }

        return cwStockDetailService.getBySkuIds(skuId);
    }

    @Override
    public SupplierSkuStockVo getSupplierSkuStock(Long supplierSkuId) {
        LambdaQueryWrapper<SupplierSkuStock> queryWrapper = new LambdaQueryWrapper<SupplierSkuStock>()
                .eq(SupplierSkuStock::getSupplierSkuId, supplierSkuId);
        return supplierSkuStockMapper.selectVoOne(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDown(RemoteRegionWhMsgBo bo) {
        log.keyword("batchDown-" + bo.getId()).info("{}自动下架{}批次【同步sku】", bo.getRegionWhName(), bo.getSaleDate());

        // 正常批次过了销售日全部下架
        LambdaUpdateWrapper<Sku> luw = new LambdaUpdateWrapper<>();
        luw.eq(Sku::getSaleDate, bo.getSaleDate());
        luw.eq(Sku::getRegionWhId, bo.getId());
        luw.in(Sku::getStatus, SupplierSkuStatusEnum.getCodesLessThan5());
        luw.set(Sku::getStatus, SupplierSkuStatusEnum.STATUS5.getCode());
        luw.set(Sku::getDownTime, new Date());
        //下架清空补贴金额
        luw.set(Sku::getSubsidyAmount, BigDecimal.ZERO);
        int updateNum = skuMapper.update(luw);

        log.keyword("batchDown-" + bo.getId()).info("{}自动下架{}批次【sku下架批次:{}】", bo.getRegionWhName(), bo.getSaleDate(), updateNum);


        // 隐藏已下架的尾货
        LambdaUpdateWrapper<Sku> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Sku::getSaleDate, bo.getSaleDate())
                .eq(Sku::getRegionWhId, bo.getId())
                .eq(Sku::getStatus, SupplierSkuStatusEnum.STATUS5.getCode())
                .eq(Sku::getBatchType, SupplierSkuBatchTypeEnum.BATCH_TYPE2.getCode())
                .set(Sku::getStatus, SupplierSkuStatusEnum.STATUS7.getCode())
        ;
        int hideNum = skuMapper.update(updateWrapper);

        log.keyword("batchDown-" + bo.getId()).info("{}自动下架{}批次【sku隐藏尾货:{}】", bo.getRegionWhName(), bo.getSaleDate(), hideNum);
    }


    @Override
    public List<SupplierSkuSaleNumBo> querySupplierSkuSaleNum(List<SupplierOnSaleNumBo> bo) {
        if (CollectionUtil.isEmpty(bo)) {
            return Collections.emptyList();
        }
        if (bo.stream().anyMatch(e -> Objects.isNull(e.getSupplierId()) || Objects.isNull(e.getRegionWhId()))) {
            throw new ServiceException("参数不能为空");
        }
        return skuMapper.querySupplierSkuSaleNum(bo, SupplierSkuSaleTypeEnum.SALE_TYPE2.getCode());
    }

    @Override
    public SupplierSkuSaleNumBo querySupplierSkuSaleNum(Long supplierId, Long regionWhId, Integer saleType) {
        if (ObjectUtil.isNotEmpty(supplierId) && ObjectUtil.isNotEmpty(regionWhId)) {
            Integer saleType1 = SupplierSkuSaleTypeEnum.SALE_TYPE2.getCode().equals(saleType) ? saleType : null;
            List<SupplierSkuSaleNumBo> supplierSkuSaleNumBos = skuMapper.querySupplierSkuSaleNum(Collections.singletonList(new SupplierOnSaleNumBo(supplierId, regionWhId)), saleType1);
            if (CollectionUtil.isNotEmpty(supplierSkuSaleNumBos)) {
                return supplierSkuSaleNumBos.get(0);
            }
        }
        return new SupplierSkuSaleNumBo();
    }

    @Override
    public List<RemoteSupplierSkuInfoVo> querySkuIds(List<Long> ids) {
        List<Sku> skuList = skuMapper.selectBatchIds(ids);
        List<RemoteSupplierSkuInfoVo> remoteSupplierSkuInfoVos = BeanUtil.copyToList(skuList, RemoteSupplierSkuInfoVo.class);
        if (ObjectUtil.isEmpty(remoteSupplierSkuInfoVos)) {
            return Collections.emptyList();
        }
        //设置图片
        List<Long> supplierSkuIdList = remoteSupplierSkuInfoVos.stream().map(RemoteSupplierSkuInfoVo::getSupplierSkuId).collect(Collectors.toList());
        QuerySupplierSkuFileBo fileBo = new QuerySupplierSkuFileBo();
        fileBo.setSupplierSkuIdList(supplierSkuIdList);
        fileBo.setTypeList(List.of(SupplierSkuFileTypeEnum.TYPE1.getCode()));
        List<SupplierSkuFileVo> fileVoList = supplierSkuFileMapper.queryList(fileBo);
        //处理数据
        Map<Long, SupplierSkuFileVo> fileMap = fileVoList.stream().collect(Collectors.toMap(SupplierSkuFileVo::getSupplierSkuId, Function.identity(), (v1, v2) -> v1));
        //处理出参
        List<String> areaCodes = remoteSupplierSkuInfoVos.stream().map(RemoteSupplierSkuInfoVo::getAreaCode).filter(StringUtils::isNotBlank).distinct().toList();
        Map<String, String> areaMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(areaCodes)) {
            List<RemotePubAreaVO> remotePubAreaVOs = remoteBasPubAreaService.getByCodes(areaCodes);
            areaMap = remotePubAreaVOs.stream().collect(Collectors.toMap(RemotePubAreaVO::getAreaCode, RemotePubAreaVO::getAreaAlias));
        }
        List<Long> categoryIdList = remoteSupplierSkuInfoVos.stream().map(e -> e.getCategoryId()).filter(e -> e != null && e > 0L).distinct().collect(Collectors.toList());
        List<CategoryVO> categoryList = categoryMapper.getBySonIds(categoryIdList);
        Map<Long, CategoryVO> categoryMap = categoryList.stream().collect(Collectors.toMap(CategoryVO::getSonId, Function.identity(), (v1, v2) -> v1));

        for (RemoteSupplierSkuInfoVo skuVo : remoteSupplierSkuInfoVos) {
            SupplierSkuFileVo fileVo = fileMap.get(skuVo.getSupplierSkuId());
            if (fileVo != null) {
                skuVo.setImgUrl(fileVo.getFileUrl());
            }
            // 规格转换
            skuVo.buildSpuStandardsName();
            // 区域简称
            if (ObjectUtil.isNotEmpty(skuVo.getAreaCode()) && areaMap.containsKey(skuVo.getAreaCode())) {
                skuVo.setShortProducer(areaMap.get(skuVo.getAreaCode()));
            }

            if (skuVo.getCategoryId() != null && skuVo.getCategoryId() > 0L) {
                CategoryVO categoryVO = categoryMap.get(skuVo.getCategoryId());
                if (categoryVO != null) {
                    skuVo.setCategoryIdLevel1(categoryVO.getParentId());
                    skuVo.setCategoryIdLevel2(categoryVO.getId());
                    skuVo.setCategoryCode(categoryVO.getCode());
                    skuVo.setCategoryPathName(categoryVO.getPathName());
                }
            }
        }
        return remoteSupplierSkuInfoVos;
    }

    /**
     * 根据skuId查询自定义字段
     *
     * @param skuIds
     * @return
     */
    @Override
    public List<RemoteSkuVo> getSkuCustomFieldByIds(List<Long> skuIds) {
        List<Sku> skuList = skuMapper.selectList(new LambdaQueryWrapper<Sku>().in(Sku::getId, skuIds)
                .select(Sku::getSupplierSkuId, Sku::getRelationSkuId, Sku::getId, Sku::getSkuCode));
        List<RemoteSkuVo> remoteSkuVo = BeanUtil.copyToList(skuList, RemoteSkuVo.class);
        return remoteSkuVo;
    }

    @Override
    public void batchRelationSkuId(Map<Long, Long> map) {
        if (ObjectUtil.isEmpty(map)) {
            return;
        }
        List<Long> skuIdList = new ArrayList<>(map.keySet());
        List<Sku> skuList = skuMapper.selectList(new LambdaQueryWrapper<Sku>().in(Sku::getId, skuIdList)
                .select(Sku::getId));
        if (ObjectUtil.isEmpty(skuList)) {
            return;
        }
        skuList.forEach(sku -> {
            sku.setRelationSkuId(map.get(sku.getId()));
        });
        skuMapper.updateBatchById(skuList);
    }

    @Override
    public List<Sku> listBySupplierSpuCodes(List<String> supplierSpuCodes) {
        return list(new LambdaQueryWrapper<Sku>().in(Sku::getSupplierSpuCode, supplierSpuCodes));
    }

    @Override
    public List<Sku> querySkuIdByDistribution(Long skuId) {
        //先获取一个
        Sku sku = skuMapper.selectById(skuId);
        if (sku != null) {
            //供应商，spuId，等级，规格，品牌，产地
            return skuMapper.getBySku(sku);
        }
        return new ArrayList<>();
    }

    @Override
    public TableDataInfo<RemoteSkuVo> getSkuIdBySupplier(Long supplierId, String spuName, BigDecimal price, PageQuery pageQuery) {
        Page<Sku> skuPage = skuMapper.getSkuIdBySupplier(supplierId, spuName, price, pageQuery.build());
        List<Sku> skuList = skuPage.getRecords();
        if (CollectionUtil.isEmpty(skuList)) {
            return TableDataInfo.build(new ArrayList<>());
        }
        List<Long> skuIds = skuList.stream().map(Sku::getId).collect(Collectors.toList());
        List<RemoteSupplierSkuInfoVo> remoteSupplierSkuInfoVos = querySkuIds(skuIds);
        Map<Long, RemoteSupplierSkuInfoVo> skuInfoVoMap = remoteSupplierSkuInfoVos.stream().collect(Collectors.toMap(RemoteSupplierSkuInfoVo::getId, item -> item, (v1, v2) -> v1));
        //根据skuid查询最新的价格
        List<RemoteSkuVo> skuVoList = skuList.stream().map(item -> {
            RemoteSupplierSkuInfoVo skuInfoVo = skuInfoVoMap.get(item.getId());
            return BeanUtil.copyProperties(skuInfoVo, RemoteSkuVo.class);
        }).collect(Collectors.toList());
        return TableDataInfo.build(skuVoList, skuPage.getTotal());
    }

    @Override
    public Integer getPriceCount(SupplierSkuSimplePageBo bo) {
        return skuMapper.getPriceCount(bo);
    }

    @Override
    public List<RemoteSkuVo> getCategoryMinPriceByNames(List<String> categoryName) {
        return skuMapper.getCategoryMinPriceByNames(categoryName);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long updatePriceById(UpdateSkuPriceBo updateSkuPriceBo) {
        // 参数校验
        if (ObjectUtil.isEmpty(updateSkuPriceBo.getId())) {
            throw new ServiceException("skuId不能为空");
        }
        BigDecimal updatePrice = updateSkuPriceBo.getPrice();
        BigDecimal spuGrossWeight = updateSkuPriceBo.getSpuGrossWeight();
        BigDecimal spuNetWeight = updateSkuPriceBo.getSpuNetWeight();
        if (updatePrice == null || updatePrice.compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("价格不合法");
        }
        if (spuGrossWeight == null || spuGrossWeight.compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("毛重不合法");
        }
        if (spuNetWeight == null || spuNetWeight.compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("净重不合法");
        }
        if (spuNetWeight.compareTo(spuGrossWeight) > 0) {
            throw new ServiceException(" 净重不能大于毛重");
        }
        Sku sku = baseMapper.selectById(updateSkuPriceBo.getId());
        if (sku != null) {
            sku.setPrice(updatePrice);
            sku.setSpuGrossWeight(spuGrossWeight);
            sku.setSpuNetWeight(spuNetWeight);
            // 修改sku
            baseMapper.updateById(sku);
            if (sku.getSupplierSkuId() != null) {
                SupplierSku supplierSku = supplierSkuMapper.selectById(sku.getSupplierSkuId());
                supplierSku.setPrice(updatePrice);
                supplierSku.setSpuGrossWeight(spuGrossWeight);
                supplierSku.setSpuNetWeight(spuNetWeight);
                // 修改supplierSku
                supplierSkuMapper.updateById(supplierSku);
            }
            return sku.getId();
        }
        return updateSkuPriceBo.getId();
    }

    @Override
    public TableDataInfo<SupplierSkuVo> findSkuPage(RemoteSkuQueryBo bo) {
        LambdaQueryWrapper<Sku> lqw = Wrappers.lambdaQuery();
        lqw.ge(ObjectUtil.isNotNull(bo.getUpdateStartTime()), Sku::getUpdateTime, bo.getUpdateStartTime());
        lqw.le(ObjectUtil.isNotNull(bo.getUpdateEndTime()), Sku::getUpdateTime, bo.getUpdateEndTime());
        IPage<SupplierSkuVo> page = skuMapper.selectVoPage(bo.build(), lqw);
        return TableDataInfo.build(page);
    }
}
