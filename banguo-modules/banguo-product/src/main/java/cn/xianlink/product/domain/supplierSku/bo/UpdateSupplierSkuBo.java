package cn.xianlink.product.domain.supplierSku.bo;

import cn.xianlink.common.api.enums.product.CategoryAfterTypeEnum;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.core.validate.AddGroup;
import cn.xianlink.common.core.validate.EditGroup;
import cn.xianlink.product.domain.SupplierSku;
import cn.xianlink.product.domain.SupplierSpu;
import cn.xianlink.product.domain.supplierSku.vo.SupplierSkuVo;
import cn.xianlink.product.domain.supplierSkuDisableCityWh.bo.AddSupplierSkuDisableCityWhBo;
import cn.xianlink.product.domain.supplierSkuFile.bo.AddSupplierSkuFileBo;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import io.swagger.annotations.ApiModelProperty;
import jakarta.validation.constraints.Min;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@AutoMappers({
        @AutoMapper(target = SupplierSku.class),
        @AutoMapper(target = SupplierSpu.class),
        @AutoMapper(target = SupplierSkuVo.class)
})
public class UpdateSupplierSkuBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("供应商商品销售批次id")
    private Long id;

    @ApiModelProperty("商品等级")
    private String spuGrade;

    @ApiModelProperty("商品等级描述")
    private String spuGradeDesc;

    @ApiModelProperty("商品规格")
    private String spuStandards;
//
    @ApiModelProperty("平台规格列表")
    private List<KeyValueBO> skuStandardsBoList;

    @ApiModelProperty(value = "状态,1提交/待审核,2驳回,3仅审核通过,4上架,5下架,6过季,7隐藏")
    private Integer oldStatus;

    @ApiModelProperty("产地来源：0国产 1进口")
    private Integer domestic ;

    @ApiModelProperty("产地")
    private String producer;

    @ApiModelProperty("商品毛重(斤)")
    private BigDecimal spuGrossWeight;

    @ApiModelProperty("商品净重(斤)")
    private BigDecimal spuNetWeight;

    @ApiModelProperty("业务类型，1市采, 10地采, 20基采, 30产地，40配货")
    private Integer businessType;

    @ApiModelProperty("装卸队id")
    private Long basPortageTeamId;

    @ApiModelProperty("供货总仓id")
    private Long provideRegionWhId;


    @ApiModelProperty("是否战略合作品，0为否，1为是")
    private Integer cooperation;

    @ApiModelProperty("礼盒，0为否，1为是")
    private Integer giftBox;

    @ApiModelProperty("小件，0为否，1为是")
    private Integer little;
    @ApiModelProperty("好吃，0为否，1为是")
    private Integer goodToEat;

    @ApiModelProperty("寿光蔬菜，0为否，1为是")
    private Integer shouguangVegetables;

    @ApiModelProperty("简介")
    private String snapshot;

    @ApiModelProperty("单件价格")
    private BigDecimal price;

    @ApiModelProperty("上架价格")
    private BigDecimal upPrice;

    @ApiModelProperty("库存")
    private Integer stock;
    private Integer addStock;

    @ApiModelProperty("上架库存")
    private Integer upStock;

    @ApiModelProperty("明日预测")
    private String predictionTomorrow;

    @ApiModelProperty("未来预测")
    private String predictionFuture;

    @ApiModelProperty("包装上的字")
    private String packageWord;

    @ApiModelProperty("包装容器-框，泡沫箱，袋等")
    private String packageType;

    @ApiModelProperty("最小甜度")
    private BigDecimal sweetMin;

    @ApiModelProperty("最大甜度")
    private BigDecimal sweetMax;

    @ApiModelProperty("下单倍数")
    @Min(value = 1, message = "下单倍数必须是大于等于1的正整数", groups = {AddGroup.class, EditGroup.class})
    private Integer placeOrderMultiple;

    @ApiModelProperty("最小购买数量，为-1不限制")
    private Integer buyMin;

    @ApiModelProperty("最大购买数量，为-1不限制")
    private Integer buyMax;

    /**
     * @see CategoryAfterTypeEnum
     */
    @ApiModelProperty("售后规则：0无售后、1正常售后、2库管验货")
    private Integer afterSaleType;

    @ApiModelProperty("售后天数：0无售后，其它暂时写死 1")
    private Integer afterSaleDay;

    @ApiModelProperty("是否免检，0表示不是免检,1表示免检")
    private Integer isCheck;

    @ApiModelProperty("免赔情况")
    private String deductibleSituation;

    @ApiModelProperty("可申请售后说明")
    private String afterSaleExplain;

    @ApiModelProperty("入口隐藏，0隐藏，1不隐藏")
    private Integer entranceHide;

    private String waterfall;

    @ApiModelProperty("同一批次批次识别码, 32位")
    private String skuLabel;

    @ApiModelProperty("采购员code")
    private String buyerCode;

    @ApiModelProperty("采购员name")
    private String buyerName;

    @ApiModelProperty("新增的禁止下单城市仓ID集合")
    private List<AddSupplierSkuDisableCityWhBo> addCityWhList;

    @ApiModelProperty("新增的禁止下单城市仓ID集合")
    private List<Long> delCityWhList;

    @ApiModelProperty("新增的文件")
    private List<AddSupplierSkuFileBo> addFileList;

    @ApiModelProperty("删除的文件")
    private List<Long> delFileList;

    @ApiModelProperty(value = "操作步骤", hidden = true)
    private String operate;

    @ApiModelProperty(value = "供应商小程序修改，为true，总仓修改为false", hidden = true)
    private boolean sup;

    @ApiModelProperty("区域编码")
    private String areaCode;

    @ApiModelProperty("品牌")
    private String brand;

    @ApiModelProperty("产地简称")
    private String shortProducer;

    @ApiModelProperty("是否送货审核 - 0否df 1是")
    private Integer hasDeliveryAudit;

    @ApiModelProperty("通过送货审核 - 0否df 1是")
    private Integer passDeliveryAudit;

    @ApiModelProperty("集采是否隐藏 1-是")
    private Integer isSaleHide;

    @ApiModelProperty("是否特价商品，0为否，1为是")
    private Integer bargain;
    private BigDecimal bargainRate;
    private BigDecimal bargainPrice;

    @ApiModelProperty("市场价")
    private BigDecimal marketPrice;

    @ApiModelProperty("补贴金额")
    private BigDecimal subsidyAmount;

    @ApiModelProperty("客户门店类型")
    private String customerStoreTypes;

    @ApiModelProperty("行情预测类型")
    private String marketForecastType;

    public void buildSkuStandardsSortJoin() {
        String listSortAndJoin = KeyValueBO.getListSortAndJoin(skuStandardsBoList);
        if(StringUtils.isNotBlank(listSortAndJoin)) {
            spuStandards = listSortAndJoin;
        }
    }
}
