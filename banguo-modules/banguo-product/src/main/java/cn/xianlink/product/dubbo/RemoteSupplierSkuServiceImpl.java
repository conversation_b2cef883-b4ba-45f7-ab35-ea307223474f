package cn.xianlink.product.dubbo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.xianlink.basic.api.RemoteBasPubAreaService;
import cn.xianlink.basic.api.domain.vo.RemotePubAreaVO;
import cn.xianlink.common.api.bo.RemoteRegionWhMsgBo;
import cn.xianlink.common.api.enums.product.SupplierSkuBusinessTypeEnum;
import cn.xianlink.common.api.enums.product.SupplierSkuStatusEnum;
import cn.xianlink.common.api.util.SaleDateUtil;
import cn.xianlink.common.api.vo.RemoteSupplierSkuFileVo;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.order.api.constant.ActivityRecordTypeEnum;
import cn.xianlink.common.log.enums.BusinessType;
import cn.xianlink.product.api.RemoteSupplierSkuService;
import cn.xianlink.product.api.domain.bo.*;
import cn.xianlink.product.api.domain.vo.*;
import cn.xianlink.product.config.ProductProperties;
import cn.xianlink.product.domain.Sku;
import cn.xianlink.product.domain.SupplierSku;
import cn.xianlink.product.domain.supplierSku.bo.SupplierSkuSimplePageBo;
import cn.xianlink.product.domain.supplierSku.vo.QueryRefundDifferenceSkuVo;
import cn.xianlink.product.domain.supplierSku.vo.SupplierSkuInfoVo;
import cn.xianlink.product.domain.supplierSku.vo.SupplierSkuVo;
import cn.xianlink.product.domain.supplierSkuFile.vo.SupplierSkuFileVo;
import cn.xianlink.product.mapper.SkuMapper;
import cn.xianlink.product.mapper.SupplierSkuMapper;
import cn.xianlink.product.mq.producer.ClearStockProducer;
import cn.xianlink.product.mq.producer.DownSkuProducer;
import cn.xianlink.product.service.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
@DubboService
@CustomLog
public class RemoteSupplierSkuServiceImpl implements RemoteSupplierSkuService {

    private final ISupplierSkuService iSupplierSkuService;

    private final SupplierSkuMapper supplierSkuMapper;

    private final SkuMapper skuMapper;

    private final ISkuService skuService;

    private final ISupplierSkuStockService iSupplierSkuStockService;

    private final ISupplierSkuDisableCityWhService iSupplierSkuDisableCityWhService;

    private final ISupplierSkuFileService iSupplierSkuFileService;
    private final ProductProperties productProperties;

    private final ClearStockProducer clearStockProducer;

    private final DownSkuProducer downSkuProducer;

    @DubboReference
    private transient RemoteBasPubAreaService remoteBasPubAreaService;

    /**
     * 根据条件查询销售批次详情
     * @param bo
     * @return
     */
    @Override
    public List<RemoteSupplierSkuInfoVo> queryInfoList(RemoteQueryInfoListBo bo) {
        //找不到不加条件的调用，全部条件判空，全部为空直接返回
        boolean flag = StringUtils.isEmpty(bo.getSpuName()) && Objects.isNull(bo.getPassAudit()) && CollectionUtil.isEmpty(bo.getSupplierSkuIdList());
        if (flag){
            return CollectionUtil.newArrayList();
        }
        return iSupplierSkuService.queryInfoList(bo);
    }

    @Override
    public List<RemoteSupplierSkuInfoVo> querySimpleInfoListHasStock(List<Long> supplierSkuIdList) {
        List<SupplierSkuVo> supplierSkuVos = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(supplierSkuIdList)) {
            supplierSkuVos = supplierSkuMapper.querySimpleInfoListHasStock(supplierSkuIdList);
        }
        return BeanUtil.copyToList(supplierSkuVos, RemoteSupplierSkuInfoVo.class);
    }

    @Override
    public List<RemoteSupplierSkuInfoVo> querySimpleInfoList(List<Long> supplierSkuIdList) {
        if(ObjectUtil.isEmpty(supplierSkuIdList)){
            return CollectionUtil.newArrayList();
        }
        List<SupplierSkuVo> supplierSkuVos = supplierSkuMapper.selectVoBatchIds(supplierSkuIdList);
        return BeanUtil.copyToList(supplierSkuVos, RemoteSupplierSkuInfoVo.class);
    }

    @Override
    public List<RemoteSupplierSkuInfoVo> queryDeliveryAuditList(RemoteDeliveryAuditBo bo) {
        SupplierSkuSimplePageBo queryBo = new SupplierSkuSimplePageBo();
        queryBo.setRegionWhId(bo.getRegionWhId());
        queryBo.setSaleDateStart(bo.getSaleDateStart());
        queryBo.setSaleDateEnd(bo.getSaleDateEnd());
        List<SupplierSkuVo> supplierSkuVos = iSupplierSkuService.queryDeliveryAuditList(queryBo);
        return BeanUtil.copyToList(supplierSkuVos, RemoteSupplierSkuInfoVo.class);
    };

    /**
     * 根据条件查询销售批次id
     * @param bo
     * @return
     */
    @Override
    public List<Long> querySkuIdList(RemoteQuerySkuIdBo bo) {
        return iSupplierSkuService.querySkuIdList(bo);
    }

    @Override
    public List<RemoteSupplierSkuFundsInfoVo> queryInfoListByFunds(List<Long> supplierSkuIds) {
        return iSupplierSkuService.queryInfoListByFunds(supplierSkuIds);
    }

    /**
     * 根据code查询供应商批次商品
     */
    @Override
    public RemoteSupplierSkuInfoVo getByCode(String code) {
        return iSupplierSkuService.getByCode(code);
    }

    @Override
    public RemoteSupplierSkuInfoVo getByLabel(RemoteQuerySkuIdBo bo) {
        return iSupplierSkuService.getByLabel(bo);
    }

    @Override
    public List<RemoteSupplierSkuInfoVo> getByLabelList(RemoteQuerySkuIdBo bo) {
        return iSupplierSkuService.getByLabelList(bo);
    }

    /**
     * 根据id查询供应商批次商品
     */
    @Override
    public RemoteSupplierSkuInfoVo getById(Long id) {
        RemoteSupplierSkuInfoVo vo = new RemoteSupplierSkuInfoVo();
        SupplierSkuInfoVo supplierSkuInfoVo = iSupplierSkuService.queryById(id);
        BeanUtil.copyProperties(supplierSkuInfoVo,vo);
        if (CollectionUtil.isNotEmpty(supplierSkuInfoVo.getFileList())){
            List<RemoteSupplierSkuFileVo> fileList = new ArrayList<>();
            for (SupplierSkuFileVo fileVo : supplierSkuInfoVo.getFileList()){
                RemoteSupplierSkuFileVo skuFileVo = new RemoteSupplierSkuFileVo();
                BeanUtil.copyProperties(fileVo, skuFileVo);
                fileList.add(skuFileVo);
            }
            vo.setFileList(fileList);
        }
        return vo;
    }

    @Override
    public RemoteSupplierSkuInfoVo getSimpleById(Long id) {
        RemoteSupplierSkuInfoVo vo = new RemoteSupplierSkuInfoVo();
        SupplierSkuVo supplierSkuInfoVo = iSupplierSkuService.querySimpleById(id);
        BeanUtil.copyProperties(supplierSkuInfoVo,vo);
        return vo;
    }

    /**
     * 城市仓多货，生成地采批次
     * @param bo
     * @return
     */
    @Override
    public Long addLand(RemoteAddLandBo bo) {
        return iSupplierSkuService.addLand(bo);
    }

    /**
     * 批量更新库存
     *
     * @param stockList
     */
    @Override
    public List<RemoteUpdateStockBo>  batchUpdateStock(List<RemoteUpdateStockBo> stockList,String type) {
        return iSupplierSkuStockService.batchUpdateStock(stockList,type);
    }

    @Override
    public List<RemoteSupplierSkuStockVo> listSupplierSkuStockBySupplierIds(List<Long> supplierSkuIds) {
        List<RemoteSupplierSkuStockVo> list = iSupplierSkuService.listSupplierSkuStockBySupplierIds(supplierSkuIds);
        return list;
    }

    @Override
    public List<RemoteSupplierDisableCityWhVo> listBySupplierSkuIds(List<Long> skuIds) {
        return iSupplierSkuDisableCityWhService.listBySupplierSkuIds(skuIds);
    }

    /**
     * 查询需要差额退款的供应商商品销售批次
     * @return
     */
    @Override
    public List<RemoteRefundDifferenceSkuVo> queryRefundDifferenceSku(LocalDate saleDate, List<Long> supplierSkuIdList) {
        List<QueryRefundDifferenceSkuVo> voList = iSupplierSkuService.queryRefundDifferenceSku(saleDate, supplierSkuIdList);
        if(voList != null && voList.size() > 0) {
            return BeanUtil.copyToList(voList, RemoteRefundDifferenceSkuVo.class);
        }
        return Collections.emptyList();
    }

    /**
     * 根据供应商商品code查询供应商批次商品(最近15天)
     */
    @Override
    public List<RemoteSupplierSkuInfoVo> getBySpuCode(String code) {
        return iSupplierSkuService.getBySpuCode(code);
    }

    /**
     * 根据总仓id查询供应商今日供货商品数量
     * @param bo
     * @return
     */
    @Override
    public List<RemoteQuerySupplierDeliverVo> querySupplierDeliver(RemoteQuerySupplierDeliverBo bo) {
        return iSupplierSkuService.querySupplierDeliver(bo);
    }

    /**
     * 查询销售批次商品文件
     */
    @Override
    public List<RemoteSupplierSkuFileVo> querySupplierSkuFile(RemoteQuerySupplierSkuFileBo bo) {
        return iSupplierSkuFileService.queryList(bo);
    }

    /**
     * 清除库存发送消息
     * @param bo
     */
    @Override
    public void sendClearStockMsg(RemoteRegionWhMsgBo bo) {
        clearStockProducer.send(bo);
    }

    /**
     * 清除库存发送消息
     * @param bo
     */
    @Override
    public void sendDownSkuMsg(RemoteRegionWhMsgBo bo) {
        downSkuProducer.send(bo);
    }

    /**
     * 判断总仓id，是否要查询旧系统数据源
     */
    @Override
    public  boolean isQueryTiaoguoshi(Long regionWhId) {
        return productProperties.isQueryTiaoguoshi(regionWhId);
    }

    @Override
    public boolean hasSupplierDeptSaleSku(Long supplierDeptId) {
        List<Integer> saleStatusList = Arrays.asList(SupplierSkuStatusEnum.STATUS4.getCode(), SupplierSkuStatusEnum.STATUS5.getCode());
        return iSupplierSkuService.hasSupplierDeptSku(supplierDeptId, saleStatusList);
    }

    @Override
    public List<Long> querySupplierSkuIdGroup(RemoteQuerySkuIdsBo bo) {
        return iSupplierSkuService.querySupplierSkuIdGroup(bo);
    }

    /**
     * 查询最新的批次id
     * @param skuIds
     * @return
     */
    @Override
    public List<Long> queryNewSupplierSkuIds(List<Long> skuIds) {
        return skuService.queryNewSupplierSkuIds(skuIds);
    }

    @Override
    public List<RemoteSupplierSkuInfoVo> querySkuIds(List<Long> ids) {
        return skuService.querySkuIds(ids);
    }

    @Override
    public List<RemoteSupplierSkuStockVo> queryYunStock(List<Long> supplierSkuIds) {
        if(CollectionUtil.isEmpty(supplierSkuIds)){
            return Collections.emptyList();
        }
        return iSupplierSkuService.queryYunStock(supplierSkuIds);
    }

    @Override
    public Map<Long, String> getSkuCombinationName(List<Long> ids) {
        Map<Long, String> map = new HashMap<>();
        List<RemoteSupplierSkuInfoVo> skuList = iSupplierSkuService.getByIds(ids);
        for (RemoteSupplierSkuInfoVo skuInfo : skuList) {
            String brand = StrUtil.isNotBlank(skuInfo.getBrand()) ? skuInfo.getBrand() + "-":"";
            String producer = "";
            //产地根据-分割取最后一个
            if( StrUtil.isNotBlank(skuInfo.getShortProducer())) {
                producer = skuInfo.getShortProducer() + "-";
            }else if (StrUtil.isNotBlank(skuInfo.getProducer())) {
                String[] split = skuInfo.getProducer().split(StrPool.DASHED);
                producer = split[split.length-1] + "-";
            }
            String name;
            if ("配货商品".equals(skuInfo.getSpuName())){
                name = brand + producer + skuInfo.getSpuName();
            }else {
                name = brand + producer + skuInfo.getSpuName() + "-" + skuInfo.getSpuStandards();
            }
            map.put(skuInfo.getId(), name);
        }
        return map;
    }

    @Override
    public Map<Long, String> getSkuProductName(List<Long> ids) {
        Map<Long, String> map = new HashMap<>();
        List<RemoteSupplierSkuInfoVo> skuList = iSupplierSkuService.getByIds(ids);
        if(ObjectUtil.isEmpty(skuList)){
            return Collections.emptyMap();
        }
        List<String> areaCodes = skuList.stream().map(RemoteSupplierSkuInfoVo::getAreaCode).distinct().toList();
        List<RemotePubAreaVO> remotePubAreaVoList = remoteBasPubAreaService.getByCodes(areaCodes);
        Map<String, String> areaAliasMap = remotePubAreaVoList.stream().collect(Collectors.toMap(RemotePubAreaVO::getAreaCode, RemotePubAreaVO::getAreaAlias, (o1, o2) -> o1));
        for (RemoteSupplierSkuInfoVo skuInfo : skuList) {
            String brand = StrUtil.isNotBlank(skuInfo.getBrand()) ? skuInfo.getBrand() + "-":"";
            // 区域简称
            String shortProducer = areaAliasMap.get(skuInfo.getAreaCode());
            String producer = "";
            //产地按顺序取
            if( StrUtil.isNotBlank(skuInfo.getShortProducer())) {
                producer = skuInfo.getShortProducer() + "-";
            }else if( StrUtil.isNotBlank(shortProducer)) {
                producer = shortProducer + "-";
            } else if (StrUtil.isNotBlank(skuInfo.getProducer())) {
                //产地根据-分割取最后一个
                String[] split = skuInfo.getProducer().split(StrPool.DASHED);
                producer = split[split.length-1] + "-";
            }
            String name;
            if ("配货商品".equals(skuInfo.getSpuName())){
                name = brand + producer + skuInfo.getSpuName();
            }else {
                name = brand + producer + skuInfo.getSpuName() + "-" + skuInfo.getSpuStandards();
            }
            map.put(skuInfo.getId(), name);
        }
        return map;
    }

    @Override
    public List<Long> getIdsByFullName(LocalDate saleDate, String fullName, List<String> filterFields) {
      return iSupplierSkuService.getIdsByFullName(saleDate, fullName, filterFields);
    }

    /**
     * 订单回滚补偿-回退库存
     * @param orderCode 订单号
     * @param cancelStock 是否回退上架库存
     */
    @Override
    public void compensateStock(String orderCode, boolean cancelStock) {
        iSupplierSkuStockService.compensateStock(orderCode, cancelStock);
    }

    @Override
    public Long selectUpSkuCount(LocalDate saleDate, Long regionWhId, String salesTimeEnd, List<String> buyerList) {
        LocalDate saleDateToday = SaleDateUtil.getSaleDate(salesTimeEnd);
        LambdaQueryWrapper<SupplierSku> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SupplierSku::getSaleDate, saleDate)
                .eq(SupplierSku::getRegionWhId, regionWhId)
                .ne(SupplierSku::getBusinessType, SupplierSkuBusinessTypeEnum.BUSINESS_TYPE40.getCode())
                .isNotNull(SupplierSku::getUpTime);
        if (CollectionUtil.isNotEmpty(buyerList)){
            lqw.in(SupplierSku::getBuyerCode, buyerList);
        }
        if (saleDate != null && !saleDate.equals(saleDateToday)) {
            lqw.apply("(down_time is null or TIMESTAMPDIFF(MINUTE, up_time, down_time) > {0})", 30);
        }
        List<SupplierSkuVo> supplierSkuVos = supplierSkuMapper.selectVoList(lqw);
        if (CollectionUtil.isNotEmpty(supplierSkuVos) && !supplierSkuVos.isEmpty()) {
            return Long.valueOf(supplierSkuVos.size());
        }
        return null;
    }

    /**
     * 查询商品信息列表
     * @param bo
     * @return
     */
    @Override
    public List<RemoteSupplierSkuInfoVo> querySkuInfoList(RemoteQueryInfoBo bo) {
        LambdaQueryWrapper<SupplierSku> lqw = Wrappers.lambdaQuery(SupplierSku.class);
        lqw.like(ObjectUtil.isNotEmpty(bo.getSpuName()),SupplierSku::getSpuName, bo.getSpuName());
        if(CollUtil.isNotEmpty(bo.getCategoryIdList())) {
            lqw.and(w ->
                    w.in(CollUtil.isNotEmpty(bo.getCategoryIdList()), SupplierSku::getCategoryId, bo.getCategoryIdList())
                            .or()
                            .in(CollUtil.isNotEmpty(bo.getCategoryIdList()), SupplierSku::getCategoryIdLevel2, bo.getCategoryIdList())
            );
        }
        PageQuery pageQuery = new PageQuery();
        pageQuery.setPageSize(1000);
        Page<SupplierSku> supplierSkuPage = supplierSkuMapper.selectPage(pageQuery.build(), lqw);
        if (supplierSkuPage == null || CollectionUtils.isEmpty(supplierSkuPage.getRecords())) {
            return new ArrayList<>();
        }
        List<SupplierSku> supplierSkuVos = supplierSkuPage.getRecords();
        return BeanUtil.copyToList(supplierSkuVos, RemoteSupplierSkuInfoVo.class);
    }

    /**
     * 限时折扣批量降价商品 --总仓维度
     */
    @Override
    public Integer batchUpdateDownPrice(List<RemoteDiscountSkuSearchBo> searchBo) {
        log.keyword("batchUpdateDownPrice").info("开始执行限时折扣批量降价商品,入参{}",searchBo);
        int count = 0;
        int pageNum = 1;
        int pageSize = 500;
        List<RemoteSupplierSkuInfoVo> skuList;
        Integer minCount = searchBo.stream().map(RemoteDiscountSkuSearchBo::getCopySold).min(Integer::compareTo).orElse(1);
        RemoteDiscountSkuSearchBo bo = new RemoteDiscountSkuSearchBo();
        bo.setCopySold(minCount);
        bo.setSaleDate(searchBo.get(0).getSaleDate());
        bo.setRegionId(searchBo.get(0).getRegionId());
        bo.setSaleType(productProperties.getActivityCheckSaleType());
        do {
            // 设置分页参数
            PageQuery pageQuery = new PageQuery();
            pageQuery.setPageNum(pageNum);
            pageQuery.setPageSize(pageSize);

            // 查询当前页的商品数据
            skuList = supplierSkuMapper.querySaleLowSku(bo, pageQuery.build());
            List<SupplierSku> supplierSkuList = new ArrayList<>();
            // 如果当前页有数据，则处理数据
            if (CollectionUtil.isNotEmpty(skuList)) {
                for (RemoteSupplierSkuInfoVo sku : skuList){
                    SupplierSku supplierSku = new SupplierSku();
                    supplierSku.setId(sku.getId());
                    supplierSku.setSkuId(sku.getSkuId());
                    BigDecimal rate = BigDecimal.ONE;
                    BigDecimal maxAmount = BigDecimal.ZERO;
                    for (RemoteDiscountSkuSearchBo search : searchBo){
                        //校验活动
                        if (check(sku, search)) {continue;}
                        if (search.getRate().compareTo(rate) < 0) {
                            rate = search.getRate();
                            maxAmount = search.getMaxDiscountAmount();
                        }
                    }
                    if (rate.compareTo(BigDecimal.ONE) < 0) {
                        log.keyword("batchUpdateDownPrice").info("商品id{},现价{},已打折降价{}", sku.getId(), sku.getPrice(), sku.getDiscountPrice());
                        //原价*折扣
                        BigDecimal price = (sku.getPrice().subtract(sku.getSubsidyAmount()).add(sku.getDiscountPrice())).multiply(rate);
                        //原价-打折后金额 得出减价金额
                        BigDecimal sub = (sku.getPrice().subtract(sku.getSubsidyAmount()).add(sku.getDiscountPrice()).subtract(price)).compareTo(maxAmount) > 0 ? maxAmount
                                : sku.getPrice().add(sku.getDiscountPrice()).subtract(price).subtract(sku.getSubsidyAmount());
                        //改价金额比已改价金额低或者改价后金额比现价高，不处理
                        if (sub.compareTo(sku.getDiscountPrice()) == 0 || price.compareTo(sku.getPrice().subtract(sku.getSubsidyAmount()))>0) {
                            continue;
                        }
                        //现价=原价-减价金额
                        price = sku.getPrice().add(sku.getDiscountPrice()).subtract(sku.getSubsidyAmount()).subtract(sub);
                        supplierSku.setDiscountPrice(sub);
                        supplierSku.setPrice(price);
                        supplierSkuList.add(supplierSku);
                    }
                }
            }
            if (CollectionUtil.isNotEmpty(supplierSkuList)){
                ListUtil.split(supplierSkuList, 10).forEach(this::updateSupplierSkuBatch);
            }
            count += supplierSkuList.size();
            // 页码递增，准备查询下一页
            pageNum++;
        } while (CollectionUtil.isNotEmpty(skuList));
        return count;
    }

    /**
     * 查询活动需要的商品信息
     * @param skuIds 供应商商品id
     */
    @Override
    public List<RemoteSkuInfoForActivityVo> queryActivityInfoList(List<Long> skuIds) {
        Integer saleType = productProperties.getActivityCheckSaleType();
        return supplierSkuMapper.queryActivityInfoList(skuIds, saleType);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateSupplierSkuBatch(List<SupplierSku> supplierSkuList) {
        try {
            for (SupplierSku supplierSku : supplierSkuList){
                supplierSkuMapper.updateById(supplierSku);
                Sku sku = new Sku();
                sku.setId(supplierSku.getSkuId());
                sku.setPrice(supplierSku.getPrice());
                sku.setDiscountPrice(supplierSku.getDiscountPrice());
                skuMapper.updateById(sku);
            }
        }catch (Exception e){
            log.keyword("batchUpdateDownPrice").info("限时折扣批量降价商品更新异常{}",e.getMessage());
        }

    }

    private static boolean check(RemoteSupplierSkuInfoVo sku, RemoteDiscountSkuSearchBo search) {
        if (search.getCopySold() < sku.getSold() + sku.getLockStock()){
            return true;
        }
        if (CollectionUtil.isNotEmpty(search.getSpuIds()) && !search.getSpuIds().contains(sku.getSpuId())){
            return true;
        }
        if (CollectionUtil.isNotEmpty(search.getSkuIds()) && !search.getSkuIds().contains(sku.getSkuId())){
            return true;
        }
        if (CollectionUtil.isNotEmpty(search.getCategoryIds())){
            if (!search.getCategoryIds().contains(sku.getCategoryId())) {
                return true;
            }
            if (!search.getCategoryIds().contains(sku.getCategoryIdLevel1())) {
                return true;
            }
            if (!search.getCategoryIds().contains(sku.getCategoryIdLevel2())) {
                return true;
            }
        }
        if (CollectionUtil.isNotEmpty(search.getRollSpuIds()) && search.getRollSpuIds().contains(sku.getSpuId())){
            return true;
        }
        if (CollectionUtil.isNotEmpty(search.getRollSkuIds()) && search.getRollSkuIds().contains(sku.getSkuId())){
            return true;
        }
        if (CollectionUtil.isNotEmpty(search.getRollCategoryIds())){
            if (search.getRollCategoryIds().contains(sku.getCategoryId())) {
                return true;
            }
            if (search.getRollCategoryIds().contains(sku.getCategoryIdLevel1())) {
                return true;
            }
            if (search.getRollCategoryIds().contains(sku.getCategoryIdLevel2())) {
                return true;
            }
        }
        return false;
    }
}
