package cn.xianlink.product.mapper;

import cn.xianlink.common.mybatis.core.mapper.BaseMapperPlus;
import cn.xianlink.product.api.domain.bo.RemoteQuerySkuIdBo;
import cn.xianlink.product.api.domain.bo.RemoteSkuQueryBo;
import cn.xianlink.product.api.domain.bo.SupplierOnSaleNumBo;
import cn.xianlink.product.api.domain.vo.RemoteSkuVo;
import cn.xianlink.product.api.domain.vo.RemoteSupplierSkuInfoVo;
import cn.xianlink.product.domain.Sku;
import cn.xianlink.product.api.domain.bo.SupplierSkuSaleNumBo;
import cn.xianlink.product.domain.supplierSku.bo.*;
import cn.xianlink.product.domain.supplierSku.vo.SupplierSkuVo;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * 供应商销售批次商品Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
public interface SkuMapper extends BaseMapperPlus<Sku, SupplierSkuVo> {


    /**
     * 查询供应商商品销售批次最后供货批次
     * @param bo
     * @return
     */
    Page<SupplierSkuVo> queryLastList(@Param("bo") QueryLastListBo bo, @Param("page") Page<SupplierSkuVo> page);

    /**
     * 查询供应商商品销售批次最后供货批次
     * @param bo
     * @return
     */
    List<SupplierSkuVo> queryLastList(@Param("bo") QueryLastListBo bo);



    /**
     * 根据供应商商品销售批次ID获取供应商商品销售批次详细信息
     * @param id
     * @return
     */
    SupplierSkuVo queryById(@Param("id") Long id);

    SupplierSkuVo querySupplierSkuById(@Param("id") Long id);


    /**
     * 根据code查询销售批次详情
     */
    SupplierSkuVo getBySupplierSkuCode(@Param("code") String code);


    SupplierSkuVo getBySkuLabel(@Param("bo") RemoteQuerySkuIdBo bo);


    /**
     * 检验这个供应商今天能不能给总仓供这个货
     * @param bo
     * @return
     */
    SupplierSkuVo checkSupplierSku(@Param("bo") CheckSupplierSkuBo bo);

    int checkSupplierSkuNum(@Param("bo") CheckSupplierSkuBo bo);

    List<Long> getSameSupplierSkuIdList(@Param("bo") CheckSupplierSkuBo bo);

    /**
     * 根据批次唯一性统一隐藏商品
     * @param bo
     * @return
     */
    int updateStatusHideByOnly(@Param("bo") CheckSupplierSkuBo bo);

    /**
     * 根据商品唯一性更新商品标签
     * @param bo
     * @return
     */
    int updateLabelByOnly(@Param("bo") CheckSupplierSkuBo bo);

    /**
     * 获取一条指定状态和子供应商（档口）的商品id
     * @param supplierDeptId
     * @param statusList
     * @return
     */
    Long getFirstIdByDeptAndStatus(@Param("supplierDeptId")Long supplierDeptId, @Param("statusList") List<Integer> statusList);

    /**
     * 根据销售日期和label搜索
     * @param bo
     * @return
     */
    List<RemoteSupplierSkuInfoVo> listBySkuLabel(@Param("bo") RemoteQuerySkuIdBo bo);

    int syncSold(@Param("supplierSkuIds") List<Long> supplierSkuIds);


    List<SupplierSkuSaleNumBo> querySupplierSkuSaleNum(@Param("bo") List<SupplierOnSaleNumBo> bo, @Param("saleType") Integer saleType);

    /**
     * 根据一个sku查询相同属性的skuId集合
     */
    List<Sku> getBySku(@Param("sku") Sku sku);

    /**
     * 根据供应商id查询相同属性的skuId集合
     */
    Page<Sku> getSkuIdBySupplier(@Param("supplierId") Long supplierId,
                                 @Param("spuName") String spuName,
                                 @Param("price") BigDecimal price,
                                 @Param("page") Page<Sku> page);

    Integer getPriceCount(@Param("bo") SupplierSkuSimplePageBo bo);

    List<RemoteSkuVo> getCategoryMinPriceByNames(@Param("categoryNames") List<String> categoryNames);
}