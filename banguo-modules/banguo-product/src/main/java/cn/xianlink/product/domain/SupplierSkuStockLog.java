package cn.xianlink.product.domain;
import cn.xianlink.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


/**
 * <AUTHOR>
 * @description: 供应商商品销售批次库存修改记录对象
 * @date 2025/6/23 11:45
 */
@Data
@TableName("supplier_sku_stock_log")
public class SupplierSkuStockLog  extends TenantEntity {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 订单总表主键
     */
    private Long orderId;

    /**
     * 订单总表唯一编码
     */
    private String orderCode;

    /**
     * 订单项主键
     */
    private Long orderItemId;

    /**
     * 供应商商品销售批次id
     */
    private Long supplierSkuId;

    /**
     * 库存扣减类型
     */
    private String type;

    /**
     * 覆盖上架库存
     */
    private Integer stock;

    /**
     * 操作上架库存
     */
    private Integer backStock;

    /**
     * 操作锁定库存
     */
    private Integer lockStock;

    /**
     * 操作已售库存
     */
    private Integer sold;

    /**
     * 删除标志（0代表存在，存入这条数据的主键代表删除）
     */
    @TableLogic(delval = "id")
    private Long delFlag;
}