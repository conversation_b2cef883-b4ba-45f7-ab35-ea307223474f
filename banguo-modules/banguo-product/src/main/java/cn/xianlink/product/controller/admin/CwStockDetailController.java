package cn.xianlink.product.controller.admin;

import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.excel.utils.ExcelUtil;
import cn.xianlink.common.log.annotation.Log;
import cn.xianlink.common.log.enums.BusinessType;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.product.domain.bo.CwStockDetailBo;
import cn.xianlink.product.domain.vo.CwStockDetailVo;
import cn.xianlink.product.domain.vo.CwStockTotalVo;
import cn.xianlink.product.service.ICwStockDetailService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 云仓库存
 * 前端访问路由地址为:/banguo-product/stockDetail
 *
 * <AUTHOR>
 * @date 2024-12-09
 * @folder 般果管理中心/云仓/云仓库存
 */
@Validated
@RequiredArgsConstructor
@RequestMapping("/product/admin/stockDetail")
@RestController("adminCwStockDetailController")
public class CwStockDetailController extends BaseController {

    private final ICwStockDetailService cwStockDetailService;

    /**
     * 查询云仓库存列表
     */
    @PostMapping("/list")
    public R<TableDataInfo<CwStockDetailVo>> list(@RequestBody CwStockDetailBo bo) {
        return R.ok(cwStockDetailService.queryPageList(bo));
    }

    /**
     * 合计库存
     */
    @GetMapping("/total_count")
    public R<CwStockTotalVo> totalCount(CwStockDetailBo bo) {
        return R.ok(cwStockDetailService.totalCount(bo));
    }

    /**
     * 导出云仓库存列表
     */
    @Log(title = "云仓库存", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(CwStockDetailBo bo, HttpServletResponse response) {
        List<CwStockDetailVo> list = cwStockDetailService.queryList(bo);
        ExcelUtil.exportExcel(list, "云仓库存", CwStockDetailVo.class, response);
    }

}
