package cn.xianlink.product.domain.supplierSku.bo;

import cn.xianlink.common.core.validate.AddGroup;
import cn.xianlink.common.core.validate.EditGroup;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import io.github.linpeilie.annotations.AutoMapper;
import io.swagger.annotations.ApiModelProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
@AutoMapper(target = SupplierSkuPageBo.class)
public class QuerySalePageBo extends PageQuery {

    @ApiModelProperty("二级类目ID")
    private List<Long> categoryIdList;

    @ApiModelProperty("平台商品名称(供应商的商品名称就是平台商品名称)")
    private String spuName;

    @ApiModelProperty("计划提货天数")
    private Integer planExtractDay;

    @ApiModelProperty("供应商id")
    private Long supplierId;

    @ApiModelProperty("档口ID")
    private Long supplierDeptId;

    @ApiModelProperty("业务类型，1市采预定，2市采代卖，3基采预定，4基采代卖，5地采预定，6地采代卖，7拼车预定，8拼车代卖")
    private List<Integer> businessTypeList;

    @ApiModelProperty("优惠类型")
    private Integer preferential;

    @NotNull(message = "城市仓id不能为空", groups = {AddGroup.class, EditGroup.class})
    @ApiModelProperty("城市仓id")
    private Long cityWhId;

    @ApiModelProperty("提货点id")
    private Long placeId;

    @ApiModelProperty("总仓ID集合")
    private List<Long> regionWhIdList;

    @ApiModelProperty("入口隐藏，1隐藏，2不隐藏")
    private Integer entranceHide;

    @ApiModelProperty(value = "状态(1提交/待审核,2驳回,3仅审核通过,4上架,5下架,6过季,7隐藏)", hidden = true)
    private Integer status;

    @ApiModelProperty(value = "三级类目ID集合", hidden = true)
    private List<Long> categoryThreeIdList;

    @ApiModelProperty(value = "批次id", hidden = true)
    private List<Long> notInSupplierSkuIdList;

    @ApiModelProperty("是否战略合作品，0为否，1为是")
    private Integer cooperation;

    @ApiModelProperty("是否特价商品，0为否，1为是")
    private Integer bargain;

    @ApiModelProperty("礼盒，0为否，1为是")
    private Integer giftBox;

    @ApiModelProperty("小件，0为否，1为是")
    private Integer little;

    @ApiModelProperty("好吃，0为否，1为是")
    private Integer goodToEat;

    @ApiModelProperty("寿光蔬菜，0为否，1为是")
    private Integer shouguangVegetables;

    @ApiModelProperty("是否限时降价 0否 1是")
    private Integer priceDownLimitTime;

    @ApiModelProperty("供应商id列表")
    private List<Long> supplierIdList;

    @ApiModelProperty("供应商spu列表")
    private List<String> supplierSpuCodeList;

    @ApiModelProperty("新商家id列表")
    private List<Long> newMerchantIds;

    @ApiModelProperty
    private List<Long> notInSkuIdList;

    @ApiModelProperty("销售类型：1为般果代采，2为商家自营")
    private Integer saleType;

    /**
     * 平台商品ID
     */
    private List<Long> spuIds;

    /**
     * 平台商品ID-取反
     */
    private List<Long> rollSpuIds;

    /**
     * 商品分类id
     */
    private List<Long> categoryIds;

    private List<Long> skuIds;

    private List<Long> rollSkuIds;

    /**
     * 商品分类id-取反
     */
    private List<Long> rollCategoryIds;

    /**
     * 销量
     */
    private Integer copySold;


    @ApiModelProperty("客户门店类型")
    private String customerStoreType;
}

