package cn.xianlink.product.service;

import cn.xianlink.common.api.bo.RemoteRegionWhMsgBo;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.product.api.domain.bo.*;
import cn.xianlink.product.api.domain.vo.RemoteQuerySupplierDeliverVo;
import cn.xianlink.product.api.domain.vo.RemoteSupplierSkuFundsInfoVo;
import cn.xianlink.product.api.domain.vo.RemoteSupplierSkuInfoVo;
import cn.xianlink.product.api.domain.vo.RemoteSupplierSkuStockVo;
import cn.xianlink.product.domain.PublicId;
import cn.xianlink.product.domain.SupplierSku;
import cn.xianlink.product.domain.supplierSku.bo.*;
import cn.xianlink.product.domain.supplierSku.vo.*;
import cn.xianlink.product.domain.supplierSkuStock.vo.SupplierSkuStockVo;

import java.time.LocalDate;
import java.util.List;

/**
 * 供应商销售批次商品Service接口
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
public interface ISupplierSkuService {


    List<SaleCategoryVo> sortName(List<SaleCategoryVo> oldList, Integer level);


    /**
     * 判断分类+总仓是否免检，免检返回1
     */
    int isNoCheck(Long regionWhId, String categoryPathName, Integer businessType, Integer saleType);

    /**
     * 根据批次id查询批次详情
     * 【注意】规格不会转换成 value 的形式!
     * @param id
     * @return
     */
    SupplierSkuVo selectById(Long id);

    /**
     * 根据供应商商品销售批次ID获取供应商商品销售批次详细信息
     */
    SupplierSkuInfoVo queryById(Long id, boolean isRegion);
    SupplierSkuInfoVo queryById(Long id);


    SupplierSkuVo querySimpleById(Long id);


    /**
     * 根据skuId 获取信息
     * @param publicId
     * @return
     */
    SupplierSkuInfoVo getSkuInfo(PublicId publicId);

    /**
     * 查询供应商销售批次商品列表
     * @param bo
     * @return
     */
    TableDataInfo<SupplierSkuVo> queryPageList(SupplierSkuPageBo bo);

    /**
     * 集采小程序销售批次列表接口
     * @param bo
     * @return
     */
    TableDataInfo<QuerySalePageVo> querySalePage(QuerySalePageBo bo);


    /**
     * 查询在售商铺基础信息列表接口
     * @param bo
     * @return
     */
    TableDataInfo<SupplierSkuSalePageVo> queryOnSaleSkuPage(SupplierSkuSalePageBo bo);

    /**
     * 供货申请列表
     * @param bo
     * @return
     */
    TableDataInfo<QuerySupSubscribePageVo> querySupSubscribePage(QuerySupSubscribePageBo bo);

    /**
     * 供货批次列表
     * @param bo
     * @return
     */
    TableDataInfo<QueryBatchPageVo> queryBatchPage(QueryBatchPageBo bo);


    /**
     * 供应商商品查询
     * @param bo
     * @return
     */
    TableDataInfo<SupplierSkuVo> queryPage(SupplierSkuSimplePageBo bo);

    /**
     * 供应商商品补贴审核分页
     * @param bo
     * @return
     */
    TableDataInfo<SupplierSkuVo> querySubsidyAuditPage(SupplierSkuSimplePageBo bo);

    /**
     * 供应商快捷查询
     * @param bo
     * @return
     */
    TableDataInfo<SupplierSkuVo> queryPageByQuickUp(SupplierSkuSimplePageBo bo);


    /**
     * 送货审核批次查询
     * @param bo
     * @return
     */
    TableDataInfo<SupplierSkuVo> queryDeliveryAuditPage(SupplierSkuSimplePageBo bo);
    List<SupplierSkuVo> queryDeliveryAuditList(SupplierSkuSimplePageBo bo);

    /**
     * 总仓小程序上架申请列表
     * @param bo
     * @return
     */
    TableDataInfo<QueryRegSubscribePageVo> querySupSubscribePage(QueryRegSubscribePageBo bo);

    /**
     * 总仓小程序上下架列表
     * @param bo
     * @return
     */
    TableDataInfo<QueryPassPageVo> queryPassPage(QueryPassPageBo bo);

    /**
     * 城市仓多货选择供应商商品销售批次列表接口
     * @param bo
     * @return
     */
    TableDataInfo<QueryHistoryPageVo> queryHistoryPage(QueryHistoryPageBo bo);
    /**
     * 新增供应商销售批次商品
     */
    Long insertByBo(AddSupplierSkuBo bo);


    void autoInsertList(Long regionWhId, LocalDate saleDate);

    /**
     * 总仓自动上架一个自定义批次，目前有：配送商品
     */
    Long autoInsert(Long regionWhId, Long spuId, LocalDate saleDate);

    /**
     * 修改供应商销售批次商品
     */
    Boolean updateByBo(UpdateSupplierSkuBo bo, SupplierSkuVo old);


    /**
     * update 接口不支持修改历史批次
     *
     * @param bo
     */
    void changeLabel(UpdateSupplierSkuBo bo);

    void auditDelivery(UpdateSupplierSkuBo bo);


    /**
     * 获取同一唯一性的商品批次id列表
     *
     * @param bo
     * @return
     */
    List<Long> querySupplierSkuIdGroup(RemoteQuerySkuIdsBo bo);

    /**
     * 修改状态
     * @param bo
     * @return
     */
    Boolean changeStatus(ChangeStatusBo bo);

    /**
     * 根据一级目录id查询有售卖批次的二级目录列表
     * @param bo
     * @return
     */
    List<SaleCategoryVo> getSaleCategory(SaleCategoryBo bo);

    /**
     * 根据供应商商品ID查询供应商供货的回填信息
     * @param bo
     * @return
     */
    GetBackInfoVo getBackInfo(GetBackInfoBo bo);

    /**
     * 根据供应商商品销售批次ID查询供应商供货的回填信息
     * @param supplierSkuId
     * @return
     */
    GetBackInfoVo getBackInfoBySkuId(Long supplierSkuId);

    /**
     * 清空库存
     * @param idList
     * @return
     */
    Boolean clearStock(List<Long> idList);

    /**
     * 快捷上架
     * @param skuVo
     * @param upType 上架类型，0-普通的快捷上架，1-补贴上架
     * @return
     */
    Boolean quickUp(SupplierSkuVo skuVo, boolean sup, String operate, Integer upType);

    /**
     * 定时批量下架供货商商品销售批次
     */
    void batchDown(RemoteRegionWhMsgBo bo);

    void sendDelayWxAuditMsg( Long supplierSkuId);

    /**
     * 供应商我要供货查询批次状态接口
     * @param bo
     * @return
     */
    List<SupplierSkuVo> queryLastList(QueryLastListBo bo);

    /**
     * 尾货上架
     */
    Boolean addTail(AddTailSupplierSkuBo bo);

    /**
     * 查询供应商供货申请数量
     * @return
     */
    Long getCount(GetCountBo bo);

    /**
     * 根据条件查询销售批次详情
     * @param bo
     * @return
     */
    List<RemoteSupplierSkuInfoVo> queryInfoList(RemoteQueryInfoListBo bo);

    /**
     * 根据条件查询销售批次id
     * @param bo
     * @return
     */
    List<Long> querySkuIdList(RemoteQuerySkuIdBo bo);

    /**
     * 资金账户场景下的查询sku信息
     * <AUTHOR> on 2024/6/29:10:56
     * @param supplierSkuIds
     * @return java.util.List<RemoteSupplierSkuFundsInfoVo>
     */
    List<RemoteSupplierSkuFundsInfoVo> queryInfoListByFunds(List<Long> supplierSkuIds);

    /**
     * 根据code查询详情
     */
    RemoteSupplierSkuInfoVo getByCode(String code);
    /**
     * 根据id获取详情
     * <AUTHOR> on 2025/1/17:14:30
     * @param ids
     * @return cn.xianlink.product.api.domain.vo.RemoteSupplierSkuInfoVo
     */
    List<RemoteSupplierSkuInfoVo> getByIds(List<Long> ids);

    /**
     * 根据商品条码获取批次
     *
     * @param bo@return
     */
    RemoteSupplierSkuInfoVo getByLabel(RemoteQuerySkuIdBo bo);

    List<RemoteSupplierSkuInfoVo> getByLabelList(RemoteQuerySkuIdBo bo);


    /**
     * 城市仓多货，生成地采批次
     */
    Long addLand(RemoteAddLandBo bo);

    /**
     * 换供应商
     * @param bo
     * @return
     */
    Boolean replaceSupplier(ReplaceSupplierBo bo);

    /**
     * 批量获取库存信息
     * @param supplierSkuIds
     * @return
     */
    List<RemoteSupplierSkuStockVo> listSupplierSkuStockBySupplierIds(List<Long> supplierSkuIds);

    /**
     * 获取供应商商品库存
     * @param supplierSkuId
     * @return
     */
    SupplierSkuStockVo getSupplierSkuStock(Long supplierSkuId);

    /**
     * 查询需要差额退款的供应商商品销售批次
     */
    List<QueryRefundDifferenceSkuVo> queryRefundDifferenceSku(LocalDate saleDate, List<Long> supplierSkuIdList);

    /**
     * 根据供应商商品code查询供应商批次商品(最近15天)
     */
    List<RemoteSupplierSkuInfoVo> getBySpuCode(String code);

    /**
     * 根据总仓id查询供应商今日供货商品数量
     * @param bo
     * @return
     */
    List<RemoteQuerySupplierDeliverVo> querySupplierDeliver(RemoteQuerySupplierDeliverBo bo);

    /**
     * 根据条件批量新增批次
     * @param bo
     */
    void createTestSku(CreateTestSkuBo bo);


    /**
     * 子供应商是否存在商品
     * @param supplierDeptId
     * @param statusList
     * @return
     */
    boolean hasSupplierDeptSku(Long supplierDeptId, List<Integer> statusList);

    /**
     * 城市仓扫描新增多货查询
     * @param bo
     * @return
     */
    QueryHistoryPageVo querySkuInfoSingle(QueryHistoryPageBo bo);

    /**
     * 批量修改
     * @param supplierSkus
     * @return
     */
    boolean updateBatchById(List<SupplierSku> supplierSkus);

    void syncSold(List<Long> supplierSkuIds);
    void asyncSold(List<Long> supplierSkuIds);

    /**
     * 根据供应商商品销售批次ID获取供应商商品销售批次详细信息
     */
    SupplierSkuInfoVo getInfo(Long id);
    /**
     * 根据供应商商品销售批次ID获取供应商商品销售批次详细信息
     */
    SupplierSkuInfoVo getInfoV2(QuerySkuInfoBo publicId);

    /**
     * 导出总仓报价
     */
    String exportTodayPrice(Long regionWhId, LocalDate saleDate);

    /**
     * 实时获取批次的库存信息（含云仓）
     * @param supplierSkuIds
     * @return
     */
    List<RemoteSupplierSkuStockVo> queryYunStock(List<Long> supplierSkuIds);

    void checkPortageTeam(Integer businessType, Long basPortageTeamId);
    void checkProvide(Integer businessType, Long provideRegionWhId);

    void sendSkuMsg(RemoteSkuMsgBo bo);

    List<Long> getIdsByFullName(LocalDate saleDate, String fullName, List<String> filterFields);

    int updateIsOut(List<Long> supplierSkuIds,Integer isOut);

    /**
     * 售罄时，提醒总仓、供应商，异步发送公众号消息
     * @param supplierSkuIds 供应商批次id
     */
    void asyncSendSellOutMessage(List<Long> supplierSkuIds);

    /**
     * sku降级，下架原sku、supplierSku，创建新sku、supplierSKu
     * @param bo 保存入参
     */
    Long skuDowngrade(CwStockDowngradeBo bo);

    /**
     * 组装商品名，品牌-产地-名称-规格
     * @param skuInfo 供应商批次
     * @return 商品名
     */
    String getProductName(SupplierSkuVo skuInfo);

    /**
     * 坑位校验
     */
    CheckSkuMaxSaleNumResVo checkSkuMaxSaleNum(Long regionWhId, Long supplierId);


    /**
     * 获取最大坑位和剩余坑位数
     * @param regionWhId
     * @param supplierId
     * @return
     */
    SaleNumVo getSaleNum(Long regionWhId, Long supplierId);

    TableDataInfo<QuerySalePageVo> queryDiscountPage(QuerySalePageBo bo);
}
