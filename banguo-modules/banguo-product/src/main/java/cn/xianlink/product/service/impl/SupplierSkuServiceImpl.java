package cn.xianlink.product.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import cn.xianlink.basic.api.*;
import cn.xianlink.basic.api.domain.bo.*;
import cn.xianlink.basic.api.domain.vo.*;
import cn.xianlink.basic.api.enums.CustomerStoreTypeEnum;
import cn.xianlink.basic.api.enums.MarketForecast;
import cn.xianlink.basic.api.enums.MsgNotifyTemplateV2Enum;
import cn.xianlink.basic.api.enums.NotifyObjectTypeEnum;
import cn.xianlink.bi.api.IRemoteBiSkuService;
import cn.xianlink.bi.api.domain.bo.RemoteSkuLossBo;
import cn.xianlink.bi.api.domain.vo.RemoteSkuCardVo;
import cn.xianlink.bi.api.domain.vo.RemoteSkuLossVo;
import cn.xianlink.common.api.bo.RemoteRegionWhMsgBo;
import cn.xianlink.common.api.enums.product.*;
import cn.xianlink.common.api.enums.trade.AccountOrgPropertyEnum;
import cn.xianlink.common.api.enums.trade.AccountOrgTypeEnum;
import cn.xianlink.common.api.util.SaleDateUtil;
import cn.xianlink.common.core.domain.vo.CodeNameVo;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.core.utils.SpringUtils;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.json.utils.JsonUtils;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.redis.utils.RedisUtils;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.marketing.api.RemoteDiscountService;
import cn.xianlink.marketing.api.RemoteMkActivityDiscountSummaryService;
import cn.xianlink.marketing.api.bo.RemoteMkActivitySkuBo;
import cn.xianlink.marketing.api.bo.RemoteMkActivitySkuDiscountBo;
import cn.xianlink.marketing.api.dto.RemoteSkuDiscountItemDTO;
import cn.xianlink.marketing.api.dto.RemoteSkuDiscountMatchDTO;
import cn.xianlink.marketing.api.vo.RemoteMkActivitySkuDiscountVo;
import cn.xianlink.marketing.api.vo.RemoteMkActivitySkuSearchVo;
import cn.xianlink.marketing.api.vo.SkuCouponMatchVO;
import cn.xianlink.order.api.RemoteCartItemService;
import cn.xianlink.order.api.RemoteOrderService;
import cn.xianlink.order.api.bo.RemoteReplaceSupplierBo;
import cn.xianlink.order.api.constant.StockTypeEnum;
import cn.xianlink.order.api.vo.RemoteCartItemCountVO;
import cn.xianlink.order.api.vo.RemoteRegionSubMaxPriceVo;
import cn.xianlink.product.api.domain.bo.*;
import cn.xianlink.product.api.domain.vo.RemoteQuerySupplierDeliverVo;
import cn.xianlink.product.api.domain.vo.RemoteSupplierSkuFundsInfoVo;
import cn.xianlink.product.api.domain.vo.RemoteSupplierSkuInfoVo;
import cn.xianlink.product.api.domain.vo.RemoteSupplierSkuStockVo;
import cn.xianlink.product.api.util.TemCode;
import cn.xianlink.product.config.*;
import cn.xianlink.product.constant.ProductRedisNames;
import cn.xianlink.product.domain.*;
import cn.xianlink.product.domain.bo.BasCustomerBo;
import cn.xianlink.product.domain.bo.BuyerCategoryBO;
import cn.xianlink.product.domain.bo.SpuBO;
import cn.xianlink.product.domain.excel.RegionSkuPriceExportVO;
import cn.xianlink.product.domain.supplierSku.bo.*;
import cn.xianlink.product.domain.supplierSku.vo.*;
import cn.xianlink.product.domain.supplierSkuApproveLog.SupplierSkuApproveLogVo;
import cn.xianlink.product.domain.supplierSkuDisableCityWh.bo.DelSupplierSkuDisableCityWhBo;
import cn.xianlink.product.domain.supplierSkuDisableCityWh.vo.SupplierSkuDisableCityWhVo;
import cn.xianlink.product.domain.supplierSkuFile.bo.AddSupplierSkuFileBo;
import cn.xianlink.product.domain.supplierSkuFile.bo.DelSupplierSkuFileBo;
import cn.xianlink.product.domain.supplierSkuFile.bo.QuerySupplierSkuFileBo;
import cn.xianlink.product.domain.supplierSkuFile.vo.SupplierSkuFileVo;
import cn.xianlink.product.domain.supplierSkuStock.bo.UpdateSupplierSkuStockBo;
import cn.xianlink.product.domain.supplierSkuStock.vo.SupplierSkuStockVo;
import cn.xianlink.product.domain.supplierSpu.bo.SupplierSpuBo;
import cn.xianlink.product.domain.supplierSpu.vo.SupplierSpuVo;
import cn.xianlink.product.domain.vo.*;
import cn.xianlink.product.mapper.*;
import cn.xianlink.product.mq.producer.SkuSendMsgProducer;
import cn.xianlink.product.service.*;
import cn.xianlink.resource.api.RemoteFileService;
import cn.xianlink.system.api.RemoteDeptService;
import cn.xianlink.system.api.domain.vo.RemoteDeptDetailVo;
import cn.xianlink.system.api.model.LoginUser;
import cn.xianlink.trade.api.RemoteOrgRelationService;
import cn.xianlink.trade.api.domain.bo.RemoteOrgRelationQueryBo;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import io.seata.spring.annotation.GlobalTransactional;
import jakarta.validation.constraints.NotNull;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.redisson.api.RAtomicLong;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.*;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DateTime.now;
import static cn.hutool.core.date.DateTime.of;

/**
 * 供应商销售批次商品Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
@RequiredArgsConstructor
@Service
@CustomLog
public class SupplierSkuServiceImpl implements ISupplierSkuService {

    private final SupplierSkuMapper supplierSkuMapper;

    private final ISkuService skuService;

    private final SkuMapper skuMapper;

    private final SpuMapper spuMapper;

    private final SupplierSpuMapper supplierSpuMapper;

    private final CategoryMapper categoryMapper;

    private final SupplierSkuFileMapper supplierSkuFileMapper;

    private final SupplierSkuDisableCityWhMapper supplierSkuDisableCityWhMapper;

    private final ISupplierSkuDisableCityWhService supplierSkuDisableCityWhService;

    private final SupplierSkuApproveLogMapper supplierSkuApproveLogMapper;

    private final SpuCreateControlMapper spuCreateControlMapper;

    private final SupplierSkuStockMapper supplierSkuStockMapper;

    private final ISpuService spuService;

    private final CategorySpuMapper categorySpuMapper;

    private final ICwStockDetailService cwStockDetailService;

    private final SkuSalesStatisticsMapper skuSalesStatisticsMapper;

    private final SkuSendMsgProducer skuSendMsgProducer;

    private final ProductProperties productProperties;

    private final SupplierSkuSubsidyAuditMapper supplierSkuSubsidyAuditMapper;

    private final SupplierSkuPriceLogMapper supplierSkuPriceLogMapper;

    private final SupplierSkuStockLogMapper supplierSkuStockLogMapper;

    @DubboReference
    private final RemoteRegionWhService remoteRegionWhService;

    @DubboReference
    private final RemoteSupplierService remoteSupplierService;

    @DubboReference
    private final RemoteCartItemService remoteCartItemService;

    @DubboReference
    private final RemoteOrderService remoteOrderService;

    @DubboReference
    private final RemotePortageTeamService remotePortageTeamService;

    @DubboReference
    private final RemoteDeptService remoteDeptService;

    @DubboReference
    private final RemoteBasPubAreaService remoteBasPubAreaService;

    @DubboReference
    private final IRemoteBiSkuService remoteBiSkuService;

    @DubboReference
    private final RemoteDiscountService discountService;

    @Autowired
    private CosProperties cosProperties;

    @Autowired
    private PhGoodsProperties phGoodsProperties;

    @Autowired
    private BargainGoodsProperties bargainGoodsProperties;

    @Autowired
    private NoCheckGoodsProperties noCheckGoodsProperties;

    @Autowired
    private SkuGlobalProperties skuGlobalProperties;

    @DubboReference
    private final RemoteRuleFreightServiceService remoteRuleFreightServiceService;

    private final RemoteRegionLogisticsService remoteRegionLogisticsService;

    @DubboReference(timeout = 300000)
    private final RemoteFileService remoteFileService;


    private final CategoryExpMapper spuExpMapper;

    @DubboReference
    private final RemoteMessageNotifyService remoteMessageNotifyService;

    @DubboReference
    private final RemoteOrgRelationService remoteOrgRelationService;

    @DubboReference
    private final RemoteCityWhService remoteCityWhService;

    @DubboReference
    private final RemoteMkActivityDiscountSummaryService discountSummaryService;

    @DubboReference
    private final RemoteCityWhPlaceService remoteCityWhPlaceService;

    /**
     * 根据批次id查询批次详情
     *
     * @param id
     * @return
     */
    @Override
    public SupplierSkuVo selectById(Long id) {
        SupplierSkuVo supplierSkuVo = supplierSkuMapper.selectVoById(id);
        if (Objects.nonNull(supplierSkuVo)) {
            // 规格
            supplierSkuVo.buildSkuStandardsList();
            // 库存
            SupplierSkuStockVo supplierSkuStock = this.getSupplierSkuStock(id);
            if (Objects.nonNull(supplierSkuStock)) {
                supplierSkuVo.setStock(supplierSkuStock.getStock());
                supplierSkuVo.setUpStock(supplierSkuStock.getUpStock());
            }
        }
        return supplierSkuVo;
    }


    @Override
    public SupplierSkuInfoVo queryById(Long id) {
        return this.queryById(id, false);
    }

    /**
     * 根据供应商商品销售批次ID获取供应商商品销售批次详细信息
     */
    @Override
    public SupplierSkuInfoVo queryById(Long id, boolean isRegion) {
        SupplierSkuVo skuVo = supplierSkuMapper.queryById(id);
        if (skuVo == null) {
            throw new ServiceException("供应商商品销售批次不存在");
        }
        //补贴后单价
        skuVo.setSubsidyPrice(skuVo.getPrice().subtract(skuVo.getSubsidyAmount()));
        //待审核状态，根据skuId获取上个批次的品牌和装卸队id
        if (SupplierSkuStatusEnum.getCodesWaitAudit().contains(skuVo.getStatus())) {
            LambdaQueryWrapper<SupplierSku> wrapper = Wrappers.lambdaQuery(SupplierSku.class);
            wrapper.eq(SupplierSku::getSkuId, skuVo.getSkuId()).orderByDesc(SupplierSku::getSaleDate)
                    .notIn(SupplierSku::getStatus, SupplierSkuStatusEnum.getCodesWaitAudit()).last("LIMIT 1");
            SupplierSku oldSku = supplierSkuMapper.selectOne(wrapper);
            if (ObjectUtil.isNotNull(oldSku)) {
                if (StringUtils.isBlank(skuVo.getBrand())) {
                    skuVo.setBrand(oldSku.getBrand());
                }
                if (skuVo.getBasPortageTeamId() == null || skuVo.getBasPortageTeamId() == 0) {
                    skuVo.setBasPortageTeamId(oldSku.getBasPortageTeamId());
                }
            }
        }
        // 写入档口信息 - 名称，简码
        fillDeptSupplierInfo(skuVo);
        // 规格转换
        skuVo.tranSpuStandardsName().buildSkuStandardsList();
        SupplierSkuInfoVo infoVo = MapstructUtils.convert(skuVo, SupplierSkuInfoVo.class);
        RemoteSupplierVo supplierVo = remoteSupplierService.getSupplierById(infoVo.getSupplierId());
        if (supplierVo == null) {
            throw new ServiceException("供应商不存在");
        }
        // 供货总仓名称
        if (ObjectUtil.isNotEmpty(infoVo.getProvideRegionWhId()) && ObjectUtil.notEqual(infoVo.getProvideRegionWhId(), 0L)) {
            RemoteRegionWhVo regionWhVo = remoteRegionWhService.queryById(infoVo.getProvideRegionWhId());
            infoVo.setProvideRegionWhName(ObjectUtil.isNotEmpty(regionWhVo) ? regionWhVo.getRegionWhName() : null);
            infoVo.setProvideRegionWhIsSaleNum(ObjectUtil.isNotEmpty(regionWhVo) ? regionWhVo.getIsSaleNum(): null);
        }
        if (ObjectUtil.isNotEmpty(infoVo.getBasPortageTeamId())) {
            RemotePortageTeamQueryBo queryBo = new RemotePortageTeamQueryBo();
            queryBo.setTeamId(infoVo.getBasPortageTeamId());
            List<RemotePortageTeamVo> remotePortageTeamVos = remotePortageTeamService.queryList(queryBo);
            if (ObjectUtil.isNotEmpty(remotePortageTeamVos)) {
                RemotePortageTeamVo vo = remotePortageTeamVos.get(0);
                infoVo.setBasPortageTeamName(vo.getTeamName());
            }
        }

        infoVo.setSpuStandardsName(infoVo.getSpuStandards());
        infoVo.buildSpuStandardsName();
        infoVo.setSupplierSimpleCode(supplierVo.getSimpleCode());
        infoVo.setSupplierAlias(supplierVo.getAlias());
        // 档口编码覆盖供应商简码
        if (StringUtils.isNotBlank(skuVo.getSupplierDeptCode())) {
            infoVo.setSupplierSimpleCode(skuVo.getSupplierDeptCode());
        }
        // 返回前端可选信息（等级、包装、规格等）
        SpuVO spuVO = spuService.getById(skuVo.getSpuId());
        if (spuVO == null) {
            throw new ServiceException("平台商品不存在");
        }
        infoVo.setExpList(spuVO.getExpList());
        BigDecimal netWeightPrice = infoVo.getPrice().divide(infoVo.getSpuNetWeight(), 2, RoundingMode.HALF_UP);
        if (netWeightPrice.compareTo(BigDecimal.ZERO) == 0) {
            netWeightPrice = new BigDecimal("0.01");
        }
        infoVo.setNetWeightPrice(netWeightPrice);
        //获取库存信息
        SupplierSkuStockVo supplierSkuStock = this.getSupplierSkuStock(id);
        if (Objects.nonNull(supplierSkuStock)) {
            infoVo.setStock(supplierSkuStock.getStock());
            infoVo.setLockStock(supplierSkuStock.getLockStock());
        }
        //根据最后供货的供应商商品批次id查询禁止下单城市仓信息
        List<Integer> statusList = isRegion ? List.of(0, 1) : List.of(1);
        // 禁用城市仓列表
        List<SupplierSkuDisableCityWhVo> cityWhVos = supplierSkuDisableCityWhMapper.queryList(skuVo.getSkuId(), statusList);
        // 设置城市仓名名称
        if (!cityWhVos.isEmpty()){
            List<Long> ids = cityWhVos.stream()
                    .map(SupplierSkuDisableCityWhVo::getCityWhId)
                    .collect(Collectors.toList());
            List<RemoteCityWhVo> remoteCityWhVos = remoteCityWhService.queryList(ids);
            // 创建映射关系并直接设置
            Map<Long, RemoteCityWhVo> idToRemoteCityWhVoMap = remoteCityWhVos.stream().collect(Collectors.toMap(RemoteCityWhVo::getId, Function.identity()));
            // 过滤掉在城市仓中找不到的并重新赋值
            cityWhVos = cityWhVos.stream()
                .filter(cityWhVo -> (idToRemoteCityWhVoMap.get(cityWhVo.getCityWhId()) != null && idToRemoteCityWhVoMap.get(cityWhVo.getCityWhId()).getStatus() == 1))
                .peek(cityWhVo -> cityWhVo.setCityWhName(idToRemoteCityWhVoMap.get(cityWhVo.getCityWhId()).getName()))
                .collect(Collectors.toList());
        }
        infoVo.setCityWhList(cityWhVos);

        //根据供应商商品id查询文件
        QuerySupplierSkuFileBo queryFileBo = new QuerySupplierSkuFileBo();
        queryFileBo.setSupplierSkuIdList(Lists.newArrayList(id));
        List<SupplierSkuFileVo> fileList = supplierSkuFileMapper.queryList(queryFileBo);
        if (fileList != null && fileList.size() > 0) {
            for (SupplierSkuFileVo file : fileList) {
                if (SupplierSkuFileTypeEnum.TYPE1.getCode().equals(file.getType())) {
                    infoVo.setImgUrl(file.getFileUrl());
                    break;
                }
            }
        }
        infoVo.setFileList(fileList);
        List<SpuCreateControlVO> spuCreateControlVOList = spuCreateControlMapper.queryList(skuVo.getSpuId());
        if (CollectionUtil.isNotEmpty(spuCreateControlVOList)) {
            RemoteRegionWhVo regionWhVo = remoteRegionWhService.queryById(skuVo.getRegionWhId());
            if (regionWhVo == null) {
                throw new ServiceException("总仓不存在");
            }
            SpuCreateControlVO spuCreateControlVO = spuCreateControlVOList.get(0);
            if (regionWhVo.getQualityReport() > 0 && spuCreateControlVO.getQualityReport() > 0) {
                spuCreateControlVO.setQualityReport(1);
            } else {
                spuCreateControlVO.setQualityReport(0);
            }
            infoVo.setControlList(spuCreateControlVOList);
        }
        //查询供应商商品销售批次审批列表
        List<SupplierSkuApproveLogVo> logVoList = supplierSkuApproveLogMapper.queryList(id);
        if (logVoList != null && logVoList.size() > 0) {
            String rerson = null;
            for (SupplierSkuApproveLogVo logVo : logVoList) {
                if (logVo.getStatus().equals(SupplierSkuStatusEnum.STATUS2.getCode()) && logVo.getReason() != null) {
                    rerson = logVo.getReason();
                }
            }
            infoVo.setApproveLogReason(rerson);
        }

        LoginUser user = LoginHelper.getLoginUser();
        if (user != null) {
            List<RemoteCartItemCountVO> cartList = remoteCartItemService.getCartItemListInfo(user.getUserId(), Lists.newArrayList(infoVo.getId()));
            if (cartList != null && cartList.size() > 0) {
                infoVo.setCartCount(cartList.get(0).getCount());
            }
        }
        infoVo.setApproveLogList(logVoList);
        infoVo.setLabel(TemCode.getTemCode(infoVo.getCode()));
        if (StringUtils.isBlank(infoVo.getSkuLabel())) {
            infoVo.setSkuLabel(infoVo.getLabel());
        }

        // 是否为榴莲商品
        infoVo.setHasDurian(0);
        if (infoVo.getCategoryId() != null) {
            Category category = categoryMapper.selectById(infoVo.getCategoryId());
            if (category != null && StringUtils.isNotBlank(category.getPathName()) && category.getPathName().contains("榴莲")) {
                String[] split = category.getPathName().split("/");
                if ((split.length > 1 && split[1].contains("榴莲")
                        || (split.length > 2 && split[2].contains("榴莲")))) {
                    infoVo.setHasDurian(1);
                }
            }
        }

        // 处理售后规则
        CategoryAfterTypeEnum afterTypeEnum = CategoryAfterTypeEnum.loadByCode(infoVo.getAfterSaleType());
        if (afterTypeEnum != null) {
            infoVo.setAfterSaleName(afterTypeEnum.getName());
            infoVo.setAfterSaleDesc(afterTypeEnum.getDesc());
        }

        // 区域简称
        if (StringUtils.isNotBlank(infoVo.getAreaCode()) && StringUtils.isBlank(infoVo.getShortProducer())) {
            RemotePubAreaVO remotePubAreaVO = remoteBasPubAreaService.queryByCode(infoVo.getAreaCode());
            if (remotePubAreaVO != null) {
                infoVo.setShortProducer(remotePubAreaVO.getAreaAlias());
            }
        }

        // 是否在当前销售日
        LocalDate saleDate = getSaleDate(infoVo.getRegionWhId());
        if (saleDate.isEqual(infoVo.getSaleDate())) {
            infoVo.setOnSalesDay(1);
        }
        // 云仓库存 - 优先与批次库存
        if (skuVo.getSkuId() != null && skuVo.getSkuId() != 0L) {
            CwStockDetailVo stockDetailVo = cwStockDetailService.getBySkuId(skuVo.getSkuId());
            if (stockDetailVo != null) {
                int stock = ObjectUtil.defaultIfNull(stockDetailVo.getOnhandQty(), BigDecimal.ZERO).intValue();
                int lockStock = ObjectUtil.defaultIfNull(stockDetailVo.getAllocationQty(), BigDecimal.ZERO).intValue();
                int onWayStock = ObjectUtil.defaultIfNull(stockDetailVo.getOnwayQty(), BigDecimal.ZERO).intValue();
                infoVo.setCloudOnWayStock(onWayStock);
                infoVo.setCloudActualStock(stock);
                infoVo.setCloudLockStock(lockStock);
                infoVo.setCloudStock(stock - lockStock + onWayStock);
            }
        }

        // 是否满足特价
        CheckSupplierSkuBo checkSupplierSkuBo = new CheckSupplierSkuBo();
        checkSupplierSkuBo.setId(infoVo.getId());
        checkSupplierSkuBo.setSkuId(infoVo.getSkuId());
        checkSupplierSkuBo.setLastUpTime(1);
        SupplierSkuVo lastOne = supplierSkuMapper.checkSupplierSku(checkSupplierSkuBo);
        if (lastOne != null) {
            infoVo.setLastUpTime(lastOne.getUpTime());
            // 特价
            LocalDate nearDay = saleDate.plusDays(-1L * bargainGoodsProperties.getNearDay());
            boolean inTime = lastOne.getSaleDate().isAfter(nearDay);
            boolean soldSome = lastOne.getCopySold() >= bargainGoodsProperties.getSold();
            if (inTime && soldSome) {
                BigDecimal bargainPrice = lastOne.getPrice().multiply(BigDecimal.ONE.subtract(bargainGoodsProperties.getRate()));
                infoVo.setBargainPrice(bargainPrice);
                log.keyword("teJia-Debug").info("商品特价={}", bargainPrice);
            }
        }

        // 批次最后上架时间
        if (infoVo.getUpTime() != null) {
            infoVo.setLastUpTime(infoVo.getUpTime());
        }


        Long skuId = infoVo.getSkuId();
        // BI 报损 + 质检不合格
        Map<Long, RemoteSkuLossVo> lossMap = getRemoteSkuLossVoMap(Lists.newArrayList(skuId), saleDate);
        SkuLossResult result = getSkuLossResult(lossMap, skuId);
        infoVo.setRemoteSkuLossVo(result.remoteSkuLossVo());
        infoVo.setRemoteSkuCard(result.remoteSkuCard());

        // 获取商品使用客户门店类型
        String customerStoreTypes = infoVo.getCustomerStoreTypes();
        if (StrUtil.isNotBlank(customerStoreTypes)) {
            infoVo.setCustomerStoreTypeValues(CustomerStoreTypeEnum.getNamesByIds(customerStoreTypes));
        }

        // 7天销售量和昨天销售量填充
        salesQuantityCount(infoVo.getRegionWhId(), skuId,infoVo.getRemoteSkuLossVo());
        return infoVo;
    }


    @Override
    public SupplierSkuVo querySimpleById(Long id) {
        SupplierSkuVo skuVo = supplierSkuMapper.selectVoById(id);
        if (skuVo == null) {
            throw new ServiceException("供应商商品销售批次不存在");
        }
        // 规格转换
        skuVo.tranSpuStandardsName().buildSkuStandardsList();
        return skuVo;
    }

    /**
     * 获取报损卡片信息
     *
     * @param lossMap
     * @param skuId
     * @return
     */
    private @org.jetbrains.annotations.NotNull SkuLossResult getSkuLossResult(@NotNull Map<Long, RemoteSkuLossVo> lossMap, @NotNull Long skuId) {
        RemoteSkuLossVo remoteSkuLossVo = lossMap.get(skuId);
        if (remoteSkuLossVo == null) {
            return new SkuLossResult(new RemoteSkuLossVo().init(), SkuGlobalProperties.getIfNullDefault(remoteSkuLossVo));
        }
        return new SkuLossResult(remoteSkuLossVo, remoteSkuLossVo.getCardVo());
    }

    /**
     * 多结果
     *
     * @param remoteSkuLossVo
     * @param remoteSkuCard
     */
    private record SkuLossResult(RemoteSkuLossVo remoteSkuLossVo, RemoteSkuCardVo remoteSkuCard) {
    }

    /**
     * 获取报损信息
     *
     * @param skuIdList
     * @param saleDate
     * @return
     */
    public Map<Long, RemoteSkuLossVo> getRemoteSkuLossVoMap(List<Long> skuIdList, LocalDate saleDate) {
        if (!skuGlobalProperties.getOpenBI()) {
            // bi 服务降级开关
            log.keyword("BI-Sku-Loss").info("bi 服务降级-不查询商品报损信息");
            return new HashMap<>();
        }
        RemoteSkuLossBo remoteSkuLossBo = new RemoteSkuLossBo();
        remoteSkuLossBo.setSkuIdList(skuIdList);
        remoteSkuLossBo.setSaleDateEnd(saleDate.plusDays(-ObjectUtil.defaultIfNull(skuGlobalProperties.getEndDay(), 1)));
        remoteSkuLossBo.setSaleDateStart(saleDate.plusDays(-ObjectUtil.defaultIfNull(skuGlobalProperties.getLossSkuDay(), 7)));
        List<RemoteSkuLossVo> skuLossByRemoteBI = null;
        List<RemoteSkuLossVo> skuInspectByRemoteBI = null;
        try {
            skuLossByRemoteBI = remoteBiSkuService.getSkuLossByRemoteBI(remoteSkuLossBo);
            remoteSkuLossBo.setSaleDateStart(saleDate.plusDays(-ObjectUtil.defaultIfNull(skuGlobalProperties.getInspectDay(), 180)));
            skuInspectByRemoteBI = remoteBiSkuService.getSkuInspectByRemoteBI(remoteSkuLossBo);
        } catch (Exception e) {
            log.keyword("BI-Sku-Loss").info("bi 服务请求出错, msg={}", e.getMessage(), e);
        }
        return RemoteSkuLossVo.mergeToMap(skuLossByRemoteBI, skuInspectByRemoteBI);
    }


    @Override
    public SupplierSkuInfoVo getSkuInfo(PublicId publicId) {
        SupplierSkuVo skuVo = skuMapper.querySupplierSkuById(publicId.getId());
        if (skuVo == null) {
            throw new ServiceException("sku不存在");
        }
        Sku sku = skuMapper.selectById(skuVo.getSkuId());
        Map<Long, BigDecimal> skuDiscount = new HashMap<>();
        //获取商品优惠信息
        List<SkuCouponMatchVO> skuCouponMatchVOS = getSkuDiscountInfo(CollectionUtil.newArrayList(sku)
                , publicId.getCityWhId(), publicId.getPlaceId());
        if (CollectionUtil.isNotEmpty(skuCouponMatchVOS)) {
            skuDiscount = skuCouponMatchVOS.stream().collect(Collectors.toMap(SkuCouponMatchVO::getSkuId, SkuCouponMatchVO::getDiscountAmount, (v1, v2) -> v1));
        }
        SupplierSkuInfoVo supplierSkuInfoVo = this.fillInfo(skuVo);
        BigDecimal skuDiscountAmount = skuDiscount.getOrDefault(skuVo.getSkuId(), BigDecimal.ZERO);
        BigDecimal price = skuVo.getPrice().subtract(skuDiscountAmount);
        price = price.subtract(skuVo.getSubsidyAmount());
        supplierSkuInfoVo.setPrice(price.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : price);
        BigDecimal netWeightPrice = supplierSkuInfoVo.getPrice().divide(skuVo.getSpuNetWeight(), 2, RoundingMode.HALF_UP);
        //限时折扣
        RemoteMkActivitySkuDiscountBo discountBo = new RemoteMkActivitySkuDiscountBo();
        discountBo.setRegionId(skuVo.getRegionWhId());
        discountBo.setCityWhId(publicId.getCityWhId());
        if (publicId.getPlaceId() != null) {
            RemoteCityWhPlaceVo remoteCityWhPlaceVo = remoteCityWhPlaceService.queryById(publicId.getPlaceId());
            Long parentPlaceId = remoteCityWhPlaceVo.getParentPlaceId();
            if (Objects.nonNull(parentPlaceId)) {
                remoteCityWhPlaceVo = remoteCityWhPlaceService.queryById(parentPlaceId);
            }
            List<Long> logisticsId = remoteRegionLogisticsService.getLogisticsId(remoteCityWhPlaceVo.getId());
            if (CollectionUtil.isNotEmpty(logisticsId)) {
                discountBo.setLogisticsId(logisticsId.get(0));
            }
        }
        discountBo.setSaleDate(supplierSkuInfoVo.getSaleDate());
        RemoteMkActivitySkuBo skuBo = new RemoteMkActivitySkuBo();
        skuBo.setSupplierSkuId(supplierSkuInfoVo.getId());
        discountBo.setSupplierSkuList(Collections.singletonList(skuBo));
        List<RemoteMkActivitySkuDiscountVo> discountList = discountSummaryService.getSkuDiscountIdList(discountBo);
        if (CollectionUtil.isNotEmpty(discountList)){
            RemoteMkActivitySkuDiscountVo discountVo = discountList.get(0);
            int count = discountVo.getTargetQuantity().intValue() - supplierSkuInfoVo.getSold() - supplierSkuInfoVo.getLockStock();
            if (count > 0 && supplierSkuInfoVo.getStock() > 0) {
                supplierSkuInfoVo.setHasLimitDiscount(discountVo.getHasLimitDiscount());
                supplierSkuInfoVo.setDiscountCount(count > supplierSkuInfoVo.getStock() ? supplierSkuInfoVo.getStock() : count);
                BigDecimal scale = supplierSkuInfoVo.getPrice().multiply(discountVo.getDiscountRate()).setScale(2, RoundingMode.HALF_UP);
                scale = scale.compareTo(BigDecimal.valueOf(0.01)) < 0 ? BigDecimal.valueOf(0.01) : scale;
                supplierSkuInfoVo.setPriceFree(supplierSkuInfoVo.getPrice().subtract(scale).compareTo(discountVo.getMaxDiscountAmount()) > 0
                        ? supplierSkuInfoVo.getPrice().subtract(discountVo.getMaxDiscountAmount()) : scale);
                netWeightPrice = supplierSkuInfoVo.getPriceFree().divide(skuVo.getSpuNetWeight(), 2, RoundingMode.HALF_UP);
//            price = supplierSkuInfoVo.getPriceFree();
            }
        }
        if (netWeightPrice.compareTo(BigDecimal.ZERO) == 0) {
            netWeightPrice = new BigDecimal("0.01");
        }
        supplierSkuInfoVo.setNetWeightPrice(netWeightPrice);
        try {
            supplierSkuInfoVo.setDailyPriceVoList(getDailyPriceVos(skuVo, price));
        } catch (Exception e) {
            log.error("商品详情显示近7天价格曲线出错", e);
        }
        // 获取商品使用的客户门店类型
        String customerStoreTypes = supplierSkuInfoVo.getCustomerStoreTypes();
        if (StrUtil.isNotBlank(customerStoreTypes)){
            supplierSkuInfoVo.setCustomerStoreTypeValues(CustomerStoreTypeEnum.getNamesByIds(customerStoreTypes));
        }
        // 获取商品的行情预测
        String marketForecastType = skuVo.getMarketForecastType();
        if (StrUtil.isNotBlank(marketForecastType)){
            supplierSkuInfoVo.setMarketForecastTypeValues(MarketForecast.getNamesByIds(marketForecastType));
        }
        return supplierSkuInfoVo;
    }

    /**
     * 商品详情显示近7天价格曲线
     *
     * @param skuVo
     * @return
     */
    private List<DailyPriceVo> getDailyPriceVos(SupplierSkuVo skuVo, BigDecimal price) {
        LocalDate endDate = Optional.ofNullable(getSaleDate(skuVo.getRegionWhId()))
                .orElseThrow(() -> new ServiceException("总仓销售结束时间为空"));//结束日期
        // 构建一周的时间范围
        LocalDate startDate = endDate.minusDays(6);
        List<LocalDate> dates = startDate.datesUntil(endDate.plusDays(1)).toList();
        List<SupplierSkuVo> priceList = supplierSkuMapper.getSupplierSkuByParam(skuVo.getSkuId(), startDate, endDate);
        // 获取supplierSku集合
        List<Long> supplierSkuIds = priceList.stream().map(SupplierSkuVo::getId).collect(Collectors.toList());

        // 获取supplierSku对应的优惠
        List<SupplierSkuSubsidyAudit> resultVos = supplierSkuSubsidyAuditMapper.selectMaxDiscountPrice(supplierSkuIds);
        // 获取最大价格优惠
        Map<LocalDate, BigDecimal> maxDiscountPrices = Optional.ofNullable(resultVos)
                .orElse(Collections.emptyList())
                .stream()
                .filter(vo -> vo != null
                        && vo.getSaleDate() != null
                        && vo.getMaxSubsidyAmount() != null)
                .collect(Collectors.toMap(SupplierSkuSubsidyAudit::getSaleDate,
                        SupplierSkuSubsidyAudit::getMaxSubsidyAmount,
                        (v1, v2) -> v1));


        Map<LocalDate, BigDecimal> priceMap = Optional.ofNullable(priceList)
                .orElse(Collections.emptyList())
                .stream()
                .collect(Collectors.toMap(
                        SupplierSkuVo::getSaleDate,
                        SupplierSkuVo::getPrice,
                        (v1, v2) -> v1
                ));

        List<DailyPriceVo> list = dates.stream().map(x -> {
            DailyPriceVo d = new DailyPriceVo();
            d.setDate(x);
            BigDecimal currentPrice = priceMap.getOrDefault(x, BigDecimal.ZERO);
            BigDecimal discountPrice = maxDiscountPrices.getOrDefault(x, BigDecimal.ZERO);
            BigDecimal finalPrice = currentPrice.subtract(discountPrice);
            if (finalPrice.compareTo(BigDecimal.ZERO) > 0) {
                d.setPrice(finalPrice);
            } else {
                d.setPrice(BigDecimal.ZERO);
            }
            return d;
        }).toList();

        List<BigDecimal> prices = list.stream().map(DailyPriceVo::getPrice).toList();
        List<BigDecimal> smoothed = smoothAllMiddleZeros(prices);
        for (int i = 0; i < dates.size(); i++) {
            list.get(i).setSmoothPrice(smoothed.get(i));
        }
        return list;
    }

    /**
     * 平滑价格
     * @param prices
     * @return
     */
    public static List<BigDecimal> smoothAllMiddleZeros(List<BigDecimal> prices) {
        int n = prices.size();
        List<BigDecimal> result = new ArrayList<>(prices);

        int i = 0;
        while (i < n) {
            if (prices.get(i).compareTo(BigDecimal.ZERO) == 0) {
                // 找到连续0的区间 [start, end)
                int start = i;
                int end = i;
                while (end < n && prices.get(end).compareTo(BigDecimal.ZERO) == 0) {
                    end++;
                }
                // 判断区间前后是否都为大于0的价格
                if (start > 0 && end < n
                        && prices.get(start - 1).compareTo(BigDecimal.ZERO) > 0
                        && prices.get(end).compareTo(BigDecimal.ZERO) > 0) {
                    // 平滑值 = (左+右)/2
                    BigDecimal avg = prices.get(start - 1).add(prices.get(end))
                            .divide(BigDecimal.valueOf(2), 2, RoundingMode.HALF_UP);
                    for (int j = start; j < end; j++) {
                        result.set(j, avg);
                    }
                }
                i = end;
            } else {
                i++;
            }
        }
        return result;
    }


    /**
     * 查询供应商销售批次商品列表
     */
    @Override
    public TableDataInfo<SupplierSkuVo> queryPageList(SupplierSkuPageBo bo) {
        Page<SupplierSkuVo> result = supplierSkuMapper.queryPage(bo, bo.build());
        if (result.getRecords() != null && result.getRecords().size() > 0) {
            //根据供应商商品销售批次id集合和类型查询供应商商品销售批次文件信息(查询商品展示图片)
            List<Long> supplierSkuIdList = result.getRecords().stream().map(SupplierSkuVo::getId).toList();
            QuerySupplierSkuFileBo fileBo = new QuerySupplierSkuFileBo();
            fileBo.setSupplierSkuIdList(supplierSkuIdList);
            List<SupplierSkuFileVo> fileVoList = supplierSkuFileMapper.queryList(fileBo);
            //处理数据
            Map<Long, List<SupplierSkuFileVo>> fileMap = fileVoList.stream().collect(Collectors.groupingBy(SupplierSkuFileVo::getSupplierSkuId));
            //处理出参
            for (SupplierSkuVo skuVo : result.getRecords()) {
                skuVo.tranSpuStandardsName().buildSkuStandardsList();
                List<SupplierSkuFileVo> fileList = fileMap.get(skuVo.getId());
                skuVo.setFileList(fileList);
                if (fileList != null && fileList.size() > 0) {
                    for (SupplierSkuFileVo file : fileList) {
                        if (SupplierSkuFileTypeEnum.TYPE1.getCode().equals(file.getType())) {
                            skuVo.setImgUrl(file.getFileUrl());
                            break;
                        }
                    }
                }
                // 规格转换 - db text jsonString to List
                skuVo.tranSpuStandardsName().buildSkuStandardsList();
                // 区域简称
                if (StringUtils.isNotBlank(skuVo.getAreaCode()) && StringUtils.isBlank(skuVo.getShortProducer())) {
                    RemotePubAreaVO remotePubAreaVO = remoteBasPubAreaService.queryByCode(skuVo.getAreaCode());
                    if (remotePubAreaVO != null) {
                        skuVo.setShortProducer(remotePubAreaVO.getAreaAlias());
                    }
                }
            }
            // 填充档口信息
            fillDeptSupplierInfo(result.getRecords());
        }
        return TableDataInfo.build(result);
    }

    /**
     * 填充档口信息
     *
     * @param result
     */
    private void fillDeptSupplierInfo(List<SupplierSkuVo> result) {
        if (CollUtil.isEmpty(result)) {
            return;
        }
        List<Long> supplierIds = result.stream().map(SupplierSkuVo::getSupplierId).distinct().toList();
        Map<Long, RemoteSupplierVo> supplierMap = remoteSupplierService.getSupplierByIds(supplierIds).stream().collect(Collectors.toMap(RemoteSupplierVo::getId, Function.identity()));
        result.forEach(e -> e.setSupplierAlias(supplierMap.getOrDefault(e.getSupplierId(), new RemoteSupplierVo()).getAlias()));

        List<Long> supplierDeptIdList = result.stream().map(SupplierSkuVo::getSupplierDeptId).filter(Objects::nonNull).filter(v -> v > 0L).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(supplierDeptIdList)) {
            return;
        }
        //填充档口信息
        Map<Long, CodeNameVo> deptNameMap = remoteDeptService.getDeptCodeNameMap(supplierDeptIdList);
        if (CollUtil.isEmpty(deptNameMap)) {
            return;
        }
        for (SupplierSkuVo record : result) {
            CodeNameVo vo = deptNameMap.get(record.getSupplierDeptId());
            if (Objects.nonNull(vo)) {
                record.setSupplierDeptName(vo.getName());
                record.setSupplierDeptCode(vo.getCode());
            }
        }
    }


    private void fillDeptSupplierInfo(SupplierSkuVo result) {
        if (Objects.isNull(result) || Objects.isNull(result.getSupplierDeptId()) || result.getSupplierDeptId() == 0L) {
            return;
        }
        RemoteDeptDetailVo byDeptId = remoteDeptService.getByDeptId(result.getSupplierDeptId());
        if (Objects.isNull(byDeptId)) {
            return;
        }
        result.setSupplierDeptName(byDeptId.getDeptName());
        result.setSupplierDeptCode(byDeptId.getDeptCode());
    }


    /**
     * 集采小程序销售批次列表接口
     *
     * @param bo
     * @return
     */
    @Override
    public TableDataInfo<QuerySalePageVo> querySalePage(QuerySalePageBo bo) {
        if (CollectionUtil.isNotEmpty(bo.getCategoryIdList())) {
            List<Category> categoryList = categoryMapper.getByParentIds(bo.getCategoryIdList());
            if (categoryList != null && categoryList.size() > 0) {
                List<Long> categoryIdList = categoryList.stream().filter(l -> l.getStatus().equals(1)).map(Category::getId).toList();
                bo.setCategoryThreeIdList(categoryIdList);
            } else {
                return TableDataInfo.build();
            }
        }
        bo.setStatus(SupplierSkuStatusEnum.STATUS4.getCode());

        // 过滤城市仓禁卖
        if (bo.getCityWhId() != null && bo.getCityWhId() != 0L) {
            bo.setNotInSkuIdList(supplierSkuDisableCityWhMapper.getSkuIdsByCityWhId(bo.getCityWhId(), null));
        }
        // 寿光-产地商品，要考虑新商户
        if (Objects.equals(bo.getShouguangVegetables(), 1)) {
            List<Long> supplierIds = remoteSupplierService.selectSupIdByNewRegister(productProperties.getSupplierStartDay());
            bo.setNewMerchantIds(supplierIds);
        }
        // 转换客户门店类型
        String customerStoreType = bo.getCustomerStoreType();
        Page<QuerySalePageVo> result = supplierSkuMapper.querySalePage(bo, bo.build());
        if (result != null && !result.getRecords().isEmpty()) {
            List<Long> supplierSkuIdList = result.getRecords().stream().map(QuerySalePageVo::getId).toList();
            List<Long> skuIdList = new ArrayList<>();
            List<Long> supplierIdList = new ArrayList<>();
            List<Long> spulierDeptIdList = new ArrayList<>();
            List<Long> regionWhIds = new ArrayList<>();
            List<RemoteMkActivitySkuBo> supplierSkuList = new ArrayList<>();
            //获取库存信息
            List<SupplierSkuStockVo> stockVos = supplierSkuStockMapper.selectBySupplierSkuIds(supplierSkuIdList);
            Map<Long, SupplierSkuStockVo> stockMap = stockVos.stream().collect(Collectors.toMap(SupplierSkuStockVo::getSupplierSkuId, Function.identity(), (v1, v2) -> v1));
            for (QuerySalePageVo vo : result.getRecords()){
                RemoteMkActivitySkuBo skuBo = new RemoteMkActivitySkuBo();
                if (!supplierIdList.contains(vo.getSupplierId())){
                    supplierIdList.add(vo.getSupplierId());
                }
                if (vo.getSupplierDeptId() != null && vo.getSupplierDeptId() != 0L && !spulierDeptIdList.contains(vo.getSupplierDeptId())){
                    spulierDeptIdList.add(vo.getSupplierDeptId());
                }
                if (vo.getProvideRegionWhId() != null && vo.getProvideRegionWhId() != 0L && !regionWhIds.contains(vo.getProvideRegionWhId())){
                    regionWhIds.add(vo.getProvideRegionWhId());
                }
                if (vo.getSkuId() != null && !skuIdList.contains(vo.getSkuId())){
                    skuIdList.add(vo.getSkuId());
                }
                //处理库存
                if (stockMap.containsKey(vo.getId())) {
                    vo.setStock(stockMap.get(vo.getId()).getStock());
                    vo.setSold(stockMap.get(vo.getId()).getSold());
                    vo.setLockStock(stockMap.get(vo.getId()).getLockStock());
                }
                skuBo.setSupplierSkuId(vo.getId());
                supplierSkuList.add(skuBo);
            }
            //根据供应商商品销售批次id集合和类型查询供应商商品销售批次文件信息(查询商品展示图片)
            QuerySupplierSkuFileBo fileBo = new QuerySupplierSkuFileBo();
            fileBo.setSupplierSkuIdList(supplierSkuIdList);
            fileBo.setTypeList(List.of(SupplierSkuFileTypeEnum.TYPE1.getCode()));
            List<SupplierSkuFileVo> fileVoList = supplierSkuFileMapper.queryList(fileBo);

            List<Sku> skuList = skuMapper.selectBatchIds(skuIdList);

            //处理数据
            Map<Long, SupplierSkuFileVo> fileMap = fileVoList.stream().collect(Collectors.toMap(SupplierSkuFileVo::getSupplierSkuId, Function.identity(), (v1, v2) -> v1));
            LoginUser user = LoginHelper.getLoginUser();
            if (user != null) {
                List<RemoteCartItemCountVO> cartList = remoteCartItemService.getCartItemListInfo(user.getUserId(), supplierSkuIdList);
                if (cartList != null && !cartList.isEmpty()) {
                    Map<Long, RemoteCartItemCountVO> cartMap = cartList.stream().collect(Collectors.toMap(RemoteCartItemCountVO::getSkuId, Function.identity(), (key1, key2) -> key2));
                    for (QuerySalePageVo pageVo : result.getRecords()) {
                        RemoteCartItemCountVO cart = cartMap.get(pageVo.getSkuId());
                        if (cart != null) {
                            pageVo.setCartCount(cart.getCount());
                        }
                    }
                }
            }
            // 供应商信息
            List<RemoteSupplierVo> supplierVoList = remoteSupplierService.getSupplierByIds(supplierIdList);
            Map<Long, RemoteSupplierVo> supplierVoMap = supplierVoList.stream().filter(Objects::nonNull).collect(Collectors.toMap(RemoteSupplierVo::getId, Function.identity(), (v1, v2) -> v1));
            // 部门信息
            Map<Long, CodeNameVo> spulierDeptIdMap = new HashMap<>();
            if (CollUtil.isNotEmpty(spulierDeptIdList)) {
                Map<Long, CodeNameVo> deptCodeNameMap = remoteDeptService.getDeptCodeNameMap(spulierDeptIdList);
                if (CollUtil.isNotEmpty(deptCodeNameMap)) {
                    spulierDeptIdMap.putAll(deptCodeNameMap);
                }
            }

            // 供货总仓名称
            Map<Long, String> regionWhVoMap = new HashMap<>();
            List<RemoteRegionWhVo> regionWhVos;
            if (CollectionUtil.isNotEmpty(regionWhIds)) {
                regionWhVos = remoteRegionWhService.selectRegionWhInfoByIds(regionWhIds);
                regionWhVoMap = regionWhVos.stream().collect(Collectors.toMap(RemoteRegionWhVo::getId
                        , RemoteRegionWhVo::getRegionWhName, (n1, n2) -> n2));
            }

            // 获取商品参与的免运活动
            LoginUser loginUser = LoginHelper.getLoginUser();
            Map<Long, RemoteRuleFreightPriceVo> longRemoteRuleFreightPriceVoMap = new HashMap<>();
            RemoteRegionWhVo remoteRegionWhVo = remoteRegionWhService.queryById(bo.getRegionWhIdList().get(0));
            LocalDate saleDate = SaleDateUtil.getSaleDate(remoteRegionWhVo.getSalesTimeEnd());
            if (CollectionUtil.isNotEmpty(bo.getRegionWhIdList()) && Objects.nonNull(loginUser)) {
                longRemoteRuleFreightPriceVoMap = this.querySkuFreightPrice(loginUser, bo.getCityWhId(), bo.getPlaceId()
                        , remoteRegionWhVo, result.getRecords());
            }
            Map<Long, BigDecimal> skuDiscount = new HashMap<>();
            //获取商品优惠信息
            List<SkuCouponMatchVO> skuCouponMatchVos = getSkuDiscountInfo(skuList, bo.getCityWhId(), bo.getPlaceId());
            if (CollectionUtil.isNotEmpty(skuCouponMatchVos)) {
                skuDiscount = skuCouponMatchVos.stream().collect(Collectors.toMap(SkuCouponMatchVO::getSkuId, SkuCouponMatchVO::getDiscountAmount, (v1, v2) -> v1));
            }
            RemoteMkActivitySkuDiscountBo discountBo = new RemoteMkActivitySkuDiscountBo();
            discountBo.setRegionId(bo.getRegionWhIdList().get(0));
            discountBo.setCityWhId(bo.getCityWhId());
            RemoteCityWhPlaceVo remoteCityWhPlaceVo = remoteCityWhPlaceService.queryById(bo.getPlaceId());
            Long parentPlaceId = remoteCityWhPlaceVo.getParentPlaceId();
            if (Objects.nonNull(parentPlaceId)) {
                remoteCityWhPlaceVo = remoteCityWhPlaceService.queryById(parentPlaceId);
            }
            List<Long> logisticsId = remoteRegionLogisticsService.getLogisticsId(remoteCityWhPlaceVo.getId());
            if (CollectionUtil.isNotEmpty(logisticsId)) {
                discountBo.setLogisticsId(logisticsId.get(0));
            }
            discountBo.setSaleDate(saleDate);
            discountBo.setSupplierSkuList(supplierSkuList);
            List<RemoteMkActivitySkuDiscountVo> discountList = discountSummaryService.getSkuDiscountIdList(discountBo);
            Map<Long, RemoteMkActivitySkuDiscountVo> discountVoMap = discountList.stream().collect(Collectors.toMap(RemoteMkActivitySkuDiscountVo::getSupplierSkuId, Function.identity()));
            //处理出参
            for (QuerySalePageVo skuVo : result.getRecords()) {
                if (StringUtils.isNotBlank(skuVo.getCategoryPathName())) {
                    skuVo.setCategoryName(skuVo.getCategoryPathName().split("/")[3]);
                }
                BigDecimal skuDiscountAmount = skuDiscount.getOrDefault(skuVo.getSkuId(), BigDecimal.ZERO);
                BigDecimal price = skuVo.getPrice().subtract(skuDiscountAmount);
                //减掉补贴
                price = price.subtract(skuVo.getSubsidyAmount());
                skuVo.setPrice(price.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : price);

                BigDecimal netWeightPrice = skuVo.getPrice().divide(skuVo.getSpuNetWeight(), 2, RoundingMode.HALF_UP);

                SupplierSkuFileVo fileVo = fileMap.get(skuVo.getId());
                if (fileVo != null) {
                    skuVo.setImgUrl(fileMap.get(skuVo.getId()).getFileUrl());
                }
                RemoteSupplierVo supplierVo = supplierVoMap.get(skuVo.getSupplierId());
                if (supplierVo != null) {
                    skuVo.setSupplierSimpleCode(supplierVo.getSimpleCode());
                    skuVo.setSupplierAlias(supplierVo.getAlias());
                }
                CodeNameVo codeNameVo = spulierDeptIdMap.get(skuVo.getSupplierDeptId());
                if (codeNameVo != null) {
                    skuVo.setSupplierSimpleCode(codeNameVo.getCode());
                    skuVo.setSupplierDeptName(codeNameVo.getName());
                }
                // 构建规格参数
                skuVo.buildSkuStandardsList().buildSpuStandardsName();
                // 处理售后规则
                CategoryAfterTypeEnum afterTypeEnum = CategoryAfterTypeEnum.loadByCode(skuVo.getAfterSaleType());
                if (afterTypeEnum != null) {
                    skuVo.setAfterSaleName(afterTypeEnum.getName());
                    skuVo.setAfterSaleDesc(afterTypeEnum.getDesc());
                }
                // 区域简称
                if (StringUtils.isNotBlank(skuVo.getAreaCode()) && StringUtils.isBlank(skuVo.getShortProducer())) {
                    RemotePubAreaVO remotePubAreaVO = remoteBasPubAreaService.queryByCode(skuVo.getAreaCode());
                    if (remotePubAreaVO != null) {
                        skuVo.setShortProducer(remotePubAreaVO.getAreaAlias());
                    }
                }
                // 处理供货总仓名称
                String rwName = regionWhVoMap.get(skuVo.getProvideRegionWhId());
                if (StringUtils.isNotBlank(rwName)) {
                    skuVo.setProvideRegionWhName(rwName);
                }
                RemoteRuleFreightPriceVo remoteRuleFreightPriceVo = longRemoteRuleFreightPriceVoMap.get(skuVo.getId());
                if (Objects.nonNull(remoteRuleFreightPriceVo)) {
                    skuVo.setIsFreightDiscount(1);
                    skuVo.setFreightDiscountMinNum(remoteRuleFreightPriceVo.getMinNum());
                }
                // 前端隐藏商户编号&店铺
                List<Long> hideMerchantRegionWhIdList = noCheckGoodsProperties.getHideMerchantRegionWhIdList();
                if (hideMerchantRegionWhIdList.contains(skuVo.getRegionWhId())) {
                    skuVo.setHideMerchant(1);
                }

                // 特价商品降价幅度
                if (Objects.equals(skuVo.getBargain(), 1)) {
                    if (skuVo.getSubsidyAmount().compareTo(BigDecimal.ZERO) > 0) {
                        skuVo.setBargainDownPrice(skuVo.getBargainDownPrice().add(skuVo.getSubsidyAmount()));
                    } else {
                        skuVo.setBargainDownPrice(skuVo.getMarketPrice().subtract(skuVo.getPrice()));
                    }
                }
                if (discountVoMap.containsKey(skuVo.getId()) && skuVo.getStock() >0){
                    RemoteMkActivitySkuDiscountVo discountVo = discountVoMap.get(skuVo.getId());
                    int count = discountVo.getTargetQuantity().intValue() - skuVo.getSold() - skuVo.getLockStock();
                    if (count > 0) {
                        skuVo.setHasLimitDiscount(discountVo.getHasLimitDiscount());
                        skuVo.setDiscountCount(count > skuVo.getStock() ? skuVo.getStock() : count);
                        BigDecimal scale = skuVo.getPrice().multiply(discountVo.getDiscountRate()).setScale(2, RoundingMode.HALF_UP);
                        scale = scale.compareTo(BigDecimal.valueOf(0.01)) < 0 ? BigDecimal.valueOf(0.01) : scale;
                        skuVo.setPriceFree(skuVo.getPrice().subtract(scale).compareTo(discountVo.getMaxDiscountAmount()) > 0
                                ? skuVo.getPrice().subtract(discountVo.getMaxDiscountAmount()) : scale);
                        netWeightPrice = skuVo.getPriceFree().divide(skuVo.getSpuNetWeight(), 2, RoundingMode.HALF_UP);
                    }
                }
                if (netWeightPrice.compareTo(BigDecimal.ZERO) == 0) {
                    netWeightPrice = new BigDecimal("0.01");
                }
                skuVo.setNetWeightPrice(netWeightPrice);
            }
        }
        return TableDataInfo.build(result);
    }

    //获取sku的优惠信息
    private List<SkuCouponMatchVO> getSkuDiscountInfo(List<Sku> skuVos, Long cityWhId, Long placeId) {
        if (CollectionUtil.isEmpty(skuVos)) {
            return new ArrayList<>();
        }
        Sku sku = skuVos.get(0);
        RemoteSkuDiscountMatchDTO skuDiscountMatchDTO = new RemoteSkuDiscountMatchDTO();
        skuDiscountMatchDTO.setSaleDate(sku.getSaleDate());
        skuDiscountMatchDTO.setRegionWhId(sku.getRegionWhId());
        skuDiscountMatchDTO.setCityWhId(cityWhId);
        skuDiscountMatchDTO.setPlaceId(placeId);
        //查询物流线
        RemoteRegionLogisticsQueryBo logisticsQueryBo = new RemoteRegionLogisticsQueryBo();
        logisticsQueryBo.setRegionWhId(sku.getRegionWhId());
        logisticsQueryBo.setCityWhId(cityWhId);
        logisticsQueryBo.setPlaceId(placeId);
        RemoteRegionLogisticsVo logisticsVo = remoteRegionLogisticsService.queryList(logisticsQueryBo)
                .stream().findFirst().orElse(null);
        if (Objects.nonNull(logisticsVo)) {
            skuDiscountMatchDTO.setLogisticsId(logisticsVo.getId());
        }
        List<Long> categoryIds = skuVos.stream().flatMap(item -> CollectionUtil.toList(item.getCategoryId(), item.getCategoryIdLevel2()).stream()).collect(Collectors.toList());
        List<CategoryVO> categoryVOS = categoryMapper.selectVoBatchIds(categoryIds);
        Map<Long, String> categoryVOMap = categoryVOS.stream().collect(Collectors.toMap(CategoryVO::getId
                , CategoryVO::getCode, (n1, n2) -> n1));
        List<RemoteSkuDiscountItemDTO> remoteSkuDiscountItemDTOS = skuVos.stream().map(item -> {
            RemoteSkuDiscountItemDTO itemDTO = new RemoteSkuDiscountItemDTO();
            itemDTO.setSkuId(item.getId());
            itemDTO.setSkuCode(item.getSkuCode());
            List<String> categoryCodes = new ArrayList<>();
            if (StringUtils.isNotBlank(categoryVOMap.get(item.getCategoryId()))) {
                categoryCodes.add(categoryVOMap.get(item.getCategoryId()));
            }
            if (StringUtils.isNotBlank(categoryVOMap.get(item.getCategoryIdLevel2()))) {
                categoryCodes.add(categoryVOMap.get(item.getCategoryIdLevel2()));
            }
            itemDTO.setCategoryCodes(categoryCodes);
            itemDTO.setSpuCode(item.getSpuCode());
            itemDTO.setSupplierSpuCode(item.getSupplierSpuCode());
            itemDTO.setPrice(item.getPrice());
            return itemDTO;
        }).collect(Collectors.toList());

        skuDiscountMatchDTO.setSkuDiscountItemDTOS(remoteSkuDiscountItemDTOS);
        return discountService.getDiscountInfo(skuDiscountMatchDTO);
    }

    /**
     * 获取运费
     *
     * @param loginUser
     * @param cityWhId
     * @param placeId
     * @param regionWhInfo
     * @param skuVos
     * @return
     */
    private Map<Long, RemoteRuleFreightPriceVo> querySkuFreightPrice(LoginUser loginUser, Long cityWhId, Long placeId
            , RemoteRegionWhVo regionWhInfo, List<QuerySalePageVo> skuVos) {

        RemoteRegionLogisticsQueryBo logisticsQueryBo = new RemoteRegionLogisticsQueryBo();
        logisticsQueryBo.setRegionWhId(regionWhInfo.getId());
        logisticsQueryBo.setCityWhId(cityWhId);
        logisticsQueryBo.setPlaceId(placeId);
        List<RemoteRegionLogisticsVo> logisticsVoList = remoteRegionLogisticsService.queryList(logisticsQueryBo);
        if (CollectionUtil.isEmpty(logisticsVoList) || Objects.isNull(regionWhInfo)) {
            return new HashMap<>();
        }
        RemoteRegionLogisticsVo logisticsVo = logisticsVoList.get(0);

        RemoteRuleFreightServiceQueryBo queryBo = new RemoteRuleFreightServiceQueryBo();
        queryBo.setLogisticsId(logisticsVo.getId());
        queryBo.setCustomerId(loginUser.getRelationId());
        LocalDate saleDate = SaleDateUtil.getSaleDate(regionWhInfo.getSalesTimeEnd());
        queryBo.setSaleDate(saleDate);
        queryBo.setCityWhId(cityWhId);
        queryBo.setPriceMode(logisticsVo.getPriceMode());
        queryBo.setRegionWhId(regionWhInfo.getId());
        List<RemoteRuleFreightServiceSkuBo> skuBos = skuVos.stream().map(item -> {
            RemoteRuleFreightServiceSkuBo skuBo = new RemoteRuleFreightServiceSkuBo();
            skuBo.setSkuId(item.getId());
            skuBo.setCount(1);
            skuBo.setSpuId(item.getSpuId());
            return skuBo;
        }).collect(Collectors.toList());
        queryBo.setSkuIds(skuBos);
        //查询物流线
        Map<Long, RemoteRuleFreightPriceVo> longRemoteRuleFreightPriceVoMap = remoteRuleFreightServiceService.querySkuFreightPrice(queryBo);
        return longRemoteRuleFreightPriceVoMap.keySet().stream().filter(item -> {
            RemoteRuleFreightPriceVo remoteRuleFreightPriceVo = longRemoteRuleFreightPriceVoMap.get(item);
            if (remoteRuleFreightPriceVo.getFreightPrice().compareTo(logisticsVo.getFreightPrice()) > 0) {
                return false;
            }
            return true;
        }).collect(Collectors.toMap(key -> key, value -> longRemoteRuleFreightPriceVoMap.get(value)));
    }

    @Override
    public TableDataInfo<SupplierSkuSalePageVo> queryOnSaleSkuPage(SupplierSkuSalePageBo bo) {
        // 入参分类处理
        if (CollUtil.isNotEmpty(bo.getCategoryIdList())) {
            List<Long> categoryList = categoryMapper.findThreeIdListByTreeIntersection(bo.getCategoryIdList());
            if (CollUtil.isEmpty(categoryList)) {
                return TableDataInfo.build();
            }
            bo.setCategoryIdList(categoryList);
        }

        Page<SupplierSkuSalePageVo> result = supplierSkuMapper.queryOnSalePage(bo, bo.build());
        if (result.getRecords() != null && !result.getRecords().isEmpty()) {
            //获取图片&文件信息
            List<Long> supplierSkuIdList = result.getRecords().stream().map(SupplierSkuSalePageVo::getId).distinct().toList();
            QuerySupplierSkuFileBo fileBo = new QuerySupplierSkuFileBo();
            fileBo.setSupplierSkuIdList(supplierSkuIdList);
            fileBo.setTypeList(List.of(SupplierSkuFileTypeEnum.TYPE1.getCode()));
            List<SupplierSkuFileVo> fileVoList = supplierSkuFileMapper.queryList(fileBo);
            Map<Long, SupplierSkuFileVo> fileMap = fileVoList.stream().collect(Collectors.toMap(SupplierSkuFileVo::getSupplierSkuId, Function.identity(), (v1, v2) -> v1));

            // 供应商信息
            List<Long> supplierIdList = result.getRecords().stream().map(SupplierSkuSalePageVo::getSupplierId).distinct().toList();
            List<RemoteSupplierVo> supplierVoList = remoteSupplierService.getSupplierByIds(supplierIdList);
            Map<Long, RemoteSupplierVo> supplierVoMap = supplierVoList.stream().filter(Objects::nonNull).collect(Collectors.toMap(RemoteSupplierVo::getId, Function.identity(), (v1, v2) -> v1));
            // 档口信息
            List<Long> spulierDeptIdList = result.getRecords().stream().map(SupplierSkuSalePageVo::getSupplierDeptId).filter(v -> v != null && v != 0L).distinct().collect(Collectors.toList());
            Map<Long, CodeNameVo> spulierDeptIdMap = new HashMap<>();
            if (CollUtil.isNotEmpty(spulierDeptIdList)) {
                Map<Long, CodeNameVo> deptCodeNameMap = remoteDeptService.getDeptCodeNameMap(spulierDeptIdList);
                if (CollUtil.isNotEmpty(deptCodeNameMap)) {
                    spulierDeptIdMap.putAll(deptCodeNameMap);
                }
            }

            //填充信息
            for (SupplierSkuSalePageVo skuVo : result.getRecords()) {
                SupplierSkuFileVo fileVo = fileMap.get(skuVo.getId());
                if (fileVo != null) {
                    skuVo.setImgUrl(fileMap.get(skuVo.getId()).getFileUrl());
                }
                RemoteSupplierVo supplierVo = supplierVoMap.get(skuVo.getSupplierId());
                if (supplierVo != null) {
                    skuVo.setSupplierSimpleCode(supplierVo.getSimpleCode());
                    skuVo.setSupplierAlias(supplierVo.getAlias());
                }
                CodeNameVo codeNameVo = spulierDeptIdMap.get(skuVo.getSupplierDeptId());
                if (codeNameVo != null) {
                    skuVo.setSupplierSimpleCode(codeNameVo.getCode());
                    skuVo.setSupplierDeptName(codeNameVo.getName());
                }
                // 构建规格参数
                skuVo.buildSkuStandardsList().buildSpuStandardsName();
                // 处理售后规则
                CategoryAfterTypeEnum afterTypeEnum = CategoryAfterTypeEnum.loadByCode(skuVo.getAfterSaleType());
                if (afterTypeEnum != null) {
                    skuVo.setAfterSaleName(afterTypeEnum.getName());
                    skuVo.setAfterSaleDesc(afterTypeEnum.getDesc());
                }
            }
        }
        return TableDataInfo.build(result);
    }

    @Override
    public TableDataInfo<SupplierSkuVo> queryPage(SupplierSkuSimplePageBo bo) {
        LoginUser user = LoginHelper.getLoginUser();
        if (user == null) {
            throw new ServiceException("非法请求");
        }
//        SupplierSkuPageBo queryBo = MapstructUtils.convert(bo, SupplierSkuPageBo.class);
        QueryLastListBo queryBo = MapstructUtils.convert(bo, QueryLastListBo.class);

        // 供应商&档口数据过滤
        if (bo.isByLoginInfo()) {
            queryBo.setSupplierIdList(Lists.newArrayList(user.getRelationId()));
            if (Objects.nonNull(user.getDeptId()) && !Objects.equals(user.getDeptId(), 0L)) {
                queryBo.setSupplierDeptIdList(Lists.newArrayList(user.getDeptId()));
            }
        }

        // 总仓数据过滤 - 仅自己
        if (bo.isOwn()) {
            queryBo.setBuyerCodeList(Lists.newArrayList(user.getUserCode()));
        }

        // 供应商要传总仓id
        if (CollUtil.isEmpty(bo.getRegionWhIdList()) && bo.getRegionWhId() != null
        ) {
            queryBo.setRegionWhIdList(Lists.newArrayList(bo.getRegionWhId()));
        }
        if (bo.getIsSaleHide() != null && bo.getIsSaleHide() == 1) {
            queryBo.setRegionWhIdList(null);
        }

        // 采购组数据过滤
        if (Objects.equals(bo.getIsManage(), 1)) {
//            log.keyword("buy_group", user.getUsername()).info("user.getUsername() isOwn={}, isAdmin={}", bo.isOwn(), RoleUtil.isRegionAdmin(user.getRolePermission()));
//            if (!RoleUtil.isRegionAdmin(user.getRolePermission())) {
//                RemoteRegionWhVo regionWhVo = remoteRegionWhService.queryById(bo.getRegionWhId());
//
//                List<Long> category2IdList = bo.isOwn()
//                        // 本人所属分类
//                        ? buyerGroupDetailMapper.selectMidCategoryIdList(regionWhVo.getRegionWhCode(), user.getUserCode())
//                        // 本人所在采购组分类
//                        : buyerGroupDetailMapper.selectMidCategoryIdListByUserGroup(regionWhVo.getRegionWhCode(), user.getUserCode());
//                if(CollUtil.isNotEmpty(category2IdList)){
//                    queryBo.setCategory2IdList(category2IdList);
//                }
//                // 不在采购组看所有权限数据，不熔断。因为没有权限还能有列表的数据&菜单权限的都是管理员
//            }
        } else {
            // 供应商如果查的是 已驳回+待审核，则逆序排列 - 已驳回置顶
            if (CollUtil.isNotEmpty(bo.getStatusList())
                    && bo.getStatusList().contains(SupplierSkuStatusEnum.STATUS2.getCode())
                    && (bo.getStatusList().contains(SupplierSkuStatusEnum.STATUS1.getCode())
                    || bo.getStatusList().contains(SupplierSkuStatusEnum.STATUS8.getCode()))
            ) {
                queryBo.setOrderByStatusDesc(1);
            }
        }

        LocalDate saleDate = getSaleDate(bo.getRegionWhId());
        queryBo.setSaleDate(saleDate);
        // 排除配货商品
        queryBo.setExBusinessTypeList(Collections.singletonList(SupplierSkuBusinessTypeEnum.BUSINESS_TYPE40.getCode()));

        Page<SupplierSkuVo> vo = supplierSkuMapper.queryLastList(queryBo, bo.build());
        // 参数转换
        fillSupplierSkuVOPage(vo, saleDate);

        return TableDataInfo.build(vo);
    }

    /**
     * 供应商商品补贴审核分页
     * @param bo
     * @return
     */
    @Override
    public TableDataInfo<SupplierSkuVo> querySubsidyAuditPage(SupplierSkuSimplePageBo bo) {
        LoginUser user = LoginHelper.getLoginUser();
        if(user == null) {
            throw new ServiceException("非法请求");
        }
        QueryLastListBo queryBo = MapstructUtils.convert(bo, QueryLastListBo.class);

        // 供应商要传总仓id
        if(CollUtil.isEmpty(bo.getRegionWhIdList()) && bo.getRegionWhId() != null
        ) {
            queryBo.setRegionWhIdList(Lists.newArrayList(bo.getRegionWhId()));
        }

        LocalDate saleDate = getSaleDate(bo.getRegionWhId());

        Page<SupplierSkuVo> vo = supplierSkuMapper.querySubsidyAuditPage(queryBo, bo.build());
        // 参数转换
        fillSupplierSkuVOPage(vo, saleDate);

        return TableDataInfo.build(vo);
    }

    /**
     * 供货申请列表
     *
     * @param bo
     * @return
     */
    @Override
    public TableDataInfo<QuerySupSubscribePageVo> querySupSubscribePage(QuerySupSubscribePageBo bo) {
        LoginUser user = LoginHelper.getLoginUser();
        if (user == null) {
            throw new ServiceException("非法请求");
        }
        if (bo.isQuickUp()) {
            QueryLastListBo lastBo = MapstructUtils.convert(bo, QueryLastListBo.class);
            lastBo.setStatusList(null);
            lastBo.setSupplierIdList(Lists.newArrayList(user.getRelationId()));
            if (Objects.nonNull(user.getDeptId()) && !Objects.equals(user.getDeptId(), 0L)) {
                lastBo.setSupplierDeptIdList(Lists.newArrayList(user.getDeptId()));
            }
            lastBo.setSaleDateLt(getSaleDate(bo.getRegionWhId()));
            lastBo.setRegionWhIdList(Lists.newArrayList(bo.getRegionWhId()));
            // 排除配货商品
            lastBo.setExBusinessTypeList(Collections.singletonList(SupplierSkuBusinessTypeEnum.BUSINESS_TYPE40.getCode()));
            Page<SupplierSkuVo> vo = supplierSkuMapper.queryLastList(lastBo, bo.build());
            if (vo.getRecords() != null && vo.getRecords().size() > 0) {
                // 填充档口信息
                fillDeptSupplierInfo(vo.getRecords());
                //根据供应商商品销售批次id集合和类型查询供应商商品销售批次文件信息(查询商品展示图片)
                List<Long> supplierSkuIdList = vo.getRecords().stream().map(SupplierSkuVo::getId).toList();
                QuerySupplierSkuFileBo fileBo = new QuerySupplierSkuFileBo();
                fileBo.setSupplierSkuIdList(supplierSkuIdList);
                List<SupplierSkuFileVo> fileVoList = supplierSkuFileMapper.queryList(fileBo);
                //处理数据
                Map<Long, List<SupplierSkuFileVo>> fileMap = fileVoList.stream().collect(Collectors.groupingBy(SupplierSkuFileVo::getSupplierSkuId));
                //处理出参
                for (SupplierSkuVo skuVo : vo.getRecords()) {
                    skuVo.tranSpuStandardsName().buildSkuStandardsList();
//                    skuVo.setPrice(skuVo.getUpPrice());
                    skuVo.setStock(skuVo.getUpStock());
                    List<SupplierSkuFileVo> fileList = fileMap.get(skuVo.getId());
                    skuVo.setFileList(fileList);
                    if (fileList != null && fileList.size() > 0) {
                        for (SupplierSkuFileVo file : fileList) {
                            if (SupplierSkuFileTypeEnum.TYPE1.getCode().equals(file.getType())) {
                                skuVo.setImgUrl(file.getFileUrl());
                                break;
                            }
                        }
                    }
                }
            }
            List<QuerySupSubscribePageVo> pageVoList = MapstructUtils.convert(vo.getRecords(), QuerySupSubscribePageVo.class);
            TableDataInfo<QuerySupSubscribePageVo> result = TableDataInfo.build(pageVoList);
            result.setTotal(vo.getTotal());
            return result;
        } else {
            SupplierSkuPageBo queryBo = MapstructUtils.convert(bo, SupplierSkuPageBo.class);
            queryBo.setSupplierIdList(Lists.newArrayList(user.getRelationId()));
            if (Objects.nonNull(user.getDeptId()) && !Objects.equals(user.getDeptId(), 0L)) {
                queryBo.setSupplierDeptIdList(Lists.newArrayList(user.getDeptId()));
            }
            //获取销售日
            LocalDate saleDate = getSaleDate(bo.getRegionWhId());
            queryBo.setSaleDateStart(saleDate);
            queryBo.setSaleDateEnd(saleDate);
            queryBo.setRegionWhIdList(Lists.newArrayList(bo.getRegionWhId()));
            TableDataInfo<SupplierSkuVo> vo = this.queryPageList(queryBo);
            List<QuerySupSubscribePageVo> pageVoList = MapstructUtils.convert(vo.getRows(), QuerySupSubscribePageVo.class);
            TableDataInfo<QuerySupSubscribePageVo> result = TableDataInfo.build(pageVoList);
            result.setTotal(vo.getTotal());
            return result;
        }
    }

    @Override
    public TableDataInfo<SupplierSkuVo> queryPageByQuickUp(SupplierSkuSimplePageBo bo) {
        LoginUser user = LoginHelper.getLoginUser();
        if (user == null) {
            throw new ServiceException("非法请求");
        }
        QueryLastListBo lastBo = MapstructUtils.convert(bo, QueryLastListBo.class);
        lastBo.setOutSeason(0);
        // 所有供过货的
        lastBo.setStatusList(null);
        // 排除当前销售日
        lastBo.setSaleDateLt(getSaleDate(bo.getRegionWhId()));
        // 供应商&档口数据过滤
        if (bo.isByLoginInfo()) {
            lastBo.setSupplierIdList(Lists.newArrayList(user.getRelationId()));
            if (Objects.nonNull(user.getDeptId()) && !Objects.equals(user.getDeptId(), 0L)) {
                lastBo.setSupplierDeptIdList(Lists.newArrayList(user.getDeptId()));
            }
        }
        // 供应商要传总仓id
        if (CollUtil.isEmpty(bo.getRegionWhIdList()) && bo.getRegionWhId() != null) {
            lastBo.setRegionWhIdList(Lists.newArrayList(bo.getRegionWhId()));
        }
        // 总仓数据过滤 - 仅自己
        if (bo.isOwn()) {
            lastBo.setBuyerCodeList(Lists.newArrayList(user.getUserCode()));
        }

        LocalDate saleDate = getSaleDate(bo.getRegionWhId());
        lastBo.setSaleDate(saleDate);
        // 排除配货商品
        lastBo.setExBusinessTypeList(Collections.singletonList(SupplierSkuBusinessTypeEnum.BUSINESS_TYPE40.getCode()));
        Page<SupplierSkuVo> vo = supplierSkuMapper.queryLastList(lastBo, bo.build());
        fillSupplierSkuVOPage(vo, saleDate);
        return TableDataInfo.build(vo);
    }

    public void fillSupplierSkuVOPage(Page<SupplierSkuVo> vo, LocalDate saleDate) {
        List<SupplierSkuVo> records = vo.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            // 填充档口信息
            fillDeptSupplierInfo(records);
            //根据供应商商品销售批次id集合和类型查询供应商商品销售批次文件信息(查询商品展示图片)
            List<Long> supplierSkuIdList = records.stream().map(SupplierSkuVo::getId).toList();
            List<Long> skuIdList = records.stream().filter(e -> e.getSkuId() != null && e.getSkuId() > 0).map(SupplierSkuVo::getSkuId).toList();
            QuerySupplierSkuFileBo fileBo = new QuerySupplierSkuFileBo();
            fileBo.setSupplierSkuIdList(supplierSkuIdList);
            List<SupplierSkuFileVo> fileVoList = supplierSkuFileMapper.queryList(fileBo);
            // 库存
            List<RemoteSupplierSkuStockVo> remoteSupplierSkuStockVos = this.listSupplierSkuStockBySupplierIds(supplierSkuIdList);
            Map<Long, RemoteSupplierSkuStockVo> stockMap = remoteSupplierSkuStockVos.stream().collect(Collectors.toMap(RemoteSupplierSkuStockVo::getSupplierSkuId, e -> e, (key1, key2) -> key2));

            // 图片视频
            Map<Long, List<SupplierSkuFileVo>> fileMap = fileVoList.stream().collect(Collectors.groupingBy(SupplierSkuFileVo::getSupplierSkuId));
            // 云仓库存 - 优先与批次库存
            List<CwStockDetailVo> stockDetailVoList = skuIdList.isEmpty() ? new ArrayList<>() : cwStockDetailService.getBySkuIds(skuIdList);
            Map<Long, CwStockDetailVo> yunStockMap = stockDetailVoList.stream().collect(Collectors.toMap(e -> e.getSkuId(), e -> e, (a, b) -> b));
            //近3天（含今日）未上架过不允许打印条码
            Set<Long> skuIdSet = skuIdList.isEmpty() ? new HashSet<>()
                    : supplierSkuMapper.nearUpSkuIdMap(skuIdList, saleDate.plusDays(-3));

            Map<Long, String> provideRegionWhMap = new HashMap<>();
            List<Long> provideRegionWhIds = records.stream().map(SupplierSkuVo::getProvideRegionWhId).filter(item -> !Objects.equals(item, 0L)).distinct().collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(provideRegionWhIds)) {
                List<RemoteRegionWhVo> remoteRegionWhVos = remoteRegionWhService.selectRegionWhInfoByIds(provideRegionWhIds);
                provideRegionWhMap = remoteRegionWhVos.stream().collect(Collectors.toMap(RemoteRegionWhVo::getId, RemoteRegionWhVo::getRegionWhName));
            }

            // BI 报损 + 质检不合格
            Map<Long, RemoteSkuLossVo> lossMap = getRemoteSkuLossVoMap(skuIdList, saleDate);

            //处理出参
            for (SupplierSkuVo skuVo : records) {
//                    skuVo.setPrice(skuVo.getUpPrice());
                // 是否在当前销售日
                if (saleDate.isEqual(skuVo.getSaleDate())) {
                    skuVo.setOnSalesDay(1);
                }
                // 处理单价 - 列表要显示
                BigDecimal netWeightPrice = skuVo.getPrice().divide(skuVo.getSpuNetWeight(), 2, RoundingMode.HALF_UP);
                if (netWeightPrice.compareTo(BigDecimal.ZERO) == 0) {
                    netWeightPrice = new BigDecimal("0.01");
                }
                // 处理售后规则
                CategoryAfterTypeEnum afterTypeEnum = CategoryAfterTypeEnum.loadByCode(skuVo.getAfterSaleType());
                if (afterTypeEnum != null) {
                    skuVo.setAfterSaleName(afterTypeEnum.getName());
                    skuVo.setAfterSaleDesc(afterTypeEnum.getDesc());
                }
                skuVo.setNetWeightPrice(netWeightPrice);
                // 商品库存
                RemoteSupplierSkuStockVo stockVo = stockMap.get(skuVo.getId());
                skuVo.setStock(stockVo == null ? 0 : stockVo.getStock());
                skuVo.setBatchStock(skuVo.getStock());
                //供货总仓
                skuVo.setProvideRegionWhName(provideRegionWhMap.get(skuVo.getProvideRegionWhId()));
                // 云仓库存
                Long skuId = skuVo.getSkuId();
                CwStockDetailVo cwStockDetailVo = yunStockMap.get(skuId);
                if (cwStockDetailVo != null) {
                    Integer stock = Optional.ofNullable(cwStockDetailVo).map(CwStockDetailVo::getOnhandQty).map(BigDecimal::intValue).orElseGet(() -> 0);
                    Integer lockStock = Optional.ofNullable(cwStockDetailVo).map(CwStockDetailVo::getAllocationQty).map(BigDecimal::intValue).orElseGet(() -> 0);
                    Integer onWayStock = Optional.ofNullable(cwStockDetailVo).map(CwStockDetailVo::getOnwayQty).map(BigDecimal::intValue).orElseGet(() -> 0);
                    skuVo.setCloudOnWayStock(onWayStock);
                    skuVo.setCloudActualStock(stock);
                    skuVo.setCloudLockStock(lockStock);
                    skuVo.setCloudStock(stock - lockStock + onWayStock);
                }
                // 规格名称
                skuVo.tranSpuStandardsName().buildSkuStandardsList();
                List<SupplierSkuFileVo> fileList = fileMap.get(skuVo.getId());
                skuVo.setFileList(fileList);
                if (CollUtil.isNotEmpty(fileList)) {
                    for (SupplierSkuFileVo file : fileList) {
                        if (SupplierSkuFileTypeEnum.TYPE1.getCode().equals(file.getType())) {
                            skuVo.setImgUrl(file.getFileUrl());
                            break;
                        }
                    }
                }
                // 区域简称
                if (StringUtils.isNotBlank(skuVo.getAreaCode()) && StringUtils.isBlank(skuVo.getShortProducer())) {
                    RemotePubAreaVO remotePubAreaVO = remoteBasPubAreaService.queryByCode(skuVo.getAreaCode());
                    if (remotePubAreaVO != null) {
                        skuVo.setShortProducer(remotePubAreaVO.getAreaAlias());
                    }
                }
                // 是否可打印条形码
                skuVo.setAllowPrinting(skuIdSet.contains(skuId) ? 1 : 0);

                // 报损&质检
                SkuLossResult result = getSkuLossResult(lossMap, skuId);
                skuVo.setRemoteSkuLossVo(result.remoteSkuLossVo());
                skuVo.setRemoteSkuCard(result.remoteSkuCard());
            }
        }
    }

    @Override
    public TableDataInfo<SupplierSkuVo> queryDeliveryAuditPage(SupplierSkuSimplePageBo bo) {

        LoginUser user = LoginHelper.getLoginUser();
        if (user == null) {
            throw new ServiceException("非法请求");
        }


        // 总仓数据过滤 - 仅自己
        if (bo.isOwn()) {
            bo.setBuyerCodeList(Lists.newArrayList(user.getUserCode()));
        }

        // 只返回指定销售日，需要审核的正常批次-尾货会跟着正常批次状态走
        Page<SupplierSkuVo> vo = supplierSkuMapper.queryDeliveryAuditPage(bo, bo.build());

        // 参数转换
        fillSupplierSkuVOPage(vo, bo.getSaleDate());

        return TableDataInfo.build(vo);
    }

    @Override
    public List<SupplierSkuVo> queryDeliveryAuditList(SupplierSkuSimplePageBo bo) {
        return supplierSkuMapper.queryDeliveryAuditPage(bo);
    }

    /**
     * 供货批次列表
     *
     * @param bo
     * @return
     */
    @Override
    public TableDataInfo<QueryBatchPageVo> queryBatchPage(QueryBatchPageBo bo) {
        SupplierSkuPageBo queryBo = MapstructUtils.convert(bo, SupplierSkuPageBo.class);
        LoginUser user = LoginHelper.getLoginUser();
        if (user == null) {
            throw new ServiceException("非法请求");
        }
        queryBo.setSupplierIdList(Lists.newArrayList(user.getRelationId()));
        if (Objects.nonNull(user.getDeptId()) && !Objects.equals(user.getDeptId(), 0L)) {
            queryBo.setSupplierDeptIdList(Lists.newArrayList(user.getDeptId()));
        }
        //获取销售日
        LocalDate saleDate = getSaleDate(bo.getRegionWhId());
        queryBo.setSaleDateStart(saleDate);
        queryBo.setSaleDateEnd(saleDate);
        queryBo.setRegionWhIdList(Lists.newArrayList(bo.getRegionWhId()));
        if (queryBo.getStatusList() == null || queryBo.getStatusList().size() == 0) {
            queryBo.setStatusList(Lists.newArrayList(SupplierSkuStatusEnum.STATUS4.getCode(), SupplierSkuStatusEnum.STATUS5.getCode()));
        }
        TableDataInfo<SupplierSkuVo> vo = this.queryPageList(queryBo);
        List<QueryBatchPageVo> pageVoList = MapstructUtils.convert(vo.getRows(), QueryBatchPageVo.class);
        TableDataInfo<QueryBatchPageVo> result = TableDataInfo.build(pageVoList);
        result.setTotal(vo.getTotal());
        return result;
    }

    /**
     * 总仓小程序上架申请列表
     *
     * @param bo
     * @return
     */
    @Override
    public TableDataInfo<QueryRegSubscribePageVo> querySupSubscribePage(QueryRegSubscribePageBo bo) {
        LoginUser user = LoginHelper.getLoginUser();
        if (user == null) {
            throw new ServiceException("非法请求");
        }
        // 含供应商+部门赋值 - 前端传入
        SupplierSkuPageBo queryBo = MapstructUtils.convert(bo, SupplierSkuPageBo.class);
        //获取销售日
        LocalDate saleDate = getSaleDate(bo.getRegionWhId());
        queryBo.setSaleDateStart(saleDate);
        queryBo.setSaleDateEnd(saleDate);
        queryBo.setRegionWhIdList(Lists.newArrayList(bo.getRegionWhId()));
        if (bo.isOwn()) {
            queryBo.setBuyerCodeList(Lists.newArrayList(user.getUserCode()));
        }
        TableDataInfo<SupplierSkuVo> vo = this.queryPageList(queryBo);
        List<QueryRegSubscribePageVo> pageVoList = MapstructUtils.convert(vo.getRows(), QueryRegSubscribePageVo.class);
        TableDataInfo<QueryRegSubscribePageVo> result = TableDataInfo.build(pageVoList);
        result.setTotal(vo.getTotal());
        return result;
    }

    /**
     * 总仓小程序上下架列表
     *
     * @param bo
     * @return
     */
    @Override
    public TableDataInfo<QueryPassPageVo> queryPassPage(QueryPassPageBo bo) {
        LoginUser user = LoginHelper.getLoginUser();
        if (user == null) {
            throw new ServiceException("非法请求");
        }
        if (bo.isQuickUp()) {
            // 含供应商+部门赋值 - 前端传入
            QueryLastListBo lastBo = MapstructUtils.convert(bo, QueryLastListBo.class);
            lastBo.setStatusList(null);
            lastBo.setSaleDateLt(getSaleDate(bo.getRegionWhId()));
            lastBo.setRegionWhIdList(Lists.newArrayList(bo.getRegionWhId()));
            if (bo.isOwn()) {
                lastBo.setBuyerCodeList(Lists.newArrayList(user.getUserCode()));
            }
            // 排除配货商品
            lastBo.setExBusinessTypeList(Collections.singletonList(SupplierSkuBusinessTypeEnum.BUSINESS_TYPE40.getCode()));
            Page<SupplierSkuVo> vo = supplierSkuMapper.queryLastList(lastBo, bo.build());
            if (vo.getRecords() != null && vo.getRecords().size() > 0) {
                // 填充档口信息
                fillDeptSupplierInfo(vo.getRecords());
                //根据供应商商品销售批次id集合和类型查询供应商商品销售批次文件信息(查询商品展示图片)
                List<Long> supplierSkuIdList = vo.getRecords().stream().map(SupplierSkuVo::getId).toList();
                QuerySupplierSkuFileBo fileBo = new QuerySupplierSkuFileBo();
                fileBo.setSupplierSkuIdList(supplierSkuIdList);
                List<SupplierSkuFileVo> fileVoList = supplierSkuFileMapper.queryList(fileBo);
                //处理数据
                Map<Long, List<SupplierSkuFileVo>> fileMap = fileVoList.stream().collect(Collectors.groupingBy(SupplierSkuFileVo::getSupplierSkuId));
                //处理出参
                for (SupplierSkuVo skuVo : vo.getRecords()) {
//                    skuVo.setPrice(skuVo.getUpPrice());
                    skuVo.setStock(skuVo.getUpStock());
                    skuVo.buildSkuStandardsList().tranSpuStandardsName();
                    List<SupplierSkuFileVo> fileList = fileMap.get(skuVo.getId());
                    skuVo.setFileList(fileList);
                    if (fileList != null && fileList.size() > 0) {
                        for (SupplierSkuFileVo file : fileList) {
                            if (SupplierSkuFileTypeEnum.TYPE1.getCode().equals(file.getType())) {
                                skuVo.setImgUrl(file.getFileUrl());
                                break;
                            }
                        }
                    }
                }
            }
            List<QueryPassPageVo> pageVoList = MapstructUtils.convert(vo.getRecords(), QueryPassPageVo.class);
            TableDataInfo<QueryPassPageVo> result = TableDataInfo.build(pageVoList);
            result.setTotal(vo.getTotal());
            return result;
        } else {
            SupplierSkuPageBo queryBo = MapstructUtils.convert(bo, SupplierSkuPageBo.class);
            //获取销售日
            LocalDate saleDate = getSaleDate(bo.getRegionWhId());
            queryBo.setSaleDateStart(saleDate);
            queryBo.setSaleDateEnd(saleDate);
            queryBo.setRegionWhIdList(Lists.newArrayList(bo.getRegionWhId()));
            if (bo.isOwn()) {
                queryBo.setBuyerCodeList(Lists.newArrayList(user.getUserCode()));
            }
            TableDataInfo<SupplierSkuVo> vo = this.queryPageList(queryBo);
            List<QueryPassPageVo> pageVoList = MapstructUtils.convert(vo.getRows(), QueryPassPageVo.class);
            TableDataInfo<QueryPassPageVo> result = TableDataInfo.build(pageVoList);
            result.setTotal(vo.getTotal());
            return result;
        }
    }

    /**
     * 城市仓多货选择供应商商品销售批次列表接口
     *
     * @param bo
     * @return
     */
    @Override
    public TableDataInfo<QueryHistoryPageVo> queryHistoryPage(QueryHistoryPageBo bo) {
        SupplierSkuPageBo queryBo = MapstructUtils.convert(bo, SupplierSkuPageBo.class);
        if (ObjectUtil.isNotNull(bo.getSupplierId())) {
            List<Long> supplierIdList = Lists.newArrayList();
            supplierIdList.add(bo.getSupplierId());
            if (ObjectUtil.isNotEmpty(queryBo.getSupplierIdList())) {
                supplierIdList.addAll(queryBo.getSupplierIdList());
            }
            queryBo.setSupplierIdList(supplierIdList);
        }

        //获取销售日
        LocalDate saleDate = getSaleDate(bo.getRegionWhId());
        queryBo.setSaleDateStart(saleDate.minusDays(15));
        queryBo.setSaleDateEnd(saleDate);
        queryBo.setRegionWhIdList(Lists.newArrayList(bo.getRegionWhId()));
        queryBo.setExBusinessType(SupplierSkuBusinessTypeEnum.BUSINESS_TYPE40.getCode());
        queryBo.setMoreGoodsFlag(1);
        if (ObjectUtil.isNotEmpty(bo.getSpuName())) {
            queryBo.setSpuName(null);
            queryBo.setSpuKeyword(bo.getSpuName());
        }
        TableDataInfo<SupplierSkuVo> vo = this.queryPageList(queryBo);
        if (ObjectUtil.isNotEmpty(vo) && ObjectUtil.isNotEmpty(vo.getRows())) {
            List<QueryHistoryPageVo> pageVoList = MapstructUtils.convert(vo.getRows(), QueryHistoryPageVo.class);
            TableDataInfo<QueryHistoryPageVo> result = TableDataInfo.build(pageVoList);
            result.setTotal(vo.getTotal());
            return result;
        } else if (ObjectUtil.isNotEmpty(bo.getSupplierSpuCode())) {
            RemoteQuerySkuIdBo skuIdBo = new RemoteQuerySkuIdBo();
            skuIdBo.setSaleDateStart(saleDate.minusDays(15));
            skuIdBo.setSaleDateEnd(saleDate);
            //如果getSupplierSpuCode查不到 就查skuLabel
            skuIdBo.setSkuLabel(bo.getSupplierSpuCode());
            List<RemoteSupplierSkuInfoVo> skuInfoVo = listByLabel(skuIdBo);
            if (ObjectUtil.isNotEmpty(skuInfoVo)) {
                if (SupplierSkuBusinessTypeEnum.BUSINESS_TYPE40.getCode().equals(skuInfoVo.get(0).getBusinessType())) {
                    return TableDataInfo.build(Collections.emptyList());
                }
                List<SupplierSkuVo> pageVoList = BeanUtil.copyToList(skuInfoVo, SupplierSkuVo.class);
                convertHandleSkuInfo(pageVoList);
                TableDataInfo<QueryHistoryPageVo> result = TableDataInfo.build(BeanUtil.copyToList(pageVoList, QueryHistoryPageVo.class));
                result.setTotal(pageVoList.size());
                return result;
            }
        }

        return TableDataInfo.build(Collections.emptyList());
    }


    @Override
    public QueryHistoryPageVo querySkuInfoSingle(QueryHistoryPageBo bo) {
        SupplierSkuPageBo queryBo = MapstructUtils.convert(bo, SupplierSkuPageBo.class);
        //获取销售日
        LocalDate saleDate = getSaleDate(bo.getRegionWhId());
        queryBo.setSaleDateStart(saleDate.minusDays(15));
        queryBo.setSaleDateEnd(saleDate);
        queryBo.setExBusinessType(SupplierSkuBusinessTypeEnum.BUSINESS_TYPE40.getCode());
        queryBo.setRegionWhIdList(Lists.newArrayList(bo.getRegionWhId()));
        if (ObjectUtil.isNotEmpty(bo.getSpuName())) {
            queryBo.setSpuName(null);
            queryBo.setSpuKeyword(bo.getSpuName());
        }
        queryBo.setSupplierIdList(Lists.newArrayList(bo.getSupplierId()));
        TableDataInfo<SupplierSkuVo> vo = this.queryPageList(queryBo);
        if (ObjectUtil.isNotEmpty(vo) && ObjectUtil.isNotEmpty(vo.getRows())) {
            QueryHistoryPageVo result = BeanUtil.toBean(vo.getRows().get(0), QueryHistoryPageVo.class);
            return result;
        } else if (ObjectUtil.isNotEmpty(bo.getSupplierSpuCode())) {
            RemoteQuerySkuIdBo skuIdBo = new RemoteQuerySkuIdBo();
            skuIdBo.setSaleDateStart(saleDate.minusDays(15));
            skuIdBo.setSaleDateEnd(saleDate);
            //如果getSupplierSpuCode查不到 就查skuLabel
            skuIdBo.setSkuLabel(bo.getSupplierSpuCode());
            List<RemoteSupplierSkuInfoVo> skuInfoVo = listByLabel(skuIdBo);
            if (ObjectUtil.isNotEmpty(skuInfoVo)) {
                if (SupplierSkuBusinessTypeEnum.BUSINESS_TYPE40.getCode().equals(skuInfoVo.get(0).getBusinessType())) {
                    return null;
                }
                List<SupplierSkuVo> pageVoList = BeanUtil.copyToList(skuInfoVo, SupplierSkuVo.class);
                convertHandleSkuInfo(pageVoList);
                QueryHistoryPageVo result = BeanUtil.toBean(pageVoList.get(0), QueryHistoryPageVo.class);
                return result;
            }
        }
        return null;
    }

    /**
     * 批量修改
     *
     * @param supplierSkus
     * @return
     */
    @Override
    public boolean updateBatchById(List<SupplierSku> supplierSkus) {
        return supplierSkuMapper.updateBatchById(supplierSkus);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncSold(List<Long> supplierSkuIds) {
        if (CollectionUtil.isEmpty(supplierSkuIds)) {
            return;
        }
        supplierSkuMapper.syncSold(supplierSkuIds);
        skuMapper.syncSold(supplierSkuIds);
    }

    @Override
    @Async
    public void asyncSold(List<Long> supplierSkuIds) {
        log.keyword("asyncSupplierSkuSold").info("异步修改库存:{}", Optional.ofNullable(supplierSkuIds).map(List::size).orElseGet(() -> 0));
        syncSold(supplierSkuIds);
    }

    /**
     * 根据供应商商品销售批次ID获取供应商商品销售批次详细信息
     * 集采用的
     *
     * @param id
     */
    @Override
    public SupplierSkuInfoVo getInfo(Long id) {
        SupplierSkuVo skuVo = supplierSkuMapper.queryById(id);
        if (skuVo == null) {
            throw new ServiceException("供应商商品销售批次不存在");
        }

        return fillInfo(skuVo);
    }

    private @org.jetbrains.annotations.NotNull SupplierSkuInfoVo fillInfo(SupplierSkuVo skuVo) {
        // 写入档口信息 - 名称，简码
        fillDeptSupplierInfo(skuVo);
        // 规格转换
        skuVo.tranSpuStandardsName().buildSkuStandardsList();
        SupplierSkuInfoVo infoVo = MapstructUtils.convert(skuVo, SupplierSkuInfoVo.class);
        RemoteSupplierVo supplierVo = remoteSupplierService.getSupplierById(infoVo.getSupplierId());
        if (supplierVo == null) {
            throw new ServiceException("供应商不存在");
        }

        infoVo.setSpuStandardsName(infoVo.getSpuStandards());
        infoVo.buildSpuStandardsName();
        infoVo.setSupplierSimpleCode(supplierVo.getSimpleCode());
        infoVo.setSupplierAlias(supplierVo.getAlias());
        // 档口编码覆盖供应商简码
        if (StringUtils.isNotBlank(skuVo.getSupplierDeptCode())) {
            infoVo.setSupplierSimpleCode(skuVo.getSupplierDeptCode());
        }

        BigDecimal netWeightPrice = infoVo.getPrice().divide(infoVo.getSpuNetWeight(), 2, RoundingMode.HALF_UP);
        if (netWeightPrice.compareTo(BigDecimal.ZERO) == 0) {
            netWeightPrice = new BigDecimal("0.01");
        }
        infoVo.setNetWeightPrice(netWeightPrice);
        //获取库存信息
        SupplierSkuStockVo supplierSkuStock = this.getSupplierSkuStock(skuVo.getId());
        if (Objects.nonNull(supplierSkuStock)) {
            infoVo.setStock(supplierSkuStock.getStock());
        }

        //根据供应商商品id查询文件
        QuerySupplierSkuFileBo queryFileBo = new QuerySupplierSkuFileBo();
        queryFileBo.setSupplierSkuIdList(Lists.newArrayList(skuVo.getId()));
        List<SupplierSkuFileVo> fileList = supplierSkuFileMapper.queryList(queryFileBo);
        if (fileList != null && fileList.size() > 0) {
            for (SupplierSkuFileVo file : fileList) {
                if (SupplierSkuFileTypeEnum.TYPE1.getCode().equals(file.getType())) {
                    infoVo.setImgUrl(file.getFileUrl());
                    break;
                }
            }
        }
        infoVo.setFileList(fileList);

        LoginUser user = LoginHelper.getLoginUser();
        if (user != null) {
            List<RemoteCartItemCountVO> cartList = remoteCartItemService.getCartItemListInfo(user.getUserId(), Lists.newArrayList(infoVo.getId()));
            if (cartList != null && cartList.size() > 0) {
                infoVo.setCartCount(cartList.get(0).getCount());
            }
        }
        infoVo.setLabel(TemCode.getTemCode(infoVo.getCode()));

        // 是否为榴莲商品
        infoVo.setHasDurian(0);
        if (infoVo.getCategoryId() != null) {
            Category category = categoryMapper.selectById(infoVo.getCategoryId());
            if (category != null && StringUtils.isNotBlank(category.getPathName()) && category.getPathName().contains("榴莲")) {
                String[] split = category.getPathName().split("/");
                if ((split.length > 1 && split[1].contains("榴莲")
                        || (split.length > 2 && split[2].contains("榴莲")))) {
                    infoVo.setHasDurian(1);
                }
            }
        }

        // 处理售后规则
        CategoryAfterTypeEnum afterTypeEnum = CategoryAfterTypeEnum.loadByCode(infoVo.getAfterSaleType());
        if (afterTypeEnum != null) {
            infoVo.setAfterSaleName(afterTypeEnum.getName());
            infoVo.setAfterSaleDesc(afterTypeEnum.getDesc());
        }

        // 区域简称
        if (StringUtils.isNotBlank(infoVo.getAreaCode()) && StringUtils.isBlank(infoVo.getShortProducer())) {
            RemotePubAreaVO remotePubAreaVO = remoteBasPubAreaService.queryByCode(infoVo.getAreaCode());
            if (remotePubAreaVO != null) {
                infoVo.setShortProducer(remotePubAreaVO.getAreaAlias());
            }
        }

        // 批次最后上架时间
/*        if(infoVo.getUpTime() != null){
            infoVo.setLastUpTime(infoVo.getUpTime());
        }else{
            //检验这个供应商今天能不能给总仓供这个货
            CheckSupplierSkuBo checkSupplierSkuBo = new CheckSupplierSkuBo();
            checkSupplierSkuBo.setSpuId(infoVo.getSpuId());
            checkSupplierSkuBo.setRegionWhId(infoVo.getRegionWhId());
            checkSupplierSkuBo.setSupplierId(infoVo.getSupplierId());
            checkSupplierSkuBo.setSupplierDeptId(infoVo.getSupplierDeptId() == null ? 0L : infoVo.getSupplierDeptId());
            checkSupplierSkuBo.setSpuGrade(Optional.ofNullable(infoVo.getSpuGrade()).orElseGet(()->""));
            checkSupplierSkuBo.setSpuStandards(Optional.ofNullable(infoVo.getSpuStandards()).orElseGet(()->""));
            checkSupplierSkuBo.setDomestic(Optional.ofNullable(infoVo.getDomestic()).orElseGet(()->0));
            checkSupplierSkuBo.setBusinessType(infoVo.getBusinessType());
            checkSupplierSkuBo.setProvideRegionWhId(infoVo.getProvideRegionWhId() == null ? 0L : infoVo.getProvideRegionWhId());
            checkSupplierSkuBo.setSpuName(infoVo.getSpuName());
            checkSupplierSkuBo.setCategoryId(infoVo.getCategoryId());
            checkSupplierSkuBo.setLastUpTime(1);
            SupplierSkuVo lastOne = supplierSkuMapper.checkSupplierSku(checkSupplierSkuBo);
            if(lastOne != null){
                infoVo.setLastUpTime(lastOne.getUpTime());
            }
        }*/


        if (ObjectUtil.isNotEmpty(infoVo.getProvideRegionWhId()) && ObjectUtil.notEqual(infoVo.getProvideRegionWhId(), 0L)) {
            RemoteRegionWhVo regionWhVo = remoteRegionWhService.queryById(infoVo.getProvideRegionWhId());
            infoVo.setProvideRegionWhName(ObjectUtil.isNotEmpty(regionWhVo) ? regionWhVo.getRegionWhName() : null);
        }

        // 前端隐藏商户编号&店铺
        List<Long> hideMerchantRegionWhIdList = noCheckGoodsProperties.getHideMerchantRegionWhIdList();
        if (hideMerchantRegionWhIdList.contains(infoVo.getRegionWhId())) {
            infoVo.setHideMerchant(1);
        }
        return infoVo;
    }

    /**
     * 集采：有商品运费优惠活动
     *
     * @param publicId
     * @return
     */
    @Override
    public SupplierSkuInfoVo getInfoV2(QuerySkuInfoBo publicId) {
        SupplierSkuInfoVo info = this.getInfo(publicId.getId());
        // 获取商品参与的免运活动
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (Objects.isNull(publicId.getRegionWhId()) || Objects.isNull(loginUser)
                || Objects.isNull(publicId.getCityWhId()) || Objects.isNull(publicId.getPlaceId())) {
            return info;
        }
        RemoteRegionWhVo remoteRegionWhVo = remoteRegionWhService.queryById(publicId.getRegionWhId());
        QuerySalePageVo querySalePageVo = new QuerySalePageVo();
        querySalePageVo.setSpuId(info.getSpuId());
        querySalePageVo.setId(info.getId());
        Map<Long, RemoteRuleFreightPriceVo> map = this.querySkuFreightPrice(loginUser, publicId.getCityWhId(), publicId.getPlaceId()
                , remoteRegionWhVo, CollectionUtil.newArrayList(querySalePageVo));
        if (CollectionUtil.isEmpty(map)) {
            return info;
        }
        RemoteRuleFreightPriceVo remoteRuleFreightPriceVo = map.values().stream().collect(Collectors.toList()).get(0);
        info.setIsFreightDiscount(1);
        info.setFreightDiscountMinNum(remoteRuleFreightPriceVo.getMinNum());
        return info;
    }

    /**
     * 导出总仓报价
     */
    @Override
    public String exportTodayPrice(Long regionWhId, LocalDate saleDate) {
        String fileUrl;
        //根据总获取销售日
        RemoteRegionWhVo whVo = remoteRegionWhService.queryById(regionWhId);
        PageQuery pageQuery = new PageQuery();
        pageQuery.setPageNum(1);
        pageQuery.setPageSize(1000);
        String fileName = saleDate + whVo.getRegionWhName() + ".xlsx";
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
             ExcelWriter excelWriter = EasyExcel.write(outputStream, RegionSkuPriceExportVO.class).build()) {
            WriteSheet writeSheet = EasyExcel.writerSheet("Sheet1").build();
            boolean hasNext;
            do {
                Page<RegionSkuPriceExportVO> page = supplierSkuMapper.queryTodayPrice(regionWhId, saleDate, pageQuery.build());
                if (CollUtil.isEmpty(page.getRecords())) {
                    break;
                }
                pageQuery.setPageNum(pageQuery.getPageNum() + 1);
                hasNext = page.hasNext();
                excelWriter.write(page.getRecords(), writeSheet);
            } while (hasNext);
            excelWriter.finish();
            byte[] excelBytes = outputStream.toByteArray();
            fileUrl = remoteFileService.uploadTempFile(fileName, fileName, ContentType.OCTET_STREAM.getValue(), excelBytes, 1).getUrl();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return fileUrl;
    }

    private void convertHandleSkuInfo(List<SupplierSkuVo> pageVoList) {

        List<Long> supplierSkuIdList = pageVoList.stream().map(SupplierSkuVo::getId).toList();
        QuerySupplierSkuFileBo fileBo = new QuerySupplierSkuFileBo();
        fileBo.setSupplierSkuIdList(supplierSkuIdList);
        List<SupplierSkuFileVo> fileVoList = supplierSkuFileMapper.queryList(fileBo);
        //处理数据
        Map<Long, List<SupplierSkuFileVo>> fileMap = fileVoList.stream().collect(Collectors.groupingBy(SupplierSkuFileVo::getSupplierSkuId));
        for (SupplierSkuVo infoVo : pageVoList) {
            List<SupplierSkuFileVo> fileList = fileMap.get(infoVo.getId());
            infoVo.setFileList(fileList);
            if (fileList != null && fileList.size() > 0) {
                for (SupplierSkuFileVo file : fileList) {
                    if (SupplierSkuFileTypeEnum.TYPE1.getCode().equals(file.getType())) {
                        infoVo.setImgUrl(file.getFileUrl());
                        break;
                    }
                }
            }
        }
        // 填充档口信息
        fillDeptSupplierInfo(pageVoList);
    }

    private List<RemoteSupplierSkuInfoVo> listByLabel(RemoteQuerySkuIdBo bo) {
        if (StrUtil.isBlank(bo.getSkuLabel())) {
            return Collections.emptyList();
        }
        // 只获取正常批次，暂不考虑尾货
        bo.setBatchType(SupplierSkuBatchTypeEnum.BATCH_TYPE1.getCode());
        List<RemoteSupplierSkuInfoVo> list = supplierSkuMapper.listBySkuLabel(bo);
        log.keyword("城市仓查询多货商品").info("城市仓查询多货商品, bo:{},list:{}", bo, list);

        if (ObjectUtil.isNotEmpty(list)) {
            for (RemoteSupplierSkuInfoVo infoVo : list) {
                // 规格转换
                infoVo.buildSpuStandardsName();
            }
            return list;
        }

        // 兼容无label的老数据 + 批次码识别功能
        RemoteSupplierSkuInfoVo skuInfoVo = this.getByCode(TemCode.getOriginalCode(bo.getSkuLabel()));
        log.keyword("城市仓查询多货商品").info("城市仓查询多货商品, bo:{},skuInfoVo:{}", bo, skuInfoVo);
        if (ObjectUtil.isEmpty(skuInfoVo)) {
            return Collections.emptyList();
        }
        return Lists.newArrayList(skuInfoVo);

    }


    private LocalDate getSaleDate(Long regionWhId) {
        RemoteRegionWhVo whVo = remoteRegionWhService.queryById(regionWhId);
        if (whVo == null) {
            throw new ServiceException("总仓不存在");
        }
        if (whVo.getStatus() == 0) {
            throw new ServiceException("总仓已被禁用");
        }
        return SaleDateUtil.getSaleDate(whVo.getSalesTimeEnd());
    }

    private void verifyWeight(BigDecimal spuGrossWeight, BigDecimal spuNetWeight) {
        boolean hasGross = spuGrossWeight != null;
        if (hasGross) {
            if (spuGrossWeight.compareTo(BigDecimal.ZERO) <= 0) {
                throw new ServiceException("毛重必须大于0");
            }
        }
        boolean hasNet = spuNetWeight != null;
        if (hasNet) {
            if (spuNetWeight.compareTo(BigDecimal.ZERO) <= 0) {
                throw new ServiceException("净重必须大于0");
            }
        }
        if (hasGross && hasNet) {
            if (spuGrossWeight.compareTo(spuNetWeight) < 0) {
                throw new ServiceException("毛重必须大于净重");
            }
        }
    }

    /**
     * 校验子账户是否禁止入金（调用平安接口检查）
     *
     * @param orgCode
     */
    private void checkOrgControlStatus(String orgCode) {
        if (productProperties.getCheckOrg() == 1) {
            RemoteOrgRelationQueryBo bo = new RemoteOrgRelationQueryBo();
            bo.setOrgCode(orgCode);
            bo.setOrgType(AccountOrgTypeEnum.SUPPLIER.getCode());
            bo.setOutOrgProperty(AccountOrgPropertyEnum.SH.getCode());
            if (remoteOrgRelationService.queryOrgControlType(bo)) {
                throw new ServiceException("供应商子账户被管控，禁止供货，请联系管理员");
            }
        }
    }

    /**
     * 新增供应商销售批次商品
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @Lock4j(name = ProductRedisNames.SUPPLIER_SKU_ADD, keys = "#bo.spuId + '_' + #bo.regionWhId + '_' + #bo.supplierId", expire = 30000, acquireTimeout = 1000)
    public Long insertByBo(AddSupplierSkuBo bo) {
        // 检查商品是否被禁用
        checkSpuIsDisable(bo.getSpuId());
        if (phGoodsProperties.getSpuIdList().contains(bo.getSpuId()) || Objects.equals(phGoodsProperties.getCategoryId(), bo.getCategoryId())) {
            throw new ServiceException("配货商品不能手动上架");
        }
        if (Objects.equals(bo.getBargain(), 1) && (bo.getMarketPrice() == null || bo.getMarketPrice().compareTo(BigDecimal.ZERO) <= 0)) {
            throw new ServiceException("特价商品必须设置市场价");
        }
        //检验毛重净重
        verifyWeight(bo.getSpuGrossWeight(), bo.getSpuNetWeight());
        //检验品类是否禁用
        Category category = checkCategory(bo.getCategoryId());
        bo.setCategoryIdLevel2(category.getParentId());
        //检验补贴
        BigDecimal subsidyFreeAmount = remoteOrderService.getSubsidyFreeAmount(bo.getRegionWhId());
        if (subsidyFreeAmount.multiply(bo.getSpuGrossWeight()).compareTo(bo.getPrice()) > 0) {
            throw new ServiceException("商品价格不能低于市场补贴价");
        }

        //商品补贴校验
        if(ObjectUtil.isNotEmpty(bo.getSubsidyAmount()) && bo.getSubsidyAmount().compareTo(BigDecimal.ZERO) > 0){
            SupplierSkuSubsidyAuditCheckVo checkVo = new SupplierSkuSubsidyAuditCheckVo();
            BeanUtils.copyProperties(bo, checkVo);
            this.checkSubsidyAudit(checkVo);
        }
        // 转换规格
        bo.buildSkuStandardsSortJoin();
        //检验平台商品信息
        SpuVO spu = spuService.getById(bo.getSpuId());
        if (spu == null) {
            throw new ServiceException("平台商品不存在");
        }
        // 检查平台商品 - 等级，规格，进出口，商品产地是否存在
        KeyValueBO.checkSpuExt(bo, spu.getExpList());
        // 填充售后类型
        bo.setAfterSaleDay(CategoryAfterTypeEnum.getSaleDayByCode(bo.getAfterSaleType()));
        // 默认非免检
        bo.setIsCheck(isNoCheck(bo.getRegionWhId(), bo.getCategoryPathName(), bo.getBusinessType(), bo.getSaleType()));
        // 进口产地简称
        if (Objects.equals(bo.getDomestic(), 1)) {
            bo.setAreaCode("");
            bo.setShortProducer(bo.getProducer());
        }
        // 产地简称
        if (StringUtils.isNotBlank(bo.getAreaCode()) && StringUtils.isBlank(bo.getShortProducer())) {
            RemotePubAreaVO remotePubAreaVO = remoteBasPubAreaService.queryByCode(bo.getAreaCode());
            if (remotePubAreaVO != null) {
                bo.setShortProducer(remotePubAreaVO.getAreaAlias());
            }
        }
        // 供应商直接上架送货审核
        if (Objects.equals(noCheckGoodsProperties.getSupplierSkuReview(), 1)) {
            bo.setHasDeliveryAudit(0);
        }

        // 特价率计算
        if (Objects.equals(1, bo.getBargain()) && Objects.nonNull(bo.getMarketPrice())) {
            BigDecimal priceDownRate = bo.getMarketPrice().subtract(bo.getPrice()).divide(bo.getMarketPrice(), 3, RoundingMode.HALF_UP);
            bo.setBargainRate(priceDownRate);
        } else {
            bo.setBargainRate(BigDecimal.ZERO);
        }

        SupplierSpu supplierSpu = MapstructUtils.convert(bo, SupplierSpu.class);
        SupplierSku supplierSku = MapstructUtils.convert(bo, SupplierSku.class);
//        supplierSku.setUpPrice(supplierSku.getPrice());

        //处理总仓信息
        RemoteRegionWhVo whVo = remoteRegionWhService.queryById(bo.getRegionWhId());
        if (whVo == null) {
            throw new ServiceException("总仓不存在");
        }
        if (whVo.getStatus() == 0) {
            throw new ServiceException("总仓已被禁用");
        }

        //收货总仓
        supplierSku.setRegionWhId(whVo.getId());
        supplierSku.setRegionWhCode(whVo.getRegionWhCode());
        supplierSku.setRegionWhName(whVo.getRegionWhName());

        //处理供应商信息
        RemoteSupplierVo supplierVo = remoteSupplierService.getSupplierById(bo.getSupplierId());
        if (supplierVo == null) {
            throw new ServiceException("供应商不存在");
        }
        //校验子账户是否禁止入金（调用平安接口检查）
        checkOrgControlStatus(supplierVo.getCode());

        // 坑位校验
        checkMaxSaleNum(bo.getSupplierId(), bo.getRegionWhId(), bo.getSaleType());
        //供应商商品设置供应商属性
        supplierSpu.setSupplierId(supplierVo.getId());
        supplierSpu.setSupplierCode(supplierVo.getCode());
        supplierSpu.setSupplierName(supplierVo.getName());
        //批次商品设置供应商属性
        supplierSku.setSupplierId(supplierVo.getId());
        supplierSku.setSupplierCode(supplierVo.getCode());
        supplierSku.setSupplierName(supplierVo.getName());

        LocalDate saleDate = SaleDateUtil.getSaleDate(whVo.getSalesTimeEnd());

        //设置销售日期
        supplierSku.setSaleDate(saleDate);
        // 平台商品新增上架商品时，要写入上架时间
        if (SupplierSkuStatusEnum.STATUS4.getCode().equals(supplierSku.getStatus())) {
            supplierSku.setUpTime(new Date());
        }
        //写入下架时间
        if (SupplierSkuStatusEnum.STATUS5.getCode().equals(supplierSku.getStatus())) {
            supplierSku.setDownTime(new Date());
        }

        //检验这个供应商今天能不能给总仓供这个货
        CheckSupplierSkuBo checkSupplierSkuBo = new CheckSupplierSkuBo();
        checkSupplierSkuBo.setSpuId(bo.getSpuId());
        checkSupplierSkuBo.setRegionWhId(bo.getRegionWhId());
        checkSupplierSkuBo.setSupplierId(bo.getSupplierId());
        checkSupplierSkuBo.setSupplierDeptId(bo.getSupplierDeptId() == null ? 0L : bo.getSupplierDeptId());
        checkSupplierSkuBo.setSpuGrade(Optional.ofNullable(bo.getSpuGrade()).orElseGet(() -> ""));
        checkSupplierSkuBo.setSpuStandards(Optional.ofNullable(bo.getSpuStandards()).orElseGet(() -> ""));
        checkSupplierSkuBo.setDomestic(Optional.ofNullable(bo.getDomestic()).orElseGet(() -> 0));
        checkSupplierSkuBo.setBusinessType(bo.getBusinessType());
        checkSupplierSkuBo.setProvideRegionWhId(bo.getProvideRegionWhId() == null ? 0L : bo.getProvideRegionWhId());
        checkSupplierSkuBo.setSpuName(spu.getName());
        checkSupplierSkuBo.setCategoryId(spu.getCategoryId());
        // 不和尾货比较
        checkSupplierSkuBo.setBatchType(1);
        SupplierSkuVo lastOne = supplierSkuMapper.checkSupplierSku(checkSupplierSkuBo);
        if (lastOne != null) {
            if (saleDate.isEqual(lastOne.getSaleDate())) {
                throw new ServiceException("当天已上架过相同规格商品，请确认");
            }
            if (Objects.equals(lastOne.getStatus(), SupplierSkuStatusEnum.STATUS6.getCode())) {
                throw new ServiceException("已过季批次不可上架！");
            }
            if (Objects.equals(bo.getCheckSkuExist(), 1)) {
                throw new ServiceException("已存在相同规格商品，请确认");
            }
        }
        //供应商商品设置平台商品部分属性
        supplierSpu.setSpuCode(spu.getCode());
        supplierSpu.setSpuName(spu.getName());
        //批次商品设置平台商品部分属性
        supplierSku.setSpuCode(spu.getCode());
        supplierSku.setSpuName(spu.getName());
        supplierSku.setCategoryId(spu.getCategoryId());
        supplierSku.setCategoryName(spu.getCategoryName());
        supplierSku.setCategoryPathName(spu.getPathName());

        //查询供应商商品
        SupplierSpuBo spuBo = new SupplierSpuBo();
        spuBo.setSpuId(spu.getId());
        spuBo.setSupplierId(supplierVo.getId());
        spuBo.setSupplierDeptId(bo.getSupplierDeptId());
        SupplierSpuVo supplierSpuVo = supplierSpuMapper.querySupplierSpu(spuBo);

        if (supplierSpuVo == null) {
            String simpleCode = supplierVo.getSimpleCode();
            if (bo.getSupplierDeptId() != null && bo.getSupplierDeptId() != 0) {
                RemoteDeptDetailVo deptDetailVo = remoteDeptService.getByDeptId(bo.getSupplierDeptId());
                simpleCode = deptDetailVo.getDeptCode().replace("-", "");
            }
            //如果不存在供应商商品，则新增供应商商品
            supplierSpu.setCode(spu.getCode() + simpleCode);
            supplierSpu.setLabel(supplierSpu.getCode());
            supplierSpu.setLastClearTime(saleDate);
            supplierSpu.setQualityReportLastClearTime(saleDate);
            supplierSpuMapper.insert(supplierSpu);
            //供应商销售批次商品加入供应商商品的相关信息
            supplierSku.setSupplierSpuId(supplierSpu.getId());
            supplierSku.setSupplierSpuCode(supplierSpu.getCode());
        } else {
            //如果存在供应商商品，则对供应商商品进行修改
            supplierSpu.setId(supplierSpuVo.getId());
            supplierSpu.setCode(supplierSpuVo.getCode());
            //判断是否更新最后清除时间
            if (supplierSpuVo.getLastClearTime() != null) {
                List<SpuCreateControlVO> spuCreateControlVOList = spuCreateControlMapper.queryList(bo.getSpuId());
                if (spuCreateControlVOList != null && spuCreateControlVOList.size() > 0) {
                    SpuCreateControlVO createControlVO = spuCreateControlVOList.get(0);
                    long interval = ChronoUnit.DAYS.between(supplierSpuVo.getLastClearTime(), saleDate);
                    if (createControlVO.getAutoCle() != 0 && createControlVO.getAutoCle() <= interval) {
                        supplierSpu.setLastClearTime(saleDate);
                    }
                    long qualityReportInterval = ChronoUnit.DAYS.between(supplierSpuVo.getQualityReportLastClearTime(), saleDate);
                    if (createControlVO.getQualityReportCle() != 0 && createControlVO.getQualityReportCle() <= qualityReportInterval) {
                        supplierSpu.setQualityReportLastClearTime(saleDate);
                    }
                }
            }
            supplierSpuMapper.updateById(supplierSpu);
            supplierSku.setSupplierSpuId(supplierSpuVo.getId());
            supplierSku.setSupplierSpuCode(supplierSpuVo.getCode());
        }

        supplierSku.setCode(createSupplierSkuCode(spu.getId(), supplierVo.getId(), supplierSpu.getCode(), supplierSku.getSaleDate()));
        // 扫描识别码 - 与历史批次保持一致
        if (StringUtils.isBlank(supplierSku.getSkuLabel())) {
            supplierSku.setSkuLabel(TemCode.getTemCode(supplierSku.getCode()));
            if (lastOne != null && StringUtils.isNotBlank(lastOne.getSkuLabel())) {
                supplierSku.setSkuLabel(lastOne.getSkuLabel());
            }
        }
        //拼接全称
        String spuAnotherName = getSpuAnotherName(spu.getCategoryId());
        String[] cateGoryPathNameArray = spu.getPathName().split("/");
        String fullName = (supplierSku.getBrand() != null ? supplierSku.getBrand() : "") +
                (supplierSku.getSpuName() != null ? supplierSku.getSpuName() : "") +
                (supplierSku.getShortProducer() != null ? supplierSku.getShortProducer() : "") +
                (supplierSku.getSpuGrade() != null ? supplierSku.getSpuGrade() : "") +
                (supplierSku.getSpuStandards() != null ? supplierSku.getSpuStandards() : "") +
                (supplierSku.getCooperation() != null && supplierSku.getCooperation() == 1 ? "战略品" : "") +
                (SupplierSkuBusinessTypeEnum.BUSINESS_TYPE20.getCode().equals(supplierSku.getBusinessType())
                        ? SupplierSkuBusinessTypeEnum.BUSINESS_TYPE20.getDesc() : "") +
                cateGoryPathNameArray[2] + cateGoryPathNameArray[3] + spuAnotherName;
        supplierSku.setFullName(fullName);

        // 专区 - 供应商编辑并快捷上架
        if (Objects.equals(1, bo.getIsSupplier()) && bo.getSupplierSkuId() != null && bo.getSupplierSkuId() > 0L) {
            SupplierSkuVo quick = supplierSkuMapper.queryById(bo.getSupplierSkuId());
            supplierSku.setShouguangVegetables(quick.getShouguangVegetables());
            supplierSku.setGiftBox(quick.getGiftBox());
        }
        //降价价格
        supplierSku.setDiscountPrice(BigDecimal.ZERO);

        //新增供应商销售批次商品
        supplierSkuMapper.insert(supplierSku);
        // 批次新增同步sku
        Long skuId = skuService.insertBatchSyncSku(supplierSku.getId());
        //生成补贴待审核单
        if(ObjectUtil.isNotEmpty(bo.getSubsidyAmount()) && bo.getSubsidyAmount().compareTo(BigDecimal.ZERO) > 0){
            addSupplierSkuSubsidyAudit(supplierSku, ObjectUtil.isNotEmpty(lastOne) ? lastOne.getStatus() : null);
        }
        // 批次隐藏 - 非当前销售日，纬度不考虑平台商品名称/分类
        if (bo.getSupplierSkuId() != null) {
            SupplierSkuVo skuVo = supplierSkuMapper.queryById(bo.getSupplierSkuId());
            if (skuVo == null) {
                throw new ServiceException("废除的历史批次不存在");
            }
            CheckSupplierSkuBo updateStatus = new CheckSupplierSkuBo();
            updateStatus.setSaleDate(saleDate);
            updateStatus.setSkuId(skuId);
            int updateStatusResult = supplierSkuMapper.updateStatusHideByOnly(updateStatus);
            int syncSku = skuMapper.updateStatusHideByOnly(updateStatus);
            log.keyword("insertByBo", bo.getSupplierSkuId()).info("修改历史批次数量={},sku修改数量={}", updateStatusResult, syncSku);
        }

        //新增库存
        SupplierSkuStock skuStock = new SupplierSkuStock();
        skuStock.setSupplierSkuId(supplierSku.getId());
        skuStock.setStock(bo.getStock());
        skuStock.setUpStock(bo.getStock());
        supplierSkuStockMapper.insert(skuStock);

        //添加操作日志
        SupplierSkuApproveLog log = new SupplierSkuApproveLog();
        log.setSupplierSkuId(supplierSku.getId());
        log.setOperate(bo.getOperate());
        log.setStatus(bo.getStatus());
        supplierSkuApproveLogMapper.insert(log);

        //新增供应商批次商品的文件
        if (bo.getFileList() != null && !bo.getFileList().isEmpty()) {
            for (AddSupplierSkuFileBo skuFileBo : bo.getFileList()) {
                if (StringUtils.isNotBlank(skuFileBo.getFileUrl()) && skuFileBo.getFileUrl().startsWith(cosProperties.getHost())) {
                    skuFileBo.setFileUrl(skuFileBo.getFileUrl().replace(cosProperties.getHost(), cosProperties.getGatewayHost()));
                }
            }
            supplierSkuFileMapper.insertByBoList(supplierSpu.getId(), supplierSku.getId(), bo.getFileList());
        }
        //检验文件
        if (!bo.getSkipFileCheck()) {
            checkControl(supplierSku, MapstructUtils.convert(bo.getFileList(), SupplierSkuFileVo.class));
        }

        // 总仓供货，指定禁用城市仓
        if (!Objects.equals(bo.getIsSupplier(), 1)) {
            if (CollUtil.isNotEmpty(bo.getCityWhList())) {
                supplierSkuDisableCityWhService.insertBySkuIfNotExist(skuId, bo.getCityWhList());
            }
        }
        //上架操作的时候，处理所属二级目录数据
        if (SupplierSkuStatusEnum.STATUS4.getCode().equals(supplierSku.getStatus())) {
            handleUp(supplierSku.getId(), supplierSku.getCategoryId(), supplierSku.getRegionWhId());
        }

        if (SupplierSkuStatusEnum.STATUS1.getCode().equals(supplierSku.getStatus())) {
            this.sendDelayWxAuditMsg(supplierSku.getId());
        }

        return supplierSku.getId();
    }

    /**
     * 坑位校验
     */
    private void checkMaxSaleNum(Long supplierId, Long regionWhId, Integer saleType) {
        if (Objects.equals(noCheckGoodsProperties.getSkuUpLimit(), 1) || SupplierSkuSaleTypeEnum.SALE_TYPE2.getCode().equals(saleType)) {
            Integer maxOnSaleNum = remoteSupplierService.queryMaxSkuNumLimit(supplierId, regionWhId);
            SupplierSkuSaleNumBo onSaleGoodNum = skuService.querySupplierSkuSaleNum(supplierId, regionWhId, saleType);
            checkSkuSaleLimit(maxOnSaleNum, onSaleGoodNum.getSaleNum());
        }
    }

    private static void checkSkuSaleLimit(Integer limitNum, Integer saleNum) {
        if (limitNum == null || limitNum == 0) {
            throw new ServiceException("没有坑位，请联系管理员开通坑位");
        }
        Integer sn = ObjectUtil.defaultIfNull(saleNum, 0);
        if (sn >= limitNum) {
            throw new ServiceException("已用完" + limitNum + "个坑位，可调整上架商品");
        }
    }

    @Override
    public int isNoCheck(@NotNull(message = "总仓id不能为空") Long regionWhId, String categoryPathName, Integer businessType, Integer saleType) {
        log.keyword("isNoCheck").info("总仓id={},{}, 商品类型={}, 配置信息:{}, 销售类型={}", regionWhId, categoryPathName, businessType, JSONObject.toJSONString(noCheckGoodsProperties), saleType);

        //销售类型为商家自营，免检
        if (SupplierSkuSaleTypeEnum.SALE_TYPE2.getCode().equals(saleType)){
            return 1;
        }

        // 全部免检开关
        if (Objects.equals(noCheckGoodsProperties.getAllNoCheck(), 1)) {
            return 1;
        }

        // 指定总仓免检
        if (noCheckGoodsProperties.getNoCheckRegionWhIdList().contains(regionWhId)) {
            return 1;
        }

        // 指定商品类型免检 - 产地免检
        if (noCheckGoodsProperties.getBusinessTypeList().contains(businessType)) {
            return 1;
        }

        if (StringUtils.isBlank(categoryPathName)) {
            return 0;
        }

        // 指定一级分类免检
        for (String e : noCheckGoodsProperties.getProductTypeNameList()) {
            if (categoryPathName.startsWith("/" + e)) {
                return 1;
            }
        }

        // 指定总仓免检
        String[] split = categoryPathName.split("/");
        Map<Integer, String> map = noCheckGoodsProperties.getNoCheckEasyMap().get(regionWhId);
        if (map != null) {
            for (int i = 1; i < split.length; i++) {
                // 指定1/2/3级免检的所有分类名
                Set<String> categoryNameSet = Optional.ofNullable(map.get(i)).filter(StringUtils::isNotBlank).map(e -> Arrays.stream(e.split(",")).filter(StringUtils::isNoneBlank).collect(Collectors.toSet())).orElseGet(() -> null);
                if (CollectionUtil.isNotEmpty(categoryNameSet)) {
                    String categoryName = split[i];
                    if (categoryNameSet.contains(categoryName)) {
                        return 1;
                    }
                }
            }
        }

        // 总仓指定规则免检 - 正则
        String regex = noCheckGoodsProperties.getNoCheckMap().get(regionWhId);
        if (StringUtils.isNotBlank(regex)) {
            return categoryPathName.matches(regex) ? 1 : 0;
        }

        return 0;
    }

    /**
     * 检验品类是否禁用
     *
     * @param categoryId
     */
    private Category checkCategory(Long categoryId) {
        Category category = categoryMapper.selectById(categoryId);
        if (category == null) {
            throw new ServiceException("品类不存在");
        }
        if (category.getLevel() == null || category.getLevel() != 2) {
            throw new ServiceException("商品绑定品类有误，如需上架请联系采购");
        }
        if (category.getStatus().equals(0)) {
            throw new ServiceException("品类已被禁用，禁止上架。如需上架请联系采购");
        }
        return category;
    }

    @Override
    public List<Long> querySupplierSkuIdGroup(RemoteQuerySkuIdsBo bo) {
        SupplierSkuVo skuVo = supplierSkuMapper.selectVoById(bo.getSupplierSkuId());
        if (skuVo == null) {
            throw new ServiceException("批次不存在");
        }

        CheckSupplierSkuBo checkSupplierSkuBo = new CheckSupplierSkuBo();
        // 相同商品条件key
        checkSupplierSkuBo.setSpuId(Optional.ofNullable(skuVo.getSpuId()).orElseGet(() -> 0L));
        checkSupplierSkuBo.setRegionWhId(Optional.ofNullable(skuVo.getRegionWhId()).orElseGet(() -> 0L));
        checkSupplierSkuBo.setSupplierId(Optional.ofNullable(skuVo.getSupplierId()).orElseGet(() -> 0L));
        checkSupplierSkuBo.setSupplierDeptId(Optional.ofNullable(skuVo.getSupplierDeptId()).orElseGet(() -> 0L));
        checkSupplierSkuBo.setSpuGrade(Optional.ofNullable(skuVo.getSpuGrade()).orElseGet(() -> ""));
        checkSupplierSkuBo.setSpuStandards(Optional.ofNullable(skuVo.getSpuStandards()).orElseGet(() -> ""));
        checkSupplierSkuBo.setDomestic(Optional.ofNullable(skuVo.getDomestic()).orElseGet(() -> 0));
        checkSupplierSkuBo.setBusinessType(Optional.ofNullable(skuVo.getBusinessType()).orElseGet(() -> 0));
        checkSupplierSkuBo.setProvideRegionWhId(Optional.ofNullable(skuVo.getProvideRegionWhId()).orElseGet(() -> 0L));
        checkSupplierSkuBo.setSpuName(Optional.ofNullable(skuVo.getSpuName()).orElseGet(() -> ""));
        checkSupplierSkuBo.setCategoryId(Optional.ofNullable(skuVo.getCategoryId()).orElseGet(() -> 0L));
        // 销售日期范围
        checkSupplierSkuBo.setSaleDateStart(bo.getSaleDateStart());
        checkSupplierSkuBo.setSaleDateEnd(bo.getSaleDateEnd());
        return supplierSkuMapper.getSameSupplierSkuIdList(checkSupplierSkuBo);
    }

    /**
     * 获取供应商商品销售批次编码
     *
     * @param spuId
     * @param supplierId
     * @param supplierSpuCode
     * @return
     */
    private String createSupplierSkuCode(Long spuId, Long supplierId, String supplierSpuCode, LocalDate saleDate) {
        String dateStr = DateUtil.format(saleDate.atStartOfDay(), "yyMMdd");
        String redisKey = ProductRedisNames.SUPPLIER_SKU_CODE + ":" + supplierId + ":" + spuId + ":" + dateStr;
        RAtomicLong atomicLong = RedisUtils.getClient().getAtomicLong(redisKey);
        long no = atomicLong.incrementAndGet();
        if (no == 1) {
            RedisUtils.getClient().getBucket(redisKey).expire(Duration.ofHours(49));
        }
        if (no >= 10000) {
            throw new ServiceException("此商品超过每天供货数量上限");
        }
        return String.format("%s%s%0" + 4 + "d", supplierSpuCode, dateStr, no);
    }

    /**
     * 修改供应商销售批次商品
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @Lock4j(name = ProductRedisNames.SUPPLIER_SKU_ADD, keys = "#old.spuId + '_' + #old.regionWhId + '_' + #old.supplierId", expire = 30000, acquireTimeout = 1000)
    public Boolean updateByBo(UpdateSupplierSkuBo bo, SupplierSkuVo old) {
        if (old == null || old.getDelFlag() != 0) {
            throw new ServiceException("销售批次不存在");
        }
        if (phGoodsProperties.getSpuIdList().contains(old.getSpuId()) || Objects.equals(phGoodsProperties.getCategoryId(), old.getCategoryId())) {
            throw new ServiceException("配货商品不能编辑");
        }
        if (Objects.equals(bo.getBargain(), 1) && (bo.getMarketPrice() == null || bo.getMarketPrice().compareTo(BigDecimal.ZERO) <= 0)) {
            throw new ServiceException("特价商品必须设置市场价");
        }
        // 恢复商品规格
        old.recoverDbSpuStandards();
        //检验毛重净重
        verifyWeight(bo.getSpuGrossWeight(), bo.getSpuNetWeight());
        BigDecimal grossWeight = ObjectUtil.defaultIfNull(bo.getSpuGrossWeight(), old.getSpuGrossWeight());
        BigDecimal netWeight = ObjectUtil.defaultIfNull(bo.getSpuNetWeight(), old.getSpuNetWeight());
        BigDecimal price = ObjectUtil.defaultIfNull(bo.getPrice(), old.getPrice());
        if (grossWeight.compareTo(netWeight) < 0) {
            throw new ServiceException("商品毛重不能小于净重");
        }
        if (bo.getPrice() != null) {
            BigDecimal spuGrossWeight = old.getSpuGrossWeight();
            if (bo.getSpuGrossWeight() != null) {
                spuGrossWeight = bo.getSpuGrossWeight();
            }
            //检验补贴
            BigDecimal subsidyFreeAmount = remoteOrderService.getSubsidyFreeAmount(old.getRegionWhId());
            if (subsidyFreeAmount.multiply(spuGrossWeight).compareTo(bo.getPrice()) > 0) {
                throw new ServiceException("商品价格不能低于市场补贴价");
            }
        }
        //校验补贴
        if(ObjectUtil.isNotEmpty(bo.getSubsidyAmount()) && bo.getSubsidyAmount().compareTo(BigDecimal.ZERO) > 0){
            SupplierSkuSubsidyAuditCheckVo checkVo = new SupplierSkuSubsidyAuditCheckVo();
            checkVo.setPrice(price);
            checkVo.setSubsidyAmount(bo.getSubsidyAmount());
            checkVo.setMarketPrice(bo.getMarketPrice());
            checkVo.setBargain(bo.getBargain());
            //校验补贴价格
            this.checkSubsidyAudit(checkVo);
        }

        // 上过架后
        if(SupplierSkuStatusEnum.getCodesGreaterEq3().contains(old.getStatus()) || old.getUpTime() != null) {
            // 总价只能下调
            if (bo.getPrice() != null && old.getPrice().compareTo(bo.getPrice()) < 0) {
                throw new ServiceException("当前状态修改价格不能高于原有价格");
            }
            // 净重单价只能下调
            BigDecimal netPrice = old.getPrice().divide(old.getSpuNetWeight(), 2, RoundingMode.HALF_UP);
            BigDecimal newNetPrice = price.divide(netWeight, 2, RoundingMode.HALF_UP);
            if (netPrice.compareTo(newNetPrice) < 0) {
                throw new ServiceException("单价变高了，请重新调整净重或价格");
            }
        }
        // 转换规格 - 多选的情况下不能修改规格
        bo.buildSkuStandardsSortJoin();
        // 检查平台商品 - 等级，规格，进出口，商品产地是否存在
        SpuVO spu = spuService.getById(old.getSpuId());
        if (spu == null) {
            throw new ServiceException("平台商品不存在");
        }
        KeyValueBO.checkSpuExt(bo, spu.getExpList());
        // 填充售后类型
        if (Objects.nonNull(bo.getAfterSaleType())) {
            bo.setAfterSaleDay(CategoryAfterTypeEnum.getSaleDayByCode(bo.getAfterSaleType()));
        }
        if (Objects.equals(bo.getDomestic(), 1)) {
            bo.setAreaCode("");
            bo.setShortProducer(bo.getProducer());
        }
        // 编辑不能修改审核信息
        bo.setPassDeliveryAudit(null);
        //当批次过了销售日期之后禁止修改
        updateVerify(old);
        //检验这个供应商今天能不能给总仓供这个货
        CheckSupplierSkuBo checkSupplierSkuBo = new CheckSupplierSkuBo();
        checkSupplierSkuBo.setId(old.getId());
        checkSupplierSkuBo.setSkuId(old.getSkuId());
        checkSupplierSkuBo.setBatchType(1);
        SupplierSkuVo lastOne = null;
        if ((StringUtils.isNotBlank(bo.getSpuGrade()) && !Objects.equals(bo.getSpuGrade(), old.getSpuGrade()))
                || (StringUtils.isNotBlank(bo.getSpuStandards()) && !Objects.equals(bo.getSpuStandards(), old.getSpuStandards()))
                || (Objects.nonNull(bo.getDomestic()) && !Objects.equals(bo.getDomestic(), old.getDomestic()))
//            || ( Objects.nonNull(bo.getSpuGrossWeight()) && Objects.nonNull(old.getSpuGrossWeight()) && bo.getSpuGrossWeight().compareTo(old.getSpuGrossWeight()) != 0 )
        ) {
            if (old.getUpTime() != null) {
                throw new ServiceException("该商品今日已上过架，无法修改规格/等级/进出口信息");
            }
            lastOne = supplierSkuMapper.checkSupplierSku(checkSupplierSkuBo);
            if (lastOne != null) {
                if (old.getSaleDate().isEqual(lastOne.getSaleDate())) {
                    throw new ServiceException("商品信息重复，请修改");
                }
                if (Objects.equals(lastOne.getStatus(), SupplierSkuStatusEnum.STATUS6.getCode())) {
                    throw new ServiceException("存在已过季的批次！请变更规格等级或恢复过季批次");
                }
            }
        }

        //修改供应商商品
        SupplierSpu updateSpu = MapstructUtils.convert(bo, SupplierSpu.class);
        updateSpu.setId(old.getSupplierSpuId());
        supplierSpuMapper.updateById(updateSpu);

        // 特价率计算
        if (Objects.equals(bo.getBargain(), 1)) {
            BigDecimal priceDownRate = bo.getMarketPrice().subtract(bo.getPrice()).divide(bo.getMarketPrice(), 3, RoundingMode.HALF_UP);
            bo.setBargainRate(priceDownRate);
        } else {
            bo.setBargainRate(BigDecimal.ZERO);
        }
        log.keyword("teJia-Debug").info("新特价信息-是否特价{}，价格{}，行情价{}，特价率{}，原特价信息-是否特价{}，价格{}，行情价{}，特价率{}", bo.getBargain(), bo.getPrice(), bo.getMarketPrice(), bo.getBargainRate(), old.getBargain(), old.getPrice(), old.getMarketPrice(), old.getBargainRate());

        // 已驳回修改后变成待审核
        SupplierSku updateSku = MapstructUtils.convert(bo, SupplierSku.class);

        if (old.getStatus().equals(SupplierSkuStatusEnum.STATUS2.getCode())) {
            updateSku.setStatus(SupplierSkuStatusEnum.STATUS1.getCode());
            //添加操作日志
            SupplierSkuApproveLog log = new SupplierSkuApproveLog();
            log.setSupplierSkuId(old.getId());
            log.setOperate(bo.getOperate());
            log.setStatus(SupplierSkuStatusEnum.STATUS1.getCode());
            supplierSkuApproveLogMapper.insert(log);
        }

        // 默认非免检
        updateSku.setIsCheck(isNoCheck(old.getRegionWhId(), old.getCategoryPathName(), old.getBusinessType(), old.getSaleType()));
        // 区域简称
        if (StringUtils.isNotBlank(updateSku.getAreaCode()) && StringUtils.isBlank(updateSku.getShortProducer())) {
            RemotePubAreaVO remotePubAreaVO = remoteBasPubAreaService.queryByCode(updateSku.getAreaCode());
            if (remotePubAreaVO != null) {
                updateSku.setShortProducer(remotePubAreaVO.getAreaAlias());
            }
        }

        // 获取用户信息
        LoginUser loginUser = LoginHelper.getLoginUser();
        //修改库存
        boolean isUpdateStock = bo.getStock() != null && bo.getStock() >= 0;
        boolean isUpdateUpStock = bo.getUpStock() != null && bo.getUpStock() >= 0;
        boolean isUpdateAddStock = bo.getAddStock() != null && bo.getAddStock() > 0;
        if (isUpdateStock || isUpdateUpStock || isUpdateAddStock) {
            log.keyword("updateStock", bo.getId(), "updateStock-" + bo.getId()).info("修改库存，改前：{} 增加库存：{} 改后库存：{}, 上架库存: {}", old.getStock(), bo.getAddStock(), bo.getStock(), bo.getUpStock());
            UpdateSupplierSkuStockBo skuStock = new UpdateSupplierSkuStockBo();
            skuStock.setSupplierSkuId(updateSku.getId());
            if (isUpdateStock) {
                skuStock.setStock(bo.getStock());
                //这里这么判断是为了避免如果今天这个批次没有审批通过，明天再供这个货的时候，拿到的还是想要的数据
                // 不懂，先留着
                if (Objects.isNull(old.getUpTime())) {
                    skuStock.setUpStock(skuStock.getStock());
                }
                updateSku.setIsOut(skuStock.getStock() <= 0 ? 1 : 0);
            }
            if (isUpdateUpStock) {
                skuStock.setUpStock(bo.getUpStock());
            }
            if (isUpdateAddStock) {
                skuStock.setAddStock(bo.getAddStock());
                updateSku.setIsOut(0);
            }
            supplierSkuStockMapper.updateStock(skuStock);
            // 增加修改库存记录
            if ((isUpdateStock && !bo.getStock().equals(old.getStock())) || isUpdateAddStock){
                recordStock(bo, old, loginUser);
            }
        }

        //拼接全称
        String spuAnotherName = getSpuAnotherName(spu.getCategoryId());
        String[] cateGoryPathNameArray = spu.getPathName().split("/");
        String fullName = (ObjectUtil.defaultIfNull(updateSku.getBrand(), old.getBrand()) +
                (ObjectUtil.defaultIfNull(old.getSpuName(), "")) +
                (ObjectUtil.defaultIfNull(updateSku.getShortProducer(), old.getShortProducer())) +
                (ObjectUtil.defaultIfNull(updateSku.getSpuGrade(), old.getSpuGrade())) +
                (ObjectUtil.defaultIfNull(updateSku.getSpuStandards(), old.getSpuStandards())) +
                (ObjectUtil.equals(ObjectUtil.defaultIfNull(updateSku.getCooperation(), old.getCooperation()), 1) ? "战略品" : "") +
                (SupplierSkuBusinessTypeEnum.BUSINESS_TYPE20.getCode().equals(old.getBusinessType()) ? SupplierSkuBusinessTypeEnum.BUSINESS_TYPE20.getDesc() : "") +
                cateGoryPathNameArray[2] + cateGoryPathNameArray[3] + spuAnotherName
        );
        if (!Objects.equals(fullName, old.getFullName())) {
            updateSku.setFullName(fullName);
        }


        //限时降价(参与限时降价后的价格要记录降价金额)
        BigDecimal discountPrice = old.getDiscountPrice();
        if (BigDecimal.ZERO.compareTo(discountPrice) < 0 && (bo.getPrice() != null && old.getPrice().compareTo(bo.getPrice()) != 0)){
            BigDecimal difference = old.getPrice().subtract(bo.getPrice()) ;
            difference = difference.compareTo(BigDecimal.ZERO) <= 0 ? BigDecimal.ZERO : difference;
            updateSku.setDiscountPrice(old.getDiscountPrice().add(difference));
        }
        // 修改行情预测
        if (StrUtil.isBlank(updateSku.getMarketForecastType())){
            updateSku.setMarketForecastType(old.getMarketForecastType());
        }
        supplierSkuMapper.updateById(updateSku);

        // 记录修改价格
        BigDecimal oldPrice = old.getPrice();
        if (oldPrice != null && bo.getPrice() != null && oldPrice.compareTo(bo.getPrice()) != 0){
            // 存储价格修改的记录
            recordPrice(bo, old, loginUser);
        }
        // 修改待审核的商品，发送微信通知
        /*if(SupplierSkuStatusEnum.STATUS1.getCode().equals(old.getStatus())) {
            sendDelayWxAuditMsg(updateSku.getId());
        }*/
        // 批次修改同步sku
        Long skuId = skuService.updateBatchSyncSku(updateSku.getId());
        if (Objects.equals(skuId, 0L)) {
            skuId = updateSku.getSkuId();
        }
        //文件
        if (bo.getDelFileList() != null && !bo.getDelFileList().isEmpty()) {
            DelSupplierSkuFileBo delFileBo = new DelSupplierSkuFileBo();
            delFileBo.setIdList(bo.getDelFileList());
            supplierSkuFileMapper.delByBo(delFileBo);
        }

        if (bo.getAddFileList() != null && !bo.getAddFileList().isEmpty()) {
            for (AddSupplierSkuFileBo skuFileBo : bo.getAddFileList()) {
                if (StringUtils.isNotBlank(skuFileBo.getFileUrl()) && skuFileBo.getFileUrl().startsWith(cosProperties.getHost())) {
                    skuFileBo.setFileUrl(skuFileBo.getFileUrl().replace(cosProperties.getHost(), cosProperties.getGatewayHost()));
                }
            }
            supplierSkuFileMapper.insertByBoList(old.getSupplierSpuId(), old.getId(), bo.getAddFileList());
        }
        //检验文件
        QuerySupplierSkuFileBo fileBo = new QuerySupplierSkuFileBo();
        fileBo.setSupplierSkuIdList(Lists.newArrayList(old.getId()));
        List<SupplierSkuFileVo> fileVoList = supplierSkuFileMapper.queryList(fileBo);
        checkControl(updateSku, fileVoList);
        //禁用城市仓
        if (bo.getAddCityWhList() != null && !bo.getAddCityWhList().isEmpty()) {
            supplierSkuDisableCityWhService.insertBySkuIfNotExist(skuId, bo.getAddCityWhList());
        }
        if (bo.getDelCityWhList() != null && !bo.getDelCityWhList().isEmpty()) {
            DelSupplierSkuDisableCityWhBo cityWhBo = new DelSupplierSkuDisableCityWhBo();
            cityWhBo.setIdList(bo.getDelCityWhList());
            supplierSkuDisableCityWhMapper.delByBo(cityWhBo);
        }
        return true;
    }

    @Override
    public void changeLabel(UpdateSupplierSkuBo bo) {
        SupplierSkuVo old = supplierSkuMapper.selectVoById(bo.getId());

        if (Objects.equals(bo.getSkuLabel(), old.getSkuLabel())) {
            return;
        }

        LocalDate saleDate = getSaleDate(old.getRegionWhId());

        if (Objects.equals(saleDate, old.getSaleDate()) && old.getUpTime() != null) {
            throw new ServiceException("该商品今日已上架，无法修改条码");
        }

        CheckSupplierSkuBo checkSkuLabel = new CheckSupplierSkuBo();
        checkSkuLabel.setSkuLabel(bo.getSkuLabel());
        int count = supplierSkuMapper.checkSupplierSkuNum(checkSkuLabel);
        if (count > 0) {
            throw new ServiceException("条码已绑定");
        }
        CheckSupplierSkuBo checkSkuCode = new CheckSupplierSkuBo();
        checkSkuCode.setId(old.getId());
        checkSkuCode.setCode(TemCode.getOriginalCode(bo.getSkuLabel()));
        int count2 = supplierSkuMapper.checkSupplierSkuNum(checkSkuCode);
        if (count2 > 0) {
            throw new ServiceException("条码与批次编码冲突");
        }


        CheckSupplierSkuBo updateStatus = new CheckSupplierSkuBo();
        updateStatus.setSkuLabel(bo.getSkuLabel());
        updateStatus.setSpuId(Optional.ofNullable(old.getSpuId()).orElseGet(() -> 0L));
        updateStatus.setRegionWhId(Optional.ofNullable(old.getRegionWhId()).orElseGet(() -> 0L));
        updateStatus.setSupplierId(Optional.ofNullable(old.getSupplierId()).orElseGet(() -> 0L));
        updateStatus.setSupplierDeptId(Optional.ofNullable(old.getSupplierDeptId()).orElseGet(() -> 0L));
        updateStatus.setSpuGrade(Optional.ofNullable(old.getSpuGrade()).orElseGet(() -> ""));
        updateStatus.setSpuStandards(Optional.ofNullable(old.getSpuStandards()).orElseGet(() -> ""));
        updateStatus.setDomestic(Optional.ofNullable(old.getDomestic()).orElseGet(() -> 0));
        updateStatus.setBusinessType(Optional.ofNullable(old.getBusinessType()).orElseGet(() -> 0));
        updateStatus.setProvideRegionWhId(Optional.ofNullable(old.getProvideRegionWhId()).orElseGet(() -> 0L));
        int updateStatusResult = supplierSkuMapper.updateLabelByOnly(updateStatus);
        log.keyword("changeLabel", bo.getId()).info("修改批次条码数量={}", updateStatusResult);

        // 同步sku
        skuMapper.updateLabelByOnly(updateStatus);
    }

    @Override
    public void auditDelivery(UpdateSupplierSkuBo bo) {
        SupplierSkuVo sku = supplierSkuMapper.queryById(bo.getId());
        if (sku == null) {
            throw new ServiceException("销售批次不存在");
        }
        if (Objects.equals(sku.getPassDeliveryAudit(), bo.getPassDeliveryAudit())) {
            return;
        }

        //检验这个供应商今天能不能给总仓供这个货
        CheckSupplierSkuBo checkSupplierSkuBo = new CheckSupplierSkuBo();
        checkSupplierSkuBo.setId(sku.getId());
        checkSupplierSkuBo.setSpuId(sku.getSpuId());
        checkSupplierSkuBo.setRegionWhId(sku.getRegionWhId());
        checkSupplierSkuBo.setSupplierId(sku.getSupplierId());
        checkSupplierSkuBo.setSupplierDeptId(sku.getSupplierDeptId() == null ? 0L : sku.getSupplierDeptId());
        checkSupplierSkuBo.setSpuGrade(Optional.ofNullable(sku.getSpuGrade()).orElseGet(() -> ""));
        checkSupplierSkuBo.setSpuStandards(Optional.ofNullable(sku.getSpuStandards()).orElseGet(() -> ""));
        checkSupplierSkuBo.setDomestic(Optional.ofNullable(sku.getDomestic()).orElseGet(() -> 0));
        checkSupplierSkuBo.setSaleDate(sku.getSaleDate());
        checkSupplierSkuBo.setProvideRegionWhId(sku.getProvideRegionWhId() == null ? 0L : sku.getProvideRegionWhId());
        checkSupplierSkuBo.setSpuName(sku.getSpuName());
        checkSupplierSkuBo.setCategoryId(sku.getCategoryId());
        List<SupplierSkuVo> todayList = supplierSkuMapper.getBacthList(checkSupplierSkuBo);

        List<Long> idList = CollUtil.newArrayList(bo.getId());
        if (CollUtil.isNotEmpty(todayList)) {
            for (SupplierSkuVo skuVo : todayList) {
                idList.add(skuVo.getId());
            }
            idList = idList.stream().distinct().collect(Collectors.toList());
        }

        // 审核通过
        supplierSkuMapper.update(new LambdaUpdateWrapper<SupplierSku>().in(SupplierSku::getId, idList).set(SupplierSku::getPassDeliveryAudit, bo.getPassDeliveryAudit()));

        // 同步sku
        skuMapper.update(new LambdaUpdateWrapper<Sku>().in(Sku::getSupplierSkuId, idList).set(Sku::getPassDeliveryAudit, sku.getPassDeliveryAudit()));
    }

    /**
     * 当批次过了销售日期之后禁止修改
     *
     * @param old
     */
    private void updateVerify(SupplierSkuVo old) {
        if (old.getSaleDate().isBefore(getSaleDate(old.getRegionWhId()))) {
            throw new ServiceException("该批次销售日已过，不支持修改");
        }
    }

    /**
     * 修改状态
     *
     * @param bo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean changeStatus(ChangeStatusBo bo) {
        SupplierSkuVo old = supplierSkuMapper.selectVoById(bo.getId());
        if (old == null || old.getDelFlag() != 0) {
            throw new ServiceException("销售批次不存在");
        }
        //查补贴待审核单
        SupplierSkuSubsidyAudit subsidyAudit = supplierSkuSubsidyAuditMapper.selectBySupplierSkuIdAndStatus(bo.getId(), SupplierSkuSubsidyAuditStatusEnum.PENDING.getCode());
        //驳回补贴审核时，要根据发起补贴审核时的状态，将商品驳回到不同状态
        if(SupplierSkuStatusEnum.STATUS2.getCode().equals(bo.getStatus()) && ObjectUtil.isNotEmpty(subsidyAudit)){
            if(ObjectUtil.isEmpty(subsidyAudit.getSkuStatus()) || SupplierSkuStatusEnum.STATUS5.getCode().equals(subsidyAudit.getSkuStatus())
                    || SupplierSkuBusinessTypeEnum.BUSINESS_TYPE20.getCode().equals(old.getBusinessType())
                    || SupplierSkuBusinessTypeEnum.BUSINESS_TYPE30.getCode().equals(old.getBusinessType())){
                bo.setStatus(SupplierSkuStatusEnum.STATUS1.getCode());
            }
        }
        // 非过季操作，当批次过了销售日期之后禁止修改
        if (!SupplierSkuStatusEnum.STATUS6.getCode().equals(bo.getStatus()) && !SupplierSkuStatusEnum.STATUS6.getCode().equals(old.getStatus())) {
            updateVerify(old);
        }
        // 上架校验
        SupplierSku sku = MapstructUtils.convert(bo, SupplierSku.class);
        if (SupplierSkuStatusEnum.STATUS4.getCode().equals(bo.getStatus())) {
            // 检查商品是否禁用
            checkSpuIsDisable(old.getSpuId());
            //处理供应商信息
            RemoteSupplierVo supplierVo = remoteSupplierService.getSupplierById(old.getSupplierId());
            if (supplierVo == null) {
                throw new ServiceException("供应商不存在");
            }
            //校验子账户是否禁止入金（调用平安接口检查）
            checkOrgControlStatus(supplierVo.getCode());
            if (SupplierSkuStatusEnum.getCodesWaitAudit().contains(old.getStatus())) {
                //如是快捷审核，根据skuId获取上个批次的品牌和装卸队id
                LambdaQueryWrapper<SupplierSku> wrapper = Wrappers.lambdaQuery(SupplierSku.class);
                wrapper.eq(SupplierSku::getSkuId, old.getSkuId()).orderByDesc(SupplierSku::getSaleDate)
                        .notIn(SupplierSku::getStatus, SupplierSkuStatusEnum.getCodesWaitAudit()).last("LIMIT 1");
                SupplierSku oldSku = supplierSkuMapper.selectOne(wrapper);
                if (ObjectUtil.isNotNull(oldSku)) {
                    // 品牌快捷上架才要自动填充
                    if (Objects.equals(bo.getIsQuick(), 1) && StringUtils.isBlank(old.getBrand())) {
                        sku.setBrand(oldSku.getBrand());
                        old.setBrand(oldSku.getBrand());
                        // 更新 fullName
                        String spuAnotherName = getSpuAnotherName(old.getCategoryId());
                        String[] cateGoryPathNameArray = old.getCategoryPathName().split("/");
                        String fullName = oldSku.getBrand() + oldSku.getSpuName() + oldSku.getShortProducer() + oldSku.getSpuGrade() + oldSku.getSpuStandards() + (oldSku.getCooperation() == 1 ? "战略品" : "") + cateGoryPathNameArray[2] + cateGoryPathNameArray[3] + spuAnotherName;
                        sku.setFullName(fullName);
                    }
                    // 装队必须不能为空
                    if (old.getBasPortageTeamId() == null || old.getBasPortageTeamId() == 0L) {
                        sku.setBasPortageTeamId(oldSku.getBasPortageTeamId());
                        old.setBasPortageTeamId(oldSku.getBasPortageTeamId());
                    }
                }
            }
            this.checkProvide(old.getBusinessType(), old.getProvideRegionWhId());
            this.checkPortageTeam(old.getBusinessType(), old.getBasPortageTeamId());

            if (!SupplierSkuStatusEnum.STATUS4.getCode().equals(old.getStatus())) {
                // 坑位校验
                checkMaxSaleNum(old.getSupplierId(), old.getRegionWhId(), old.getSaleType());
            }
            //不是补贴审核通过，清空补贴价格
            if(ObjectUtil.isEmpty(subsidyAudit) && !SupplierSkuStatusEnum.STATUS4.getCode().equals(old.getStatus())){
                sku.setSubsidyAmount(BigDecimal.ZERO);
            }
        }
        // 供应商端的判断
        if (bo.isSupplier()) {
            Set<String> operationSet = new HashSet<>();
            operationSet.add(SupplierSkuStatusEnum.STATUS5.getCode() + "-" + SupplierSkuStatusEnum.STATUS1.getCode());
            operationSet.add(SupplierSkuStatusEnum.STATUS5.getCode() + "-" + SupplierSkuStatusEnum.STATUS6.getCode());
            operationSet.add(SupplierSkuStatusEnum.STATUS6.getCode() + "-" + SupplierSkuStatusEnum.STATUS5.getCode());
            operationSet.add(SupplierSkuStatusEnum.STATUS2.getCode() + "-" + SupplierSkuStatusEnum.STATUS1.getCode());
            if (!operationSet.contains(old.getStatus() + "-" + bo.getStatus())) {
                throw new ServiceException("供应商不能这样修改状态！");
            }
        }
        //补充允许状态流转
        Set<String> operationRegSet = new HashSet<>();
        operationRegSet.add(SupplierSkuStatusEnum.STATUS5.getCode() + "-" + SupplierSkuStatusEnum.STATUS8.getCode());
        if( !bo.isSupplier() && SupplierSkuStatusEnum.getCodesGreaterEq3().contains(old.getStatus()) && SupplierSkuStatusEnum.getCodesLessEq3().contains(bo.getStatus())
            && !operationRegSet.contains(old.getStatus() + "-" + bo.getStatus())) {
            throw new ServiceException("当前状态不能这样修改");
        }
        if (old.getOriginalSupplierId() != null) {
            if (old.getOriginalSupplierId() == 0) {
                throw new ServiceException("换过供应商的旧批次，不能再次上架");
            }
            if (old.getOriginalSupplierId() > 0) {
                throw new ServiceException("换过供应商的新批次，不能再次上架");
            }
        } else if (old.getOriginalSupplierSkuId() != null && old.getOriginalSupplierSkuId() == 0) {
            if (!SupplierSkuBatchTypeEnum.BATCH_TYPE2.getCode().equals(old.getBatchType())) {
                throw new ServiceException("该商品已设置成尾货，不允许修改状态");
            }
        }
        // 过季限制
        boolean changeError1 = Objects.equals(old.getStatus(), SupplierSkuStatusEnum.STATUS6.getCode()) && !Objects.equals(bo.getStatus(), SupplierSkuStatusEnum.STATUS5.getCode());
        boolean changeError2 = Objects.equals(bo.getStatus(), SupplierSkuStatusEnum.STATUS6.getCode()) && !Objects.equals(old.getStatus(), SupplierSkuStatusEnum.STATUS5.getCode());
        if (changeError1 || changeError2) {
            throw new ServiceException("下架商品才能设置已过季，已过季商品只能变更为下架");
        }
        if (SupplierSkuStatusEnum.STATUS4.getCode().equals(bo.getStatus())) {
            //检验品类是否禁用
            checkCategory(old.getCategoryId());
            //判断是否第一次上架
            if(SupplierSkuStatusEnum.getCodesLessEq3().contains(old.getStatus())) {
//                sku.setUpPrice(old.getPrice());
                sku.setUpTime(now());

//                SupplierSkuStockVo stock = supplierSkuStockMapper.selectBySupplierSkuId(bo.getId());
//                SupplierSkuStock updateStock = new SupplierSkuStock();
//                updateStock.setId(stock.getId());
//                updateStock.setUpStock(stock.getStock());
//                supplierSkuStockMapper.updateById(updateStock);
            }
        }
        //处理补贴审核单
        this.subsidyAudit(subsidyAudit, bo, sku, old);

        if(SupplierSkuStatusEnum.STATUS5.getCode().equals(bo.getStatus())) {
            sku.setDownTime(now());
            //下架清空补贴
            sku.setSubsidyAmount(BigDecimal.ZERO);
        }
        if (SupplierSkuStatusEnum.STATUS6.getCode().equals(bo.getStatus())) {
            sku.setOutTime(now());
        }
        supplierSkuMapper.updateById(sku);
        // 同步sku
        skuService.updateBatchSyncSku(sku.getId());

        SupplierSkuApproveLog approveLog = MapstructUtils.convert(bo, SupplierSkuApproveLog.class);
        approveLog.setId(null);
        approveLog.setSupplierSkuId(bo.getId());
        approveLog.setOperate(SupplierSkuApproveLogOperateEnum.BUYER.getDesc() + LoginHelper.getLoginUser().getRealName());
        if(SupplierSkuStatusEnum.getCodesGreaterEq3().contains(bo.getStatus())) {
            SupplierSkuApproveLogVo logVo = supplierSkuApproveLogMapper.queryPass(bo.getId());
            if (logVo == null) {
                approveLog.setStatus(SupplierSkuStatusEnum.STATUS3.getCode());
                supplierSkuApproveLogMapper.insert(approveLog);
            }
        } else {
            supplierSkuApproveLogMapper.insert(approveLog);
        }
        //上架操作的时候，处理所属二级目录数据
        if (SupplierSkuStatusEnum.STATUS4.getCode().equals(sku.getStatus())) {
            handleUp(old.getId(), old.getCategoryId(), old.getRegionWhId());
        }
        //下操作的时候，处理所属二级目录数据
        if (SupplierSkuStatusEnum.STATUS5.getCode().equals(sku.getStatus())) {
            handleDown(old.getId(), old.getCategoryId(), old.getRegionWhId());
        }

        // 审核驳回消息 to 供应商
        try {
            if (SupplierSkuStatusEnum.STATUS2.getCode().equals(bo.getStatus())) {
                SupplierSkuApproveLog supplierSkuApproveLog = supplierSkuApproveLogMapper.selectById(approveLog.getId());
                String createName = supplierSkuApproveLog.getCreateName();
                Date createTime = supplierSkuApproveLog.getCreateTime();
                String reason = bo.getReason();

                sendAuditWxInfo(old, SupplierSkuStatusEnum.STATUS2, reason, createName, createTime, true);
            }
        } catch (Exception e) {
            log.keyword("skuChangeStatus", bo.getId()).error("审核驳回发送微信公众消息失败！", e);
        }

        return true;
    }

    /**
     * 处理补贴审核单
     * @param bo
     * @param sku
     */
    private void subsidyAudit(SupplierSkuSubsidyAudit subsidyAudit, ChangeStatusBo bo, SupplierSku sku, SupplierSkuVo old) {
        //待审核到补贴待审核
        if(SupplierSkuStatusEnum.STATUS8.getCode().equals(bo.getStatus())) {
            //现在没有补贴审核单，生成
            if(ObjectUtil.isNotEmpty(subsidyAudit)){
                throw new ServiceException("该商品已经存在待审核的补贴记录");
            }
            //新增补贴待审核记录
            this.addSupplierSkuSubsidyAudit(old);
        }else if(ObjectUtil.isNotEmpty(subsidyAudit)){
            //已经存在补贴审核单，操作补贴待审核记录
            if(SupplierSkuStatusEnum.STATUS4.getCode().equals(bo.getStatus())){
                //补贴待审核 ->>> 上架
                sku.setSubsidyAmount(bo.getSubsidyAmount());
                subsidyAudit.setSubsidyAmount(bo.getSubsidyAmount());
                subsidyAudit.setStatus(SupplierSkuSubsidyAuditStatusEnum.AUDITED.getCode());
                subsidyAudit.setPrice(old.getPrice());
                //校验补贴金额
                if(ObjectUtil.isEmpty(subsidyAudit.getSubsidyAmount())) {
                    throw new ServiceException("补贴上架的补贴金额不能为空");
                }
                if(subsidyAudit.getSubsidyAmount().compareTo(BigDecimal.ZERO) <= 0 || subsidyAudit.getSubsidyAmount().compareTo(new BigDecimal("99.99")) > 0){
                    throw new ServiceException("补贴金额必须大于0且小于等于99.99");
                }
                //单价的百分之90
                BigDecimal priceNinetyPercent = old.getPrice().multiply(new BigDecimal("0.9"));
                if(subsidyAudit.getSubsidyAmount().compareTo(priceNinetyPercent) > 0){
                    throw new ServiceException("补贴金额不能大于单价的90%");
                }
                supplierSkuSubsidyAuditMapper.updateById(subsidyAudit);
            }else{
                //补贴待审核 ->>> 驳回、待审核，清空补贴价格
                sku.setSubsidyAmount(BigDecimal.ZERO);
                subsidyAudit.setPrice(old.getPrice());
                subsidyAudit.setStatus(SupplierSkuSubsidyAuditStatusEnum.REJECTED.getCode());
                supplierSkuSubsidyAuditMapper.updateById(subsidyAudit);
            }
        }
    }

    /**
     * 新增补贴待审核记录
     * @param old
     */
    private void addSupplierSkuSubsidyAudit(SupplierSkuVo old) {
        SupplierSkuSubsidyAudit subsidyAudit = new SupplierSkuSubsidyAudit();
        subsidyAudit.setSkuId(old.getSkuId());
        subsidyAudit.setSupplierSkuId(old.getId());
        subsidyAudit.setSaleDate(old.getSaleDate());
        subsidyAudit.setSubsidyAmount(old.getSubsidyAmount());
        subsidyAudit.setSubmitSubsidyAmount(old.getSubsidyAmount());
        subsidyAudit.setMarketPrice(old.getMarketPrice());
        subsidyAudit.setSubmitTime(new Date());
        subsidyAudit.setRegionWhId(old.getRegionWhId());
        subsidyAudit.setSkuStatus(old.getStatus());
        supplierSkuSubsidyAuditMapper.insert(subsidyAudit);
    }

    /**
     * 新增补贴待审核记录
     * @param supplierSku
     */
    private void addSupplierSkuSubsidyAudit(SupplierSku supplierSku, Integer status) {
        SupplierSkuSubsidyAudit subsidyAudit = new SupplierSkuSubsidyAudit();
        subsidyAudit.setSkuId(supplierSku.getSkuId());
        subsidyAudit.setSupplierSkuId(supplierSku.getId());
        subsidyAudit.setSaleDate(supplierSku.getSaleDate());
        subsidyAudit.setSubsidyAmount(supplierSku.getSubsidyAmount());
        subsidyAudit.setSubmitSubsidyAmount(supplierSku.getSubsidyAmount());
        subsidyAudit.setMarketPrice(supplierSku.getMarketPrice());
        subsidyAudit.setSubmitTime(new Date());
        subsidyAudit.setRegionWhId(supplierSku.getRegionWhId());
        subsidyAudit.setSkuStatus(status);
        supplierSkuSubsidyAuditMapper.insert(subsidyAudit);
    }

    @Override
    public void sendSkuMsg(RemoteSkuMsgBo bo) {
        SupplierSkuVo supplierSkuVo = supplierSkuMapper.selectVoById(bo.getSupplierSkuId());
        if (!Objects.equals(bo.getStatus(), supplierSkuVo.getStatus())) {
            log.keyword("SkuSendMsg", bo.getSupplierSkuId()).info("商品已处理，无需提醒审核");
            return;
        }
        // todo 动作类型多了，再加枚举扩展，目前仅审核提醒
        log.keyword("SkuSendMsg", bo.getSupplierSkuId()).info("商品审核通知");
        this.sendAuditWxInfo(supplierSkuVo, SupplierSkuStatusEnum.STATUS1, "你有供货商品等待审核~", bo.getCreateName(), bo.getCreateTime(), false);
    }

    private void sendAuditWxInfo(SupplierSkuVo skuVo, SupplierSkuStatusEnum statusEnum, String reason, String createName, Date createTime, boolean supply) {
        LinkedHashMap<String, String> wxTempParams = new LinkedHashMap<>();

        //商品名称拼接：品牌+产地+名称+规格
        String spuFullName = (StringUtils.isEmpty(skuVo.getBrand()) ? "" : skuVo.getBrand() + "-")
                + (StringUtils.isEmpty(skuVo.getShortProducer()) ? "" : skuVo.getShortProducer() + "-")
                + skuVo.getSpuName() + (StringUtils.isNotEmpty(skuVo.getSpuStandards()) ? skuVo.getSpuStandards() : "");
        if (spuFullName.length() > 20) {
            spuFullName = spuFullName.substring(0, 15) + "...";
        }
        wxTempParams.put("spuName", spuFullName);
        wxTempParams.put("status", statusEnum.getDesc());
        if (reason != null && reason.length() > 20) {
            reason = reason.substring(0, 15) + "...";
        }
        wxTempParams.put("reason", reason);
        wxTempParams.put("createName", createName);
        wxTempParams.put("createTime", DateUtil.formatDateTime(createTime));
        NotifyObjectTypeEnum notifyObjectTypeEnum = supply ? NotifyObjectTypeEnum.SUPPLIER : NotifyObjectTypeEnum.REGION_WH;
        String sendObject = supply ? skuVo.getSupplierId() + "" : skuVo.getBuyerCode();
        if (StringUtils.isEmpty(sendObject)) {
            log.keyword("SkuSendMsg", skuVo.getSupplierSkuId()).error("通知对象为空！skuVo={}", JSON.toJSONString(skuVo));
        }
        RemoteMessageNotifyV2Bo sendBo = new RemoteMessageNotifyV2Bo(notifyObjectTypeEnum,
                Collections.singletonList(sendObject), MsgNotifyTemplateV2Enum.sku_approve,
                wxTempParams, String.format("?regionWhId=%d&regionWhName=\"%s\"&regionWhCode=\"%s\"", skuVo.getRegionWhId(), skuVo.getRegionWhName(), skuVo.getRegionWhCode())
        );
        remoteMessageNotifyService.sendMessageV2(sendBo);
    }


    /**
     * 根据一级目录id查询有售卖批次的二级目录列表
     *
     * @param bo
     * @return
     */
    @Override
    public List<SaleCategoryVo> getSaleCategory(SaleCategoryBo bo) {
        Map<String, SaleCategoryVo> result = new HashMap<>();
        String selectKey;
        if (bo.getLevel().equals(0)) {
            selectKey = ProductRedisNames.SALE_TOP_CATEGORY_SELECT;
        } else {
            selectKey = ProductRedisNames.SALE_CATEGORY_SELECT + bo.getCategoryName();
        }
        Map<String, HashMap<String, SaleCategoryVo>> cacheMap = RedisUtils.getCacheMap(selectKey);
        for (Long regionWhId : bo.getRegionWhIdList()) {
            Map<String, SaleCategoryVo> regionWhMap = cacheMap.get(regionWhId.toString());
            if (regionWhMap != null) {
                for (String key : regionWhMap.keySet()) {
                    if (result.containsKey(key)) {
                        result.get(key).getCategoryIdList().addAll(regionWhMap.get(key).getCategoryIdList());
                    } else {
                        result.put(key, regionWhMap.get(key));
                    }
                }
            }
        }
        List<SaleCategoryVo> list = result.values().stream().toList();
        return sortName(list, bo.getLevel());
    }

    /**
     * 按照标准排序
     *
     * @param oldList
     * @return
     */
    @Override
    public List<SaleCategoryVo> sortName(List<SaleCategoryVo> oldList, Integer level) {
        if (CollectionUtil.isEmpty(oldList)) {
            return new ArrayList<>();
        }
        Map<String, SaleCategoryVo> map = oldList.stream().collect(Collectors.toMap(SaleCategoryVo::getCategoryName, Function.identity()));
        List<SaleCategoryVo> newList = new ArrayList<>();
        List<String> sortNames = switch (level) {
            case 1 -> productProperties.getSortNameList2();
            case 2 -> productProperties.getSortNameList3();
            default -> productProperties.getSortNameList();
        };
        for (String name : sortNames) {
            if (map.get(name) != null) {
                newList.add(map.get(name));
                map.remove(name);
            }
        }
        newList.addAll(map.values().stream().toList());
        return newList;
    }

    private String getMergeName(String categoryName) {
        if (productProperties.getFruitSet().contains(categoryName)) {
            return productProperties.getMergeFruitName();
        }
        if (productProperties.getVegetableSet().contains(categoryName)) {
            return productProperties.getMergeVegetableName();
        }
        return null;
    }

    /**
     * 上架操作的时候，处理所属二级目录数据
     *
     * @param supplierSkuId
     * @param categoryId
     * @param regionWhId
     */
    private void handleUp(Long supplierSkuId, Long categoryId, Long regionWhId) {
        //根据三级分类查询二级分类
        CategoryVO categoryVO = categoryMapper.getParParent(categoryId);
        if (categoryVO == null || categoryVO.getLevel() == null || categoryVO.getLevel() == 0) {
            log.keyword("商品绑定品类有误").info(supplierSkuId.toString());
            throw new ServiceException("商品绑定品类有误，如需上架请联系采购");
        }
        //处理总仓下二级分类现在有多少个上架批次
        String updateKey = ProductRedisNames.SALE_CATEGORY_UPDATE + regionWhId;
        String updateHkey = categoryVO.getName();
        //获取总仓下二级分类现在有多少个上架批次
        HashSet<String> updateValue = RedisUtils.getCacheMapValue(updateKey, updateHkey);
        if (updateValue == null) {
            updateValue = new HashSet<>();
        }
        //把新上架的批次加入到所属的二级分类下
        updateValue.add(supplierSkuId.toString());
        RedisUtils.setCacheMapValue(updateKey, updateHkey, updateValue);
        //处理一级分类下当前总仓所有的二级分类
        CategoryVO topCategoryVO = categoryMapper.selectVoById(categoryVO.getParentId());
        //需求是把国产水果和进口水果合成水果
        String mergeTopName = getMergeName(topCategoryVO.getName());
        if (mergeTopName == null) {
            mergeTopName = topCategoryVO.getName();
        }
        String selectKey = ProductRedisNames.SALE_CATEGORY_SELECT + mergeTopName;
        String selectHkey = regionWhId.toString();
        //获取一级分类下当前总仓所有的二级分类
        HashMap<String, SaleCategoryVo> map = RedisUtils.getCacheMapValue(selectKey, selectHkey);
        if (map == null || map.size() == 0) {
            map = new HashMap<>();

            //如果一级分类下当前总仓所有的二级分类为空，那么需要把一级分类加入到所属总仓下面
            String topSelectKey = ProductRedisNames.SALE_TOP_CATEGORY_SELECT;
            String topSelectHkey = regionWhId.toString();
            //获取当前总仓下的一级分类
            HashMap<String, SaleCategoryVo> topMap = RedisUtils.getCacheMapValue(topSelectKey, topSelectHkey);
            if (topMap == null) {
                topMap = new HashMap<>();
            }
            SaleCategoryVo vo = topMap.get(mergeTopName);
            //组装一级分类信息
            if (vo == null) {
                vo = new SaleCategoryVo();
                HashSet<Long> topSet = new HashSet<>();
                vo.setCategoryIdList(topSet);
                vo.setCategoryName(mergeTopName);
                vo.setImgUrl(topCategoryVO.getImgUrl());
            }
            vo.getCategoryIdList().add(topCategoryVO.getId());
            //把一级分类加入到所属总仓下面
            topMap.put(mergeTopName, vo);
            RedisUtils.setCacheMapValue(topSelectKey, topSelectHkey, topMap);
        }
        //获取当前批次所属二级分类
        SaleCategoryVo vo = map.get(categoryVO.getName());
        if (vo == null) {
            //如果为空，则把当前批次所属的二级分类加入到所属总仓下
            vo = new SaleCategoryVo();
            HashSet<Long> set = new HashSet<>();
            vo.setCategoryIdList(set);
            vo.setCategoryName(categoryVO.getName());
            vo.setImgUrl(categoryVO.getImgUrl());
        }
        vo.getCategoryIdList().add(categoryVO.getId());
        //把当前批次所属的二级分类加入到所属总仓下
        map.put(categoryVO.getName(), vo);
        RedisUtils.setCacheMapValue(selectKey, selectHkey, map);
    }

    /**
     * 下架操作的时候，处理所属二级目录数据
     *
     * @param supplierSkuId
     * @param categoryId
     * @param regionWhId
     */
    private void handleDown(Long supplierSkuId, Long categoryId, Long regionWhId) {
        //根据三级分类查询二级分类
        CategoryVO categoryVO = categoryMapper.getParParent(categoryId);
        if (categoryVO == null || categoryVO.getLevel() == null || categoryVO.getLevel() == 0) {
            log.keyword("商品绑定品类有误").info(supplierSkuId.toString());
            throw new ServiceException("商品绑定品类有误，如需上架请联系采购");
        }
        //处理总仓下二级分类现在有多少个上架批次
        String updateKey = ProductRedisNames.SALE_CATEGORY_UPDATE + regionWhId;
        String updateHkey = categoryVO.getName();
        //获取总仓下二级分类现在有多少个上架批次
        HashSet<String> updateValue = RedisUtils.getCacheMapValue(updateKey, updateHkey);
        if (updateValue != null) {
            //如果不为空，则删除二级分类下的这个批次id
            updateValue.remove(supplierSkuId.toString());
            if (updateValue.size() == 0) {
                //二级分类下没有批次了，那就把二级分类也删除
                RedisUtils.delCacheMapValue(updateKey, updateHkey);
            } else {
                //二级分类下还有批次，则把现有的批次集合存到所属二级分类下
                RedisUtils.setCacheMapValue(updateKey, updateHkey, updateValue);
            }
        }
        //如果下架批次，二级目录下还有批次，那么不用管二级分类了
        if (updateValue == null || updateValue.size() == 0) {
            //如果下架批次，二级目录下没有批次了，那么当前批次所属二级分类也需要删掉
            CategoryVO topCategoryVO = categoryMapper.selectVoById(categoryVO.getParentId());
            //需求是把国产水果和进口水果合成水果
            String mergeTopName = getMergeName(topCategoryVO.getName());
            if (mergeTopName == null) {
                mergeTopName = topCategoryVO.getName();
            }
            String selectKey = ProductRedisNames.SALE_CATEGORY_SELECT + mergeTopName;
            String selectHkey = regionWhId.toString();
            //获取一级分类下当前总仓下的所有二级分类
            HashMap<String, SaleCategoryVo> map = RedisUtils.getCacheMapValue(selectKey, selectHkey);
            if (map != null) {
                //获取所属二级分类信息
                SaleCategoryVo vo = map.get(categoryVO.getName());
                if (vo != null) {
                    vo.getCategoryIdList().remove(categoryVO.getId());
                    if (vo.getCategoryIdList().size() == 0) {
                        //如果所属二级分类下面的id被删完了，那么删除这个二级分类的信息
                        map.remove(categoryVO.getName());
                    }
                }
                if (map.size() == 0) {
                    //如果一级分类下当前总仓下没有二级分类了，则删除一级分类下的总仓
                    RedisUtils.delCacheMapValue(selectKey, selectHkey);

                    String topSelectKey = ProductRedisNames.SALE_TOP_CATEGORY_SELECT;
                    String topSelectHkey = regionWhId.toString();
                    //获取总仓的所有一级分类
                    HashMap<String, SaleCategoryVo> topMap = RedisUtils.getCacheMapValue(topSelectKey, topSelectHkey);
                    if (topMap != null) {
                        //删除总仓下的一级分类
                        SaleCategoryVo topVo = topMap.get(mergeTopName);
                        if (topVo != null) {
                            topVo.getCategoryIdList().remove(topCategoryVO.getId());
                            if (topVo.getCategoryIdList().size() == 0) {
                                topMap.remove(mergeTopName);
                            }
                        }
                        if (topMap.size() == 0) {
                            //如果总仓下没有一级分类了，就把整个总仓都删掉
                            RedisUtils.delCacheMapValue(topSelectKey, topSelectHkey);
                        } else {
                            //如果总仓下还有一级分类，则保存最新数据
                            RedisUtils.setCacheMapValue(topSelectKey, topSelectHkey, topMap);
                        }
                    }
                } else {
                    //如果一级分类下当前总仓下还有二级分类了，则保存最新数据
                    RedisUtils.setCacheMapValue(selectKey, selectHkey, map);
                }
            }
        }
    }

    /**
     * 每天定时任务批量下架总仓所有商品，处理总仓所属二级目录
     *
     * @param regionWhId
     */
    public void handleAll(Long regionWhId) {
        log.keyword("batchDown-" + regionWhId).info("删除分类缓存");
        //删除所属总仓下的所有数据
        String updateKey = ProductRedisNames.SALE_CATEGORY_UPDATE + regionWhId;
        RedisUtils.deleteObject(updateKey);
        RedisUtils.delCacheMapValue(ProductRedisNames.SALE_TOP_CATEGORY_SELECT, regionWhId.toString());
        List<CategoryVO> categoryList = categoryMapper.getTopCategory();
        if (categoryList != null && categoryList.size() > 0) {
            String selectHkey = regionWhId.toString();
            for (CategoryVO vo : categoryList) {
                String mergeTopName = getMergeName(vo.getName());
                if (mergeTopName == null) {
                    mergeTopName = vo.getName();
                }
                String selectKey = ProductRedisNames.SALE_CATEGORY_SELECT + mergeTopName;
                RedisUtils.delCacheMapValue(selectKey, selectHkey);
            }
        }
    }

    /**
     * 根据供应商商品ID查询供应商供货的回填信息
     *
     * @param bo
     * @return
     */
    @Override
    public GetBackInfoVo getBackInfo(GetBackInfoBo bo) {
        SpuVO spuVO = spuService.getById(bo.getSpuId());
        if (spuVO == null) {
            throw new ServiceException("平台商品不存在");
        }
        if (spuVO.getStatus() == 0){
            throw new ServiceException("平台商品已被禁用，请联系管理员");
        }
        Category category = categoryMapper.selectById(spuVO.getCategoryId());
        if (category == null) {
            throw new ServiceException("品类不存在");
        }
        if (category.getStatus() == 0) {
            throw new ServiceException("平台商品品类已被禁用，请联系管理员");
        }
        RemoteRegionWhVo regionWhVo = remoteRegionWhService.queryById(bo.getRegionWhId());
        if (regionWhVo == null) {
            throw new ServiceException("总仓不存在");
        }
        BuyerCategoryBO categoryBO = new BuyerCategoryBO();
        categoryBO.setCategoryId(category.getParentId());
        categoryBO.setRegionWhCode(regionWhVo.getRegionWhCode());
        //获取这个中类的采购员
        BasCustomerBo buyer = spuService.getBuyerByCategoryId(categoryBO);

        //根据供应商商品ID查询批次商品最后供货的供应商商品批次
        QueryLastListBo queryLastListBo = new QueryLastListBo();
        queryLastListBo.setSpuIdList(Lists.newArrayList(bo.getSpuId()));
        //获取供应商信息
        LoginUser loginUser = LoginHelper.getLoginUser();
        Long supplierId = loginUser.getRelationId();
        Long supplierDeptId = loginUser.getDeptId();
        queryLastListBo.setRegionWhIdList(Lists.newArrayList(bo.getRegionWhId()));
        if (ObjectUtil.isNotEmpty(bo.getBusinessType())) {
            queryLastListBo.setBusinessType(bo.getBusinessType());
        }
        // 供应商供货
        if (!bo.isManage()) {
            queryLastListBo.setSupplierIdList(Lists.newArrayList(supplierId));
            // 无档口是 supplierDeptId=0，也要生效，查出无档口的信息
            queryLastListBo.setSupplierDeptIdList(Lists.newArrayList(supplierDeptId));
            // 供应商支持多种类型，getBackInfo 传入 businessType 吧
//            queryLastListBo.setBusinessTypeList(Lists.newArrayList(SupplierSkuBusinessTypeEnum.BUSINESS_TYPE1.getCode(), SupplierSkuBusinessTypeEnum.BUSINESS_TYPE10.getCode()));
        } else {
            // 总仓供货
            queryLastListBo.setBusinessTypeList(Lists.newArrayList(SupplierSkuBusinessTypeEnum.BUSINESS_TYPE20.getCode(), SupplierSkuBusinessTypeEnum.BUSINESS_TYPE30.getCode()));
        }
//        List<SupplierSkuVo> supplierSkuVoList = supplierSkuMapper.queryLastList(queryLastListBo);
        // 填充档口名称
//        fillDeptSupplierInfo(supplierSkuVoList);
        GetBackInfoVo infoVo;
        //查询供应商商品
        SupplierSpuBo supplierSpuBo = new SupplierSpuBo();
        supplierSpuBo.setSpuId(bo.getSpuId());
        // 总仓无需关注供应商
        if (!bo.isManage()) {
            supplierSpuBo.setSupplierId(supplierId);
            supplierSpuBo.setSupplierDeptId(supplierDeptId);
        }
        // 档口版本如有脏数据则为null
        SupplierSpuVo supplierSpuVo = supplierSpuMapper.querySupplierSpu(supplierSpuBo);
//        if(supplierSkuVoList == null || supplierSkuVoList.isEmpty()) {
        if (supplierSpuVo != null) {
            infoVo = GetBackInfoVo.createBackInfoVo(supplierSpuVo);
        } else {
            infoVo = GetBackInfoVo.createBackInfoVo(spuVO);
        }
//            infoVo.setIsCheck(SupplierSkuCheckEnum.BUSINESS_TYPE2.getCode());
//            infoVo.buildSpuStandardsName();
        infoVo.setRegionWhId(bo.getRegionWhId());
        infoVo.setRegionWhName(regionWhVo.getRegionWhName());
        List<SpuCreateControlVO> spuCreateControlVOList = spuCreateControlMapper.queryList(bo.getSpuId());
        if (CollectionUtil.isNotEmpty(spuCreateControlVOList)) {
            SpuCreateControlVO spuCreateControlVO = spuCreateControlVOList.get(0);
            if (regionWhVo.getQualityReport() > 0 && spuCreateControlVO.getQualityReport() > 0) {
                spuCreateControlVO.setQualityReport(1);
            } else {
                spuCreateControlVO.setQualityReport(0);
            }
            infoVo.setControlList(spuCreateControlVOList);
        }
        infoVo.setCategoryId(category.getId());
        infoVo.setCategoryPathName(category.getPathName());
        // 指定总仓不返回采购员 - 天津总仓
        if (!noCheckGoodsProperties.getRegionWhIdList().contains(bo.getRegionWhId())) {
            infoVo.setBuyerCode(buyer.getCode());
            infoVo.setBuyerName(buyer.getName());
        }
        // 返回前端可选信息（等级、包装、规格等）
        infoVo.setExpList(spuVO.getExpList());
        // 售后规则取3分类的
        infoVo.setAfterSaleType(category.getAfterType());
        // 处理售后规则
        CategoryAfterTypeEnum afterTypeEnum = CategoryAfterTypeEnum.loadByCode(infoVo.getAfterSaleType());
        if (afterTypeEnum != null) {
            infoVo.setAfterSaleName(afterTypeEnum.getName());
            infoVo.setAfterSaleDesc(afterTypeEnum.getDesc());
        }
        // 供应商回填档口信息
        if (!bo.isManage() && supplierDeptId != null && supplierDeptId != 0L) {
            RemoteDeptDetailVo byDeptId = remoteDeptService.getByDeptId(supplierDeptId);
            if (Objects.nonNull(byDeptId)) {
                infoVo.setSupplierDeptId(byDeptId.getDeptId());
                infoVo.setSupplierDeptName(byDeptId.getDeptName());
            }
        }
        // 获取商品使用客户门店类型
        String customerStoreTypes = infoVo.getCustomerStoreTypes();
        if (StrUtil.isNotBlank(customerStoreTypes)) {
            infoVo.setCustomerStoreTypeValues(CustomerStoreTypeEnum.getNamesByIds(customerStoreTypes));
        }
        return infoVo;
//        }
/*        // 取的最后一个
        SupplierSkuVo supplierSkuVo = supplierSkuVoList.get(0);
        // 规格转换
        supplierSkuVo.tranSpuStandardsName().buildSkuStandardsList();
        infoVo = MapstructUtils.convert(supplierSkuVo, GetBackInfoVo.class);

        if (ObjectUtil.isNotEmpty(supplierSkuVo.getProvideRegionWhId()) && ObjectUtil.notEqual(supplierSkuVo.getProvideRegionWhId(), 0L)) {
            RemoteRegionWhVo provideRegionWhId = remoteRegionWhService.queryById(supplierSkuVo.getProvideRegionWhId());
            infoVo.setProvideRegionWhName(ObjectUtil.isNotEmpty(provideRegionWhId) ? provideRegionWhId.getRegionWhName() : null);
        }
        if (ObjectUtil.isNotEmpty(supplierSkuVo.getBasPortageTeamId())) {
            RemotePortageTeamQueryBo queryBo = new RemotePortageTeamQueryBo();
            queryBo.setTeamId(supplierSkuVo.getBasPortageTeamId());
            List<RemotePortageTeamVo> remotePortageTeamVos = remotePortageTeamService.queryList(queryBo);
            if (ObjectUtil.isNotEmpty(remotePortageTeamVos)) {
                RemotePortageTeamVo vo = remotePortageTeamVos.get(0);
                infoVo.setBasPortageTeamName(vo.getTeamName());
            }
        }
        infoVo.setSpuStandards(null);
        infoVo.setSpuStandardsName(null);
        // 采购员批次大于分类
        if(StringUtils.isBlank(infoVo.getBuyerCode())) {
            infoVo.setBuyerCode(buyer.getCode());
            infoVo.setBuyerName(buyer.getName());
        }
        // 指定总仓不返回采购员 - 天津总仓
        if(noCheckGoodsProperties.getRegionWhIdList().contains(bo.getRegionWhId())){
            infoVo.setBuyerCode(null);
            infoVo.setBuyerName(null);
        }
        infoVo.buildSpuStandardsName();
        infoVo.setSpuStandards(null);
//        infoVo.setPrice(supplierSkuVo.getPrice());
        infoVo.setStock(supplierSkuVo.getUpStock());
        infoVo.setControlList(spuCreateControlVOList);

        //根据最后供货的供应商商品批次id查询禁止下单城市仓信息
        infoVo.setCityWhList(supplierSkuDisableCityWhMapper.queryList(supplierSkuVo.getId()));

        //根据平台商品控制参数，判断需要回填哪些文件
        getBackFile(infoVo, spuCreateControlVOList, supplierSpuVo, supplierSkuVo);
        // 返回前端可选信息（等级、包装、规格等）
        infoVo.setExpList(spuVO.getExpList());
        // 售后规则, 商品没有就取3分类的
        if(infoVo.getAfterSaleType() == null){
            infoVo.setAfterSaleType(category.getAfterType());
        }
        // 处理售后规则
        CategoryAfterTypeEnum afterTypeEnum = CategoryAfterTypeEnum.loadByCode(infoVo.getAfterSaleType());
        if(afterTypeEnum != null){
            infoVo.setAfterSaleName(afterTypeEnum.getName());
            infoVo.setAfterSaleDesc(afterTypeEnum.getDesc());
        }
        infoVo.setRegionWhName(regionWhVo.getRegionWhName());

        // 区域简称
        if(StringUtils.isNotBlank(infoVo.getAreaCode()) && StringUtils.isBlank(infoVo.getShortProducer())){
            RemotePubAreaVO remotePubAreaVO = remoteBasPubAreaService.queryByCode(infoVo.getAreaCode());
            if(remotePubAreaVO != null){
                infoVo.setShortProducer(remotePubAreaVO.getAreaAlias());
            }
        }
        // 云仓库存 - 优先与批次库存
        infoVo.setBatchStock(infoVo.getStock());
        if(supplierSkuVo.getSkuId() != null && supplierSkuVo.getSkuId() != 0L) {
            CwStockDetailVo stockDetailVo = cwStockDetailService.getBySkuId(supplierSkuVo.getSkuId());
            Integer stock = ObjectUtil.defaultIfNull(stockDetailVo.getOnhandQty(), BigDecimal.ZERO).intValue();
            infoVo.setCloudStock(stock);
        }

        // 返回质检显示
        infoVo.setIsCheck(isNoCheck(infoVo.getRegionWhId(), infoVo.getCategoryPathName(), infoVo.getBusinessType()));
        return infoVo;*/
    }

    /**
     * 根据供应商商品销售批次ID查询供应商供货的回填信息
     *
     * @param supplierSkuId
     * @return
     */
    @Override
    public GetBackInfoVo getBackInfoBySkuId(Long supplierSkuId) {
        SupplierSkuVo supplierSkuVo = supplierSkuMapper.queryById(supplierSkuId);
        if (supplierSkuVo == null) {
            throw new ServiceException("销售批次不存在");
        }
        // 写入档口信息
        fillDeptSupplierInfo(supplierSkuVo);
        // 构建规格信息
        supplierSkuVo.tranSpuStandardsName().buildSkuStandardsList();
        SpuVO spuVO = spuService.getById(supplierSkuVo.getSpuId());
        if (spuVO == null) {
            throw new ServiceException("平台商品不存在");
        }
        if (spuVO.getStatus() == 0){
            throw new ServiceException("平台商品已被禁用，请联系管理员");
        }

        Category category = categoryMapper.selectById(spuVO.getCategoryId());
        if (category == null) {
            throw new ServiceException("品类不存在");
        }
        if (category.getStatus() == 0){
            throw new ServiceException("平台商品品类已被禁用，请联系管理员");
        }
        RemoteRegionWhVo regionWhVo = remoteRegionWhService.queryById(supplierSkuVo.getRegionWhId());
        if (regionWhVo == null) {
            throw new ServiceException("总仓不存在");
        }
        BuyerCategoryBO categoryBO = new BuyerCategoryBO();
        categoryBO.setCategoryId(category.getParentId());
        categoryBO.setRegionWhCode(regionWhVo.getRegionWhCode());
        //获取这个中类的采购员
        BasCustomerBo buyer = spuService.getBuyerByCategoryId(categoryBO);
        SupplierSpuVo supplierSpuVo = supplierSpuMapper.selectVoById(supplierSkuVo.getSupplierSpuId());
        if (supplierSpuVo == null) {
            throw new ServiceException("供应商商品已被删除，请在供应商小程序重新供货");
        }
        GetBackInfoVo infoVo = MapstructUtils.convert(supplierSkuVo, GetBackInfoVo.class);
        // 供货总仓名称
        if (ObjectUtil.isNotEmpty(supplierSkuVo.getProvideRegionWhId()) && ObjectUtil.notEqual(supplierSkuVo.getProvideRegionWhId(), 0L)) {
            RemoteRegionWhVo provideRegionWhVo = remoteRegionWhService.queryById(supplierSkuVo.getProvideRegionWhId());
            infoVo.setProvideRegionWhName(ObjectUtil.isNotEmpty(provideRegionWhVo) ? provideRegionWhVo.getRegionWhName() : null);
            infoVo.setProvideRegionWhIsSaleNum(ObjectUtil.isNotEmpty(provideRegionWhVo) ? regionWhVo.getIsSaleNum() : null);
        }

        // 优先取批次的
        if (StringUtils.isBlank(infoVo.getBuyerCode())) {
            infoVo.setBuyerCode(buyer.getCode());
            infoVo.setBuyerName(buyer.getName());
        }
        // 免检规则
        infoVo.setIsCheck(isNoCheck(infoVo.getRegionWhId(), infoVo.getCategoryPathName(), infoVo.getBusinessType(), infoVo.getSaleType()));
        // 指定总仓不返回采购员 - 天津总仓
        if (noCheckGoodsProperties.getRegionWhIdList().contains(supplierSkuVo.getRegionWhId())) {
            infoVo.setBuyerCode(null);
            infoVo.setBuyerName(null);
        }
        RemoteSupplierVo supplierVo = remoteSupplierService.getSupplierById(infoVo.getSupplierId());
        if (supplierVo == null) {
            throw new ServiceException("供应商不存在");
        }
        infoVo.setSupplierName(supplierVo.getName());
        infoVo.buildSpuStandardsName();
//        infoVo.setPrice(supplierSkuVo.getPrice());
        infoVo.setStock(supplierSkuVo.getUpStock());
        List<SpuCreateControlVO> spuCreateControlVOList = spuCreateControlMapper.queryList(supplierSkuVo.getSpuId());
        if (CollectionUtil.isNotEmpty(spuCreateControlVOList)) {
            SpuCreateControlVO spuCreateControlVO = spuCreateControlVOList.get(0);
            if (regionWhVo.getQualityReport() > 0 && spuCreateControlVO.getQualityReport() > 0) {
                spuCreateControlVO.setQualityReport(1);
            } else {
                spuCreateControlVO.setQualityReport(0);
            }
            infoVo.setControlList(spuCreateControlVOList);
        }
        // 返回前端可选信息（等级、包装、规格等）
        infoVo.setExpList(spuVO.getExpList());
        // 售后规则, 商品没有就取3分类的
        if (infoVo.getAfterSaleType() == null) {
            infoVo.setAfterSaleType(category.getAfterType());
        }
        // 处理售后规则
        CategoryAfterTypeEnum afterTypeEnum = CategoryAfterTypeEnum.loadByCode(infoVo.getAfterSaleType());
        if (afterTypeEnum != null) {
            infoVo.setAfterSaleName(afterTypeEnum.getName());
            infoVo.setAfterSaleDesc(afterTypeEnum.getDesc());
        }
        List<SupplierSkuDisableCityWhVo> cityWhVos = supplierSkuDisableCityWhMapper.queryList(supplierSkuVo.getSkuId(), List.of(1));
        // 设置城市仓名名称
        if (!cityWhVos.isEmpty()){
            List<Long> ids = cityWhVos.stream()
                    .map(SupplierSkuDisableCityWhVo::getCityWhId)
                    .collect(Collectors.toList());
            List<RemoteCityWhVo> remoteCityWhVos = remoteCityWhService.queryList(ids);
            // 创建映射关系并直接设置
            Map<Long, RemoteCityWhVo> idToRemoteCityWhVoMap = remoteCityWhVos.stream().collect(Collectors.toMap(RemoteCityWhVo::getId, Function.identity()));
            // 过滤掉在城市仓中找不到的并重新赋值
            cityWhVos = cityWhVos.stream()
                .filter(cityWhVo -> (idToRemoteCityWhVoMap.get(cityWhVo.getCityWhId()) != null && idToRemoteCityWhVoMap.get(cityWhVo.getCityWhId()).getStatus() == 1))
                .peek(cityWhVo -> cityWhVo.setCityWhName(idToRemoteCityWhVoMap.get(cityWhVo.getCityWhId()).getName()))
                .collect(Collectors.toList());
        }
        //根据最后供货的供应商商品批次id查询禁止下单城市仓信息
        infoVo.setCityWhList(cityWhVos);

        //根据平台商品控制参数，判断需要回填哪些文件
        getBackFile(infoVo, spuCreateControlVOList, supplierSpuVo, supplierSkuVo);

        // 区域简称
        if (StringUtils.isNotBlank(infoVo.getAreaCode()) && StringUtils.isBlank(infoVo.getShortProducer())) {
            RemotePubAreaVO remotePubAreaVO = remoteBasPubAreaService.queryByCode(infoVo.getAreaCode());
            if (remotePubAreaVO != null) {
                infoVo.setShortProducer(remotePubAreaVO.getAreaAlias());
            }
        }
        // 云仓库存 - 优先与批次库存
        Long skuId = supplierSkuVo.getSkuId();
        if (skuId != null && skuId != 0L) {
            CwStockDetailVo stockDetailVo = cwStockDetailService.getBySkuId(skuId);
            if (stockDetailVo != null) {
                int stock = ObjectUtil.defaultIfNull(stockDetailVo.getOnhandQty(), BigDecimal.ZERO).intValue();
                int lockStock = ObjectUtil.defaultIfNull(stockDetailVo.getAllocationQty(), BigDecimal.ZERO).intValue();
                int onWayStock = ObjectUtil.defaultIfNull(stockDetailVo.getOnwayQty(), BigDecimal.ZERO).intValue();
                infoVo.setCloudOnWayStock(onWayStock);
                infoVo.setCloudActualStock(stock);
                infoVo.setCloudLockStock(lockStock);
                infoVo.setCloudStock(stock - lockStock + onWayStock);
            }
        }
        infoVo.setBatchStock(supplierSkuVo.getStock());
        // 供货总仓名称
        if (ObjectUtil.isNotEmpty(infoVo.getProvideRegionWhId()) && ObjectUtil.notEqual(infoVo.getProvideRegionWhId(), 0L)) {
            RemoteRegionWhVo provideRegion = remoteRegionWhService.queryById(infoVo.getProvideRegionWhId());
            infoVo.setProvideRegionWhName(ObjectUtil.isNotEmpty(provideRegion) ? provideRegion.getRegionWhName() : null);
        }
        // 批次最后上架时间
        SupplierSkuVo lastOne = null;
        if (infoVo.getUpTime() != null) {
            infoVo.setLastUpTime(infoVo.getUpTime());
            lastOne = supplierSkuVo;
        } else {
            //检验这个供应商今天能不能给总仓供这个货
            CheckSupplierSkuBo checkSupplierSkuBo = new CheckSupplierSkuBo();
            checkSupplierSkuBo.setSkuId(supplierSkuVo.getSkuId());
            checkSupplierSkuBo.setLastUpTime(1);
            lastOne = supplierSkuMapper.checkSupplierSku(checkSupplierSkuBo);
            if (lastOne != null) {
                infoVo.setLastUpTime(lastOne.getUpTime());
            }
        }

        BigDecimal netWeightPrice = infoVo.getPrice().divide(infoVo.getSpuNetWeight(), 2, RoundingMode.HALF_UP);
        if (netWeightPrice.compareTo(BigDecimal.ZERO) == 0) {
            netWeightPrice = new BigDecimal("0.01");
        }
        infoVo.setNetWeightPrice(netWeightPrice);

        // BI 报损 + 质检不合格
        LocalDate saleDate = getSaleDate(supplierSkuVo.getRegionWhId());
        Map<Long, RemoteSkuLossVo> lossMap = getRemoteSkuLossVoMap(Lists.newArrayList(skuId), saleDate);
        SkuLossResult result = getSkuLossResult(lossMap, skuId);
        infoVo.setRemoteSkuLossVo(result.remoteSkuLossVo());
        infoVo.setRemoteSkuCard(result.remoteSkuCard());

        // 设置7天销售数量和昨天销售数量
        salesQuantityCount(infoVo.getRegionWhId(), skuId, infoVo.getRemoteSkuLossVo());
        // 清空特价信息
        infoVo.setBargain(0);
        infoVo.setBargainRate(null);
        infoVo.setMarketPrice(null);
        // 特价
        if (lastOne != null) {
            LocalDate nearDay = saleDate.plusDays(-1L * bargainGoodsProperties.getNearDay());
            boolean inTime = lastOne.getSaleDate().isAfter(nearDay);
            boolean soldSome = lastOne.getCopySold() >= bargainGoodsProperties.getSold();
            if (inTime && soldSome) {
                BigDecimal bargainPrice = lastOne.getPrice().multiply(BigDecimal.ONE.subtract(bargainGoodsProperties.getRate()));
                infoVo.setBargainPrice(bargainPrice);
                log.keyword("teJia-Debug").info("商品特价={}", bargainPrice);
            }
        }
        // 获取商品使用的门店类型
        String customerStoreTypes = infoVo.getCustomerStoreTypes();
        if (StrUtil.isNotBlank(customerStoreTypes)) {
            infoVo.setCustomerStoreTypeValues(CustomerStoreTypeEnum.getNamesByIds(customerStoreTypes));
        }
        return infoVo;
    }


    /**
     * 根据平台商品控制参数，判断需要回填哪些文件
     *
     * @param infoVo
     * @param spuCreateControlVOList
     * @param supplierSpuVo
     * @param supplierSkuVo
     */
    public void getBackFile(GetBackInfoVo infoVo, List<SpuCreateControlVO> spuCreateControlVOList, SupplierSpuVo supplierSpuVo, SupplierSkuVo supplierSkuVo) {
        //根据平台商品控制参数，判断需要回填哪些文件
        if (spuCreateControlVOList != null && spuCreateControlVOList.size() > 0) {
            //计算当前时间和上次清除时间直接的天数差
            long interval = supplierSpuVo == null ? 0 : ChronoUnit.DAYS.between(supplierSpuVo.getLastClearTime(), LocalDate.now());
            long qualityReportInterval = supplierSpuVo == null ? 0 : ChronoUnit.DAYS.between(supplierSpuVo.getQualityReportLastClearTime(), LocalDate.now());
            SpuCreateControlVO createControlVO = spuCreateControlVOList.get(0);
            //判断是否回填商品简介
            if (createControlVO.getSkuIntroductionCle() == 1 && createControlVO.getAutoCle() > 0 && createControlVO.getAutoCle() <= interval) {
                infoVo.setSnapshot(null);
            }
            //查询批次文件
            QuerySupplierSkuFileBo queryFileBo = new QuerySupplierSkuFileBo();
            queryFileBo.setSupplierSkuIdList(Lists.newArrayList(supplierSkuVo.getId()));
            List<SupplierSkuFileVo> fileVoList = supplierSkuFileMapper.queryList(queryFileBo);
            Map<Integer, List<SupplierSkuFileVo>> fileMap = new HashMap<>();
            if (fileVoList != null && fileVoList.size() > 0) {
                fileMap = fileVoList.stream().collect(Collectors.groupingBy(SupplierSkuFileVo::getType));
            }
            List<SupplierSkuFileVo> newFileVoList = new ArrayList<>();
            //判断是否回填商品图片
            if (createControlVO.getSkuDetailImgCle() == 0 || createControlVO.getAutoCle() == 0 || createControlVO.getAutoCle() > interval) {
                List<SupplierSkuFileVo> type1 = fileMap.get(SupplierSkuFileTypeEnum.TYPE1.getCode());
                if (CollectionUtil.isNotEmpty(type1)) {
                    newFileVoList.addAll(type1);
                }
            }
            //判断是否回填商品视频
            if (createControlVO.getSkuVideoCle() == 0 || createControlVO.getAutoCle() == 0 || createControlVO.getAutoCle() > interval) {
                List<SupplierSkuFileVo> type2 = fileMap.get(SupplierSkuFileTypeEnum.TYPE2.getCode());
                if (CollectionUtil.isNotEmpty(type2)) {
                    newFileVoList.addAll(type2);
                }
            }
            //判断是否回填包装图片
            if (createControlVO.getSkuPackImgCle() == 0 || createControlVO.getAutoCle() == 0 || createControlVO.getAutoCle() > interval) {
                List<SupplierSkuFileVo> type3 = fileMap.get(SupplierSkuFileTypeEnum.TYPE3.getCode());
                if (CollectionUtil.isNotEmpty(type3)) {
                    newFileVoList.addAll(type3);
                }
                List<SupplierSkuFileVo> type31 = fileMap.get(SupplierSkuFileTypeEnum.TYPE31.getCode());
                if (CollectionUtil.isNotEmpty(type31)) {
                    newFileVoList.addAll(type31);
                }
                List<SupplierSkuFileVo> type32 = fileMap.get(SupplierSkuFileTypeEnum.TYPE32.getCode());
                if (CollectionUtil.isNotEmpty(type32)) {
                    newFileVoList.addAll(type32);
                }
                List<SupplierSkuFileVo> type33 = fileMap.get(SupplierSkuFileTypeEnum.TYPE33.getCode());
                if (CollectionUtil.isNotEmpty(type33)) {
                    newFileVoList.addAll(type33);
                }
            }
            //判断是否回填包装视频
            if (createControlVO.getSkuPackVideoCle() == 0 || createControlVO.getAutoCle() == 0 || createControlVO.getAutoCle() > interval) {
                List<SupplierSkuFileVo> type4 = fileMap.get(SupplierSkuFileTypeEnum.TYPE4.getCode());
                if (CollectionUtil.isNotEmpty(type4)) {
                    newFileVoList.addAll(type4);
                }
            }
            //判断是否回填质检报告
            //这个跟上面的判断不一样
            if (createControlVO.getQualityReportCle() == 0 || createControlVO.getQualityReportCle() > qualityReportInterval) {
                List<SupplierSkuFileVo> type5 = fileMap.get(SupplierSkuFileTypeEnum.TYPE5.getCode());
                if (CollectionUtil.isNotEmpty(type5)) {
                    newFileVoList.addAll(type5);
                }
            }
            infoVo.setFileList(newFileVoList);
        }
    }

    /**
     * 清空库存
     *
     * @param idList
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean clearStock(List<Long> idList) {
        LambdaUpdateWrapper<SupplierSkuStock> lqw = Wrappers.lambdaUpdate();
        lqw.in(SupplierSkuStock::getSupplierSkuId, idList);
        lqw.set(SupplierSkuStock::getStock, 0);
        return supplierSkuStockMapper.update(lqw) > 0;
    }

    /**
     * 快捷上架
     *
     * @param skuVo
     * @param upType 上架类型，0-普通的快捷上架，1-补贴上架
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @Lock4j(name = ProductRedisNames.SUPPLIER_SKU_ADD, keys = "#skuVo.spuId + '_' + #skuVo.regionWhId + '_' + #skuVo.supplierId", expire = 30000, acquireTimeout = 1000)
    public Boolean quickUp(SupplierSkuVo skuVo, boolean sup, String operate, Integer upType) {
        // 检查商品是否被禁用
        checkSpuIsDisable(skuVo.getSpuId());
        if(phGoodsProperties.getSpuIdList().contains(skuVo.getSpuId()) || Objects.equals(phGoodsProperties.getCategoryId(),skuVo.getCategoryId())){
            throw new ServiceException("配货商品不能快捷上架");
        }
        //处理供应商信息
        RemoteSupplierVo supplierVo = remoteSupplierService.getSupplierById(skuVo.getSupplierId());
        if (supplierVo == null) {
            throw new ServiceException("供应商不存在");
        }
        //校验子账户是否禁止入金（调用平安接口检查）
        checkOrgControlStatus(supplierVo.getCode());
        // 恢复商品规格
        skuVo.recoverDbSpuStandards();
        //检验毛重净重
        verifyWeight(skuVo.getSpuGrossWeight(), skuVo.getSpuNetWeight());
        //检验品类是否禁用
        checkCategory(skuVo.getCategoryId());
        LocalDate saleDate = getSaleDate(skuVo.getRegionWhId());
        //补贴上架
        if(Objects.equals(upType, 1)){
            SupplierSkuSubsidyAuditCheckVo checkVo = new SupplierSkuSubsidyAuditCheckVo();
            BeanUtils.copyProperties(skuVo, checkVo);
            this.checkSubsidyAudit(checkVo);
            // 特价率计算
            BigDecimal priceDownRate = skuVo.getMarketPrice().subtract(skuVo.getPrice()).divide(skuVo.getMarketPrice(), 3, RoundingMode.HALF_UP);
            skuVo.setBargainRate(priceDownRate);
        }else{
            // 清空特价信息
            skuVo.setBargain(0);
            skuVo.setBargainRate(null);
            skuVo.setMarketPrice(null);
            //不继承补贴
            skuVo.setSubsidyAmount(BigDecimal.ZERO);
        }
        // 坑位校验
        checkMaxSaleNum(skuVo.getSupplierId(), skuVo.getRegionWhId(), skuVo.getSaleType());
        //检验这个供应商今天能不能给总仓供这个货
        CheckSupplierSkuBo checkSupplierSkuBo = new CheckSupplierSkuBo();
        checkSupplierSkuBo.setSpuId(skuVo.getSpuId());
        checkSupplierSkuBo.setRegionWhId(skuVo.getRegionWhId());
        checkSupplierSkuBo.setSupplierId(skuVo.getSupplierId());
        checkSupplierSkuBo.setSupplierDeptId(skuVo.getSupplierDeptId());
        checkSupplierSkuBo.setSpuGrade(Optional.ofNullable(skuVo.getSpuGrade()).orElseGet(() -> ""));
        checkSupplierSkuBo.setSpuStandards(Optional.ofNullable(skuVo.getSpuStandards()).orElseGet(() -> ""));
        checkSupplierSkuBo.setDomestic(Optional.ofNullable(skuVo.getDomestic()).orElseGet(() -> 0));
        checkSupplierSkuBo.setBusinessType(skuVo.getBusinessType());
        checkSupplierSkuBo.setProvideRegionWhId(skuVo.getProvideRegionWhId());
        checkSupplierSkuBo.setSpuName(skuVo.getSpuName());
        checkSupplierSkuBo.setCategoryId(skuVo.getCategoryId());
        // 不和尾货比较
        checkSupplierSkuBo.setBatchType(1);
        SupplierSkuVo lastOne = supplierSkuMapper.checkSupplierSku(checkSupplierSkuBo);
        if (lastOne != null) {
            if (saleDate.isEqual(lastOne.getSaleDate())) {
                throw new ServiceException("商品信息重复，请修改");
            }
            if (Objects.equals(lastOne.getStatus(), SupplierSkuStatusEnum.STATUS6.getCode())) {
                throw new ServiceException("该批次已过季，请恢复/修改后重新上架");
            }
        }
        // 检查平台商品 - 等级，规格，进出口，商品产地是否存在
        SpuVO spuVO = spuService.getById(skuVo.getSpuId());
        if (spuVO == null) {
            throw new ServiceException("平台商品不存在");
        }
        KeyValueBO.checkSpuExt(skuVo, spuVO.getExpList());
        // 快捷上架取最新的平台商品等级描述
        if (StringUtils.isNotBlank(skuVo.getSpuGrade())) {
            Map<Integer, Set<String>> spuExtJSONInfo = KeyValueBO.parseSpuExtJSONInfo(spuVO.getExpList());
            Set<String> gradeDescSet = spuExtJSONInfo.get(KeyValueBO.LEVEL_DESC);
            if (gradeDescSet != null) {
                Map<String, String> gradeDescMap = gradeDescSet.stream()
                        .filter(StringUtils::isNotEmpty).filter(s -> s.contains(":"))
                        .map(s -> s.split(":")).filter(s -> s.length > 1)
                        .collect(Collectors.toMap(s -> s[0], s -> s[1], (a, b) -> b));
                skuVo.setSpuGradeDesc(gradeDescMap.get(skuVo.getSpuGrade()));
            }
        }
        // 快捷上架平台分类/名称变更，隐藏历史无效批次
        if (!Objects.equals(spuVO.getCategorySpuId(), skuVo.getCategoryId()) || !Objects.equals(spuVO.getName(), skuVo.getSpuName())) {
            CheckSupplierSkuBo updateStatus = new CheckSupplierSkuBo();
            updateStatus.setSaleDate(saleDate);
            updateStatus.setSkuId(skuVo.getSkuId());
            int updateStatusResult = supplierSkuMapper.updateStatusHideByOnly(updateStatus);
            // 同步sku
            int syncSku = skuMapper.updateStatusHideByOnly(updateStatus);
            log.keyword("quickUp", skuVo.getId()).info("快捷上架修改历史批次数量={},sku修改数量={}", updateStatusResult, syncSku);
        }

        // 售后规则, 商品没有就取3分类的
        Category category = categoryMapper.selectById(spuVO.getCategoryId());
        if (category == null) {
            throw new ServiceException("品类不存在");
        }
        if (skuVo.getAfterSaleType() == null) {
            skuVo.setAfterSaleType(category.getAfterType());
        }
        // 处理售后规则
        CategoryAfterTypeEnum afterTypeEnum = CategoryAfterTypeEnum.loadByCode(skuVo.getAfterSaleType());

        //添加供应商商品批次
        SupplierSku addSku = MapstructUtils.convert(skuVo, SupplierSku.class);
        addSku.setId(null);
        convertSupplierSku(addSku);
        // 供应商 && 免审核开关未启动
        if (sup && !Objects.equals(noCheckGoodsProperties.getSupplierSkuReview(), 1) && !SupplierSkuSaleTypeEnum.SALE_TYPE2.getCode().equals(addSku.getSaleType())) {
            addSku.setStatus(SupplierSkuStatusEnum.STATUS1.getCode());
            addSku.setUpTime(null);
            // 无需送货审核
            addSku.setHasDeliveryAudit(0);
        }else {
            //总仓小程序操作快捷上架，而且是补贴上架
            if(Objects.equals(1, upType)){
                addSku.setStatus(SupplierSkuStatusEnum.STATUS8.getCode());
            }else{
                addSku.setStatus(SupplierSkuStatusEnum.STATUS4.getCode());
                addSku.setUpTime(now());
            }
        }

        addSku.setBatchType(SupplierSkuBatchTypeEnum.BATCH_TYPE1.getCode());
        //降价价格
        addSku.setDiscountPrice(BigDecimal.ZERO);
        addSku.setOriginalSupplierSkuId(null);
        addSku.setOriginalSupplierId(null);
        addSku.setSaleDate(saleDate);
        addSku.setDownTime(null);
        addSku.setPassDeliveryAudit(null);
        addSku.setCode(createSupplierSkuCode(addSku.getSpuId(), addSku.getSupplierId(), addSku.getSupplierSpuCode(), addSku.getSaleDate()));
        // 进口产地简称
        if (Objects.equals(addSku.getDomestic(), 1)) {
            addSku.setAreaCode("");
            addSku.setShortProducer(addSku.getProducer());
        }
        // 产地简称
        if (StringUtils.isNotBlank(addSku.getAreaCode()) && StringUtils.isBlank(addSku.getShortProducer())) {
            RemotePubAreaVO remotePubAreaVO = remoteBasPubAreaService.queryByCode(addSku.getAreaCode());
            if (remotePubAreaVO != null) {
                addSku.setShortProducer(remotePubAreaVO.getAreaAlias());
            }
        }
        // 默认非免检

        // 默认非免检
        Long regionWhId = addSku.getRegionWhId();
        addSku.setIsCheck(isNoCheck(regionWhId, addSku.getCategoryPathName(), addSku.getBusinessType(), addSku.getSaleType()));
        // 是否售罄
        addSku.setIsOut(ObjectUtil.defaultIfNull(skuVo.getStock(), 0) > 0 ? 0 : 1);
        //拼接全称
        String[] cateGoryPathNameArray = spuVO.getPathName().split("/");
        String spuAnotherName = getSpuAnotherName(spuVO.getCategoryId());
        String fullName = (addSku.getBrand() != null ? addSku.getBrand() : "") +
                (addSku.getSpuName() != null ? addSku.getSpuName() : "") +
                (addSku.getShortProducer() != null ? addSku.getShortProducer() : "") +
                (addSku.getSpuGrade() != null ? addSku.getSpuGrade() : "") +
                (addSku.getSpuStandards() != null ? addSku.getSpuStandards() : "") +
                (addSku.getCooperation() != null && addSku.getCooperation() == 1 ? "战略品" : "") +
                (SupplierSkuBusinessTypeEnum.BUSINESS_TYPE20.getCode().equals(addSku.getBusinessType())
                        ? SupplierSkuBusinessTypeEnum.BUSINESS_TYPE20.getDesc() : "") +
                cateGoryPathNameArray[2] + cateGoryPathNameArray[3] + spuAnotherName;
        addSku.setFullName(fullName);

        supplierSkuMapper.insert(addSku);
        // 批次新增同步sku
        Long id = addSku.getId();
        skuService.insertBatchSyncSku(id);
        //总仓操作补贴上架，生成补贴待审核单
        if(!sup && Objects.equals(1, upType)){
            addSupplierSkuSubsidyAudit(addSku, skuVo.getStatus());
        }
        //添加操作日志
        SupplierSkuApproveLog skuLog = new SupplierSkuApproveLog();
        skuLog.setSupplierSkuId(id);
        skuLog.setOperate(operate);
        skuLog.setStatus(addSku.getStatus());
        //检验补贴
        BigDecimal subsidyFreeAmount = remoteOrderService.getSubsidyFreeAmount(regionWhId);
        if (subsidyFreeAmount.multiply(addSku.getSpuGrossWeight()).compareTo(addSku.getPrice()) > 0) {
            throw new ServiceException("商品价格不能低于市场补贴价");
        }
        supplierSkuApproveLogMapper.insert(skuLog);

        //根据旧批次id给新批次复制创建库存，禁用城市仓，文件
        addSupplierSkuOther(addSku, skuVo.getId(), skuVo.getStock());
        if (SupplierSkuStatusEnum.STATUS4.getCode().equals(addSku.getStatus())) {
            handleUp(id, addSku.getCategoryId(), regionWhId);
        }

        if (sup) {
            sendDelayWxAuditMsg(id);
        }

        return true;
    }

    /**
     * 如果不在下午4-6点供货，发送10min的延迟消息，如果10分钟后尚未上架，则发送微信消息（1次）
     *
     * @param supplierSkuId
     */
    @Override
    public void sendDelayWxAuditMsg(Long supplierSkuId) {
        if (supplierSkuId == null) {
            throw new ServiceException("发送上架审核消息，批次id不能为空");
        }
        SupplierSku supplierSku = supplierSkuMapper.selectById(supplierSkuId);
        if (!SupplierSkuStatusEnum.STATUS1.getCode().equals(supplierSku.getStatus())) {
            return;
        }
        RemoteRegionWhVo whVo = remoteRegionWhService.queryById(supplierSku.getRegionWhId());
        LocalTime now = LocalTime.now();
        if (now.isBefore(LocalTime.parse(whVo.getSalesTimeStart())) || now.isAfter(LocalTime.parse(whVo.getSalesTimeEnd()))) {
            RemoteSkuMsgBo bo = new RemoteSkuMsgBo(supplierSku.getId(), supplierSku.getStatus(), -1, supplierSku.getCreateName(), supplierSku.getCreateTime());
            skuSendMsgProducer.send(bo);
        }
    }

    /**
     * 从3级分类
     *
     * @param categoryId
     * @return
     */
//    @Cacheable(cacheNames = ProductRedisNames.PRODUCT_CATEGORY_EXP_ID_KEY, key = "#categoryId")
    public String getSpuAnotherName(Long categoryId) {
        // 获取3级分类下的近似名
        LambdaQueryWrapper<CategoryExp> expLqw = new LambdaQueryWrapper<>();
        expLqw.eq(CategoryExp::getDelFlag, 0).eq(CategoryExp::getCategoryId, categoryId);
        List<CategoryExpVO> expList = spuExpMapper.selectVoList(expLqw);

        Map<Integer, Set<String>> spuExtJSONInfo = KeyValueBO.parseSpuExtJSONInfo(expList);
        String result = Optional.ofNullable(spuExtJSONInfo.get(KeyValueBO.ANOTHER_WORD)).map(e -> String.join("", e)).orElseGet(() -> "");
        log.keyword("Pei_debug").info("别名={}, categoryId={}, spuExpList={}, map={}", result, categoryId, JSONObject.toJSONString(expList), JSON.toJSONString(spuExtJSONInfo));
        return result;
    }

    /**
     * 定时批量下架供货商商品销售批次
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @Lock4j(name = ProductRedisNames.DOWN_SKU_LOCK, keys = "#bo.id + '_' + #bo.saleDate", expire = 30000, acquireTimeout = 1000)
    public void batchDown(RemoteRegionWhMsgBo bo) {
        log.keyword("batchDown-" + bo.getId()).info("{} 自动下架 {} 批次", bo.getRegionWhName(), bo.getSaleDate());
        if (!bo.isHigh()) {
            String key = ProductRedisNames.DOWN_SKU + bo.getId() + bo.getSaleDate();
            String value = RedisUtils.getCacheObject(key);
            if (StringUtils.isNotBlank(value)) {
                log.keyword("batchDown-" + bo.getId()).info("{}跳过执行自动下架", bo.getRegionWhName());
                return;
            }
            RedisUtils.setCacheObject(key, bo.getSaleDate().toString());
            RedisUtils.expire(key, Duration.ofHours(25));
        }
        //每天定时任务批量下架所有商品
        handleAll(bo.getId());
        // 下架同步sku
        skuService.batchDown(bo);
        // 指定总仓上架配货商品
        log.keyword("autoInsertList", bo.getId(), "batchDown-" + bo.getId()).info("配货商品生成范围：{}", JSON.toJSONString(phGoodsProperties.getRegionWhIdList()));
        if (phGoodsProperties.getRegionWhIdList().contains(bo.getId())) {
            log.keyword("autoInsertList", bo.getId(), "batchDown-" + bo.getId()).info("生成配货商品, 总仓id = {}", bo.getId());
            autoInsertList(bo.getId(), bo.getSaleDate().plusDays(1));
        }

        // 正常批次过了销售日全部下架
        LambdaUpdateWrapper<SupplierSku> luw = new LambdaUpdateWrapper<SupplierSku>()
                .eq(SupplierSku::getSaleDate, bo.getSaleDate())
                .eq(SupplierSku::getRegionWhId, bo.getId())
                .in(SupplierSku::getStatus, SupplierSkuStatusEnum.getCodesLessThan5())
                .set(SupplierSku::getStatus, SupplierSkuStatusEnum.STATUS5.getCode())
                .set(SupplierSku::getDownTime, new Date())
        //下架清空补贴金额
        .set(SupplierSku::getSubsidyAmount, BigDecimal.ZERO);
        int updateNum = supplierSkuMapper.update(luw);
        log.keyword("batchDown-" + bo.getId()).info("{} 批次下架: {}", bo.getRegionWhName(), updateNum);

        // 非当天的尾货全部隐藏
        LambdaUpdateWrapper<SupplierSku> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SupplierSku::getBatchType, SupplierSkuBatchTypeEnum.BATCH_TYPE2.getCode())
                .eq(SupplierSku::getStatus, SupplierSkuStatusEnum.STATUS5.getCode())
                .eq(SupplierSku::getSaleDate, bo.getSaleDate())
                // 没指定总仓就会MQ并发死锁
                .eq(SupplierSku::getRegionWhId, bo.getId())
                .set(SupplierSku::getStatus, SupplierSkuStatusEnum.STATUS7.getCode());
        int hideNum = supplierSkuMapper.update(updateWrapper);
        log.keyword("batchDown-" + bo.getId()).info("{} 尾货隐藏: {}", bo.getRegionWhName(), hideNum);


        int downSkuNum = skuSalesStatisticsMapper.batchDown(bo.getId());
        log.keyword("batchDown-" + bo.getId()).info("{} 统计sku批量下架: {}", bo.getRegionWhName(), downSkuNum);
    }

    @Override
    public void autoInsertList(Long regionWhId, LocalDate saleDate) {
        // 生成多个配货商品
        if (!phGoodsProperties.getSpuIdList().isEmpty()) {
            for (Long spuId : phGoodsProperties.getSpuIdList()) {
                try {
                    log.keyword("autoInsertList", spuId, regionWhId).info("开始生成配货商品, regionWhId={}, spuId = {}", regionWhId, spuId);
                    autoInsert(regionWhId, spuId, saleDate);
                } catch (Exception e) {
                    log.keyword("autoInsertList", spuId, regionWhId).error("配货商品生成失败", e);
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Lock4j(name = ProductRedisNames.SUPPLIER_SKU_ADD, keys = "#regionWhId + '_' + #spuId", expire = 30000, acquireTimeout = 1000)
    public Long autoInsert(Long regionWhId, Long spuId, LocalDate saleDate) {

        SupplierSku add = new SupplierSku();

        //检验平台商品信息
        SpuVO spu = spuService.getById(spuId);
        if (spu == null) {
            throw new ServiceException("配货-平台商品不存在！");
        }
        if (!Objects.equals(spu.getCategoryId(), phGoodsProperties.getCategoryId())) {
            throw new ServiceException("配货-平台商品设置分类不正确！");
        }
        add.setSpuId(spuId);
        //处理供应商信息
        RemoteSupplierVo supplierVo = remoteSupplierService.getSupplierById(phGoodsProperties.getSupplierId());
        if (supplierVo == null) {
            throw new ServiceException("供应商不存在");
        }
        add.setSupplierId(phGoodsProperties.getSupplierId());

        //处理总仓信息
        RemoteRegionWhVo whVo = remoteRegionWhService.queryById(regionWhId);
        if (whVo == null) {
            throw new ServiceException("总仓不存在");
        }
        if (whVo.getStatus() == 0) {
            throw new ServiceException("总仓已被禁用");
        }
        add.setRegionWhId(regionWhId);
        add.setRegionWhName(whVo.getRegionWhName());
        add.setRegionWhCode(whVo.getRegionWhCode());

        // 商品类型
        add.setBusinessType(SupplierSkuBusinessTypeEnum.BUSINESS_TYPE40.getCode());

        // 填充售后类型
        add.setAfterSaleDay(CategoryAfterTypeEnum.NOT.getCode());
        add.setAfterSaleType(CategoryAfterTypeEnum.NOT.getCode());
        add.setAfterSaleExplain("");
        // 全部质检
        add.setIsCheck(0);
        add.setSnapshot("");
        // 暂不限购
        add.setBuyMin(1);
        add.setBuyMax(-1);
        // 单击加购数量 1
        add.setPlaceOrderMultiple(1);

        // 进口产地简称
        add.setAreaCode("");
        add.setShortProducer("");

        // 价格库存毛重净重
        add.setPrice(phGoodsProperties.getPrice());
        add.setSpuGrossWeight(phGoodsProperties.getGrossWeight());
        add.setSpuNetWeight(phGoodsProperties.getNetWeight());

        //设置销售日期
        if (saleDate == null) {
            saleDate = SaleDateUtil.getSaleDate(whVo.getSalesTimeEnd());
        }
        add.setSaleDate(saleDate);
        // 上架
        add.setStatus(SupplierSkuStatusEnum.STATUS4.getCode());
        add.setUpTime(new Date());

        //批次商品设置平台商品部分属性
        add.setSpuCode(spu.getCode());
        add.setSpuName(spu.getName());
        add.setCategoryId(spu.getCategoryId());
        add.setCategoryName(spu.getCategoryName());
        add.setCategoryPathName(spu.getPathName());

        //设置供应商属性
        add.setSupplierId(supplierVo.getId());
        add.setSupplierCode(supplierVo.getCode());
        add.setSupplierName(supplierVo.getName());

        SupplierSpu supplierSpu = MapstructUtils.convert(add, SupplierSpu.class);
        SupplierSku supplierSku = MapstructUtils.convert(add, SupplierSku.class);


        //检验这个供应商今天能不能给总仓供这个货
        CheckSupplierSkuBo checkSupplierSkuBo = new CheckSupplierSkuBo();
        checkSupplierSkuBo.setSpuId(add.getSpuId());
        checkSupplierSkuBo.setRegionWhId(add.getRegionWhId());
        // 供货商品只允许拥有一份，与供应商无关
//        checkSupplierSkuBo.setSupplierId(add.getSupplierId());
//        checkSupplierSkuBo.setSupplierDeptId(add.getSupplierDeptId() == null ? 0L : add.getSupplierDeptId());
        checkSupplierSkuBo.setSpuGrade(Optional.ofNullable(add.getSpuGrade()).orElseGet(() -> ""));
        checkSupplierSkuBo.setSpuStandards(Optional.ofNullable(add.getSpuStandards()).orElseGet(() -> ""));
        checkSupplierSkuBo.setDomestic(Optional.ofNullable(add.getDomestic()).orElseGet(() -> 0));
        checkSupplierSkuBo.setBusinessType(add.getBusinessType());
        checkSupplierSkuBo.setProvideRegionWhId(add.getProvideRegionWhId() == null ? 0L : add.getProvideRegionWhId());
        checkSupplierSkuBo.setSpuName(spu.getName());
        checkSupplierSkuBo.setCategoryId(spu.getCategoryId());
        checkSupplierSkuBo.setSaleDate(saleDate);
        SupplierSkuVo lastOne = supplierSkuMapper.checkSupplierSku(checkSupplierSkuBo);
        if (lastOne != null) {
            if (saleDate.isEqual(lastOne.getSaleDate())) {
                throw new ServiceException("配货商品已上架");
            }
        }

        //查询供应商商品
        SupplierSpuBo spuBo = new SupplierSpuBo();
        spuBo.setSpuId(spu.getId());
        spuBo.setSupplierId(supplierVo.getId());
        spuBo.setSupplierDeptId(add.getSupplierDeptId());
        SupplierSpuVo supplierSpuVo = supplierSpuMapper.querySupplierSpu(spuBo);

        if (supplierSpuVo == null) {
            String simpleCode = supplierVo.getSimpleCode();
            if (add.getSupplierDeptId() != null && add.getSupplierDeptId() != 0) {
                RemoteDeptDetailVo deptDetailVo = remoteDeptService.getByDeptId(add.getSupplierDeptId());
                simpleCode = deptDetailVo.getDeptCode().replace("-", "");
            }
            //如果不存在供应商商品，则新增供应商商品
            supplierSpu.setCode(spu.getCode() + simpleCode);
            supplierSpu.setLabel(supplierSpu.getCode());
            supplierSpu.setLastClearTime(saleDate);
            supplierSpu.setQualityReportLastClearTime(saleDate);
            supplierSpuMapper.insert(supplierSpu);
            //供应商销售批次商品加入供应商商品的相关信息
            supplierSku.setSupplierSpuId(supplierSpu.getId());
            supplierSku.setSupplierSpuCode(supplierSpu.getCode());
        } else {
            //如果存在供应商商品，则对供应商商品进行修改
            supplierSpu.setId(supplierSpuVo.getId());
            supplierSpu.setCode(supplierSpuVo.getCode());
            //判断是否更新最后清除时间
            if (supplierSpuVo.getLastClearTime() != null) {
                List<SpuCreateControlVO> spuCreateControlVOList = spuCreateControlMapper.queryList(add.getSpuId());
                if (spuCreateControlVOList != null && spuCreateControlVOList.size() > 0) {
                    SpuCreateControlVO createControlVO = spuCreateControlVOList.get(0);
                    long interval = ChronoUnit.DAYS.between(supplierSpuVo.getLastClearTime(), saleDate);
                    if (createControlVO.getAutoCle() != 0 && createControlVO.getAutoCle() <= interval) {
                        supplierSpu.setLastClearTime(saleDate);
                    }
                    long qualityReportInterval = ChronoUnit.DAYS.between(supplierSpuVo.getQualityReportLastClearTime(), saleDate);
                    if (createControlVO.getQualityReportCle() != 0 && createControlVO.getQualityReportCle() <= qualityReportInterval) {
                        supplierSpu.setQualityReportLastClearTime(saleDate);
                    }
                }
            }
            supplierSpuMapper.updateById(supplierSpu);
            supplierSku.setSupplierSpuId(supplierSpuVo.getId());
            supplierSku.setSupplierSpuCode(supplierSpuVo.getCode());
        }

        supplierSku.setCode(createSupplierSkuCode(spu.getId(), supplierVo.getId(), supplierSpu.getCode(), supplierSku.getSaleDate()));
        // 扫描识别码 - 与历史批次保持一致
        if (StringUtils.isBlank(supplierSku.getSkuLabel())) {
            supplierSku.setSkuLabel(TemCode.getTemCode(supplierSku.getCode()));
            if (lastOne != null && StringUtils.isNotBlank(lastOne.getSkuLabel())) {
                supplierSku.setSkuLabel(lastOne.getSkuLabel());
            }
        }

        supplierSku.setFullName(supplierSku.getSpuName());
        //新增供应商销售批次商品
        supplierSkuMapper.insert(supplierSku);
        // 同步sku
        skuService.insertBatchSyncSku(supplierSku.getId());

        //新增库存
        SupplierSkuStock skuStock = new SupplierSkuStock();
        skuStock.setSupplierSkuId(supplierSku.getId());
        skuStock.setStock(phGoodsProperties.getStock());
        skuStock.setUpStock(phGoodsProperties.getStock());
        supplierSkuStockMapper.insert(skuStock);

        //添加操作日志
        SupplierSkuApproveLog log = new SupplierSkuApproveLog();
        log.setSupplierSkuId(supplierSku.getId());
        log.setOperate("admin 自动上架配货商品-" + SupplierSkuBatchTypeEnum.BATCH_TYPE3.getDesc());
        log.setStatus(add.getStatus());
        supplierSkuApproveLogMapper.insert(log);

        //新增供应商批次商品的文件
        String image = phGoodsProperties.getImage();
        if (StringUtils.isNotBlank(image) && image.startsWith(cosProperties.getHost())) {
            image = image.replace(cosProperties.getHost(), cosProperties.getGatewayHost());
        }
        AddSupplierSkuFileBo addSupplierSkuFileBo = new AddSupplierSkuFileBo();
        addSupplierSkuFileBo.setType(1);
        addSupplierSkuFileBo.setFileUrl(image);
        supplierSkuFileMapper.insertByBoList(supplierSpu.getId(), supplierSku.getId(), CollUtil.newArrayList(addSupplierSkuFileBo));

        //上架操作的时候，处理所属二级目录数据
        if (SupplierSkuStatusEnum.STATUS4.getCode().equals(supplierSku.getStatus())) {
            handleUp(supplierSku.getId(), supplierSku.getCategoryId(), supplierSku.getRegionWhId());
        }
        return supplierSku.getId();
    }

    /**
     * 尾货上架
     *
     * @param bo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @Lock4j(name = ProductRedisNames.SUPPLIER_SKU_ADD_TAIL, keys = "#bo.supplierSkuId", expire = 30000, acquireTimeout = 1000)
    public Boolean addTail(AddTailSupplierSkuBo bo) {
        LoginUser user = LoginHelper.getLoginUser();
        if (user == null) {
            throw new ServiceException("用户不存在");
        }
        //根据ID获取供应商商品销售批次
        SupplierSkuVo skuVo = supplierSkuMapper.selectVoById(bo.getSupplierSkuId());
        if (skuVo == null || skuVo.getDelFlag() != 0) {
            throw new ServiceException("供应商商品销售批次不存在");
        }
        if (skuVo.getStatus() < SupplierSkuStatusEnum.STATUS4.getCode()) {
            throw new ServiceException("当前状态不能生成尾货");
        }
        //处理供应商信息
        RemoteSupplierVo supplierVo = remoteSupplierService.getSupplierById(skuVo.getSupplierId());
        if (supplierVo == null) {
            throw new ServiceException("供应商不存在");
        }
        //校验子账户是否禁止入金（调用平安接口检查）
        checkOrgControlStatus(supplierVo.getCode());
        // 检查平台商品 - 等级，规格，进出口，商品产地是否存在
        SpuVO spu = spuService.getById(skuVo.getSpuId());
        if (spu == null) {
            throw new ServiceException("平台商品不存在");
        }
        // 校验商品是否被禁用
        checkSpuIsDisable(spu.getId());
        KeyValueBO.checkSpuExt(skuVo, spu.getExpList());

        // 尾货唯一性
        CheckSupplierSkuBo checkSupplierSkuBo = new CheckSupplierSkuBo();
        checkSupplierSkuBo.setSkuId(skuVo.getSkuId());
        checkSupplierSkuBo.setBatchType(2);
        checkSupplierSkuBo.setSaleDate(skuVo.getSaleDate());
        SupplierSkuVo lastOne = supplierSkuMapper.checkSupplierSku(checkSupplierSkuBo);
        if (lastOne != null) {
            throw new ServiceException("尾货重复添加");
        }

        //下架操作
        SupplierSku updateSku = new SupplierSku();
        updateSku.setId(skuVo.getId());
        updateSku.setStatus(SupplierSkuStatusEnum.STATUS5.getCode());
        updateSku.setDownTime(now());
        updateSku.setOriginalSupplierSkuId(0L);
        updateSku.setSubsidyAmount(BigDecimal.ZERO);
        supplierSkuMapper.updateById(updateSku);
        // 批次修改同步sku
        skuService.updateBatchSyncSku(skuVo.getId());
        //添加供应商商品尾货批次
        SupplierSku addSku = MapstructUtils.convert(skuVo, SupplierSku.class);
        addSku.setId(null);
        addSku.setOriginalSupplierSkuId(updateSku.getOriginalSupplierSkuId());
        addSku.setStatus(SupplierSkuStatusEnum.STATUS4.getCode());
        if (bo.getBuyMin() != null) {
            addSku.setBuyMin(bo.getBuyMin());
        } else {
            //每次上架，自动清空起订件数
            addSku.setBuyMin(-1);
        }
//        addSku.setBusinessType(SupplierSkuBusinessTypeEnum.BUSINESS_TYPE1.getCode());
        addSku.setBatchType(SupplierSkuBatchTypeEnum.BATCH_TYPE2.getCode());
        addSku.setUpTime(now());
        addSku.setDownTime(null);
        addSku.setCode(createSupplierSkuCode(addSku.getSpuId(), addSku.getSupplierId(), addSku.getSupplierSpuCode(), addSku.getSaleDate()));
        addSku.setIsOut(ObjectUtil.defaultIfNull(bo.getStock(), 0) > 0 ? 0 : 1);
        //拼接全称
        String spuAnotherName = getSpuAnotherName(spu.getCategoryId());
        String[] cateGoryPathNameArray = spu.getPathName().split("/");
        String fullName = (addSku.getBrand() != null ? addSku.getBrand() : "") +
                (addSku.getSpuName() != null ? addSku.getSpuName() : "") +
                (addSku.getShortProducer() != null ? addSku.getShortProducer() : "") +
                (addSku.getSpuGrade() != null ? addSku.getSpuGrade() : "") +
                (addSku.getSpuStandards() != null ? addSku.getSpuStandards() : "") +
                (addSku.getCooperation() != null && addSku.getCooperation() == 1 ? "战略品" : "") +
                (SupplierSkuBusinessTypeEnum.BUSINESS_TYPE20.getCode().equals(addSku.getBusinessType())
                        ? SupplierSkuBusinessTypeEnum.BUSINESS_TYPE20.getDesc() : "") +
                cateGoryPathNameArray[2] + cateGoryPathNameArray[3] + spuAnotherName;
        addSku.setFullName(fullName);
        supplierSkuMapper.insert(addSku);
        // 批次新增同步sku
        skuService.insertBatchSyncSku(addSku.getId());

        //添加操作日志
        SupplierSkuApproveLog log = new SupplierSkuApproveLog();
        log.setSupplierSkuId(addSku.getId());
        log.setOperate(SupplierSkuApproveLogOperateEnum.BUYER.getDesc() + user.getRealName() + "," + SupplierSkuBatchTypeEnum.BATCH_TYPE2.getDesc());
        log.setStatus(SupplierSkuStatusEnum.STATUS4.getCode());
        supplierSkuApproveLogMapper.insert(log);

        //根据旧批次id给新批次复制创建库存，禁用城市仓，文件
        addSupplierSkuOther(addSku, bo.getSupplierSkuId(), bo.getStock());

        handleUp(addSku.getId(), addSku.getCategoryId(), addSku.getRegionWhId());
        handleDown(skuVo.getId(), skuVo.getCategoryId(), skuVo.getRegionWhId());
        return true;
    }

    /**
     * 根据旧批次id给新批次复制创建库存，禁用城市仓，文件
     *
     * @param newSku
     * @param oldId
     * @param stock
     * @return
     */
    private String addSupplierSkuOther(SupplierSku newSku, Long oldId, Integer stock) {
        //新增库存
        SupplierSkuStock skuStock = new SupplierSkuStock();
        skuStock.setSupplierSkuId(newSku.getId());
        if (stock == null) {
            SupplierSkuStockVo oldStock = supplierSkuStockMapper.selectBySupplierSkuId(oldId);
            skuStock.setStock(oldStock.getUpStock());
        } else {
            skuStock.setStock(stock);
        }
        skuStock.setUpStock(skuStock.getStock());
        supplierSkuStockMapper.insert(skuStock);

        //添加供应商商品批次的禁下单城市仓
        List<SupplierSkuDisableCityWhVo> cityWhVoList = supplierSkuDisableCityWhMapper.queryList(newSku.getSkuId(), List.of(1));
        if (cityWhVoList != null && cityWhVoList.size() > 0) {
            List<SupplierSkuDisableCityWh> cityWhList = new ArrayList<>();
            for (SupplierSkuDisableCityWhVo CityWhVo : cityWhVoList) {
                SupplierSkuDisableCityWh cityWh = new SupplierSkuDisableCityWh();
                cityWh.setSupplierSkuId(newSku.getId());
                cityWh.setCityWhId(CityWhVo.getCityWhId());
                cityWh.setCityWhCode(CityWhVo.getCityWhCode());
                cityWhList.add(cityWh);
            }
            supplierSkuDisableCityWhMapper.insertBatch(cityWhList);
        }
        //添加供应商商品批次的文件
        QuerySupplierSkuFileBo fileBo = new QuerySupplierSkuFileBo();
        fileBo.setSupplierSkuIdList(Lists.newArrayList(oldId));
        List<SupplierSkuFileVo> fileVoList = supplierSkuFileMapper.queryList(fileBo);
        //检验图片文件简介是否重新上传
        checkControl(newSku, fileVoList);
        String imgUrl = "";
        if (fileVoList != null && fileVoList.size() > 0) {
            List<SupplierSkuFile> fileList = new ArrayList<>();
            for (SupplierSkuFileVo fileVo : fileVoList) {
                SupplierSkuFile file = new SupplierSkuFile();
                file.setSupplierSpuId(newSku.getSupplierSpuId());
                file.setSupplierSkuId(newSku.getId());
                file.setType(fileVo.getType());
                file.setFileUrl(fileVo.getFileUrl());
                fileList.add(file);
                if (imgUrl.length() == 0 && SupplierSkuFileTypeEnum.TYPE1.getCode().equals(fileVo.getType())) {
                    imgUrl = fileVo.getFileUrl();
                }
            }
            supplierSkuFileMapper.insertBatch(fileList);
        }
        return imgUrl;
    }

    /**
     * 检验文件
     *
     * @param sku
     * @param fileVoList
     */
    private void checkControl(SupplierSku sku, List<SupplierSkuFileVo> fileVoList) {
        List<SpuCreateControlVO> spuCreateControlVOList = spuCreateControlMapper.queryList(sku.getSpuId());
        if (spuCreateControlVOList != null && spuCreateControlVOList.size() > 0) {
            Map<Integer, List<SupplierSkuFileVo>> fileMap = new HashMap<>();
            if (fileVoList != null && fileVoList.size() > 0) {
                fileMap = fileVoList.stream().collect(Collectors.groupingBy(SupplierSkuFileVo::getType));
            }
            //获取实例
            SpuCreateControlVO createControlVO = spuCreateControlVOList.get(0);
            if (createControlVO.getSkuIntroduction() == 1) {
                if (StringUtils.isBlank(sku.getSnapshot())) {
                    throw new ServiceException("商品简介必须填写");
                }
            }
            if (createControlVO.getSkuDetailImg() == 1) {
                if (CollectionUtil.isEmpty(fileMap.get(SupplierSkuFileTypeEnum.TYPE1.getCode()))) {
                    throw new ServiceException("商品图片必须上传");
                }
            } else if (createControlVO.getSkuVideo() == 1) {
                if (CollectionUtil.isEmpty(fileMap.get(SupplierSkuFileTypeEnum.TYPE2.getCode()))) {
                    throw new ServiceException("商品视频必须上传");
                }
            } else if (createControlVO.getSkuPackImg() == 1) {
                if (CollectionUtil.isEmpty(fileMap.get(SupplierSkuFileTypeEnum.TYPE3.getCode()))
                        && CollectionUtil.isEmpty(fileMap.get(SupplierSkuFileTypeEnum.TYPE31.getCode()))
                        && CollectionUtil.isEmpty(fileMap.get(SupplierSkuFileTypeEnum.TYPE32.getCode()))
                        && CollectionUtil.isEmpty(fileMap.get(SupplierSkuFileTypeEnum.TYPE33.getCode()))) {
                    throw new ServiceException("包装图片必须上传");
                }
            } else if (createControlVO.getSkuPackVideo() == 1) {
                if (CollectionUtil.isEmpty(fileMap.get(SupplierSkuFileTypeEnum.TYPE4.getCode()))) {
                    throw new ServiceException("包装视频必须上传");
                }
            }
            //现在质检报告要根据总仓配置来判断
            RemoteRegionWhVo whVo = remoteRegionWhService.queryById(sku.getRegionWhId());
            if (whVo == null) {
                throw new ServiceException("总仓不存在");
            }
            if (whVo.getStatus() == 0) {
                throw new ServiceException("总仓已被禁用");
            }
            //判断质检报告
            if (whVo.getQualityReport() > 0 && createControlVO.getQualityReport() > 0) {
                if (CollectionUtil.isEmpty(fileMap.get(SupplierSkuFileTypeEnum.TYPE5.getCode()))) {
                    throw new ServiceException("质检报告必须上传");
                }
            }
//            SupplierSpuVo supplierSpuVo = supplierSpuMapper.selectVoById(sku.getSupplierSpuId());
//            if(supplierSpuVo == null) {
//                throw new ServiceException("供应商商品不存在，请重新供货");
//            }
//            if(supplierSpuVo.getLastClearTime() != null) {
//                //计算当前时间和上次清除时间直接的天数差
//                long interval = ChronoUnit.DAYS.between(supplierSpuVo.getLastClearTime(), getSaleDate(sku.getRegionWhId()));
//                //如果超过自动清除时间，且文件设置了自动清理，就不回填
//                if (createControlVO.getAutoCle() > 0 && createControlVO.getAutoCle() <= interval) {
//                    //判断是否回填商品简介
//                    if (createControlVO.getSkuIntroduction() == 1) {
//                        if (createControlVO.getSkuIntroductionCle() == 1 || StringUtils.isBlank(sku.getSnapshot())) {
//                            throw new ServiceException("商品简介已清除，需要重新填写");
//                        }
//                    }
//                    if (createControlVO.getSkuDetailImg() == 1) {
//                        if (createControlVO.getSkuDetailImgCle() == 1 || CollectionUtil.isEmpty(fileMap.get(SupplierSkuFileTypeEnum.TYPE1.getCode()))) {
//                            throw new ServiceException("商品图片已清除，需要重新上传");
//                        }
//                    } else if (createControlVO.getSkuVideo() == 1) {
//                        if (createControlVO.getSkuVideoCle() == 1 || CollectionUtil.isEmpty(fileMap.get(SupplierSkuFileTypeEnum.TYPE2.getCode()))) {
//                            throw new ServiceException("商品视频已清除，需要重新上传");
//                        }
//                    } else if (createControlVO.getSkuPackImg() == 1) {
//                        if (createControlVO.getSkuPackImgCle() == 1 || (CollectionUtil.isEmpty(fileMap.get(SupplierSkuFileTypeEnum.TYPE3.getCode()))
//                                && CollectionUtil.isEmpty(fileMap.get(SupplierSkuFileTypeEnum.TYPE31.getCode()))
//                                && CollectionUtil.isEmpty(fileMap.get(SupplierSkuFileTypeEnum.TYPE32.getCode()))
//                                && CollectionUtil.isEmpty(fileMap.get(SupplierSkuFileTypeEnum.TYPE33.getCode())))) {
//                            throw new ServiceException("包装图片已清除，需要重新上传");
//                        }
//                    } else if (createControlVO.getSkuPackVideo() == 1) {
//                        if (createControlVO.getSkuPackVideoCle() == 1 || CollectionUtil.isEmpty(fileMap.get(SupplierSkuFileTypeEnum.TYPE4.getCode()))) {
//                            throw new ServiceException("包装视频已清除，需要重新上传");
//                        }
//                    }
//                    else if (createControlVO.getQualityReport() == 1) {
//                        if (createControlVO.getQualityReportCle() == 1 || CollectionUtil.isEmpty(fileMap.get(SupplierSkuFileTypeEnum.TYPE5.getCode()))) {
//                            throw new ServiceException("质检报告已清除，需要重新上传");
//                        }
//                    }
//                }
//            }
//            if(supplierSpuVo.getQualityReportLastClearTime() != null) {
//                long qualityReportInterval = ChronoUnit.DAYS.between(supplierSpuVo.getQualityReportLastClearTime(), LocalDate.now());
//                //现在质检报告要根据总仓配置来判断
//                RemoteRegionWhVo whVo = remoteRegionWhService.queryById(sku.getRegionWhId());
//                if(whVo == null) {
//                    throw new ServiceException("总仓不存在");
//                }
//                if(whVo.getStatus() == 0) {
//                    throw new ServiceException("总仓已被禁用");
//                }
//                //判断质检报告
//                if(whVo.getQualityReport() > 0 && createControlVO.getQualityReport() > 0) {
//                    if(createControlVO.getQualityReportCle() > 0) {
//                        if(createControlVO.getQualityReportCle() <= qualityReportInterval || CollectionUtil.isEmpty(fileMap.get(SupplierSkuFileTypeEnum.TYPE5.getCode()))) {
//                            throw new ServiceException("质检报告已清除，需要重新上传");
//                        }
//                    }
//                }
//            }
        }
    }

    /**
     * 供应商我要供货查询批次状态接口
     *
     * @param bo
     * @return
     */
    @Override
    public List<SupplierSkuVo> queryLastList(QueryLastListBo bo) {
        List<SupplierSkuVo> skuVoList = supplierSkuMapper.queryLastList(bo);
        if (skuVoList != null && skuVoList.size() > 0) {
            List<String> regionWhCodeList = skuVoList.stream().map(SupplierSkuVo::getRegionWhCode).toList();
            List<RemoteRegionWhVo> regionWhVoList = remoteRegionWhService.queryListByCodes(regionWhCodeList);
            if (regionWhVoList == null || regionWhVoList.size() == 0) {
                throw new ServiceException("总仓异常");
            }
            Map<String, RemoteRegionWhVo> map = regionWhVoList.stream().collect(Collectors.toMap(RemoteRegionWhVo::getRegionWhCode, Function.identity()));
            for (SupplierSkuVo skuVo : skuVoList) {
                RemoteRegionWhVo regionWhVo = map.get(skuVo.getRegionWhCode());
                if (regionWhVo == null) {
                    throw new ServiceException("总仓异常");
                }
                LocalDate localDate = SaleDateUtil.getSaleDate(regionWhVo.getSalesTimeEnd());
                if (!skuVo.getSaleDate().isEqual(localDate)) {
                    skuVo.setStatus(SupplierSkuStatusEnum.STATUS0.getCode());
                }
            }
        }
        return skuVoList;
    }

    /**
     * 查询供应商供货申请数量
     *
     * @return
     */
    @Override
    public Long getCount(GetCountBo bo) {
        LambdaQueryWrapper<SupplierSku> lqw = bo.buildQueryWrapper();
        lqw.ge(SupplierSku::getSaleDate, getSaleDate(bo.getRegionWhId()));
        return supplierSkuMapper.selectCount(lqw);
    }

    /**
     * 根据条件查询销售批次详情
     *
     * @param bo
     * @return
     */
    @Override
    public List<RemoteSupplierSkuInfoVo> queryInfoList(RemoteQueryInfoListBo bo) {
        List<RemoteSupplierSkuInfoVo> remoteSupplierSkuInfoVos = supplierSkuMapper.queryInfoList(bo);

        if (ObjectUtil.isEmpty(remoteSupplierSkuInfoVos)) {
            return Collections.emptyList();
        }

        List<Long> spuIdList = remoteSupplierSkuInfoVos.stream().map(RemoteSupplierSkuInfoVo::getSupplierSpuId).toList();
        List<SupplierSpu> spuList = supplierSpuMapper.selectBatchSupplierSpuInfoByIds(spuIdList);
        if (ObjectUtil.isEmpty(spuList)) {
            throw new ServiceException("供应商商品异常");
        }
        Map<Long, SupplierSpu> spuMap = spuList.stream().collect(Collectors.toMap(SupplierSpu::getId, Function.identity(), (v1, v2) -> v1));
        //设置图片
        List<Long> supplierSkuIdList = remoteSupplierSkuInfoVos.stream().map(RemoteSupplierSkuInfoVo::getId).collect(Collectors.toList());
        QuerySupplierSkuFileBo fileBo = new QuerySupplierSkuFileBo();
        fileBo.setSupplierSkuIdList(supplierSkuIdList);
        fileBo.setTypeList(List.of(SupplierSkuFileTypeEnum.TYPE1.getCode(), SupplierSkuFileTypeEnum.TYPE5.getCode()));
        List<SupplierSkuFileVo> fileVoList = supplierSkuFileMapper.queryList(fileBo);
        //处理数据
        Map<Long, List<SupplierSkuFileVo>> fileMap = fileVoList.stream().collect(Collectors.groupingBy(SupplierSkuFileVo::getSupplierSkuId));
        //处理出参
        List<String> areaCodes = remoteSupplierSkuInfoVos.stream().map(RemoteSupplierSkuInfoVo::getAreaCode).filter(StringUtils::isNotBlank).distinct().toList();
        Map<String, String> areaMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(areaCodes)) {
            List<RemotePubAreaVO> remotePubAreaVOs = remoteBasPubAreaService.getByCodes(areaCodes);
            areaMap = remotePubAreaVOs.stream().collect(Collectors.toMap(RemotePubAreaVO::getAreaCode, RemotePubAreaVO::getAreaAlias));
        }

        // 供货总仓名称
        Map<Long, String> regionWhVoMap = new HashMap<>();
        List<Long> regionWhIds = remoteSupplierSkuInfoVos.stream().map(RemoteSupplierSkuInfoVo::getProvideRegionWhId).filter(ObjectUtil::isNotNull).filter(e -> !Objects.equals(e, 0L)).distinct().toList();
        if (CollectionUtil.isNotEmpty(regionWhIds)) {
            List<RemoteRegionWhVo> regionWhVos = remoteRegionWhService.selectRegionWhInfoByIds(regionWhIds);
            regionWhVoMap = regionWhVos.stream().collect(Collectors.toMap(RemoteRegionWhVo::getId, RemoteRegionWhVo::getRegionWhName, (n1, n2) -> n2));
        }

        List<Long> categoryIdList = remoteSupplierSkuInfoVos.stream().map(e -> e.getCategoryId()).filter(e -> e != null && e > 0L).distinct().collect(Collectors.toList());
        List<CategoryVO> categoryList = categoryMapper.getBySonIds(categoryIdList);
        Map<Long, CategoryVO> categoryMap = categoryList.stream().collect(Collectors.toMap(CategoryVO::getSonId, Function.identity(), (v1, v2) -> v1));
// 前端隐藏商户编号&店铺
        List<Long> hideMerchantRegionWhIdList = noCheckGoodsProperties.getHideMerchantRegionWhIdList();

        for (RemoteSupplierSkuInfoVo skuVo : remoteSupplierSkuInfoVos) {
            if (hideMerchantRegionWhIdList.contains(skuVo.getRegionWhId())) {
                skuVo.setHideMerchant(1);
            }
            List<SupplierSkuFileVo> fileList1 = fileMap.get(skuVo.getId());
            if (!CollectionUtils.isEmpty(fileList1)) {
                Map<Integer, List<SupplierSkuFileVo>> fileList2 = fileList1.stream().collect(Collectors.groupingBy(SupplierSkuFileVo::getType));
                List<SupplierSkuFileVo> imgFileList = fileList2.get(SupplierSkuFileTypeEnum.TYPE1.getCode());
                if (!CollectionUtils.isEmpty(imgFileList)) {
                    skuVo.setImgUrl(imgFileList.get(0).getFileUrl());
                }
                List<SupplierSkuFileVo> qualityReportFileList = fileList2.get(SupplierSkuFileTypeEnum.TYPE5.getCode());
                if (!CollectionUtils.isEmpty(qualityReportFileList)) {
                    List<String> qualityReportUrlList = new ArrayList<>();
                    for (SupplierSkuFileVo fileVo : qualityReportFileList) {
                        qualityReportUrlList.add(fileVo.getFileUrl());
                    }
                    skuVo.setQualityReportUrlList(qualityReportUrlList);
                }
            }
            SupplierSpu spu = spuMap.get(skuVo.getSupplierSpuId());
            if (spu == null) {
                throw new ServiceException("'" + skuVo.getSpuName() + "'这个供应商商品异常");
            }
            skuVo.setFreightRatio(spu.getFreightRatio());
            // 规格转换
            skuVo.buildSpuStandardsName();
            // 区域简称
            if (ObjectUtil.isNotEmpty(skuVo.getAreaCode()) && areaMap.containsKey(skuVo.getAreaCode())) {
                skuVo.setShortProducer(areaMap.get(skuVo.getAreaCode()));
            }
            String rwName = regionWhVoMap.get(skuVo.getProvideRegionWhId());
            if (StringUtils.isNotBlank(rwName)) {
                skuVo.setProvideRegionWhName(rwName);
            }
            if (skuVo.getCategoryId() != null && skuVo.getCategoryId() > 0L) {
                CategoryVO categoryVO = categoryMap.get(skuVo.getCategoryId());
                if (categoryVO != null) {
                    skuVo.setCategoryIdLevel1(categoryVO.getParentId());
                    skuVo.setCategoryIdLevel2(categoryVO.getId());
                    skuVo.setCategoryCode(categoryVO.getCode());
                    skuVo.setCategoryPathName(categoryVO.getPathName());
                }
            }
            if (StringUtils.isBlank(skuVo.getSkuLabel())) {
                skuVo.setSkuLabel(TemCode.getTemCode(skuVo.getCode()));
            }
        }
        return remoteSupplierSkuInfoVos;
    }

    /**
     * 根据条件查询销售批次id
     *
     * @param bo
     * @return
     */
    @Override
    public List<Long> querySkuIdList(RemoteQuerySkuIdBo bo) {
        if (ObjectUtil.isNull(bo.getRegionWhId())) {
            throw new ServiceException("总仓id必传");
        }
        if (ObjectUtil.isNull(bo.getSaleDate())) {
            throw new ServiceException("销售日期必传");
        }
        LambdaQueryWrapper<SupplierSku> lqw = Wrappers.lambdaQuery();
        lqw.eq(SupplierSku::getRegionWhId, bo.getRegionWhId());
        lqw.eq(SupplierSku::getSaleDate, bo.getSaleDate());
        lqw.eq(StrUtil.isNotBlank(bo.getBuyerCode()), SupplierSku::getBuyerCode, bo.getBuyerCode());
        lqw.eq(ObjectUtil.isNotNull(bo.getBasPortageTeamId()), SupplierSku::getBasPortageTeamId, bo.getBasPortageTeamId());
        return supplierSkuMapper.selectList(lqw).stream().map(SupplierSku::getId).collect(Collectors.toList());
    }

    @Override
    public List<RemoteSupplierSkuFundsInfoVo> queryInfoListByFunds(List<Long> supplierSkuIds) {
        if (CollUtil.isEmpty(supplierSkuIds)) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<SupplierSku> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(SupplierSku::getId, supplierSkuIds);
        wrapper.select(SupplierSku::getId, SupplierSku::getSpuName, SupplierSku::getCode, SupplierSku::getSpuCode, SupplierSku::getSpuNetWeight, SupplierSku::getSpuGrossWeight, SupplierSku::getPrice, SupplierSku::getSaleDate,
                SupplierSku::getSupplierDeptId, SupplierSku::getSupplierId, SupplierSku::getPackageWord,
                SupplierSku::getSpuGrade, SupplierSku::getSpuStandards, SupplierSku::getProducer, SupplierSku::getAfterSaleType, SupplierSku::getBusinessType
                , SupplierSku::getAreaCode, SupplierSku::getBrand, SupplierSku::getSkuId
        );
        List<SupplierSku> supplierSkuList = supplierSkuMapper.selectList(wrapper);

        QuerySupplierSkuFileBo fileBo = new QuerySupplierSkuFileBo();
        fileBo.setSupplierSkuIdList(supplierSkuIds);
        fileBo.setTypeList(List.of(SupplierSkuFileTypeEnum.TYPE1.getCode()));
        List<SupplierSkuFileVo> fileVoList = supplierSkuFileMapper.queryList(fileBo);
        Map<Long, SupplierSkuFileVo> fileMap = fileVoList.stream().collect(Collectors.toMap(SupplierSkuFileVo::getSupplierSkuId, Function.identity(), (v1, v2) -> v1));
        List<RemoteSupplierSkuFundsInfoVo> result = new ArrayList<>();
        //处理出参
        List<String> areaCodes = supplierSkuList.stream().map(SupplierSku::getAreaCode).filter(StringUtils::isNotBlank).distinct().toList();
        Map<String, String> areaMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(areaCodes)) {
            List<RemotePubAreaVO> remotePubAreaVOs = remoteBasPubAreaService.getByCodes(areaCodes);
            areaMap = remotePubAreaVOs.stream().collect(Collectors.toMap(RemotePubAreaVO::getAreaCode, RemotePubAreaVO::getAreaAlias));
        }
        for (SupplierSku supplierSku : supplierSkuList) {
            RemoteSupplierSkuFundsInfoVo vo = new RemoteSupplierSkuFundsInfoVo();
            BeanUtil.copyProperties(supplierSku, vo);
            vo.setRealSkuId(supplierSku.getSkuId());
            vo.setSkuId(supplierSku.getId());
            vo.setSkuCode(supplierSku.getCode());
            vo.setSkuName(supplierSku.getSpuName());
//            vo.setSpuCode(supplierSku.getSpuCode());
//            vo.setSaleDate(supplierSku.getSaleDate());
            SupplierSkuFileVo file = fileMap.get(supplierSku.getId());
            if (file != null) {
                vo.setImgUrl(file.getFileUrl());
            }
//            vo.setPrice(supplierSku.getPrice());
//            vo.setAfterSaleType(supplierSku.getAfterSaleType());
//            vo.setSpuGrossWeight(supplierSku.getSpuGrossWeight());
//            vo.setSpuNetWeight(supplierSku.getSpuNetWeight());
//            vo.setSpuGrade(supplierSku.getSpuGrade());
//            vo.setSpuStandards(supplierSku.getSpuStandards());
//            vo.setProducer(supplierSku.getProducer());
//            vo.setSupplierId(supplierSku.getSupplierId());
//            vo.setSupplierDeptId(supplierSku.getSupplierDeptId());
//            vo.setBusinessType(supplierSku.getBusinessType());
//            vo.setPackageWord(supplierSku.getPackageWord());
            // 规格转换
            vo.buildSpuStandardsName();
            // 产地
//            vo.setAreaCode(supplierSku.getAreaCode());
//            vo.setBrand(supplierSku.getBrand());
            // 区域简称
            if (StringUtils.isNotBlank(vo.getAreaCode()) && areaMap.containsKey(vo.getAreaCode())) {
                vo.setShortProducer(areaMap.get(vo.getAreaCode()));
            }
            result.add(vo);
        }
        return result;
    }

    /**
     * 根据code查询详情
     */
    @Override
    public RemoteSupplierSkuInfoVo getByCode(String code) {
        RemoteSupplierSkuInfoVo byCode = supplierSkuMapper.getByCode(code);
        // 规格转换
        if (ObjectUtil.isNotEmpty(byCode)) {
            byCode.buildSpuStandardsName();
        }
        return byCode;
    }

    @Override
    public List<RemoteSupplierSkuInfoVo> getByIds(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return new ArrayList<>();
        }
        List<SupplierSku> list = supplierSkuMapper.selectBatchIds(ids);
        List<RemoteSupplierSkuInfoVo> result = BeanUtil.copyToList(list, RemoteSupplierSkuInfoVo.class);
        for (RemoteSupplierSkuInfoVo skuInfoVo : result) {
            skuInfoVo.buildSpuStandardsName();
        }
        return result;
    }

    @Override
    public RemoteSupplierSkuInfoVo getByLabel(RemoteQuerySkuIdBo bo) {
        if (StrUtil.isBlank(bo.getSkuLabel())) {
            throw new ServiceException("商品码不能为空");
        }

        // 只获取正常批次，暂不考虑尾货
        bo.setBatchType(SupplierSkuBatchTypeEnum.BATCH_TYPE1.getCode());
        RemoteSupplierSkuInfoVo byCode = supplierSkuMapper.getBySkuLabel(bo);

        // 规格转换
        if (ObjectUtil.isNotEmpty(byCode)) {
            byCode.buildSpuStandardsName();
        }

        // 兼容无label的老数据 + 批次码识别功能
        if (ObjectUtil.isEmpty(byCode)) {
            byCode = this.getByCode(TemCode.getOriginalCode(bo.getSkuLabel()));
        }
        return byCode;
    }

    @Override
    public List<RemoteSupplierSkuInfoVo> getByLabelList(RemoteQuerySkuIdBo bo) {
        if (StrUtil.isBlank(bo.getSkuLabel())) {
            throw new ServiceException("商品码不能为空");
        }

        List<RemoteSupplierSkuInfoVo> byCodeList = supplierSkuMapper.getBySkuLabelList(bo);


        if (CollectionUtil.isEmpty(byCodeList)) {
            // 扫唯一码只能获取到一条数据
            RemoteSupplierSkuInfoVo byCode = this.getByCode(TemCode.getOriginalCode(bo.getSkuLabel()));
            if (byCode != null) {
                byCodeList = CollUtil.newArrayList(byCode);
            }
        } else {
            byCodeList.forEach(RemoteSupplierSkuInfoVo::buildSpuStandardsName);
        }

        return byCodeList;
    }

    /**
     * 城市仓多货，生成地采批次
     *
     * @param bo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addLand(RemoteAddLandBo bo) {
        SupplierSku addSku = supplierSkuMapper.selectById(bo.getSupplierSkuId());
        if (addSku == null) {
            throw new ServiceException("供应商商品销售批次不存在");
        }
        // 检查平台商品 - 等级，规格，进出口，商品产地是否存在
        SpuVO spu = spuService.getById(addSku.getSpuId());
        if (spu == null) {
            throw new ServiceException("平台商品不存在");
        }
        KeyValueBO.checkSpuExt(addSku, spu.getExpList());
        log.keyword("addLand", "生成地采").info("生成地采，bo：{}，addSku.id：{} ", bo, addSku.getRegionWhId());
        RemoteRegionWhVo whVo = remoteRegionWhService.queryById(addSku.getRegionWhId());
        log.keyword("addLand", "生成地采总仓数据").info("生成地采总仓数据，addSku：{}", addSku);
        if (ObjectUtil.isEmpty(whVo)) {
            throw new ServiceException("总仓不存在");
        }
        if (whVo.getStatus() == 0) {
            throw new ServiceException("总仓已被禁用");
        }
        // 坑位校验
        checkMaxSaleNum(addSku.getSupplierId(), addSku.getRegionWhId(), addSku.getSaleType());
        convertSupplierSku(addSku);
        addSku.setId(null);
        addSku.setStatus(SupplierSkuStatusEnum.STATUS5.getCode());
        addSku.setBatchType(SupplierSkuBatchTypeEnum.BATCH_TYPE3.getCode());
        addSku.setBusinessType(SupplierSkuBusinessTypeEnum.BUSINESS_TYPE10.getCode());
        addSku.setPrice(bo.getDiscountPrice());
        addSku.setSaleDate(SaleDateUtil.getSaleDate(whVo.getSalesTimeEnd()));
        addSku.setUpTime(now());
        addSku.setDownTime(null);
        addSku.setCode(createSupplierSkuCode(addSku.getSpuId(), addSku.getSupplierId(), addSku.getSupplierSpuCode(), addSku.getSaleDate()));
        //拼接全称
        String spuAnotherName = getSpuAnotherName(addSku.getCategoryId());
        String[] cateGoryPathNameArray = spu.getPathName().split("/");
        String fullName = (addSku.getBrand() != null ? addSku.getBrand() : "") +
                (addSku.getSpuName() != null ? addSku.getSpuName() : "") +
                (addSku.getShortProducer() != null ? addSku.getShortProducer() : "") +
                (addSku.getSpuGrade() != null ? addSku.getSpuGrade() : "") +
                (addSku.getSpuStandards() != null ? addSku.getSpuStandards() : "") +
                (addSku.getCooperation() != null && addSku.getCooperation() == 1 ? "战略品" : "") +
                (SupplierSkuBusinessTypeEnum.BUSINESS_TYPE20.getCode().equals(addSku.getBusinessType())
                        ? SupplierSkuBusinessTypeEnum.BUSINESS_TYPE20.getDesc() : "") +
                spuAnotherName;
        addSku.setFullName(fullName);
        supplierSkuMapper.insert(addSku);
        // 批次新增同步sku
        skuService.insertBatchSyncSku(addSku.getId());

        //根据旧批次id给新批次复制创建库存，禁用城市仓，文件
        addSupplierSkuOther(addSku, bo.getSupplierSkuId(), bo.getStock());

        return addSku.getId();
    }

    private void convertSupplierSku(SupplierSku addSku) {
        addSku.setCreateCode(null);
        addSku.setCreateName(null);
        addSku.setCreateTime(null);
        addSku.setUpdateCode(null);
        addSku.setUpdateName(null);
        addSku.setUpdateTime(null);
    }

    /**
     * 换供应商
     *
     * @param bo
     * @return
     */
    @Override
    @GlobalTransactional
    @Lock4j(name = ProductRedisNames.REPLACE_SUPPLIER, keys = "#bo.newSupplierId + '_' + #bo.oldSupplierSkuId", expire = 30000, acquireTimeout = 1000)
    public Boolean replaceSupplier(ReplaceSupplierBo bo) {
        LoginUser user = LoginHelper.getLoginUser();
        if (user == null) {
            throw new ServiceException("用户不存在");
        }
        //查询旧销售批次
        SupplierSkuVo skuVo = supplierSkuMapper.selectVoById(bo.getOldSupplierSkuId());
        if (skuVo == null) {
            throw new ServiceException("供应商商品销售批次不存在");
        }
        if (Objects.equals(skuVo.getSupplierId(), bo.getNewSupplierId())) {
            throw new ServiceException("不能自己替换自己");
        }
        if (skuVo.getOriginalSupplierId() != null) {
            throw new ServiceException("已经换过一次供应商，不能再换");
        }
        //处理新供应商信息
        RemoteSupplierVo supplierVo = remoteSupplierService.getSupplierById(bo.getNewSupplierId());
        if (supplierVo == null) {
            throw new ServiceException("新供应商不存在");
        }
        //校验子账户是否禁止入金（调用平安接口检查）
        checkOrgControlStatus(supplierVo.getCode());
        // 检查平台商品 - 等级，规格，进出口，商品产地是否存在
        SpuVO spu = spuService.getById(skuVo.getSpuId());
        if (spu == null) {
            throw new ServiceException("平台商品不存在");
        }
        KeyValueBO.checkSpuExt(skuVo, spu.getExpList());
        //下架旧批次
        SupplierSku updateSku = new SupplierSku();
        updateSku.setId(skuVo.getId());
        updateSku.setStatus(SupplierSkuStatusEnum.STATUS5.getCode());
        updateSku.setOriginalSupplierId(0L);
        updateSku.setOriginalSupplierSkuId(0L);
        updateSku.setBatchType(SupplierSkuBatchTypeEnum.BATCH_TYPE4.getCode());
        supplierSkuMapper.updateById(updateSku);
        // 修改同步sku
        skuService.updateBatchSyncSku(skuVo.getId());

        //查询新供应商有没有供过这个商品
        // TODO 入参需要加入档口id
        Long supplierDeptId = 0L;
        SupplierSpuBo spuBo = new SupplierSpuBo();
        spuBo.setSupplierId(bo.getNewSupplierId());
        spuBo.setSupplierDeptId(supplierDeptId);
        spuBo.setSpuId(skuVo.getSpuId());
        SupplierSpuVo supplierSpuVo = supplierSpuMapper.querySupplierSpu(spuBo);
        SupplierSku addSku = MapstructUtils.convert(skuVo, SupplierSku.class);
        if (supplierSpuVo == null) {//如果新供应商没有供过这个商品，就新增
            //查询旧供应商商品，直接复制，然后改供应商信息，进行新增
            spuBo.setSupplierId(skuVo.getSupplierId());
            spuBo.setSupplierDeptId(skuVo.getSupplierDeptId());
            SupplierSpuVo updateSpuVo = supplierSpuMapper.querySupplierSpu(spuBo);
            if (updateSpuVo == null) {
                throw new ServiceException("被换供应商商品不存在");
            }
            //新增供应商商品
            String simpleCode = supplierVo.getCode();
            if (supplierDeptId != null && supplierDeptId != 0) {
                RemoteDeptDetailVo deptDetailVo = remoteDeptService.getByDeptId(skuVo.getSupplierDeptId());
                simpleCode = deptDetailVo.getDeptCode();
            }
            SupplierSpu addSupplierSpu = MapstructUtils.convert(updateSpuVo, SupplierSpu.class);
            addSupplierSpu.setId(null);
            addSupplierSpu.setCode(skuVo.getSpuCode() + simpleCode);
            addSupplierSpu.setLabel(addSupplierSpu.getCode());
            addSupplierSpu.setSupplierId(supplierVo.getId());
            addSupplierSpu.setSupplierDeptId(supplierDeptId);
            addSupplierSpu.setSupplierCode(supplierVo.getCode());
            addSupplierSpu.setSupplierName(supplierVo.getName());
            supplierSpuMapper.insert(addSupplierSpu);

            //供应商商品批次获取供应商商品id和code
            addSku.setSupplierSpuId(addSupplierSpu.getId());
            addSku.setSupplierSpuCode(addSupplierSpu.getCode());
        } else {
            //供应商商品批次获取供应商商品id和code
            addSku.setSupplierSpuId(supplierSpuVo.getId());
            addSku.setSupplierSpuCode(supplierSpuVo.getCode());
        }

        //上架新批次
        convertSupplierSku(addSku);
        addSku.setSupplierId(supplierVo.getId());
        addSku.setSupplierDeptId(supplierDeptId);
        addSku.setSupplierCode(supplierVo.getCode());
        addSku.setSupplierName(supplierVo.getName());
        addSku.setId(null);
        addSku.setSupplierId(bo.getNewSupplierId());
        addSku.setBatchType(SupplierSkuBatchTypeEnum.BATCH_TYPE4.getCode());
        addSku.setOriginalSupplierId(skuVo.getSupplierId());
        addSku.setOriginalSupplierSkuId(skuVo.getId());
        addSku.setStatus(SupplierSkuStatusEnum.STATUS5.getCode());
        addSku.setUpTime(now());
        addSku.setDownTime(null);
        addSku.setCode(createSupplierSkuCode(addSku.getSpuId(), addSku.getSupplierId(), addSku.getSupplierSpuCode(), addSku.getSaleDate()));
        //拼接全称
        String spuAnotherName = getSpuAnotherName(skuVo.getCategoryId());
        String[] cateGoryPathNameArray = spu.getPathName().split("/");
        String fullName = (addSku.getBrand() != null ? addSku.getBrand() : "") +
                (addSku.getSpuName() != null ? addSku.getSpuName() : "") +
                (addSku.getShortProducer() != null ? addSku.getShortProducer() : "") +
                (addSku.getSpuGrade() != null ? addSku.getSpuGrade() : "") +
                (addSku.getSpuStandards() != null ? addSku.getSpuStandards() : "") +
                (addSku.getCooperation() != null && addSku.getCooperation() == 1 ? "战略品" : "") +
                (SupplierSkuBusinessTypeEnum.BUSINESS_TYPE20.getCode().equals(addSku.getBusinessType())
                        ? SupplierSkuBusinessTypeEnum.BUSINESS_TYPE20.getDesc() : "") +
                cateGoryPathNameArray[2] + cateGoryPathNameArray[3] + spuAnotherName;
        addSku.setFullName(fullName);
        supplierSkuMapper.insert(addSku);
        // 新增同步sku
        skuService.insertBatchSyncSku(addSku.getId());


        //添加操作日志
        SupplierSkuApproveLog log = new SupplierSkuApproveLog();
        log.setSupplierSkuId(addSku.getId());
        log.setOperate(SupplierSkuApproveLogOperateEnum.BUYER.getDesc() + user.getRealName() + "," + SupplierSkuBatchTypeEnum.BATCH_TYPE4.getDesc());
        log.setStatus(SupplierSkuStatusEnum.STATUS4.getCode());
        supplierSkuApproveLogMapper.insert(log);

        //根据旧批次id给新批次复制创建库存，禁用城市仓，文件
        String imgUrl = addSupplierSkuOther(addSku, bo.getOldSupplierSkuId(), null);

        //订单项处理，以及生成转单记录
        RemoteReplaceSupplierBo remoteReplaceSupplierBo = new RemoteReplaceSupplierBo();
        remoteReplaceSupplierBo.setNewSupplierId(bo.getNewSupplierId());
        remoteReplaceSupplierBo.setOldSupplierSkuId(bo.getOldSupplierSkuId());
        remoteReplaceSupplierBo.setNewSupplierSkuId(addSku.getId());
        remoteReplaceSupplierBo.setSpuStandards(skuVo.getSpuStandards());
        remoteReplaceSupplierBo.setSpuGrossWeight(skuVo.getSpuGrossWeight());
        remoteReplaceSupplierBo.setSpuNetWeight(skuVo.getSpuNetWeight());
        remoteReplaceSupplierBo.setPrice(skuVo.getPrice());
        remoteReplaceSupplierBo.setImgUrl(imgUrl);
        remoteOrderService.replaceSupplier(remoteReplaceSupplierBo);
        return true;
    }

    @Override
    public List<RemoteSupplierSkuStockVo> listSupplierSkuStockBySupplierIds(List<Long> supplierSkuIds) {
        if (CollectionUtil.isEmpty(supplierSkuIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<SupplierSkuStock> queryWrapper = new LambdaQueryWrapper<SupplierSkuStock>()
                .in(SupplierSkuStock::getSupplierSkuId, supplierSkuIds);
        List<SupplierSkuStock> supplierSkuStocks = supplierSkuStockMapper.selectList(queryWrapper);
        if (ObjectUtil.isEmpty(supplierSkuStocks)) {
            return Collections.emptyList();
        }
        return BeanUtil.copyToList(supplierSkuStocks, RemoteSupplierSkuStockVo.class);
    }

    @Override
    public SupplierSkuStockVo getSupplierSkuStock(Long supplierSkuId) {
        LambdaQueryWrapper<SupplierSkuStock> queryWrapper = new LambdaQueryWrapper<SupplierSkuStock>()
                .eq(SupplierSkuStock::getSupplierSkuId, supplierSkuId);

        SupplierSkuStockVo stockVo = supplierSkuStockMapper.selectVoOne(queryWrapper);

        SupplierSku skuVo = supplierSkuMapper.selectOne(new LambdaQueryWrapper<SupplierSku>().select(SupplierSku::getSkuId).eq(SupplierSku::getId, supplierSkuId));

        // 云仓库存-如有
        if (skuVo.getSkuId() != null && skuVo.getSkuId() != 0L) {
            CwStockDetailVo stockDetailVo = cwStockDetailService.getBySkuId(skuVo.getSkuId());
            if (stockDetailVo != null) {
                int stock = ObjectUtil.defaultIfNull(stockDetailVo.getOnhandQty(), BigDecimal.ZERO).intValue();
                int lockStock = ObjectUtil.defaultIfNull(stockDetailVo.getAllocationQty(), BigDecimal.ZERO).intValue();
                int onWayStock = ObjectUtil.defaultIfNull(stockDetailVo.getOnwayQty(), BigDecimal.ZERO).intValue();
                stockVo.setCloudOnWayStock(onWayStock);
                stockVo.setCloudActualStock(stock);
                stockVo.setCloudLockStock(lockStock);
                stockVo.setCloudStock(stock - lockStock + onWayStock);
            }
        }
        return stockVo;
    }

    /**
     * 查询需要差额退款的供应商商品销售批次
     *
     * @return
     */
    @Override
    public List<QueryRefundDifferenceSkuVo> queryRefundDifferenceSku(LocalDate saleDate, List<Long> supplierSkuIdList) {
        int offset = 0;
        int pageSize = 1000;
        RefundDifferenceBo bo = new RefundDifferenceBo();
        if (CollectionUtil.isNotEmpty(supplierSkuIdList)) {
            bo.setSupplierSkuIdList(supplierSkuIdList);
        } else {
            bo.setSaleDate(saleDate);
        }
        bo.setStatus(SupplierSkuStatusEnum.STATUS5.getCode());
        List<QueryRefundDifferenceSkuVo> voList = new ArrayList<>();
        while (true) {
            bo.setOffset(offset * pageSize);
            bo.setPageSize(pageSize);
            List<QueryRefundDifferenceSkuVo> result = supplierSkuMapper.queryRefundDifference(bo);
            if (result != null && result.size() > 0) {
                voList.addAll(result);
            } else {
                break;
            }
            offset++;
        }
        return voList;
    }

    /**
     * 根据供应商商品code查询供应商批次商品(最近15天)
     */
    @Override
    public List<RemoteSupplierSkuInfoVo> getBySpuCode(String code) {
        //获取前15天的销售日
        LocalDate saleDate = LocalDate.now().minusDays(15);
        List<RemoteSupplierSkuInfoVo> bySpuCode = supplierSkuMapper.getBySpuCode(code, saleDate);
        if (CollectionUtil.isNotEmpty(bySpuCode)) {
            // 规格转换
            bySpuCode.forEach(RemoteSupplierSkuInfoVo::buildSpuStandardsName);
        }
        return bySpuCode;
    }

    /**
     * 根据总仓id查询供应商今日供货商品数量
     *
     * @param bo
     * @return
     */
    @Override
    public List<RemoteQuerySupplierDeliverVo> querySupplierDeliver(RemoteQuerySupplierDeliverBo bo) {
        bo.setSaleDate(getSaleDate(bo.getRegionWhId()));
        return supplierSkuMapper.querySupplierDeliver(bo);
    }


    /**
     * 根据条件批量新增批次
     *
     * @param bo
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createTestSku(CreateTestSkuBo bo) {
        RemoteSupplierVo supplierVo = remoteSupplierService.getSupplierById(bo.getSupplierId());
        int createNum = 0;
        LambdaUpdateWrapper<Spu> luw = new LambdaUpdateWrapper<>();
        luw.eq(Spu::getDelFlag, 0);
        List<Spu> spuList = spuMapper.selectList(luw);
        RemoteRegionWhVo whVo = remoteRegionWhService.queryById(bo.getRegionWhId());
        if (whVo == null) {
            throw new ServiceException("总仓不存在");
        }
        if (whVo.getStatus() == 0) {
            throw new ServiceException("总仓已被禁用");
        }
        AddSupplierSkuFileBo fileBo = new AddSupplierSkuFileBo();
        fileBo.setFileUrl(bo.getFileUrl());
        fileBo.setType(SupplierSkuFileTypeEnum.TYPE1.getCode());
        List<AddSupplierSkuFileBo> fileList = new ArrayList<>();
        fileList.add(fileBo);
        for (Spu spu : spuList) {
            if (createNum >= bo.getNum()) {
                break;
            }
            QueryLastListBo queryLastListBo = bo.getQueryLastListBo();
            queryLastListBo.setSpuIdList(Lists.newArrayList(spu.getId()));
            List<SupplierSkuVo> skuVoList = supplierSkuMapper.queryLastList(queryLastListBo);
            LocalDate saleDate = SaleDateUtil.getSaleDate(whVo.getSalesTimeEnd());
            if (skuVoList != null && skuVoList.size() > 0) {
                SupplierSkuVo vo = skuVoList.get(0);
                if (vo.getSaleDate().isEqual(saleDate)) {
                    continue;
                }
            }
            AddSupplierSkuBo add = new AddSupplierSkuBo();
            add.setFileList(fileList);
            add.setSupplierId(bo.getSupplierId());
            add.setSupplierDeptId(0L);
            SpuVO spuVo = spuMapper.getSpu(new SpuBO().setId(spu.getId()));
            Category category = categoryMapper.selectById(spuVo.getCategoryId());
            add.setCategoryId(spuVo.getCategoryId());
            add.setCategoryPathName(category.getName());
            add.setSpuId(spu.getId());
            add.setRegionWhId(bo.getRegionWhId());
            add.setSpuGrossWeight(bo.getSpuGrossWeight());
            add.setSpuNetWeight(bo.getSpuNetWeight());
            add.setProducer("陕西");
            add.setSnapshot("批量新增批次，压测用数据");
            add.setPrice(bo.getPrice());
            add.setStock(999);
            add.setPredictionTomorrow("xspcsxmj_tomorrowPredict_rise");
            add.setPredictionFuture("xspcsxmj_originPricePredict_slowRise");
            add.setPackageWord("十大打算撒啊飒飒飒飒十大打算撒");
            add.setSweetMin(new BigDecimal(10));
            add.setSweetMax(new BigDecimal(12));
            add.setPlaceOrderMultiple(1);
            add.setBuyMin(1);
            add.setBuyMax(999);
            add.setBuyerCode("1000000024");
            add.setBuyerName("胡斐");
            add.setAfterSaleDay(1);
            add.setAfterSaleType(CategoryAfterTypeEnum.NORMAL.getCode());
            add.setDeductibleSituation("阿三大苏打飒飒的");
            add.setAfterSaleExplain("阿萨大厦");
            add.setOperate(SupplierSkuApproveLogOperateEnum.SUPPLIER.getDesc() + supplierVo.getName());
            Long skuId = insertByBo(add);
            ChangeStatusBo changeStatusBo = new ChangeStatusBo();
            changeStatusBo.setId(skuId);
            changeStatusBo.setStatus(SupplierSkuStatusEnum.STATUS4.getCode());
            changeStatusBo.setReason("测试啊");
            changeStatus(changeStatusBo);
            createNum++;
        }
    }

    @Override
    public boolean hasSupplierDeptSku(Long supplierDeptId, List<Integer> statusList) {
        Long firstIdByDeptAndStatus = supplierSkuMapper.getFirstIdByDeptAndStatus(supplierDeptId, statusList);
        return firstIdByDeptAndStatus != null;
    }


    @Override
    public List<RemoteSupplierSkuStockVo> queryYunStock(List<Long> supplierSkuIds) {
        if (CollectionUtil.isEmpty(supplierSkuIds)) {
            return Collections.emptyList();
        }
        return supplierSkuMapper.queryYunStock(supplierSkuIds);
    }


    /**
     * 装卸队检测
     *
     * @param businessType
     * @param basPortageTeamId
     */
    @Override
    public void checkPortageTeam(Integer businessType, Long basPortageTeamId) {
        // 不校验装卸队
        if (Objects.equals(noCheckGoodsProperties.getSupplierSkuReview(), 1)) {
            return;
        }

        if (SupplierSkuBusinessTypeEnum.BUSINESS_TYPE20.getCode().equals(businessType)
                || SupplierSkuBusinessTypeEnum.BUSINESS_TYPE30.getCode().equals(businessType)
        ) {
            if (basPortageTeamId != null && basPortageTeamId == 0L) {
                throw new ServiceException("商品装卸队不能为空");
            }
        }
    }

    /**
     * 发货总仓校验
     *
     * @param businessType
     * @param provideRegionWhId
     */
    @Override
    public void checkProvide(Integer businessType, Long provideRegionWhId) {
        if (SupplierSkuBusinessTypeEnum.BUSINESS_TYPE20.getCode().equals(businessType)) {
            if (provideRegionWhId != null && provideRegionWhId == 0L) {
                throw new ServiceException("基采商品发货总仓不能为空");
            }
        }
    }


    @Override
    public List<Long> getIdsByFullName(LocalDate saleDate, String fullName, List<String> filterFields) {
        if (CollUtil.isEmpty(filterFields)) {
            Wrapper<SupplierSku> wrapper = new LambdaQueryWrapper<SupplierSku>()
                    .select(SupplierSku::getId)
                    .eq(SupplierSku::getSaleDate, saleDate)
                    .like(SupplierSku::getFullName, fullName);
            return supplierSkuMapper.selectVoList(wrapper, SupplierSkuVo.class).stream()
                    .map(SupplierSkuVo::getId)
                    .collect(Collectors.toList());
        } else {
            Wrapper<SupplierSku> wrapper = new LambdaQueryWrapper<SupplierSku>()
                    .eq(SupplierSku::getSaleDate, saleDate)
                    .like(SupplierSku::getFullName, fullName);
            return supplierSkuMapper.selectVoList(wrapper, SupplierSkuVo.class).stream()
                    .filter(vo -> matchSupplierSku(vo, fullName, filterFields))
                    .map(SupplierSkuVo::getId)
                    .collect(Collectors.toList());
        }
    }

    /**
     * 更新售罄标识
     *
     * @param supplierSkuIds
     * @param isOut
     * @return
     */
    @Override
    public int updateIsOut(List<Long> supplierSkuIds, Integer isOut) {
        if (CollectionUtil.isEmpty(supplierSkuIds)) {
            return 0;
        }
        SupplierSku supplierSku = new SupplierSku();
        supplierSku.setIsOut(isOut);
        return supplierSkuMapper.update(supplierSku, new LambdaUpdateWrapper<SupplierSku>()
                .in(SupplierSku::getId, supplierSkuIds));
    }

    private boolean matchSupplierSku(SupplierSkuVo vo, String fullName, List<String> filterFields) {
        for (String field : filterFields) {
            if ("spuName".equals(field) && vo.getSpuName().contains(fullName)) {
                return true;
            } else if ("spuStandards".equals(field) && vo.getSpuStandards().contains(fullName)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 售罄时，提醒总仓、供应商，异步发送公众号消息
     *
     * @param supplierSkuIds 供应商批次id
     */
    @Override
    @Async
    public void asyncSendSellOutMessage(List<Long> supplierSkuIds) {
        if (ObjectUtil.isEmpty(supplierSkuIds)) {
            return;
        }
        //查询供应商批次
        LambdaQueryWrapper<SupplierSku> qw = new LambdaQueryWrapper<SupplierSku>()
                .in(SupplierSku::getId, supplierSkuIds);
        List<SupplierSkuVo> supplierSkuList = supplierSkuMapper.selectVoList(qw);
        if (ObjectUtil.isEmpty(supplierSkuList)) {
            return;
        }
        supplierSkuList.forEach(supplierSku -> {
            supplierSku.tranSpuStandardsName();
        });
        //发送消息
        List<RemoteMessageNotifyV2Bo> messageList = this.assSellOutMessage(supplierSkuList);
        log.keyword("asyncSendSellOutMessage").info("多个供应商批次售罄，发送公众号消息给供应商和采购：{}", JSON.toJSONString(messageList));
        remoteMessageNotifyService.sendMessagesV2(messageList);
    }

    /**
     * 组装售罄消息
     *
     * @param supplierSkuList 供应商批次ID
     * @return
     */
    private List<RemoteMessageNotifyV2Bo> assSellOutMessage(List<SupplierSkuVo> supplierSkuList) {
        List<RemoteMessageNotifyV2Bo> messageList = new ArrayList<>();
        supplierSkuList.forEach(supplierSku -> {
            //参数
            LinkedHashMap<String, String> params = new LinkedHashMap<>();
            params.put("spuName", this.getProductName(supplierSku));
            //通知对象
            NotifyObjectTypeEnum objectType;
            //对象标识
            List<String> objectIds;
            //消息对象
            RemoteMessageNotifyV2Bo message;
            //发送给供应商
            objectType = NotifyObjectTypeEnum.SUPPLIER;
            objectIds = Collections.singletonList(String.valueOf(supplierSku.getSupplierId()));
            message = new RemoteMessageNotifyV2Bo(objectType, objectIds, MsgNotifyTemplateV2Enum.sku_sell_out, params, "");
            messageList.add(message);
            //发送给总仓
            objectType = NotifyObjectTypeEnum.REGION_WH;
            objectIds = Collections.singletonList(supplierSku.getBuyerCode());
            message = new RemoteMessageNotifyV2Bo(objectType, objectIds, MsgNotifyTemplateV2Enum.sku_sell_out, params, "");
            messageList.add(message);
        });
        return messageList;
    }

    /**
     * 组装商品名，品牌-产地-名称-规格
     *
     * @param skuInfo 供应商批次
     * @return 商品名
     */
    @Override
    public String getProductName(SupplierSkuVo skuInfo) {
        String brand = StrUtil.isNotBlank(skuInfo.getBrand()) ? skuInfo.getBrand() + "-" : "";
        String producer = "";
        //产地根据-分割取最后一个
        if (StrUtil.isNotBlank(skuInfo.getProducer())) {
            String[] split = skuInfo.getProducer().split(StrPool.DASHED);
            producer = split[split.length - 1] + "-";
        }
        String name;
        if ("配货商品".equals(skuInfo.getSpuName())) {
            name = brand + producer + skuInfo.getSpuName();
        } else {
            name = brand + producer + skuInfo.getSpuName() + "-" + skuInfo.getSpuStandards();
        }
        return name;
    }

    /**
     * sku降级，下架原sku、supplierSku，创建新sku、supplierSKu
     *
     * @param bo
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long skuDowngrade(CwStockDowngradeBo bo) {
        LoginUser user = LoginHelper.getLoginUser();
        //查询原SKU
        SupplierSkuVo skuVo = skuMapper.querySupplierSkuById(bo.getSkuId());
        if (ObjectUtil.isEmpty(skuVo)) {
            throw new ServiceException("原商品不存在");
        }
        //检验平台商品信息
        SpuVO spu = spuService.getById(skuVo.getSpuId());
        if (ObjectUtil.isEmpty(spu)) {
            throw new ServiceException("平台商品不存在");
        }
        //sku新增入参组装
        GetBackInfoVo backInfoBySkuId = this.getBackInfoBySkuId(skuVo.getSupplierSkuId());
        AddSupplierSkuBo addBo = JSON.parseObject(JSON.toJSONString(backInfoBySkuId), AddSupplierSkuBo.class);
        bo.setAddSupplierSkuBo(addBo);
        addBo.setSpuGrade(bo.getDowngradeSpuGrade());
        addBo.setPrice(bo.getDowngradePrice());
        addBo.setStock(skuVo.getStock());
        // 转换规格
        addBo.buildSkuStandardsSortJoin();

        //降级前检查，校验等级是否降级、单价是否减少
        this.checkDowngrade(spu, addBo, skuVo);
        //下架原sku
        this.downWhenDowngrade(skuVo);
        //扣减原sku的库存
        if (skuVo.getStock() > 0) {
            UpdateSupplierSkuStockBo stockUpdate = new UpdateSupplierSkuStockBo();
            stockUpdate.setBackStock(-skuVo.getStock());
            stockUpdate.setSupplierSkuId(skuVo.getSupplierSkuId());
            supplierSkuStockMapper.updateStock(stockUpdate);
            List<SupplierSkuStockVo> supplierSkuStockVos = supplierSkuStockMapper.selectBySupplierSkuIds(Collections.singletonList(skuVo.getSupplierSkuId()));
            SupplierSkuStockVo supplierSkuStockVo = supplierSkuStockVos.get(0);
            if (supplierSkuStockVo.getStock() < 0) {
                log.keyword("skuDowngrade").error("商品库存波动中:" + JsonUtils.toJsonString(supplierSkuStockVo));
                throw new ServiceException("商品库存波动中，请稍后重试");
            }
        }
        //查询降级后的sku是否存在了
        SupplierSkuVo lastSku = this.checkSkuExist(addBo, spu);
        //如果降级后的sku不存在，新增sku的批次id
        Long newSupplierSkuId = null;
        //降级后商品是否已存在
        if (ObjectUtil.isEmpty(lastSku)) {
            //开始新增批次及sku
            addBo.setStatus(SupplierSkuStatusEnum.STATUS5.getCode());
            addBo.setOperate(SupplierSkuApproveLogOperateEnum.BUYER.getDesc() + user.getRealName() + "," + SupplierSkuBatchTypeEnum.BATCH_TYPE111.getDesc());
            // 档口不传默认为供应商
            addBo.setSupplierDeptId(Optional.ofNullable(addBo.getSupplierDeptId()).orElseGet(() -> 0L));
            Integer businessType = addBo.getBusinessType();
            this.checkProvide(businessType, addBo.getProvideRegionWhId());
            this.checkPortageTeam(businessType, addBo.getBasPortageTeamId());
            addBo.setSkipFileCheck(true);
            addBo.setUpTime(new Date());
            addBo.setSkuLabel(null);
            addBo.setSnapshot(addBo.getSnapshot() == null ? "" : addBo.getSnapshot());
            addBo.setSubsidyAmount(null);
            log.keyword("skuDowngrade addBo").info("降级复制的实体：{}", JSON.toJSONString(addBo));
            //调用新增
            newSupplierSkuId = SpringUtils.getBean(SupplierSkuServiceImpl.class).insertByBo(addBo);
            log.keyword("skuDowngrade").info("skuId：{}，降级后sku等级：{}，降级后不存在该等级，新增newSupplierSkuId：{}", bo.getSkuId(), bo.getDowngradeSpuGrade(), newSupplierSkuId);
        } else {
            //下架
            downWhenDowngrade(lastSku);
            //修改库存，库存增加降级入库的库存
            if (skuVo.getStock() > 0) {
                UpdateSupplierSkuStockBo stockUpdate = new UpdateSupplierSkuStockBo();
                stockUpdate.setAddStock(skuVo.getStock());
                stockUpdate.setSupplierSkuId(lastSku.getSupplierSkuId());
                supplierSkuStockMapper.updateStock(stockUpdate);
            }
        }
        return ObjectUtil.isEmpty(lastSku) ? newSupplierSkuId : lastSku.getSupplierSkuId();
    }

    /**
     * 降级，下架sku及批次sku
     *
     * @param skuVo
     */
    private void downWhenDowngrade(SupplierSkuVo skuVo) {
        // 销售批次，下架
        Date date = new Date();
        LambdaUpdateWrapper<SupplierSku> luw = new LambdaUpdateWrapper<SupplierSku>()
                .eq(SupplierSku::getId, skuVo.getSupplierSkuId())
                .ne(SupplierSku::getStatus, SupplierSkuStatusEnum.STATUS5.getCode())
                .set(SupplierSku::getStatus, SupplierSkuStatusEnum.STATUS5.getCode())
                .set(SupplierSku::getDownTime, date)
                .set(SupplierSku::getSubsidyAmount, BigDecimal.ZERO);
        supplierSkuMapper.update(luw);
        // sku，下架
        LambdaUpdateWrapper<Sku> wrp = new LambdaUpdateWrapper<Sku>()
                .eq(Sku::getId, skuVo.getSkuId())
                .ne(Sku::getStatus, SupplierSkuStatusEnum.STATUS5.getCode())
                .set(Sku::getStatus, SupplierSkuStatusEnum.STATUS5.getCode())
                .set(Sku::getDownTime, date)
                .set(Sku::getSubsidyAmount, BigDecimal.ZERO);
        skuMapper.update(wrp);
    }

    /**
     * 降级前校验
     *
     * @param spu   平台商品
     * @param addBo 降级后新增入参
     * @param skuVo 原sku
     */
    private void checkDowngrade(SpuVO spu, AddSupplierSkuBo addBo, SupplierSkuVo skuVo) {
        List<CategoryExpVO> expList = spu.getExpList();
        Optional<CategoryExpVO> first = expList.stream().filter(e -> Objects.equals(KeyValueBO.LEVEL, e.getExpType())).findFirst();
        if (first.isEmpty()) {
            throw new ServiceException("该商品spu缺少等级配置");
        }
        //等级是否低于原等级
        CategoryExpVO categoryExpVO = first.get();
        JSONArray jsonArray = JSON.parseArray(categoryExpVO.getExpValue());
        if (jsonArray.isEmpty()) {
            throw new ServiceException("商品等级配置中不存在等级");
        }
        List<String> nameList = jsonArray.stream().map(e -> (String) ((JSONObject) e).get("name")).toList();

        //商品等级需要低于原等级，按配置的顺序比较高低，配置项在前的是高级
        int index = nameList.indexOf(addBo.getSpuGrade());
        if (index < 0) {
            throw new ServiceException("商品等级配置中不存在等级（" + addBo.getSpuGrade() + "）");
        }
        if (index <= nameList.indexOf(skuVo.getSpuGrade())) {
            throw new ServiceException("商品等级需要低于原等级（" + skuVo.getSpuGrade() + "）");
        }
        //单价是否低于原单价
        if (skuVo.getPrice().compareTo(addBo.getPrice()) <= 0) {
            throw new ServiceException("商品单价需要低于原单价（" + skuVo.getPrice() + "）");
        }
        Map<String, String> levelDesMap = jsonArray.stream().collect(Collectors.toMap(e -> (String) ((JSONObject) e).get("name"), e -> (String) ((JSONObject) e).get("describe")));
        //设置等级描述
        addBo.setSpuGradeDesc(levelDesMap.get(addBo.getSpuGrade()));
    }

    /**
     * 校验sku是否存在
     *
     * @param bo  sku新增入参
     * @param spu 平台商品
     * @return
     */
    private SupplierSkuVo checkSkuExist(AddSupplierSkuBo bo, SpuVO spu) {
        // 批次匹配skuId
        CheckSupplierSkuBo checkSupplierSkuBo = new CheckSupplierSkuBo();
        checkSupplierSkuBo.setSpuId(bo.getSpuId());
        checkSupplierSkuBo.setRegionWhId(bo.getRegionWhId());
        checkSupplierSkuBo.setSupplierId(bo.getSupplierId());
        checkSupplierSkuBo.setSupplierDeptId(bo.getSupplierDeptId() == null ? 0L : bo.getSupplierDeptId());
        checkSupplierSkuBo.setSpuGrade(Optional.ofNullable(bo.getSpuGrade()).orElseGet(() -> ""));
        checkSupplierSkuBo.setSpuStandards(Optional.ofNullable(bo.getSpuStandards()).orElseGet(() -> ""));
        checkSupplierSkuBo.setDomestic(Optional.ofNullable(bo.getDomestic()).orElseGet(() -> 0));
        checkSupplierSkuBo.setBusinessType(bo.getBusinessType());
        checkSupplierSkuBo.setProvideRegionWhId(bo.getProvideRegionWhId() == null ? 0L : bo.getProvideRegionWhId());
        checkSupplierSkuBo.setSpuName(spu.getName());
        checkSupplierSkuBo.setCategoryId(spu.getCategoryId());
        checkSupplierSkuBo.setBatchType(SupplierSkuBatchTypeEnum.BATCH_TYPE1.getCode());
        SupplierSkuVo lastSku = skuMapper.checkSupplierSku(checkSupplierSkuBo);
        if (ObjectUtil.isNotEmpty(lastSku)) {
            lastSku.setSkuId(lastSku.getId());
        }
        return lastSku;
    }

    /**
     * 校验补贴审核单
     */
    private void checkSubsidyAudit(SupplierSkuSubsidyAuditCheckVo checkVo) {
        if(ObjectUtil.isEmpty(checkVo.getSubsidyAmount()) || ObjectUtil.isEmpty(checkVo.getMarketPrice())) {
            throw new ServiceException("补贴上架的行情价和补贴金额不能为空");
        }
        if(ObjectUtil.isEmpty(checkVo.getPrice())){
            throw new ServiceException("单价不能为空");
        }
        if(!Objects.equals(1, checkVo.getBargain()) || checkVo.getMarketPrice().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("补贴上架需为特价商品且市场价必须大于0");
        }
        if(checkVo.getSubsidyAmount().compareTo(BigDecimal.ZERO) <= 0 || checkVo.getSubsidyAmount().compareTo(new BigDecimal("99.99")) > 0){
            throw new ServiceException("补贴金额必须大于0且小于等于99.99");
        }
        if(checkVo.getMarketPrice().compareTo(checkVo.getPrice()) <= 0){
            throw new ServiceException("行情价必须大于单价");
        }
        //单价的百分之90
        BigDecimal priceNinetyPercent = checkVo.getPrice().multiply(new BigDecimal("0.9"));
        if(checkVo.getSubsidyAmount().compareTo(priceNinetyPercent) > 0){
            throw new ServiceException("补贴金额不能大于单价的90%");
        }
    }

    @Override
    public CheckSkuMaxSaleNumResVo checkSkuMaxSaleNum(Long regionWhId, Long supplierId) {
        Integer limitNum = remoteSupplierService.queryMaxSkuNumLimit(supplierId, regionWhId);
        SupplierSkuSaleNumBo onSaleGoodNum = skuService.querySupplierSkuSaleNum(supplierId, regionWhId, SupplierSkuSaleTypeEnum.SALE_TYPE2.getCode());
        if(limitNum == null || limitNum == 0){
            return new CheckSkuMaxSaleNumResVo(false, "没有坑位，请联系采购开通坑位");
        }
        Integer sn = ObjectUtil.defaultIfNull(onSaleGoodNum.getSaleNum(), 0);
        if(sn >= limitNum){
            return new CheckSkuMaxSaleNumResVo(false, "“商家自营”坑位已超上限，暂不可上架！如想上架，可下架其他“商家自营”商品再上架");
        }
        return new CheckSkuMaxSaleNumResVo(true, "");
    }

    /**
     * 获取已上架商品数量
     * @param regionWhId
     * @param supplierId
     * @return
     */
    @Override
    public SaleNumVo getSaleNum(Long regionWhId, Long supplierId) {
        // 获取最大SKU限制数量，并做空值处理
        Integer limitNum = ObjectUtil.defaultIfNull(
                remoteSupplierService.queryMaxSkuNumLimit(supplierId, regionWhId), 0);
        // 查询供应商在仓区的在售商品数量信息
        SupplierSkuSaleNumBo onSaleSkuInfo = skuService.querySupplierSkuSaleNum(
                supplierId, regionWhId, SupplierSkuSaleTypeEnum.SALE_TYPE2.getCode());
        // 安全获取已使用数量
        Integer useNum = ObjectUtil.defaultIfNull(
                onSaleSkuInfo == null ? null : onSaleSkuInfo.getSaleNum(), 0);
        return new SaleNumVo(limitNum, useNum);
    }
    @Override
    public TableDataInfo<QuerySalePageVo> queryDiscountPage(QuerySalePageBo bo) {
        RemoteMkActivitySkuDiscountBo discountBo = new RemoteMkActivitySkuDiscountBo();
        RemoteRegionWhVo remoteRegionWhVo = remoteRegionWhService.queryById(bo.getRegionWhIdList().get(0));
        LocalDate saleDate = SaleDateUtil.getSaleDate(remoteRegionWhVo.getSalesTimeEnd());
        discountBo.setSaleDate(saleDate);
        discountBo.setRegionId(bo.getRegionWhIdList().get(0));
        discountBo.setCityWhId(bo.getCityWhId());
        RemoteCityWhPlaceVo remoteCityWhPlaceVo = remoteCityWhPlaceService.queryById(bo.getPlaceId());
        Long parentPlaceId = remoteCityWhPlaceVo.getParentPlaceId();
        if (Objects.nonNull(parentPlaceId)) {
            remoteCityWhPlaceVo = remoteCityWhPlaceService.queryById(parentPlaceId);
        }
        List<Long> logisticsId = remoteRegionLogisticsService.getLogisticsId(remoteCityWhPlaceVo.getId());
        if (CollectionUtil.isNotEmpty(logisticsId)) {
            discountBo.setLogisticsId(logisticsId.get(0));
        }
        discountBo.setActivityType(90);
        RemoteMkActivitySkuSearchVo discountQueryInfo = getDiscountQueryInfo(discountBo);
        if (ObjectUtil.isEmpty(discountQueryInfo)){
            return TableDataInfo.build();
        }
        bo.setSkuIds(discountQueryInfo.getSkuIds());
        bo.setRollSkuIds(discountQueryInfo.getRollSkuIds());
        bo.setCategoryIds(discountQueryInfo.getCategoryIds());
        bo.setRollCategoryIds(discountQueryInfo.getRollCategoryIds());
        bo.setSpuIds(discountQueryInfo.getSpuIds());
        bo.setRollSpuIds(discountQueryInfo.getRollSpuIds());
        bo.setCopySold(discountQueryInfo.getCopySold());
        if (bo.getSaleType() != null && !bo.getSaleType().equals(productProperties.getActivityCheckSaleType())) {
            bo.setSaleType(0);
        }else if (bo.getSaleType() == null){
            bo.setSaleType(productProperties.getActivityCheckSaleType());
        }

        bo.setStatus(SupplierSkuStatusEnum.STATUS4.getCode());
        // 过滤城市仓禁卖
        if (bo.getCityWhId() != null && bo.getCityWhId() != 0L) {
            bo.setNotInSkuIdList(supplierSkuDisableCityWhMapper.getSkuIdsByCityWhId(bo.getCityWhId(), null));
        }else {
            bo.setNotInSkuIdList(discountQueryInfo.getRollSkuIds());
        }
        Page<QuerySalePageVo> result = supplierSkuMapper.queryDiscountPage(bo, bo.build());
        if (result != null && !result.getRecords().isEmpty()) {
            List<Long> supplierSkuIdList = result.getRecords().stream().map(QuerySalePageVo::getId).toList();
            List<Long> skuIdList = new ArrayList<>();
            List<Long> supplierIdList = new ArrayList<>();
            List<Long> spulierDeptIdList = new ArrayList<>();
            List<Long> regionWhIds = new ArrayList<>();
            List<RemoteMkActivitySkuBo> supplierSkuList = new ArrayList<>();
            //获取库存信息
            List<SupplierSkuStockVo> stockVos = supplierSkuStockMapper.selectBySupplierSkuIds(supplierSkuIdList);
            Map<Long, SupplierSkuStockVo> stockMap = stockVos.stream().collect(Collectors.toMap(SupplierSkuStockVo::getSupplierSkuId, Function.identity(), (v1, v2) -> v1));
            for (QuerySalePageVo vo : result.getRecords()){
                RemoteMkActivitySkuBo skuBo = new RemoteMkActivitySkuBo();
                if (!supplierIdList.contains(vo.getSupplierId())){
                    supplierIdList.add(vo.getSupplierId());
                }
                if (vo.getSupplierDeptId() != null && vo.getSupplierDeptId() != 0L && !spulierDeptIdList.contains(vo.getSupplierDeptId())){
                    spulierDeptIdList.add(vo.getSupplierDeptId());
                }
                if (vo.getProvideRegionWhId() != null && vo.getProvideRegionWhId() != 0L && !regionWhIds.contains(vo.getProvideRegionWhId())){
                    regionWhIds.add(vo.getProvideRegionWhId());
                }
                if (vo.getSkuId() != null && !skuIdList.contains(vo.getSkuId())){
                    skuIdList.add(vo.getSkuId());
                }
                //处理库存
                if (stockMap.containsKey(vo.getId())) {
                    vo.setStock(stockMap.get(vo.getId()).getStock());
                    vo.setSold(stockMap.get(vo.getId()).getSold());
                    vo.setLockStock(stockMap.get(vo.getId()).getLockStock());
                }
                skuBo.setSupplierSkuId(vo.getId());
                supplierSkuList.add(skuBo);
            }
            //根据供应商商品销售批次id集合和类型查询供应商商品销售批次文件信息(查询商品展示图片)
            QuerySupplierSkuFileBo fileBo = new QuerySupplierSkuFileBo();
            fileBo.setSupplierSkuIdList(supplierSkuIdList);
            fileBo.setTypeList(List.of(SupplierSkuFileTypeEnum.TYPE1.getCode()));
            List<SupplierSkuFileVo> fileVoList = supplierSkuFileMapper.queryList(fileBo);

            List<Sku> skuList = skuMapper.selectBatchIds(skuIdList);

            //处理数据
            Map<Long, SupplierSkuFileVo> fileMap = fileVoList.stream().collect(Collectors.toMap(SupplierSkuFileVo::getSupplierSkuId, Function.identity(), (v1, v2) -> v1));
            LoginUser user = LoginHelper.getLoginUser();
            if (user != null) {
                List<RemoteCartItemCountVO> cartList = remoteCartItemService.getCartItemListInfo(user.getUserId(), supplierSkuIdList);
                if (cartList != null && !cartList.isEmpty()) {
                    Map<Long, RemoteCartItemCountVO> cartMap = cartList.stream().collect(Collectors.toMap(RemoteCartItemCountVO::getSkuId, Function.identity(), (key1, key2) -> key2));
                    for (QuerySalePageVo pageVo : result.getRecords()) {
                        RemoteCartItemCountVO cart = cartMap.get(pageVo.getSkuId());
                        if (cart != null) {
                            pageVo.setCartCount(cart.getCount());
                        }
                    }
                }
            }
            // 供应商信息
            List<RemoteSupplierVo> supplierVoList = remoteSupplierService.getSupplierByIds(supplierIdList);
            Map<Long, RemoteSupplierVo> supplierVoMap = supplierVoList.stream().filter(Objects::nonNull).collect(Collectors.toMap(RemoteSupplierVo::getId, Function.identity(), (v1, v2) -> v1));
            // 部门信息
            Map<Long, CodeNameVo> spulierDeptIdMap = new HashMap<>();
            if (CollUtil.isNotEmpty(spulierDeptIdList)) {
                Map<Long, CodeNameVo> deptCodeNameMap = remoteDeptService.getDeptCodeNameMap(spulierDeptIdList);
                if (CollUtil.isNotEmpty(deptCodeNameMap)) {
                    spulierDeptIdMap.putAll(deptCodeNameMap);
                }
            }

            // 供货总仓名称
            Map<Long, String> regionWhVoMap = new HashMap<>();
            List<RemoteRegionWhVo> regionWhVos;
            if (CollectionUtil.isNotEmpty(regionWhIds)) {
                regionWhVos = remoteRegionWhService.selectRegionWhInfoByIds(regionWhIds);
                regionWhVoMap = regionWhVos.stream().collect(Collectors.toMap(RemoteRegionWhVo::getId
                        , RemoteRegionWhVo::getRegionWhName, (n1, n2) -> n2));
            }

            // 获取商品参与的免运活动
            LoginUser loginUser = LoginHelper.getLoginUser();
            Map<Long, RemoteRuleFreightPriceVo> longRemoteRuleFreightPriceVoMap = new HashMap<>();
            if (Objects.nonNull(loginUser)) {
                longRemoteRuleFreightPriceVoMap = this.querySkuFreightPrice(loginUser, bo.getCityWhId(), bo.getPlaceId()
                        , remoteRegionWhVo, result.getRecords());
            }
            Map<Long, BigDecimal> skuDiscount = new HashMap<>();
            //获取商品优惠信息
            List<SkuCouponMatchVO> skuCouponMatchVos = getSkuDiscountInfo(skuList, bo.getCityWhId(), bo.getPlaceId());
            if (CollectionUtil.isNotEmpty(skuCouponMatchVos)) {
                skuDiscount = skuCouponMatchVos.stream().collect(Collectors.toMap(SkuCouponMatchVO::getSkuId, SkuCouponMatchVO::getDiscountAmount, (v1, v2) -> v1));
            }
            discountBo.setSupplierSkuList(supplierSkuList);
            List<RemoteMkActivitySkuDiscountVo> discountList = discountSummaryService.getSkuDiscountIdList(discountBo);
            Map<Long, RemoteMkActivitySkuDiscountVo> discountVoMap = discountList.stream().collect(Collectors.toMap(RemoteMkActivitySkuDiscountVo::getSupplierSkuId, Function.identity()));
            //处理出参
            for (QuerySalePageVo skuVo : result.getRecords()) {
                if (StringUtils.isNotBlank(skuVo.getCategoryPathName())) {
                    skuVo.setCategoryName(skuVo.getCategoryPathName().split("/")[3]);
                }
                BigDecimal skuDiscountAmount = skuDiscount.getOrDefault(skuVo.getSkuId(), BigDecimal.ZERO);
                BigDecimal price = skuVo.getPrice().subtract(skuDiscountAmount);
                //减掉补贴
                price = price.subtract(skuVo.getSubsidyAmount());
                skuVo.setPrice(price.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : price);

                BigDecimal netWeightPrice = skuVo.getPrice().divide(skuVo.getSpuNetWeight(), 2, RoundingMode.HALF_UP);

                SupplierSkuFileVo fileVo = fileMap.get(skuVo.getId());
                if (fileVo != null) {
                    skuVo.setImgUrl(fileMap.get(skuVo.getId()).getFileUrl());
                }
                RemoteSupplierVo supplierVo = supplierVoMap.get(skuVo.getSupplierId());
                if (supplierVo != null) {
                    skuVo.setSupplierSimpleCode(supplierVo.getSimpleCode());
                    skuVo.setSupplierAlias(supplierVo.getAlias());
                }
                CodeNameVo codeNameVo = spulierDeptIdMap.get(skuVo.getSupplierDeptId());
                if (codeNameVo != null) {
                    skuVo.setSupplierSimpleCode(codeNameVo.getCode());
                    skuVo.setSupplierDeptName(codeNameVo.getName());
                }
                // 构建规格参数
                skuVo.buildSkuStandardsList().buildSpuStandardsName();
                // 处理售后规则
                CategoryAfterTypeEnum afterTypeEnum = CategoryAfterTypeEnum.loadByCode(skuVo.getAfterSaleType());
                if (afterTypeEnum != null) {
                    skuVo.setAfterSaleName(afterTypeEnum.getName());
                    skuVo.setAfterSaleDesc(afterTypeEnum.getDesc());
                }
                // 区域简称
                if (StringUtils.isNotBlank(skuVo.getAreaCode()) && StringUtils.isBlank(skuVo.getShortProducer())) {
                    RemotePubAreaVO remotePubAreaVO = remoteBasPubAreaService.queryByCode(skuVo.getAreaCode());
                    if (remotePubAreaVO != null) {
                        skuVo.setShortProducer(remotePubAreaVO.getAreaAlias());
                    }
                }
                // 处理供货总仓名称
                String rwName = regionWhVoMap.get(skuVo.getProvideRegionWhId());
                if (StringUtils.isNotBlank(rwName)) {
                    skuVo.setProvideRegionWhName(rwName);
                }
                RemoteRuleFreightPriceVo remoteRuleFreightPriceVo = longRemoteRuleFreightPriceVoMap.get(skuVo.getId());
                if (Objects.nonNull(remoteRuleFreightPriceVo)) {
                    skuVo.setIsFreightDiscount(1);
                    skuVo.setFreightDiscountMinNum(remoteRuleFreightPriceVo.getMinNum());
                }
                // 前端隐藏商户编号&店铺
//                List<Long> hideMerchantRegionWhIdList = noCheckGoodsProperties.getHideMerchantRegionWhIdList();
//                if (hideMerchantRegionWhIdList.contains(skuVo.getRegionWhId())) {
//                    skuVo.setHideMerchant(1);
//                }

                // 特价商品降价幅度
                if (Objects.equals(skuVo.getBargain(), 1)) {
                    skuVo.setBargainDownPrice(skuVo.getMarketPrice().subtract(skuVo.getPrice()));
                }
                if (discountVoMap.containsKey(skuVo.getId()) && skuVo.getStock() > 0){
                    RemoteMkActivitySkuDiscountVo discountVo = discountVoMap.get(skuVo.getId());
                    int count = discountVo.getTargetQuantity().intValue() - skuVo.getSold() - skuVo.getLockStock();
                    if (count > 0) {
                        skuVo.setHasLimitDiscount(discountVo.getHasLimitDiscount());
                        skuVo.setDiscountCount(count > skuVo.getStock() ? skuVo.getStock() : count);
                        BigDecimal scale = skuVo.getPrice().multiply(discountVo.getDiscountRate()).setScale(2, RoundingMode.HALF_UP);
                        scale = scale.compareTo(BigDecimal.valueOf(0.01)) < 0 ? BigDecimal.valueOf(0.01) : scale;
                        skuVo.setPriceFree(skuVo.getPrice().subtract(scale).compareTo(discountVo.getMaxDiscountAmount()) > 0
                                ? skuVo.getPrice().subtract(discountVo.getMaxDiscountAmount()) : scale);
                        netWeightPrice = skuVo.getPriceFree().divide(skuVo.getSpuNetWeight(), 2, RoundingMode.HALF_UP);
                    }
                }
                if (netWeightPrice.compareTo(BigDecimal.ZERO) == 0) {
                    netWeightPrice = new BigDecimal("0.01");
                }
                skuVo.setNetWeightPrice(netWeightPrice);
            }
        }
        return TableDataInfo.build(result);
    }

    /**
     * 获取生效活动商品查询条件
     * @param dbo
     * @return
     */
    @Cacheable(cacheNames = ProductRedisNames.SKU_DISCOUNT_QUERY_INFO, key = "#dbo.regionId"+"_"+"dbo.cityWhId"+"_"+"dbo.logisticsId"+"_"+"dbo.saleDate")
    public RemoteMkActivitySkuSearchVo getDiscountQueryInfo(RemoteMkActivitySkuDiscountBo dbo){
        return discountSummaryService.getSkuDiscountQueryInfo(dbo);
    }

    /**
     * 查询商品是否禁用
     * @param spuId
     * @return
     */
    public void checkSpuIsDisable(Long spuId) {
        // 1. 参数校验
        if (spuId == null) {
            throw new ServiceException("平台商品ID不能为空");
        }

        // 2. 查询并检查SPU状态
        SpuVO spuVO = spuService.getById(spuId);
        if (spuVO == null) {
            throw new ServiceException("平台商品不存在");
        }
        if (spuVO.getStatus() == 0) {
            throw new ServiceException("平台商品已被禁用，请联系管理员");
        }

        // 3. 查询品类信息
        QueryWrapper<CategorySpu> wrapper = new QueryWrapper<>();
        wrapper.eq("spu_id", spuVO.getId())
                .eq("del_flag", 0)
                .eq("type", 0);
        CategorySpu categorySpu = categorySpuMapper.selectOne(wrapper);

        if (categorySpu == null) {
            throw new ServiceException("平台商品品类不存在");
        }
        Category category = categoryMapper.selectById(categorySpu.getCategoryId());
        // 4. 检查当前品类状态
        if (category.getStatus() == 0) {
            throw new ServiceException("平台商品品类已被禁用，请联系管理员");
        }
    }

    /**
     * 修改价格存入价格记录表中
     */
    public void recordPrice(UpdateSupplierSkuBo bo, SupplierSkuVo old, LoginUser user) {
        SupplierSkuPriceLog supplierSkuPriceLog = new SupplierSkuPriceLog();
        supplierSkuPriceLog.setSupplierSkuId(bo.getId());
        supplierSkuPriceLog.setOldPrice(old.getPrice());
        supplierSkuPriceLog.setNewPrice(bo.getPrice());
        supplierSkuPriceLog.setCreateCode(user.getUserCode());
        supplierSkuPriceLog.setCreateName(user.getRealName());
        supplierSkuPriceLog.setCreateTime(LocalDateTime.now());
        supplierSkuPriceLog.setUpdateName(bo.getOperate());
        supplierSkuPriceLog.setUpdateCode(user.getUserCode());
        supplierSkuPriceLog.setCreateTime(LocalDateTime.now());
        supplierSkuPriceLogMapper.insert(supplierSkuPriceLog);
    }

    /**
     * 记录修改库存操作
     */
    public void recordStock(UpdateSupplierSkuBo bo, SupplierSkuVo old, LoginUser user) {
        SupplierSkuStockLog supplierSkuStockLog = new SupplierSkuStockLog();
        supplierSkuStockLog.setSupplierSkuId(bo.getId());
        // 加库存场景
        if (bo.getAddStock() != null && bo.getAddStock() > 0){
           supplierSkuStockLog.setSold(old.getStock() + bo.getAddStock());
        } else if (bo.getStock() != null && bo.getStock() > 0) {
            supplierSkuStockLog.setSold(bo.getStock());
        }
        supplierSkuStockLog.setStock(old.getStock());
        supplierSkuStockLog.setType(StockTypeEnum.OTHER.getCode());
        // 因为记录的是库存操作的日志，所以订单相关的设置为0
        supplierSkuStockLog.setOrderCode("0");
        supplierSkuStockLog.setOrderId(0L);
        supplierSkuStockLog.setOrderItemId(0L);
        supplierSkuStockLog.setCreateCode(user.getUserCode());
        supplierSkuStockLog.setCreateName(user.getRealName());
        supplierSkuStockLog.setCreateTime(new Date());
        supplierSkuStockLog.setUpdateName(bo.getOperate());
        supplierSkuStockLog.setUpdateCode(user.getUserCode());
        supplierSkuStockLog.setUpdateTime(new Date());
        supplierSkuStockLogMapper.insert(supplierSkuStockLog);
    }

    public void salesQuantityCount(Long regionWhVoId, Long skuId, RemoteSkuLossVo lossVo) {
        if (regionWhVoId != null) {
            RemoteRegionWhVo regionWhVo = remoteRegionWhService.queryById(regionWhVoId);
            // 获取总仓的销售日
            LocalDate saleDate = SaleDateUtil.getSaleDate(regionWhVo.getSalesTimeEnd());
            // 获取昨天的销售日
            LocalDate yesterday = saleDate.minusDays(1);
            // 获取前7天的销售日
            LocalDate week = saleDate.minusDays(7);
            Map<String, Long> salesQuantityCount = remoteOrderService.getSalesQuantityCount(skuId, yesterday, week, saleDate);
            if (lossVo != null) {
                lossVo.setYesterdaySalesQuantity(salesQuantityCount.get("yesterday"));
                lossVo.setWeekSalesQuantity(salesQuantityCount.get("week"));
            }
            // 1.获取今年第一次上架日期
            // 2.构建日期范围
            // 3.当前年份
            int currentYear = LocalDateTime.now().getYear();
            // 4.当年第一天 00:00:00
            LocalDateTime yearStart = LocalDateTime.of(currentYear, 1, 1, 0, 0, 0);
            // 5.当年最后一天 23:59:59
            LocalDateTime yearEnd = LocalDateTime.of(currentYear, 12, 31, 23, 59, 59);
            LambdaQueryWrapper<SupplierSku> lqw = new LambdaQueryWrapper<>();
            lqw.select(SupplierSku::getUpTime)
                    .eq(SupplierSku::getSkuId, skuId)
                    .eq(SupplierSku::getRegionWhId, regionWhVoId)
                    .between(SupplierSku::getUpTime, yearStart, yearEnd)
                    .orderByAsc(SupplierSku::getUpTime).last("LIMIT 1");
            SupplierSku supplierSku = supplierSkuMapper.selectOne(lqw);
            if (lossVo != null && supplierSku != null) {
                lossVo.setFirstYearUpTime(supplierSku.getUpTime());
            }
        }
    }
}
