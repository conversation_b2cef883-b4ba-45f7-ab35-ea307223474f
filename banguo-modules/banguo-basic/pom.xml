<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.xianlink</groupId>
        <artifactId>banguo-modules</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>banguo-basic</artifactId>

    <description>
        banguo-basic基础数据模块
    </description>

    <dependencies>

        <!-- SpringCloud Alibaba Nacos -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Nacos Config -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-common-sentinel</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <!-- xianlink Common Log -->
        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-common-log</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-common-dict</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-common-doc</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-common-web</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-common-mybatis</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-common-dubbo</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-common-seata</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-common-idempotent</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-common-tenant</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-common-security</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-common-translation</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-common-sensitive</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-common-encrypt</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <!-- xianlink Api System -->
        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-api-system</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>xianjian-api-resource</artifactId>
            <version>${xianjian.vision}</version>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>banguo-api-basic</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>banguo-api-order</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>banguo-api-bi</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>banguo-api-product</artifactId>
        </dependency>


        <dependency>
            <groupId>cn.xianlink</groupId>
            <artifactId>banguo-api-trade</artifactId>
        </dependency>


        <!-- annotations -->
        <dependency>
            <groupId>org.jetbrains</groupId>
            <artifactId>annotations</artifactId>
            <version>24.0.1</version>
            <scope>compile</scope>
        </dependency>


    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>