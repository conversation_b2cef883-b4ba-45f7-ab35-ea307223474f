package cn.xianlink.basic.controller.admin;

import cn.xianlink.basic.domain.operation.CityChangeAdminBo;
import cn.xianlink.basic.domain.operation.CustomerCityBo;
import cn.xianlink.basic.service.IBasCustomerService;
import cn.xianlink.basic.service.ICityWhService;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.web.core.BaseController;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 数据运维
 * <AUTHOR> xiaodaibing on 2024-12-13 10:43
 * @folder 般果管理中心/数据运维
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/dataOperation")
public class DataOperationController extends BaseController {

    private final IBasCustomerService customerService;

    private final ICityWhService cityWhService;



    /**
     * 客户切换城市仓
     */
    @RepeatSubmit
    @PostMapping("/customerChangeCity")
    public R<Void> customerChangeCity(@RequestBody @Validated CustomerCityBo bo) {
        customerService.changeCity(bo.getCustomerId(), bo.getCityWhId());
        return R.ok();
    }



    /**
     * 城市仓换超管
     */
    @RepeatSubmit
    @PostMapping("/cityChangeAdmin")
    public R<Void> cityChangeAdmin(@RequestBody @Validated CityChangeAdminBo bo) {
        cityWhService.changeAdmin(bo.getCityWhId(), bo.getPhoneNumber());
        return R.ok();
    }



    /**
     * 子用户切换城市仓
     */
    @RepeatSubmit
    @PostMapping("/userChangeCity")
    public R<Void> userChangeCity(@RequestBody @Validated CityChangeAdminBo bo) {
        cityWhService.userChangeCity(bo.getCityWhId(), bo.getPhoneNumber());
        return R.ok();
    }

}
