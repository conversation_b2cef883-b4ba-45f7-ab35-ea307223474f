package cn.xianlink.basic;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;

/**
 * 基础数据模块
 *
 * <AUTHOR>
 */
@EnableDubbo
@SpringBootApplication
public class BanguoBasicApplication {
    public static void main(String[] args) {
        System.setProperty("nacos.logging.default.config.enabled", "false");
        System.setProperty("druid.filters", "xLogSql");
        SpringApplication application = new SpringApplication(BanguoBasicApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
        System.out.println("(♥◠‿◠)ﾉﾞ  基础数据模块启动成功   ლ(´ڡ`ლ)ﾞ  ");
    }
}
