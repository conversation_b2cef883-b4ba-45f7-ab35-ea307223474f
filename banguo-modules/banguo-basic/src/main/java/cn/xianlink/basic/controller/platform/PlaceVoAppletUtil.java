package cn.xianlink.basic.controller.platform;

import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.basic.domain.vo.citywh.CityWhPlaceVo;
import cn.xianlink.basic.domain.vo.citywh.CityWhPlaceVoApplet;
import cn.xianlink.basic.domain.vo.customer.BasCustomerUsedPlaceVo;
import cn.xianlink.basic.domain.vo.regionwh.RegionWhVo;
import cn.xianlink.basic.service.IBasCustomerUsedPlaceService;
import cn.xianlink.basic.service.IBasRegionLogisticsService;
import cn.xianlink.basic.service.ICityWhPlaceService;
import cn.xianlink.basic.service.IRegionWhService;
import cn.xianlink.basic.util.GeoCalculatorUtil;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.utils.MapstructUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Component
public class PlaceVoAppletUtil {

    private final transient ICityWhPlaceService cityWhPlaceService;
    private final transient IBasCustomerUsedPlaceService customerUsedPlaceService;
    private List<CityWhPlaceVoApplet> toApplet(List<CityWhPlaceVo> list, Double lng, Double lat) {
        return list.stream().map(e -> {
            CityWhPlaceVoApplet voApplet = MapstructUtils.convert(e, CityWhPlaceVoApplet.class);
            if (lng != null && lat != null) {
                voApplet.setDistance((int) GeoCalculatorUtil.calculateDistance(lat, lng, Double.parseDouble(e.getLat()), Double.parseDouble(e.getLng())));
            }
            return voApplet;
        }).collect(Collectors.toList());
    }

    public List<CityWhPlaceVoApplet> getPlaceVoAppletList(Long cityWhId, Double lng, Double lat) {
        return this.toApplet(cityWhPlaceService.selectListByCityWhId(cityWhId), lng, lat);
    }
    public List<CityWhPlaceVoApplet> getPlaceVoAppletList(String positionTitle, Double lng, Double lat) {
        return this.toApplet(cityWhPlaceService.selectListByPositionTitle(positionTitle), lng, lat);
    }

    public List<CityWhPlaceVoApplet> getUsedPlaceVoAppletList(Long customerId, Double lng, Double lat) {
        // id倒序查询
        List<BasCustomerUsedPlaceVo> usedPlaceVos = customerUsedPlaceService.selectUsedPlace(customerId);
        if (usedPlaceVos.size() == 0) {
            return null;
        }
        // 只显示5个，多余的删除
        int index = usedPlaceVos.size();
        if (usedPlaceVos.size() > 5) {
            index = 5;
            List<Long> ids = usedPlaceVos.subList(index, usedPlaceVos.size()).stream().map(BasCustomerUsedPlaceVo::getId).collect(Collectors.toList());
            customerUsedPlaceService.deleteBatchIds(ids);
        }
        // 根据提货点ids，查询提货点信息
        List<Long> placeIds = usedPlaceVos.subList(0, index).stream().map(BasCustomerUsedPlaceVo::getPlaceId).collect(Collectors.toList());
        Map<Long, CityWhPlaceVoApplet> map = this.toApplet(cityWhPlaceService.selectVoBatchIds(placeIds), lng, lat).stream().collect(Collectors.toMap(CityWhPlaceVoApplet::getId, Function.identity()));
        return placeIds.stream().filter(placeId -> map.containsKey(placeId)).map(placeId -> map.get(placeId)).collect(Collectors.toList());
    }
}
