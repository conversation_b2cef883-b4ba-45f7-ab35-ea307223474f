package cn.xianlink.basic.controller.admin;

import cn.xianlink.basic.domain.bo.freightlog.*;
import cn.xianlink.basic.domain.vo.freightlog.BasFreightLogisticsVo;
import cn.xianlink.basic.service.IBasFreightLogisticsService;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.core.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 基采物流
 * <AUTHOR>
 * @date 2024-03-21
 * @folder 般果管理中心/物流/基采物流(新)
 */
@Tag(name = "基采物流(新)")
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/basFreightLogistics")
public class BasFreightLogisticsController extends BaseController {

    private final transient IBasFreightLogisticsService basFreightLogisticsService;

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    public R<TableDataInfo<BasFreightLogisticsVo>> page(@RequestBody BasFreightLogisticsQueryBo queryBo) {
        return R.ok(basFreightLogisticsService.customPageList(queryBo));
    }

    /**
     @Operation(summary = "导出")
     @SaCheckPermission("basic:basFreightLogistics:export")
     @Log(title = "基采物流", businessType = BusinessType.EXPORT)
     @PostMapping("/export")
     public void export(@RequestBody BasFreightLogisticsQueryBo queryBo, HttpServletResponse response) {
     List<BasFreightLogisticsVo> list = basFreightLogisticsService.customList(queryBo);
     ExcelUtil.exportExcel(list, "基采物流", BasFreightLogisticsVo.class, response);
     }*/

    @Operation(summary = "新增单条")
    @RepeatSubmit()
    @PostMapping("/create")
    public R<Void> create(@Validated @RequestBody BasFreightLogisticsAddBo addBo) {
        return toAjax(basFreightLogisticsService.insertByBo(addBo));
    }

    @Operation(summary = "修改单条")
    @RepeatSubmit()
    @PostMapping("/update")
    public R<Void> update(@Validated @RequestBody BasFreightLogisticsEditBo editBo) {
        return toAjax(basFreightLogisticsService.updateByBo(editBo));
    }
    @Operation(summary = "修改价格")
    @RepeatSubmit()
    @PostMapping("/updatePrice")
    public R<Void> updatePrice(@Validated @RequestBody BasFreightLogisticsPriceBo editBo) {
        return toAjax(basFreightLogisticsService.updatePriceByBo(editBo));
    }

    @Operation(summary = "修改状态")
    @RepeatSubmit()
    @PostMapping("/updateStatus")
    public R<Void> updateStatus(@Validated @RequestBody BasFreightLogisticsStatusBo statusBo) {
        return toAjax(basFreightLogisticsService.updateStatusByBo(statusBo));
    }


    @Operation(summary = "删除单条")
    @PostMapping("/delete/{id}")
    public R<Void> delete(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return toAjax(basFreightLogisticsService.deleteById(id, true));
    }

}
