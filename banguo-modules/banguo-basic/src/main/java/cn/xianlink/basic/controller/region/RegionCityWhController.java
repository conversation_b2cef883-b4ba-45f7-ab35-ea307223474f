package cn.xianlink.basic.controller.region;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.basic.domain.bo.citywh.CityWhFuzzyQueryBo;
import cn.xianlink.basic.domain.bo.citywh.CityWhPlaceQueryBo;
import cn.xianlink.basic.domain.vo.BasPubVo;
import cn.xianlink.basic.domain.vo.citywh.CityWhVo;
import cn.xianlink.basic.service.ICityWhPlaceService;
import cn.xianlink.basic.service.ICityWhService;
import cn.xianlink.basic.service.IWhNexusService;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.system.api.RemoteUserService;
import cn.xianlink.system.api.domain.bo.RemoteUserBo;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 城市仓
 *
 * <AUTHOR>
 * @date 2024-03-21
 * @folder 总仓助手(小程序)/城市仓
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/region/cityWh")
public class RegionCityWhController extends BaseController {
    private final transient IWhNexusService whNexusService;
    private final transient ICityWhPlaceService cityWhPlaceService;
    private final transient ICityWhService cityWhService;
    @DubboReference
    private final transient RemoteUserService remoteUserService;

    /**
     * 查询-详情
     *
     * @return 城市仓详情
     */
    // @SaCheckPermission("basic:admin:cityWh:info")
    @GetMapping("/info/{id}")
    public R<CityWhVo> info(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        CityWhVo vo = cityWhService.selectAndCheckNullById(id);
        vo.setWhNexusList(whNexusService.customListByCityWhId(vo.getId()));

        Map<String, RemoteUserBo> userMap = remoteUserService.getUsersByUserCodes(ListUtil.toList(vo.getAdminCode()));
        vo.setAdminName(userMap.getOrDefault(vo.getAdminCode(), new RemoteUserBo()).getRealName());
        vo.setAdminPhone(userMap.getOrDefault(vo.getAdminCode(), new RemoteUserBo()).getPhoneNo());
        return R.ok(vo);
    }

    /**
     * 查询-城市仓列表
     *
     */
    @PostMapping("/fuzzyPage")
    public R<TableDataInfo<BasPubVo>> fuzzyPage(@RequestBody CityWhFuzzyQueryBo bo) {
        if (ObjectUtil.isNull(bo.getRegionWhId())) {
            return R.warn("总仓id不能为空");
        }
        List<BasPubVo> list = whNexusService.fuzzyPage(bo).getRows().stream().map(e -> {
            BasPubVo vo = BeanUtils.instantiateClass(BasPubVo.class);
            vo.setId(e.getCityWhId());
            vo.setCode(e.getCityWhCode());
            vo.setName(e.getCityWhName());
            return vo;
        }).collect(Collectors.toList());

        TableDataInfo<BasPubVo> table = new TableDataInfo<>();
        table.setTotal(list.size());
        table.setRows(list);
        return R.ok(table);
    }

    /**
     * 查询-提货点列表
     *
     */
    @PostMapping("/placeFuzzyPage")
    public R<TableDataInfo<BasPubVo>> placeFuzzyPage(@RequestBody CityWhFuzzyQueryBo bo) {
        CityWhPlaceQueryBo queryBo = new CityWhPlaceQueryBo();
        BeanUtils.copyProperties(bo, queryBo);
        queryBo.setPlaceName(bo.getName());

        List<BasPubVo> list = cityWhPlaceService.pageList(queryBo).getRows().stream().map(e -> {
            BasPubVo vo = BeanUtils.instantiateClass(BasPubVo.class);
            vo.setId(e.getId());
            vo.setCode(e.getPlaceCode());
            vo.setName(e.getPlaceName());
            return vo;
        }).collect(Collectors.toList());

        TableDataInfo<BasPubVo> table = new TableDataInfo<>();
        table.setTotal(list.size());
        table.setRows(list);
        return R.ok(table);
    }
}