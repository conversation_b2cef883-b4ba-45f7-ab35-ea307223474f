package cn.xianlink.basic.controller.city;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.xianlink.basic.domain.user.CitySubUserListVo;
import cn.xianlink.basic.domain.user.CityUserCreateForm;
import cn.xianlink.basic.domain.user.CityUserUpdateForm;
import cn.xianlink.basic.domain.vo.citywh.CityWhUserPlaceVo;
import cn.xianlink.basic.domain.vo.citywh.CityWhVo;
import cn.xianlink.basic.service.ICityWhPlaceService;
import cn.xianlink.basic.service.ICityWhService;
import cn.xianlink.common.api.bo.UserRelation;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.domain.page.RemoteTableData;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.system.api.RemoteSubUserService;
import cn.xianlink.system.api.domain.bo.RemoteResourceBindBo;
import cn.xianlink.system.api.domain.user.RemoteSubListQuery;
import cn.xianlink.system.api.domain.user.RemoteSubListVo;
import cn.xianlink.system.api.domain.user.RemoteSubUserUpdateForm;
import cn.xianlink.system.api.domain.user.RemoteUserForm;
import cn.xianlink.system.api.model.LoginUser;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 城市仓子用户管理
 * <AUTHOR> xiaodaibing on 2024-11-20 15:06
 * @folder 城市仓端(小程序)/子用户管理
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/city/subuser")
public class CitySubUserController extends BaseController {

    private final ICityWhPlaceService cityWhPlaceService;
    private final ICityWhService cityWhService;

    @DubboReference
    private RemoteSubUserService remoteSubUserService;

    /**
     * 列表查询
     * <AUTHOR> on 2024/11/20:17:46
     * @param query
     * @return cn.xianlink.common.core.domain.R<cn.xianlink.common.mybatis.core.page.TableDataInfo<cn.xianlink.basic.domain.user.CitySubUserListVo>>
     */
    @PostMapping("/list")
    public R<TableDataInfo<CitySubUserListVo>> getList(@RequestBody RemoteSubListQuery query) {
        TableDataInfo<CitySubUserListVo> tableDataInfo = new TableDataInfo<>();
        RemoteTableData<RemoteSubListVo> subList =  remoteSubUserService.listSelect(query);
        if (subList == null || CollUtil.isEmpty(subList.getRows())) {
            return R.ok(tableDataInfo);
        }
        List<CitySubUserListVo> cityUserList = new ArrayList<>();
        List<Long> userIds = new ArrayList<>();
        for (RemoteSubListVo row : subList.getRows()) {
            CitySubUserListVo vo = new CitySubUserListVo();
            BeanUtil.copyProperties(row,vo);
            vo.setPhoneNo(DesensitizedUtil.mobilePhone(row.getPhoneNo()));
            cityUserList.add(vo);

            userIds.add(row.getUserId());
        }
        Map<Long, List<CityWhUserPlaceVo>> placeMap = cityWhPlaceService.getPlaceByUser(userIds);
        for (CitySubUserListVo user : cityUserList) {
            List<CityWhUserPlaceVo> list = placeMap.get(user.getUserId());
            if (CollUtil.isNotEmpty(list)) {
                user.setPlaceNames(list.stream().map(CityWhUserPlaceVo::getPlaceName).toList());
                user.setPlaceIds(list.stream().map(CityWhUserPlaceVo::getPlaceId).toList());
            }
        }
        tableDataInfo.setTotal(subList.getTotal());
        tableDataInfo.setRows(cityUserList);
        return R.ok(tableDataInfo);
    }


    /**
     * 创建
     * <AUTHOR> on 2024/11/20:18:33
     * @param form
     * @return cn.xianlink.common.core.domain.R<java.lang.Void>
     */
    @GlobalTransactional
    @PostMapping("/create")
    public R<Void> create(@RequestBody CityUserCreateForm form) {
        RemoteUserForm userForm = new RemoteUserForm();
        BeanUtil.copyProperties(form, userForm);
        Long userId = remoteSubUserService.create(userForm);
        cityWhPlaceService.bindingUser(userId, form.getPlaceIds());

        // 自动创建 pc管理台用户
        LoginUser loginUser = LoginHelper.getLoginUser();
        Long cityWhId = loginUser.getRelationId();
        Long orgId = loginUser.getOrgId();
        CityWhVo cityWhVo = cityWhService.selectAndCheckNullById(cityWhId);
        RemoteResourceBindBo bindBo = new RemoteResourceBindBo().setResourceCode(UserRelation.CITY_WH_ID)
                .setItemCode(cityWhVo.getId().toString()).setItemName(cityWhVo.getCityWhName());
        cityWhService.autoCreateSysUser(form.getPhoneNo(), form.getRealName(), orgId, bindBo);
        return R.ok();
    }

    /**
     * 更新
     * <AUTHOR> on 2024/11/20:18:33
     * @param form
     * @return cn.xianlink.common.core.domain.R<java.lang.Void>
     */
    @GlobalTransactional
    @PostMapping("/update")
    public R<Void> update(@RequestBody CityUserUpdateForm form) {
        cityWhPlaceService.bindingUser(form.getUserId(), form.getPlaceIds());
        RemoteSubUserUpdateForm userForm = new RemoteSubUserUpdateForm();
        BeanUtil.copyProperties(form, userForm);
        remoteSubUserService.update(userForm);
        //如果修改了这个用户的信息，踢这个用户下线
        StpUtil.logout(LoginHelper.getLoginUser().getLoginId(form.getUserId()));
        return R.ok();
    }


    /**
     * 删除
     * <AUTHOR> on 2024/11/20:18:33
     * @param userId
     * @return cn.xianlink.common.core.domain.R<java.lang.Void>
     */
    @GlobalTransactional
    @PostMapping("/remove/{userId}")
    public R<Void> remove(@PathVariable Long userId) {
        cityWhPlaceService.bindingUser(userId, null);
        remoteSubUserService.remove(userId);
        //如果移除了这个用户，踢这个用户下线
        StpUtil.logout(LoginHelper.getLoginUser().getLoginId(userId));
        return R.ok();
    }

}