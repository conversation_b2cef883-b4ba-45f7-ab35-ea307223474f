package cn.xianlink.basic.controller.admin;

import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.basic.domain.bo.regionlog.BasRegionLogisticsRegionQueryBo;
import cn.xianlink.basic.domain.bo.regionlog.BasRegionLogisticsRmQueryBo;
import cn.xianlink.basic.domain.vo.regionlog.BasRegionLogisticsVo;
import cn.xianlink.basic.domain.vo.regionwh.RegionWhVo;
import cn.xianlink.basic.service.IBasRegionLogisticsService;
import cn.xianlink.basic.service.IRegionWhService;
import com.alibaba.nacos.common.utils.CollectionUtils;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
@CustomLog
public class BasRegionLogisticsUtils {

    private final transient IBasRegionLogisticsService basRegionLogisticsService;
    private final transient IRegionWhService regionWhService;


    public List<BasRegionLogisticsVo> logisticsList(BasRegionLogisticsRegionQueryBo queryBo) {
        Long regionWhId = queryBo.getRegionWhId();
        if (StringUtils.isNotBlank(queryBo.getRegionWhCode())) {
            List<RegionWhVo> vos = regionWhService.selectListByStatus1(0L).stream()
                    .filter(vo -> queryBo.getRegionWhCode().equals(vo.getRegionWhCode())).toList();
            if (vos.size() > 0) {
                regionWhId = regionWhId == null || regionWhId.equals(vos.get(0).getId()) ? vos.get(0).getId() : -1;
            }
        }
        List<BasRegionLogisticsVo> vos = basRegionLogisticsService.queryList(new BasRegionLogisticsRmQueryBo()
                .setRegionWhId(regionWhId).setCityWhId(queryBo.getCityWhId()).setPriceMode(queryBo.getPriceMode())
                .setLogisticsName(queryBo.getLogisticsName()).setParentPlaceId(queryBo.getParentPlaceId()).setPlaceIds(queryBo.getPlaceIds())
                .setLogisticsType(queryBo.getLogisticsType()).setIsDisplay(1).setAllStatus(false));
        if (ObjectUtil.isNotEmpty(vos)) {
            List<Long> regionWhIds = vos.stream().map(BasRegionLogisticsVo::getRegionWhId).filter(vo -> vo != null && vo != 0L).distinct().toList();
            if (CollectionUtils.isNotEmpty(regionWhIds)) {
                List<RegionWhVo> regionWhVos = regionWhService.selectRegionWhInfoByIds(regionWhIds);
                Map<Long, RegionWhVo> map = regionWhVos.stream().collect(Collectors.toMap(RegionWhVo::getId, u -> u, (n1, n2) -> n1));
                for (BasRegionLogisticsVo vo : vos) {
                    RegionWhVo whVo = map.get(vo.getRegionWhId());
                    if (whVo != null) {
                        vo.setRegionWhName(whVo.getRegionWhName());
                        vo.setRegionWhCode(whVo.getRegionWhCode());
                    }
                }
            }
        }
        log.keyword("查询总仓物流信息").info("查询总仓物流信息vos:{}", vos);
        return queryBo.getMaxSize() != null && vos.size() > queryBo.getMaxSize() ? vos.subList(0, queryBo.getMaxSize()): vos;
    }
}
