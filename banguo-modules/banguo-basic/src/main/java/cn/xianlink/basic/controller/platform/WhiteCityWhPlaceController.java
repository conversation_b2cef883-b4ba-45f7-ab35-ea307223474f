package cn.xianlink.basic.controller.platform;

import cn.hutool.core.collection.CollectionUtil;
import cn.xianlink.basic.domain.vo.citywh.*;
import cn.xianlink.basic.domain.vo.regionwh.RegionWhVo;
import cn.xianlink.basic.domain.vo.regionwh.RegionWhVoApplet;
import cn.xianlink.basic.service.ICityWhPlaceService;
import cn.xianlink.basic.service.ICityWhService;
import cn.xianlink.basic.service.IRegionWhService;
import cn.xianlink.basic.service.IWhNexusService;
import cn.xianlink.basic.util.GeoCalculatorUtil;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.enums.StatusEnum;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.system.api.RemoteClientService;
import cn.xianlink.system.api.RemoteMiniAppService;
import cn.xianlink.system.api.RemoteUserService;
import cn.xianlink.system.api.domain.bo.RemoteMiniAuthBo;
import cn.xianlink.system.api.domain.vo.RemoteClientVo;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 提货点查询
 *
 * <AUTHOR>
 * @folder 采集平台(小程序)/提货点
 */
@Validated
@RequiredArgsConstructor
@RestController("PlatformWhiteCityWhPlaceController")
@RequestMapping("/platform/white/cityWhPlace")
public class WhiteCityWhPlaceController extends BaseController {
    private final transient ICityWhService cityWhService;
    private final transient IRegionWhService regionWhService;
    private final transient IWhNexusService whNexusService;
    private final transient ICityWhPlaceService cityWhPlaceService;
    private final transient PlaceVoAppletUtil placeVoAppletUtil;
    @DubboReference
    private final transient RemoteMiniAppService remoteMiniAppService;
    @DubboReference
    private final transient RemoteClientService remoteClientService;
    @DubboReference
    private final transient RemoteUserService remoteUserService;

    /**
     * 查询-城市列表 【停用】
     *
     * @param lng 经度
     * @param lat 纬度
     */
    @GetMapping(value = "/queryCityWhList")
    public R<List<CityWhVoApplet>> queryCityWhList(@NotNull(message = "坐标无效") @RequestParam(name = "lng") Double lng,
                                                   @NotNull(message = "坐标无效") @RequestParam(name = "lat") Double lat) {
        // 查询全部城市仓，并计算距离
        List<CityWhVoApplet> list = cityWhService.selectListByStatus1().stream().map(e -> {
            CityWhVoApplet voApplet = MapstructUtils.convert(e, CityWhVoApplet.class);
            voApplet.setDistance((int) GeoCalculatorUtil.calculateDistance(lat, lng, Double.parseDouble(e.getLat()), Double.parseDouble(e.getLng())));
            return voApplet;
        }).collect(Collectors.toList());
        if (list.size() == 0) {
            return R.ok();
        }
        // 查询全部总仓信息
        Map<Long, RegionWhVo> regionWhMap = regionWhService.selectListByStatus1(0L).stream().collect(Collectors.toMap(RegionWhVo::getId, Function.identity(), (key1, key2) -> key2));
        // 根据距离排序
        list.sort(Comparator.comparingInt(CityWhVoApplet::getDistance));
        // 截取前10
        list = list.subList(0, Math.min(list.size(), 10));
        // 查询城市仓关联的总仓
        Map<Long, List<WhNexusVo>> regionWhListMap = whNexusService.selectListByCityWhId(list.stream().map(CityWhVoApplet::getId).collect(Collectors.toList()))
                .stream().collect(Collectors.groupingBy(WhNexusVo::getCityWhId));
        // 加载城市仓途径总仓
        list.forEach(e-> {
            if (CollectionUtil.isNotEmpty(regionWhListMap.get(e.getId()))) {
                List<RegionWhVo> routeRegionWhList = regionWhListMap.get(e.getId()).stream().map(re -> regionWhMap.get(re.getRegionWhId())).collect(Collectors.toList());
                e.setRouteRegionWhList(MapstructUtils.convert(routeRegionWhList, RegionWhVoApplet.class));
            }
        });
        return R.ok(list);
    }

    /**
     * 查询-提货点列表 【停用】
     *
     * @param cityWhId 城市仓id
     * @param lng      经度
     * @param lat      纬度
     */
    @GetMapping(value = "/queryPlaceList")
    public R<List<CityWhPlaceVoApplet>> queryPlaceList(@NotNull(message = "城市仓id为空") @RequestParam(name = "cityWhId") Long cityWhId,
                                                       @NotNull(message = "坐标无效") @RequestParam(name = "lng") Double lng,
                                                       @NotNull(message = "坐标无效") @RequestParam(name = "lat") Double lat) {
        List<CityWhPlaceVoApplet> list = placeVoAppletUtil.getPlaceVoAppletList(cityWhId, lng, lat);
        // 根据距离排序
        list.sort(Comparator.comparingInt(CityWhPlaceVoApplet::getDistance));
        return R.ok(list);
    }

    /**
     * 查询-提货点列表
     *
     * @param positionTitle 提货点名称
     * @param lng      经度
     * @param lat      纬度
     */
    @GetMapping(value = "/queryPlaceListByTitle")
    public R<List<CityWhPlaceVoApplet>> queryPlaceListByTitle(@NotNull(message = "提货点名称为空") @RequestParam(name = "positionTitle") String positionTitle,
                                                       @RequestParam(name = "lng", required = false) Double lng,
                                                       @RequestParam(name = "lat", required = false) Double lat) {
        List<CityWhPlaceVoApplet> list = placeVoAppletUtil.getPlaceVoAppletList(positionTitle, lng, lat);
        if (lng != null && lat != null) {
            // 根据距离排序
            list.sort(Comparator.comparingInt(CityWhPlaceVoApplet::getDistance));
        }
        // 截取前10
        list = list.subList(0, Math.min(list.size(), 15));
        return R.ok(list);
    }

    /**
     * 查询-提货点详情
     */
    @GetMapping(value = "/queryPlaceById")
    public R<CityWhPlaceVoApplet> queryPlaceById(@NotNull(message = "提货点id为空") @RequestParam(name = "placeId") Long placeId) {
        try {
            CityWhPlaceVo placeVo = cityWhPlaceService.selectAndCheckNullById(placeId);
            CityWhVo cityWhVo =  cityWhService.selectAndCheckNullById(placeVo.getCityWhId());
            if (cityWhVo.getStatus().intValue() == StatusEnum.DISABLE.getCode()) {
                return R.ok();
            }
            return R.ok(MapstructUtils.convert(placeVo, CityWhPlaceVoApplet.class));
        } catch (ServiceException se) {
            return R.ok();
        }
    }

    /**
     * 查询-附近提货点列表
     *
     * @param lng  经度
     * @param lat  纬度
     */
    @GetMapping(value = "/queryNearPlaceList")
    public R<List<CityWhPlaceVoApplet>> queryNearPlaceList(@NotNull(message = "坐标无效") @RequestParam(name = "lng") Double lng,
                                                       @NotNull(message = "坐标无效") @RequestParam(name = "lat") Double lat) {
        return R.ok(MapstructUtils.convert(cityWhPlaceService.queryNearPlaceListByLocation(lng, lat), CityWhPlaceVoApplet.class));
    }

    /**
     * 查询-常用-提货点
     *
     * @param wxCode 微信code, 用于获取openid
     * @param clientId 客户端ID
     */
    @GetMapping(value = "/queryUsedPlace")
    public R<List<CityWhPlaceVoApplet>> queryUsedPlaceByWxCode(@NotBlank(message = "wxCode参数为空") @RequestParam(name = "wxCode") String wxCode,
                                                               @NotBlank(message = "clientid参数为空") @RequestParam(name = "clientId") String clientId,
                                                               @RequestParam(name = "lng", required = false) Double lng,
                                                               @RequestParam(name = "lat", required = false) Double lat) {
        RemoteClientVo clientVo = remoteClientService.getClientInfo(clientId);
        RemoteMiniAuthBo authBo = remoteMiniAppService.auth(wxCode, clientVo.getDeviceId());
        if(authBo == null || authBo.getUserId() == null) {
            return R.ok();
        }
        Long customerId = remoteUserService.getUserRelation(authBo.getUserId());
        return R.ok(placeVoAppletUtil.getUsedPlaceVoAppletList(customerId, lng, lat));
    }

}
