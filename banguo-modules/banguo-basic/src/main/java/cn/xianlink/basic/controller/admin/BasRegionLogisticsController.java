package cn.xianlink.basic.controller.admin;

import cn.xianlink.basic.domain.bo.regionlog.*;
import cn.xianlink.basic.domain.vo.regionlog.BasRegionLogisticsSimpleVo;
import cn.xianlink.basic.domain.vo.regionlog.BasRegionLogisticsVo;
import cn.xianlink.basic.service.IBasRegionLogisticsService;
import cn.xianlink.common.api.enums.SSDSEnum;
import cn.xianlink.common.api.util.SSDSUtil;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.core.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 基础物流
 * <AUTHOR>
 * @date 2024-03-21
 * @folder 般果管理中心/物流/基础物流
 */
@Tag(name = "基础物流")
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/basRegionLogistics")
public class BasRegionLogisticsController extends BaseController {

    private final transient IBasRegionLogisticsService basRegionLogisticsService;
    private final transient BasRegionLogisticsUtils basRegionLogisticsUtils;

    @Operation(summary = "分页查询")
    // @SaCheckPermission("basic:basRegionLogistics:list")
    @PostMapping("/page")
    public R<TableDataInfo<BasRegionLogisticsVo>> page(@RequestBody BasRegionLogisticsQueryBo queryBo) {
        return R.ok(basRegionLogisticsService.customPageList(queryBo));
    }

    @Operation(summary = "分页查询")
    // @SaCheckPermission("basic:basRegionLogistics:list")
    @PostMapping("/authority_page")
    public R<TableDataInfo<BasRegionLogisticsVo>> authorityPage(@RequestBody BasRegionLogisticsQueryBo queryBo) {
        List<Long> cityWhIds = SSDSUtil.getAuthorizeDate(SSDSEnum.CITY_WH_ID.getCode());
        List<Long> regionWhIds = SSDSUtil.getAuthorizeDate(SSDSEnum.REGION_WH_ID.getCode());
        queryBo.setCityWhIds(cityWhIds);
        queryBo.setRegionWhIds(regionWhIds);
        return R.ok(basRegionLogisticsService.customPageList(queryBo));
    }

    /**
    @Operation(summary = "导出")
    @SaCheckPermission("basic:basRegionLogistics:export")
    @Log(title = "基础物流", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(@RequestBody BasRegionLogisticsQueryBo queryBo, HttpServletResponse response) {
        List<BasRegionLogisticsVo> list = basRegionLogisticsService.customList(queryBo);
        ExcelUtil.exportExcel(list, "基础物流", BasRegionLogisticsVo.class, response);
    }*/

    @Operation(summary = "单条查询")
    // @SaCheckPermission("basic:basRegionLogistics:list")
    @GetMapping("/{id}")
    public R<BasRegionLogisticsVo> getInfo(@NotNull(message = "主键不能为空")
                                           @PathVariable Long id) {
        return R.ok(basRegionLogisticsService.queryById(id));
    }

    @Operation(summary = "新增单条")
    // @SaCheckPermission("basic:basRegionLogistics:create")
    // @Log(title = "基础物流", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/create")
    public R<Void> create(@Validated @RequestBody BasRegionLogisticsAddBo addBo) {
        return toAjax(basRegionLogisticsService.insertByBo(addBo));
    }

    @Operation(summary = "修改单条")
    // @SaCheckPermission("basic:basRegionLogistics:update")
    // @Log(title = "基础物流", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/update")
    public R<Void> update(@Validated @RequestBody BasRegionLogisticsEditBo editBo) {
        return toAjax(basRegionLogisticsService.updateByBo(editBo));
    }
    @Operation(summary = "修改市采价格")
    // @SaCheckPermission("basic:basRegionLogistics:update")
    // @Log(title = "基础物流", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/updateCityPrice")
    public R<Void> updateCityPrice(@Validated @RequestBody BasRegionLogisticsEditCityPriceBo editBo) {
        return toAjax(basRegionLogisticsService.updateCityPriceByBo(editBo));
    }

    @Operation(summary = "修改状态")
    // @SaCheckPermission("basic:basRegionLogistics:update")
    // @Log(title = "基础物流", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/updateStatus")
    public R<Void> updateStatus(@Validated @RequestBody BasRegionLogisticsStatusBo statusBo) {
        return toAjax(basRegionLogisticsService.updateStatusByBo(statusBo));
    }

    /**
    @Operation(summary = "删除单条")
    @SaCheckPermission("basic:basRegionLogistics:delete")
    @Log(title = "基础物流", businessType = BusinessType.DELETE)
    @PostMapping("/delete/{id}")
    public R<Void> delete(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return toAjax(basRegionLogisticsService.deleteById(id, true));
    }*/


    @Operation(summary = "装车位物流查询（排除被占用的物流）")
    @PostMapping("/parking")
    public R<List<BasRegionLogisticsSimpleVo>> parking(@Validated @RequestBody BasRegionLogisticsParkBo parkBo) {
        List<BasRegionLogisticsVo> vos = basRegionLogisticsService.queryParkRegionLogistics(parkBo);
        return R.ok(MapstructUtils.convert(vos, BasRegionLogisticsSimpleVo.class));
    }

    @Operation(summary = "总仓物流线查询（支持模糊名称)")
    @PostMapping("/logisticsList")
    public R<List<BasRegionLogisticsSimpleVo>> logisticsList(@Validated @RequestBody BasRegionLogisticsRegionQueryBo queryBo) {
        return R.ok(MapstructUtils.convert(basRegionLogisticsUtils.logisticsList(queryBo), BasRegionLogisticsSimpleVo.class));
    }
}
