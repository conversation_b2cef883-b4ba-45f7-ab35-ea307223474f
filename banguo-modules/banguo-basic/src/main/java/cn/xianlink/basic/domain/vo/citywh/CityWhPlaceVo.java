package cn.xianlink.basic.domain.vo.citywh;

import cn.xianlink.basic.domain.CityWhPlace;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * city_wh_place，城市仓提货点表
 *
 * <AUTHOR>
 * @date 2024-03-21
 */
@Data
@AutoMapper(target = CityWhPlace.class)
public class CityWhPlaceVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 城市仓提货点id，主键
     */
    private Long id;
    /**
     * 城市仓id，主键
     */
    private Long cityWhId;
    /**
     * 城市仓编码[冗余]
     */
    private String cityWhCode;
    /**
     * 城市仓名称[冗余]
     */
    private String cityWhName;
    /**
     * 提货点编码
     */
    private String placeCode;
    /**
     * 提货点名称
     */
    private String placeName;
    /**
     * 详细地址
     */
    private String address;
    /**
     * 经度
     */
    private String lng;
    /**
     * 纬度
     */
    private String lat;
    /**
     * 位置标题
     */
    private String positionTitle;
    /**
     * 联系人姓名
     */
    private String contactName;
    /**
     * 联系人电话
     */
    private String contactPhone;
    /**
     * 备注
     */
    private String remark;
    /**
     * 修改人
     */
    private String updateName;
    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 子提货点个数
     */
    private Integer childCount;
    /**
     * 父级提货点id
     */
    private Long parentPlaceId;
    /**
     * 父级提货点名称
     */
    private String parentPlaceCode;
    /**
     * 父级提货点名称
     */
    private String parentPlaceName;
    /**
     * 提货点等级； 1一级提货点，2二级提货点
     */
    private Integer placeLevel;
    public Integer getPlaceLevel() {
        return parentPlaceId == null ? 1 : 2;
    }

    /**
     * path地址；/parentId/id
     */
    private String path;

    /**
     * 子提货点名称列表，“；”分号间隔
     */
    private String sonPlaceNames;


    /**
     * 子提货点物流名称
     */
    private Long logisticsId;
    /**
     * 子提货点物流名称
     */
    private String logisticsName;
    /**
     * 子提货点基准运费单价
     */
    private BigDecimal freightPrice;
    /**
     * 子提货点运费计价方式；0按件，1按斤。默认1
     */
    private Integer priceMode;

    /**
     * 距离，单位：米
     */
    private Integer distance;

}
