package cn.xianlink.basic.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.basic.api.domain.bo.RemoteBasImAfterSalesBo;
import cn.xianlink.basic.api.domain.vo.RemoteBasImUserVo;
import cn.xianlink.basic.api.domain.vo.RemoteSupplierVo;
import cn.xianlink.basic.api.enums.ImAfterSalesBizTypeEnum;
import cn.xianlink.basic.domain.BasImAfterSales;
import cn.xianlink.basic.domain.bo.BasImAfterSalesBo;
import cn.xianlink.basic.domain.vo.BasImAfterSalesVo;
import cn.xianlink.basic.domain.vo.citywh.CityWhVo;
import cn.xianlink.basic.mapper.BasImAfterSalesMapper;
import cn.xianlink.basic.service.IBasImAfterSalesService;
import cn.xianlink.basic.service.IBasSupplierService;
import cn.xianlink.basic.service.ICityWhService;
import cn.xianlink.common.api.constant.BanguoCommonConstant;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.system.api.RemoteUserService;
import cn.xianlink.system.api.domain.bo.RemoteUserBo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jodd.util.StringUtil;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


/**
 * IM售后服务客服配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@RequiredArgsConstructor
@Service
@CustomLog
public class BasImAfterSalesServiceImpl implements IBasImAfterSalesService {

    private final BasImAfterSalesMapper baseMapper;
    private final IBasSupplierService supplierService;
    @Lazy
    @Autowired
    private ICityWhService cityWhService;
    @DubboReference
    private final RemoteUserService remoteUserService;
    @DubboReference
    private final RemoteUserService userService;

    /**
     * 查询IM售后服务客服配置
     */
    @Override
    public BasImAfterSalesVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询IM售后服务客服配置列表
     */
    @Override
    public TableDataInfo<BasImAfterSalesVo> queryPageList(BasImAfterSalesBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<BasImAfterSales> lqw = buildQueryWrapper(bo);
        Page<BasImAfterSalesVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询IM售后服务客服配置列表
     */
    @Override
    public List<BasImAfterSalesVo> queryList(BasImAfterSalesBo bo) {
        LambdaQueryWrapper<BasImAfterSales> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<BasImAfterSales> buildQueryWrapper(BasImAfterSalesBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BasImAfterSales> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getBizId() != null, BasImAfterSales::getBizId, bo.getBizId());
        lqw.eq(StringUtils.isNotBlank(bo.getBizType()), BasImAfterSales::getBizType, bo.getBizType());
        lqw.eq(StringUtils.isNotBlank(bo.getBizName()), BasImAfterSales::getBizName, bo.getBizName());
        lqw.eq(StringUtils.isNotBlank(bo.getUserCode()), BasImAfterSales::getUserCode, bo.getUserCode());
        lqw.eq(StringUtils.isNotBlank(bo.getUserName()), BasImAfterSales::getUserName, bo.getUserName());
        lqw.eq(bo.getStatus() != null, BasImAfterSales::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增IM售后服务客服配置
     */
    @Override
    public Boolean insertByBo(BasImAfterSalesBo bo) {
        BasImAfterSales add = MapstructUtils.convert(bo, BasImAfterSales.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改IM售后服务客服配置
     */
    @Override
    public Boolean updateByBo(BasImAfterSalesBo bo) {
        BasImAfterSales update = MapstructUtils.convert(bo, BasImAfterSales.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BasImAfterSales entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除IM售后服务客服配置
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 查询IM售后服务客服配置列表
     */
    @Override
    public BasImAfterSalesVo queryByBizIdAndBizType(Long bizId, String bizType) {
        LambdaQueryWrapper<BasImAfterSales> lqw = Wrappers.lambdaQuery();
        lqw.eq(BasImAfterSales::getBizId, bizId)
                .eq(BasImAfterSales::getBizType, bizType)
                .eq(BasImAfterSales::getDelFlag, BanguoCommonConstant.notDelFlag);
        List<BasImAfterSalesVo> list = baseMapper.selectVoList(lqw);
        return list.isEmpty() ? null : list.get(0);
    }

    /**
     * 查询IM售后服务客服配置列表
     */
    @Override
    public List<BasImAfterSalesVo> queryByBizIdAndBizTypeMap(Map<String, Long> bizIdTypeMap) {
        if (MapUtil.isEmpty(bizIdTypeMap)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<BasImAfterSales> lqw = Wrappers.lambdaQuery();
        //lqw.eq(BasImAfterSales::getDelFlag, BanguoCommonConstant.notDelFlag);
        // 构建每个 (bizId, bizType) 对应的 OR 条件
        bizIdTypeMap.forEach((bizType, bizId) -> {
            lqw.or(wrapper -> wrapper
                    .eq(BasImAfterSales::getBizId, bizId)
                    .eq(BasImAfterSales::getBizType, bizType));
        });
        return baseMapper.selectVoList(lqw);
    }


    /**
     * 新增IM售后服务客服配置
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean insertByBo(RemoteBasImAfterSalesBo bo) {
        validInsertByBo(bo);
        // 查询现有有效记录
        LambdaQueryWrapper<BasImAfterSales> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BasImAfterSales::getBizId, bo.getBizId())
                .eq(BasImAfterSales::getBizType, bo.getBizType())
                .eq(BasImAfterSales::getDelFlag, BanguoCommonConstant.notDelFlag);
        List<BasImAfterSales> list = baseMapper.selectList(wrapper);

        if (CollectionUtil.isNotEmpty(list)) {
            baseMapper.deleteBatchIds(list);
        }
        if(ObjectUtil.isNotNull(bo.getUserCode())
                && ObjectUtil.isNotNull(bo.getUserName())
                && ObjectUtil.isNotNull(bo.getBizName())) {
            // 新增记录
            BasImAfterSales add = new BasImAfterSales();
            BeanUtils.copyProperties(bo, add);
            add.setDelFlag(BanguoCommonConstant.notDelFlag);
            baseMapper.insert(add);
            // 更新用户是否导入im标识
            remoteUserService.updateUserIsImportIm(add.getUserCode(), 0);
        }
        return Boolean.TRUE;
    }

    /**
     * 批量删除IM售后服务客服配置
     * @param bizTypeList
     * @param userCode
     * @return
     */
    @Override
    public Boolean deleteByParm(List<String> bizTypeList, String userCode, Long bizId) {
        return baseMapper.delete(Wrappers.lambdaUpdate(BasImAfterSales.class)
                .in(CollectionUtil.isNotEmpty(bizTypeList), BasImAfterSales::getBizType, bizTypeList)
                .eq(ObjectUtil.isNotEmpty(bizId), BasImAfterSales::getBizId, bizId)
                .eq(BasImAfterSales::getUserCode, userCode)
                .eq(BasImAfterSales::getDelFlag, BanguoCommonConstant.notDelFlag)) > 0;
    }

    /**
     * 批量删除IM售后服务客服配置
     * @return
     */
    @Override
    public Boolean deleteByBiz(String bizType, List<Long> bizIdList) {
        return baseMapper.delete(Wrappers.lambdaUpdate(BasImAfterSales.class)
                .eq(BasImAfterSales::getBizType, bizType)
                .in(BasImAfterSales::getBizId, bizIdList)
                .eq(BasImAfterSales::getDelFlag, BanguoCommonConstant.notDelFlag)) > 0;
    }

    /**
     *  获取im售后客服
     */
    @Override
    public List<RemoteBasImUserVo> queryImUserList(Map<String, Long> bizMap) {
        Map<String, Long> bizIdTypeMap = new HashMap<>();
        Map<ImAfterSalesBizTypeEnum, Long> tempMap = new HashMap<>();

        // 城市仓相关
        addIfPresent(tempMap, ImAfterSalesBizTypeEnum.PLACE2, bizMap.get(ImAfterSalesBizTypeEnum.PLACE2.getType()));
        addIfPresent(tempMap, ImAfterSalesBizTypeEnum.PLACE1, bizMap.get(ImAfterSalesBizTypeEnum.PLACE1.getType()));
        addIfPresent(tempMap, ImAfterSalesBizTypeEnum.CITY, bizMap.get(ImAfterSalesBizTypeEnum.CITY.getType()));

        // 供应商相关
        addIfPresent(tempMap, ImAfterSalesBizTypeEnum.DEPT, bizMap.get(ImAfterSalesBizTypeEnum.DEPT.getType()));
        addIfPresent(tempMap, ImAfterSalesBizTypeEnum.SUPPLIER, bizMap.get(ImAfterSalesBizTypeEnum.SUPPLIER.getType()));

        // 总仓相关
        addIfPresent(tempMap, ImAfterSalesBizTypeEnum.REGION, bizMap.get(ImAfterSalesBizTypeEnum.REGION.getType()));

        // 构建 bizType -> bizId 映射
        tempMap.forEach((type, id) -> bizIdTypeMap.put(type.getType(), id));

        List<BasImAfterSalesVo> afterSalesVoList = queryByBizIdAndBizTypeMap(bizIdTypeMap);

        Map<String, BasImAfterSalesVo> map = afterSalesVoList.stream()
                .collect(Collectors.toMap(
                        item -> item.getBizType() + "-" + item.getBizId(),
                        item -> item,
                        (v1, v2) -> v1));

        List<RemoteBasImUserVo> userList = new ArrayList<>();
        //userList.add(buildImUser(user.getUserCode(), user.getRealName())); // 客户

        // 添加城市仓售后人员
        String place2Biz = buildBizKey(ImAfterSalesBizTypeEnum.PLACE2, bizMap.get(ImAfterSalesBizTypeEnum.PLACE2.getType()));
        String place1Biz = buildBizKey(ImAfterSalesBizTypeEnum.PLACE1, bizMap.get(ImAfterSalesBizTypeEnum.PLACE1.getType()));
        String cityWhBiz = buildBizKey(ImAfterSalesBizTypeEnum.CITY, bizMap.get(ImAfterSalesBizTypeEnum.CITY.getType()));

        if (map.containsKey(place2Biz)) {
            addUserFromMap(map, place2Biz, userList);
        } else if (map.containsKey(place1Biz)) {
            addUserFromMap(map, place1Biz, userList);
        } /*else if ((bizMap.getOrDefault(ImAfterSalesBizTypeEnum.PLACE2.getType(), 0L) != 0L //没2级提货点售后人员，没1提货点售后人员 则添加城市仓管理员
                || bizMap.getOrDefault(ImAfterSalesBizTypeEnum.PLACE1.getType(), 0L) != 0L)
                && ObjectUtil.isNotNull(bizMap.get(ImAfterSalesBizTypeEnum.CITY.getType()))) {
            CityWhVo cityWh = Optional.ofNullable(cityWhService.selectAndCheckNullById(bizMap.get(ImAfterSalesBizTypeEnum.CITY.getType())))
                    .orElseThrow(() -> new ServiceException("城市仓不存在"));
            // 查询用户表
            RemoteUserBo userBo = Optional.ofNullable(userService.getUserByUserCode(cityWh.getAdminCode()))
                    .orElseThrow(() -> new ServiceException("城市仓超管用户不存在"));
            String name = StringUtil.isNotBlank(userBo.getNickName()) ? userBo.getNickName() : cityWh.getCityWhName();
            userList.add(new RemoteBasImUserVo(cityWh.getAdminCode(), name, ImAfterSalesBizTypeEnum.CITY.getType()));
        }*/ else if (map.containsKey(cityWhBiz)) {
            addUserFromMap(map, cityWhBiz, userList);
        } else {
            if(ObjectUtil.isNotNull(bizMap.get(ImAfterSalesBizTypeEnum.CITY.getType()))) {
                CityWhVo cityWh = Optional.ofNullable(cityWhService.selectAndCheckNullById(bizMap.get(ImAfterSalesBizTypeEnum.CITY.getType())))
                        .orElseThrow(() -> new ServiceException("城市仓不存在"));
                // 查询用户表
                RemoteUserBo userBo = Optional.ofNullable(userService.getUserByUserCode(cityWh.getAdminCode()))
                        .orElseThrow(() -> new ServiceException("城市仓超管用户不存在"));
                String name = StringUtil.isNotBlank(userBo.getRealName()) ? userBo.getRealName() : cityWh.getCityWhName();
                userList.add(new RemoteBasImUserVo(cityWh.getAdminCode(), name, ImAfterSalesBizTypeEnum.CITY.getType()));
            }
        }

        // 添加供应商
        String supplierDeptBiz = buildBizKey(ImAfterSalesBizTypeEnum.DEPT, bizMap.get(ImAfterSalesBizTypeEnum.DEPT.getType()));
        String supplierBiz = buildBizKey(ImAfterSalesBizTypeEnum.SUPPLIER, bizMap.get(ImAfterSalesBizTypeEnum.SUPPLIER.getType()));

        if (map.containsKey(supplierDeptBiz)) {
            addUserFromMap(map, supplierDeptBiz, userList);
        } /*else if (bizMap.getOrDefault(ImAfterSalesBizTypeEnum.DEPT.getType(), 0L) != 0L//没有档口售后人员取城市仓管理员
                && ObjectUtil.isNotNull(bizMap.get(ImAfterSalesBizTypeEnum.SUPPLIER.getType()))) {
            RemoteSupplierVo supplier = Optional.ofNullable(supplierService.querySupplierById(bizMap.get(ImAfterSalesBizTypeEnum.SUPPLIER.getType())))
                    .orElseThrow(() -> new ServiceException("供应商不存在"));
            // 查询用户表
            RemoteUserBo userBo = Optional.ofNullable(userService.getUserByUserCode(supplier.getAdminCode()))
                    .orElseThrow(() -> new ServiceException("供应商超管用户不存在"));
            String supplierName = StringUtil.isNotBlank(userBo.getNickName()) ? userBo.getNickName() : supplier.getAlias();
            *//*String supplierName = StringUtil.isBlank(supplier.getAlias()) ?
                    String.format("商铺：%s", supplier.getSimpleCode()) : supplier.getAlias();*//*
            userList.add(new RemoteBasImUserVo(supplier.getAdminCode(), supplierName, ImAfterSalesBizTypeEnum.SUPPLIER.getType()));
        }*/ else if (map.containsKey(supplierBiz)) {
            addUserFromMap(map, supplierBiz, userList);
        } else {
            if(ObjectUtil.isNotNull(bizMap.get(ImAfterSalesBizTypeEnum.SUPPLIER.getType()))) {
                RemoteSupplierVo supplier = Optional.ofNullable(supplierService.querySupplierById(bizMap.get(ImAfterSalesBizTypeEnum.SUPPLIER.getType())))
                        .orElseThrow(() -> new ServiceException("供应商不存在"));
                // 查询用户表
                RemoteUserBo userBo = Optional.ofNullable(userService.getUserByUserCode(supplier.getAdminCode()))
                        .orElseThrow(() -> new ServiceException("供应商超管用户不存在"));
                String supplierName = StringUtil.isNotBlank(userBo.getRealName()) ? userBo.getRealName() : supplier.getAlias();
                /*String supplierName = StringUtil.isBlank(supplier.getAlias()) ?
                        String.format("商铺：%s", supplier.getSimpleCode()) : supplier.getAlias();*/
                userList.add(new RemoteBasImUserVo(supplier.getAdminCode(), supplierName, ImAfterSalesBizTypeEnum.SUPPLIER.getType()));
            }
        }
        // 添加总仓
        String regionBiz = buildBizKey(ImAfterSalesBizTypeEnum.REGION, bizMap.get(ImAfterSalesBizTypeEnum.REGION.getType()));
        if (map.containsKey(regionBiz)) {
            addUserFromMap(map, regionBiz, userList);
        }
        return userList;
    }

    private void addIfPresent(Map<ImAfterSalesBizTypeEnum, Long> map, ImAfterSalesBizTypeEnum type, Long value) {
        if (ObjectUtil.isNotEmpty(value)) {
            map.put(type, value);
        }
    }

    private String buildBizKey(ImAfterSalesBizTypeEnum type, Long id) {
        return ObjectUtil.isNotEmpty(id) ? type.getType() + "-" + id : "";
    }

    private void addUserFromMap(Map<String, BasImAfterSalesVo> map, String key, List<RemoteBasImUserVo> userList) {
        BasImAfterSalesVo afterSales = map.get(key);
        if (afterSales != null) {
            userList.add(new RemoteBasImUserVo(afterSales.getUserCode(), afterSales.getUserName(), key.split("-")[0]));
        }
    }


    private void validInsertByBo(RemoteBasImAfterSalesBo bo) {
        if (bo == null) {
            throw new ServiceException("传入的参数对象不能为空");
        }
        if (ObjectUtil.isEmpty(bo.getBizId())) {
            throw new ServiceException("业务ID（bizId）不能为空");
        }
        if (StringUtils.isBlank(bo.getBizType())) {
            throw new ServiceException("业务类型（bizType）不能为空");
        }
        if (ObjectUtil.isEmpty(bo.getBizName())) {
            throw new ServiceException("业务名称（bizName）不能为空");
        }
        if (StringUtils.isBlank(bo.getUserName())) {
            throw new ServiceException("用户名称（userName）不能为空");
        }
        if (StringUtils.isBlank(bo.getUserCode())) {
            throw new ServiceException("用户编码（userCode）不能为空");
        }
    }
}
