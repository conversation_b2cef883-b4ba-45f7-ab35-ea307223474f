package cn.xianlink.basic.service;


import cn.xianlink.basic.api.domain.bo.RemoteBasCustomerQueryBo;
import cn.xianlink.basic.api.domain.vo.RemoteCustomerVo;
import cn.xianlink.basic.domain.bo.customer.*;
import cn.xianlink.basic.domain.vo.customer.BasCustomerSimperVo;
import cn.xianlink.basic.domain.vo.customer.BasCustomerVo;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 客户Service接口
 *
 * <AUTHOR>
 * @date 2024-04-09
 */
public interface IBasCustomerService {

    /**
     * 查询客户信息
     *
     * @param id
     */
    BasCustomerVo selectByIdOrCode(Long id, String code);

    List<RemoteCustomerVo> getByIds(List<Long> customerIds);


    /**
     * 查询客户列表
     */
    TableDataInfo<BasCustomerVo> pageList(BasCustomerQueryBo bo, boolean currentIsSales);

    TableDataInfo<BasCustomerSimperVo> miniPageList(CustomerQueryMiniBo bo);

    /**
     * 校验客户名称是否唯一
     *
     * @param name 客户名称
     * @return 结果
     */
    @Deprecated
    boolean checkNameUnique(String name);

    /**
     * 新增客户
     */
    int insertCustomer(BasCustomerAddBo bo);

    void updateCustomer(BasCustomerUpdateBo bo);

    void cityUpdate(CustomerUpdateCityBo bo);

    /**
     * 修改客户
     */
    int updateRemark(Long id, String remark);

    /**
     * 修改状态
     *
     * @param id     客户id
     * @param status 状态，0为不可用；1为可用
     * @return 结果
     */
    int updateStatus(Long id, Integer status);
    /**
     * 修改最新下单时间
     */
    void updateLastOrderTime(Long id, Long cityWhId, Long placeId);
    /**
     * 修改客户城市仓
     */
    void changeCity(Long customerId, Long cityWhId);

    /**
     * 修改售后状态
     *
     * @param id     客户id
     * @param status 状态，0关闭；1开启
     * @return 结果
     */
    int updateAfterSaleStatus(Long id, Integer status);

    /**
     * 开启或关闭用户的售后状态
     * <AUTHOR> on 2024/7/13:16:44
     * @param closeIds
     * @param enableIds
     * @return void
     */
    void enableOrCloseAfterSale(Set<Long> closeIds, Set<Long> enableIds);

    /**
     * 根据客户名称模糊查询客户id
     * @param name
     * @return
     */
    List<Long> getByName(String name);

    /**
     * 统计城市仓销售员的客户数
     *
     * @param cityWhId
     * @param salesmans
     * @return
     */
    Long countCustomer(Long cityWhId, List<String> salesmans,String noSalesman);

    /**
     * 过滤出属于销售员的客户
     *
     * @param customers
     * @param salesmans
     * @return
     */
    List<Long> filterSalesmanCustomer(List<Long> customers, List<String> salesmans,String noSalesman);
    /**
     * 过滤没有销售员的客户
     * @param orderCustomers
     * @return
     */
    List<Long> filterNoSalesmanCustomer(List<Long> orderCustomers);
    /**
     * 获取注册客户
     * @param cityWhId
     * @param startTime
     * @param endTime
     * @return
     */
    Long getRegisterCustomerCount(Long cityWhId,LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据用户code获取客户信息
     * @param userCode
     * @return
     */
    Long getByAdminCode(String userCode);
    List<Long> getListByAdminCode(String userCode);


    /**
     * 记录客户最常下单的SKU
     * <AUTHOR> on 2025/1/13:11:09
     * @param customerMostSkuMap
     * @return void
     */
    void customerMostOrderSku(Map<Long, String> customerMostSkuMap);

    /**
     * 根据手机号获取客户id
     **/
    List<Long> getIdByPhones(List<String> list);

    List<RemoteCustomerVo> findCustomers(String customerName, String customerAlias);

    Long getIdById(Long customerId);

    /**
     * 开启符合标准的客户售后
     */
    Integer openCustomerAfterSale();

    /**
     * 按城市仓汇总查询 客户数
     */
    Map<Long, Integer> getCustomerCountListByCityWhIds(List<Long> cityWhIds, LocalDate createDate);

    TableDataInfo<BasCustomerVo> findCustomerPage(RemoteBasCustomerQueryBo bo);

    /**
     * 根据用户code获取客户信息
     * @param userCode
     * @return
     */
    RemoteCustomerVo getCustomerByAdminCode(String userCode);
}