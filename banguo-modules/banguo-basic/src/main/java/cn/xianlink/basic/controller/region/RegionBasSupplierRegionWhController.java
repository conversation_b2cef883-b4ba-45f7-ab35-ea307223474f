package cn.xianlink.basic.controller.region;

import cn.xianlink.basic.domain.bo.supplier.BasSupplierAuditBo;
import cn.xianlink.basic.domain.bo.supplier.BasSupplierRegionWhAuditBo;
import cn.xianlink.basic.domain.bo.supplier.BasSupplierRegionWhQueryBo;
import cn.xianlink.basic.domain.vo.supplier.BasSupplierRegionWhApplyVo;
import cn.xianlink.basic.service.IBasSupplierRegionWhService;
import cn.xianlink.basic.service.IBasSupplierService;
import cn.xianlink.common.api.enums.basic.SupplierAuditEnum;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.core.BaseController;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 供应商供货总仓
 *
 * <AUTHOR>
 * @folder 总仓助手(小程序)/供应商供货总仓
 * @date 2024-04-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/region/supplierRegionWh")
public class RegionBasSupplierRegionWhController extends BaseController {

    private final transient IBasSupplierRegionWhService basSupplierRegionWhService;
    private final transient IBasSupplierService basSupplierService;

    /**
     * 查询-供应商供货申请列表
     */
    @PostMapping(value = "/page")
    public R<TableDataInfo<BasSupplierRegionWhApplyVo>> page(@Validated @RequestBody BasSupplierRegionWhQueryBo bo) {
        return R.ok(basSupplierRegionWhService.customPageList(bo));
    }



    /**
     * 审核
     */
    @PostMapping("/audit")
    public R<Void> audit(@Validated @RequestBody BasSupplierRegionWhAuditBo bo) {
        BasSupplierRegionWhApplyVo vo = basSupplierRegionWhService.selectById(bo.getApplyId());
        if (vo.getAuditStatus().intValue() != SupplierAuditEnum.WAIT.getCode()) {
            return R.warn("非待审核状态，不允许操作");
        }
        bo.setRegionWhId(vo.getRegionWhId());
        bo.setSupplierId(vo.getSupplierId());

        // 入驻审核
        if (vo.getAuditType() == 1) {
            BasSupplierAuditBo auditBo = new BasSupplierAuditBo();
            auditBo.setId(vo.getSupplierId());
            auditBo.setAuditStatus(bo.getAuditStatus());
            auditBo.setAuditRemark(bo.getAuditRemark());
            auditBo.setAlias(bo.getSupplierAlias());
            return toAjax(basSupplierService.audit(auditBo));
        }
        // 供货审核
        return toAjax(basSupplierRegionWhService.audit(bo));
    }
}
