package cn.xianlink.basic.controller.admin;

import cn.xianlink.basic.domain.bo.regionwh.RegionWhAddBo;
import cn.xianlink.basic.domain.bo.regionwh.RegionWhEditBo;
import cn.xianlink.basic.domain.bo.regionwh.RegionWhEditTimeBo;
import cn.xianlink.basic.domain.bo.regionwh.RegionWhQueryBo;
import cn.xianlink.basic.domain.vo.BasPubVo;
import cn.xianlink.basic.domain.vo.regionwh.RegionWhTimeSetUpVo;
import cn.xianlink.basic.domain.vo.regionwh.RegionWhVo;
import cn.xianlink.basic.service.IRegionWhService;
import cn.xianlink.common.api.enums.SSDSEnum;
import cn.xianlink.common.api.util.SSDSUtil;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.enums.StatusEnum;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.core.BaseController;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 总仓
 *
 * <AUTHOR>
 * @date 2024-03-21
 * @folder 般果管理中心/仓库/总仓
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/regionWh")
public class RegionWhController extends BaseController {

    private final transient IRegionWhService regionWhService;

    /**
     * 查询-下拉选项
     *
     * @param name 总仓名称或编码
     * @return
     */
    @GetMapping("/selectList")
    public R<List<BasPubVo>> selectList(@RequestParam(name = "name", required = false) String name) {
        List<BasPubVo> list = regionWhService.selectListByName(name, 0L).stream().map(e -> {
            BasPubVo vo = BeanUtils.instantiateClass(BasPubVo.class);
            vo.setId(e.getId());
            vo.setCode(e.getRegionWhCode());
            vo.setName(e.getRegionWhName());
            return vo;
        }).collect(Collectors.toList());
        return R.ok(list);
    }

    @GetMapping("/authority_list")
    public R<List<BasPubVo>> authorityList(@RequestParam(name = "name", required = false) String name) {
        List<Long> authorityRegionWhIds = SSDSUtil.getAuthorizeDate(SSDSEnum.REGION_WH_ID.getCode());
        List<BasPubVo> list = regionWhService.authorityListByName(name, 0L,authorityRegionWhIds).stream().map(e -> {
            BasPubVo vo = BeanUtils.instantiateClass(BasPubVo.class);
            vo.setId(e.getId());
            vo.setCode(e.getRegionWhCode());
            vo.setName(e.getRegionWhName());
            return vo;
        }).collect(Collectors.toList());
        return R.ok(list);
    }

    /**
     * 查询-列表
     *
     * @return 总仓列表
     */
    @PostMapping("/page")
    public R<TableDataInfo<RegionWhVo>> page(@RequestBody RegionWhQueryBo bo) {
        return R.ok(regionWhService.pageList(bo));
    }

    /**
     * 查询-时间设置详情
     */
    @GetMapping("/timeByRegionWhId/{id}")
    public R<RegionWhTimeSetUpVo> timeByRegionWhId(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(regionWhService.timeByRegionWhId(id));
    }

    /**
     * 新增
     */
    @RepeatSubmit()
    @PostMapping("/create")
    public R<Void> create(@Validated @RequestBody RegionWhAddBo bo) {
        if (!regionWhService.checkRegionWhCodeUnique(bo.getRegionWhCode())) {
            return R.warn("总仓编码已存在");
        }
        if (!regionWhService.checkRegionWhNameUnique(bo.getRegionWhName(), null)) {
            return R.warn("总仓名称已存在");
        }
        if (bo.getDeptId() == null) {
            bo.setDeptId(0L);
        }
        return toAjax(regionWhService.insertRegionWh(bo));
    }

    /**
     * 修改
     */
    @RepeatSubmit()
    @PostMapping("/update")
    public R<Void> update(@Validated @RequestBody RegionWhEditBo bo) {
        if (!regionWhService.checkRegionWhNameUnique(bo.getRegionWhName(), bo.getId())) {
            return R.warn("总仓名称已存在");
        }
        return toAjax(regionWhService.updateRegionWh(bo));
    }
    /**
     * 修改时间
     */
    @RepeatSubmit()
    @PostMapping("/updateTime")
    public R<Void> updateTime(@Validated @RequestBody RegionWhEditTimeBo bo) {
        return toAjax(regionWhService.updateRegionWhTime(bo));
    }

    /**
     * 启用
     */
    @PostMapping("/enable/{id}")
    public R<Void> enable(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return toAjax(regionWhService.updateRegionWhStatus(id, StatusEnum.ENABLE.getCode()));
    }

    /**
     * 禁用
     */
    @PostMapping("/disabled/{id}")
    public R<Void> disabled(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return toAjax(regionWhService.updateRegionWhStatus(id, StatusEnum.DISABLE.getCode()));
    }

    /**
     * 根据总仓id获取这个总仓当前时间的销售日
     * @param regionWhId
     * @return
     */
    @GetMapping("/getSaleDate/{regionWhId}")
    public R<LocalDate> getSaleDate(@NotNull(message = "总仓不能为空") @PathVariable Long regionWhId) {
        return R.ok(regionWhService.getSaleDate(regionWhId));
    }
}
