package cn.xianlink.basic.controller.region;

import cn.xianlink.basic.domain.bo.customer.CustomerQueryMiniBo;
import cn.xianlink.basic.domain.vo.customer.BasCustomerSimperVo;
import cn.xianlink.basic.service.IBasCustomerService;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.core.BaseController;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 客户
 * <AUTHOR> xiaodaibing on 2024-06-03 18:45
 * @folder 总仓助手(小程序)/客户
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/region/customer")
public class RCustomerController extends BaseController {
    private final IBasCustomerService basCustomerService;

    /**
     * 分页模糊查询客户
     * <AUTHOR> on 2024/6/3:18:47
     * @return cn.xianlink.common.core.domain.R
     */
    @PostMapping("/page")
    public R<TableDataInfo<BasCustomerSimperVo>> page(@RequestBody CustomerQueryMiniBo bo) {
        return R.ok(basCustomerService.miniPageList(bo));
    }


}
