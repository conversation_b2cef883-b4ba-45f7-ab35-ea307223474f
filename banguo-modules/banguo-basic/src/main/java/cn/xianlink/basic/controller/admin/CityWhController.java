package cn.xianlink.basic.controller.admin;

import cn.dev33.satoken.context.SaHolder;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.ContentType;
import cn.xianlink.basic.domain.bo.citywh.*;
import cn.xianlink.basic.domain.vo.BasPubVo;
import cn.xianlink.basic.domain.vo.ExportExcelResultVo;
import cn.xianlink.basic.domain.vo.citywh.CityWhPlaceSumVo;
import cn.xianlink.basic.domain.vo.citywh.CityWhTypeVo;
import cn.xianlink.basic.domain.vo.citywh.CityWhVo;
import cn.xianlink.basic.domain.vo.citywh.WhNexusVo;
import cn.xianlink.basic.domain.vo.regionwh.RegionWhVo;
import cn.xianlink.basic.listener.CityWhExcelListener;
import cn.xianlink.basic.service.*;
import cn.xianlink.common.api.bo.UserRelation;
import cn.xianlink.common.api.enums.SSDSEnum;
import cn.xianlink.common.api.enums.system.SysUserTypeEnum;
import cn.xianlink.common.api.util.SSDSUtil;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.enums.StatusEnum;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.excel.utils.ExcelUtil;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.json.utils.JsonUtils;
import cn.xianlink.common.log.annotation.Log;
import cn.xianlink.common.log.enums.BusinessType;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.mybatis.helper.DataPermissionHelper;
import cn.xianlink.common.mybatis.service.SysDataScopeService;
import cn.xianlink.common.redis.utils.RedisUtils;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.resource.api.RemoteFileService;
import cn.xianlink.resource.api.domain.RemoteFile;
import cn.xianlink.system.api.RemoteUserService;
import cn.xianlink.system.api.domain.bo.RemoteUserBo;
import cn.xianlink.system.api.model.LoginUser;
import com.alibaba.excel.EasyExcel;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.constraints.NotNull;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.rmi.ServerException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 城市仓
 *
 * <AUTHOR>
 * @date 2024-03-21
 * @folder 般果管理中心/仓库/城市仓
 */
@CustomLog
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/cityWh")
public class CityWhController extends BaseController {

    private final transient ICityWhService cityWhService;
    private final transient ICityWhPlaceService cityWhPlaceService;
    private final transient IWhProfitRuleService whProfitRuleService;
    private final transient IWhNexusService whNexusService;
    @DubboReference
    private final transient RemoteUserService remoteUserService;
    @DubboReference(timeout = 300000)
    private final transient RemoteFileService remoteFileService;
    private final transient IRegionWhService regionWhService;

    /**
     * 查询-下拉选项
     *
     * @param name  城市仓名称或编码
     * @param rCode 总仓编码
     * @return
     */
    // @SaCheckPermission("basic:admin:cityWh:selectList")
    @GetMapping("/selectList")
    public R<List<BasPubVo>> selectList(@RequestParam(name = "name", required = false) String name, @RequestParam(name = "rCode", required = false) String rCode) {
        List<Long> cityWhIds = null;
        if (StringUtils.isNotBlank(rCode)) {
            cityWhIds = whNexusService.selectListByRegionWhCode(rCode).stream().map(WhNexusVo::getCityWhId).collect(Collectors.toList());
            if (cityWhIds.size() == 0) {
                return R.ok();
            }
        }
        List<BasPubVo> list = cityWhService.selectListByName(name, cityWhIds,null).stream().map(e -> {
            BasPubVo vo = BeanUtils.instantiateClass(BasPubVo.class);
            vo.setId(e.getId());
            vo.setCode(e.getCityWhCode());
            vo.setName(e.getCityWhName());
            return vo;
        }).collect(Collectors.toList());
        return R.ok(list);
    }
    /**
     * 城市仓类型-下拉选项
     */
    @GetMapping("/selectCityWhTypeList")
    public R<List<CityWhTypeVo>> selectCityWhTypeList() {
        return R.ok(cityWhService.selectCityWhTypeList());
    }

    /**
     * 查询-下拉选项
     *
     * @param name  城市仓名称或编码
     * @param rCode 总仓编码
     * @return
     */
    // @SaCheckPermission("basic:admin:cityWh:selectList")
    @GetMapping("/authority_list")
    public R<List<BasPubVo>> authorityList(@RequestParam(name = "name", required = false) String name, @RequestParam(name = "rCode", required = false) String rCode) {
        List<Long> cityWhIds = null;
        if (StringUtils.isNotBlank(rCode)) {
            cityWhIds = whNexusService.selectListByRegionWhCode(rCode).stream().map(WhNexusVo::getCityWhId).collect(Collectors.toList());
            if (cityWhIds.size() == 0) {
                return R.ok();
            }
        }
        SysDataScopeService sysDataScopeService = SpringUtil.getBean(SysDataScopeService.class);
        List<Long> authorityCityWhIds =  SSDSUtil.getAuthorizeDate(SSDSEnum.CITY_WH_ID.getCode());
        List<BasPubVo> list = cityWhService.selectListByName(name, cityWhIds,authorityCityWhIds).stream().map(e -> {
            BasPubVo vo = BeanUtils.instantiateClass(BasPubVo.class);
            vo.setId(e.getId());
            vo.setCode(e.getCityWhCode());
            vo.setName(e.getCityWhName());
            return vo;
        }).collect(Collectors.toList());
        return R.ok(list);
    }

    /**
     * 查询-列表
     *
     * @return 城市仓列表
     */
    // @SaCheckPermission("basic:admin:cityWh:page")
    @PostMapping("/page")
    public R<TableDataInfo<CityWhVo>> page(@RequestBody CityWhQueryBo bo) {
        TableDataInfo<CityWhVo> table = cityWhService.customPageList(bo);
        if (table.getRows().size() > 0) {
            List<Long> cityWhIds = table.getRows().stream().map(CityWhVo::getId).collect(Collectors.toList());
            Map<Long, Integer> placeMap = cityWhPlaceService.countByCityWhIds(cityWhIds).stream().collect(Collectors.toMap(CityWhPlaceSumVo::getCityWhId, CityWhPlaceSumVo::getPlaceCount, (key1, key2) -> key2));
//            Map<Long, Integer> ruleMap = whProfitRuleService.countByCityWhIds(cityWhIds).stream().collect(Collectors.toMap(WhProfitRuleSumVo::getCityWhId, WhProfitRuleSumVo::getProfitRuleCount, (key1, key2) -> key2));
            List<String> adminCodes = table.getRows().stream().filter(e -> ObjectUtil.isNotEmpty(e.getAdminCode())).map(CityWhVo::getAdminCode).collect(Collectors.toList());
            Map<String, RemoteUserBo> userMap = remoteUserService.getUsersByUserCodes(adminCodes);
            Map<Integer, CityWhTypeVo> cityWhTypeMap = cityWhService.selectCityWhTypeMap();
            for (CityWhVo vo : table.getRows()) {
                vo.setPlaceCount(placeMap.get(vo.getId()));
//                vo.setProfitRuleCount(ruleMap.get(vo.getId()));
                vo.setAdminName(userMap.getOrDefault(vo.getAdminCode(), new RemoteUserBo()).getRealName());
                vo.setAdminPhone(userMap.getOrDefault(vo.getAdminCode(), new RemoteUserBo()).getPhoneNo());
                if (vo.getCityWhTypeCode() != null) {
                    CityWhTypeVo typeVo = cityWhTypeMap.getOrDefault(vo.getCityWhTypeCode(), new CityWhTypeVo());
                    vo.setCityWhTypeName(typeVo.getName());
                    vo.setCityWhTypeDesc(typeVo.getCityWhTypeDesc());
                }
            }
        }
        return R.ok(table);
    }

    /**
     * 查询-详情
     *
     * @return 城市仓详情
     */
    // @SaCheckPermission("basic:admin:cityWh:info")
    @GetMapping("/info/{id}")
    public R<CityWhVo> info(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        CityWhVo vo = cityWhService.selectAndCheckNullById(id);
        vo.setWhNexusList(whNexusService.customListByCityWhId(vo.getId()));

        Map<String, RemoteUserBo> userMap = remoteUserService.getUsersByUserCodes(ListUtil.toList(vo.getAdminCode()));
        vo.setAdminName(userMap.getOrDefault(vo.getAdminCode(), new RemoteUserBo()).getRealName());
        vo.setAdminPhone(userMap.getOrDefault(vo.getAdminCode(), new RemoteUserBo()).getPhoneNo());

        if (vo.getCityWhTypeCode() != null) {
            CityWhTypeVo typeVo = cityWhService.selectCityWhTypeMap().getOrDefault(vo.getCityWhTypeCode(), new CityWhTypeVo());
            vo.setCityWhTypeName(typeVo.getName());
            vo.setCityWhTypeDesc(typeVo.getCityWhTypeDesc());
        }
        return R.ok(vo);
    }

    /**
     * 新增
     */
    // @SaCheckPermission("basic:admin:cityWh:create")
    @Log(title = "新增城市仓", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/create")
    public R<Void> create(@Validated @RequestBody CityWhAddBo bo) {
        if (CollectionUtil.isNotEmpty(bo.getWhNexusList())) {
            Map<String, String> whNexusMap = bo.getWhNexusList().stream().collect(Collectors.toMap(WhNexusAddBo::getRegionWhCode, WhNexusAddBo::getRegionWhCode, (key1, key2) -> key2));
            if (whNexusMap.size() != bo.getWhNexusList().size()) {
                throw new ServiceException("关联总仓有重复");
            }
        }
        if (!cityWhService.checkCityWhCodeUnique(bo.getCityWhCode())) {
            return R.warn("城市仓编码已存在");
        }
        if (!cityWhService.checkCityWhNameUnique(bo.getCityWhName(), null)) {
            return R.warn("城市仓名称已存在");
        }
        return toAjax(cityWhService.insertCityWh(bo));
    }

    /**
     * 修改
     */
    // @SaCheckPermission("basic:admin:cityWh:update")
    @Log(title = "修改城市仓", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/update")
    public R<Void> update(@Validated @RequestBody CityWhEditBo bo) {
        if (!cityWhService.checkCityWhNameUnique(bo.getCityWhName(), bo.getId())) {
            return R.warn("城市仓名称已存在");
        }
        return toAjax(cityWhService.updateCityWh(bo));
    }

    /**
     * 修改城市仓地址
     */
    @Log(title = "修改城市仓地址", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/updateAddress")
    public R<Void> updateAddress(@Validated @RequestBody CityWhEditAddressBo bo) {
        return toAjax(cityWhService.updateAddress(bo));
    }

    /**
     * 启用
     */
    // @SaCheckPermission("basic:admin:cityWh:enable")
    @Log(title = "启用", businessType = BusinessType.UPDATE)
    @PostMapping("/enable/{id}")
    public R<Void> enable(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return toAjax(cityWhService.updateCityWhStatus(id, StatusEnum.ENABLE.getCode()));
    }

    /**
     * 禁用
     */
    // @SaCheckPermission("basic:admin:cityWh:disabled")
    @Log(title = "禁用", businessType = BusinessType.UPDATE)
    @PostMapping("/disabled/{id}")
    public R<Void> disabled(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return toAjax(cityWhService.updateCityWhStatus(id, StatusEnum.DISABLE.getCode()));
    }

    /**
     * 城市仓及提货点导入
     */
    @PostMapping("/exportExcel")
    public R<ExportExcelResultVo> exportExcel(@RequestParam("excelFile") MultipartFile file,
                                              @NotNull(message = "总仓id必填") @RequestParam("regionWhId") Long regionWhId) throws ServerException {
        if (file == null) {
            throw new ServerException("网络繁忙，请稍后重试");
        }
        RegionWhVo regionWhVo = regionWhService.selectById(regionWhId);
        ExportExcelResultVo resultVo = new ExportExcelResultVo();
        try {
            List<CityWhExportBo> errorList = new ArrayList<>();
            CityWhExcelListener listener = new CityWhExcelListener(cityWhService, errorList, regionWhVo);
            EasyExcel.read(file.getInputStream(), CityWhExportBo.class, listener).sheet().doRead();
            resultVo.setTotalCount((int) listener.totalCount.get());
            //导出错误文档
            if (CollectionUtil.isNotEmpty(errorList)) {
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                ExcelUtil.exportExcel(errorList, "导入失败明细", CityWhExportBo.class, baos);
                String fileName = String.format("cityWh_exportExcel_fail_%s.xlsx", DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN));
                RemoteFile remoteFile = remoteFileService.uploadTempFile(fileName, fileName, ContentType.OCTET_STREAM.getValue(), baos.toByteArray(), 1);
                resultVo.setFileName("城市仓及提货点导入失败明细.xlsx");
                resultVo.setDownUrl(remoteFile.getUrl());
                resultVo.setFailCount(errorList.size());
                return R.ok(resultVo);
            } else {
                return R.ok();
            }
        } catch (IOException e) {
            throw new ServerException(e.getMessage());
        }
    }

    /**
     * 获取当前用户信息
     *
     * @return 用户信息
     */
    @GetMapping("/getUserInfo")
    public R<Dict> getUserInfo(HttpServletRequest request) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (SysUserTypeEnum.SYS_USER.getType().equals(loginUser.getUserType())) {
            loadUserRelation(loginUser);
        }

        String clientid = request.getHeader(LoginHelper.CLIENT_KEY);
        String info = remoteUserService.getLoginUserInfo(clientid);
        return R.ok(JsonUtils.parseMap(info));
    }

    private void loadUserRelation(LoginUser loginUser) {
        try {
            String phoneNo = loginUser.getPhoneNo();
            log.keyword("getUserInfo", "loadUserRelation", phoneNo).info("userId:[ {} ]  token:[ {} ]", loginUser.getUserId(),  SaHolder.getRequest().getHeader("Authorization"));

            UserRelation userRels = new UserRelation().setPhone(phoneNo);
            userRels.setToken(SaHolder.getRequest().getHeader("Authorization"));
            // 用户关联的城市仓
            List<Long> cityWhIds = remoteUserService.getUserRelationIdByPhone(phoneNo, SysUserTypeEnum.CITY_USER.getType());
            // 用户部门关联的总仓
            List<Long> deptIds = remoteUserService.getDeptsByPhone(phoneNo);
            List<Long> regionIds = regionWhService.selectRegionWhByDepts(deptIds);
            userRels.setDeptIds(deptIds).setCityWhIdByUser(cityWhIds).setRegionWhIdByUser(regionIds);

            // 权限的配置
            List<String> cityPermission = trimNullList(loginUser.getDataPermission(UserRelation.CITY_WH_ID));
            List<String> regionPermission = trimNullList(loginUser.getDataPermission(UserRelation.REGION_WH_ID));
            // 可能有全量*
            userRels.setCityWhIdByConfig(cityPermission).setRegionWhIdByConfig(regionPermission);

            // 城市仓和总仓的关联
            userRels.setCityWhIdByRegionWh(Collections.emptyList()).setRegionWhIdByCityWhId(Collections.emptyList());
            if (isEmpty(cityPermission)) {
                List<Long> cityWhIdsRel = userRels.hasAllRegionWhIdByConfig() ? whNexusService.selectAllCityWhIds() :  whNexusService.selectCityWhIdByRegionWhId(UserRelation.joinList(regionPermission));
                userRels.setCityWhIdByRegionWh(cityWhIdsRel);
            }
            if (isEmpty(regionPermission)) {
                List<Long> regionIdsRel = userRels.hasAllCityWhIdByConfig() ? whNexusService.selectAllRegionWhIds() :  whNexusService.selectRegionWhIdByCityWhId(UserRelation.joinList(cityPermission));
                userRels.setRegionWhIdByCityWhId(regionIdsRel);
            }
            userRels.debug(log);
            // 缓存用户的关联
            //remoteUserService.saverUserRelationMap(loginUser.getUserId(), phoneNo, userRels.asCacheMap());
            Map<String, Object> cacheMap = userRels.asCacheMap();
            log.keyword("saverUserRelationMap").info("relations={}",  JsonUtils.toJsonString(cacheMap));
            RedisUtils.setCacheObject(DataPermissionHelper.getUserRelactionCacheKey(phoneNo), cacheMap);
        } catch (Exception e) {
            log.keyword("getUserInfo").error("获取用户信息异常", e);
        }

    }

    private List<String> trimNullList(List<String> permission) {
        if (CollUtil.isEmpty(permission)) {
            return Collections.emptyList();
        }
        return permission.stream().filter(Objects::nonNull).toList();
    }

    /**
     * 判断是否所有list都是空
     * @param lists
     * @return
     */
    private static boolean isEmpty(List... lists) {
        for (List list : lists) {
            if (CollUtil.isNotEmpty(list)) {
                return false;
            }
        }
        return true;
    }
}