package cn.xianlink.basic.domain.bo.citywh;

import cn.xianlink.basic.domain.CityWhPlace;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import java.io.Serial;
import java.io.Serializable;

/**
 * city_wh_place，城市仓提货点业务对象
 *
 * <AUTHOR>
 * @date 2024-03-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@AutoMapper(target = CityWhPlace.class, reverseConvertGenerate = false)
public class CityWhPlaceEditBo  implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 城市仓提货点id，主键
     */
    @NotNull(message = "主键id不能为空")
    private Long id;
    /**
     * 提货点名称
     */
    @NotBlank(message = "提货点名称不能为空")
    private String placeName;
    /**
     * 详细地址
     */
    @NotBlank(message = "详细地址不能为空")
    private String address;
    /**
     * 经度
     */
    @NotBlank(message = "经度不能为空")
    @Pattern(regexp = "^[-+]?((180(\\.\\d{1,20})?)|((1[0-7]\\d)|([1-9]\\d?))(\\.\\d{1,20})?)$", message = "经度值不合法")
    private String lng;
    /**
     * 纬度
     */
    @NotBlank(message = "纬度不能为空")
    @Pattern(regexp = "^[-+]?((90(\\.\\d{1,20})?)|([1-8]?\\d(\\.\\d{1,20})?))$", message = "纬度值不合法")
    private String lat;
    /**
     * 位置标题
     */
    @NotBlank(message = "位置标题不能为空")
    private String positionTitle;
    /**
     * 联系人姓名
     */
    @NotBlank(message = "联系人姓名不能为空")
    private String contactName;
    /**
     * 联系人电话
     */
    @NotBlank(message = "联系人电话不能为空")
    private String contactPhone;

    /**
     * 备注
     */
    @Length(max = 255, message = "备注不能超过255个字符")
    private String remark = "";
}
