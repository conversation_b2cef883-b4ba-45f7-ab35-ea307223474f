package cn.xianlink.basic.service;

import cn.xianlink.basic.api.domain.bo.RemoteBasSupplierQueryBo;
import cn.xianlink.basic.api.domain.bo.RemoteQueryDeliverBo;
import cn.xianlink.basic.api.domain.vo.RemoteSupplierVo;
import cn.xianlink.basic.domain.bo.supplier.*;
import cn.xianlink.basic.domain.dept.DeptApplyResultEnum;
import cn.xianlink.basic.domain.vo.supplier.BasSupplierAdminListVo;
import cn.xianlink.basic.domain.vo.supplier.BasSupplierSimperVo;
import cn.xianlink.basic.domain.vo.supplier.BasSupplierVo;
import cn.xianlink.basic.domain.vo.supplier.QuerySupplierDeliverPageVo;
import cn.xianlink.common.core.enums.StatusEnum;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.bo.BasRemarkBo;
import cn.xianlink.system.api.model.LoginUser;

import java.util.List;

/**
 * 供应商Service接口
 *
 * <AUTHOR>
 * @date 2024-04-09
 */
public interface IBasSupplierService {

    List<BasSupplierVo> selectByIds(List<Long> supplierIds);

    /**
     * 查询供应商列表；状态：可用
     * 名称左模糊匹配
     */
    List<BasSupplierVo> selectListByName(String name);

    BasSupplierVo queryAndPassRegionWhById(Long id);

    /**
     * 查询供应商
     */
    BasSupplierVo queryById(Long id);

    /**
     * 查询供应商，简单查询，没有info、总仓和超管转换
     *
     * @param id
     * @return cn.xianlink.basic.domain.vo.supplier.BasSupplierVo
     * <AUTHOR> on 2024/4/19:11:10
     */
    RemoteSupplierVo querySimperById(Long id);

    List<RemoteSupplierVo> querySimperByIds(List<Long> ids);


    List<BasSupplierSimperVo> queryByUser(LoginUser loginUser);

    /**
     * 根据登录人，查找他当前绑定的供应商
     * 不读缓存，每次都查最新的数据
     *
     * @param loginUser
     * @return java.util.List<cn.xianlink.basic.domain.vo.supplier.BasSupplierVo>
     * <AUTHOR> on 2024/4/12:16:01
     */
    BasSupplierVo queryByUserCheck(LoginUser loginUser);

    List<RemoteSupplierVo> getSupplierByCode(List<String> supplierCodes);

    /**
     * 查询供应商列表
     */
    TableDataInfo<BasSupplierAdminListVo> queryPageList(BasSupplierSearchBo bo, PageQuery pageQuery);


    /**
     * 新增供应商
     */
    DeptApplyResultEnum insertByBo(BasSupplierBo bo);

    /**
     * 修改供应商
     */
    Boolean updateByBo(BasSupplierBo bo);

    /**
     * 供应商审核失败，修改完成后重新申请
     *
     * @param supplierId
     * @return java.lang.Boolean
     * <AUTHOR> on 2024/4/12:16:39
     */
    Boolean reapply(Long supplierId);

    /**
     * 修改供应商
     */
    Integer updateAliasById(BasSupplierEditAliasBo bo);

    /**
     * 供应商启用或禁用
     *
     * @param supplierId
     * @param statusEnum
     * @return java.lang.Boolean
     * <AUTHOR> on 2024/4/12:16:52
     */
    Boolean enableOrDisabled(Long supplierId, StatusEnum statusEnum);

    void accountCancel(Long supplierId);

    /**
     * 设置备注
     *
     * @param bo
     * @return java.lang.Boolean
     * <AUTHOR> on 2024/4/12:17:30
     */
    Boolean remark(BasRemarkBo bo);


    /**
     * 审核
     *
     * @param bo
     * @return java.lang.Boolean
     * <AUTHOR> on 2024/4/12:17:37
     */
    Boolean audit(BasSupplierAuditBo bo);

    /**
     * 修改供应商板车容量
     *
     * @param scooterCapacity
     * @param supplierId
     * @return void
     * <AUTHOR> on 2024/5/30:14:21
     */
    void scooterCapacity(Integer scooterCapacity, Long supplierId);


    /**
     * 根据供应商名称模糊搜索
     *
     * @param name
     * @return java.util.List<cn.xianlink.basic.domain.vo.supplier.BasSupplierSimperVo>
     * <AUTHOR> on 2024/5/31:14:19
     */
    List<BasSupplierSimperVo> fuzzySearchByName(String name, String status);

    List<BasSupplierSimperVo> fuzzySearchByName2(SupCommonFuzzySearchBo searchBo);

    TableDataInfo<BasSupplierSimperVo> fuzzySearchByNameByPage(SupCommonFuzzySearchBo searchBo);

    /**
     * 根据总仓id查询供应商列表
     *
     * @param bo
     * @return
     */
    List<QuerySupplierDeliverPageVo> querySupplierDeliverPage(QuerySupplierDeliverPageBo bo);

    /**
     * 查询供应商列表
     */
    TableDataInfo<QuerySupplierDeliverPageVo> querySupplierByRegion(QuerySupplierDeliverPageBo bo);

    /**
     * 审核供应商是否能查询待送货列表
     *
     * @param bo
     * @return
     */
    Boolean updateSupplierDeliverStatus(UpdateSupplierDeliverStatusBo bo);

    /**
     * 查询这个供应商能不能查看待送货列表的数据,false不可以查看，true可以查看
     *
     * @param bo
     * @return
     */
    Boolean queryDeliver(RemoteQueryDeliverBo bo);

    /**
     * 查询所有状态正常的供应商id
     * 已开户，非注销，被禁用也可以被查出
     *
     * @return java.util.List<java.lang.Long>
     * <AUTHOR> on 2024/9/14:15:29
     */
    List<Long> selectSupIdByNormalStatus();

    List<Long> selectSupIdByNewRegister(Integer day);

    /**
     * 查询供应商列表
     */
    TableDataInfo<BasSupplierVo> findSupplierPage(RemoteBasSupplierQueryBo bo);
    /**
     * 根据管理员编码查询供应商id
     * @param list
     * @return
     */
    List<Long> getSupplierByAdminCode(List<String> list);

    /**
     * 查询供应商
     * @param supplierId
     * @return
     */
    RemoteSupplierVo querySupplierById(Long supplierId);
}
