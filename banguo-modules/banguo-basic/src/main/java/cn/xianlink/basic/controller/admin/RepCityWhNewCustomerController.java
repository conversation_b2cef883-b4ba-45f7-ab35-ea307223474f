package cn.xianlink.basic.controller.admin;

import cn.hutool.core.collection.CollUtil;
import cn.xianlink.basic.domain.bo.rep.RepCityWhNewCustomerQueryBo;
import cn.xianlink.basic.domain.vo.citywh.CityWhTypeVo;
import cn.xianlink.basic.domain.vo.rep.RepCityWhNewCustomerVo;
import cn.xianlink.basic.service.ICityWhService;
import cn.xianlink.basic.service.IRepCityWhNewCustomerService;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.core.BaseController;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 总仓
 *
 * <AUTHOR>
 * @date 2024-03-21
 * @folder 般果管理中心/城市仓结算/地推榜
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/repCityWhNewCustomer")
public class RepCityWhNewCustomerController extends BaseController {

    private final transient IRepCityWhNewCustomerService repCityWhNewCustomerService;
    private final transient ICityWhService cityWhService;

    /**
     * 分页查询
     */
    @PostMapping("/page")
    public R<TableDataInfo<RepCityWhNewCustomerVo>> page(@Validated @RequestBody RepCityWhNewCustomerQueryBo bo) {
        TableDataInfo<RepCityWhNewCustomerVo> table = repCityWhNewCustomerService.pageList(bo);
        if (CollUtil.isNotEmpty(table.getRows())) {
            Map<Integer, CityWhTypeVo> cityWhTypeMap = cityWhService.selectCityWhTypeMap();
            table.getRows().forEach(vo -> vo.setCityWhTypeDesc(cityWhTypeMap.getOrDefault(vo.getCityWhTypeCode(), new CityWhTypeVo()).getCityWhTypeDesc()));
        }
        return R.ok(repCityWhNewCustomerService.pageList(bo));
    }
}
