package cn.xianlink.basic.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.xianlink.basic.constant.BasCacheNames;
import cn.xianlink.basic.domain.BasRegionLogistics;
import cn.xianlink.basic.domain.CityWhPlace;
import cn.xianlink.basic.domain.CityWhUserPlace;
import cn.xianlink.basic.domain.bo.citywh.CityWhPlaceAddBo;
import cn.xianlink.basic.domain.bo.citywh.CityWhPlaceEditBo;
import cn.xianlink.basic.domain.bo.citywh.CityWhPlaceEditSonBo;
import cn.xianlink.basic.domain.bo.citywh.CityWhPlaceQueryBo;
import cn.xianlink.basic.domain.vo.citywh.CityWhPlaceSumVo;
import cn.xianlink.basic.domain.vo.citywh.CityWhPlaceVo;
import cn.xianlink.basic.domain.vo.citywh.CityWhUserPlaceVo;
import cn.xianlink.basic.domain.vo.citywh.CityWhVo;
import cn.xianlink.basic.domain.vo.regionlog.BasRegionLogisticsVo;
import cn.xianlink.basic.mapper.BasRegionLogisticsMapper;
import cn.xianlink.basic.mapper.CityWhPlaceMapper;
import cn.xianlink.basic.mapper.CityWhUserPlaceMapper;
import cn.xianlink.basic.service.ICityWhPlaceService;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.core.utils.StreamUtils;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.redis.utils.CacheUtils;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.system.api.model.LoginUser;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.ibatis.annotations.Param;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 城市仓-提货点 业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-21 10:05:56
 */
@CustomLog
@RequiredArgsConstructor
@Service
public class CityWhPlaceServiceImpl   extends ServiceImpl<CityWhPlaceMapper, CityWhPlace> implements ICityWhPlaceService {

    private final transient CityWhPlaceMapper cityWhPlaceMapper;
    private final transient CityWhUserPlaceMapper cityWhUserPlaceMapper;
    private final transient BasRegionLogisticsMapper regionLogisticsMapper;

    /**
     * 查询提货点列表 by 城市仓编码
     *
     * @param name
     * @param cityWhCode
     * @return
     */
    @Override
    public IPage<CityWhPlaceVo> selectListByName(String name, String cityWhCode) {
        LambdaQueryWrapper<CityWhPlace> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(cityWhCode), CityWhPlace::getCityWhCode, cityWhCode);
        lqw.and(StringUtils.isNotBlank(name), q -> q.likeRight(CityWhPlace::getPlaceCode, name).or().likeRight(CityWhPlace::getPlaceName, name));
        lqw.orderByAsc(CityWhPlace::getPlaceName);

        PageQuery page = new PageQuery();
        page.setPageNum(1);
        page.setPageSize(20);
        return cityWhPlaceMapper.selectVoPage(page.build(), lqw);
    }

    /**
     * 查询提货点列表 by 位置标题
     */
    @Override
    public List<CityWhPlaceVo> selectListByPositionTitle(String positionTitle) {
        LambdaQueryWrapper<CityWhPlace> lqw = Wrappers.lambdaQuery();
        lqw.like(CityWhPlace::getPositionTitle, positionTitle);
        lqw.orderByAsc(CityWhPlace::getPositionTitle);
        return cityWhPlaceMapper.selectVoList(lqw);
    }

    /**
     * 查询单条提货点信息
     *
     * @return 提货点信息
     */
    @Override
    public CityWhPlaceVo selectAndCheckNullById(Long id) {
        CityWhPlaceVo vo = cityWhPlaceMapper.selectVoById(id);
        if (vo == null) {
            throw new ServiceException("提货点id不存在");
        }
        return vo;
    }

    public List<CityWhPlaceVo> selectListByParentPlaceId(Long parentPlaceId, Boolean isQueryLogistics) {
        CityWhPlaceVo parentVo = selectAndCheckNullById(parentPlaceId);
        LambdaQueryWrapper<CityWhPlace> lqw = Wrappers.lambdaQuery();
        lqw.eq(CityWhPlace::getParentPlaceId, parentPlaceId);
        List<CityWhPlaceVo> list = cityWhPlaceMapper.selectVoList(lqw);
        if (isQueryLogistics) {
            if (list.size() > 0) {
                List<Long> placeIds = list.stream().map(CityWhPlaceVo::getId).collect(Collectors.toList());
                Map<Long, BasRegionLogistics> logisticsMap = regionLogisticsMapper.selectList(new LambdaQueryWrapper<BasRegionLogistics>().in(BasRegionLogistics::getPlaceId, placeIds))
                        .stream().collect(Collectors.toMap(BasRegionLogistics::getPlaceId, Function.identity(), (key1, key2) -> key2));
                list.forEach(e -> {
                    e.setParentPlaceName(parentVo.getPlaceName());
                    BasRegionLogistics logistics = logisticsMap.getOrDefault(e.getId(), new BasRegionLogistics());
                    e.setLogisticsId(logistics.getId());
                    e.setLogisticsName(logistics.getLogisticsName());
                    e.setFreightPrice(logistics.getFreightPrice());
                    e.setPriceMode(logistics.getPriceMode());
                });
            }
        }
        return list;
    }

    /**
     * 查询单条提货点信息
     *
     * @param placeCode
     * @return 提货点信息
     */
    @Override
    public CityWhPlaceVo selectAndCheckNullByCode(String placeCode) {
        LambdaQueryWrapper<CityWhPlace> lqw = Wrappers.lambdaQuery();
        lqw.eq(CityWhPlace::getPlaceCode, placeCode);
        CityWhPlaceVo vo = cityWhPlaceMapper.selectVoOne(lqw);
        if (vo == null) {
            throw new ServiceException("提货点编码不存在");
        }
        return vo;
    }

    /**
     * 查询提货点列表
     *
     * @param cityWhIds
     * @return 提货点列表
     */
    @Override
    public List<CityWhPlaceVo> selectListByCityWhIds(List<Long> cityWhIds) {
        if (CollectionUtils.isEmpty(cityWhIds)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<CityWhPlace> lqw = Wrappers.lambdaQuery();
        lqw.in(CityWhPlace::getCityWhId, cityWhIds);
        return cityWhPlaceMapper.selectVoList(lqw);
    }

    /**
     * 查询提货点列表
     *
     * @param cityWhId
     * @return 提货点列表
     */
//    @Cacheable(cacheNames = BasCacheNames.CITY_WH_PLACE_BY_CITYWHID, key = "#cityWhId")
    @Override
    public List<CityWhPlaceVo> selectListByCityWhId(Long cityWhId) {
        LambdaQueryWrapper<CityWhPlace> lqw = Wrappers.lambdaQuery();
        lqw.eq(CityWhPlace::getCityWhId, cityWhId);
        return cityWhPlaceMapper.selectVoList(lqw);
    }

    /**
     * 提货点计数
     *
     * @return
     */
    @Override
    public List<CityWhPlaceSumVo> countByCityWhIds(List<Long> cityWhIds) {
        return cityWhPlaceMapper.countByCityWhIds(cityWhIds);
    }

    /**
     * 查询提货点列表
     *
     * @return 提货点列表
     */
    @Override
    public List<CityWhPlaceVo> selectVoBatchIds(List<Long> placeIds) {
        return cityWhPlaceMapper.selectVoBatchIds(placeIds);
    }

    @Override
    public List<CityWhPlaceVo> queryNearPlaceListByLocation(@Param("lng") Double lng, @Param("lat") Double lat) {
        return cityWhPlaceMapper.queryNearPlaceListByLocation(lng, lat);
    }

    /**
     * 查询提货点列表
     *
     * @param bo
     * @return 提货点列表
     */
    @Override
    public TableDataInfo<CityWhPlaceVo> pageList(CityWhPlaceQueryBo bo) {
        List<Long> ids = new ArrayList<>();
        if (StringUtils.isNotBlank(bo.getSonPlaceName())) {
            LambdaQueryWrapper<CityWhPlace> lqw = Wrappers.lambdaQuery();
            lqw.like(CityWhPlace::getPlaceName, bo.getSonPlaceName()).isNotNull(CityWhPlace::getParentPlaceId);
            lqw.select(CityWhPlace::getParentPlaceId).groupBy(CityWhPlace::getParentPlaceId);
            ids = cityWhPlaceMapper.selectList(lqw).stream().map(CityWhPlace::getParentPlaceId).collect(Collectors.toList());
            if (ids.size() == 0) {
                return TableDataInfo.build();
            }
        }
        LambdaQueryWrapper<CityWhPlace> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotEmpty(bo.getPlaceName()), CityWhPlace::getPlaceName, bo.getPlaceName());
        lqw.eq(ObjectUtil.isNotNull(bo.getCityWhId()), CityWhPlace::getCityWhId, bo.getCityWhId());
        lqw.eq(StringUtils.isNotEmpty(bo.getCityWhCode()), CityWhPlace::getCityWhCode, bo.getCityWhCode());
        lqw.like(ObjectUtil.isNotEmpty(bo.getUpdateName()), CityWhPlace::getUpdateName, bo.getUpdateName());
        lqw.ge(ObjectUtil.isNotNull(bo.getUpdateTimeStart()), CityWhPlace::getUpdateTime, bo.getUpdateTimeStart());
        lqw.lt(ObjectUtil.isNotNull(bo.getUpdateTimeEnd()), CityWhPlace::getUpdateTime, bo.getUpdateTimeEnd());
        lqw.in(CollUtil.isNotEmpty(ids), CityWhPlace::getId, ids);
        if (ObjectUtil.isNotNull(bo.getPlaceLevel())) {
            if (bo.getPlaceLevel() == 1) {
                lqw.isNull(CityWhPlace::getParentPlaceId);
            } else {
                lqw.isNotNull(CityWhPlace::getParentPlaceId);
            }
        }
        lqw.in(CollUtil.isNotEmpty(bo.getPlaceIds()), CityWhPlace::getId, bo.getPlaceIds());
        Page<CityWhPlaceVo> result = cityWhPlaceMapper.selectVoPage(bo.build(), lqw);
        if (result.getRecords().size() > 0) {
            if (ObjectUtil.isNotNull(bo.getPlaceLevel()) && bo.getPlaceLevel() == 1) {
                Map<Long, List<CityWhPlace>> sonListMap = cityWhPlaceMapper.selectList(new LambdaQueryWrapper<CityWhPlace>()
                                .in(CityWhPlace::getParentPlaceId, result.getRecords().stream().map(CityWhPlaceVo::getId).collect(Collectors.toList())))
                        .stream().collect(Collectors.groupingBy(CityWhPlace::getParentPlaceId));

                result.getRecords().forEach(e -> {
                    if (sonListMap.containsKey(e.getId())) {
                        e.setSonPlaceNames(String.join("；", sonListMap.get(e.getId()).stream().map(CityWhPlace::getPlaceName).collect(Collectors.toList())));
                    }
                });
            } else {
                List<Long> parentPlaceIds = result.getRecords().stream().filter(f -> f.getParentPlaceId() != null)
                        .map(CityWhPlaceVo::getParentPlaceId).distinct().collect(Collectors.toList());
                if (CollUtil.isNotEmpty(parentPlaceIds)) {
                    Map<Long, String> parentPlaceNameMap = cityWhPlaceMapper.selectBatchIds(parentPlaceIds)
                            .stream().collect(Collectors.toMap(CityWhPlace::getId, CityWhPlace::getPlaceName));
                    result.getRecords().forEach(e -> e.setParentPlaceName(parentPlaceNameMap.get(e.getParentPlaceId())));
                }
            }
        }
        return TableDataInfo.build(result);
    }

    /**
     * 校验提货点名称 是否唯一
     *
     * @param placeName 提货点名称
     * @param excludeId 提货点id(排除)，可为空
     * @return 结果
     */
    @Override
    public boolean checkPlaceNameUnique(String placeName, Long excludeId) {
//        boolean exist = cityWhPlaceMapper.exists(new LambdaQueryWrapper<CityWhPlace>()
//                .eq(CityWhPlace::getPlaceName, placeName)
//                .ne(ObjectUtil.isNotNull(excludeId), CityWhPlace::getId, excludeId));
//        return !exist;
        List<CityWhPlace> list = cityWhPlaceMapper.selectList(new LambdaQueryWrapper<CityWhPlace>()
                .eq(CityWhPlace::getPlaceName, placeName)
                .ne(ObjectUtil.isNotNull(excludeId), CityWhPlace::getId, excludeId));
        if (list.size() > 0) {
            if (ObjectUtil.isNotNull(list.get(0).getParentPlaceId())) {
                throw new ServiceException("提货点名称与子仓名称重复");
            }
            throw new ServiceException("提货点名称已存在");
        }
        return true;
    }

    /**
     * 新增提货点
     *
     * @param bo 提货点信息
     * @return 结果
     */
//    @CacheEvict(cacheNames = BasCacheNames.CITY_WH_PLACE_BY_CITYWHID, key = "#cityWhVo.getId()")
    @Override
    public int insertPlace(CityWhPlaceAddBo bo, CityWhVo cityWhVo) {
        int count = cityWhPlaceMapper.countAllByCityWhId(cityWhVo.getId());
        CityWhPlace entity = MapstructUtils.convert(bo, CityWhPlace.class);
        entity.setCityWhId(cityWhVo.getId());
        entity.setCityWhName(cityWhVo.getCityWhName());
        entity.setPlaceCode(bo.getCityWhCode() + String.format("%02d", count + 1));
        int num = cityWhPlaceMapper.insert(entity);
        cityWhPlaceMapper.updateLocationByIds(ListUtil.toList(entity.getId()));
        this.updatePath(entity);
        // 自动绑定二级提货点
        autoBindUserPlace(entity);
        return num;
    }

    private void autoBindUserPlace(CityWhPlace entity) {
        if (entity.getParentPlaceId() != null) {
            // 配置了一级提货点权限的账号，默认有一级点下的所有子仓权限，且会自动拥有未来新增子仓的权限（即默认全选了子仓）
            // 李员工账号仅有一级提货点X的权限，则他默认有子仓A、B、C的权限；此时把李员工的账号权限设置为仅有一级提货点X、子仓A的权限（即设定了固定范围的权限），则未来新增子仓D，则李员工的账号未来不会自动新增子仓D的权限

            // 查询绑定一级的人员
            List<CityWhUserPlace> bindLevel1 = cityWhUserPlaceMapper.selectList(new LambdaQueryWrapper<CityWhUserPlace>()
                    .eq(CityWhUserPlace::getPlaceId, entity.getParentPlaceId()));

            LambdaQueryWrapper<CityWhPlace> lqw = Wrappers.lambdaQuery();
            lqw.eq(CityWhPlace::getParentPlaceId, entity.getParentPlaceId());
            // 新增提货点id
            Long newPlaceId = entity.getId();
            lqw.ne(CityWhPlace::getId, newPlaceId);
            // 查询二级的情况
            List<CityWhPlace> secondLevelPlaces = cityWhPlaceMapper.selectList(lqw);
            if (CollUtil.isEmpty(secondLevelPlaces) && CollUtil.isNotEmpty(bindLevel1)) {
                // 没有其他的二级，默认绑定上用户和新的二级提货点
                List<Long> userIds = bindLevel1.stream().map(CityWhUserPlace::getUserId).toList();
                log.keyword("insertPlace", entity.getParentPlaceId(), newPlaceId).info("case1 一级提货点:{}，新增二级提货点:{}，默认绑定用户:{}"
                        , entity.getParentPlaceId(), newPlaceId, StrUtil.join(",", userIds));
                bindingSubPlace(userIds, newPlaceId);
            } else if (CollUtil.isNotEmpty(bindLevel1)){
                List<Long> userIds = bindLevel1.stream().map(CityWhUserPlace::getUserId).toList();
                List<Long> placeIds = secondLevelPlaces.stream().map(CityWhPlace::getId).toList();
                // 过滤出绑定了所有二级的userId
                List<Long> bindUserIds = cityWhUserPlaceMapper.checkUserBindAllPlaces(userIds, placeIds, placeIds.size());
                log.keyword("checkUserBindAllPlaces").info("userIds:{} placeIds:{} 过滤后全绑定的用户bindUserIds:{}"
                        , StrUtil.join(",", userIds), StrUtil.join(",", placeIds), StrUtil.join(",", bindUserIds));

                bindingSubPlace(bindUserIds, newPlaceId);
                log.keyword("insertPlace", entity.getParentPlaceId(), newPlaceId).info("case2 一级提货点:{}，新增二级提货点:{}，绑定用户:{}, 其他的二级提货点:{}"
                        , entity.getParentPlaceId(), newPlaceId, StrUtil.join(",", bindUserIds), StrUtil.join(",", placeIds));
            } else {
                log.keyword("insertPlace", entity.getParentPlaceId(), newPlaceId).info("case3 一级提货点:{}，新增二级提货点:{} 无自动绑定", entity.getParentPlaceId(), newPlaceId);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void bindingSubPlace(List<Long> userIds, Long placeId) {
        if (CollUtil.isEmpty(userIds)) {
            return;
        }
        List<CityWhUserPlace> list = StreamUtils.toList(userIds, userId -> {
            CityWhUserPlace ud = new CityWhUserPlace();
            ud.setUserId(userId);
            // 绑定二级提货点
            ud.setPlaceId(placeId);
            // 清除用户缓存
            removeUserPlaceCache(userId);
            return ud;
        });
        cityWhUserPlaceMapper.insertBatch(list);
    }

    @Caching(evict = {
            @CacheEvict(cacheNames = BasCacheNames.CITY_USER_PLACE, key = "#userId")
    })
    public void removeUserPlaceCache(Long userId) {
        log.keyword("CityWhPlaceServiceImpl", "removeUserPlaceCache", userId).info("清除用户提货点缓存{}{}", BasCacheNames.CITY_USER_PLACE, userId);
    }

    private void updatePath(CityWhPlace entity) {
        String path = "";
        if (entity.getParentPlaceId() != null) {
            path += "/" + entity.getParentPlaceId();
        }
        path += "/" + entity.getId();
        CityWhPlace update = new CityWhPlace();
        update.setId(entity.getId());
        update.setPath(path);
        cityWhPlaceMapper.updateById(update);
    }

    //    /**
//     * 批量新增提货点-二级
//     */
//    @Override
//    @Transactional(rollbackFor = Throwable.class)
//    public boolean batchInsertPlace(List<CityWhPlaceAddSonBo> bos) {
//        if (CollUtil.isEmpty(bos)) {
//            throw new ServiceException("二级提货点列表为空");
//        }
//        CityWhPlaceVo parentVo = this.selectAndCheckNullById(bos.get(0).getParentPlaceId());
//        if (ObjectUtil.isNotNull(parentVo.getParentPlaceId())) {
//            throw new ServiceException("所属提货点非一级，不允许添加二级提货点");
//        }
//        List<String> placeNames = bos.stream().map(CityWhPlaceAddSonBo::getPlaceName).distinct().collect(Collectors.toList());
//        if (placeNames.size() != bos.size()) {
//            throw new ServiceException("添加的二级提货点名称有重复");
//        }
//        LambdaQueryWrapper<CityWhPlace> lqw = Wrappers.lambdaQuery();
//        lqw.in(CityWhPlace::getPlaceName, placeNames);
//        lqw.select(CityWhPlace::getPlaceName);
//        List<CityWhPlace> namelist = cityWhPlaceMapper.selectList(lqw);
//        if (namelist.size() > 0) {
//            throw new ServiceException(String.format("提货点名称已存在【%s】",
//                    String.join("，", namelist.stream().map(CityWhPlace::getPlaceName).toList())));
//        }
//        // 查询子提货点个数
//        AtomicLong placeCount = new AtomicLong(cityWhPlaceMapper.countAllByParentPlaceId(parentVo.getId()).longValue());
//        List<CityWhPlace> entityList = MapstructUtils.convert(bos, CityWhPlace.class);
//        entityList.forEach(entity -> {
//            entity.setParentPlaceCode(parentVo.getPlaceCode());
//            entity.setCityWhId(parentVo.getCityWhId());
//            entity.setCityWhCode(parentVo.getCityWhCode());
//            entity.setCityWhName(parentVo.getCityWhName());
//            entity.setPlaceCode(parentVo.getPlaceCode() + String.format("-%03d", placeCount.incrementAndGet()));
//        });
//        boolean result = cityWhPlaceMapper.insertBatch(entityList);
//        if (result) {
//            // 更新父级提货点字段：子级物流个数
//            this.updateChildCount(parentVo.getId(), parentVo.getChildCount() + entityList.size());
//        }
//        return result;
//    }
    private void updateChildCount(Long parentPlaceId, Integer childCount) {
        cityWhPlaceMapper.update(Wrappers.lambdaUpdate(CityWhPlace.class).set(CityWhPlace::getChildCount, childCount)
                .eq(CityWhPlace::getId, parentPlaceId));
    }

    /**
     * 修改提货点
     *
     * @param bo 提货点信息
     * @return 结果
     */
    @Override
    public int updatePlace(CityWhPlaceEditBo bo) {
        // 修改提货点的城市仓，需要更换提货点下用户关联的城市仓（如果是超管怎么办？）
        int count = cityWhPlaceMapper.updateById(MapstructUtils.convert(bo, CityWhPlace.class));
        cityWhPlaceMapper.updateLocationByIds(ListUtil.toList(bo.getId()));
        return count;
    }

    /**
     * 批量更新子提货点
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public boolean editSonPlace(List<CityWhPlaceEditSonBo> bos) {
        if (CollectionUtil.isEmpty(bos)) {
            throw new ServiceException("参数子仓名列表为空");
        }
        CityWhPlaceVo parentVo = this.selectAndCheckNullById(bos.get(0).getParentPlaceId());
        if (ObjectUtil.isNotNull(parentVo.getParentPlaceId())) {
            throw new ServiceException("所属提货点非一级，不允许操作");
        }
        AtomicLong placeCount = null;
        AtomicLong logisticsCount = null;
        List<CityWhPlaceEditSonBo> addBos = bos.stream().filter(f -> f.getEditType() == 1).collect(Collectors.toList());
        List<CityWhPlaceEditSonBo> updateBos = bos.stream().filter(f -> f.getEditType() == 2).collect(Collectors.toList());
        if (addBos.size() > 0 || updateBos.size() > 0) {
            updateBos.addAll(addBos);
            List<String> placeNames = updateBos.stream().map(CityWhPlaceEditSonBo::getPlaceName).distinct().collect(Collectors.toList());
            if (placeNames.size() != updateBos.size()) {
                throw new ServiceException("子仓名称有重复");
            }
            List<Long> placeIds = bos.stream().filter(f -> f.getEditType() != 1).map(CityWhPlaceEditSonBo::getId).distinct().collect(Collectors.toList());
            LambdaQueryWrapper<CityWhPlace> lqw = Wrappers.lambdaQuery();
            lqw.in(CityWhPlace::getPlaceName, placeNames).notIn(CollUtil.isNotEmpty(placeIds), CityWhPlace::getId, placeIds);
            lqw.select(CityWhPlace::getPlaceName);
            List<CityWhPlace> namelist = cityWhPlaceMapper.selectList(lqw);
            if (namelist.size() > 0) {
                throw new ServiceException(String.format("子仓名称已存在【%s】",
                        String.join("，", namelist.stream().map(CityWhPlace::getPlaceName).toList())));
            }
            if (addBos.size() > 0) {
                // 查询全部子仓个数，包括删除，用于生成子仓编码
                placeCount = new AtomicLong(cityWhPlaceMapper.countAllByParentPlaceId(parentVo.getId()).longValue());
                // 查询城市仓下，子仓物流个数
                BasRegionLogisticsVo basRegionLogisticsVo = regionLogisticsMapper.queryRegionMaxLogisticsCode(
                        Wrappers.lambdaQuery(BasRegionLogistics.class)
                                .eq(BasRegionLogistics::getRegionWhId, 0)
                                .eq(BasRegionLogistics::getCityWhId, parentVo.getCityWhId()));
                logisticsCount = new AtomicLong(basRegionLogisticsVo.getId());
            }
        }

        List<Long> deletePlaceIds = new ArrayList<>();
        List<Long> deleteLogisticsIds = new ArrayList<>();
        List<CityWhPlace> updatePlaceList = new ArrayList<>();
        List<BasRegionLogistics> updateLogisticsList = new ArrayList<>();
        List<CityWhPlace> addPlaceList = new ArrayList<>();
        List<BasRegionLogistics> addLogisticsList = new ArrayList<>();
        for (CityWhPlaceEditSonBo bo : bos) {
            if (bo.getEditType() == 1) {
                CityWhPlace entity = MapstructUtils.convert(bo, CityWhPlace.class);
                entity.setId(null);
                entity.setParentPlaceCode(parentVo.getPlaceCode());
                entity.setCityWhId(parentVo.getCityWhId());
                entity.setCityWhCode(parentVo.getCityWhCode());
                entity.setCityWhName(parentVo.getCityWhName());
                entity.setPlaceCode(parentVo.getPlaceCode() + String.format("-%03d", placeCount.incrementAndGet()));
                cityWhPlaceMapper.insert(entity);
                this.updatePath(entity);
                // 自动绑定二级提货点
                autoBindUserPlace(entity);
                addPlaceList.add(entity);

                BasRegionLogistics logEntity = new BasRegionLogistics();
                logEntity.setLogisticsCode(parentVo.getCityWhCode() + String.format("%02d", logisticsCount.incrementAndGet()));
                logEntity.setLogisticsName(bo.getLogisticsName());
                logEntity.setFreightPrice(bo.getFreightPrice());
                logEntity.setCityFreightPrice(bo.getFreightPrice());
                logEntity.setPriceMode(bo.getPriceMode());
                logEntity.setCityWhId(parentVo.getCityWhId());
                logEntity.setPlaceId(entity.getId());
                logEntity.setParentPlaceId(parentVo.getId());
                logEntity.setRegionWhId(0L);
                logEntity.setLogisticsType(1);
                logEntity.setStatus(1);
                logEntity.setStatusTime(Convert.toDate(DateUtil.now()));
                regionLogisticsMapper.insert(logEntity);
                addLogisticsList.add(logEntity);
            } else if (bo.getEditType() == 2) {
                CityWhPlace entity = MapstructUtils.convert(bo, CityWhPlace.class);
                entity.setParentPlaceId(null);
                updatePlaceList.add(entity);

                BasRegionLogistics logEntity = new BasRegionLogistics();
                logEntity.setId(bo.getLogisticsId());
                logEntity.setLogisticsName(bo.getLogisticsName());
                logEntity.setFreightPrice(bo.getFreightPrice());
                logEntity.setCityFreightPrice(bo.getFreightPrice());
                logEntity.setPriceMode(bo.getPriceMode());
                updateLogisticsList.add(logEntity);
            } else if (bo.getEditType() == 3) {
                deletePlaceIds.add(bo.getId());
                deleteLogisticsIds.add(bo.getLogisticsId());
            }
        }
        if (deletePlaceIds.size() > 0) {
            cityWhPlaceMapper.deleteBatchIds(deletePlaceIds);
            // 删除提货点，同时删除绑定
            cityWhUserPlaceMapper.deleteByPlaceIds(deletePlaceIds);
            regionLogisticsMapper.deleteBatchIds(deleteLogisticsIds);
        }
        if (updatePlaceList.size() > 0) {
            cityWhPlaceMapper.updateBatchById(updatePlaceList);
            regionLogisticsMapper.updateBatchById(updateLogisticsList);
        }
        cityWhPlaceMapper.updateLocationByIds(updatePlaceList.stream().map(CityWhPlace::getId).collect(Collectors.toList()));
        if (addPlaceList.size() != deletePlaceIds.size()) {
            // 更新父级提货点字段：子级物流个数
            this.updateChildCount(parentVo.getId(), parentVo.getChildCount() + addPlaceList.size() - deletePlaceIds.size());
        }
        return true;
    }

    /**
     * 删除提货点
     *
     * @param placeId 提货点ID
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int deleteById(Long placeId) {
        this.clearUserPlaceRelation(placeId);
        CityWhPlaceVo placeVo = this.selectAndCheckNullById(placeId);
        if (placeVo.getParentPlaceId() == null && placeVo.getChildCount() > 0) {
            throw new ServiceException("该提货点有关联的二级提货点，不允许删除");
        }
        int count = cityWhPlaceMapper.deleteById(placeId);
        //删除了子提货点
        if (count > 0 && placeVo.getParentPlaceId() != null) {
            // 更新父级提货点字段：子级物流个数
            this.updateChildCount(placeVo.getParentPlaceId(), this.selectAndCheckNullById(placeVo.getParentPlaceId()).getChildCount() + 1);
        }
        return count;
    }


    @Caching(evict = {
            @CacheEvict(cacheNames = BasCacheNames.CITY_USER_PLACE, key = "#userId")
    })
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void bindingUser(Long userId, List<Long> placeIds) {
        cityWhUserPlaceMapper.deleteByUserId(userId);
        if (CollUtil.isNotEmpty(placeIds)) {
            List<CityWhUserPlace> list = StreamUtils.toList(placeIds, placeId -> {
                CityWhUserPlace ud = new CityWhUserPlace();
                ud.setUserId(userId);
                ud.setPlaceId(placeId);
                return ud;
            });
            cityWhUserPlaceMapper.insertBatch(list);
        }
    }

    @Cacheable(cacheNames = BasCacheNames.CITY_USER_PLACE, key = "#userId")
    @Override
    public List<CityWhUserPlaceVo> getPlaceByUser(Long userId) {
        var map = this.getPlaceByUser(Lists.newArrayList(userId));
        if (CollUtil.isEmpty(map)) {
            return new ArrayList<>();
        }
        return map.get(userId);
    }

    @Override
    public Map<Long, List<CityWhUserPlaceVo>> getPlaceByUser(List<Long> userIds) {
        List<CityWhUserPlace> list = cityWhUserPlaceMapper.selectByUserIds(userIds);
        if (CollUtil.isEmpty(list)) {
            return new HashMap<>();
        }
        LambdaQueryWrapper<CityWhPlace> lqw = Wrappers.lambdaQuery();
        lqw.in(CityWhPlace::getId, list.stream().map(CityWhUserPlace::getPlaceId).toList())
                .select(CityWhPlace::getId, CityWhPlace::getPlaceName);
        var map = cityWhPlaceMapper.selectList(lqw).stream().collect(Collectors.toMap(CityWhPlace::getId, i -> i));

        Map<Long, List<CityWhUserPlaceVo>> result = new HashMap<>();

        for (CityWhUserPlace user : list) {
            if (map.get(user.getPlaceId()) != null) {
                var t = result.getOrDefault(user.getUserId(), new ArrayList<>());

                CityWhUserPlaceVo placeVo = new CityWhUserPlaceVo();
                placeVo.setUserId(user.getUserId());
                placeVo.setPlaceId(user.getPlaceId());
                placeVo.setPlaceName(map.get(user.getPlaceId()).getPlaceName());

                t.add(placeVo);
                result.put(user.getUserId(), t);
            }
        }
        return result;
    }

    private void clearUserPlaceRelation(Long placeId) {
        //找出关联的用户，并清除缓存
        List<Long> userIds = cityWhUserPlaceMapper.selectUserIdByPlaceId(placeId);
        if (CollUtil.isNotEmpty(userIds)) {
            for (Long userId : userIds) {
                CacheUtils.evict(BasCacheNames.CITY_USER_PLACE, userId);
            }
            cityWhUserPlaceMapper.deleteByPlaceId(placeId);
        }
    }

    /**
     * 返回提货点id 用于查询过滤
     *
     * @param userId
     * @return
     */
    @Override
    public List<Long> getPlaceIdsByUser(Long userId) {
        List<Long> places = getPlaceByUser(userId).stream().map(CityWhUserPlaceVo::getPlaceId).toList();
        if (!CollUtil.isEmpty(places)) {
            // 已关联了提货点，直接返回
            return places;
        }
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (Objects.equals(loginUser.getUserId(), userId)) {
            // 获取关联的城市仓id
            Long relationId = loginUser.getRelationId();
            // 没有关联提货点，需求是默认返回所有提货点id
            return selectListByCityWhId(relationId).stream().map(CityWhPlaceVo::getId).toList();
        }
        return places;
    }

    @Override
    public List<CityWhPlace> searchByName(String name){
        LambdaQueryWrapper<CityWhPlace> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(CityWhPlace::getPlaceName, name);
        return cityWhPlaceMapper.selectList(queryWrapper);
    }
    
    @Override
    public List<CityWhPlace> listByCityWhId(Long cityWhId){
        LambdaQueryWrapper<CityWhPlace> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CityWhPlace::getCityWhId, cityWhId);
        return cityWhPlaceMapper.selectList(queryWrapper);
    }

    @Override
    public List<CityWhPlace> listByCityWhIds(Collection<? extends Serializable> whIdList){
        LambdaQueryWrapper<CityWhPlace> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CityWhPlace::getCityWhId, whIdList);
        return cityWhPlaceMapper.selectList(queryWrapper);
    }

}