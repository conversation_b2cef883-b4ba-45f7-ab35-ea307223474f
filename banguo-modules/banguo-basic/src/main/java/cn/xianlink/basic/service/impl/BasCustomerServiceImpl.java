package cn.xianlink.basic.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.xianlink.basic.api.domain.bo.RemoteBasCustomerQueryBo;
import cn.xianlink.basic.api.domain.vo.RemoteCustomerVo;
import cn.xianlink.basic.api.enums.OssBusinessTypeEnum;
import cn.xianlink.basic.api.enums.OssTagEnum;
import cn.xianlink.basic.domain.BasCustomer;
import cn.xianlink.basic.domain.BasCustomerRecord;
import cn.xianlink.basic.domain.bo.customer.*;
import cn.xianlink.basic.domain.vo.citywh.CityWhVo;
import cn.xianlink.basic.domain.vo.customer.BasCustomerCountVo;
import cn.xianlink.basic.domain.vo.customer.BasCustomerSimperVo;
import cn.xianlink.basic.domain.vo.customer.BasCustomerVo;
import cn.xianlink.basic.mapper.BasCustomerMapper;
import cn.xianlink.basic.mapper.BasCustomerRecordMapper;
import cn.xianlink.basic.service.IBasCustomerService;
import cn.xianlink.basic.service.IBasPubAreaService;
import cn.xianlink.basic.service.ICityWhService;
import cn.xianlink.bi.api.RemoteBiOrderService;
import cn.xianlink.bi.api.domain.vo.RemoteCustomerLossRateVo;
import cn.xianlink.bi.api.domain.vo.RemoteOrderLossRateVo;
import cn.xianlink.common.api.enums.basic.CustomerAfterSaleStatusEnum;
import cn.xianlink.common.api.enums.basic.CustomerAfterSaleTypeEnum;
import cn.xianlink.common.api.enums.basic.UpdateLogTypeEnum;
import cn.xianlink.common.api.enums.system.OrgTypeEnum;
import cn.xianlink.common.api.enums.system.SysUserTypeEnum;
import cn.xianlink.common.core.enums.StatusEnum;
import cn.xianlink.common.core.enums.YNStatusEnum;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.order.api.RemoteReceiveGoodsService;
import cn.xianlink.order.api.bo.RemoteGetCustomerBo;
import cn.xianlink.product.api.RemoteSkuSalesStatisticsService;
import cn.xianlink.resource.api.RemoteFileService;
import cn.xianlink.resource.api.domain.RemoteOssBo;
import cn.xianlink.system.api.RemoteSysOrgService;
import cn.xianlink.system.api.RemoteUserService;
import cn.xianlink.system.api.domain.bo.RemoteSysOrgBo;
import cn.xianlink.system.api.domain.bo.RemoteUserBo;
import cn.xianlink.system.api.domain.bo.RemoteUserParamBo;
import cn.xianlink.system.api.model.LoginUser;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

/**
 * 客户Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-09
 */
@RequiredArgsConstructor
@Service
@CustomLog
public class BasCustomerServiceImpl implements IBasCustomerService {

    private final transient BasCustomerMapper basCustomerMapper;
    private final transient IBasPubAreaService basPubAreaService;
    private final transient BasCustomerRecordMapper basCustomerRecordMapper;
    private final transient RemoteUserService remoteUserService;
    private final transient ICityWhService iCityWhService;
    @DubboReference
    private final transient RemoteSysOrgService remoteSysOrgService;
    @DubboReference
    private final transient RemoteReceiveGoodsService receiveGoodsService;


    @DubboReference
    private RemoteFileService remoteFileService;
    @DubboReference
    private RemoteSkuSalesStatisticsService remoteSkuSalesStatisticsService;
    @DubboReference
    private RemoteBiOrderService remoteBiOrderService;

    /**
     * 查询客户信息
     *
     * @param id
     */
    @Override
    public BasCustomerVo selectByIdOrCode(Long id, String code) {
        BasCustomerVo customerVo = null;
        if (id != null) {
            customerVo = basCustomerMapper.selectVoById(id);
        } else {
            customerVo = basCustomerMapper.selectVoByCode(code);
        }

        if (ObjectUtil.isNotEmpty(customerVo)) {
            loadAdminInfo(ListUtil.toList(customerVo));

            if (customerVo.getCityWhId() != null) {
                CityWhVo city = iCityWhService.selectAndCheckNullById(customerVo.getCityWhId());
                customerVo.setCityWhName(city.getCityWhName());
                customerVo.setCityWhCode(city.getCityWhCode());
            }
        }
        return customerVo;
    }

    @Override
    public List<RemoteCustomerVo> getByIds(List<Long> customerIds) {
        if (CollUtil.isEmpty(customerIds)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<BasCustomer> lqw = Wrappers.lambdaQuery();
//        lqw.select(BasCustomer::getId, BasCustomer::getCode, BasCustomer::getName, BasCustomer::getCityWhId, BasCustomer::getAfterSaleStatus, BasCustomer::getCreateCode, BasCustomer::getAdminCode);
        lqw.in(BasCustomer::getId, customerIds);
        return basCustomerMapper.selectList(lqw).stream().map(customer -> {
            RemoteCustomerVo remoteCustomerVo = new RemoteCustomerVo();
            remoteCustomerVo.setId(customer.getId());
            remoteCustomerVo.setCityWhId(customer.getCityWhId());
            remoteCustomerVo.setCode(customer.getCode());
            remoteCustomerVo.setName(customer.getName());
            remoteCustomerVo.setAlias(customer.getAlias());
            remoteCustomerVo.setAfterSaleStatus(customer.getAfterSaleStatus());
            remoteCustomerVo.setUserCode(customer.getAdminCode());
            remoteCustomerVo.setAreaCode(customer.getAreaCode());
            remoteCustomerVo.setAreaFullName(customer.getAreaFullName());
            remoteCustomerVo.setAddress(customer.getAddress());
            remoteCustomerVo.setUpdateAfterSaleTime(customer.getUpdateAfterSaleTime());
            remoteCustomerVo.setCreateTime(customer.getCreateTime());
            remoteCustomerVo.setAdminCode(customer.getAdminCode());
            return remoteCustomerVo;
        }).toList();
    }

    /**
     * 查询客户列表
     */
    @Override
    public TableDataInfo<BasCustomerVo> pageList(BasCustomerQueryBo bo, boolean currentIsSales) {
        List<Long> excludeCustomerIds = new ArrayList<>();
        if (!Objects.isNull(bo.getAoRecordId())){
            excludeCustomerIds = remoteSkuSalesStatisticsService.getStatisticsCustomers(bo.getAoRecordId());
        }
        if(ObjectUtil.isNotEmpty(bo.getKeyWord()) && (bo.getKeyWord().length() == 11 && StringUtils.isNumeric(bo.getKeyWord()))) {
            RemoteUserParamBo paramBo = new RemoteUserParamBo();
            paramBo.setPhoneNo(bo.getKeyWord());
            paramBo.setUserType(SysUserTypeEnum.CUSTOMER_USER.getType());
            List<RemoteUserBo> remoteUserBos = remoteUserService.queryByParam(paramBo);
            if (ObjectUtil.isNotEmpty(remoteUserBos)) {
                bo.setAdminCodeList(remoteUserBos.stream().map(RemoteUserBo::getUserCode).collect(Collectors.toList()));
            }
        }

        QueryWrapper<BasCustomer> lqw = Wrappers.query();
        lqw.eq(ObjectUtil.isNotNull(bo.getCityWhId()), "bc.city_wh_id", bo.getCityWhId());
        lqw.notIn(CollectionUtil.isNotEmpty(excludeCustomerIds), "bc.id", excludeCustomerIds);
        lqw.eq("bc.del_flag", 0);
        if (StringUtils.isNotBlank(bo.getName())) {
            lqw.and(w -> w.likeRight("bc.name", bo.getName()).or().likeRight("bc.alias", bo.getName()));
        }
        lqw.eq(StringUtils.isNotBlank(bo.getAdminCode()), "bc.admin_code", bo.getAdminCode());
        lqw.likeRight(ObjectUtil.isNotEmpty(bo.getAlias()), "bc.alias", bo.getAlias());
        lqw.likeRight(ObjectUtil.isNotEmpty(bo.getBusinessCategory()), "bc.business_category", bo.getBusinessCategory());
        lqw.likeRight(ObjectUtil.isNotEmpty(bo.getProvince()), "bc.area_full_name", bo.getProvince());
        lqw.likeRight(ObjectUtil.isNotEmpty(bo.getCity()), "bc.area_full_name", bo.getCity());
        lqw.likeRight(ObjectUtil.isNotEmpty(bo.getArea()), "bc.area_full_name", bo.getArea());
        lqw.likeRight(ObjectUtil.isNotEmpty(bo.getAddress()), "bc.address", bo.getAddress());
        lqw.eq(ObjectUtil.isNotNull(bo.getAfterSaleType()), "bc.after_sale_type", bo.getAfterSaleType());
        lqw.eq(ObjectUtil.isNotNull(bo.getStatus()), "bc.status", bo.getStatus());
        lqw.eq(ObjectUtil.isNotNull(bo.getAfterSaleStatus()), "bc.after_sale_status", bo.getAfterSaleStatus());
        lqw.ge(ObjectUtil.isNotNull(bo.getCreateTimeStart()), "bc.create_time", bo.getCreateTimeStart());
        lqw.lt(ObjectUtil.isNotNull(bo.getCreateTimeEnd()), "bc.create_time", bo.getCreateTimeEnd());
        lqw.ge(ObjectUtil.isNotNull(bo.getLastOrderTimeStart()), "bc.last_order_time", bo.getLastOrderTimeStart());
        lqw.lt(ObjectUtil.isNotNull(bo.getLastOrderTimeEnd()), "bc.last_order_time", bo.getLastOrderTimeEnd());
        //当前用户是销售员，只能看到自己负责的客户和未绑定的客户
        if (currentIsSales) {
            String userCode = LoginHelper.getLoginUser().getUserCode();
            lqw.and(w -> w.eq("bc.salesman_code", userCode).or().isNull("bc.salesman_code"));
        }
        //指定销售员和查询没有销售员的客户，两个条件互斥
        if (StringUtils.isNotBlank(bo.getSalesmanCode())) {
            lqw.eq("bc.salesman_code", bo.getSalesmanCode());
        } else {
            lqw.isNull(StringUtils.isNotBlank(bo.getNoSalesman()), "bc.salesman_code");
        }

        if (ObjectUtil.isNotEmpty(bo.getAdminCodeList())) {
            lqw.and(w -> w.in("bc.admin_code", bo.getAdminCodeList()));
        } else if (ObjectUtil.isNotEmpty(bo.getKeyWord())) {
            lqw.and(w -> w.like("bc.name", bo.getKeyWord()).or().like("bc.alias", bo.getKeyWord()));
        }

        //下单时间 - 筛选项
        if (bo.getOrderTimeOutStatus() != null) {
            Date now = new Date();
            if (bo.getOrderTimeOutStatus() == 1) {
                //3~7天未下单：3≤X≤7
                lqw.between("bc.last_order_time", DateUtil.endOfDay(DateUtil.offsetDay(now, -7)), DateUtil.beginOfDay(DateUtil.offsetDay(now, -3)));
            } else if (bo.getOrderTimeOutStatus() == 2){
                //超7天未下单：X＞7
                lqw.lt("bc.last_order_time", DateUtil.beginOfDay(DateUtil.offsetDay(now, -7)));
            }
        }
        // id查询详情
        lqw.ge(ObjectUtil.isNotNull(bo.getId()), "bc.id", bo.getId());

        Page page = bo.buildJoin();
        page.setCountId("customPageList_count");
        Page<BasCustomerVo> result = basCustomerMapper.customPageList(page, lqw);
        // 加载超管信息
        loadAdminInfo(result.getRecords());
        return TableDataInfo.build(result);
    }

    private void loadAdminInfo(List<BasCustomerVo> list) {
        if (list.size() > 0) {
            List<String> adminCodes = new ArrayList<>();
            list.forEach(vo -> {
                if (StrUtil.isNotBlank(vo.getAdminCode())) {
                    adminCodes.add(vo.getAdminCode());
                }
                if (StrUtil.isNotBlank(vo.getSalesmanCode())) {
                    adminCodes.add(vo.getSalesmanCode());
                }
            });
            if (adminCodes.size() == 0) {
                return;
            }
            Map<String, RemoteUserBo> userMap = remoteUserService.getUsersByUserCodes(adminCodes);
            list.forEach(vo -> {
                vo.setAdminName(userMap.getOrDefault(vo.getAdminCode(), new RemoteUserBo()).getRealName());
                vo.setAdminPhone(userMap.getOrDefault(vo.getAdminCode(), new RemoteUserBo()).getPhoneNo());
                vo.setAdminOriginPhone(vo.getAdminPhone());
                vo.setSalesmanName(userMap.getOrDefault(vo.getSalesmanCode(), new RemoteUserBo()).getRealName());
            });
        }
    }

    @Override
    public TableDataInfo<BasCustomerSimperVo> miniPageList(CustomerQueryMiniBo bo) {
        if(ObjectUtil.isNotEmpty(bo.getKeyWord()) && (bo.getKeyWord().length() == 11 && StringUtils.isNumeric(bo.getKeyWord()))) {
            RemoteUserParamBo paramBo = new RemoteUserParamBo();
            paramBo.setPhoneNo(bo.getKeyWord());
            paramBo.setUserType(SysUserTypeEnum.CUSTOMER_USER.getType());
            List<RemoteUserBo> remoteUserBos = remoteUserService.queryByParam(paramBo);
            if (ObjectUtil.isNotEmpty(remoteUserBos)) {
                bo.setAdminCodeList(remoteUserBos.stream().map(RemoteUserBo::getUserCode).collect(Collectors.toList()));
            }
        }
        LambdaQueryWrapper<BasCustomer> lqw = Wrappers.lambdaQuery();
        String useIndex = "";
        if(ObjectUtil.isNotNull(bo.getCityWhId())) {
            lqw.eq(BasCustomer::getCityWhId, bo.getCityWhId());
            useIndex = "true";
        }
        lqw.eq(StringUtils.isNotBlank(bo.getAdminCode()), BasCustomer::getAdminCode, bo.getAdminCode());
        lqw.eq(ObjectUtil.isNotNull(bo.getStatus()),BasCustomer::getStatus, bo.getStatus());
        lqw.eq(BasCustomer::getDelFlag,0);
        if (StringUtils.isNotBlank(bo.getName())) {
            lqw.and(w -> w.likeRight(BasCustomer::getName, bo.getName()).or().likeRight(BasCustomer::getAlias, bo.getName()));
        }
        if (StringUtils.isNotBlank(bo.getAddress())) {
            lqw.and(w -> w.likeRight(BasCustomer::getAreaFullName, bo.getAddress()).or().likeRight(BasCustomer::getAddress, bo.getAddress()));
        }
        // 销售员权限查询
        if (bo.getIsSalesmanQuery()) {
            lqw.and(w -> w.eq(BasCustomer::getSalesmanCode, bo.getSalesmanCode()).or().isNull(BasCustomer::getSalesmanCode));
        } else {
            lqw.eq(StringUtils.isNotBlank(bo.getSalesmanCode()), BasCustomer::getSalesmanCode, bo.getSalesmanCode());
        }
        if (ObjectUtil.isNotEmpty(bo.getAdminCodeList())) {
            lqw.and(q -> q.in(BasCustomer::getAdminCode, bo.getAdminCodeList()).or());
        } else if (ObjectUtil.isNotEmpty(bo.getKeyWord())) {
            lqw.and(w -> w.like(BasCustomer::getName, bo.getKeyWord()).or().like(BasCustomer::getAlias, bo.getKeyWord()));
        }
        //该判断是手动新增少货单，查询客户列表的入参，有就查，没有就返回空列表
        if (ObjectUtil.isNotEmpty(bo.getSaleDate())) {
            RemoteGetCustomerBo getCustomerBo = new RemoteGetCustomerBo();
            getCustomerBo.setSaleDate(bo.getSaleDate());
            getCustomerBo.setCityWhId(bo.getStockoutCityWhId());
            List<Long> customerList = receiveGoodsService.getCustomerList(getCustomerBo);
            log.keyword("手动创建少货单", bo.getSaleDate()).info("手动创建少货单获取到提货单的用户列表，：{}", customerList);
            if (ObjectUtil.isNotEmpty(customerList)) {
                lqw.in(BasCustomer::getId, customerList);
            } else {
                return TableDataInfo.build(Collections.emptyList());
            }
        }
        Page<BasCustomerVo> result = basCustomerMapper.miniPageList(bo.build(), lqw, useIndex);
        // 加载超管信息
        loadAdminInfo(result.getRecords());
        return TableDataInfo.build(MapstructUtils.convert(result.getRecords(), BasCustomerSimperVo.class), result.getTotal());
    }

    /**
     * 校验客户名称是否唯一
     *
     * @param name 客户名称
     * @return 结果
     */
    @Override
    public boolean checkNameUnique(String name) {
        boolean exist = basCustomerMapper.exists(new LambdaQueryWrapper<BasCustomer>()
                .eq(BasCustomer::getName, name));
        return !exist;
    }

    /**
     * 新增客户
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public int insertCustomer(BasCustomerAddBo bo) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        BasCustomer entity = MapstructUtils.convert(bo, BasCustomer.class);
        entity.setCode(createCustomerCode());
        entity.setAdminCode(loginUser.getUserCode());
        entity.setAfterSaleStatus(YNStatusEnum.ENABLE.getCode());
        entity.setAfterSaleType(CustomerAfterSaleTypeEnum.SYSTEM.getCode());
        validEntityBeforeSave(entity);
        if (StrUtil.isNotBlank(bo.getAreaCode())){
            entity.setAreaFullName(basPubAreaService.querAndCheckLeafByCode(bo.getAreaCode()).getFullName());
        }
        int num = basCustomerMapper.insert(entity);
        if (num > 0) {
            RemoteSysOrgBo remoteSysOrgBo = new RemoteSysOrgBo();
            remoteSysOrgBo.setCode(entity.getCode());
            remoteSysOrgBo.setName(entity.getName());
            remoteSysOrgBo.setRelationId(entity.getId());
            remoteSysOrgBo.setRelationType(OrgTypeEnum.CUSTOMER);
            remoteUserService.bindingSysOrg(loginUser.getUserId(), remoteSysOrgBo);
            // 添加操作记录
            addRecord(entity.getId(), UpdateLogTypeEnum.CREATE.getCode());
        }

        return num;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateCustomer(BasCustomerUpdateBo bo) {
        BasCustomer entity = MapstructUtils.convert(bo, BasCustomer.class);
        if (StrUtil.isNotBlank(bo.getAreaCode())) {
            entity.setAreaFullName(basPubAreaService.querAndCheckLeafByCode(bo.getAreaCode()).getFullName());
        }
        basCustomerMapper.updateById(entity);
        // 添加操作记录
        addRecord(entity.getId(), UpdateLogTypeEnum.MODIFY.getCode());
    }

    @Transactional
    @Override
    public void cityUpdate(CustomerUpdateCityBo bo) {
        BasCustomer oldEntity = basCustomerMapper.selectById(bo.getId());
        BasCustomer entity = MapstructUtils.convert(bo, BasCustomer.class);
        if (StrUtil.isNotBlank(bo.getAreaCode())) {
            entity.setAreaFullName(basPubAreaService.querAndCheckLeafByCode(bo.getAreaCode()).getFullName());
        }
        basCustomerMapper.updateById(entity);
        // 添加操作记录
        addRecord(entity.getId(), UpdateLogTypeEnum.MODIFY.getCode());

        String keyword = bo.getId().toString();
        if (CollUtil.isNotEmpty(bo.getPhotoAlbum())) {
            List<RemoteOssBo> bos = new ArrayList<>();
            for (String url : bo.getPhotoAlbum()) {
                RemoteOssBo ossBo = new RemoteOssBo();
                ossBo.setUrl(url);
                ossBo.setBusinessType(OssBusinessTypeEnum.CUSTOMER.getCode());
                ossBo.setKeyword(keyword);
                ossBo.setTag(OssTagEnum.IMAGE.getCode());
                bos.add(ossBo);
            }
            remoteFileService.deleteAndInsert(OssBusinessTypeEnum.CUSTOMER.getCode(), keyword, bos);
        } else {
            remoteFileService.delete(OssBusinessTypeEnum.CUSTOMER.getCode(), keyword);
        }
        if(entity.getStatus() != null && !entity.getStatus().equals(oldEntity.getStatus())) {
            //状态变化才更新
            updateStatus(entity.getId(), entity.getStatus());
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BasCustomer entity) {
        LambdaQueryWrapper<BasCustomer> lqw = Wrappers.lambdaQuery();
        lqw.eq(BasCustomer::getAdminCode, entity.getAdminCode());
        lqw.eq(BasCustomer::getDelFlag, 0);
        Long count = basCustomerMapper.selectCount(lqw);
        if (count != null && count > 0L) {
            throw new ServiceException("你已经注册了客户，不支持重复注册");
        }
    }
    private String createCustomerCode() {
        String code = "KH" + String.format("%08d", ThreadLocalRandom.current().nextInt(1, 99999999));
        LambdaQueryWrapper<BasCustomer> lqw = Wrappers.lambdaQuery();
        lqw.eq(BasCustomer::getCode, code);
        Long count = basCustomerMapper.selectCount(lqw);
        if (count != null && count > 0L) {
            code = createCustomerCode();
        }
        return code;
    }

    /**
     * 修改客户
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public int updateRemark(Long id, String remark) {
        BasCustomer entity = new BasCustomer();
        entity.setId(id);
        entity.setRemark(remark);
        int num = basCustomerMapper.updateById(entity);
        // 添加操作记录
        if (num > 0) {
            addRecord(entity.getId(), UpdateLogTypeEnum.REMARK.getCode());
        }
        return num;
    }

    /**
     * 修改状态
     *
     * @param id     客户id
     * @param status 状态，0为不可用；1为可用
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public int updateStatus(Long id, Integer status) {

        BasCustomer entity = new BasCustomer();
        entity.setId(id);
        entity.setStatus(status);
        int num = basCustomerMapper.updateById(entity);
        // 添加操作记录
        if (num > 0) {
            addRecord(id, status.intValue() == StatusEnum.ENABLE.getCode() ? UpdateLogTypeEnum.ENABLE.getCode() : UpdateLogTypeEnum.DISABLED.getCode());
        }

        // 调用system模块，完成关联的系统机构的状态更新
        remoteSysOrgService.changeStatus(id, OrgTypeEnum.CUSTOMER, StatusEnum.isEnable(status));

        return num;
    }

    /**
     * 修改最新下单时间和城市仓
     */
    @Override
    public void updateLastOrderTime(Long id, Long cityWhId, Long placeId) {
        BasCustomer entity = new BasCustomer();
        entity.setId(id);
        entity.setLastOrderTime(DateTime.now());
        entity.setCityWhId(cityWhId);
        entity.setLastOrderPlaceId(placeId);
        basCustomerMapper.updateById(entity);
    }

    @Override
    public void changeCity(Long customerId, Long cityWhId) {
        BasCustomer entity = new BasCustomer();
        entity.setId(customerId);
        entity.setCityWhId(cityWhId);
        basCustomerMapper.updateById(entity);
    }

    /**
     * 修改售后状态
     *
     * @param id     客户id
     * @param status 状态，0关闭；1开启
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public int updateAfterSaleStatus(Long id, Integer status) {
        BasCustomer entity = new BasCustomer();
        entity.setId(id);
        entity.setAfterSaleStatus(status);
        entity.setAfterSaleType(CustomerAfterSaleTypeEnum.MANUAL.getCode());
        entity.setUpdateAfterSaleTime(DateTime.now());
        int num = basCustomerMapper.updateById(entity);
        // 添加操作记录
        if (num > 0) {
            addRecord(id, status.intValue() == YNStatusEnum.ENABLE.getCode() ? UpdateLogTypeEnum.OPEN.getCode() : UpdateLogTypeEnum.CLOSE.getCode());
        }
        return num;
    }

    @Override
    public void enableOrCloseAfterSale(Set<Long> closeIds, Set<Long> enableIds) {
        if (CollUtil.isNotEmpty(closeIds)) {
            basCustomerMapper.batchEditAfterSale(closeIds, CustomerAfterSaleStatusEnum.CLOSE.getCode(), CustomerAfterSaleTypeEnum.SYSTEM.getCode());
        }
        if (CollUtil.isNotEmpty(enableIds)) {
            basCustomerMapper.batchEditAfterSale(enableIds, CustomerAfterSaleStatusEnum.OPEN.getCode(), CustomerAfterSaleTypeEnum.SYSTEM.getCode());
        }
    }

    /**
     *
     * @param customerId
     * @param updateType  操作类型，10新增，20修改，21备注，30状态启用，40状态禁用，50售后开启，60售后关闭
     */
    private void addRecord(Long customerId, Integer updateType){
        BasCustomerRecord entity = MapstructUtils.convert(basCustomerMapper.selectById(customerId), BasCustomerRecord.class);
        entity.setId(null);
        entity.setCustomerId(customerId);
        entity.setUpdateType(updateType);
        entity.setCreateCode(LoginHelper.getUserCode());
        entity.setCreateTime(null);
        entity.setCreateName(LoginHelper.getRealName());
        entity.setUpdateTime(null);
        entity.setUpdateCode(LoginHelper.getUserCode());
        entity.setUpdateName(LoginHelper.getRealName());
        basCustomerRecordMapper.insert(entity);
    }

    /**
     * 根据客户名称模糊查询客户id
     * @param name
     * @return
     */
    @Override
    public List<Long> getByName(String name) {
        return basCustomerMapper.getByName(name);
    }

    @Override
    public Long countCustomer(Long cityWhId, List<String> salesmans,String noSalesman) {
        return basCustomerMapper.countCustomer(cityWhId, salesmans,noSalesman);
    }

    @Override
    public List<Long> filterSalesmanCustomer(List<Long> customers, List<String> salesmans,String noSalesman) {
        if ((CollUtil.isEmpty(salesmans) || CollUtil.isEmpty(customers)) && StringUtils.isEmpty(noSalesman)) {
            return customers;
        }
        return basCustomerMapper.filterSalesmanCustomer(customers, salesmans,noSalesman);
    }

    @Override
    public List<Long> filterNoSalesmanCustomer(List<Long> orderCustomers) {
        if (CollUtil.isEmpty(orderCustomers)) {
            return orderCustomers;
        }
        return basCustomerMapper.filterNoSalesmanCustomer(orderCustomers);
    }

    /**
     * 获取注册客户
     * @param cityWhId
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public Long getRegisterCustomerCount(Long cityWhId,LocalDateTime startTime, LocalDateTime endTime) {
        return basCustomerMapper.selectCount(Wrappers.lambdaQuery(BasCustomer.class)
                .between(BasCustomer::getCreateTime, startTime, endTime)
                .eq(ObjectUtil.isNotNull(cityWhId),BasCustomer::getCityWhId, cityWhId));
    }

    @Override
    public Long getByAdminCode(String userCode) {
        if (StringUtils.isEmpty(userCode)){
            return null;
        }
        BasCustomer basCustomerVo = basCustomerMapper.selectOne(new LambdaQueryWrapper<BasCustomer>()
                .eq(BasCustomer::getAdminCode, userCode)
                .eq(BasCustomer::getDelFlag, 0));
        if (ObjectUtil.isNull(basCustomerVo)){
            return null;
        }
        return basCustomerVo.getId();
    }

    @Override
    public List<Long> getListByAdminCode(String userCode) {
        if (StringUtils.isEmpty(userCode)){
            return new ArrayList<>();
        }
        List<BasCustomer> list = basCustomerMapper.selectList(new LambdaQueryWrapper<BasCustomer>()
                .eq(BasCustomer::getAdminCode, userCode));
        if (list.size() == 0){
            return new ArrayList<>();
        }
        return list.stream().map(BasCustomer::getId).collect(Collectors.toList());
    }

    @Override
    public void customerMostOrderSku(Map<Long, String> customerMostSkuMap) {
        if (CollUtil.isEmpty(customerMostSkuMap)) {
            return;
        }
        List<BasCustomer> updateList = new ArrayList<>();
        customerMostSkuMap.forEach((k, v) -> {
            BasCustomer c = new BasCustomer();
            c.setId(k);
            c.setMostSku(v);
            updateList.add(c);
        });
        basCustomerMapper.updateBatchById(updateList);
    }

    /**
     * 根据手机号获取客户id
     */
    @Override
    public List<Long> getIdByPhones(List<String> list) {
        if (CollUtil.isNotEmpty(list)) {
            List<RemoteUserBo> users = remoteUserService.getUsersByPhoneList(list, SysUserTypeEnum.CUSTOMER_USER.getType());
            if (CollUtil.isNotEmpty(users)) {
                //获取customerId
                List<Long> orgIds = users.stream().map(RemoteUserBo::getOrgId).toList();
                List<RemoteSysOrgBo> orgBos = remoteSysOrgService.getOrgListByIds(orgIds);
                List<Long> ids = orgBos.stream().filter(l -> l.getRelationType().getType().equals(OrgTypeEnum.CUSTOMER.getType()) && l.getStatus())
                        .map(RemoteSysOrgBo::getRelationId).toList();
                if (CollUtil.isEmpty(ids)) {
                    return null;
                }
                List<BasCustomer> basCustomers = basCustomerMapper.selectList(new LambdaQueryWrapper<BasCustomer>()
                        .in(BasCustomer::getId, ids));
                if (CollUtil.isNotEmpty(basCustomers)) {
                    return basCustomers.stream().map(BasCustomer::getId).toList();
                }
            }
        }
        return null;
    }

    @Override
    public List<RemoteCustomerVo> findCustomers(String customerName, String customerAlias) {
        LambdaQueryWrapper<BasCustomer> lqw = Wrappers.lambdaQuery(BasCustomer.class);
        lqw.like(StringUtils.isNotBlank(customerName), BasCustomer::getName, customerName);
        lqw.like(StringUtils.isNotBlank(customerAlias), BasCustomer::getAlias, customerAlias);
        List<BasCustomerVo> voList = basCustomerMapper.selectVoList(lqw);
        return BeanUtil.copyToList(voList, RemoteCustomerVo.class);
    }

    /**
     * 根据id获取客户id
     * @param customerId
     * @return
     */
    @Override
    public Long getIdById(Long customerId) {
        BasCustomerVo basCustomerVo = basCustomerMapper.selectVoOne(new LambdaQueryWrapper<BasCustomer>().eq(BasCustomer::getId, customerId)
                .select(BasCustomer::getId));
        if (ObjectUtil.isNull(basCustomerVo)){
            return null;
        }
        return basCustomerVo.getId();
    }

    /**
     * 开启符合标准的客户售后
     */
    @Override
    public Integer openCustomerAfterSale() {
        List<Long> updateIdList = new ArrayList<>();
        //获取售后时间关闭超过30天的用户
        List<BasCustomer> list = basCustomerMapper.selectList(new LambdaQueryWrapper<BasCustomer>()
                .eq(BasCustomer::getAfterSaleStatus, YNStatusEnum.DISABLE.getCode())
                .lt(BasCustomer::getUpdateAfterSaleTime, DateUtil.offsetDay(new Date(), -30)));
        if (CollUtil.isNotEmpty(list)){
            List<Long> customerIds = list.stream().map(BasCustomer::getId).toList();
            //获取对应订单报损率数据
            RemoteOrderLossRateVo lossRateVo = remoteBiOrderService.getLossRate(customerIds);
            if (ObjectUtil.isNotEmpty(lossRateVo) && CollUtil.isNotEmpty(lossRateVo.getCustomerLossRateVos())){
                for (RemoteCustomerLossRateVo customerLossRateVo : lossRateVo.getCustomerLossRateVos()){
                    if (customerLossRateVo.getLossRate().compareTo(lossRateVo.getLossRate()) <= 0
                    && customerLossRateVo.getLossCountRate().compareTo(lossRateVo.getLossCountRate()) <= 0){
                        updateIdList.add(customerLossRateVo.getCustomerId());
                    }
                    if (CollUtil.isNotEmpty(updateIdList)){
                        basCustomerMapper.update(null, new LambdaUpdateWrapper<BasCustomer>()
                                .in(BasCustomer::getId, updateIdList)
                                .set(BasCustomer::getAfterSaleStatus, YNStatusEnum.ENABLE.getCode())
                                .set(BasCustomer::getUpdateAfterSaleTime, new Date()));
                    }
                }
            }
        }
        return updateIdList.size();
    }

    /**
     * 按城市仓汇总查询 客户数
     */
    @Override
    public Map<Long, Integer> getCustomerCountListByCityWhIds(List<Long> cityWhIds, LocalDate createDate) {
        return basCustomerMapper.getCustomerCountListByCityWhIds(cityWhIds, createDate)
                .stream().collect(Collectors.toMap(BasCustomerCountVo::getCityWhId, BasCustomerCountVo::getCustomerCount));
    }

    /**
     * 查询客户列表
     */
    @Override
    public TableDataInfo<BasCustomerVo> findCustomerPage(RemoteBasCustomerQueryBo bo) {
        LambdaQueryWrapper<BasCustomer> lqw = Wrappers.lambdaQuery();
        lqw.like(Objects.nonNull(bo.getCodeOrName()), BasCustomer::getCode, bo.getCodeOrName())
                .or()
                .like(Objects.nonNull(bo.getCodeOrName()), BasCustomer::getName, bo.getCodeOrName());
        IPage<BasCustomerVo> page = basCustomerMapper.selectVoPage(bo.build(), lqw);
        return TableDataInfo.build(page);
    }

    @Override
    public RemoteCustomerVo getCustomerByAdminCode(String userCode) {
        if (StringUtils.isEmpty(userCode)){
            return null;
        }
        BasCustomer basCustomerVo = basCustomerMapper.selectOne(new LambdaQueryWrapper<BasCustomer>()
                .eq(BasCustomer::getAdminCode, userCode)
                .eq(BasCustomer::getDelFlag, 0)
                .eq(BasCustomer::getStatus, 1));
        if (ObjectUtil.isNull(basCustomerVo)){
            return null;
        }
        return BeanUtil.copyProperties(basCustomerVo, RemoteCustomerVo.class);
    }
}
