package cn.xianlink.basic.controller.platform;

import cn.xianlink.basic.domain.bo.regionlog.BasRegionLogisticsPlanCartBo;
import cn.xianlink.basic.domain.bo.regionlog.BasRegionLogisticsRmQueryBo;
import cn.xianlink.basic.domain.vo.citywh.CityWhPlaceVoApplet;
import cn.xianlink.basic.domain.vo.regionlog.BasRegionLogisticsSimpleVo;
import cn.xianlink.basic.domain.vo.regionlog.BasRegionLogisticsVo;
import cn.xianlink.basic.service.IBasRegionLogisticsPlanService;
import cn.xianlink.basic.service.IBasRegionLogisticsService;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.web.core.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 购物车 - 提货点查询
 *
 * <AUTHOR>
 * @folder 采集平台(小程序)/购物车/物流点&物流
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/platform/cityWhPlace")
public class PlatformCityWhPlaceController extends BaseController {
    private final transient PlaceVoAppletUtil placeVoAppletUtil;
    private final transient IBasRegionLogisticsService basRegionLogisticsService;
    private final transient IBasRegionLogisticsPlanService basRegionLogisticsPlanService;
    /**
     * @param cityWhId 城市仓id
     */
    @Operation(summary = "城市仓查询提货物流点 - 购物车")
    @PostMapping(value = "/queryPlaceList")
    public R<List<CityWhPlaceVoApplet>> queryPlaceList(
            @NotNull(message = "城市仓id为空") @RequestParam(name = "cityWhId") Long cityWhId) {
        return R.ok(placeVoAppletUtil.getPlaceVoAppletList(cityWhId, null, null));
    }

    @Operation(summary = "城市仓查询物流")
    @PostMapping(value = "/queryLogisticsList")
    public R<List<BasRegionLogisticsSimpleVo>> queryLogisticsList(
            @Validated @RequestBody BasRegionLogisticsPlanCartBo cartBo) {
//        List<BasRegionLogisticsPlanVo> vos = basRegionLogisticsPlanService.queryPlaceLogisticsPlan(
//                new BasRegionLogisticsPlanResidueBo().setPlaceId(cartBo.getPlaceId()).setRegionWhId(cartBo.getRegionWhId()));
//        return R.ok(MapstructUtils.convert(vos, BasRegionLogisticsPlanSimpleVo.class));
        List<BasRegionLogisticsVo> vos = basRegionLogisticsService.queryList(new BasRegionLogisticsRmQueryBo()
                .setPlaceId(cartBo.getPlaceId()).setRegionWhId(cartBo.getRegionWhId()));
        return R.ok(MapstructUtils.convert(vos, BasRegionLogisticsSimpleVo.class));
    }

}
