package cn.xianlink.basic.controller.admin;

import cn.xianlink.basic.domain.bo.offsitelog.BasOffsiteLogisticsAddBo;
import cn.xianlink.basic.domain.bo.offsitelog.BasOffsiteLogisticsEditBo;
import cn.xianlink.basic.domain.bo.offsitelog.BasOffsiteLogisticsQueryBo;
import cn.xianlink.basic.domain.bo.offsitelog.BasOffsiteLogisticsStatusBo;
import cn.xianlink.basic.domain.vo.offsitelog.BasOffsiteLogisticsVo;
import cn.xianlink.basic.service.IBasOffsiteLogisticsService;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.core.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
 * 基采物流
 * <AUTHOR>
 * @date 2024-03-21
 * @folder 般果管理中心/物流/基采物流
 */
@Tag(name = "基采物流")
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/basOffsiteLogistics")
public class BasOffsiteLogisticsController extends BaseController {

    private final transient IBasOffsiteLogisticsService basOffsiteLogisticsService;

    @Operation(summary = "分页查询")
    // @SaCheckPermission("basic:basOffsiteLogistics:list")
    @PostMapping("/page")
    public R<TableDataInfo<BasOffsiteLogisticsVo>> page(@RequestBody BasOffsiteLogisticsQueryBo queryBo) {
        return R.ok(basOffsiteLogisticsService.customPageList(queryBo));
    }

    /**
    @Operation(summary = "导出")
    @SaCheckPermission("basic:basOffsiteLogistics:export")
    @Log(title = "基采物流", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(@RequestBody BasOffsiteLogisticsQBo queryBo, HttpServletResponse response) {
        List<BasOffsiteLogisticsVo> list = basOffsiteLogisticsService.queryList(queryBo);
        ExcelUtil.exportExcel(list, "基采物流", BasOffsiteLogisticsVo.class, response);
    }*/

    @Operation(summary = "单条查询")
    // @SaCheckPermission("basic:basOffsiteLogistics:list")
    @GetMapping("/{id}")
    public R<BasOffsiteLogisticsVo> getInfo(@NotNull(message = "主键不能为空")
                                            @PathVariable Long id) {
        return R.ok(basOffsiteLogisticsService.queryById(id));
    }

    @Operation(summary = "新增单条")
    // @SaCheckPermission("basic:basOffsiteLogistics:create")
    // @Log(title = "基采物流", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/create")
    public R<Void> create(@Validated @RequestBody BasOffsiteLogisticsAddBo addBo) {
        return toAjax(basOffsiteLogisticsService.insertByBo(addBo));
    }

    @Operation(summary = "修改单条")
    // @SaCheckPermission("basic:basOffsiteLogistics:update")
    // @Log(title = "基采物流", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/update")
    public R<Void> update(@Validated @RequestBody BasOffsiteLogisticsEditBo editBo) {
        return toAjax(basOffsiteLogisticsService.updateByBo(editBo));
    }

    @Operation(summary = "修改状态")
    // @SaCheckPermission("basic:basOffsiteLogistics:update")
    // @Log(title = "基采物流", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/updateStatus")
    public R<Void> updateStatus(@Validated @RequestBody BasOffsiteLogisticsStatusBo statusBo) {
        return toAjax(basOffsiteLogisticsService.updateStatusByBo(statusBo));
    }

    /**
    @Operation(summary = "删除单条")
    @SaCheckPermission("basic:basOffsiteLogistics:delete")
    @Log(title = "基采物流", businessType = BusinessType.DELETE)
    @PostMapping("/delete/{id}")
    public R<Void> delete(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return toAjax(basOffsiteLogisticsService.deleteById(id, true));
    }*/
}
