package cn.xianlink.basic.controller.admin;

import cn.xianlink.basic.domain.bo.regionwh.RegionWhParkingAddBo;
import cn.xianlink.basic.domain.bo.regionwh.RegionWhParkingEditBo;
import cn.xianlink.basic.domain.bo.regionwh.RegionWhParkingQueryBo;
import cn.xianlink.basic.domain.bo.regionwh.RegionWhPeopleEditBo;
import cn.xianlink.basic.domain.vo.regionwh.RegionWhParkingVo;
import cn.xianlink.basic.service.IRegionWhParkingService;
import cn.xianlink.basic.service.IRegionWhPeopleService;
import cn.xianlink.common.api.enums.basic.ParkingTypeEnum;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.core.validate.QueryGroup;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.log.annotation.Log;
import cn.xianlink.common.log.enums.BusinessType;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.core.BaseController;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 总仓-装货位
 *
 * <AUTHOR>
 * @date 2024-03-21
 * @folder 般果管理中心/仓库/总仓-装货位
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/regionWhParking")
public class RegionWhParkingController extends BaseController {

    private final transient IRegionWhParkingService regionWhParkingService;
    private final transient IRegionWhPeopleService regionWhPeopleService;

    /**
     * 查询-下拉选项(固定装货位)
     *
     * @return 固定装货位列表
     */
    // @SaCheckPermission("basic:admin:regionWhParking:fixedListByRegionWhId")
    @GetMapping(value = "/fixedList/{regionWhId}")
    public R<List<RegionWhParkingVo>> fixedListByRegionWhId(@NotNull(message = "总仓id不能为空") @PathVariable Long regionWhId) {
        return R.ok(regionWhParkingService.selectListByRegionWhId(regionWhId, ParkingTypeEnum.FIXED.getCode()));
    }

    /**
     * 查询-列表
     *
     * @return 装货位列表
     */
    // @SaCheckPermission("basic:admin:regionWhParking:page")
    @PostMapping("/page")
    public R<TableDataInfo<RegionWhParkingVo>> page(@Validated(QueryGroup.class) @RequestBody RegionWhParkingQueryBo bo) {
        return R.ok(regionWhParkingService.customPageList(bo));
    }

    /**
     * 新增
     */
    // @SaCheckPermission("basic:admin:regionWhParking:create")
    @Log(title = "新增装货位", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/create")
    public R<Void> create(@Validated @RequestBody RegionWhParkingAddBo bo) {
        if (!regionWhParkingService.checkParkingNoUnique(bo.getRegionWhId(), bo.getParkingNo())) {
            return R.warn("装货位编号已存在");
        }
        if (StringUtils.isNotEmpty(bo.getLogisticsCode())) {
            if (!regionWhParkingService.checkLogisticsCodeUnique(bo.getRegionWhId(), bo.getLogisticsCode(), null)) {
                return R.warn("基础物流id已绑定车位");
            }
        }
        if (bo.getParkingType() == 2) {
            if (StringUtils.isEmpty(bo.getFixedParkingNo())) {
                return R.warn("固定车位编号不能为空");
            }
            if (regionWhParkingService.checkParkingNoUnique(bo.getRegionWhId(), bo.getFixedParkingNo())) {
                return R.warn("关联固定车位编号不存在");
            }
        }
        return toAjax(regionWhParkingService.insertRegionWh(bo));
    }

    /**
     * 修改
     */
    // @SaCheckPermission("basic:admin:regionWhParking:update")
    @Log(title = "修改装货位", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/update")
    public R<Void> update(@Validated @RequestBody RegionWhParkingEditBo bo) {
        return toAjax(regionWhParkingService.updateRegionWh(bo));
    }

    /**
     * 编辑装车员
     */
    // @SaCheckPermission("basic:admin:regionWhParking:update")
    @Log(title = "编辑装车员", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/editPeople")
    public R<Void> editPeople(@Validated @RequestBody RegionWhPeopleEditBo bo) {
        return toAjax(regionWhPeopleService.updateEdit(bo.getId(), bo.getPeopleList()));
    }

    /**
     * 删除
     */
    // @SaCheckPermission("basic:admin:regionWhParking:delete")
    @Log(title = "删除", businessType = BusinessType.DELETE)
    @PostMapping("/delete/{id}")
    public R<Void> delete(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return toAjax(regionWhParkingService.deleteById(id));
    }
}
