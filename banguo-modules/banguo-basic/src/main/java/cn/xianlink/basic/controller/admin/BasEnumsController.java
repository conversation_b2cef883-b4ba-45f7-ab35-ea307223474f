package cn.xianlink.basic.controller.admin;

import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.domain.vo.CoreEnumVo;
import cn.xianlink.common.core.service.DictService;
import cn.xianlink.common.web.core.BaseController;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 枚举查询
 *
 * @Author: chengxuegang
 * @Date: 2024/4/17 15:15
 * @folder 般果管理中心/系统管理/枚举查询
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/enums")
public class BasEnumsController extends BaseController {

    @Resource
    private final transient DictService dictService;


    @GetMapping("/{enumType}")
    public R<List<CoreEnumVo>> queryEnum(@PathVariable String enumType) {
        return R.ok(dictService.getAllEnumByEnumType(enumType));
    }

}
