package cn.xianlink.basic.controller.admin;

import cn.xianlink.basic.domain.bo.regionlog.BasRegionLogisticsPlanAddBo;
import cn.xianlink.basic.domain.bo.regionlog.BasRegionLogisticsPlanEditBo;
import cn.xianlink.basic.domain.bo.regionlog.BasRegionLogisticsPlanQueryBo;
import cn.xianlink.basic.domain.vo.regionlog.BasRegionLogisticsPlanVo;
import cn.xianlink.basic.service.IBasRegionLogisticsPlanService;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.core.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 基础吨位
 * <AUTHOR>
 * @date 2024-03-21
 * @folder 般果管理中心/物流/基础吨位
 */
@Tag(name = "基础吨位")
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/basRegionLogisticsPlan")
public class BasRegionLogisticsPlanController extends BaseController {

    private final transient IBasRegionLogisticsPlanService basRegionLogisticsPlanService;

    @Operation(summary = "分页查询")
    // @SaCheckPermission("basic:basRegionLogisticsPlan:list")
    @PostMapping("/page")
    public R<TableDataInfo<BasRegionLogisticsPlanVo>> page(@Validated @RequestBody BasRegionLogisticsPlanQueryBo queryBo) {
        return R.ok(basRegionLogisticsPlanService.customPageList(queryBo));
    }

    /**
    @Operation(summary = "导出")
    @SaCheckPermission("basic:basRegionLogisticsPlan:export")
    @Log(title = "基础吨位", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(@Validated(QueryGroup.class)  @RequestBody BasRegionLogisticsPlanQBo queryBo, HttpServletResponse response) {
        List<BasRegionLogisticsPlanVo> list = basRegionLogisticsPlanService.customList(queryBo);
        ExcelUtil.exportExcel(list, "基础吨位", BasRegionLogisticsPlanVo.class, response);
    }*/

    @Operation(summary = "单条查询")
    // @SaCheckPermission("basic:basRegionLogisticsPlan:list")
    @GetMapping("/{id}")
    public R<BasRegionLogisticsPlanVo> getInfo(@NotNull(message = "主键不能为空")
                                               @PathVariable Long id) {
        return R.ok(basRegionLogisticsPlanService.customById(id));
    }

    @Operation(summary = "新增单条")
    // @SaCheckPermission("basic:basRegionLogisticsPlan:create")
    // @Log(title = "基础吨位", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/create")
    public R<Void> create(@Validated @RequestBody BasRegionLogisticsPlanAddBo addBo) {
        return toAjax(basRegionLogisticsPlanService.insertByBo(addBo));
    }

    @Operation(summary = "修改单条")
    // @SaCheckPermission("basic:basRegionLogisticsPlan:update")
    // @Log(title = "基础吨位", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/update")
    public R<Void> update(@Validated @RequestBody BasRegionLogisticsPlanEditBo editBo) {
        return toAjax(basRegionLogisticsPlanService.updateByBo(editBo));
    }

    @Operation(summary = "删除单条")
    // @SaCheckPermission("basic:basRegionLogisticsPlan:delete")
    // @Log(title = "基础吨位", businessType = BusinessType.DELETE)
    @PostMapping("/delete/{id}")
    public R<Void> delete(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return toAjax(basRegionLogisticsPlanService.deleteById(id, true));
    }
}
