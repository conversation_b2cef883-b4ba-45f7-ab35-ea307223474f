package cn.xianlink.basic.controller.platform;

import cn.hutool.core.util.NumberUtil;
import cn.xianlink.basic.domain.bo.regionlog.BasRegionLogisticsPlanResidueBo;
import cn.xianlink.basic.domain.vo.citywh.CityWhPlaceVo;
import cn.xianlink.basic.domain.vo.regionlog.BasRegionLogisticsPlanResidueVo;
import cn.xianlink.basic.domain.vo.regionlog.BasRegionLogisticsVo;
import cn.xianlink.basic.service.IBasRegionLogisticsPlanService;
import cn.xianlink.basic.service.IBasRegionLogisticsService;
import cn.xianlink.basic.service.ICityWhPlaceService;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.web.core.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 基础吨位
 * <AUTHOR>
 * @date 2024-03-21
 * @folder 采集平台(小程序)/物流/基础吨位
 */
@Tag(name = "基础吨位")
@Validated
@RequiredArgsConstructor
@RestController("PlatformBasRegionLogisticsPlanController")
@RequestMapping("/platform/basRegionLogisticsPlan")
public class BasRegionLogisticsPlanController extends BaseController {

    private final transient IBasRegionLogisticsPlanService basRegionLogisticsPlanService;
    private final transient IBasRegionLogisticsService basRegionLogisticsService;
    private final transient ICityWhPlaceService cityWhPlaceService;

    @Operation(summary = "查询物流线剩余吨位")
    // @SaCheckPermission("basic:basRegionLogisticsPlan:list")
    @PostMapping("queryResidueList")
    public R<List<BasRegionLogisticsPlanResidueVo>> queryResidueList(@Validated @RequestBody BasRegionLogisticsPlanResidueBo residueBo) {
        setParentPlaceId(residueBo);
        List<BasRegionLogisticsPlanResidueVo> vos = MapstructUtils.convert(
                basRegionLogisticsPlanService.queryPlaceLogisticsPlan(residueBo), BasRegionLogisticsPlanResidueVo.class);
        // 去掉不可用 和 不显示的
        Map<Long, List<BasRegionLogisticsVo>> logisticsMap = basRegionLogisticsService.selectListByStatus1()
                .stream().collect(Collectors.groupingBy(BasRegionLogisticsVo::getId));
        vos = vos.stream().filter(vo -> logisticsMap.containsKey(vo.getLogisticsId()))
                .peek(vo -> vo.setIsDisplayWeight(logisticsMap.get(vo.getLogisticsId()).get(0).getIsDisplayWeight())).toList();
        return R.ok(vos);
    }

    @Operation(summary = "查询总仓剩余吨位")
    // @SaCheckPermission("basic:basRegionLogisticsPlan:list")
    @PostMapping("queryResidueRegionWh")
    public R<List<BasRegionLogisticsPlanResidueVo>> queryResidueRegionWh(@Validated @RequestBody BasRegionLogisticsPlanResidueBo residueBo) {
        setParentPlaceId(residueBo);
        return R.ok(basRegionLogisticsPlanService.queryPlaceResidueWeight(residueBo));
    }

    @Operation(summary = "查询剩余吨位汇总")
    // @SaCheckPermission("basic:basRegionLogisticsPlan:list")
    @PostMapping("queryResidueSum")
    public R<BasRegionLogisticsPlanResidueVo> queryResidueSum(@Validated @RequestBody BasRegionLogisticsPlanResidueBo residueBo) {
        setParentPlaceId(residueBo);
        List<BasRegionLogisticsPlanResidueVo> list = basRegionLogisticsPlanService.queryPlaceResidueWeight(residueBo);
        BasRegionLogisticsPlanResidueVo residueVo = new BasRegionLogisticsPlanResidueVo();
        residueVo.setResidueWeight(NumberUtil.add(list.stream().map(BasRegionLogisticsPlanResidueVo::getResidueWeight).toList().toArray(new BigDecimal[0])));
        return R.ok(residueVo);
    }

    private void setParentPlaceId(BasRegionLogisticsPlanResidueBo residueBo) {
        CityWhPlaceVo placeVo = cityWhPlaceService.selectAndCheckNullById(residueBo.getPlaceId());
        if (placeVo.getParentPlaceId() != null) {
            residueBo.setPlaceId(placeVo.getParentPlaceId());
        }
    }

}
