package cn.xianlink.basic.controller.region;

import cn.xianlink.basic.domain.bo.freightlog.*;
import cn.xianlink.basic.domain.vo.freightlog.BasPortageTeamPageVo;
import cn.xianlink.basic.domain.vo.freightlog.BasPortageTeamVo;
import cn.xianlink.basic.domain.vo.freightlog.BasPortageWorkerVo;
import cn.xianlink.basic.service.IBasPortageTeamService;
import cn.xianlink.common.api.util.RoleUtil;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.system.api.model.LoginUser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 装卸队管理
 *
 * <AUTHOR>
 * @date 2024-03-21
 * @folder 总仓助手(小程序)/物流/装卸队管理
 */
@Tag(name = "装卸队管理")
@Validated
@RequiredArgsConstructor
@RestController("RegionBasPortageTeamController")
@RequestMapping("/region/basPortageTeam")
public class BasPortageTeamController extends BaseController {

    private final transient IBasPortageTeamService basPortageTeamService;

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    public R<TableDataInfo<BasPortageTeamPageVo>> page(@RequestBody BasPortageTeamQueryBo queryBo) {
        return R.ok(basPortageTeamService.customPageList(queryBo));
    }

    @Operation(summary = "列表查询")
    @PostMapping("/list")
    public R<List<BasPortageTeamPageVo>> list(@RequestBody BasPortageTeamQueryBo queryBo) {
        return R.ok(basPortageTeamService.customList(queryBo));
    }

    @Operation(summary = "新增单条")
    @RepeatSubmit()
    @PostMapping("/create")
    public R<Void> create(@Validated @RequestBody BasPortageTeamAddBo addBo) {
        return toAjax(basPortageTeamService.insertByBo(addBo));
    }

    @Operation(summary = "修改单条")
    @RepeatSubmit()
    @PostMapping("/update")
    public R<Void> update(@Validated @RequestBody BasPortageTeamEditBo editBo) {
        return toAjax(basPortageTeamService.updateByBo(editBo));
    }

    @Operation(summary = "修改状态")
    @RepeatSubmit()
    @PostMapping("/updateStatus")
    public R<Void> updateStatus(@Validated @RequestBody BasPortageTeamStatusBo statusBo) {
        return toAjax(basPortageTeamService.updateStatusByBo(statusBo));
    }

    @Operation(summary = "删除单条")
    @PostMapping("/delete/{id}")
    public R<Void> delete(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return toAjax(basPortageTeamService.deleteById(id, true));
    }

    @Operation(summary = "装卸队列表")
    @PostMapping("/teamList")
    public R<List<BasPortageTeamVo>> teamList(@RequestBody BasPortageTeamRmQueryBo queryBo) {
        return R.ok(basPortageTeamService.queryList(queryBo.setAllStatus(true)));
    }

    @Operation(summary = "装卸队列表（当前用户下）")
    @PostMapping("/userTeamList")
    public R<List<BasPortageTeamVo>> userTeamList(@RequestBody BasPortageTeamRmQueryBo queryBo) {
        LoginUser user = LoginHelper.getLoginUser();
        if (!RoleUtil.isRegionAdmin(user.getRolePermission())) {
            // 用户装卸队查询权限
            queryBo.setTeamIds(basPortageTeamService.queryWorkers(new BasPortageWorkerRmQueryBo()
                    .setWorkerId(user.getUserId()).setAllStatus(true)).stream().map(BasPortageWorkerVo::getTeamId).toList());
        }
        return R.ok(basPortageTeamService.queryList(queryBo.setAllStatus(true)));
    }

    @Operation(summary = "装卸队队员")
    @PostMapping("/teamWorkerList")
    public R<List<BasPortageWorkerVo>> teamWorkerList(@RequestBody BasPortageWorkerRmQueryBo queryBo) {
        return R.ok(basPortageTeamService.queryWorkers(queryBo.setAllStatus(true)));
    }

}
