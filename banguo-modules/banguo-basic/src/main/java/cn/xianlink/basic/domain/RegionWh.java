package cn.xianlink.basic.domain;

import cn.xianlink.common.api.enums.basic.RegionWhTypeEnum;
import cn.xianlink.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * region_wh，总仓定义表（大区）
 *
 * <AUTHOR>
 * @date 2024-03-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("bas_region_wh")
public class RegionWh extends BaseEntity {
    /**
     * 总仓id，主键
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 总仓编码
     */
    private String regionWhCode;
    /**
     * 总仓名称
     */
    private String regionWhName;
    /**
     * 超管代码[外键]
     */
    private String adminCode;
    /**
     * 状态，0为不可用；1为可用
     */
    private Integer status;
    /**
     * 是否可用状态变化时间
     */
    private Date statusTime;
    /**
     * 区域编码
     */
    private String areaCode;
    /**
     * 区域全称[冗余]
     */
    private String areaFullName;
    /**
     * 详细地址
     */
    private String address;
    /**
     * 经度
     */
    private String lng;
    /**
     * 纬度
     */
    private String lat;
    /**
     * 自动清库存时间；格式：HH:mm
     */
    private String clearWhTime;
    /**
     * 销售开始时间；格式：HH:mm
     */
    private String salesTimeStart;
    /**
     * 销售结束时间；格式：HH:mm
     */
    private String salesTimeEnd;
    /**
     * 提前销售结束时间；格式：HH:mm
     */
    private String earlySalesTimeEnd;
	/**
     * 发货结束时间；格式：HH:mm
     */
    private String deliveryTimeEnd;
    /**
     * 发货完成时间-自动履约补数(缺货、装车、发车)；格式：HH:mm
     */
    private String deliveryCompleteTime;
    /**
     * 关联部门id
     */
    private Long deptId;
    /**
     * 备注
     */
    private String remark;
    /**
     * 删除标志（0代表存在 存入这条数据的主键代表删除）
     */
    @TableLogic(delval = "id")
    private Long delFlag;

    /**
     * 类型, 1普通总仓, 0为地采
     * @see RegionWhTypeEnum
     */
    private String type;

    /**
     * 促销日
     */
    private String promoDay;

    /**
     * 质检报告是否必传(0否 1是)
     */
    private Integer qualityReport;

    /**
     * 劣品等级标准：报损订单，报损金额，报损率
     */
    private String badLevel;

    /**
     * 云仓服务费单价；元/斤
     */
    private BigDecimal cwServiceFeePrice;

    /**
     * 是否试点按订单项送货(0否 1是)
     */
    private Integer isOrderItemDelivery;
    /**
     * 生效销售日期
     */
    private LocalDate effectSaleDate;

    /**
     * 是否需要坑位，0否1是
     */
    private Integer isSaleNum;
}