package cn.xianlink.basic.controller.region;

import cn.hutool.core.bean.BeanUtil;
import cn.xianlink.basic.controller.admin.BasRegionLogisticsUtils;
import cn.xianlink.basic.domain.bo.regionlog.BasRegionLogisticsParkBo;
import cn.xianlink.basic.domain.bo.regionlog.BasRegionLogisticsQueryBo;
import cn.xianlink.basic.domain.bo.regionlog.BasRegionLogisticsRegionQueryBo;
import cn.xianlink.basic.domain.vo.regionlog.BasRegionLogisticsSimpleVo;
import cn.xianlink.basic.domain.vo.regionlog.BasRegionLogisticsVo;
import cn.xianlink.basic.service.IBasRegionLogisticsService;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.enums.YNStatusEnum;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.core.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 基础吨位
 * <AUTHOR>
 * @date 2024-06-06
 * @folder 总仓助手(小程序)/物流/基础物流
 */
@Tag(name = "基础物流")
@Validated
@RequiredArgsConstructor
@RestController("RegionBasRegionLogisticsController")
@RequestMapping("/region/basRegionLogistics")
public class BasRegionLogisticsController extends BaseController {

    private final transient IBasRegionLogisticsService basRegionLogisticsService;
    private final transient BasRegionLogisticsUtils basRegionLogisticsUtils;

    @Operation(summary = "装车位物流查询（排除被占用的物流）")
    @PostMapping("/parking")
    public R<List<BasRegionLogisticsSimpleVo>> parking(@Validated @RequestBody BasRegionLogisticsParkBo parkBo) {
        List<BasRegionLogisticsVo> vos = basRegionLogisticsService.queryParkRegionLogistics(parkBo);
        return R.ok(MapstructUtils.convert(vos, BasRegionLogisticsSimpleVo.class));
    }

    @Operation(summary = "总仓物流线查询（支持模糊名称)")
    @PostMapping("/logisticsList")
    public R<List<BasRegionLogisticsSimpleVo>> logisticsList(@Validated @RequestBody BasRegionLogisticsRegionQueryBo queryBo) {
        return R.ok(BeanUtil.copyToList(basRegionLogisticsUtils.logisticsList(queryBo), BasRegionLogisticsSimpleVo.class));
    }


    @Operation(summary = "总仓物流线分页查询（支持模糊名称)")
    @PostMapping("/page")
    public R<TableDataInfo<BasRegionLogisticsSimpleVo>> page(@RequestBody BasRegionLogisticsQueryBo queryBo) {
        // regionWhId, logisticsName
        queryBo.setStatus(YNStatusEnum.ENABLE.getCode());
        queryBo.setLogisticsType(0);
        queryBo.setIsDisplay(1);
        TableDataInfo<BasRegionLogisticsVo> table = basRegionLogisticsService.customPageList(queryBo);

        TableDataInfo<BasRegionLogisticsSimpleVo> newTable = new TableDataInfo<>();
        newTable.setRows(BeanUtil.copyToList(table.getRows(), BasRegionLogisticsSimpleVo.class));
        newTable.setTotal(table.getTotal());
        return R.ok(newTable);
    }

}
