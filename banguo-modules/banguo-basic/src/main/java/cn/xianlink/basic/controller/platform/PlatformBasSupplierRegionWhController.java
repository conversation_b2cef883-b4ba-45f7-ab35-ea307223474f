package cn.xianlink.basic.controller.platform;

import cn.xianlink.basic.domain.bo.supplier.SupplierRegionLinkBo;
import cn.xianlink.basic.domain.bo.supplier.SupplierRegionSaleLimitBo;
import cn.xianlink.basic.service.IBasSupplierRegionWhService;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.log.annotation.Log;
import cn.xianlink.common.log.enums.BusinessType;
import cn.xianlink.common.web.core.BaseController;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 供应商-总仓坑位
 *
 * <AUTHOR>
 * @folder 管理台
 * @date 2025/03/17 16:34:09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/platform/supplierRegionWh")
public class PlatformBasSupplierRegionWhController extends BaseController {

    private transient final IBasSupplierRegionWhService basSupplierRegionWhService;


    /**
     * 编辑供应商坑位
     */
    @Log(title = "编辑供应商坑位", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/update")
    public R<Void> batchUpdate(@Validated @RequestBody SupplierRegionLinkBo bo) {
        basSupplierRegionWhService.batchUpdate(bo.getRegionSaleLimitList());
        return R.ok();
    }

    /**
     * 供应商坑位查询
     */
    @GetMapping("/list")
    public R<List<SupplierRegionSaleLimitBo>> getList(@NotNull(message = "供应商id不能为空") @RequestParam(name = "supplierId") Long supplierId) {
        return R.ok(basSupplierRegionWhService.getBoListBySupplierId(supplierId));
    }

}
