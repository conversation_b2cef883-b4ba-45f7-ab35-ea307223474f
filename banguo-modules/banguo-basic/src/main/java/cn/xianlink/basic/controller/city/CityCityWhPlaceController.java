package cn.xianlink.basic.controller.city;

import cn.hutool.core.collection.CollUtil;
import cn.xianlink.basic.domain.bo.citywh.CityWhPlaceEditAddressBo;
import cn.xianlink.basic.domain.bo.citywh.CityWhPlaceEditBo;
import cn.xianlink.basic.domain.bo.citywh.CityWhPlaceQueryBo;
import cn.xianlink.basic.domain.vo.citywh.CityWhPlaceVo;
import cn.xianlink.basic.domain.vo.citywh.CityWhUserPlaceVo;
import cn.xianlink.basic.service.ICityWhPlaceService;
import cn.xianlink.basic.service.ICityWhService;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.log.annotation.Log;
import cn.xianlink.common.log.enums.BusinessType;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.system.api.model.LoginUser;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 提货点查询
 *
 * <AUTHOR>
 * @folder 城市仓端(小程序)/提货点
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/city/cityWhPlace")
public class CityCityWhPlaceController extends BaseController {
    private final transient ICityWhPlaceService cityWhPlaceService;
    private final transient ICityWhService cityWhService;
//    /**
//     * 查询二级提货点列表
//     */
//    @PostMapping(value = "/queryListByParentPlaceId")
//    public R<List<CityWhPlaceVo>> queryListByParentPlaceId(Long parentPlaceId) {
//        return R.ok(cityWhPlaceService.selectListByParentPlaceId(parentPlaceId));
//    }

    /**
     * 查询-列表
     *
     * @return 提货点列表
     */
    // @SaCheckPermission("basic:admin:cityWhPlace:page")
    @PostMapping("/page")
    public R<TableDataInfo<CityWhPlaceVo>> page(@RequestBody CityWhPlaceQueryBo bo) {
        bo.setCityWhId(LoginHelper.getLoginUser().getRelationId());
        return R.ok(cityWhPlaceService.pageList(bo));
    }

    /**
     * 用户提货点列表 - 有权限的自提点
     */
    @PostMapping(value = "/userPage")
    public R<TableDataInfo<CityWhPlaceVo>> userPage(@RequestBody CityWhPlaceQueryBo bo) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        bo.setCityWhId(loginUser.getRelationId());
        List<CityWhUserPlaceVo> userPlaces = cityWhPlaceService.getPlaceByUser(loginUser.getUserId());
        if (CollUtil.isEmpty(userPlaces)) {
            // 没配置返回城市仓所有的提货点
            return R.ok(cityWhPlaceService.pageList(bo));
        }

        bo.setPlaceIds(userPlaces.stream().map(e -> e.getPlaceId()).collect(Collectors.toList()));
        return R.ok(cityWhPlaceService.pageList(bo));
    }

//    /**
//     * 查询二级提货点列表 - 子仓物流管理
//     */
//    @PostMapping(value = "/userPageByLevel2")
//    public R<TableDataInfo<CityWhPlaceVo>> userPageByLevel2(@RequestBody CityWhPlaceQueryBo bo) {
//        LoginUser loginUser = LoginHelper.getLoginUser();
//        List<CityWhUserPlaceVo> userPlaces = cityWhPlaceService.getPlaceByUser(loginUser.getUserId());
//        if (CollUtil.isEmpty(userPlaces)) {
//            return R.ok(TableDataInfo.build());
//        }
//        bo.setCityWhId(loginUser.getRelationId());
//        bo.setPlaceOrParentIds(userPlaces.stream().map(e -> e.getPlaceId()).collect(Collectors.toList()));
//        bo.setPlaceLevel(2);
//        return R.ok(cityWhPlaceService.pageList(bo));
//    }


    /**
     * 修改提货点地址信息
     */
    @Log(title = "修改提货点地址信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/updateAddress")
    public R<Void> updateAddress(@Validated @RequestBody CityWhPlaceEditAddressBo bo) {
        CityWhPlaceEditBo editBo = new CityWhPlaceEditBo();
        BeanUtils.copyProperties(bo, editBo);
        return toAjax(cityWhPlaceService.updatePlace(editBo));
    }

}