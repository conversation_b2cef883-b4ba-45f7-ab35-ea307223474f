package cn.xianlink.basic.service;

import cn.xianlink.basic.api.domain.vo.RemoteOriginCityWhPlaceVo;
import cn.xianlink.basic.domain.CityWhPlace;
import cn.xianlink.basic.domain.bo.citywh.CityWhPlaceAddBo;
import cn.xianlink.basic.domain.bo.citywh.CityWhPlaceEditBo;
import cn.xianlink.basic.domain.bo.citywh.CityWhPlaceEditSonBo;
import cn.xianlink.basic.domain.bo.citywh.CityWhPlaceQueryBo;
import cn.xianlink.basic.domain.vo.citywh.CityWhPlaceSumVo;
import cn.xianlink.basic.domain.vo.citywh.CityWhPlaceVo;
import cn.xianlink.basic.domain.vo.citywh.CityWhUserPlaceVo;
import cn.xianlink.basic.domain.vo.citywh.CityWhVo;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.ibatis.annotations.Param;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 城市仓-提货点 业务层
 *
 * <AUTHOR>
 * @date 2024-03-21
 */
public interface ICityWhPlaceService extends IService<CityWhPlace> {

    /**
     * 查询提货点列表 by 城市仓编码
     * @param name
     * @param cityWhCode
     * @return
     */
    IPage<CityWhPlaceVo> selectListByName(String name, String cityWhCode);
    /**
     * 查询提货点列表 by 位置标题
     */
    List<CityWhPlaceVo> selectListByPositionTitle(String positionTitle);
    /**
     * 查询单条提货点信息
     * @return 提货点信息
     */
    CityWhPlaceVo selectAndCheckNullById(Long id);
    List<CityWhPlaceVo> selectListByParentPlaceId(Long parentPlaceId, Boolean isQueryLogistics);

    /**
     * 查询单条城市仓信息
     *
     * @param placeCode
     * @return 城市仓信息
     */
    CityWhPlaceVo selectAndCheckNullByCode(String placeCode);

    /**
     * 查询提货点列表
     *
     * @return 提货点列表
     */
    List<CityWhPlaceVo> selectListByCityWhIds(List<Long> cityWhIds);

    /**
     * 查询提货点列表
     *
     * @param cityWhId
     * @return 提货点列表
     */
    List<CityWhPlaceVo> selectListByCityWhId(Long cityWhId);

    /**
     * 提货点计数
     *
     * @return
     */
    List<CityWhPlaceSumVo> countByCityWhIds(List<Long> cityWhIds);


    /**
     * 查询提货点列表
     *
     * @return 提货点列表
     */
    List<CityWhPlaceVo> selectVoBatchIds(List<Long> placeIds);

    List<CityWhPlaceVo> queryNearPlaceListByLocation(@Param("lng") Double lng, @Param("lat") Double lat);

    /**
     * 查询提货点列表
     *
     * @return 提货点列表
     */
    TableDataInfo<CityWhPlaceVo> pageList(CityWhPlaceQueryBo bo);

    /**
     * 校验提货点名称 是否唯一
     *
     * @param placeName 提货点名称
     * @param excludeId   提货点id(排除)，可为空
     * @return 结果
     */
    boolean checkPlaceNameUnique(String placeName, Long excludeId);

    /**
     * 新增提货点
     *
     * @param bo 提货点信息
     * @return 结果
     */
    int insertPlace(CityWhPlaceAddBo bo, CityWhVo cityWhVo);
//    /**
//     * 批量新增提货点-二级
//     */
//    boolean batchInsertPlace(List<CityWhPlaceAddSonBo> bos);

    /**
     * 修改提货点
     *
     * @param bo 提货点信息
     * @return 结果
     */
    int updatePlace(CityWhPlaceEditBo bo);
    /**
     * 批量更新子提货点
     */
    boolean editSonPlace(List<CityWhPlaceEditSonBo> bos);

    /**
     * 删除提货点
     *
     * @param id 提货点ID
     * @return 结果
     */
    int deleteById(Long id);

    /**
     * 绑定用户和提货点的关系
     * <AUTHOR> on 2024/11/20:11:03
     * @param placeIds
     * @param userId
     * @return void
     */
    void bindingUser(Long userId, List<Long> placeIds);

    /**
     * 查询用户绑定的提货点
     * <AUTHOR> on 2024/11/20:11:11
     * @param userId
     * @return java.util.List<cn.xianlink.basic.domain.vo.citywh.CityWhUserPlaceVo>
     */
    List<CityWhUserPlaceVo> getPlaceByUser(Long userId);

    Map<Long, List<CityWhUserPlaceVo>> getPlaceByUser(List<Long> userIds);

    List<Long> getPlaceIdsByUser(Long userId);

    /**
     * 根据名称查询提货点信息列表
     * @param name 名称
     * @return 提货点信息列表
     */
    List<CityWhPlace> searchByName(String name);

    /**
     * 根据城市仓id查询提货点信息列表
     * @param cityWhId 城市仓id
     * @return 提货点信息列表
     */
    List<CityWhPlace> listByCityWhId(Long cityWhId);

    /**
     * 根据城市仓id列表查询提货点信息列表
     *
     * @param whIdList 城市仓id列表
     * @return 提货点信息列表
     */
    List<CityWhPlace> listByCityWhIds(Collection<? extends Serializable> whIdList);

}