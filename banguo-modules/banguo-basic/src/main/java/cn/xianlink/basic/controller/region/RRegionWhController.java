package cn.xianlink.basic.controller.region;

import cn.xianlink.basic.domain.vo.regionwh.RegionWhVo;
import cn.xianlink.basic.service.IRegionWhService;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.web.core.BaseController;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;

/**
 * 总仓查询
 *
 * <AUTHOR>
 * @folder 总仓助手(小程序)/总仓
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/region/regionWh")
public class RRegionWhController extends BaseController {
    private final transient IRegionWhService regionWhService;

    /**
     * 查询-详情
     */
    @GetMapping("/getInfo/{regionWhId}")
    public R<RegionWhVo> getInfo(@NotNull(message = "总仓不能为空") @PathVariable Long regionWhId) {
        return R.ok(regionWhService.selectById(regionWhId));
    }

    /**
     * 根据总仓id获取这个总仓当前时间的销售日
     * @param regionWhId
     * @return
     */
    @GetMapping("/getSaleDate/{regionWhId}")
    public R<LocalDate> getSaleDate(@NotNull(message = "总仓不能为空") @PathVariable Long regionWhId) {
        return R.ok(regionWhService.getSaleDate(regionWhId));
    }
}
