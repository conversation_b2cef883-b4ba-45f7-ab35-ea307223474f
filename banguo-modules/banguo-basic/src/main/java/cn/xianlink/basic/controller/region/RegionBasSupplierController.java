package cn.xianlink.basic.controller.region;

import cn.xianlink.basic.domain.bo.supplier.BasSupplierEditAliasBo;
import cn.xianlink.basic.domain.bo.supplier.QuerySupplierDeliverPageBo;
import cn.xianlink.basic.domain.bo.supplier.UpdateSupplierDeliverStatusBo;
import cn.xianlink.basic.domain.vo.supplier.BasSupplierVo;
import cn.xianlink.basic.domain.vo.supplier.QuerySupplierDeliverPageVo;
import cn.xianlink.basic.service.IBasSupplierService;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.log.annotation.Log;
import cn.xianlink.common.log.enums.BusinessType;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.bo.BasRemarkBo;
import cn.xianlink.common.web.core.BaseController;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 供应商基础信息
 *
 * <AUTHOR>
 * @folder 总仓助手(小程序)/供应商
 * @date 2024-04-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/region/supplier")
public class RegionBasSupplierController extends BaseController {

    private final transient IBasSupplierService basSupplierService;

    /**
     * 根据id获取供应商详情
     * @param id
     * @return cn.xianlink.common.core.domain.R<cn.xianlink.basic.domain.vo.supplier.BasSupplierVo>
     * <AUTHOR> on 2024/4/15:11:12
     */
    @GetMapping("/get/{id}")
    public R<BasSupplierVo> getInfoById(@NotNull(message = "主键不能为空")
                                        @PathVariable Long id) {
        return R.ok(basSupplierService.queryById(id));
    }

    /**
     * 修改备注
     */
    @PostMapping("/remark")
    public R<Void> remark(@Validated @RequestBody BasRemarkBo bo) {
        return toAjax(basSupplierService.remark(bo));
    }

    /**
     * 根据总仓id查询有供货的供应商列表
     * @param bo
     * @return
     */
    @PostMapping("/querySupplierDeliverPage")
    public R<List<QuerySupplierDeliverPageVo>> querySupplierDeliverPage(@RequestBody QuerySupplierDeliverPageBo bo) {
        return R.ok(basSupplierService.querySupplierDeliverPage(bo));
    }

    /**
     * 根据总仓id查询供应商列表
     * <AUTHOR> on 2024/10/21:10:04
     * @param bo
     * @return cn.xianlink.common.core.domain.R<TableDataInfo<QuerySupplierDeliverPageVo>>
     */
    @PostMapping("/querySupplier")
    public R<TableDataInfo<QuerySupplierDeliverPageVo>> querySupplier(@RequestBody QuerySupplierDeliverPageBo bo) {
        return R.ok(basSupplierService.querySupplierByRegion(bo));
    }

    /**
     * 审核供应商是否能查询待送货列表
     * @param bo
     * @return
     */
    @PostMapping("/updateSupplierDeliverStatus")
    public R<Void> updateSupplierDeliverStatus(@RequestBody UpdateSupplierDeliverStatusBo bo) {
        return toAjax(basSupplierService.updateSupplierDeliverStatus(bo));
    }

    /**
     * 修改供应商别名
     */
    @Log(title = "修改供应商别名", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/updateAlias")
    public R<Void> updateAlias(@Validated @RequestBody BasSupplierEditAliasBo bo) {
        return toAjax(basSupplierService.updateAliasById(bo));
    }

}
