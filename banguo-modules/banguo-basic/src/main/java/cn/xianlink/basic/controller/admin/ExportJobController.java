package cn.xianlink.basic.controller.admin;

import cn.xianlink.basic.domain.export.ExportJobQueryBo;
import cn.xianlink.basic.domain.export.ExportJobVo;
import cn.xianlink.basic.service.IExportJobService;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.common.web.core.BaseController;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 离线导出
 * <AUTHOR> xiaodaibing on 2025-01-08 16:37
 * @folder 般果管理中心/离线导出
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/export")
public class ExportJobController  extends BaseController {

    private final IExportJobService exportJobService;


    /**
     * 导出任务列表
     * <AUTHOR> on 2025/1/8:17:13
     * @param bo
     * @return cn.xianlink.common.core.domain.R<cn.xianlink.common.mybatis.core.page.TableDataInfo<cn.xianlink.basic.domain.export.ExportJobVo>>
     */
    @PostMapping(value = "/page")
    public R<TableDataInfo<ExportJobVo>> page(@RequestBody ExportJobQueryBo bo){
        bo.setCreateCode(LoginHelper.getUserCode());
        return R.ok(exportJobService.jobTable(bo));
    }

    /**
     * 重试失败的导出任务
     * <AUTHOR> on 2025/1/8:17:20
     * @param jobId
     * @return cn.xianlink.common.core.domain.R<java.lang.Void>
     */
    @PutMapping(value = "/retry/{jobId}")
    public R<Void> exportRetry(@PathVariable Long jobId){
        exportJobService.failRetry(jobId);
        return R.ok();
    }
}
