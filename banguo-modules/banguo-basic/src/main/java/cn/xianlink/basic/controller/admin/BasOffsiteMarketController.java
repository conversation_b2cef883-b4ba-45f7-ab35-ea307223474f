package cn.xianlink.basic.controller.admin;

import cn.xianlink.basic.domain.bo.BasPubQueryBo;
import cn.xianlink.basic.domain.bo.offsitelog.BasOffsiteMarketEditBo;
import cn.xianlink.basic.domain.bo.offsitelog.BasOffsiteMarketQueryBo;
import cn.xianlink.basic.domain.bo.offsitelog.BasOffsiteMarketStatusBo;
import cn.xianlink.basic.domain.vo.BasPubVo;
import cn.xianlink.basic.domain.vo.offsitelog.BasOffsiteMarketVo;
import cn.xianlink.basic.service.IBasOffsiteMarketService;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.validate.AddGroup;
import cn.xianlink.common.core.validate.EditGroup;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.core.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 基采市场
 * <AUTHOR>
 * @date 2024-03-21
 * @folder 般果管理中心/物流/基采市场
 */
@Tag(name = "基采市场")
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/basOffsiteMarket")
public class BasOffsiteMarketController extends BaseController {

    private final transient IBasOffsiteMarketService basOffsiteMarketService;

    @Operation(summary = "分页查询")
    // @SaCheckPermission("basic:basOffsiteMarket:list")
    @PostMapping("/page")
    public R<TableDataInfo<BasOffsiteMarketVo>> page(@RequestBody BasOffsiteMarketQueryBo queryBo) {
        return R.ok(basOffsiteMarketService.queryPageList(queryBo));
    }

    /**
    @Operation(summary = "导出")
    // @SaCheckPermission("basic:basOffsiteMarket:export")
    @Log(title = "基采（异地货）市场", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(@RequestBody BasOffsiteMarketQueryBo queryBo, HttpServletResponse response) {
        List<BasOffsiteMarketVo> list = basOffsiteMarketService.queryList(queryBo);
        ExcelUtil.exportExcel(list, "基采市场定义", BasOffsiteMarketVo.class, response);
    }*/

    @Operation(summary = "单条查询")
    // @SaCheckPermission("basic:basOffsiteMarket:list")
    @GetMapping("/{id}")
    public R<BasOffsiteMarketVo> getInfo(@NotNull(message = "主键不能为空")
                                         @PathVariable Long id) {
        return R.ok(basOffsiteMarketService.queryById(id));
    }

    @Operation(summary = "公用模糊查询")
    // @SaCheckPermission("basic:basOffsiteMarket:list")
    @PostMapping("/queryFuzzy")
    public R<List<BasPubVo>> queryFuzzy(@RequestBody BasPubQueryBo queryBo) {
        return R.ok(basOffsiteMarketService.queryFuzzyCodeOrName(queryBo));
    }

    @Operation(summary = "新增单条")
    // @SaCheckPermission("basic:basOffsiteMarket:create")
    // @Log(title = "基采市场", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/create")
    public R<Void> create(@Validated(AddGroup.class) @RequestBody BasOffsiteMarketEditBo editBo) {
        return toAjax(basOffsiteMarketService.insertByBo(editBo));
    }

    @Operation(summary = "修改单条")
    // @SaCheckPermission("basic:basOffsiteMarket:update")
    // @Log(title = "基采市场", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/update")
    public R<Void> update(@Validated(EditGroup.class) @RequestBody BasOffsiteMarketEditBo editBo) {
        return toAjax(basOffsiteMarketService.updateByBo(editBo));
    }

    @Operation(summary = "修改状态")
    // @SaCheckPermission("basic:basOffsiteMarket:update")
    // @Log(title = "基采市场", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/updateStatus")
    public R<Void> updateStatus(@Validated @RequestBody BasOffsiteMarketStatusBo statusBo) {
        return toAjax(basOffsiteMarketService.updateStatusByBo(statusBo));
    }
    /**
    @Operation(summary = "删除单条")
    @SaCheckPermission("basic:basOffsiteMarket:delete")
    @Log(title = "基采市场", businessType = BusinessType.DELETE)
    @PostMapping("/delete/{id}")
    public R<Void> delete(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return toAjax(basOffsiteMarketService.deleteById(id, true));
    }*/
}
