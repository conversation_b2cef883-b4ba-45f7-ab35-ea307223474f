package cn.xianlink.basic.controller.platform;

import cn.xianlink.basic.domain.vo.bank.BasBankVo;
import cn.xianlink.basic.service.IBankService;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.web.core.BaseController;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 银行卡相关
 *
 * <AUTHOR> xiaodaibing on 2024-05-25 15:21
 * @folder 采集平台(小程序)/客户绑卡
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/platform/bank")
public class PlatformBankController extends BaseController {

    private final IBankService iBankService;

    /**
     * 银行列表-模糊搜索
     * <AUTHOR> on 2024/5/27:19:06
     * @param bankName
     * @return cn.xianlink.common.core.domain.R<java.util.List<cn.xianlink.basic.domain.vo.bank.BasBankVo>>
     */
    @GetMapping("/fuzzySearch")
    public R<List<BasBankVo>> list(@RequestParam(name = "bankName", required = false) String bankName) {
        return R.ok(iBankService.getBankList(bankName));
    }

}
