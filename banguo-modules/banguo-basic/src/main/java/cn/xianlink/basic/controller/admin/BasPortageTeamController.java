package cn.xianlink.basic.controller.admin;

import cn.xianlink.basic.domain.bo.freightlog.BasPortageTeamAddBo;
import cn.xianlink.basic.domain.bo.freightlog.BasPortageTeamEditBo;
import cn.xianlink.basic.domain.bo.freightlog.BasPortageTeamQueryBo;
import cn.xianlink.basic.domain.bo.freightlog.BasPortageTeamStatusBo;
import cn.xianlink.basic.domain.vo.freightlog.BasPortageTeamPageVo;
import cn.xianlink.basic.service.IBasPortageTeamService;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.core.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 装卸队管理
 * <AUTHOR>
 * @date 2024-03-21
 * @folder 般果管理中心/物流/装卸队管理
 */
@Tag(name = "装卸队管理")
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/basPortageTeam")
public class BasPortageTeamController extends BaseController {

    private final transient IBasPortageTeamService basPortageTeamService;

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    public R<TableDataInfo<BasPortageTeamPageVo>> page(@RequestBody BasPortageTeamQueryBo queryBo) {
        return R.ok(basPortageTeamService.customPageList(queryBo));
    }

    /**
     @Operation(summary = "导出")
     @SaCheckPermission("basic:basPortageTeam:export")
     @Log(title = "装卸队", businessType = BusinessType.EXPORT)
     @PostMapping("/export")
     public void export(@RequestBody BasPortageTeamQueryBo queryBo, HttpServletResponse response) {
     List<BasPortageTeamWorkersVo> list = basPortageTeamService.customList(queryBo);
     ExcelUtil.exportExcel(list, "装卸队", BasPortageTeamWorkersVo.class, response);
     }*/

    @Operation(summary = "新增单条")
    @RepeatSubmit()
    @PostMapping("/create")
    public R<Void> create(@Validated @RequestBody BasPortageTeamAddBo addBo) {
        return toAjax(basPortageTeamService.insertByBo(addBo));
    }

    @Operation(summary = "修改单条")
    @RepeatSubmit()
    @PostMapping("/update")
    public R<Void> update(@Validated @RequestBody BasPortageTeamEditBo editBo) {
        return toAjax(basPortageTeamService.updateByBo(editBo));
    }

    @Operation(summary = "修改状态")
    @RepeatSubmit()
    @PostMapping("/updateStatus")
    public R<Void> updateStatus(@Validated @RequestBody BasPortageTeamStatusBo statusBo) {
        return toAjax(basPortageTeamService.updateStatusByBo(statusBo));
    }

    @Operation(summary = "删除单条")
    @PostMapping("/delete/{id}")
    public R<Void> delete(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return toAjax(basPortageTeamService.deleteById(id, true));
    }
}
