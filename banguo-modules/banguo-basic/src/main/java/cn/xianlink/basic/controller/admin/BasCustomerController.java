package cn.xianlink.basic.controller.admin;


import cn.xianlink.basic.domain.bo.customer.*;
import cn.xianlink.basic.domain.vo.customer.BasCustomerRecordVo;
import cn.xianlink.basic.domain.vo.customer.BasCustomerSimperVo;
import cn.xianlink.basic.domain.vo.customer.BasCustomerVo;
import cn.xianlink.basic.listener.CustomerPhoneExcelListener;
import cn.xianlink.basic.service.IBasCustomerRecordService;
import cn.xianlink.basic.service.IBasCustomerService;
import cn.xianlink.basic.util.CustomerPageUtil;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.enums.StatusEnum;
import cn.xianlink.common.core.enums.YNStatusEnum;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.log.annotation.Log;
import cn.xianlink.common.log.enums.BusinessType;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.bo.BasRemarkBo;
import cn.xianlink.common.web.core.BaseController;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.WriteSheet;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.rmi.ServerException;
import java.util.ArrayList;
import java.util.List;

/**
 * 客户管理
 *
 * <AUTHOR>
 * @folder 般果管理中心/客户
 * @date 2024-04-09
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/customer")
public class BasCustomerController extends BaseController {

    private final transient IBasCustomerService basCustomerService;
    private final transient IBasCustomerRecordService basCustomerRecordService;

    /**
     * 查询-列表
     */
    // @SaCheckPermission("basic:admin:customer:page")
    @PostMapping("/page")
    public R<TableDataInfo<BasCustomerVo>> page(@RequestBody BasCustomerQueryBo bo) {
        CustomerPageUtil.convertOrderItem(bo);
        return R.ok(basCustomerService.pageList(bo, false));
    }

    /**
     * 获取客户详情
     */
    @GetMapping("/get/{id}")
    public R<BasCustomerVo> getInfoById(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(basCustomerService.selectByIdOrCode(id, null));
    }

    /**
     * 编辑
     */
    @RepeatSubmit()
    @PostMapping("/update")
    public R<Void> update(@Validated @RequestBody BasCustomerUpdateBo bo) {
        bo.setSalesmanCode(null);
        bo.setCityWhId(null);
        basCustomerService.updateCustomer(bo);
        return R.ok();
    }

    /**
     * 修改-备注
     */
    // @SaCheckPermission("basic:admin:customer:updateRemark")
    @Log(title = "修改备注", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/updateRemark")
    public R<Void> updateRemark(@Validated @RequestBody BasRemarkBo bo) {
        return toAjax(basCustomerService.updateRemark(bo.getId(), bo.getRemark()));
    }

    /**
     * 状态-启用
     */
    // @SaCheckPermission("basic:admin:customer:enable")
    @Log(title = "状态启用", businessType = BusinessType.UPDATE)
    @PostMapping("/enable/{id}")
    public R<Void> enable(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return toAjax(basCustomerService.updateStatus(id, StatusEnum.ENABLE.getCode()));
    }

    /**
     * 状态-禁用
     */
    // @SaCheckPermission("basic:admin:customer:disabled")
    @Log(title = "状态禁用", businessType = BusinessType.UPDATE)
    @PostMapping("/disabled/{id}")
    public R<Void> disabled(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return toAjax(basCustomerService.updateStatus(id, StatusEnum.DISABLE.getCode()));
    }

    /**
     * 售后-开启
     */
    // @SaCheckPermission("basic:admin:customer:open")
    @Log(title = "售后开启", businessType = BusinessType.UPDATE)
    @PostMapping("/open/{id}")
    public R<Void> open(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return toAjax(basCustomerService.updateAfterSaleStatus(id, YNStatusEnum.ENABLE.getCode()));
    }

    /**
     * 售后-关闭
     */
    // @SaCheckPermission("basic:admin:customer:close")
    @Log(title = "售后关闭", businessType = BusinessType.UPDATE)
    @PostMapping("/close/{id}")
    public R<Void> close(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return toAjax(basCustomerService.updateAfterSaleStatus(id, YNStatusEnum.DISABLE.getCode()));
    }

    /**
     * 操作记录
     *
     * @return 总仓列表
     */
    // @SaCheckPermission("basic:admin:customer:record")
    @GetMapping(value = "/record/{customerId}")
    public R<List<BasCustomerRecordVo>> record(@NotNull(message = "客户id不能为空") @PathVariable Long customerId) {
        return R.ok(basCustomerRecordService.selectListByCustomerId(customerId));
    }

    /**
     * 模糊分页查询客户基础信息
     * <AUTHOR> on 2024/7/17:10:38
     * @param bo
     * @return cn.xianlink.common.core.domain.R<cn.xianlink.common.mybatis.core.page.TableDataInfo<cn.xianlink.basic.domain.vo.customer.BasCustomerSimperVo>>
     */
    @PostMapping("/fuzzyPage")
    public R<TableDataInfo<BasCustomerSimperVo>> page(@RequestBody CustomerQueryMiniBo bo) {
        return R.ok(basCustomerService.miniPageList(bo));
    }


    @PostMapping("/exportExcel")
    public R<List<Long>> exportExcel(@RequestParam("excelFile") MultipartFile file) throws ServerException {
        if(file == null) {
            throw new ServerException("网络繁忙，请稍后重试");
        }
        try {
            List<Long> customerIdList = new ArrayList<>();
            CustomerPhoneExcelListener listener = new CustomerPhoneExcelListener(basCustomerService,customerIdList);
            EasyExcel.read(file.getInputStream(), CustomerPhoneExportBO.class, listener).sheet().doRead();
            return R.ok(customerIdList.stream().distinct().toList());
        } catch (IOException e) {
            throw new ServerException(e.getMessage());
        }
    }

    @GetMapping("/downloadTemplate")
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        // 设置文件名
        String fileName = URLEncoder.encode("优惠券批量发券模板", "UTF-8");
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

        // 创建一个空的数据列表
        List<PhoneTemplateBo> data = new ArrayList<>();

        // 写入Excel
        WriteSheet writeSheet = EasyExcel.writerSheet("手机号").build();
        EasyExcel.write(response.getOutputStream(), String.class).sheet("手机号").doWrite(data);
    }
}
