package cn.xianlink.basic.controller.region;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.xianlink.basic.domain.bo.rule.*;
import cn.xianlink.basic.domain.vo.regionlog.BasRegionLogisticsVo;
import cn.xianlink.basic.domain.vo.rule.BasCityFreightDetailVo;
import cn.xianlink.basic.domain.vo.rule.BasCityFreightRulePageVo;
import cn.xianlink.basic.service.IBasCityFreightRuleService;
import cn.xianlink.basic.service.IBasRegionLogisticsService;
import cn.xianlink.basic.service.IRegionWhService;
import cn.xianlink.common.api.enums.basic.PriceModeEnum;
import cn.xianlink.common.api.util.SaleDateUtil;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.core.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 免运活动
 * <AUTHOR>
 * @date 2024-03-21
 * @folder 总仓助手(小程序)/免运活动
 */
@Tag(name = "免运活动")
@Validated
@RequiredArgsConstructor
@RestController("RegionBasCityFreightRuleController")
@RequestMapping("/region/basCityFreightRule")
public class BasCityFreightRuleController extends BaseController {

    private final transient IBasCityFreightRuleService basCityFreightRuleService;
    private final transient IBasRegionLogisticsService basRegionLogisticsService;
    private final transient IRegionWhService regionWhService;

    @Operation(summary = "城市仓对应总仓的运费类型")
    @GetMapping("/priceMode/{id}")
    public R<Integer> priceMode(@NotNull(message = "城市仓id不能为空") @PathVariable Long id) {
        List<Integer> priceModes = basRegionLogisticsService.selectListByStatus1()
                .stream().filter(vo -> id.equals(vo.getCityWhId())).map(BasRegionLogisticsVo::getPriceMode).distinct().toList();
        return R.ok(priceModes.size() == 1 ? priceModes.get(0) : PriceModeEnum.WEIGHT.getCode());
    }

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    public R<TableDataInfo<BasCityFreightRulePageVo>> page(@Validated @RequestBody BasCityFreightRuleQueryBo queryBo) {
        List<Long> regionWhIds = CollectionUtil.newArrayList(queryBo.getRegionWhId());
        queryBo.setCurrentDates(regionWhService.selectListByStatus1(0L)
                .stream().filter(vo -> regionWhIds.contains(vo.getId()))
                .map(vo -> SaleDateUtil.getSaleDate(vo.getSalesTimeEnd())).distinct().toList());
        return R.ok(basCityFreightRuleService.customPageList(queryBo));
    }

    @Operation(summary = "新增单条")
    @RepeatSubmit()
    @PostMapping("/create")
    public R<Void> create(@Validated @RequestBody BasRegionFreightRuleAddBo addBo) {
        BasCityFreightRuleAddBo basCityFreightRuleAddBo = BeanUtil.copyProperties(addBo, BasCityFreightRuleAddBo.class);
        return toAjax(basCityFreightRuleService.insertByBo(basCityFreightRuleAddBo, null));
    }

    @Operation(summary = "修改单条")
    @RepeatSubmit()
    @PostMapping("/update")
    public R<Void> update(@Validated @RequestBody BasCityFreightRuleEditBo editBo) {
        BasCityFreightRuleEditBo basCityFreightRuleEditBo = BeanUtil.copyProperties(editBo, BasCityFreightRuleEditBo.class);
        return toAjax(basCityFreightRuleService.updateByBo(basCityFreightRuleEditBo, null));
    }

    @Operation(summary = "删除单条")
    @PostMapping("/delete")
    public R<Void> delete(@Validated @RequestBody BasCityFreightRuleDelBo delBo) {
        return toAjax(basCityFreightRuleService.deleteById(delBo));
    }

    /**
     * 生效规则查询
     * @ignore
     */
    @PostMapping("/querySkuFreightPriceMap")
    public R<Map<Long, BasCityFreightDetailVo>> querySkuFreightPriceMap(@Validated @RequestBody BasRuleFreightServiceRmQueryBo bo) {
        return R.ok(basCityFreightRuleService.querySkuFreightPriceMap(bo));
    }
}
