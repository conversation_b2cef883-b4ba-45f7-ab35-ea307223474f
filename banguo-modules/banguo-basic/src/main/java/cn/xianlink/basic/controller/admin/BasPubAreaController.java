package cn.xianlink.basic.controller.admin;

import cn.xianlink.basic.domain.bo.BasPubAreaQueryBo;
import cn.xianlink.basic.domain.vo.BasPubAreaVo;
import cn.xianlink.basic.domain.vo.BasPubTreeVo;
import cn.xianlink.basic.service.IBasPubAreaService;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.web.core.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 地区数据
 * <AUTHOR>
 * @date 2024-03-21
 * @folder 般果管理中心/物流/地区数据
 */
@Tag(name = "地区数据")
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/basPubArea")
public class BasPubAreaController extends BaseController {

    private final transient IBasPubAreaService basPubAreaService;


    @Operation(summary = "地区列表查询")
    // @SaCheckPermission("basic:basPubArea:list")
    @PostMapping("/list")
    public R<List<BasPubAreaVo>> list(@RequestBody BasPubAreaQueryBo queryBo) {
        return R.ok(basPubAreaService.customList(queryBo));
    }

    @Operation(summary = "地区树查询")
    // @SaCheckPermission("basic:basPubArea:list")
    @PostMapping("/tree")
    public R<List<BasPubTreeVo>> tree(@RequestBody BasPubAreaQueryBo queryBo) {
        return R.ok(basPubAreaService.customTree(queryBo));
    }

}
