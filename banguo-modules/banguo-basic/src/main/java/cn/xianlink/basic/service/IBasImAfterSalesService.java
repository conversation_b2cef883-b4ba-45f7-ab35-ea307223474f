package cn.xianlink.basic.service;


import cn.xianlink.basic.api.domain.bo.RemoteBasImAfterSalesBo;
import cn.xianlink.basic.api.domain.vo.RemoteBasImUserVo;
import cn.xianlink.basic.domain.bo.BasImAfterSalesBo;
import cn.xianlink.basic.domain.vo.BasImAfterSalesVo;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * IM售后服务客服配置Service接口
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
public interface IBasImAfterSalesService {

    /**
     * 查询IM售后服务客服配置
     */
    BasImAfterSalesVo queryById(Long id);

    /**
     * 查询IM售后服务客服配置列表
     */
    TableDataInfo<BasImAfterSalesVo> queryPageList(BasImAfterSalesBo bo, PageQuery pageQuery);

    /**
     * 查询IM售后服务客服配置列表
     */
    List<BasImAfterSalesVo> queryList(BasImAfterSalesBo bo);

    /**
     * 新增IM售后服务客服配置
     */
    Boolean insertByBo(BasImAfterSalesBo bo);

    /**
     * 修改IM售后服务客服配置
     */
    Boolean updateByBo(BasImAfterSalesBo bo);

    /**
     * 校验并批量删除IM售后服务客服配置信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据业务id和业务类型查询
     * @param bizId
     * @param bizType
     * @return
     */
    BasImAfterSalesVo queryByBizIdAndBizType(Long bizId, String bizType);

    /**
     * 根据业务id和业务类型批量查询
     * @param bizIdTypeMap
     * @return
     */
    List<BasImAfterSalesVo> queryByBizIdAndBizTypeMap(Map<String,Long> bizIdTypeMap);

    /**
     * 新增售后客服信息
     * @param bo
     * @return
     */
    Boolean insertByBo(RemoteBasImAfterSalesBo bo);

    Boolean deleteByParm(List<String> bizTypeList, String userCode);

    List<RemoteBasImUserVo> queryImUserList(Map<String, Long> map);
}
