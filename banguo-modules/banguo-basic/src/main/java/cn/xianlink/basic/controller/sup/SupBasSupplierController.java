package cn.xianlink.basic.controller.sup;

import cn.xianlink.basic.domain.bo.supplier.BasSupplierBo;
import cn.xianlink.basic.domain.bo.supplier.BasSupplierEditAliasBo;
import cn.xianlink.basic.domain.bo.supplier.SupplierScooterCapacityBo;
import cn.xianlink.basic.domain.dept.DeptApplyResultEnum;
import cn.xianlink.basic.domain.dept.DeptApplyResultVo;
import cn.xianlink.basic.domain.vo.supplier.BasSupplierSimperVo;
import cn.xianlink.basic.domain.vo.supplier.BasSupplierVo;
import cn.xianlink.basic.domain.vo.supplier.SupplierUserVo;
import cn.xianlink.basic.service.IBasSupplierService;
import cn.xianlink.common.api.enums.basic.SupUserRegisterTypeEnum;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.validate.AddGroup;
import cn.xianlink.common.core.validate.EditGroup;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.log.annotation.Log;
import cn.xianlink.common.log.enums.BusinessType;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.common.web.bo.BaseIdBo;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.system.api.RemoteUserService;
import cn.xianlink.system.api.domain.bo.RemoteUserBo;
import cn.xianlink.system.api.model.LoginUser;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 供应商基础信息
 *
 * <AUTHOR>
 * @folder 供应商端(小程序)/供应商
 * @date 2024-04-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/sup/supplier")
public class SupBasSupplierController extends BaseController {

    private final IBasSupplierService basSupplierService;

    @DubboReference
    private RemoteUserService remoteUserService;

    /**
     * 获取当前用户选中的供应商详细信息
     * 根据当前登录人信息自动查询
     *
     * @param
     * @return cn.xianlink.common.core.domain.R<cn.xianlink.basic.domain.vo.supplier.BasSupplierVo>
     * <AUTHOR> on 2024/4/12:16:36
     */
    @GetMapping("/get")
    public R<BasSupplierVo> getInfo() {
        var supplier = basSupplierService.queryAndPassRegionWhById(LoginHelper.getLoginUser().getRelationId());
        if (supplier != null) {
            LoginUser user = LoginHelper.getLoginUser();
            SupplierUserVo userVo = new SupplierUserVo();

            if (supplier.getAdminCode().equals(user.getUserCode())) {
                userVo.setRegisterType(SupUserRegisterTypeEnum.SUPPLIER.getCode());
            } else if (user.getDeptId() != null && user.getDeptId() != 0){
                userVo.setRegisterType(SupUserRegisterTypeEnum.DEPT.getCode());
            } else {
                userVo.setRegisterType(SupUserRegisterTypeEnum.USER.getCode());
            }
            RemoteUserBo remoteUserBo = remoteUserService.getUserByUserCode(user.getUserCode());
            if(remoteUserBo == null) {
                return R.ok(new BasSupplierVo());
            }
            userVo.setAuditStatus(remoteUserBo.getAuditStatus());

            supplier.setUserVo(userVo);
        } else {
            supplier = new BasSupplierVo();
        }
        return R.ok(supplier);
    }

    /**
     * 根据id获取供应商详情
     * @param id
     * @return cn.xianlink.common.core.domain.R<cn.xianlink.basic.domain.vo.supplier.BasSupplierVo>
     * <AUTHOR> on 2024/4/15:11:12
     */
    @GetMapping("/get/{id}")
    public R<BasSupplierVo> getInfoById(@NotNull(message = "主键不能为空")
                                        @PathVariable Long id) {
        return R.ok(basSupplierService.queryById(id));
    }


    /**
     * 当前用户关联的所有供应商列表信息
     * 根据当前登录人信息自动查询
     *
     * @param
     * @return cn.xianlink.common.core.domain.R<java.util.List < cn.xianlink.basic.domain.vo.supplier.BasSupplierSimperVo>>
     * <AUTHOR> on 2024/4/12:16:36
     */
    @GetMapping("/getList")
    public R<List<BasSupplierSimperVo>> getList() {
        return R.ok(basSupplierService.queryByUser(LoginHelper.getLoginUser()));
    }


    /**
     * 当前用户，切换供应商
     * 能力待提供
     *
     * @param bo
     * @return cn.xianlink.common.core.domain.R<java.lang.Void>
     * <AUTHOR> on 2024/4/12:16:15
     */
    @Log(title = "供应商", businessType = BusinessType.OTHER)
    @RepeatSubmit()
    @PostMapping("/switchSupplier")
    public R<Void> switchSupplier(@Validated @RequestBody BaseIdBo bo) {
        return null;
    }

    /**
     * 注册供应商
     *
     * @param bo
     * @return cn.xianlink.common.core.domain.R<java.lang.Void>
     * <AUTHOR> on 2024/4/11:15:30
     */
    @Log(title = "供应商", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/create")
    public R<DeptApplyResultVo> add(@Validated(AddGroup.class) @RequestBody BasSupplierBo bo) {
        DeptApplyResultEnum resultEnum = basSupplierService.insertByBo(bo);
        return R.ok(DeptApplyResultVo.builder().resultCode(resultEnum.getCode()).build());
    }

    /**
     * 修改供应商基础信息
     *
     * @param bo
     * @return cn.xianlink.common.core.domain.R<java.lang.Void>
     * <AUTHOR> on 2024/4/11:15:31
     */
    @Log(title = "供应商", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/update")
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BasSupplierBo bo) {
        return toAjax(basSupplierService.updateByBo(bo));
    }


    /**
     * 重新申请审核
     * 被驳回后，调整完信息，再次提交申请
     */
    @Log(title = "供应商", businessType = BusinessType.GRANT)
    @RepeatSubmit()
    @PostMapping("/reapply")
    public R<Void> reapply(@Validated @RequestBody BaseIdBo bo) {
        return toAjax(basSupplierService.reapply(bo.getId()));
    }


    /**
     * 供应商修改板车容量
     * <AUTHOR> on 2024/5/25:14:50
     * @param bo
     * @return cn.xianlink.common.core.domain.R<java.lang.Void>
     */
    @PostMapping("/scooterCapacity")
    public R<Void> scooterCapacity(@Validated @RequestBody SupplierScooterCapacityBo bo) {
        basSupplierService.scooterCapacity(bo.getScooterCapacity(), LoginHelper.getLoginUser().getRelationId());
        return R.ok();
    }

    /**
     * 修改供应商别名
     */
    @Log(title = "修改供应商别名", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/updateAlias")
    public R<Void> updateAlias(@Validated @RequestBody BasSupplierEditAliasBo bo) {
        return toAjax(basSupplierService.updateAliasById(bo));
    }

}
