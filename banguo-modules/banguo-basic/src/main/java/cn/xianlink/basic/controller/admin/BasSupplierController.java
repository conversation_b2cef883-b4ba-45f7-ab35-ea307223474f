package cn.xianlink.basic.controller.admin;

import cn.xianlink.basic.domain.bo.supplier.BasSupplierAuditBo;
import cn.xianlink.basic.domain.bo.supplier.BasSupplierEditAliasBo;
import cn.xianlink.basic.domain.bo.supplier.BasSupplierSearchBo;
import cn.xianlink.basic.domain.vo.supplier.BasSupplierAdminListVo;
import cn.xianlink.basic.domain.vo.supplier.BasSupplierVo;
import cn.xianlink.basic.service.IBasSupplierService;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.enums.StatusEnum;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.log.annotation.Log;
import cn.xianlink.common.log.enums.BusinessType;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.bo.BasRemarkBo;
import cn.xianlink.common.web.core.BaseController;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 供应商管理
 *
 * <AUTHOR>
 * @folder 般果管理中心/供应商
 * @date 2024-04-09
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/supplier")
public class BasSupplierController extends BaseController {

    private final IBasSupplierService basSupplierService;

    /**
     * 供应商列表查询
     */
    @PostMapping("/list")
    public R<TableDataInfo<BasSupplierAdminListVo>> list(@RequestBody BasSupplierSearchBo bo, PageQuery pageQuery) {
        return R.ok(basSupplierService.queryPageList(bo, pageQuery));
    }

    /**
     * 获取供应商详细信息
     *
     * @param id 主键
     */
    @GetMapping("/get/{id}")
    public R<BasSupplierVo> getInfo(@NotNull(message = "主键不能为空")
                                    @PathVariable Long id) {
        return R.ok(basSupplierService.queryById(id));
    }
//    @SaCheckPermission("basic:admin:supplier:enable")

    /**
     * 启用
     */
    @Log(title = "启用", businessType = BusinessType.UPDATE)
    @PostMapping("/enable/{id}")
    public R<Void> enable(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return toAjax(basSupplierService.enableOrDisabled(id, StatusEnum.ENABLE));
    }
//    @SaCheckPermission("basic:admin:supplier:disabled")

    /**
     * 禁用
     */
    @Log(title = "禁用", businessType = BusinessType.UPDATE)
    @PostMapping("/disabled/{id}")
    public R<Void> disabled(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return toAjax(basSupplierService.enableOrDisabled(id, StatusEnum.DISABLE));
    }

    /**
     * 注销
     * <AUTHOR> on 2024/8/13:16:37
     * @param id
     * @return cn.xianlink.common.core.domain.R<java.lang.Void>
     */
    @Log(title = "注销供应商", businessType = BusinessType.DELETE)
    @PostMapping("/accountCancel/{id}")
    public R<Void> accountCancel(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        basSupplierService.accountCancel(id);
        return R.ok();
    }

//    @SaCheckPermission("basic:admin:supplier:remark")

    /**
     * 修改备注
     */
    @PostMapping("/remark")
    public R<Void> remark(@Validated @RequestBody BasRemarkBo bo) {
        return toAjax(basSupplierService.remark(bo));
    }

//    @SaCheckPermission("basic:admin:supplier:audit")


    /**
     * 审核供应商
     * 审核通过，系统同步为供应商注册平安子账户，创建失败审核状态修改为-2不通过
     */
    @PostMapping("/audit")
    public R<Void> audit(@Validated @RequestBody BasSupplierAuditBo bo) {
        return toAjax(basSupplierService.audit(bo));
    }


    /**
     * 修改供应商别名
     */
    @Log(title = "修改供应商别名", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/updateAlias")
    public R<Void> updateAlias(@Validated @RequestBody BasSupplierEditAliasBo bo) {
        return toAjax(basSupplierService.updateAliasById(bo));
    }

}
