package cn.xianlink.basic.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.basic.domain.*;
import cn.xianlink.basic.domain.bo.regionwh.*;
import cn.xianlink.basic.domain.vo.citywh.CityWhVo;
import cn.xianlink.basic.domain.vo.offsitelog.BasOffsiteLogisticsRegionSumVo;
import cn.xianlink.basic.domain.vo.regionwh.RegionWhTimeCategoryVo;
import cn.xianlink.basic.domain.vo.regionwh.RegionWhTimeSetUpVo;
import cn.xianlink.basic.domain.vo.regionwh.RegionWhTimeVo;
import cn.xianlink.basic.domain.vo.regionwh.RegionWhVo;
import cn.xianlink.basic.mapper.*;
import cn.xianlink.basic.service.IBasPubAreaService;
import cn.xianlink.basic.service.IBasSupplierRegionWhService;
import cn.xianlink.basic.service.ICityWhService;
import cn.xianlink.basic.service.IRegionWhService;
import cn.xianlink.common.api.util.SaleDateUtil;
import cn.xianlink.common.core.enums.StatusEnum;
import cn.xianlink.common.core.enums.YNStatusEnum;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.core.utils.StreamUtils;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.product.api.RemoteCategoryService;
import cn.xianlink.product.api.RemoteCwDepotService;
import cn.xianlink.product.api.domain.bo.RemoteCwDepotBO;
import cn.xianlink.product.api.domain.vo.RemoteCategoryVO;
import cn.xianlink.system.api.RemoteDeptService;
import cn.xianlink.system.api.RemoteUserService;
import cn.xianlink.system.api.domain.bo.RemoteUserBo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 总仓 业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-21
 */
@RequiredArgsConstructor
@Service
public class RegionWhServiceImpl implements IRegionWhService {

    private final transient RegionWhMapper regionWhMapper;
    private final transient RegionWhTimeMapper regionWhTimeMapper;
    private final transient RegionWhTimeCategoryMapper regionWhTimeCategoryMapper;
    private final transient BasOffsiteLogisticsMapper basOffsiteLogisticsMapper;
    private final transient BasRegionLogisticsMapper basRegionLogisticsMapper;
    private final transient IBasPubAreaService basPubAreaService;
    private final transient IBasSupplierRegionWhService basSupplierRegionWhService;
    @DubboReference
    private final transient RemoteUserService remoteUserService;
    @DubboReference
    private final transient RemoteDeptService remoteDeptService;
    private final transient WhNexusMapper whNexusMapper;
    @DubboReference
    private final transient RemoteCwDepotService remoteCwDepotService;
    @DubboReference
    private final transient RemoteCategoryService remoteCategoryService;
    private ICityWhService cityWhService;

    @Autowired
    public void setCityWhService(@Lazy ICityWhService cityWhService) {
        this.cityWhService = cityWhService;
    }

    /**
     * 查询总仓列表；状态：可用
     *
     * @param deptId 查所有传0，否则查指定部门的总仓
     * @return 总仓列表
     */
    @Override
    public List<RegionWhVo> selectListByName(String name, Long deptId) {
        LambdaQueryWrapper<RegionWh> lqw = Wrappers.lambdaQuery();
        lqw.eq(RegionWh::getStatus, StatusEnum.ENABLE.getCode());
        if (deptId != null && deptId != 0L) {
            lqw.eq(RegionWh::getDeptId, deptId);
        }
        lqw.and(StringUtils.isNotBlank(name), q -> q.like(RegionWh::getRegionWhCode, name).or().like(RegionWh::getRegionWhName, name));
        lqw.orderByAsc(RegionWh::getId);
        return regionWhMapper.selectVoList(lqw);
    }

    @Override
    public TableDataInfo<RegionWhVo> selectListByNamePage(String name, Long deptId, PageQuery pageQuery) {
        LambdaQueryWrapper<RegionWh> lqw = Wrappers.lambdaQuery();
        lqw.eq(RegionWh::getStatus, StatusEnum.ENABLE.getCode());
        if (deptId != null && deptId != 0L) {
            lqw.eq(RegionWh::getDeptId, deptId);
        }
        lqw.and(StringUtils.isNotBlank(name), q -> q.like(RegionWh::getRegionWhCode, name).or().like(RegionWh::getRegionWhName, name));
        lqw.orderByAsc(RegionWh::getId);
        IPage<RegionWhVo> result = regionWhMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result.getRecords(), result.getTotal());
    }

    /**
     * 查询总仓列表；状态：可用
     *
     * @param deptId 查所有传0，否则查指定部门的总仓
     * @return 总仓列表
     */
//    @Cacheable(cacheNames = BasCacheNames.REGION_WH_FULL, key = "#deptId")
    @Override
    public List<RegionWhVo> selectListByStatus1(Long deptId) {
        LambdaQueryWrapper<RegionWh> lqw = Wrappers.lambdaQuery();
        lqw.eq(RegionWh::getStatus, StatusEnum.ENABLE.getCode());
        if (deptId != null && deptId != 0L) {
            lqw.eq(RegionWh::getDeptId, deptId);
        }
        lqw.orderByAsc(RegionWh::getId);
        return regionWhMapper.selectVoList(lqw);
    }


    /**
     * 查询总仓列表（分页）；状态：可用
     *
     * @param deptId 查所有传0，否则查指定部门的总仓
     * @return 总仓列表
     */
    @Override
    public TableDataInfo<RegionWhVo> selectListByStatusByPage(Long deptId, PageQuery pageQuery) {
        LambdaQueryWrapper<RegionWh> lqw = Wrappers.lambdaQuery();
        lqw.eq(RegionWh::getStatus, StatusEnum.ENABLE.getCode());
        if (deptId != null && deptId != 0L) {
            lqw.eq(RegionWh::getDeptId, deptId);
        }
        lqw.orderByAsc(RegionWh::getId);
        IPage<RegionWhVo> result = regionWhMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result.getRecords(), result.getTotal());
    }

    /**
     * 查询单条总仓信息
     *
     * @param id
     * @return 总仓信息
     */
    @Override
    public RegionWhVo selectById(Long id) {
        RegionWhVo vo = regionWhMapper.selectVoById(id);
        if (vo == null) {
            throw new ServiceException("总仓id不存在");
        }
        return vo;
    }

    /**
     * 根据总仓id获取这个总仓当前时间的销售日
     *
     * @param id
     * @return
     */
    @Override
    public LocalDate getSaleDate(Long id) {
        String salesTimeEnd = selectById(id).getSalesTimeEnd();
        if (StringUtils.isBlank(salesTimeEnd)) {
            throw new ServiceException("请联系采购设置总仓销售截止日期");
        }
        return SaleDateUtil.getSaleDate(salesTimeEnd);
    }

    /**
     * 查询总仓列表
     *
     * @return 总仓列表
     */
    @Override
    public List<RegionWhVo> selectVoList() {
        return regionWhMapper.selectVoList();
    }

    /**
     * 查询单条总仓信息
     *
     * @param regionWhCode
     * @return 总仓信息
     */
    @Override
    public RegionWhVo selectAndCheckNullByCode(String regionWhCode) {
        LambdaQueryWrapper<RegionWh> wp = Wrappers.lambdaQuery();
        wp.eq(RegionWh::getRegionWhCode, regionWhCode);
        RegionWhVo vo = regionWhMapper.selectVoOne(wp);
        if (vo == null) {
            throw new ServiceException("总仓仓编码不存在");
        }
        return vo;
    }

    /**
     * 查询单条总仓信息
     *
     * @param cityWhId
     * @return 总仓信息
     */
    @Override
    public RegionWhVo selectByAffiliatedCityWhId(Long cityWhId) {
        List<WhNexus> nexusVos = this.getAffiliatedByCityWhId(ListUtil.toList(cityWhId), null);
        if (CollUtil.isEmpty(nexusVos)) {
            return null;
        }
        return regionWhMapper.selectVoById(nexusVos.get(0).getRegionWhId());
    }

    /**
     * 根据总仓编码list批量查询
     *
     * @param regionWhCodes
     * @return 总仓信息
     */
    @Override
    public List<RegionWhVo> selectVoBatchCodes(List<String> regionWhCodes) {
        if (ObjectUtil.isEmpty(regionWhCodes)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<RegionWh> lqw = Wrappers.lambdaQuery();
        if (regionWhCodes.size() == 1) {
            lqw.eq(RegionWh::getRegionWhCode, regionWhCodes.get(0));
        } else {
            lqw.in(RegionWh::getRegionWhCode, regionWhCodes);
        }
        return regionWhMapper.selectVoList(lqw);
    }

    /**
     * 批量查询 by id
     *
     * @param id
     * @return
     */
    @Override
    public List<RegionWhVo> selectVoBatchIds(List<Long> id) {
        if (CollectionUtil.isEmpty(id)) {
            return new ArrayList<>();
        }
        return regionWhMapper.selectVoBatchIds(id);
    }

    /**
     * 查询总仓列表
     *
     * @return 总仓列表
     */
    @Override
    public TableDataInfo<RegionWhVo> pageList(RegionWhQueryBo bo) {
        LambdaQueryWrapper<RegionWh> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getRegionWhCode()), RegionWh::getRegionWhCode, bo.getRegionWhCode());
        lqw.like(StringUtils.isNotBlank(bo.getRegionWhName()), RegionWh::getRegionWhName, bo.getRegionWhName());
        lqw.eq(ObjectUtil.isNotNull(bo.getStatus()), RegionWh::getStatus, bo.getStatus());
        lqw.ge(ObjectUtil.isNotNull(bo.getStatusTimeStart()), RegionWh::getStatusTime, bo.getStatusTimeStart());
        lqw.lt(ObjectUtil.isNotNull(bo.getStatusTimeEnd()), RegionWh::getStatusTime, bo.getStatusTimeEnd());
        lqw.eq(StringUtils.isNotBlank(bo.getAdminCode()), RegionWh::getAdminCode, bo.getAdminCode());
        lqw.eq(StringUtils.isNotBlank(bo.getUpdateCode()), RegionWh::getUpdateCode, bo.getUpdateCode());
        lqw.ge(ObjectUtil.isNotNull(bo.getUpdateTimeStart()), RegionWh::getUpdateTime, bo.getUpdateTimeStart());
        lqw.lt(ObjectUtil.isNotNull(bo.getUpdateTimeEnd()), RegionWh::getUpdateTime, bo.getUpdateTimeEnd());
        lqw.eq(StringUtils.isNotBlank(bo.getAreaCode()), RegionWh::getAreaCode, bo.getAreaCode());

        Page<RegionWhVo> result = regionWhMapper.selectVoPage(bo.build(), lqw);
        if (result.getRecords().size() > 0) {
            List<Long> regionWhIds = result.getRecords().stream().map(RegionWhVo::getId).collect(Collectors.toList());
            List<WhNexus> nexusList = this.getAffiliatedByRegionWhIds(regionWhIds);
            Map<Long, List<WhNexus>> nexusListMap = nexusList.stream().collect(Collectors.groupingBy(WhNexus::getRegionWhId));

            List<Long> cityWhIds = nexusList.stream().map(WhNexus::getCityWhId).collect(Collectors.toList());
            List<String> adminCodes = result.getRecords().stream().filter(e -> StringUtils.isNotEmpty(e.getAdminCode())).map(RegionWhVo::getAdminCode).collect(Collectors.toList());
            List<Long> deptIds = result.getRecords().stream().filter(e -> ObjectUtils.isNotNull(e.getDeptId())).map(RegionWhVo::getDeptId).collect(Collectors.toList());

            Map<Long, Long> marketCountMap = basOffsiteLogisticsMapper.queryRegionWhMarketCount(null).stream().collect(Collectors.toMap(BasOffsiteLogisticsRegionSumVo::getRegionWhId, BasOffsiteLogisticsRegionSumVo::getMarketTotal, (key1, key2) -> key2));
            Map<String, RemoteUserBo> userMap = remoteUserService.getUsersByUserCodes(adminCodes);
            Map<Long, String> cityWhMap = cityWhService.selectVoBatchIds(cityWhIds).stream().collect(Collectors.toMap(CityWhVo::getId, CityWhVo::getCityWhName, (key1, key2) -> key2));
            Map<Long, String> deptNameMap = remoteDeptService.getDeptNameMap(deptIds);
            result.getRecords().forEach(e -> {
                e.setMarketNumber(marketCountMap.getOrDefault(e.getId(), 0L).intValue());
                e.setAdminName(userMap.getOrDefault(e.getAdminCode(), new RemoteUserBo()).getRealName());
                e.setDeptName(deptNameMap.get(e.getDeptId()));
                if (nexusListMap.containsKey(e.getId())) {
                    e.setAffiliatedCityWhList(nexusListMap.get(e.getId()).stream().map(e1 -> {
                        AffiliatedCityWhBean cityWh = BeanUtil.copyProperties(e1, AffiliatedCityWhBean.class);
                        cityWh.setCityWhName(cityWhMap.get(cityWh.getCityWhId()));
                        return cityWh;
                    }).collect(Collectors.toList()));
                }
            });

        }
        return TableDataInfo.build(result);
    }

    /**
     * 查询单条总仓信息
     */
    @Override
    public RegionWhTimeSetUpVo timeByRegionWhId(Long id) {
        RegionWhTimeSetUpVo vo = BeanUtil.copyProperties(this.selectById(id), RegionWhTimeSetUpVo.class);
        List<RegionWhTimeVo> clearWhTimeList = regionWhTimeMapper.selectVoList(Wrappers.lambdaQuery(RegionWhTime.class).eq(RegionWhTime::getRegionWhId, vo.getId()));
        if (CollUtil.isNotEmpty(clearWhTimeList)) {
            List<RegionWhTimeCategoryVo> categoryList = regionWhTimeCategoryMapper.selectVoList(Wrappers.lambdaQuery(RegionWhTimeCategory.class).eq(RegionWhTimeCategory::getRegionWhId, vo.getId()));
            List<Long> categoryIds = categoryList.stream().map(RegionWhTimeCategoryVo::getCategoryId).collect(Collectors.toList());
            Map<Long, RemoteCategoryVO> categoryNameMap = remoteCategoryService.getCategoryByIds(categoryIds).stream().collect(Collectors.toMap(RemoteCategoryVO::getId, Function.identity()));
            categoryList.forEach(e -> {
                e.setCategoryName(categoryNameMap.getOrDefault(e.getCategoryId(), new RemoteCategoryVO()).getName());
                e.setPathName(categoryNameMap.getOrDefault(e.getCategoryId(), new RemoteCategoryVO()).getPathName());
            });
            Map<Long, List<RegionWhTimeCategoryVo>> categoryListMap = categoryList.stream().collect(Collectors.groupingBy(RegionWhTimeCategoryVo::getTimeId));
            clearWhTimeList.forEach(e -> e.setCategoryList(categoryListMap.get(e.getId())));
            vo.setCategoryClearWhTimeList(clearWhTimeList);
        }
        return vo;
    }

    /**
     * 校验总仓编码是否唯一
     *
     * @param regionWhCode 总仓编码
     * @return 结果
     */
    @Override
    public boolean checkRegionWhCodeUnique(String regionWhCode) {
        boolean exist = regionWhMapper.exists(new LambdaQueryWrapper<RegionWh>()
                .eq(RegionWh::getRegionWhCode, regionWhCode));
        return !exist;
    }

    /**
     * 校验总仓名称是否唯一
     *
     * @param regionWhName 总仓名称
     * @param excludeId    总仓id(排除)，可为空
     * @return 结果
     */
    @Override
    public boolean checkRegionWhNameUnique(String regionWhName, Long excludeId) {
        boolean exist = regionWhMapper.exists(new LambdaQueryWrapper<RegionWh>()
                .eq(RegionWh::getRegionWhName, regionWhName)
                .ne(ObjectUtil.isNotNull(excludeId), RegionWh::getId, excludeId));
        return !exist;
    }

    /**
     * 新增总仓
     *
     * @param bo 总仓信息
     * @return 结果
     */
//    @Caching( evict = {
//        @CacheEvict(cacheNames = BasCacheNames.REGION_WH_FULL, key = "#bo.deptId"),
//        @CacheEvict(cacheNames = BasCacheNames.REGION_WH_FULL, key = "0"),
//        @CacheEvict(cacheNames = BasCacheNames.REGION_WH_FULL_SALE_DATE, key = "#bo.deptId")
//    })
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertRegionWh(RegionWhAddBo bo) {
        RegionWh entity = MapstructUtils.convert(bo, RegionWh.class);
        entity.setAreaFullName(basPubAreaService.querAndCheckLeafByCode(bo.getAreaCode()).getFullName());
        // 直属仓 创建关联关系
        List<WhNexus> nexusList = new ArrayList<>();
        if (CollUtil.isNotEmpty(bo.getAffiliatedCityWhList())) {
            Map<Long, AffiliatedCityWhBean> cityWhMap = bo.getAffiliatedCityWhList().stream().collect(Collectors.toMap(AffiliatedCityWhBean::getCityWhId, Function.identity(), (key1, key2) -> key1));
            List<WhNexus> nexusVos = this.getAffiliatedByCityWhId(cityWhMap.keySet().stream().toList(), null);
            if (CollUtil.isNotEmpty(nexusVos)) {
                throw new ServiceException(String.format("【%s】城市仓已存在直属关系", cityWhMap.get(nexusVos.get(0).getCityWhId()).getCityWhName()));
            }
            bo.getAffiliatedCityWhList().forEach(e -> {
                WhNexus nexus = BeanUtil.copyProperties(e, WhNexus.class);
                nexus.setIsAffiliated(YNStatusEnum.ENABLE.getCode());
                nexus.setRegionWhCode(entity.getRegionWhCode());
                nexusList.add(nexus);
            });
        }
        int rows = regionWhMapper.insert(entity);
        if (rows > 0 && CollUtil.isNotEmpty(nexusList)) {
            nexusList.forEach(e -> e.setRegionWhId(entity.getId()));
            whNexusMapper.insertBatch(nexusList);
        }
        //插入库存
        RemoteCwDepotBO depotBO = new RemoteCwDepotBO();
        depotBO.setCode("YC" + entity.getRegionWhCode());
        depotBO.setName(entity.getRegionWhName());
        depotBO.setSearchWord(entity.getRegionWhCode());
        depotBO.setDepotAddress(entity.getAddress());
        depotBO.setDepotPhone("0");
        depotBO.setRegionWhId(entity.getId());
        depotBO.setRegionWhCode(entity.getRegionWhCode());
        remoteCwDepotService.createDepot(depotBO);
        return rows;
    }

    /**
     * 新增总仓
     *
     * @param bo 总仓信息
     * @return 结果
     */
//    @Caching( evict = {
//            @CacheEvict(cacheNames = BasCacheNames.REGION_WH_FULL, key = "#bo.deptId"),
//            @CacheEvict(cacheNames = BasCacheNames.REGION_WH_FULL, key = "0"),
//            @CacheEvict(cacheNames = BasCacheNames.REGION_WH_FULL_SALE_DATE, key = "#bo.deptId")
//    })
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateRegionWh(RegionWhEditBo bo) {
        RegionWh entity = MapstructUtils.convert(bo, RegionWh.class);
        entity.setAreaFullName(basPubAreaService.querAndCheckLeafByCode(bo.getAreaCode()).getFullName());

        RegionWh regionWh = regionWhMapper.selectById(bo.getId());
        if (regionWh == null) {
            throw new ServiceException("总仓id不存在");
        }

        // 直属仓
        if (CollUtil.isNotEmpty(bo.getAffiliatedCityWhList())) {
            Map<Long, AffiliatedCityWhBean> cityWhMap = bo.getAffiliatedCityWhList().stream().collect(Collectors.toMap(AffiliatedCityWhBean::getCityWhId, Function.identity(), (key1, key2) -> key1));
            List<WhNexus> nexusVos = this.getAffiliatedByCityWhId(cityWhMap.keySet().stream().toList(), regionWh.getId());
            if (CollUtil.isNotEmpty(nexusVos)) {
                throw new ServiceException(String.format("【%s】城市仓已存在直属关系", cityWhMap.get(nexusVos.get(0).getCityWhId()).getCityWhName()));
            }
            nexusVos = whNexusMapper.selectList(new LambdaQueryWrapper<WhNexus>().eq(WhNexus::getRegionWhId, regionWh.getId()));
            for (WhNexus whNexus : nexusVos) {
                AffiliatedCityWhBean cityWh = cityWhMap.get(whNexus.getCityWhId());
                if (ObjectUtil.isNotNull(cityWh)) {
                    if (whNexus.getIsAffiliated().equals(YNStatusEnum.DISABLE.getCode())) {
                        WhNexus nexus = new WhNexus();
                        nexus.setId(whNexus.getId());
                        nexus.setIsAffiliated(YNStatusEnum.ENABLE.getCode());
                        whNexusMapper.updateById(nexus);
                    }
                } else {
                    WhNexus nexus = new WhNexus();
                    nexus.setId(whNexus.getId());
                    nexus.setIsAffiliated(YNStatusEnum.DISABLE.getCode());
                    whNexusMapper.updateById(nexus);
                }
                cityWhMap.remove(whNexus.getCityWhId());
            }
            if (cityWhMap.size() > 0) {
                whNexusMapper.insertBatch(cityWhMap.values().stream().map(e -> {
                    WhNexus nexus = BeanUtil.copyProperties(e, WhNexus.class);
                    nexus.setIsAffiliated(YNStatusEnum.ENABLE.getCode());
                    nexus.setRegionWhId(regionWh.getId());
                    nexus.setRegionWhCode(regionWh.getRegionWhCode());
                    return nexus;
                }).collect(Collectors.toList()));
            }
        } else {
            List<WhNexus> nexusVos = this.getAffiliatedByRegionWhIds(ListUtil.toList(regionWh.getId()));
            if (CollUtil.isNotEmpty(nexusVos)) {
                nexusVos.forEach(e -> e.setIsAffiliated(YNStatusEnum.DISABLE.getCode()));
                whNexusMapper.updateBatchById(nexusVos);
            }
        }
        // 坑位开关变化，刷新关联该总仓的供应商坑位
        if(!Objects.equals(entity.getIsSaleNum(), regionWh.getIsSaleNum())){
            int maxSaleNum = Objects.equals(1, entity.getIsSaleNum()) ? 5 : 0;
            basSupplierRegionWhService.updateMaxSaleNumByRegionId(regionWh.getId(), maxSaleNum);
        }
        return regionWhMapper.updateById(entity);
    }

    private void updateLogisticsSalesTimeEnd(Long regionWhId, Integer isEarlyEnd, String updateSalesTimeEnd, Integer updateIsEarlyEnd) {
        BasRegionLogistics logEntity = new BasRegionLogistics();
        logEntity.setSalesTimeEnd(updateSalesTimeEnd);
        if (updateIsEarlyEnd != null) {
            logEntity.setIsEarlyEnd(updateIsEarlyEnd);
        }
        basRegionLogisticsMapper.update(logEntity, Wrappers.lambdaUpdate(BasRegionLogistics.class)
                .eq(BasRegionLogistics::getRegionWhId, regionWhId)
                .eq(BasRegionLogistics::getIsEarlyEnd, isEarlyEnd));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateRegionWhTime(RegionWhEditTimeBo bo) {
        if (StringUtils.isBlank(bo.getEarlySalesTimeEnd())) {
            bo.setEarlySalesTimeEnd("");
        }
        if (StringUtils.isBlank(bo.getDeliveryCompleteTime())) {
            bo.setDeliveryCompleteTime("");
        }
        if (StringUtils.isBlank(bo.getPromoDay())) {
            bo.setPromoDay("");
        }
        List<RegionWhTimeEditBo> addAndUpdateList = bo.getCategoryClearWhTimeList().stream().filter(timeBo -> timeBo.getEditType() != 3).collect(Collectors.toList());
        if (addAndUpdateList.size() != addAndUpdateList.stream().map(RegionWhTimeEditBo::getCategoryClearWhTime).distinct().toList().size()) {
            throw new ServiceException("分类清理库存时间不能有重复");
        }
        RegionWh regionWh = regionWhMapper.selectById(bo.getId());
        if (regionWh == null) {
            throw new ServiceException("总仓id不存在");
        }
        RegionWh entity = MapstructUtils.convert(bo, RegionWh.class);
        // 清理库存时间 有变更
        if (!bo.getClearWhTime().equals(regionWh.getClearWhTime())) {
            // 更新物流线设置 销售结束时间=清理库存时间 的数据
            this.updateLogisticsSalesTimeEnd(bo.getId(), YNStatusEnum.DISABLE.getCode(), bo.getClearWhTime(), null);
        }
        // 提前 销售结束时间 有变更
        if (StringUtils.isNotBlank(regionWh.getEarlySalesTimeEnd())) {
            if (StringUtils.isNotBlank(bo.getEarlySalesTimeEnd())) {
                if (!bo.getEarlySalesTimeEnd().equals(regionWh.getEarlySalesTimeEnd())) {
                    // 更新物流线设置 销售结束时间=提前销售时间 的数据
                    this.updateLogisticsSalesTimeEnd(bo.getId(), YNStatusEnum.ENABLE.getCode(), bo.getEarlySalesTimeEnd(), null);
                }
            } else {
                this.updateLogisticsSalesTimeEnd(bo.getId(), YNStatusEnum.ENABLE.getCode(), bo.getClearWhTime(), YNStatusEnum.DISABLE.getCode());
            }
        }
        int count = 1;
        // 有变更，执行update
        if (!bo.getClearWhTime().equals(regionWh.getClearWhTime())
                || !bo.getSalesTimeStart().equals(regionWh.getSalesTimeStart())
                || !bo.getSalesTimeEnd().equals(regionWh.getSalesTimeEnd())
                || !bo.getDeliveryTimeEnd().equals(regionWh.getDeliveryTimeEnd())
                || !bo.getEarlySalesTimeEnd().equals(regionWh.getEarlySalesTimeEnd())
                || !bo.getDeliveryCompleteTime().equals(regionWh.getDeliveryCompleteTime())
                || !bo.getPromoDay().equals(regionWh.getPromoDay())) {
            count = regionWhMapper.updateById(entity);
        }
        Long regionWhId = bo.getId();
        // 分类清库存时间
        if (CollUtil.isNotEmpty(bo.getCategoryClearWhTimeList())) {
            Map<Long, String> timeMap = regionWhTimeMapper.selectList(Wrappers.lambdaQuery(RegionWhTime.class).eq(RegionWhTime::getRegionWhId, regionWhId))
                    .stream().collect(Collectors.toMap(RegionWhTime::getId, RegionWhTime::getCategoryClearWhTime));
//            Map<String, Long> categoryMap = regionWhTimeCategoryMapper.selectVoList(Wrappers.lambdaQuery(RegionWhTimeCategory.class).eq(RegionWhTimeCategory::getRegionWhId, regionWhId))
//                            .stream().collect(Collectors.toMap(RegionWhTimeCategoryVo::getTimeIdAndCategoryIdKey, RegionWhTimeCategoryVo::getId, (key1, key2) -> key2));

            Map<Long, List<RegionWhTimeCategoryVo>> categoryListMap = regionWhTimeCategoryMapper.selectVoList(Wrappers.lambdaQuery(RegionWhTimeCategory.class).eq(RegionWhTimeCategory::getRegionWhId, regionWhId))
                    .stream().collect(Collectors.groupingBy(RegionWhTimeCategoryVo::getTimeId));
            // 新增
            List<RegionWhTimeEditBo> addList = bo.getCategoryClearWhTimeList().stream().filter(timeBo -> timeBo.getEditType() == 1).collect(Collectors.toList());
            for (RegionWhTimeEditBo timeBo : addList) {
                RegionWhTime add = new RegionWhTime();
                add.setRegionWhId(regionWhId);
                add.setCategoryClearWhTime(timeBo.getCategoryClearWhTime());
                regionWhTimeMapper.insert(add);
                List<RegionWhTimeCategory> categoryList = MapstructUtils.convert(timeBo.getCategoryList(), RegionWhTimeCategory.class);
                categoryList.forEach(category -> {
                    category.setRegionWhId(regionWhId);
                    category.setTimeId(add.getId());
                });
                regionWhTimeCategoryMapper.insertBatch(categoryList);
            }
            // 修改
            List<RegionWhTimeEditBo> updateList = bo.getCategoryClearWhTimeList().stream().filter(timeBo -> timeBo.getEditType() == 2).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(updateList)) {
                for (RegionWhTimeEditBo timeBo : updateList) {
                    if (!timeBo.getCategoryClearWhTime().equals(timeMap.get(timeBo.getId()))) {
                        regionWhTimeMapper.updateById(MapstructUtils.convert(timeBo, RegionWhTime.class));
                    }
                    Map<Long, Long> categoryMap = categoryListMap.getOrDefault(timeBo.getId(), new ArrayList<>())
                            .stream().collect(Collectors.toMap(RegionWhTimeCategoryVo::getCategoryId, RegionWhTimeCategoryVo::getId, (key1, key2) -> key2));
                    for (RegionWhTimeCategoryEditBo categoryBo : timeBo.getCategoryList()) {
                        if (!categoryMap.containsKey(categoryBo.getCategoryId())) {
                            RegionWhTimeCategory category  = MapstructUtils.convert(categoryBo, RegionWhTimeCategory.class);
                            category.setRegionWhId(regionWhId);
                            category.setTimeId(timeBo.getId());
                            regionWhTimeCategoryMapper.insert(category);
                        } else {
                            categoryMap.remove(categoryBo.getCategoryId());
                        }
                    }
                    if (CollUtil.isNotEmpty(categoryMap)) {
                        regionWhTimeCategoryMapper.deleteBatchIds(categoryMap.values());
                    }
                }
            }
            // 删除
            List<Long> deleteIds = bo.getCategoryClearWhTimeList().stream().filter(timeBo -> timeBo.getEditType() == 3).map(RegionWhTimeEditBo::getId).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(deleteIds)) {
                regionWhTimeMapper.deleteBatchIds(deleteIds);
                regionWhTimeCategoryMapper.delete(Wrappers.lambdaQuery(RegionWhTimeCategory.class).in(RegionWhTimeCategory::getTimeId, deleteIds));
            }
        }
        return count;
    }

    /**
     * 修改状态
     *
     * @param id     总仓id
     * @param status 状态，0为不可用；1为可用
     * @return 结果
     */
//    @Caching( evict = {
//            @CacheEvict(cacheNames = BasCacheNames.REGION_WH_FULL, key = "#deptId"),
//            @CacheEvict(cacheNames = BasCacheNames.REGION_WH_FULL, key = "0"),
//            @CacheEvict(cacheNames = BasCacheNames.REGION_WH_FULL_SALE_DATE, key = "#deptId")
//    })
    @Override
    public int updateRegionWhStatus(Long id, Integer status) {
        return regionWhMapper.update(new UpdateWrapper<RegionWh>()
                .eq("id", id)
                .set("status", status)
                .set("status_time", DateTime.now()));
    }

    @Override
    public List<RegionWhVo> selectRegionWhInfoByIds(List<Long> regionWhIds) {
        if (ObjectUtil.isEmpty(regionWhIds)) {
            return new ArrayList<>();
        }
        return regionWhMapper.selectVoBatchIds(regionWhIds);
    }

    /**
     * 根据部门过滤出总仓
     *
     * @param deptIds
     * @return
     */
    public List<Long> selectRegionWhByDepts(List<Long> deptIds) {
        if (CollUtil.isEmpty(deptIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<RegionWh> lqw = Wrappers.lambdaQuery();
        lqw.select(RegionWh::getId);
        lqw.eq(RegionWh::getStatus, StatusEnum.ENABLE.getCode());
        lqw.in(RegionWh::getDeptId, deptIds);
        lqw.eq(RegionWh::getType, 1); // 总仓
        List<RegionWhVo> regionWhVos = regionWhMapper.selectVoList(lqw);
        return StreamUtils.toList(regionWhVos, RegionWhVo::getId);
    }

    @Override
    public List<RegionWhVo> authorityListByName(String name, Long deptId, List<Long> authorityRegionWhIds) {
        LambdaQueryWrapper<RegionWh> lqw = Wrappers.lambdaQuery();
        lqw.eq(RegionWh::getStatus, StatusEnum.ENABLE.getCode());
        lqw.eq(ObjectUtil.isNotNull(deptId) && !ObjectUtil.equals(deptId, 0L),RegionWh::getDeptId, deptId);
        lqw.in(CollectionUtil.isNotEmpty(authorityRegionWhIds) , RegionWh::getId, authorityRegionWhIds);
        lqw.and(StringUtils.isNotBlank(name), q -> q.like(RegionWh::getRegionWhCode, name).or().like(RegionWh::getRegionWhName, name));
        lqw.orderByAsc(RegionWh::getId);
        return regionWhMapper.selectVoList(lqw);
    }

//    @Cacheable(cacheNames = "region:saledate#1m" )
    @Override
    public LocalDate getMinSaleDate() {
        Map<Long, LocalDate> regionSaleDate = getRegionSaleDate();
        return regionSaleDate.values().stream().filter(Objects::nonNull).min(LocalDate::compareTo).orElse(LocalDate.now());
    }

    @Override
    public Map<Long, LocalDate> getRegionSaleDate() {
        LambdaQueryWrapper<RegionWh> lqw = Wrappers.lambdaQuery();
        lqw.select(RegionWh::getId, RegionWh::getSalesTimeEnd);
        lqw.eq(RegionWh::getStatus, StatusEnum.ENABLE.getCode());
        List<RegionWhVo> regionWhVos = regionWhMapper.selectVoList(lqw);

        Map<Long, LocalDate> saleDateMap = new HashMap<>();
        Map<String, LocalDate> saleDateCache = new HashMap<>();  // 缓存销售日
        for (RegionWhVo vo : regionWhVos) {
            LocalDate localDate = saleDateCache.computeIfAbsent(vo.getSalesTimeEnd(), SaleDateUtil::getSaleDate);
            saleDateMap.put(vo.getId(), localDate);
        }
        return saleDateMap;
    }

    /**
     * 根据城市仓id，查询直属关系
     * @param cityWhIds
     * @return
     */
    private List<WhNexus> getAffiliatedByCityWhId(List<Long> cityWhIds, Long excludeRegionWhIdId) {
        List<WhNexus> nexusVos =  whNexusMapper.selectList(new LambdaQueryWrapper<WhNexus>()
                .in(WhNexus::getCityWhId, cityWhIds)
                .eq(WhNexus::getIsAffiliated, YNStatusEnum.ENABLE.getCode())
                .ne(ObjectUtil.isNotNull(excludeRegionWhIdId), WhNexus::getRegionWhId, excludeRegionWhIdId));
        return nexusVos;
    }
    /**
     * 根据总仓id，查询直属关系
     * @param regionWhIds
     * @return
     */
    @Override
    public List<WhNexus> getAffiliatedByRegionWhIds(List<Long> regionWhIds) {
        List<WhNexus> nexusVos =  whNexusMapper.selectList(new LambdaQueryWrapper<WhNexus>()
                .in(WhNexus::getRegionWhId, regionWhIds)
                .eq(WhNexus::getIsAffiliated, YNStatusEnum.ENABLE.getCode()));
        return nexusVos;
    }
}