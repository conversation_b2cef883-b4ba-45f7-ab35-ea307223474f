package cn.xianlink.basic.controller.admin;

import cn.xianlink.basic.domain.bo.offsitelog.BasOffsiteLogisticsPlanEditBo;
import cn.xianlink.basic.domain.bo.offsitelog.BasOffsiteLogisticsPlanQueryBo;
import cn.xianlink.basic.domain.vo.BasDynamicsDataInfo;
import cn.xianlink.basic.domain.vo.offsitelog.BasOffsiteLogisticsPlanPageVo;
import cn.xianlink.basic.service.IBasOffsiteLogisticsPlanService;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.web.core.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 基采吨位
 * <AUTHOR>
 * @date 2024-03-21
 * @folder 般果管理中心/物流/基采吨位
 */
@Tag(name = "基采吨位")
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/basOffsiteLogisticsPlan")
public class BasOffsiteLogisticsPlanController extends BaseController {

    private final transient IBasOffsiteLogisticsPlanService basOffsiteLogisticsPlanService;

    @Operation(summary = "分页查询")
    // @SaCheckPermission("basic:basOffsiteLogisticsPlan:list")
    @PostMapping("/page")
    public R<BasDynamicsDataInfo<BasOffsiteLogisticsPlanPageVo>> page(@RequestBody BasOffsiteLogisticsPlanQueryBo queryBo) {
        return R.ok(basOffsiteLogisticsPlanService.customPageList(queryBo));
    }

    /**
    @Operation(summary = "导出")
    @SaCheckPermission("basic:basOffsiteLogisticsPlan:export")
    @Log(title = "基采吨位", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(@RequestBody BasOffsiteLogisticsPlanQBo queryBo, HttpServletResponse response) {
        BasDynamicsDataInfo<BasOffsiteLogisticsPlanVo> info = basOffsiteLogisticsPlanService.customList(queryBo);
        Properties headPlaceholders = new Properties();
        headPlaceholders.putAll(info.getHeads());
        DynamicsExcelUtil.exportExcel(info.getRows(), "基采吨位", BasOffsiteLogisticsPlanVo.class, response, headPlaceholders);
    }*/

    @Operation(summary = "修改单条")
    // @SaCheckPermission("basic:basOffsiteLogisticsPlan:update")
    // @Log(title = "基采吨位", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/update")
    public R<Void> update(@Validated @RequestBody BasOffsiteLogisticsPlanEditBo editBo) {
        return toAjax(basOffsiteLogisticsPlanService.updateByBo(editBo));
    }
}
