package cn.xianlink.basic.controller.platform;

import cn.xianlink.basic.domain.bo.customer.BasCustomerAddBo;
import cn.xianlink.basic.domain.bo.customer.BasCustomerUpdateBo;
import cn.xianlink.basic.domain.vo.citywh.CityWhPlaceVoApplet;
import cn.xianlink.basic.domain.vo.customer.BasCustomerVo;
import cn.xianlink.basic.service.*;
import cn.xianlink.basic.service.IBasCustomerService;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.system.api.model.LoginUser;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 客户
 *
 * <AUTHOR>
 * @folder 采集平台(小程序)/客户
 * @date 2024-04-09
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/platform/customer")
public class PlatformBasCustomerController extends BaseController {
    private final transient IBasCustomerService basCustomerService;
    private final transient PlaceVoAppletUtil placeVoAppletUtil;

    /**
     * 查询-详情
     *
     * @return 客户详情
     */
    // @SaCheckPermission("basic:admin:customer:info")
    @GetMapping("/info")
    public R<BasCustomerVo> info() {
        LoginUser loginUser = LoginHelper.getLoginUser();
        return R.ok(basCustomerService.selectByIdOrCode(loginUser.getRelationId(), null));
    }

    /**
     * 新增
     */
    // @SaCheckPermission("basic:admin:customer:create")
    @RepeatSubmit()
    @PostMapping("/add")
    public R<Void> add(@Validated @RequestBody BasCustomerAddBo bo) {
        return toAjax(basCustomerService.insertCustomer(bo));
    }


    /**
     * 编辑
     * <AUTHOR> on 2024/8/13:15:08
     * @param bo
     * @return cn.xianlink.common.core.domain.R<java.lang.Void>
     */
    @RepeatSubmit()
    @PostMapping("/update")
    public R<Void> add(@Validated @RequestBody BasCustomerUpdateBo bo) {
        basCustomerService.updateCustomer(bo);
        return R.ok();
    }

    /**
     * 查询-常用-提货点 【停用】
     *
     * @param lng 经度
     * @param lat 纬度
     */
    @GetMapping(value = "/queryUsedPlace")
    public R<List<CityWhPlaceVoApplet>> queryUsedPlace(@RequestParam(name = "lng", required = false) Double lng,
                                                       @RequestParam(name = "lat", required = false) Double lat) {
        return R.ok(placeVoAppletUtil.getUsedPlaceVoAppletList(LoginHelper.getLoginUser().getRelationId(), lng, lat));
    }

}
