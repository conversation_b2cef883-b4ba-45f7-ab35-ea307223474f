package cn.xianlink.basic.controller.admin;

import cn.xianlink.basic.domain.bo.citywh.CityWhPlaceAddBo;
import cn.xianlink.basic.domain.bo.citywh.CityWhPlaceEditBo;
import cn.xianlink.basic.domain.bo.citywh.CityWhPlaceEditSonBo;
import cn.xianlink.basic.domain.bo.citywh.CityWhPlaceQueryBo;
import cn.xianlink.basic.domain.vo.BasPubVo;
import cn.xianlink.basic.domain.vo.citywh.CityWhPlaceVo;
import cn.xianlink.basic.domain.vo.citywh.CityWhVo;
import cn.xianlink.basic.service.ICityWhPlaceService;
import cn.xianlink.basic.service.ICityWhService;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.log.annotation.Log;
import cn.xianlink.common.log.enums.BusinessType;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.core.BaseController;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 城市仓-提货点
 *
 * <AUTHOR>
 * @date 2024-03-21
 * @folder 般果管理中心/仓库/城市仓-提货点
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/cityWhPlace")
public class CityWhPlaceController extends BaseController {

    private final transient ICityWhPlaceService cityWhPlaceService;
    private final transient ICityWhService cityWhService;
    /**
     * 查询-下拉选项
     *
     * @param name 提货点名称或编码
     * @param cCode 城市仓编码
     * @return
     */
    // @SaCheckPermission("basic:admin:cityWhPlace:selectList")
    @GetMapping("/selectList")
    public R<List<BasPubVo>> selectList(@RequestParam(name = "name", required = false) String name, @RequestParam(name = "cCode", required = false) String cCode) {
        List<BasPubVo> list = cityWhPlaceService.selectListByName(name, cCode).getRecords().stream().map(e -> {
            BasPubVo vo = BeanUtils.instantiateClass(BasPubVo.class);
            vo.setId(e.getId());
            vo.setCode(e.getPlaceCode());
            vo.setName(e.getPlaceName());
            return vo;
        }).collect(Collectors.toList());

        return R.ok(list);
    }

    /**
     * 查询-列表
     *
     * @return 提货点列表
     */
    // @SaCheckPermission("basic:admin:cityWhPlace:page")
    @PostMapping("/page")
    public R<TableDataInfo<CityWhPlaceVo>> page(@RequestBody CityWhPlaceQueryBo bo) {
        bo.setPlaceLevel(1);
        return R.ok(cityWhPlaceService.pageList(bo));
    }

    /**
     * 查询-提货点-子仓列表
     */
    @PostMapping("/sonPlaceListByParentPlaceId/{id}")
    public R<List<CityWhPlaceVo>> sonPlaceListByParentPlaceId(@NotNull(message = "主键不能为空") @PathVariable Long id) {

        return R.ok(cityWhPlaceService.selectListByParentPlaceId(id, true));
    }

    /**
     * 查询-用户绑定的列表 【作废-迁移到CityCityWhPlaceController】
     * <AUTHOR> on 2024/11/21:19:59
     * @param bo
     * @return cn.xianlink.common.core.domain.R<cn.xianlink.common.mybatis.core.page.TableDataInfo<cn.xianlink.basic.domain.vo.citywh.CityWhPlaceVo>>
     */
//    @PostMapping("/user/page")
//    public R<TableDataInfo<CityWhPlaceVo>> userPage(@RequestBody CityWhPlaceQueryBo bo) {
//        TableDataInfo<CityWhPlaceVo> tableDataInfo = page(bo).getData();
//        LoginUser loginUser = LoginHelper.getLoginUser();
//        if (CollUtil.isNotEmpty(tableDataInfo.getRows()) && loginUser != null && loginUser.getUserType().equals(SysUserTypeEnum.CITY_USER.getType())) {
//            List<CityWhUserPlaceVo> userPlaces = cityWhPlaceService.getPlaceByUser(loginUser.getUserId());
//            if (CollUtil.isNotEmpty(userPlaces)) {
//                Set<Long> placeIdSet = userPlaces.stream().map(e -> e.getPlaceId()).collect(Collectors.toSet());
//                tableDataInfo.setRows(tableDataInfo.getRows().stream().filter(vo -> placeIdSet.contains(vo.getId())).toList());
//            }
//        }
//        return R.ok(tableDataInfo);
//    }

    /**
     * 新增
     */
    // @SaCheckPermission("basic:admin:cityWhPlace:create")
    @Log(title = "新增提货点", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/create")
    public R<Void> create(@Validated @RequestBody CityWhPlaceAddBo bo) {
        cityWhPlaceService.checkPlaceNameUnique(bo.getPlaceName(), null);
        CityWhVo cityWhVo = cityWhService.selectAndCheckNullByCode(bo.getCityWhCode());
        return toAjax(cityWhPlaceService.insertPlace(bo, cityWhVo));
    }
//    /**
//     * 批量新增提货点-二级
//     */
//    @Log(title = "批量新增提货点-二级", businessType = BusinessType.INSERT)
//    @RepeatSubmit()
//    @PostMapping("/batchCreate")
//    public R<Void> batchCreate(@Validated @RequestBody List<CityWhPlaceAddSonBo> bos) {
//        return toAjax(cityWhPlaceService.batchInsertPlace(bos));
//    }

    /**
     * 修改
     */
    // @SaCheckPermission("basic:admin:cityWhPlace:update")
    @Log(title = "修改提货点", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/update")
    public R<Void> update(@Validated @RequestBody CityWhPlaceEditBo bo) {
        cityWhPlaceService.checkPlaceNameUnique(bo.getPlaceName(), bo.getId());
        return toAjax(cityWhPlaceService.updatePlace(bo));
    }
    /**
     * 批量更新子提货点
     */
    @Log(title = "批量更新子提货点", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/editSonPlace")
    public R<Void> editSonPlace(@Validated @RequestBody List<CityWhPlaceEditSonBo> bos) {
        return toAjax(cityWhPlaceService.editSonPlace(bos));
    }

    /**
     * 删除
     */
    // @SaCheckPermission("basic:admin:cityWhPlace:delete")
    @Log(title = "删除", businessType = BusinessType.DELETE)
    @PostMapping("/delete/{id}")
    public R<Void> delete(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return toAjax(cityWhPlaceService.deleteById(id));
    }

    /**
     * 用户提货点列表-详细点的
     */
    @PostMapping(value = "/userPage")
    public R<TableDataInfo<CityWhPlaceVo>> userPage(@RequestBody CityWhPlaceQueryBo bo) {
        return R.ok(cityWhPlaceService.pageList(bo));
    }
}
