package cn.xianlink.basic.controller.sup;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.xianlink.basic.api.RemoteBasImAfterSalesService;
import cn.xianlink.basic.api.RemoteSupplierService;
import cn.xianlink.basic.api.domain.bo.RemoteBasImAfterSalesBo;
import cn.xianlink.basic.api.domain.vo.RemoteBasImAfterSalesVo;
import cn.xianlink.basic.api.domain.vo.RemoteSupplierVo;
import cn.xianlink.basic.api.enums.ImAfterSalesBizTypeEnum;
import cn.xianlink.basic.domain.dept.*;
import cn.xianlink.basic.domain.vo.BasImAfterSalesVo;
import cn.xianlink.basic.domain.vo.supplier.BasSupplierVo;
import cn.xianlink.basic.service.IBasImAfterSalesService;
import cn.xianlink.basic.service.IBasSupplierService;
import cn.xianlink.common.api.enums.system.OrgTypeEnum;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.enums.AuditEnum;
import cn.xianlink.common.core.enums.StatusEnum;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.redis.utils.RedisUtils;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.common.web.core.BaseController;
import cn.xianlink.product.api.RemoteSupplierSkuService;
import cn.xianlink.system.api.RemoteDeptService;
import cn.xianlink.system.api.RemoteSysOrgService;
import cn.xianlink.system.api.RemoteUserService;
import cn.xianlink.system.api.domain.bo.*;
import cn.xianlink.system.api.domain.vo.RemoteDeptDetailVo;
import cn.xianlink.system.api.model.LoginUser;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.google.common.collect.Lists;
import io.seata.spring.annotation.GlobalTransactional;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jodd.util.StringUtil;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;

import static cn.xianlink.system.api.domain.bo.RemoteImUserBo.buildImUser;

/**
 * 部门管理
 * 供应商-定制化的部门管理
 * <AUTHOR> xiaodaibing on 2024-09-29 17:23
 * @folder 供应商端(小程序)/部门
 */
@RequiredArgsConstructor
@CustomLog
@RestController
@RequestMapping("/sup/dept")
public class DeptController extends BaseController {

    private final IBasSupplierService basSupplierService;

    @DubboReference
    private RemoteDeptService remoteDeptService;

    @DubboReference
    private RemoteUserService remoteUserService;

    @DubboReference
    private RemoteSupplierSkuService remoteSupplierSkuService;

    @DubboReference
    private RemoteSysOrgService remoteSysOrgService;

    private final IBasImAfterSalesService imAfterSalesService;

    /**
     * 用户注册档口
     * 根据出参判断注册是否成功
     * <AUTHOR> on 2024/9/29:17:41
     * @param bo
     * @return cn.xianlink.common.core.domain.R<cn.xianlink.basic.domain.dept.DeptApplyResultVo>
     */
    @GlobalTransactional
    @RepeatSubmit()
    @PostMapping("/userRegister")
    public R<DeptApplyResultVo> register(@Validated @RequestBody DeptRegisterBo bo) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        RemoteUserBo remoteUserVo = remoteUserService.getUserByUserCode(loginUser.getUserCode());
        RemoteSysOrgBo orgBo = remoteSysOrgService.getSysOrgByRelation(bo.getSupplierId(), OrgTypeEnum.SUPPLIER);

        // 判断用户是否已绑定机构
        if (remoteUserVo.getOrgId().equals(orgBo.getId())) {
            return R.ok(DeptApplyResultVo.builder().resultCode(DeptApplyResultEnum.REPEAT.getCode()).build());
        } else if (!remoteUserVo.getOrgId().equals(0L)){
            return R.ok(DeptApplyResultVo.builder().resultCode(DeptApplyResultEnum.FAIL.getCode()).build());
        }
        DeptSearchBo searchBo = new DeptSearchBo();
        searchBo.setSupplierId(bo.getSupplierId());
        searchBo.setDeptName(bo.getDeptName());
        var list = this.list(searchBo).getData();
        if (CollUtil.isNotEmpty(list)) {
            return R.fail("档口名称重复，请调整");
        }

        //注册部门的同时绑定用户部门
        RemoteDeptAddBo input = new RemoteDeptAddBo();
        input.setRelationId(bo.getSupplierId());
        input.setDeptName(bo.getDeptName());
        input.setDeptCode(this.deptCode(bo.getSupplierId()));
        input.setStatus(StatusEnum.ENABLE.getCode());
        input.setAuditStatus(AuditEnum.WAIT.getCode());
        input.setSort(0);
        input.setLeaderCode(loginUser.getUserCode());

        Map<String, Long> deptIdMap = remoteDeptService.batchCreate(Lists.newArrayList(input), OrgTypeEnum.SUPPLIER);
        Long deptId = deptIdMap.get(input.getDeptName());

        RemoteUserUpdateBo remoteUserBo = new RemoteUserUpdateBo();
        remoteUserBo.setUserId(loginUser.getUserId());
        remoteUserBo.setUsername(bo.getPeopleName());
        remoteUserBo.setAuditStatus(AuditEnum.WAIT.getCode());
        remoteUserBo.setDeptId(deptId);
        remoteUserBo.setRemark("申请创建并加入档口【" + bo.getDeptName() + "】");
        remoteUserService.updateUser(remoteUserBo);
        //绑定用户和供应商的关系
        RemoteSysOrgBo remoteSysOrgBo = new RemoteSysOrgBo().setRelationId(bo.getSupplierId()).setRelationType(OrgTypeEnum.SUPPLIER).setBindingAdminRole(false);
        remoteUserService.bindingSysOrg(loginUser.getUserId(), remoteSysOrgBo);

        return R.ok(DeptApplyResultVo.builder().resultCode(DeptApplyResultEnum.SUCCESS.getCode()).build());
    }

    /**
     * 用户申请加入部门
     * <AUTHOR> on 2024/10/8:16:35
     * @param bo
     * @return cn.xianlink.common.core.domain.R<cn.xianlink.basic.domain.dept.DeptApplyResultVo>
     */
    @GlobalTransactional
    @RepeatSubmit()
    @PostMapping("/applyJoinDept")
    public R<DeptApplyResultVo> applyJoinDept(@Validated @RequestBody DeptJoinBo bo) {
        if (Objects.isNull(bo.getDeptId())) {
            bo.setDeptId(0L);
        }
        LoginUser loginUser = LoginHelper.getLoginUser();

        RemoteUserBo remoteUserVo = remoteUserService.getUserByUserCode(loginUser.getUserCode());
        RemoteSysOrgBo orgBo = remoteSysOrgService.getSysOrgByRelation(bo.getSupplierId(), OrgTypeEnum.SUPPLIER);

        // 判断用户是否已绑定机构
        if (remoteUserVo.getOrgId().equals(orgBo.getId())) {
            return R.ok(DeptApplyResultVo.builder().resultCode(DeptApplyResultEnum.REPEAT.getCode()).build());
        } else if (!remoteUserVo.getOrgId().equals(0L)){
            return R.ok(DeptApplyResultVo.builder().resultCode(DeptApplyResultEnum.FAIL.getCode()).build());
        }
        RemoteUserUpdateBo remoteUserBo = new RemoteUserUpdateBo();
        remoteUserBo.setUserId(loginUser.getUserId());
        remoteUserBo.setUsername(bo.getPeopleName());
        remoteUserBo.setAuditStatus(AuditEnum.WAIT.getCode());
        remoteUserBo.setDeptId(bo.getDeptId());
        if (bo.getDeptId() == 0) {
            remoteUserBo.setRemark("申请加入");
        } else {
            RemoteDeptDetailVo deptDetailVo = remoteDeptService.getByDeptId(bo.getDeptId());
            if (Objects.isNull(deptDetailVo)) {
                return R.fail("档口不存在");
            }
            remoteUserBo.setRemark("申请加入档口【" + deptDetailVo.getDeptName() + "】");
        }
        remoteUserService.updateUser(remoteUserBo);

        //绑定用户和供应商的关系
        RemoteSysOrgBo remoteSysOrgBo = new RemoteSysOrgBo().setRelationId(bo.getSupplierId()).setRelationType(OrgTypeEnum.SUPPLIER).setBindingAdminRole(false);
        remoteUserService.bindingSysOrg(loginUser.getUserId(), remoteSysOrgBo);
        return R.ok(DeptApplyResultVo.builder().resultCode(DeptApplyResultEnum.SUCCESS.getCode()).build());
    }


    /**
     * 取消申请加入供应商
     * <AUTHOR> on 2024/10/8:16:25
     * @return cn.xianlink.common.core.domain.R<java.lang.Void>
     */
    @GlobalTransactional
    @RepeatSubmit()
    @PostMapping("/cancelApply")
    public R<Void> cancelApply() {
        LoginUser loginUser = LoginHelper.getLoginUser();

        RemoteUserUpdateBo remoteUserBo = new RemoteUserUpdateBo();
        remoteUserBo.setUserId(loginUser.getUserId());
        remoteUserBo.setAuditStatus(AuditEnum.PASS.getCode());
        remoteUserBo.setDeptId(0L);
        remoteUserBo.setRemark("");
        remoteUserService.updateUser(remoteUserBo);

        //绑定用户和供应商的关系
        remoteUserService.unBindingSysOrg(loginUser.getUserId());

        //如果用户是关联的档口负责人，就同步删除档口
        if (loginUser.getDeptId() != null && loginUser.getDeptId() > 0) {
            RemoteDeptDetailVo deptDetailVo = remoteDeptService.getByDeptId(loginUser.getDeptId());
            if (loginUser.getUserCode().equals(deptDetailVo.getLeaderCode())) {
                remoteDeptService.deleteByIds(Lists.newArrayList(loginUser.getDeptId()));
            }
        }
        return R.ok();
    }


    /**
     * 编辑档口
     * <AUTHOR> on 2024/10/9:17:10
     * @param bo
     * @return cn.xianlink.common.core.domain.R<java.lang.Void>
     */
    @GlobalTransactional
    @RepeatSubmit()
    @PostMapping("/modify")
    public R<Void> modify(@Validated @RequestBody DeptModifyBo bo) {
        if (CollUtil.isNotEmpty(bo.getCreates())) {
            this.create(bo.getCreates());
        }
        if (CollUtil.isNotEmpty(bo.getUpdates())) {
            this.update(bo.getUpdates());
        }
        if (CollUtil.isNotEmpty(bo.getDeleteIds())) {
            this.remove(bo.getDeleteIds());
        }
        return R.ok();
    }

    private void create(List<DeptCreateBo> bos) {
        //目前只有供应商才添加部门，以后客户也要的话得改
        Long supplierId = LoginHelper.getLoginUser().getRelationId();
        List<RemoteDeptAddBo> addBos = new ArrayList<>();
        for (DeptCreateBo bo : bos) {
            if (bo.getSort() == null) {
                bo.setSort(0);
            }
            LoginUser loginUser = LoginHelper.getLoginUser();
            //注册部门的同时绑定用户部门
            RemoteDeptAddBo input = new RemoteDeptAddBo();
            input.setRelationId(loginUser.getRelationId());
            input.setDeptName(bo.getDeptName());
            input.setDeptCode(this.deptCode(supplierId));
            input.setStatus(StatusEnum.ENABLE.getCode());
            input.setAuditStatus(AuditEnum.PASS.getCode());
            input.setSort(bo.getSort());
            input.setLeaderCode(loginUser.getUserCode());

            addBos.add(input);
        }
        remoteDeptService.batchCreate(addBos, OrgTypeEnum.SUPPLIER);
    }


    private void update(List<DeptUpdateBo> bos) {
        List<RemoteDeptUpdateBo> updateBos = new ArrayList<>();
        for (DeptUpdateBo bo : bos) {
            RemoteDeptUpdateBo input = new RemoteDeptUpdateBo();
            input.setDeptId(bo.getDeptId());
            input.setDeptName(bo.getDeptName());
            input.setSort(bo.getSort());

            updateBos.add(input);
        }
        remoteDeptService.batchUpdate(updateBos);
    }

    private void remove(List<Long> ids) {
        for (Long id : ids) {
            if (remoteSupplierSkuService.hasSupplierDeptSaleSku(id)) {
                throw new ServiceException("档口上架过商品，不能删除");
            }
        }
        //先删除用户再删部门
        for (Long id : ids) {
            remoteUserService.cancelUserByDept(id);
        }
        remoteDeptService.deleteByIds(ids);
    }

    /**
     * 查询供应商的所有部门
     * <AUTHOR> on 2024/10/8:16:49
     * @param searchBo
     * @return cn.xianlink.common.core.domain.R<java.util.List<cn.xianlink.basic.domain.dept.DeptDetailVo>>
     */
    @PostMapping("/list")
    public R<List<DeptDetailVo>> list(@Validated @RequestBody DeptSearchBo searchBo) {
        List<DeptDetailVo> list = new ArrayList<>();
        RemoteDeptDetailVo vo = remoteDeptService.getDetailByRelationId(searchBo.getSupplierId(), OrgTypeEnum.SUPPLIER);
        if (CollUtil.isEmpty(vo.getChildren())) {
            return R.ok(list);
        }
        for (RemoteDeptDetailVo child : vo.getChildren()) {
            if (searchBo.getAuditStatus() != null && !child.getAuditStatus().equals(searchBo.getAuditStatus())) {
                continue;
            }
            if (StrUtil.isNotBlank(searchBo.getDeptName()) && !child.getDeptName().equals(searchBo.getDeptName())) {
                continue;
            }
            if (StrUtil.isNotBlank(searchBo.getDeptNameFuzzySearch()) && !child.getDeptName().contains(searchBo.getDeptNameFuzzySearch())) {
                continue;
            }
            list.add(BeanUtil.copyProperties(child, DeptDetailVo.class));
        }
        return R.ok(list);
    }


    /**
     * 批量查询供应商部门
     * <AUTHOR> on 2024/10/18:18:15
     * @param searchBo
     * @return cn.xianlink.common.core.domain.R<java.util.List<cn.xianlink.basic.domain.dept.DeptDetailVo>>
     */
    @PostMapping("/list/v2")
    public R<List<DeptDetailVo>> listV2(@Validated @RequestBody DeptSearchBo searchBo) {
        List<DeptDetailVo> list = new ArrayList<>();
        for (Long supplierId : searchBo.getSupplierIds()) {
            RemoteDeptDetailVo vo = remoteDeptService.getDetailByRelationId(supplierId, OrgTypeEnum.SUPPLIER);
            if (CollUtil.isEmpty(vo.getChildren())) {
                continue;
            }
            for (RemoteDeptDetailVo child : vo.getChildren()) {
                if (searchBo.getAuditStatus() != null && !child.getAuditStatus().equals(searchBo.getAuditStatus())) {
                    continue;
                }
                if (StrUtil.isNotBlank(searchBo.getDeptName()) && !child.getDeptName().equals(searchBo.getDeptName())) {
                    continue;
                }
                if (StrUtil.isNotBlank(searchBo.getDeptNameFuzzySearch()) && !child.getDeptName().contains(searchBo.getDeptNameFuzzySearch())) {
                    continue;
                }
                list.add(BeanUtil.copyProperties(child, DeptDetailVo.class));
            }
        }
        return R.ok(list);
    }

    /**
     * 根据用户角色查询部门列表
     * <AUTHOR> on 2024/10/24:10:11
     * @param searchBo
     * @return cn.xianlink.common.core.domain.R<java.util.List<cn.xianlink.basic.domain.dept.DeptDetailVo>>
     */
    @PostMapping("/listByRole")
    public R<List<DeptDetailVo>> listByRole(@Validated @RequestBody DeptSearchBo searchBo) {
        RemoteDeptDetailVo vo = remoteDeptService.getDetailByRelationId(searchBo.getSupplierId(), OrgTypeEnum.SUPPLIER);
        if (CollUtil.isEmpty(vo.getChildren())) {
            return R.ok();
        }
        Long deptId = LoginHelper.getLoginUser().getDeptId();
        List<DeptDetailVo> list = new ArrayList<>();
        for (RemoteDeptDetailVo child : vo.getChildren()) {
            if (deptId > 0 && !child.getDeptId().equals(deptId)) {
                continue;
            }
            if (searchBo.getAuditStatus() != null && !child.getAuditStatus().equals(searchBo.getAuditStatus())) {
                continue;
            }
            if (StrUtil.isNotBlank(searchBo.getDeptName()) && !child.getDeptName().equals(searchBo.getDeptName())) {
                continue;
            }
            if (StrUtil.isNotBlank(searchBo.getDeptNameFuzzySearch()) && !child.getDeptName().contains(searchBo.getDeptNameFuzzySearch())) {
                continue;
            }
            list.add(BeanUtil.copyProperties(child, DeptDetailVo.class));
        }
        return R.ok(list);
    }




    private String deptCode(Long supplierId) {
        BasSupplierVo supplierVo =  basSupplierService.queryById(supplierId);
        if (supplierVo == null) {
            throw new ServiceException("供应商不存在");
        }
        String key = supplierVo.getSimpleCode();
        long atomicValue = RedisUtils.incrAtomicValue(key);
        return key + "-" + String.format("%02d", atomicValue);
    }

    /**
     * 获取部门IM客服
     * <AUTHOR> @param bizId
     * @param type  ImAfterSalesBizTypeEnum.DEPT.getType()
     */
    @GetMapping("/getDeptImUser")
    public R<BasImAfterSalesVo> getDeptImUser(@NotNull(message = "bizId不能为空") @RequestParam Long bizId,
                                              @NotBlank(message = "type不能为空") @RequestParam String type,
                                              @NotNull(message = "supplierId不能为空") @RequestParam Long supplierId) {
        //档口客服
        BasImAfterSalesVo info = imAfterSalesService.queryByBizIdAndBizType(bizId, type);
        if (info != null) {
            return R.ok(info);
        }
        //供应商客服
        if (ImAfterSalesBizTypeEnum.DEPT.getType().equals(type)) {
            info = imAfterSalesService.queryByBizIdAndBizType(supplierId, ImAfterSalesBizTypeEnum.SUPPLIER.getType());
            if (info != null) {
                return R.ok(info);
            }
        }
        //供应商管理员客服
        RemoteSupplierVo supplier = basSupplierService.querySupplierById(supplierId);
        if (supplier == null) {
            return R.fail("供应商信息不存在");
        }
        // 查询用户表
        RemoteUserBo userBo = Optional.ofNullable(remoteUserService.getUserByUserCode(supplier.getAdminCode()))
                .orElseThrow(() -> new ServiceException("供应商超管用户不存在"));
        String supplierName = StringUtil.isNotBlank(userBo.getRealName()) ? userBo.getRealName() : supplier.getAlias();
        info = new BasImAfterSalesVo();
        info.setUserName(supplierName);
        info.setUserCode(supplier.getAdminCode());
        return R.ok(info);
    }



    /**
     * 设置部门IM客服
     */
    @PostMapping("/setDeptImUser")
    public R<Void> setDeptImUser(@Validated @RequestBody RemoteBasImAfterSalesBo bo) {
        imAfterSalesService.insertByBo(bo);
        return R.ok();
    }

    /**
     * 清除部门IM客服
     */
    @PostMapping("/deleteDeptImUser")
    public R<Void> deleteDeptImUser(@RequestParam(required = false) Long bizId, @NotBlank(message = "用户Code不能为空") @RequestParam String userCode) {
        imAfterSalesService.deleteByParm(null, userCode, bizId);
        return R.ok();
    }

}
