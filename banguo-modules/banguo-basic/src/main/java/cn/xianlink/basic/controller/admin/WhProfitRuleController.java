package cn.xianlink.basic.controller.admin;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.basic.domain.WhProfitRule;
import cn.xianlink.basic.domain.bo.profitrule.WhProfitRuleAddBo;
import cn.xianlink.basic.domain.bo.profitrule.WhProfitRuleEditBo;
import cn.xianlink.basic.domain.bo.profitrule.WhProfitRuleQueryBo;
import cn.xianlink.basic.domain.vo.citywh.CityWhVo;
import cn.xianlink.basic.domain.vo.profitrule.WhProfitRuleRecordVo;
import cn.xianlink.basic.domain.vo.profitrule.WhProfitRuleVo;
import cn.xianlink.basic.domain.vo.regionwh.RegionWhVo;
import cn.xianlink.basic.service.*;
import cn.xianlink.common.api.enums.basic.WhProfitRuleTypeEnum;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.enums.StatusEnum;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.log.annotation.Log;
import cn.xianlink.common.log.enums.BusinessType;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.core.BaseController;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 分润规则
 *
 * <AUTHOR>
 * @date 2024-03-21
 * @folder 般果管理中心/仓库/分润规则
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/whProfitRule")
public class WhProfitRuleController extends BaseController {
    private final transient IWhProfitRuleService whProfitRuleService;
    private final transient ICityWhService cityWhService;
    private final transient IRegionWhService regionWhService;
    private final transient IWhProfitRuleRecordService whProfitRuleRecordService;
    private final transient IWhNexusService whNexusService;

    /**
     * 分润类型枚举列表
     */
    @GetMapping("/enums/whProfitRuleType")
    public R<List<Map<String, Object>>> whProfitRuleType() {
        return R.ok(WhProfitRuleTypeEnum.enumlist());
    }
    /**
     * 查询-列表
     *
     * @return 分润规则列表
     */
    @PostMapping("/page")
    public R<TableDataInfo<WhProfitRuleVo>> page(@RequestBody WhProfitRuleQueryBo bo) {
        TableDataInfo<WhProfitRuleVo> table = whProfitRuleService.pageList(bo);
        if (table.getRows().size() > 0) {
            List<Long> cityWhIds = table.getRows().stream().map(WhProfitRuleVo::getCityWhId).collect(Collectors.toList());
            cityWhIds.addAll(table.getRows().stream().filter(e -> e.getLevel2CityWhId().intValue() > 0).map(WhProfitRuleVo::getLevel2CityWhId).collect(Collectors.toList()));
            cityWhIds.addAll(table.getRows().stream().map(WhProfitRuleVo::getSuperiorCityWhId).filter(superiorCityWhId -> superiorCityWhId > 0).toList());
            cityWhIds = cityWhIds.stream().distinct().toList();
            List<Long> regionWhIds = table.getRows().stream().map(WhProfitRuleVo::getRegionWhId).collect(Collectors.toList());

            Map<Long, String> cityWhMap = cityWhService.selectVoBatchIds(cityWhIds).stream().collect(Collectors.toMap(CityWhVo::getId, CityWhVo::getCityWhName, (key1, key2) -> key2));
            Map<Long, String> regionWhMap = regionWhService.selectVoBatchIds(regionWhIds).stream().collect(Collectors.toMap(RegionWhVo::getId, RegionWhVo::getRegionWhName, (key1, key2) -> key2));
            for (WhProfitRuleVo vo : table.getRows()) {
                vo.setCityWhName(cityWhMap.get(vo.getCityWhId()));
                vo.setRegionWhName(regionWhMap.get(vo.getRegionWhId()));
                vo.setLevel2CityWhName(cityWhMap.get(vo.getLevel2CityWhId()));
                vo.setSuperiorCityWhName(cityWhMap.get(vo.getSuperiorCityWhId()));
            }
        }
        return R.ok(table);
    }

    /**
     * 新增
     */
    @Log(title = "新增分润规则", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/create")
    public R<Void> create(@Validated @RequestBody WhProfitRuleAddBo bo) {
        WhProfitRule entity = MapstructUtils.convert(bo, WhProfitRule.class);
        validEntityBeforeSave(entity, true);
        return toAjax(whProfitRuleService.insertWhProfitRule(entity));
    }

    /**
     * 修改
     */
    @Log(title = "修改分润规则", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/update")
    public R<Void> update(@Validated @RequestBody WhProfitRuleEditBo bo) {
        WhProfitRule entity = MapstructUtils.convert(bo, WhProfitRule.class);
        validEntityBeforeSave(entity, false);
        return toAjax(whProfitRuleService.updateWhProfitRule(entity));
    }

    /**
     * 启用
     */
    @Log(title = "启用", businessType = BusinessType.UPDATE)
    @PostMapping("/enable/{id}")
    public R<Void> enable(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return toAjax(whProfitRuleService.updateWhProfitRuleStatus(id, StatusEnum.ENABLE.getCode()));
    }

    /**
     * 禁用
     */
    @Log(title = "禁用", businessType = BusinessType.UPDATE)
    @PostMapping("/disabled/{id}")
    public R<Void> disabled(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return toAjax(whProfitRuleService.updateWhProfitRuleStatus(id, StatusEnum.DISABLE.getCode()));
    }

    /**
     * 操作记录
     *
     * @return 总仓列表
     */
    @GetMapping(value = "/record/{ruleId}")
    public R<List<WhProfitRuleRecordVo>> record(@NotNull(message = "分润规则id不能为空") @PathVariable Long ruleId) {
        return R.ok(whProfitRuleRecordService.selectListByRuleId(ruleId));
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WhProfitRule entity, Boolean isAdd) {
        if (ObjectUtil.isNull(entity.getSuperiorCityWhId())) {
            entity.setSuperiorCityWhId(0L);
        }
        if (NumberUtil.add(entity.getCityWhRatio(), entity.getRegionWhRatio(), entity.getSuperiorCityWhRatio()).doubleValue() != 1) {
            throw new ServiceException("分润比例合计不等于100%");
        }
        if (isAdd) {
            if (whProfitRuleService.checkUniqueKey(entity.getRuleType(), entity.getCityWhId(), entity.getRegionWhId())) {
                throw new ServiceException("分润类型+分润城市仓+分润总仓，相同规则已存在");
            }
            // 校验 销售城市仓与分润总仓关系
            if (whNexusService.selectByWhId(entity.getRegionWhId(), entity.getCityWhId()) == null) {
                throw new ServiceException("分润城市仓与分润总仓未关联");
            }
        } else {
                // 修改
                WhProfitRuleVo vo = whProfitRuleService.selectAndCheckNullById(entity.getId());
                entity.setRegionWhId(vo.getRegionWhId());
                entity.setCityWhId(vo.getCityWhId());
                if (vo.getSuperiorCityWhId().intValue() == entity.getSuperiorCityWhId()) {
                    return;
                }
        }
        // 校验分润城市仓
        if (entity.getSuperiorCityWhId() > 0) {
            if (entity.getSuperiorCityWhId().equals(entity.getCityWhId())) {
                throw new ServiceException("分润城市仓与上级城市仓不能相同");
            }
            // 校验 分润城市仓与分润总仓关系
            if (whNexusService.selectByWhId(entity.getRegionWhId(), entity.getSuperiorCityWhId()) == null) {
                throw new ServiceException("分润总仓与上级城市仓未关联");
            }
        }
    }
}
