package cn.xianlink.basic.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.xianlink.basic.api.domain.bo.RemoteBasSupplierQueryBo;
import cn.xianlink.basic.api.domain.bo.RemoteQueryDeliverBo;
import cn.xianlink.basic.api.domain.vo.RemoteSupplierVo;
import cn.xianlink.basic.constant.BasCacheNames;
import cn.xianlink.basic.domain.BasSupplier;
import cn.xianlink.basic.domain.BasSupplierInfo;
import cn.xianlink.basic.domain.BasSupplierRegionWh;
import cn.xianlink.basic.domain.bo.supplier.*;
import cn.xianlink.basic.domain.dept.DeptApplyResultEnum;
import cn.xianlink.basic.domain.vo.regionwh.RegionWhVo;
import cn.xianlink.basic.domain.vo.supplier.*;
import cn.xianlink.basic.mapper.BasSupplierInfoMapper;
import cn.xianlink.basic.mapper.BasSupplierMapper;
import cn.xianlink.basic.mapper.BasSupplierRegionWhMapper;
import cn.xianlink.basic.service.IBasSupplierService;
import cn.xianlink.basic.service.IRegionWhService;
import cn.xianlink.common.api.constant.BanguoCommonConstant;
import cn.xianlink.common.api.constant.CacheNamesBanguo;
import cn.xianlink.common.api.enums.basic.EnterpriseTypeEnum;
import cn.xianlink.common.api.enums.basic.OpenAccountEnum;
import cn.xianlink.common.api.enums.basic.SupplierAuditEnum;
import cn.xianlink.common.api.enums.system.OrgTypeEnum;
import cn.xianlink.common.api.enums.trade.AccountOrgTypeEnum;
import cn.xianlink.common.api.util.NoUtil;
import cn.xianlink.common.api.util.SaleDateUtil;
import cn.xianlink.common.core.enums.AuditEnum;
import cn.xianlink.common.core.enums.StatusEnum;
import cn.xianlink.common.core.exception.ServiceException;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.core.utils.StreamUtils;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.mybatis.core.page.PageQuery;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.redis.utils.RedisUtils;
import cn.xianlink.common.satoken.utils.LoginHelper;
import cn.xianlink.common.web.bo.BasRemarkBo;
import cn.xianlink.product.api.RemoteSupplierSkuService;
import cn.xianlink.product.api.domain.bo.RemoteQuerySupplierDeliverBo;
import cn.xianlink.product.api.domain.vo.RemoteQuerySupplierDeliverVo;
import cn.xianlink.system.api.RemoteSysOrgService;
import cn.xianlink.system.api.RemoteUserService;
import cn.xianlink.system.api.domain.bo.RemoteSysOrgBo;
import cn.xianlink.system.api.domain.bo.RemoteUserBo;
import cn.xianlink.system.api.model.LoginUser;
import cn.xianlink.trade.api.RemoteOrgRelationService;
import cn.xianlink.trade.api.domain.bo.RemoteOrgRelationBindBo;
import cn.xianlink.trade.api.domain.bo.RemoteOrgRelationQueryBo;
import cn.xianlink.trade.api.domain.bo.RemoteOrgRelationUnbindBo;
import cn.xianlink.trade.api.domain.vo.RemoteOrgAuthVo;
import cn.xianlink.trade.api.domain.vo.RemoteOrgRelationStatusVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.CustomLog;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 供应商Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-09
 */
@CustomLog
@RequiredArgsConstructor
@Service
public class BasSupplierServiceImpl implements IBasSupplierService {

    private final transient BasSupplierMapper basSupplierMapper;
    private final transient BasSupplierInfoMapper basSupplierInfoMapper;
    private final transient BasSupplierRegionWhMapper basSupplierRegionWhMapper;
    private final transient IRegionWhService regionWhService;
    @DubboReference(timeout = 10000)
    private final transient RemoteOrgRelationService remoteOrgRelationService;
    @DubboReference
    private final transient RemoteUserService remoteUserService;
    @DubboReference
    private final transient RemoteSupplierSkuService remoteSupplierSkuService;
    @DubboReference
    private final transient RemoteSysOrgService remoteSysOrgService;


    @Value("${sup.no-prefix}")
    private String supNoPrefix;


    @Override
    public List<BasSupplierVo> selectByIds(List<Long> supplierIds) {
        return basSupplierMapper.selectVoBatchIds(supplierIds);
    }

    /**
     * 查询供应商列表；状态：可用
     */
    @Override
    public List<BasSupplierVo> selectListByName(String name) {
        LambdaQueryWrapper<BasSupplier> lqw = Wrappers.lambdaQuery();
        lqw.eq(BasSupplier::getStatus, StatusEnum.ENABLE.getCode());
        lqw.and(StringUtils.isNotBlank(name), q -> q.likeRight(BasSupplier::getCode, name).or().likeRight(BasSupplier::getName, name).or().likeRight(BasSupplier::getAlias, name));
        lqw.orderByAsc(BasSupplier::getId);
        return basSupplierMapper.selectVoList(lqw);
    }

    @Override
    public BasSupplierVo queryAndPassRegionWhById(Long supplierId) {
        return this.queryDbById(supplierId, AuditEnum.PASS.getCode());
    }

    /**
     * 查询供应商
     */
    @Cacheable(cacheNames = CacheNamesBanguo.SUPPLIER_CACHE, key = "#supplierId")
    @Override
    public BasSupplierVo queryById(Long supplierId) {
        return this.queryDbById(supplierId, null);
    }

    /**
     * 查询供应商
     */
    private BasSupplierVo queryDbById(Long supplierId, Integer auditStatus) {
        if (supplierId == null || supplierId == 0L) {
            return null;
        }
        BasSupplierVo vo = basSupplierMapper.selectVoById(supplierId);
        if (vo == null) {
            throw new ServiceException("供应商id不存在，请检查数据");
        }
        RemoteUserBo userBo = remoteUserService.getUsersByUserCodes(Lists.newArrayList(vo.getAdminCode())).get(vo.getAdminCode());
        if (userBo != null) {
            vo.setAdminPhone(userBo.getPhoneNo());
            vo.setAdminName(userBo.getRealName());
        }

        //供货主体
        if (vo.getSupplyBg().equals(1) && vo.getSupplyYls().equals(1)) {
            vo.setToSupply("般果，易良食");
        } else if (vo.getSupplyBg().equals(1)) {
            vo.setToSupply("般果");
        } else if (vo.getSupplyYls().equals(1)) {
            vo.setToSupply("易良食");
        }
//        vo.setAuditStatusName(SupplierAuditEnum.loadByCode(vo.getAuditStatus()).getDesc());
        if (StatusEnum.ENABLE.getCode().equals(vo.getStatus())) {
            vo.setStatusName("启用");
        } else {
            vo.setStatusName("禁用");
        }
//        vo.setStatusName(StatusEnum.loadByCode(vo.getStatus()).getDesc());

        BasSupplierInfoVo info = MapstructUtils.convert(basSupplierInfoMapper.selectBySupplierId(supplierId), BasSupplierInfoVo.class);
        info.setUrlBusinessLicense(info.getUrlBusinessLicense());
        info.setUrlIdCardObverse(info.getUrlIdCardObverse());
        info.setUrlIdCardReverse(info.getUrlIdCardReverse());
        vo.setSupplierInfo(info);

        //详情取最新的总仓数据
        LambdaQueryWrapper<BasSupplierRegionWh> lqw = Wrappers.lambdaQuery();
        lqw.eq(BasSupplierRegionWh::getSupplierId, supplierId);
        lqw.eq(ObjectUtil.isNotNull(auditStatus), BasSupplierRegionWh::getAuditStatus, auditStatus);
        List<BasSupplierRegionWhVo> regionWhVos = basSupplierRegionWhMapper.selectVoList(lqw);
        Map<Long, RegionWhVo> regionWhNameMap = StreamUtils.toMap(regionWhService.selectVoBatchIds(StreamUtils.toList(regionWhVos, BasSupplierRegionWhVo::getRegionWhId)), RegionWhVo::getId, Function.identity());
        regionWhVos.forEach(e -> {
            RegionWhVo regionWhVo = regionWhNameMap.get(e.getRegionWhId());
            if (ObjectUtil.isNotNull(regionWhVo)) {
                e.setRegionWhName(regionWhVo.getRegionWhName());
                e.setIsOrderItemDelivery(regionWhVo.getIsOrderItemDelivery());
                e.setEffectSaleDate(regionWhVo.getEffectSaleDate());
                //总仓是否开启坑位功能
                e.setIsSaleNum(regionWhVo.getIsSaleNum());
            }
        });
        vo.setRegionWhList(regionWhVos);

        //转账授权 0未授权；1已授权
        Integer transferAuthStatus = AuditEnum.PASS.getCode();
        RemoteOrgRelationQueryBo queryBo = new RemoteOrgRelationQueryBo();
        queryBo.setOrgType(AccountOrgTypeEnum.SUPPLIER.getCode());
        queryBo.setOrgCode(vo.getCode());
        RemoteOrgAuthVo authVO = remoteOrgRelationService.querySameOrgAuth(queryBo);
        if (authVO != null && authVO.getAuthValidDate() != null) {
            //取到过期时间，减去30天，判断是否需要重新授权
            LocalDate expireDate = authVO.getAuthValidDate().minusDays(30);
            if (expireDate.isBefore(LocalDate.now())) {
                transferAuthStatus = AuditEnum.WAIT.getCode();
            }
        }
        vo.setTransferAuthStatus(transferAuthStatus);
        return vo;
    }

    @Cacheable(cacheNames = CacheNamesBanguo.SUPPLIER_SIMPLE_CACHE, key = "#supplierId")
    @Override
    public RemoteSupplierVo querySimperById(Long supplierId) {
        if (supplierId == null || supplierId == 0L) {
            return null;
        }
        LambdaQueryWrapper<BasSupplier> lqw = Wrappers.lambdaQuery();
        lqw.eq(BasSupplier::getId, supplierId);
//        lqw.select(BasSupplier::getId, BasSupplier::getName, BasSupplier::getCode, BasSupplier::getStatus,
//                BasSupplier::getAuditStatus, BasSupplier::getSimpleCode, BasSupplier::getAlias);
        BasSupplier vo = basSupplierMapper.selectOne(lqw);
        if (vo == null) {
            throw new ServiceException("供应商id不存在，请检查数据");
        }
        return BeanUtil.copyProperties(vo, RemoteSupplierVo.class);
    }

    @Override
    public List<RemoteSupplierVo> querySimperByIds(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<BasSupplier> lqw = Wrappers.lambdaQuery();
        lqw.in(BasSupplier::getId, ids);
        List<BasSupplier> suppliers = basSupplierMapper.selectList(lqw);
        return BeanUtil.copyToList(suppliers, RemoteSupplierVo.class);
    }

    @Override
    public List<BasSupplierSimperVo> queryByUser(LoginUser loginUser) {
        //todo 查询用户关联的所有供应商id
        List<Long> supplierIds = new ArrayList<>();
        if (CollUtil.isEmpty(supplierIds)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<BasSupplier> lqw = Wrappers.lambdaQuery();
        lqw.in(BasSupplier::getId, supplierIds);
//        lqw.select(BasSupplier::getId, BasSupplier::getCode, BasSupplier::getName, BasSupplier::getStatus, BasSupplier::getAuditStatus, BasSupplier::getSimpleCode);
        List<BasSupplier> list = basSupplierMapper.selectList(lqw);
        List<BasSupplierSimperVo> result = MapstructUtils.convert(list, BasSupplierSimperVo.class);
        for (BasSupplierSimperVo basSupplierSimperVo : result) {
            basSupplierSimperVo.setAuditStatusName(SupplierAuditEnum.loadByCode(basSupplierSimperVo.getAuditStatus()).getDesc());
            basSupplierSimperVo.setStatusName(StatusEnum.loadByCode(basSupplierSimperVo.getStatus()).getDesc());
        }
        return result;
    }


    @Override
    public BasSupplierVo queryByUserCheck(LoginUser loginUser) {
        return queryDbById(loginUser.getRelationId(), null);
    }

    @Override
    public List<RemoteSupplierVo> getSupplierByCode(List<String> supplierCodes) {
        if (CollUtil.isEmpty(supplierCodes)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<BasSupplier> lqw = Wrappers.lambdaQuery();
        lqw.in(BasSupplier::getCode, supplierCodes);
        lqw.eq(BasSupplier::getDelFlag, 0);
//        lqw.select(BasSupplier::getId, BasSupplier::getCode, BasSupplier::getName, BasSupplier::getStatus, BasSupplier::getAuditStatus, BasSupplier::getSimpleCode);
        List<BasSupplier> list = basSupplierMapper.selectList(lqw);
        return BeanUtil.copyToList(list, RemoteSupplierVo.class);
    }

    /**
     * 查询供应商列表
     */
    @Override
    public TableDataInfo<BasSupplierAdminListVo> queryPageList(BasSupplierSearchBo bo, PageQuery pageQuery) {
        bo.setTenantId(LoginHelper.getTenantId());
        if (bo.getToSupply() != null) {
            if (bo.getToSupply().equals("1")) {
                bo.setSupplyBg(1);
            } else {
                bo.setSupplyYls(2);
            }
        }
        Page<BasSupplierAdminListVo> result = basSupplierMapper.selectPageByAdmin(bo, pageQuery.build());
        var list = result.getRecords();
        if (CollUtil.isNotEmpty(list)) {
            var idSet = list.stream().map(BasSupplierAdminListVo::getId).collect(Collectors.toSet());
            List<BasSupplierRegionWh> regionWhList = basSupplierRegionWhMapper.selectBySupplierIds(idSet, false);
            var regionMap = regionWhList.stream()
                    .collect(Collectors.groupingBy(BasSupplierRegionWh::getSupplierId));
            Set<String> userCodes = new HashSet<>();
            for (BasSupplierAdminListVo vo : list) {
                //供货主体
                if (vo.getSupplyBg().equals(1) && vo.getSupplyYls().equals(1)) {
                    vo.setToSupply("般果,易良食");
                } else if (vo.getSupplyBg().equals(1)) {
                    vo.setToSupply("般果");
                } else if (vo.getSupplyBg().equals(1)) {
                    vo.setToSupply("易良食");
                }
                if (StatusEnum.ENABLE.getCode().equals(vo.getStatus())) {
                    vo.setStatusName("启用");
                } else {
                    vo.setStatusName("禁用");
                }
                vo.setAuditStatusName(SupplierAuditEnum.loadByCode(vo.getAuditStatus()).getDesc());
                //总仓
                List<BasSupplierRegionWh> regions = regionMap.get(vo.getId());
                if (CollUtil.isNotEmpty(regions)) {
                    List<BasSupplierRegionWhVo> regionWhVos = MapstructUtils.convert(regions, BasSupplierRegionWhVo.class);
                    vo.setRegionWhList(regionWhVos);
                    //查找任意一个总仓坑位大于0
                    Optional<BasSupplierRegionWhVo> findAny = regionWhVos.stream().filter(e -> ObjectUtil.isNotEmpty(e.getMaxSaleNum()) && e.getMaxSaleNum() > 0).findAny();
                    vo.setSaleNumStatus(findAny.isPresent() ? 1 : 0);
                }
                userCodes.add(vo.getAdminCode());
            }
            if (CollUtil.isNotEmpty(userCodes)) {
                Map<String, RemoteUserBo> userMap = remoteUserService.getUsersByUserCodes(userCodes.stream().toList());
                for (BasSupplierAdminListVo vo : list) {
                    RemoteUserBo userBo = userMap.get(vo.getAdminCode());
                    if (userBo != null) {
                        vo.setAdminPhone(userBo.getPhoneNo());
                        vo.setAdminName(userBo.getRealName());
                    }
                }
            }
        }
        return TableDataInfo.build(result);
    }

    /**
     * 新增供应商
     */
    @GlobalTransactional
    @Override
    public DeptApplyResultEnum insertByBo(BasSupplierBo bo) {
        BasSupplier supplier = MapstructUtils.convert(bo, BasSupplier.class);

        LoginUser loginUser = LoginHelper.getLoginUser();
        supplier.setTenantId(loginUser.getTenantId());
        supplier.setAdminCode(loginUser.getUserCode());
        validEntityBeforeSave(supplier, bo.getSupplierInfo().getEnterpriseCreditCode());
        insertSupplierFill(supplier);

        RemoteUserBo remoteUserVo = remoteUserService.getUserByUserCode(loginUser.getUserCode());
        if (remoteUserVo.getOrgId() != null && remoteUserVo.getOrgId() > 0L) {
            //已绑定机构，不能注册供应商
            remoteUserService.refreshUserCache(loginUser.getUserId());
            return DeptApplyResultEnum.FAIL;
        }

        basSupplierMapper.insert(supplier);

        bo.setId(supplier.getId());

        BasSupplierInfo info = MapstructUtils.convert(bo.getSupplierInfo(), BasSupplierInfo.class);
        info.setSupplierId(supplier.getId());
        info.setTenantId(supplier.getTenantId());
        basSupplierInfoMapper.insert(info);
        //bank暂时不做
        BasSupplierRegionWh regionWh = MapstructUtils.convert(bo.getRegionWhList().get(0), BasSupplierRegionWh.class);
        regionWh.setSupplierId(supplier.getId());
        regionWh.setTenantId(supplier.getTenantId());
        regionWh.setAuditType(1);
        basSupplierRegionWhMapper.insert(regionWh);
        //绑定超管和供应商的关系
        RemoteSysOrgBo remoteSysOrgBo = new RemoteSysOrgBo().setCode(supplier.getCode()).setName(supplier.getName()).setRelationId(supplier.getId()).setRelationType(OrgTypeEnum.SUPPLIER);
        remoteUserService.bindingSysOrg(loginUser.getUserId(), remoteSysOrgBo);
        return DeptApplyResultEnum.SUCCESS;
    }

    /**
     * 修改供应商
     */
    @CacheEvict(cacheNames = {CacheNamesBanguo.SUPPLIER_CACHE, CacheNamesBanguo.SUPPLIER_SIMPLE_CACHE}, key = "#bo.id")
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateByBo(BasSupplierBo bo) {
        BasSupplier updateSupplier = MapstructUtils.convert(bo, BasSupplier.class);

        BasSupplier oldSupplier = basSupplierMapper.selectById(updateSupplier.getId());
        this.updateSupplierFill(updateSupplier, oldSupplier);
        boolean flag = basSupplierMapper.updateById(updateSupplier) > 0;
        if (!flag) {
            return false;
        }
        //--------------------供应商额外信息--------------------
        BasSupplierInfo oldInfo = basSupplierInfoMapper.selectBySupplierId(bo.getId());
//        basSupplierInfoMapper.selectById()
        BasSupplierInfo info = MapstructUtils.convert(bo.getSupplierInfo(), BasSupplierInfo.class);

        if (basSupplierMapper.checkEnterprise(updateSupplier.getId(), info.getEnterpriseCreditCode()) > 0) {
            throw new ServiceException("统一社会信用代码已使用，不支持重复注册");
        }

        info.setId(oldInfo.getId());
        info.setSupplierId(updateSupplier.getId());
        info.setTenantId(updateSupplier.getTenantId());
        info.setScooterCapacity(oldInfo.getScooterCapacity());
        basSupplierInfoMapper.updateById(info);
        //--------------------供应商额外信息--------------------

        //--------------------供货总仓--------------------
        //未传总仓，则终止所有总仓供货
        if (CollUtil.isEmpty(bo.getRegionWhList())) {
            basSupplierRegionWhMapper.stopAllSupply(bo.getId());
            return Boolean.TRUE;
        }
        List<BasSupplierRegionWh> nowRegions = MapstructUtils.convert(bo.getRegionWhList(), BasSupplierRegionWh.class);
        Map<Long, BasSupplierRegionWh> nowRegionMap = nowRegions.stream().collect(Collectors.toMap(BasSupplierRegionWh::getRegionWhId, Function.identity(), (key1, key2) -> key2));

        List<BasSupplierRegionWh> dbRegions = basSupplierRegionWhMapper.selectBySupplierId(bo.getId(), true);
        Map<Long, BasSupplierRegionWh> dbRegionMap = dbRegions.stream().collect(Collectors.toMap(BasSupplierRegionWh::getRegionWhId, Function.identity(), (key1, key2) -> key2));

        Set<Long> nowIds = nowRegionMap.keySet();
        Set<Long> oldIds = dbRegionMap.keySet();
        //找出需要删除的
        Set<Long> deleteIds = Sets.newHashSet(oldIds);
        deleteIds.removeAll(nowIds);
        if (!deleteIds.isEmpty()) {
            basSupplierRegionWhMapper.deleteBySupply(new ArrayList<>(deleteIds), bo.getId());
        }
        //找出需要新增的
        Set<Long> insetIds = Sets.newHashSet(nowIds);
        insetIds.removeAll(oldIds);
        if (!insetIds.isEmpty()) {
            List<BasSupplierRegionWh> insetRegions = nowRegions.stream().filter(v -> insetIds.contains(v.getRegionWhId())).map(p -> {
                p.setSupplierId(updateSupplier.getId());
                p.setTenantId(updateSupplier.getTenantId());
                return p;
            }).collect(Collectors.toList());
            basSupplierRegionWhMapper.insertBatch(insetRegions);
        }
        //找出需要更新name的
        nowIds.retainAll(oldIds);
        List<BasSupplierRegionWh> updateRegion = new ArrayList<>();
        for (BasSupplierRegionWh dbRegion : dbRegions) {
            BasSupplierRegionWh newRegion = nowRegionMap.get(dbRegion.getRegionWhId());
            //判断是否需要修改名称
            if (newRegion != null) {
                if (dbRegion.getDelFlag() != 0L || !dbRegion.getRegionWhName().equals(newRegion.getRegionWhName())) {
                    dbRegion.setRegionWhName(newRegion.getRegionWhName());
                    updateRegion.add(dbRegion);
                }
            }
        }
        for (BasSupplierRegionWh regionWh : updateRegion) {
            //批量修改有问题，暂时先循环修改
            basSupplierRegionWhMapper.updateName(regionWh);
        }
        //--------------------供货总仓--------------------
        return Boolean.TRUE;
    }

    @CacheEvict(cacheNames = {CacheNamesBanguo.SUPPLIER_CACHE, CacheNamesBanguo.SUPPLIER_SIMPLE_CACHE}, key = "#supplierId")
    @Override
    public Boolean reapply(Long supplierId) {
        BasSupplier db = basSupplierMapper.selectById(supplierId);
        if (db == null) {
            log.error("supplierId 不存在，不能重新申请审核");
            throw new ServiceException("供应商不存在，不能重新申请审核");
        }
        if (!db.getAuditStatus().equals(2)) {
            log.error("auditStatus 错误，不能重新申请审核");
            throw new ServiceException("审核状态不是“审核不通过”，不能重新申请审核");
        }
        BasSupplier su = new BasSupplier();
        su.setId(supplierId);
        su.setAuditStatus(0);
        basSupplierMapper.updateById(su);
        this.updateRegionWhBySupplierId(su);
        return true;
    }

    /**
     * 修改供应商
     */
    @CacheEvict(cacheNames = {CacheNamesBanguo.SUPPLIER_CACHE, CacheNamesBanguo.SUPPLIER_SIMPLE_CACHE}, key = "#bo.id")
    @Override
    public Integer updateAliasById(BasSupplierEditAliasBo bo) {
        BasSupplier entity = MapstructUtils.convert(bo, BasSupplier.class);
        return basSupplierMapper.updateById(entity);
    }

    @CacheEvict(cacheNames = {CacheNamesBanguo.SUPPLIER_CACHE, CacheNamesBanguo.SUPPLIER_SIMPLE_CACHE}, key = "#supplierId")
    @Override
    public Boolean enableOrDisabled(Long supplierId, StatusEnum statusEnum) {
        LambdaQueryWrapper<BasSupplier> lqw = Wrappers.lambdaQuery();
        lqw.eq(BasSupplier::getId, supplierId);
        lqw.select(BasSupplier::getId, BasSupplier::getStatus);
        BasSupplier supplier = basSupplierMapper.selectOne(lqw);
        if (supplier.getStatus().equals(statusEnum.getCode())) {
            throw new ServiceException("操作失败，请刷新供应商状态");
        }
        supplier.setStatus(statusEnum.getCode());
        basSupplierMapper.updateById(supplier);

        // 调用system模块，完成关联的系统机构的状态更新
        remoteSysOrgService.changeStatus(supplierId, OrgTypeEnum.SUPPLIER, StatusEnum.isEnable(supplier.getStatus()));

        return true;
    }

    @GlobalTransactional
    @CacheEvict(cacheNames = {CacheNamesBanguo.SUPPLIER_CACHE, CacheNamesBanguo.SUPPLIER_SIMPLE_CACHE}, key = "#supplierId")
    @Override
    public void accountCancel(Long supplierId) {
        LambdaQueryWrapper<BasSupplier> lqw = Wrappers.lambdaQuery();
        lqw.eq(BasSupplier::getId, supplierId);
        lqw.select(BasSupplier::getId, BasSupplier::getCode, BasSupplier::getAccountStatus);
        BasSupplier supplier = basSupplierMapper.selectOne(lqw);
        if (supplier == null) {
            throw new ServiceException("供应商不存在");
        }
        Integer oldAccountStatus = supplier.getAccountStatus();
        //更新本地状态
        supplier.setStatus(StatusEnum.DISABLE.getCode());
        supplier.setAccountStatus(OpenAccountEnum.CANCEL.getCode());
        basSupplierMapper.updateById(supplier);
        //解除用户和机构的绑定，踢人下线
        remoteSysOrgService.orgCancel(supplierId, OrgTypeEnum.SUPPLIER);

        if (OpenAccountEnum.OPEN.getCode().equals(oldAccountStatus)) {
            //开了户的情况下，才需要注销子账户，否则会抛异常
            var unbindBo = new RemoteOrgRelationUnbindBo().setOrgType(AccountOrgTypeEnum.SUPPLIER.getCode()).setOrgCode(supplier.getCode());
            remoteOrgRelationService.closeAccount(unbindBo);
        }
    }

    @CacheEvict(cacheNames = {CacheNamesBanguo.SUPPLIER_CACHE, CacheNamesBanguo.SUPPLIER_SIMPLE_CACHE}, key = "#bo.getId()")
    @Override
    public Boolean remark(BasRemarkBo bo) {
        BasSupplier supplier = new BasSupplier();
        supplier.setId(bo.getId());
        supplier.setRemark(bo.getRemark() == null ? "" : bo.getRemark());
        return basSupplierMapper.updateById(supplier) > 0;
    }

    @CacheEvict(cacheNames = {CacheNamesBanguo.SUPPLIER_CACHE, CacheNamesBanguo.SUPPLIER_SIMPLE_CACHE}, key = "#bo.id", beforeInvocation = true)
    @Override
    public Boolean audit(BasSupplierAuditBo bo) {
        //todo 校验这个人是否有权限审核这个客户
        LambdaQueryWrapper<BasSupplier> lqw = Wrappers.lambdaQuery();
        lqw.eq(BasSupplier::getId, bo.getId());
        lqw.select(BasSupplier::getId, BasSupplier::getAuditStatus, BasSupplier::getCode, BasSupplier::getName);
        BasSupplier supplier = basSupplierMapper.selectOne(lqw);
        if (supplier.getAuditStatus().equals(bo.getAuditStatus())) {
            throw new ServiceException("操作失败，请刷新供应商审核状态");
        }
        if (supplier.getAuditStatus().equals(SupplierAuditEnum.PASS.getCode())) {
            throw new ServiceException("操作失败，供应商已审核通过，不能再次审核");
        }
        LoginUser user = LoginHelper.getLoginUser();
        Date now = new Date();
        supplier.setAuditor(user.getNickName());
        supplier.setAuditStatus(bo.getAuditStatus());
        supplier.setAuditRemark(bo.getAuditRemark());
        supplier.setAuditTime(now);
        supplier.setAuditorCode(user.getUserCode());

        if (bo.getAuditStatus().equals(SupplierAuditEnum.PASS.getCode())) {
            supplier.setStatus(StatusEnum.ENABLE.getCode());
            supplier.setStatusTime(now);
            //审核通过，同步开通平安子账户
            BasSupplierInfo info = basSupplierInfoMapper.selectBySupplierId(supplier.getId());

            RemoteOrgRelationBindBo bindBo = new RemoteOrgRelationBindBo();
            bindBo.setOrgType(AccountOrgTypeEnum.SUPPLIER.getCode());
            bindBo.setOrgId(supplier.getId());
            bindBo.setOrgCode(supplier.getCode());
            bindBo.setOrgName(supplier.getName());
            bindBo.setBankMobile(info.getLegalPersonPhone());
            bindBo.setBusinessFlag(EnterpriseTypeEnum.convertBusinessFlag(info.getEnterpriseType()));
            bindBo.setCompanyName(supplier.getName());
            bindBo.setCompanyGlobalId(info.getEnterpriseCreditCode());
            bindBo.setReprName(info.getLegalPersonName());
            bindBo.setReprGlobalId(info.getLegalPersonIdCard());

            RemoteOrgRelationStatusVo relationVo;
            try {
                relationVo = remoteOrgRelationService.openAccount(bindBo);
            } catch (Exception e) {
                BasSupplier errorSupplier = new BasSupplier();
                errorSupplier.setId(supplier.getId());
                errorSupplier.setAuditStatus(SupplierAuditEnum.REJECT.getCode());
                errorSupplier.setAuditRemark(e.getMessage());
                errorSupplier.setAuditor(user.getNickName());
                errorSupplier.setAuditTime(now);
                errorSupplier.setAuditorCode(user.getUserCode());
                basSupplierMapper.updateById(errorSupplier);
                this.updateRegionWhBySupplierId(errorSupplier);
                String errMsg = StrUtil.format("开通平安子账号失败，自动驳回，失败原因：{}", e.getMessage());
                throw new ServiceException(errMsg);
            }
            supplier.setAccountSub(relationVo.getOutAcctCode());
            supplier.setAccountStatus(OpenAccountEnum.OPEN.getCode());
            supplier.setAlias(bo.getAlias());
        }
        basSupplierMapper.updateById(supplier);
        this.updateRegionWhBySupplierId(supplier);
        return Boolean.TRUE;
    }

    /**
     * 修改供货总仓审核状态
     */
    private int updateRegionWhBySupplierId(BasSupplier supplier) {
        LoginUser user = LoginHelper.getLoginUser();
        LambdaQueryWrapper<BasSupplierRegionWh> lqw = Wrappers.lambdaQuery();
        lqw.eq(BasSupplierRegionWh::getSupplierId, supplier.getId());
        BasSupplierRegionWh entity = new BasSupplierRegionWh();
        entity.setAuditStatus(supplier.getAuditStatus());
        entity.setAuditRemark(supplier.getAuditRemark());
        entity.setAuditTime(DateTime.now());
        entity.setAuditorCode(user.getUserCode());
        entity.setAuditorName(user.getNickName());
        return basSupplierRegionWhMapper.update(entity, lqw);
    }

    @CacheEvict(cacheNames = CacheNamesBanguo.SUPPLIER_CACHE, key = "#supplierId")
    @Override
    public void scooterCapacity(Integer scooterCapacity, Long supplierId) {
        basSupplierInfoMapper.updateScooterCapacity(supplierId, scooterCapacity);
    }

    @Override
    public List<BasSupplierSimperVo> fuzzySearchByName(String name, String status) {
        List<BasSupplierSimperVo> result = basSupplierMapper.selectByNameAndStatus(name, status);
        for (BasSupplierSimperVo basSupplierSimperVo : result) {
            basSupplierSimperVo.setAuditStatusName(SupplierAuditEnum.loadByCode(basSupplierSimperVo.getAuditStatus()).getDesc());
            basSupplierSimperVo.setStatusName(StatusEnum.loadByCode(basSupplierSimperVo.getStatus()).getDesc());
        }
        return result;
    }

    @Override
    public List<BasSupplierSimperVo> fuzzySearchByName2(SupCommonFuzzySearchBo searchBo) {
        List<BasSupplierSimperVo> result = basSupplierMapper.dropdownBoxSearch(searchBo);
        for (BasSupplierSimperVo basSupplierSimperVo : result) {
            basSupplierSimperVo.setAuditStatusName(SupplierAuditEnum.loadByCode(basSupplierSimperVo.getAuditStatus()).getDesc());
            basSupplierSimperVo.setStatusName(StatusEnum.loadByCode(basSupplierSimperVo.getStatus()).getDesc());
        }
        return result;
    }

    /**
     * 根据供应商名称模糊搜索（分页）
     *
     * @param searchBo
     * @return
     */
    public TableDataInfo<BasSupplierSimperVo> fuzzySearchByNameByPage(SupCommonFuzzySearchBo searchBo) {
        Page<BasSupplierSimperVo> resultPage = basSupplierMapper.dropdownBoxSearchByPage(searchBo, searchBo.build());
        List<BasSupplierSimperVo> result = resultPage.getRecords();
        for (BasSupplierSimperVo basSupplierSimperVo : result) {
            basSupplierSimperVo.setAuditStatusName(SupplierAuditEnum.loadByCode(basSupplierSimperVo.getAuditStatus()).getDesc());
            basSupplierSimperVo.setStatusName(StatusEnum.loadByCode(basSupplierSimperVo.getStatus()).getDesc());
        }
        return TableDataInfo.build(result,resultPage.getTotal());
    }

    /**
     * 根据总仓id查询供应商列表
     *
     * @param bo
     * @return
     */
    @Override
    public List<QuerySupplierDeliverPageVo> querySupplierDeliverPage(QuerySupplierDeliverPageBo bo) {
        Page<QuerySupplierDeliverPageVo> page = basSupplierMapper.querySupplierDeliverPage(bo, bo.build());
        List<QuerySupplierDeliverPageVo> voList = page.getRecords();
        if (voList != null && voList.size() > 0) {
            List<Long> supplierIdList = voList.stream().map(QuerySupplierDeliverPageVo::getId).distinct().toList();
            RemoteQuerySupplierDeliverBo deliverBo = new RemoteQuerySupplierDeliverBo();
            deliverBo.setRegionWhId(bo.getRegionWhId());
            deliverBo.setSupplierIdList(supplierIdList);
            List<RemoteQuerySupplierDeliverVo> deliverVoList = remoteSupplierSkuService.querySupplierDeliver(deliverBo);
            Map<Long, RemoteQuerySupplierDeliverVo> deliverMap = new HashMap<>();
            if (deliverVoList != null && deliverVoList.size() > 0) {
                deliverMap = deliverVoList.stream().collect(Collectors.toMap(RemoteQuerySupplierDeliverVo::getSupplierId, Function.identity(), (key1, key2) -> key2));
            }
            List<QuerySupplierDeliverPageVo> result = new ArrayList<>();
            String key = BasCacheNames.SUPPLIER_DELIVER + bo.getRegionWhId() + getDeliveryDate(bo.getRegionWhId()).toString();
            Map<String, String> map = RedisUtils.getCacheMap(key);
            for (QuerySupplierDeliverPageVo vo : voList) {
                RemoteQuerySupplierDeliverVo deliverVo = deliverMap.get(vo.getId());
                if (deliverVo != null) {
                    if (deliverVo.getCount() > 0) {
                        result.add(vo);
                        vo.setSkuCount(deliverVo.getCount());
                    } else {
                        continue;
                    }
                }
                vo.setDeliver(map != null && map.containsKey(String.valueOf(vo.getId())));
            }
            //手动排序
            return result.stream().sorted(Comparator.comparing(QuerySupplierDeliverPageVo::getDeliver)).collect(Collectors.toList());
        }
        return voList;
    }

    @Override
    public TableDataInfo<QuerySupplierDeliverPageVo> querySupplierByRegion(QuerySupplierDeliverPageBo bo) {
        Page<QuerySupplierDeliverPageVo> page = basSupplierMapper.querySupplierDeliverPage(bo, bo.build());
        return TableDataInfo.build(page);
    }

    /**
     * 审核供应商是否能查询待送货列表
     *
     * @param bo
     * @return
     */
    @Override
    public Boolean updateSupplierDeliverStatus(UpdateSupplierDeliverStatusBo bo) {
        String saleDate = getDeliveryDate(bo.getRegionWhId()).toString();
        for (Long supplierId : bo.getSupplierIdList()) {
            String key = BasCacheNames.SUPPLIER_DELIVER + bo.getRegionWhId() + saleDate;
            RedisUtils.setCacheMapValue(key, String.valueOf(supplierId), saleDate);
            RedisUtils.expire(key, Duration.ofHours(25));
        }
        return true;
    }

    /**
     * 查询这个供应商能不能查看待送货列表的数据,false不可以查看，true可以查看
     *
     * @param bo
     * @return
     */
    @Override
    public Boolean queryDeliver(RemoteQueryDeliverBo bo) {
        String key = BasCacheNames.SUPPLIER_DELIVER + bo.getRegionWhId() + bo.getDeliverDate();
        Map<String, String> map = RedisUtils.getCacheMap(key);
        return map != null && map.containsKey(String.valueOf(bo.getSupplierId()));
    }

    @Override
    public List<Long> selectSupIdByNormalStatus() {
        LambdaQueryWrapper<BasSupplier> lqw = Wrappers.lambdaQuery();
        lqw.eq(BasSupplier::getAccountStatus, OpenAccountEnum.OPEN.getCode());
        lqw.eq(BasSupplier::getDelFlag, BanguoCommonConstant.notDelFlag);
        lqw.select(BasSupplier::getId);
        return basSupplierMapper.selectList(lqw).stream().map(BasSupplier::getId).collect(Collectors.toList());
    }

    /**
     * 根据发货结束时间获取发货日
     *
     * @param regionWhId
     * @return
     */
    private LocalDate getDeliveryDate(Long regionWhId) {
        RegionWhVo whVo = regionWhService.selectById(regionWhId);
        if (whVo == null) {
            throw new ServiceException("总仓不存在");
        }
        if (whVo.getStatus() == 0) {
            throw new ServiceException("总仓已被禁用");
        }
        return SaleDateUtil.getSaleDate(whVo.getDeliveryTimeEnd());
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BasSupplier entity, String enterpriseCreditCode) {
        LambdaQueryWrapper<BasSupplier> lqw = Wrappers.lambdaQuery();
        lqw.eq(BasSupplier::getAdminCode, entity.getAdminCode());
        lqw.eq(BasSupplier::getDelFlag, 0);
        Long count = basSupplierMapper.selectCount(lqw);
        if (count != null && count > 0L) {
            throw new ServiceException("你已经注册了供应商账户，不支持重复注册");
        }
        if (basSupplierMapper.checkEnterprise(entity.getId(), enterpriseCreditCode) > 0) {
            throw new ServiceException("统一社会信用代码已使用，不支持重复注册");
        }
    }

    private void insertSupplierFill(BasSupplier add) {
        add.setCode(this.seqNo());
        add.setSimpleCode(this.seqSimpleNo());
        //默认向般果供货，不向易良食供货
        add.setSupplyBg(1);
        add.setSupplyYls(0);
    }

    private void updateSupplierFill(BasSupplier updateSupplier, BasSupplier oldSupplier) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        updateSupplier.setTenantId(loginUser.getTenantId());
        updateSupplier.setAdminCode(oldSupplier.getAdminCode());
        updateSupplier.setCode(oldSupplier.getCode());
        updateSupplier.setSimpleCode(oldSupplier.getSimpleCode());
    }


    private String seqNo() {
        return supNoPrefix
                + (1000000 + RedisUtils.incrAtomicValue(BasCacheNames.SUPPLIER_NO_KEY))
                + NoUtil.randomNumberCode(2, null);
    }

    private String seqSimpleNo() {
        String no = null;
        do {
            no = NoUtil.randomCode(5, NoUtil.IGNORE_SUP_SIMP_CODE);
        } while (!RedisUtils.addCacheSet(BasCacheNames.SUPPLIER_NO_SIMPLE_KEY, no));
        return no;
    }


    @Override
    public List<Long> selectSupIdByNewRegister(Integer day) {
        return basSupplierMapper.selectSupIdByNewRegister(day);
    }

    /**
     * 查询供应商
     * @param bo
     * @return
     */
    @Override
    public TableDataInfo<BasSupplierVo> findSupplierPage(RemoteBasSupplierQueryBo bo) {
        LambdaQueryWrapper<BasSupplier> lqw = Wrappers.lambdaQuery();
        lqw.like(Objects.nonNull(bo.getCodeOrName()), BasSupplier::getCode, bo.getCodeOrName())
                .or().like(Objects.nonNull(bo.getCodeOrName()), BasSupplier::getName, bo.getCodeOrName());
        IPage<BasSupplierVo> page = basSupplierMapper.selectVoPage(bo.build(), lqw);
        return TableDataInfo.build(page);
    }

    /**
     * 根据管理员编码查询供应商id
     * @param list
     * @return
     */
    @Override
    public List<Long> getSupplierByAdminCode(List<String> list) {
        LambdaQueryWrapper<BasSupplier> lqw = Wrappers.lambdaQuery();
        lqw.in(BasSupplier::getAdminCode, list)
                .eq(BasSupplier::getDelFlag, BanguoCommonConstant.notDelFlag)
                .eq(BasSupplier::getStatus, StatusEnum.ENABLE.getCode());
        lqw.select(BasSupplier::getId);
        return basSupplierMapper.selectList(lqw).stream().map(BasSupplier::getId).collect(Collectors.toList());
    }

    /**
     * 根据供应商id查询供应商信息
     * @param supplierId
     * @return
     */
    @Override
    public RemoteSupplierVo querySupplierById(Long supplierId) {
        LambdaQueryWrapper<BasSupplier> lqw = Wrappers.lambdaQuery();
        lqw.eq(BasSupplier::getId, supplierId)
                .eq(BasSupplier::getDelFlag, BanguoCommonConstant.notDelFlag)
                .eq(BasSupplier::getStatus, StatusEnum.ENABLE.getCode());
        BasSupplier vo = basSupplierMapper.selectOne(lqw);
        return BeanUtil.copyProperties(vo, RemoteSupplierVo.class);
    }
}
