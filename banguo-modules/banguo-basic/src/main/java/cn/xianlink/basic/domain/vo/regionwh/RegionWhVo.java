package cn.xianlink.basic.domain.vo.regionwh;

import cn.xianlink.basic.domain.RegionWh;
import cn.xianlink.basic.domain.bo.regionwh.AffiliatedCityWhBean;
import cn.xianlink.common.dict.annotation.DictConvertFiled;
import cn.xianlink.common.dict.enums.DictConvertTypeEnum;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * region_wh，总仓视图对象
 *
 * <AUTHOR>
 * @date 2024-03-21
 */
@Data
@AutoMapper(target = RegionWh.class)
public class RegionWhVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 总仓id，主键
     */
    private Long id;
    /**
     * 总仓编码
     */
    private String regionWhCode;
    /**
     * 总仓名称
     */
    private String regionWhName;
    /**
     * 超管代码[外键]
     */
    private String adminCode;
    /**
     * 超管名称[冗余]
     */
    private String adminName;
    /**
     * 状态，0为不可用；1为可用
     */
    private Integer status;
    /**
     * 是否可用状态变化时间
     */
    private Date statusTime;
    /**
     * 区域编码
     */
    private String areaCode;
    /**
     * 区域全称[冗余]
     */
    private String areaFullName;
    /**
     * 详细地址
     */
    private String address;
    /**
     * 经度
     */
    private String lng;
    /**
     * 纬度
     */
    private String lat;
    /**
     * 自动清库存时间；格式：HH:mm
     */
    private String clearWhTime;
    /**
     * 销售开始时间；格式：HH:mm
     */
    private String salesTimeStart;
    /**
     * 销售结束时间；格式：HH:mm
     */
    private String salesTimeEnd;
    /**
     * 提前销售结束时间；格式：HH:mm
     */
    private String earlySalesTimeEnd;
    /**
     * 发货结束时间；格式：HH:mm
     */
    private String deliveryTimeEnd;
    /**
     * 发货完成时间-自动履约补数(缺货、装车、发车)；格式：HH:mm
     */
    private String deliveryCompleteTime;
    /**
     * 直属城市仓列表
     */
    private List<AffiliatedCityWhBean> affiliatedCityWhList;
    /**
     * 关联部门id
     */
    private Long deptId;
    /**
     * 关联部门名称
     */
    private String deptName;
    /**
     * 备注
     */
    private String remark;
    /**
     * 修改人
     */
    private String updateName;
    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 基采物流数
     */
    private Integer marketNumber;
    /**
     * 促销日
     */
    private String promoDay;
    /**
     * 质检报告是否必传(0否 1是)
     */
    private Integer qualityReport;
    /**
     * 劣品等级标准：报损订单，报损金额，报损率
     */
    private String badLevel;
    /**
     * 云仓服务费单价；元/斤
     */
    private BigDecimal cwServiceFeePrice;
    /**
     * 是否试点按订单项送货(0否 1是)
     */
    private Integer isOrderItemDelivery;
    /**
     * 生效销售日期
     */
    private LocalDate effectSaleDate;

    /**
     * 类型, 1普通总仓, 0为地采
     */
    @DictConvertFiled(dictCode = "basicRegionWhType", filedName = "typeName" ,type = DictConvertTypeEnum.ENUM)
    private Integer type;

    /**
     * 是否需要坑位，0否1是
     */
    private Integer isSaleNum;
}
