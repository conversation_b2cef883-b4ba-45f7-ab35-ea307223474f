package cn.xianlink.basic.controller.platform;

import cn.hutool.core.collection.CollUtil;
import cn.xianlink.basic.api.RemoteRegionLogisticsService;
import cn.xianlink.basic.domain.vo.citywh.CityWhPlaceVo;
import cn.xianlink.basic.domain.vo.citywh.WhNexusVo;
import cn.xianlink.basic.domain.vo.regionwh.RegionWhVo;
import cn.xianlink.basic.domain.vo.regionwh.RegionWhVoApplet;
import cn.xianlink.basic.service.ICityWhPlaceService;
import cn.xianlink.basic.service.IRegionWhService;
import cn.xianlink.basic.service.IWhNexusService;
import cn.xianlink.common.api.util.SaleDateUtil;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.core.utils.MapstructUtils;
import cn.xianlink.common.core.utils.StringUtils;
import cn.xianlink.common.web.core.BaseController;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 总仓查询
 *
 * <AUTHOR>
 * @folder 集采查询总仓列表
 */
@Validated
@RefreshScope
@RequiredArgsConstructor
@RestController("platformWhiteRegionWhController")
@RequestMapping("/platform/white/regionWh")
public class WhiteRegionWhController extends BaseController {

    private final transient IRegionWhService regionWhService;
    private final transient IWhNexusService iWhNexusService;

    private final transient ICityWhPlaceService cityWhPlaceService;

    private final transient RemoteRegionLogisticsService remoteRegionLogisticsService;
    //禁止下单的总仓id集合
    @Value("${region.prohibitOrder:0}")
    private String prohibitOrderIds;

    /**
     * 查询-城市仓提货点的总仓列表（有吨位的排前面
     */
    @GetMapping(value = "/queryList")
    public R<List<RegionWhVoApplet>> queryList( @RequestParam(value = "cityWhId") Long cityWhId,
                                                @RequestParam("placeId") Long placeId) {

        List<Long> regionWhIds = iWhNexusService.customListByCityWhId(cityWhId).stream().map(WhNexusVo::getRegionWhId).collect(Collectors.toList());
        if (regionWhIds.isEmpty()) {
            return R.ok();
        }
        List<RegionWhVo> vos = regionWhService.selectVoBatchIds(regionWhIds);
        if(CollUtil.isEmpty(vos)){
            return R.ok();
        }
        List<RegionWhVoApplet> convert = MapstructUtils.convert(vos, RegionWhVoApplet.class);
        for(RegionWhVoApplet vo : convert) {
            if(StringUtils.isNotBlank(vo.getPromoDay())) {
                LocalDate saleDate = SaleDateUtil.getSaleDate(vo.getSalesTimeEnd());
                String day = String.valueOf(saleDate.getDayOfMonth());
                String[] promoDayList = vo.getPromoDay().split(",");
                for(String promoDay : promoDayList) {
                    if(day.equals(promoDay)) {
                        vo.setIsPromoDay(true);
                        break;
                    }
                }
            }
        }
        //获取提货点信息，如果有父级提货点，则取父级提货点
        CityWhPlaceVo cityWhPlaceVo = cityWhPlaceService.selectAndCheckNullById(placeId);
        Long parentPlaceId = Objects.isNull(cityWhPlaceVo.getParentPlaceId())? placeId :  cityWhPlaceVo.getParentPlaceId();

        // 都没有吨位 or 都有，则无需排序
        List<Long> hasLogisticsRegionWhIds = remoteRegionLogisticsService.checkForSalePage(parentPlaceId, regionWhIds);
        if(CollUtil.isEmpty(hasLogisticsRegionWhIds) || hasLogisticsRegionWhIds.size() == regionWhIds.size()){
            return R.ok(convert);
        }

        Collection<RegionWhVoApplet> top = convert.stream().filter(vo -> hasLogisticsRegionWhIds.contains(vo.getId())).sorted(Comparator.comparingLong(RegionWhVoApplet::getId)).toList();
        Collection<RegionWhVoApplet> tail = convert.stream().filter(vo -> !hasLogisticsRegionWhIds.contains(vo.getId())).sorted(Comparator.comparingLong(RegionWhVoApplet::getId)).toList();
        List<RegionWhVoApplet> result = new ArrayList<>(top);
        result.addAll(tail);
        return R.ok(result);
    }

}
