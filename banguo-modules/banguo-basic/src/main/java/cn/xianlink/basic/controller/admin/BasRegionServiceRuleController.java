package cn.xianlink.basic.controller.admin;

import cn.xianlink.basic.domain.bo.rule.BasRegionServiceRuleAddBo;
import cn.xianlink.basic.domain.bo.rule.BasRegionServiceRuleEditBo;
import cn.xianlink.basic.domain.bo.rule.BasRegionServiceRuleQueryBo;
import cn.xianlink.basic.domain.bo.rule.BasRuleFreightServiceRmQueryBo;
import cn.xianlink.basic.domain.vo.rule.BasRegionServiceDetailVo;
import cn.xianlink.basic.domain.vo.rule.BasRegionServiceRulePageVo;
import cn.xianlink.basic.service.IBasRegionServiceRuleService;
import cn.xianlink.common.api.util.SaleDateUtil;
import cn.xianlink.common.core.domain.R;
import cn.xianlink.common.idempotent.annotation.RepeatSubmit;
import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.common.web.core.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 免代活动
 * <AUTHOR>
 * @date 2024-03-21
 * @folder 般果管理中心/免代活动
 */
@Tag(name = "免代活动")
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/basRegionServiceRule")
public class BasRegionServiceRuleController extends BaseController {

    private final transient IBasRegionServiceRuleService basRegionServiceRuleService;

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    public R<TableDataInfo<BasRegionServiceRulePageVo>> page(@Validated @RequestBody BasRegionServiceRuleQueryBo queryBo) {
        queryBo.setCurrentDate(SaleDateUtil.getSaleDate("18:00"));
        return R.ok(basRegionServiceRuleService.customPageList(queryBo));
    }

    @Operation(summary = "新增单条")
    @RepeatSubmit()
    @PostMapping("/create")
    public R<Void> create(@Validated @RequestBody BasRegionServiceRuleAddBo addBo) {
        return toAjax(basRegionServiceRuleService.insertByBo(addBo));
    }

    @Operation(summary = "修改单条")
    @RepeatSubmit()
    @PostMapping("/update")
    public R<Void> update(@Validated @RequestBody BasRegionServiceRuleEditBo editBo) {
        return toAjax(basRegionServiceRuleService.updateByBo(editBo));
    }

    @Operation(summary = "删除单条")
    @PostMapping("/delete/{id}")
    public R<Void> delete(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return toAjax(basRegionServiceRuleService.deleteById(id, true));
    }

    /**
     * 生效规则查询
     * @ignore
     */
    @PostMapping("/querySkuServiceAmtMap")
    public R<Map<Long, BasRegionServiceDetailVo>> querySkuServiceAmtMap(@Validated @RequestBody BasRuleFreightServiceRmQueryBo bo) {
        return R.ok(basRegionServiceRuleService.querySkuServiceAmtMap(bo));
    }
}
