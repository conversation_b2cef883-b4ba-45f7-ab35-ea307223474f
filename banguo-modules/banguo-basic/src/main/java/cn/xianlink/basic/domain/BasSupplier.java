package cn.xianlink.basic.domain;

import cn.xianlink.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 供应商对象 bas_supplier
 *
 * <AUTHOR>
 * @date 2024-04-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("bas_supplier")
public class BasSupplier extends TenantEntity {

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 供应商编码
     */
    private String code;
    /**
     * 供应商简码
     */
    private String simpleCode;

    /**
     * 供应商名称
     */
    private String name;

    /**
     * 供应商别名
     */
    private String alias;

    /**
     * 超管代码
     */
    private String adminCode;

    /**
     * 向般果供货
     */
    private Integer supplyBg;

    /**
     * 向易良食供货
     */
    private Integer supplyYls;

    /**
     * 供应商状态，0禁用；1启用
     * @see cn.xianlink.common.core.enums.StatusEnum
     */
    private Integer status;

    /**
     * 供应商状态变化时间
     */
    private Date statusTime;

    /**
     * 申请时间
     */
    private Date applyTime;

    /**
     * 审核人姓名
     */
    private String auditor;

    /**
     * 审核人code
     */
    private String auditorCode;

    /**
     * 审核状态，0待审核；1审核通过；2审核不通过
     */
    private Integer auditStatus;

    /**
     * 审核备注
     */
    private String auditRemark;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 子账号
     */
    private String accountSub;

    /**
     * 开户状态: 0未开户；1已开户；2开户失败
     * @see cn.xianlink.common.api.enums.basic.OpenAccountEnum
     */
    private Integer accountStatus;

    /**
     * 开户状态说明
     */
    private String accountRemark;

    /**
     * 绑卡状态，0未绑卡；1已绑卡
     */
    private Integer bankStatus;

    /**
     * 银行卡预留手机号
     */
    private String bankMobile;

    /**
     * 银行卡
     */
    private String bankNo;

    /**
     * 分行行号
     */
    private String bankCode;
    /**
     * 分行名称
     */
    private String bankName;

    /**
     * 绑卡异常说明
     */
    private String bankRemark;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0代表存在 存入这条数据的主键代表删除）
     */
    private Long delFlag;

}
