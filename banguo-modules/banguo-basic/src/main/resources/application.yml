# Tomcat
server:
  port: 9206

nacos:
  server: 10.210.0.15:8848
  group: @nacos.discovery.group@
  seata:
    group: @nacos.seata.group@
local-active: @profiles.active@

# Spring
spring:
  application:
    # 应用名称
    name: banguo-basic
  profiles:
    # 环境配置
    active: ${SPRING_ACTIVE:${local-active}}

--- # nacos 配置
spring:
  cloud:
    nacos:
      # nacos 服务地址
      server-addr: ${NACOS_SERVER:${nacos.server}}
      discovery:
        # 注册组
        group: ${NACOS_GROUP:${nacos.group}}
        namespace: ${spring.profiles.active}
        username: ${NACOS_NAME:nacos}
        password: ${NACOS_PWD:nacos}
      config:
        # 配置组
        group: ${NACOS_GROUP:${nacos.group}}
        namespace: ${spring.profiles.active}
        username: ${NACOS_NAME:nacos}
        password: ${NACOS_PWD:nacos}
  config:
    import:
      - optional:nacos:application-common.yml
      - optional:nacos:datasource.yml
      - optional:nacos:${spring.application.name}.yml
