<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.basic.mapper.BasPubAreaMapper">
    <select id="customList" resultType="cn.xianlink.basic.domain.vo.BasPubAreaVo">
        SELECT area_code,area_name,area_alias,full_name,full_path,parent_code,level_num,is_leaf
        FROM bas_area ${ew.customSqlSegment}
    </select>
</mapper>
