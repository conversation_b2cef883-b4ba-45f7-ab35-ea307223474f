<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.basic.mapper.CustomerReportLossStatisticsMapper">

    <update id="zeroByCustomerIds">
        update customer_report_loss_statistics
        set amount = 0.0, order_amount = 0.0, amount_rate = 0.0, nums = 0, order_nums = 0, nums_rate = 0.0
        where customer_id in
        <foreach collection="customerIds" item="customerId" open="(" separator="," close=")">
            #{customerId}
        </foreach>
        and cycle = #{cycle}
    </update>

    <select id="getCopyRecord" resultType="cn.xianlink.basic.domain.CustomerReportLossStatistics">
        select * from customer_report_loss_statistics_copy
        where customer_id = #{customerId}
        and cycle = 3
    </select>
</mapper>
