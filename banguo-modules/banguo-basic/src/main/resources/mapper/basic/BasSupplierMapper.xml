<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.basic.mapper.BasSupplierMapper">
    <select id="selectPageByAdmin" resultType="cn.xianlink.basic.domain.vo.supplier.BasSupplierAdminListVo">
        select sup.id,
               sup.code,
               sup.simple_code,
               sup.name,
               sup.alias,
               sup.admin_code,
               sup.supply_bg,
               sup.supply_yls,
               sup.status,
               sup.status_time,
               sup.auditor,
               sup.audit_status,
               sup.audit_remark,
               sup.audit_time,
               sup.account_sub,
               sup.account_status,
               sup.account_remark,
               sup.bank_status,
               sup.bank_no,
               sup.bank_remark,
               sup.remark,
               sup.create_code,
               sup.create_name,
               sup.create_time,
               sup.update_code,
               sup.update_name,
               sup.update_time,
               info.enterprise_type,
               info.area_full_name,
               info.address,
               info.enterprise_credit_code
        from bas_supplier as sup
                 inner join bas_supplier_info as info on sup.id = info.supplier_id
        <where>
            <if test="bo.regionWhId != null">
                    sup.id in (select distinct region.supplier_id
                               from bas_supplier_region_wh as region
                               where region_wh_id = #{bo.regionWhId,jdbcType=BIGINT}
                                 and del_flag = 0
                                 and tenant_id = #{bo.tenantId,jdbcType=VARCHAR})
            </if>
            <if test="bo.code != null">
                <bind name="codelikevalue"
                      value="bo.code + '%'"/>
                and sup.code like
                    #{codelikevalue}
            </if>
            <if test="bo.name != null">
                <bind name="namelikevalue"
                      value="bo.name + '%'"/>
                and (sup.name like #{namelikevalue} or sup.alias like #{namelikevalue})
            </if>
            <if test="bo.adminCode != null">
                and sup.admin_code =
                    #{bo.adminCode,jdbcType=VARCHAR}
            </if>

            <if test="bo.status != null">
                and sup.status = #{bo.status,jdbcType=INTEGER}
            </if>
            <if test="bo.statusTimeStart != null">
                and sup.status_time >= #{bo.statusTimeStart,jdbcType=TIMESTAMP}
            </if>
            <if test="bo.statusTimeEnd != null">
                and sup.status_time &lt;= #{bo.statusTimeEnd,jdbcType=TIMESTAMP}
            </if>
            <if test="bo.accountSub != null">
                <bind name="accountSublikevalue"
                      value="bo.accountSub + '%'"/>
                and account_sub like
                    #{accountSublikevalue}
            </if>
            <if test="bo.bankNo != null">
                <bind name="bankNolikevalue"
                      value="bo.bankNo + '%'"/>
                and bank_no like
                    #{bankNolikevalue}
            </if>
            <if test="bo.areaCode != null">
                and info.area_code = #{bo.areaCode,jdbcType=VARCHAR}
            </if>
            <if test="bo.supplyBg != null">
                and sup.supply_bg = 1
            </if>
            <if test="bo.supplyYls != null">
                and sup.supply_yls = 1
            </if>
            <if test="bo.auditStatus != null">
                <choose>
                    <when test="bo.auditStatus == 3">
                        and sup.audit_status in (0,2)
                    </when>
                    <otherwise>
                        and sup.audit_status = #{bo.auditStatus,jdbcType=INTEGER}
                    </otherwise>
                </choose>
            </if>
            <if test="bo.auditorCode != null">
                and sup.auditor_code = #{bo.auditorCode,jdbcType=VARCHAR}
            </if>
            <if test="bo.auditTimeStart != null">
                and sup.audit_time >= #{bo.auditTimeStart,jdbcType=TIMESTAMP}
            </if>
            <if test="bo.auditTimeEnd != null">
                and sup.audit_time &lt;= #{bo.auditTimeEnd,jdbcType=TIMESTAMP}
            </if>
            <if test="bo.applyTimeStart != null">
                and sup.apply_time >= #{bo.applyTimeStart,jdbcType=TIMESTAMP}
            </if>
            <if test="bo.applyTimeEnd != null">
                and sup.apply_time &lt;= #{bo.applyTimeEnd,jdbcType=TIMESTAMP}
            </if>
            <if test="bo.accountStatus != null">
                and sup.account_status = #{bo.accountStatus,jdbcType=INTEGER}
            </if>
            <if test="bo.bankStatus != null">
                and sup.bank_status = #{bo.bankStatus,jdbcType=INTEGER}
            </if>
            <if test="bo.enterpriseCreditCode != null and bo.enterpriseCreditCode != ''">
                and info.enterprise_credit_code = #{bo.enterpriseCreditCode,jdbcType=VARCHAR}
            </if>
            <if test="bo.saleNumStatus != null">
                AND
                <if test="bo.saleNumStatus == 1">
                    EXISTS
                </if>
                <if test="bo.saleNumStatus == 0">
                    NOT EXISTS
                </if>
                (
                    select 1
                    from bas_supplier_region_wh sr
                    where sr.del_flag = 0
                    and sr.supplier_id = sup.id
                    and sr.max_sale_num > 0
                )
            </if>
            and del_flag = 0
        </where>
        order by sup.update_time desc
    </select>



    <select id="selectByNameAndStatus" resultType="cn.xianlink.basic.domain.vo.supplier.BasSupplierSimperVo">
        select id,code,name,alias,status,audit_status
        from bas_supplier
        <where>
            del_flag = 0
            <if test="name != null and name != ''">
                and (name like concat('%', #{name}, '%') or alias like concat('%', #{name}, '%'))
            </if>
            <if test="status != null">
                and status = #{status,jdbcType=INTEGER}
            </if>

        </where>
        limit 20
    </select>

    <update id="updateAccountSub">
        update bas_supplier
        set account_sub = #{accountSub,jdbcType=VARCHAR},
        account_status = 1,
        account_remark = ''
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="checkEnterprise" resultType="int">
        select count(1)
        from bas_supplier s
                 inner join bas_supplier_info info on s.id = info.supplier_id
        <where>
            <if test="supplierId != null">
                s.id != #{supplierId,jdbcType=BIGINT}
            </if>
            and s.del_flag = 0
            and s.account_status <![CDATA[<>]]>  3
            and info.enterprise_credit_code = #{enterpriseCreditCode,jdbcType=VARCHAR}
        </where>
    </select>

    <select id="dropdownBoxSearch" resultType="cn.xianlink.basic.domain.vo.supplier.BasSupplierSimperVo">
        select sup.id,sup.code,sup.name,sup.alias,sup.status,sup.audit_status
        from bas_supplier sup
        <if test="searchBo.regionWhId != null">
            inner join bas_supplier_region_wh region on sup.id = region.supplier_id and region.del_flag = 0
        </if>

        <where>
            <if test="searchBo.name != null and searchBo.name != ''">
                and (sup.name like concat('%', #{searchBo.name}, '%') or sup.alias like concat('%', #{searchBo.name}, '%'))
            </if>
            <if test="searchBo.status != null">
                and sup.status = #{searchBo.status,jdbcType=INTEGER}
            </if>
            <if test="searchBo.auditStatus != null">
                and sup.audit_status = #{searchBo.auditStatus,jdbcType=INTEGER}
            </if>
            <if test="searchBo.regionWhId != null">
                and region.region_wh_id = #{searchBo.regionWhId,jdbcType=BIGINT}
            </if>
            <if test="searchBo.openPingAn != null">
                <choose>
                    <when test="searchBo.openPingAn == 1">
                        and sup.account_sub is not null
                    </when>
                    <otherwise>
                        and sup.account_sub is null
                    </otherwise>
                </choose>
            </if>
            and sup.del_flag = 0
        </where>
        limit 20
    </select>

    <select id="dropdownBoxSearchByPage" resultType="cn.xianlink.basic.domain.vo.supplier.BasSupplierSimperVo">
        select sup.id,sup.code,sup.name,sup.alias,sup.status,sup.audit_status
        from bas_supplier sup
        <if test="searchBo.regionWhId != null">
            inner join bas_supplier_region_wh region on sup.id = region.supplier_id and region.del_flag = 0
        </if>
        <where>
            <if test="searchBo.name != null and searchBo.name != ''">
                and (sup.name like concat('%', #{searchBo.name}, '%') or sup.alias like concat('%',
                #{searchBo.name}, '%'))
            </if>
            <if test="searchBo.status != null">
                and sup.status = #{searchBo.status,jdbcType=INTEGER}
            </if>
            <if test="searchBo.auditStatus != null">
                and sup.audit_status = #{searchBo.auditStatus,jdbcType=INTEGER}
            </if>
            <if test="searchBo.regionWhId != null">
                and region.region_wh_id = #{searchBo.regionWhId,jdbcType=BIGINT}
            </if>
            <if test="searchBo.openPingAn != null">
                <choose>
                    <when test="searchBo.openPingAn == 1">
                        and sup.account_sub is not null
                    </when>
                    <otherwise>
                        and sup.account_sub is null
                    </otherwise>
                </choose>
            </if>
            and sup.del_flag = 0
        </where>
        order by sup.id
    </select>

        <select id="querySupplierDeliverPage" resultType="cn.xianlink.basic.domain.vo.supplier.QuerySupplierDeliverPageVo">
        select
            bs.id,
            bs.name,
            bs.alias
        from
            bas_supplier bs
        left join bas_supplier_region_wh rw on bs.id = rw.supplier_id
        <where>
            bs.del_flag = 0 and bs.status = 1 and bs.account_status = 1 and rw.del_flag = 0
            <if test="bo.regionWhId != null">
                and rw.region_wh_id = #{bo.regionWhId}
            </if>
            <if test="bo.supplierName != null and bo.supplierName != ''">
                and (bs.name like concat('%', #{bo.supplierName}, '%') or bs.alias like concat('%', #{bo.supplierName}, '%'))
            </if>
        </where>
        group by bs.id
    </select>

    <select id="selectSupIdByNewRegister" resultType="java.lang.Long">
        SELECT
            id
        FROM bas_supplier
        WHERE
            del_flag = 0
            AND STATUS = 1
            AND audit_status = 1
            AND audit_time >= DATE_SUB( CURRENT_DATE (), INTERVAL #{day} DAY )
        ORDER BY id DESC
    </select>

</mapper>
