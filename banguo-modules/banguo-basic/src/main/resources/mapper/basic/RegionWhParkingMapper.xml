<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.basic.mapper.RegionWhParkingMapper">
    <resultMap type="cn.xianlink.basic.domain.vo.regionwh.RegionWhParkingVo" id="RegionWhParkingResult"/>

    <select id="customPageList" resultMap="RegionWhParkingResult">
        select brwp.id,
               brwp.region_wh_id,
               brwp.parking_type,
               brwp.parking_no,
               brwp.fixed_parking_no,
               brwp.update_time,
               brwp.logistics_id,
               brwp.logistics_code,
               brwp.update_time,
               brl.logistics_name,
               brlp.plan_weight,
               brl.city_wh_id,
               bcw.city_wh_code,
               bcw.city_wh_name
        from bas_region_wh_parking brwp
                 left join bas_region_logistics brl on brwp.logistics_id = brl.id
                 left join bas_region_logistics_plan brlp on brl.id = brlp.logistics_id and brlp.plan_date = now()
                 inner join bas_city_wh bcw on brl.city_wh_id = bcw.id
            ${ew.getCustomSqlSegment}
    </select>

    <select id="customList" resultMap="RegionWhParkingResult">
        select brwp.id,
               brwp.parking_no,
               brwp.logistics_id,
               brl.logistics_name,
               brl.city_wh_id
        from bas_region_wh_parking brwp
                 inner join bas_region_logistics brl on brwp.logistics_id = brl.id
        where brwp.del_flag = 0
          and brwp.region_wh_id = #{regionWhId}
        order by brwp.parking_no asc
    </select>
</mapper>
