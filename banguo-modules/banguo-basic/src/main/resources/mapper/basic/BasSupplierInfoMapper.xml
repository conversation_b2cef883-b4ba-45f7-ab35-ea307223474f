<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.basic.mapper.BasSupplierInfoMapper">

    <select id="selectBySupplierId" resultType="cn.xianlink.basic.domain.BasSupplierInfo">
        select * from bas_supplier_info where supplier_id = #{supplierId,jdbcType=BIGINT}
    </select>

    <select id="selectIdBySupplierId" resultType="java.lang.Long">
        select id from bas_supplier_info where supplier_id = #{supplierId,jdbcType=BIGINT}
    </select>

    <update id="updateScooterCapacity">
        update bas_supplier_info set scooter_capacity = #{scooterCapacity,jdbcType=INTEGER} where supplier_id = #{supplierId,jdbcType=BIGINT}
    </update>
</mapper>
