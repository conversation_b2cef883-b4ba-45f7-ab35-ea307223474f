<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.basic.mapper.BasBankMapper">

    <select id="selectByBankName" resultType="cn.xianlink.basic.domain.vo.bank.BasBankVo">
        SELECT
            bank.bank_dreccode AS bankCode,
            bank.bank_lname AS bankName,
            bank.bank_bnkcode AS bankBnkCode
        FROM
            bas_bank bank
        <where>
            <if test="bankName != null and bankName != ''">
                bank_lname like concat('%', #{bankName}, '%')
            </if>
        </where>
        limit 20
    </select>

</mapper>
