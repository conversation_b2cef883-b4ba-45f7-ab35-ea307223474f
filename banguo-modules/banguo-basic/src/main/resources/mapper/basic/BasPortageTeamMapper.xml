<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.basic.mapper.BasPortageTeamMapper">

    <select id="customList" resultType="cn.xianlink.basic.domain.vo.freightlog.BasPortageTeamPageVo">
        SELECT a.id, a.region_wh_id, a.team_name, a.status, b.region_wh_code, b.region_wh_name,
        a.update_code, a.update_name, a.update_time
        FROM bas_portage_team a
        LEFT JOIN bas_region_wh b ON a.region_wh_id = b.id
        ${ew.customSqlSegment}
        ORDER BY a.id DESC
    </select>

</mapper>
