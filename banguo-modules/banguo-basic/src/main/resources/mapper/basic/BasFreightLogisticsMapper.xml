<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.basic.mapper.BasFreightLogisticsMapper">

    <select id="customList" resultType="cn.xianlink.basic.domain.vo.freightlog.BasFreightLogisticsVo">
        SELECT a.id, a.region_wh_id, a.provide_region_wh_id, a.freight_price, a.price_mode, a.status, a.remark,
        a.update_code, a.update_name, a.update_time, b.region_wh_code, b.region_wh_name,
        c.region_wh_code as provide_region_wh_code, c.region_wh_name as provide_region_wh_name
        FROM bas_freight_logistics a
        LEFT JOIN bas_region_wh b ON a.region_wh_id = b.id
        LEFT JOIN bas_region_wh c ON a.provide_region_wh_id = c.id
        ${ew.customSqlSegment}
    </select>

</mapper>
