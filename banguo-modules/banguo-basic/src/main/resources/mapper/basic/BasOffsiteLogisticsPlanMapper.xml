<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.basic.mapper.BasOffsiteLogisticsPlanMapper">
    <select id="customList" resultType="cn.xianlink.basic.domain.vo.offsitelog.BasOffsiteLogisticsPlanPageVo">
        SELECT a.id as offsite_id,a.market_id,a.region_wh_id,
        <foreach collection="dates" item="date" index="index" separator=",">
            coalesce(b.title${index}, '--') as title${index},coalesce(b.plan_weight${index}, 0) as plan_weight${index}
        </foreach>
        FROM bas_offsite_logistics a LEFT JOIN
            (
                SELECT offsite_id,
                <foreach collection="dates" item="date" index="index" separator=",">
                    max(case when plan_date=#{date} then concat(format(plan_weight/2000,2),' 吨 / ',
                    format(ceil(order_weight/20)/100,2),' 吨') else null end) as title${index},
                    max(case when plan_date=#{date} then plan_weight/2000 else null end) as plan_weight${index}
                </foreach>
                FROM bas_offsite_logistics_plan
                WHERE plan_date BETWEEN #{dates[0]} AND #{dates[${dates.size() - 1}]}
                <if test="qbo.updateCode != null and qbo.updateCode != ''">
                    AND update_code = #{qbo.updateCode}
                </if>
                <if test="qbo.updateTimeStart != null">
                    AND update_time &gt;= #{qbo.updateTimeStart}
                </if>
                <if test="qbo.updateTimeEnd != null">
                    AND update_time &lt;= #{qbo.updateTimeEnd}
                </if>
                GROUP BY offsite_id
            ) b ON a.id=b.offsite_id
        ${ew.customSqlSegment}
    </select>
    <update id="updateOrderWeight">
        update bas_offsite_logistics_plan
          set order_weight = case when (order_weight + #{bo.orderWeight}) > 0 then order_weight + #{bo.orderWeight} else 0 end
        where offsite_id = #{bo.offsiteId} and plan_date = #{bo.planDate} and del_flag = 0
    </update>
</mapper>
