<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.basic.mapper.WhProfitRuleMapper">

    <select id="countByCityWhIds" resultType="cn.xianlink.basic.domain.vo.profitrule.WhProfitRuleSumVo">
        SELECT bwpr.city_wh_id, count(0) as profit_rule_count
        FROM bas_wh_profit_rule bwpr
        WHERE bwpr.del_flag = 0 AND bwpr.status = 1
        AND bwpr.city_wh_id IN
        <foreach collection="cityWhIds" item="cityWhId" open="(" separator="," close=")">
            #{cityWhId}
        </foreach>
        GROUP BY bwpr.city_wh_id
    </select>
</mapper>
