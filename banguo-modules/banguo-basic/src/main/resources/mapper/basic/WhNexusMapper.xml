<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.basic.mapper.WhNexusMapper">
    <select id="customListByCityWhId" resultType="cn.xianlink.basic.domain.vo.citywh.WhNexusVo">
        select bwn.id,
               bwn.city_wh_id,
               bwn.city_wh_code,
               bwn.region_wh_id,
               bwn.region_wh_code,
               bwn.arrival_days,
               bwn.update_name,
               bwn.update_time,
               brw.region_wh_name
        from bas_wh_nexus bwn
                 inner join bas_region_wh brw on bwn.region_wh_id = brw.id and brw.del_flag = 0
        where bwn.city_wh_id = #{cityWhId} and bwn.del_flag = 0
    </select>

    <select id="fuzzyPage" resultType="cn.xianlink.basic.domain.vo.citywh.WhNexusVo">
        select bwn.city_wh_id,
               bwn.city_wh_code,
               bcw.city_wh_name
        from bas_wh_nexus bwn
                 inner join bas_city_wh bcw on bwn.city_wh_id = bcw.id and bwn.del_flag = bcw.del_flag
            ${ew.getCustomSqlSegment}
    </select>

    <select id="customCountByRegionWhIds" resultType="cn.xianlink.basic.domain.vo.citywh.WhNexusCountVo">
        select bwn.region_wh_id, count(bwn.id) as id_count
        from bas_wh_nexus bwn
        where bwn.del_flag = 0
          and bwn.region_wh_id IN
        <foreach collection="regionWhIds" item="regionWhId" index="i" open="(" separator="," close=")">
            #{regionWhId}
        </foreach>
        group by bwn.region_wh_id
    </select>

    <select id="selectAllRegionWhIds" resultType="Long">
        select distinct bwn.region_wh_id
        from bas_wh_nexus bwn
        where bwn.del_flag = 0
    </select>

    <select id="selectAllCityWhIds" resultType="Long">
        select distinct bwn.city_wh_id
        from bas_wh_nexus bwn
        where bwn.del_flag = 0
    </select>

    <select id="selectCityWh" resultType="cn.xianlink.basic.api.domain.vo.CityWhVoAppletVo">
        select bcw.id,
        bwn.city_wh_code,
        bcw.city_wh_name,
        bcw.area_full_name,
        bcw.address,
        bcw.status
        from bas_wh_nexus bwn
        left join bas_city_wh bcw on bwn.city_wh_id = bcw.id
        <where>
            bcw.del_flag = 0 and bwn.del_flag = 0 and bcw.status = 1
            <if test="cityWhName != null and cityWhName != ''">
                and bcw.city_wh_name like concat('%', #{cityWhName}, '%')
            </if>
            <if test="regionWhCode != null and regionWhCode != ''">
                and bwn.region_wh_code = #{regionWhCode}
            </if>
        </where>
    </select>
</mapper>
