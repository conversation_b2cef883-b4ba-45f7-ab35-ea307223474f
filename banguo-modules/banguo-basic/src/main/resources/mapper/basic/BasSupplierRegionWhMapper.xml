<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.basic.mapper.BasSupplierRegionWhMapper">

    <select id="selectBySupplierId" resultType="cn.xianlink.basic.domain.BasSupplierRegionWh">
        select * from bas_supplier_region_wh where supplier_id = #{supplierId,jdbcType=BIGINT}
        <if test="isAll != null and !isAll">
            and del_flag = 0
        </if>
    </select>

    <select id="selectBySupplierIds" resultType="cn.xianlink.basic.domain.BasSupplierRegionWh">
        select *
        from bas_supplier_region_wh where supplier_id in
        (
        <foreach collection="supplierIds" item="g" separator=",">
            #{g,jdbcType=BIGINT}
        </foreach>
        )
        <if test="isAll != null and !isAll">
            and del_flag = 0
        </if>
    </select>

    <update id="stopAllSupply">
        update bas_supplier_region_wh set del_flag = id  where supplier_id = #{supplierId,jdbcType=BIGINT}
    </update>

    <update id="deleteBySupply">
        update bas_supplier_region_wh set del_flag = id  where supplier_id = #{supplierId,jdbcType=BIGINT} and region_wh_id in (
        <foreach collection="regionIds" item="g" separator=",">
            #{g,jdbcType=BIGINT}
        </foreach>
        )
    </update>

    <update id="updateName">
        update bas_supplier_region_wh set region_wh_name = #{regionWh.regionWhName,jdbcType=VARCHAR}, del_flag = 0 where id = #{regionWh.id,jdbcType=BIGINT}
    </update>

    <select id="customPageList" resultType="cn.xianlink.basic.domain.vo.supplier.BasSupplierRegionWhApplyVo">
        select bsrw.id as apply_id,
               bsrw.supplier_id,
               bs.code as supplier_code,
               bs.name as supplier_name,
               bs.alias as supplier_alias,
               bs.remark,
               bsrw.region_wh_id,
               bsrw.audit_type,
               bsrw.audit_status,
               bsrw.audit_remark,
               bsrw.create_time,
               bsrw.update_time
        from bas_supplier_region_wh bsrw
                 inner join bas_supplier bs on bsrw.supplier_id = bs.id
            ${ew.getCustomSqlSegment}
    </select>

    <select id="selectIsSaleNum" resultType="cn.xianlink.basic.domain.BasSupplierRegionWh">
        select sr.supplier_id,sr.region_wh_id,sr.region_wh_name,sr.max_sale_num
        from bas_supplier_region_wh sr
        left join bas_region_wh reg on sr.region_wh_id = reg.id
        where sr.del_flag = 0
            and reg.del_flag = 0
            and reg.is_sale_num = 1
            and sr.supplier_id = #{supplierId,jdbcType=BIGINT}
    </select>
</mapper>
