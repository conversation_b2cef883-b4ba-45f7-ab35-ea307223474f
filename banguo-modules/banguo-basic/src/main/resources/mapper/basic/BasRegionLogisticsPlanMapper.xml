<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.basic.mapper.BasRegionLogisticsPlanMapper">
    <select id="customList" resultType="cn.xianlink.basic.domain.vo.regionlog.BasRegionLogisticsPlanVo">
        SELECT id,logistics_id,logistics_code,logistics_name,region_wh_id,region_wh_code,region_wh_name,
            city_wh_id,city_wh_code,city_wh_name,place_id,place_code,place_name,plan_date,
            round(plan_weight/2000,2) as plan_weight,round(ceil(order_weight/20)/100,2) as order_weight,
            case when round(plan_weight/2000,2) &lt;= ceil(order_weight/20)/100 then 0 else round(plan_weight/2000-ceil(order_weight/20)/100,2) end as residue_weight,
            remark,update_code,update_name,update_time
        FROM bas_region_logistics_plan ${ew.customSqlSegment}
    </select>
    <select id="queryPlaceLogisticsPlan" resultType="cn.xianlink.basic.domain.vo.regionlog.BasRegionLogisticsPlanVo">
        SELECT id,logistics_id,logistics_code,logistics_name,region_wh_id,region_wh_code,region_wh_name,
            city_wh_id,city_wh_code,city_wh_name,place_id,place_code,place_name,plan_date,
            round(plan_weight/2000,2) as plan_weight,round(ceil(order_weight/20)/100,2) as order_weight,
            case when round(plan_weight/2000,2) &lt;= ceil(order_weight/20)/100 then 0 else round(plan_weight/2000-ceil(order_weight/20)/100,2) end as residue_weight,
            remark,update_code,update_name,update_time
        FROM bas_region_logistics_plan
        WHERE del_flag = 0
        <if test="bo.placeId != null">
            AND place_id = #{bo.placeId}
        </if>
        <if test="bo.regionWhId != null">
            AND region_wh_id = #{bo.regionWhId}
        </if>
        <if test="bo.dates != null and bo.dates.size()>0">
            AND (region_wh_id,plan_date) in <foreach collection="bo.dates" item="date" open="(" separator="," close=")">(#{date.regionWhId},#{date.planDate})</foreach>
        </if>
    </select>
    <select id="queryPlaceResidueWeight" resultType="cn.xianlink.basic.domain.vo.regionlog.BasRegionLogisticsPlanResidueVo">
        SELECT region_wh_id,region_wh_name,plan_date,
        sum(case when round(plan_weight/2000,2) &lt;= ceil(order_weight/20)/100 then 0 else round(plan_weight/2000-ceil(order_weight/20)/100,2) end) as residue_weight
        FROM bas_region_logistics_plan
        WHERE del_flag = 0
        <if test="bo.placeId != null">
            AND place_id = #{bo.placeId}
        </if>
        <if test="bo.regionWhId != null">
            AND region_wh_id = #{bo.regionWhId}
        </if>
        <if test="bo.dates != null and bo.dates.size()>0">
            AND (region_wh_id,plan_date) in <foreach collection="bo.dates" item="date" open="(" separator="," close=")">(#{date.regionWhId},#{date.planDate})</foreach>
        </if>
        GROUP BY region_wh_id,region_wh_name,plan_date
    </select>

    <update id="updateOrderWeight">
        update bas_region_logistics_plan
          set order_weight = case when (order_weight + #{bo.orderWeight}) > 0 then order_weight + #{bo.orderWeight} else 0 end
            <if test="bo.planWeight != null ">
                ,plan_weight = case when (plan_weight + #{bo.planWeight}) > 0 then plan_weight + #{bo.planWeight} else 0 end
            </if>
        where logistics_id = #{bo.logisticsId} and plan_date = #{bo.planDate} and del_flag = 0
    </update>

    <select id="totalPlanWeightByLogisticsIds" resultType="java.math.BigDecimal">
        select coalesce(sum(plan_weight), 0 ) as totalPlanWeight
        from bas_region_logistics_plan
        where city_wh_id = #{cityWhId} and plan_date = #{saleDate} and del_flag = 0
    </select>

    <update id="cleanResidueWeight">
        update bas_region_logistics_plan
        set plan_weight = order_weight
        where plan_date = #{saleDate} and del_flag = 0
        and logistics_id IN
        <foreach collection="logisticsIds" item="logisticsId" index="i" open="(" separator="," close=")">
            #{logisticsId}
        </foreach>
    </update>

</mapper>
