<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.basic.mapper.CityWhPlaceMapper">
    <select id="countByCityWhIds" resultType="cn.xianlink.basic.domain.vo.citywh.CityWhPlaceSumVo">
        SELECT bcwp.city_wh_id, count(0) as place_count
        FROM bas_city_wh_place bcwp
        WHERE bcwp.del_flag = 0
        AND bcwp.city_wh_id IN
        <foreach collection="cityWhIds" item="cityWhId" open="(" separator="," close=")">
            #{cityWhId}
        </foreach>
    </select>

    <select id="countAllByCityWhId" resultType="java.lang.Integer">
        SELECT count(0)
        FROM bas_city_wh_place bcwp
        WHERE bcwp.city_wh_id = #{cityWhId}
    </select>
    <select id="countAllByParentPlaceId" resultType="java.lang.Integer">
        SELECT count(0)
        FROM bas_city_wh_place bcwp
        WHERE bcwp.parent_place_id = #{parentPlaceId}
    </select>

    <select id="queryNearPlaceListByLocation" resultType="cn.xianlink.basic.domain.vo.citywh.CityWhPlaceVo">
        SELECT bcwp.*,
               CAST(ST_Distance_Sphere(bcwp.location, ST_GeomFromText(CONCAT('POINT(', #{lat},' ', #{lng}, ')'), 4326)) AS UNSIGNED) AS distance
        FROM bas_city_wh_place bcwp
        WHERE bcwp.del_flag = 0
        ORDER BY distance LIMIT 10
    </select>

    <update id="updateLocationByIds">
        UPDATE bas_city_wh_place SET location = ST_GeomFromText(CONCAT('POINT(', lat,' ', lng, ')'), 4326)
        WHERE location is null
        <if test="ids != null and ids.size() > 0">
            OR id IN
            <foreach collection="ids" item="id" index="i" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </update>
</mapper>
