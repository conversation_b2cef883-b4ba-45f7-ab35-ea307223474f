<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.basic.mapper.CityWhMapper">
    <select id="customPageList" resultType="cn.xianlink.basic.domain.vo.citywh.CityWhVo">
        select bcw.id,
               bcw.city_wh_code,
               bcw.city_wh_name,
               bcw.city_wh_initial,
               bcw.city_wh_type_code,
               bcw.admin_code,
               bcw.status,
               bcw.status_time,
               bcw.area_code,
               bcw.area_full_name,
               bcw.address,
               bcw.lng,
               bcw.lat,
               bcw.is_show,
               bcw.sort,
               bcw.delivery_days,
               bcw.delivery_complete_rule,
               bcw.remark,
               bcw.update_name,
               bcw.update_time,
               count(brl.id)  logistics_count
        from bas_city_wh bcw
                 left join bas_region_logistics brl on bcw.id = brl.city_wh_id and brl.status = 1
            ${ew.getCustomSqlSegment}
        GROUP BY bcw.id
    </select>

    <select id="getCityByRegionWhId" resultType="cn.xianlink.basic.domain.vo.citywh.CityWhVo">
        select  bcw.*,bwb.arrival_days AS deliveryDays
        from bas_wh_nexus bwb
        left join  bas_city_wh bcw on bcw.city_wh_code = bwb.city_wh_code
        where bwb.region_wh_id = #{regionWhId}
          <if test="cityWhId != null">
              and bwb.city_wh_id = #{cityWhId}
          </if>
          and bwb.del_flag = 0 and bcw.del_flag = 0 and  bcw.status = 1
    </select>

    <update id="unbindingAdmin">
        update bas_city_wh set admin_code = '' where admin_code = #{adminCode} and del_flag = 0
    </update>
</mapper>
