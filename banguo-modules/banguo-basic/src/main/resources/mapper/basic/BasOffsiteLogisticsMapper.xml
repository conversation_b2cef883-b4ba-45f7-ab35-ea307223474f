<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.basic.mapper.BasOffsiteLogisticsMapper">
    <select id="customList" resultType="cn.xianlink.basic.domain.vo.offsitelog.BasOffsiteLogisticsVo">
        SELECT a.id, a.region_wh_id, a.market_id, a.arrival_days, a.freight_price, a.sale_start_time,
               a.sale_end_time, a.status, a.status_time, a.remark, a.update_code, a.update_name, a.update_time,
               b.region_wh_code, b.region_wh_name, c.market_code, c.market_name, c.area_full_name
        FROM bas_offsite_logistics a
         LEFT JOIN bas_region_wh b ON a.region_wh_id = b.id
         LEFT JOIN bas_offsite_market c ON a.market_id = c.id
         ${ew.customSqlSegment}
    </select>
    <select id="queryRegionWhMarketCount" resultType="cn.xianlink.basic.domain.vo.offsitelog.BasOffsiteLogisticsRegionSumVo">
        SELECT a.region_wh_id, count(*) as market_total
        FROM bas_offsite_logistics a
            inner join bas_region_wh b on a.region_wh_id = b.id and b.del_flag = 0 and b.status = 1
        WHERE a.del_flag = 0 and a.status = 1
        <if test="regionWhIds != null and regionWhIds.size > 0">
            and a.region_wh_id in
            <foreach collection="regionWhIds" item="item" open="(" separator="," close=")">#{item}</foreach>
        </if>
        GROUP BY a.region_wh_id
    </select>

</mapper>
