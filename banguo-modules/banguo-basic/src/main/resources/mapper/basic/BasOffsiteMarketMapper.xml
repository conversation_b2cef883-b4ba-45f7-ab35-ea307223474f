<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.basic.mapper.BasOffsiteMarketMapper">
    <select id="queryFuzzyCodeOrName" resultType="cn.xianlink.basic.domain.vo.BasPubVo">
        SELECT market_code as code,market_name as name
        FROM bas_offsite_market
        WHERE del_flag = 0 AND ${ew.sqlSegment} ORDER BY id
    </select>
    <select id="queryOffsiteMaxMarketCode" resultType="cn.xianlink.basic.domain.vo.offsitelog.BasOffsiteMarketVo">
        SELECT count(*)+1 as id,max(market_code) as market_code
        FROM bas_offsite_market
    </select>
</mapper>
