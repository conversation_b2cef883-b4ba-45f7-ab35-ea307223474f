<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.basic.mapper.WhProfitRuleRecordMapper">
    <select id="queryLastUpdateList" resultType="cn.xianlink.basic.api.domain.vo.RemoteWhProfitRuleVo">

        SELECT *
        FROM (
             SELECT
                 prr.*,
                 pr.rule_type as ruleType,
                 ROW_NUMBER() OVER (PARTITION BY pr.rule_type,pr.city_wh_id,pr.region_wh_id ORDER BY prr.id DESC) AS rn
             FROM
                 bas_wh_profit_rule pr
                     LEFT JOIN
                 bas_wh_profit_rule_record prr ON prr.rule_id = pr.id
             WHERE
                pr.`status` = 1
                <if test="bo.cityWhId != null ">
                    and pr.city_wh_id = #{bo.cityWhId}
                </if>
                <if test="bo.regionWhId != null ">
                    AND pr.region_wh_id = #{bo.regionWhId}
                </if>
                <if test="bo.createTimeStart != null and bo.createTimeEnd != null">
                    and prr.create_time > #{bo.createTimeStart} and prr.create_time &lt;= #{bo.createTimeEnd}
                </if>
                and prr.del_flag = 0 and pr.del_flag = 0
                group by pr.rule_type,pr.city_wh_id,pr.region_wh_id
             ) t
        WHERE rn = 1

    </select>
</mapper>
