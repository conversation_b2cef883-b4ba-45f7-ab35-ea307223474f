<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.basic.mapper.BasCustomerMapper">

    <select id="customPageList" resultType="cn.xianlink.basic.domain.vo.customer.BasCustomerVo">
        select bc.*,
               bcw.city_wh_name,
               bcwp.place_name as last_order_place_name,
               crls.amount as threeLossAmount,
               crls.order_amount as threeOrderAmount,
               crls.amount_rate as threeLossAmountProportion,
               crls.nums as threeLossGoodsTimes,
               crls.order_nums as threeOrderGoodsTimes,
               crls.nums_rate as threeLossGoodsProportion
        from bas_customer bc
                 left join bas_city_wh bcw on bc.city_wh_id = bcw.id
                 left join bas_city_wh_place bcwp on bc.last_order_place_id = bcwp.id
                 left join customer_report_loss_statistics crls on bc.id = crls.customer_id and crls.cycle = 3
            ${ew.getCustomSqlSegment}
        ORDER BY bc.id desc
    </select>

    <select id="customPageList_count" resultType="java.lang.Integer">
        SELECT count(*) as total FROM bas_customer bc ${ew.getCustomSqlSegment}
    </select>

    <select id="selectVoByCode" resultType="cn.xianlink.basic.domain.vo.customer.BasCustomerVo">
        select * from bas_customer where code = #{code} and del_flag = 0 limit 1
    </select>

    <update id="batchEditAfterSale">
        update bas_customer
        set after_sale_status = #{afterSaleStatus,jdbcType=INTEGER}, after_sale_type = #{afterSaleType,jdbcType=INTEGER},
        update_after_sale_time = now()
        where id in
        <foreach collection="customerIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="getByName" resultType="long">
        select id from bas_customer where del_flag = 0
        and
        (name like concat(#{name}, '%') or alias like concat(#{name}, '%'))
    </select>

    <select id="countCustomer" resultType="long">
        select count(1) from bas_customer
        <where>
            del_flag = 0 and city_wh_id=#{cityWhId}
            <if test="salesmans != null and salesmans.size()>0">
                and salesman_code in
                <foreach collection="salesmans" item="salesmanCode" open="(" close=")" separator=",">
                    #{salesmanCode}
                </foreach>
            </if>
            <if test="noSalesman != null and noSalesman !=''">
                and salesman_code is null
            </if>
        </where>
    </select>

    <select id="filterSalesmanCustomer" resultType="long">
        select id from bas_customer
        <where>
            del_flag = 0
            <if test="salesmans != null and salesmans.size()>0">
                and salesman_code in
                <foreach collection="salesmans" item="salesmanCode" open="(" close=")" separator=",">
                    #{salesmanCode}
                </foreach>
            </if>
            and id in
            <foreach collection="customers" item="customer" open="(" separator="," close=")">
                #{customer}
            </foreach>
            <if test="noSalesman != null and noSalesman !=''">
                and salesman_code is null
            </if>
        </where>
    </select>

    <select id="filterNoSalesmanCustomer" resultType="long">
        select id from bas_customer
        <where>
            del_flag = 0 and  salesman_code is null and
            id in
            <foreach collection="customers" item="customer" open="(" separator="," close=")">
                #{customer}
            </foreach>
        </where>
    </select>

    <select id="miniPageList" resultType="cn.xianlink.basic.domain.vo.customer.BasCustomerVo">
        select * from bas_customer
        <if test="useIndex != null and useIndex != ''">
            use index(idx_city_wh_id)
        </if>
        ${ew.getCustomSqlSegment}
        order by last_order_time desc
    </select>

    <select id="getCustomerCountListByCityWhIds" resultType="cn.xianlink.basic.domain.vo.customer.BasCustomerCountVo">
        select city_wh_id, count(0) as customer_count
        from bas_customer
        where del_flag = 0
          and city_wh_id in
        <foreach collection="cityWhIds" item="cityWhId" open="(" separator="," close=")">
            #{cityWhId}
        </foreach>
          and create_time > concat(#{createDate}, ' 00:00:00') and create_time &lt; concat(#{createDate}, ' 23:59:59')
          and city_wh_id is not null
        group by city_wh_id
    </select>
</mapper>