<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.basic.mapper.CityWhUserPlaceMapper">

    <select id="checkUserBindAllPlaces" resultType="java.lang.Long">
        select user_id
        from bas_city_wh_user_place
        <where>
            <if test="userIds != null and userIds.size() > 0">
                and user_id in
                <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                    #{userId}
                </foreach>
            </if>
            <if test="placeIds != null and placeIds.size() > 0">
                and place_id in
                <foreach collection="placeIds" item="placeId" open="(" separator="," close=")">
                    #{placeId}
                </foreach>
            </if>
            group by user_id
            having count(distinct place_id) >= #{placeCount}
        </where>
    </select>
</mapper>