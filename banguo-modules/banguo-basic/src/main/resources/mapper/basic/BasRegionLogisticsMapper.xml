<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xianlink.basic.mapper.BasRegionLogisticsMapper">
    <sql id="customListSql">
        SELECT a.id, a.logistics_code, a.logistics_name, a.region_wh_id, a.place_id, a.freight_price,
        a.city_freight_price, a.price_mode, a.transport_mode, a.is_weight_check, a.is_display, a.display_customers,
        a.is_display_weight, a.status, a.status_time, a.remark, a.update_code, a.update_name, a.update_time,
        b.region_wh_code,b.region_wh_name, c.city_wh_id, c.city_wh_code, c.city_wh_name, c.place_code, c.place_name,
        a.logistics_type,a.parent_place_id,d.place_code as parent_place_code, d.place_name as parent_place_name,
        a.sales_time_end, a.is_early_end, a.current_sale_date, a.current_is_early_end,
        b.clear_wh_time as region_sales_time_end, b.early_sales_time_end as region_early_sales_time_end
        FROM bas_region_logistics a
        LEFT JOIN bas_region_wh b ON a.region_wh_id = b.id
        LEFT JOIN bas_city_wh_place c ON a.place_id = c.id
        LEFT JOIN bas_city_wh_place d ON a.parent_place_id = c.id
    </sql>
    <resultMap id="customListResultMap" type="cn.xianlink.basic.domain.vo.regionlog.BasRegionLogisticsVo">
        <result property="displayCustomers" column="display_customers"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler" />
    </resultMap>
    <select id="customList" resultMap="customListResultMap">
        <include refid="customListSql"/>
        ${ew.customSqlSegment}
    </select>
    <select id="queryRegionMaxLogisticsCode" resultType="cn.xianlink.basic.domain.vo.regionlog.BasRegionLogisticsVo">
        SELECT count(*)+1 as id,max(logistics_code) as logistics_code
        FROM bas_region_logistics ${ew.customSqlSegment}
    </select>

    <select id="queryParkRegionLogistics" resultType="cn.xianlink.basic.domain.vo.regionlog.BasRegionLogisticsVo">
        SELECT a.id, a.logistics_code, a.logistics_name, c.city_wh_id, c.city_wh_code, c.city_wh_name
        FROM bas_region_logistics a
        LEFT JOIN bas_city_wh_place c ON a.place_id = c.id
        WHERE a.logistics_type = 0 and a.id not in (SELECT d.logistics_id FROM bas_region_wh_parking d
            WHERE d.region_wh_id = #{qbo.regionWhId} AND d.del_flag = 0 and d.logistics_id is not null
            <if test="qbo.parkingId != null">
                AND d.id != #{qbo.parkingId}
            </if>
        ) AND a.region_wh_id = #{qbo.regionWhId} AND a.del_flag = 0 AND a.status = 1
        <if test="qbo.cityWhId != null">
            AND a.city_wh_id = #{qbo.cityWhId}
        </if>
        ORDER BY a.id ASC
    </select>

    <update id="updateByPrice">
        update bas_region_logistics set logistics_name = #{bo.logisticsName}, price_mode = #{bo.priceMode}, is_display_weight = #{bo.isDisplayWeight},
        transport_mode = #{bo.transportMode}, is_weight_check = case when logistics_type = 1 then 0 else #{bo.isWeightCheck} end, is_display = #{bo.isDisplay},
        city_freight_price = case when city_freight_price = 0 or city_freight_price = freight_price or city_freight_price > #{bo.freightPrice}
        then #{bo.freightPrice} else city_freight_price end, freight_price = #{bo.freightPrice}, remark = #{bo.remark},
        display_customers = #{bo.displayCustomers,jdbcType=VARCHAR,typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler}
        where id = #{bo.id,jdbcType=BIGINT}
    </update>

    <update id="updateCurrentIsEarlyEnd">
        update bas_region_logistics brl
            set brl.current_sale_date = #{localDate},
                brl.current_is_early_end = brl.is_early_end
        where brl.del_flag = 0
          and brl.logistics_type = 0
          and brl.region_wh_id = #{regionWhId}
    </update>

</mapper>
