package cn.xianlink.product.api;

import cn.xianlink.common.api.bo.RemoteRegionWhMsgBo;
import cn.xianlink.common.api.vo.RemoteSupplierSkuFileVo;
import cn.xianlink.product.api.domain.bo.*;
import cn.xianlink.product.api.domain.vo.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 供应商商品服务
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
public interface RemoteSupplierSkuService {

    /**
     * 查询供应商批次商品信息
     * @param bo
     * @return
     */
    List<RemoteSupplierSkuInfoVo> queryInfoList(RemoteQueryInfoListBo bo);

    List<RemoteSupplierSkuInfoVo> querySimpleInfoListHasStock(List<Long> supplierSkuIdList);

    /**
     * 获取批次单表的信息 （无库存图片等
     * @param supplierSkuIdList
     * @return
     */
    List<RemoteSupplierSkuInfoVo> querySimpleInfoList(List<Long> supplierSkuIdList);

    List<RemoteSupplierSkuInfoVo> queryDeliveryAuditList(RemoteDeliveryAuditBo bo);



    /**
     * 根据条件查询销售批次id
     * @param bo
     * @return
     */
    List<Long> querySkuIdList(RemoteQuerySkuIdBo bo);

    /**
     * 资金账户场景下的查询sku信息
     * <AUTHOR> on 2024/6/29:10:52
     * @param supplierSkuIds
     * @return java.util.List<RemoteSupplierSkuFundsInfoVo>
     */
    List<RemoteSupplierSkuFundsInfoVo> queryInfoListByFunds(List<Long> supplierSkuIds);

    /**
     * 根据code查询供应商批次商品
     */
    RemoteSupplierSkuInfoVo getByCode(String code);

    /**
     * 根据商品条码获取批次
     *
     * @param bo@see TemCode#getOriginalCode(String) 替代这个方法的 getByCode
     * @return
     */
    RemoteSupplierSkuInfoVo getByLabel(RemoteQuerySkuIdBo bo);

    List<RemoteSupplierSkuInfoVo> getByLabelList(RemoteQuerySkuIdBo bo);


    /**
     * 根据id查询供应商批次商品【很重慎用】
     */
    RemoteSupplierSkuInfoVo getById(Long id);

    /**
     * 根据id查询供应商批次商品
     */
    RemoteSupplierSkuInfoVo getSimpleById(Long id);

    /**
     * 城市仓多货，生成地采批次
     * @param bo
     * @return
     */
    Long addLand(RemoteAddLandBo bo);

    /**
     * 批量更新库存
     *
     * @param stockList
     */
    List<RemoteUpdateStockBo>  batchUpdateStock(List<RemoteUpdateStockBo> stockList,String type);

    /**
     * 获取库存信息
     * @param supplierSkuIds
     * @return
     */
    List<RemoteSupplierSkuStockVo> listSupplierSkuStockBySupplierIds(List<Long> supplierSkuIds);

    /**
     * 根据商品批次查询禁用的城市仓id
     * @param skuIds
     * @return
     */
    List<RemoteSupplierDisableCityWhVo> listBySupplierSkuIds(List<Long> skuIds);

    /**
     * 查询需要差额退款的供应商商品销售批次
     * @return
     */
    List<RemoteRefundDifferenceSkuVo> queryRefundDifferenceSku(LocalDate saleDate, List<Long> supplierSkuIdList);

    /**
     * 根据供应商商品code查询供应商批次商品(最近15天)
     */
    List<RemoteSupplierSkuInfoVo> getBySpuCode(String code);

    /**
     * 根据总仓id查询供应商今日供货商品数量
     * @param bo
     * @return
     */
    List<RemoteQuerySupplierDeliverVo> querySupplierDeliver(RemoteQuerySupplierDeliverBo bo);

    /**
     * 查询销售批次商品文件
     */
    List<RemoteSupplierSkuFileVo> querySupplierSkuFile(RemoteQuerySupplierSkuFileBo bo);

    /**
     * 清除库存发送消息
     * @param bo
     */
    void sendClearStockMsg(RemoteRegionWhMsgBo bo);

    /**
     * 下架商品发送消息
     * @param bo
     */
    void sendDownSkuMsg(RemoteRegionWhMsgBo bo);

    /**
     * 判断总仓id，是否要查询旧系统数据源
     */
    boolean isQueryTiaoguoshi(Long regionWhId);

    /**
     * 是否存在子供应商已上架过的商品（状态为 4,5 - 上架，下架）
     * @param supplierDeptId 子供应商id
     * @return
     */
    boolean hasSupplierDeptSaleSku(Long supplierDeptId);

    /**
     * 获取具有同一唯一性的批次
     *
     * @param bo
     * @return
     */
    List<Long> querySupplierSkuIdGroup(RemoteQuerySkuIdsBo bo);

    /**
     * 查询最新的批次id
     * @param skuIds
     * @return
     */
    List<Long> queryNewSupplierSkuIds(List<Long> skuIds);

    /**
     * 根据sku获取商品详情
     * @param ids
     * @return
     */
    List<RemoteSupplierSkuInfoVo> querySkuIds(List<Long> ids);

    /**
     * 实时获取批次的库存信息（含云仓）
     * @param supplierSkuIds
     * @return
     */
    List<RemoteSupplierSkuStockVo> queryYunStock(List<Long> supplierSkuIds);

    /**
     * 根据sku获取商品组合名称
     * <AUTHOR> on 2025/1/17:11:48
     * @param ids
     * @return java.util.Map<java.lang.Long,java.lang.String>
     */
    Map<Long, String> getSkuCombinationName(List<Long> ids);

    /**
     * 根据sku获取商品组合名称，产地用的是产地简称
     * @param ids
     * @return
     */
    Map<Long, String> getSkuProductName(List<Long> ids);

    /**
     * 模糊搜索商品名称和规格
     *
     * @param fullName
     * @param filterFields
     * @return
     */
    List<Long> getIdsByFullName(LocalDate saleDate, String fullName, List<String> filterFields);

    /**
     * 订单回滚补偿-回退库存
     * @param orderCode 订单号
     * @param cancelStock 是否回退上架库存
     */
    void compensateStock(String orderCode, boolean cancelStock);

    Long selectUpSkuCount(LocalDate saleDate, Long regionWhId, String salesTimeEnd, List<String> buyerList);

    /**
     * 获取商品信息
     * @param bo
     * @return
     */
    List<RemoteSupplierSkuInfoVo> querySkuInfoList(RemoteQueryInfoBo bo);

    /**
     * 限时折扣批量降价商品 --总仓维度
     */
    Integer batchUpdateDownPrice(List<RemoteDiscountSkuSearchBo> searchBo);

    /**
     * 查询活动需要的商品信息
     */
    List<RemoteSkuInfoForActivityVo> queryActivityInfoList(List<Long> skuIds);
}
