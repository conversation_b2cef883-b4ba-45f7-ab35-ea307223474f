package cn.xianlink.product.api.util;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * @author: Pei
 * Date: 2024/10/29 下午2:27
 */
public class SupplierSkuUtils {


    /**
     * 规格转换
     * @param spuStandards
     * @return
     */
    public static String parseSkuStandardsValuesName(String spuStandards) {
        if (StringUtils.isNotBlank(spuStandards)) {
            if (spuStandards.contains("|")) {
                // 支持多规格
                String[] split = spuStandards.split("\\|");
                return Arrays.stream(split).filter(s -> StringUtils.isNotBlank(s) && s.contains(":"))
                        .map(s -> s.split(":")[1])
                        .collect(Collectors.joining(" "));
            } else {
                // 和单规格
                return spuStandards;
            }
        }
        return "";
    }
}
