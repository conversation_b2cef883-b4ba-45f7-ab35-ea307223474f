package cn.xianlink.product.api.domain.bo;

import cn.xianlink.common.mybatis.core.domain.BaseEntity;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * 活动匹配入参
 * <AUTHOR>
 */
@Data
public class RemoteDiscountSkuSearchBo implements Serializable {

    /**
     * 物流线
     */
    List<Long> logisticsIds;

    /**
     * 总仓
     */
    Long regionId;

    /**
     * 平台商品ID
     */
    private List<Long> spuIds = new ArrayList<>();

    /**
     * 平台商品ID-取反
     */
    private List<Long> rollSpuIds = new ArrayList<>();

    /**
     * 商品分类id
     */
    private List<Long> categoryIds = new ArrayList<>();

    private List<Long> skuIds = new ArrayList<>();

    private List<Long> rollSkuIds = new ArrayList<>();

    /**
     * 商品分类id-取反
     */
    private List<Long> rollCategoryIds = new ArrayList<>();

    /**
     * 销量
     */
    private Integer copySold;

    /**
     * 折扣率
     */
    private BigDecimal rate;

    /**
     * 优惠上限
     */
    private BigDecimal maxDiscountAmount;

    private LocalDate saleDate;

    /**
     * 销售类型：1为般果代采，2为商家自营
     */
    private Integer saleType;

}
