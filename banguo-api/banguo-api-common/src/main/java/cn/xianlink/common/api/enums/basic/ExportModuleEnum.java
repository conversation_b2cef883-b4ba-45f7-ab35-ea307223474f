package cn.xianlink.common.api.enums.basic;

import cn.xianlink.common.core.domain.vo.CoreEnumVo;
import cn.xianlink.common.core.enums.CoreEnumRow;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 导出模块
 * <AUTHOR> xiaodaibing on 2025-01-08 10:40
 */
@Getter
@AllArgsConstructor
public enum ExportModuleEnum implements CoreEnumRow {
    //导出订单明细
    ORDER_ITEM("order_item", "导出订单明细", ExportServiceEnum.ORDER_SERVICE),

    DEDUCTION_RECORD("deduction_record", "导出供应商加扣款记录", ExportServiceEnum.ORDER_SERVICE),
    //bi导出订单
    BI_ORDER("bi_order", "导出订单", ExportServiceEnum.BI_ORDER_SERVICE),
    //导出订单明细
    BI_ORDER_ITEM("bi_order_item", "导出订单明细", ExportServiceEnum.BI_ORDER_SERVICE),
    //导出预订单
    BI_ADVANCE_ORDER_ITEM("bi_advance_order_item", "预订单明细导出", ExportServiceEnum.BI_ORDER_SERVICE),
    //导出退款记录
    REFUND_RECORD("refund_record", "导出退款记录", ExportServiceEnum.ORDER_SERVICE),
    //bi导出退款记录
    BI_REFUND("bi_refund_record", "导出退款记录", ExportServiceEnum.BI_ORDER_SERVICE),
    //导出总仓分货单
    BI_ENTRUCK_RECORD("bi_entruck_record", "导出分货数据", ExportServiceEnum.BI_ORDER_SERVICE),
    //导出缺货数据
    BI_STOCKOUT_RECORD("bi_stockout_record", "导出缺货数据", ExportServiceEnum.BI_ORDER_SERVICE),
    BI_REFUND_DETAIL_RECORD ("bi_refund_detail_record", "导出退款明细", ExportServiceEnum.BI_ORDER_SERVICE),
    BI_BUYER_RECORD ("bi_buyer_record", "导出采购数据", ExportServiceEnum.BI_ORDER_SERVICE),
    BI_BUYER_COUNT_RECORD ("bi_buyer_count_record", "导出采购数量数据", ExportServiceEnum.BI_ORDER_SERVICE),
    BI_ORDER_RECORD ("bi_order_record", "导出订单数据", ExportServiceEnum.BI_ORDER_SERVICE),
    BI_UN_NORMAL_RECORD ("bi_unnormal_record", "导出异常数据", ExportServiceEnum.BI_ORDER_SERVICE),
    BI_INTO_STORAGE_RECORD ("bi_into_storage_record", "导出入库数据", ExportServiceEnum.BI_ORDER_SERVICE),
    BI_INTERVENE_RECORD ("bi_intervene_record", "导出客服介入数据", ExportServiceEnum.BI_ORDER_SERVICE),
    //导出财务报表-订单详情
    BI_FINANCE_ORDER_DETAIL_RECORD ("bi_finance_order_detail_record", "导出财务报表-订单详情", ExportServiceEnum.BI_ORDER_SERVICE),
    //导出财务报表-供应商对账
    BI_FINANCE_SUPPLIER_RECORD ("bi_finance_supplier_record", "导出财务报表-供应商对账", ExportServiceEnum.BI_ORDER_SERVICE),
    ;
    private final String code;
    private final String desc;
    private final ExportServiceEnum service;

    @Override
    public CoreEnumVo getEnumRow() {
        return new CoreEnumVo(this.code, this.desc);
    }
}
