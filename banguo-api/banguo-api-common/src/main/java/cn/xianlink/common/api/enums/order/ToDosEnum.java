package cn.xianlink.common.api.enums.order;

import cn.xianlink.common.core.domain.vo.CoreEnumVo;
import cn.xianlink.common.core.enums.CoreEnumRow;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 待办看板类型枚举
 */
@Getter
@AllArgsConstructor
public enum ToDosEnum implements CoreEnumRow {
    AUDIT_DELIVERY(1, "送货待审核"),
    Arrival_STORAGE(82, "到货入库"),

    WAIT_DELIVERY(5, "待送货"),
    EARLY_WAIT_DELIVERY(7, "提前发车"),
    WAIT_INSPECT(10, "待质检"),
    WAIT_ENTRUCK(20, "待装车"),

    WAIT_PICKING_PRINT(6, "待分拣"),
    WAIT_PICKING_ENTRUCK(21, "拣货列表"),
    RW_WAIT_PICKING_ENTRUCK(22, "库房待拣货"),

    WAIT_CREATE_ENTRUCK_NO(30, "未生成发车单"),
    AUDIT_STOCKOUT(31, "缺货待确认"),
    WAIT_DEPART(40, "待发车"),
    WAIT_PICKUP(50, "待接车"),


    WAIT_SORT_GOODS(60, "待分货"),
    SORT_GOODS_IN(61, "分货中"),
    WAIT_FINISH_SORT_GOODS(62, "待完成分货"),
    AUDIT_LESS_GOODS(63, "少货待确认"),
    LESS_GOODS_BLAME(64, "少货待判责"),

    AUDIT_REPORT_LOSS(73, "报损待审核"),
    AUDIT_GOODS_DISABLE_CITY_WH(81, "屏蔽商品"),

    AUDIT_SUBSIDY(90, "补贴待审核"),

    WAIT_CREATE_INVOICE(80, "待开发票"),
    ;

    private Integer code;
    private String desc;

    @Override
    public CoreEnumVo getEnumRow() {
        return new CoreEnumVo(String.valueOf(this.code), this.desc);
    }
}
