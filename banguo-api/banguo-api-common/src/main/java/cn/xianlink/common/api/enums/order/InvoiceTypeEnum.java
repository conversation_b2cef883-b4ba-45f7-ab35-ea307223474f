package cn.xianlink.common.api.enums.order;

import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.common.core.domain.vo.CoreEnumVo;
import cn.xianlink.common.core.enums.CoreEnumRow;
import lombok.Getter;

/**
 * 发票类型枚举
 * <AUTHOR>
 */
@Getter
public enum InvoiceTypeEnum implements CoreEnumRow {
    /**
     * 发票类型 (normal普票, special专票)
     */
    NORMAL("normal", "普票"),
    SPECIAL("special", "专票");

    ;

    private final String code;
    private final String desc;

    InvoiceTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static InvoiceTypeEnum loadByDesc(String desc) {
        for (InvoiceTypeEnum value : values()) {
            if (value.getDesc().equals(desc)) {
                return value;
            }
        }
        return null;
    }

    public static InvoiceTypeEnum loadByCode(Integer code) {
        for (InvoiceTypeEnum value : values()) {
            if (ObjectUtil.equal(value.getCode(), code)) {
                return value;
            }
        }
        return null;
    }

    @Override
    public CoreEnumVo getEnumRow() {
        return new CoreEnumVo(this.code, this.desc);
    }
}
