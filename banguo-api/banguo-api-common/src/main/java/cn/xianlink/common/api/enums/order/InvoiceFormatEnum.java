package cn.xianlink.common.api.enums.order;

import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.common.core.domain.vo.CoreEnumVo;
import cn.xianlink.common.core.enums.CoreEnumRow;
import lombok.Getter;

/**
 * 发票格式枚举
 * <AUTHOR>
 */
@Getter
public enum InvoiceFormatEnum implements CoreEnumRow {

    ELECTRONIC("electronic", "电子发票"),
    PAPER("paper", "纸质发票");

    ;

    private final String code;
    private final String desc;

    InvoiceFormatEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static InvoiceFormatEnum loadByDesc(String desc) {
        for (InvoiceFormatEnum value : values()) {
            if (value.getDesc().equals(desc)) {
                return value;
            }
        }
        return null;
    }

    public static InvoiceFormatEnum loadByCode(Integer code) {
        for (InvoiceFormatEnum value : values()) {
            if (ObjectUtil.equal(value.getCode(), code)) {
                return value;
            }
        }
        return null;
    }

    @Override
    public CoreEnumVo getEnumRow() {
        return new CoreEnumVo(this.code, this.desc);
    }
}
