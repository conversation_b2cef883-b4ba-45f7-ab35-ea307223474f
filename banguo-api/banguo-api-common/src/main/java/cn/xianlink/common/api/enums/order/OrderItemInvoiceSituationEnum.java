package cn.xianlink.common.api.enums.order;

import cn.xianlink.common.core.domain.vo.CoreEnumVo;
import cn.xianlink.common.core.enums.CoreEnumRow;
import lombok.Getter;

/**
 * 订单项-开票情况
 * <AUTHOR>
 */
@Getter
public enum OrderItemInvoiceSituationEnum implements CoreEnumRow {
    /**
     * ，1-未开发票，10-已开商品发票
     */
    UN_INVOICE(1, "未开发票"),
    PRODUCT_INVOICE(10, "已开商品发票"),
    ;

    private final Integer code;
    private final String desc;

    OrderItemInvoiceSituationEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public CoreEnumVo getEnumRow() {
        return new CoreEnumVo(String.valueOf(this.code), this.desc);
    }
}
