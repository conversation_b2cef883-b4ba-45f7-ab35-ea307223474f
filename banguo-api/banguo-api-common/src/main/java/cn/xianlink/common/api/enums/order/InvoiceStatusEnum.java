package cn.xianlink.common.api.enums.order;

import cn.hutool.core.util.ObjectUtil;
import cn.xianlink.common.core.domain.vo.CoreEnumVo;
import cn.xianlink.common.core.enums.CoreEnumRow;
import lombok.Getter;

/**
 * 发票状态枚举
 * <AUTHOR>
 */
@Getter
public enum InvoiceStatusEnum implements CoreEnumRow {
    APPLIED("applied", "申请中"),
    UPLOADED("uploaded", "已上传"),
    AUDITED_APPROVED("audited_approved", "审核通过"),
    AUDITED_REJECTED("audited_rejected", "审核拒绝");
    ;

    private final String code;
    private final String desc;

    InvoiceStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static InvoiceStatusEnum loadByDesc(String desc) {
        for (InvoiceStatusEnum value : values()) {
            if (value.getDesc().equals(desc)) {
                return value;
            }
        }
        return null;
    }

    public static InvoiceStatusEnum loadByCode(Integer code) {
        for (InvoiceStatusEnum value : values()) {
            if (ObjectUtil.equal(value.getCode(), code)) {
                return value;
            }
        }
        return null;
    }

    @Override
    public CoreEnumVo getEnumRow() {
        return new CoreEnumVo(this.code, this.desc);
    }
}
