package cn.xianlink.basic.api.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * region_wh，总仓视图对象
 *
 * <AUTHOR>
 * @date 2024-03-21
 */
@Data
public class RemoteRegionWhVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 总仓id，主键
     */
    private Long id;
    /**
     * 总仓编码
     */
    private String regionWhCode;
    /**
     * 总仓名称
     */
    private String regionWhName;
    /**
     * 状态，0为不可用；1为可用
     */
    private Integer status;
    /**
     * 自动清库存时间；格式：HH:mm
     */
    private String clearWhTime;
    /**
     * 销售开始时间；格式：HH:mm
     */
    private String salesTimeStart;
    /**
     * 销售结束时间；格式：HH:mm
     */
    private String salesTimeEnd;
    /**
     * 提前销售结束时间；格式：HH:mm
     */
    private String earlySalesTimeEnd;
    /**
     * 发货结束时间；格式：HH:mm
     */
    private String deliveryTimeEnd;
    /**
     * 发货完成时间-自动履约补数(缺货、装车、发车)；格式：HH:mm
     */
    private String deliveryCompleteTime;
    /**
     * 直属城市仓id列表
     */
    private List<Long> cityWhIdList;
    /**
     * 云仓服务费单价；元/斤
     */
    private BigDecimal cwServiceFeePrice;
    /**
     * 是否试点按订单项送货(0否 1是)
     */
    private Integer isOrderItemDelivery;
    /**
     * 生效销售日期
     */
    private LocalDate effectSaleDate;
    /**
     * 质检报告是否必传(0否 1是)
     */
    private Integer qualityReport;
    /**
     * 劣品等级标准：报损订单，报损金额，报损率
     */
    private String badLevel;

    private List<RemoteRegionWhParkingVo> parkingList;

    /**
     * 类型, 1普通总仓, 0为地采
     */
    private Integer type;

    /**
     * 是否需要坑位，0否1是
     */
    private Integer isSaleNum;
    /**
     * 超管代码[外键]
     */
    private String adminCode;

}
