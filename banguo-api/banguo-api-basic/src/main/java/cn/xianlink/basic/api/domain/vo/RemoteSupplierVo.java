package cn.xianlink.basic.api.domain.vo;

import cn.xianlink.common.core.enums.StatusEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR> xiaodaibing on 2024-04-19 10:05
 */
@Accessors(chain = true)
@Data
public class RemoteSupplierVo implements Serializable {


    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 供应商编码
     */
    private String code;

    /**
     * 供应商简码
     */
    private String simpleCode;

    /**
     * 供应商名称
     */
    private String name;

    /**
     * 供应商别名
     */
    private String alias;

    /**
     * 供应商状态，0禁用；1启用
     * @see StatusEnum
     */
    private Integer status;

    /**
     * 审核状态，0待审核；1审核通过；2审核不通过
     * 2审核不通过，可能是单纯审核不通过，也可能是开户不通过
     * see SupplierAuditEnum
     */
    private Integer auditStatus;

    /**
     * 超管代码
     */
    private String adminCode;


    /**
     * 供应商可提供发票类型（1-不提供，2-提供普通发票，3-提供普通发票+专用发票）
     */
    private Integer provideInvoice;

}
