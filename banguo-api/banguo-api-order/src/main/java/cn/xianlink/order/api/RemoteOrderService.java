package cn.xianlink.order.api;

import cn.xianlink.common.mybatis.core.page.TableDataInfo;
import cn.xianlink.order.api.bo.RemoteOrderCancelBo;
import cn.xianlink.order.api.bo.RemoteOrderQueryBo;
import cn.xianlink.order.api.bo.RemotePayUpdateStatusBo;
import cn.xianlink.order.api.bo.RemoteReplaceSupplierBo;
import cn.xianlink.order.api.vo.*;
import cn.xianlink.order.api.vo.RemoteOrderInfoVo;
import cn.xianlink.order.api.vo.RemoteOrderItemVo;
import cn.xianlink.order.api.vo.RemoteOrderSalesVO;
import cn.xianlink.order.api.vo.RemoteRegionSubMaxPriceVo;
import cn.xianlink.order.api.vo.RemoteOrderVo;
import net.bytebuddy.asm.Advice;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;

public interface RemoteOrderService {

    /**
     * 获取优惠补贴金额
     * @param regionWhId
     * @return
     */
    BigDecimal getSubsidyFreeAmount(Long regionWhId);

    /**
     * 换供应商，订单项处理，以及生成转单记录
     * @param bo
     * @return
     */
    Boolean replaceSupplier(RemoteReplaceSupplierBo bo);

    /**
     * 订单支付成功回调接口
     * @param bo
     * @return
     */
    void payUpdateStatus(RemotePayUpdateStatusBo bo);

    /**
     * 支付成功后不管后面订单会不会取消，都要先加入支付信息
     * @param bo
     */
    void updatePayInfo(RemotePayUpdateStatusBo bo);

    /**
     * 差额退款定时任务
     */
    void refundOrderDifference(LocalDate saleDate, List<Long> supplierSkuIdList, List<Long> orderItemIds);

    /**
     * 取消订单，发送消息
     * @param bo
     */
    void orderCancel(RemoteOrderCancelBo bo);

    /**
     * 根据订单id获取订单信息
     * @param orderId
     * @return
     */
    RemoteOrderInfoVo getOrderInfo(Long orderId);
    /**
     * 获取城市仓销量信息
     */
    List<RemoteOrderSalesVO> getOrderSalesByCity(Long cityWhId);

    /**
     * 获取订单查看时间
     * @return
     */
    Date getDelayQueryTime();

    void couponAutoCreateOrder(List<Long> skuIds,Long couponUserId);
    /**
     * 获取最近购买过该sku的订单
     *
     * @param customerId
     * @param skuId
     * @return
     */
    RemoteOrderItemVo findLastBuySku(Long customerId, List<Long> skuIds);
//    Map<Long, RemoteOrderItemVo> findLastBuySku(Long customerId, List<Long> skuIds);

    /**
     * 根据销售日期，获取有销售记录城市仓id列表
     */
    List<RemoteOrderSalesVO> getCityWhIdListByExistSales(LocalDate saleDateStart, LocalDate saleDateEnd, Long notRunRegionWhId);

    /**
     * 修复订单利润规则
     * @param ruleType
     * @param regionWhId
     * @param cityWhId
     * @param saleDate
     * @param id
     */
    void fixProfitRule(Long ruleType, Long regionWhId, Long cityWhId, LocalDate saleDate, Long id);

    /**
     * 查询订单列表
     * @param bo
     * @return
     */
    TableDataInfo<RemoteOrderVo> findOrderPage(RemoteOrderQueryBo bo);

    List<RemoteRegionSubMaxPriceVo> selectMinPriceBySkuIds(Set<Long> supSkuIds);

    /**
     * 根据 ID 查询
     *
     * @param id 主键ID
     */
    RemoteOriginOrderVo getById(Serializable id);

    /**
     * 查询（根据ID 批量查询）
     *
     * @param idList 主键ID列表
     */
    List<RemoteOriginOrderVo> listByIds(Collection<? extends Serializable> idList);

    /**
     * 根据订单号获取订单信息
     * @param orderCode
     * @return
     */
    RemoteOrderInfoVo getOrderInfoByCode(String orderCode);

    /**
     * 根据订单id获取订单详情
     * @param orderId
     * @return
     */
    RemoteOrderDetailVo getOrderDetailById(Long orderId);

    /**
     * 根据订单号获取订单详情
     * @param orderCode
     * @return
     */
    RemoteOrderDetailVo getOrderDetailByCode(String orderCode);


    /**
     * 根据订单号查询订单ID
     */
    Long getIdByCode(String code);


    /**
     * 群组清理任务
     */
    boolean cleanupExpiredGroups();

    Map<String, Long> getSalesQuantityCount(Long skuId, LocalDate yesterday, LocalDate week, LocalDate saleDate);
}
