package cn.xianlink.order.api.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 发票视图对象 inv_invoice
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Data
public class RemoteInvoiceVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 发票ID (主键)
     */
    private Long id;

    /**
     * 发票号 (唯一)
     */
    private String invoiceNumber;

    /**
     * 总仓id
     */
    private Long regionWhId;

    /**
     * 供应商ID[外键]
     */
    private Long supplierId;

    /**
     * 发票抬头ID[外键]
     */
    private Long invoiceTitleId;

    /**
     * 客户Id
     */
    private Long customerId;

    /**
     * 发票金额 (从发票项汇总)
     */
    private BigDecimal invoiceAmount;

    /**
     * 开票日期
     */
    private Date issueDate;

    /**
     * 状态 (applied申请中, uploaded已上传, audited_approved审核通过, audited_rejected审核拒绝)
     */
    private String status;

    /**
     * 场景 (supplier供应商开票, general_warehouse总仓发票, city_warehouse城市仓发票等)
     */
    private String scene;

    /**
     * 发票类型 (normal普票, special专票)
     */
    private String invoiceType;

    /**
     * 发票格式 (electronic电子, paper纸质)
     */
    private String invoiceFormat;

    /**
     * 抬头类型 (company公司, personal个人)
     */
    private String titleType;

    /**
     * 供应商名称快照 (冗余)
     */
    private String supplierNameSnapshot;

    /**
     * 发票抬头快照 (冗余)
     */
    private String invoiceTitleSnapshot;

    /**
     * 税号快照 (冗余)
     */
    private String taxNumberSnapshot;

    /**
     * 地址快照 (冗余)
     */
    private String addressSnapshot;

    /**
     * 银行账号快照 (冗余)
     */
    private String bankAccountSnapshot;

    /**
     * 银行名称快照
     */
    private String bankNameSnapshot;

    /**
     * 电话快照 (冗余)
     */
    private String phoneSnapshot;

    /**
     * 邮箱地址快照 (冗余)
     */
    private String emailAddressSnapshot;

    /**
     * 快递_联系电话
     */
    @ExcelProperty(value = "快递_联系电话")
    private String contactPhone;

    /**
     * 快递_所在地区
     */
    @ExcelProperty(value = "快递_所在地区")
    private String region;

    /**
     * 快递_详细地址
     */
    @ExcelProperty(value = "快递_详细地址")
    private String detailedAddress;

    /**
     * 快递_姓名
     */
    @ExcelProperty(value = "快递_姓名")
    private String recipientName;

    /**
     * 快递_纬度
     */
    @ExcelProperty(value = "快递_纬度")
    private BigDecimal latitude;

    /**
     * 快递_经度
     */
    @ExcelProperty(value = "快递_经度")
    private BigDecimal longitude;

    /**
     * 快递单号 (纸质发票供应商填写)
     */
    @ExcelProperty(value = "快递单号 (纸质发票供应商填写)")
    private String expressNumber;

    /**
     * 文件URL列表 (JSON数组，支持多张)
     */
    @ExcelProperty(value = "文件URL列表 (JSON数组，支持多张)")
    private String fileUrls;

    /**
     * 申请日期
     */
    @ExcelProperty(value = "申请日期")
    private Date applyDate;

    /**
     * 审核日期
     */
    @ExcelProperty(value = "审核日期")
    private Date auditDate;

    /**
     * 商户编码
     */
    @ExcelProperty(value = "商户编码")
    private String merchantCode;

    /**
     * 商品合计件数
     */
    @ExcelProperty(value = "商品合计件数")
    private Integer totalItems;

    /**
     * 开票订单区间
     */
    @ExcelProperty(value = "开票订单区间")
    private String orderDateRange;

    /**
     * 创建用户代码
     */
    @ExcelProperty(value = "创建用户代码")
    private String createCode;

    /**
     * 创建用户名称
     */
    @ExcelProperty(value = "创建用户名称")
    private String createName;

    /**
     * 修改用户代码
     */
    @ExcelProperty(value = "修改用户代码")
    private String updateCode;

    /**
     * 修改用户名称
     */
    @ExcelProperty(value = "修改用户名称")
    private String updateName;


}
