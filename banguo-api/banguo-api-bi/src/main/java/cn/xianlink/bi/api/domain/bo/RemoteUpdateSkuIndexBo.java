package cn.xianlink.bi.api.domain.bo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2025/8/2
 **/
@Accessors(chain = true)
@Data
public class RemoteUpdateSkuIndexBo implements Serializable {

    /**
     *
     */
    private Long skuId;

    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * 销售日
     */
    private Date saleDate;

    /**
     * 发票类型
     */
    private Integer provideInvoice;

}
