# 项目编码规则 - 基于RuoYi-Cloud-Plus

## 项目概述
- 项目名称：banguo（般果）微服务系统
- 基础框架：RuoYi-Cloud-Plus 微服务通用权限管理系统
- 技术栈：Spring Boot 3.x + Spring Cloud 2023.x + Spring Cloud Alibaba 2022.x
- JDK版本：17+
- 数据库：MySQL 8.0+
- 缓存：Redis 6.0+
- 消息队列：RocketMQ 5.x
- 注册中心：Nacos 2.x
- 配置中心：Nacos Config
- 网关：Spring Cloud Gateway
- 负载均衡：Spring Cloud LoadBalancer
- 熔断降级：Sentinel
- 分布式事务：Seata
- 任务调度：PowerJob
- 链路追踪：SkyWalking
- 监控：Prometheus + Grafana
- 文档：Knife4j (OpenAPI 3)

## 框架核心特性
- 采用前后端分离的模式，微服务版本前端(基于RuoYi-Vue)
- 后端采用Spring Boot、Spring Cloud & Alibaba
- 注册中心、配置中心选型Nacos，权限认证使用Redis
- 流量控制框架选型Sentinel，分布式事务选型Seata
- 提供了技术栈（Vue3 Element Plus Vite）版本
- 特别鸣谢：感谢RuoYi作者若依的无私奉献

## 模块结构规范

### 主要模块
- `ruoyi-gateway`：网关模块 [8080]
- `ruoyi-auth`：认证中心 [9200]
- `ruoyi-modules-system`：系统模块 [9201]
- `ruoyi-modules-gen`：代码生成 [9202]
- `ruoyi-modules-job`：定时任务 [9203]
- `ruoyi-modules-file`：文件服务 [9300]
- `ruoyi-visual-monitor`：监控中心 [9100]

### 业务模块（banguo扩展）
- `banguo-modules/banguo-product`：商品模块 [9204]
- `banguo-modules/banguo-order`：订单模块 [9205]
- `banguo-modules/banguo-basic`：基础模块 [9206]
- `banguo-modules/banguo-trade`：交易模块 [9207]
- `banguo-modules/banguo-job`：定时任务模块 [9208]
- `banguo-modules/banguo-bi`：BI模块 [9209]
- `banguo-modules/banguo-ai`：AI模块 [9210]

### 公共模块
- `ruoyi-common-core`：核心模块
- `ruoyi-common-datascope`：权限范围
- `ruoyi-common-datasource`：多数据源
- `ruoyi-common-log`：日志记录
- `ruoyi-common-redis`：缓存服务
- `ruoyi-common-security`：安全模块
- `ruoyi-common-swagger`：系统接口
- `ruoyi-common-translation`：翻译模块

## 编码规范

### 包命名规范
- 基础包名：`org.dromara.ruoyi` (框架) / `cn.xianlink` (业务)
- 控制器：`{package}.controller`
- 服务层：`{package}.service` / `{package}.service.impl`
- 数据层：`{package}.mapper`
- 实体类：`{package}.domain`
- DTO/VO：`{package}.domain.vo` / `{package}.domain.bo`

### 类命名规范
- 实体类：直接使用实体名，如 `SysUser`、`SysDept`
- 控制器：以 `Controller` 结尾，如 `SysUserController`
- 服务接口：以 `Service` 结尾，如 `ISysUserService`
- 服务实现：以 `ServiceImpl` 结尾，如 `SysUserServiceImpl`
- Mapper接口：以 `Mapper` 结尾，如 `SysUserMapper`
- BO类：以 `Bo` 结尾，如 `SysUserBo`
- VO类：以 `Vo` 结尾，如 `SysUserVo`

### 实体与模块间调用规范
- 数据库表a的mapper尽量不要写,写数据库b表的sql语句
- 如果需要再一个service用到多个数据库表，同模块通过xxmapper调用，跨模块通过Remotexxservice调用;

### 编码规范补充
- 每个Controller的注释里面都写清楚业务实现步骤，1.xx 2.xx 3.xx 等
- Bo对象：Bo对象只用于接收参数，不允许Bo对象返回给前端，要做参数校验，要写清说明
- 每个表内置系统字段不需要手动维护，系统会自动维护,也不需要前端传参，具体字段：createCode,createName,updateCode,updateName
- 使用LoginHelper.getLoginUser()获取当前登录用户。loginUser.getRelationId()为当前登录的客户id或者供应商，城市仓id，系统字段使用；
- AI编程方案文件请统一写到resources/docs/下以模块名命名；
- 考虑性能不能有慢sql，代码考虑循环时间复杂度

### 接口文档
-采用swagger实现接口文档，接口文档需要补充接口说明，请求和响应参数要与对应中文说明，以及做example示例，不需要写一些通用的规范如状态码200表示成功等

### 注解使用规范

#### 控制器注解
```java
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/user")
public class SysUserController extends BaseController {
    
    @SaCheckPermission("system:user:list")
    @GetMapping("/list")
    public TableDataInfo<SysUserVo> list(SysUserBo user, PageQuery pageQuery) {
        return userService.selectPageUserList(user, pageQuery);
    }
}
```

#### 服务层注解
```java
@RequiredArgsConstructor
@Service
public class SysUserServiceImpl implements ISysUserService {
    
    private final SysUserMapper baseMapper;
    
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public TableDataInfo<SysUserVo> selectPageUserList(SysUserBo user, PageQuery pageQuery) {
        // 实现逻辑
    }
}
```

#### 实体类注解
```java
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_user")
public class SysUser extends BaseEntity {
    
    @TableId(value = "user_id")
    private Long userId;
    
    @Excel(name = "用户序号", cellType = ColumnType.NUMERIC, prompt = "用户编号")
    private Long userId;
    
    @Excel(name = "登录名称")
    @NotBlank(message = "用户账号不能为空")
    @Size(min = 0, max = 30, message = "用户账号长度不能超过30个字符")
    private String userName;
}
```

### 数据校验规范
- 使用JSR-303注解：`@NotNull`、`@NotBlank`、`@Size`、`@Pattern`
- 分组校验：`@Validated({AddGroup.class})`、`@Validated({EditGroup.class})`
- 自定义校验：实现 `ConstraintValidator` 接口

### 权限控制规范
- 使用Sa-Token进行权限控制
- 权限注解：`@SaCheckPermission("system:user:list")`
- 角色注解：`@SaCheckRole("admin")`
- 数据权限：`@DataScope(deptAlias = "d", userAlias = "u")`

### 数据库规范

#### 表命名
- 系统表：以 `sys_` 开头，如 `sys_user`、`sys_role`
- 业务表：以业务模块开头，如 `bas_customer`、`ord_order`
- 字典表：以 `sys_dict_` 开头

#### 字段规范
- 主键：统一使用 `{table}_id`，如 `user_id`、`role_id`
- 创建时间：`create_time`
- 更新时间：`update_time`
- 创建者：`create_by`
- 更新者：`update_by`
- 删除标志：`del_flag` (0-正常 1-删除)
- 状态字段：`status` (0-停用 1-正常)

#### 基础实体类
```java
@Data
public class BaseEntity implements Serializable {
    
    @Schema(description = "搜索值")
    @JsonIgnore
    @TableField(exist = false)
    private String searchValue;
    
    @Schema(description = "创建者")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;
    
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    
    @Schema(description = "更新者")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;
    
    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
```

### 多租户规范
- 继承 `TenantEntity` 基类
- 租户字段：`tenant_id`
- 自动填充：使用 `@TenantId` 注解

### 缓存使用规范
- 使用 `@Cacheable`、`@CacheEvict`、`@CachePut` 注解
- 缓存键命名：`模块:功能:参数`，如 `sys:user:1`
- 缓存过期时间：根据业务场景设置合理的TTL

### 异常处理规范
- 业务异常：继承 `ServiceException`
- 全局异常处理：`@RestControllerAdvice`
- 异常码定义：使用枚举类统一管理

### 日志规范
- 操作日志：使用 `@Log` 注解
- 日志级别：ERROR、WARN、INFO、DEBUG
- 日志格式：统一使用Logback配置

### API文档规范
- 使用Knife4j (OpenAPI 3.0)
- 控制器：`@Tag(name = "用户管理", description = "用户管理")`
- 接口：`@Operation(summary = "获取用户列表")`
- 参数：`@Parameter(description = "用户ID", required = true)`
- 响应：`@Schema(description = "用户信息")`

### 配置管理规范
- 使用 `@ConfigurationProperties` 绑定配置
- 配置类以 `Properties` 结尾
- 支持Nacos动态配置刷新

### 代码生成规范
- 使用框架内置代码生成器
- 模板路径：`vm/java/`
- 生成文件：Controller、Service、ServiceImpl、Mapper、Entity、VO、BO

### 分布式事务规范
- 使用Seata进行分布式事务管理
- 事务注解：`@GlobalTransactional`
- 事务模式：AT模式（默认）

### 消息队列规范
- 使用RocketMQ进行异步消息处理
- 生产者：`@RocketMQMessageListener`
- 消费者：实现 `RocketMQListener` 接口

### 定时任务规范
- 使用PowerJob进行任务调度
- 任务类：实现相应的Job接口
- 任务配置：通过PowerJob控制台管理

### 监控规范
- 健康检查：`/actuator/health`
- 指标监控：`/actuator/prometheus`
- 链路追踪：集成SkyWalking

### 部署规范
- 容器化部署：使用Docker
- 基础镜像：`eclipse-temurin:17-jre`
- 配置外部化：使用ConfigMap和Secret
- 服务发现：通过Nacos注册中心

### 环境配置
- local：本地开发环境
- dev：开发环境
- test：测试环境
- prod：生产环境

### 代码质量规范
- 使用SonarQube进行代码质量检查
- 单元测试覆盖率要求：>= 70%
- 使用Checkstyle进行代码风格检查

### 安全规范
- 密码加密：使用BCrypt
- 接口加密：支持AES/RSA加密
- XSS防护：使用Jsoup过滤
- SQL注入防护：使用MyBatis Plus预编译
- CSRF防护：使用Sa-Token内置防护

### 国际化规范
- 使用Spring MessageSource
- 资源文件：`messages_zh_CN.properties`
- 翻译注解：`@Translation`

### Excel导入导出规范
- 使用EasyExcel框架
- 导出注解：`@Excel`
- 导入校验：实现相应的校验器

### 文件上传规范
- 支持本地存储、OSS、MinIO
- 文件类型校验：白名单机制
- 文件大小限制：根据业务需求配置

这个规则文件基于RuoYi-Cloud-Plus框架的最佳实践，涵盖了微服务开发的各个方面，确保代码的一致性和可维护性。